package com.danding.cds.v2.bean.dao;

import com.danding.cds.common.model.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.Table;

/**
 * <p>
 * 加工贸易账册单耗信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
@ApiModel(value = "ProcessTradeBookConsumption对象", description = "加工贸易账册单耗信息表")
@Table(name = "ccs_process_trade_book_consumption")
public class ProcessTradeBookConsumptionDO extends BaseDO {

    private static final long serialVersionUID = 1L;


    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    private Long refBookId;

    /**
     * 成品id
     */
    @ApiModelProperty(value = "成品id")
    private Long endPrdId;

    /**
     * 料件id
     */
    @ApiModelProperty(value = "料件id")
    private Long mtpckId;

    /**
     * 单耗版本号 成品序号-料件序号
     */
    @ApiModelProperty(value = "单耗版本号 成品序号-料件序号")
    private String consumptionVersionNo;

    /**
     * 单耗有效期
     */
    @ApiModelProperty(value = "单耗有效期")
    private Date consumptionValidity;

    /**
     * 净耗
     */
    @ApiModelProperty(value = "净耗")
    private Integer netConsumption;

    /**
     * 有形损耗率（%），默认0
     */
    @ApiModelProperty(value = "有形损耗率（%），默认0")
    private BigDecimal tangibleLossRate;

    /**
     * 无形损耗率（%），默认0
     */
    @ApiModelProperty(value = "无形损耗率（%），默认0")
    private BigDecimal intangibleLossRate;

    /**
     * 单耗申报状态 1-未申报 2-已申报 3-已确定
     */
    @ApiModelProperty(value = "单耗申报状态 1-未申报 2-已申报 3-已确定")
    private Integer declareStatus;

    /**
     * 保税料件比例（%），默认100
     */
    @ApiModelProperty(value = "保税料件比例（%），默认100")
    private BigDecimal bondedMaterialRatio;

    /**
     * 修改标志，默认3-增加
     */
    @ApiModelProperty(value = "修改标志，默认3-增加")
    private String modifyFlag;


    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

}
