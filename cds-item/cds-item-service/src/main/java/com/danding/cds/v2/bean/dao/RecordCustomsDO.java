package com.danding.cds.v2.bean.dao;

import com.danding.cds.common.model.BaseDO;
import com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 备案-口岸
 * @date 2022/3/7 15:12
 */
@Data
@Table(name = "`ccs_record_customs`")
public class RecordCustomsDO extends BaseDO {
    /**
     * 商品备案id
     */
    private Long recordId;
    /**
     * 料号
     */
    private String productId;
    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;

    /**
     * 原产国放到口岸上维护
     */
    private String originCountry;
    /**
     * hs编码
     */
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;

    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 状态 待审核0 /通过 1
     * 1, "待审核";2, "备案完成";4, "审核驳回"  GoodsRecordStatusEnum
     */
    private Integer status;

    /**
     * 状态 新品1 /更新 2
     * {@link RecordCustomsSubmitTypeEnum}
     */
    private Integer submitType;

    /**
     * 备案提交时间
     */
    private Date submitTime;

    /**
     * 审核方式 0-人工审核， 1-系统审核
     */
    @Column(name = "audit_way")
    private Integer auditWay;

    /**
     * 口岸提交备案json串
     */
    private String baseInfoJson;

    /**
     * ERP提交审核状态
     */
    private Integer erpCommitStatus;

    /**
     * 关务备注
     */
    private String guanWuRemark;

    /**
     * 租户
     */
    private Long tenantryId;
}
