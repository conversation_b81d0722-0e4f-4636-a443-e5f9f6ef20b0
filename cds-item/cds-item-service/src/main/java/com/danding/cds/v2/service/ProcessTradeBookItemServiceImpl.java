package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dao.ProcessTradeBookItemDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.enums.ProcessTradeBookCompanyExecutionFlagEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookCustomsExecutionFlagEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookFocusMarkEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookItemQtyControlFlagEnums;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.service.base.ProcessTradeBookItemBaseService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <p>
 * 加工贸易账册表体 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Slf4j
@DubboService
public class ProcessTradeBookItemServiceImpl implements ProcessTradeBookItemService {

    @Resource
    private ProcessTradeBookItemBaseService baseService;

    @Resource
    private ProcessTradeBookItemServiceImpl self;

    @DubboReference
    private ProcessTradeBookConsumptionService processTradeBookConsumptionService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Override
    public ListVO<ProcessTradeBookItemDTO> paging(ProcessTradeBookItemSearch param) {
        return baseService.paging(param);
    }

    @Override
    public List<ProcessTradeBookItemDTO> pagingList(ProcessTradeBookItemSearch param) {
        return baseService.pagingList(param);
    }

    @Override
    public void createOrEdit(ProcessTradeBookItemDTO param) {
        List<ProcessTradeBookItemDO> itemDOList = baseService.findByBookIdAndProductId(param.getRefBookId(), param.getProductId());
        if (Objects.nonNull(param.getId())) {
            if (CollUtil.isNotEmpty(itemDOList) && itemDOList.stream().anyMatch(itemDO -> !itemDO.getId().equals(param.getId()))) {
                throw new ArgsInvalidException("料号：" + param.getProductId() + "已存在，无法添加");
            }
            ProcessTradeBookItemDTO itemDTO = this.findById(param.getId());
            if (!Objects.equals(itemDTO.getRefBookId(), param.getRefBookId())) {
                throw new ArgsInvalidException("账册id不一致");
            }
            if (!Objects.equals(itemDTO.getGoodsType(), param.getGoodsType())) {
                throw new ArgsInvalidException("商品类型不一致");
            }
            if (!Objects.equals(itemDTO.getSeqNo(), param.getSeqNo())) {
                throw new ArgsInvalidException("序号不一致");
            }
            baseService.updateByPrimaryKeySelective(ConvertUtil.beanConvert(param, ProcessTradeBookItemDO.class));
        } else {
            if (CollUtil.isNotEmpty(itemDOList)) {
                throw new ArgsInvalidException("料号：" + param.getProductId() + "已存在，无法添加");
            }
            ProcessTradeBookItemDO insertDO = ConvertUtil.beanConvert(param, ProcessTradeBookItemDO.class);
            insertDO.setSeqNo(baseService.getNextSeqNo(param.getRefBookId(), param.getGoodsType()));
            insertDO.setCompanyExecutionFlag(ProcessTradeBookCompanyExecutionFlagEnums.RUNNING.getCode());
            insertDO.setCustomsExecutionFlag(ProcessTradeBookCustomsExecutionFlagEnums.NORMAL.getCode());
            insertDO.setQtyControlFlag(ProcessTradeBookItemQtyControlFlagEnums.NO_CONTROL_QTY.getCode());
            insertDO.setFocusMark(ProcessTradeBookFocusMarkEnums.NOT_FOCUS_GOODS.getCode());
            insertDO.setInitialQty(0);
            insertDO.setModifyFlag("3");
            insertDO.setDutyExemptionMethod("3");
            baseService.insertSelective(insertDO);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteItem(Long bookId, List<Long> idList) {
        List<ProcessTradeBookItemDO> itemDOList = baseService.findById(idList);
        // 对账册表体根据商品类型进行分组 获取每组中最小的序号
        Map<Integer, Integer> minSeqNoMap = itemDOList.stream()
                .collect(Collectors.groupingBy(ProcessTradeBookItemDO::getGoodsType,
                        Collectors.minBy(Comparator.comparing(ProcessTradeBookItemDO::getSeqNo))))
                .entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().get().getSeqNo()));
        baseService.deleteById(idList);
        self.resetSeqNo(bookId, minSeqNoMap);
        processTradeBookConsumptionService.deleteByItemId(idList);
    }

    @Override
    public ProcessTradeBookItemDTO findById(Long id) {
        return ConvertUtil.beanConvert(baseService.findById(id), ProcessTradeBookItemDTO.class);
    }

    @Override
    public List<ProcessTradeBookItemDTO> findById(List<Long> id) {
        return ConvertUtil.listConvert(baseService.findById(id), ProcessTradeBookItemDTO.class);
    }

    @Override
    public List<ProcessTradeBookItemDTO> matchItem(Long refBookId, Long bookId, Integer goodsType, String productId) {
        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(bookId, productId);
        List<ProcessTradeBookItemDTO> itemDTOList = new ArrayList<>();
        if (goodsRecordDTO != null) {
            ProcessTradeBookItemDTO itemDTO = new ProcessTradeBookItemDTO();
            itemDTO.setRefBookId(refBookId);
            itemDTO.setSeqNo(baseService.getNextSeqNo(refBookId, goodsType));
            itemDTO.setGoodsType(goodsType);
            itemDTO.setProductId(goodsRecordDTO.getProductId());
            itemDTO.setHsCode(goodsRecordDTO.getHsCode());
            itemDTO.setCurrency(goodsRecordDTO.getDeclareCurrency());
            itemDTO.setGoodsName(goodsRecordDTO.getGoodsRecordName());
            itemDTO.setGoodsModel(goodsRecordDTO.getModel());
            itemDTO.setLegalUnit(goodsRecordDTO.getFirstUnit());
            itemDTO.setLegalSecondUnit(goodsRecordDTO.getSecondUnit());
            itemDTO.setCountryRegion(goodsRecordDTO.getOriginCountry());
            itemDTO.setDeclareUnit(goodsRecordDTO.getDeclareUnit());
            itemDTO.setDeclareUnitPrice(goodsRecordDTO.getDeclarePrice());
            itemDTO.setDutyExemptionMethod("3");
            itemDTO.setCompanyExecutionFlag(1);
            itemDTO.setFocusMark(0);
            itemDTO.setCountryRegion(goodsRecordDTO.getOriginCountry());
            itemDTO.setModifyFlag("3");
            itemDTO.setCustomsExecutionFlag(1);
            itemDTO.setQtyControlFlag(2);

            if (Objects.equals(goodsType, 1)) { // 料件
                itemDTO.setInitialQty(0);
            }
            itemDTOList.add(itemDTO);
        }
        return itemDTOList;
    }

    @Override
    public ProcessTradeBookItemDTO findByProductIdAndBookId(Integer goodsType, String productId, Long bookId) {
        if (Objects.isNull(goodsType) || Objects.isNull(bookId) || Objects.isNull(productId)) {
            throw new ArgsInvalidException("商品类型、账册id、料号不能为空");
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andEqualTo("refBookId", bookId)
                .andEqualTo("goodsType", goodsType)
                .andEqualTo("productId", productId)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookItemDO> itemDOList = baseService.selectByExample(example);
        if (CollUtil.isEmpty(itemDOList)) {
            return null;
        }
        return ConvertUtil.beanConvert(itemDOList.get(0), ProcessTradeBookItemDTO.class);
    }

    @Override
    public List<ProcessTradeBookItemDTO> findByGoodsTypeAndBookId(Integer goodsType, Long bookId) {
        if (Objects.isNull(goodsType) || Objects.isNull(bookId)) {
            throw new ArgsInvalidException("商品类型、账册id不能为空");
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andEqualTo("refBookId", bookId)
                .andEqualTo("goodsType", goodsType)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookItemDO> itemDOList = baseService.selectByExample(example);
        return ConvertUtil.listConvert(itemDOList, ProcessTradeBookItemDTO.class);
    }

    @Override
    public void importExcel(Long bookId, Integer goodsType, List<ProcessTradeBookItemDTO> itemDTOList) {
        Integer nextSeqNo = baseService.getNextSeqNo(bookId, goodsType);
        for (ProcessTradeBookItemDTO itemDTO : itemDTOList) {
            itemDTO.setSeqNo(nextSeqNo++);
        }
        baseService.insertList(ConvertUtil.listConvert(itemDTOList, ProcessTradeBookItemDO.class));
    }

    @Override
    public List<ProcessTradeBookItemDTO> listItemAllById(Long refBookId) {
        if (Objects.isNull(refBookId)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andEqualTo("refBookId", refBookId)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookItemDO> itemDOList = baseService.selectByExample(example);
        return ConvertUtil.listConvert(itemDOList, ProcessTradeBookItemDTO.class);
    }

    @Transactional(rollbackFor = Exception.class)
    public void resetSeqNo(Long bookId, Map<Integer, Integer> minSeqNoMap) {
        if (CollUtil.isEmpty(minSeqNoMap)) {
            return;
        }
        for (Map.Entry<Integer, Integer> entry : minSeqNoMap.entrySet()) {
            Integer goodType = entry.getKey();
            Integer minSeqNo = entry.getValue();
            List<ProcessTradeBookItemDO> itemDOList = baseService.findByBookIdAndType(bookId, goodType, minSeqNo);
            if (CollUtil.isEmpty(itemDOList)) {
                continue;
            }
            AtomicInteger seqNo = new AtomicInteger(minSeqNo);
            List<ProcessTradeBookItemDO> updateDOList = itemDOList.stream().map(dto -> {
                ProcessTradeBookItemDO itemDO = new ProcessTradeBookItemDO();
                itemDO.setId(dto.getId());
                itemDO.setSeqNo(seqNo.getAndIncrement());
                return itemDO;
            }).collect(Collectors.toList());
            baseService.batchUpdate(updateDOList);
        }
    }
}