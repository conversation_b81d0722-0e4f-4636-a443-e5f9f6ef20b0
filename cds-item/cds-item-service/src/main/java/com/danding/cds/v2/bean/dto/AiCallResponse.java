package com.danding.cds.v2.bean.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * AI调用响应对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Accessors(chain = true)
public class AiCallResponse implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 响应ID
     */
    private String id;

    /**
     * 对象类型
     */
    private String object;

    /**
     * 创建时间
     */
    private Long created;

    /**
     * 模型名称
     */
    private String model;

    /**
     * 选择列表
     */
    private List<Choice> choices;

    /**
     * 使用情况
     */
    private Usage usage;

    /**
     * 选择对象
     */
    @Data
    @Accessors(chain = true)
    public static class Choice implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 索引
         */
        private Integer index;

        /**
         * 消息
         */
        private AiMessage message;

        /**
         * 完成原因
         */
        private String finishReason;
    }

    /**
     * 使用情况对象
     */
    @Data
    @Accessors(chain = true)
    public static class Usage implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 提示令牌数
         */
        private Integer promptTokens;

        /**
         * 完成令牌数
         */
        private Integer completionTokens;

        /**
         * 总令牌数
         */
        private Integer totalTokens;
    }

    /**
     * 获取第一个选择的消息内容
     *
     * @return 消息内容
     */
    public String getFirstChoiceContent() {
        if (choices != null && !choices.isEmpty() && choices.get(0).getMessage() != null) {
            return choices.get(0).getMessage().getContent();
        }
        return null;
    }

    /**
     * 判断响应是否成功
     *
     * @return 是否成功
     */
    public boolean isSuccess() {
        return choices != null && !choices.isEmpty() && choices.get(0).getMessage() != null;
    }
}
