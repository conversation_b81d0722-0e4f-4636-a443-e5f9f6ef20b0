package com.danding.cds.item.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.InventoryTransitEnums;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemParam;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.enums.JdGoodsRecordType;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.CustomsGoodsService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.JdGoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.item.entity.CustomsBookItemDO;
import com.danding.cds.item.entity.GoodsRecordDO;
import com.danding.cds.item.mapper.CustomsBookItemMapper;
import com.danding.cds.item.mapper.GoodsRecordMapper;
import com.danding.cds.v2.api.InvOrderItemGenerateExecutorInterface;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.enums.BizDeclareFormMtpckEndprdTypeEnums;
import com.danding.cds.v2.enums.InvOrderItemGenTypeEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.v2.service.base.GoodsRecordBaseService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;

@Slf4j
@DubboService
public class CustomsGoodsServiceImpl implements CustomsGoodsService {

    @Autowired
    private CustomsBookItemMapper customsBookItemMapper;

    @Autowired
    private GoodsRecordMapper goodsRecordMapper;

    @DubboReference
    private JdGoodsRecordService jdGoodsRecordService;

    @DubboReference
    private GoodsRecordService goodsRecordService;
    @Autowired
    private GoodsRecordBaseService goodsRecordBaseService;
    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private RecordCustomsService recordCustomsService;

    @DubboReference
    private InvOrderItemGenerateExecutorInterface invOrderItemGenerateExecutorInterface;

    @Override
    public List<CustomsGoodsItemInfoDTO> findItemDetailBy(String oldOrNew, String productId, String bookId, String goodsSeqNo) {
        if ("old".equalsIgnoreCase(oldOrNew)) {
            Example example = new Example(CustomsBookItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
            criteria.andEqualTo("goodsSeqNo", goodsSeqNo);
            criteria.andEqualTo("productId", productId);
            criteria.andEqualTo("deleted", false);
            List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
            return buildDTO(list, oldOrNew, Long.valueOf(bookId), productId, null);
        } else if ("new".equalsIgnoreCase(oldOrNew)) {
            List<Long> jdCustomsBookId = jdServProviderService.getJdCustomsBookId();
            if (EnvironmentConfig.isOnline() && jdCustomsBookId.contains(NumberUtils.createLong(bookId))) {
                JdGoodsRecordCustoms jdGoodsRecordCustoms = jdGoodsRecordService.getGoodsInfoBySku(productId, true);
                List<JdGoodsRecordCustoms> jdGoodsRecordCustomsList = new ArrayList<>();
                jdGoodsRecordCustomsList.add(jdGoodsRecordCustoms);
                return buildDTOByJdGoods(jdGoodsRecordCustomsList, Long.valueOf(bookId), null);
            } else {
//                Example example = new Example(GoodsRecordDO.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
//                criteria.andEqualTo("productId", productId);
//                criteria.andEqualTo("deleted", false);
//                List<GoodsRecordDO> list = goodsRecordMapper.selectByExample(example);
                List<GoodsRecordDO> list = goodsRecordBaseService.findByBookIdAndProId(NumberUtils.createLong(bookId), productId);
                return buildDTO(NumberUtils.createLong(bookId), list, oldOrNew);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public List<CustomsGoodsItemInfoDTO> findItemDetailByV2(String oldOrNew, String productId, String skuId, String bookId, String goodsSeqNo) {
        return this.findItemDetailByV2(oldOrNew, productId, null, skuId, bookId, goodsSeqNo, null);
    }

    @Override
    public List<CustomsGoodsItemInfoDTO> findItemDetailByV2(String oldOrNew, String productId, String customsRecordProductId, String skuId, String bookId, String goodsSeqNo, Long inventoryOrderId) throws ArgsInvalidException {
        if ("old".equalsIgnoreCase(oldOrNew)) {
            Example example = new Example(CustomsBookItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
            criteria.andEqualTo("goodsSeqNo", goodsSeqNo);
            if (Objects.nonNull(customsRecordProductId)) {
                criteria.andEqualTo("productId", customsRecordProductId);
            }
//            else {
//                criteria.andEqualTo("productId", productId);
//            }
            criteria.andEqualTo("deleted", false);
            List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
            //查询备案 构建参数
            return this.buildDTO(list, oldOrNew, Long.valueOf(bookId), productId, inventoryOrderId);
        } else if ("new".equalsIgnoreCase(oldOrNew)) {
//            if (EnvironmentConfig.isOnline() && "10".equals(bookId)) {
            List<Long> jdCustomsBookId = jdServProviderService.getJdCustomsBookId();
            if (jdCustomsBookId.contains(Long.valueOf(bookId))) {
                // 查询京东备案区分服务商
                JdServProviderDTO servProviderDTO = jdServProviderService.getServProviderByBookId(Long.valueOf(bookId));
                String providerId = servProviderDTO.getCode();
                JdGoodsRecordCustoms jdGoodsRecordCustoms = jdGoodsRecordService.getGoodsInfoByItemCode(productId, providerId, true,
                        Arrays.asList(JdGoodsRecordType.DIRECT, JdGoodsRecordType.INDEPENDENT, JdGoodsRecordType.POP_NEW));
                if (Objects.isNull(jdGoodsRecordCustoms)) {
                    throw new ArgsErrorException("京东备案管理不存在商品货号为[" + productId + "]备案完成的备案信息;");
                }
                List<JdGoodsRecordCustoms> jdGoodsRecordCustomsList = new ArrayList<>();
                jdGoodsRecordCustomsList.add(jdGoodsRecordCustoms);
                return buildDTOByJdGoods(jdGoodsRecordCustomsList, Long.valueOf(bookId), inventoryOrderId);
            } else {
//                Example example = new Example(GoodsRecordDO.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
//                criteria.andEqualTo("productId", productId);
//                criteria.andEqualTo("deleted", false);
//                List<GoodsRecordDO> list = goodsRecordMapper.selectByExample(example);
                List<GoodsRecordDO> list = goodsRecordBaseService.findByBookIdAndProId(NumberUtils.createLong(bookId), productId);
                return buildDTO(NumberUtils.createLong(bookId), list, oldOrNew, true, inventoryOrderId);
            }
        }
        return new ArrayList<>();
    }

    /**
     * 中转主单生成表体
     * 中转主单新品没有账册维度，取id最大的备案，不反填海关备案料号
     *
     * @return
     */
    @Override
    public List<CustomsGoodsItemInfoDTO> findItemDetailByV3(String oldOrNew, String productId, String customsRecordProductId, String skuId, String bookId, String goodsSeqNo, Long inventoryOrderId) throws ArgsErrorException {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        if (Objects.isNull(inventoryOrderInfoDTO)) {
            return this.findItemDetailByV2(oldOrNew, productId, customsRecordProductId, skuId, bookId, goodsSeqNo, inventoryOrderId);
        }
        boolean isTransitMaster = Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode());
        boolean isBondedProcess = Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode())
                || Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode());

        if (isTransitMaster && Objects.equals(oldOrNew, "new")) {
            List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(productId);
            if (CollectionUtils.isEmpty(goodsRecordDTOList)) {
                return new ArrayList<>();
            }
            GoodsRecordDTO goodsRecordDTO = goodsRecordDTOList.get(0);
            GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
            BeanUtils.copyProperties(goodsRecordDTO, goodsRecordDO);
            List<GoodsRecordDO> list = new ArrayList<>();
            list.add(goodsRecordDO);
            return buildDTO(NumberUtils.createLong(bookId), list, oldOrNew, false, inventoryOrderId);
        } else if (isBondedProcess && Objects.equals(oldOrNew, "old")) {
            return buildDTOByBondedProcess(bookId, productId, inventoryOrderInfoDTO);
        } else {
            //非新品 或者 非中转主单
            return this.findItemDetailByV2(oldOrNew, productId, customsRecordProductId, skuId, bookId, goodsSeqNo, inventoryOrderId);
        }
    }

    @Override
    public List<CustomsGoodsItemInfoDTO> matchByProductIdOrGoodsSeqNo(String oldOrNew, String bookId, String queryInfo, Long inventoryOrderId) {
        if ("old".equalsIgnoreCase(oldOrNew)) {
            Example example = new Example(CustomsBookItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
            //criteria.andEqualTo("enable",1);
            criteria.andEqualTo("deleted", false);
            Example.Criteria criteriaQueryInfo = example.createCriteria();
            if (StringUtils.isNotEmpty(queryInfo)) {
                criteriaQueryInfo.orEqualTo("productId", queryInfo).orEqualTo("goodsSeqNo", queryInfo);
            }
            example.and(criteriaQueryInfo);
            List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
            return buildDTO(list, oldOrNew, Long.valueOf(bookId), null, inventoryOrderId);
        } else if ("new".equalsIgnoreCase(oldOrNew)) {
            // 线上78账册新品取京东备案
//            if (EnvironmentConfig.isOnline() && "10".equals(bookId)) {
            //查询账册id是否能匹配到京东服务商
            JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(Long.valueOf(bookId));
            if (Objects.nonNull(jdServProviderDTO)) {
                JdGoodsRecordCustoms jdGoodsRecordCustom = jdGoodsRecordService.getListByItemCode(queryInfo, jdServProviderDTO.getCode()
                        , Arrays.asList(JdGoodsRecordType.DIRECT, JdGoodsRecordType.POP_NEW, JdGoodsRecordType.INDEPENDENT));
                if (Objects.isNull(jdGoodsRecordCustom)) {
                    throw new ArgsInvalidException("京东备案管理不存在商品货号为[" + queryInfo + "]备案完成的备案信息;");
                }
                return buildDTOByJdGoods(Arrays.asList(jdGoodsRecordCustom), Long.valueOf(bookId), inventoryOrderId);
            }
            List<GoodsRecordDO> goodsRecordDOList = goodsRecordBaseService.findByBookIdAndProId(Long.valueOf(bookId), queryInfo);
            for (GoodsRecordDO goodsRecordDO : goodsRecordDOList) {
                if (!Objects.equals(goodsRecordDO.getRecordStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {
                    throw new ArgsInvalidException("料号:" + goodsRecordDO.getProductId() + "备案状态未成功");
                }
            }
//                return buildDTO(goodsRecordDO, oldOrNew);
            //前端取值只用到料号和通关料号
//                return goodsRecordDOList.stream().map(g -> {
//                    CustomsGoodsItemInfoDTO itemInfoDTO = ConvertUtil.beanConvert(g, CustomsGoodsItemInfoDTO.class);
//                    itemInfoDTO.setOldOrNew(oldOrNew);
//                    itemInfoDTO.setRecordProductName(g.getGoodsRecordName());
//                    return itemInfoDTO;
//                }).collect(Collectors.toList());
            return buildDTO(Long.valueOf(bookId), goodsRecordDOList, oldOrNew, true, inventoryOrderId);
        }
        return new ArrayList<>();
    }


    /**
     * 中转主单新建表体， 新品没有账册维度
     *
     * @return 若备案有多个，多条展示下拉
     */
    @Override
    public List<CustomsGoodsItemInfoDTO> matchByProductIdOrGoodsSeqNoV2(String oldOrNew, String bookId, String queryInfo, Long inventoryOrderId) {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        if (Objects.isNull(inventoryOrderInfoDTO)) {
            return matchByProductIdOrGoodsSeqNo(oldOrNew, bookId, queryInfo, inventoryOrderId);
        }
        boolean isTransitMaster = Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode());
        boolean isBondedProcess = Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode())
                || Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode());
        if (isTransitMaster && Objects.equals(oldOrNew, "new")) {
            // 共享备案
            List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(queryInfo);
            for (GoodsRecordDTO goodsRecordDTO : goodsRecordDTOList) {
                if (!Objects.equals(goodsRecordDTO.getRecordStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {
                    throw new ArgsInvalidException("料号:" + goodsRecordDTO.getProductId() + "备案状态未成功");
                }
            }
            List<GoodsRecordDO> goodsRecordDOS = ConvertUtil.listConvert(goodsRecordDTOList, GoodsRecordDO.class);
            // 共享备案不反填 海关备案料号
            return buildDTO(Long.valueOf(bookId), goodsRecordDOS, oldOrNew, false, inventoryOrderId);
        } else if (isBondedProcess && Objects.equals(oldOrNew, "old")) {
            return buildDTOByBondedProcess(bookId, queryInfo, inventoryOrderInfoDTO);
        } else {
            return matchByProductIdOrGoodsSeqNo(oldOrNew, bookId, queryInfo, inventoryOrderId);
        }
    }

    private List<CustomsGoodsItemInfoDTO> buildDTOByBondedProcess(String bookId, String queryInfo, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        if (StrUtil.isEmpty(queryInfo)) {
            return new ArrayList<>();
        }
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(Long.valueOf(bookId));
        CustomsBookDTO customsBookDTO = new CustomsBookDTO();
        BeanUtils.copyProperties(customsBookResVo, customsBookDTO);
        InventoryOrderItemParam invOrderItemParam = new InventoryOrderItemParam();
        invOrderItemParam.setProductId(queryInfo);
        if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode())) {
            invOrderItemParam.setGoodsType(1);
        } else {
            invOrderItemParam.setGoodsType(2);
        }
        InventoryOrderItemDTO itemDTO = null;
        try {
            itemDTO = invOrderItemGenerateExecutorInterface.execute(InvOrderItemGenTypeEnums.PROCESSING_TRADE, customsBookDTO, invOrderItemParam);
        } catch (BusinessException e) {
            log.error("保税加工业务类型生成清关单表体异常: {}", e.getMessage(), e);
        }
        if (Objects.isNull(itemDTO) || StrUtil.isEmpty(itemDTO.getGoodsSeqNo())) {
            return new ArrayList<>();
        }
        CustomsGoodsItemInfoDTO customsGoodsItemInfoDTO = new CustomsGoodsItemInfoDTO();
        BeanUtils.copyProperties(itemDTO, customsGoodsItemInfoDTO);
        customsGoodsItemInfoDTO.setOldOrNew("old");
        return Collections.singletonList(customsGoodsItemInfoDTO);
    }

    @Override
    public List<CustomsGoodsItemInfoDTO> findItemDetailByBizDeclareForm(Long bookId, String mtpckEndprdMarkCd, String queryInfo) {
        BizDeclareFormMtpckEndprdTypeEnums typeEnums = BizDeclareFormMtpckEndprdTypeEnums.getEnums(mtpckEndprdMarkCd);
        if (typeEnums == null || StrUtil.isEmpty(queryInfo)) {
            return new ArrayList<>();
        }
        switch (typeEnums) {
            case MATERIAL_PACKAGE:
                Example example = new Example(CustomsBookItemDO.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("customsBookId", bookId);
                criteria.andEqualTo("deleted", false);
                Example.Criteria criteriaQueryInfo = example.createCriteria();
                if (StringUtils.isNotEmpty(queryInfo)) {
                    criteriaQueryInfo.orEqualTo("productId", queryInfo).orEqualTo("goodsSeqNo", queryInfo);
                }
                example.and(criteriaQueryInfo);
                List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
                return buildDTO(list, "old", bookId);
            case END_PRODUCT:
                List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordAssociateService.findAllByBookIdAndProId(bookId, queryInfo);
                List<GoodsRecordDO> goodsRecordDOList = ConvertUtil.listConvert(goodsRecordDTOList, GoodsRecordDO.class);
                return buildDTO(bookId, goodsRecordDOList, "new");
        }
        return Collections.emptyList();
    }


    @Override
    public List<CustomsGoodsItemInfoDTO> findListBy(String oldOrNew, String bookId) {
        if ("old".equalsIgnoreCase(oldOrNew)) {
            Example example = new Example(CustomsBookItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
            //criteria.andEqualTo("enable",1);
            criteria.andEqualTo("deleted", false);
            List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
            return buildDTO(list, oldOrNew, Long.valueOf(bookId), null, null);
        } else if ("new".equalsIgnoreCase(oldOrNew)) {
            // 线上78账册新品取京东备案
//            if (EnvironmentConfig.isOnline() && "10".equals(bookId)) {
            List<Long> jdCustomsBookId = jdServProviderService.getJdCustomsBookId();
            if (jdCustomsBookId.contains(Long.valueOf(bookId))) {
                List<JdGoodsRecordCustoms> jdGoodsRecordCustomsList = jdGoodsRecordService.getListBySku("");
                return buildDTOByJdGoods(jdGoodsRecordCustomsList, Long.valueOf(bookId), null);
            } else {
//                Example example = new Example(GoodsRecordDO.class);
//                Example.Criteria criteria = example.createCriteria();
//                criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
//                criteria.andEqualTo("deleted", false);
//                List<GoodsRecordDO> list = goodsRecordMapper.selectByExample(example);
                List<GoodsRecordDO> list = goodsRecordBaseService.findByCustomsBookId(NumberUtils.createLong(bookId));
                return buildDTO(NumberUtils.createLong(bookId), list, oldOrNew);
            }
        }
        return new ArrayList<>();
    }

//    private GoodsRecordDTO findGoodsRecord(String productId,String bookId)
//    {
////        Example example = new Example(GoodsRecordDO.class);
////        Example.Criteria criteria = example.createCriteria();
////        criteria.andEqualTo("productId", productId)
////                .andEqualTo("customsBookId", NumberUtils.createLong(bookId))
////                .andEqualTo("deleted", 0);
//        GoodsRecordDO goodsRecordDO = goodsRecordBaseService.findRecordSuccessByBookIdAndProId(Long.valueOf(bookId), productId);
//        if (goodsRecordDO == null) {
//            return null;
//        } else {
//            return JSON.parseObject(JSON.toJSONString(goodsRecordDO), GoodsRecordDTO.class);
//        }
//    }

    /**
     * 根据京东备案生成清关单表体
     *
     * @param jdGoodsRecordCustomsList
     * @param inventoryOrderId
     * @return
     */
    private List<CustomsGoodsItemInfoDTO> buildDTOByJdGoods(List<JdGoodsRecordCustoms> jdGoodsRecordCustomsList, Long bookId, Long inventoryOrderId) {
        List<CustomsGoodsItemInfoDTO> oretList = new ArrayList<CustomsGoodsItemInfoDTO>();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(bookId);
        for (JdGoodsRecordCustoms jdGoodsRecordCustoms : jdGoodsRecordCustomsList) {
            CustomsGoodsItemInfoDTO oretDTO = new CustomsGoodsItemInfoDTO();
//            oretDTO.setOriginProductId(jdGoodsRecordCustoms.getSkuId());
//            oretDTO.setSkuId(jdGoodsRecordCustoms.getSkuId());
//            oretDTO.setProductId(jdGoodsRecordCustoms.getProductId());
            oretDTO.setOriginProductId(jdGoodsRecordCustoms.getItemCode());
            oretDTO.setSkuId(jdGoodsRecordCustoms.getItemCode());
            oretDTO.setProductId(jdGoodsRecordCustoms.getItemCode());
            oretDTO.setOldOrNew("new");
            oretDTO.setGoodsSeqNo("");
            oretDTO.setGoodsName(jdGoodsRecordCustoms.getItemChineseName());
            oretDTO.setDeclareCustomsGoodsSeqNo("");
            oretDTO.setRecordProductName(jdGoodsRecordCustoms.getItemChineseName());
            oretDTO.setGoodsModel(jdGoodsRecordCustoms.getPackSpecification());
            oretDTO.setNetweight(jdGoodsRecordCustoms.getJingZhong());
            oretDTO.setGrossWeight(jdGoodsRecordCustoms.getMaoZhong());
            oretDTO.setDeclareFactor("");
            oretDTO.setOriginCountry(jdGoodsRecordCustoms.getHgOriginCountry());
            oretDTO.setGoodsBar(jdGoodsRecordCustoms.getBarCode());
            oretDTO.setHsCode(jdGoodsRecordCustoms.getHs());
            oretDTO.setUnit(jdGoodsRecordCustoms.getHgMeteringUnit());
            // 2022.9.7 申报单价和币制不带出
            BigDecimal declarePrice = jdGoodsRecordCustoms.getItemSalePrice();
            if (declarePrice == null) {
                declarePrice = new BigDecimal(0);
            }
            oretDTO.setDeclarePrice(declarePrice);
            oretDTO.setDeclareUnitQfy(new BigDecimal(0));
            oretDTO.setFirstUnit(jdGoodsRecordCustoms.getLegalFirstUnit());
            oretDTO.setFirstUnitQfy(jdGoodsRecordCustoms.getFirstAmount());
            oretDTO.setSecondUnit(jdGoodsRecordCustoms.getLegalSecondUnit());
            oretDTO.setSecondUnitQfy(jdGoodsRecordCustoms.getSecondAmount());

            oretDTO.setCountryRecordNo(""); //add
            // 2022.9.7 申报单价和币制不带出
            oretDTO.setCurrency("142"); //add
            oretDTO.setProductCompany("");
            oretDTO.setOrderVersion("");//add
            oretDTO.setDestinationCountry("142");
            oretDTO.setDestinationCountryDesc("中国");
            oretDTO.setAvoidTaxMethod("3");//add
            oretDTO.setDangerousFlag(getDangerousFlag(inventoryOrderInfoDTO));
            if (Objects.nonNull(inventoryOrderInfoDTO)
                    && Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND.getCode())) {
                oretDTO.setDestinationCountry(inventoryOrderInfoDTO.getShipmentCountry());
            }
            //专用账册+美国 = 境外重点
            //专用+非美国 =普通
            //非专用账册 = 空
            if (Objects.nonNull(customsBookResVo) && Objects.equals(customsBookResVo.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {
                if (Objects.equals(jdGoodsRecordCustoms.getHgOriginCountry(), "502")) {
                    oretDTO.setGoodsSource(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode());
                } else {
                    oretDTO.setGoodsSource(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode());
                }
            }
            // 通关校验 新增不校验
//            InventoryItemVerifyParamDTO paramDTO = new InventoryItemVerifyParamDTO();
//            paramDTO.setCustomsBookId(bookId);
//            paramDTO.setProductId(oretDTO.getOriginProductId());
//            paramDTO.setCustomsRecordProductId(oretDTO.getProductId());
//            List<InventoryVerifyResult> verifyResultList = inventoryOrderInfoService.verifyInventoryItem(paramDTO);
//            List<Object> list = verifyResultList.stream().map(i -> (Object) i).collect(Collectors.toList());
//            oretDTO.setVerifyResultList(list);
            oretList.add(oretDTO);
        }
        return oretList;
    }

    private List<CustomsGoodsItemInfoDTO> buildDTO(Long bookId, List<GoodsRecordDO> listGoodsRecordDO, String oldOrNew) {
        return this.buildDTO(bookId, listGoodsRecordDO, oldOrNew, false, null);
    }

    /**
     * @param listGoodsRecordDO
     * @param oldOrNew
     * @param isAssociate       判断是否需要替换关联料号
     * @return
     */
    private List<CustomsGoodsItemInfoDTO> buildDTO(Long bookId, List<GoodsRecordDO> listGoodsRecordDO, String oldOrNew, Boolean isAssociate, Long refInventoryOrderId) {
        /**DTO查询备案信息*/
        List<CustomsGoodsItemInfoDTO> oretList = new ArrayList<>();
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(bookId);
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(refInventoryOrderId);
        for (GoodsRecordDO goodsRecordDO : listGoodsRecordDO) {
            String customsCode = customsBookResVo.getCustomsAreaCode();
            RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(goodsRecordDO.getId(), customsCode);
            log.info("备案查询口岸数据 recordCustomsDTO={}", JSON.toJSONString(recordCustomsDTO));
            CustomsGoodsItemInfoDTO oretDTO = new CustomsGoodsItemInfoDTO();
            BeanUtils.copyProperties(goodsRecordDO, oretDTO);
            oretDTO.setOriginProductId(goodsRecordDO.getProductId());
            oretDTO.setProductId(goodsRecordDO.getProductId());
            if (isAssociate) {
                String finalProductId = goodsRecordAssociateService.getFinalProductId(goodsRecordDO.getProductId(), goodsRecordDO.getId(), bookId);
                if (Objects.nonNull(finalProductId)) {
                    oretDTO.setProductId(finalProductId);
                }
            }
            oretDTO.setOldOrNew(oldOrNew);
            oretDTO.setGoodsSeqNo("");
            oretDTO.setGoodsName(goodsRecordDO.getGoodsRecordName());
            oretDTO.setDeclareCustomsGoodsSeqNo("");
            oretDTO.setRecordProductName(goodsRecordDO.getGoodsRecordName());
            oretDTO.setGoodsModel(goodsRecordDO.getModel());
            oretDTO.setGrossWeight(goodsRecordDO.getGrossWeight());
            oretDTO.setNetweight(goodsRecordDO.getNetWeight());
            oretDTO.setDeclareFactor(goodsRecordDO.getHgsbys());
            oretDTO.setOriginCountry(goodsRecordDO.getOriginCountry());
            oretDTO.setGoodsBar(oretDTO.getProductId());
            oretDTO.setHsCode(goodsRecordDO.getHsCode());
            oretDTO.setUnit(goodsRecordDO.getDeclareUnit());
            BigDecimal declarePrice = goodsRecordDO.getDeclarePrice();
            if (declarePrice == null) {
                declarePrice = new BigDecimal(0);
            }
            oretDTO.setDeclarePrice(declarePrice);
            oretDTO.setDeclareUnitQfy(new BigDecimal(0));
            oretDTO.setFirstUnit(goodsRecordDO.getFirstUnit());
            oretDTO.setFirstUnitQfy(goodsRecordDO.getFirstUnitAmount());
            oretDTO.setSecondUnit(goodsRecordDO.getSecondUnit());
            oretDTO.setSecondUnitQfy(goodsRecordDO.getSecondUnitAmount());
            oretDTO.setSkuId(goodsRecordDO.getSkuId());
            oretDTO.setCountryRecordNo(goodsRecordDO.getCountryRecordNo());
            oretDTO.setCurrency(goodsRecordDO.getDeclareCurrency());
            oretDTO.setProductCompany("");
            oretDTO.setOrderVersion("");//add
            oretDTO.setDestinationCountry("142");
            oretDTO.setDestinationCountryDesc("中国");
            oretDTO.setAvoidTaxMethod("3");//add
            oretDTO.setDangerousFlag(getDangerousFlag(inventoryOrderInfoDTO));
            if (Objects.nonNull(inventoryOrderInfoDTO) &&
                    Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND.getCode())) {
                oretDTO.setDestinationCountry(inventoryOrderInfoDTO.getShipmentCountry());
            }
            Integer goodsRecordTag = goodsRecordDO.getGoodsRecordTag();
            List<Integer> recordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordTag);
            if (Objects.equals(customsBookResVo.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {

                oretDTO.setGoodsSource(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode());
                oretDTO.setGoodsSourceDesc(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getDesc());
                if (recordTag.contains(GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getCode())) {
//                    if (Objects.equals(goodsRecordDO.getOriginCountry(), "502")) {
                    oretDTO.setGoodsSource(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode());
                    oretDTO.setGoodsSourceDesc(GoodsSourceEnums.OUTER_KEY_MATERIAL.getDesc());
//                    }
                }
            }
            if (Objects.nonNull(recordCustomsDTO)) {
                oretDTO.setOriginCountry(recordCustomsDTO.getOriginCountry());
            }

            oretList.add(oretDTO);
        }
        return oretList;
    }


    private List<CustomsGoodsItemInfoDTO> buildDTO(List<CustomsBookItemDO> listCustomsBookItemDO, String oldOrNew, Long bookId) {
        return this.buildDTO(listCustomsBookItemDO, oldOrNew, bookId, null, null);
    }

    /**
     * @param listCustomsBookItemDO
     * @param oldOrNew              新老品
     * @param bookId                账册号
     * @param productId             统一料号！！！
     * @return
     */
    private List<CustomsGoodsItemInfoDTO> buildDTO(List<CustomsBookItemDO> listCustomsBookItemDO, String oldOrNew, Long bookId, String productId, Long inventoryOrderId) {
        GoodsRecordDO goodsRecordDO = null;
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        if (!StringUtils.isEmpty(productId)) {
//            Example example = new Example(GoodsRecordDO.class);
//            Example.Criteria criteria = example.createCriteria();
//            criteria.andEqualTo("customsBookId", NumberUtils.createLong(bookId));
//            criteria.andEqualTo("productId",productId);
//            criteria.andEqualTo("deleted",false);
//            List<GoodsRecordDO> list  =  goodsRecordMapper.selectByExample(example);
            List<GoodsRecordDO> list = goodsRecordBaseService.findByBookIdAndProId(bookId, productId);
            if (!CollectionUtils.isEmpty(list)) {
                goodsRecordDO = list.get(0);
            } else if (Objects.nonNull(inventoryOrderInfoDTO)
                    && Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode())) {
                // 中转单可能没有商品备案
                List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(productId);
                if (!CollectionUtils.isEmpty(goodsRecordDTOList)) {
                    goodsRecordDO = new GoodsRecordDO();
                    BeanUtils.copyProperties(goodsRecordDTOList.get(0), goodsRecordDO);
                }
            }
        }
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(bookId);
        // 通关校验
//        List<CustomsBookItemDTO> customsBookItemDTOList = ConvertUtil.listConvert(listCustomsBookItemDO, CustomsBookItemDTO.class);
        List<CustomsGoodsItemInfoDTO> oretList = new ArrayList<CustomsGoodsItemInfoDTO>();
        for (CustomsBookItemDO customsBookItemDO : listCustomsBookItemDO) {
            CustomsGoodsItemInfoDTO oretDTO = new CustomsGoodsItemInfoDTO();
            oretDTO.setOriginProductId(customsBookItemDO.getProductId());
            if (StringUtils.isNotBlank(customsBookItemDO.getUnifiedProductId())) {
                oretDTO.setOriginProductId(customsBookItemDO.getUnifiedProductId());
            }
            oretDTO.setSkuId(customsBookItemDO.getProductId());
            oretDTO.setProductId(customsBookItemDO.getProductId());
            oretDTO.setOldOrNew(oldOrNew);
            oretDTO.setGoodsSeqNo(customsBookItemDO.getGoodsSeqNo());
            oretDTO.setNetweight(new BigDecimal(0));
            oretDTO.setOriginCountry(customsBookItemDO.getOriginCountry());
            oretDTO.setHsCode(customsBookItemDO.getHsCode());
            oretDTO.setUnit(customsBookItemDO.getGoodsUnit());
            oretDTO.setGoodsModel(customsBookItemDO.getGoodsModel());
            oretDTO.setDeclareUnitQfy(new BigDecimal(0));
            oretDTO.setFirstUnit(customsBookItemDO.getFirstUnit());
            oretDTO.setFirstUnitQfy(customsBookItemDO.getFirstUnitAmount());
            oretDTO.setSecondUnit(customsBookItemDO.getSecondUnit());
            oretDTO.setSecondUnitQfy(customsBookItemDO.getSecondUnitAmount());
            oretDTO.setGoodsName(customsBookItemDO.getGoodsName());
            oretDTO.setCountryRecordNo("");
            oretDTO.setCurrency(customsBookItemDO.getCurrCode());
            oretDTO.setDeclarePrice(customsBookItemDO.getDeclarePrice());
            oretDTO.setProductCompany("");
            oretDTO.setOrderVersion("");//add
            oretDTO.setDestinationCountry("");//add
            oretDTO.setAvoidTaxMethod("3");//add
            oretDTO.setRecordProductName(customsBookItemDO.getGoodsName());
            oretDTO.setDestinationCountry("142");
            oretDTO.setDestinationCountryDesc("中国");
            oretDTO.setDangerousFlag(getDangerousFlag(inventoryOrderInfoDTO));
            if (Objects.nonNull(inventoryOrderInfoDTO)
                    && Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND.getCode())) {
                oretDTO.setDestinationCountry(inventoryOrderInfoDTO.getShipmentCountry());
            }
            if (goodsRecordDO != null) {
                if (Objects.isNull(oretDTO.getFirstUnitQfy())) {
                    oretDTO.setFirstUnitQfy(goodsRecordDO.getFirstUnitAmount());
                }
                if (Objects.isNull(oretDTO.getSecondUnitQfy())) {
                    oretDTO.setSecondUnitQfy(goodsRecordDO.getSecondUnitAmount());
                }
                oretDTO.setGrossWeight(goodsRecordDO.getGrossWeight());
                oretDTO.setNetweight(goodsRecordDO.getNetWeight());
                oretDTO.setDeclareFactor(goodsRecordDO.getHgsbys());
                oretDTO.setCountryRecordNo(goodsRecordDO.getCountryRecordNo());
            }
            oretDTO.setGoodsBar(customsBookItemDO.getProductId());
            // 通关校验 新增不校验
//            InventoryItemVerifyParamDTO paramDTO = new InventoryItemVerifyParamDTO();
//            paramDTO.setCustomsBookItemDTOList(customsBookItemDTOList);
//            paramDTO.setCustomsBookId(Long.valueOf(bookId));
//            paramDTO.setRefInvOrderId(inventoryOrderId);
//            paramDTO.setProductId(oretDTO.getOriginProductId());
//            paramDTO.setGoodsRecordDTO(goodsRecordDTO);
//            paramDTO.setCustomsRecordProductId(customsBookItemDO.getProductId());
//            List<InventoryVerifyResult> verifyResultList = inventoryOrderInfoService.verifyInventoryItem(paramDTO);
//            List<Object> list = verifyResultList.stream().map(i -> (Object) i).collect(Collectors.toList());
//            oretDTO.setVerifyResultList(list);
            if (Objects.equals(customsBookResVo.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {
                String goodsSource = customsBookItemDO.getGoodsSource();
                if (Objects.nonNull(goodsSource)) {
                    GoodsSourceEnums goodsSourceEnums = GoodsSourceEnums.getEnums(goodsSource);
                    if (Objects.nonNull(goodsSourceEnums)) {
                        oretDTO.setGoodsSource(goodsSourceEnums.getCode());
                        oretDTO.setGoodsSourceDesc(goodsSourceEnums.getDesc());
                    }
                }
            }
            oretList.add(oretDTO);
        }
        return oretList;
    }

    /**
     * 获取是否危化品标志
     *
     * @param inventoryOrderInfoDTO
     * @return 危险品标志 默认- 0：否
     */
    private String getDangerousFlag(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        List<String> nonDangerFlagBusinessType = Arrays.asList(
                InventoryOrderBusinessEnum.BUSSINESS_EMPTY.getCode(),
                InventoryOrderBusinessEnum.BUSINESS_INVENTORY_PROFIT.getCode(),
                InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode());
        if (Objects.isNull(inventoryOrderInfoDTO)
                || nonDangerFlagBusinessType.contains(inventoryOrderInfoDTO.getInveBusinessType())) {
            // 是否危化品标志 - 置空
            return null;
        }
        return "0";
    }
}
