package com.danding.cds.item.mq.canal;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.cds.item.api.dto.CustomsBookItemCanalDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummaryDTO;
import com.danding.cds.itemstocklist.api.service.ItemStockSummaryService;
import com.dt.component.canal.mq.AbstractCanalMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.junit.jupiter.api.Test;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 账册库存-canal监听
 * @date 2023/8/1 09:50
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "danding_ccs_customs_book_item_topic",
        consumerGroup = "ccs_customs_book_item_consumer")
public class CustomsBookItemCanalConsumer extends AbstractCanalMQService<CustomsBookItemCanalDTO> implements RocketMQListener<FlatMessage>, ApplicationRunner {

    @DubboReference
    private ItemStockSummaryService itemStockSummaryService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @Override
    protected void insert(CustomsBookItemCanalDTO customsBookItemCanalDTO) {
        try {
            if (Objects.nonNull(customsBookItemCanalDTO) && Objects.nonNull(customsBookItemCanalDTO.getId()) && Objects.nonNull(customsBookItemCanalDTO.getAccountNum())) {
                ItemStockSummaryDTO itemStockSummaryDTO = new ItemStockSummaryDTO();
                itemStockSummaryDTO.setCustomsBookItemId(customsBookItemCanalDTO.getId());
                itemStockSummaryDTO.setAccountQty(customsBookItemCanalDTO.getAccountNum());
                itemStockSummaryService.updateAccountQty(itemStockSummaryDTO);
            }

            customsBookItemService.updateGoodsSourceTokeyMaterialById(customsBookItemCanalDTO.getId());
        } catch (Exception e) {
            log.error("CustomsBookItemCanalConsumer - insert CustomsBookItemId={} error: " + e.getMessage(), customsBookItemCanalDTO.getId(), e);
        }
    }

    @Override
    protected void update(CustomsBookItemCanalDTO before, CustomsBookItemCanalDTO after) {
        try {
            if (Objects.nonNull(after) && Objects.nonNull(after.getId()) && Objects.nonNull(after.getAccountNum())) {
                ItemStockSummaryDTO itemStockSummaryDTO = new ItemStockSummaryDTO();
                itemStockSummaryDTO.setCustomsBookItemId(after.getId());
                itemStockSummaryDTO.setAccountQty(after.getAccountNum());
                itemStockSummaryService.updateAccountQty(itemStockSummaryDTO);
            }
            customsBookItemService.updateGoodsSourceTokeyMaterialById(after.getId());

        } catch (Exception e) {
            log.error("CustomsBookItemCanalConsumer - update CustomsBookItemId={} error: " + e.getMessage(), after.getId(), e);
        }
    }

    @Override
    protected void delete(CustomsBookItemCanalDTO customsBookItemCanalDTO) {
        try {
            if (Objects.nonNull(customsBookItemCanalDTO) && Objects.nonNull(customsBookItemCanalDTO.getId()) && Objects.nonNull(customsBookItemCanalDTO.getAccountNum())) {
                ItemStockSummaryDTO itemStockSummaryDTO = new ItemStockSummaryDTO();
                itemStockSummaryDTO.setCustomsBookItemId(customsBookItemCanalDTO.getId());
                itemStockSummaryDTO.setAccountQty(customsBookItemCanalDTO.getAccountNum());
                itemStockSummaryService.updateAccountQty(itemStockSummaryDTO);
            }
        } catch (Exception e) {
            log.error("CustomsBookItemCanalConsumer - delete CustomsBookItemId={} error: " + e.getMessage(), customsBookItemCanalDTO.getId(), e);
        }
    }

    @Override
    public void onMessage(FlatMessage flatMessage) {
        process(flatMessage);
    }


    @Test
    public void test11() {
        String json = "{\n" +
                "    \"data\":[\n" +
                "        {\n" +
                "            \"id\":\"32537\",\n" +
                "            \"goods_seq_no\":\"1003718220919000011\",\n" +
                "            \"product_id\":\"100371822091900001\",\n" +
                "            \"product_stock_id\":null,\n" +
                "            \"unified_product_id\":null,\n" +
                "            \"hs_code\":\"**********\",\n" +
                "            \"goods_name\":\"结转28\",\n" +
                "            \"curr_code\":\"502\",\n" +
                "            \"declare_price\":\"92.8100\",\n" +
                "            \"goods_model\":\"230ml/瓶\",\n" +
                "            \"origin_country\":\"116\",\n" +
                "            \"goods_unit\":\"123\",\n" +
                "            \"first_unit\":\"123\",\n" +
                "            \"first_unit_amount\":\"123.0\",\n" +
                "            \"second_unit\":\"123\",\n" +
                "            \"second_unit_amount\":\"123.0\",\n" +
                "            \"customs_book_id\":\"22\",\n" +
                "            \"invt_goods_no\":\"32\",\n" +
                "            \"invt_no\":\"HZ2305071745010188\",\n" +
                "            \"in_qty\":\"100\",\n" +
                "            \"ongoing_num\":null,\n" +
                "            \"locked_num\":null,\n" +
                "            \"occupied_num\":\"0\",\n" +
                "            \"used_num\":\"10\",\n" +
                "            \"available_num\":\"10\",\n" +
                "            \"in_legal_qty\":\"2.0700\",\n" +
                "            \"in_second_legal_qty\":\"100.0\",\n" +
                "            \"in_date\":\"2023-08-01 13:04:48\",\n" +
                "            \"avg_price\":null,\n" +
                "            \"total_amt\":null,\n" +
                "            \"enable\":\"1\",\n" +
                "            \"remark\":\"\",\n" +
                "            \"push_status\":null,\n" +
                "            \"push_msg\":null,\n" +
                "            \"push_msg_id\":null,\n" +
                "            \"create_by\":\"0\",\n" +
                "            \"update_by\":\"100418\",\n" +
                "            \"create_time\":\"2023-06-05 15:00:48\",\n" +
                "            \"update_time\":\"2023-08-01 13:04:49\",\n" +
                "            \"deleted\":\"0\",\n" +
                "            \"account_num\":\"10\",\n" +
                "            \"tenantry_id\":\"1001\",\n" +
                "            \"total_in_qty\":\"20\",\n" +
                "            \"total_out_qty\":\"10\",\n" +
                "            \"total_diff_qty\":\"10\",\n" +
                "            \"diff_type\":\"Equal\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"database\":\"ccs_item\",\n" +
                "    \"es\":*************,\n" +
                "    \"id\":1178,\n" +
                "    \"isDdl\":false,\n" +
                "    \"mysqlType\":{\n" +
                "        \"id\":\"bigint(20) unsigned\",\n" +
                "        \"goods_seq_no\":\"varchar(50)\",\n" +
                "        \"product_id\":\"varchar(50)\",\n" +
                "        \"product_stock_id\":\"bigint(20)\",\n" +
                "        \"unified_product_id\":\"varchar(64)\",\n" +
                "        \"hs_code\":\"varchar(20)\",\n" +
                "        \"goods_name\":\"varchar(100)\",\n" +
                "        \"curr_code\":\"varchar(10)\",\n" +
                "        \"declare_price\":\"decimal(12,4)\",\n" +
                "        \"goods_model\":\"varchar(255)\",\n" +
                "        \"origin_country\":\"varchar(50)\",\n" +
                "        \"goods_unit\":\"varchar(10)\",\n" +
                "        \"first_unit\":\"varchar(10)\",\n" +
                "        \"first_unit_amount\":\"decimal(12,5)\",\n" +
                "        \"second_unit\":\"varchar(10)\",\n" +
                "        \"second_unit_amount\":\"decimal(12,5)\",\n" +
                "        \"customs_book_id\":\"int(11)\",\n" +
                "        \"invt_goods_no\":\"varchar(50)\",\n" +
                "        \"invt_no\":\"varchar(50)\",\n" +
                "        \"in_qty\":\"int(11)\",\n" +
                "        \"ongoing_num\":\"int(11)\",\n" +
                "        \"locked_num\":\"int(11)\",\n" +
                "        \"occupied_num\":\"int(11)\",\n" +
                "        \"used_num\":\"int(11)\",\n" +
                "        \"available_num\":\"int(11)\",\n" +
                "        \"in_legal_qty\":\"decimal(12,4)\",\n" +
                "        \"in_second_legal_qty\":\"decimal(12,4)\",\n" +
                "        \"in_date\":\"timestamp\",\n" +
                "        \"avg_price\":\"decimal(12,4)\",\n" +
                "        \"total_amt\":\"decimal(12,4)\",\n" +
                "        \"enable\":\"tinyint(3)\",\n" +
                "        \"remark\":\"varchar(512)\",\n" +
                "        \"push_status\":\"tinyint(255)\",\n" +
                "        \"push_msg\":\"varchar(255)\",\n" +
                "        \"push_msg_id\":\"varchar(50)\",\n" +
                "        \"create_by\":\"bigint(20)\",\n" +
                "        \"update_by\":\"bigint(20)\",\n" +
                "        \"create_time\":\"timestamp\",\n" +
                "        \"update_time\":\"timestamp\",\n" +
                "        \"deleted\":\"tinyint(1)\",\n" +
                "        \"account_num\":\"int(11)\",\n" +
                "        \"tenantry_id\":\"bigint(20)\",\n" +
                "        \"total_in_qty\":\"int(11)\",\n" +
                "        \"total_out_qty\":\"int(11)\",\n" +
                "        \"total_diff_qty\":\"int(11)\",\n" +
                "        \"diff_type\":\"varchar(30)\"\n" +
                "    },\n" +
                "    \"old\":[\n" +
                "        {\n" +
                "            \"in_date\":\"2023-08-01 13:04:47\",\n" +
                "            \"update_time\":\"2023-08-01 13:04:47\",\n" +
                "            \"diff_type\":\"GreaterThan\"\n" +
                "        }\n" +
                "    ],\n" +
                "    \"pkNames\":[\n" +
                "        \"id\"\n" +
                "    ],\n" +
                "    \"sql\":\"\",\n" +
                "    \"sqlType\":{\n" +
                "        \"id\":-5,\n" +
                "        \"goods_seq_no\":12,\n" +
                "        \"product_id\":12,\n" +
                "        \"product_stock_id\":-5,\n" +
                "        \"unified_product_id\":12,\n" +
                "        \"hs_code\":12,\n" +
                "        \"goods_name\":12,\n" +
                "        \"curr_code\":12,\n" +
                "        \"declare_price\":3,\n" +
                "        \"goods_model\":12,\n" +
                "        \"origin_country\":12,\n" +
                "        \"goods_unit\":12,\n" +
                "        \"first_unit\":12,\n" +
                "        \"first_unit_amount\":3,\n" +
                "        \"second_unit\":12,\n" +
                "        \"second_unit_amount\":3,\n" +
                "        \"customs_book_id\":4,\n" +
                "        \"invt_goods_no\":12,\n" +
                "        \"invt_no\":12,\n" +
                "        \"in_qty\":4,\n" +
                "        \"ongoing_num\":4,\n" +
                "        \"locked_num\":4,\n" +
                "        \"occupied_num\":4,\n" +
                "        \"used_num\":4,\n" +
                "        \"available_num\":4,\n" +
                "        \"in_legal_qty\":3,\n" +
                "        \"in_second_legal_qty\":3,\n" +
                "        \"in_date\":93,\n" +
                "        \"avg_price\":3,\n" +
                "        \"total_amt\":3,\n" +
                "        \"enable\":-6,\n" +
                "        \"remark\":12,\n" +
                "        \"push_status\":-6,\n" +
                "        \"push_msg\":12,\n" +
                "        \"push_msg_id\":12,\n" +
                "        \"create_by\":-5,\n" +
                "        \"update_by\":-5,\n" +
                "        \"create_time\":93,\n" +
                "        \"update_time\":93,\n" +
                "        \"deleted\":-6,\n" +
                "        \"account_num\":4,\n" +
                "        \"tenantry_id\":-5,\n" +
                "        \"total_in_qty\":4,\n" +
                "        \"total_out_qty\":4,\n" +
                "        \"total_diff_qty\":4,\n" +
                "        \"diff_type\":12\n" +
                "    },\n" +
                "    \"table\":\"ccs_customs_book_item\",\n" +
                "    \"ts\":*************,\n" +
                "    \"type\":\"UPDATE\"\n" +
                "}";
        FlatMessage flatMessage = JSON.parseObject(json, FlatMessage.class);
        process(flatMessage);

    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String name = this.getClass().getName();
        log.info("MessageHandlerAfterInit-run class:{}", name);
    }
}

