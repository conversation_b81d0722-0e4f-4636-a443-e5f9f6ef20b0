package com.danding.cds.v2.bean.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * AI调用请求对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Accessors(chain = true)
public class AiCallRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 是否流式返回
     */
    private Boolean stream = false;

    /**
     * 是否返回详细信息
     */
    private Boolean detail = false;

    /**
     * 消息列表
     */
    private List<AiMessage> messages;

    /**
     * 构造方法
     */
    public AiCallRequest() {
    }

    /**
     * 构造方法
     *
     * @param messages 消息列表
     */
    public AiCallRequest(List<AiMessage> messages) {
        this.messages = messages;
    }

    /**
     * 构造方法
     *
     * @param stream   是否流式返回
     * @param detail   是否返回详细信息
     * @param messages 消息列表
     */
    public AiCallRequest(Boolean stream, Boolean detail, List<AiMessage> messages) {
        this.stream = stream;
        this.detail = detail;
        this.messages = messages;
    }
}
