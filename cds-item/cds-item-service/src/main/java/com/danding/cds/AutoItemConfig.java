package com.danding.cds;

import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.config.SpecialConfig;
import com.danding.cds.declare.sdk.listener.CustomsListener;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@SpringBootConfiguration
public class AutoItemConfig {

    public static String customsBookItemTrackLogIndex = "ccs_item_track_log_index";
    public static String goodsRecordTrackLogIndex = "ccs_record_track_log_index";

    @Bean
    public String getItemTrackLogIndex() {
        return customsBookItemTrackLogIndex + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    @Bean
    public String getRecordTrackLogIndex() {
        return goodsRecordTrackLogIndex + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    //    @Bean
    public CustomsSupport customsSupport(SpecialConfig specialConfig, MessageSender messageSender) {
        if (EnvironmentConfig.isOnline()) {
            CustomsSupport customsSupport = new CustomsSupport(new CustomsListener() {
                @Override
                public void accept(CustomsReport report) {
                    super.accept(report);
                }
            }, CustomsSupport.ENV_ONLINE, specialConfig, messageSender);
            return customsSupport;
        } else {
            CustomsSupport customsSupport = new CustomsSupport(new CustomsListener() {
                @Override
                public void accept(CustomsReport report) {
                    super.accept(report);
                }
            }, CustomsSupport.ENV_MOCK, specialConfig, messageSender);
            return customsSupport;
        }
    }

    @Bean(name = "goodsRecordCountTaskExecutor")
    public ThreadPoolTaskExecutor goodsRecordCountTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("goodsRecordCountTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "aiRecommendHsCodeTaskExecutor")
    public ThreadPoolTaskExecutor aiRecommendHsCodeTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("aiRecommendHsCodeTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
