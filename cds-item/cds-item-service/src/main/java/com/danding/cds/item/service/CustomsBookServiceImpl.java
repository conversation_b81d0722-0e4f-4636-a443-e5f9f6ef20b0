package com.danding.cds.item.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookSearchCondition;
import com.danding.cds.item.api.dto.CustomsBookSubmit;
import com.danding.cds.item.api.dto.param.CustomsBookConfigParam;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookInfoResVo;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.item.entity.CustomsBookDO;
import com.danding.cds.item.mapper.CustomsBookMapper;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.service.ItemBaseDataSyncService;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: shenhui
 * @Date: 2020/4/28 10:38
 */
@DubboService
public class CustomsBookServiceImpl implements CustomsBookService {

    @Autowired
    private CustomsBookMapper customsBookMapper;

    @Autowired
    private Validator validator;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private RouteService routeService;
    @Autowired
    private ItemBaseDataSyncService baseDataSyncService;

    @Override
    @PageSelect
    public ListVO<CustomsBookResVo> newPaging(CustomsBookSearchCondition condition) {
        Example example = new Example(CustomsBookDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(condition.getCustomsBookId())) {
            criteria.andEqualTo("id", condition.getCustomsBookId());
        }
        if (!StringUtils.isEmpty(condition.getAreaCompanyId())) {
            criteria.andEqualTo("areaCompanyId", condition.getAreaCompanyId());
        }
        if (!StringUtils.isEmpty(condition.getCustomsDistrictCode())) {
            criteria.andEqualTo("customsDistrictCode", condition.getCustomsDistrictCode());
        }
        if (!StringUtils.isEmpty(condition.getBookType())) {
            criteria.andEqualTo("bookType", condition.getBookType());
        }
        if (Objects.nonNull(condition.getBookTag())) {
            criteria.andCondition("book_tag & " + condition.getBookTag() + " = " + condition.getBookTag());
        }
        //排序
        example.setOrderByClause("create_time DESC");
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        ListVO<CustomsBookResVo> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CustomsBookResVo.class));
        // 分页
        PageInfo<CustomsBookDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @PageSelect
    public ListVO<CustomsBookDTO> paging(CustomsBookSearchCondition condition) {
        Example example = new Example(CustomsBookDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(condition.getCustomsBookId())) {
            criteria.andEqualTo("id", condition.getCustomsBookId());
        }
        if (!StringUtils.isEmpty(condition.getAreaCompanyId())) {
            criteria.andEqualTo("areaCompanyId", condition.getAreaCompanyId());
        }
        if (!StringUtils.isEmpty(condition.getCustomsDistrictCode())) {
            criteria.andEqualTo("customsDistrictCode", condition.getCustomsDistrictCode());
        }
        //排序
        example.setOrderByClause("create_time DESC");
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        ListVO<CustomsBookDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CustomsBookDTO.class));
        // 分页
        PageInfo<CustomsBookDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<CustomsBookDTO> listAllBooks() {
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectAll()), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllBooksByType(Integer bookType) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria().andEqualTo("deleted", false);
        if (Objects.nonNull(bookType)) {
            criteria.andEqualTo("bookType", bookType);
        }
        List<CustomsBookDO> customsBookDOS = customsBookMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsBookDOS, CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllBooksAuth() {
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectAll()), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllBooksAuth(List<Long> accountBookList) {
        if (!CollectionUtils.isEmpty(accountBookList)) {
            Example example = new Example(CustomsBookDO.class);
            example.createCriteria().andIn("id", accountBookList);
            return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
        }
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectAll()), CustomsBookDTO.class);
    }


    /**
     * 通过账册列表获取可用账册
     *
     * @param accountBookList
     * @return
     */
    @Override
    public List<CustomsBookDTO> listBooksByIds(List<Long> accountBookList) {
        if (CollectionUtils.isEmpty(accountBookList)) {
            return Collections.emptyList();
        }
        Example example = new Example(CustomsBookDO.class);
        example.createCriteria().andIn("id", accountBookList);
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllInUseBooks() {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", 1);
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllInUseBooks(Integer bookType) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("enable", 1);
        criteria.andEqualTo("deleted", false);
        if (Objects.nonNull(bookType)) {
            criteria.andEqualTo("bookType", bookType);
        }
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllInUseBooksAuth(List<Long> accountBookList) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(accountBookList)) {
            criteria.andIn("id", accountBookList);
        }
        criteria.andEqualTo("enable", 1);
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
    }

    @Override
    public List<CustomsBookDTO> listAllInUseBooksAuth(List<Long> accountBookList, Integer bookType) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(accountBookList)) {
            criteria.andIn("id", accountBookList);
        }
        if (Objects.nonNull(bookType)) {
            criteria.andEqualTo("bookType", bookType);
        }
        criteria.andEqualTo("enable", 1);
        return JSON.parseArray(JSON.toJSONString(customsBookMapper.selectByExample(example)), CustomsBookDTO.class);
    }

    @Override
//    @Cacheable(value = "CustomsBookWithId", key = "#id", unless = "#result==null")
    public CustomsBookDTO findById(Long id) {
        if (id == 0) {
            return null;
        } else {
            CustomsBookDO customsBookDO = customsBookMapper.selectByPrimaryKey(id);
            if (customsBookDO == null) {
                return null;
            } else {
                CustomsBookDTO customsBookDTO = new CustomsBookDTO();
                BeanUtils.copyProperties(customsBookDO, customsBookDTO);
                return customsBookDTO;
            }
        }
    }

    @Override
    public CustomsBookResVo findByIdV2(Long id) {
        if (id == null || id == 0) {
            return null;
        } else {
            CustomsBookDO customsBookDO = customsBookMapper.selectByPrimaryKey(id);
            if (customsBookDO == null) {
                return null;
            } else {
                CustomsBookResVo bookResVo = new CustomsBookResVo();
                BeanUtils.copyProperties(customsBookDO, bookResVo);
                List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(customsBookDO.getBookTag());
                bookResVo.setBookTagList(bookTagList);
                return bookResVo;
            }
        }
    }

    @Override
    public List<CustomsBookResVo> findByIdV2(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", false);
        List<CustomsBookDO> customsBookDOS = customsBookMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsBookDOS, CustomsBookResVo.class);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = "CustomsBookWithBookNo", key = "#submit.getBookNo()")
            , @CacheEvict(value = "CustomsBookWithId", key = "#submit.getId()")
    })
    public Long update(CustomsBookSubmit submit) throws ArgsErrorException {
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (null != inputError) {
            throw new ArgsErrorException(inputError);
        }
        CustomsBookDTO old = this.findByCode(submit.getBookNo());
        if (old != null && (submit.getId() == null || submit.getId() == 0 || !submit.getId().equals(old.getId()))) {
            throw new ArgsErrorException("账册编码" + submit.getBookNo() + "已存在！");
        }

        if ((submit.getId() == null || submit.getId() == 0) || (submit.getEnable() != null && submit.getEnable() == 1)) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(submit.getAreaCompanyId());
            if (companyDTO.getEnable() != 1) {
                throw new ArgsErrorException(companyDTO.getName() + "企业未启用，请启用后再试");
            }
        }
        CustomsBookDO customsBookDO = new CustomsBookDO();
        BeanUtils.copyProperties(submit, customsBookDO);
        if (submit.getId() == null || submit.getId() == 0) {//新增
            customsBookDO.setId(null);
            customsBookMapper.insertSelective(customsBookDO);

            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.CUSTOMS_BOOK, BaseDataSyncTypeEnums.INSERT, customsBookDO);
        } else {//更新
            if (submit.getEnable() != null && submit.getEnable() == 0) {
                List<RouteDTO> list = routeService.findByBookId(submit.getId());
                if (!CollectionUtils.isEmpty(list)) {
                    String routeNames = list.stream().map(s -> s.getName()).collect(Collectors.joining(","));
                    throw new ArgsErrorException("该账册已被[" + routeNames + "]申报路径使用,禁用该账册之前需禁用相关路径");
                }
            }
            customsBookMapper.updateByPrimaryKeySelective(customsBookDO);

            CustomsBookDO syncData = customsBookMapper.selectByPrimaryKey(customsBookDO.getId());
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.CUSTOMS_BOOK, BaseDataSyncTypeEnums.UPDATE, syncData);
        }
        return customsBookDO.getId();
    }

    @Override
    public void updConfigParam(CustomsBookConfigParam param) {
        if (StringUtils.isEmpty(param.getId())) {
            throw new ArgsInvalidException("更新账册id为空");
        }
        CustomsBookDO customsBookDO = customsBookMapper.selectByPrimaryKey(param.getId());
        if (Objects.nonNull(param.getStorageAttr())) {
            customsBookDO.setStorageAttr(param.getStorageAttr());
        }
        if (Objects.nonNull(param.getStockListGenerateEnable())) {
            customsBookDO.setStockListGenerateEnable(param.getStockListGenerateEnable());
        }
        customsBookMapper.updateByPrimaryKeySelective(customsBookDO);
        CustomsBookDO syncData = customsBookMapper.selectByPrimaryKey(customsBookDO.getId());
        baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.CUSTOMS_BOOK, BaseDataSyncTypeEnums.UPDATE, syncData);
    }

    @Override
    public void updateBookTag(CustomsBookConfigParam param) {
        CustomsBookDO customsBookDO = customsBookMapper.selectByPrimaryKey(param.getId());
        List<Integer> bookTagList = param.getBookTagList();
        if (bookTagList.contains(CustomsBookTagEnums.CARRY_OVER_ENABLE.getCode())) {
            Long areaCompanyId = customsBookDO.getAreaCompanyId();
            CustomsBookDO queryCompanyDO = new CustomsBookDO();
            queryCompanyDO.setAreaCompanyId(areaCompanyId);
//            queryCompanyDO.setCarryOverEnable(1);
            List<CustomsBookDO> customsBookDOList = customsBookMapper.select(queryCompanyDO);
            customsBookDOList = customsBookDOList.stream().filter(c -> !Objects.equals(c.getId(), param.getId())
                            && CustomsBookTagEnums.getBookTag(c.getBookTag()).contains(CustomsBookTagEnums.CARRY_OVER_ENABLE.getCode()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customsBookDOList)) {
                throw new ArgsInvalidException("该企业已存在结转账册");
            }
        }
        int bookTag = bookTagList.stream().filter(CustomsBookTagEnums::checkData).mapToInt(Integer::intValue).sum();
        // 若账册属于进口-保税账册（专用）且标签属于自动切换专用账册，则报错“专用账册无法选中自动切换专用账册标签”
        if (Objects.equals(customsBookDO.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())
                && CustomsBookTagEnums.contains(bookTag, CustomsBookTagEnums.AUTO_SWITCH_SPECIAL_BOOKS)) {
            throw new ArgsInvalidException("专用账册无法选中自动切换专用账册标签");
        }
        customsBookDO.setBookTag(bookTag);
        if (Objects.nonNull(param.getStockListGenerateEnable())) {
            customsBookDO.setStockListGenerateEnable(param.getStockListGenerateEnable());
        }
        customsBookMapper.updateByPrimaryKeySelective(customsBookDO);
        CustomsBookDO syncData = customsBookMapper.selectByPrimaryKey(customsBookDO.getId());
        baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.CUSTOMS_BOOK, BaseDataSyncTypeEnums.UPDATE, syncData);
    }

    @Override
//    @Cacheable(value = "CustomsBookWithBookNo", key = "#bookNo", unless = "#result==null")
    public CustomsBookDTO findByCode(String bookNo) {
        if (Objects.isNull(bookNo)) {
            return null;
        }
        CustomsBookDO customsBookDO = new CustomsBookDO();
        customsBookDO.setBookNo(bookNo);
        customsBookDO = customsBookMapper.selectOne(customsBookDO);
        if (customsBookDO == null) {
            return null;
        } else {
            CustomsBookDTO customsBookDTO = new CustomsBookDTO();
            BeanUtils.copyProperties(customsBookDO, customsBookDTO);
            return customsBookDTO;
        }
    }

    /**
     * 根据账册编号列表查询
     */
    @Override
    public List<CustomsBookDTO> findByCodeList(List<String> bookNoList) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", 0);
        criteria.andIn("bookNo", bookNoList);
        List<CustomsBookDO> bookDTOList = customsBookMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(bookDTOList)) {
            return new ArrayList<>();
        }
        return bookDTOList.stream()
                .map(z -> {
                    CustomsBookDTO customsBookDTO = new CustomsBookDTO();
                    BeanUtils.copyProperties(z, customsBookDTO);
                    return customsBookDTO;
                }).collect(Collectors.toList());
    }

    public List<CustomsBookDTO> findListByCompanyId(Long companyId) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("areaCompanyId", companyId);
        criteria.andEqualTo("enable", true);
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        if (list == null) list = new ArrayList<CustomsBookDO>();
        return list.stream().map(s -> {
            CustomsBookDTO customsBookDTO = new CustomsBookDTO();
            BeanUtils.copyProperties(s, customsBookDTO);
            return customsBookDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<CustomsBookDTO> findBookByDistrictCode(String customsDistrictCode) {
        return this.findBookByDistrictCode(Arrays.asList(customsDistrictCode));
    }

    @Override
    public List<CustomsBookDTO> findBookByDistrictCode(List<String> customsDistrictCodeList) {
        if (CollectionUtils.isEmpty(customsDistrictCodeList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("customsDistrictCode", customsDistrictCodeList);
        criteria.andEqualTo("deleted", false);
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        return ConvertUtil.listConvert(list, CustomsBookDTO.class);
    }

    /**
     * 根据口岸查询账册
     *
     * @param customsDistrictCode
     * @return
     */
    @Override
    public List<CustomsBookInfoResVo> findBookList(String customsDistrictCode) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsDistrictCode", customsDistrictCode);
        criteria.andEqualTo("enable", true);
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        List<CustomsBookInfoResVo> resVos = new ArrayList<>();
        for (CustomsBookDO bookDO : list) {
            CustomsBookInfoResVo resVo = BeanUtil.copyProperties(bookDO, CustomsBookInfoResVo.class);
            resVo.setName(bookDO.getBookNo());
            resVo.setId(bookDO.getId());
            resVo.setCustomsDistrictCode(bookDO.getCustomsDistrictCode());
            resVo.setCustomsDistrictName(CustomsDistrictEnum.getEnum(bookDO.getCustomsDistrictCode()).getDesc());
            resVos.add(resVo);
        }
        return resVos;
    }

    @Override
    public List<CustomsBookInfoResVo> findBookList(String customsDistrictCode, Integer bookType) {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsDistrictCode", customsDistrictCode);
        criteria.andEqualTo("enable", true);
        criteria.andEqualTo("bookType", bookType);
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        List<CustomsBookInfoResVo> resVos = new ArrayList<>();
        for (CustomsBookDO bookDO : list) {
            CustomsBookInfoResVo resVo = BeanUtil.copyProperties(bookDO, CustomsBookInfoResVo.class);
            resVo.setName(bookDO.getBookNo());
            resVo.setId(bookDO.getId());
            resVo.setCustomsDistrictCode(bookDO.getCustomsDistrictCode());
            resVo.setCustomsDistrictName(CustomsDistrictEnum.getEnum(bookDO.getCustomsDistrictCode()).getDesc());
            resVos.add(resVo);
        }
        return resVos;
    }

    /**
     * 根据区内企业查询账册信息
     *
     * @param areaCompanyId
     * @return
     */
    @Override
    public List<CustomsBookResVo> findByAreaCompanyId(Long areaCompanyId) {
        if (Objects.isNull(areaCompanyId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("areaCompanyId", areaCompanyId)
                .andEqualTo("deleted", false)
                .andIsNotNull("customsAreaCode");
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        List<CustomsBookResVo> customsBookResVos = ConvertUtil.listConvert(list, CustomsBookResVo.class);
        return customsBookResVos;
    }

    @Override
    public List<CustomsBookResVo> findByAreaCompanyId(List<Long> areaCompanyId) {
        if (CollUtil.isEmpty(areaCompanyId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("areaCompanyId", areaCompanyId)
                .andEqualTo("deleted", false)
                .andEqualTo("enable", 1)
                .andIsNotNull("customsAreaCode");
        List<CustomsBookDO> list = customsBookMapper.selectByExample(example);
        return ConvertUtil.listConvert(list, CustomsBookResVo.class);
    }

    @Override
    public void enable(List<Long> idList, Integer enable) {
        if (Objects.isNull(idList)) {
            return;
        }
        if (enable != 1 && enable != 0) {
            return;
        }
        CustomsBookDO customsBookDO = new CustomsBookDO();
        customsBookDO.setEnable(enable);
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", idList)
                .andNotEqualTo("enable", enable);
        customsBookMapper.updateByExampleSelective(customsBookDO, example);
        idList.forEach(i -> {
            CustomsBookDO syncData = customsBookMapper.selectByPrimaryKey(i);
            baseDataSyncService.syncDataMQSend(BaseDataSyncTypeEnums.CUSTOMS_BOOK, BaseDataSyncTypeEnums.UPDATE, syncData);
        });
    }

    @Override
    public List<CustomsBookResVo> listByStorageAttr(Integer storageAttr) {
        if (Objects.isNull(storageAttr)) {
            return new ArrayList<>();
        }
        CustomsBookDO customsBookDO = new CustomsBookDO();
        customsBookDO.setStorageAttr(storageAttr);
        return ConvertUtil.listConvert(customsBookMapper.select(customsBookDO), CustomsBookResVo.class);
    }

    @Override
    public CustomsBookResVo findCarryOverBookById(Long companyId) {
        if (Objects.isNull(companyId)) {
            return null;
        }
        CustomsBookDO customsBookDO = new CustomsBookDO();
        customsBookDO.setAreaCompanyId(companyId);
        List<CustomsBookDO> customsBookDOList = customsBookMapper.select(customsBookDO);
        customsBookDOList =customsBookDOList.stream().filter(c->{
            List<Integer> bookTag = CustomsBookTagEnums.getBookTag(c.getBookTag());
            return bookTag.contains(CustomsBookTagEnums.CARRY_OVER_ENABLE.getCode());
        }).collect(Collectors.toList());
        if (customsBookDOList.isEmpty()) {
            throw new ArgsInvalidException("该企业不存在结转账册");
        } else if (customsBookDOList.size() > 1) {
            throw new ArgsInvalidException("同企业存在多本结转账册");
        }
        CustomsBookDO bookDO = customsBookDOList.get(0);
        return ConvertUtil.beanConvert(bookDO, CustomsBookResVo.class);
    }

    @Override
    public List<CustomsBookDTO> findAllByCancelOrderNonLimit() {
        Example example = new Example(CustomsBookDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        criteria.andCondition("book_tag & " + CustomsBookTagEnums.CANCEL_ORDER_NON_LIMIT.getCode() + "!="
                + CustomsBookTagEnums.CANCEL_ORDER_NON_LIMIT.getCode());
        List<CustomsBookDO> customsBookDOS = customsBookMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsBookDOS, CustomsBookDTO.class);
    }
}
