package com.danding.cds.item.mq;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.entity.GoodsRecordDO;
import com.danding.cds.item.mapper.GoodsRecordMapper;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeRefreshDTO;
import com.danding.cds.v2.bean.dto.GoodsRecordEsDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 商品备案es同步topic
 * @date 2022/7/23 16:31
 */
@Slf4j
@Component
@RocketMQMessageListener(
        topic = "ccs-goods-record-dump-topic",
        consumerGroup = "ccs-goods-record-dump-consumer"
)
public class GoodsRecordSyncEsReceiver extends MessageHandlerAfterInit {
    @Autowired
    private GoodsRecordSaveToESConsumer goodsRecordSaveToESConsumer;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @Autowired
    private GoodsRecordMapper goodsRecordMapper;

    @Autowired
    private MessageSender messageSender;

    @Override
    public void handle(Object o) throws RuntimeException {
        Long recordId = (Long) o;
        if (Objects.isNull(recordId)) {
            return;
        }
        GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(recordId);
        GoodsRecordEsDTO goodsRecordEsDTO = BeanUtil.copyProperties(goodsRecordDO, GoodsRecordEsDTO.class);
        Long tenantId = SimpleTenantHelper.getTenantId();
        if (Objects.isNull(tenantId)) {
            log.warn("GoodsRecordSyncEsReceiver 未取到租户id 先用默认值 id={}", recordId);
            goodsRecordEsDTO.setTenantryId(1001L);
        } else {
            goodsRecordEsDTO.setTenantryId(tenantId);
        }
        goodsRecordSaveToESConsumer.initGoodsRecordToEs(goodsRecordEsDTO);
        // 刷新备案AI推荐数据
        AiRecommendHsCodeRefreshDTO refreshDTO = new AiRecommendHsCodeRefreshDTO();
        refreshDTO.setRecordId(recordId);
        messageSender.sendMsg(JSON.toJSONString(refreshDTO), "ccs-ai-recommend-hsCode-refresh-topic");
    }
}
