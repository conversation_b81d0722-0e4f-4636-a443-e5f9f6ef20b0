package com.danding.cds.v2.service.base;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.bean.dao.RecordWarehouseProductIdDO;
import com.danding.cds.v2.bean.dto.RecordWarehouseDTO;
import com.danding.cds.v2.bean.dto.RecordWarehouseProductIdDTO;
import com.danding.cds.v2.mapper.RecordWarehouseProductIdMapper;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 备案-实体仓-通关料号
 * @date 2022/7/11 17:44
 */
@Service
public class RecordWarehouseProductIdBaseService {
    @Autowired
    private RecordWarehouseProductIdMapper mapper;

    public RecordWarehouseProductIdDTO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        RecordWarehouseProductIdDO productIdDO = mapper.selectByPrimaryKey(id);
        return ConvertUtil.beanConvert(productIdDO, RecordWarehouseProductIdDTO.class);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return new ArrayList<>();
        }
        RecordWarehouseProductIdDO warehouseProductIdDO = new RecordWarehouseProductIdDO();
        warehouseProductIdDO.setRecordId(recordId);
        return this.selectByDO(warehouseProductIdDO);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordId(List<Long> recordId) {
        if (CollUtil.isEmpty(recordId)) {
            return new ArrayList<>();
        }
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("deleted" ,false).andIn("recordId", recordId);
        List<RecordWarehouseProductIdDO> recordWarehouseProductIdDOS = mapper.selectByExample(example);
        return ConvertUtil.listConvert(recordWarehouseProductIdDOS, RecordWarehouseProductIdDTO.class);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordIdAndRecordWarehouseId(Long recordId, Long recordWarehouseId) {
        if (Objects.isNull(recordId) && Objects.isNull(recordWarehouseId)) {
            return new ArrayList<>();
        }
        RecordWarehouseProductIdDO warehouseProductIdDO = new RecordWarehouseProductIdDO();
        warehouseProductIdDO.setRecordId(recordId);
        if (Objects.nonNull(recordWarehouseId)) {
            warehouseProductIdDO.setRecordWarehouseId(recordWarehouseId);
        }
        return this.selectByDO(warehouseProductIdDO);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordIdAndRecordWarehouseId(Long recordId, List<Long> recordWarehouseId) {
        if (Objects.isNull(recordId) || CollectionUtils.isEmpty(recordWarehouseId)) {
            return new ArrayList<>();
        }
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andIn("recordWarehouseId", recordWarehouseId).andEqualTo("deleted", false);
        List<RecordWarehouseProductIdDO> warehouseProductIdDOList = mapper.selectByExample(example);
        return ConvertUtil.listConvert(warehouseProductIdDOList, RecordWarehouseProductIdDTO.class);
    }


    public List<RecordWarehouseProductIdDTO> findByCustomsDeclareProductId(String customsDeclareProductId) {
        if (Objects.isNull(customsDeclareProductId)) {
            return new ArrayList<>();
        }
        return this.findByCustomsDeclareProductIdAndCustomsBook(customsDeclareProductId, null);
    }

    /**
     * 根据通关料号和账册号查询
     *
     * @param customsDeclareProductId 通关料号
     * @param customsBookId           账册id
     * @return
     */
    public List<RecordWarehouseProductIdDTO> findByCustomsDeclareProductIdAndCustomsBook(String customsDeclareProductId, Long customsBookId) {
        if (Objects.isNull(customsDeclareProductId)) {
            return new ArrayList<>();
        }
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setCustomsDeclareProductId(customsDeclareProductId);
        if (Objects.nonNull(customsBookId)) {
            productIdDO.setCustomsBookId(customsBookId);
        }
        return selectByDO(productIdDO);
    }

    /**
     * 根据备案-实体仓表id查询
     *
     * @param recordWarehouseId
     * @return
     */
    public List<RecordWarehouseProductIdDTO> findByRecordWarehouseId(Long recordWarehouseId) {
        if (Objects.isNull(recordWarehouseId)) {
            return new ArrayList<>();
        }
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setRecordWarehouseId(recordWarehouseId);
        return selectByDO(productIdDO);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordIdAndCustomsBookId(Long recordId, Long customsBookId) {
        if (Objects.isNull(recordId) || Objects.isNull(customsBookId)) {
            return new ArrayList<>();
        }
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setRecordId(recordId);
        productIdDO.setCustomsBookId(customsBookId);
        return selectByDO(productIdDO);
    }

    public List<RecordWarehouseProductIdDTO> selectByDO(RecordWarehouseProductIdDO productIdDO) {
        if (Objects.isNull(productIdDO)) {
            return new ArrayList<>();
        }
        List<RecordWarehouseProductIdDO> productIdDOList = mapper.select(productIdDO);
        return ConvertUtil.listConvert(productIdDOList, RecordWarehouseProductIdDTO.class);
    }

    public List<RecordWarehouseProductIdDTO> findByRecordWarehouseId(List<Long> warehouseIdList) {
        if (CollectionUtils.isEmpty(warehouseIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("recordWarehouseId", warehouseIdList);
        List<RecordWarehouseProductIdDO> productIdDOList = mapper.selectByExample(example);
        return ConvertUtil.listConvert(productIdDOList, RecordWarehouseProductIdDTO.class);
    }

    /**
     * 根据备案实体仓id全部软删除
     *
     * @param recordWarehouseId
     */
    public void deletedByRecordWarehouseId(Long recordWarehouseId) {
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("recordWarehouseId", recordWarehouseId).andEqualTo("deleted", false);
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setDeleted(true);
        UserUtils.setUpdateBy(productIdDO);
        productIdDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(productIdDO, example);
    }

    public void deletedByRecordId(Long recordId) {
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("deleted", false);
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setDeleted(true);
        UserUtils.setUpdateBy(productIdDO);
        productIdDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(productIdDO, example);
    }

    public void deletedByRecordIdAndCustomsBookId(Long recordId, Long customsBookId) {
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("customsBookId", customsBookId).andEqualTo("deleted", false);
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setDeleted(true);
        UserUtils.setUpdateBy(productIdDO);
        productIdDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(productIdDO, example);
    }

    /**
     * 将未回传的id做一个软删除
     *
     * @param recordWarehouseId
     * @param submitIdList
     */
    public void deletedById(Long recordWarehouseId, List<Long> submitIdList) {
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andEqualTo("recordWarehouseId", recordWarehouseId).andNotIn("id", submitIdList).andEqualTo("deleted", false);
        RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
        productIdDO.setDeleted(true);
        UserUtils.setUpdateBy(productIdDO);
        productIdDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(productIdDO, example);
    }


    /**
     * 新增通关料号
     *
     * @param recordWarehouseDTOList 同账册实体仓列表
     * @param newList
     */
    public void insertDeclareProductIdList(List<RecordWarehouseDTO> recordWarehouseDTOList, List<String> newList) {
        if (CollectionUtils.isEmpty(recordWarehouseDTOList) || CollectionUtils.isEmpty(newList)) {
            return;
        }
        recordWarehouseDTOList.forEach(r -> {
            List<RecordWarehouseProductIdDO> saveList = newList.stream().map(n -> {
                RecordWarehouseProductIdDO productIdDO = new RecordWarehouseProductIdDO();
                productIdDO.setRecordId(r.getRecordId());
                productIdDO.setRecordWarehouseId(r.getId());
                productIdDO.setCustomsBookId(r.getCustomsBookId());
                productIdDO.setCustomsBookNo(r.getCustomsBookNo());
                productIdDO.setCustomsDeclareProductId(n);
                UserUtils.setCreateAndUpdateBy(productIdDO);
                return productIdDO;
            }).collect(Collectors.toList());
            mapper.insertList(saveList);
        });
    }

    public List<RecordWarehouseProductIdDTO> findAnyExist(Long recordId, List<String> productIdSubmitList, Long customsBookId) {
        if (Objects.isNull(recordId) || CollectionUtils.isEmpty(productIdSubmitList)) {
            throw new ArgsInvalidException("查询是否已被绑定参数异常:参数为空");
        }
        productIdSubmitList = productIdSubmitList.stream().filter(Objects::nonNull).collect(Collectors.toList());
        Example example = new Example(RecordWarehouseProductIdDO.class);
        example.createCriteria().andNotEqualTo("recordId", recordId)
                .andIn("customsDeclareProductId", productIdSubmitList)
                .andEqualTo("customsBookId", customsBookId)
                .andEqualTo("deleted", false);
        List<RecordWarehouseProductIdDO> warehouseProductIdDOList = mapper.selectByExample(example);
        return ConvertUtil.listConvert(warehouseProductIdDOList, RecordWarehouseProductIdDTO.class);
    }
}
