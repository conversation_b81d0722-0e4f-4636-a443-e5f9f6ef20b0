package com.danding.cds.inventory.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.inventory.api.dto.InventoryChangeDTO;
import com.danding.cds.inventory.api.dto.InventoryChangeDetailSearch;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.cds.inventory.entity.InventoryChangeDO;
import com.danding.cds.inventory.mapper.InventoryChangeMapper;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.entity.CustomsBookItemDO;
import com.danding.cds.item.mapper.CustomsBookItemMapper;
import com.danding.cds.stock.StockContextUtil;
import com.danding.cds.stock.bean.OrderStoreCacheDto;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@RefreshScope
public class InventoryChangeServiceImpl implements InventoryChangeService {
    @Autowired
    private CustomsBookItemMapper customsBookItemMapper;
    @Autowired
    private InventoryChangeMapper inventoryChangeMapper;
    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @Value("${offlineUrl:}")
    private String offlineUrl;

    /**
     * 保存变动日志
     *
     * @param u
     * @param
     */
    @Override
    public Long saveLogs(UpdateInventoryDTO u) {
        OrderStoreCacheDto o = StockContextUtil.getGoodsOpsRec(u.getCustomsBookId(), u.getProductId(), u.getGoodsSeqNo());
        log.info("[InventoryChangeServiceImpl-saveLogs orderStoreCacheDto={}]", JSON.toJSONString(o));
        InventoryChangeDO inventoryChangeDO = new InventoryChangeDO();
        inventoryChangeDO.setCustomsBookItemId(u.getCustomsBookItemId());
        inventoryChangeDO.setGoodsSeqNo(u.getGoodsSeqNo());
        inventoryChangeDO.setBusinessNo(u.getBusinessNo());
        inventoryChangeDO.setProductId(u.getProductId());
        inventoryChangeDO.setUpdateType(u.getChangeType().getCode());
        inventoryChangeDO.setChangeNum(u.getDeclareUnitQfy());
        inventoryChangeDO.setCustomsBookId(u.getCustomsBookId());
        inventoryChangeDO.setProductStockId(u.getProductStockId());
        inventoryChangeDO.setUnifiedProductId(u.getUnifiedProductId());
        inventoryChangeDO.setOperator(StrUtil.isNotBlank(u.getOperator()) ? u.getOperator() : SimpleUserHelper.getRealUserName());
        inventoryChangeDO.setRemark(u.getRemark());
        inventoryChangeDO.setCustomsNo(u.getCustomsNo());
        Long businessTime = StockContextUtil.getMillisTime();
        inventoryChangeDO.setBusinessTime(businessTime);
        inventoryChangeDO.setCreateTime(new Date(businessTime));
        if (o != null) {
            inventoryChangeDO.setAccountNum(o.getTotalNum());
            inventoryChangeDO.setOccupiedNum(o.getOccupyNum());
            inventoryChangeDO.setUsedNum(o.getUsedNum());
            inventoryChangeDO.setAvailableNum(o.getAvailableNum());
            inventoryChangeDO.setLockedNum(o.getLockedNum());
        } else {
            CustomsBookItemDO item = customsBookItemMapper.selectByPrimaryKey(u.getCustomsBookItemId());
            log.info("[InventoryChangeServiceImpl-saveLogs getGoodsOpsRec为空 item={}]", JSON.toJSONString(item));
            inventoryChangeDO.setAccountNum(item.getAccountNum());
            inventoryChangeDO.setOccupiedNum(item.getOccupiedNum());
            inventoryChangeDO.setUsedNum(item.getUsedNum());
            inventoryChangeDO.setAvailableNum(item.getAvailableNum());
            inventoryChangeDO.setLockedNum(item.getLockedNum());
        }
        log.info("[InventoryChangeServiceImpl-saveLogs logsDO={}]", JSON.toJSONString(inventoryChangeDO));
        inventoryChangeMapper.insertSelective(inventoryChangeDO);
        log.info("[InventoryChangeServiceImpl-saveLogs id={}", inventoryChangeDO.getId());
        return inventoryChangeDO.getId();
    }

    /**
     * 批量保存变动日志
     */
    @Override
    public List<Long> batchSaveLogs(List<UpdateInventoryDTO> updateInventoryDTOList) {
        if (CollectionUtils.isEmpty(updateInventoryDTOList)) {
            return new ArrayList<>();
        }

        // 预先计算公共数据，避免重复计算
        Long businessTime = StockContextUtil.getMillisTime();
        Date createTime = new Date(businessTime);
        String defaultOperator = SimpleUserHelper.getRealUserName();

        // 批量获取库存上下文信息，避免重复查询
        Map<String, OrderStoreCacheDto> contextMap = buildContextMap(updateInventoryDTOList);

        // 批量查询账册库存信息，替换原来的单行查询
        Map<Long, CustomsBookItemDO> itemMap = batchQueryCustomsBookItems(updateInventoryDTOList);

        // 构建批量插入数据
        List<InventoryChangeDO> batchInsertList = buildInventoryChangeList(
                updateInventoryDTOList, contextMap, itemMap, businessTime, createTime, defaultOperator);

        // 分批插入并返回ID列表
        return batchInsertAndCollectIds(batchInsertList);
    }

    /**
     * 构建库存上下文映射
     */
    private Map<String, OrderStoreCacheDto> buildContextMap(List<UpdateInventoryDTO> updateInventoryDTOList) {
        Map<String, OrderStoreCacheDto> contextMap = new HashMap<>();

        for (UpdateInventoryDTO u : updateInventoryDTOList) {
            String contextKey = u.getCustomsBookId() + "_" + u.getProductId() + "_" + u.getGoodsSeqNo();
            if (!contextMap.containsKey(contextKey)) {
                OrderStoreCacheDto o = StockContextUtil.getGoodsOpsRec(u.getCustomsBookId(), u.getProductId(), u.getGoodsSeqNo());
                contextMap.put(contextKey, o);
            }
        }

        return contextMap;
    }

    /**
     * 批量查询账册库存信息，替换单行查询
     */
    private Map<Long, CustomsBookItemDO> batchQueryCustomsBookItems(List<UpdateInventoryDTO> updateInventoryDTOList) {
        // 收集所有需要查询的customsBookItemId
        Set<Long> customsBookItemIds = updateInventoryDTOList.stream()
                .map(UpdateInventoryDTO::getCustomsBookItemId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        if (customsBookItemIds.isEmpty()) {
            return new HashMap<>();
        }

        // 使用批量查询替换单行查询
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andIn("id", new ArrayList<>(customsBookItemIds));
        List<CustomsBookItemDO> items = customsBookItemMapper.selectByExample(example);

        // 转换为Map便于查找
        return items.stream().collect(Collectors.toMap(CustomsBookItemDO::getId, item -> item));
    }

    /**
     * 构建库存变动记录列表
     */
    private List<InventoryChangeDO> buildInventoryChangeList(
            List<UpdateInventoryDTO> updateInventoryDTOList,
            Map<String, OrderStoreCacheDto> contextMap,
            Map<Long, CustomsBookItemDO> itemMap,
            Long businessTime,
            Date createTime,
            String defaultOperator) {

        List<InventoryChangeDO> batchInsertList = new ArrayList<>(updateInventoryDTOList.size());

        for (UpdateInventoryDTO u : updateInventoryDTOList) {
            InventoryChangeDO inventoryChangeDO = createInventoryChangeDO(u, businessTime, createTime, defaultOperator);

            // 设置库存数量信息
            setInventoryNumbers(inventoryChangeDO, u, contextMap, itemMap);

            batchInsertList.add(inventoryChangeDO);
        }

        return batchInsertList;
    }

    /**
     * 创建库存变动记录对象
     */
    private InventoryChangeDO createInventoryChangeDO(UpdateInventoryDTO u, Long businessTime, Date createTime, String defaultOperator) {
        InventoryChangeDO inventoryChangeDO = new InventoryChangeDO();
        inventoryChangeDO.setCustomsBookItemId(u.getCustomsBookItemId());
        inventoryChangeDO.setGoodsSeqNo(u.getGoodsSeqNo());
        inventoryChangeDO.setBusinessNo(u.getBusinessNo());
        inventoryChangeDO.setProductId(u.getProductId());
        inventoryChangeDO.setUpdateType(u.getChangeType().getCode());
        inventoryChangeDO.setChangeNum(u.getDeclareUnitQfy());
        inventoryChangeDO.setCustomsBookId(u.getCustomsBookId());
        inventoryChangeDO.setProductStockId(u.getProductStockId());
        inventoryChangeDO.setUnifiedProductId(u.getUnifiedProductId());
        inventoryChangeDO.setOperator(StrUtil.isNotBlank(u.getOperator()) ? u.getOperator() : defaultOperator);
        inventoryChangeDO.setRemark(u.getRemark());
        inventoryChangeDO.setCustomsNo(u.getCustomsNo());
        inventoryChangeDO.setBusinessTime(businessTime);
        inventoryChangeDO.setCreateTime(createTime);
        inventoryChangeDO.setOngoingNum(0);

        return inventoryChangeDO;
    }

    /**
     * 设置库存数量信息
     */
    private void setInventoryNumbers(InventoryChangeDO inventoryChangeDO, UpdateInventoryDTO u,
                                     Map<String, OrderStoreCacheDto> contextMap, Map<Long, CustomsBookItemDO> itemMap) {
        String contextKey = u.getCustomsBookId() + "_" + u.getProductId() + "_" + u.getGoodsSeqNo();
        OrderStoreCacheDto o = contextMap.get(contextKey);

        if (o != null) {
            // 优先使用上下文缓存数据
            inventoryChangeDO.setAccountNum(o.getTotalNum());
            inventoryChangeDO.setOccupiedNum(o.getOccupyNum());
            inventoryChangeDO.setUsedNum(o.getUsedNum());
            inventoryChangeDO.setAvailableNum(o.getAvailableNum());
            inventoryChangeDO.setLockedNum(o.getLockedNum() == null ? 0 : o.getLockedNum());
        } else {
            // 使用批量查询的账册库存数据
            CustomsBookItemDO item = itemMap.get(u.getCustomsBookItemId());
            if (item != null) {
                inventoryChangeDO.setAccountNum(item.getAccountNum());
                inventoryChangeDO.setOccupiedNum(item.getOccupiedNum());
                inventoryChangeDO.setUsedNum(item.getUsedNum());
                inventoryChangeDO.setAvailableNum(item.getAvailableNum());
                inventoryChangeDO.setLockedNum(item.getLockedNum() == null ? 0 : item.getLockedNum());
            }
        }
    }

    /**
     * 分批插入并收集ID
     */
    private List<Long> batchInsertAndCollectIds(List<InventoryChangeDO> batchInsertList) {
        List<Long> allLogIds = new ArrayList<>();
        int batchSize = 1000;

        for (int i = 0; i < batchInsertList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, batchInsertList.size());
            List<InventoryChangeDO> batch = batchInsertList.subList(i, endIndex);

            inventoryChangeMapper.insertList(batch);

            // 收集插入后的ID
            List<Long> batchIds = batch.stream()
                    .map(InventoryChangeDO::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            allLogIds.addAll(batchIds);

            log.info("[InventoryChangeServiceImpl-batchSaveLogs batch insert size={}, ids={}]", batch.size(), batchIds);
        }

        log.info("[InventoryChangeServiceImpl-batchSaveLogs total insert size={}, total ids size={}]",
                batchInsertList.size(), allLogIds.size());
        return allLogIds;
    }


    @Override
    public ListVO<InventoryChangeDTO> paging(InventoryChangeDetailSearch search) {
        log.info("InventoryChangeServiceImpl-paging search={}", JSON.toJSONString(search));
        Example example = new Example(InventoryChangeDO.class);
        if (search.getTimeDesc()) {
            example.orderBy("businessTime").desc();
        } else {
            example.orderBy("businessTime").asc();
        }
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", 0);
        if (Objects.nonNull(search.getId())) {
            criteria.andEqualTo("customsBookItemId", search.getId());
        }
        if (Objects.nonNull(search.getProductStockId())) {
            criteria.andEqualTo("productStockId", search.getProductStockId());
        }
        //设置业务类型和变动类型
        if (search.getBusinessType() != null) {
            List<Integer> codeList = Arrays.asList(search.getBusinessType());
            if (!CollectionUtils.isEmpty(codeList)) {
                criteria.andIn("updateType", codeList);
            }
        }
        if (search.getBizNo() != null) {
            criteria.andEqualTo("businessNo", search.getBizNo());
        }
        if (StrUtil.isNotBlank(search.getCustomsNo())) {
            criteria.andEqualTo("customsNo", search.getCustomsNo());
        }
        //变动时间
        Long beginTime = search.getTimeBegin();
        if (!LongUtil.isNone(beginTime)) {
            criteria.andGreaterThanOrEqualTo("businessTime", beginTime);
        }
        Long endTime = search.getTimeEnd();
        if (!LongUtil.isNone(endTime)) {
            criteria.andLessThanOrEqualTo("businessTime", endTime);
        }
        log.info("InventoryChangeServiceImpl-paging Example={}", JSON.toJSONString(example));
        PageHelper.startPage(search.getCurrentPage(), search.getPageSize());
        List<InventoryChangeDO> list = inventoryChangeMapper.selectByExample(example);

//        //************************ 取线下备份日志信息
//        if (!StringUtils.isEmpty(offlineUrl)) {
//            List<InventoryChangeDO> offlineDOList = getOfflineData(offlineUrl + "/getInventoryChangeLogsList", JSON.toJSONString(search));
//            if (Objects.nonNull(offlineDOList) && !offlineDOList.isEmpty()) {
//                list.addAll(offlineDOList);
//                // 去重
//                list = new ArrayList<>(list.stream()
//                        .collect(Collectors.toMap((i -> i.getBusinessTime() + "#" + i.getBusinessNo() + "#" + i.getChangeNum()),
//                                o -> o, (k1, k2) -> k1)).values());
//            }
//        }
//        //************************
//        if (search.getTimeDesc()) {
//            list = list.stream().sorted(Comparator.comparing(InventoryChangeDO::getBusinessTime).reversed()).collect(Collectors.toList());
//        } else {
//            list = list.stream().sorted(Comparator.comparing(InventoryChangeDO::getBusinessTime)).collect(Collectors.toList());
//        }

//        log.info("InventoryChangeServiceImpl-paging InventoryChangeDOList={}", JSON.toJSONString(list));
        // 分页数据封装
        ListVO<InventoryChangeDTO> result = new ListVO<>();
        List<InventoryChangeDTO> dtoList = list.stream().map(i -> {
            InventoryChangeDTO inventoryChangeDTO = new InventoryChangeDTO();
            BeanUtils.copyProperties(i, inventoryChangeDTO);
            // 这里重写下createTime，用时间戳的形式，精确到毫秒
            inventoryChangeDTO.setCreateTime(new Date(inventoryChangeDTO.getBusinessTime()));
            return inventoryChangeDTO;
        }).collect(Collectors.toList());
//        int startIdx = (search.getCurrentPage() - 1) * search.getPageSize();
//        int endIdx = Math.min(startIdx + search.getPageSize(), dtoList.size());
//        int pages = list.size() / search.getPageSize() + 1;
//        List<InventoryChangeDTO> dataList = new ArrayList<>();
//        if (startIdx <= endIdx) {
//            dataList = dtoList.subList(startIdx, endIdx);
//        }
        result.setDataList(dtoList);
        //DO ==> DTO
        PageInfo<InventoryChangeDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;

    }

    private List<InventoryChangeDO> getOfflineData(String postUrl, String paramInput) {
        try {
            log.info("postDandingOffline url = {} : body = {}", postUrl, paramInput);
            HttpRequest httpRequest = HttpRequestUtil.post(postUrl, paramInput);
            if (httpRequest.ok()) {
                String body = httpRequest.body();
                JSONObject jsonObject = JSON.parseObject(body);
                if (Objects.equals(jsonObject.get("code").toString(), "200")) {
                    return JSON.parseArray(jsonObject.get("data").toString(), InventoryChangeDO.class);
                } else {
                    log.info("调用offline接口失败 body={}", body);
                    return null;
                }
            } else {
                String body = httpRequest.body();
                log.info("调用offline接口失败 body={}", body);
                return null;
            }
        } catch (Exception e) {
            log.error("GetOfflineData exception:{}", e.getMessage(), e);
            return null;
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertByStockOccupiedCount(List<StockOccupiedCountResDTO> stockOccupiedCountResDTOList) {
        if (CollectionUtils.isEmpty(stockOccupiedCountResDTOList)) {
            return;
        }
        for (StockOccupiedCountResDTO stockOccupiedCountResDTO : stockOccupiedCountResDTOList) {
            Integer stockOccupiedNum = stockOccupiedCountResDTO.getStockOccupiedNum().intValue();
            Long bookId = stockOccupiedCountResDTO.getBookId();
            String productId = stockOccupiedCountResDTO.getProductId();
            String goodsSeqNo = stockOccupiedCountResDTO.getGoodsSeqNo();
            //新增变动明细
            Example changeExample = new Example(InventoryChangeDO.class);
            Example.Criteria criteria = changeExample.createCriteria();
            criteria.andEqualTo("customsBookId", bookId);
            criteria.andEqualTo("goodsSeqNo", goodsSeqNo);
            changeExample.setOrderByClause("id DESC limit 1");
            List<InventoryChangeDO> inventoryChangeDOS = inventoryChangeMapper.selectByExample(changeExample);
            InventoryChangeDO lastInventoryChangeDO;
            if (CollectionUtils.isEmpty(inventoryChangeDOS)) {
                log.info("insertByStockOccupiedCount 账册id={},料号={},序号={},未查询到变动记录", bookId, productId, goodsSeqNo);
                CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNo(stockOccupiedCountResDTO.getBookId(), stockOccupiedCountResDTO.getGoodsSeqNo());
                if (Objects.isNull(customsBookItemDTO)) {
                    log.error("insertByStockOccupiedCount 账册id={},料号={},序号={},未查询账册库存", bookId, productId, goodsSeqNo);
                    continue;
                }
                lastInventoryChangeDO = new InventoryChangeDO();
                lastInventoryChangeDO.setCustomsBookItemId(customsBookItemDTO.getId());
                lastInventoryChangeDO.setProductStockId(customsBookItemDTO.getProductStockId());
                lastInventoryChangeDO.setUnifiedProductId(customsBookItemDTO.getUnifiedProductId());
                lastInventoryChangeDO.setAccountNum(customsBookItemDTO.getAccountNum());
                lastInventoryChangeDO.setOccupiedNum(customsBookItemDTO.getOccupiedNum());
                lastInventoryChangeDO.setAvailableNum(customsBookItemDTO.getAvailableNum());
            } else {
                lastInventoryChangeDO = inventoryChangeDOS.get(0);
            }
            log.info("insertByStockOccupiedCount 账册id={},料号={},序号={},最近一条变动明细数据-{}", bookId, productId, goodsSeqNo, JSONUtils.toJSONString(lastInventoryChangeDO));
            InventoryChangeDO inventoryChangeDO = new InventoryChangeDO();
            inventoryChangeDO.setCustomsBookItemId(lastInventoryChangeDO.getCustomsBookItemId());
            inventoryChangeDO.setBusinessTime(new Date().getTime());
            inventoryChangeDO.setCustomsBookId(bookId);
            inventoryChangeDO.setGoodsSeqNo(goodsSeqNo);
            inventoryChangeDO.setProductId(productId);
            inventoryChangeDO.setProductStockId(lastInventoryChangeDO.getProductStockId());
            inventoryChangeDO.setUnifiedProductId(lastInventoryChangeDO.getUnifiedProductId());
            int changeNum = stockOccupiedNum - lastInventoryChangeDO.getOccupiedNum();
            inventoryChangeDO.setChangeNum(changeNum);
            if (changeNum > 0) {
                log.info("insertByStockOccupiedCount 变动数量<0变动类型为【手工调整库存核增】");
                inventoryChangeDO.setUpdateType(InventoryChangeTypeEnums.EXCEL_ADJUST_IMPORT_INCREASE.getCode());
            } else if (changeNum < 0) {
                log.info("insertByStockOccupiedCount 变动数量>0变动类型为【手工调整库存核减】");
                inventoryChangeDO.setUpdateType(InventoryChangeTypeEnums.EXCEL_ADJUST_IMPORT_REDUCE.getCode());
            } else {
                log.info("insertByStockOccupiedCount 变动数量为0不新增明细");
                continue;
            }
            inventoryChangeDO.setBusinessNo(inventoryChangeDOS.size() + "_" + productId + "_" + goodsSeqNo);
            inventoryChangeDO.setAvailableNum(lastInventoryChangeDO.getAccountNum() - stockOccupiedNum);
            inventoryChangeDO.setAccountNum(lastInventoryChangeDO.getAccountNum());
            inventoryChangeDO.setOccupiedNum(stockOccupiedNum);
            log.info("insertByStockOccupiedCount,新增一条变动明细数据{}", JSONUtils.toJSONString(inventoryChangeDO));
            inventoryChangeMapper.insertSelective(inventoryChangeDO);
            //账册库存 数量更新
            Example itemExample = new Example(CustomsBookItemDO.class);
            itemExample.createCriteria().andEqualTo("productId", productId)
                    .andEqualTo("goodsSeqNo", goodsSeqNo)
                    .andEqualTo("customsBookId", bookId)
                    .andEqualTo("deleted", 0);
            CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
            customsBookItemDO.setOccupiedNum(inventoryChangeDO.getOccupiedNum());
            customsBookItemDO.setAvailableNum(inventoryChangeDO.getAvailableNum());
            log.info("insertByStockOccupiedCount,更新账册库存数据-{}", JSONUtils.toJSONString(customsBookItemDO));
            customsBookItemMapper.updateByExampleSelective(customsBookItemDO, itemExample);
        }
        log.info("insertByStockOccupiedCount - 占用库存修复完成");
    }

}
