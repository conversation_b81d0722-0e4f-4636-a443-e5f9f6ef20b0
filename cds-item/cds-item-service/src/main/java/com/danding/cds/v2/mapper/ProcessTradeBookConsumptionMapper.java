package com.danding.cds.v2.mapper;

import com.danding.cds.v2.bean.dao.ProcessTradeBookConsumptionDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

/**
 * <p>
 * 加工贸易账册单耗信息表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ProcessTradeBookConsumptionMapper extends Mapper<ProcessTradeBookConsumptionDO>, InsertListMapper<ProcessTradeBookConsumptionDO>, BatchUpdateMapper<ProcessTradeBookConsumptionDO>, AggregationPlusMapper<ProcessTradeBookConsumptionDO> {

}
