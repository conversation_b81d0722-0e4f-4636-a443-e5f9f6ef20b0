package com.danding.cds.item.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Table ( name = "ccs_customs_book_item" )
@Getter
@Setter
@Accessors(chain = true)
public class CustomsBookItemDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -603352805258530749L;
    /**
     * 商品序号
     */
    @Column(name = "goods_seq_no")
    private String goodsSeqNo;
    /**
     * 商品料号
     */
    @Column(name = "product_id")
    private String productId;
    /**
     * 料号库存id
     */
    private Long productStockId;
    /**
     * 统一料号
     */
    private String unifiedProductId;
    /**
     * HS编码
     */
    @Column(name = "hs_code")
    private String hsCode;

    /**
     * 商品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 商品来源标识:1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件
     */
    @Column(name = "goods_source")
    private String goodsSource;

    /**
     * 申报币制
     */
    @Column ( name = "curr_code" )
    private String currCode;

    /**
     * 申报单价金额
     */
    @Column ( name = "declare_price" )
    private BigDecimal declarePrice;

    /**
     * 规格型号
     */
    @Column ( name = "goods_model" )
    private String goodsModel;

    /**
     * 原产国
     */
    @Column ( name = "origin_country" )
    private String originCountry;

    /**
     * 申报单位
     */
    @Column(name = "goods_unit")
    private String goodsUnit;

    /**
     * 第一计量单位
     */
    @Column(name = "first_unit")
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    @Column(name = "first_unit_amount")
    private BigDecimal firstUnitAmount;

    /**
     * 第二计量单位
     */
    @Column(name = "second_unit")
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    @Column(name = "second_unit_amount")
    private BigDecimal secondUnitAmount;

    /**
     * 账册主键id
     */
    @Column(name = "customs_book_id")
    private Long customsBookId;

    /**
     * 记账清单商品序号
     */
    @Column ( name = "invt_goods_no" )
    private String invtGoodsNo;

    /**
     * 记账清单编号
     */
    @Column ( name = "invt_no" )
    private String invtNo;

    /**
     * 入仓数量
     */
    @Column ( name = "in_qty" )
    private Integer inQty;

    /**
     * 账册库存数量
     */
    @Column ( name = "account_num" )
    private Integer accountNum;

    /**
     * 在途库存数量
     */
    @Column ( name = "ongoing_num" )
    private Integer ongoingNum;

    /**
     * 锁定库存数量
     */
    @Column ( name = "locked_num" )
    private Integer lockedNum;

    /**
     * 占用库存数量
     */
    @Column( name = "occupied_num")
    private Integer occupiedNum;

    /**
     * 已用库存数量
     */
    @Column ( name = "used_num" )
    private Integer usedNum;

    /**
     * 可用库存数量
     */
    @Column ( name = "available_num" )
    private Integer availableNum;


    /**
     * 入仓法定数量
     */
    @Column ( name = "in_legal_qty" )
    private BigDecimal inLegalQty;

    /**
     * 第二入仓法定数量
     */
    @Column ( name = "in_second_legal_qty" )
    private BigDecimal inSecondLegalQty;

    /**
     * 最近入仓（核增）日期
     */
    @Column ( name = "in_date" )
    private Date inDate;

    /**
     * 平均美元单价
     */
    @Column ( name = "avg_price" )
    private BigDecimal avgPrice;

    /**
     * 库存美元总价
     */
    @Column ( name = "total_amt" )
    private BigDecimal totalAmt;

    /**
     * 备注
     */
    @Column ( name = "remark" )
    private String remark;

    /**
     * 推送状态  0：待推送；1：已推送；2：推送失败；3：海关已入库；4：海关入库异常；
     */
    @Column ( name = "push_status" )
    private Integer pushStatus;

    /**
     * 关企回执信息
     */
    @Column ( name = "push_msg" )
    private String pushMsg;

    /**
     * 关企推送id
     */
    @Column ( name = "push_msg_id" )
    private String pushMsgId;

    /**
     * 是否启用
     */
    private Integer enable;

    /**
     * 总入数量
     */
    @Column(name = "total_in_qty")
    private Integer totalInQty;

    /**
     * 总出数量
     */
    @Column(name = "total_out_qty")
    private Integer totalOutQty;

    /**
     * 总差异数量
     */
    @Column(name = "total_diff_qty")
    private Integer totalDiffQty;

    /**
     * 差异类型
     */
    @Column(name = "diff_type")
    private String diffType;
}