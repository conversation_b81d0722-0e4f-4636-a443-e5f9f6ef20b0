package com.danding.cds.item.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IGoodsRecordRpcFacade;
import com.danding.business.client.rpc.goods.facade.IGoodsRpcFacade;
import com.danding.business.client.rpc.goods.param.GoodRpcEditParam;
import com.danding.business.client.rpc.goods.result.GoodsRecordCssV1RpcResult;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.param.UserRpcQueryParam;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.business.common.ares.enums.goods.RecordSource;
import com.danding.business.rpc.client.oms.order.dto.JdSellerRecordRpcDTO;
import com.danding.business.rpc.client.oms.order.facade.IJdSellerRecordRpcFacade;
import com.danding.business.rpc.client.oms.order.param.JdSellerRecordQueryParam;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.common.bean.dto.ItemTrackLogExtraDTO;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.*;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordSubmit;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordWarehouseInfoDTO;
import com.danding.cds.item.api.enums.GoodsRecordActionEnum;
import com.danding.cds.item.api.enums.GoodsRecordChannel;
import com.danding.cds.item.api.enums.GoodsRecordEnum;
import com.danding.cds.item.api.enums.JdGoodsRecordType;
import com.danding.cds.item.api.service.*;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.item.entity.GoodsRecordDO;
import com.danding.cds.item.entity.es.GoodsRecordEsDO;
import com.danding.cds.item.es.RecordEsDao;
import com.danding.cds.item.mapper.GoodsRecordMapper;
import com.danding.cds.item.mq.AuditCallbackProducer;
import com.danding.cds.item.mq.BookItemCallbackReceiver;
import com.danding.cds.item.mq.GoodsRecordDumpProducer;
import com.danding.cds.mq.sender.MessageSenderService;
import com.danding.cds.out.bean.vo.req.CentralGoodsRecordDeleteSyncReqVo;
import com.danding.cds.record.api.service.RecordBaseService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountDTO;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountSubmit;
import com.danding.cds.taxes.api.enums.EnableStatus;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.cds.v2.bean.dao.RecordCustomsDO;
import com.danding.cds.v2.bean.dao.RecordWarehouseDO;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordAuditWayEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordSourceEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTypeEnums;
import com.danding.cds.v2.bean.es.RecordTrackLogEsDO;
import com.danding.cds.v2.bean.es.RecordTrackLogEsDTO;
import com.danding.cds.v2.bean.es.RecordTrackLogSaveDTO;
import com.danding.cds.v2.bean.param.DeleteGoodsRecordSubmit;
import com.danding.cds.v2.bean.param.RecordModifyAssociateInfoSubmit;
import com.danding.cds.v2.bean.vo.req.RecordTrackLogSearch;
import com.danding.cds.v2.bean.vo.req.UpdFinishGoodsRecordReqVo;
import com.danding.cds.v2.bean.vo.res.AssociateCustomsBookItemResVO;
import com.danding.cds.v2.bean.vo.res.GoodsRecordAuditPassResVO;
import com.danding.cds.v2.bean.vo.res.GoodsRecordCustomsWarehouseResVO;
import com.danding.cds.v2.bean.vo.res.RecordItemAssociateInfoResVO;
import com.danding.cds.v2.es.GoodsRecordEsDao;
import com.danding.cds.v2.service.*;
import com.danding.cds.v2.service.base.*;
import com.danding.cds.warehouse.api.WarehouseService;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.component.uc.model.CurrentUserInfo;
import com.danding.component.uc.model.RealUserInfo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.exceptions.TooManyResultsException;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class GoodsRecordServiceImpl implements GoodsRecordService {

    @Autowired
    private GoodsRecordMapper goodsRecordMapper;

    @Autowired
    private Validator validator;

    @Autowired
    private AuditCallbackProducer auditCallbackProducer;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private WarehouseService warehouseService;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private RecordItemAssociateInfoService recordItemAssociateInfoService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @Autowired
    private GoodsRecordEsDao goodsRecordEsDao;

    @Autowired
    private MessageSender messageSender;
    @Autowired
    private MessageSenderService messageSenderService;

    @Autowired
    private GoodsRecordBaseService goodsRecordBaseService;
    @DubboReference
    private RecordCustomsService recordCustomsService;
    @DubboReference
    private RecordWarehouseService recordWarehouseService;
    @Autowired
    private RecordWarehouseBaseService recordWarehouseBaseService;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;
    @DubboReference
    private RecordBaseService recordBaseService;

    @Autowired
    private RecordCustomsBaseService recordCustomsBaseService;
    @Autowired
    private BookItemCallbackReceiver bookItemCallbackReceiver;

    @Autowired
    private RecordProductBaseService recordProductBaseService;

    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;

    @Autowired
    private RecordProductStockBaseService recordProductStockBaseService;

    @Autowired
    private GoodsRecordDumpProducer goodsRecordDumpProducer;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private IGoodsRecordRpcFacade iGoodsRecordRpcFacade;
    @DubboReference
    private IGoodsRpcFacade iGoodsRpcFacade;

    @DubboReference
    private IUserRpcFacade userRpcFacade;

    @Autowired
    private UserServiceUtil userServiceUtil;

    @Autowired
    private RedisTemplate redisTemplate;

    @DubboReference
    private JdGoodsRecordService jdGoodsRecordService;
    @DubboReference
    private JdServProviderService jdServProviderService;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private IJdSellerRecordRpcFacade iJdSellerRecordRpcFacade;

    @Resource(name = "goodsRecordCountTaskExecutor")
    private ThreadPoolTaskExecutor goodsRecordCountTaskExecutor;

    @Resource
    private FourCategoryGoodsService fourCategoryGoodsService;

    @DubboReference
    private AiRecommendHsCodeService aiRecommendHsCodeService;


    @Override
    public Long updateGoodsRecordTagByCountryCode(Long id, String originCountryCode, Integer goodsRecordTag) {

        if (id == null) {
            return null;
        }

        List<RecordCustomsDO> goodsRecordDTOList = recordCustomsBaseService.findByRecordId(id);
        Boolean isFourCategoryGoods = false;
        if (!CollectionUtils.isEmpty(goodsRecordDTOList)) {
            for (RecordCustomsDO recordCustomsDO : goodsRecordDTOList) {
                if (!GoodsRecordStatusEnum.RECORD_SUCCESS.getCode().equals(recordCustomsDO.getStatus())) {
                    String baseInfoJson = recordCustomsDO.getBaseInfoJson();
                    if (StringUtils.hasText(baseInfoJson)) {
                        GoodsRecordDTO goodsRecordDTO = JSON.parseObject(baseInfoJson, GoodsRecordDTO.class);
                        Boolean keyMaterial = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(goodsRecordDTO.getOriginCountry(), goodsRecordDTO.getHsCode());
                        if (keyMaterial) {
                            isFourCategoryGoods = true;
                        }
                    }
                } else {
                    Boolean keyMaterial = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(recordCustomsDO.getOriginCountry(), recordCustomsDO.getHsCode());
                    if (keyMaterial) {
                        isFourCategoryGoods = true;
                    }
                }

            }
        }
        Integer tag = getAndSetGoodsRecordTag(isFourCategoryGoods, goodsRecordTag);
        if (!Objects.equals(tag, goodsRecordTag)) {
            GoodsRecordDO recordDO = new GoodsRecordDO();
            recordDO.setId(id);
            recordDO.setGoodsRecordTag(tag);
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                recordDO.setUpdateBy(UserUtils.getUserId());
            }
            goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        }
        return id;
    }

    /**
     * 商品备案实体仓
     *
     * @param warehouseSn  实体仓
     * @param recordStatus
     * @return
     */
    @Override
    public List<GoodsRecordWarehouseDTO> goodRecordWarehouseInfo(String warehouseSn, Integer recordStatus, Date startDate, Date endDate) {
        List<GoodWarehouseDTO> goodWarehouseDTOList = goodsRecordMapper.goodRecordWarehouseSnNum(warehouseSn, recordStatus, startDate, endDate);
        if (CollectionUtils.isEmpty(goodWarehouseDTOList)) {
            return Collections.emptyList();
        }
        return goodWarehouseDTOList.stream()
                .map(z -> {
                    GoodsRecordWarehouseDTO goodsRecordWarehouseDTO = new GoodsRecordWarehouseDTO();
                    goodsRecordWarehouseDTO.setWarehouseName(z.getWarehouseName());
                    goodsRecordWarehouseDTO.setNum(z.getNum());
                    return goodsRecordWarehouseDTO;
                }).collect(Collectors.toList());

    }

//    @Override
//    public void invokeGenerateGoodsRecord(String resultInfo, String customsBook) {
//
//        BookItemCallback bookItemCallback = ConvertUtil.beanConvert(resultInfo, BookItemCallback.class);
//        CustomsBookDTO customsBookDTO = ConvertUtil.beanConvert(customsBook, CustomsBookDTO.class);
//        bookItemCallbackReceiver.invokeGenerateGoodsRecord(bookItemCallback, customsBookDTO);
//    }

    @Autowired
    private GoodsRecordServiceImpl goodsRecordService;

    @Override
    public ListVO<GoodsRecordDTO> paging(GoodsRecordSearchCondition condition) {
        try {
            log.info("paging condition={}", JSON.toJSONString(condition));
            this.handleCondition(condition);
        } catch (Exception e) {
            log.error("商品备案查询参数转换错误");
            throw new ArgsInvalidException("商品备案查询参数转换错误");
        }
        return goodsRecordService.pagingEs(condition);
//        return goodsRecordService.warehouseRecordPaging(condition);
    }

    @Override
    public List<GoodsRecordDTO> searchByDB(CentralGoodsRecordSearchCondition condition) {
        Example example = new Example(GoodsRecordDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", 0);
        criteria.andEqualTo("goodsSource", "1");
        //过滤erp删除tag的数据
        if (condition.isFilterErpDeleted()) {
            criteria.andCondition("order_tag & " + GoodsRecordTagEnums.ERP_DELETE.getCode() + " = 0");
        }
        //saas租户
//        Long tenantryId = SimpleTenantHelper.getTenantId();
//        if (Objects.nonNull(tenantryId)) {
//            criteria.andEqualTo("tenantryId", tenantryId);
//        }
        //用户
        if (Objects.nonNull(condition.getUserId())) {
            criteria.andEqualTo("tenantId", condition.getUserId());
        }
        if (Objects.nonNull(condition.getGoodsRecordName())) {
            criteria.andLike("goodsRecordName", "%" + condition.getGoodsRecordName() + "%");
        }
        if (!CollectionUtils.isEmpty(condition.getProductIdList())) {
            criteria.andIn("productId", condition.getProductIdList());
        }
        if (!CollectionUtils.isEmpty(condition.getSkuList())) {
            criteria.andIn("skuId", condition.getSkuList());
        }
        if (!CollectionUtils.isEmpty(condition.getGoodsCodeList())) {
            criteria.andIn("goodsCode", condition.getGoodsCodeList());
        }
        if (!CollectionUtils.isEmpty(condition.getBarCodeList())) {
            criteria.andIn("barCode", condition.getBarCodeList());
        }
        if (!CollectionUtils.isEmpty(condition.getExternalProductIdList())) {
            criteria.andIn("externalProductId", condition.getExternalProductIdList());
        }
        example.setOrderByClause("create_time DESC");
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(goodsRecordDOList)) {
            return new ArrayList<>();
        }
        return goodsRecordDOList.stream().map(g -> {
            GoodsRecordDTO goodsRecordDTO = ConvertUtil.beanConvert(g, GoodsRecordDTO.class);
            if (Objects.nonNull(g.getCreateTime())) {
                goodsRecordDTO.setCreateTime(g.getCreateTime().getTime());
            }
            if (Objects.nonNull(g.getUpdateTime())) {
                goodsRecordDTO.setUpdateTime(g.getUpdateTime().getTime());
            }
            return goodsRecordDTO;
        }).collect(Collectors.toList());
    }

    /**
     * 根据名称查询
     *
     * @param name
     * @return
     */
    @Override
    public List<Long> findByName(String name) {
        Example example = new Example(GoodsRecordDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andLike("goodsRecordName", "%" + name + "%");
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(goodsRecordDOList)) {
            List<Long> ids = goodsRecordDOList.stream().map(GoodsRecordDO::getId).collect(Collectors.toList());
            return ids;
        }
        return null;
    }


    /**
     * 对搜索条件做一个处理 之后可以考虑放到切面+注解去做转换
     *
     * @param condition
     */
    private void handleCondition(GoodsRecordSearchCondition condition) {
        if (Objects.isNull(condition)) {
            return;
        }
        if (!StringUtils.isEmpty(condition.getSearchType()) && !StringUtils.isEmpty(condition.getSearchKey())) {
            if ("1".equals(condition.getSearchType())) {//sku
                condition.setSkuList(Splitter.on(",").splitToList(condition.getSearchKey()));
            }
            if ("2".equals(condition.getSearchType())) {//条码
                condition.setBarCodeList(Splitter.on(",").splitToList(condition.getSearchKey()));
            }
        }
        if (Objects.nonNull(condition.getGoodsCode())) {
            condition.setGoodsCodeList(Arrays.asList(condition.getGoodsCode().split(",")));
        }
        if (Objects.nonNull(condition.getProductId())) {
            condition.setProductIdList(Arrays.asList(condition.getProductId().split(",")));
        }
        if (Objects.nonNull(condition.getCustomsRecordProductId())) {
            condition.setCustomsRecordProductIdList(Arrays.asList(condition.getCustomsRecordProductId().split(",")));
        }
        if (Objects.nonNull(condition.getWarehouseExternalProductId())) {
            condition.setWarehouseExternalProductIdList(Arrays.asList(condition.getWarehouseExternalProductId().split(",")));
        }
        if (Objects.nonNull(condition.getExternalProductId())) {
            condition.setExternalProductIdList(Arrays.asList(condition.getExternalProductId().split(",")));
        }
        if (Objects.nonNull(condition.getRecordTimeFrom())) {
            condition.setRecordTimeFromDate(new Date(condition.getRecordTimeFrom()));
        }
        if (Objects.nonNull(condition.getRecordTimeTo())) {
            condition.setRecordTimeToDate(new Date(condition.getRecordTimeTo()));
        }
    }

    /**
     * 判断是否需要走连表查询
     *
     * @param condition
     * @return
     */
    private boolean isMultiTableSearch(GoodsRecordSearchCondition condition) {
        if (Objects.nonNull(condition.getWarehouseSn())
                || !CollectionUtils.isEmpty(condition.getRoleAccountBookIdList())
                || Objects.nonNull(condition.getWarehouseExternalProductId())
                || Objects.nonNull(condition.getCustomsBookId())
        ) {
            return true;
        }
        return false;
    }

    @PageSelect
    public ListVO<GoodsRecordDTO> pagingCore(GoodsRecordSearchCondition condition) {
        Example example = new Example(GoodsRecordDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(condition.getGoodsRecordName())) {
            criteria.andLike("goodsRecordName", "%" + condition.getGoodsRecordName() + "%");
        }
        if (!StringUtils.isEmpty(condition.getTenantId())) {
            criteria.andEqualTo("tenantId", condition.getTenantId());
        }
        if (condition.getRecordStatus() != null) {
            criteria.andEqualTo("recordStatus", condition.getRecordStatus());
        }
        if (condition.getEnable() != null) {
            criteria.andEqualTo("enable", condition.getEnable());
        }
        if (!StringUtils.isEmpty(condition.getSearchType()) && !StringUtils.isEmpty(condition.getSearchKey())) {
            if ("1".equals(condition.getSearchType())) {//sku
                criteria.andIn("skuId", Splitter.on(",").splitToList(condition.getSearchKey()));
            }
            if ("2".equals(condition.getSearchType())) {//条码
                criteria.andIn("barCode", Splitter.on(",").splitToList(condition.getSearchKey()));
            }
//            if ("3".equals(condition.getSearchType())) {//料号
//                criteria.andIn("productId", Splitter.on(",").splitToList(condition.getSearchKey()));
//            }
//            if ("4".equals(condition.getSearchType())){//货品id
//                criteria.andIn("goodsCode", Splitter.on(",").splitToList(condition.getSearchKey()));
//            }
//            if ("5".equals(condition.getSearchType())){//外部料号
//                criteria.andIn("externalProductId", Splitter.on(",").splitToList(condition.getSearchKey()));
//            }
        }
        if (!CollectionUtils.isEmpty(condition.getProductIdList())) {
            criteria.andIn("productId", condition.getProductIdList());
        }
        if (!CollectionUtils.isEmpty(condition.getGoodsCodeList())) {
            criteria.andIn("goodsCode", condition.getGoodsCodeList());
        }
        if (!CollectionUtils.isEmpty(condition.getExternalProductIdList())) {
            criteria.andIn("externalProductId", condition.getExternalProductIdList());
        }
        if (!LongUtil.isNone(condition.getRecordTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(condition.getRecordTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getRecordTimeTo())) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(condition.getRecordTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getFinishedTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("recordFinishTime", new DateTime(condition.getFinishedTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getFinishedTimeTo())) {
            criteria.andLessThanOrEqualTo("recordFinishTime", new DateTime(condition.getFinishedTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!StringUtils.isEmpty(condition.getCustomDistrict())) {
            criteria.andEqualTo("customDistrict", condition.getCustomDistrict());
        }
        if (!LongUtil.isNone(condition.getCustomsBookId())) {
            criteria.andEqualTo("customsBookId", condition.getCustomsBookId());
        }
        // 账册Id列表
        List<Long> accountBookIdList = condition.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)
                && condition.getCustomsBookId() == null) {
            criteria.andIn("customsBookId", accountBookIdList);
        }
        criteria.andEqualTo("deleted", 0);
        //排序
        example.setOrderByClause("create_time DESC");
        List<GoodsRecordDO> list = goodsRecordMapper.selectByExample(example);
        ListVO<GoodsRecordDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), GoodsRecordDTO.class));
        // 分页
        PageInfo<GoodsRecordDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Autowired
    private RecordEsDao recordEsDao;

    public ListVO<GoodsRecordDTO> pagingEs(GoodsRecordSearchCondition condition) {
        Page<GoodsRecordEsDO> esDOPage = recordEsDao.paging(condition);
//        log.warn("pagingEs pagingESwarn 查询结果 - {} ", JSON.toJSONString(esDOPage.getContent()));
        ListVO<GoodsRecordDTO> goodsRecordDTOListVO = new ListVO<>();
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount((int) esDOPage.getTotalElements());
        pageResult.setTotalPage(esDOPage.getTotalPages());
        pageResult.setCurrentPage(condition.getCurrentPage());
        pageResult.setPageSize(condition.getPageSize());
        goodsRecordDTOListVO.setPage(pageResult);
        List<GoodsRecordDTO> goodsRecordDTOList = new ArrayList<>();
        for (GoodsRecordEsDO goodsRecordEsDO : esDOPage) {
            GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
            BeanUtil.copyProperties(goodsRecordEsDO, goodsRecordDTO, "id", "recordFinishTime");
            if (Objects.nonNull(goodsRecordEsDO.getRecordFinishTime())) {
                goodsRecordDTO.setRecordFinishTime(goodsRecordEsDO.getRecordFinishTime().getTime());
            }
            String recordEsDOId = goodsRecordEsDO.getId();
            if (recordEsDOId.contains("_")) {
                String[] split = recordEsDOId.split("_");
                String realId = split[1];
                goodsRecordDTO.setId(Long.valueOf(realId));
            } else {
                goodsRecordDTO.setId(Long.valueOf(recordEsDOId));
            }
            List<Integer> goodsRecordTagList = goodsRecordEsDO.getGoodsRecordTagList();
            if (!CollectionUtils.isEmpty(goodsRecordTagList)) {
                int sum = goodsRecordTagList.stream().reduce(Integer::sum).orElse(0);
                goodsRecordDTO.setGoodsRecordTag(sum);
            }
            goodsRecordDTO.setCreateTime(goodsRecordEsDO.getCreateTime());
            goodsRecordDTO.setUpdateTime(goodsRecordEsDO.getUpdateTime());
            goodsRecordDTOList.add(goodsRecordDTO);
        }
        goodsRecordDTOListVO.setDataList(goodsRecordDTOList);
        return goodsRecordDTOListVO;
    }

    @Override
    public ListVO<GoodsRecordDTO> warehouseRecordPaging(GoodsRecordSearchCondition condition) {
        PageHelper.startPage(condition.getCurrentPage(), condition.getPageSize());
        List<ItemGoodsRecordDTO> itemGoodsRecordDTOS = goodsRecordMapper.goodsRecordPaging(condition);
        List<GoodsRecordDTO> goodsRecordDTOS = itemGoodsRecordDTOS.stream().map(i -> {
            GoodsRecordDTO goodsRecordDTO = ConvertUtil.beanConvert(i, GoodsRecordDTO.class);
            goodsRecordDTO.setCreateTime(i.getCreateTime().getTime());
            goodsRecordDTO.setUpdateTime(i.getUpdateTime().getTime());
            return goodsRecordDTO;
        }).collect(Collectors.toList());
        ListVO<GoodsRecordDTO> result = new ListVO<>();
        result.setDataList(goodsRecordDTOS);
        // 分页
        PageInfo<GoodsRecordDTO> pageInfo = new PageInfo(itemGoodsRecordDTOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(condition.getCurrentPage());
        pageResult.setPageSize(condition.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    /**
     * 更新备案并记录
     *
     * @param submit
     * @param operateType 可以为null
     * @return
     * @throws ArgsErrorException
     */
    @Override
    public Long update(GoodsRecordSubmit submit, String operateType) throws ArgsErrorException {
        // before可能为null
        GoodsRecordDTO before = this.findById(submit.getId());
        Long recordId = this.upset(submit);
        GoodsRecordDTO after = this.findById(recordId);
        this.buildTrackLogAndSend(submit, before, after, operateType);
        return recordId;
    }


    @Override
    public Long upset(GoodsRecordSubmit submit) throws ArgsErrorException {
        GoodsRecordSubmitDTO dto = new GoodsRecordSubmitDTO();
        BeanUtils.copyProperties(submit, dto);
        return this.upsetCore(dto);
    }


    /**
     * todo 这里有submit-id为空情况，具体这种情况
     * todo 1. 应该是导入功能，暂时不考虑，等导入功能再次上线时在考虑
     * todo 2. 记账回执生成的逻辑，需要考虑
     *
     * @param submit
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upsetCore(GoodsRecordSubmitDTO submit) {
        //校验必填项
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (null != inputError) {
            log.info("GoodsRecordServiceImpl-upset inputError={}", inputError);
            throw new ArgsErrorException(inputError);
        }

        //校验参数合法性
        CustomsCountryDTO countryDTO = customsCountryService.findByCode(submit.getOriginCountry());
        if (countryDTO == null) {
            throw new ArgsErrorException("海关原产国错误");
        }
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(submit.getHsCode());
        if (customsHsDTO == null) {
            throw new ArgsErrorException("HS编码错误");
        }
        CustomsUomDTO customsUomDTO = customsUomService.findByCode(submit.getDeclareUnit());
        if (customsUomDTO == null) {
            throw new ArgsErrorException("申报单位错误");
        }
        if (!StringUtils.isEmpty(submit.getDeclareCurrency())) {
            CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(submit.getDeclareCurrency());
            if (currencyDTO == null) {
                throw new ArgsErrorException("申报币制错误");
            }
        }
        customsUomDTO = customsUomService.findByCode(submit.getFirstUnit());
        if (customsUomDTO == null) {
            throw new ArgsErrorException("法定第一计量单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnit()) && customsUomService.findByCode(submit.getSecondUnit()) == null) {
            throw new ArgsErrorException("法定第二计量单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnit()) && StringUtils.isEmpty(submit.getSecondUnitAmount())) {
            throw new ArgsErrorException("法定第二计量单位不为空时，法定第二计量数量不能为空！");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnitAmount()) && StringUtils.isEmpty(submit.getSecondUnit())) {
            throw new ArgsErrorException("法定第二计量单位为空时，法定第二计量数量不允许传入！");
        }
        if (submit.getNetWeight().compareTo(submit.getGrossWeight()) == 1) {
            throw new ArgsErrorException("商品备案的毛重（" + submit.getGrossWeight() + "kg）小于净重（" + submit.getNetWeight() + "kg）");
        }
        if (StrUtil.isNotBlank(submit.getGuanWuRemark()) && submit.getGuanWuRemark().length() > 512) {
            throw new ArgsErrorException("关务备注长度不能超过512");
        }
        //初始化
        GoodsRecordDO recordDO = new GoodsRecordDO();
        BeanUtil.copyProperties(submit, recordDO);
        log.info("upset submit ={}", JSON.toJSONString(submit));
        log.info("upset recordDO ={}", JSON.toJSONString(recordDO));
        recordDO.setRecordFinishTime(submit.getRecordFinishTime() != null ? new Date(submit.getRecordFinishTime()) : null);
        if (customsHsDTO.getTariff() == null || customsHsDTO.getVat() == null) {
            throw new ArgsErrorException("hsCode：" + customsHsDTO.getHsCode() + "相关信息未完善");
        }
        recordDO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
        recordDO.setTaxRate(customsHsDTO.getTariff().multiply(new BigDecimal(100)).intValue());
        if (recordDO.getRecordStatus() == null) {
            recordDO.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
        }
        //条码不规范赋值
        Integer intGoodsRecordTag = GoodsRecordTagEnums.addGoodsRecordTag(recordDO.getGoodsRecordTag(), GoodsRecordTagEnums.GTIN_NO_RULE);
        if (!GTINCheck.checkRules(submit.getBarCode())) {
            recordDO.setGoodsRecordTag(intGoodsRecordTag);
        }

        // 如果原产国为美国, 则认为是四类商品
//        Integer goodsRecordTag = getAndSetGoodsRecordTagByOriginCountry(submit.getOriginCountry(), recordDO.getGoodsRecordTag());
//        recordDO.setGoodsRecordTag(goodsRecordTag);
        if (LongUtil.isNone(submit.getId())) {
            recordDO.setId(null);
            if (submit.getEnable() != null) {
                submit.setEnable(1);
            }
            goodsRecordMapper.insertSelective(recordDO);
            // 完成的 同步备案库
            syncGoodsRecord(recordDO);
        } else {
            // 校验备案是否审核完成，新版本应该校验的是备案是否在当前口岸审核完成
            // TODO: 2022/3/18 改为备案id
//            RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findByProductIdAndCustoms(submit.getProductId(), submit.getCustomsCode());
            RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findByRecordIdAndCustomsCode(submit.getId(), submit.getCustomsCode());
            if (Objects.nonNull(recordCustomsDO)
                    && Objects.equals(recordCustomsDO.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())
                    && !Boolean.TRUE.equals(submit.getAllowEdit())) {
                throw new ArgsInvalidException(String.format("商品料号[%s]，在口岸[%s]下已备案审核完成，无须再次审核", submit.getProductId(), submit.getCustomsCode()));
            }
            GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(submit.getId());
            if (goodsRecordDO == null) {
                throw new ArgsErrorException("ID不正确");
            }
            GoodsRecordDO updateGoodsRecordDO = ConvertUtil.beanConvert(goodsRecordDO, GoodsRecordDO.class);
            log.info("updateCore submit={}", JSON.toJSONString(submit));
            BeanUtils.copyProperties(submit, updateGoodsRecordDO, "enable", "recordStatus", "reason", "recordFinishTime", "channel", "tenantId");
            log.info("updateCore goodsRecordDO={}", JSON.toJSONString(goodsRecordDO));
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                goodsRecordDO.setUpdateBy(UserUtils.getUserId());
            }
            updateGoodsRecordDO.setGoodsSource(goodsRecordDO.getGoodsSource());
            goodsRecordMapper.updateByPrimaryKey(updateGoodsRecordDO);
            // 同步备案库
            syncGoodsRecord(updateGoodsRecordDO);
            RecordCustomsDO customsDO = recordCustomsBaseService.findByRecordIdAndCustomsCode(submit.getId(), submit.getCustomsCode());
            if (Objects.nonNull(customsDO)) {
                customsDO.setOriginCountry(submit.getOriginCountry());
                customsDO.setHsCode(submit.getHsCode());
                customsDO.setFirstUnit(submit.getFirstUnit());
                customsDO.setFirstUnitAmount(submit.getFirstUnitAmount());
                customsDO.setSecondUnit(submit.getSecondUnit());
                customsDO.setSecondUnitAmount(submit.getSecondUnitAmount());
                if (Objects.equals(submit.getOpinion(), 1)) {
                    customsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                    customsDO.setSubmitType(RecordCustomsSubmitTypeEnum.NULL.getCode());
                } else if (Objects.equals(submit.getOpinion(), 0)) {
                    customsDO.setStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
                    customsDO.setSubmitType(RecordCustomsSubmitTypeEnum.NULL.getCode());
                } else {
                    customsDO.setStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
                    customsDO.setSubmitType(RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode());
                }
                customsDO.setReason(submit.getReason());
                customsDO.setGuanWuRemark(submit.getGuanWuRemark());
                recordCustomsBaseService.updateByPrimaryKey(customsDO);
            }
            //更新所有口岸状态
            this.recordStatusFix(recordDO.getId());
        }
        submit.setId(recordDO.getId());
        return recordDO.getId();
    }

    public Integer getAndSetGoodsRecordTag(Boolean isFourCategoryGoods, Integer goodsRecordTag) {

        Integer intGoodsRecordTag = goodsRecordTag;
        // 如果原产国为美国, 则认为是四类商品
        if (isFourCategoryGoods) {
            if (intGoodsRecordTag == null) {
                intGoodsRecordTag = GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getCode();
            } else {
                intGoodsRecordTag = intGoodsRecordTag | GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getCode();
            }
        } else {
            if (intGoodsRecordTag != null) {
                intGoodsRecordTag = GoodsRecordTagEnums.removeGoodsRecordTag(intGoodsRecordTag, GoodsRecordTagEnums.FOUR_CATEGORY_GOODS);
            }
        }
        return intGoodsRecordTag;
    }

    private void syncGoodsRecord(GoodsRecordDO recordDO) {
        //完成的备案的同步备案库
        if (GoodsRecordStatusEnum.RECORD_SUCCESS.getCode().equals(recordDO.getRecordStatus())) {
            GoodsRecordDTO recordDTO = new GoodsRecordDTO();
            BeanUtils.copyProperties(recordDO, recordDTO);
            recordBaseService.syncRecordBase(recordDTO);
        }
    }

    @Override
    @Deprecated
    public Long syncUpset(GoodsRecordSyncSubmit submit) throws ArgsErrorException {
        GoodsRecordSyncSubmitDTO dto = new GoodsRecordSyncSubmitDTO();
        BeanUtils.copyProperties(submit, dto);
        return syncUpsetCore(dto);
    }

    @Override
    public Long syncUpset(GoodsRecordSyncSubmitV2 submit) throws ArgsErrorException {
        GoodsRecordSyncSubmitDTO goodsRecordSyncSubmitDTO = ConvertUtil.beanConvert(submit.getGoodsRecordBaseInfo(), GoodsRecordSyncSubmitDTO.class);
        goodsRecordSyncSubmitDTO.setRecordType(submit.getRecordType());
        goodsRecordSyncSubmitDTO.setGoodsSource(GoodsRecordEnum.MERCHANT.getCode());
        goodsRecordSyncSubmitDTO.setAutoRecordFlag(submit.getAutoRecordFlag());
        return this.syncUpsetCore(goodsRecordSyncSubmitDTO);
    }

    @Override
    public Long syncUpsetCore(GoodsRecordSyncSubmitDTO submit) {
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (StringUtils.hasText(inputError)) {
            throw new ArgsErrorException(inputError);
        }
        CustomsBookDTO customsBookDTO = null;
        if (submit.getCustomsBookNo() != null) {
            customsBookDTO = customsBookService.findByCode(submit.getCustomsBookNo());
        }
        //校验参数合法性
        CustomsCountryDTO countryDTO = customsCountryService.findByCode(submit.getOriginCountry());
        if (countryDTO == null) {
            throw new ArgsErrorException("海关原产国错误");
        }
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(submit.getHsCode());
        if (customsHsDTO == null) {
            throw new ArgsErrorException("HS编码错误");
        }
        CustomsUomDTO customsUomDTO = customsUomService.findByCode(submit.getDeclareUnit());
        if (customsUomDTO == null) {
            throw new ArgsErrorException("申报单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnit()) && Objects.isNull(submit.getSecondUnitAmount())) {
            throw new ArgsErrorException("法定第二数量单位有值时,法定第二数量不允许为空");
        }
        if (StringUtils.isEmpty(submit.getSecondUnit()) && Objects.nonNull(submit.getSecondUnitAmount())) {
            throw new ArgsErrorException("法定第二数量有值时,法定第二数量单位不允许为空");
        }
        GoodsRecordDTO old;
        //如果是老料号逻辑 就取老的料号
        if (GoodsRecordTypeEnums.OLD.getCode().equals(submit.getRecordType())) {
            // 账册ID+useId+skuId数据唯一
            old = this.findByBookIdUserIdAndSkuId(customsBookDTO.getId(), submit.getTenantId(), submit.getSkuId());
        } else {
            if (Objects.isNull(submit.getProductId())) {
                throw new ArgsErrorException("提交审核:提交备案料号为空!");
            }
            // 走新料号逻辑 按照唯一的料号去取备案
            old = this.findByProId(submit.getProductId());
        }
        if (old != null) {
            if (GoodsRecordTypeEnums.NEW.getCode().equals(submit.getRecordType()) && !Boolean.TRUE.equals(submit.getAutoRecordFlag())) {
                //如果是新料号逻辑 不直接更新库 只存实体仓的关系 等审核通过后再更新; 若为自动备案，对原有备案进行更新
                return old.getId();
            }
            if (LongUtil.isNone(submit.getId())) {//导入
                submit.setId(old.getId());
            } else {//编辑
                if (!submit.getId().equals(old.getId())) {
                    throw new ArgsErrorException("账册编码" + customsBookDTO.getBookNo() + ":sku为" + old.getSkuId() + "的记录已存在！");
                }
            }
            log.info("syncUpset 上游推送备案 清除备案已有关联关系 料号:{} 备案Id:{}", old.getProductId(), old.getId());
        } else if (GoodsRecordTypeEnums.OLD.getCode().equals(submit.getRecordType())) {
            // 如果是首次提交的老备案 不处理
            log.error("ERP首次推送老备案数据 料号:{} submit:{}", submit.getProductId(), JSON.toJSONString(submit));
            throw new ArgsErrorException("料号:" + submit.getProductId() + "为首次提交的老备案,请重新创建并提交");
        }


        //初始化
        GoodsRecordDO recordDO = new GoodsRecordDO();
        BeanUtil.copyProperties(submit, recordDO);

        if (customsHsDTO.getTariff() == null || customsHsDTO.getVat() == null) {
            throw new ArgsErrorException("hsCode：" + customsHsDTO.getHsCode() + "相关信息未完善");
        }
        recordDO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
        recordDO.setTaxRate(customsHsDTO.getTariff().multiply(new BigDecimal(100)).intValue());

        recordDO.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
        recordDO.setReason(null);
        //条码不符合规则
        Integer intGoodsRecordTag = GoodsRecordTagEnums.addGoodsRecordTag(recordDO.getGoodsRecordTag(), GoodsRecordTagEnums.GTIN_NO_RULE);
        if (!GTINCheck.checkRules(submit.getBarCode())) {
            recordDO.setGoodsRecordTag(intGoodsRecordTag);
        }

        // 如果原产国为美国, 则认为是四类商品
//        Integer goodsRecordTagTmp = getAndSetGoodsRecordTagByOriginCountry(submit.getOriginCountry(), recordDO.getGoodsRecordTag());
//        recordDO.setGoodsRecordTag(goodsRecordTagTmp);

        if (!StringUtils.isEmpty(submit.getTenantId())) {
            //现在的userId都是1L
            TaxesTenantAccountDTO taxesTenantAccountDTO = taxesTenantAccountService.findByUserIdAndTenantId(1L, submit.getTenantId());
            if (taxesTenantAccountDTO == null) {
                TaxesTenantAccountSubmit tenantAccountSubmit = new TaxesTenantAccountSubmit();
                tenantAccountSubmit.setEnable(EnableStatus.CAN.getValue());
                tenantAccountSubmit.setName(submit.getTenantName());
                tenantAccountSubmit.setTenantId(submit.getTenantId());
                tenantAccountSubmit.setConsumedAmount(new BigDecimal("0"));
                tenantAccountSubmit.setRechargeTotal(new BigDecimal("0"));
                tenantAccountSubmit.setAvailable(new BigDecimal("0"));
                tenantAccountSubmit.setUserId(1L);
                try {
                    taxesTenantAccountService.save(tenantAccountSubmit);
                } catch (Exception e) {
                    log.warn("处理异常：{}", e.getMessage(), e);
                }
            }
            recordDO.setTenantId(submit.getTenantId());
        }
        if (LongUtil.isNone(submit.getId())) {
            recordDO.setId(null);
            if (submit.getEnable() != null) {
                submit.setEnable(1);
            }
            goodsRecordMapper.insertSelective(recordDO);
        } else {
            GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(submit.getId());
            if (goodsRecordDO == null) {
                throw new ArgsErrorException("ID不正确");
            }
            BeanUtils.copyProperties(submit, goodsRecordDO);
            goodsRecordDO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
            if (0 == customsHsDTO.getConsumptionFlag()) {
                goodsRecordDO.setTaxRate(customsHsDTO.getConsumptionTax().multiply(new BigDecimal(100)).intValue());
            } else if (5 == customsHsDTO.getConsumptionFlag()) {
                goodsRecordDO.setTaxRate(customsHsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)).intValue());
            }
            if (Boolean.TRUE.equals(submit.getAutoRecordFlag())) {
                //自动备案  ERP已删除的备案重新下发，消除ERP删除标记 并对 备案进行更新
                Integer goodsRecordTag = GoodsRecordTagEnums.removeGoodsRecordTag(goodsRecordDO.getGoodsRecordTag(), GoodsRecordTagEnums.ERP_DELETE);
                goodsRecordDO.setGoodsRecordTag(goodsRecordTag);
            }
            goodsRecordDO.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                goodsRecordDO.setUpdateBy(UserUtils.getUserId());
            }
            GoodsRecordDTO before = this.findById(goodsRecordDO.getId());
            goodsRecordMapper.updateByPrimaryKey(goodsRecordDO);
            GoodsRecordDTO after = this.findById(goodsRecordDO.getId());
            this.buildTrackLogDiffAndSend(before, after, submit.getCustoms(), ItemTrackLogConfig.UPDATE_GOODS);
            if (!Boolean.TRUE.equals(submit.getAutoRecordFlag())) {
                //老料号口岸更新; 自动备案更新 不修改口岸状态
                recordCustomsService.updateStatus(recordDO.getId(), submit.getCustoms(), GoodsRecordStatusEnum.WAIT_EXAMINE.getCode(), null, RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode());
            }
        }
        submit.setId(recordDO.getId());
        return recordDO.getId();
    }

    @Override
    public CustomsBookDTO getCustomsBookByWareCode(String wareCdoe) {
        CustomsBookDTO customsBookDTO = null;
        String customsBookNo = warehouseService.getBookSnByWarehouse(wareCdoe);
        if (!StringUtils.isEmpty(customsBookNo)) {
            customsBookDTO = customsBookService.findByCode(customsBookNo);
        }
        return customsBookDTO;
    }

    @Override
    public GoodsRecordDTO queryCustomsRecord(GoodsRecordQuerySubmit submit) {
        CustomsBookDTO customsBookDTO = getCustomsBookByWareCode(submit.getWareCode());
        if (customsBookDTO == null) {
            throw new ArgsErrorException("未能获取到有效账册信息");
        }

        GoodsRecordDTO recordDTO = findByBookIdAndSkuId(customsBookDTO.getId(), submit.getSkuCode());
        if (recordDTO == null) {
            throw new ArgsErrorException("未找到相应备案商品记录");
        }
        return recordDTO;
    }

    @Override
    public GoodsRecordDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(id);
            if (goodsRecordDO == null) {
                return null;
            } else {
                //GoodsRecordDTO goodsRecordDTO = JSON.parseObject(JSON.toJSONString(goodsRecordDO),GoodsRecordDTO.class);
                //BeanUtils.copyProperties(goodsRecordDO, goodsRecordDTO);
                return JSON.parseObject(JSON.toJSONString(goodsRecordDO), GoodsRecordDTO.class);
            }
        }
    }

    @Override
    public List<GoodsRecordDTO> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        List<GoodsRecordDO> goodsRecordDOS = goodsRecordMapper.selectByExample(example);
        return ConvertUtil.listConvert(goodsRecordDOS, GoodsRecordDTO.class);
    }

    @Override
    public GoodsRecordDTO findByProId(String productId) {
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setProductId(productId);
        goodsRecordDO.setDeleted(false);
        goodsRecordDO = goodsRecordMapper.selectOne(goodsRecordDO);
        if (goodsRecordDO == null) {
            return null;
        } else {
            return JSON.parseObject(JSON.toJSONString(goodsRecordDO), GoodsRecordDTO.class);
        }
    }

    /**
     * 通过统一料号 获取商品备案
     *
     * @param productId 统一料号
     * @return id逆序备案DTOList
     */
    @Override
    public List<GoodsRecordDTO> findDescListByProId(String productId) {
        if (StringUtil.isBlank(productId)) {
            log.info("findDescListByProId 统一料号不存在");
            return new ArrayList<>();
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andEqualTo("productId", productId);
        example.orderBy("id").desc();
        List<GoodsRecordDO> goodsRecordDOS = goodsRecordMapper.selectByExample(example);
        return ConvertUtil.listConvert(goodsRecordDOS, GoodsRecordDTO.class);
    }

    @Override
    public List<GoodsRecordDTO> findByProId(List<String> productId) {
        if (CollUtil.isEmpty(productId)) {
            return new ArrayList<>();
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andIn("productId", productId);
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.selectByExample(example);
        List<GoodsRecordDTO> goodsRecordDTOS = ConvertUtil.listConvert(goodsRecordDOList, GoodsRecordDTO.class);
        return goodsRecordDTOS;
    }

    @Override
    public List<GoodsRecordDTO> findByBookIdAndProId(Long customsBookId, List<String> productIdList) {
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordBaseService.findByBookIdAndProIdList(customsBookId, productIdList);
        return ConvertUtil.listConvert(goodsRecordDOList, GoodsRecordDTO.class);
    }

    @Override
    public List<GoodsRecordDTO> findSuccessByProId(List<String> productId) {
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("deleted", 0).andEqualTo("recordStatus", GoodsRecordStatusEnum.RECORD_SUCCESS.getCode()).andIn("productId", productId);
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.selectByExample(example);
        List<GoodsRecordDTO> goodsRecordDTOS = ConvertUtil.listConvert(goodsRecordDOList, GoodsRecordDTO.class);
        return goodsRecordDTOS;
    }

    @Override
    public GoodsRecordDTO findByBookIdAndProId(Long customsBookId, String productId) {
        log.warn("[-po findByBookIdAndProId] 账册 - {}, 料号 - {}", customsBookId, productId);
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordBaseService.findByBookIdAndProId(customsBookId, productId);
        if (CollectionUtils.isEmpty(goodsRecordDOList)) {
            return null;
        } else if (1 < goodsRecordDOList.size()) {
            throw new ArgsInvalidException(String.format("料号[%s]在同一账册下有多条备案", productId));
        } else {
            log.warn("[-po findByBookIdAndProId] 备案获取结果 - {}", JSON.toJSONString(goodsRecordDOList.get(0)));
            GoodsRecordDO goodsRecordDO = goodsRecordDOList.get(0);
            return JSON.parseObject(JSON.toJSONString(goodsRecordDO), GoodsRecordDTO.class);
        }
    }

    @Override
    public GoodsRecordDTO findByBookIdAndSkuId(Long customsBookId, String skuId) {
        log.warn("[-po findByBookIdAndSkuId] 根据账册 - {},skuId - {}关联查询备案", customsBookId, skuId);
        GoodsRecordDO goodsRecordDO = goodsRecordBaseService.findByBookIdAndSkuId(customsBookId, skuId);
        if (goodsRecordDO == null) {
            return null;
        }
        log.warn("[-po findByBookIdAndSkuId] 备案获取结果 - {}", JSON.toJSONString(goodsRecordDO));
        GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
        BeanUtils.copyProperties(goodsRecordDO, goodsRecordDTO);
        return goodsRecordDTO;
    }

//    private GoodsRecordDTO findByBookIdAndSkuIdAnyStatus(Long customsBookId, String skuId) {
//        Example example = new Example(GoodsRecordDO.class);
//        example.orderBy("createTime").desc();
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("skuId", skuId)
//                .andEqualTo("customsBookId", customsBookId)
//                .andEqualTo("deleted", 0);
//        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.selectByExample(example);
//        if (0 == goodsRecordDOList.size()) {
//            return null;
//        } else {
//            return JSON.parseObject(JSON.toJSONString(goodsRecordDOList.get(0)), GoodsRecordDTO.class);
//        }
//    }

    /**
     * 获取商品备案，通过用户ID和skuid，
     * erp:用户id+skuId可以确定数据唯一
     *
     * @param customsBookId 账册ID
     * @param userId        用户ID
     * @param skuId
     * @return
     */
    private GoodsRecordDTO findByBookIdUserIdAndSkuId(Long customsBookId, String userId, String skuId) {
        log.warn("[-po findByBookIdUserIdAndSkuId] 根据账册 - {},用户id - {},skuId关联查询备案 - {}", customsBookId, userId, skuId);
        if (userId == null || skuId == null) {
            return null;
        }
        GoodsRecordDO goodsRecordDO = goodsRecordBaseService.findByBookIdUserIdAndSkuId(customsBookId, userId, skuId);
        if (goodsRecordDO == null) {
            return null;
        }
        log.warn("[-po findByBookIdUserIdAndSkuId] 备案获取结果 - {}", JSON.toJSONString(goodsRecordDO));
        GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
        BeanUtils.copyProperties(goodsRecordDO, goodsRecordDTO);
        return goodsRecordDTO;
    }

    @Override
    public GoodsRecordDTO findByWareCodeAndSku(String wareCode, String skuCode) {
        CustomsBookDTO customsBookDTO = getCustomsBookByWareCode(wareCode);
        if (customsBookDTO == null) {
            log.error("[GoodsRecordServiceImpl-findByWareCodeAndSku] exception, 未能获取到有效账册信息 warehouse={}", wareCode);
            throw new ArgsErrorException("未能获取到有效账册信息");
        }

        GoodsRecordDTO recordDTO = findByBookIdAndSkuId(customsBookDTO.getId(), skuCode);
        if (recordDTO == null) {
            throw new ArgsErrorException("未找到相应备案商品记录");
        }
        return recordDTO;
    }

    @Override
    public Long updateEnable(Long id, Integer enable) {
        GoodsRecordDO recordDO = new GoodsRecordDO();
        recordDO.setId(id);
        recordDO.setEnable(enable);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            recordDO.setUpdateBy(UserUtils.getUserId());
        }
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        this.buildTrackLogEnableAndSend(id, enable);
        return recordDO.getId();
    }

    @Override
    public Long audit(GoodsRecordSubmit submit) {
        GoodsRecordDO recordDO = new GoodsRecordDO();
        recordDO.setId(submit.getId());
        if (submit.getOpinion() == 1) {
            recordDO.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
            recordDO.setRecordFinishTime(new Date(submit.getRecordFinishTime()));
        } else if (submit.getOpinion() == 0) {
            recordDO.setRecordStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
            recordDO.setReason(submit.getReason());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            recordDO.setUpdateBy(UserUtils.getUserId());
        }
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        return recordDO.getId();
    }

    /**
     * @param submit
     * @return
     */
    @Deprecated
    @Override
    public Long audit(GoodsRecordAuditSubmit submit) {
        if (Objects.isNull(submit.getId())) {
            throw new ArgsInvalidException("商品备案提交审核ID为空");
        }
        GoodsRecordDTO recordDTO = findById(submit.getId());
        GoodsRecordDO recordDO = goodsRecordMapper.selectByPrimaryKey(submit.getId());
        if (submit.getOpinion() == 1) {
            GoodsRecordDTO goodsRecord = findByBookIdAndProId(recordDTO.getCustomsBookId(), submit.getProductId());
            if (goodsRecord != null && !goodsRecord.getId().equals(submit.getId())) {
                throw new ArgsInvalidException("同一账册下料号需唯一！");
            }
            recordDO.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
            recordDO.setCountryRecordNo(submit.getCountryRecordNo());
            recordDO.setProductId(submit.getProductId());
            recordDO.setRecordFinishTime(new Date());
            recordDO.setFirstUnit(submit.getFirstUnit());
            recordDO.setFirstUnitAmount(submit.getFirstUnitAmount());
            recordDO.setSecondUnit(submit.getSecondUnit());
            recordDO.setSecondUnitAmount(submit.getSecondUnitAmount());
            recordDO.setGrossWeight(submit.getGrossWeight());
            recordDO.setNetWeight(submit.getNetWeight());
            //同步保存关联关系
            GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
            goodsRecordDTO.setRecordStatus(recordDO.getRecordStatus());
            goodsRecordDTO.setProductId(recordDO.getProductId());
            goodsRecordDTO.setFirstUnit(recordDO.getFirstUnit());
            goodsRecordDTO.setFirstUnitAmount(recordDO.getFirstUnitAmount());
            goodsRecordDTO.setSecondUnit(recordDO.getSecondUnit());
            goodsRecordDTO.setSecondUnitAmount(recordDO.getSecondUnitAmount());
            recordItemAssociateInfoService.generateAssociateInfo(goodsRecordDTO);
        } else if (submit.getOpinion() == 0) {
            if (StringUtils.isEmpty(submit.getReason())) {
                throw new ArgsErrorException("驳回原因不可为空");
            }
            recordDO.setRecordStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
            recordDO.setReason(submit.getReason());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            recordDO.setUpdateBy(UserUtils.getUserId());
        }
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
//        this.buildTrackLogAuditAndSend(recordDO, GoodsRecordStatusEnum.RECORD_SUCCESS.getCode(),);
        //回传erp
        this.callBackChannel(submit, recordDTO);

        return recordDO.getId();
    }

    private void callBackChannel(GoodsRecordAuditSubmit submit, GoodsRecordDTO recordDTO) {
        if (recordDTO != null && GoodsRecordChannel.LOGISTICS.getValue().equals(recordDTO.getChannel())) {
            GoodsRecordAuditSubmitDTO auditSubmitDTO = new GoodsRecordAuditSubmitDTO();
            BeanUtils.copyProperties(recordDTO, auditSubmitDTO);
            if (GoodsRecordTypeEnums.NEW.getCode().equals(recordDTO.getRecordType())) {
                auditSubmitDTO.setCustomsCode(submit.getCustomsCode());
            }
            auditSubmitDTO.setProductId(submit.getProductId());
            auditSubmitDTO.setOpinion(submit.getOpinion());
            auditSubmitDTO.setReason(submit.getReason());
            // erp GoodsRecordService-syncCustomsRecord 同步过来的商品备案信息，tenantId在erp 对应用户id
            auditSubmitDTO.setTenantId(recordDTO.getTenantId());
            auditSubmitDTO.setSku(recordDTO.getSkuId());
            auditSubmitDTO.setWarehouseId(recordDTO.getWarehouseId());
            auditSubmitDTO.setExternalProductId(submit.getExternalProductId());
            auditSubmitDTO.setChannel(GoodsRecordChannel.LOGISTICS);
            Long customsBookId = recordDTO.getCustomsBookId();
            if (customsBookId != null) {
                CustomsBookDTO customsBookDTO = customsBookService.findById(recordDTO.getCustomsBookId());
                auditSubmitDTO.setBookNo(customsBookDTO.getBookNo());
            }
            //回传货号
            if (Objects.nonNull(recordDTO.getGoodsCode())) {
                auditSubmitDTO.setGoodsCode(recordDTO.getGoodsCode());
            }
            auditSubmitDTO.setGrossWeight(submit.getGrossWeight());
            auditSubmitDTO.setNetWeight(submit.getNetWeight());
            log.info("[op:GoodsRecordServiceImpl-audit auditSubmitDto={}]", JSON.toJSONString(auditSubmitDTO));
            auditCallbackProducer.send(auditSubmitDTO);
        }
    }

    /**
     * 审核通过:
     * 1.将该口岸状态改为通过
     * 2.更新备案
     * 驳回:
     * 1.
     * 回传ERP审核状态
     *
     * @param submit
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long auditV2(GoodsRecordAuditSubmitV2 submit) {
        log.info("auditV2 submit={}", JSON.toJSONString(submit));
        GoodsRecordAuditSubmit goodsRecordAuditSubmit = submit.getGoodsRecordAuditSubmit();
        GoodsRecordSubmit goodsRecordSubmit = submit.getGoodsRecordSubmit();
        String productId = goodsRecordSubmit.getProductId();
        Long goodsRecordId = goodsRecordSubmit.getId();
        String operator = SimpleUserHelper.getRealUserName();
        if (Objects.isNull(goodsRecordId)) {
            throw new ArgsInvalidException("商品备案提交审核ID为空");
        }
        GoodsRecordDTO recordDTO = findById(goodsRecordId);
        if (Objects.isNull(recordDTO)) {
            throw new ArgsInvalidException("未查询到备案信息");
        }
        if (GoodsRecordTypeEnums.OLD.getCode().equals(recordDTO.getRecordType())
                && Objects.isNull(recordDTO.getProductId())
                && Objects.nonNull(recordDTO.getCustomsBookId())) {
            this.updateRecordStatus(goodsRecordId, GoodsRecordStatusEnum.EXAMINE_REFUSE);
            this.callBackChannel(goodsRecordAuditSubmit, recordDTO);
            return goodsRecordId;
        }
        RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(goodsRecordId, goodsRecordAuditSubmit.getCustomsCode());
        if (Objects.isNull(recordCustomsDTO)) {
            throw new ArgsInvalidException("未查询到备案关联口岸信息");
        }
        if (!Objects.equals(recordCustomsDTO.getStatus(), GoodsRecordStatusEnum.WAIT_EXAMINE.getCode())) {
            throw new ArgsInvalidException("口岸状态非待审核不能审核");
        }
        if (goodsRecordAuditSubmit.getOpinion() == 1) {
            if (Objects.equals(recordCustomsDTO.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {
                throw new ArgsInvalidException("该口岸已审核通过");
            }
            // 这里应该校验的是，口岸和实体仓的关系中，同一账册下料号需唯一
            recordWarehouseBaseService.checkProductOnlyOneInOneAccount(productId);
            // 这里赋值下口岸信息进去，方便后面判断
            goodsRecordSubmit.setCustoms(recordCustomsDTO.getCustoms());
            goodsRecordSubmit.setCustomsCode(recordCustomsDTO.getCustomsCode());
            goodsRecordSubmit.setOpinion(goodsRecordAuditSubmit.getOpinion());
            goodsRecordSubmit.setGuanWuRemark(goodsRecordAuditSubmit.getGuanWuRemark());
            try {
                this.upset(goodsRecordSubmit);
                if (Objects.nonNull(submit.getErpGoodsRecordDTO())) {
                    this.buildTrackLogAuditERP(submit, recordDTO, recordCustomsDTO.getCustoms(), operator);
                }
            } catch (ArgsErrorException e) {
                throw new ArgsInvalidException(e.getErrorMessage());
            }
            //回传erp
            recordDTO = this.findById(goodsRecordId);
            log.info("auditV2 recordDTO={}", JSON.toJSONString(recordDTO));
            // 审核通过之后 同步生成备案库记录
            recordBaseService.syncRecordBase(recordDTO);
            //统一备案上线后 不预生成跨境通关信息
//            recordItemAssociateInfoService.generateAssociateInfo(recordDTO);
            // 同步账册库存法定数量
            try {
                RecordCustomsDTO newRecordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(goodsRecordId, goodsRecordAuditSubmit.getCustomsCode());
                this.syncBookItemLawNum(recordDTO.getId(), recordCustomsDTO.getId(), recordDTO.getProductId(),
                        newRecordCustomsDTO.getFirstUnit(), newRecordCustomsDTO.getFirstUnitAmount(),
                        newRecordCustomsDTO.getSecondUnit(), newRecordCustomsDTO.getSecondUnitAmount());
            } catch (Exception e) {
                log.error("同步账册库存法定数量失败 recordId={}, customsCode={}", recordDTO.getId(), recordCustomsDTO.getCustomsCode());
                throw new ArgsInvalidException("同步账册库存法定数量失败");
            }
        } else if (goodsRecordAuditSubmit.getOpinion() == 0) {
            if (GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode().equals(recordCustomsDTO.getStatus())) {
                throw new ArgsInvalidException("该口岸已审核驳回");
            }
            if (StringUtils.isEmpty(goodsRecordAuditSubmit.getReason())) {
                throw new ArgsInvalidException("驳回原因不可为空");
            }
            recordCustomsService.updateStatus(recordDTO.getId(), goodsRecordAuditSubmit.getCustomsCode(), GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode(), goodsRecordAuditSubmit.getReason());
            recordCustomsService.updateGuanWuRemark(recordDTO.getId(), goodsRecordAuditSubmit.getCustomsCode(), goodsRecordAuditSubmit.getGuanWuRemark());
            this.recordStatusFix(goodsRecordId);
            GoodsRecordDO goodsRecordDO = ConvertUtil.beanConvert(recordDTO, GoodsRecordDO.class);
            CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(goodsRecordAuditSubmit.getCustomsCode());
            this.buildTrackLogAuditAndSend(goodsRecordDO, GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode(), districtEnum.getDesc(), goodsRecordAuditSubmit.getReason());
        }
        //回传erp
        log.info("auditV2 回传erp");
        this.callBackChannel(goodsRecordAuditSubmit, recordDTO);
        recordEsDao.syncGoodsRecordToEs(recordDTO.getId());
        return goodsRecordId;
    }

    private void buildTrackLogAuditERP(GoodsRecordAuditSubmitV2 submit, GoodsRecordDTO recordDTO, String customs, String operator) {
        if (Objects.nonNull(operator)) {
            // todo 跨服务调用的时候 SimpleUserHelper.getRealUserName UC_CURRENT_USER_INFO 会丢失，先手动补上，之后优化
            CurrentUserInfo currentUserInfo = new CurrentUserInfo();
            RealUserInfo realUserInfo = new RealUserInfo();
            realUserInfo.setUserName(operator);
            currentUserInfo.setRealUser(realUserInfo);
            SimpleUserHelper.setCurrentUserInfo(currentUserInfo);
        }
        this.buildTrackLogAuditERP(submit, recordDTO, customs);
    }

    /**
     * 同步账册库存法定数量
     * 存在通关料号，则按照【账册编号+通关料号】方式查询账册库存，多个时统一更新，记录下账册库存的日志；
     * 若不存在通关料号，则按照【统一料号】查询账册库存的海关备案料号，存在则统一更新，记录下账册库存的日志；若不存在，则跳过；
     *
     * @param recordId         商品备案id
     * @param recordCustomsId  备案关联口岸id
     * @param productId        统一料号
     * @param firstUnitAmount  法一数量
     * @param secondUnitAmount 法二数量
     */
    private void syncBookItemLawNum(Long recordId, Long recordCustomsId, String productId,
                                    String firstUnit, BigDecimal firstUnitAmount,
                                    String secondUnit, BigDecimal secondUnitAmount) {
        if (Objects.isNull(recordId) || Objects.isNull(recordCustomsId) || Objects.isNull(productId)) {
            log.info("syncBookItemLawNum 参数为空");
            return;
        }
        //获取仓库信息
        List<RecordWarehouseDTO> recordWarehouseDTOList = goodsRecordService.getRecordWarehouseInfo(recordCustomsId);
        //构造通关料号
        List<Long> warehouseIdList = recordWarehouseDTOList.stream().map(RecordWarehouseDTO::getId).collect(Collectors.toList());
        List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOList = goodsRecordService.getRecordWarehouseProductIdList(recordId, warehouseIdList);

        if (CollUtil.isNotEmpty(recordWarehouseProductIdDTOList)) {
            // 【账册编号+通关料号】查询账册库存
            for (RecordWarehouseProductIdDTO dto : recordWarehouseProductIdDTOList) {
                customsBookItemService.updLawAmountByProIdAndBookId(dto.getCustomsDeclareProductId(), dto.getCustomsBookId(),
                        firstUnit, firstUnitAmount, secondUnit, secondUnitAmount);
            }
        } else {
            // 【账册编号+统一料号】查询账册库存
            for (RecordWarehouseDTO dto : recordWarehouseDTOList) {
                customsBookItemService.updLawAmountByProIdAndBookId(productId, dto.getCustomsBookId(),
                        firstUnit, firstUnitAmount, secondUnit, secondUnitAmount);
            }
        }
    }

    private void buildTrackLogAuditERP(GoodsRecordAuditSubmitV2 submit, GoodsRecordDTO recordDTO, String customs) {
        GoodsRecordDTO erpGoodsRecordDTO = submit.getErpGoodsRecordDTO();
        erpGoodsRecordDTO.setDeclarePrice(erpGoodsRecordDTO.getDeclarePrice().setScale(5, BigDecimal.ROUND_HALF_UP));
        erpGoodsRecordDTO.setNetWeight(erpGoodsRecordDTO.getNetWeight().setScale(5, BigDecimal.ROUND_HALF_UP));
        erpGoodsRecordDTO.setGrossWeight(erpGoodsRecordDTO.getGrossWeight().setScale(5, BigDecimal.ROUND_HALF_UP));
        erpGoodsRecordDTO.setFirstUnitAmount(erpGoodsRecordDTO.getFirstUnitAmount().setScale(5, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
        if (Objects.nonNull(erpGoodsRecordDTO.getSecondUnitAmount())) {
            erpGoodsRecordDTO.setSecondUnitAmount(erpGoodsRecordDTO.getSecondUnitAmount().setScale(5, BigDecimal.ROUND_HALF_UP).stripTrailingZeros());
        }
        GoodsRecordDTO afterDTO = this.findById(recordDTO.getId());
        this.buildTrackLogDiffAndSend(erpGoodsRecordDTO, afterDTO, customs, ItemTrackLogConfig.AUDIT_PASS);
    }

    /**
     * 1.除去驳回的 剩下全部是完成的算完成
     * 2.存在待审核的 状态改成待审核
     * 3.全部是驳回的 改成驳回
     *
     * @param recordId
     */
    @Override
    public void recordStatusFix(Long recordId) {
        log.info("recordStatusFix id={}", recordId);
        List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsBaseService.findDTOByRecordId(recordId);
        log.info("recordStatusFix recordCustomsDTOList={}", JSON.toJSONString(recordCustomsDTOList));
        int erpCommitStatus = 1;
        if (recordCustomsDTOList.stream().allMatch(r -> Objects.isNull(r.getErpCommitStatus()) || Objects.equals(r.getErpCommitStatus(), 0))) {
            erpCommitStatus = 0;
        }
        GoodsRecordStatusEnum statusEnum;
        if (!CollectionUtils.isEmpty(recordCustomsDTOList) && recordCustomsDTOList.stream().allMatch(r -> Objects.equals(GoodsRecordStatusEnum.WAIT_COMMIT.getCode(), r.getStatus()))) {
            statusEnum = GoodsRecordStatusEnum.WAIT_COMMIT;
        } else {
            recordCustomsDTOList = recordCustomsDTOList.stream().filter(r -> !Objects.equals(GoodsRecordStatusEnum.WAIT_COMMIT.getCode(), r.getStatus())).collect(Collectors.toList());
            if (CollUtil.isEmpty(recordCustomsDTOList) || recordCustomsDTOList.stream().anyMatch(r -> GoodsRecordStatusEnum.WAIT_EXAMINE.getCode().equals(r.getStatus()))) {
                statusEnum = GoodsRecordStatusEnum.WAIT_EXAMINE;
            } else if (recordCustomsDTOList.stream().allMatch(r -> GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode().equals(r.getStatus()))) {
                statusEnum = GoodsRecordStatusEnum.EXAMINE_REFUSE;
            } else {
                statusEnum = GoodsRecordStatusEnum.RECORD_SUCCESS;
            }
        }
        log.info("recordStatusFix recordId={} statusEnum={} erpCommitStatus={}", recordId, statusEnum.getDesc(), erpCommitStatus);
        this.updateRecordStatusAndCommitStatus(recordId, statusEnum, erpCommitStatus);
    }

    @Test
    public void test() {
        List<RecordCustomsDTO> recordCustomsDTOList = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            RecordCustomsDTO recordCustomsDTO = new RecordCustomsDTO();
            recordCustomsDTO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
            recordCustomsDTOList.add(recordCustomsDTO);
        }
//        for (int i = 0;i<3;i++){
//            RecordCustomsDTO recordCustomsDTO = new RecordCustomsDTO();
//            recordCustomsDTO.setStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
//            recordCustomsDTOList.add(recordCustomsDTO);
//        }
//        RecordCustomsDTO recordCustomsDTO = new RecordCustomsDTO();
//        recordCustomsDTO.setStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
//        recordCustomsDTOList.add(recordCustomsDTO);
        log.info("recordStatusFix recordCustomsDTOList={}", JSON.toJSONString(recordCustomsDTOList));
        GoodsRecordStatusEnum statusEnum;
        if (recordCustomsDTOList.stream().anyMatch(r -> GoodsRecordStatusEnum.WAIT_EXAMINE.getCode().equals(r.getStatus()))) {
            statusEnum = GoodsRecordStatusEnum.WAIT_EXAMINE;
            log.info("1 recordStatusFix recordCustomsDTOList={}", JSON.toJSONString(recordCustomsDTOList));
        } else if (recordCustomsDTOList.stream().allMatch(r -> GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode().equals(r.getStatus()))) {
            statusEnum = GoodsRecordStatusEnum.EXAMINE_REFUSE;
            log.info("2 recordStatusFix recordCustomsDTOList={}", JSON.toJSONString(recordCustomsDTOList));
        } else {
            statusEnum = GoodsRecordStatusEnum.RECORD_SUCCESS;
            log.info("3 recordStatusFix recordCustomsDTOList={}", JSON.toJSONString(recordCustomsDTOList));
        }
        log.info("recordStatusFix statusEnum={}", statusEnum.getDesc());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long deleteById(Long id) throws ArgsErrorException {
        GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(id);
        if (Objects.isNull(goodsRecordDO)) {
            log.error("deleteById 商品备案不存在 recordId={}", id);
            throw new ArgsInvalidException("商品备案不存在");
        }
        log.info("deleteById goodsRecord={}", JSONUtils.toJSONString(goodsRecordDO));
        //若此备案的统一料号/跨境通关料号/通关料号/海关备案料号，其一存在启用的账册库存，则无法删除
        GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
        BeanUtils.copyProperties(goodsRecordDO, goodsRecordDTO);
        List<String> productIdList = goodsRecordAssociateService.getProductIdList(goodsRecordDTO, goodsRecordDTO.getCustomsBookId());
        log.info("deleteById 备案相关料号{}", JSONUtils.toJSONString(productIdList));
        List<CustomsBookItemDTO> customsBookItemDTOS = customsBookItemService.findByProductIdList(productIdList);
        for (CustomsBookItemDTO customsBookItemDTO : customsBookItemDTOS) {
            if (Objects.equals(customsBookItemDTO.getEnable(), 1)) {
                log.error("deleteById 统一料号" + goodsRecordDTO.getProductId() + "备案下，海关备案料号：" + customsBookItemDTO.getProductId() + "存在账册库存已启用，不允许删除");
                throw new ArgsInvalidException("统一料号" + goodsRecordDTO.getProductId() + "备案下，海关备案料号：" + customsBookItemDTO.getProductId() + "存在账册库存已启用，不允许删除");
            }
        }
        //清单校验
        if (StringUtils.isEmpty(goodsRecordDTO.getProductId())) {
            log.error("deleteById 统一料号不能为空");
            throw new ArgsInvalidException("deleteById 统一料号不能为空");
        }
        if (customsInventoryRpc.judgeExist30DaysInByProductId(goodsRecordDTO.getProductId())) {
            log.error("deleteById 统一料号" + goodsRecordDTO.getProductId() + "存在申报完成未出区的清单，不允许删除");
            throw new ArgsInvalidException("统一料号" + goodsRecordDTO.getProductId() + "存在申报完成未出区的清单，不允许删除");
        }
        //清关单校验
        if (inventoryOrderInfoService.judgeExist30DaysInByProductId(goodsRecordDTO.getProductId())) {
            log.error("deleteById 统一料号" + goodsRecordDTO.getProductId() + "存在待清关的清关单，不允许删除");
            throw new ArgsInvalidException("统一料号" + goodsRecordDTO.getProductId() + "存在待清关的清关单，不允许删除");
        }
        GoodsRecordDO recordDO = new GoodsRecordDO();
        recordDO.setId(id);
        recordDO.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            recordDO.setUpdateBy(UserUtils.getUserId());
        }
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        recordCustomsService.clearRelation(id);
        recordWarehouseService.clearRelation(id);
        recordWarehouseProductIdBaseService.deletedByRecordId(id);
        recordItemAssociateInfoService.disassociateByRecordId(id);
        recordProductBaseService.deletedByRecordId(id);
        // es 同步删除
        recordEsDao.deleteById(id);
        try {
            this.deleteRecordSyncErp(goodsRecordDO);
        } catch (Exception e) {
            log.error("同步erp删除备案失败 error={}", e.getMessage(), e);
        }
        return id;
    }

    public void deleteRecordSyncErp(Long goodsRecordId) {
        GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(goodsRecordId);
        if (Objects.isNull(goodsRecordDO)) {
            log.error("deleteRecordSyncErp 商品备案不存在 recordId={}", goodsRecordId);
            return;
        }
        this.deleteRecordSyncErp(goodsRecordDO);
    }

    private void deleteRecordSyncErp(GoodsRecordDO goodsRecordDO) {
        if (Objects.equals(goodsRecordDO.getChannel(), GoodsRecordChannel.LOGISTICS.getValue())) {
            CentralGoodsRecordDeleteSyncReqVo reqVo = new CentralGoodsRecordDeleteSyncReqVo();
            reqVo.setGoodsRecordId(goodsRecordDO.getId());
            reqVo.setUserId(Long.valueOf(goodsRecordDO.getTenantId()));
            reqVo.setGoodsCode(goodsRecordDO.getGoodsCode());
            messageSenderService.asyncSendMsgDelay(reqVo, "ccs-goods-record-delete-callback-topic", 2);
        }
    }

    @Override
    public Long updateRecordStatus(Long id, GoodsRecordStatusEnum statusEnum) {
        return this.updateRecordStatus(id, statusEnum, null);
    }

    @Override
    public Long updateRecordStatus(Long id, GoodsRecordStatusEnum statusEnum, String reason) {
        GoodsRecordDO recordDO = new GoodsRecordDO();
        recordDO.setId(id);
        recordDO.setRecordStatus(statusEnum.getCode());
        if (Objects.nonNull(reason)) {
            recordDO.setReason(reason);
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            recordDO.setUpdateBy(UserUtils.getUserId());
        }
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        return recordDO.getId();
    }

    public Long updateRecordStatusAndCommitStatus(Long id, GoodsRecordStatusEnum statusEnum, Integer erpCommitStatus) {
        GoodsRecordDO recordDO = new GoodsRecordDO();
        recordDO.setId(id);
        recordDO.setRecordStatus(statusEnum.getCode());
        recordDO.setErpCommitStatus(erpCommitStatus);
        goodsRecordMapper.updateByPrimaryKeySelective(recordDO);
        return recordDO.getId();
    }

    @Override
    @Deprecated
    public Long syncCustomsRecord(GoodsRecordSyncSubmit submit) {
        log.info("[GoodsRecordServiceImpl-syncCustomsRecord] submit,info={}", JSON.toJSONString(submit));
        submit.setEnable(1);
        submit.setChannel(GoodsRecordChannel.LOGISTICS.getValue());
        try {
            return syncUpset(submit);
        } catch (ArgsErrorException e) {
            log.info("[GoodsRecordServiceImpl-syncCustomsRecord] exception, cause={}", e.getErrorMessage(), e);
            throw new ArgsInvalidException(e.getErrorMessage());
        } catch (Exception e) {
            log.info("[GoodsRecordServiceImpl-syncCustomsRecord] exception, cause={}", e.getMessage(), e);
            throw new ArgsInvalidException(e.getMessage());
        }
    }

    /**
     * 带基础信息与实体仓
     *
     * @param submit
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long syncCustomsRecordV2(GoodsRecordSyncSubmitV2 submit) {
        log.info("[GoodsRecordServiceImpl-syncCustomsRecordV2] submit,info={}", JSON.toJSONString(submit));
        try {
            Long recordId = this.syncUpset(submit);
            if (GoodsRecordTypeEnums.NEW.getCode().equals(submit.getRecordType())) {
                this.handleRecordCustomsWarehouse(recordId, submit);
            }
            try {
                goodsRecordDumpProducer.send(recordId);
            } catch (Exception e) {
                log.error("syncCustomsRecordV2 同步商品备案ES失败 error={}", e.getMessage(), e);
            }
            return recordId;
        } catch (ArgsErrorException e) {
            log.error("[GoodsRecordServiceImpl-syncCustomsRecordV2] exception, cause={}", e.getErrorMessage(), e);
            throw new ArgsInvalidException(e.getErrorMessage());
        } catch (Exception e) {
            log.error("[GoodsRecordServiceImpl-syncCustomsRecordV2] exception, cause={}", e.getMessage(), e);
            throw new ArgsInvalidException(e.getMessage());
        }
    }

    /**
     * 货品中心化保存备案
     *
     * @param centralGoodsRecordSubmit
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long saveCentralCustomsRecord(CentralGoodsRecordSubmit centralGoodsRecordSubmit) {
        log.info("saveCentralCustomsRecord info={}", JSON.toJSONString(centralGoodsRecordSubmit));
        if (Objects.isNull(centralGoodsRecordSubmit)) {
            throw new ArgsInvalidException("备案请求数据为空");
        }
        if (Objects.equals(centralGoodsRecordSubmit.getRecordType(), "OLD")) {
            throw new ArgsInvalidException("老备案不允许更新");
        }
        try {
            //构造备案同步dto
            GoodsRecordSyncSubmitDTO goodsRecordSyncSubmitDTO = this.prepareSubmitDTO(centralGoodsRecordSubmit);
            log.info("CentralGoodsRecordRpcImpl saveGoodsRecord GoodsRecordSyncSubmitDTO={}", JSON.toJSONString(goodsRecordSyncSubmitDTO));
            StringBuilder paramsCheckInfo = new StringBuilder();
            CustomsBookDTO customsBookDTO = null;
            if (!StringUtils.isEmpty(goodsRecordSyncSubmitDTO.getCustomsBookNo())) {
                customsBookDTO = customsBookService.findByCode(goodsRecordSyncSubmitDTO.getCustomsBookNo());
                if (Objects.isNull(customsBookDTO)) {
                    throw new ArgsInvalidException("账册信息错误");
                }
            }
            //校验参数合法性
            CustomsCountryDTO customsCountryDTO = validateCountry(goodsRecordSyncSubmitDTO.getOriginCountry());
            CustomsHsDTO customsHsDTO = validateHs(goodsRecordSyncSubmitDTO.getHsCode());
            CustomsUomDTO customsUomDTO = validateUom(goodsRecordSyncSubmitDTO.getDeclareUnit());
            if (!StringUtils.isEmpty(goodsRecordSyncSubmitDTO.getSecondUnit()) && Objects.isNull(goodsRecordSyncSubmitDTO.getSecondUnitAmount())) {
                paramsCheckInfo.append("法定第二数量单位有值时,法定第二数量不允许为空");
            }
            if (StringUtils.isEmpty(goodsRecordSyncSubmitDTO.getSecondUnit()) && Objects.nonNull(goodsRecordSyncSubmitDTO.getSecondUnitAmount())) {
                paramsCheckInfo.append("法定第二数量有值时,法定第二数量单位不允许为空");
            }
            if (Objects.equals(GoodsRecordTypeEnums.NEW.getCode(), goodsRecordSyncSubmitDTO.getRecordType()) && Objects.isNull(goodsRecordSyncSubmitDTO.getProductId())) {
                paramsCheckInfo.append("提交保存失败:提交备案料号为空!");
            }
            if (paramsCheckInfo.length() > 0) {
                throw new ArgsInvalidException(paramsCheckInfo.toString());
            }
            //构建提交内容json对象
            GoodsRecordDO jsonRecordDO = new GoodsRecordDO();
            BeanUtil.copyProperties(goodsRecordSyncSubmitDTO, jsonRecordDO);
            String commitBaseJsonInfo = JSON.toJSONString(jsonRecordDO);
            GoodsRecordDTO old = getGoodsRecordDTO(goodsRecordSyncSubmitDTO, customsBookDTO);
            Long recordId;
            if (Objects.nonNull(old)) {
                if (Objects.nonNull(goodsRecordSyncSubmitDTO.getId()) && !Objects.equals(goodsRecordSyncSubmitDTO.getId(), old.getId())) {
                    throw new ArgsInvalidException("账册编码" + customsBookDTO.getBookNo() + ":sku为" + old.getSkuId() + "的记录已存在！");
                }
                recordId = old.getId();
                goodsRecordSyncSubmitDTO.setId(old.getId());
                if (Objects.equals(GoodsRecordTypeEnums.NEW.getCode(), goodsRecordSyncSubmitDTO.getRecordType())) {
                    if (Objects.equals(goodsRecordSyncSubmitDTO.getAutoRecordFlag(), true)) {
                        GoodsRecordDTO externalGoodsIdGoodsRecordDTO = this.findByExternalGoodsId(goodsRecordSyncSubmitDTO.getExternalGoodsId());
                        if (Objects.nonNull(externalGoodsIdGoodsRecordDTO) && !Objects.equals(externalGoodsIdGoodsRecordDTO.getId(), old.getId())) {
                            throw new ArgsInvalidException("外部货品id:" + goodsRecordSyncSubmitDTO.getExternalGoodsId() + "的记录已存在！");
                        }
                        GoodsRecordDTO cainiaoGoodsIdGoodsRecordDTO = this.findByCainiaoGoodsId(goodsRecordSyncSubmitDTO.getCainiaoGoodsId());
                        if (Objects.nonNull(cainiaoGoodsIdGoodsRecordDTO) && !Objects.equals(cainiaoGoodsIdGoodsRecordDTO.getId(), old.getId())) {
                            throw new ArgsInvalidException("菜鸟货品id:" + cainiaoGoodsIdGoodsRecordDTO.getCainiaoGoodsId() + "的记录已存在！");
                        }
//                        List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOList = recordWarehouseProductIdBaseService
//                                .findByCustomsDeclareProductId(goodsRecordSyncSubmitDTO.getExternalProductId());
//                        if (recordWarehouseProductIdDTOList.stream().anyMatch(i -> !Objects.equals(i.getRecordId(), old.getId()))) {
//                            throw new ArgsInvalidException("通关料号:" + goodsRecordSyncSubmitDTO.getExternalProductId() + "的记录已存在！");
//                        }
                        GoodsRecordDO goodsRecordDO = goodsRecordMapper.selectByPrimaryKey(goodsRecordSyncSubmitDTO.getId());
                        //自动备案  ERP已删除的备案重新下发，消除ERP删除标记 并对 备案进行更新
                        if (Objects.isNull(goodsRecordSyncSubmitDTO.getDeclarePrice())) {
                            goodsRecordSyncSubmitDTO.setDeclarePrice(goodsRecordDO.getDeclarePrice());
                        }
                        BeanUtils.copyProperties(goodsRecordSyncSubmitDTO, goodsRecordDO);
                        goodsRecordDO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
                        if (0 == customsHsDTO.getConsumptionFlag()) {
                            goodsRecordDO.setTaxRate(customsHsDTO.getConsumptionTax().multiply(new BigDecimal(100)).intValue());
                        } else if (5 == customsHsDTO.getConsumptionFlag()) {
                            goodsRecordDO.setTaxRate(customsHsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)).intValue());
                        }
                        Integer goodsRecordTag = GoodsRecordTagEnums.removeGoodsRecordTag(goodsRecordDO.getGoodsRecordTag(), GoodsRecordTagEnums.ERP_DELETE);
                        goodsRecordDO.setGoodsRecordTag(goodsRecordTag);

                        goodsRecordDO.setUpdateBy(UserUtils.getUserId());
                        GoodsRecordDTO before = this.findById(goodsRecordDO.getId());
                        goodsRecordMapper.updateByPrimaryKey(goodsRecordDO);
                        GoodsRecordDTO after = this.findById(goodsRecordDO.getId());
                        this.buildTrackLogDiffAndSend(before, after, goodsRecordSyncSubmitDTO.getCustoms(), ItemTrackLogConfig.UPDATE_GOODS);
                    } else if (Objects.equals(goodsRecordSyncSubmitDTO.getAutoRecordFlag(), false)) {
//                        log.info("saveCentralCustomsRecord 新备案 非自动备案 走口岸与仓库更新逻辑 id={}", old.getId());
//                        //如果是新料号逻辑 不直接更新库 只存实体仓的关系 等审核通过后再更新; 若为自动备案，对原有备案进行更新
//                        CentralGoodsRecordWarehouseInfoDTO centralGoodsRecordWarehouseInfoDTO = centralGoodsRecordSubmit.getCentralGoodsRecordWarehouseInfoDTO();
//                        if (Objects.nonNull(centralGoodsRecordWarehouseInfoDTO)) {
//                            String customsCode = centralGoodsRecordWarehouseInfoDTO.getCustomsCode();
//                            recordCustomsService.updateBaseJson(old.getId(), customsCode, JSON.toJSONString(jsonRecordDO));
//                        }
//                        handleNewCustomsAndWarehouse(submit, centralGoodsRecordSubmit, old.getId(), JSON.toJSONString(jsonRecordDO), old, GoodsRecordActionEnum.SAVE_NOT_COMMIT);
                        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
                        if (Objects.nonNull(goodsRecordSyncSubmitDTO.getId())) {
                            GoodsRecordDO oldGoodsRecord = goodsRecordMapper.selectByPrimaryKey(goodsRecordSyncSubmitDTO.getId());
                            if (Objects.nonNull(oldGoodsRecord)) {
                                Integer goodsRecordTag = GoodsRecordTagEnums.removeGoodsRecordTag(oldGoodsRecord.getGoodsRecordTag(), GoodsRecordTagEnums.ERP_DELETE);
                                goodsRecordDO.setGoodsRecordTag(goodsRecordTag);
                            }
                        }
                        goodsRecordDO.setId(old.getId());
                        goodsRecordDO.setErpCommitStatus(1);
                        goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);
                    }
                }
            } else {
                if (Objects.equals(GoodsRecordTypeEnums.NEW.getCode(), goodsRecordSyncSubmitDTO.getRecordType())
                        && Objects.equals(goodsRecordSyncSubmitDTO.getAutoRecordFlag(), true)) {
                    // 自动备案新增校验
                    GoodsRecordDTO externalGoodsIdGoodsRecordDTO = this.findByExternalGoodsId(goodsRecordSyncSubmitDTO.getExternalGoodsId());
                    if (Objects.nonNull(externalGoodsIdGoodsRecordDTO)) {
                        throw new ArgsInvalidException("外部货品id:" + goodsRecordSyncSubmitDTO.getExternalGoodsId() + "的记录已存在！");
                    }
                    GoodsRecordDTO cainiaoGoodsIdGoodsRecordDTO = this.findByCainiaoGoodsId(goodsRecordSyncSubmitDTO.getCainiaoGoodsId());
                    if (Objects.nonNull(cainiaoGoodsIdGoodsRecordDTO)) {
                        throw new ArgsInvalidException("菜鸟货品id:" + cainiaoGoodsIdGoodsRecordDTO.getCainiaoGoodsId() + "的记录已存在！");
                    }
//                    List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOList = recordWarehouseProductIdBaseService
//                            .findByCustomsDeclareProductId(goodsRecordSyncSubmitDTO.getExternalProductId());
//                    if (CollUtil.isNotEmpty(recordWarehouseProductIdDTOList)) {
//                        throw new ArgsInvalidException("通关料号:" + goodsRecordSyncSubmitDTO.getExternalProductId() + "的记录已存在！");
//                    }
                }

                //初始化
                GoodsRecordDO recordDO = new GoodsRecordDO();
                BeanUtil.copyProperties(goodsRecordSyncSubmitDTO, recordDO);
                recordDO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
                recordDO.setTaxRate(customsHsDTO.getTariff().multiply(new BigDecimal(100)).intValue());
                recordDO.setErpCommitStatus(1);
                //条码不符合规则
                if (!GTINCheck.checkRules(goodsRecordSyncSubmitDTO.getBarCode())) {
                    Integer intGoodsRecordTag = GoodsRecordTagEnums.addGoodsRecordTag(recordDO.getGoodsRecordTag(), GoodsRecordTagEnums.GTIN_NO_RULE);
                    recordDO.setGoodsRecordTag(intGoodsRecordTag);
                }

//                Integer goodsRecordTag = getAndSetGoodsRecordTagByOriginCountry(goodsRecordSyncSubmitDTO.getOriginCountry(), recordDO.getGoodsRecordTag());
//                recordDO.setGoodsRecordTag(goodsRecordTag);

                if (!StringUtils.isEmpty(goodsRecordSyncSubmitDTO.getTenantId())) {
                    buildTaxTenantIfNotExist(goodsRecordSyncSubmitDTO, recordDO);
                }
                if (goodsRecordSyncSubmitDTO.getEnable() != null) {
                    goodsRecordSyncSubmitDTO.setEnable(1);
                }
                recordDO.setRecordStatus(GoodsRecordStatusEnum.WAIT_COMMIT.getCode());
                log.info("saveCentralCustomsRecord insert recordDO={}", JSON.toJSONString(recordDO));
                goodsRecordMapper.insertSelective(recordDO);
                recordId = recordDO.getId();
                goodsRecordSyncSubmitDTO.setId(recordDO.getId());
            }
            goodsRecordService.postGoodsRecordSaveProcess(goodsRecordSyncSubmitDTO, centralGoodsRecordSubmit, commitBaseJsonInfo);
            return recordId;
        } catch (ArgsErrorException e) {
            log.error("[GoodsRecordServiceImpl-syncCustomsRecordV2] exception, cause={}", e.getErrorMessage(), e);
            throw new ArgsInvalidException(e.getErrorMessage());
        } catch (Exception e) {
            log.error("[GoodsRecordServiceImpl-syncCustomsRecordV2] exception, cause={}", e.getMessage(), e);
            throw new ArgsInvalidException(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void auditCentralGoodsRecord(Long goodsRecordId, String customsCode) {
        GoodsRecordDTO goodsRecordDTO = this.findById(goodsRecordId);
        if (Objects.isNull(goodsRecordDTO)) {
            throw new ArgsInvalidException("备案不存在");
        }
        if (Objects.equals(goodsRecordDTO.getRecordType(), "OLD")) {
            throw new ArgsInvalidException("老备案不允许更新");
        }
        if (Objects.equals(goodsRecordDTO.getErpCommitStatus(), 0)) {
            throw new ArgsInvalidException("备案未提交更新,无法提交审核");
        }
        List<RecordCustomsDTO> recordCustomsDTOS = recordCustomsService.findByRecordId(goodsRecordId);
        RecordCustomsDTO recordCustomsDTO = recordCustomsDTOS.stream().filter(r -> Objects.equals(r.getCustomsCode(), customsCode)).findAny().orElse(null);
        if (Objects.isNull(recordCustomsDTO)) {
            throw new ArgsInvalidException("备案相关口岸信息不存在");
        }
        if (Objects.equals(recordCustomsDTO.getErpCommitStatus(), 0)) {
            throw new ArgsInvalidException("口岸未提交更新,无法提交审核");
        }
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setId(goodsRecordId);
        goodsRecordDO.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
        boolean allMatch = recordCustomsDTOS.stream().filter(r -> !Objects.equals(r.getCustomsCode(), customsCode))
                .allMatch(r -> Objects.equals(r.getErpCommitStatus(), 0));
        if (allMatch) {
            goodsRecordDO.setErpCommitStatus(0);
        }
        goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);

        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setId(recordCustomsDTO.getId());
        recordCustomsDO.setErpCommitStatus(0);
        recordCustomsDO.setStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
        if (Objects.equals(recordCustomsDTO.getSubmitType(), RecordCustomsSubmitTypeEnum.NULL.getCode())) {
            recordCustomsDO.setSubmitType(RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode());
        }
        recordCustomsBaseService.updateByPrimaryKeySelective(recordCustomsDO);
        this.recordStatusFix(goodsRecordId);
        //记录备案提交审核记录
        CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(customsCode);
        this.buildTrackLogCommitAuditAndSend(goodsRecordId, districtEnum.getDesc());

    }

    @Autowired
    private CentralGoodsRecordBaseService centralGoodsRecordBaseService;

    @Override
    public CentralGoodsRecordDetailDTO getCentralGoodsRecordDetail(CentralGoodsRecordDetailSearchCondition searchDTO, String customsCode) {
        GoodsRecordDTO goodsRecordDTO = centralGoodsRecordBaseService.getDetailByCondition(searchDTO);
        if (Objects.isNull(goodsRecordDTO)) {
            return null;
        }
        CentralGoodsRecordDetailDTO centralGoodsRecordDetailDTO = new CentralGoodsRecordDetailDTO();
        CentralGoodsRecordBaseInfoDTO centralGoodsRecordBaseInfoDTO = ConvertUtil.beanConvert(goodsRecordDTO, CentralGoodsRecordBaseInfoDTO.class);
        centralGoodsRecordDetailDTO.setBaseInfoVO(centralGoodsRecordBaseInfoDTO);
        if (Objects.nonNull(customsCode)) {
            RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findByRecordIdAndCustomsCode(goodsRecordDTO.getId(), searchDTO.getCustomsCode());
            if (Objects.isNull(recordCustomsDO)) {
                throw new ArgsInvalidException("未查询到当前备案在该口岸的数据");
            }
            if (!Objects.equals(recordCustomsDO.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode()) && Objects.nonNull(recordCustomsDO.getBaseInfoJson())) {
                GoodsRecordDTO recordDTO = JSON.parseObject(recordCustomsDO.getBaseInfoJson(), GoodsRecordDTO.class);
                BeanUtils.copyProperties(recordDTO, centralGoodsRecordBaseInfoDTO, "id", "recordStatus", "createTime", "updateTime", "createBy", "updateBy", "guanWuRemark");
            }
            centralGoodsRecordDetailDTO.setBaseInfoVO(centralGoodsRecordBaseInfoDTO);
            CentralGoodsRecordCustomsInfoDTO customsInfoDTO = ConvertUtil.beanConvert(recordCustomsDO, CentralGoodsRecordCustomsInfoDTO.class);
            List<CentralGoodsRecordCustomsInfoDTO> customsInfoDTOList = Arrays.asList(customsInfoDTO);
            centralGoodsRecordDetailDTO.setCustomsWarehouseInfoVOList(customsInfoDTOList);
        } else {
            List<RecordCustomsDO> recordCustomsDOList = recordCustomsBaseService.findByRecordId(goodsRecordDTO.getId());
            List<CentralGoodsRecordCustomsInfoDTO> customsInfoDTOList = ConvertUtil.listConvert(recordCustomsDOList, CentralGoodsRecordCustomsInfoDTO.class);
            centralGoodsRecordDetailDTO.setCustomsWarehouseInfoVOList(customsInfoDTOList);
        }
        log.debug("getCentralGoodsRecordDetail detail={}", JSON.toJSONString(centralGoodsRecordDetailDTO));
        return centralGoodsRecordDetailDTO;
    }

    @Override
    public List<RecordCustomsDTO> getCustomsListByUserId(String userId) {
        if (StringUtils.isEmpty(userId)) {
            return new ArrayList<>();
        }
        return recordCustomsBaseService.findByUserId(userId, null);
    }

    @Override
    public List<RecordCustomsDTO> getCustomsListByUserIdAndGoodsCode(String userId, String goodsCode) {
        if (StringUtils.isEmpty(userId)) {
            return new ArrayList<>();
        }
        return recordCustomsBaseService.findByUserId(userId, goodsCode);
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public Long saveAndAuditGoodsRecord(CentralGoodsRecordSubmit centralGoodsRecordSubmit) {
        Long recordId = this.saveCentralCustomsRecord(centralGoodsRecordSubmit);
        String customsCode = centralGoodsRecordSubmit.getCentralGoodsRecordWarehouseInfoDTO().getCustomsCode();
        this.auditCentralGoodsRecord(recordId, customsCode);
        return recordId;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCentralImgInfo(List<CentralUpdateImgInfoSubmit> updateImgInfoSubmitList) {
        updateImgInfoSubmitList.forEach(u -> {
            try {
                GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
                goodsRecordDO.setId(u.getRecordId());
                goodsRecordDO.setFrontImage(u.getFrontImage());
                goodsRecordDO.setSideImage(u.getSideImage());
                goodsRecordDO.setBackImage(u.getBackImage());
                goodsRecordDO.setLabelImage(u.getLabelImage());
                goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);
                centralGoodsRecordBaseService.updateCentralRecordCustomsImgInfo(u.getRecordId(), u.getFrontImage(), u.getSideImage(), u.getBackImage(), u.getLabelImage());
            } catch (Exception e) {
                log.error("updateCentralImgInfo 备案中心化更新图片信息时异常 error={}", e.getMessage(), e);
            }
        });
    }

    @Override
    public void updateCentralNameAndBarCode(List<CentralUpdateNameAndBarCodeSubmit> centralUpdateNameAndBarCodeSubmitList) {
        for (CentralUpdateNameAndBarCodeSubmit u : centralUpdateNameAndBarCodeSubmitList) {
            if (Objects.isNull(u.getRecordId())) {
                continue;
            }
            GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
            goodsRecordDO.setId(u.getRecordId());
            goodsRecordDO.setGoodsRecordName(u.getGoodsRecordName());
            goodsRecordDO.setBarCode(u.getBarCode());
            goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);
        }
    }

    @Override
    public GoodsRecordEsDTO buildGoodsRecordEsDTO(Long goodsRecordId) {
        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(goodsRecordId);
        return this.buildGoodsRecordEsDTO(goodsRecordDTO);
    }

    @Override
    public GoodsRecordEsDTO buildGoodsRecordEsDTO(GoodsRecordDTO goodsRecordDTO) {
        if (Objects.isNull(goodsRecordDTO)) {
            return null;
        }
        GoodsRecordEsDTO goodsRecordEsDTO = new GoodsRecordEsDTO();
        BeanUtil.copyProperties(goodsRecordDTO, goodsRecordEsDTO);

        if (Objects.nonNull(goodsRecordDTO.getGoodsRecordTag())) {
            List<Integer> goodsRecordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordDTO.getGoodsRecordTag());
            goodsRecordEsDTO.setGoodsRecordTagList(goodsRecordTag);
        }
        List<RecordCustomsESDTO> recordCustomsDTOList = recordCustomsService.queryByRecordId(goodsRecordDTO.getId());
        goodsRecordEsDTO.setEsRecordCustomsDTO(recordCustomsDTOList);
        //构造商品列表数据
        List<RecordProductDTO> recordProductList = recordProductBaseService.findByRecordId(goodsRecordDTO.getId());
        if (!CollectionUtils.isEmpty(recordProductList)) {
            List<RecordProductESDTO> recordProductEsDOList = recordProductList.stream().map(r -> {
                RecordProductESDTO recordProductEsDO = ConvertUtil.beanConvert(r, RecordProductESDTO.class);
                recordProductEsDO.setDeleted(r.getDeleted() ? 1 : 0);
                return recordProductEsDO;
            }).collect(Collectors.toList());
            goodsRecordEsDTO.setEsRecordProducts(recordProductEsDOList);
        }
        List<RecordWarehouseESDTO> recordWarehouseDTOList = recordWarehouseService.findByRecordId(goodsRecordDTO.getId());
        List<RecordWarehouseProductIdDTO> productIdDTOS = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordDTO.getId());
        Map<Long, List<String>> warehouseIdProductIdMap = productIdDTOS.stream().collect(
                Collectors.groupingBy(
                        RecordWarehouseProductIdDTO::getRecordWarehouseId,
                        Collectors.mapping(RecordWarehouseProductIdDTO::getCustomsDeclareProductId, Collectors.toList())
                ));
        if (!CollectionUtils.isEmpty(recordWarehouseDTOList)) {
            for (RecordWarehouseESDTO recordWarehouseESDTO : recordWarehouseDTOList) {
                if (warehouseIdProductIdMap.containsKey(recordWarehouseESDTO.getId())) {
                    List<String> productIdList = warehouseIdProductIdMap.get(recordWarehouseESDTO.getId());
                    recordWarehouseESDTO.setCustomsDeclareProductId(productIdList);
                }
            }
            goodsRecordEsDTO.setEsRecordWarehouseDTOS(recordWarehouseDTOList);
        }
        log.info("buildGoodsRecordEsDTO goodsRecordEsDTO:{}", JSON.toJSONString(goodsRecordEsDTO));
        return goodsRecordEsDTO;
    }

    @Override
    public GoodsRecordDTO findByExternalGoodsId(String externalGoodsId) {
        if (StringUtil.isBlank(externalGoodsId)) {
            return null;
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("externalGoodsId", externalGoodsId)
                .andEqualTo("deleted", false);
        List<GoodsRecordDO> goodsRecordDOS = goodsRecordMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(goodsRecordDOS)) {
            return ConvertUtil.beanConvert(goodsRecordDOS.get(0), GoodsRecordDTO.class);
        }
        return null;
    }

    @Override
    public List<GoodsRecordDTO> findByExternalGoodsIds(List<String> externalGoodsIds) {
        if (CollUtil.isEmpty(externalGoodsIds)) {
            return new ArrayList<>();
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andIn("externalGoodsId", externalGoodsIds)
                .andEqualTo("deleted", false);
        List<GoodsRecordDO> goodsRecordDOS = goodsRecordMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(goodsRecordDOS)) {
            return ConvertUtil.listConvert(goodsRecordDOS, GoodsRecordDTO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public GoodsRecordDTO findByCainiaoGoodsId(String cainiaoGoodsId) {
        if (StringUtil.isBlank(cainiaoGoodsId)) {
            return null;
        }
        Example example = new Example(GoodsRecordDO.class);
        example.createCriteria().andEqualTo("cainiaoGoodsId", cainiaoGoodsId)
                .andEqualTo("deleted", false);
        List<GoodsRecordDO> goodsRecordDOS = goodsRecordMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(goodsRecordDOS)) {
            return ConvertUtil.beanConvert(goodsRecordDOS.get(0), GoodsRecordDTO.class);
        }
        return null;
    }

    private GoodsRecordDTO getGoodsRecordDTO(GoodsRecordSyncSubmitDTO submit, CustomsBookDTO customsBookDTO) {
        GoodsRecordDTO old;
        //如果是老料号逻辑 就取老的料号
        if (GoodsRecordTypeEnums.OLD.getCode().equals(submit.getRecordType())) {
            // 账册ID+useId+skuId数据唯一
            old = this.findByBookIdUserIdAndSkuId(customsBookDTO.getId(), submit.getTenantId(), submit.getSkuId());
        } else {
            // 走新料号逻辑 按照唯一的料号去取备案
            old = this.findByProId(submit.getProductId());
        }
        return old;
    }

    private GoodsRecordSyncSubmitDTO prepareSubmitDTO(CentralGoodsRecordSubmit centralGoodsRecordSubmit) {
        GoodsRecordSyncSubmitDTO submit = ConvertUtil.beanConvert(centralGoodsRecordSubmit.getCentralGoodsRecordBaseInfoDTO(), GoodsRecordSyncSubmitDTO.class);
        submit.setTenantId(centralGoodsRecordSubmit.getCentralGoodsRecordBaseInfoDTO().getUserId());
        submit.setTenantName(centralGoodsRecordSubmit.getCentralGoodsRecordBaseInfoDTO().getUserName());
        submit.setRecordType(centralGoodsRecordSubmit.getRecordType());
        submit.setGoodsSource(GoodsRecordEnum.MERCHANT.getCode());
        submit.setAutoRecordFlag(centralGoodsRecordSubmit.getAutoRecordFlag());
        if (Objects.nonNull(centralGoodsRecordSubmit.getSource())) {
            submit.setSource(String.valueOf(centralGoodsRecordSubmit.getSource()));
        }
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (StringUtils.hasText(inputError)) {
            throw new ArgsInvalidException(inputError);
        }
        return submit;
    }

    private CustomsHsDTO validateHs(String hsCode) {
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(hsCode);
        if (customsHsDTO == null) {
            throw new ArgsInvalidException("HS编码错误");
        } else {
            if (customsHsDTO.getTariff() == null || customsHsDTO.getVat() == null) {
                throw new ArgsInvalidException("hsCode:" + customsHsDTO.getHsCode() + "税率未完善");
            }
        }
        return customsHsDTO;
    }

    private CustomsUomDTO validateUom(String declareUnit) {
        CustomsUomDTO customsUomDTO = customsUomService.findByCode(declareUnit);
        if (customsUomDTO == null) {
            throw new ArgsInvalidException("申报单位错误");
        }
        return customsUomDTO;
    }

    private CustomsCountryDTO validateCountry(String originCountry) {
        CustomsCountryDTO countryDTO = customsCountryService.findByCode(originCountry);
        if (countryDTO == null) {
            throw new ArgsInvalidException("海关原产国错误");
        }
        return countryDTO;
    }


    @Transactional(rollbackFor = Exception.class)
    public void postGoodsRecordSaveProcess(GoodsRecordSyncSubmitDTO submit, CentralGoodsRecordSubmit centralGoodsRecordSubmit, String baseJsonInfo) {
        log.info("saveCentralCustomsRecord postGoodsRecordSaveProcess submit={} centralGoodsRecordSubmit={} recordId={}"
                , JSON.toJSONString(submit), JSON.toJSONString(centralGoodsRecordSubmit), submit.getId());
        Long recordId = submit.getId();
        GoodsRecordDTO recordDTO = this.findById(recordId);
        log.info("saveCentralCustomsRecord postGoodsRecordSaveProcess recordDTO={}", JSON.toJSONString(recordDTO));
        if (Objects.isNull(recordDTO)) {
            return;
        }
//        if (Boolean.FALSE.equals(submit.getAutoRecordFlag())) {
        //老料号口岸更新; 自动备案更新 不修改口岸状态
//            recordCustomsService.updateStatus(recordId, submit.getCustoms(), GoodsRecordStatusEnum.WAIT_COMMIT.getCode(), null, RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode());
//        }
        if (GoodsRecordTypeEnums.NEW.getCode().equals(submit.getRecordType())) {
            GoodsRecordActionEnum actionEnum = (Boolean.TRUE.equals(submit.getAutoRecordFlag())) ? GoodsRecordActionEnum.AUTO : GoodsRecordActionEnum.UPDATE;
            log.info("saveCentralCustomsRecord postGoodsRecordSaveProcess  action={}", actionEnum.getCode());
            handleCentralNewCustomsAndWarehouse(submit, centralGoodsRecordSubmit, baseJsonInfo, recordDTO, actionEnum);
        }
        try {
            goodsRecordDumpProducer.send(recordId);
        } catch (Exception e) {
            log.error("syncCustomsRecordV2 同步商品备案ES失败 error={}", e.getMessage(), e);
        }
    }

    private void handleCentralNewCustomsAndWarehouse(GoodsRecordSyncSubmitDTO submit, CentralGoodsRecordSubmit centralGoodsRecordSubmit, String baseJsonInfo, GoodsRecordDTO recordDTO, GoodsRecordActionEnum actionEnum) {
        log.info("postGoodsRecordSaveProcess handleRecordCustomsWarehouse recordId={} submit={}", recordDTO.getId(), JSON.toJSONString(submit));
        Long recordId = recordDTO.getId();
        //获取下发的实体仓配置
        CentralGoodsRecordWarehouseInfoDTO centralGoodsRecordWarehouseInfoDTO = centralGoodsRecordSubmit.getCentralGoodsRecordWarehouseInfoDTO();
        if (Objects.isNull(centralGoodsRecordWarehouseInfoDTO) || CollectionUtils.isEmpty(centralGoodsRecordWarehouseInfoDTO.getWmsWarehouseList())) {
            log.info("postGoodsRecordSaveProcess handleNewCustomsAndWarehouse 提交的实体仓列表为空");
            return;
        }
        List<String> wmsWarehouseList = centralGoodsRecordWarehouseInfoDTO.getWmsWarehouseList();
        String customsCode = centralGoodsRecordWarehouseInfoDTO.getCustomsCode();
        CustomsDistrictEnum customs = CustomsDistrictEnum.getEnum(customsCode);
        Boolean isNewCustoms = recordCustomsService.checkIsNewCustoms(recordId, customsCode);
        List<EntityWarehouseDTO> unsavedWarehouseList;
        if (isNewCustoms) {
            //新口岸直接根据提供的列表获取实体仓
            unsavedWarehouseList = entityWarehouseService.findDTOByWmsCode(wmsWarehouseList);
            List<String> existWmsList = unsavedWarehouseList.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).collect(Collectors.toList());
            String unExistWms = CollUtil.subtract(wmsWarehouseList, existWmsList).stream().collect(Collectors.joining("/"));
            if (!StringUtils.isEmpty(unExistWms)) {
                throw new ArgsInvalidException("实体仓:" + unExistWms + "未在CCS系统中配置");
            }
            String itemTrackLog = null;
            if (GoodsRecordActionEnum.AUTO.equals(actionEnum)) {
                itemTrackLog = ItemTrackLogConfig.ITEM_TYPE_ERP_AUTO;
            }
            if (Objects.nonNull(submit.getSource())) {
                //通过来源创建日志
                itemTrackLog = GoodsRecordSourceEnums.getEnums(centralGoodsRecordSubmit.getSource()).getItemTrackLog();
            }
            if (Objects.nonNull(itemTrackLog)) {
                goodsRecordService.buildTrackLogNewAndSend(recordId, itemTrackLog, Objects.nonNull(customs) ? customs.getDesc() : null);
            }
        } else {
            //如果是老口岸 获取未保存的实体仓
            unsavedWarehouseList = recordWarehouseService.getUnsavedWarehouseList(recordId, customsCode, wmsWarehouseList);
            GoodsRecordDTO submitDTO = ConvertUtil.beanConvert(centralGoodsRecordSubmit.getCentralGoodsRecordBaseInfoDTO(), GoodsRecordDTO.class);
            submitDTO.setDeclarePrice(submitDTO.getDeclarePrice().setScale(5, RoundingMode.HALF_UP));
            submitDTO.setNetWeight(submitDTO.getNetWeight().setScale(5, RoundingMode.HALF_UP));
            submitDTO.setGrossWeight(submitDTO.getGrossWeight().setScale(5, RoundingMode.HALF_UP));
            submitDTO.setFirstUnitAmount(submitDTO.getFirstUnitAmount().setScale(5, RoundingMode.HALF_UP));
            if (Objects.nonNull(submitDTO.getSecondUnitAmount())) {
                submitDTO.setSecondUnitAmount(submitDTO.getSecondUnitAmount().setScale(5, RoundingMode.HALF_UP));
            }
            //骚操作跳过校验
            submitDTO.setVatRate(recordDTO.getVatRate());
            submitDTO.setTaxRate(recordDTO.getTaxRate());
            submitDTO.setCountryRecordNo(recordDTO.getCountryRecordNo());
            String itemTrackLog = ItemTrackLogConfig.UPDATE_GOODS;
            if (StringUtil.isNotEmpty(submit.getSource())) {
                String sourceSystem = GoodsRecordSourceEnums.getEnums(centralGoodsRecordSubmit.getSource()).getSystem();
                if ("erp".equalsIgnoreCase(sourceSystem)) {
                    itemTrackLog = ItemTrackLogConfig.UPDATE_GOODS_ERP;
                }
            }
            goodsRecordService.buildTrackLogDiffAndSend(recordDTO, submitDTO, Objects.nonNull(customs) ? customs.getDesc() : null, itemTrackLog);
        }
        unsavedWarehouseList.forEach(u -> {
            if (!customsCode.equals(u.getCustomsCode())) {
                throw new ArgsInvalidException("提交的实体仓" + u.getErpWarehouseName() + "不属于当前推送口岸");
            }
        });
        String customsBookNo = centralGoodsRecordSubmit.getCentralGoodsRecordBaseInfoDTO().getCustomsBookNo();
        if (GoodsRecordActionEnum.AUTO.equals(actionEnum) && StringUtil.isNotBlank(customsBookNo)) {
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(customsBookNo);
            if (Objects.nonNull(customsBookDTO)) {
                recordDTO.setCustomsBookId(customsBookDTO.getId());
            } else {
                log.info("handleRecordCustomsWarehouse 账册编号不存在：{}", customsBookNo);
            }
        }
        log.info("handleRecordCustomsWarehouse unsavedWarehouseList={}", JSON.toJSONString(unsavedWarehouseList));
        // 保存新的关联关系
        recordWarehouseService.saveCentralGoodsNewRelation(recordDTO, baseJsonInfo, unsavedWarehouseList, isNewCustoms, customsCode, actionEnum.getCode());
    }

    public void buildTaxTenantIfNotExist(GoodsRecordSyncSubmitDTO submit, GoodsRecordDO recordDO) {
        //现在的userId都是1L
        TaxesTenantAccountDTO taxesTenantAccountDTO = taxesTenantAccountService.findByUserIdAndTenantId(1L, submit.getTenantId());
        if (taxesTenantAccountDTO == null) {
            TaxesTenantAccountSubmit tenantAccountSubmit = new TaxesTenantAccountSubmit();
            tenantAccountSubmit.setEnable(EnableStatus.CAN.getValue());
            tenantAccountSubmit.setName(submit.getTenantName());
            tenantAccountSubmit.setTenantId(submit.getTenantId());
            tenantAccountSubmit.setConsumedAmount(new BigDecimal("0"));
            tenantAccountSubmit.setRechargeTotal(new BigDecimal("0"));
            tenantAccountSubmit.setAvailable(new BigDecimal("0"));
            tenantAccountSubmit.setUserId(1L);
            try {
                taxesTenantAccountService.save(tenantAccountSubmit);
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
        }
        recordDO.setTenantId(submit.getTenantId());
    }

    /**
     * 只要在备案审核中提交的口岸一律变成待审核状态
     *
     * @param recordId
     * @param submit
     */
    @Override
    public void handleRecordCustomsWarehouse(Long recordId, GoodsRecordSyncSubmitV2 submit) {
        GoodsRecordActionEnum actionEnum = (Boolean.TRUE.equals(submit.getAutoRecordFlag())) ? GoodsRecordActionEnum.AUTO : GoodsRecordActionEnum.UPDATE;
        this.handleRecordCustomsWarehouse(recordId, submit, actionEnum);
    }

    /**
     * 只要在备案审核中提交的口岸一律变成待审核状态
     *
     * @param recordId
     * @param submit
     */
    @Override
    public void handleRecordCustomsWarehouse(Long recordId, GoodsRecordSyncSubmitV2 submit, GoodsRecordActionEnum actionEnum) {
        log.info("handleRecordCustomsWarehouse recordId={} submit={}", recordId, JSON.toJSONString(submit));
        if (CollectionUtils.isEmpty(submit.getGoodsRecordWarehouseInfo().getWmsWarehouseList())) {
            log.warn("提交的实体仓列表为空");
            throw new ArgsInvalidException("提交的实体仓列表为空");
        }
        GoodsRecordDTO recordDTO = this.findById(recordId);
        log.info("handleRecordCustomsWarehouse recordDTO={}", JSON.toJSONString(recordDTO));

        GoodsRecordWarehouseInfoDTO goodsRecordWarehouseInfo = submit.getGoodsRecordWarehouseInfo();
        List<String> wmsWarehouseList = goodsRecordWarehouseInfo.getWmsWarehouseList();
        String customsCode = goodsRecordWarehouseInfo.getCustomsCode();
        CustomsDistrictEnum customs = CustomsDistrictEnum.getEnum(customsCode);
        Boolean isNewCustoms = recordCustomsService.checkIsNewCustoms(recordId, customsCode);
        List<EntityWarehouseDTO> unsavedWarehouseList;
        if (isNewCustoms) {
            //新口岸直接根据提供的列表获取实体仓
            unsavedWarehouseList = entityWarehouseService.findDTOByWmsCode(wmsWarehouseList);
            validateWmsWarehouseExists(wmsWarehouseList, unsavedWarehouseList);
            String itemTrackLog = determineItemTrackLog(actionEnum, submit.getSource());
            goodsRecordService.buildTrackLogNewAndSend(recordId, itemTrackLog, Objects.nonNull(customs) ? customs.getDesc() : null);
        } else {
            //如果是老口岸 获取未保存的实体仓
            unsavedWarehouseList = recordWarehouseService.getUnsavedWarehouseList(recordId, customsCode, wmsWarehouseList);
            if (Objects.nonNull(submit.getGoodsRecordBaseInfo())) {
                GoodsRecordDTO submitDTO = ConvertUtil.beanConvert(submit.getGoodsRecordBaseInfo(), GoodsRecordDTO.class);
                submitDTO.setDeclarePrice(submitDTO.getDeclarePrice().setScale(5, BigDecimal.ROUND_HALF_UP));
                submitDTO.setNetWeight(submitDTO.getNetWeight().setScale(5, BigDecimal.ROUND_HALF_UP));
                submitDTO.setGrossWeight(submitDTO.getGrossWeight().setScale(5, BigDecimal.ROUND_HALF_UP));
                submitDTO.setFirstUnitAmount(submitDTO.getFirstUnitAmount().setScale(5, BigDecimal.ROUND_HALF_UP));
                if (Objects.nonNull(submitDTO.getSecondUnitAmount())) {
                    submitDTO.setSecondUnitAmount(submitDTO.getSecondUnitAmount().setScale(5, BigDecimal.ROUND_HALF_UP));
                }
                //骚操作跳过校验
                submitDTO.setVatRate(recordDTO.getVatRate());
                submitDTO.setTaxRate(recordDTO.getTaxRate());
                submitDTO.setCountryRecordNo(recordDTO.getCountryRecordNo());
                goodsRecordService.buildTrackLogDiffAndSend(recordDTO, submitDTO, Objects.nonNull(customs) ? customs.getDesc() : null, ItemTrackLogConfig.UPDATE_GOODS);
            }
        }
        // 校验实体仓是否属于当前推送口岸
        validateWarehouseCustomsCode(unsavedWarehouseList, customsCode);

        if (GoodsRecordActionEnum.AUTO.equals(actionEnum) && StringUtil.isNotBlank(submit.getGoodsRecordBaseInfo().getCustomsBookNo())) {
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(submit.getGoodsRecordBaseInfo().getCustomsBookNo());
            if (Objects.nonNull(customsBookDTO)) {
                recordDTO.setCustomsBookId(customsBookDTO.getId());
            } else {
                log.info("handleRecordCustomsWarehouse 账册编号不存在：{}", submit.getGoodsRecordBaseInfo().getCustomsBookNo());
            }
        }
        log.info("handleRecordCustomsWarehouse unsavedWarehouseList={}", JSON.toJSONString(unsavedWarehouseList));
        recordWarehouseService.saveNewRelation(recordDTO, unsavedWarehouseList, isNewCustoms, customsCode, actionEnum.getCode());
    }

    /**
     * 校验 WMS 仓库是否存在
     */
    private void validateWmsWarehouseExists(List<String> wmsWarehouseList, List<EntityWarehouseDTO> unsavedWarehouseList) {
        List<String> existWmsList = unsavedWarehouseList.stream()
                .map(EntityWarehouseDTO::getWmsWarehouseCode)
                .collect(Collectors.toList());
        String unExistWms = CollUtil.subtract(wmsWarehouseList, existWmsList)
                .stream()
                .collect(Collectors.joining("/"));
        if (!StringUtils.isEmpty(unExistWms)) {
            throw new ArgsInvalidException("实体仓:" + unExistWms + "未在CCS系统中配置");
        }
    }

    /**
     * 确定 itemTrackLog 的值
     */
    private String determineItemTrackLog(GoodsRecordActionEnum actionEnum, Integer source) {
        String itemTrackLog = ItemTrackLogConfig.ITEM_TYPE_ERP_FIRST;
        if (GoodsRecordActionEnum.AUTO.equals(actionEnum)) {
            itemTrackLog = ItemTrackLogConfig.ITEM_TYPE_ERP_AUTO;
        }
        if (Objects.nonNull(source)) {
            itemTrackLog = GoodsRecordSourceEnums.getEnums(source).getItemTrackLog();
        }
        return itemTrackLog;
    }

    /**
     * 校验实体仓是否属于当前推送口岸
     */
    private void validateWarehouseCustomsCode(List<EntityWarehouseDTO> unsavedWarehouseList, String customsCode) {
        for (EntityWarehouseDTO warehouse : unsavedWarehouseList) {
            if (!customsCode.equals(warehouse.getCustomsCode())) {
                throw new ArgsInvalidException("提交的实体仓" + warehouse.getErpWarehouseName() + "不属于当前推送口岸");
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitUpdateList(List<GoodsRecordSubmit> updateList) throws ArgsErrorException, ArgsInvalidException {
        try {
            for (GoodsRecordSubmit goodsRecordSubmit : updateList) {
                GoodsRecordSubmitDTO dto = new GoodsRecordSubmitDTO();
                BeanUtils.copyProperties(goodsRecordSubmit, dto);
                dto.setGoodsSource(GoodsRecordEnum.SELF_BUILT.getCode());
                dto.setSource(ItemTrackLogConfig.ITEM_TYPE_IMPORT);
                Long recordId = this.upsetCore(dto);
                // 获取备案
                GoodsRecordSyncSubmitV2 goodsRecordSyncSubmitV2 = bookItemCallbackReceiver.getGoodsRecordSyncSubmitV2ByBookId(goodsRecordSubmit.getCustomsBookId());
                this.handleRecordCustomsWarehouse(recordId, goodsRecordSyncSubmitV2, GoodsRecordActionEnum.IMPORT);
            }
        } catch (ArgsErrorException e) {
            log.error("submitUpdateList error={}", e.getErrorMessage(), e);
            throw new ArgsInvalidException(e.getErrorMessage());
        }
    }

    @Override
    public ImportResultResVo submitUpdateListNew(GoodsRecordSubmit submit) throws ArgsErrorException, ArgsInvalidException {
        ImportResultResVo res = new ImportResultResVo();
        res.setFlag(Boolean.TRUE);
        try {
            this.submitUpdateList(Arrays.asList(submit));
        } catch (ArgsInvalidException e) {
            log.info("submitUpdateList error={}", e.getErrorMessage(), e);
            res.setFlag(false);
            res.setReason(e.getErrorMessage());
        } catch (Exception e) {
            log.error("submitUpdateList error={}", e.getMessage(), e);
            res.setFlag(false);
            res.setReason(e.getMessage());
        }
        return res;
    }

    /**
     * 修改关联料号
     *
     * @param submit
     */
    @Override
    public void modifyAssociatedInfo(RecordModifyAssociateInfoSubmit submit) throws ArgsErrorException {
        recordItemAssociateInfoService.modifyAssociatedInfo(submit);
    }

    @Override
    public RecordItemAssociateInfoResVO viewAssociatedInfoVO(Long goodsRecordId) throws ArgsErrorException {
        RecordItemAssociateInfoResVO info = new RecordItemAssociateInfoResVO();
        try {
            RecordItemAssociateInfoDTO dto = this.viewAssociatedInfoDTO(goodsRecordId);
            if (Objects.isNull(dto) || Objects.isNull(dto.getId())) {
                throw new ArgsErrorException("备案关联清关信息不存在");
            }
            log.info("viewAssociatedInfo RecordItemAssociateInfoDTO={}", JSON.toJSONString(dto));
            this.fillInfoVO(dto, info);
        } catch (ArgsErrorException e) {
            log.warn("viewAssociatedInfo 查看关联清关信息 id={} error={}", goodsRecordId, e.getErrorMessage(), e);
        } catch (Exception e) {
            log.warn("viewAssociatedInfo 查看关联清关信息 id={} error={}", goodsRecordId, e.getMessage(), e);
        }
        return info;
    }


    @Override
    public RecordItemAssociateInfoDTO viewAssociatedInfoDTO(Long goodsRecordId) throws ArgsErrorException {
        RecordItemAssociateInfoDTO dto = new RecordItemAssociateInfoDTO();
        try {
            GoodsRecordDTO recordDTO = this.findById(goodsRecordId);
            if (Objects.isNull(recordDTO)) {
                throw new ArgsErrorException("商品备案不存在");
            }
            dto = recordItemAssociateInfoService.viewAssociatedInfo(goodsRecordId);
//            if (Objects.isNull(dto.getId())) {
//                throw new ArgsErrorException("备案关联清关信息不存在");
//            }
            log.info("viewAssociatedInfo RecordItemAssociateInfoDTO={}", JSON.toJSONString(dto));
        } catch (ArgsErrorException e) {
            log.warn("viewAssociatedInfo 查看关联清关信息 id={} error={}", goodsRecordId, e.getErrorMessage(), e);
        } catch (Exception e) {
            log.warn("viewAssociatedInfo 查看关联清关信息 id={} error={}", goodsRecordId, e.getMessage(), e);
        }
        return dto;
    }

    @Override
    public GoodsRecordAuditPassResVO auditPassPreCheck(Long id) throws ArgsErrorException {
        return this.auditPassPreCheck(id, null);
    }

    /**
     * 审核通过前 预校验
     * 该料号在账册库存信息所有的法一法二数量是否一致
     *
     * @param id
     * @return
     */
    @Override
    public GoodsRecordAuditPassResVO auditPassPreCheck(Long id, String customsCode) throws ArgsErrorException {
        log.info("auditPassPreCheck id={} customsCode={}", id, customsCode);
        GoodsRecordDTO goodsRecordDTO = this.findById(id);
        log.info("auditPassPreCheck id={} goodsRecordDTO={}", JSON.toJSONString(goodsRecordDTO));
        String productId = goodsRecordDTO.getProductId();
        if (Objects.isNull(customsCode)) {
            log.info("auditPassPreCheck 口岸code为空");
            throw new ArgsErrorException("口岸code为空");
        }
        RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(id, customsCode);
        if (Objects.isNull(recordCustomsDTO)) {
            log.info("auditPassPreCheck 未找到备案关联口岸");
            throw new ArgsErrorException("未找到备案关联口岸");
        }
        List<RecordWarehouseDTO> recordWarehouseInfoList = recordWarehouseService.getRecordWarehouseInfo(recordCustomsDTO.getId());
        if (CollectionUtils.isEmpty(recordWarehouseInfoList)) {
            log.info("auditPassPreCheck 该备案关联实体仓为空");
            throw new ArgsErrorException("该备案关联实体仓为空");
        }
        GoodsRecordAuditPassResVO resVO = new GoodsRecordAuditPassResVO();
        resVO.setGoodsRecordId(goodsRecordDTO.getId());
        resVO.setCustoms(recordCustomsDTO.getCustoms());
        resVO.setCustomsCode(recordCustomsDTO.getCustomsCode());
        resVO.setProductId(goodsRecordDTO.getProductId());
        resVO.setHsCode(recordCustomsDTO.getHsCode());
        resVO.setExternalProductId(goodsRecordDTO.getExternalProductId());
        resVO.setFirstUnitAmount(recordCustomsDTO.getFirstUnitAmount());
        resVO.setFirstUnit(recordCustomsDTO.getFirstUnit());
        resVO.setSecondUnitAmount(recordCustomsDTO.getSecondUnitAmount());
        resVO.setSecondUnit(recordCustomsDTO.getSecondUnit());
        log.info("auditPassPreCheck id={} resVO={}", JSON.toJSONString(resVO));
        if (Objects.isNull(productId)) {
            log.info("商品备案没有料号 直接返回");
            return resVO;
        }
//        StringBuffer firstSb = new StringBuffer();
//        StringBuffer secondSb = new StringBuffer();
//        for (RecordWarehouseDTO r : recordWarehouseInfoList) {
//            List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(r.getCustomsBookId(), productId);
//            if (!CollectionUtils.isEmpty(customsBookItemDTOList)) {
//                BigDecimal firstUnitAmount = goodsRecordDTO.getFirstUnitAmount();
//                customsBookItemDTOList.forEach(c -> {
//                    if (!Objects.equals(c.getFirstUnitAmount(), firstUnitAmount)) {
//                        firstSb.append("账册号:" + r.getCustomsBookNo() + "金二序号:" + c.getGoodsSeqNo() + ",料号:" + c.getProductId() + ",法定第一数量:" + c.getFirstUnitAmount() + "不一致");
//                    }
//                    if (Objects.nonNull(goodsRecordDTO.getSecondUnitAmount())) {
//                        if (!Objects.equals(c.getSecondUnitAmount(), goodsRecordDTO.getSecondUnitAmount())) {
//                            secondSb.append("账册号:" + r.getCustomsBookNo() + "金二序号:" + c.getGoodsSeqNo() + ",料号:" + c.getProductId() + ",法定第二数量:" + c.getSecondUnitAmount() + "不一致");
//                        }
//                    }
//                });
//            }
//            if (firstSb.length() > 0) {
//                resVO.setFirstUnitCheckResult(firstSb.toString());
//            }
//            if (secondSb.length() > 0) {
//                resVO.setSecondUnitCheckResult(secondSb.toString());
//            }
//        }
        return resVO;
    }

    /**
     * 根据备案展示第三个tab 关联料号的账册信息
     *
     * @param id
     * @return
     */
    @Override
    public List<AssociateCustomsBookItemResVO> viewItemListByGoodsRecordId(Long id) throws ArgsErrorException {
        if (id == null) {
            throw new ArgsErrorException("查询id为空");
        }
        String productId;
        GoodsRecordDTO recordDTO = this.findById(id);
        if (Objects.isNull(recordDTO)) {
            throw new ArgsErrorException("未找到关联备案信息");
        }
        RecordItemAssociateInfoResVO associateInfoResVO = this.viewAssociatedInfoVO(id);
        if (Objects.nonNull(associateInfoResVO)) {
            productId = associateInfoResVO.getAssociatedProductId();
        } else {
            productId = recordDTO.getProductId();
        }
        List<AssociateCustomsBookItemResVO> associateCustomsBookItemResVOS = customsBookItemService.viewItemListByProductId(productId, recordDTO.getCustomsBookId());
        return associateCustomsBookItemResVOS;
    }

    @Override
    public RecordItemAssociateInfoDTO getAssociateInfo(Long recordId) {
        if (recordId == null) {
            return null;
        }
        RecordItemAssociateInfoDTO recordItemAssociateInfoDTO = recordItemAssociateInfoService.viewAssociatedInfo(recordId);
        return recordItemAssociateInfoDTO;
    }

    @Override
    public RecordItemAssociateInfoDTO getAssociateInfo(Long customsBookId, String productId) {
        GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookId, productId);
        if (Objects.nonNull(goodsRecordDTO)) {
            return this.getAssociateInfo(goodsRecordDTO.getId());
        } else {
            return null;
        }
    }

    /**
     * 先查关联表 获取通关信息关联料号，如果没有则获取下备案关联俩号
     * 兼容老的备案
     *
     * @param recordId
     * @return
     */
    @Override
    public String getAssociateProductId(Long recordId) {
        try {
            RecordItemAssociateInfoDTO recordItemAssociateInfoDTO = this.getAssociateInfo(recordId);
            log.info("getAssociateProductId 获取关联info DTO={}", JSON.toJSONString(recordItemAssociateInfoDTO));
            if (Objects.nonNull(recordItemAssociateInfoDTO)) {
                return recordItemAssociateInfoDTO.getAssociatedProductId();
            }
//            GoodsRecordDTO goodsRecordDTO = goodsRecordBaseService.selectByPrimaryKey(recordId);
//            return Optional.ofNullable(goodsRecordDTO).map(GoodsRecordDTO::getExternalProductId).orElse(null);
        } catch (Exception e) {
            log.error("getAssociateProductId 获取关联关系异常", e);
        }
        return null;
    }

    /**
     * 拿账册号+料号查询备案
     *
     * @param customsBookId
     * @param productId
     * @return
     */
    @Override
    public String getAssociateProductId(Long customsBookId, String productId) {
        if (Objects.isNull(customsBookId)) {
            throw new ArgsErrorException("账册id为空");
        }
        if (Objects.isNull(productId)) {
            throw new ArgsErrorException("料号为空");
        }
        GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookId, productId);
        if (Objects.nonNull(goodsRecordDTO)) {
            return this.getAssociateProductId(goodsRecordDTO.getId());
        } else {
            return null;
        }
    }


    /**
     * 填充一下 申报单位 币制等说明
     *
     * @param dto
     * @param info
     */
    private void fillInfoVO(RecordItemAssociateInfoDTO dto, RecordItemAssociateInfoResVO info) {
        BeanUtils.copyProperties(dto, info);
        GoodsRecordDTO goodsRecordDTO = this.findById(dto.getGoodsRecordId());
        log.info("fillInfoVO goodsRecordDTO={}", JSON.toJSONString(goodsRecordDTO));
        if (Objects.isNull(goodsRecordDTO)) {
            return;
        }
        List<CustomsBookItemDTO> associateItemList = customsBookItemService.findByBookIdAndProId(dto.getCustomsBookId(), dto.getAssociatedProductId());
        if (CollectionUtils.isEmpty(associateItemList)) {
            return;
        }
        CustomsBookItemDTO customsBookItemDTO = associateItemList.stream().findFirst().orElse(null);
        if (customsBookItemDTO == null) {
            return;
        }
        info.setProductName(customsBookItemDTO.getGoodsName());
        info.setModel(customsBookItemDTO.getGoodsModel());
        info.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
        info.setVatRate(goodsRecordDTO.getVatRate());
        info.setTaxRate(goodsRecordDTO.getTaxRate());
        info.setNetWeight(goodsRecordDTO.getNetWeight());
        info.setGrossWeight(goodsRecordDTO.getGrossWeight());
        info.setFirstUnitAmount(customsBookItemDTO.getFirstUnitAmount());
        info.setSecondUnitAmount(customsBookItemDTO.getSecondUnitAmount());
        if (Objects.nonNull(dto.getCustomsBookId())) {
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(dto.getCustomsBookId());
            if (Objects.nonNull(customsBookResVo)) {
                info.setCustomsBookNo(customsBookResVo.getBookNo());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getFirstUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(customsBookItemDTO.getFirstUnit());
            if (Objects.nonNull(uomDTO)) {
                info.setFirstUnit(customsBookItemDTO.getFirstUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getSecondUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(customsBookItemDTO.getSecondUnit());
            if (Objects.nonNull(uomDTO)) {
                info.setSecondUnit(customsBookItemDTO.getSecondUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getOriginCountry())) {
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(customsBookItemDTO.getOriginCountry());
            if (Objects.nonNull(countryDTO)) {
                info.setOriginCountry(customsBookItemDTO.getOriginCountry() + ":" + countryDTO.getName());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getGoodsUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(customsBookItemDTO.getGoodsUnit());
            if (Objects.nonNull(uomDTO)) {
                info.setDeclareUnit(customsBookItemDTO.getGoodsUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getCurrCode())) {
            CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(customsBookItemDTO.getCurrCode());
            if (Objects.nonNull(currencyDTO)) {
                info.setDeclareCurrency(customsBookItemDTO.getCurrCode() + ":" + currencyDTO.getName());
            }
        }
        if (Objects.nonNull(customsBookItemDTO.getHsCode())) {
            CustomsHsDTO customsHsDTO = customsHsService.findByCode(customsBookItemDTO.getHsCode());
            if (Objects.nonNull(customsHsDTO)) {
                info.setHsCode(customsBookItemDTO.getHsCode() + ":" + customsHsDTO.getHsName());
            }
        }
    }

    @Override
    public ListVO<RecordTrackLogEsDTO> pagingTrackLog(RecordTrackLogSearch search) {
        Page<RecordTrackLogEsDO> page = goodsRecordEsDao.paging(search);
        ListVO<RecordTrackLogEsDTO> recordTrackLogEsDTOListVO = new ListVO<>();
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(page.getTotalElements());
        pageResult.setTotalPage(page.getTotalPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        recordTrackLogEsDTOListVO.setPage(pageResult);
        List<RecordTrackLogEsDTO> recordTrackLogEsDTOS = page.getContent().stream().map(p -> {
            RecordTrackLogEsDTO recordTrackLogEsDTO = new RecordTrackLogEsDTO();
            BeanUtils.copyProperties(p, recordTrackLogEsDTO);
            return recordTrackLogEsDTO;
        }).collect(Collectors.toList());
        recordTrackLogEsDTOListVO.setDataList(recordTrackLogEsDTOS);
        return recordTrackLogEsDTOListVO;
    }

    private void trackLogEsSaveSend(RecordTrackLogSaveDTO recordTrackLogSaveDTO) {
        log.info("trackLogEsSaveSend recordTrackLogSaveDTO={}", JSON.toJSONString(recordTrackLogSaveDTO));
        try {
            messageSender.sendMsg(recordTrackLogSaveDTO, ItemTrackLogConfig.GOODS_RECORD_TRACK_LOG_TOPIC);
        } catch (Exception e) {
            log.error("trackLogEsSaveSend error={}", e.getMessage(), e);
            log.error("日志mq发送异常 改为直接调用方法");
            this.trackLogEsSave(recordTrackLogSaveDTO);
        }
    }

    @Override
    public void trackLogEsSave(RecordTrackLogSaveDTO recordTrackLogSaveDTO) {
        log.info("trackLogEsSave recordTrackLogSaveDTO={}", JSON.toJSONString(recordTrackLogSaveDTO));
        goodsRecordEsDao.esSave(recordTrackLogSaveDTO);
    }

    @Override
    public List<GoodsRecordCustomsWarehouseResVO> getCustomsWarehouseInfo(Long id) {
        List<GoodsRecordCustomsWarehouseResVO> customsWarehouseResVOS = goodsRecordBaseService.getWarehouseDetail(id);
        return customsWarehouseResVOS;
    }

    @Override
    public List<RecordCustomsDTO> recordCustomsDTOList(Long recordId) {
        if (Objects.isNull(recordId)) {
            return new ArrayList<>();
        }
        List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsService.findByRecordId(recordId);
        return recordCustomsDTOList;
    }

    @Override
    public List<RecordWarehouseDTO> getRecordWarehouseInfo(Long recordCustomsId) {
        return recordWarehouseService.getRecordWarehouseInfo(recordCustomsId);
    }


    /**
     * 根据操作类型记录日志
     *
     * @param submit
     * @param before
     * @param after
     * @param operateType
     */
    private void buildTrackLogAndSend(GoodsRecordSubmit submit, GoodsRecordDTO before, GoodsRecordDTO after, String operateType) {
        //新建
        if (Objects.isNull(before) && Objects.equals(operateType, ItemTrackLogConfig.CREATE)) {
            this.buildTrackLogNewAndSend(after.getId(), submit.getSource(), submit.getCustoms());
        }
        this.buildTrackLogDiffAndSend(before, after, submit.getCustoms(), operateType);
    }

    /**
     * 构建erp删除备案日志
     *
     * @param recordDTO
     * @return
     */
    private RecordTrackLogSaveDTO buildTrackLogErpDeleted(GoodsRecordDTO recordDTO) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(recordDTO.getId());
        recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.ERP_DELETE);
        recordTrackLogSaveDTO.setOperateDes("ERP删除备案");
        CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(recordDTO.getCustoms());
        recordTrackLogSaveDTO.setCustoms(districtEnum.getCustoms());
        recordTrackLogSaveDTO.setOperateTime(new Date());
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        return recordTrackLogSaveDTO;
    }

    public void buildTrackLogErpDeletedAndSend(GoodsRecordDTO recordDTO) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogErpDeleted(recordDTO);
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    private void buildTrackLogAuditAndSend(GoodsRecordDO recordDO, Integer recordStatus, String customs) {
        this.buildTrackLogAuditAndSend(recordDO, recordStatus, customs, null);
    }

    private void buildTrackLogAuditAndSend(GoodsRecordDO recordDO, Integer recordStatus, String customs, String reason) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogAudit(recordDO, recordStatus, customs, reason);
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    //需要兼容口岸
    private RecordTrackLogSaveDTO buildTrackLogAudit(GoodsRecordDO recordDO, Integer recordStatus, String customs, String reason) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(recordDO.getId());
        if (Objects.equals(recordStatus, GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {
            recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.AUDIT_PASS);
            recordTrackLogSaveDTO.setOperateDes("备案ID:" + recordDO.getId() + ",备案完成,料号为:" + recordDO.getProductId());
        } else if (Objects.equals(recordStatus, GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode())) {
            recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.AUDIT_REFUSE);
            recordTrackLogSaveDTO.setOperateDes("驳回原因:" + reason);
        } else if (Objects.equals(recordStatus, GoodsRecordStatusEnum.WAIT_EXAMINE.getCode())) {
            recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.AUDIT_SUBMIT);
            recordTrackLogSaveDTO.setOperateDes("备案待审核");
        } else {
            recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.OTHER);
            recordTrackLogSaveDTO.setOperateDes("审核后状态为:" + GoodsRecordStatusEnum.getEnum(recordStatus).getDesc());
        }
        recordTrackLogSaveDTO.setCustoms(customs);
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        recordTrackLogSaveDTO.setOperateTime(new Date());
        return recordTrackLogSaveDTO;
    }

    //需要兼容口岸
    public void buildTrackLogCommitAuditAndSend(Long recordId, String customs) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(recordId);
        recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.AUDIT_SUBMIT);
        recordTrackLogSaveDTO.setOperateDes("备案待审核");
        recordTrackLogSaveDTO.setCustoms(customs);
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        recordTrackLogSaveDTO.setOperateTime(new Date());
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    public void buildTrackLogNewAndSend(Long goodsRecordId, String source, String customs) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogNew(goodsRecordId, source, customs);
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    @Override
    public GoodsRecordStatusCount getRecordStatusCount(List<Long> accountBookList) {
        return goodsRecordMapper.getRecordStatusCount(accountBookList);
    }

    @Override
    public GoodsRecordStatusCount getRecordStatusCount(GoodsRecordSearchCondition condition, List<Long> accountBookList) {
        return recordEsDao.countGoodsRecord(condition, accountBookList);
    }


    @Override
    public GoodsRecordBusinessCount getRecordBusinessCount(String warehouseCode) {
        return recordEsDao.recordBusinessCount(warehouseCode);
    }

    //es 没有维护更新人 -- 方法弃用
    @Override
    @Deprecated
    public Map<String, GoodsRecordBusinessCountDetail> getRecordBusinessCountDetailES(Long beginTime, Long endTime) {
        List<CustomsDictionaryDTO> handlerList = customsDictionaryService.findByType(DataDictionaryTypeEnums.HANDLER.getValue());
        List<String> handlerCodeList = handlerList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        return recordEsDao.getRecordBusinessCountDetailMap(handlerCodeList, new Date(beginTime), new Date(endTime));
    }


    @Override
    public Map<String, GoodsRecordBusinessCountDetail> getRecordBusinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        ConcurrentHashMap<String, GoodsRecordBusinessCountDetail> result = new ConcurrentHashMap<>();
        Example example = new Example(GoodsRecordDO.class);
        Example.Criteria unionCriteria = example.createCriteria();
        unionCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andEqualTo("deleted", false);

        GoodsRecordSearchCondition condition = new GoodsRecordSearchCondition();
        condition.setRecordTimeFrom(beginTime);
        condition.setRecordTimeTo(endTime);
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        GoodsRecordStatusCount recordStatusCount = this.getRecordStatusCount(condition, accountBookList);
        int waitNewAndUpdate = recordStatusCount.getWaitNewCount() + recordStatusCount.getWaitUpdateCount();

        //备案完成（全部）
        Example example1 = new Example(GoodsRecordDO.class);
        example1.createCriteria().andEqualTo("recordStatus", GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
        example1.and(unionCriteria);
        int successfulCountAll = goodsRecordMapper.selectCountByExample(example1);

        //审核驳回（全部）
        Example example2 = new Example(GoodsRecordDO.class);
        example2.createCriteria().andEqualTo("recordStatus", GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
        example2.and(unionCriteria);
        int refuseCountAll = goodsRecordMapper.selectCountByExample(example2);

        int allSubmitAudit = waitNewAndUpdate + successfulCountAll + refuseCountAll;

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, unionCriteria, result, waitNewAndUpdate, allSubmitAudit);
            futureList.add(CompletableFuture.runAsync(runnable, goodsRecordCountTaskExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("备案统计组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("备案统计组合CompletableFuture结果异常" + e.getMessage(), e);
        }
        return result;
    }

    class Task extends TraceDataRunnable {

        private final String handlerId;

        private final int waitNewAndUpdate;

        private final int allSubmitAudit;

        private final Example.Criteria unionCriteria;

        private ConcurrentHashMap<String, GoodsRecordBusinessCountDetail> result;

        public Task(String handlerId, Example.Criteria unionCriteria, ConcurrentHashMap<String, GoodsRecordBusinessCountDetail> result, int waitNewAndUpdate, int allSubmitAudit) {
            super();
            this.handlerId = handlerId;
            this.unionCriteria = unionCriteria;
            this.waitNewAndUpdate = waitNewAndUpdate;
            this.allSubmitAudit = allSubmitAudit;
            this.result = result;
        }

        @Override
        public void proxy() {
            GoodsRecordBusinessCountDetail detail = new GoodsRecordBusinessCountDetail();

            //备案完成
            Example example3 = new Example(GoodsRecordDO.class);
            example3.createCriteria().andEqualTo("recordStatus", GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(unionCriteria);
            int successfulCount = goodsRecordMapper.selectCountByExample(example3);

            //审核驳回
            Example example4 = new Example(GoodsRecordDO.class);
            example4.createCriteria().andEqualTo("recordStatus", GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example4.and(unionCriteria);
            int refuseCount = goodsRecordMapper.selectCountByExample(example4);

            //总提交审核数量 - 待审核新品+待审核更新+审核通过+审核驳回+时间段
            detail.setAllSubmitAudit(allSubmitAudit);
            //审核通过数量 - 备案完成+时间段
            detail.setAuditPass(successfulCount);
            //审核驳回 - 审核驳回+时间段
            detail.setAuditReject(refuseCount);
            //总未审核 - 待审核新品+待审核更新+时间段
            detail.setAllAuditPending(waitNewAndUpdate);
            detail.setUserId(handlerId);
            result.put(handlerId, detail);
        }

    }


    @Override
    public List<RecordProductDTO> getRecordCustomsProduct(Long recordId, Long recordCustomsId) {
        List<RecordProductDTO> customsProductDTOList = recordProductBaseService.findByRecordIdAndRecordCustomsId(recordId, recordCustomsId);
        return customsProductDTOList;
    }

    /**
     * 生成商品列表
     * 1.记账回执回来先根据统一料号查询
     * 2.没有的话根据通关料号查询
     * 3.在查询是否生成料号库存
     * 4.写入数据库
     *
     * @param customsBookItemDTO
     * @param productId          统一料号
     * @param goodsSeqNo         金二序号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void generateRecordProduct(CustomsBookItemDTO customsBookItemDTO, String productId, String goodsSeqNo) {
        Long customsBookId = customsBookItemDTO.getCustomsBookId();
        GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookId, productId);
        RecordWarehouseDTO recordWarehouseDTO = null;
        if (Objects.nonNull(goodsRecordDTO)) {
            List<RecordWarehouseDTO> warehouseList = recordWarehouseBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
            if (!CollectionUtils.isEmpty(warehouseList)) {
                recordWarehouseDTO = warehouseList.get(0);
            }
        } else {
            List<RecordWarehouseProductIdDTO> warehouseProductIdList = recordWarehouseProductIdBaseService.findByCustomsDeclareProductIdAndCustomsBook(productId, customsBookId);
            if (!CollectionUtils.isEmpty(warehouseProductIdList)) {
                RecordWarehouseProductIdDTO warehouseProductIdDTO = warehouseProductIdList.get(0);
                recordWarehouseDTO = recordWarehouseBaseService.findDTOById(warehouseProductIdDTO.getRecordWarehouseId());
                if (Objects.nonNull(recordWarehouseDTO)) {
                    goodsRecordDTO = this.findById(recordWarehouseDTO.getRecordId());
                }
            }
        }
        if (Objects.isNull(recordWarehouseDTO) || Objects.isNull(goodsRecordDTO)) {
            return;
        }
        this.generateProductStockCore(customsBookItemDTO, productId, goodsSeqNo, customsBookId, goodsRecordDTO, recordWarehouseDTO);
    }

    private void generateProductStockCore(CustomsBookItemDTO customsBookItemDTO, String productId, String goodsSeqNo, Long customsBookId, GoodsRecordDTO goodsRecordDTO, RecordWarehouseDTO recordWarehouseDTO) {
        List<RecordProductDTO> recordProductDTOList = recordProductBaseService.findByCustomsBookItemId(customsBookItemDTO.getId());
        if (!CollectionUtils.isEmpty(recordProductDTOList)) {
            RecordProductDTO recordProductDTO = recordProductDTOList.get(0);
            GoodsRecordDTO recordDTO = this.findById(recordProductDTO.getRecordId());
            throw new ArgsInvalidException("料号:" + customsBookItemDTO.getProductId() + " 序号:" + customsBookItemDTO.getGoodsSeqNo() + "已被备案(统一料号:" + recordDTO.getProductId() + ")绑定商品列表");
        }
        Long productStockId = recordProductStockBaseService.generateProductStock(goodsRecordDTO, customsBookId, recordWarehouseDTO.getCustomsBookNo());
        recordProductBaseService.generateProduct(productStockId, customsBookItemDTO, goodsRecordDTO, recordWarehouseDTO, productId, goodsSeqNo);
        customsBookItemService.updateUnifiedProductId(customsBookItemDTO.getId(), productStockId, goodsRecordDTO.getProductId());
        // 同步es
        goodsRecordDumpProducer.send(goodsRecordDTO.getId(), goodsRecordDTO.getProductId());
    }

    @Override
    public void updateRecordCustomsProduct(Long customsBookItemId) {
        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(customsBookItemId);
        if (Objects.isNull(customsBookItemDTO)) {
            return;
        }
        recordProductBaseService.updateRecordCustomsProduct(customsBookItemDTO);
        try {
            RecordProductDTO recordProductDTO = recordProductBaseService.selectOneByCustomsBookItemId(customsBookItemId);
            goodsRecordDumpProducer.send(recordProductDTO.getRecordId());
        } catch (TooManyResultsException e) {
            log.error("更新商品列表 存在重复商品 itemId={}", customsBookItemId, e);
        }
    }

    @Override
    public void deleteRecordCustomsProduct(Long recordCustomsProductId) {
        if (Objects.isNull(recordCustomsProductId)) {
            return;
        }
        RecordProductDTO recordProductDTO = recordProductBaseService.findById(recordCustomsProductId);
        customsBookItemService.clearProductStock(recordProductDTO.getCustomsBookItemId(), recordProductDTO.getProductStockId());
        recordProductBaseService.deletedById(recordCustomsProductId);
    }

    @Override
    public Map<Long, List<String>> getCustomsProductIdByRecordId(List<Long> recordIdList) {
        List<RecordProductDTO> recordProductDTOS = recordProductBaseService.findByRecordId(recordIdList);
        Map<Long, List<String>> recordIdProductIdMap = recordProductDTOS.stream().collect(Collectors.groupingBy(RecordProductDTO::getRecordId, Collectors.mapping(RecordProductDTO::getCustomsRecordProductId, Collectors.toList())));
        return recordIdProductIdMap;
    }

    @Override
    public List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId) {
        return recordWarehouseProductIdBaseService.findByRecordId(recordId);
    }

    @Override
    public List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId, Long recordWarehouseId) {
        return recordWarehouseProductIdBaseService.findByRecordIdAndRecordWarehouseId(recordId, recordWarehouseId);
    }

    @Override
    public List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId, List<Long> recordWarehouseId) {
        return recordWarehouseProductIdBaseService.findByRecordIdAndRecordWarehouseId(recordId, recordWarehouseId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultResVo importProductStock(ProductStockImportDTO productStockImportDTO) {
        ImportResultResVo importResultResVo = new ImportResultResVo();
        importResultResVo.setFlag(false);
        String reason;
        String customsBookNo = productStockImportDTO.getCustomsBookNo();
        String productId = productStockImportDTO.getProductId();
        String goodsSeqNo = productStockImportDTO.getGoodsSeqNo();
        String unifiedProductId = productStockImportDTO.getUnifiedProductId();
        CustomsBookDTO customsBookDTO = customsBookService.findByCode(customsBookNo);
        if (Objects.isNull(customsBookDTO)) {
            reason = customsBookNo + "未找到对应账册";
            importResultResVo.setReason(reason);
            return importResultResVo;
        }
        Long customsBookId = customsBookDTO.getId();
        GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookDTO.getId(), unifiedProductId);
        if (Objects.isNull(goodsRecordDTO)) {
            reason = "未找到统一料号对应备案";
            importResultResVo.setReason(reason);
            return importResultResVo;
        }
        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findIdByBookIdAndProIdAndSeq(customsBookDTO.getId(), productId, goodsSeqNo);
        if (Objects.isNull(customsBookItemDTO)) {
            reason = "未找到海关备案料号对应账册库存";
            importResultResVo.setReason(reason);
            return importResultResVo;
        }
        List<RecordWarehouseDTO> warehouseList = recordWarehouseBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
        if (CollectionUtils.isEmpty(warehouseList)) {
            reason = "未找到备案下该账册对应实体仓信息";
            importResultResVo.setReason(reason);
            return importResultResVo;
        }
        RecordWarehouseDTO recordWarehouseDTO = warehouseList.get(0);
        try {
            this.generateProductStockCore(customsBookItemDTO, productId, goodsSeqNo, customsBookId, goodsRecordDTO, recordWarehouseDTO);
//            Long productStockId = recordProductStockBaseService.generateProductStock(goodsRecordDTO, customsBookId, recordWarehouseDTO.getCustomsBookNo());
//            recordProductBaseService.generateProduct(productStockId, customsBookItemDTO, goodsRecordDTO, recordWarehouseDTO, productId, goodsSeqNo);
//            customsBookItemService.updateUnifiedProductId(customsBookItemDTO.getId(), productStockId, goodsRecordDTO.getProductId());
//            // 同步es
//            goodsRecordDumpProducer.send(goodsRecordDTO.getId(), goodsRecordDTO.getProductId());
        } catch (ArgsInvalidException e) {
            log.error("商品列表已被绑定 error={}", e.getMessage(), e);
            importResultResVo.setReason(e.getMessage());
            return importResultResVo;
        } catch (Exception e) {
            log.error("生成商品列表失败 error={}", e.getMessage(), e);
            importResultResVo.setReason("生成商品列表失败");
            return importResultResVo;
        }
        importResultResVo.setFlag(true);
        return importResultResVo;
    }

    @Override
    public List<String> getProductIdListByUnifiedProductId(String unifiedProductId, Long customsBookId) {
        List<String> productIdList = new ArrayList<>();
        GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookId, unifiedProductId);
        if (Objects.isNull(goodsRecordDTO)) {
            return productIdList;
        }
        List<RecordProductDTO> recordProductDTOS = recordProductBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
        if (!CollectionUtils.isEmpty(recordProductDTOS)) {
            return recordProductDTOS.stream().map(RecordProductDTO::getCustomsRecordProductId).distinct().collect(Collectors.toList());
        }
        String associateProductId = this.getAssociateProductId(goodsRecordDTO.getId());
        if (Objects.nonNull(associateProductId) && !StringUtils.isEmpty(associateProductId.trim())) {
            productIdList.add(associateProductId);
            return productIdList;
        }
        List<RecordWarehouseProductIdDTO> productIdDTOList = recordWarehouseProductIdBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
        if (!CollectionUtils.isEmpty(productIdDTOList)) {
            return productIdDTOList.stream().map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId).distinct().collect(Collectors.toList());
        }
        return productIdList;

    }

    @Override
    public Map<String, List<CustomsBookItemDTO>> getCustomsDeclareProductIdList(Long customsBookId, List<String> unifiedProductIdList) {
        Map<String, List<CustomsBookItemDTO>> map = new HashMap<>();
        unifiedProductIdList.forEach(p -> {
            List<CustomsBookItemDTO> customsBookItemDTOList = new ArrayList<>();
            GoodsRecordDTO goodsRecordDTO = this.findByBookIdAndProId(customsBookId, p);
            if (Objects.isNull(goodsRecordDTO)) {
                return;
            }
            List<RecordProductDTO> recordProductDTOS = recordProductBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
            if (!CollectionUtils.isEmpty(recordProductDTOS)) {
                List<String> customsRecordProductIdList = recordProductDTOS.stream().map(RecordProductDTO::getCustomsRecordProductId).distinct().collect(Collectors.toList());
                List<CustomsBookItemDTO> customsBookItemDTOS = customsBookItemService.findByBookIdAndProIds(customsBookId, customsRecordProductIdList);
                customsBookItemDTOS = customsBookItemDTOS.stream().sorted(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo).reversed()).collect(Collectors.toList());
                customsBookItemDTOList.addAll(customsBookItemDTOS);
            }
            RecordItemAssociateInfoDTO recordItemAssociateInfoDTO = recordItemAssociateInfoService.viewAssociatedInfo(goodsRecordDTO.getId());
            if (Objects.nonNull(recordItemAssociateInfoDTO)) {
                List<CustomsBookItemDTO> associateInfoItemDTOS = customsBookItemService.findByBookIdAndProId(customsBookId, recordItemAssociateInfoDTO.getAssociatedProductId());
                if (!CollectionUtils.isEmpty(associateInfoItemDTOS)) {
                    associateInfoItemDTOS = associateInfoItemDTOS.stream().sorted(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo).reversed()).collect(Collectors.toList());
                    customsBookItemDTOList.addAll(associateInfoItemDTOS);
                }
            }
            List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOS = recordWarehouseProductIdBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
            if (!CollectionUtils.isEmpty(recordWarehouseProductIdDTOS)) {
                List<String> customsDeclareProductId = recordWarehouseProductIdDTOS.stream().map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId).distinct().collect(Collectors.toList());
                List<CustomsBookItemDTO> customsDeclareItemDTOS = customsBookItemService.findByBookIdAndProIds(customsBookId, customsDeclareProductId);
                if (!CollectionUtils.isEmpty(customsDeclareItemDTOS)) {
                    customsDeclareItemDTOS = customsDeclareItemDTOS.stream().sorted(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo).reversed()).collect(Collectors.toList());
                    customsBookItemDTOList.addAll(customsDeclareItemDTOS);
                }
            }
            if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
                List<CustomsBookItemDTO> customsBookItemDTOS = customsBookItemService.findByBookIdAndProId(customsBookId, p);
                customsBookItemDTOS = customsBookItemDTOS.stream().sorted(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo).reversed()).collect(Collectors.toList());
                customsBookItemDTOList.addAll(customsBookItemDTOS);
            }
            map.put(p, customsBookItemDTOList);
        });
        return map;
    }

    @Override
    public List<RecordWarehouseProductIdDTO> findWarehouseProductIdByRecordIdAndCustomsBookId(Long recordId, Long customsBookId) {
        return recordWarehouseProductIdBaseService.findByRecordIdAndCustomsBookId(recordId, customsBookId);
    }

    /**
     * 保存实体仓信息
     *
     * @param recordCustomsId
     * @param entityWarehouseSn
     */
    @Override
    public void saveNewWarehouseInfo(Long recordCustomsId, String entityWarehouseSn) {
        List<RecordWarehouseDO> warehouseDOList = recordWarehouseBaseService.findByRecordCustomsId(recordCustomsId);
        if (!CollectionUtils.isEmpty(warehouseDOList)) {
            boolean anyMatch = warehouseDOList.stream().anyMatch(w -> Objects.equals(w.getWarehouseSn(), entityWarehouseSn));
            if (anyMatch) {
                throw new ArgsInvalidException("已存在相同的实体仓");
            }
        }
        EntityWarehouseDTO entityWarehouseDTO = entityWarehouseService.findBySn(entityWarehouseSn);
        if (Objects.isNull(entityWarehouseDTO)) {
            throw new ArgsInvalidException("未查询到实体仓信息");
        }
        RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findById(recordCustomsId);
        if (Objects.isNull(recordCustomsDO)) {
            throw new ArgsInvalidException("未查询到备案口岸数据");
        }
        GoodsRecordDTO goodsRecordDTO = this.findById(recordCustomsDO.getRecordId());
        if (Objects.isNull(goodsRecordDTO)) {
            throw new ArgsInvalidException("未查询到备案数据");
        }
        String productId = goodsRecordDTO.getProductId();
        List<RecordWarehouseDO> existResult = recordWarehouseBaseService.findByProductIdWarehouseSnCustomsBook(productId, entityWarehouseSn, entityWarehouseDTO.getCustomsBookId());
        if (!CollectionUtils.isEmpty(existResult)) {
            throw new ArgsInvalidException("已存在料号:+" + productId + "+实体仓+" + entityWarehouseDTO.getErpWarehouseName() + "账册号:" + entityWarehouseDTO.getCustomsBookNo() + "的组合");
        }
        RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
        recordWarehouseDO.setRecordId(recordCustomsDO.getRecordId())
                .setProductId(goodsRecordDTO.getProductId())
                .setWarehouseSn(entityWarehouseDTO.getSn())
                .setCustomsBookId(entityWarehouseDTO.getCustomsBookId())
                .setCustomsBookNo(entityWarehouseDTO.getCustomsBookNo())
                .setRecordCustomsId(recordCustomsId)
                .setCustomsCode(recordCustomsDO.getCustomsCode())
                .setCustoms(recordCustomsDO.getCustoms())
                .setErpWarehouseName(entityWarehouseDTO.getErpWarehouseName())
                .setWmsWarehouseCode(entityWarehouseDTO.getWmsWarehouseCode());
        UserUtils.setCreateAndUpdateBy(recordWarehouseDO);
        log.info("备案id:{} 新增实体仓:{} / {}", goodsRecordDTO.getId(), entityWarehouseDTO.getSn(), entityWarehouseDTO.getErpWarehouseName());
        recordWarehouseBaseService.save(recordWarehouseDO);
        goodsRecordDumpProducer.send(goodsRecordDTO.getId());
    }

    /**
     * ERP删除备案 软删除
     *
     * @param deleteGoodsRecordSubmit
     */
    @Override
    public void addDeleteFlag(DeleteGoodsRecordSubmit deleteGoodsRecordSubmit) {
        log.info("addDeleteFlag submit={}", JSON.toJSONString(deleteGoodsRecordSubmit));
        String skuId = deleteGoodsRecordSubmit.getSkuId();
        String customsBookNo = deleteGoodsRecordSubmit.getCustomsBookNo();
        String tenantId = deleteGoodsRecordSubmit.getTenantId();
        GoodsRecordDTO goodsRecordDTO = null;
        if (Objects.equals("OLD", deleteGoodsRecordSubmit.getRecordType())) {
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(customsBookNo);
            if (Objects.isNull(customsBookDTO)) {
                log.error("addDeleteFlag 未查询到{}账册信息", customsBookNo);
                throw new ArgsInvalidException("未查询到" + customsBookNo + "账册信息");
            }
            goodsRecordDTO = this.findOldByBookIdTenantIdAndSkuId(customsBookDTO.getId(), tenantId, skuId);
            log.info("addDeleteFlag 查询老备案 record={}", JSON.toJSONString(goodsRecordDTO));

        } else if (Objects.equals("NEW", deleteGoodsRecordSubmit.getRecordType())) {
            goodsRecordDTO = this.findByProId(deleteGoodsRecordSubmit.getProductId());
            log.info("addDeleteFlag 查询新备案 record={}", JSON.toJSONString(goodsRecordDTO));
        }
        if (Objects.isNull(goodsRecordDTO)) {
            log.info("addDeleteFlag 未查询到关联备案信息 直接返回成功");
        }
//        goodsRecordDTO = this.findById(goodsRecordDTO.getId());
//        if (Objects.equals("NEW", goodsRecordDTO.getRecordType())) {
//            log.error("addDeleteFlag 未查询到关联的老备案");
//            throw new ArgsInvalidException("未查询到关联的老备案");
//        }
        Integer goodsRecordTag = goodsRecordDTO.getGoodsRecordTag();
        List<Integer> recordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordTag);
        if (recordTag.contains(GoodsRecordTagEnums.ERP_DELETE.getCode())) {
            log.error("addDeleteFlag 已删除 直接返回成功");
        }
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setId(goodsRecordDTO.getId());
        goodsRecordTag = GoodsRecordTagEnums.addGoodsRecordTag(goodsRecordTag, GoodsRecordTagEnums.ERP_DELETE);
        goodsRecordDO.setGoodsRecordTag(goodsRecordTag);
        goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);
        try {
            this.buildTrackLogErpDeletedAndSend(goodsRecordDTO);
        } catch (Exception e) {
            log.error("addDeleteFlag 记录日志异常");
        }
        this.deleteRecordSyncErp(goodsRecordDTO.getId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addDeleteFlag(GoodsRecordDTO goodsRecordDTO) {
        if (Objects.isNull(goodsRecordDTO)) {
            throw new ArgsInvalidException("备案参数错误");
        }
        Integer goodsRecordTag = goodsRecordDTO.getGoodsRecordTag();
        List<Integer> recordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordTag);
        if (recordTag.contains(GoodsRecordTagEnums.ERP_DELETE.getCode())) {
            log.error("addDeleteFlag 已删除 直接返回成功");
            return;
        }
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setId(goodsRecordDTO.getId());
        goodsRecordTag = GoodsRecordTagEnums.addGoodsRecordTag(goodsRecordTag, GoodsRecordTagEnums.ERP_DELETE);
        goodsRecordDO.setGoodsRecordTag(goodsRecordTag);
        goodsRecordMapper.updateByPrimaryKeySelective(goodsRecordDO);
        try {
            goodsRecordService.buildTrackLogErpDeletedAndSend(goodsRecordDTO);
        } catch (Exception e) {
            log.error("addDeleteFlag 记录日志异常");
        }
        this.deleteRecordSyncErp(goodsRecordDTO.getId());
    }

    @Override
    public List<GoodsRecordEsDTO> findEsWaitExamineRecord() {
        return recordEsDao.findEsWaitExamineRecord();
    }

    @Override
    public void syncEsByUpdateTimeAcc(List<Long> goodsRecordIdList) {
        goodsRecordMapper.syncEsByUpdateTimeAcc(goodsRecordIdList);
    }

    /**
     * 通过不同映射方式 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     料号
     * @param mappingWay    映射方式
     * @return
     */
    @Override
    public GoodsRecordDTO findByBookIdAndProIdAndMappingWay(Long customsBookId, String productId, String mappingWay) {
        return goodsRecordAssociateService.findByBookIdAndProIdAndMappingWay(customsBookId, productId, mappingWay);
    }

    private String importAutoRecordParamCheck(GoodsAutoRecordImportVo vo) {
        List<String> errorList = new ArrayList<>();
        String validatorRes = ValidatorUtils.doValidator(validator, vo);
        if (StringUtils.hasText(validatorRes)) {
            errorList.add(validatorRes);
        }
        if (StringUtil.isNotBlank(vo.getTenantName())) {
            UserRpcQueryParam queryParam = new UserRpcQueryParam();
            queryParam.setUserName(vo.getTenantName());
            List<UserRpcResult> userRpcResults = userRpcFacade.listByParam(queryParam);
            if (CollectionUtil.isEmpty(userRpcResults)) {
                errorList.add("用户名称不存在");
            } else if (userRpcResults.size() > 1) {
                errorList.add("用户名称存在重复");
            } else {
                UserRpcResult userRpcResult = userRpcResults.get(0);
                vo.setTenantId(userRpcResult.getId());
            }
        }
        if (StringUtil.isNotBlank(vo.getCustomsBookNo())) {
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(vo.getCustomsBookNo());
            if (Objects.isNull(customsBookDTO)) {
                errorList.add("账册编号不存在");
            } else {
                vo.setCustomsCode(customsBookDTO.getCustomsDistrictCode());
            }
        }
        if (StringUtil.isNotBlank(vo.getDeclareUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(vo.getDeclareUnit());
            if (Objects.isNull(customsUomDTO)) {
                errorList.add("申报单位不存在");
            } else {
                vo.setDeclareUnitName(customsUomDTO.getName());
            }
        }
        if (StringUtil.isNotBlank(vo.getCurrency())) {
            CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(vo.getCurrency());
            if (Objects.isNull(currencyDTO)) {
                errorList.add("申报币制不存在");
            } else {
                vo.setCurrencyName(currencyDTO.getName());
            }
        }
        if (StringUtil.isNotBlank(vo.getOriginCountry())) {
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(vo.getOriginCountry());
            if (Objects.isNull(countryDTO)) {
                errorList.add("原产国不存在");
            } else {
                vo.setOriginCountryName(countryDTO.getName());
            }
        }
        if (StringUtil.isNotBlank(vo.getFirstUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(vo.getFirstUnit());
            if (Objects.isNull(customsUomDTO)) {
                errorList.add("第一法定单位不存在");
            } else {
                vo.setFirstUnitName(customsUomDTO.getName());
            }
        }
        if (StringUtil.isNotBlank(vo.getSecondUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(vo.getSecondUnit());
            if (Objects.isNull(customsUomDTO)) {
                errorList.add("第二法定单位不存在");
            } else {
                vo.setSecondUnitName(customsUomDTO.getName());
            }
        }
        if (StringUtil.isNotBlank(vo.getHsCode())) {
            CustomsHsDTO customsHsDTO = customsHsService.findByCode(vo.getHsCode());
            if (Objects.isNull(customsHsDTO)) {
                errorList.add("hsCode不存在");
            } else {
                vo.setHsCodeName(customsHsDTO.getHsName());
            }
        }
        return String.join(",", errorList);
    }

    /**
     * 自动备案导入
     * 调用ERP检查备案是否存在 + 生成备案
     *
     * @param vo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultResVo importAutoRecord(GoodsAutoRecordImportVo vo, Long operatorId) {
        ImportResultResVo res = new ImportResultResVo();
        res.setFlag(Boolean.TRUE);
        try {
            String errorMsg = this.importAutoRecordParamCheck(vo);
            if (StringUtil.isNotBlank(errorMsg)) {
                throw new ArgsInvalidException(errorMsg);
            }
            UserRpcQueryParam param = new UserRpcQueryParam();
            param.setId(operatorId);
            UserRpcResult userRpcResult = userRpcFacade.getByParam(param);
            String operator = userRpcResult.getUserName();
            String redisKey = vo.getWarehouseExternalProductId() + "_" + CustomsDistrictEnum.getEnum(vo.getCustomsCode()).getDesc();
            log.info("importAutoRecord - redisKey={} - operator={}", redisKey, operator);
            redisTemplate.boundHashOps(ItemTrackLogConfig.ITEM_TYPE_ERP_CCS_IMPORT_AUTO + "_operator")
                    .put(redisKey, operator);
            log.info("importAutoRecord - GoodsAutoRecordImportVo={}", JSON.toJSONString(vo));
            GoodsRecordCssV1RpcResult rpcResult = this.buildGoodsRecordRpcResult(vo);
            log.info("importAutoRecord send ERP, GoodsRecordRpcResult={}", JSON.toJSONString(rpcResult));
            List<GoodsRecordCssV1RpcResult> goodsRecordRpcResults = iGoodsRecordRpcFacade.checkAndReportGoodsRecord(rpcResult, RecordSource.CCS);
            log.info("importAutoRecord result, goodsRecordRpcResults={}", JSON.toJSONString(goodsRecordRpcResults));
            if (CollectionUtils.isEmpty(goodsRecordRpcResults)) {
                throw new RuntimeException("ERP导入失败");
            }
//            for (GoodsRecordRpcResult goodsRecordRpcResult : goodsRecordRpcResults) {
//                if (!RecordStatus.SUCCESS.equals(goodsRecordRpcResult.getRecordStatus())) {
//                    throw new RuntimeException("ERP导入失败, 备案状态不为【备案完成】");
//                }
//            }
        } catch (Exception e) {
            log.error("自动备案导入失败: {}", e.getMessage(), e);
            res.setFlag(Boolean.FALSE);
            res.setReason(e.getMessage());
        }
        return res;
    }

    @Override
    public ImportResultResVo goodsRecordImportUpdate(GoodsRecordImportUpdateReqVo vo) {
        ImportResultResVo importResultResVo = new ImportResultResVo();
        importResultResVo.setFlag(false);
        String errorMsg = validateImportUpdateData(vo);
        if (StringUtil.isNotBlank(errorMsg)) {
            log.info("商品备案批量更新审核参数校验失败: {}", errorMsg);
            importResultResVo.setReason(errorMsg);
            return importResultResVo;
        }
        List<GoodsRecordDO> goodsRecordDOList = goodsRecordMapper.findByProductIdAndCustomsAndNotErpDeleted(vo.getProductId(), vo.getCustoms());
        if (goodsRecordDOList.isEmpty()) {
            importResultResVo.setReason("备案不存在");
            return importResultResVo;
        } else if (goodsRecordDOList.size() > 1) {
            importResultResVo.setReason("存在重复备案");
            return importResultResVo;
        }
        GoodsRecordDO goodsRecordDO = goodsRecordDOList.get(0);
        Long id = goodsRecordDO.getId();
        RecordCustomsDO customsCode = recordCustomsBaseService.findByRecordIdAndCustomsCode(id, vo.getCustoms());
        if (Objects.isNull(customsCode)) {
            importResultResVo.setReason("备案口岸数据不存在");
            return importResultResVo;
        }
        if (!Objects.equals(customsCode.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {
            importResultResVo.setReason("口岸状态不属于备案完成，无法更新备案");
            return importResultResVo;
        }
        goodsRecordDO.setId(id);
        goodsRecordDO.setGoodsRecordName(vo.getGoodsRecordName());
        goodsRecordDO.setModel(vo.getModel());
        goodsRecordDO.setHsCode(vo.getHsCode());
        goodsRecordDO.setDeclareUnit(vo.getDeclareUnit());
        goodsRecordDO.setOriginCountry(vo.getOriginCountry());
        goodsRecordDO.setFirstUnit(vo.getFirstUnit());
        goodsRecordDO.setFirstUnitAmount(vo.getFirstUnitAmount());
        if (StringUtil.isNotBlank(vo.getSecondUnit())) {
            goodsRecordDO.setSecondUnit(vo.getSecondUnit());
            goodsRecordDO.setSecondUnitAmount(vo.getSecondUnitAmount());
        } else {
            goodsRecordDO.setSecondUnit(null);
            goodsRecordDO.setSecondUnitAmount(null);
        }
        UserUtils.setUpdateBy(goodsRecordDO);
        log.info("商品备案批量更新 updateDo={}", JSON.toJSONString(goodsRecordDO));
        goodsRecordMapper.updateByPrimaryKey(goodsRecordDO);
        customsCode.setHsCode(vo.getHsCode());
        customsCode.setOriginCountry(vo.getOriginCountry());
        customsCode.setFirstUnit(vo.getFirstUnit());
        customsCode.setFirstUnitAmount(vo.getFirstUnitAmount());
        if (StringUtil.isNotBlank(vo.getSecondUnit())) {
            customsCode.setSecondUnit(vo.getSecondUnit());
            customsCode.setSecondUnitAmount(vo.getSecondUnitAmount());
        } else {
            customsCode.setSecondUnit(null);
            customsCode.setSecondUnitAmount(null);
        }
        UserUtils.setUpdateBy(goodsRecordDO);
        log.info("商品备案批量更新 updateCustomsDO={}", JSON.toJSONString(customsCode));
        recordCustomsBaseService.updateByPrimaryKey(customsCode);
        importResultResVo.setFlag(true);
        return importResultResVo;
    }

    private String validateImportUpdateData(GoodsRecordImportUpdateReqVo vo) {
        String validatorMsg = ValidatorUtils.doValidator(validator, vo);
        if (Objects.nonNull(validatorMsg)) {
            return validatorMsg;
        }
        StringBuilder errorMsg = new StringBuilder();
        CustomsHsDTO hsDTO = customsHsService.findByCode(vo.getHsCode());
        if (Objects.isNull(hsDTO)) {
            errorMsg.append("hs：").append(vo.getHsCode()).append("，不存在正面清单;");
        } else {
            CustomsDictionaryDTO firstUom = null;
            List<CustomsDictionaryDTO> customsDictionaryDTOS = customsDictionaryService.findByTypeAndName(DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit());
            if (CollUtil.isEmpty(customsDictionaryDTOS)) {
                errorMsg.append("法一单位不存在;");
            } else {
                firstUom = customsDictionaryDTOS.get(0);
                vo.setFirstUnit(firstUom.getCode());
            }
            if (Objects.isNull(firstUom) || !Objects.equals(firstUom.getCode(), hsDTO.getFirstLegalUnit())) {
                errorMsg.append("法一单位与HS编码法定计量单位不符");
            }
            if (StrUtil.isNotBlank(hsDTO.getSecondLegalUnit())) {
                if (StrUtil.isBlank(vo.getSecondUnit())) {
                    errorMsg.append("法二单位不能为空;");
                } else {
                    CustomsDictionaryDTO SecondUomDTO = null;
                    List<CustomsDictionaryDTO> secondUomList = customsDictionaryService.findByTypeAndName(DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit());
                    if (CollUtil.isEmpty(secondUomList)) {
                        errorMsg.append("法二单位不存在;");
                    } else {
                        SecondUomDTO = secondUomList.get(0);
                        vo.setSecondUnit(SecondUomDTO.getCode());
                    }
                    if (Objects.isNull(SecondUomDTO) || !Objects.equals(SecondUomDTO.getCode(), hsDTO.getSecondLegalUnit())) {
                        errorMsg.append("法二单位与HS编码法定第二计量单位不符;");
                    }
                }
                if (Objects.isNull(vo.getSecondUnitAmount())) {
                    errorMsg.append("法二数量不能为空;");
                }
            } else {
                if (StrUtil.isNotBlank(vo.getSecondUnit())) {
                    errorMsg.append("法二单位填写错误;");
                }
            }
        }
        List<CustomsDictionaryDTO> declareUnitList = customsDictionaryService.findByTypeAndName(DataDictionaryTypeEnums.UOM.getValue(), vo.getDeclareUnit());
        if (CollUtil.isEmpty(declareUnitList)) {
            errorMsg.append("申报单位不存在;");
        } else {
            vo.setDeclareUnit(declareUnitList.get(0).getCode());
        }
        CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnumByName(vo.getCustoms());
        if (Objects.isNull(districtEnum)) {
            errorMsg.append("口岸不存在;");
        } else {
            vo.setCustoms(districtEnum.getCode());
        }
        List<CustomsDictionaryDTO> originCountryList = customsDictionaryService.findByTypeAndName(DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getOriginCountry());
        if (CollUtil.isEmpty(originCountryList)) {
            errorMsg.append("原产国不存在;");
        } else {
            vo.setOriginCountry(originCountryList.get(0).getCode());
        }

        return errorMsg.toString();
    }


    /**
     * 构建同步ERP备案请求vo
     * 导入 + 更新
     *
     * @param vo
     */
    private GoodsRecordCssV1RpcResult buildGoodsRecordRpcResult(GoodsAutoRecordImportVo vo) {
        GoodsRecordCssV1RpcResult res = new GoodsRecordCssV1RpcResult();
        res.setUserId(vo.getTenantId());
        res.setUserName(vo.getTenantName());
        res.setGoodsName(vo.getGoodsName());
        res.setPort(vo.getCustomsCode());
        res.setGrossWeight(vo.getGrossWeight().doubleValue());
        res.setNetWeight(vo.getNetWeight().doubleValue());
        res.setModel(vo.getModel());
        if (Objects.nonNull(vo.getDeclarePrice())) {
            res.setDeclaredUnitPrice(vo.getDeclarePrice());
        }
        res.setDeclaredUnit(vo.getDeclareUnitName());
        if (Objects.nonNull(vo.getCurrency())) {
            res.setDeclaredCurrency(vo.getCurrency());
        }
        res.setFirstUnit(vo.getFirstUnitName());
        res.setFirstQuantity(vo.getFirstUnitAmount().doubleValue());
        if (Objects.nonNull(vo.getSecondUnitAmount()) && StringUtil.isNotBlank(vo.getSecondUnit())) {
            res.setSecondUnit(vo.getSecondUnitName());
            res.setSecondQuantity(vo.getSecondUnitAmount().doubleValue());
        }
        res.setOriginCountry(vo.getOriginCountry());
        res.setHsCode(vo.getHsCode());
        res.setComposition(vo.getComposition());
        res.setEffect(vo.getRecordFunction());
        res.setSku(vo.getSkuId());
        res.setCompanyName(vo.getProductCompanyName());
        res.setCompanyCode(vo.getProductCompanyCode());
        res.setCompanyAdd(vo.getProductCompanyAddress());
        res.setProLink(vo.getProductLink());
        res.setBarcode(vo.getBarCode());
        res.setPurpose(vo.getRecordUsage());
        res.setDeclareElement(vo.getHgsbys());
        res.setAccountCode(vo.getCustomsBookNo());
        res.setDeclaredUnitName(vo.getDeclareUnitName());
        res.setDeclaredCurrencyName(vo.getCurrencyName());
        res.setFirstUnitName(vo.getFirstUnitName());
        res.setSecondUnitName(vo.getSecondUnitName());
        res.setOriginCountryName(vo.getOriginCountryName());
        res.setHsCodeName(vo.getHsCodeName());
        res.setExternalCode(vo.getOutGoodsId());
        res.setExternalSku(vo.getCainiaoGoodsId());
        res.setExternalMaterialCode(vo.getWarehouseExternalProductId());
        return res;
    }

    /**
     * 更新备案（） 同步
     *
     * @param reqVo
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateFinishGoodsRecord(UpdFinishGoodsRecordReqVo reqVo) {
        String errorMsg = ValidatorUtils.doValidator(validator, reqVo);
        if (StringUtils.hasText(errorMsg)) {
            throw new ArgsInvalidException(errorMsg);
        }
        GoodsRecordDTO goodsRecordDTO = this.findById(reqVo.getId());
        if (StringUtil.isNotBlank(reqVo.getExternalGoodsId())
                && !Objects.equals(goodsRecordDTO.getExternalGoodsId(), reqVo.getExternalGoodsId())) {
            GoodsRecordDTO existGoodsDTO = this.findByExternalGoodsId(reqVo.getExternalGoodsId());
            if (Objects.nonNull(existGoodsDTO)) {
                throw new ArgsInvalidException("外部货品ID已存在统一料号：" + existGoodsDTO.getProductId());
            }
        }
        if (StringUtil.isNotBlank(reqVo.getExternalGoodsId())
                && !Objects.equals(goodsRecordDTO.getCainiaoGoodsId(), reqVo.getCainiaoGoodsId())) {
            GoodsRecordDTO existGoodsDTO = this.findByCainiaoGoodsId(reqVo.getCainiaoGoodsId());
            if (Objects.nonNull(existGoodsDTO)) {
                throw new ArgsInvalidException("菜鸟货品ID已存在统一料号：" + existGoodsDTO.getProductId());
            }
        }
        GoodsRecordSubmit submit = new GoodsRecordSubmit();
        BeanUtils.copyProperties(goodsRecordDTO, submit);
        submit.setId(reqVo.getId());
        submit.setCustomsCode(reqVo.getCustomsCode());
        submit.setGoodsRecordName(reqVo.getGoodsName());
        submit.setHsCode(reqVo.getHsCode());
        submit.setFirstUnit(reqVo.getFirstUnit());
        submit.setFirstUnitAmount(reqVo.getFirstUnitAmount());
        submit.setSecondUnit(reqVo.getSecondUnit());
        submit.setSecondUnitAmount(reqVo.getSecondUnitAmount());
        submit.setOriginCountry(reqVo.getOriginCountry());
        submit.setDeclareUnit(reqVo.getDeclareUnit());
        submit.setDeclarePrice(reqVo.getDeclarePrice());
        submit.setDeclareCurrency(reqVo.getDeclareCurrency());
        submit.setModel(reqVo.getModel());
        submit.setComposition(reqVo.getComposition());
        submit.setHgsbys(reqVo.getHgsbys());
        submit.setSkuId(goodsRecordDTO.getSkuId());
        submit.setNetWeight(reqVo.getNetWeight());
        submit.setGrossWeight(reqVo.getGrossWeight());
        submit.setAllowEdit(Boolean.TRUE);
        submit.setOpinion(1);
        submit.setGrossWeight(reqVo.getGrossWeight());
        submit.setNetWeight(reqVo.getNetWeight());
        submit.setCountryRecordNo(reqVo.getCountryRecordNo());
        submit.setGuanWuRemark(reqVo.getGuanWuRemark());
        if (StringUtil.isNotBlank(reqVo.getExternalGoodsId())) {
            submit.setExternalGoodsId(StringUtils.trimWhitespace(reqVo.getExternalGoodsId()));
        }
        if (StringUtil.isNotBlank(reqVo.getCainiaoGoodsId())) {
            submit.setCainiaoGoodsId(StringUtils.trimWhitespace(reqVo.getCainiaoGoodsId()));
        }
        if (StringUtil.isNotBlank(reqVo.getBarCode())) {
            submit.setBarCode(StringUtils.trimWhitespace(reqVo.getBarCode()));
        }
        try {
            this.update(submit, ItemTrackLogConfig.UPDATE_GOODS);
        } catch (ArgsErrorException ex) {
            log.error("更新备案失败 recordId={}, submit={} error={}", submit.getId(), JSON.toJSONString(submit), ex.getErrorMessage(), ex);
            throw new ArgsInvalidException(ex.getErrorMessage());
        } catch (Exception ex) {
            log.info("更新备案失败 recordId={}, submit={} error={}", submit.getId(), JSON.toJSONString(submit), ex.getMessage(), ex);
            throw new ArgsInvalidException(ex.getMessage());
        }
        goodsRecordDumpProducer.send(reqVo.getId());
        if (!Objects.equals(reqVo.getBarCode(), goodsRecordDTO.getBarCode())) {
            log.info("条码编辑 同步erp recordId={}, barCode={} ", reqVo.getId(), reqVo.getBarCode());
            try {
                GoodRpcEditParam goodRpcEditParam = new GoodRpcEditParam();
                goodRpcEditParam.setGoodsCode(goodsRecordDTO.getGoodsCode());
                goodRpcEditParam.setBarcode(reqVo.getBarCode());
                log.info("同步更新ERP param={}", JSON.toJSONString(goodRpcEditParam));
                boolean result = iGoodsRpcFacade.goodsEdit(goodRpcEditParam);
                log.info("同步更新ERP成功 result={}", result);
            } catch (Exception e) {
                log.error("同步更新ERP失败 error={}", e.getMessage(), e);
                throw new ArgsInvalidException("同步更新ERP失败:" + e.getMessage());
            }
        }
        // 同步账册库存法定数量
        try {
            RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(goodsRecordDTO.getId(), reqVo.getCustomsCode());
            this.syncBookItemLawNum(goodsRecordDTO.getId(), recordCustomsDTO.getId(), goodsRecordDTO.getProductId(),
                    recordCustomsDTO.getFirstUnit(), recordCustomsDTO.getFirstUnitAmount(),
                    recordCustomsDTO.getSecondUnit(), recordCustomsDTO.getSecondUnitAmount());
        } catch (Exception e) {
            log.error("同步账册库存法定数量失败 recordId={}, customsCode={}", goodsRecordDTO.getId(), reqVo.getCustomsCode());
            throw new ArgsInvalidException("同步账册库存法定数量失败");
        }
        // 同步更新ERP
//        GoodsRecordRpcResult goodsRecordRpcResult = new GoodsRecordRpcResult();
//        if (StringUtil.isNotBlank(goodsRecordDTO.getTenantId())) {
//            goodsRecordRpcResult.setUserId(Long.valueOf(goodsRecordDTO.getTenantId()));
//        }
//        goodsRecordRpcResult.setPort(reqVo.getCustomsCode());
//        goodsRecordRpcResult.setGoodsCode(goodsRecordDTO.getGoodsCode());
//        goodsRecordRpcResult.setGoodsName(reqVo.getGoodsName());
//        goodsRecordRpcResult.setPort(reqVo.getCustomsCode());
//        goodsRecordRpcResult.setModel(reqVo.getModel());
//        goodsRecordRpcResult.setDeclaredUnitPrice(reqVo.getDeclarePrice());
//        goodsRecordRpcResult.setDeclaredUnit(reqVo.getDeclareUnit());
//        goodsRecordRpcResult.setDeclaredCurrency(reqVo.getDeclareCurrency());
//        goodsRecordRpcResult.setFirstUnit(reqVo.getFirstUnit());
//        goodsRecordRpcResult.setFirstQuantity(reqVo.getFirstUnitAmount().doubleValue());
//        goodsRecordRpcResult.setSecondUnit(reqVo.getSecondUnit());
//        goodsRecordRpcResult.setGrossWeight(reqVo.getGrossWeight().doubleValue());
//        goodsRecordRpcResult.setNetWeight(reqVo.getNetWeight().doubleValue());
//        if (Objects.nonNull(reqVo.getSecondUnitAmount())) {
//            goodsRecordRpcResult.setSecondQuantity(reqVo.getSecondUnitAmount().doubleValue());
//        }
//        goodsRecordRpcResult.setOriginCountry(reqVo.getOriginCountry());
//        goodsRecordRpcResult.setHsCode(reqVo.getHsCode());
//        if (StringUtil.isNotBlank(reqVo.getComposition())) {
//            goodsRecordRpcResult.setComposition(reqVo.getComposition());
//        }
//        goodsRecordRpcResult.setSku(goodsRecordDTO.getSkuId());
//        if (StringUtil.isNotBlank(reqVo.getHgsbys())) {
//            goodsRecordRpcResult.setDeclareElement(reqVo.getHgsbys());
//        }
//        CustomsUomDTO declareUnitDTO = customsUomService.findByCode(reqVo.getDeclareUnit());
//        if (Objects.nonNull(declareUnitDTO)) {
//            goodsRecordRpcResult.setDeclaredUnitName(declareUnitDTO.getName());
//        }
//        CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(reqVo.getDeclareCurrency());
//        if (Objects.nonNull(currencyDTO)) {
//            goodsRecordRpcResult.setDeclaredCurrencyName(currencyDTO.getName());
//        }
//        CustomsUomDTO firstUnitDTO = customsUomService.findByCode(reqVo.getFirstUnit());
//        if (Objects.nonNull(firstUnitDTO)) {
//            goodsRecordRpcResult.setFirstUnitName(firstUnitDTO.getName());
//        }
//        if (StringUtil.isNotBlank(reqVo.getSecondUnit())) {
//            CustomsUomDTO secondUnitDTO = customsUomService.findByCode(reqVo.getSecondUnit());
//            if (Objects.nonNull(secondUnitDTO)) {
//                goodsRecordRpcResult.setSecondUnitName(secondUnitDTO.getName());
//            }
//        }
//        CustomsCountryDTO countryDTO = customsCountryService.findByCode(reqVo.getOriginCountry());
//        if (Objects.nonNull(countryDTO)) {
//            goodsRecordRpcResult.setOriginCountryName(countryDTO.getName());
//        }
//        CustomsHsDTO customsHsDTO = customsHsService.findByCode(reqVo.getHsCode());
//        if (Objects.nonNull(customsHsDTO)) {
//            goodsRecordRpcResult.setHsCodeName(customsHsDTO.getHsName());
//        }
//        try {
//            log.info("updateFinishGoodsRecord send to ERP, goodsRecordRpcResult={}", JSON.toJSONString(goodsRecordRpcResult));
//            RpcResult rpcResult = iGoodsRecordRpcFacade.updateGoodsRecord(goodsRecordRpcResult);
//            log.info("updateFinishGoodsRecord send to ERP, rpcResult={}", JSON.toJSONString(rpcResult));
//            if (Objects.isNull(rpcResult)) {
//                log.error("updateFinishGoodsRecord send to ERP failed, rpcResult is null");
//            } else if (Objects.equals(rpcResult.getCode(), 200)) {
//                log.info("updateFinishGoodsRecord send to ERP success");
//            } else {
//                log.error("updateFinishGoodsRecord send to ERP failed, error={}", rpcResult.getMessage());
//            }
//        } catch (Exception e) {
//            log.error("updateFinishGoodsRecord erp更新备案失败", e);
//            throw new RuntimeException("ERP更新备案失败");
//        }
    }

    @Override
    public void warehouseFix(String addBookNo, String queryBookNo, String wmsWarehouseCode) {
        CustomsBookDTO addBook = customsBookService.findByCode(addBookNo);
        CustomsBookDTO queryBook = customsBookService.findByCode(queryBookNo);
        List<RecordWarehouseDO> queryList = recordWarehouseBaseService.findByBookIdAndWarehouseCode(queryBook.getId(), wmsWarehouseCode);
        log.info("warehouseFix queryList.size={}", queryList.size());
        List<RecordWarehouseDO> warehouseDOS = queryList.stream().map(recordWarehouseDO -> {
            RecordWarehouseDO newRecordWarehouse = ConvertUtil.beanConvert(recordWarehouseDO, RecordWarehouseDO.class, "id");
            newRecordWarehouse.setCustomsBookId(addBook.getId());
            newRecordWarehouse.setCustomsBookNo(addBook.getBookNo());
            return newRecordWarehouse;
        }).collect(Collectors.toList());
        recordWarehouseBaseService.saveBatch(warehouseDOS);
    }

    @Override
    public void retryAuditMsg(List<Long> idList) {
        List<GoodsRecordDTO> goodsRecordDTOS = this.findById(idList);
        List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsService.findByRecordId(idList);
        Map<Long, List<RecordCustomsDTO>> recordCustomsMap = recordCustomsDTOList.stream().collect(Collectors.groupingBy(RecordCustomsDTO::getRecordId));
        boolean isRetry = false;
        for (GoodsRecordDTO recordDTO : goodsRecordDTOS) {
            if (GoodsRecordChannel.LOGISTICS.getValue().equals(recordDTO.getChannel()) && recordCustomsMap.containsKey(recordDTO.getId())) {
                List<RecordCustomsDTO> recordCustomsDTOS = recordCustomsMap.get(recordDTO.getId());
                for (RecordCustomsDTO recordCustomsDTO : recordCustomsDTOS) {
                    GoodsRecordAuditSubmitDTO auditSubmitDTO = new GoodsRecordAuditSubmitDTO();
                    BeanUtils.copyProperties(recordDTO, auditSubmitDTO);
                    GoodsRecordStatusEnum statusEnum = GoodsRecordStatusEnum.getEnum(recordCustomsDTO.getStatus());
                    switch (statusEnum) {
                        case RECORD_SUCCESS:
                            auditSubmitDTO.setOpinion(1);
                            break;
                        case EXAMINE_REFUSE:
                            auditSubmitDTO.setOpinion(0);
                            auditSubmitDTO.setReason(recordCustomsDTO.getReason());
                            break;
                        default:
                            log.info("备案料号={},口岸状态为={}, 不回传ERP", recordDTO.getProductId(), statusEnum.getDesc());
                            continue;
                    }
                    auditSubmitDTO.setFirstUnit(recordCustomsDTO.getFirstUnit());
                    auditSubmitDTO.setFirstUnitAmount(recordCustomsDTO.getFirstUnitAmount());
                    auditSubmitDTO.setSecondUnit(recordCustomsDTO.getSecondUnit());
                    auditSubmitDTO.setSecondUnitAmount(recordCustomsDTO.getSecondUnitAmount());
                    auditSubmitDTO.setHsCode(recordCustomsDTO.getHsCode());
                    auditSubmitDTO.setChannel(GoodsRecordChannel.LOGISTICS);
                    auditSubmitDTO.setCustomsCode(recordCustomsDTO.getCustomsCode());
                    auditSubmitDTO.setSku(recordDTO.getSkuId());
                    CustomsBookResVo customsBookDTO = customsBookService.findByIdV2(recordDTO.getCustomsBookId());
                    if (Objects.nonNull(customsBookDTO)) {
                        auditSubmitDTO.setBookNo(customsBookDTO.getBookNo());
                    }
                    log.info("[op:GoodsRecordServiceImpl-retryAuditMsg auditSubmitDto={}]", JSON.toJSONString(auditSubmitDTO));
                    auditCallbackProducer.send(auditSubmitDTO);
                    isRetry = true;
                }
            }
        }
        if (Boolean.FALSE.equals(isRetry)) {
            throw new ArgsInvalidException("请选择含已审核/已驳回口岸的备案");
        }
    }

    /**
     * 查商品备案
     *
     * @param productId     统一料号
     * @param customsBookId 账册id
     * @return
     */
    @Override
    public List<GoodsRecordDTO> findByProductIdAndCustomsBookId(String productId, Long customsBookId) {
        return goodsRecordBaseService.findByProductIdAndCustomsBookId(productId, customsBookId);
    }

    /**
     * 查商品备案
     *
     * @param productId        统一料号
     * @param erpWarehouseCode erp实体仓code
     * @return
     */
    @Override
    public List<GoodsRecordDTO> findByProductIdAndErpWarehouseCode(String productId, String erpWarehouseCode) {
        return goodsRecordBaseService.findByProductIdAndErpWarehouseCode(productId, erpWarehouseCode);
    }

    public GoodsRecordDTO findOldByBookIdTenantIdAndSkuId(Long customsBookId, String tenantId, String skuId) {
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setCustomsBookId(customsBookId);
        goodsRecordDO.setTenantId(tenantId);
        goodsRecordDO.setSkuId(skuId);
        goodsRecordDO.setRecordType(GoodsRecordTypeEnums.OLD.getCode());
        try {
            GoodsRecordDO recordDO = goodsRecordMapper.selectOne(goodsRecordDO);
            return ConvertUtil.beanConvert(recordDO, GoodsRecordDTO.class);
        } catch (Exception e) {
            log.error("老备案查询失败 error={}", e.getMessage(), e);
            throw new ArgsInvalidException("老备案查询失败");
        }
    }

    private RecordTrackLogSaveDTO buildTrackLogNew(Long goodsRecordId, String source, String customs) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(goodsRecordId);
        recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.CREATE);
        recordTrackLogSaveDTO.setCustoms(customs);
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        if (Objects.nonNull(source)) {
            switch (source) {
                case ItemTrackLogConfig.ITEM_TYPE_IMPORT:
                    recordTrackLogSaveDTO.setOperateDes("导入成功");
                    break;
                case ItemTrackLogConfig.ITEM_TYPE_RECEIVE:
                    recordTrackLogSaveDTO.setOperateDes("记账回执接收成功");
                    break;
                case ItemTrackLogConfig.ITEM_TYPE_ERP_FIRST:
                    recordTrackLogSaveDTO.setOperateDes("ERP下发");
                    break;
                case ItemTrackLogConfig.ITEM_TYPE_ERP_AUTO:
                    recordTrackLogSaveDTO.setOperateDes("ERP下发(自动)");
                    recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getDefaultSystmeUser().getUserName()); //系统
                    break;
                case ItemTrackLogConfig.ITEM_TYPE_ERP_CCS_IMPORT_AUTO:
                    recordTrackLogSaveDTO.setOperateDes("导入成功（清关系统发起）");
                    GoodsRecordDTO goodsRecordDTO = this.findById(goodsRecordId);
                    String operator = (String) redisTemplate.boundHashOps(ItemTrackLogConfig.ITEM_TYPE_ERP_CCS_IMPORT_AUTO + "_operator")
                            .get(goodsRecordDTO.getExternalProductId() + '_' + customs);
                    log.info("importAutoRecord - redisKey={} - operator={}", goodsRecordDTO.getExternalProductId() + '_' + customs, operator);
                    recordTrackLogSaveDTO.setOperator(operator);
                    break;
                default:
                    break;
            }
        }
        recordTrackLogSaveDTO.setOperateTime(new Date());
        return recordTrackLogSaveDTO;
    }

    /**
     * 构建启用/禁用日志并发送
     *
     * @param goodsRecordId
     * @param enable
     */
    private void buildTrackLogEnableAndSend(Long goodsRecordId, Integer enable) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogEnable(goodsRecordId, enable);
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    /**
     * 构建启用/禁用日志
     *
     * @param goodsRecordId
     * @param enable
     * @return
     */
    private RecordTrackLogSaveDTO buildTrackLogEnable(Long goodsRecordId, Integer enable) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(goodsRecordId);
        if (Objects.nonNull(enable)) {
            switch (enable) {
                case 0:
                    recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.DISABLE);
                    recordTrackLogSaveDTO.setOperateDes("库存状态：启用->禁用");
                    break;
                case 1:
                    recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.ENABLE);
                    recordTrackLogSaveDTO.setOperateDes("库存状态：禁用->启用");
                    break;
                default:
                    break;
            }
        }
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        recordTrackLogSaveDTO.setOperateTime(new Date());
        return recordTrackLogSaveDTO;
    }

    /**
     * 构建更新日志并发送
     *
     * @param before
     * @param after
     */
    private void buildTrackLogDiffAndSend(GoodsRecordDO before, GoodsRecordDO after, String customs, String operateType) {
        GoodsRecordDTO beforeDTO = new GoodsRecordDTO();
        GoodsRecordDTO afterDTO = new GoodsRecordDTO();
        BeanUtils.copyProperties(before, beforeDTO);
        BeanUtils.copyProperties(after, afterDTO);
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogDiff(beforeDTO, afterDTO, customs, operateType);
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    private void buildTrackLogDiffAndSend(GoodsRecordDTO before, GoodsRecordDTO after, String customs) {
        this.buildTrackLogDiffAndSend(before, after, customs, null);
    }

    /**
     * 构建更新日志并发送
     *
     * @param before
     * @param after
     */
    @Override
    public void buildTrackLogDiffAndSend(GoodsRecordDTO before, GoodsRecordDTO after, String customs, String operateType) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogDiff(before, after, customs, operateType);
        if (Objects.nonNull(recordTrackLogSaveDTO)) {
            if (CollectionUtils.isEmpty(recordTrackLogSaveDTO.getOperateExtra()) && !Objects.equals(operateType, ItemTrackLogConfig.AUDIT_PASS)) {
                log.info("goodsRecordServiceImpl buildTrackLogDiffAndSend 前后比较为空");
                return;
            }
        }
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    private RecordTrackLogSaveDTO buildTrackLogDiff(GoodsRecordDTO before, GoodsRecordDTO after, String customs) {
        return this.buildTrackLogDiff(before, after, customs, null);
    }

    /**
     * 构建更新日志
     *
     * @param before
     * @param after
     * @return
     */
    private RecordTrackLogSaveDTO buildTrackLogDiff(GoodsRecordDTO before, GoodsRecordDTO after, String customs, String operatorType) {
        log.info("buildTrackLogDiff before={}", JSON.toJSONString(before));
        log.info("buildTrackLogDiff after={}", JSON.toJSONString(after));
        log.info("buildTrackLogDiff customs={} operatorType={}", customs, operatorType);
        RecordTrackLogSaveDTO dto = new RecordTrackLogSaveDTO();
        dto.setGoodsRecordId(before.getId());
        dto.setOperateType(Objects.nonNull(operatorType) ? operatorType : ItemTrackLogConfig.UPDATE_GOODS);
        dto.setCustoms(customs);
        dto.setOperator(SimpleUserHelper.getRealUserName());
        dto.setOperateTime(new Date());
        GoodsRecordCompareDTO beforeCompare = new GoodsRecordCompareDTO();
        GoodsRecordCompareDTO afterCompare = new GoodsRecordCompareDTO();
        BeanUtils.copyProperties(before, beforeCompare);
        BeanUtils.copyProperties(after, afterCompare);
        log.info("buildTrackLogDiff beforeCompare={}", JSON.toJSONString(beforeCompare));
        log.info("buildTrackLogDiff afterCompare={}", JSON.toJSONString(afterCompare));
        List<ItemTrackLogExtraDTO> itemTrackLogExtraDTOS = null;
        if (operatorType.equalsIgnoreCase(ItemTrackLogConfig.UPDATE_GOODS_ERP)) {
            itemTrackLogExtraDTOS = TrackLogUtils.generateDiffExtra(beforeCompare, afterCompare, "externalGoodsId", "cainiaoGoodsId");
        } else {
            itemTrackLogExtraDTOS = TrackLogUtils.generateDiffExtra(beforeCompare, afterCompare);
        }
        dto.setOperateExtra(itemTrackLogExtraDTOS);
        return dto;
    }

    @Override
    public void buildTrackLogDiffAndSend(Long recordId, GoodsRecordCompareDTO before, GoodsRecordCompareDTO after, String customs, String operateType) {
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = this.buildTrackLogDiff(recordId, before, after, customs, operateType);
        if (Objects.nonNull(recordTrackLogSaveDTO)) {
            if (CollectionUtils.isEmpty(recordTrackLogSaveDTO.getOperateExtra()) && !Objects.equals(operateType, ItemTrackLogConfig.AUDIT_PASS)) {
                log.info("goodsRecordServiceImpl buildTrackLogDiffAndSend 前后比较为空");
                return;
            }
        }
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);
    }

    @Override
    public List<RecordWarehouseProductIdDTO> findByCustomsDeclareProductIdAndCustomsBook(String customsDeclareProductId, Long customsBookId) {
        return recordWarehouseProductIdBaseService.findByCustomsDeclareProductIdAndCustomsBook(customsDeclareProductId, customsBookId);
    }

    @Override
    public void batchAuditByImport(GoodsRecordAuditSubmitV2 submit, Long operatorId) {
        if (Objects.nonNull(operatorId)) {
            Map<Long, UserRpcResult> userRpcResultMap = userServiceUtil.listByIds(Collections.singletonList(operatorId));
            UserRpcResult userRpcResult = userRpcResultMap.get(operatorId);
            CurrentUserInfo currentUserInfo = new CurrentUserInfo();
            RealUserInfo realUserInfo = new RealUserInfo();
            realUserInfo.setUserId(userRpcResult.getId());
            realUserInfo.setUserName(userRpcResult.getUserName());
            currentUserInfo.setRealUser(realUserInfo);
            SimpleUserHelper.setCurrentUserInfo(currentUserInfo);
        }
        goodsRecordService.auditV2(submit);
    }

    /**
     * 根据京东备案创建商品备案
     *
     * @param itemCode
     * @param jdGoodsRecordDTO
     * @return
     */
    private GoodsRecordDO buildGoodsRecordDOByJdGoodsRecord(String itemCode, JdGoodsRecordDTO jdGoodsRecordDTO) {
        GoodsRecordDO goodsRecordDO = new GoodsRecordDO();
        goodsRecordDO.setSkuId(itemCode);
        goodsRecordDO.setProductId(itemCode);
        goodsRecordDO.setBarCode(jdGoodsRecordDTO.getUpc());
        goodsRecordDO.setGoodsRecordName(jdGoodsRecordDTO.getItemChineseName());
        goodsRecordDO.setDeclarePrice(new BigDecimal(jdGoodsRecordDTO.getItemSalePrice()));
        goodsRecordDO.setDeclareUnit(jdGoodsRecordDTO.getHgMeteringUnit());
        goodsRecordDO.setDeclareCurrency("142");
        goodsRecordDO.setNetWeight(jdGoodsRecordDTO.getJingZhong());
        goodsRecordDO.setGrossWeight(jdGoodsRecordDTO.getMaoZhong());
        goodsRecordDO.setSkuPicture(null);
        goodsRecordDO.setLength(null);
        goodsRecordDO.setWidth(null);
        goodsRecordDO.setHeight(null);
        goodsRecordDO.setModel(jdGoodsRecordDTO.getPackSpecification());
        goodsRecordDO.setBrand(jdGoodsRecordDTO.getChineseBrand());
        goodsRecordDO.setBrandEn(jdGoodsRecordDTO.getEnglishBrand());
        goodsRecordDO.setLesseeNo(jdGoodsRecordDTO.getShangJiaId());
        goodsRecordDO.setWarehouseId(null);
        if (Objects.equals(jdGoodsRecordDTO.getType(), JdGoodsRecordType.DIRECT)) {
            goodsRecordDO.setTenantId("JDZY");
        } else if (Objects.equals(jdGoodsRecordDTO.getType(), JdGoodsRecordType.POP)) {
            goodsRecordDO.setTenantId(jdGoodsRecordDTO.getShangJiaId());
        } else if (Objects.equals(jdGoodsRecordDTO.getType(), JdGoodsRecordType.INDEPENDENT)
                || Objects.equals(jdGoodsRecordDTO.getType(), JdGoodsRecordType.POP_NEW)) {
            JdSellerRecordQueryParam param = new JdSellerRecordQueryParam();
            param.setEclpCode(jdGoodsRecordDTO.getEclpCode());
            try {
                log.info("根据京东备案创建商品备案 调用oms接口事业部编码获取商家ID、店铺名称 param={}", JSON.toJSONString(param));
                List<JdSellerRecordRpcDTO> jdSellerRecordRpcDTOS = iJdSellerRecordRpcFacade.list(param);
                log.info("根据京东备案创建商品备案 调用oms接口事业部编码获取商家ID、店铺名称 jdSellerRecordRpcDTOS={}", JSON.toJSONString(jdSellerRecordRpcDTOS));
                if (!CollectionUtils.isEmpty(jdSellerRecordRpcDTOS)) {
                    JdSellerRecordRpcDTO jdSellerRecordRpcDTO = jdSellerRecordRpcDTOS.get(0);
                    if (Objects.nonNull(jdSellerRecordRpcDTO)) {
                        goodsRecordDO.setTenantId(String.valueOf(jdSellerRecordRpcDTO.getVenderId()));
                    }
                }
            } catch (Exception e) {
                log.error("根据京东备案创建商品备案 调用oms接口事业部编码获取商家ID、店铺名称异常", e);
            }
        }
        goodsRecordDO.setChannel(null);
        goodsRecordDO.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
        goodsRecordDO.setErpCommitStatus(0);
        goodsRecordDO.setReason(null);
        goodsRecordDO.setHsCode(jdGoodsRecordDTO.getHs());
        goodsRecordDO.setVatRate(jdGoodsRecordDTO.getVat());
        goodsRecordDO.setTaxRate(jdGoodsRecordDTO.getAdValoremConsumption().intValue());
        goodsRecordDO.setComposition(jdGoodsRecordDTO.getElement());
        goodsRecordDO.setHgsbys(jdGoodsRecordDTO.getDeclareElement());
        goodsRecordDO.setOriginCountry(jdGoodsRecordDTO.getHgOriginCountry());
        goodsRecordDO.setRecordFunction(jdGoodsRecordDTO.getAbility());
        goodsRecordDO.setRecordUsage(jdGoodsRecordDTO.getPurpose());
        goodsRecordDO.setFirstUnit(jdGoodsRecordDTO.getLegalFirstUnit());
        goodsRecordDO.setFirstUnitAmount(jdGoodsRecordDTO.getFirstAmount());
        goodsRecordDO.setSecondUnit(jdGoodsRecordDTO.getLegalSecondUnit());
        goodsRecordDO.setSecondUnitAmount(jdGoodsRecordDTO.getSecondAmount());
        //默认启用
        goodsRecordDO.setEnable(1);
        goodsRecordDO.setRecordFinishTime(new Date());
        goodsRecordDO.setCountryRecordNo(jdGoodsRecordDTO.getGjRecordCode());
        goodsRecordDO.setIoGoodsSerialNo(null);
        goodsRecordDO.setGoodsRegNo(null);
        goodsRecordDO.setImportEntryDeclareNo(null);
        goodsRecordDO.setCiqOriginCountry(null);
        goodsRecordDO.setCustoms(null);
        goodsRecordDO.setGoodsCode(null);
        goodsRecordDO.setProductCompanyName(jdGoodsRecordDTO.getProductCompanyName());
        goodsRecordDO.setProductCompanyRegisterNumber(null);
        goodsRecordDO.setProductCompanyAddress(jdGoodsRecordDTO.getProductCompanyAddress());
        goodsRecordDO.setProductLink(jdGoodsRecordDTO.getSaleWebsite());
        goodsRecordDO.setAttachmentName(null);
        goodsRecordDO.setAttachmentUrl(null);
        goodsRecordDO.setFrontImage(null);
        goodsRecordDO.setSideImage(null);
        goodsRecordDO.setBackImage(null);
        goodsRecordDO.setLabelImage(null);
        if (Objects.nonNull(jdGoodsRecordDTO.getSafeDays())) {
            try {
                goodsRecordDO.setShelfLife(Integer.valueOf(jdGoodsRecordDTO.getSafeDays()));
            } catch (Exception e) {
                log.info("createGoodsRecordByJdGoodsRecord safeDays is not number :{}", jdGoodsRecordDTO.getSafeDays());
                goodsRecordDO.setShelfLife(null);
            }
        } else {
            goodsRecordDO.setShelfLife(null);
        }
        goodsRecordDO.setExternalProductId(null);
        goodsRecordDO.setRecordType(GoodsRecordTypeEnums.NEW.getCode());
        goodsRecordDO.setGoodsSource(GoodsRecordEnum.SELF_BUILT.getCode());
        goodsRecordDO.setGoodsRecordTag(null);
        goodsRecordDO.setExternalGoodsId(null);
        goodsRecordDO.setCainiaoGoodsId(null);
        goodsRecordDO.setDeleted(false);
        UserUtils.setCreateAndUpdateBy(goodsRecordDO);
        return goodsRecordDO;
    }

    @Override
    public void createGoodsRecordByJdGoodsRecord(Long jdGoodsRecordId) {
        log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={}", jdGoodsRecordId);
        JdGoodsRecordDTO jdGoodsRecordDTO = jdGoodsRecordService.findById(jdGoodsRecordId);
        if (Objects.isNull(jdGoodsRecordDTO)) {
            log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={} jdGoodsRecordDTO is null", jdGoodsRecordId);
            return;
        }
        Long servProviderId = jdGoodsRecordDTO.getServProviderId();
        JdServProviderDTO jdServProviderDTO = jdServProviderService.findById(servProviderId);
        if (Objects.isNull(jdServProviderDTO)) {
            log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={} servProviderId={} jdServProviderDTO is null", jdGoodsRecordId, servProviderId);
            return;
        }
        String customsCode = jdServProviderDTO.getCustomsCode();
        // 用货号
        String itemCode = jdGoodsRecordDTO.getItemCode();
        if (Objects.isNull(itemCode)) {
            log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={} itemCode is null", jdGoodsRecordId);
            return;
        }
        List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordBaseService.selectByProductId(itemCode);
        if (!CollectionUtils.isEmpty(goodsRecordDTOS)) {
            log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={} itemCode={} goodsRecordDTOS={}", jdGoodsRecordId, itemCode, JSON.toJSONString(goodsRecordDTOS));
            return;
        }
        GoodsRecordDO goodsRecordDO = this.buildGoodsRecordDOByJdGoodsRecord(itemCode, jdGoodsRecordDTO);
        goodsRecordMapper.insertSelective(goodsRecordDO);

        Long recordId = goodsRecordDO.getId();
        List<RecordCustomsDTO> customsDTOS = recordCustomsService.findByRecordId(recordId);
        List<String> customsCodeList = customsDTOS.stream().map(RecordCustomsDTO::getCustomsCode).distinct().collect(Collectors.toList());
        log.info("createGoodsRecordByJdGoodsRecord jdGoodsRecordId={} itemCode={} customsCode={} customsCodeList={}", jdGoodsRecordId, itemCode, customsCode, JSON.toJSONString(customsCodeList));
        if (Objects.equals(customsCode, "2924") && !customsCodeList.contains("JINYI")) {
            List<String> warehouseWmsCodeList = Arrays.asList("DT_YWKJWMS0312");
            buildGoodsRecordCustomsAndWarehouse(goodsRecordDO, "JINYI", warehouseWmsCodeList);
        }
        if (Objects.equals(customsCode, "2925") && !customsCodeList.contains("YIWU")) {
            List<String> warehouseWmsCodeList = Arrays.asList("DT_YWZBSANWMS0111");
            buildGoodsRecordCustomsAndWarehouse(goodsRecordDO, "YIWU", warehouseWmsCodeList);
        }
        RecordTrackLogSaveDTO recordTrackLogSaveDTO = new RecordTrackLogSaveDTO();
        recordTrackLogSaveDTO.setGoodsRecordId(goodsRecordDO.getId());
        recordTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.CREATE);
        recordTrackLogSaveDTO.setOperateDes("京东备案审核通过自动创建");
        CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(customsCode);
        recordTrackLogSaveDTO.setCustoms(districtEnum.getCustoms());
        recordTrackLogSaveDTO.setOperateTime(new Date());
        recordTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        this.trackLogEsSaveSend(recordTrackLogSaveDTO);

    }


    private void buildGoodsRecordCustomsAndWarehouse(GoodsRecordDO goodsRecordDO, String customsCode, List<String> warehouseWmsCodeList) {
        log.info("buildGoodsRecordCustomsAndWarehouse goodsRecordDO={}, customsCode={}, warehouseWmsCodeList={}", JSON.toJSONString(goodsRecordDO), customsCode, JSON.toJSONString(warehouseWmsCodeList));
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setRecordId(goodsRecordDO.getId());
        Long recordCustomsId = sequenceService.generateId();
        recordCustomsDO.setId(recordCustomsId);
        recordCustomsDO.setCustoms(CustomsDistrictEnum.getEnum(customsCode).getDesc());
        recordCustomsDO.setCustomsCode(customsCode);
        recordCustomsDO.setHsCode(goodsRecordDO.getHsCode());
        recordCustomsDO.setOriginCountry(goodsRecordDO.getOriginCountry());
        recordCustomsDO.setFirstUnit(goodsRecordDO.getFirstUnit());
        recordCustomsDO.setFirstUnitAmount(goodsRecordDO.getFirstUnitAmount());
        recordCustomsDO.setSecondUnit(goodsRecordDO.getSecondUnit());
        recordCustomsDO.setSecondUnitAmount(goodsRecordDO.getSecondUnitAmount());
        recordCustomsDO.setProductId(goodsRecordDO.getProductId());
        recordCustomsDO.setSubmitType(1);
        recordCustomsDO.setSubmitTime(new Date());
        recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.MANUAL.getCode());
        recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
        recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
        recordCustomsDO.setBaseInfoJson(JSON.toJSONString(goodsRecordDO));
        log.info("buildGoodsRecordCustomsAndWarehouse recordCustomsDO={}", JSON.toJSONString(recordCustomsDO));
        recordCustomsBaseService.save(recordCustomsDO);
        List<EntityWarehouseDTO> warehouseDTOS = entityWarehouseService.findDTOByWmsCode(warehouseWmsCodeList);
        log.info("buildGoodsRecordCustomsAndWarehouse warehouseDTOS={}", JSON.toJSONString(warehouseDTOS));
        warehouseDTOS.forEach(e -> {
            RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
            recordWarehouseDO.setRecordId(goodsRecordDO.getId());
            recordWarehouseDO.setProductId(goodsRecordDO.getProductId());
            recordWarehouseDO.setRecordCustomsId(recordCustomsId);
            BeanUtils.copyProperties(e, recordWarehouseDO, "id", "createTime", "updateTime", "deleted");
            recordWarehouseDO.setWarehouseSn(e.getSn());
            log.info("buildGoodsRecordCustomsAndWarehouse recordWarehouseDO={}", JSON.toJSONString(recordWarehouseDO));
            recordWarehouseBaseService.save(recordWarehouseDO);
        });
    }


    /**
     * @param recordId
     * @param before
     * @param after
     * @param customs
     * @param operatorType
     * @return
     */
    public RecordTrackLogSaveDTO buildTrackLogDiff(Long recordId, GoodsRecordCompareDTO before, GoodsRecordCompareDTO after, String customs, String operatorType) {
        log.info("buildTrackLogDiff before={}", JSON.toJSONString(before));
        log.info("buildTrackLogDiff after={}", JSON.toJSONString(after));
        log.info("buildTrackLogDiff customs={} operatorType={}", customs, operatorType);
        RecordTrackLogSaveDTO dto = new RecordTrackLogSaveDTO();
        dto.setGoodsRecordId(recordId);
        dto.setOperateType(Objects.nonNull(operatorType) ? operatorType : ItemTrackLogConfig.UPDATE_GOODS);
        dto.setCustoms(customs);
        dto.setOperator(SimpleUserHelper.getRealUserName());
        dto.setOperateTime(new Date());
        List<ItemTrackLogExtraDTO> itemTrackLogExtraDTOS = TrackLogUtils.generateDiffExtra(before, after);
        dto.setOperateExtra(itemTrackLogExtraDTOS);
        return dto;
    }
}
