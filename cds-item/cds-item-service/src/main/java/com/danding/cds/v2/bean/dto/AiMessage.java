package com.danding.cds.v2.bean.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * AI消息对象
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
@Data
@Accessors(chain = true)
public class AiMessage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 消息内容
     */
    private String content;

    /**
     * 角色类型：user、assistant、system
     */
    private String role;

    /**
     * 构造方法
     */
    public AiMessage() {
    }

    /**
     * 构造方法
     *
     * @param content 消息内容
     * @param role    角色类型
     */
    public AiMessage(String content, String role) {
        this.content = content;
        this.role = role;
    }

    /**
     * 创建用户消息
     *
     * @param content 消息内容
     * @return AiMessage
     */
    public static AiMessage createUserMessage(String content) {
        return new AiMessage(content, "user");
    }

    /**
     * 创建助手消息
     *
     * @param content 消息内容
     * @return AiMessage
     */
    public static AiMessage createAssistantMessage(String content) {
        return new AiMessage(content, "assistant");
    }

    /**
     * 创建系统消息
     *
     * @param content 消息内容
     * @return AiMessage
     */
    public static AiMessage createSystemMessage(String content) {
        return new AiMessage(content, "system");
    }
}
