package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.dao.ProcessTradeBookDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookDeclareCompanyTypeEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookStatusEnums;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookDetailEditReq;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.cds.v2.service.base.ProcessTradeBookBaseService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 加工贸易账册 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Slf4j
@DubboService
public class ProcessTradeBookServiceImpl implements ProcessTradeBookService {

    @Resource
    private ProcessTradeBookBaseService baseService;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private ProcessTradeBookItemService processTradeBookItemService;

    @DubboReference
    private ProcessTradeBookConsumptionService processTradeBookConsumptionService;

    @Override
    public ListVO<ProcessTradeBookDTO> paging(ProcessTradeBookSearch search) {
        return baseService.paging(search);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(Long bookId, Integer bookType) {
        CustomsBookResVo customsBookDTO = customsBookService.findByIdV2(bookId);
        if (Objects.equals(customsBookDTO.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_BOOKS.getCode())) {
            throw new ArgsInvalidException("账册类型不是保税账册");
        }
        ProcessTradeBookDTO processTradeBookDTO = this.findAllByLogisticsBookId(bookId);
        if (Objects.nonNull(processTradeBookDTO)) {
            throw new ArgsInvalidException("加工贸易账册已存在");
        }
        Long areaCompanyId = customsBookDTO.getAreaCompanyId();
        CompanyDTO companyDTO = companyService.findById(areaCompanyId);
        ProcessTradeBookDO insertDO = new ProcessTradeBookDO();
        insertDO.setSn(sequenceService.generateProcessTradeBookSn());
        insertDO.setLogisticsBookId(bookId);
        insertDO.setProcessTradeBookNo(customsBookDTO.getBookNo());
        insertDO.setCompanyName(companyDTO.getName());
        insertDO.setMasterCustoms(customsBookDTO.getCustomsDistrictCode());
        insertDO.setProcessCompanyId(customsBookDTO.getAreaCompanyId());
        insertDO.setOperateCompanyId(customsBookDTO.getAreaCompanyId());
        insertDO.setDeclareCompanyId(customsBookDTO.getAreaCompanyId());
        insertDO.setInputCompanyId(customsBookDTO.getAreaCompanyId());
        insertDO.setDeclareCompanyType(ProcessTradeBookDeclareCompanyTypeEnums.ENTERPRISE.getCode());
        insertDO.setBookType(bookType);
        insertDO.setStatus(ProcessTradeBookStatusEnums.CREATED.getCode());
        insertDO.setEnable(1);
        baseService.insertSelective(insertDO);
    }

    @Override
    public void enable(List<Long> idList, Integer enable) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        if (enable != 1 && enable != 0) {
            throw new ArgsInvalidException("启用状态异常");
        }
        ProcessTradeBookDO updateDO = new ProcessTradeBookDO();
        updateDO.setEnable(enable);
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andIn("id", idList);
        baseService.updateByExampleSelective(updateDO, example);
    }

    @Override
    public void updateStatus(List<Long> idList, Integer status) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        if (Objects.isNull(status)) {
            throw new ArgsInvalidException("数据状态不能为空");
        }
        ProcessTradeBookDO updateDO = new ProcessTradeBookDO();
        updateDO.setStatus(status);
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andIn("id", idList);
        baseService.updateByExampleSelective(updateDO, example);
    }

    @Override
    public ProcessTradeBookDTO findById(Long id) {
        return ConvertUtil.beanConvert(baseService.findById(id), ProcessTradeBookDTO.class);
    }

    @Override
    public List<ProcessTradeBookDTO> findById(List<Long> idList) {
        return ConvertUtil.listConvert(baseService.findById(idList), ProcessTradeBookDTO.class);
    }

    @Override
    public void updateDetail(ProcessTradeBookDetailEditReq editReq) {
        editCheck(editReq.getId());
        ProcessTradeBookDO updateDO = ConvertUtil.beanConvert(editReq, ProcessTradeBookDO.class);
        updateDO.setBookEndValidity(new Date(editReq.getBookEndValidity()));
        baseService.updateByPrimaryKeySelective(updateDO);
    }

    @Override
    public ListVO<ProcessTradeBookItemDTO> itemPaging(ProcessTradeBookItemSearch param) {
        return processTradeBookItemService.paging(param);
    }

    @Override
    public void createOrEditItem(ProcessTradeBookItemDTO param) {
        editCheck(param.getRefBookId());
        processTradeBookItemService.createOrEdit(param);
    }

    @Override
    public void deleteItem(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("账册表体id不能为空");
        }
        Long itemId = idList.get(0);
        ProcessTradeBookItemDTO itemDTO = processTradeBookItemService.findById(itemId);
        Long refBookId = itemDTO.getRefBookId();
        editCheck(refBookId);
        processTradeBookItemService.deleteItem(refBookId, idList);
    }

    @Override
    public ListVO<ProcessTradeBookConsumptionDTO> pagingConsumption(ProcessTradeBookConsumptionSearch param) {
        return processTradeBookConsumptionService.paging(param);
    }

    @Override
    public void createOrEditConsumption(ProcessTradeBookConsumptionDTO param) {
        editCheck(param.getRefBookId());
        processTradeBookConsumptionService.createOrEdit(param);
    }

    @Override
    public void deleteConsumption(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("账册表体id不能为空");
        }
        Long consumptionId = idList.get(0);
        ProcessTradeBookItemDTO consumptionDTO = processTradeBookConsumptionService.findById(consumptionId);
        editCheck(consumptionDTO.getRefBookId());
        processTradeBookConsumptionService.deleteById(idList);
    }

    @Override
    public List<ProcessTradeBookItemDTO> findItemByIdList(List<Long> itemIdList) {
        return processTradeBookItemService.findById(itemIdList);
    }

    @Override
    public List<ProcessTradeBookItemDTO> matchItem(Long id, Long bookId, Integer goodsType, String productId) {
        return processTradeBookItemService.matchItem(id, bookId, goodsType, productId);
    }

    @Override
    public ProcessTradeBookItemDTO findItemByProductIdAndBookId(Integer goodsType, String productId, Long bookId) {
        if (Objects.isNull(bookId)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        ProcessTradeBookDTO processTradeBookDTO = this.findByLogisticsBookId(bookId);
        if (Objects.isNull(processTradeBookDTO)) {
            throw new ArgsInvalidException("加贸账册不存在");
        }
        return processTradeBookItemService.findByProductIdAndBookId(goodsType, productId, processTradeBookDTO.getId());
    }

    @Override
    public List<ProcessTradeBookConsumptionDTO> findConsumptionByProductIdAndBookId(Integer goodsType, String productId, Long bookId) {
        if (Objects.isNull(bookId)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        if (Objects.isNull(goodsType)) {
            throw new ArgsInvalidException("商品类型不能为空");
        }
        if (Objects.isNull(productId)) {
            throw new ArgsInvalidException("料号不能为空");
        }
        ProcessTradeBookItemDTO endPrdItemDTO = this.findItemByProductIdAndBookId(goodsType, productId, bookId);
        return processTradeBookConsumptionService.findByEndPrdId(endPrdItemDTO.getId());
    }

    @Override
    public ProcessTradeBookDTO findByLogisticsBookId(Long bookId) {
        if (Objects.isNull(bookId)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andEqualTo("logisticsBookId", bookId)
                .andEqualTo("enable", 1)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookDO> bookDOList = baseService.selectByExample(example);
        if (CollUtil.isEmpty(bookDOList)) {
            return null;
        }
        ProcessTradeBookDO bookDO = bookDOList.get(0);
        return ConvertUtil.beanConvert(bookDO, ProcessTradeBookDTO.class);
    }

    @Override
    public ProcessTradeBookDTO findAllByLogisticsBookId(Long bookId) {
        if (Objects.isNull(bookId)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andEqualTo("logisticsBookId", bookId)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookDO> bookDOList = baseService.selectByExample(example);
        if (CollUtil.isEmpty(bookDOList)) {
            return null;
        }
        ProcessTradeBookDO bookDO = bookDOList.get(0);
        return ConvertUtil.beanConvert(bookDO, ProcessTradeBookDTO.class);
    }

    @Override
    public List<ProcessTradeBookDTO> findByLogisticsBookId(List<Long> bookIdList) {
        if (CollUtil.isEmpty(bookIdList)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andIn("logisticsBookId", bookIdList)
                .andEqualTo("enable", 1)
                .andEqualTo("deleted", false);
        List<ProcessTradeBookDO> bookDOList = baseService.selectByExample(example);
        return ConvertUtil.listConvert(bookDOList, ProcessTradeBookDTO.class);
    }

    @Override
    public void importItemExcel(Long bookId, Integer goodsType, String operator, List<ProcessTradeBookItemDTO> successList) {
        log.info("加贸账册表体导入 bookId={}, operator={}, successList={}", bookId, JSON.toJSONString(successList), operator);
        processTradeBookItemService.importExcel(bookId, goodsType, successList);
    }

    @Override
    public List<ProcessTradeBookItemDTO> listEndprdSeqById(Long id) {
        if (id == null) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        return processTradeBookItemService.findByGoodsTypeAndBookId(2, id);
    }

    @Override
    public List<ProcessTradeBookItemDTO> listMtpckSeqById(Long id) {
        if (id == null) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        return processTradeBookItemService.findByGoodsTypeAndBookId(1, id);
    }

    @Override
    public List<ProcessTradeBookItemDTO> listItemAllById(Long id) {
        return processTradeBookItemService.listItemAllById(id);
    }

    @Override
    public List<ProcessTradeBookConsumptionDTO> listConsumption(Long refBookId) {
        return processTradeBookConsumptionService.findByRefBookId(refBookId);
    }

    @Override
    public void importConsumptionExcel(Long refBookId, String operator, List<ProcessTradeBookConsumptionDTO> successList) {
        processTradeBookConsumptionService.importExcel(refBookId, operator, successList);
    }

    private void editCheck(Long bookId) {
        if (Objects.isNull(bookId)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        ProcessTradeBookDTO processTradeBookDTO = this.findById(bookId);
        if (Objects.isNull(processTradeBookDTO)) {
            throw new ArgsInvalidException("加贸账册不存在");
        }
        if (ProcessTradeBookStatusEnums.RECORDED.getCode().equals(processTradeBookDTO.getStatus())
                || ProcessTradeBookStatusEnums.RECORDING.getCode().equals(processTradeBookDTO.getStatus())) {
            throw new ArgsInvalidException("数据状态" + ProcessTradeBookStatusEnums.getDesc(processTradeBookDTO.getStatus()) + "无法编辑表体");
        }
        if (Objects.equals(1, processTradeBookDTO.getEnable())) {
            throw new ArgsInvalidException("数据状态启用中，无法编辑表体");
        }
    }
}