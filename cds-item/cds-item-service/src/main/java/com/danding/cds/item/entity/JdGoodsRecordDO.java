package com.danding.cds.item.entity;

import com.danding.cds.common.model.BaseDO;
import com.danding.cds.item.api.enums.JdGoodsRecordStatusEnum;
import com.danding.cds.item.api.enums.JdGoodsRecordType;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Table(name = "ccs_jd_goods_record")
@Data
public class JdGoodsRecordDO extends BaseDO {

    /**
     * 备案信息类型(区分京东自营还是pop)
     */
    @Column(name = "type")
    private JdGoodsRecordType type;

    /**
     * 流程唯一标识
     */
    @Column(name = "flow_sign")
    private String flowSign;

    /**
     * 商品编号
     */
    @Column(name = "sku_id")
    private String skuId;

    /**
     * 主sku(共享货号)
     */
    @Column(name = "main_sku_id")
    private String mainSkuId;

    /**
     * 主商品编码
     */
    @Column(name = "emg")
    private String emg;

    /**
     * 主商品编码-京东交互
     */
    @Column(name = "jd_emg")
    private String jdEmg;

    /**
     * 商品货号
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商家id
     */
    @Column(name = "shang_jia_id")
    private String shangJiaId;

    /**
     * 店铺编号
     */
    @Column(name = "shop_code")
    private String shopCode;

    /**
     * 店铺名称
     */
    @Column(name = "shop_name")
    private String shopName;

    /**
     * 商品名称(中文)
     */
    @Column(name = "item_chinese_name")
    private String itemChineseName;

    /**
     * 口岸名称
     */
    @Column(name = "custom_label")
    private String customLabel;

    /**
     * 流程开始时间
     */
    @Column(name = "flow_start_time")
    private Date flowStartTime;

    /**
     * 流程状态
     */
    @Column(name = "flow_state")
    private JdGoodsRecordStatusEnum flowState;

    /**
     * 流程状态名称
     */
    @Column(name = "flow_state_label")
    private String flowStateLabel;

    /**
     * 条形码
     */
    private String upc;

    /**
     * hs编码
     */
    private String hs;

    /**
     * 品牌（中文）
     */
    @Column(name = "chinese_brand")
    private String chineseBrand;

    /**
     * 品牌（英文）
     */
    @Column(name = "english_brand")
    private String englishBrand;

    /**
     * 商品名称（英文）
     */
    @Column(name = "item_english_name")
    private String itemEnglishName;

    /**
     * 毛重
     */
    @Column(name = "mao_zhong")
    private BigDecimal maoZhong;

    /**
     * 净重
     */
    @Column(name = "jing_zhong")
    private BigDecimal jingZhong;

    /**
     * 实物重量
     */
    @Column(name = "actual_weight")
    private BigDecimal actualWeight;

    /**
     * 企业名称
     */
    @Column(name = "cc_provider_name")
    private String ccProviderName;

    /**
     * 型号
     */
    private String model;

    /**
     * 申报合同单位
     */
    @Column(name = "declare_agreement_unit")
    private String declareAgreementUnit;

    /**
     * 法定第一单位数量
     */
    @Column(name = "first_amount")
    private BigDecimal firstAmount;

    /**
     * 法定第一单位数量
     */
    @Column(name = "second_amount")
    private BigDecimal secondAmount;

    /**
     * 法定第一单位
     */
    @Column(name = "legal_first_unit")
    private String legalFirstUnit;

    /**
     * 法定第二单位
     */
    @Column(name = "legal_second_unit")
    private String legalSecondUnit;

    /**
     * 包装方式
     */
    @Column(name = "pack_mode")
    private String packMode;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 功能
     */
    private String ability;

    /**
     * 生产企业名称
     */
    @Column(name = "product_company_name")
    private String productCompanyName;

    /**
     * 生产企业地址
     */
    @Column(name = "product_company_address")
    private String productCompanyAddress;

    /**
     * 供应商联系人
     */
    @Column(name = "shang_jia_contacts")
    private String shangJiaContacts;

    /**
     * 供应商联系电话
     */
    @Column(name = "shang_jia_phone")
    private String shangJiaPhone;

    /**
     * 供应商邮箱
     */
    @Column(name = "shang_jia_email")
    private String shangJiaEmail;

    /**
     * 海关原产国
     */
    @Column(name = "hg_origin_country")
    private String hgOriginCountry;

    /**
     * 国检原产国
     */
    @Column(name = "gj_origin_country")
    private String gjOriginCountry;

    /**
     * 原产地区
     */
    @Column(name = "origin_zone")
    private String originZone;

    /**
     * 原产国/地区
     */
    @Column(name = "origin_area")
    private String originArea;

    /**
     * 税费承担方 0：商家承担 1：用户承担 默认0
     */
    @Column(name = "tax_commitments_way")
    private Integer taxCommitmentsWay;

    /**
     * 商品成本价
     */
    @Column(name = "item_cost_price")
    private BigDecimal itemCostPrice;

    /**
     * 商品销售价
     */
    @Column(name = "item_sale_price")
    private String itemSalePrice;

    /**
     * 体积
     */
    private String volume;

    /**
     * 保质期(天数)
     */
    @Column(name = "safe_days")
    private String safeDays;

    /**
     * 销售网站
     */
    @Column(name = "sale_website")
    private String saleWebsite;

    /**
     * 海关计量单位
     */
    @Column(name = "hg_metering_unit")
    private String hgMeteringUnit;

    /**
     * 国检计量单位
     */
    @Column(name = "gj_metering_unit")
    private String gjMeteringUnit;

    /**
     * 备注
     */
    private String remark;

    /**
     * 包装规格
     */
    @Column(name = "pack_specification")
    private String packSpecification;

    /**
     * 申报要素
     */
    @Column(name = "declare_element")
    private String declareElement;

    /**
     * 成分
     */
    private String element;

    /**
     * 商品图片链接拼接字符串
     */
    @Column(name = "item_pic")
    private String itemPic;

    /**
     * 商品资料
     */
    @Column(name = "item_data")
    private String itemData;

    /**
     * 海关备案号
     */
    @Column(name = "hg_record_code")
    private String hgRecordCode;

    /**
     * 国检备案号
     */
    @Column(name = "gj_record_code")
    private String gjRecordCode;

    /**
     * 税率浮动标识(0. 不浮动  1. 化妆品浮动   2. 面膜浮动)
     */
    @Column(name = "tax_float")
    private Integer taxFloat;

    /**
     * 驳回原因
     */
    @Column(name = "back_reason")
    private String backReason;

    /**
     * 错误信息
     */
    @Column(name = "error_desc")
    private String errorDesc;

    /**
     * 增值税率
     */
    private Integer vat;

    /**
     * 从价消费税
     */
    private BigDecimal adValoremConsumption;

    /**
     * 消费税率-从量
     */
    private BigDecimal consumption;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private Date finishTime;

    /**
     * 关区编号
     */
    @Column(name = "customs_region_code")
    private String customsRegionCode;

    /**
     * eclp事业部编号
     */
    @Column(name = "eclp_code")
    private String eclpCode;

    /**
     * eclp事业部名称
     */
    @Column(name = "eclp_name")
    private String eclpName;

    /**
     *
     */
    @Column(name = "goods_attach")
    private String goodsAttach;

    /**
     *
     */
    @Column(name = "mfn_tariff")
    private String mfnTariff;

    /**
     *
     */
    @Column(name = "penalty_tariff")
    private String penaltyTariff;

    /**
     * 1-保税区商品；0-非保税区商品
     */
    @Column(name = "bonded_flag")
    private Integer bondedFlag;

    /**
     * 调用方法
     */
    @Column(name = "operation")
    private Integer operation;

    /**
     * 操作标识
     */
    @Column(name = "operation_sign")
    private Integer operationSign;

    /**
     * pop京东状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 第一次备案完成时间
     */
    @Column(name = "first_record_time")
    private Date firstRecordTime;

    /**
     * 第一次提交的备案时间
     */
    @Column(name = "first_to_customs_time")
    private Date firstToCustomsTime;

    /**
     * 服务商id
     */
    @Column(name = "serv_provider_id")
    private Long servProviderId;

    /**
     * 服务商编码
     */
    private String providerId;

    /**
     * 独立站外部平台的编码
     */
    @Column(name = "platform_id")
    private String platformId;


    /**
     * 独立站外部平台
     */
    @Column(name = "platform_name")
    private String platformName;

    /**
     * 线上备案类型
     * 0:线下
     * 1:线上
     */
    @Column(name = "online_type")
    private Integer onlineType;

    /**
     * jd备案编码
     */
    @Column(name = "jd_record_no")
    private String jdRecordNo;


    /**
     * 模式
     */
    @Column(name = "custom_model")
    private String customsModel;

    /**
     * 修改的信息
     */
    @Column(name = "extend")
    private String extend;

    /**
     * 是否境外套盒0:非套盒 1:套盒
     */
    @Column(name = "if_over_sea_box")
    private Integer ifOverSeaBox;

    /**
     * 套盒数据，只有ifOverSeaBox=1的时候才有数据
     */
    @Column(name = "over_sea_box_json")
    private String overSeaBoxJson;
}