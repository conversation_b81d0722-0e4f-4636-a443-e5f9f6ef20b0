package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.utils.AiRequestBuilder;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.GoodsRecordStatusEnum;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dao.AiRecommendHsCodeDO;
import com.danding.cds.v2.bean.dto.AiCallResponse;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeDTO;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeRefreshDTO;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.service.base.AiRecommendHsCodeBaseService;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * AI推荐商品编码表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Slf4j
@DubboService
@RefreshScope
public class AiRecommendHsCodeServiceImpl implements AiRecommendHsCodeService {

    @Autowired
    private AiRecommendHsCodeServiceImpl self;

    @Resource
    private AiRecommendHsCodeBaseService baseService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private RecordCustomsService recordCustomsService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Resource(name = "aiRecommendHsCodeTaskExecutor")
    private ThreadPoolTaskExecutor aiRecommendHsCodeTaskExecutor;

    @Value("${ai.recommend.hs.code.url:https://ai.yang800.com.cn/api/v1/chat/completions?appId=67e611d623c9c1c87e215275}")
    private String aiRecommendHsCodeUrl;

    @Value("${ai.recommend.hs.code.apiKey:fastgpt-dVR85Rwss31mXNA3xV3MNgFFmflzXlNWRbKzHdeOCBuB75kuJ62s}")
    private String aiRecommendHsCodeApiKey;


    @Override
    public void init(Long recordId) {
        if (Objects.isNull(recordId)) {
            return;
        }
        String taskStatus = stringRedisTemplate.opsForValue().get("ccs:ai:recommend:hs:code:task:" + recordId);
        try {
            if (StringUtil.isBlank(taskStatus) || !"1".equals(taskStatus)) {
                long startTime = System.currentTimeMillis();
                log.info("开始初始化ai推荐hsCode recordId={} startTime {}", recordId, DateUtil.format(new Date(startTime), "yyyy-MM-dd HH:mm:ss"));
                stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "1", 90, TimeUnit.SECONDS);
                Example example = new Example(AiRecommendHsCodeDO.class);
                example.createCriteria()
                        .andEqualTo("deleted", 0)
                        .andEqualTo("recordId", recordId);
                List<AiRecommendHsCodeDO> oldRecommendDOList = baseService.selectByExample(example);

                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(recordId);
                if (goodsRecordDTO == null) {
                    log.error("商品备案为空 recordId={}", recordId);
                    return;
                }

                List<AiRecommendHsCodeDTO> recommendList;
                Map<String, List<AiRecommendHsCodeDO>> custosmCodeMap = new HashMap<>();
                if (CollUtil.isEmpty(oldRecommendDOList)) {
                    log.info("recordId={} 不存在走ai调用 创建数据", recordId);
                    recommendList = callAi(goodsRecordDTO.getGoodsRecordName(), goodsRecordDTO.getComposition());
                } else {
                    log.info("recordId={} 存在历史数据", recordId);
                    custosmCodeMap = oldRecommendDOList.stream().collect(Collectors.groupingBy(AiRecommendHsCodeDO::getCustomsCode));
                    // 历史数据取与备案名称、成分一致的，防止同步成中间数据推荐结果
                    Map<String, List<AiRecommendHsCodeDO>> goodsRecordCustomsCodeMap = oldRecommendDOList.stream().filter(i ->
                            Objects.equals(i.getRecordComposition(), goodsRecordDTO.getComposition()) && Objects.equals(i.getRecordGoodsName(), goodsRecordDTO.getGoodsRecordName())
                    ).collect(Collectors.groupingBy(AiRecommendHsCodeDO::getCustomsCode));
                    if (CollUtil.isEmpty(goodsRecordCustomsCodeMap)) {
                        log.info("recordId={} 历史数据无备案名称、成分相同数据", recordId);
                        recommendList = callAi(goodsRecordDTO.getGoodsRecordName(), goodsRecordDTO.getComposition());
                    } else {
                        log.info("recordId={} 历史数据存在备案名称、成分相同数据", recordId);
                        List<AiRecommendHsCodeDO> oldRecommendList = goodsRecordCustomsCodeMap.entrySet().iterator().next().getValue();
                        recommendList = ConvertUtil.listConvert(oldRecommendList, AiRecommendHsCodeDTO.class);
                    }
                }

                List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsService.findByRecordId(recordId);
                if (CollUtil.isNotEmpty(recordCustomsDTOList)) {
                    List<AiRecommendHsCodeDTO> insertList = new ArrayList<>();
                    for (RecordCustomsDTO recordCustomsDTO : recordCustomsDTOList) {
                        if (erpWaitedAuditRecordProcess(recordId, recordCustomsDTO, custosmCodeMap, goodsRecordDTO))
                            continue;
                        if (custosmCodeMap.containsKey(recordCustomsDTO.getCustomsCode())) {
                            log.info("recordId={} customsCode={} 历史数据存在略过", recordId, recordCustomsDTO.getCustomsCode());
                            continue;
                        }
                        for (AiRecommendHsCodeDTO aiRecommendHsCodeDTO : recommendList) {
                            insertList.add(buildInsertDTO(recordId, recordCustomsDTO.getCustomsCode(), goodsRecordDTO.getGoodsRecordName(),
                                    goodsRecordDTO.getComposition(), aiRecommendHsCodeDTO));
                        }
                    }
                    if (CollUtil.isNotEmpty(insertList)) {
                        baseService.insertList(insertList);
                    }
                }
                log.info("[AiRecommendHsCodeServiceImpl-init] success, recordId={}", recordId);
                stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "2", 60, TimeUnit.SECONDS);
                log.info("初始化ai推荐hsCode recordId={} endTime {} 耗时 {}ms", recordId, DateUtil.format(new Date(startTime), "yyyy-MM-dd HH:mm:ss"), System.currentTimeMillis() - startTime);
            }
        } catch (Exception e) {
            log.error("[AiRecommendHsCodeServiceImpl-init] exception, cause={}", e.getMessage(), e);
            stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "-1", 60, TimeUnit.SECONDS);
        }
    }

    private AiRecommendHsCodeDTO buildInsertDTO(Long recordId, String customsCode, String goodsRecordName, String recordComposition, AiRecommendHsCodeDTO aiRecommendHsCodeDTO) {
        AiRecommendHsCodeDTO insertDTO = new AiRecommendHsCodeDTO();
        BeanUtils.copyProperties(aiRecommendHsCodeDTO, insertDTO);
        insertDTO.setRecordId(recordId);
        insertDTO.setCustomsCode(customsCode);
        insertDTO.setRecordGoodsName(goodsRecordName);
        insertDTO.setRecordComposition(recordComposition);
        insertDTO.setChooseFlag(0);
        return insertDTO;
    }

    private boolean erpWaitedAuditRecordProcess(Long recordId, RecordCustomsDTO recordCustomsDTO, Map<String, List<AiRecommendHsCodeDO>> custosmCodeMap, GoodsRecordDTO goodsRecordDTO) {
        log.info("erpWaitedAuditRecordProcess recordId={} customsCode={} 开始校验历史数据", recordId, recordCustomsDTO.getCustomsCode());
        if (Objects.equals(recordCustomsDTO.getStatus(), GoodsRecordStatusEnum.WAIT_EXAMINE.getCode())
                && Objects.equals(recordCustomsDTO.getErpCommitStatus(), 1)) {
            // 若为erp待审核备案，需要校验erp更新的备案名称和成分与之前是否一致，不一致需要重新获取数据
            String baseInfoJson = recordCustomsDTO.getBaseInfoJson();
            GoodsRecordDTO baseInfo = JSON.parseObject(baseInfoJson, GoodsRecordDTO.class);
            if (custosmCodeMap.containsKey(recordCustomsDTO.getCustomsCode())) {
                List<AiRecommendHsCodeDO> aiRecommendHsCodeDOS = custosmCodeMap.get(recordCustomsDTO.getCustomsCode());
                AiRecommendHsCodeDO historyRecommendHsCode = aiRecommendHsCodeDOS.get(0);
                if (Objects.equals(historyRecommendHsCode.getRecordGoodsName(), baseInfo.getGoodsRecordName())
                        && Objects.equals(historyRecommendHsCode.getRecordComposition(), baseInfo.getComposition())) {
                    log.info("recordId={} customsCode={} 历史数据存在且备案名称和成分无变动略过", recordId, recordCustomsDTO.getCustomsCode());
                    return true;
                } else {
                    log.info("recordId={} customsCode={} 历史数据失效", recordId, recordCustomsDTO.getCustomsCode());
                    custosmCodeMap.remove(recordCustomsDTO.getCustomsCode());
                }
            }
            if (!Objects.equals(baseInfo.getGoodsRecordName(), goodsRecordDTO.getGoodsRecordName())
                    || !Objects.equals(baseInfo.getComposition(), goodsRecordDTO.getComposition())) {
                log.info("recordId={} customsCode={} 历史数据不存在且备案名称和成分有变动，需要重新获取数据", recordId, recordCustomsDTO.getCustomsCode());
                self.initCustomsCore(recordId, recordCustomsDTO.getCustomsCode(), baseInfo.getGoodsRecordName(), baseInfo.getComposition());
                return true;
            }
        }
        return false;
    }

    @Override
    public List<AiRecommendHsCodeDTO> recommendHsCodeList(Long recordId, String customsCode, String recordGoodsName, String recordComposition) {
        List<AiRecommendHsCodeDTO> list = selectByRecordIdAndCustomsCode(recordId, customsCode);
        if (CollUtil.isEmpty(list)) {
            // 创建子线程处理初始化操作
            aiRecommendHsCodeTaskExecutor.submit(new TraceDataRunnable() {
                @Override
                protected void proxy() {
                    log.info("[AiRecommendHsCodeServiceImpl-recommendHsCodeList] recordId={}, customsCode={} 创建初始化线程", recordId, customsCode);
                    init(recordId);
                }
            });
        } else {
            log.info("[AiRecommendHsCodeServiceImpl-recommendHsCodeList] recordId={}, customsCode={} 存在历史数据", recordId, customsCode);
            AiRecommendHsCodeDTO aiRecommendHsCodeDTO = list.get(0);
            if (!Objects.equals(aiRecommendHsCodeDTO.getRecordGoodsName(), recordGoodsName)
                    || !Objects.equals(aiRecommendHsCodeDTO.getRecordComposition(), recordComposition)) {
                log.info("[AiRecommendHsCodeServiceImpl-recommendHsCodeList] recordId={}, customsCode={} 历史数据存在且备案名称和成分有变动，需要重新获取数据", recordId, customsCode);
                aiRecommendHsCodeTaskExecutor.submit(new TraceDataRunnable() {
                    @Override
                    protected void proxy() {
                        log.info("[AiRecommendHsCodeServiceImpl-recommendHsCodeList] recordId={}, customsCode={} 创建初始化线程", recordId, customsCode);
                        initCustoms(recordId, customsCode, recordGoodsName, recordComposition);
                    }
                });
                return Collections.emptyList();
            }
        }
        return list;
    }

    @Override
    public void refreshCustomsRecommend(AiRecommendHsCodeRefreshDTO refreshDTO) {
        if (refreshDTO == null || Objects.isNull(refreshDTO.getRecordId()) || StrUtil.isBlank(refreshDTO.getCustomsCode())) {
            return;
        }
        Long recordId = refreshDTO.getRecordId();
        String customsCode = refreshDTO.getCustomsCode();
        aiRecommendHsCodeTaskExecutor.submit(new TraceDataRunnable() {
            @Override
            protected void proxy() {
                log.info("[AiRecommendHsCodeServiceImpl-recommendHsCodeList] recordId={}, customsCode={} 创建初始化线程", recordId, customsCode);
                initCustoms(recordId, customsCode, refreshDTO.getRecordGoodsName(), refreshDTO.getRecordComposition());
            }
        });
    }

    public void initCustoms(Long recordId, String customsCode, String recordGoodsName, String recordComposition) {
        log.info("initCustoms 开始初始化海关数据 recordId={}", recordId);
        String taskStatus = stringRedisTemplate.opsForValue().get("ccs:ai:recommend:hs:code:task:" + recordId);
        try {
            if (StringUtil.isBlank(taskStatus) || !"1".equals(taskStatus)) {
                stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "1", 90, TimeUnit.SECONDS);
                self.initCustomsCore(recordId, customsCode, recordGoodsName, recordComposition);
                stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "2", 90, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("initCustoms 初始化海关数据异常 recordId={}, cause={}", recordId, e.getMessage(), e);
            stringRedisTemplate.opsForValue().set("ccs:ai:recommend:hs:code:task:" + recordId, "-1", 90, TimeUnit.SECONDS);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void initCustomsCore(Long recordId, String customsCode, String recordGoodsName, String recordComposition) {
        if (Objects.isNull(recordId) || StrUtil.isBlank(customsCode)) {
            log.info("参数为空 recordId={}, customsCode={}", recordId, customsCode);
            return;
        }
        List<AiRecommendHsCodeDTO> list = selectByRecordIdAndCustomsCode(recordId, customsCode);
        String chooseHsCode = list.stream().filter(i -> Objects.equals(i.getChooseFlag(), 1))
                .findAny().map(AiRecommendHsCodeDTO::getRecommendHsCode).orElse(null);
        log.info("initCustoms 清空历史数据 recordId={}, customsCode={}", recordId, customsCode);
        clearHistory(recordId, customsCode);
        List<AiRecommendHsCodeDTO> aiRecommendHsCodeDTOS = callAi(recordGoodsName, recordComposition);
        List<AiRecommendHsCodeDTO> customsInsertList = new ArrayList<>();
        for (AiRecommendHsCodeDTO aiRecommendHsCodeDTO : aiRecommendHsCodeDTOS) {
            if (Objects.equals(aiRecommendHsCodeDTO.getRecommendHsCode(), chooseHsCode)) {
                aiRecommendHsCodeDTO.setChooseFlag(1);
            }
            customsInsertList.add(buildInsertDTO(recordId, customsCode, recordGoodsName, recordComposition, aiRecommendHsCodeDTO));
        }
        if (CollUtil.isNotEmpty(customsInsertList)) {
            baseService.insertList(customsInsertList);
        }
    }

    @Override
    public void choose(Long recordId, String customsCode, String hsCode) {
        if (StrUtil.isBlank(customsCode) || Objects.isNull(recordId)) {
            log.info("参数为空 recordId={}, customsCode={}", recordId, customsCode);
            return;
        }
        clearChooseFlag(recordId, customsCode);
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode)
                .andEqualTo("recommendHsCode", hsCode);
        List<AiRecommendHsCodeDO> aiRecommendHsCodeDOS = baseService.selectByExample(example);
        if (CollUtil.isEmpty(aiRecommendHsCodeDOS)) {
            log.info("未推荐过商品编码 recordId={}, customsCode={} ,hsCode={}", recordId, customsCode, hsCode);
            return;
        }
        AiRecommendHsCodeDO updateDO = new AiRecommendHsCodeDO();
        updateDO.setChooseFlag(1);
        baseService.updateByExample(updateDO, example);
    }

    private void clearChooseFlag(Long recordId, String customsCode) {
        log.info("initChooseFlag 初始化选择标识 recordId={}, customsCode={}", recordId, customsCode);
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode);
        AiRecommendHsCodeDO updateDO = new AiRecommendHsCodeDO();
        updateDO.setChooseFlag(0);
        baseService.updateByExample(updateDO, example);
    }

    @Override
    public void refresh(AiRecommendHsCodeRefreshDTO refreshDTO) {
        if (refreshDTO == null || Objects.isNull(refreshDTO.getRecordId())) {
            log.info("refresh 参数为空 recordId={}", refreshDTO.getRecordId());
            return;
        }
        Long recordId = refreshDTO.getRecordId();
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId);
        List<AiRecommendHsCodeDO> aiRecommendHsCodeDOS = baseService.selectByExample(example);
        List<AiRecommendHsCodeDO> choseList = aiRecommendHsCodeDOS.stream()
                .filter(aiRecommendHsCodeDO -> Objects.equals(aiRecommendHsCodeDO.getChooseFlag(), 1)).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(aiRecommendHsCodeDOS)) {
            AiRecommendHsCodeDO aiRecommendHsCodeDO = aiRecommendHsCodeDOS.get(0);
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(recordId);
            if (!refreshDTO.isForce() && Objects.equals(aiRecommendHsCodeDO.getRecordGoodsName(), goodsRecordDTO.getGoodsRecordName())
                    && Objects.equals(aiRecommendHsCodeDO.getRecordComposition(), goodsRecordDTO.getComposition())) {
                log.info("AiRecommendHsCodeServiceImpl.refresh recordId={}, 备案名称及成分无变动无需刷新数据", recordId);
                return;
            }
            log.info("AiRecommendHsCodeServiceImpl.refresh recordId={}, 清空历史数据", recordId);
            clearHistory(recordId);
        }
        log.info("AiRecommendHsCodeServiceImpl.refresh recordId={}, 初始化数据", recordId);
        init(recordId);
        // 恢复选择标识
        if (CollUtil.isNotEmpty(choseList)) {
            choseList.forEach(aiRecommendHsCodeDO ->
                    choose(aiRecommendHsCodeDO.getRecordId(), aiRecommendHsCodeDO.getCustomsCode(), aiRecommendHsCodeDO.getRecommendHsCode()));
        }
    }

    @Override
    public void clearHistory(Long recordId) {
        if (Objects.isNull(recordId)) {
            return;
        }
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId);
        AiRecommendHsCodeDO updateDO = new AiRecommendHsCodeDO();
        updateDO.setDeleted(true);
        baseService.updateByExampleSelective(updateDO, example);
        stringRedisTemplate.delete("ccs:ai:recommend:hs:code:task:" + recordId);
    }

    public void clearHistory(Long recordId, String customsCode) {
        if (Objects.isNull(recordId)) {
            return;
        }
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode);
        AiRecommendHsCodeDO updateDO = new AiRecommendHsCodeDO();
        updateDO.setDeleted(true);
        baseService.updateByExampleSelective(updateDO, example);
    }

    private List<AiRecommendHsCodeDTO> selectByRecordIdAndCustomsCode(Long recordId, String customsCode) {
        if (Objects.isNull(recordId) || StrUtil.isBlank(customsCode)) {
            throw new ArgsInvalidException("参数为空，请检查");
        }
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria()
                .andEqualTo("deleted", 0)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode);
        List<AiRecommendHsCodeDO> list = baseService.selectByExample(example);
        return ConvertUtil.listConvert(list, AiRecommendHsCodeDTO.class);
    }

    private List<AiRecommendHsCodeDTO> callAi(String goodsName, String component) {
        if (StrUtil.isBlank(goodsName)) {
            throw new ArgsInvalidException("参数为空，请检查");
        }
        component = StrUtil.isBlank(component) ? "暂无" : component;
        // 使用工具类构建AI请求
        String body = AiRequestBuilder.buildHsCodeRecommendRequest(goodsName, component);
        long startTime = System.currentTimeMillis();
        log.info("recommendHsCode callAi startTime={} url:{} ,apiKey{},  body:{}",
                DateUtil.format(new Date(startTime), "yyyy-MM-dd HH:mm:ss"), aiRecommendHsCodeUrl, aiRecommendHsCodeApiKey, body);
        HttpRequest request = HttpRequest.post(aiRecommendHsCodeUrl)
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Authorization", "Bearer " + aiRecommendHsCodeApiKey)
                .send(body);
        if (request.ok()) {
            String responseJson = request.body();
            log.info("recommendHsCode callAi endTime={} 耗时{}ms responseJson:{}",
                    DateUtil.format(new Date(), "yyyy-MM-dd HH:mm:ss"), System.currentTimeMillis() - startTime, responseJson);
            AiCallResponse aiCallResponse = JSON.parseObject(responseJson, AiCallResponse.class);
            String content = aiCallResponse.getChoices().get(0).getMessage().getContent();
            String resultJson = content.substring(content.indexOf("["), content.lastIndexOf("]") + 1);
            List<AiRecommendHsCodeDTO> list = JSONObject.parseArray(resultJson, AiRecommendHsCodeDTO.class);
            log.info("recommendHsCode callAi list:{}", JSONObject.toJSONString(list));
            return list;
        } else {
            log.error("recommendHsCode callAi fail, code={}, body={}", request.code(), request.body());
            throw new ArgsInvalidException("AI服务调用失败");
        }
    }

    @Test
    public void test() {
        String goodsName = "星巴克摩卡风味胶囊咖啡123g/盒";
        String component = "\t主要成分\t浓缩咖啡粉50%、奶粉31.4%、糖17.4%、天然香料1%、牛乳脂0.2%";

        aiRecommendHsCodeUrl = "https://ai.yang800.com.cn/api/v1/chat/completions?appId=67e611d623c9c1c87e215275";
        aiRecommendHsCodeApiKey = "fastgpt-dVR85Rwss31mXNA3xV3MNgFFmflzXlNWRbKzHdeOCBuB75kuJ62s";
        List<AiRecommendHsCodeDTO> aiRecommendHsCodeDTOS = callAi(goodsName, component);
    }
}