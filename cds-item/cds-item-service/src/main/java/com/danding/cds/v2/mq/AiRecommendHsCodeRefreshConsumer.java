package com.danding.cds.v2.mq;


import com.alibaba.fastjson.JSON;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeRefreshDTO;
import com.danding.cds.v2.service.AiRecommendHsCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-ai-recommend-hsCode-refresh-consumer",
        topic = "ccs-ai-recommend-hsCode-refresh-topic"
)
public class AiRecommendHsCodeRefreshConsumer extends MessageHandlerAfterInit {

    @DubboReference
    private AiRecommendHsCodeService aiRecommendHsCodeService;

    @Override
    public void handle(Object param) throws RuntimeException {
        log.info("AiRecommendHsCodeRefreshConsumer handle param={}", param);
        try {
            AiRecommendHsCodeRefreshDTO refreshDTO = JSON.parseObject((String) param, AiRecommendHsCodeRefreshDTO.class);
            aiRecommendHsCodeService.refresh(refreshDTO);
        } catch (Exception e) {
            log.error("AiRecommendHsCodeRefreshConsumer handle param={}, error={}", param, e.getMessage(), e);
        }
    }
}
