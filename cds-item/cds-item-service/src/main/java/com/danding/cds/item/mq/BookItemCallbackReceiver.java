package com.danding.cds.item.mq;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.sdk.enums.CustomsCallBackResultOperEnum;
import com.danding.cds.declare.sdk.model.callback.BookItemCallback;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.GoodsRecordSyncSubmitV2;
import com.danding.cds.item.api.dto.submit.GoodsRecordWarehouseInfoDTO;
import com.danding.cds.item.api.enums.GoodsRecordActionEnum;
import com.danding.cds.item.api.enums.GoodsRecordEnum;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.JdGoodsRecordService;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.service.OrderSnTenantService;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.cds.v2.bean.dao.RecordCustomsDO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.dto.JdWarehouseDTO;
import com.danding.cds.v2.bean.dto.RecordWarehouseDTO;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordSourceEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTypeEnums;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.cds.v2.service.base.*;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.mq.common.handler.MessageSender;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 记账回执mq
 */
@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-book-item-callback-consumer",
        topic = "ccs-book-item-callback-topic"
)
public class BookItemCallbackReceiver extends MessageHandlerAfterInit {

    @Autowired
    private BookItemCallbackReceiver bookItemCallbackReceiver;

    @Autowired
    protected RedisTemplate redisTemplate;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private InventoryChangeService inventoryChangeService;

    @Autowired
    private RecordCustomsBaseService recordCustomsBaseService;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private RecordWarehouseBaseService recordWarehouseBaseService;

    @Autowired
    private EntityWarehouseBaseService entityWarehouseBaseService;

    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;

    @DubboReference
    private OrderSnTenantService orderSnTenantService;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @DubboReference
    private JdGoodsRecordService jdGoodsRecordService;

    @Autowired
    private JdWarehouseBaseService jdWarehouseBaseService;

    /**
     * 修改标记 0-未修改 1-修改 2-删除 3-增加
     */
    static final String MODF_ADD = "3";

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void handle(Object message) throws RuntimeException {
        if (ObjectUtils.isEmpty(message)) {
            return;
        }
        String key = message.toString();
        log.info("[op:BookItemCallbackReceiver-handle] key={}", key);
        String bizData = (String) redisTemplate.boundHashOps(CustomsCallBackResultOperEnum.BOOK_ITEM_BACK.getKey()).get(key);
        BookItemCallback resultInfo = JSON.parseObject(bizData, BookItemCallback.class);
        log.info("[op:BookItemCallbackReceiver] receive info={}", JSON.toJSONString(resultInfo));
        //根据关联单号查询品所在的租户
        String invtNo = resultInfo.getInvtNo();

        if (Objects.isNull(SimpleTenantHelper.getTenantId())) {
            if (Objects.nonNull(invtNo)) {
                Long tenantId = orderSnTenantService.getTenantIdByRealNo(invtNo);
                if (Objects.nonNull(tenantId)) {
                    SimpleTenantHelper.setTenantId(tenantId);
                } else {
                    log.error("记账回执未查询到租户id");
                }
            }
        }
        CustomsBookDTO customsBookDTO = customsBookService.findByCode(resultInfo.getBwsNo());
        if (customsBookDTO == null) {
            return;
        }
        if (MODF_ADD.equals(resultInfo.getModfMarkcd())) {
            CustomsBookItemDTO old = customsBookItemService.findByBookIdAndSeqNoAndProId(customsBookDTO.getId(), resultInfo.getGdsSeqno(), resultInfo.getGdsMtno());
            if (old != null) {
                log.info("[op:BookItemCallbackReceiver] receive id={} 已处理 略过", resultInfo.getGdsMtno());
                return;
            } else {
                //记账金二序号回执
                messageSender.sendMsg(resultInfo, "ccs-inventory-item-seq-callback-topic");
            }
        }
        bookItemCallbackReceiver.handleBookItem(resultInfo, customsBookDTO);
        bookItemCallbackReceiver.handleGoodsRecords(resultInfo, customsBookDTO);
        try {
            messageSender.sendMsg(resultInfo, "ccs-goods-item-associate-topic");
            log.info("BookItemCallbackReceiver 记账回执 反写备案商品列表 料号:{}", resultInfo.getGdsMtno());
        } catch (Exception e) {
            log.error("BookItemCallbackReceiver 记账回执 反写备案商品列表失败 料号:{} info={}", resultInfo.getGdsMtno(), JSON.toJSONString(resultInfo), e);
        }
    }

    //    @StockVerify(methodParameters = {0,1},changeType = InventoryCalculationTypeEnums.ADD_AVAILABLE,handler = StockCustomsBookItemHandler.class)
    public void handleBookItem(BookItemCallback resultInfo, CustomsBookDTO customsBookDTO) throws RuntimeException {
        CustomsBookItemSubmitDTO submit = buildSubmit(resultInfo, customsBookDTO.getId());
        // 存在重复料号先禁用，等待人工核实
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(customsBookDTO.getId(), submit.getProductId());
        if (!CollectionUtils.isEmpty(customsBookItemDTOList)) {
            if (customsBookItemDTOList.size() >= 2) {
                submit.setEnable(0);
            } else {
                CustomsBookItemDTO customsBookItemDTO = customsBookItemDTOList.get(0);
                if (!customsBookItemDTO.getGoodsSeqNo().equals(submit.getGoodsSeqNo())) {
                    submit.setEnable(0);
                }
            }
        }
        try {
            submit.setSource(ItemTrackLogConfig.ITEM_TYPE_RECEIVE);
            customsBookItemService.upsetCore(submit);
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }

    }

    public void handleGoodsRecords(BookItemCallback resultInfo, CustomsBookDTO customsBookDTO) {
        log.info("记账回执 商品备案创建 resultInfo={} customsBookDTO={}", JSON.toJSONString(resultInfo), JSON.toJSONString(customsBookDTO));
        String productId = resultInfo.getGdsMtno();
        //统一料号版本上线后只有78账册需要生成备案
        List<JdWarehouseDTO> jdWarehouseDTOList = jdWarehouseBaseService.findByBookId(customsBookDTO.getId());
        log.info("账册={} 获取到jd仓库={}", customsBookDTO.getBookNo(), JSON.toJSONString(jdWarehouseDTOList));
        if (!CollectionUtils.isEmpty(jdWarehouseDTOList)) {
            //处理京东备案
            this.handleJdGoodsRecord(resultInfo, customsBookDTO);
        } else {
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProIdAndMappingWay(customsBookDTO.getId(), productId,
                    GoodsRecordMappingWayEnums.WAREHOUSE_UNIFIED.getValue());
            if (Objects.isNull(goodsRecordDTO)) {
                log.info("记账回执 账册={},海关备案料号={} 在商品备案中未查到", customsBookDTO.getBookNo(), resultInfo.getGdsMtno());
                return;
            }
            boolean edit = false;
            if (Objects.isNull(goodsRecordDTO.getDeclarePrice()) || Objects.equals(BigDecimal.ZERO.compareTo(goodsRecordDTO.getDeclarePrice()), 0)) {
                goodsRecordDTO.setDeclarePrice(new BigDecimal(resultInfo.getDclUprcAmt()));
                edit = true;
            }
            if (StringUtil.isBlank(goodsRecordDTO.getDeclareCurrency())) {
                goodsRecordDTO.setDeclareCurrency(resultInfo.getDclCurrcd());
                edit = true;
            }
            if (Boolean.FALSE.equals(edit)) {
                log.info("记账回执 未对商品备案编辑");
                return;
            }
            GoodsRecordSubmit submit = new GoodsRecordSubmit();
            BeanUtils.copyProperties(goodsRecordDTO, submit);
            submit.setAllowEdit(Boolean.TRUE);
            submit.setOpinion(1);
            log.info("记账回执编辑商品备案 recordId={}, submit={}", submit.getId(), JSON.toJSONString(submit));
            goodsRecordService.update(submit, ItemTrackLogConfig.UPDATE_GOODS);
        }
    }


    /**
     * 生成备案关联信息
     * 判断备案是否存在: (变动逻辑同原备案更新)
     * 存在 - 检查记账回执的账册在账册管理所属的口岸，此口岸是否符合已存在备案的【已审核口岸】
     * 若符合则无需对备案新增新口岸，
     * 若不符合则按照此账册在账册管理所属的口岸新增新口岸，全部的备案信息按照新口岸的记账回执内容覆盖
     *
     * @param resultInfo     记账回执
     * @param customsBookDTO 账册
     */
    public void handleJdGoodsRecord(BookItemCallback resultInfo, CustomsBookDTO customsBookDTO) {
        String productId = resultInfo.getGdsMtno();
        Long customsBookId = customsBookDTO.getId();
        List<RecordWarehouseDTO> recordWarehouseDTOList = recordWarehouseBaseService.findByProductIdAndBookId(productId, customsBookId);
        if (!CollectionUtils.isEmpty(recordWarehouseDTOList)) {
            log.info("查询商品备案的统一料号:{} 账册:{} 备案实体仓关系已存在", productId, customsBookId);
            return;
        }
        JdGoodsRecordDTO jdGoodsRecordDTO = jdGoodsRecordService.getRecordSuccessDTOByItemCodeAndCustoms(productId, customsBookDTO.getCustomsAreaCode());
        if (Objects.isNull(jdGoodsRecordDTO)) {
            log.info("商品备案料号={} 未找到成功JD备案商品", productId);
            return;
        }
        List<GoodsRecordDTO> recordDTOList = goodsRecordService.findDescListByProId(productId);
        if (CollectionUtils.isEmpty(recordDTOList)) {
            //不存在，新增备案，备案信息来源记账回执
            try {
                GoodsRecordSubmit submit = buildGoodsSubmit(resultInfo, customsBookDTO, jdGoodsRecordDTO);
                GoodsRecordSubmitDTO submitDTO = ConvertUtil.beanConvert(submit, GoodsRecordSubmitDTO.class);
                log.info("海关记账回执，新增备案数据:{}", JSON.toJSONString(submitDTO));
                submitDTO.setGoodsSource(GoodsRecordEnum.SELF_BUILT.getCode());
                Long recordId = goodsRecordService.upsetCore(submitDTO);
                goodsRecordService.handleRecordCustomsWarehouse(recordId, getGoodsRecordSyncSubmitV2ByBookId(customsBookId), GoodsRecordActionEnum.RECEIVE);
            } catch (ArgsErrorException ex) {
                log.error("海关记账回执，新增备案异常:{}", ex.getErrorMessage(), ex);
            } catch (Exception ex) {
                log.error("海关记账回执，新增备案异常:{}", ex.getMessage(), ex);
            }
        } else {
            GoodsRecordDTO goodsRecordDTO = recordDTOList.get(0);
            if (Objects.isNull(goodsRecordDTO)) {
                log.info("商品备案料号={} 商品备案为空", productId);
                return;
            }
            RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findByRecordIdAndCustomsCode(goodsRecordDTO.getId(), customsBookDTO.getCustomsDistrictCode());
            if (Objects.nonNull(recordCustomsDO) && Objects.equals(recordCustomsDO.getStatus(), GoodsRecordStatusEnum.WAIT_COMMIT.getCode())) {
                log.info("商品备案料号={} 口岸={} 商品备案状态为待提交，无需处理", productId, customsBookDTO.getCustomsAreaCode());
                return;
            }
            goodsRecordService.handleRecordCustomsWarehouse(goodsRecordDTO.getId(), getGoodsRecordSyncSubmitV2ByBookId(customsBookId), GoodsRecordActionEnum.RECEIVE);
        }
    }

    public GoodsRecordSyncSubmitV2 getGoodsRecordSyncSubmitV2ByBookId(Long bookId) {

        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseBaseService.findByCustomsBookId(bookId);
        if (CollectionUtils.isEmpty(entityWarehouseDTOList)) {
            log.info("海关记账回执，新增备案异常，根据账册ID:{},没有找到对应实体仓账册基础数据，记账回执组装的备案信息:{}", bookId);
            return null;
        }
        EntityWarehouseDTO recordWarehouseDO = entityWarehouseDTOList.get(0);
        GoodsRecordSyncSubmitV2 syncSubmitV2 = new GoodsRecordSyncSubmitV2();
        syncSubmitV2.setRecordType(GoodsRecordTypeEnums.NEW.getCode());
        GoodsRecordWarehouseInfoDTO goodsRecordWarehouseInfo = new GoodsRecordWarehouseInfoDTO();
        goodsRecordWarehouseInfo.setCustomsCode(recordWarehouseDO.getCustomsCode());
        List<String> wmsWarehouseList = new ArrayList() {{
            addAll(entityWarehouseDTOList.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).collect(Collectors.toList()));
        }};
        goodsRecordWarehouseInfo.setWmsWarehouseList(wmsWarehouseList);
        syncSubmitV2.setGoodsRecordWarehouseInfo(goodsRecordWarehouseInfo);
        syncSubmitV2.setSource(GoodsRecordSourceEnums.RECEIVE.getCode());
        return syncSubmitV2;
    }


    public void saveLogs(CustomsBookItemSubmit submit, Long customsBookId) {
        UpdateInventoryDTO u = new UpdateInventoryDTO();
        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(submit.getCustomsBookId(), submit.getGoodsSeqNo(), submit.getProductId());
        u.setCustomsBookItemId(customsBookItemDTO.getId());
        u.setBusinessNo(customsBookId + "_" + submit.getProductId() + "_" + submit.getGoodsSeqNo());
        u.setProductId(submit.getProductId());
        u.setChangeType(InventoryChangeTypeEnums.BOOKS_CHECKLIST_RECEIPT);
        u.setDeclareUnitQfy(submit.getInQty());
        u.setCustomsBookId(customsBookId);
        inventoryChangeService.saveLogs(u);
    }

    private CustomsBookItemSubmitDTO buildSubmit(BookItemCallback bwsDt, Long customsBookId) {
        CustomsBookItemSubmitDTO submit = new CustomsBookItemSubmitDTO();
        submit.setGoodsSeqNo(bwsDt.getGdsSeqno());
        submit.setProductId(bwsDt.getGdsMtno());
        submit.setHsCode(bwsDt.getGdecd());
        submit.setGoodsName(bwsDt.getGdsNm());
        submit.setCurrCode(bwsDt.getDclCurrcd());
        submit.setDeclarePrice(new BigDecimal(bwsDt.getDclUprcAmt()));
        submit.setGoodsModel(bwsDt.getGdsSpcfModelDesc());
        submit.setOriginCountry(bwsDt.getNatcd());
        submit.setGoodsUnit(bwsDt.getDclUnitcd());
        submit.setFirstUnit(bwsDt.getLawfUnitcd());
        submit.setSecondUnit(bwsDt.getSecdLawfUnitcd());
        submit.setCustomsBookId(customsBookId);
        submit.setInvtGoodsNo(bwsDt.getInvtGNo());
        submit.setInvtNo(bwsDt.getInvtNo());
        submit.setInQty(Integer.valueOf(bwsDt.getInQty()));
        submit.setInLegalQty(new BigDecimal(bwsDt.getInLawfQty()));
        //账册记账清单回执 增加账册库存和可用库存
        submit.setAccountNum(submit.getInQty());
        submit.setAvailableNum(Integer.valueOf(bwsDt.getInQty()));
        //入库时 将占用、已用库存设置为0
        submit.setOccupiedNum(0);
        submit.setUsedNum(0);
        if (!StringUtils.isEmpty(bwsDt.getInLawfQty()) && !StringUtils.isEmpty(bwsDt.getInQty())) {
            BigDecimal firstUnitAmount = new BigDecimal(bwsDt.getInLawfQty()).divide(new BigDecimal(bwsDt.getInQty()), 4, RoundingMode.HALF_UP);
            submit.setFirstUnitAmount(firstUnitAmount);
        }
        if (!StringUtils.isEmpty(bwsDt.getSecdLawfUnitcd()) && !StringUtils.isEmpty(bwsDt.getInSecdLawfQty()) && !StringUtils.isEmpty(bwsDt.getInQty())) {
            BigDecimal secondUnitAmount = new BigDecimal(bwsDt.getInSecdLawfQty()).divide(new BigDecimal(bwsDt.getInQty()), 4, RoundingMode.HALF_UP);
            submit.setSecondUnitAmount(secondUnitAmount);
        }
//        //可能出现（法定第二单位不为空，但是第二入仓数量为空） 默认给1000
//        if (!StringUtils.isEmpty(bwsDt.getSecdLawfUnitcd()) && StringUtils.isEmpty(bwsDt.getInSecdLawfQty())) {
//            bwsDt.setInSecdLawfQty("1000");
//        }
//        //可能出现（法定第二单位为空，但是第二入仓数量不为空） 去除法二数量
//        if (StringUtils.isEmpty(bwsDt.getSecdLawfUnitcd()) && !StringUtils.isEmpty(bwsDt.getInSecdLawfQty())) {
//            bwsDt.setInSecdLawfQty("");
//        }
        if (!StringUtils.isEmpty(bwsDt.getInSecdLawfQty())) {
            submit.setInSecondLegalQty(new BigDecimal(bwsDt.getInSecdLawfQty()));
        }
        // 法二单位，第二入仓法定数量， 有一个为空 法二单位及数量置空
        if (StringUtil.isEmpty(bwsDt.getSecdLawfUnitcd()) || StringUtils.isEmpty(bwsDt.getInSecdLawfQty())) {
            submit.setSecondUnit(null);
            submit.setSecondUnitAmount(null);
            submit.setInSecondLegalQty(null);
        }
        submit.setInDate(DateTime.parse(bwsDt.getInDate(), DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss")).getMillis());
        submit.setAvgPrice(new BigDecimal(bwsDt.getAvgPrice()));
        submit.setTotalAmt(new BigDecimal(bwsDt.getTotalAmt()));
        submit.setGoodsSource(bwsDt.getCol1());
        return submit;
    }

    private GoodsRecordSubmit buildGoodsSubmit(BookItemCallback bwsDt, CustomsBookDTO customsBookDTO, JdGoodsRecordDTO jdGoodsRecordDTO) {
        GoodsRecordSubmit submit = new GoodsRecordSubmit();
        submit.setSkuId(bwsDt.getGdsMtno());
        submit.setProductId(bwsDt.getGdsMtno());
        submit.setCustomsBookId(customsBookDTO.getId());
        submit.setBarCode(bwsDt.getGdsMtno());
        submit.setGoodsRecordName(bwsDt.getGdsNm());
        submit.setModel(bwsDt.getGdsSpcfModelDesc());
        submit.setBrand("待补充");
        submit.setBrandEn("待补充");
        submit.setDeclarePrice(new BigDecimal(bwsDt.getDclUprcAmt()));
        submit.setDeclareUnit(bwsDt.getDclUnitcd());
        submit.setLength(1);
        submit.setHeight(1);
        submit.setWidth(1);
        submit.setHsCode(bwsDt.getGdecd());
        submit.setComposition("暂无");
        submit.setHgsbys("暂无");
        submit.setOriginCountry(bwsDt.getNatcd());
        submit.setRecordFunction("暂无");
        submit.setRecordUsage("暂无");


        submit.setFirstUnit(bwsDt.getLawfUnitcd());
        BigDecimal firstUnitAmount = new BigDecimal(bwsDt.getInLawfQty()).divide(new BigDecimal(bwsDt.getInQty()), 4, RoundingMode.HALF_UP);
        submit.setFirstUnitAmount(firstUnitAmount);
        BigDecimal secondUnitAmount = BigDecimal.ZERO;
        if (!StringUtils.isEmpty(bwsDt.getSecdLawfUnitcd())) {
            //可能出现（法定第二单位不为空，但是第二入仓数量为空） 默认给1000
            if (StringUtils.isEmpty(bwsDt.getInSecdLawfQty())) {
                bwsDt.setInSecdLawfQty("1000");
            }
            secondUnitAmount = new BigDecimal(bwsDt.getInSecdLawfQty()).divide(new BigDecimal(bwsDt.getInQty()), 4, RoundingMode.HALF_UP);
            submit.setSecondUnit(bwsDt.getSecdLawfUnitcd());
            submit.setSecondUnitAmount(secondUnitAmount);
        }
        //净重毛重
        if ("035".equals(bwsDt.getLawfUnitcd())) {
            submit.setGrossWeight(firstUnitAmount);
            submit.setNetWeight(firstUnitAmount);
        } else if (!StringUtils.isEmpty(bwsDt.getSecdLawfUnitcd()) && "035".equals(bwsDt.getSecdLawfUnitcd())) {
            submit.setGrossWeight(secondUnitAmount);
            submit.setNetWeight(secondUnitAmount);
        }

        // 京东备案特殊处理
        if (Objects.nonNull(jdGoodsRecordDTO)) {
            if (StrUtil.isNotBlank(jdGoodsRecordDTO.getUpc())) {
                submit.setBarCode(jdGoodsRecordDTO.getUpc());
            }
            if (Objects.nonNull(jdGoodsRecordDTO.getJingZhong())) {
                submit.setNetWeight(jdGoodsRecordDTO.getJingZhong().setScale(4, RoundingMode.HALF_UP));
            }
            if (Objects.nonNull(jdGoodsRecordDTO.getMaoZhong())) {
                submit.setGrossWeight(jdGoodsRecordDTO.getMaoZhong().setScale(4, RoundingMode.HALF_UP));
            }
            String shangJiaId = jdGoodsRecordDTO.getShangJiaId();
            if (StrUtil.isNotBlank(shangJiaId)) {
                if (Objects.equals("0", shangJiaId)) {
                    submit.setTenantId("JDZY");
                } else {
                    submit.setTenantId(jdGoodsRecordDTO.getShangJiaId());
                }
            }
        }

        //默认给审核完成
        submit.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
        submit.setOpinion(1);

        submit.setSource(ItemTrackLogConfig.ITEM_TYPE_RECEIVE);
        submit.setCustomsCode(customsBookDTO.getCustomsDistrictCode());
        return submit;
    }
}
