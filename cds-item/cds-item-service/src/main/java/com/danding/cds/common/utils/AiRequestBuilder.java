package com.danding.cds.common.utils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.v2.bean.dto.AiCallRequest;
import com.danding.cds.v2.bean.dto.AiMessage;

import java.util.Collections;

/**
 * AI请求构建工具类
 *
 * <AUTHOR>
 * @since 2025-07-15
 */
public class AiRequestBuilder {

    /**
     * 构建海关HS编码推荐请求
     *
     * @param goodsName 商品名称
     * @param component 成分
     * @return JSON字符串
     */
    public static String buildHsCodeRecommendRequest(String goodsName, String component) {
        // 构建消息内容
        String messageContent = String.format("API调用-海关HS编码推荐```商品名称:%s 成分:%s```",
                goodsName, component);

        // 创建AI请求对象
        AiMessage userMessage = AiMessage.createUserMessage(messageContent);
        AiCallRequest aiRequest = new AiCallRequest()
                .setStream(false)
                .setDetail(false)
                .setMessages(Collections.singletonList(userMessage));

        return JSON.toJSONString(aiRequest);
    }

    /**
     * 构建通用AI请求
     *
     * @param content 消息内容
     * @param stream  是否流式返回
     * @param detail  是否返回详细信息
     * @return JSON字符串
     */
    public static String buildGeneralRequest(String content, boolean stream, boolean detail) {
        // 对内容进行转义处理

        AiMessage userMessage = AiMessage.createUserMessage(content);
        AiCallRequest aiRequest = new AiCallRequest()
                .setStream(stream)
                .setDetail(detail)
                .setMessages(Collections.singletonList(userMessage));

        return JSON.toJSONString(aiRequest);
    }

    /**
     * 构建AI请求对象
     *
     * @param goodsName 商品名称
     * @param component 成分
     * @return AiCallRequest对象
     */
    public static AiCallRequest buildHsCodeRecommendRequestObject(String goodsName, String component) {
        // 构建消息内容
        String messageContent = String.format("API调用-海关HS编码推荐```商品名称:%s 成分:%s```",
                goodsName, component);

        // 创建AI请求对象
        AiMessage userMessage = AiMessage.createUserMessage(messageContent);
        return new AiCallRequest()
                .setStream(false)
                .setDetail(false)
                .setMessages(Collections.singletonList(userMessage));
    }
}
