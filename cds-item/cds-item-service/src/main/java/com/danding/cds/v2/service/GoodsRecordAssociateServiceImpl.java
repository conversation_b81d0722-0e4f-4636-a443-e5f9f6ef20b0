package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dto.RecordItemAssociateInfoDTO;
import com.danding.cds.v2.bean.dto.RecordProductDTO;
import com.danding.cds.v2.bean.dto.RecordWarehouseProductIdDTO;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.service.base.RecordProductBaseService;
import com.danding.cds.v2.service.base.RecordWarehouseProductIdBaseService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: 备案关联业务 + 备案映射
 * @author: 潘本乐（Belep）
 * @create: 2022-03-15 18:08
 **/
@DubboService
@Slf4j
public class GoodsRecordAssociateServiceImpl implements GoodsRecordAssociateService {

    @DubboReference
    private GoodsRecordService goodsRecordService;
    @Autowired
    private RecordProductBaseService recordProductBaseService;
    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;
    @Autowired
    private RecordItemAssociateInfoService recordItemAssociateInfoService;

    @Override
    public String getFinalProductId(String productId, Long goodsRecordId, Long bookId) {
        GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
        //用于兜底 防止没有配置的情况下无法取到最终料号
        goodsRecordDTO.setProductId(productId);
        goodsRecordDTO.setId(goodsRecordId);
        return this.getFinalProductId(goodsRecordDTO, bookId);
    }

    /**
     * 获取最终的料号名称
     * <p>
     * 1. 先获取跨境通关信息中关联的料号
     * 2. 如果1没有获取到，获取当前备案信息中的关联料号
     * 3. 如果2没有获取到，则用备案的料号
     *
     * @param goodsRecordDTO 备案信息
     * @return
     */
    @Override
    public String getFinalProductId(GoodsRecordDTO goodsRecordDTO, Long customsBookId) {
        return this.getFinalProductId(goodsRecordDTO, customsBookId, null, null);
    }

    @Override
    public String getFinalProductId(GoodsRecordDTO goodsRecordDTO, Long customsBookId, String customsProductId, GoodsRecordMappingWayEnums goodsRecordMappingWay) {
        String productId = goodsRecordDTO.getProductId();
        // 商品备案#商品列表 - 海关备案料号
//        List<RecordProductDTO> productDTOList = recordProductBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
//        if (!CollectionUtils.isEmpty(productDTOList)) {
//            if (Objects.nonNull(customsProductId)) {
//                productDTOList = productDTOList.stream().filter(p -> Objects.equals(customsProductId, p.getCustomsRecordProductId())).collect(Collectors.toList());
//            }
//            if (!CollectionUtils.isEmpty(productDTOList)) {
//                RecordProductDTO recordProductDTO = productDTOList.get(0);
//                if (Objects.nonNull(recordProductDTO)) {
//                    goodsRecordDTO.setOriginCountry(recordProductDTO.getOriginCountryCode());
//                    return recordProductDTO.getCustomsRecordProductId();
//                }
//            }
//        }
        // 商品备案#跨境通关信息 - 关联料号
//        String associatedProductId = null;
//        if (!Objects.equals(GoodsRecordMappingWayEnums.CUSTOMS_WAREHOUSE, goodsRecordMappingWay)) {
//            RecordItemAssociateInfoDTO associateInfoDTO = goodsRecordService.getAssociateInfo(goodsRecordDTO.getId());
//            associatedProductId = Optional.ofNullable(associateInfoDTO).map(RecordItemAssociateInfoDTO::getAssociatedProductId).orElse(null);
//        }
//        if (StringUtils.hasText(associatedProductId)) {
//            return associatedProductId;
//        }
        // 商品备案#仓库信息 - 通关料号
        List<RecordWarehouseProductIdDTO> warehouseProductIdList = recordWarehouseProductIdBaseService.findByRecordIdAndCustomsBookId(goodsRecordDTO.getId(), customsBookId);
        if (Objects.nonNull(customsProductId)) {
            List<RecordWarehouseProductIdDTO> warehouseProductIdListFilter = warehouseProductIdList.stream()
                    .filter(p -> Objects.equals(customsProductId, p.getCustomsDeclareProductId())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(warehouseProductIdListFilter) && Objects.equals(GoodsRecordMappingWayEnums.CUSTOMS_WAREHOUSE, goodsRecordMappingWay)) {
                throw new ArgsInvalidException("备案:" + goodsRecordDTO.getProductId() + "关联通关列表中不存在指定的海关备案料号:" + customsProductId);
            }
            if (Objects.equals(GoodsRecordMappingWayEnums.CUSTOMS_WAREHOUSE, goodsRecordMappingWay)) {
                return warehouseProductIdListFilter.get(0).getCustomsDeclareProductId();
            }
        }
        if (!CollectionUtils.isEmpty(warehouseProductIdList)) {
            RecordWarehouseProductIdDTO warehouseProductIdDTO = warehouseProductIdList.get(0);
            if (Objects.nonNull(warehouseProductIdDTO)) {
                return warehouseProductIdDTO.getCustomsDeclareProductId();
            }
        }
        // 商品备案 - 统一料号
        return productId;
    }

    /**
     * 获取最终的料号名称
     * 1.通过统一料号查询备案
     * 2.根据备案的海关备案料号>跨境通关信息>通关料号
     * <p>
     * -----OutOfDate-----
     * 1. 先获取跨境通关信息中关联的料号
     * 2. 如果1没有获取到，获取当前备案信息中的关联料号
     * 3. 如果2没有获取到，则用备案的料号
     *
     * @param customsBookId 账册ID
     * @param productId     料号
     * @return
     */
    @Override
    public String getFinalProductId(Long customsBookId, String productId) {
        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(customsBookId, productId);
        if (Objects.isNull(goodsRecordDTO)) {
            return null;
        }
        return this.getFinalProductId(goodsRecordDTO, customsBookId);
    }


    /**
     * 获取备案 统一料号， 海关备案料号， 通关料号， 跨境通关料号
     *
     * @param goodsRecordDTO
     * @param customsBookId
     * @return
     */
    @Override
    public List<String> getProductIdList(GoodsRecordDTO goodsRecordDTO, Long customsBookId) {
        List<String> productIdList = new ArrayList<>();
        if (Objects.isNull(goodsRecordDTO)) {
            return productIdList;
        }
        productIdList.add(goodsRecordDTO.getProductId());
        List<RecordProductDTO> productDTOList = recordProductBaseService.findByRecordId(goodsRecordDTO.getId());
        if (!CollectionUtils.isEmpty(productDTOList)) {
            List<String> customsRecordProductIdList = productDTOList.stream().map(RecordProductDTO::getCustomsRecordProductId).collect(Collectors.toList());
            productIdList.addAll(customsRecordProductIdList);
        }
        RecordItemAssociateInfoDTO associateInfoDTO = goodsRecordService.getAssociateInfo(goodsRecordDTO.getId());
        String associatedProductId = Optional.ofNullable(associateInfoDTO).map(RecordItemAssociateInfoDTO::getAssociatedProductId).orElse(null);
        if (StringUtils.hasText(associatedProductId)) {
            productIdList.add(associatedProductId);
        }
        List<RecordWarehouseProductIdDTO> warehouseProductIdList = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordDTO.getId());
        if (!CollectionUtils.isEmpty(warehouseProductIdList)) {
            List<String> customsDeclareProductIdList = warehouseProductIdList.stream().map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId).collect(Collectors.toList());
            productIdList.addAll(customsDeclareProductIdList);
        }
        return productIdList;
    }

    /**
     * 根据指定映射方式 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     料号
     * @param mappingWay    映射方式
     * @return
     */
    @Override
    public GoodsRecordDTO findByBookIdAndProIdAndMappingWay(Long customsBookId, String productId, String mappingWay) {
        GoodsRecordMappingWayEnums mappingWayEnum = GoodsRecordMappingWayEnums.getEnums(mappingWay);
        GoodsRecordDTO goodsRecordDTO = null;
        switch (mappingWayEnum) {
            case CUSTOMS_WAREHOUSE: //海关备案料号 > 通关料号
                //海关备案料号映射
                goodsRecordDTO = getByCustomsItemProId(customsBookId, productId);
                if (Objects.nonNull(goodsRecordDTO)) break;
                //通关料号映射
                goodsRecordDTO = getByWarehouseProId(customsBookId, productId);
                break;
            case WAREHOUSE_UNIFIED:
            case DEFAULT:
                //通关料号
                goodsRecordDTO = getByWarehouseProId(customsBookId, productId);
                if (Objects.nonNull(goodsRecordDTO)) break;
                //统一料号
                goodsRecordDTO = getByUnifiedProId(customsBookId, productId);
                break;
            case WAREHOUSE:
                //通关料号
                goodsRecordDTO = getByWarehouseProId(customsBookId, productId);
                if (Objects.nonNull(goodsRecordDTO)) break;
        }
        return goodsRecordDTO;
    }

    @Override
    public List<GoodsRecordDTO> findAllByBookIdAndProId(Long bookId, String productId) {
        if (Objects.isNull(bookId) || StringUtils.isEmpty(productId)) {
            return new ArrayList<>();
        }
        Set<Long> recordIdSet = new HashSet<>();
        //海关备案料号
        List<Long> customsItemRecordId = getAllRecordIdByCustomsItemProId(bookId, productId);
        if (CollUtil.isNotEmpty(customsItemRecordId)) {
            recordIdSet.addAll(customsItemRecordId);
        }
        //通关料号
        List<Long> warehouseItemRecordId = getAllRecordIdByWarehouseProId(bookId, productId);
        if (CollUtil.isNotEmpty(warehouseItemRecordId)) {
            recordIdSet.addAll(warehouseItemRecordId);
        }
        //跨境通关信息
        List<Long> associatedItemRecordId = getAllRecordIdByAssociatedItemProId(bookId, productId);
        if (CollUtil.isNotEmpty(associatedItemRecordId)) {
            recordIdSet.addAll(associatedItemRecordId);
        }
        //统一料号
        GoodsRecordDTO goodsRecordDTO = getByUnifiedProId(bookId, productId);
        if (goodsRecordDTO != null) {
            recordIdSet.add(goodsRecordDTO.getId());
            if (recordIdSet.size() == 1) {
                return Collections.singletonList(goodsRecordDTO);
            }
        }
        if (CollUtil.isNotEmpty(recordIdSet)) {
            List<Long> recordIdList = new ArrayList<>(recordIdSet);
            return goodsRecordService.findById(recordIdList);
        }
        return new ArrayList<>();
    }

    /**
     * 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     统一料号
     * @return
     */
    private GoodsRecordDTO getByUnifiedProId(Long customsBookId, String productId) {
        return goodsRecordService.findByBookIdAndProId(customsBookId, productId);
    }

    /**
     * 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     通关料号
     * @return
     */
    private GoodsRecordDTO getByWarehouseProId(Long customsBookId, String productId) {
        GoodsRecordDTO goodsRecordDTO = null;
        List<RecordWarehouseProductIdDTO> warehouseProductIdList = recordWarehouseProductIdBaseService.findByCustomsDeclareProductIdAndCustomsBook(productId, customsBookId);
        if (CollUtil.isNotEmpty(warehouseProductIdList)) {
            RecordWarehouseProductIdDTO recordWarehouseProductIdDTO = warehouseProductIdList.stream()
                    .max(Comparator.comparing(RecordWarehouseProductIdDTO::getRecordId)).orElse(null);
            if (Objects.nonNull(recordWarehouseProductIdDTO)) {
                Long recordId = recordWarehouseProductIdDTO.getRecordId();
                goodsRecordDTO = goodsRecordService.findById(recordId);
                log.info("findByBookIdAndProIdAndMappingWay - productId={}, bookId={} 找到通关料号存在, 备案id={}", productId, customsBookId, recordId);
                return goodsRecordDTO;
            }
        }
        return goodsRecordDTO;
    }

    /**
     * 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     海关备案料号
     * @return
     */
    private GoodsRecordDTO getByCustomsItemProId(Long customsBookId, String productId) {
        GoodsRecordDTO goodsRecordDTO = null;
        List<RecordProductDTO> recordProductDTOS = recordProductBaseService.findByCustomsRecordProductIdAndBookId(productId, customsBookId);
        if (CollUtil.isNotEmpty(recordProductDTOS)) {
            RecordProductDTO recordProductDTO = recordProductDTOS.get(0);
            Long recordId = recordProductDTO.getRecordId();
            goodsRecordDTO = goodsRecordService.findById(recordId);
            log.info("findByBookIdAndProIdAndMappingWay - productId={}, bookId={} 海关备案料号存在, 备案id={}", productId, customsBookId, recordId);
            return goodsRecordDTO;
        }
        return goodsRecordDTO;
    }


    private List<Long> getAllRecordIdByWarehouseProId(Long customsBookId, String productId) {
        List<RecordWarehouseProductIdDTO> warehouseProductIdList = recordWarehouseProductIdBaseService.findByCustomsDeclareProductIdAndCustomsBook(productId, customsBookId);
        if (CollUtil.isNotEmpty(warehouseProductIdList)) {
            Set<Long> recordId = warehouseProductIdList.stream().map(RecordWarehouseProductIdDTO::getRecordId).collect(Collectors.toSet());
            log.info("findByBookIdAndProIdAndMappingWay - productId={}, bookId={} 找到通关料号存在, 备案id={}", productId, customsBookId, recordId);
            return new ArrayList<>(recordId);
        }
        return new ArrayList<>();
    }

    private List<Long> getAllRecordIdByCustomsItemProId(Long customsBookId, String productId) {
        List<RecordProductDTO> recordProductDTOS = recordProductBaseService.findByCustomsRecordProductIdAndBookId(productId, customsBookId);
        if (CollUtil.isNotEmpty(recordProductDTOS)) {
            Set<Long> recordId = recordProductDTOS.stream().map(RecordProductDTO::getRecordId).collect(Collectors.toSet());
            log.info("findByBookIdAndProIdAndMappingWay - productId={}, bookId={} 海关备案料号存在, 备案id={}", productId, customsBookId, recordId);
            return new ArrayList<>(recordId);
        }
        return new ArrayList<>();
    }

    /**
     * 获取商品备案
     *
     * @param customsBookId 账册id
     * @param productId     跨境通关料号
     * @return
     */
    private List<Long> getAllRecordIdByAssociatedItemProId(Long customsBookId, String productId) {
        List<RecordItemAssociateInfoDTO> recordItemAssociateInfoDTOS = recordItemAssociateInfoService.findByBookIdAndProdId(customsBookId, productId);
        if (CollUtil.isNotEmpty(recordItemAssociateInfoDTOS)) {
            Set<Long> recordId = recordItemAssociateInfoDTOS.stream().map(RecordItemAssociateInfoDTO::getGoodsRecordId).collect(Collectors.toSet());
            log.info("findByBookIdAndProIdAndMappingWay - productId={}, bookId={} 海关备案料号存在, 备案id={}", productId, customsBookId, recordId);
            return new ArrayList<>(recordId);
        }
        return new ArrayList<>();
    }
}
