package com.danding.cds.item.es;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.declare.sdk.utils.DateUtils;
import com.danding.cds.es.search.GoodsRecordEsBuilder;
import com.danding.cds.item.api.dto.GoodsRecordBusinessCountDetail;
import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.api.dto.GoodsRecordStatusEnum;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.entity.es.GoodsRecordEsDO;
import com.danding.cds.item.entity.es.RecordCustomsEsDO;
import com.danding.cds.item.entity.es.RecordProductEsDO;
import com.danding.cds.item.entity.es.RecordWarehouseEsDO;
import com.danding.cds.v2.bean.dto.*;
import com.danding.common.es.annotations.EsDao;
import com.danding.common.es.dao.AbstractEsDao;
import com.danding.core.tenant.SimpleTenantHelper;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.lucene.search.join.ScoreMode;
import org.assertj.core.util.Lists;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.support.WriteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商品备案
 * @date 2022/6/2
 */
@EsDao
@Slf4j
@Repository
public class RecordEsDao extends AbstractEsDao<GoodsRecordEsDO> {

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    public Page<GoodsRecordEsDO> paging(GoodsRecordSearchCondition search) {
        log.warn("ES备案查询 ");
        BoolQueryBuilder boolQueryBuilder = GoodsRecordEsBuilder.getBoolQueryBuilder(search);

        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(search.getCurrentPage() - 1, search.getPageSize())).build();
        log.warn("ESGoodsRecord - {}", JSONUtil.toJsonStr(searchQuery.getQuery().toString().replaceAll("\n\t", "")));
        Page<GoodsRecordEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, GoodsRecordEsDO.class);
        log.warn("ES备案查询 - {}", JSONUtil.toJsonStr(page));
        return page;
    }

    /**
     * 同步es更新
     *
     * @param goodsRecordId
     */
    public void syncGoodsRecordToEs(Long goodsRecordId) {
        GoodsRecordEsDO goodsRecordEsDO = this.buildGoodsRecordEsDO(goodsRecordId);
        IndexRequest indexRequest = new IndexRequest("ccs_goods_record", "goods_record");
        indexRequest.source(JSON.toJSONString(goodsRecordEsDO), XContentType.JSON);
        indexRequest.setRefreshPolicy(WriteRequest.RefreshPolicy.IMMEDIATE);
        try {
            restHighLevelClient.index(indexRequest, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("新增es文档异常:{}", e.getMessage(), e);
        }
    }

    private GoodsRecordEsDO buildGoodsRecordEsDO(Long goodsRecordId) {
        GoodsRecordEsDTO goodsRecordEsDTO = goodsRecordService.buildGoodsRecordEsDTO(goodsRecordId);
        GoodsRecordEsDO goodsRecordEsDO = ConvertUtil.beanConvert(goodsRecordEsDTO, GoodsRecordEsDO.class);
        if (!CollectionUtils.isEmpty(goodsRecordEsDTO.getEsRecordCustomsDTO())) {
            List<RecordCustomsEsDO> recordCustomsEsDOS = new ArrayList<>();
            for (RecordCustomsESDTO recordCustomsEsDO : goodsRecordEsDTO.getEsRecordCustomsDTO()) {
                RecordCustomsEsDO esDO = new RecordCustomsEsDO();
                BeanUtil.copyProperties(recordCustomsEsDO, esDO);
                if (recordCustomsEsDO.getDeleted()) {
                    esDO.setDeleted(1);
                } else {
                    esDO.setDeleted(0);
                }
                esDO.setSubmitDateTime(DateUtils.dateToTimeMillis(recordCustomsEsDO.getSubmitTime()));
                recordCustomsEsDOS.add(esDO);
            }
            goodsRecordEsDO.setEsRecordCustomsDO(recordCustomsEsDOS);
        }
        if (!CollectionUtils.isEmpty(goodsRecordEsDTO.getEsRecordProducts())) {
            List<RecordProductEsDO> recordProductEsDOList = ConvertUtil.listConvert(goodsRecordEsDTO.getEsRecordProducts(), RecordProductEsDO.class);
            goodsRecordEsDO.setEsRecordProducts(recordProductEsDOList);
        }
        List<RecordWarehouseESDTO> esRecordWarehouseDTOS = goodsRecordEsDTO.getEsRecordWarehouseDTOS();
        if (!CollectionUtils.isEmpty(esRecordWarehouseDTOS)) {
            List<RecordWarehouseEsDO> recordWarehouseEsDOList = esRecordWarehouseDTOS.stream().map(recordWarehouseESDTO -> {
                RecordWarehouseEsDO recordWarehouseEsDO = ConvertUtil.beanConvert(esRecordWarehouseDTOS, RecordWarehouseEsDO.class);
                if (recordWarehouseESDTO.getDeleted()) {
                    recordWarehouseEsDO.setDeleted(1);
                } else {
                    recordWarehouseEsDO.setDeleted(0);
                }
                return recordWarehouseEsDO;
            }).collect(Collectors.toList());
            goodsRecordEsDO.setEsRecordWarehouseDOS(recordWarehouseEsDOList);
        }
        goodsRecordEsDO.setId(SimpleTenantHelper.getTenantIdStr() + "_" + goodsRecordEsDTO.getId());
        log.info("buildGoodsRecordEsDO:{}", JSON.toJSONString(goodsRecordEsDO));
        return goodsRecordEsDO;
    }

    /**
     * 状态统计
     *
     * @param condition
     * @param accountBookList
     * @return
     */
    public GoodsRecordStatusCount countGoodsRecord(GoodsRecordSearchCondition condition, List<Long> accountBookList) {
        condition.setRoleAccountBookIdList(accountBookList);
        GoodsRecordStatusCount count = new GoodsRecordStatusCount();

        for (int i = 1; i < 4; i++) {
            condition.setRecordStatus(i);
            condition.setCurrentPage(1);
            condition.setPageSize(20);
            BoolQueryBuilder boolQueryBuilder = GoodsRecordEsBuilder.getBoolQueryBuilder(condition);
            SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(condition.getCurrentPage() - 1, condition.getPageSize())).build();
            Page<GoodsRecordEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, GoodsRecordEsDO.class);
            if (Objects.equals(condition.getRecordStatus(), 1)) {
                count.setWaitNewCount((int) page.getTotalElements());
            }
            if (Objects.equals(condition.getRecordStatus(), 2)) {
                count.setWaitUpdateCount((int) page.getTotalElements());
            }
            if (Objects.equals(condition.getRecordStatus(), 3)) {
                count.setOverTimeCount((int) page.getTotalElements());
            }
        }

        return count;
    }


    /**
     * 统计备案数量
     *
     * @return
     */
    public GoodsRecordBusinessCount recordBusinessCount(String warehouseCode) {
        GoodsRecordSearchCondition condition = new GoodsRecordSearchCondition();
        GoodsRecordStatusCount count = new GoodsRecordStatusCount();
        for (int i = 1; i < 4; i++) {
            condition.setRecordStatus(i);
            condition.setCurrentPage(1);
            condition.setPageSize(20);
            if (StringUtils.isNotBlank(warehouseCode) && !"all".equalsIgnoreCase(warehouseCode)) {
                condition.setWarehouseSn(warehouseCode);
            }
            BoolQueryBuilder boolQueryBuilder = GoodsRecordEsBuilder.getBoolQueryBuilder(condition);
            SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(condition.getCurrentPage() - 1, condition.getPageSize())).build();
            Page<GoodsRecordEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, GoodsRecordEsDO.class);
            if (Objects.equals(condition.getRecordStatus(), 1)) {
                count.setWaitNewCount((int) page.getTotalElements());
            }
            if (Objects.equals(condition.getRecordStatus(), 2)) {
                count.setWaitUpdateCount((int) page.getTotalElements());
            }
            if (Objects.equals(condition.getRecordStatus(), 3)) {
                count.setOverTimeCount((int) page.getTotalElements());
            }
        }
        GoodsRecordBusinessCount businessCount = new GoodsRecordBusinessCount();
        businessCount.setPendingCount(count.getWaitNewCount() + count.getWaitUpdateCount());
        businessCount.setOverTimeCount(count.getOverTimeCount());
        return businessCount;
    }

    public Map<String, GoodsRecordBusinessCountDetail> getRecordBusinessCountDetailMap(List<String> handlerList, Date startTime, Date endTime) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        //租户id
        boolQueryBuilder.filter(QueryBuilders.termsQuery("updateBy", handlerList));
        //创建时间
        if (!ObjectUtils.isEmpty(startTime) && !ObjectUtils.isEmpty(endTime)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(startTime.getTime()).to(endTime.getTime()));
        }


        //聚合检索条件租户id、担保企业id、总税金
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("updateBy").field("updateBy").size(Integer.MAX_VALUE)
                .subAggregation(AggregationBuilders.terms("recordStatus").field("recordStatus")
                        .subAggregation(AggregationBuilders.count("count").field("recordStatus")));

        //设置搜索语句
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .build();
        //开始搜索
        log.info("开始搜索：Aggregations = {}, Query = {}", nativeSearchQuery.getAggregations(), nativeSearchQuery.getQuery());
        Map<String, GoodsRecordBusinessCountDetail> queryResult = (Map<String, GoodsRecordBusinessCountDetail>) elasticsearchTemplate.query(nativeSearchQuery, (ResultsExtractor<Object>) response -> {
            //返回值
            Map<String, GoodsRecordBusinessCountDetail> resVoMap = new HashMap<>();
            Terms handler = response.getAggregations().get("updateBy");
            //按租户id进行迭代遍历（第一层）
            Iterator<? extends Terms.Bucket> iterator = handler.getBuckets().iterator();
            while (iterator.hasNext()) {
                Terms.Bucket next = iterator.next();
                GoodsRecordBusinessCountDetail detail = new GoodsRecordBusinessCountDetail();
                detail.setUserId(next.getKey().toString());
                Terms recordStatus = next.getAggregations().get("recordStatus");
                //按担保企业id进行迭代遍历（第二层）
                List<? extends Terms.Bucket> recordStatusBucket = recordStatus.getBuckets();
                Iterator<? extends Terms.Bucket> iterator1 = recordStatusBucket.iterator();
                Map<String, Integer> statusCountMap = new HashMap<>();
                while (iterator1.hasNext()) {
                    //组装数据resVO
                    Terms.Bucket next1 = iterator1.next();
                    statusCountMap.put(next1.getKeyAsString(), next1.getAggregations().get("count"));
                }
                log.info("response extractor statusCountMap={}", JSONUtils.toJSONString(statusCountMap));
                //审核状态(0:全部/1:待审核新品/2:待审核更新/3:待审核超时/4:备案完成/5:备案驳回)

                // 总提交审核数量 - 待审核新品+待审核更新+审核通过+审核驳回+时间段，无法统计到人，所有用户数值一致
                detail.setAllSubmitAudit(statusCountMap.get(1) + statusCountMap.get(2) + statusCountMap.get(4) + statusCountMap.get(5));
                // 审核通过 - 备案完成+时间段，用户操作审核通过的次数
                detail.setAuditPass(statusCountMap.get(4));
                // 审核驳回 - 审核驳回+时间段，用户操作审核驳回的次数
                detail.setAuditPass(statusCountMap.get(5));
                // 总未审核 - 待审核新品+待审核更新+时间段，当前无法统计到人，所有用户数值一致
                detail.setAuditPass(statusCountMap.get(1) + statusCountMap.get(2));

                resVoMap.put(detail.getUserId(), detail);
            }
            return resVoMap;
        });
        log.info("搜索完成：queryResult.size = {}, queryResult.Content = {}", queryResult.size(), JSONUtils.toJSONString(queryResult));
        return queryResult;
    }


    private BoolQueryBuilder getBoolQueryBuilder(GoodsRecordSearchCondition search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long tenantryId = SimpleTenantHelper.getTenantId();
        //saas租户
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        //用户
        if (Objects.nonNull(search.getTenantId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantId", search.getTenantId()));
        }
        //来源
        if (Objects.nonNull(search.getSource())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("goodsSource", search.getSource()));
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        boolQueryBuilder.filter(QueryBuilders.boolQuery().mustNot(QueryBuilders.matchQuery("recordStatus", GoodsRecordStatusEnum.WAIT_COMMIT.getCode())));
        //状态查询与口岸查询
        statusAndCustomsCode(search, boolQueryBuilder);
        //备案名称
        if (Objects.nonNull(search.getGoodsRecordName())) {
            boolQueryBuilder.filter(QueryBuilders.matchQuery("goodsRecordName", search.getGoodsRecordName()));
//            List<Long> ids = goodsRecordService.findByName(search.getGoodsRecordName());
//            if (Objects.nonNull(ids)) {
//                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
//                outOrderNoBuilder.should(QueryBuilders.termsQuery("id", ids.stream().distinct().collect(Collectors.toList())));
//                boolQueryBuilder.filter(outOrderNoBuilder);
//            }
        }
        if (Objects.nonNull(search.getGoodsRecordTag())) {
            Integer goodsRecordTag = search.getGoodsRecordTag();
            QueryBuilder orderQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("goodsRecordTagList", goodsRecordTag));
            boolQueryBuilder.must(orderQuery);
        }
//        //账册
//        if (Objects.nonNull(search.getCustomsBookId())) {
//            boolQueryBuilder.filter(QueryBuilders.termQuery("customsBookId", search.getCustomsBookId()));
//        }
        //搜索下拉类型
        if (Objects.nonNull(search.getSearchKey())) {
            List<String> searchType = Lists.newArrayList(search.getSearchKey().split(","));
            List<String> list = searchType.stream().distinct().collect(Collectors.toList());
            ;
            if ("1".equals(search.getSearchType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("skuId", list));
                boolQueryBuilder.filter(outOrderNoBuilder);
            } else if ("2".equals(search.getSearchType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("barCode", list));
                boolQueryBuilder.filter(outOrderNoBuilder);
            }
        }
        //启用状态
        if (Objects.nonNull(search.getEnable())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("enable", search.getEnable()));
        }
        //货品id
        if (Objects.nonNull(search.getGoodsCode())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            List<String> goodsCode = Lists.newArrayList(search.getGoodsCode().split(","));
            outOrderNoBuilder.should(QueryBuilders.termsQuery("goodsCode", goodsCode.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(outOrderNoBuilder);
        }
        //创建时间
        Long customsPassFrom = LongUtil.getFrom(search.getRecordTimeFrom(), search.getRecordTimeTo());
        Long customsPassTo = LongUtil.getEnd(search.getRecordTimeFrom(), search.getRecordTimeTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(customsPassFrom).to(customsPassTo));

        }
        //创建时间
        Long finishedTimeFrom = LongUtil.getFrom(search.getFinishedTimeFrom(), search.getFinishedTimeTo());
        Long finishedTimeTo = LongUtil.getEnd(search.getFinishedTimeFrom(), search.getFinishedTimeTo());
        if (!LongUtil.isNone(finishedTimeFrom) && !LongUtil.isNone(finishedTimeTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("recordFinishTime").from(finishedTimeFrom).to(finishedTimeTo));
        }
        //海关备案料号
        if (Objects.nonNull(search.getProductId())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            search.setProductIdList(Arrays.asList(search.getProductId().split(",")));
            outOrderNoBuilder.should(QueryBuilders.termsQuery("productId", search.getProductIdList().stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(outOrderNoBuilder);

        }
        //外部料号
        if (Objects.nonNull(search.getExternalProductId())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            List<String> externalProductIdList = Lists.newArrayList(search.getExternalProductId().split(","));
            List<String> list = externalProductIdList.stream().distinct().collect(Collectors.toList());
            outOrderNoBuilder.should(QueryBuilders.termsQuery("externalProductId", list));
            boolQueryBuilder.filter(outOrderNoBuilder);

        }
        //通关料号
        if (!CollectionUtils.isEmpty(search.getWarehouseExternalProductIdList())) {
            List<String> list = search.getWarehouseExternalProductIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder declareProductQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordWarehouseDOS.customsDeclareProductId", list)),
                    ScoreMode.None);
            boolQueryBuilder.must(declareProductQuery);
        }
        //海关通关料号
        if (!CollectionUtils.isEmpty(search.getCustomsRecordProductIdList())) {
            List<String> list = search.getCustomsRecordProductIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordProducts",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordProducts.customsRecordProductId", list)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        //口岸
        if (Objects.nonNull(search.getCustomDistrict())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict())),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        //CCS实体仓编码
        if (Objects.nonNull(search.getWarehouseSn())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.matchQuery("esRecordWarehouseDOS.wmsWarehouseCode", search.getWarehouseSn())),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
            if (Objects.nonNull(search.getCustomsBookId())) {
                QueryBuilder bookQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordWarehouseDOS.customsBookId", search.getCustomsBookId())),
                        ScoreMode.None);
                boolQueryBuilder.must(bookQuery);
            }
        }
        if (Objects.nonNull(search.getCustomsBookId())) {
            List<Long> bookIds = new ArrayList<>(Arrays.asList(search.getCustomsBookId()));
            search.setRoleAccountBookIdList(bookIds);
        }
        if (Objects.nonNull(search.getRoleAccountBookIdList())) {
            List<Long> roleAccountBookIdList = search.getRoleAccountBookIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordWarehouseDOS.customsBookId", roleAccountBookIdList)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        if (Objects.nonNull(search.getAuditWay())) {
            boolQueryBuilder.must(QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("esRecordCustomsDO.auditWay", search.getAuditWay())),
                    ScoreMode.None));
        }
        // 外部货品id
        if (StringUtil.isNotBlank(search.getExternalGoodsIdList())) {
            List<String> externalGoodsIdList = Splitter.on(",").splitToList(search.getExternalGoodsIdList());
            boolQueryBuilder.filter(QueryBuilders.termsQuery("externalGoodsId", externalGoodsIdList.stream().distinct().collect(Collectors.toList())));
        }


        return boolQueryBuilder;
    }


    private void statusAndCustomsCode(GoodsRecordSearchCondition search, BoolQueryBuilder boolQueryBuilder) {

        if (Objects.nonNull(search.getRecordStatus()) && !Objects.equals(search.getRecordStatus(), 0)) {
            if (Objects.equals(search.getRecordStatus(), 1) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict()))
                                .must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 1)).must(QueryBuilders.termQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 1) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 1)).must(QueryBuilders.termQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 2) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 2)).must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 1))
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 2) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 2)).must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -2);
            // TODO: 2022/6/10 查询大于48小时的数据
            if (Objects.equals(search.getRecordStatus(), 3) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict()))
                                .filter(QueryBuilders.rangeQuery("esRecordCustomsDO.submitTime").lt(calendar.getTimeInMillis())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 3) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .filter(QueryBuilders.rangeQuery("esRecordCustomsDO.submitTime").lt(calendar.getTimeInMillis())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 4) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 2)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery);
            }
            if (Objects.equals(search.getRecordStatus(), 5) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 4)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery);
            }
            if (Objects.equals(search.getRecordStatus(), 4) && Objects.isNull(search.getCustomDistrict())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 2));
            }
            if (Objects.equals(search.getRecordStatus(), 5) && Objects.isNull(search.getCustomDistrict())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 4));
            }
        }
    }

    @Test
    public void tes() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -2);
        System.out.println(calendar.getTimeInMillis());
    }


    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    public GoodsRecordEsDO getById(Long id) {
        return getById(String.valueOf(id));
    }


    public List<GoodsRecordEsDTO> findEsWaitExamineRecord() {
        List<GoodsRecordEsDTO> result = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).withIndices("ccs_goods_record").build();
        long scrollTimeInMillis = 10 * 1000;
        ScrolledPage<GoodsRecordEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, GoodsRecordEsDO.class);
        while (scrolledPage.hasContent()) {
            List<GoodsRecordEsDO> content = scrolledPage.getContent();
            result.addAll(content.stream().map(this::buildDTO).collect(Collectors.toList()));
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, GoodsRecordEsDO.class);
        }
        return result;
    }

    private GoodsRecordEsDTO buildDTO(GoodsRecordEsDO goodsRecordEsDO) {
        if (Objects.isNull(goodsRecordEsDO)) {
            return null;
        }
        GoodsRecordEsDTO goodsRecordEsDTO = new GoodsRecordEsDTO();
        BeanUtils.copyProperties(goodsRecordEsDO, goodsRecordEsDTO, "id");
        String recordEsDOId = goodsRecordEsDO.getId();
        if (recordEsDOId.contains("_")) {
            String[] split = recordEsDOId.split("_");
            String realId = split[1];
            goodsRecordEsDTO.setId(Long.valueOf(realId));
        } else {
            goodsRecordEsDTO.setId(Long.valueOf(recordEsDOId));
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordCustomsDO())) {
            List<RecordCustomsEsDO> esRecordCustomsDO = goodsRecordEsDO.getEsRecordCustomsDO();
            List<RecordCustomsESDTO> recordCustomsESDTOList = esRecordCustomsDO.stream().map(i -> {
                RecordCustomsESDTO recordCustomsESDTO = new RecordCustomsESDTO();
                BeanUtil.copyProperties(i, recordCustomsESDTO);
                if (StrUtil.isNotBlank(i.getId())) {
                    recordCustomsESDTO.setId(Long.valueOf(i.getId()));
                }
                return recordCustomsESDTO;
            }).collect(Collectors.toList());
            goodsRecordEsDTO.setEsRecordCustomsDTO(recordCustomsESDTOList);
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordWarehouseDOS())) {
            goodsRecordEsDTO.setEsRecordWarehouseDTOS(ConvertUtil.listConvert(goodsRecordEsDO.getEsRecordWarehouseDOS(), RecordWarehouseESDTO.class));
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordProducts())) {
            goodsRecordEsDTO.setEsRecordProducts(ConvertUtil.listConvert(goodsRecordEsDO.getEsRecordProducts(), RecordProductESDTO.class));
        }
        return goodsRecordEsDTO;
    }

    public void deleteById(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("id", SimpleTenantHelper.getTenantIdStr() + "_" + id));
        DeleteByQueryRequest request = new DeleteByQueryRequest("ccs_goods_record");
        request.setQuery(boolQueryBuilder);
        request.setRefresh(true);
        try {
            restHighLevelClient.deleteByQuery(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("recordEsDao deleteById id={} e={}", id, e.getMessage(), e);
//            throw new RuntimeException(e);
        }
    }
}
