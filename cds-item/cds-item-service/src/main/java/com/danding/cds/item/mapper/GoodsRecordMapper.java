package com.danding.cds.item.mapper;

import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.entity.GoodsRecordDO;
import com.danding.cds.v2.bean.dto.GoodWarehouseDTO;
import com.danding.cds.v2.bean.dto.GoodsRecordStatusCount;
import com.danding.cds.v2.bean.dto.ItemGoodsRecordDTO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.*;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

public interface GoodsRecordMapper extends Mapper<GoodsRecordDO>, InsertListMapper<GoodsRecordDO>, BatchUpdateMapper<GoodsRecordDO>, AggregationPlusMapper<GoodsRecordDO> {

    @Results(id = "GoodsRecordMap", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "customsBookId", column = "customs_book_id"),
            @Result(property = "skuId", column = "sku_id"),
            @Result(property = "productId", column = "product_id"),
            @Result(property = "externalProductId", column = "external_product_id"),
            @Result(property = "barCode", column = "bar_code"),
            @Result(property = "goodsRecordName", column = "goods_record_name"),
            @Result(property = "declarePrice", column = "declare_price"),
            @Result(property = "declareUnit", column = "declare_unit"),
            @Result(property = "declareCurrency", column = "declare_currency"),
            @Result(property = "netWeight", column = "net_weight"),
            @Result(property = "skuPicture", column = "sku_picture"),
            @Result(property = "length", column = "length"),
            @Result(property = "width", column = "width"),
            @Result(property = "height", column = "height"),
            @Result(property = "grossWeight", column = "gross_weight"),
            @Result(property = "model", column = "model"),
            @Result(property = "brand", column = "brand"),
            @Result(property = "brandEn", column = "brand_en"),
            @Result(property = "lesseeNo", column = "lessee_no"),
            @Result(property = "warehouseId", column = "warehouse_id"),
            @Result(property = "tenantId", column = "tenant_id"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "recordStatus", column = "record_status"),
            @Result(property = "countryRecordNo", column = "country_record_no"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "hsCode", column = "hs_code"),
            @Result(property = "vatRate", column = "vat_rate"),
            @Result(property = "taxRate", column = "tax_rate"),
            @Result(property = "originCountry", column = "origin_country"),
            @Result(property = "composition", column = "composition"),
            @Result(property = "hgsbys", column = "hgsbys"),
            @Result(property = "recordFunction", column = "record_function"),
            @Result(property = "recordUsage", column = "record_usage"),
            @Result(property = "firstUnit", column = "first_unit"),
            @Result(property = "firstUnitAmount", column = "first_unit_amount"),
            @Result(property = "secondUnit", column = "second_unit"),
            @Result(property = "secondUnitAmount", column = "second_unit_amount"),
            @Result(property = "recordFinishTime", column = "record_finish_time"),
            @Result(property = "goodsRegNo", column = "goods_reg_no"),
            @Result(property = "importEntryDeclareNo", column = "import_entry_declare_no"),
            @Result(property = "ioGoodsSerialNo", column = "io_goods_serial_no"),
            @Result(property = "ciqOriginCountry", column = "ciq_origin_country"),
            @Result(property = "customs", column = "customs"),
            @Result(property = "goodsCode", column = "goods_code"),
            @Result(property = "goodsSource", column = "goods_source"),
            @Result(property = "productCompanyName", column = "product_company_name"),
            @Result(property = "productCompanyRegisterNumber", column = "product_company_register_number"),
            @Result(property = "productCompanyAddress", column = "product_company_address"),
            @Result(property = "productLink", column = "product_link"),
            @Result(property = "attachmentName", column = "attachment_name"),
            @Result(property = "attachmentUrl", column = "attachment_url"),
            @Result(property = "frontImage", column = "front_image"),
            @Result(property = "sideImage", column = "side_image"),
            @Result(property = "backImage", column = "back_image"),
            @Result(property = "labelImage", column = "label_image"),
            @Result(property = "enable", column = "enable"),
            @Result(property = "createBy", column = "create_by"),
            @Result(property = "updateBy", column = "update_by"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "deleted", column = "deleted"),
            @Result(property = "recordCustomsCode", column = "recordCustomsCode"),
            @Result(property = "recordCustomsName", column = "recordCustomsName"),
            @Result(property = "customsStatus", column = "customsStatus"),
            @Result(property = "customsHsCode", column = "customsHsCode"),
            @Result(property = "customsFirstUnit", column = "customsFirstUnit"),
            @Result(property = "customsFirstUnitAmount", column = "customsFirstUnitAmount"),
            @Result(property = "customsSecondUnit", column = "customsSecondUnit"),
            @Result(property = "customsSecondUnitAmount", column = "customsSecondUnitAmount"),
            @Result(property = "recordCustomsBookNo", column = "recordCustomsBookNo"),
            @Result(property = "recordCustomsBookId", column = "recordCustomsBookId"),
            @Result(property = "recordExternalProductId", column = "recordExternalProductId"),
            @Result(property = "recordType", column = "record_type"),
            @Result(property = "shelfLife", column = "shelf_life"),
            @Result(property = "goodsRecordTag", column = "goods_record_tag"),
    })

    @Select("<script>" +
            "SELECT gr.*," +
            "rc.customs_code AS recordCustomsCode,rc.customs AS recordCustomsName,rc.status AS customsStatus,rc.hs_code AS customsHsCode,rc.first_unit AS customsFirstUnit,rc.first_unit_amount AS customsFirstUnitAmount,rc.second_unit AS customsSecondUnit,rc.second_unit_amount AS customsSecondUnitAmount," +
            "rw.customs_book_no AS recordCustomsBookNo,rw.customs_book_id AS recordCustomsBookId,rw.external_product_id AS recordExternalProductId" +
            " FROM ccs_goods_record gr " +
            " INNER JOIN ccs_record_customs rc on rc.record_id = gr.id " +
            " INNER JOIN ccs_record_warehouse rw on rw.record_customs_id = rc.id " +
            " WHERE gr.deleted = 0 and rc.deleted = 0 and rw.deleted = 0 " +
            "<if test='productIdList != null'>" +
            " AND gr.product_id in " +
            "<foreach collection='productIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test = 'customsBookId != null'> AND rw.customs_book_id = #{customsBookId} </if>" +
            "<if test = 'status != null'> AND rc.status = #{status} </if>" +
            "<if test = 'tenantId != null'> AND gr.tenant_id = #{tenantId} </if>" +
            "<if test = 'skuId != null'> AND gr.sku_id = #{skuId} </if>" +
            "</script>")
    List<ItemGoodsRecordDTO> selectGoodsRecordInfoList(@Param("productIdList") List<String> productIdList,
                                                       @Param("customsBookId") Long customsBookId,
                                                       @Param("status") Integer status,
                                                       @Param("skuId") String skuId,
                                                       @Param("tenantId") String tenantId);

    @ResultMap(value = "GoodsRecordMap")
    @Select("<script>" +
            "SELECT gr.*," +
            "rc.customs_code AS recordCustomsCode,rc.customs AS recordCustomsName,rc.status AS customsStatus,rc.hs_code AS customsHsCode,rc.first_unit AS customsFirstUnit,rc.first_unit_amount AS customsFirstUnitAmount,rc.second_unit AS customsSecondUnit,rc.second_unit_amount AS customsSecondUnitAmount," +
            "rw.customs_book_no AS recordCustomsBookNo,rw.customs_book_id AS recordCustomsBookId,rw.external_product_id AS recordExternalProductId" +
            " FROM ccs_goods_record gr " +
            " INNER JOIN ccs_record_customs rc on rc.record_id = gr.id " +
            " INNER JOIN ccs_record_warehouse rw on rw.record_customs_id = rc.id " +
            " WHERE gr.deleted = 0 and rc.deleted = 0 and rw.deleted = 0 " +
            "<if test = 'productId != null'> AND gr.product_id = #{productId} </if>" +
            "<if test = 'customsBookId != null'> AND rw.customs_book_id = #{customsBookId} </if>" +
            "<if test = 'status != null'> AND rc.status = #{status} </if>" +
            "<if test = 'tenantId != null'> AND gr.tenant_id = #{tenantId} </if>" +
            "<if test = 'skuId != null'> AND gr.sku_id = #{skuId} </if>" +
            "</script>")
    List<ItemGoodsRecordDTO> selectGoodsRecordInfo(@Param("productId") String productId,
                                                   @Param("customsBookId") Long customsBookId,
                                                   @Param("status") Integer status,
                                                   @Param("skuId") String skuId,
                                                   @Param("tenantId") String tenantId);

    @ResultMap(value = "GoodsRecordMap")
    @Select("<script>" +
            "SELECT gr.* " +
            " FROM ccs_goods_record gr " +
            " INNER JOIN ccs_record_customs rc on rc.record_id = gr.id " +
            " WHERE gr.deleted = 0 and rc.deleted = 0 " +
            "<if test = 'productId != null'> AND gr.product_id = #{productId} </if>" +
            "<if test = 'sku != null'> AND gr.sku_id = #{sku} </if>" +
            "<if test = 'goodsCode != null'> AND gr.goods_code = #{goodsCode} </if>" +
            "<if test = 'tenantId != null'> AND gr.tenant_id = #{tenantId} </if>" +
            "<if test = 'customsCode != null'> AND rc.customs_code = #{customsCode} </if>" +
            "and gr.goods_source = 1 and (goods_record_tag &amp; 1 = 0 or goods_record_tag is null)" +
            "limit 1 " +
            "</script>")
    ItemGoodsRecordDTO selectCentralGoodsRecordInfo(@Param("productId") String productId,
                                                    @Param("sku") String sku,
                                                    @Param("goodsCode") String goodsCode,
                                                    @Param("customsCode") String customsCode,
                                                    @Param("tenantId") String tenantId);


    @ResultMap(value = "GoodsRecordMap")
    @Select(value = "<script>" +
            "SELECT DISTINCT GOODS.* " +
            "FROM ccs_goods_record GOODS " +
            "INNER JOIN ccs_record_customs CUSTOMS ON GOODS.ID = CUSTOMS.RECORD_ID " +
            "INNER JOIN ccs_record_warehouse WAREHOUSE ON GOODS.ID = WAREHOUSE.RECORD_ID " +
            "WHERE GOODS.deleted = 0 and CUSTOMS.deleted=0 and WAREHOUSE.deleted = 0 " +
            "<if test='condition.customDistrict!=null'>" +
            "AND CUSTOMS.customs_code = #{condition.customDistrict} " +
            "</if>" +
            "<if test='condition.recordStatus != null and condition.recordStatus!= 0'>" +
            "<if test='condition.recordStatus == 1'>" +
            "and CUSTOMS.submit_type = 1 and CUSTOMS.status = 1 AND GOODS.record_status = 1 " +
            "</if>" +
            "<if test='condition.recordStatus == 2'>" +
            "and CUSTOMS.submit_type = 2 and CUSTOMS.status = 1 AND GOODS.record_status = 1 " +
            "</if>" +
            "<if test='condition.recordStatus == 3'>" +
            "and TIMESTAMPDIFF(HOUR,CUSTOMS.submit_time,NOW())>48 and CUSTOMS.status = 1 AND GOODS.record_status = 1 " +
            "</if>" +
            "<if test='condition.recordStatus == 4 and condition.customDistrict!=null'>" +
            "and CUSTOMS.status = 2 " +
            "</if>" +
            "<if test='condition.recordStatus == 5 and condition.customDistrict!=null'>" +
            "and CUSTOMS.status = 4 " +
            "</if>" +
            "<if test='condition.recordStatus == 4 and condition.customDistrict == null'>" +
            "AND GOODS.record_status = 2 " +
            "</if>" +
            "<if test='condition.recordStatus == 5 and condition.customDistrict == null'>" +
            "AND GOODS.record_status = 4 " +
            "</if>" +
            "</if>" +

            "<if test='condition.warehouseSn != null'>" +
            "AND WAREHOUSE.warehouse_sn=#{condition.warehouseSn} " +
            "</if>" +
            "<if test='condition.customsBookId != null'>" +
            "AND WAREHOUSE.customs_book_id = #{condition.customsBookId} " +
            "</if>" +
            "<if test='condition.roleAccountBookIdList != null'>" +
            "AND WAREHOUSE.customs_book_id in " +
            "<foreach collection='condition.roleAccountBookIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test='condition.warehouseExternalProductIdList != null'>" +
            "AND WAREHOUSE.external_product_id in " +
            "<foreach collection='condition.warehouseExternalProductIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +

            "<if test='condition.goodsRecordName != null'>" +
            "AND GOODS.goods_record_name like CONCAT('%',#{condition.goodsRecordName},'%') " +
            "</if>" +
            "<if test='condition.tenantId != null'>" +
            "AND GOODS.tenant_id = #{condition.tenantId} " +
            "</if>" +
            "<if test='condition.enable != null'>" +
            "AND GOODS.enable = #{condition.enable} " +
            "</if>" +
            "<if test='condition.searchType != null and condition.searchKey !=null'>" +
            "<if test='condition.searchType == 1'>" +
            "AND GOODS.sku_id in " +
            "<foreach collection='condition.skuList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test='condition.searchType == 2'>" +
            "AND GOODS.bar_code in " +
            "<foreach collection='condition.barCodeList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "</if>" +
            "<if test='condition.recordTimeFromDate != null'>" +
            "AND GOODS.create_time &gt;= #{condition.recordTimeFromDate} " +
            "</if>" +
            "<if test='condition.recordTimeToDate != null'>" +
            "AND GOODS.create_time &lt;= #{condition.recordTimeToDate} " +
            "</if>" +
            "<if test='condition.finishedTimeFrom != null'>" +
            "AND GOODS.record_finish_time &gt;= #{condition.finishedTimeFrom} " +
            "</if>" +
            "<if test='condition.finishedTimeTo != null'>" +
            "AND GOODS.record_finish_time &lt;= #{condition.finishedTimeTo} " +
            "</if>" +
            "<if test='condition.goodsCodeList != null'>" +
            "AND GOODS.goods_code in " +
            "<foreach collection='condition.goodsCodeList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test='condition.productIdList != null'>" +
            "AND GOODS.product_id in " +
            "<foreach collection='condition.productIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "<if test='condition.externalProductIdList != null'>" +
            "AND GOODS.external_product_id in " +
            "<foreach collection='condition.externalProductIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            "ORDER BY GOODS.CREATE_TIME DESC " +
            "</script>"
    )
    List<ItemGoodsRecordDTO> goodsRecordPaging(@Param("condition") GoodsRecordSearchCondition condition);

    @Select(value = "<script>" +
            "select m.erp_warehouse_name as warehouseName,count(*) as num from (" +
            "    select DISTINCT a.`product_id` ,c.`erp_warehouse_name`  from `ccs_goods_record` a " +
            "    inner join `ccs_record_customs` b on a.id = b.`record_id` " +
            "    inner join `ccs_record_warehouse` c on b.`id` = c.`record_customs_id` " +
            "    where a.`deleted` = 0" +
            "<if test='warehouseSn != null'> and c.`warehouse_sn` = #{warehouseSn}</if>" +
            "<if test='recordStatus != null'> and a.`record_status` = #{recordStatus}</if>" +
            "<if test='startDate != null'> and a.`create_time` between #{startDate} and #{endDate} </if>" +
            ")m group by erp_warehouse_name" +
            "</script>")
    List<GoodWarehouseDTO> goodRecordWarehouseSnNum(@Param("warehouseSn") String warehouseSn,
                                                    @Param("recordStatus") Integer recordStatus,
                                                    @Param("startDate") Date startDate,
                                                    @Param("endDate") Date endDate);


    @Select(value = "<script>" +
            "select waitNewCount,waitUpdateCount,overTimeCount from " +
            "(select count(distinct record.id) as waitNewCount " +
            "from `ccs_goods_record` record " +
            "inner join `ccs_record_customs` customs on record.id= customs.`record_id` " +
            "inner join `ccs_record_warehouse` warehouse on record.id= warehouse.`record_id`" +
            "where record.`deleted`= 0 " +
            "and record.`record_status`= 1 " +
            "AND customs.deleted = 0 " +
            "AND customs.submit_type = 1 " +
            "AND customs.`status` = 1 " +
            "AND warehouse.`deleted` = 0" +
            "<if test='accountList != null'>" +
            "AND warehouse.customs_book_id in " +
            "<foreach collection='accountList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            " ) a, " +

            "(select count(distinct record.id) as waitUpdateCount " +
            "from `ccs_goods_record` record " +
            "inner join `ccs_record_customs` customs on record.id= customs.`record_id`" +
            "inner join `ccs_record_warehouse` warehouse on record.id= warehouse.`record_id`" +
            "where record.`deleted` = 0 " +
            "AND record.`record_status` = 1 " +
            "AND customs.deleted = 0 " +
            "AND customs.submit_type = 2 " +
            "AND customs.`status` = 1 " +
            "AND warehouse.`deleted` = 0 " +
            "<if test='accountList != null'>" +
            "AND warehouse.customs_book_id in " +
            "<foreach collection='accountList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +
            " ) b, " +

            "(select count(distinct record.id) as overTimeCount " +
            "from `ccs_goods_record` record " +
            "inner join `ccs_record_customs` customs on record.id= customs.`record_id`" +
            "inner join `ccs_record_warehouse` warehouse on record.id= warehouse.`record_id`" +
            "where record.`deleted` = 0 " +
            "AND record.`record_status` = 1 " +
            "AND customs.deleted = 0 " +
            "AND customs.`status` = 1 " +
            "and TIMESTAMPDIFF(HOUR, customs.submit_time, NOW()) >48 " +
            "AND warehouse.`deleted` = 0 " +
            "<if test='accountList != null'>" +
            "AND warehouse.customs_book_id in " +
            "<foreach collection='accountList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach>" +
            "</if>" +

            " ) c " +

            "</script>"
    )
    GoodsRecordStatusCount getRecordStatusCount(@Param("accountList") List<Long> accountBookList);

    @Update(value = "<script>" +
            "UPDATE ccs_goods_record " +
            "<set> " +
            "   `update_time` = `update_time` + INTERVAL 1 SECOND " +
            "</set> " +
            " WHERE id IN " +
            "   <foreach collection='goodsRecordIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            "       #{item} " +
            "   </foreach>" +
            "</script>")
    void syncEsByUpdateTimeAcc(@Param("goodsRecordIdList") List<Long> goodsRecordIdList);

    @ResultMap(value = "GoodsRecordMap")
    @Select(value = "<script>" +
            " SELECT gd.*" +
            " FROM" +
            " ccs_goods_record gd" +
            " INNER JOIN ccs_record_warehouse rw ON gd.id = rw.record_id " +
            " WHERE" +
            " gd.product_id = #{productId}" +
            " AND rw.customs_book_id = #{customsBookId}" +
            " AND gd.deleted = false; " +
            "</script>")
    List<GoodsRecordDO> findByProductIdAndCustomsBookId(@Param("productId") String productId,
                                                        @Param("customsBookId") Long customsBookId);

    @ResultMap(value = "GoodsRecordMap")
    @Select(value = "<script>" +
            " SELECT " +
            "   gd.*" +
            " FROM" +
            " ccs_goods_record gd" +
            " INNER JOIN ccs_record_warehouse rw ON gd.id = rw.record_id " +
            " INNER JOIN ccs_entity_warehouse ew ON ew.wms_warehouse_code = rw.wms_warehouse_code " +
            " WHERE" +
            " gd.product_id = #{productId} " +
            " AND ew.erp_warehouse_code = #{erpWarehouseCode}" +
            " AND gd.deleted = false;" +
            "</script>")
    List<GoodsRecordDO> findByProductIdAndErpWarehouseCode(@Param("productId") String productId,
                                                           @Param("erpWarehouseCode") String erpWarehouseCode);

    @ResultMap(value = "GoodsRecordMap")
    @Select(value = "<script>" +
            "select cgr.* " +
            "from ccs_goods_record cgr " +
            "         left join ccs_record_customs crc on cgr.id = crc.record_id " +
            "where cgr.product_id = #{productId} " +
            " and crc.customs_code = #{customsCode} " +
            " and (goods_record_tag &amp; 1 = 0 or goods_record_tag is null) and cgr.deleted = 0 and crc.deleted = 0 " +
            "</script>")
    List<GoodsRecordDO> findByProductIdAndCustomsAndNotErpDeleted(@Param("productId") String productId, @Param("customsCode") String customsCode);
}