package com.danding.cds.v2.service;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum;
import com.danding.cds.v2.bean.dao.RecordCustomsDO;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.bean.dto.RecordCustomsESDTO;
import com.danding.cds.v2.mapper.RecordCustomsMapper;
import com.danding.cds.v2.service.base.RecordCustomsBaseService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/9 13:42
 */
@Slf4j
@DubboService
public class RecordCustomsServiceImpl implements RecordCustomsService {
    @Autowired
    private RecordCustomsBaseService recordCustomsBaseService;
    @Autowired
    private RecordCustomsMapper recordCustomsMapper;

    @Override
    public Boolean checkIsNewCustoms(Long recordId, String customsCode) {
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsBaseService.findByRecordId(recordId);
        return recordCustomsDOList.stream().allMatch(s -> !s.getCustomsCode().equals(customsCode));
    }

    @Override
    public RecordCustomsDTO findByRecordIdAndCustomsCode(Long recordId, String customsCode) {
        RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findByRecordIdAndCustomsCode(recordId, customsCode);
        return ConvertUtil.beanConvert(recordCustomsDO, RecordCustomsDTO.class);
    }

    @Override
    public List<RecordCustomsDTO> findByRecordId(Long recordId) {
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsBaseService.findByRecordId(recordId);
        return ConvertUtil.listConvert(recordCustomsDOList, RecordCustomsDTO.class);
    }

    @Override
    public RecordCustomsDTO findById(Long id) {
        RecordCustomsDO recordCustomsDO = recordCustomsBaseService.findById(id);
        return ConvertUtil.beanConvert(recordCustomsDO, RecordCustomsDTO.class);
    }

    @Override
    public List<RecordCustomsDTO> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        log.info("RecordCustomsServiceImpl findById idList={}", idList);
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andIn("id", idList);
        List<RecordCustomsDO> recordCustomsDOS = recordCustomsMapper.selectByExample(example);
        return ConvertUtil.listConvert(recordCustomsDOS, RecordCustomsDTO.class);
    }


    @Override
    public List<RecordCustomsESDTO> queryByRecordId(Long recordId) {
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsBaseService.findByRecordId(recordId);
        return ConvertUtil.listConvert(recordCustomsDOList, RecordCustomsESDTO.class);
    }

    @Override
    public List<RecordCustomsDTO> findByRecordId(List<Long> recordIdList) {
        return recordCustomsBaseService.findDTOByRecordId(recordIdList);
    }

    @Override
    public void updateStatus(Long recordId, String customsCode, Integer statusCode) {
        recordCustomsBaseService.updateStatus(recordId, customsCode, statusCode);
    }

    @Override
    public void updateStatus(Long recordId, String customsCode, Integer statusCode, String reason) {
        recordCustomsBaseService.updateStatus(recordId, customsCode, statusCode, reason);
    }

    @Override
    public void updateStatus(Long recordId, String customsCode, Integer statusCode, String reason, Integer submitType) {
        recordCustomsBaseService.updateStatus(recordId, customsCode, statusCode, reason, submitType);
    }

    @Override
    public void clearRelation(Long recordId) {
        if (Objects.isNull(recordId)) {
            return;
        }
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("deleted", 0);
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setDeleted(true);
        UserUtils.setUpdateBy(recordCustomsDO);
        recordCustomsDO.setUpdateTime(new Date());
        recordCustomsMapper.updateByExampleSelective(recordCustomsDO, example);
    }

    @Override
    public void updateConfigProperty(Long recordId, String productId, String customsCode, Integer status, String hsCode, String firstUnit, BigDecimal firstUnitAmount, String secondUnit, BigDecimal secondUnitAmount) {
        if (Objects.isNull(recordId)) {
            return;
        }
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("customsCode", customsCode).andEqualTo("deleted", 0);
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setProductId(productId);
        recordCustomsDO.setHsCode(hsCode);
        recordCustomsDO.setStatus(status);
        recordCustomsDO.setFirstUnit(firstUnit);
        recordCustomsDO.setFirstUnitAmount(firstUnitAmount);
        recordCustomsDO.setSecondUnit(secondUnit);
        recordCustomsDO.setSecondUnitAmount(secondUnitAmount);
        UserUtils.setUpdateBy(recordCustomsDO);
        recordCustomsDO.setUpdateTime(new Date());
        recordCustomsMapper.updateByExampleSelective(recordCustomsDO, example);
    }

    @Override
    public Map<Long, RecordCustomsDTO> findByProductIdListAndCustomsCode(List<String> submitProductIdList, String customsCode) {
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andIn("productId", submitProductIdList).andEqualTo("customsCode", customsCode).andEqualTo("deleted", 0);
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsMapper.selectByExample(example);
        List<RecordCustomsDTO> recordCustomsDTOS = ConvertUtil.listConvert(recordCustomsDOList, RecordCustomsDTO.class);
        Map<Long, RecordCustomsDTO> collect = recordCustomsDTOS.stream().collect(Collectors.toMap(RecordCustomsDTO::getRecordId, Function.identity(), (v1, v2) -> v1));
        return collect;
    }

    @Override
    public Map<Long, RecordCustomsDTO> findByRecordIdListAndCustomsCode(List<Long> recordIdList, String customsCode) {
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andIn("recordId", recordIdList).andEqualTo("customsCode", customsCode).andEqualTo("deleted", 0);
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsMapper.selectByExample(example);
        List<RecordCustomsDTO> recordCustomsDTOS = ConvertUtil.listConvert(recordCustomsDOList, RecordCustomsDTO.class);
        Map<Long, RecordCustomsDTO> collect = recordCustomsDTOS.stream().collect(Collectors.toMap(RecordCustomsDTO::getRecordId, Function.identity(), (v1, v2) -> v1));
        return collect;
    }

    /**
     * 获取本次备案提交的提交类型
     * 1.备案+口岸只有一条 且口岸相当 -> 新品
     * 2.备案+口岸已有一或多条 一条的时候不为自己的时候 -> 更新
     *
     * @param recordId
     * @param customsCode
     * @return {@link com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum}
     */
    @Override
    public Integer getSubmitType(Long recordId, String customsCode) {
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsBaseService.findByRecordId(recordId);
        if (CollectionUtils.isEmpty(recordCustomsDOList)) {
            return RecordCustomsSubmitTypeEnum.NEW_SUBMIT.getCode();
        }
        return RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode();
    }

    @Override
    public void deleteByRecordId(Long recordId) {
        if (Objects.isNull(recordId)) {
            return;
        }
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setDeleted(true);
        UserUtils.setUpdateBy(recordCustomsDO);
        recordCustomsDO.setUpdateTime(new Date());
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("deleted", false);
        recordCustomsMapper.updateByExampleSelective(recordCustomsDO, example);
        log.info("RecordCustomsServiceImpl deleteByRecordId recordId={}", recordId);
    }

    @Override
    public void updateBaseJson(Long recordId, String customsCode, String jsonString) {
        if (Objects.isNull(recordId) || Objects.isNull(customsCode)) {
            return;
        }
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setBaseInfoJson(jsonString);
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode);
        recordCustomsMapper.updateByExampleSelective(recordCustomsDO, example);
    }

    @Override
    public void updateGuanWuRemark(Long recordId, String customsCode, String guanWuRemark) {
        if (Objects.isNull(recordId) || Objects.isNull(customsCode)) {
            return;
        }
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setGuanWuRemark(guanWuRemark);
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("recordId", recordId)
                .andEqualTo("customsCode", customsCode);
        recordCustomsMapper.updateByExampleSelective(recordCustomsDO, example);
    }

    @Override
    public List<RecordCustomsDTO> findByStatus(Integer status) {
        if (Objects.isNull(status)) {
            return new ArrayList<>();
        }
        Example example = new Example(RecordCustomsDO.class);
        example.createCriteria().andEqualTo("status", status)
                .andEqualTo("deleted", false);
        List<RecordCustomsDO> recordCustomsDOList = recordCustomsMapper.selectByExample(example);
        return ConvertUtil.listConvert(recordCustomsDOList, RecordCustomsDTO.class);
    }

    @Override
    public RecordCustomsDTO getNextWaitAuditRecord(List<Long> accountBookIdList, String customsCode, Long recordId) {
        if (CollectionUtils.isEmpty(accountBookIdList)) {
            return null;
        }
        RecordCustomsDO nextWaitAuditRecord = recordCustomsMapper.getNextWaitAuditRecord(accountBookIdList, customsCode, recordId);
        return ConvertUtil.beanConvert(nextWaitAuditRecord, RecordCustomsDTO.class);
    }
}
