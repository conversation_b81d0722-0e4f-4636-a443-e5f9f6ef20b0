package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.bean.dao.ProcessTradeBookConsumptionDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.enums.ProcessTradeBookConsumptionDeclareStatusEnums;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.service.base.ProcessTradeBookConsumptionBaseService;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboService;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 加工贸易账册单耗信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Slf4j
@DubboService
public class ProcessTradeBookConsumptionServiceImpl implements ProcessTradeBookConsumptionService {

    @Resource
    private ProcessTradeBookConsumptionBaseService baseService;

    @Resource
    private ProcessTradeBookItemService processTradeBookItemService;

    @Override
    public ListVO<ProcessTradeBookConsumptionDTO> paging(ProcessTradeBookConsumptionSearch param) {
        ProcessTradeBookItemSearch search = new ProcessTradeBookItemSearch();
        search.setRefBookId(param.getRefBookId());

        ListVO<ProcessTradeBookConsumptionDTO> emptyListVO = ListVO.emptyListVO();
        emptyListVO.getPage().setPageSize(param.getPageSize());
        emptyListVO.getPage().setCurrentPage(param.getCurrentPage());

        if (Objects.nonNull(param.getEndPrdSeqNo()) || StrUtil.isNotBlank(param.getEndPrdName())) {
            search.setGoodsType(2);
            search.setSeqNo(param.getEndPrdSeqNo());
            search.setGoodsName(param.getEndPrdName());
            List<ProcessTradeBookItemDTO> endPrdItemDTOList = processTradeBookItemService.pagingList(search);
            if (CollUtil.isEmpty(endPrdItemDTOList)) {
                return emptyListVO;
            }
            param.setEndPrdIdList(endPrdItemDTOList.stream().map(ProcessTradeBookItemDTO::getId).collect(Collectors.toList()));
        }
        if (Objects.nonNull(param.getMtpckSeqNo()) || StrUtil.isNotBlank(param.getMtpckName())) {
            search.setGoodsType(1);
            search.setSeqNo(param.getMtpckSeqNo());
            search.setGoodsName(param.getMtpckName());
            List<ProcessTradeBookItemDTO> mtpckItemDTOList = processTradeBookItemService.pagingList(search);
            if (CollUtil.isEmpty(mtpckItemDTOList)) {
                return emptyListVO;
            }
            param.setMtpckIdList(mtpckItemDTOList.stream().map(ProcessTradeBookItemDTO::getId).collect(Collectors.toList()));
        }
        return baseService.paging(param);
    }

    @Override
    public List<ProcessTradeBookConsumptionDTO> findByEndPrdId(Long endPrdId) {
        if (Objects.isNull(endPrdId)) {
            return Collections.emptyList();
        }
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andEqualTo("endPrdId", endPrdId).andEqualTo("deleted", false);
        List<ProcessTradeBookConsumptionDO> consumptionDOList = baseService.selectByExample(example);
        return ConvertUtil.listConvert(consumptionDOList, ProcessTradeBookConsumptionDTO.class);
    }

    @Override
    public void createOrEdit(ProcessTradeBookConsumptionDTO param) {
        ProcessTradeBookConsumptionDTO consumptionDTO = findByEndprdIdAndMtpckId(param.getEndPrdId(), param.getMtpckId());
        ProcessTradeBookItemDTO endPrdItemDTO = processTradeBookItemService.findById(param.getEndPrdId());
        ProcessTradeBookItemDTO mtpckItemDTO = processTradeBookItemService.findById(param.getMtpckId());
        if (Objects.isNull(endPrdItemDTO) || Objects.isNull(mtpckItemDTO)) {
            throw new ArgsInvalidException("成品或料件不存在");
        }
        param.setConsumptionVersionNo(mtpckItemDTO.getProductId());
        if (param.getId() == null) {
            if (Objects.nonNull(consumptionDTO)) {
                throw new ArgsInvalidException("成品和料件组合已存在");
            }
            ProcessTradeBookConsumptionDO insertDO = buildInsertDO(param);
            baseService.insertSelective(insertDO);
        } else {
            if (Objects.nonNull(consumptionDTO) && !Objects.equals(consumptionDTO.getId(), param.getId())) {
                throw new ArgsInvalidException("成品和料件组合已存在");
            }
            baseService.updateByPrimaryKeySelective(ConvertUtil.beanConvert(param, ProcessTradeBookConsumptionDO.class));
        }
    }

    private ProcessTradeBookConsumptionDO buildInsertDO(ProcessTradeBookConsumptionDTO param) {
        param.setTangibleLossRate(BigDecimal.ZERO);
        param.setIntangibleLossRate(BigDecimal.ZERO);
        param.setBondedMaterialRatio(BigDecimal.valueOf(100));
        param.setModifyFlag("3");
        if (Objects.isNull(param.getDeclareStatus())) {
            param.setDeclareStatus(ProcessTradeBookConsumptionDeclareStatusEnums.DECLARED.getCode());
        }
        param.setConsumptionValidity(DateUtils.addYears(new Date(), 3));
        return ConvertUtil.beanConvert(param, ProcessTradeBookConsumptionDO.class);
    }

    private ProcessTradeBookConsumptionDTO findByEndprdIdAndMtpckId(Long endPrdId, Long mtpckId) {
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andEqualTo("endPrdId", endPrdId)
                .andEqualTo("mtpckId", mtpckId)
                .andEqualTo("deleted", false);
        ProcessTradeBookConsumptionDO consumptionDO = baseService.selectOneByExample(example);
        return ConvertUtil.beanConvert(consumptionDO, ProcessTradeBookConsumptionDTO.class);
    }

    @Override
    public void deleteById(List<Long> idList) {
        baseService.deleteById(idList);
    }

    @Override
    public void deleteByItemId(Long itemId) {
        baseService.deleteByItemId(Collections.singletonList(itemId));
    }

    @Override
    public void deleteByItemId(List<Long> itemId) {
        baseService.deleteByItemId(itemId);
    }

    @Override
    public List<ProcessTradeBookConsumptionDTO> findByRefBookId(Long refBookId) {
        if (Objects.isNull(refBookId)) {
            return new ArrayList<>();
        }
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andEqualTo("refBookId", refBookId).andEqualTo("deleted", false);
        List<ProcessTradeBookConsumptionDO> consumptionDOList = baseService.selectByExample(example);
        return ConvertUtil.listConvert(consumptionDOList, ProcessTradeBookConsumptionDTO.class);
    }

    @Override
    public void importExcel(Long refBookId, String operator, List<ProcessTradeBookConsumptionDTO> successList) {
        if (CollUtil.isEmpty(successList)) {
            log.info("无导入数据");
            return;
        }
        List<ProcessTradeBookConsumptionDO> insertDOList = successList.stream()
                .map(this::buildInsertDO)
                .collect(Collectors.toList());
        baseService.insertList(insertDOList);
    }

    @Override
    public ProcessTradeBookItemDTO findById(Long consumptionId) {
        if (Objects.isNull(consumptionId)) {
            return null;
        }
        ProcessTradeBookConsumptionDO consumptionDO = baseService.findById(consumptionId);
        return ConvertUtil.beanConvert(consumptionDO, ProcessTradeBookItemDTO.class);
    }
}