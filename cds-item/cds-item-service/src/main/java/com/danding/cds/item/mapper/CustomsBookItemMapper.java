package com.danding.cds.item.mapper;


import com.danding.cds.inventory.api.dto.InventoryChangeSqlDTO;
import com.danding.cds.item.entity.CustomsBookItemDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface CustomsBookItemMapper extends Mapper<CustomsBookItemDO>, InsertListMapper<CustomsBookItemDO>, BatchUpdateMapper<CustomsBookItemDO>, AggregationPlusMapper<CustomsBookItemDO> {
    @Update(value = "<script>" +
            "update ccs_item.ccs_customs_book_item" +
            "        <set>" +
            "            <if test='accountFlag!=null'>" +
            "                account_num = account_num " +
            "                <if test='accountFlag'>  + </if>" +
            "                <if test='!accountFlag'> - </if>" +
            "                #{changeNum}," +
            "            </if>" +
            "            <if test='occupiedFlag!=null'>" +
            "                occupied_num = occupied_num " +
            "                <if test='occupiedFlag'>  + </if>" +
            "                <if test='!occupiedFlag'> - </if>" +
            "                #{changeNum}," +
            "            </if>" +
            "            <if test='usedFlag!=null'>" +
            "                used_num = used_num " +
            "                <if test='usedFlag'>  + </if>" +
            "                <if test='!usedFlag'> - </if>" +
            "                #{changeNum}," +
            "            </if>" +
            "            <if test='availableFlag!=null'>" +
            "                available_num = available_num " +
            "                <if test='availableFlag'>  + </if>" +
            "                <if test='!availableFlag'> - </if>" +
            "                #{changeNum}," +
            "            </if>" +
            "            <if test='lockedFlag!=null'>" +
            "                locked_num = locked_num " +
            "                <if test='lockedFlag'>  + </if>" +
            "                <if test='!lockedFlag'> - </if>" +
            "                #{changeNum}," +
            "            </if>" +
            "        </set>" +
            "        where id = #{id}" +
            "</script>"
    )
    void updateInventoryNum(InventoryChangeSqlDTO inventoryChangeSqlDTO);


    @Update(value = "<script>" +
            "update ccs_item.ccs_customs_book_item" +
            "        <set>" +
            "                total_in_qty = total_in_qty + " +
            "                #{changeNum}," +
            "                total_diff_qty = total_diff_qty + " +
            "                #{changeNum}," +
            "        </set>" +
            "        where id = #{id}" +
            "</script>"
    )
    void updateTotalInQtyNum(@Param("id") Long id, @Param("changeNum") Integer changeNum);


    @Update(value = "<script>" +
            "update ccs_item.ccs_customs_book_item" +
            "        <set>" +
            "                total_out_qty = total_out_qty + " +
            "                #{changeNum}," +
            "                total_diff_qty = total_diff_qty - " +
            "                #{changeNum}," +
            "        </set>" +
            "        where id = #{id}" +
            "</script>"
    )
    void updateTotalOutQtyNum(@Param("id") Long id, @Param("changeNum") Integer changeNum);

    /**
     * 批量更新库存数量
     */
    @Update({
            "<script>",
            "update ccs_item.ccs_customs_book_item",
            "<set>",
            "  <trim suffixOverrides=','>",
            "    <foreach collection='list' item='item' index='index'>",
            "      <if test='item.id != null'>",
            "        <if test='index == 0'>",
            "          <if test='item.accountFlag != null'>",
            "            account_num = CASE id ",
            "            <foreach collection='list' item='i'>",
            "              <if test='i.accountFlag'>WHEN #{i.id} THEN account_num + #{i.changeNum} </if>",
            "              <if test='!i.accountFlag'>WHEN #{i.id} THEN account_num - #{i.changeNum} </if>",
            "            </foreach>",
            "            ELSE account_num END,",
            "          </if>",
            "          <if test='item.availableFlag != null'>",
            "            available_num = CASE id ",
            "            <foreach collection='list' item='i'>",
            "              <if test='i.availableFlag'>WHEN #{i.id} THEN available_num + #{i.changeNum} </if>",
            "              <if test='!i.availableFlag'>WHEN #{i.id} THEN available_num - #{i.changeNum} </if>",
            "            </foreach>",
            "            ELSE available_num END,",
            "          </if>",
            "          <if test='item.occupiedFlag != null'>",
            "            occupied_num = CASE id ",
            "            <foreach collection='list' item='i'>",
            "              <if test='i.occupiedFlag'>WHEN #{i.id} THEN occupied_num + #{i.changeNum} </if>",
            "              <if test='!i.occupiedFlag'>WHEN #{i.id} THEN occupied_num - #{i.changeNum} </if>",
            "            </foreach>",
            "            ELSE occupied_num END,",
            "          </if>",
            "          <if test='item.usedFlag != null'>",
            "            used_num = CASE id ",
            "            <foreach collection='list' item='i'>",
            "              <if test='i.usedFlag'>WHEN #{i.id} THEN used_num + #{i.changeNum} </if>",
            "              <if test='!i.usedFlag'>WHEN #{i.id} THEN used_num - #{i.changeNum} </if>",
            "            </foreach>",
            "            ELSE used_num END,",
            "          </if>",
            "          <if test='item.lockedFlag != null'>",
            "            locked_num = CASE id ",
            "            <foreach collection='list' item='i'>",
            "              <if test='i.lockedFlag'>WHEN #{i.id} THEN locked_num + #{i.changeNum} </if>",
            "              <if test='!i.lockedFlag'>WHEN #{i.id} THEN locked_num - #{i.changeNum} </if>",
            "            </foreach>",
            "            ELSE locked_num END,",
            "          </if>",
            "        </if>",
            "      </if>",
            "    </foreach>",
            "  </trim>",
            "</set>",
            "where id in",
            "<foreach collection='list' item='item' open='(' separator=',' close=')'>",
            "  #{item.id}",
            "</foreach>",
            "</script>"
    })
    void batchUpdateInventoryNum(@Param("list") List<InventoryChangeSqlDTO> list);
}