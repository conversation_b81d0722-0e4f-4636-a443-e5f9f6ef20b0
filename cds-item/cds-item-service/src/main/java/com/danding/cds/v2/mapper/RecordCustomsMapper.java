package com.danding.cds.v2.mapper;

import com.danding.cds.v2.bean.dao.RecordCustomsDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 备案-口岸
 * @date 2022/3/7 15:18
 */
public interface RecordCustomsMapper extends Mapper<RecordCustomsDO>, InsertListMapper<RecordCustomsDO>, BatchUpdateMapper<RecordCustomsDO>, AggregationPlusMapper<RecordCustomsDO> {

    @Select("<script>" +
            "SELECT distinct crc.customs_code ,crc.customs " +
            " FROM ccs_record_customs crc " +
            "         left join ccs_goods_record cgr on crc.record_id = cgr.id " +
            " WHERE cgr.deleted = 0 and crc.deleted = 0 " +
            " and cgr.tenant_id = #{userId} " +
            "<if test = 'goodsCode != null'> AND cgr.goods_code = #{goodsCode} </if>" +
            "</script>")
    List<RecordCustomsDO> findByUserId(@Param("userId") String userId, @Param("goodsCode") String goodsCode);

    //  `id` bigint(20) NOT NULL AUTO_INCREMENT,
    //  `record_id` bigint(20) NOT NULL COMMENT '商品备案id',
    //  `product_id` varchar(32) NOT NULL COMMENT '料号',
    //  `customs_code` varchar(32) NOT NULL COMMENT '口岸code',
    //  `customs` varchar(32) NOT NULL COMMENT '口岸',
    //  `origin_country` varchar(50) DEFAULT NULL COMMENT '海关原产国(业务不许为空)',
    //  `hs_code` varchar(10) DEFAULT NULL COMMENT 'HS编码',
    //  `first_unit` varchar(10) DEFAULT NULL COMMENT '法定第一计量单位',
    //  `first_unit_amount` decimal(10,5) DEFAULT NULL COMMENT '法定第一计量单位数量',
    //  `second_unit` varchar(10) DEFAULT NULL COMMENT '法定第二计量单位',
    //  `second_unit_amount` decimal(10,5) DEFAULT NULL COMMENT '法定第二计量单位数量',
    //  `reason` varchar(255) DEFAULT NULL COMMENT '驳回原因',
    //  `status` tinyint(3) NOT NULL COMMENT '状态 1：待审核；2：审核通过； 4：驳回',
    //  `submit_type` tinyint(1) NOT NULL COMMENT '状态 无 0/新品1/更新2',
    //  `submit_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '提交时间',
    //  `audit_way` int(10) DEFAULT NULL COMMENT '审核方式 0-人工审核，1-系统审核',
    //  `base_info_json` text COMMENT '口岸提交备案json串',
    //  `erp_commit_status` tinyint(4) DEFAULT NULL COMMENT 'ERP提交审核状态',
    //  `guan_wu_remark` varchar(1024) DEFAULT NULL COMMENT '关务备案',
    //  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
    //  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
    //  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    //  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    //  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
    //  `tenantry_id` bigint(20) NOT NULL DEFAULT '1001' COMMENT 'Sass租户ID',
    @Results(id = "recordCustomsMap", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "recordId", column = "record_id"),
            @Result(property = "customsCode", column = "customs_code"),
            @Result(property = "customs", column = "customs"),
            @Result(property = "hsCode", column = "hs_code"),
            @Result(property = "firstUnit", column = "first_unit"),
            @Result(property = "firstUnitAmount", column = "first_unit_amount"),
            @Result(property = "secondUnit", column = "second_unit"),
            @Result(property = "secondUnitAmount", column = "second_unit_amount"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "status", column = "status"),
            @Result(property = "submitType", column = "submit_type"),
            @Result(property = "submitTime", column = "submit_time"),
            @Result(property = "auditWay", column = "audit_way"),
            @Result(property = "baseInfoJson", column = "base_info_json"),
            @Result(property = "erpCommitStatus", column = "erp_commit_status"),
            @Result(property = "guanWuRemark", column = "guan_wu_remark"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "deleted", column = "deleted"),
            @Result(property = "tenantryId", column = "tenantry_id"),
            @Result(property = "createBy", column = "create_by"),
            @Result(property = "updateBy", column = "update_by"),
            @Result(property = "originCountry", column = "origin_country"),
            @Result(property = "productId", column = "product_id")
    })
    @Select("<script>" +
            "SELECT crc.* " +
            " FROM ccs_record_customs crc " +
            " INNER JOIN ccs_record_warehouse rw ON crc.id = rw.record_customs_id " +
            " WHERE rw.deleted = 0 and crc.deleted = 0 and crc.status = 1 " +
            " AND rw.customs_book_id IN " +
            "   <foreach collection='accountBookIdList' item='item' index ='index' open='(' separator=',' close=')' > " +
            "       #{item} " +
            "   </foreach>" +
            "<if test = 'customsCode != null'> AND crc.customs_code = #{customsCode} </if>" +
            "<if test = 'recordId != null'> AND crc.record_id != #{recordId} </if>" +
            "order by create_time asc limit 1" +
            "</script>")
    RecordCustomsDO getNextWaitAuditRecord(@Param("accountBookIdList") List<Long> accountBookIdList,
                                           @Param("customsCode") String customsCode,
                                           @Param("recordId") Long recordId);
}