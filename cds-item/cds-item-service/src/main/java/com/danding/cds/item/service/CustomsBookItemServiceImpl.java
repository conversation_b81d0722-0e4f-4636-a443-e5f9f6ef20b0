package com.danding.cds.item.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.bean.dto.ItemTrackLogExtraDTO;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.exception.ArgsErrorRpcException;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.enums.InventoryInOutEnum;
import com.danding.cds.inventory.api.dto.InventoryChangeSqlDTO;
import com.danding.cds.inventory.api.dto.ItemLogsIdDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.cds.inventory.mapper.InventoryChangeMapper;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.entity.CustomsBookDO;
import com.danding.cds.item.entity.CustomsBookItemDO;
import com.danding.cds.item.mapper.CustomsBookItemMapper;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.stock.bean.OrderInfoDto;
import com.danding.cds.stock.service.StoreRedisBaseService;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.cds.v2.bean.dto.CustomsBookItemAdjustLogDTO;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.bean.es.CustomsBookItemCompareDTO;
import com.danding.cds.v2.bean.es.ItemTrackLogEsDO;
import com.danding.cds.v2.bean.es.ItemTrackLogEsDTO;
import com.danding.cds.v2.bean.es.ItemTrackLogSaveDTO;
import com.danding.cds.v2.bean.vo.req.CustomsBookItemAdjustLogSearch;
import com.danding.cds.v2.bean.vo.req.ItemTrackLogSearch;
import com.danding.cds.v2.bean.vo.res.AssociateCustomsBookItemResVO;
import com.danding.cds.v2.es.CustomsBookItemEsDao;
import com.danding.cds.v2.service.CustomsBookItemAdjustLogService;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.session.RowBounds;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Caching;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author: shenhui
 * @Date: 2020/5/11 14:35
 */
@DubboService
@Slf4j
@RefreshScope
public class CustomsBookItemServiceImpl implements CustomsBookItemService {

    @Autowired
    private CustomsBookItemMapper customsBookItemMapper;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private InventoryChangeService inventoryChangeService;

    @Autowired
    private Validator validator;

    @Autowired
    private InventoryChangeMapper inventoryChangeMapper;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private StoreRedisBaseService storeRedisBaseService;

    @Autowired
    private CustomsBookItemEsDao customsBookItemEsDao;

    @Autowired
    private MessageSender messageSender;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private ItemStockListService itemStockListService;

    @Autowired
    private CustomsBookItemAdjustLogService customsBookItemAdjustLogService;

    @Autowired
    private FourCategoryGoodsService fourCategoryGoodsService;

    /**
     * 此方法Cannal 同步数据，请勿修改此方法, 防止 Cannal 同步数据死循环
     * @param id
     */
    @Override
    public void updateGoodsSourceTokeyMaterialById(Long id) {
        CustomsBookItemDTO customBookItemDTO = findById(id);
        if (customBookItemDTO == null) {
            return;
        }

        Boolean isFourCategoryGoods = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(customBookItemDTO.getOriginCountry(), customBookItemDTO.getHsCode());
        Boolean keyMaterial = Objects.equals(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode(), customBookItemDTO.getGoodsSource());
        // 判断是否是四类商品且维护的数据不是重点料件
        if (isFourCategoryGoods && !keyMaterial) {
            CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
            customsBookItemDO.setId(id);
            customsBookItemDO.setGoodsSource(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode());
            customsBookItemDO.setUpdateTime(new Date());
            customsBookItemMapper.updateByPrimaryKeySelective(customsBookItemDO);
        } else if (!isFourCategoryGoods && StringUtils.isEmpty(customBookItemDTO.getGoodsSource())) {
            CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
            customsBookItemDO.setId(id);
            customsBookItemDO.setGoodsSource(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode());
            customsBookItemDO.setUpdateTime(new Date());
            customsBookItemMapper.updateByPrimaryKeySelective(customsBookItemDO);
        }
    }

    @Override
    @PageSelect
    public ListVO<CustomsBookItemDTO> paging(CustomsBookItemSearchCondition condition) {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!StringUtils.isEmpty(condition.getItemName())) {
            criteria.andLike("goodsName", "%" + condition.getItemName() + "%");
        }
        if (!StringUtils.isEmpty(condition.getProductId())) {
            criteria.andIn("productId", Splitter.on(",").splitToList(condition.getProductId()));
        }
        if (!StringUtils.isEmpty(condition.getHsCode())) {
            criteria.andIn("hsCode", Splitter.on(",").splitToList(condition.getHsCode()));
        }
        if (!StringUtils.isEmpty(condition.getGoodsSeqNo())) {
            criteria.andIn("goodsSeqNo", Splitter.on(",").splitToList(condition.getGoodsSeqNo()));
        }
        if (!CollectionUtils.isEmpty(condition.getRoleAccountBookIdList())) {
            criteria.andIn("customsBookId", condition.getRoleAccountBookIdList());
        }
        if (!LongUtil.isNone(condition.getCustomsBookId())) {
            criteria.andEqualTo("customsBookId", condition.getCustomsBookId());
        }
        if (Objects.nonNull(condition.getUnifiedProductId())) {
            criteria.andIn("unifiedProductId", Splitter.on(",").splitToList(condition.getUnifiedProductId()));
        }
        if (!LongUtil.isNone(condition.getCreateFrom())) {
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(condition.getCreateFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getCreateTo())) {
            criteria.andLessThanOrEqualTo("createTime", new DateTime(condition.getCreateTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getUpdateFrom())) {
            criteria.andGreaterThanOrEqualTo("updateTime", new DateTime(condition.getUpdateFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getUpdateTo())) {
            criteria.andLessThanOrEqualTo("updateTime", new DateTime(condition.getUpdateTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (condition.getEnableStatus() != null) {
            criteria.andEqualTo("enable", condition.getEnableStatus());
        }
        //差异类型
        if (Objects.equals("Equal", condition.getDiffType())) {
            criteria.andCondition(" total_diff_qty - account_num = 0 ");
        } else if (Objects.equals("LessThen", condition.getDiffType())) {
            criteria.andCondition(" total_diff_qty - account_num < 0 ");
        } else if (Objects.equals("GreaterThan", condition.getDiffType())) {
            criteria.andCondition(" total_diff_qty - account_num > 0 ");
        }
        //原产国
        if (!CollectionUtils.isEmpty(condition.getOriginCountryCodeList())) {
            criteria.andIn("originCountry", condition.getOriginCountryCodeList());
        }


//        if (!StringUtils.isEmpty(condition.getDiffType())) {
//            criteria.andEqualTo("diffType", condition.getDiffType());
//        }

        // 库存异常查询 占用库存或可用库存<0 的情况
        if (Objects.equals(condition.getExceptionType(), CustomsBookItemExceptionEnum.STOCK_EXCEPTION.getCode())) {
            Example.Criteria exception = example.createCriteria().orLessThan("availableNum", 0).orLessThan("occupiedNum", 0);
            example.and(exception);
        }
        //账册库存排序条件
        if (!StringUtils.isEmpty(condition.getAccountNumCondition()) && !StringUtils.isEmpty(condition.getAccountNum())) {
            if ("Greater".equals(condition.getAccountNumCondition())) {
                criteria.andGreaterThan("accountNum", Integer.valueOf(condition.getAccountNum()));
            } else if ("Equal".equals(condition.getAccountNumCondition())) {
                criteria.andEqualTo("accountNum", Integer.valueOf(condition.getAccountNum()));
            } else if ("Less".equals(condition.getAccountNumCondition())) {
                criteria.andLessThan("accountNum", Integer.valueOf(condition.getAccountNum()));
            }
        }
        //金二序号排序条件
        if (StringUtils.isEmpty(condition.getGoodsSeqNoCondition()) || "Default".equals(condition.getGoodsSeqNoCondition())) {
            example.setOrderByClause("length(goods_seq_no) DESC ,goods_seq_no DESC,create_time DESC");
        } else if ("Asc".equals(condition.getGoodsSeqNoCondition())) {
            example.setOrderByClause("length(goods_seq_no) Asc ,goods_seq_no Asc,id Asc");
        } else if ("Desc".equals(condition.getGoodsSeqNoCondition())) {
            example.setOrderByClause("length(goods_seq_no) DESC ,goods_seq_no DESC,id DESC");
        }
        //排序
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        ListVO<CustomsBookItemDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class));
        // 分页
        PageInfo<CustomsBookDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    @Override
//    @Cacheable(value = "ItemWithId", key = "#id", unless = "#result==null")
    public CustomsBookItemDTO findById(Long id) {
        if (Objects.isNull(id) || id == 0) {
            return null;
        } else {
            CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectByPrimaryKey(id);
            if (customsBookItemDO == null) {
                return null;
            } else {
                /*CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
                BeanUtils.copyProperties(customsBookItemDO, customsBookItemDTO);
                customsBookItemDTO.setInDate(customsBookItemDO.getInDate());*/
                return JSON.parseObject(JSON.toJSONString(customsBookItemDO), CustomsBookItemDTO.class);
            }
        }
    }

    @Override
    public CustomsBookItemDTO findByinvtNoAndProductId(String invtNo, String productId) {
        if (StringUtils.isEmpty(invtNo) || StringUtils.isEmpty(productId)) return null;
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("invtNo", invtNo);
        criteria.andEqualTo("productId", productId);
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectOneByExample(example);
        return JSON.parseObject(JSON.toJSONString(customsBookItemDO), CustomsBookItemDTO.class);
    }

    @Override
    public List<CustomsBookItemDTO> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", false);
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsBookItemDOS, CustomsBookItemDTO.class);
    }

    @Override
    @Caching(evict = {
            @CacheEvict(value = "ItemWithBookIdAndProId", key = "#submit.getCustomsBookId()+'-'+#submit.getProductId()")
            , @CacheEvict(value = "ItemWithBookIdAndSeqNoAndProId", key = "#submit.getCustomsBookId()+'-'+#submit.getGoodsSeqNo()+'-'+#submit.getProductId()")
            , @CacheEvict(value = "ItemWithId", key = "#submit.getId()")
    })
    public Long upset(CustomsBookItemSubmit submit) throws ArgsErrorException {
        CustomsBookItemSubmitDTO submitDTO = new CustomsBookItemSubmitDTO();
        return upsetCore(submitDTO);
    }

    @Override
    public Long upsetCore(CustomsBookItemSubmitDTO submit) throws ArgsErrorException {
        //校验必填项
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (!StringUtils.isEmpty(inputError)) {
            log.info("[CustomsBookItemServiceImpl-upset] submit={},errMsg = {}", JSON.toJSONString(submit), inputError);
            throw new ArgsErrorException(inputError);
        }

        CustomsBookDTO customsBookDTO = customsBookService.findById(submit.getCustomsBookId());
        /*if (customsBookDTO == null) {
            throw new ArgsErrorException("账册编号错误");
        }*/
        //这里之前是加料号去判断唯一 现在去掉了料号
        CustomsBookItemDTO old = this.findByBookIdAndSeqNoAndProId(customsBookDTO.getId(), submit.getGoodsSeqNo(), null);
        if (old != null) {
            if (LongUtil.isNone(submit.getId())) {//导入
                submit.setId(old.getId());
            } else {//编辑
                if (!submit.getId().equals(old.getId())) {
                    throw new ArgsErrorException("账册编码" + customsBookDTO.getBookNo()
                            + ":料号为" + old.getProductId()
                            + "，商品序号为" + old.getGoodsSeqNo() + "的记录已存在！");
                }
            }
        }

        CustomsHsDTO customsHsDTO = customsHsService.findByCode(submit.getHsCode());
        if (customsHsDTO == null) {
            throw new ArgsErrorException("HS编码错误");
        }
        CustomsCountryDTO countryDTO = customsCountryService.findByCode(submit.getOriginCountry());
        if (countryDTO == null) {
            throw new ArgsErrorException("国别错误");
        }
        CustomsUomDTO uomDTO;
        uomDTO = customsUomService.findByCode(submit.getGoodsUnit());
        if (uomDTO == null) {
            throw new ArgsErrorException("申报计量单位错误");
        }
        uomDTO = customsUomService.findByCode(submit.getFirstUnit());
        if (uomDTO == null) {
            throw new ArgsErrorException("法定计量单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnit()) && customsUomService.findByCode(submit.getSecondUnit()) == null) {
            throw new ArgsErrorException("第二法定计量单位错误");
        }
        if (!StringUtils.isEmpty(submit.getSecondUnit()) && StringUtils.isEmpty(submit.getInSecondLegalQty())) {
            throw new ArgsErrorException("第二法定计量单位不为空时，第二入仓法定数量不能为空");
        }
        if (StringUtils.isEmpty(submit.getSecondUnit()) && !StringUtils.isEmpty(submit.getInSecondLegalQty())) {
            throw new ArgsErrorException("第二法定计量单位为空时，第二入仓法定数量不能传入");
        }
        CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(submit.getCurrCode());
        if (currencyDTO == null) {
            throw new ArgsErrorException("申报币制错误");
        }


        //初始化
        CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
        BeanUtils.copyProperties(submit, customsBookItemDO);
        customsBookItemDO.setGoodsSource(submit.getGoodsSource());
        customsBookItemDO.setOriginCountry(submit.getOriginCountry());
        customsBookItemDO.setGoodsUnit(submit.getGoodsUnit());
        customsBookItemDO.setFirstUnit(submit.getFirstUnit());
        customsBookItemDO.setSecondUnit(submit.getSecondUnit());
        customsBookItemDO.setCustomsBookId(customsBookDTO.getId());
        customsBookItemDO.setInDate(submit.getInDate() != null ? new Date(submit.getInDate()) : null);
        if (submit.getId() == null || submit.getId() == 0) {//新增
            //导入的时候 可用库存要等于账册库存
            if (Objects.equals(submit.getSource(), ItemTrackLogConfig.ITEM_TYPE_IMPORT)) {
                customsBookItemDO.setEnable(1);
            }
            customsBookItemDO.setAccountNum(submit.getInQty());
            customsBookItemDO.setAvailableNum(submit.getInQty());
            customsBookItemDO.setOccupiedNum(0);
            customsBookItemDO.setUsedNum(0);
            customsBookItemDO.setId(null);
            customsBookItemMapper.insertSelective(customsBookItemDO);
            // 库存变动日志
            this.saveLogs(customsBookItemDO, submit.getInQty(), submit.getInvtNo());
            this.buildTrackLogNewAndSend(customsBookItemDO.getId(), submit.getSource());
        } else {//更新
//            customsBookItemDO.setAccountNum(old.getAccountNum() + submit.getInQty());
            CustomsBookItemDO bookItemDO = customsBookItemMapper.selectByPrimaryKey(submit.getId());
            BeanUtils.copyProperties(submit, bookItemDO, "enable", "accountNum", "occupiedNum", "usedNum", "availableNum");
            bookItemDO.setCustomsBookId(customsBookDTO.getId());
            bookItemDO.setInDate(submit.getInDate() != null ? new Date(submit.getInDate()) : null);
            if (Objects.isNull(submit.getSecondUnit())) {
                bookItemDO.setSecondUnit("");
                bookItemDO.setSecondUnitAmount(null);
            }
            customsBookItemMapper.updateByPrimaryKey(bookItemDO);
            CustomsBookItemDO itemDO = customsBookItemMapper.selectByPrimaryKey(submit.getId());
            CustomsBookItemDTO itemDTO = new CustomsBookItemDTO();
            BeanUtils.copyProperties(itemDO, itemDTO);
            //账册库存变动日志
            this.buildTrackLogDiffAndSend(old, itemDTO);
//            customsBookItemDO.setId(old.getId());
//            OrderInfoDto orderInfoDto = new OrderInfoDto();
//            orderInfoDto.setAccountBookId(old.getCustomsBookId());
//            orderInfoDto.setNum(submit.getInQty());
//            orderInfoDto.setProductId(old.getProductId());
//            orderInfoDto.setGoodsSeqNo(submit.getGoodsSeqNo());
//            try {
//                这里有问题的 会加两次 做的时候要重新设计下
//                storeRedisBaseService.incrementStoreDirect(orderInfoDto);
//                saveLogs(customsBookItemDO, submit.getInQty());
//            } catch (Exception e) {
//                try {
//                    storeRedisBaseService.decrementStoreDirect(orderInfoDto);
//                } catch (Exception e1) {
//                    log.error("[CustomsBookItemServiceImpl-upset] errMsg ={}", e1);
//                }
//            } finally {
//                StockContextUtil.remove();
//            }
        }
        return customsBookItemDO.getId();
    }

    private void saveLogs(CustomsBookItemDO customsBookItemDO, Integer inQty, String invtNo) {
        UpdateInventoryDTO u = new UpdateInventoryDTO();
        u.setCustomsBookItemId(customsBookItemDO.getId());
        String businessNo = (customsBookItemDO.getCustomsBookId() + "_" + customsBookItemDO.getProductId() + "_" + customsBookItemDO.getGoodsSeqNo());
        if (StringUtil.isNotEmpty(invtNo)) {
            EndorsementDTO endorsementDTO = endorsementService.findByRealOrderNo(invtNo);
            if (Objects.nonNull(endorsementDTO)) {
                businessNo = endorsementDTO.getSn();
                u.setCustomsNo(invtNo);
            }
        }
        u.setBusinessNo(businessNo);
        u.setProductId(customsBookItemDO.getProductId());
        u.setChangeType(InventoryChangeTypeEnums.BOOKS_CHECKLIST_RECEIPT);
        u.setDeclareUnitQfy(inQty);
        u.setCustomsBookId(customsBookItemDO.getCustomsBookId());
        u.setProductStockId(customsBookItemDO.getProductStockId());
        u.setUnifiedProductId(customsBookItemDO.getUnifiedProductId());
        inventoryChangeService.saveLogs(u);
    }

    @Override
    @Transactional
    public void submitUpdateList(List<CustomsBookItemSubmit> submitList) throws Exception {
        for (CustomsBookItemSubmit customsBookItemSubmit : submitList) {
            this.upset(customsBookItemSubmit);
        }
    }

    @Override
    public Long upsetEnable(CustomsBookItemSubmit customsBook) throws Exception {
        CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
        customsBookItemDO.setId(customsBook.getId());
        customsBookItemDO.setEnable(customsBook.getEnable());
        customsBookItemMapper.updateByPrimaryKeySelective(customsBookItemDO);
        try {
            this.buildTrackLogEnableAndSend(customsBook.getId(), customsBook.getEnable());
        } catch (Exception e) {
            log.error("upsetEnable 账册库存切换启用禁用失败 error={}", e.getMessage(), e);
        }
        // 缓存操作
        this.cacheOps(customsBook.getId(), customsBook.getEnable());
        return customsBookItemDO.getId();
    }

    /**
     * 更新账册库存禁用启用
     *
     * @param idList
     * @param enable
     * @return
     * @throws Exception
     */
    @Override
    public void upsetEnable(List<Long> idList, Integer enable) throws Exception {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        if (Objects.equals(enable, 1)) {
            this.enableCheck(idList);
        } else {
//            this.disableCheck(idList);
        }
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andIn("id", idList);
        CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
        customsBookItemDO.setEnable(enable);
        UserUtils.setUpdateBy(customsBookItemDO);
        customsBookItemDO.setUpdateTime(new Date());
        customsBookItemMapper.updateByExampleSelective(customsBookItemDO, example);
        for (Long id : idList) {
            try {
                this.buildTrackLogEnableAndSend(id, enable);
            } catch (Exception e) {
                log.error("upsetEnable 账册库存切换启用禁用失败 error={}", e.getMessage(), e);
            }
            // 缓存操作
            this.cacheOps(id, enable);
        }
    }

    /**
     * 账册库存启用校验
     *
     * @param idList
     */
    private void enableCheck(List<Long> idList) {
        Example queryExample = new Example(CustomsBookItemDO.class);
        queryExample.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExample(queryExample);
        List<String> secondUnitErrorList = new ArrayList<>();
        List<String> secondAmountErrorList = new ArrayList<>();
        for (CustomsBookItemDO customsBookItemDO : customsBookItemDOS) {
            if (StringUtil.isEmpty(customsBookItemDO.getSecondUnit()) && Objects.nonNull(customsBookItemDO.getSecondUnitAmount())) {
                secondUnitErrorList.add(customsBookItemDO.getProductId());
            }
            if (StringUtil.isNotEmpty(customsBookItemDO.getSecondUnit()) && Objects.isNull(customsBookItemDO.getSecondUnitAmount())) {
                secondAmountErrorList.add(customsBookItemDO.getProductId());
            }
        }
        List<String> errorMsgList = new ArrayList<>();
        if (CollUtil.isNotEmpty(secondUnitErrorList)) {
            errorMsgList.add("海关备案料号" + JSON.toJSONString(secondUnitErrorList) + ",法二单位为空，无法启用");
        }
        if (CollUtil.isNotEmpty(secondAmountErrorList)) {
            errorMsgList.add("海关备案料号" + JSON.toJSONString(secondAmountErrorList) + ",法二数量为空，无法启用");
        }
        if (CollUtil.isNotEmpty(errorMsgList)) {
            throw new ArgsInvalidException(StringUtil.join(errorMsgList, "\r\n"));
        }
    }

    /**
     * 账册库存禁用校验
     *
     * @param idList
     */
    private void disableCheck(List<Long> idList) {
        Example queryExample = new Example(CustomsBookItemDO.class);
        queryExample.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExample(queryExample);

        Map<String, Long> groupedCounts = customsBookItemDOS.stream()
                .filter(item -> Objects.equals(item.getEnable(), 1))
                .collect(Collectors.groupingBy(
                        item -> String.format("%s#%s", item.getProductId(), item.getCustomsBookId()),
                        Collectors.counting()
                ));

        List<String> errorMsgList = new ArrayList<>();
        groupedCounts.forEach((k, v) -> {
            List<String> split = StrUtil.split(k, '#');
            String productId = split.get(0);
            Long bookId = Long.valueOf(split.get(1));

            Example example = new Example(CustomsBookItemDO.class);
            example.createCriteria()
                    .andEqualTo("productId", productId)
                    .andEqualTo("enable", 1)
                    .andEqualTo("customsBookId", bookId);
            int count = customsBookItemMapper.selectCountByExample(example);
            // 全部禁用
            if (count - v < 1) {
                errorMsgList.add("海关备案料号: " + productId + ",禁用失败，至少保留1条开启的金二序号");
            }
        });
        if (CollUtil.isNotEmpty(errorMsgList)) {
            throw new ArgsInvalidException(StringUtil.join(errorMsgList, "\r\n"));
        }
    }

    @Override
    public Long updBook(CustomsBookItemUpdBookSubmit submit) throws ArgsErrorException {

        if (Objects.equals(submit.getGoodsName(), "")) {
            throw new ArgsErrorException("商品名称不能为空！");
        }
        if (Objects.equals(submit.getGoodsModel(), "")) {
            throw new ArgsErrorException("规格型号不能为空！");
        }
        if (Objects.equals(submit.getGoodsSeqNo(), "")) {
            throw new ArgsErrorException("金二序号不能为空！");
        }
        if (Objects.isNull(submit.getId())) {
            throw new ArgsErrorException("账册库存id为空！");
        }
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectByPrimaryKey(submit.getId());
        try {
            BeanUtils.copyProperties(submit, customsBookItemDO, "customsBookId");
            CustomsBookItemDO before = customsBookItemMapper.selectByPrimaryKey(submit.getId());
            UserUtils.setUpdateBy(customsBookItemDO);
            customsBookItemDO.setUpdateTime(new Date());
            customsBookItemMapper.updateByPrimaryKey(customsBookItemDO);
            CustomsBookItemDO after = customsBookItemMapper.selectByPrimaryKey(submit.getId());
            if (!Objects.equals(before.getGoodsSeqNo(), after.getGoodsSeqNo()) || !Objects.equals(before.getOriginCountry(), after.getOriginCountry())) {
                messageSender.sendMsg(before.getId(), "ccs-goods-record-customs-product-topic");
            }
            //获取更新前后的变化参数
            this.buildTrackLogDiffAndSend(before, after);
        } catch (DuplicateKeyException e) {
            throw new ArgsErrorException("该账册下已存在此金二序号！");
        }
        return customsBookItemDO.getId();
    }


    /**
     * 缓存操作
     *
     * @param id     账册库存主键ID
     * @param enable 0禁用；1启用
     */
    public void cacheOps(Long id, Integer enable) throws Exception {
        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(id);
        if (customsBookItemDTO == null) {
            return;
        }
        OrderInfoDto orderInfoDto = new OrderInfoDto();
        orderInfoDto.setAccountBookId(customsBookItemDTO.getCustomsBookId());
        orderInfoDto.setProductId(customsBookItemDTO.getProductId());
        orderInfoDto.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
        if (Objects.equals(enable, 0)) {
            storeRedisBaseService.delKey(orderInfoDto);
        } else if (Objects.equals(enable, 1)) {
            storeRedisBaseService.initLock(orderInfoDto);
        }
    }

    @Override
    public List<CustomsBookItemDTO> findByBookNoAndProId(String customsBookNo, String productId) throws ArgsErrorException {
        CustomsBookDTO customsBookDTO = customsBookService.findByCode(customsBookNo);
        if (customsBookDTO == null) {
            throw new ArgsErrorException("账册编码错误");
        }
        if (StringUtils.isEmpty(productId)) {
            throw new ArgsErrorException("料号不能为空");
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId)
                .andEqualTo("customsBookId", customsBookDTO.getId());
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (list.size() == 0) {
            return null;
        } else {
            return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
        }
    }

    @Override
//    @Cacheable(value = "ItemWithBookIdAndProId", key = "#customsBookId+'-'+#productId", unless = "#result==null")
    public List<CustomsBookItemDTO> findByBookIdAndProId(Long customsBookId, String productId) throws ArgsErrorException {
        if (Objects.isNull(customsBookId) || StringUtil.isEmpty(productId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId);
        criteria.andEqualTo("customsBookId", customsBookId);
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (list.size() == 0) {
            return new ArrayList<>();
        } else {
            return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
        }
    }

    @Override
    public List<CustomsBookItemDTO> findByProductId(String productId) {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId);
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (list.size() == 0) {
            return null;
        } else {
            return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
        }
    }

    @Override
    public List<CustomsBookItemDTO> findByProductIdList(List<String> productId) {
        if (CollectionUtils.isEmpty(productId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("productId", productId);
        criteria.andEqualTo("deleted", 0);
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        } else {
            return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
        }
    }

    @Override
    public List<CustomsBookItemDTO> findByBookIdAndProIds(Long customsBookId, List<String> productIds) throws ArgsErrorException {
        if (LongUtil.isNone(customsBookId) || CollectionUtils.isEmpty(productIds)) {
            log.warn("findByBookIdAndProIds 料号或账册id为空");
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("productId", productIds);
        criteria.andEqualTo("customsBookId", customsBookId);
        criteria.andEqualTo("deleted", 0);
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (list.size() == 0) {
            return new ArrayList<>();
        } else {
            return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
        }
    }

    @Override
//    @Cacheable(value = "ItemWithBookIdAndSeqNoAndProId", key = "#customsBookId+'-'+#goodsSeqNo+'-'+#productId", unless = "#result==null")
    public CustomsBookItemDTO findByBookIdAndSeqNoAndProId(Long customsBookId, String goodsSeqNo, String productId) throws ArgsErrorException {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("goodsSeqNo", goodsSeqNo)
                .andEqualTo("customsBookId", customsBookId);
        if (Objects.nonNull(productId)) {
            criteria.andEqualTo("productId", productId);
        }
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectOneByExample(example);
        if (customsBookItemDO == null) {
            return null;
        } else {
            CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
            BeanUtils.copyProperties(customsBookItemDO, customsBookItemDTO);
            return customsBookItemDTO;
        }
    }

    @Override
    public List<CustomsBookItemDTO> findByBookIdAndSeqNoList(Long customsBookId, List<String> goodsSeqNoList) {
        if (CollUtil.isEmpty(goodsSeqNoList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("goodsSeqNo", goodsSeqNoList)
                .andEqualTo("customsBookId", customsBookId);
        List<CustomsBookItemDO> customsBookItemList = customsBookItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(customsBookItemList)) {
            return null;
        }
        return customsBookItemList.stream()
                .map(z -> {
                    CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
                    BeanUtils.copyProperties(z, customsBookItemDTO);
                    return customsBookItemDTO;
                }).collect(Collectors.toList());
    }

    @Override
    public CustomsBookItemDTO findByBookIdAndSeqNo(Long customsBookId, String goodsSeqNo) {
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andEqualTo("goodsSeqNo", goodsSeqNo)
                .andEqualTo("customsBookId", customsBookId)
                .andEqualTo("deleted", 0);
        List<CustomsBookItemDO> customsBookItemList = customsBookItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(customsBookItemList)) {
            return null;
        }
        CustomsBookItemDO customsBookItemDO = customsBookItemList.get(0);
        CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
        BeanUtils.copyProperties(customsBookItemDO, customsBookItemDTO);
        return customsBookItemDTO;
    }

    /**
     * 通过账册/料号/序号来查询
     *
     * @param customsBookId 账册ID
     * @param productId     料号
     * @param goodsSeqNo    序号
     * @return
     */
    public CustomsBookItemDO findBookItemDo(Long customsBookId, String productId, String goodsSeqNo) {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsBookId", customsBookId).andEqualTo("productId", productId).andEqualTo("goodsSeqNo", goodsSeqNo);
        return customsBookItemMapper.selectOneByExample(example);
    }

    /**
     * 通过账册/料号/序号来查询
     *
     * @param customsBookId
     * @param productId
     * @param goodsSeqNo
     * @return
     */
    @Override
    public CustomsBookItemDTO findIdByBookIdAndProIdAndSeq(Long customsBookId, String productId, String goodsSeqNo) {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsBookId", customsBookId).andEqualTo("productId", productId).andEqualTo("goodsSeqNo", goodsSeqNo);
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectOneByExample(example);
        if (customsBookItemDO != null) {
            CustomsBookItemDTO itemDTO = new CustomsBookItemDTO();
            BeanUtils.copyProperties(customsBookItemDO, itemDTO);
            return itemDTO;
        }
        return null;
    }

    /**
     * 查找可用的账册
     *
     * @param customsBookId 账册ID
     * @param productId     料号
     * @param goodsSeqNo    序号
     * @return
     */
    @Override
    public CustomsBookItemDTO findEffectiveBook(Long customsBookId, String productId, String goodsSeqNo) {
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("goodsSeqNo", goodsSeqNo)
                .andEqualTo("customsBookId", customsBookId)
                .andEqualTo("productId", productId)
                .andEqualTo("enable", 1)
                .andEqualTo("deleted", 0);
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectOneByExample(example);
        if (customsBookItemDO == null) {
            return null;
        } else {
            CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
            BeanUtils.copyProperties(customsBookItemDO, customsBookItemDTO);
            return customsBookItemDTO;
        }
    }


    /**
     * 检查核注单下 商品可用库存是否全部可用
     * Map<String, Map<String, BigDecimal>>
     * Map<productId,Map<goodsSeqNo,declareUnitQfy>>
     *
     * @param customsBookId
     * @param bookItemIdCount
     * @return
     */
    @Override
    public synchronized String checkAvailableNum(Long customsBookId, Map<String, BigDecimal> bookItemIdCount) {
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andIn("id", bookItemIdCount.keySet());
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExample(example);
        StringBuffer sb = new StringBuffer();
        if (!CollectionUtils.isEmpty(customsBookItemDOS)) {
            for (CustomsBookItemDO c : customsBookItemDOS) {
                BigDecimal declareUnitQfy = bookItemIdCount.get(c.getProductId());
                if (c.getAvailableNum() < 0 || BigDecimal.valueOf(c.getAvailableNum()).subtract(declareUnitQfy).intValue() < 0) {
                    Boolean isEnough = false;
                    example.clear();
                    //找出该账册料号下的其他项号
                    example.createCriteria().andEqualTo("productId", c.getProductId())
                            .andEqualTo("customsBookId", c.getCustomsBookId())
                            .andNotEqualTo("goodsSeqNo", c.getGoodsSeqNo());
                    example.orderBy("goodsSeqNo").desc();
                    List<CustomsBookItemDO> moreCustomsBookItems = customsBookItemMapper.selectByExample(example);
                    //如果该项号不足 取另一个料号的
                    if (!CollectionUtils.isEmpty(moreCustomsBookItems)) {
                        Iterator<CustomsBookItemDO> it = moreCustomsBookItems.iterator();
                        while (it.hasNext()) {
                            CustomsBookItemDO item = it.next();
                            if (BigDecimal.valueOf(item.getAvailableNum()).subtract(declareUnitQfy).compareTo(BigDecimal.ZERO) == 1) {
                                isEnough = true;
                                break;
                            }
                        }
                    }
                    //全部项号都不够抵扣 记录库存不足的料号
                    if (!isEnough) {
                        sb.append("料号:" + c.getProductId() + ";账册可用库存不足;");
                    }

                }

            }
        }
        return sb.toString();
    }

    /**
     * 账册库存日志的分页查询
     *
     * @param search
     * @return
     */
    @Override
    public ListVO<ItemTrackLogEsDTO> pagingTrackLog(ItemTrackLogSearch search) {
        Page<ItemTrackLogEsDO> page = customsBookItemEsDao.paging(search);
        ListVO<ItemTrackLogEsDTO> itemTrackLogEsDTOListVO = new ListVO<>();
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(page.getTotalElements());
        pageResult.setTotalPage(page.getTotalPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        itemTrackLogEsDTOListVO.setPage(pageResult);
        List<ItemTrackLogEsDTO> itemTrackLogEsDTOS = page.getContent().stream().map(p -> {
            ItemTrackLogEsDTO itemTrackLogEsDTO = new ItemTrackLogEsDTO();
            BeanUtils.copyProperties(p, itemTrackLogEsDTO);
            return itemTrackLogEsDTO;
        }).collect(Collectors.toList());
        itemTrackLogEsDTOListVO.setDataList(itemTrackLogEsDTOS);
        return itemTrackLogEsDTOListVO;
    }

    /**
     * 用于保存切换启用/禁用的日志
     *
     * @param customsBookItemId
     * @param enable
     */
    private void buildTrackLogEnableAndSend(Long customsBookItemId, Integer enable) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = this.buildTrackLogEnable(customsBookItemId, enable);
        this.trackLogEsSaveSend(itemTrackLogSaveDTO);
    }

    private ItemTrackLogSaveDTO buildTrackLogEnable(Long customsBookItemId, Integer enable) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = new ItemTrackLogSaveDTO();
        itemTrackLogSaveDTO.setCustomsBookItemId(customsBookItemId);
        if (Objects.nonNull(enable)) {
            switch (enable) {
                case 0:
                    itemTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.DISABLE);
                    itemTrackLogSaveDTO.setOperateDes("库存状态：启用->禁用");
                    break;
                case 1:
                    itemTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.ENABLE);
                    itemTrackLogSaveDTO.setOperateDes("库存状态：禁用->启用");
                    break;
                default:
                    break;
            }
        }
        itemTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        itemTrackLogSaveDTO.setOperateTime(new Date());
        return itemTrackLogSaveDTO;
    }

    /**
     * 新建类型的日志保存
     *
     * @param customsBookItemId
     * @param source
     */
    private void buildTrackLogNewAndSend(Long customsBookItemId, String source) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = this.buildTrackLogNew(customsBookItemId, source);
        this.trackLogEsSaveSend(itemTrackLogSaveDTO);
    }

    private ItemTrackLogSaveDTO buildTrackLogNew(Long customsBookItemId, String source) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = new ItemTrackLogSaveDTO();
        itemTrackLogSaveDTO.setCustomsBookItemId(customsBookItemId);
        itemTrackLogSaveDTO.setOperateType(ItemTrackLogConfig.CREATE);
        if (Objects.nonNull(source)) {
            switch (source) {
                case ItemTrackLogConfig.ITEM_TYPE_IMPORT:
                    itemTrackLogSaveDTO.setOperateDes("导入成功");
                    break;
                case ItemTrackLogConfig.ITEM_TYPE_RECEIVE:
                    itemTrackLogSaveDTO.setOperateDes("记账回执接收成功");
                    break;
                default:
                    break;
            }
        }
        itemTrackLogSaveDTO.setOperator(UserUtils.getUserRealName());
        itemTrackLogSaveDTO.setOperateTime(new Date());
        return itemTrackLogSaveDTO;
    }

    private void buildTrackLogDiffAndSend(CustomsBookItemDO before, CustomsBookItemDO after) {
        CustomsBookItemDTO beforeDTO = new CustomsBookItemDTO();
        CustomsBookItemDTO afterDTO = new CustomsBookItemDTO();
        BeanUtils.copyProperties(before, beforeDTO);
        BeanUtils.copyProperties(after, afterDTO);
        this.buildTrackLogDiffAndSend(beforeDTO, afterDTO);
    }

    /**
     * 修改类型的保存日志 找不同
     *
     * @param before
     * @param after
     */
    private void buildTrackLogDiffAndSend(CustomsBookItemDTO before, CustomsBookItemDTO after) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = this.buildTrackLogDiff(before, after);
        if (Objects.nonNull(itemTrackLogSaveDTO)) {
            if (CollectionUtils.isEmpty(itemTrackLogSaveDTO.getOperateExtra())) {
                log.info("customsBookItemServiceImpl buildTrackLogDiffAndSend 前后比较为空");
                return;
            }
        }
        this.trackLogEsSaveSend(itemTrackLogSaveDTO);
    }

    private ItemTrackLogSaveDTO buildTrackLogDiff(CustomsBookItemDO before, CustomsBookItemDO after) {
        CustomsBookItemDTO beforeDTO = new CustomsBookItemDTO();
        CustomsBookItemDTO afterDTO = new CustomsBookItemDTO();
        BeanUtils.copyProperties(before, beforeDTO);
        BeanUtils.copyProperties(after, afterDTO);
        return this.buildTrackLogDiff(beforeDTO, afterDTO);
    }

    /**
     * 构造dto
     *
     * @param before
     * @param after
     * @return
     */
    private ItemTrackLogSaveDTO buildTrackLogDiff(CustomsBookItemDTO before, CustomsBookItemDTO after) {
        ItemTrackLogSaveDTO dto = new ItemTrackLogSaveDTO();
        dto.setCustomsBookItemId(before.getId());
        dto.setOperateType(ItemTrackLogConfig.UPDATE);
        dto.setOperator(SimpleUserHelper.getRealUserName());
        dto.setOperateTime(new Date());
        CustomsBookItemCompareDTO beforeCompare = new CustomsBookItemCompareDTO();
        CustomsBookItemCompareDTO afterCompare = new CustomsBookItemCompareDTO();
        BeanUtils.copyProperties(before, beforeCompare);
        BeanUtils.copyProperties(after, afterCompare);
        List<ItemTrackLogExtraDTO> itemTrackLogExtraDTOS = TrackLogUtils.generateDiffExtra(beforeCompare, afterCompare, "id", "createTime");
        dto.setOperateExtra(itemTrackLogExtraDTOS);
        return dto;
    }

    /**
     * 发送mq消息解耦
     *
     * @param itemTrackLogSaveDTO
     */
    private void trackLogEsSaveSend(ItemTrackLogSaveDTO itemTrackLogSaveDTO) {
        log.info("trackLogEsSaveSend itemTrackLogSaveDTO={}", JSON.toJSONString(itemTrackLogSaveDTO));
        try {
            messageSender.sendMsg(itemTrackLogSaveDTO, ItemTrackLogConfig.CUSTOMS_BOOK_ITEM_TRACK_LOG_TOPIC);
        } catch (Exception e) {
            log.error("trackLogEsSaveSend error={}", e.getMessage(), e);
            log.error("日志mq发送异常 改为直接调用方法");
            this.trackLogEsSave(itemTrackLogSaveDTO);
        }
    }

    /**
     * 用于保存变动日志
     *
     * @param itemTrackLogSaveDTO
     */
    @Override
    public void trackLogEsSave(ItemTrackLogSaveDTO itemTrackLogSaveDTO) {
        log.info("trackLogEsSave itemTrackLogSaveDTO={}", JSON.toJSONString(itemTrackLogSaveDTO));
        customsBookItemEsDao.esSave(itemTrackLogSaveDTO);
    }

    /**
     * 更新统一料号
     *
     * @param id
     * @param productStockId
     * @param unifiedProductId
     */
    @Override
    public void updateUnifiedProductId(Long id, Long productStockId, String unifiedProductId) {
        CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
        customsBookItemDO.setId(id);
        customsBookItemDO.setProductStockId(productStockId);
        customsBookItemDO.setUnifiedProductId(unifiedProductId);
        customsBookItemMapper.updateByPrimaryKeySelective(customsBookItemDO);
    }

    @Override
    public List<CustomsBookItemDTO> findByCustomsBookRowBounds(Long accountBookId, Integer offset, Integer limit) {
        List<Integer> status = new ArrayList<>(Arrays.asList(0, 2, 4));
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria()
                .andEqualTo("customsBookId", accountBookId)
                //推送状态 200 推送成功 -1推送失败
                .andIn("pushStatus", status)
                .andEqualTo("deleted", false);
        RowBounds rowBounds = new RowBounds(offset, limit);
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExampleAndRowBounds(example, rowBounds);
        return ConvertUtil.listConvert(customsBookItemDOS, CustomsBookItemDTO.class);
    }

    @Override
    public void clearProductStock(Long customsBookItemId, Long productStockId) {
        if (Objects.isNull(customsBookItemId) || Objects.isNull(productStockId)) {
            return;
        }
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectByPrimaryKey(customsBookItemId);
        customsBookItemDO.setProductStockId(null);
        customsBookItemDO.setUnifiedProductId(null);
        customsBookItemMapper.updateByPrimaryKey(customsBookItemDO);
    }

    @Override
    public List<CustomsBookItemDTO> findByBookId(List<Long> bookIdList) {
        if (CollectionUtils.isEmpty(bookIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("customsBookId", bookIdList);
        List<CustomsBookItemDO> customsBookItemDOS = customsBookItemMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsBookItemDOS, CustomsBookItemDTO.class);
    }

    @Override
    public Map<Long, CustomsBookItemDTO> findByIdList(List<Long> collect) {
        if (CollectionUtils.isEmpty(collect)) {
            return null;
        }
        List<CustomsBookItemDTO> customsBookItemDTOS = this.findById(collect);
        Map<Long, CustomsBookItemDTO> bookItemDOMap = customsBookItemDTOS.stream().collect(Collectors.toMap(CustomsBookItemDTO::getId, item -> item, (v1, v2) -> v1));
        return bookItemDOMap;
    }

    /**
     * 用于保存错误信息
     */
    private static String errorMsg;


    /**
     * 结转出核注创建 找出所有库存不足的信息
     *
     * @param list
     */
//    @Override
//    @Transactional(rollbackFor = ArgsErrorException.class)
//    public synchronized void updateInventory(List<UpdateInventoryDTO> list) {
//        if (!CollectionUtils.isEmpty(list)){
//            list.forEach(u -> updateInventory(u));
//        }
//    }

    @Value("${stock.lock.update.effective:false}")
    Boolean stockLockUpdateEffective;

    /**
     * 1、先过滤校验数据
     * 2、根据是否有item主键分成两个list去查询itemDO
     * 3、做批量更新操作
     *
     * @param list
     * @return
     */
    @Override
    public List<ItemLogsIdDTO> updateInventory(List<UpdateInventoryDTO> list) {
        if (!stockLockUpdateEffective) {
            log.info("[CustomsBookItemServiceImpl-updateInventory stockLockUpdateEffective={}]", stockLockUpdateEffective);
            return null;
        }
        log.info("[CustomsBookItemServiceImpl-updateInventory updateInventoryDTOList={}]", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("[CustomsBookItemServiceImpl-updateInventory list为空]");
            return null;
        }
        if (list.parallelStream().anyMatch(u -> u.getCustomsBookId() == null)) {
            throw new ArgsErrorException("账册id为空");
        }
        if (list.parallelStream().anyMatch(u -> u.getBusinessNo() == null)) {
            throw new ArgsErrorException("业务单号为空");
        }
        UpdateInventoryDTO countZeroDto = list.parallelStream().filter(u -> u.getDeclareUnitQfy() == 0).findAny().orElse(null);
        if (countZeroDto != null) {
            throw new ArgsErrorException(countZeroDto.getProductId() + countZeroDto.getChangeType().getChangeType() + ":库存变动数量为0");
        }
        List<UpdateInventoryDTO> itemIdList = new ArrayList<>();
        List<UpdateInventoryDTO> proIdList = new ArrayList<>();
        // 有些有账册库存主键ID有的没有,进行分组查询
        list.forEach(l -> {
            if (l.getCustomsBookItemId() == null && (l.getGoodsSeqNo() == null || StringUtils.isEmpty(l.getGoodsSeqNo()))) {
                log.info("[CustomsBookItemServiceImpl-updateInventory product={}为新品 核注时不进行库存变动]", l.getProductId());
                return;
            }
            if (l.getCustomsBookItemId() != null) {
                itemIdList.add(l);
            } else {
                proIdList.add(l);
            }
        });
        List<CustomsBookItemDO> itemIdDOList = new ArrayList<>();
        //查有主键id的
        if (!CollectionUtils.isEmpty(itemIdList)) {
            Example example = new Example(CustomsBookItemDO.class);
            example.createCriteria().andIn("id", itemIdList.stream().map(UpdateInventoryDTO::getCustomsBookItemId).collect(Collectors.toList()));
            itemIdDOList = customsBookItemMapper.selectByExample(example);
        }
        if (!CollectionUtils.isEmpty(proIdList)) {
            for (UpdateInventoryDTO u : proIdList) {
                CustomsBookItemDO itemDO = this.findBookItemDo(u.getCustomsBookId(), u.getProductId(), u.getGoodsSeqNo());
                if (itemDO == null) {
                    String message = String.format("业务单号：%s，账册：%s，料号：%s，序号：%s，不存在", u.getBusinessNo(), u.getCustomsBookId(), u.getProductId(), u.getGoodsSeqNo());
                    if (u.getChangeType().getIsNotThrowError()) {
                        log.info("业务单号：{}，账册：{}，料号：{}，序号：{}，不存在， 跳过异常，继续执行", u.getBusinessNo(), u.getCustomsBookId(), u.getProductId(), u.getGoodsSeqNo());
                        continue;
                    }
                    log.error(message);
                    throw new ArgsErrorRpcException(message);
                }
                u.setCustomsBookItemId(itemDO.getId()).setProductId(itemDO.getProductId()).setCustomsBookId(itemDO.getCustomsBookId()).setGoodsSeqNo(itemDO.getGoodsSeqNo());
                itemIdDOList.add(itemDO);
            }
        }
        log.info("[CustomsBookItemServiceImpl-updateInventory itemIdDOList={}]", JSON.toJSONString(itemIdDOList));
        //合并两个list 获得过滤新品的后的结果
        itemIdList.addAll(proIdList);
        log.info("[CustomsBookItemServiceImpl-updateInventory itemIdList={}]", JSON.toJSONString(itemIdList));
        //保存日志id
        List<ItemLogsIdDTO> changeIDList = new ArrayList<>();
        //list转map
        Map<Long, CustomsBookItemDO> customsBookItemDOMap = itemIdDOList.stream().collect(Collectors.toMap(CustomsBookItemDO::getId, Function.identity(), (v1, v2) -> v1));
        log.info("[CustomsBookItemServiceImpl-updateInventory customsBookItemDOMap={}]", JSON.toJSONString(customsBookItemDOMap));
        
        // 批量更新库存的数据准备
        List<InventoryChangeSqlDTO> batchUpdateList = new ArrayList<>();
        List<UpdateInventoryDTO> batchLogsList = new ArrayList<>();
        
        for (UpdateInventoryDTO u : itemIdList) {
            if (!customsBookItemDOMap.containsKey(u.getCustomsBookItemId())) {
                log.warn("未找到此主键下账册库存数据，pk：{}，请确认此账册库存是否存在或有效", u.getCustomsBookItemId());
                continue;
            }
            CustomsBookItemDO customsBookItemDO = customsBookItemDOMap.get(u.getCustomsBookItemId());
            InventoryChangeSqlDTO inventoryChangeSqlDTO = new InventoryChangeSqlDTO();
            inventoryChangeSqlDTO.setId(customsBookItemDO.getId());
            inventoryChangeSqlDTO.setChangeNum(u.getDeclareUnitQfy());
            //判断库存变动类型 再进行对应操作
            this.judgeChangeType(u.getChangeType(), inventoryChangeSqlDTO);
            
            // 添加到批量更新列表
            batchUpdateList.add(inventoryChangeSqlDTO);
            
            ItemLogsIdDTO itemAndLogsIdDTO = new ItemLogsIdDTO();
            if (u.getGoodsSeqNo() != null) {
                u.setProductStockId(customsBookItemDO.getProductStockId());
                u.setUnifiedProductId(customsBookItemDO.getUnifiedProductId());
                // 添加到批量日志列表
                batchLogsList.add(u);
                itemAndLogsIdDTO.setCustomsBookItemId(customsBookItemDO.getId());
                changeIDList.add(itemAndLogsIdDTO);
            }
        }
        
        // 批量更新库存
        if (!batchUpdateList.isEmpty()) {
            this.batchUpdateBookItem(batchUpdateList);
        }
        
        // 批量保存日志并更新返回结果
        if (!batchLogsList.isEmpty()) {
            List<Long> logIds = inventoryChangeService.batchSaveLogs(batchLogsList);
            for (int i = 0; i < logIds.size() && i < changeIDList.size(); i++) {
                changeIDList.get(i).setInventoryChangeLogId(logIds.get(i));
            }
        }
        
        return changeIDList;
    }

    /**
     * 批量更新库存
     */
    private void batchUpdateBookItem(List<InventoryChangeSqlDTO> batchUpdateList) {
        // 可以考虑分批处理，避免单次更新数据过多
        int batchSize = 100;
        for (int i = 0; i < batchUpdateList.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, batchUpdateList.size());
            List<InventoryChangeSqlDTO> batch = batchUpdateList.subList(i, endIndex);
            customsBookItemMapper.batchUpdateInventoryNum(batch);
            log.info("[CustomsBookItemServiceImpl-batchUpdateBookItem batch update size={}]", batch.size());
        }
    }

    /**
     * 更新库存 并记录到数据库中
     */
    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public String updateInventory(UpdateInventoryDTO updateInventoryDTO) {
        if (!stockLockUpdateEffective) {
            log.info("[CustomsBookItemServiceImpl-updateInventory stockLockUpdateEffective={}]", stockLockUpdateEffective);
            return null;
        }

        log.info("[CustomsBookItemServiceImpl-updateInventory updateInventoryDTO={}]", JSON.toJSONString(updateInventoryDTO));

        if (updateInventoryDTO.getCustomsBookItemId() == null && updateInventoryDTO.getGoodsSeqNo() == null) {
            log.info("[[CustomsBookItemServiceImpl-updateInventory product={}为新品 核注时不进行库存变动]", updateInventoryDTO.getProductId());
            return null;
        }

        if (StringUtils.isEmpty(updateInventoryDTO.getCustomsBookId())) {
            throw new ArgsErrorException("账册id为空");
        }
        if (StringUtils.isEmpty(updateInventoryDTO.getProductId())) {
            throw new ArgsErrorException("料号为空");
        }
        if (StringUtils.isEmpty(updateInventoryDTO.getBusinessNo())) {
            throw new ArgsErrorException("业务单号为空");
        }
        if (updateInventoryDTO.getDeclareUnitQfy() == 0) {
            throw new ArgsErrorException(updateInventoryDTO.getChangeType().getChangeType() + ":库存变动数量为0");
        }
        errorMsg = null;

        CustomsBookItemDO customsBookItemDO;
        if (updateInventoryDTO.getCustomsBookItemId() != null) {
            customsBookItemDO = customsBookItemMapper.selectByPrimaryKey(updateInventoryDTO.getCustomsBookItemId());
        } else if (updateInventoryDTO.getProductId() != null) {
            //获取ccs_customs_book_item的主键
            customsBookItemDO = getItemDOByUpdateDto(updateInventoryDTO);
        } else {
            throw new ArgsErrorException("未查到该料号的信息!");
        }
        log.info("[CustomsBookItemServiceImpl-updateInventory customsBookItemDO={}]", JSON.toJSONString(customsBookItemDO));

        InventoryChangeSqlDTO inventoryChangeSqlDTO = new InventoryChangeSqlDTO();
        inventoryChangeSqlDTO.setId(customsBookItemDO.getId());
        inventoryChangeSqlDTO.setChangeNum(updateInventoryDTO.getDeclareUnitQfy());
        updateInventoryDTO.setProductId(customsBookItemDO.getProductId()).setCustomsBookId(customsBookItemDO.getCustomsBookId()).setGoodsSeqNo(customsBookItemDO.getGoodsSeqNo());
        updateInventoryDTO.setCustomsBookItemId(customsBookItemDO.getId());
        updateInventoryDTO.setProductStockId(customsBookItemDO.getProductStockId());
        updateInventoryDTO.setUnifiedProductId(customsBookItemDO.getUnifiedProductId());

        //判断库存变动类型 再进行对应操作
        this.judgeChangeType(updateInventoryDTO.getChangeType(), inventoryChangeSqlDTO);
        //更新库存
        this.updateBookItem(inventoryChangeSqlDTO);
        //获取更新后的数据
        log.info("[CustomsBookItemServiceImpl-updateInventory update finish inventoryChangeSqlDTO={}]", JSON.toJSONString(inventoryChangeSqlDTO));
        inventoryChangeService.saveLogs(updateInventoryDTO);

        return errorMsg;

    }

    public CustomsBookItemDO getItemDOByUpdateDto(UpdateInventoryDTO updateInventoryDTO) {
        Example example = new Example(CustomsBookItemDO.class);
        example.orderBy("goodsSeqNo").desc();
        example.createCriteria().andEqualTo("productId", updateInventoryDTO.getProductId())
                .andEqualTo("customsBookId", updateInventoryDTO.getCustomsBookId())
                .andEqualTo("goodsSeqNo", updateInventoryDTO.getGoodsSeqNo())
                .andEqualTo("enable", 1)
                .andEqualTo("deleted", 0);
        return customsBookItemMapper.selectOneByExample(example);
    }


    @Override
    public void updateBookItem(InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        customsBookItemMapper.updateInventoryNum(inventoryChangeSqlDTO);
        log.info("[CustomsBookItemServiceImpl-updateBookItem update={}", JSON.toJSONString(inventoryChangeSqlDTO));
    }

    /**
     * 修改推送状态
     *
     * @param itemDTOS
     */
    @Override
    public void updateBookItemList(List<CustomsBookItemDTO> itemDTOS, Integer pushStatus, String pushMsgId, String pushMsg) {
        if (CollectionUtils.isEmpty(itemDTOS)) {
            return;
        }
        List<Long> idList = itemDTOS.stream().map(CustomsBookItemDTO::getId).distinct().collect(Collectors.toList());
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        CustomsBookItemDO itemDO = new CustomsBookItemDO();
        itemDO.setPushStatus(pushStatus);
        itemDO.setPushMsg(pushMsg);
        itemDO.setPushMsgId(pushMsgId);
        customsBookItemMapper.updateByExampleSelective(itemDO, example);
    }

    @Override
    public void updateByPush(Integer pushStatus, String pushMsgId, String pushMsg) {
        Example example = new Example(CustomsBookItemDO.class);
        example.createCriteria().andEqualTo("pushMsgId", pushMsgId);
        CustomsBookItemDO customsBookItemDO = customsBookItemMapper.selectOneByExample(example);
        if (Objects.nonNull(customsBookItemDO)) {
            CustomsBookItemDO itemDO = new CustomsBookItemDO();
            itemDO.setId(customsBookItemDO.getId());
            itemDO.setPushStatus(pushStatus);
            itemDO.setPushMsg(pushMsg);
            itemDO.setPushMsgId(pushMsgId);
            customsBookItemMapper.updateByPrimaryKeySelective(itemDO);
        }
    }

    @Override
    public List<AssociateCustomsBookItemResVO> viewItemListByProductId(String productId, Long accountBookNoId) {
        if (Objects.isNull(productId)) {
            throw new ArgsErrorException("请输入料号");
        }
        if (Objects.isNull(accountBookNoId)) {
            throw new ArgsErrorException("账册号为空");
        }
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(accountBookNoId, productId);
        List<AssociateCustomsBookItemResVO> list = new ArrayList<>();
        if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
            return list;
        }
        customsBookItemDTOList.forEach(c -> {
            AssociateCustomsBookItemResVO itemResVO = getAssociateCustomsBookItemResVO(c);
            list.add(itemResVO);
        });
        return list;
    }

    private AssociateCustomsBookItemResVO getAssociateCustomsBookItemResVO(CustomsBookItemDTO c) {
        AssociateCustomsBookItemResVO itemResVO = new AssociateCustomsBookItemResVO();
        itemResVO.setCustomsBookItemId(c.getId());
        itemResVO.setProductId(c.getProductId());
        itemResVO.setGoodsSeqNo(c.getGoodsSeqNo());
        itemResVO.setGoodsName(c.getGoodsName());
        if (Objects.nonNull(c.getGoodsUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(c.getGoodsUnit());
            if (Objects.nonNull(customsUomDTO)) {
                itemResVO.setDeclareUnitDesc(customsUomDTO.getCode() + ":" + customsUomDTO.getName());
            }
        }
        if (Objects.nonNull(c.getFirstUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(c.getFirstUnit());
            if (Objects.nonNull(customsUomDTO)) {
                itemResVO.setFirstUnitDesc(customsUomDTO.getCode() + ":" + customsUomDTO.getName());
            }
        }
        if (Objects.nonNull(c.getSecondUnit())) {
            CustomsUomDTO customsUomDTO = customsUomService.findByCode(c.getSecondUnit());
            if (Objects.nonNull(customsUomDTO)) {
                itemResVO.setSecondUnitDesc(customsUomDTO.getCode() + ":" + customsUomDTO.getName());
            }
        }
        if (Objects.nonNull(c.getHsCode())) {
            CustomsHsDTO hsDTO = customsHsService.findByCode(c.getHsCode());
            if (Objects.nonNull(hsDTO)) {
                itemResVO.setHsCodeDesc(hsDTO.getHsCode() + ":" + hsDTO.getHsName());
            }
        }
        if (Objects.nonNull(c.getOriginCountry())) {
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(c.getOriginCountry());
            if (Objects.nonNull(countryDTO)) {
                itemResVO.setOriginCountryDesc(countryDTO.getCode() + ":" + countryDTO.getName());
            }
        }
        if (Objects.nonNull(c.getCurrCode())) {
            CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(c.getCurrCode());
            if (Objects.nonNull(currencyDTO)) {
                itemResVO.setDeclareCurrencyDesc(currencyDTO.getCode() + ":" + currencyDTO.getName());
            }
        }
        itemResVO.setFirstUnitAmount(c.getFirstUnitAmount());
        itemResVO.setSecondUnitAmount(c.getSecondUnitAmount());
        itemResVO.setModel(c.getGoodsModel());
        itemResVO.setDeclarePrice(c.getDeclarePrice());
        itemResVO.setInvtNo(c.getInvtNo());
        return itemResVO;
    }


    private CustomsBookItemDO getBookItemInventory(Long id) {
        return customsBookItemMapper.selectByPrimaryKey(id);
    }

    /**
     * 根据变动类型进行库存变动
     *
     * @param changeType
     */
    private void judgeChangeType(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case BOOKS_CHECKLIST_RECEIPT:
            case ONELINE_IN_ENDORSEMENT_FINISH:
            case ONELINE_IN_ENDORSEMENT_DELETE:
            case NEW_SECTION_IN_FINISH:
            case NEW_SECTION_INNER_IN_FINISH:
            case NEW_SECTION_IN_DELETE:
            case NEW_SECTION_INNER_IN_DELETE:
            case EXCEL_ADJUST_IMPORT_INCREASE:
            case EXCEL_ADJUST_IMPORT_REDUCE:
            case EXCEL_IMPORT:
            case ONELINE_REFUND:
            case BONDED_TO_TRADE:
            case SUBSEQUENCE_TAX:
            case ONELINE_REFUND_DELETE:
            case BONDED_TO_TRADE_DELETE:
            case SUBSEQUENCE_TAX_DELETE:
            case BONDED_ONELINE_IN:
            case INVENTORY_PROFIT:
            case RANDOM_INSPECTION_DECLARATION:
            case BONDED_ONELINE_IN_DELETE:
            case INVENTORY_PROFIT_DELETE:
            case RANDOM_INSPECTION_DECLARATION_DELETE:
            case REFUND_INAREA_EXCEL_IMPORT:
                //增加可用库存
                updateAvailableNum(changeType, inventoryChangeSqlDTO);
                //增加账册库存
                updateAccountNum(changeType, inventoryChangeSqlDTO);
                break;
            case LIST_ORDER_APPLYING:
            case LIST_ORDER_APPLY_CANCEL:
            case CANCEL_LIST_ORDER_APPLY_FINISH:
            case LIST_ORDER_UPDATED:
            case DELETE_APPLY:
                //占用库存 可用库存
                updateOccupiedNum(changeType, inventoryChangeSqlDTO);
                updateAvailableNum(changeType, inventoryChangeSqlDTO);
                break;
            case SECOND_OUT_FINISH:
                updateAccountNum(changeType, inventoryChangeSqlDTO);
                updateOccupiedNum(changeType, inventoryChangeSqlDTO);
                updateUsedNum(changeType, inventoryChangeSqlDTO);
                break;
            case REFUND_INAREA:
            case NEW_SECTION_OUT_DELETE:
            case NEW_SECTION_INNER_OUT_DELETE:
            case NEW_SECTION_OUT_FINISH:
            case NEW_SECTION_INNER_OUT_FINISH:
            case DESTORY_FINISH:
            case DESTORY_DELETE:
            case SIMPLE_PROCESSING:
            case SIMPLE_PROCESSING_DELETE:
            case SECONDE_OUT_EXCEL_IMPORT:
                updateAvailableNum(changeType, inventoryChangeSqlDTO);
                updateAccountNum(changeType, inventoryChangeSqlDTO);
                updateUsedNum(changeType, inventoryChangeSqlDTO);
                break;
            case LOCK_STOCK:
            case RELEASE_LOCK_STOCK:
                updateAvailableNum(changeType, inventoryChangeSqlDTO);
                updateLockNum(changeType, inventoryChangeSqlDTO);
                break;
            default:
                throw new ArgsErrorException(UNKNOWN_INVENTORY_CHANGED);
        }

    }

    private final static String UNKNOWN_INVENTORY_CHANGED = "未知库存变动类型";
    private final static String ACCOUNTNUM_ERROR = "变动后账册库存小于0;";
    private final static String ONGOINGNUM_ERROR = "变动后在途库存小于0;";
    private final static String LOCKEDNUM_ERROR = "变动后锁定库存小于0;";
    private final static String OCCUPIEDNUM_ERROR = "变动后占用库存小于0;";
    private final static String USEDNUM_ERROR = "变动后已用库存小于0;";
    private final static String AVAILABLENUM_ERROR = "变动后可用库存小于0;";

    /**
     * 更新账册库存
     *
     * @param changeType
     * @param inventoryChangeSqlDTO
     */
    private void updateAccountNum(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case BOOKS_CHECKLIST_RECEIPT:
            case ONELINE_IN_ENDORSEMENT_FINISH:
            case NEW_SECTION_IN_FINISH:
            case NEW_SECTION_INNER_IN_FINISH:
            case REFUND_INAREA:
            case NEW_SECTION_OUT_DELETE:
            case NEW_SECTION_INNER_OUT_DELETE:
            case EXCEL_ADJUST_IMPORT_INCREASE:
                //就加负数吧 懒得改了
            case EXCEL_ADJUST_IMPORT_REDUCE:
            case EXCEL_IMPORT:
            case ONELINE_REFUND_DELETE:
            case BONDED_TO_TRADE_DELETE:
            case SUBSEQUENCE_TAX_DELETE:
            case DESTORY_DELETE:
            case BONDED_ONELINE_IN:
            case INVENTORY_PROFIT:
            case RANDOM_INSPECTION_DECLARATION_DELETE:
            case SIMPLE_PROCESSING_DELETE:
            case REFUND_INAREA_EXCEL_IMPORT:
                inventoryChangeSqlDTO.setAccountFlag(true);
                log.info("[CustomsBookItemServiceImpl-updateAccountNum id={} accountFlag=true]", inventoryChangeSqlDTO.getId());
                break;
            case ONELINE_IN_ENDORSEMENT_DELETE:
            case NEW_SECTION_IN_DELETE:
            case NEW_SECTION_INNER_IN_DELETE:
            case NEW_SECTION_OUT_FINISH:
            case NEW_SECTION_INNER_OUT_FINISH:
            case SECOND_OUT_FINISH:
            case DESTORY_FINISH:
            case ONELINE_REFUND:
            case BONDED_TO_TRADE:
            case SUBSEQUENCE_TAX:
            case BONDED_ONELINE_IN_DELETE:
            case INVENTORY_PROFIT_DELETE:
            case RANDOM_INSPECTION_DECLARATION:
            case SIMPLE_PROCESSING:
            case SECONDE_OUT_EXCEL_IMPORT:
                inventoryChangeSqlDTO.setAccountFlag(false);
                log.info("[CustomsBookItemServiceImpl-updateAccountNum id={} accountFlag=false]", inventoryChangeSqlDTO.getId());
            default:
                return;
        }

    }

    /**
     * 更新占用库存
     *
     * @param changeType
     */
    private void updateOccupiedNum(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case LIST_ORDER_APPLYING:
                inventoryChangeSqlDTO.setOccupiedFlag(true);
                log.info("[CustomsBookItemServiceImpl-updateOccupiedNum id={} occupiedFlag=true]", inventoryChangeSqlDTO.getId());
                break;
            case LIST_ORDER_APPLY_CANCEL:
            case LIST_ORDER_UPDATED:
            case CANCEL_LIST_ORDER_APPLY_FINISH:
            case SECOND_OUT_FINISH:
            case DELETE_APPLY:
                inventoryChangeSqlDTO.setOccupiedFlag(false);
                log.info("[CustomsBookItemServiceImpl-updateOccupiedNum id={} occupiedFlag=false]", inventoryChangeSqlDTO.getId());
                break;
            default:
                return;
        }
    }

    /**
     * 更新已用库存
     *
     * @param changeType
     */
    private void updateUsedNum(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case NEW_SECTION_OUT_FINISH:
            case NEW_SECTION_INNER_OUT_FINISH:
            case SECOND_OUT_FINISH:
            case DESTORY_FINISH:
            case SIMPLE_PROCESSING:
            case SECONDE_OUT_EXCEL_IMPORT:
                inventoryChangeSqlDTO.setUsedFlag(true);
                log.info("[CustomsBookItemServiceImpl-updateUsedNum id={} usedFlag=true]", inventoryChangeSqlDTO.getId());
                break;
            case REFUND_INAREA:
            case NEW_SECTION_OUT_DELETE:
            case NEW_SECTION_INNER_OUT_DELETE:
            case DESTORY_DELETE:
            case SIMPLE_PROCESSING_DELETE:
                inventoryChangeSqlDTO.setUsedFlag(false);
                log.info("[CustomsBookItemServiceImpl-updateUsedNum id={} usedFlag=false]", inventoryChangeSqlDTO.getId());
                break;
            default:
                return;
        }
    }


    /**
     * 更新可用库存
     *
     * @param changeType
     */
    private void updateAvailableNum(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case BOOKS_CHECKLIST_RECEIPT:
            case ONELINE_IN_ENDORSEMENT_FINISH:
            case NEW_SECTION_IN_FINISH:
            case NEW_SECTION_INNER_IN_FINISH:
            case REFUND_INAREA:
            case NEW_SECTION_OUT_DELETE:
            case NEW_SECTION_INNER_OUT_DELETE:
            case LIST_ORDER_APPLY_CANCEL:
            case LIST_ORDER_UPDATED:
            case CANCEL_LIST_ORDER_APPLY_FINISH:
                //手动调整允许加负数 所以给true
            case EXCEL_ADJUST_IMPORT_INCREASE:
            case EXCEL_ADJUST_IMPORT_REDUCE:
            case EXCEL_IMPORT:
            case DELETE_APPLY:
            case ONELINE_REFUND_DELETE:
            case BONDED_TO_TRADE_DELETE:
            case SUBSEQUENCE_TAX_DELETE:
            case DESTORY_DELETE:
            case BONDED_ONELINE_IN:
            case INVENTORY_PROFIT:
            case RANDOM_INSPECTION_DECLARATION_DELETE:
            case SIMPLE_PROCESSING_DELETE:
            case RELEASE_LOCK_STOCK:
            case REFUND_INAREA_EXCEL_IMPORT:
                inventoryChangeSqlDTO.setAvailableFlag(true);
                log.info("[CustomsBookItemServiceImpl-updateAvailableNum id={} availableFlag=true]", inventoryChangeSqlDTO.getId());
                break;
            case ONELINE_IN_ENDORSEMENT_DELETE:
            case NEW_SECTION_IN_DELETE:
            case NEW_SECTION_INNER_IN_DELETE:
            case NEW_SECTION_OUT_FINISH:
            case NEW_SECTION_INNER_OUT_FINISH:
            case LIST_ORDER_APPLYING:
            case DESTORY_FINISH:
            case ONELINE_REFUND:
            case BONDED_TO_TRADE:
            case SUBSEQUENCE_TAX:
            case BONDED_ONELINE_IN_DELETE:
            case INVENTORY_PROFIT_DELETE:
            case RANDOM_INSPECTION_DECLARATION:
            case SIMPLE_PROCESSING:
            case LOCK_STOCK:
            case SECONDE_OUT_EXCEL_IMPORT:
                inventoryChangeSqlDTO.setAvailableFlag(false);
                log.info("[CustomsBookItemServiceImpl-updateAvailableNum id={} availableFlag=false]", inventoryChangeSqlDTO.getId());
                break;
            default:
                return;
        }
    }

    /**
     * 更新锁定库存
     *
     * @param changeType
     */
    private void updateLockNum(InventoryChangeTypeEnums changeType, InventoryChangeSqlDTO inventoryChangeSqlDTO) {
        switch (changeType) {
            case LOCK_STOCK:
                inventoryChangeSqlDTO.setLockedFlag(true);
                log.info("[CustomsBookItemServiceImpl-updateLockNum id={} lockedFlag=true]", inventoryChangeSqlDTO.getId());
                break;
            case RELEASE_LOCK_STOCK:
                inventoryChangeSqlDTO.setLockedFlag(false);
                log.info("[CustomsBookItemServiceImpl-updateLockNum id={} lockedFlag=false]", inventoryChangeSqlDTO.getId());
                break;
        }
    }

    /**
     * 账册库存-锁定库存-占用库存-已用库存+在途库存
     *
     * @param cbid
     * @param changeNum
     * @return
     */
    private boolean checkAvailableNumIsEnough(CustomsBookItemDO cbid, Integer changeNum) {
        if (cbid.getInQty() - cbid.getLockedNum() - cbid.getOccupiedNum() - cbid.getUsedNum() + cbid.getOngoingNum() - changeNum < 0) {
            return true;
        }
        return false;
    }

    /**
     * 更新库存流水的总数量
     *
     * @param customsBookItemDTO
     */
    @Override
    public void updateTotalInOutQty(CustomsBookItemDTO customsBookItemDTO, String inventoryInOutFlag, Integer changeNum) {
        log.info("CustomsBookItemServiceImpl updateTotalInOutQty customsBookItemDTO JSON：{},{},{}", JSON.toJSONString(customsBookItemDTO), inventoryInOutFlag, changeNum);
        //查找账册库存
        CustomsBookItemDTO customsBookItemDTOTemp = this.findByBookIdAndSeqNoAndProId(customsBookItemDTO.getCustomsBookId(), customsBookItemDTO.getGoodsSeqNo(), customsBookItemDTO.getProductId());
        log.info("CustomsBookItemServiceImpl updateTotalInOutQty customsBookItemDTOTemp JSON：{}", JSON.toJSONString(customsBookItemDTOTemp));
        if (Objects.nonNull(customsBookItemDTOTemp)) {
            if (Objects.equals(inventoryInOutFlag, InventoryInOutEnum.IN.getCode())) {
                customsBookItemMapper.updateTotalInQtyNum(customsBookItemDTOTemp.getId(), changeNum);
            } else if (Objects.equals(inventoryInOutFlag, InventoryInOutEnum.OUT.getCode())) {
                customsBookItemMapper.updateTotalOutQtyNum(customsBookItemDTOTemp.getId(), changeNum);
            }
        }
    }

    @Override
    public List<CustomsBookItemDTO> findEnableByBookIdAndProIds(Long customsBookId, List<String> productIds) {
        if (LongUtil.isNone(customsBookId) || CollectionUtils.isEmpty(productIds)) {
            log.warn("findByBookIdAndProIds 料号或账册id为空");
            return new ArrayList<>();
        }
        Example example = new Example(CustomsBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("productId", productIds);
        criteria.andEqualTo("customsBookId", customsBookId);
        criteria.andEqualTo("enable", 1);
        criteria.andEqualTo("deleted", 0);
        List<CustomsBookItemDO> list = customsBookItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        return JSON.parseArray(JSON.toJSONString(list), CustomsBookItemDTO.class);
    }

    @Override
    public ListVO<CustomsBookItemAdjustLogDTO> pagingAdjustLog(CustomsBookItemAdjustLogSearch search) {
        return customsBookItemAdjustLogService.paging(search);
    }

    @Override
    public void updLawAmountByProIdAndBookId(String productId, Long customsBookId, String firstUnit, BigDecimal firstUnitAmount,
                                             String secondUnit, BigDecimal secondUnitAmount) {
        if (Objects.isNull(customsBookId) || StringUtils.isEmpty(productId)) {
            return;
        }
        log.info("更新账册库存法定数量 料号-{}，账册id-{}，法一数量-{}，法二数量-{} ", productId, customsBookId, firstUnitAmount, secondUnitAmount);
        List<CustomsBookItemDTO> customsBookItemDTOS = this.findByBookIdAndProId(customsBookId, productId);
        for (CustomsBookItemDTO bookItemDTO : customsBookItemDTOS) {
            if (Objects.equals(bookItemDTO.getFirstUnit(), firstUnit) && Objects.equals(bookItemDTO.getSecondUnit(), secondUnit)) {
                CustomsBookItemDO customsBookItemDO = new CustomsBookItemDO();
                customsBookItemDO.setId(bookItemDTO.getId());
                customsBookItemDO.setFirstUnitAmount(firstUnitAmount);
                customsBookItemDO.setSecondUnitAmount(secondUnitAmount);
                customsBookItemMapper.updateByPrimaryKeySelective(customsBookItemDO);
                log.info("更新成功，账册id：{},产品id：{},法一单位：{},法二单位：{},更新前法一数量：{},法一数量：{},更新前法二数量：{},法二数量：{}",
                        bookItemDTO.getCustomsBookId(), bookItemDTO.getProductId(),
                        firstUnit, secondUnit, bookItemDTO.getFirstUnitAmount(), firstUnitAmount, bookItemDTO.getSecondUnitAmount(), secondUnitAmount);
                //用于生成对比日志
                CustomsBookItemDO oldItemLogDO = new CustomsBookItemDO();
                oldItemLogDO.setId(bookItemDTO.getId());
                oldItemLogDO.setFirstUnitAmount(bookItemDTO.getFirstUnitAmount());
                oldItemLogDO.setSecondUnitAmount(bookItemDTO.getSecondUnitAmount());
                this.buildTrackLogDiffAndSend(oldItemLogDO, customsBookItemDO);
            } else {
                log.error("更新失败，法一/法二单位不一致");
                this.buildTrackLogUpdateErrorLogAndSend(bookItemDTO, "更新失败，法一/法二单位不一致");
            }
        }
    }

    private void buildTrackLogUpdateErrorLogAndSend(CustomsBookItemDTO customsBookItemDTO, String errorMsg) {
        ItemTrackLogSaveDTO itemTrackLogSaveDTO = new ItemTrackLogSaveDTO();
        itemTrackLogSaveDTO.setCustomsBookItemId(customsBookItemDTO.getId());
        itemTrackLogSaveDTO.setOperateType("商品备案更新");
        itemTrackLogSaveDTO.setOperator(SimpleUserHelper.getRealUserName());
        itemTrackLogSaveDTO.setOperateTime(new Date());
        itemTrackLogSaveDTO.setOperateDes(errorMsg);
        this.trackLogEsSaveSend(itemTrackLogSaveDTO);
    }

}
