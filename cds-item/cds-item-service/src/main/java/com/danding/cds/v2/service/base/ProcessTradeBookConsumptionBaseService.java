package com.danding.cds.v2.service.base;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.bean.dao.ProcessTradeBookConsumptionDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.mapper.ProcessTradeBookConsumptionMapper;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 加工贸易账册单耗信息表 baseService 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class ProcessTradeBookConsumptionBaseService {

    @Resource
    private ProcessTradeBookConsumptionMapper mapper;

    public ProcessTradeBookConsumptionDO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookConsumptionDO> findById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andIn("id", idList);
        return this.selectByExample(example);
    }

    public ProcessTradeBookConsumptionDO selectByPrimaryKey(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookConsumptionDO> selectByExample(Example example) {
        return mapper.selectByExample(example);
    }

    public ProcessTradeBookConsumptionDO selectOneByExample(Example example) {
        return mapper.selectOneByExample(example);
    }

    public void updateByPrimaryKey(ProcessTradeBookConsumptionDO record) {
        mapper.updateByPrimaryKey(record);
    }

    public void updateByPrimaryKeySelective(ProcessTradeBookConsumptionDO record) {
        mapper.updateByPrimaryKeySelective(record);
    }

    public void updateByExample(ProcessTradeBookConsumptionDO record, Example example) {
        mapper.updateByExample(record, example);
    }

    public void updateByExampleSelective(ProcessTradeBookConsumptionDO record, Example example) {
        mapper.updateByExampleSelective(record, example);
    }

    public Integer selectCountByExample(Example example) {
        return mapper.selectCountByExample(example);
    }

    public void insertSelective(ProcessTradeBookConsumptionDO record) {
        UserUtils.setCommonData(record);
        mapper.insertSelective(record);
    }

    @PageSelect
    public ListVO<ProcessTradeBookConsumptionDTO> paging(ProcessTradeBookConsumptionSearch search) {
        Example example = this.buildExample(search);
        List<ProcessTradeBookConsumptionDO> list = mapper.selectByExample(example);
        // 分页
        ListVO<ProcessTradeBookConsumptionDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, ProcessTradeBookConsumptionDTO.class));
        PageInfo<ProcessTradeBookConsumptionDTO> pageInfo = new PageInfo(list);
        pageInfo.setPageSize(search.getPageSize());
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example buildExample(ProcessTradeBookConsumptionSearch search) {
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.setOrderByClause("create_time DESC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (Objects.isNull(search.getRefBookId())) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        criteria.andEqualTo("refBookId", search.getRefBookId());
        if (CollUtil.isNotEmpty(search.getEndPrdIdList())) {
            criteria.andEqualTo("endPrdId", search.getEndPrdIdList());
        }
        if (CollUtil.isNotEmpty(search.getMtpckIdList())) {
            criteria.andIn("mtpckId", search.getMtpckIdList());
        }
        if (Objects.nonNull(search.getDeclareStatus())) {
            criteria.andEqualTo("declareStatus", search.getDeclareStatus());
        }
        return example;
    }

    public void deleteById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andIn("id", idList);
        ProcessTradeBookConsumptionDO updateDO = new ProcessTradeBookConsumptionDO();
        updateDO.setDeleted(true);
        UserUtils.setUpdateBy(updateDO);
        updateDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(updateDO, example);
    }

    public void deleteByItemId(List<Long> itemIdList) {
        if (CollUtil.isEmpty(itemIdList)) {
            return;
        }
        Example example = new Example(ProcessTradeBookConsumptionDO.class);
        example.createCriteria().andIn("endPrdId", itemIdList).orIn("mtpckId", itemIdList);
        ProcessTradeBookConsumptionDO updateDO = new ProcessTradeBookConsumptionDO();
        updateDO.setDeleted(true);
        UserUtils.setUpdateBy(updateDO);
        updateDO.setUpdateTime(new Date());
        mapper.updateByExampleSelective(updateDO, example);
    }

    public void insertList(List<ProcessTradeBookConsumptionDO> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }
        list.forEach(UserUtils::setCommonData);
        mapper.insertList(list);
    }
}