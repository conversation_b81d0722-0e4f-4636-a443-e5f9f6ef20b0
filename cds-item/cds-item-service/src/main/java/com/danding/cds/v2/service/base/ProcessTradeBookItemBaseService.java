package com.danding.cds.v2.service.base;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.bean.dao.ProcessTradeBookItemDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.mapper.ProcessTradeBookItemMapper;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 加工贸易账册表体 baseService 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class ProcessTradeBookItemBaseService {

    @Resource
    private ProcessTradeBookItemMapper mapper;

    // region
    public ProcessTradeBookItemDO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookItemDO> findById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andIn("id", idList);
        return this.selectByExample(example);
    }

    public ProcessTradeBookItemDO selectByPrimaryKey(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookItemDO> selectByExample(Example example) {
        return mapper.selectByExample(example);
    }

    public ProcessTradeBookItemDO selectOneByExample(Example example) {
        return mapper.selectOneByExample(example);
    }

    public void updateByPrimaryKey(ProcessTradeBookItemDO record) {
        mapper.updateByPrimaryKey(record);
    }

    public void updateByPrimaryKeySelective(ProcessTradeBookItemDO record) {
        mapper.updateByPrimaryKeySelective(record);
    }

    public void updateByExample(ProcessTradeBookItemDO record, Example example) {
        mapper.updateByExample(record, example);
    }

    public void updateByExampleSelective(ProcessTradeBookItemDO record, Example example) {
        mapper.updateByExampleSelective(record, example);
    }

    public Integer selectCountByExample(Example example) {
        return mapper.selectCountByExample(example);
    }

    public void insertSelective(ProcessTradeBookItemDO record) {
        UserUtils.setCommonData(record);
        mapper.insertSelective(record);
    }
    // endregion

    @PageSelect
    public ListVO<ProcessTradeBookItemDTO> paging(ProcessTradeBookItemSearch search) {
        Example example = this.buildExample(search);
        List<ProcessTradeBookItemDO> list = mapper.selectByExample(example);
        // 分页
        ListVO<ProcessTradeBookItemDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, ProcessTradeBookItemDTO.class));
        PageInfo<ProcessTradeBookItemDTO> pageInfo = new PageInfo(list);
        pageInfo.setPageSize(search.getPageSize());
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    public List<ProcessTradeBookItemDTO> pagingList(ProcessTradeBookItemSearch search) {
        Example example = this.buildExample(search);
        List<ProcessTradeBookItemDO> list = mapper.selectByExample(example);
        return ConvertUtil.listConvert(list, ProcessTradeBookItemDTO.class);
    }

    private Example buildExample(ProcessTradeBookItemSearch search) {
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.setOrderByClause("seq_no DESC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (Objects.isNull(search.getRefBookId())) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        if (Objects.isNull(search.getGoodsType())) {
            throw new ArgsInvalidException("商品类型不能为空");
        }
        criteria.andEqualTo("refBookId", search.getRefBookId());
        criteria.andEqualTo("goodsType", search.getGoodsType());
        if (Objects.nonNull(search.getProductId())) {
            criteria.andEqualTo("productId", search.getProductId());
        }
        if (Objects.nonNull(search.getHsCode())) {
            criteria.andEqualTo("hsCode", search.getHsCode());
        }
        if (Objects.nonNull(search.getGoodsName())) {
            criteria.andLike("goodsName", "%" + search.getGoodsName() + "%");
        }
        if (Objects.nonNull(search.getSeqNo())) {
            criteria.andEqualTo("seqNo", search.getSeqNo());
        }
        return example;
    }

    public void deleteById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return;
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andIn("id", idList);
        ProcessTradeBookItemDO updateDO = new ProcessTradeBookItemDO();
        updateDO.setDeleted(true);
        mapper.updateByExampleSelective(updateDO, example);
    }

    public void deleteByBookIdAndGoodsType(Long bookId, Integer goodsType) {
        if (Objects.isNull(bookId)) {
            return;
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.createCriteria().andEqualTo("refBookId", bookId).andEqualTo("goodsType", goodsType);
        ProcessTradeBookItemDO updateDO = new ProcessTradeBookItemDO();
        updateDO.setDeleted(true);
        mapper.updateByExampleSelective(updateDO, example);
    }

    public Integer getNextSeqNo(Long bookId, Integer goodsType) {
        Example example = new Example(ProcessTradeBookItemDO.class);
        example.setOrderByClause("seq_no DESC limit 1");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refBookId", bookId);
        criteria.andEqualTo("goodsType", goodsType);
        criteria.andEqualTo("deleted", false);
        ProcessTradeBookItemDO itemDO = mapper.selectOneByExample(example);
        if (Objects.isNull(itemDO)) {
            return 1;
        }
        return itemDO.getSeqNo() + 1;
    }

    public List<ProcessTradeBookItemDO> findByBookIdAndType(Long bookId, Integer goodsType, Integer startSeqNo) {
        if (Objects.isNull(bookId) || Objects.isNull(goodsType)) {
            return new ArrayList<>();
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refBookId", bookId);
        criteria.andEqualTo("goodsType", goodsType);
        if (Objects.nonNull(startSeqNo)) {
            criteria.andGreaterThan("seqNo", startSeqNo);
        }
        criteria.andEqualTo("deleted", false);
        return mapper.selectByExample(example);
    }

    public List<ProcessTradeBookItemDO> findByBookIdAndProductId(Long refBookId, String productId) {
        if (Objects.isNull(refBookId)) {
            return new ArrayList<>();
        }
        Example example = new Example(ProcessTradeBookItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refBookId", refBookId);
        criteria.andEqualTo("productId", productId);
        criteria.andEqualTo("deleted", false);
        return mapper.selectByExample(example);
    }

    public void batchUpdate(List<ProcessTradeBookItemDO> updateDOList) {
        if (CollUtil.isEmpty(updateDOList)) {
            return;
        }
        mapper.batchUpdateByPrimaryKey(updateDOList);
    }

    public void insertList(List<ProcessTradeBookItemDO> processTradeBookItemDOS) {
        if (CollUtil.isEmpty(processTradeBookItemDOS)) {
            return;
        }
        processTradeBookItemDOS.forEach(UserUtils::setCommonData);
        mapper.insertList(processTradeBookItemDOS);
    }
}