package com.danding.cds.v2.mq;


import com.alibaba.fastjson.JSON;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeDTO;
import com.danding.cds.v2.service.AiRecommendHsCodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-ai-recommend-hsCode-choose-consumer",
        topic = "ccs-ai-recommend-hsCode-choose-topic"
)
public class AiRecommendHsCodeChooseConsumer extends MessageHandlerAfterInit {

    @DubboReference
    private AiRecommendHsCodeService aiRecommendHsCodeService;

    @Override
    public void handle(Object param) throws RuntimeException {
        log.info("AiRecommendHsCodeDiscardConsumer handle param={}", param);
        try {
            AiRecommendHsCodeDTO aiRecommendHsCodeDTO = JSON.parseObject((String) param, AiRecommendHsCodeDTO.class);
            aiRecommendHsCodeService.choose(aiRecommendHsCodeDTO.getRecordId(), aiRecommendHsCodeDTO.getCustomsCode(), aiRecommendHsCodeDTO.getRecommendHsCode());
        } catch (Exception e) {
            log.error("AiRecommendHsCodeDiscardConsumer handle param={}, error={}", param, e.getMessage(), e);
        }
    }
}
