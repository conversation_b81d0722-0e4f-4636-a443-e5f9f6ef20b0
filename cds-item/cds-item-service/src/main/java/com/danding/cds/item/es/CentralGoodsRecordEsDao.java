package com.danding.cds.item.es;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.item.api.dto.CentralGoodsRecordSearchCondition;
import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.entity.es.GoodsRecordEsDO;
import com.danding.cds.out.bean.enums.CentralRecordStatusEnum;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.common.es.annotations.EsDao;
import com.danding.common.es.dao.AbstractEsDao;
import com.danding.core.tenant.SimpleTenantHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商品备案中心化
 * @date 2023/8/26
 */
@EsDao
@Slf4j
@Repository
public class CentralGoodsRecordEsDao extends AbstractEsDao<GoodsRecordEsDO> {

    @DubboReference
    private GoodsRecordService goodsRecordService;

    public Page<GoodsRecordEsDO> paging(CentralGoodsRecordSearchCondition search) {
        BoolQueryBuilder boolQueryBuilder = getBoolQueryBuilder(search);

        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(search.getCurrentPage() - 1, search.getPageSize())).build();
//        log.info("ESGoodsRecord query:{}", JSONUtil.toJsonStr(searchQuery.getQuery().toString().replaceAll("\n\t", "")));
        Page<GoodsRecordEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, GoodsRecordEsDO.class);
//        log.info("ES备案查询 result:{}", JSONUtil.toJsonStr(page));
        return page;
    }

    /**
     * 状态统计
     *
     * @param condition
     * @param statusList
     * @return
     */
    public CentralGoodsRecordStatusCount countGoodsRecord(CentralGoodsRecordSearchCondition condition, List<Integer> statusList) {
        CentralGoodsRecordStatusCount count = new CentralGoodsRecordStatusCount();
        for (Integer i : statusList) {
            CentralRecordStatusEnum centralRecordStatusEnum = CentralRecordStatusEnum.getEnums(i);
            if (Objects.isNull(centralRecordStatusEnum) || Objects.equals(centralRecordStatusEnum, CentralRecordStatusEnum.NULL)) {
                continue;
            }
            condition.setRecordStatus(i);
            condition.setCurrentPage(1);
            condition.setPageSize(20);
            BoolQueryBuilder boolQueryBuilder = getBoolQueryBuilder(condition);
            SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(condition.getCurrentPage() - 1, condition.getPageSize())).build();
            Page<GoodsRecordEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, GoodsRecordEsDO.class);
            if (Objects.equals(centralRecordStatusEnum, CentralRecordStatusEnum.WAIT_COMMIT)) {
                count.setWaitCommitCount((int) page.getTotalElements());
            } else if (Objects.equals(centralRecordStatusEnum, CentralRecordStatusEnum.WAIT_EXAMINE)) {
                count.setWaitExamineCount((int) page.getTotalElements());
            } else if (Objects.equals(centralRecordStatusEnum, CentralRecordStatusEnum.FINISH)) {
                count.setFinishCount((int) page.getTotalElements());
            } else if (Objects.equals(centralRecordStatusEnum, CentralRecordStatusEnum.REJECT)) {
                count.setRejectCount((int) page.getTotalElements());
            }
        }

        return count;
    }


    private BoolQueryBuilder getBoolQueryBuilder(CentralGoodsRecordSearchCondition search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));

        //saas租户
        Long tenantryId = SimpleTenantHelper.getTenantId();
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }

        //用户
        if (Objects.nonNull(search.getUserId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantId", search.getUserId()));
        }

        //备案名称
        if (Objects.nonNull(search.getGoodsRecordName())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("goodsRecordName", "*" + search.getGoodsRecordName() + "*"));
        }
        //只展示商家端数据
        boolQueryBuilder.filter(QueryBuilders.matchQuery("goodsSource", "1"));

        //过滤erp删除tag的数据
        Integer goodsRecordTag = GoodsRecordTagEnums.ERP_DELETE.getCode();
        QueryBuilder deleteTagFilterQuery = QueryBuilders.boolQuery()
                .mustNot(QueryBuilders.matchQuery("goodsRecordTagList", goodsRecordTag));
        boolQueryBuilder.must(deleteTagFilterQuery);

        //备案状态
        Integer recordStatus = search.getRecordStatus();
        CentralRecordStatusEnum centralRecordStatusEnum = CentralRecordStatusEnum.getEnums(recordStatus);
        //做成switch是为了之后便于拓展条件查询
        if (Objects.nonNull(centralRecordStatusEnum)) {
            switch (centralRecordStatusEnum) {
                case WAIT_EXAMINE:
                    boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 1));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("erpCommitStatus", 1));
                    break;
                case FINISH:
                    boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 2));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("erpCommitStatus", 1));
                    break;
                case REJECT:
                    boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 4));
                    boolQueryBuilder.mustNot(QueryBuilders.termQuery("erpCommitStatus", 1));
                    break;
                case WAIT_COMMIT:
                    BoolQueryBuilder waitCommitQuery = new BoolQueryBuilder();
                    waitCommitQuery.should(QueryBuilders.termQuery("recordStatus", 8));
                    waitCommitQuery.should(QueryBuilders.termQuery("erpCommitStatus", 1));
                    waitCommitQuery.should(QueryBuilders.nestedQuery("esRecordCustomsDO",
                            QueryBuilders.boolQuery()
                                    .should(QueryBuilders.termQuery("esRecordCustomsDO.status", 8)),
                            ScoreMode.None));
                    boolQueryBuilder.filter(waitCommitQuery);
                    break;
            }
        }

        if (!CollectionUtils.isEmpty(search.getUnderReviewCustomsCodeList())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordCustomsDO.customsCode", search.getUnderReviewCustomsCodeList()))
                            .must(QueryBuilders.termQuery("esRecordCustomsDO.status", 1))
                            .mustNot(QueryBuilders.termQuery("esRecordCustomsDO.erpCommitStatus", 1)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        if (!CollectionUtils.isEmpty(search.getExaminedCustomsCodeList())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordCustomsDO.customsCode", search.getExaminedCustomsCodeList()))
                            .must(QueryBuilders.termQuery("esRecordCustomsDO.status", 2))
                            .mustNot(QueryBuilders.termQuery("esRecordCustomsDO.erpCommitStatus", 1)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        if (!CollectionUtils.isEmpty(search.getWaitExamineCustomsCodeList())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordCustomsDO.customsCode", search.getWaitExamineCustomsCodeList()))
                            .mustNot(QueryBuilders.termQuery("esRecordCustomsDO.erpCommitStatus", 1)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        if (!CollectionUtils.isEmpty(search.getCustomsCodeList())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordCustomsDO.customsCode", search.getCustomsCodeList())),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }

        //料号
        if (Objects.nonNull(search.getProductIdList())) {
            BoolQueryBuilder productIdBuilder = new BoolQueryBuilder();
            List<String> productIdList = search.getProductIdList();
            productIdBuilder.should(QueryBuilders.termsQuery("productId", productIdList.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(productIdBuilder);
        }

        //sku
        if (Objects.nonNull(search.getSkuList())) {
            BoolQueryBuilder skuBuilder = new BoolQueryBuilder();
            List<String> skuList = search.getSkuList();
            skuBuilder.should(QueryBuilders.termsQuery("skuId", skuList.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(skuBuilder);
        }

        //货品id
        if (Objects.nonNull(search.getGoodsCodeList())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            List<String> goodsCode = search.getGoodsCodeList();
            outOrderNoBuilder.should(QueryBuilders.termsQuery("goodsCode", goodsCode.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(outOrderNoBuilder);
        }

        //条码
        if (Objects.nonNull(search.getBarCodeList())) {
            BoolQueryBuilder barCodeBuilder = new BoolQueryBuilder();
            List<String> barCodeList = search.getBarCodeList();
            barCodeBuilder.should(QueryBuilders.termsQuery("barCode", barCodeList.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(barCodeBuilder);
        }

        //外部料号
        if (Objects.nonNull(search.getExternalProductIdList())) {
            BoolQueryBuilder externalProductIdBuilder = new BoolQueryBuilder();
            List<String> externalProductIdList = search.getExternalProductIdList();
            List<String> list = externalProductIdList.stream().distinct().collect(Collectors.toList());
            externalProductIdBuilder.should(QueryBuilders.termsQuery("externalProductId", list));
            boolQueryBuilder.filter(externalProductIdBuilder);
        }

        //账册编码
        if (Objects.nonNull(search.getCustomsBookId())) {
            QueryBuilder bookQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.matchQuery("esRecordWarehouseDOS.customsBookId", search.getCustomsBookId())),
                    ScoreMode.None);
            boolQueryBuilder.must(bookQuery);
        }

        //创建时间
        Long customsPassFrom = LongUtil.getFrom(search.getRecordTimeFrom(), search.getRecordTimeTo());
        Long customsPassTo = LongUtil.getEnd(search.getRecordTimeFrom(), search.getRecordTimeTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(customsPassFrom).to(customsPassTo));

        }

        // 用于已备案云仓查询
        if (CollUtil.isNotEmpty(search.getRecordIdList())) {
            List<String> idList = search.getRecordIdList().stream().map(id -> tenantryId + "_" + id).collect(Collectors.toList());
            boolQueryBuilder.filter(QueryBuilders.termsQuery("id", idList));

            QueryBuilder recordedLogicWarehoseQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordCustomsDO.recordId", search.getRecordIdList()))
                            .must(QueryBuilders.termQuery("esRecordCustomsDO.status", 2))
                            .mustNot(QueryBuilders.termQuery("esRecordCustomsDO.erpCommitStatus", 1)),
                    ScoreMode.None);
            boolQueryBuilder.filter(recordedLogicWarehoseQuery);
        }

        // 关务备注
        if (StringUtils.isNotBlank(search.getGuanWuRemark())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.wildcardQuery("esRecordCustomsDO.guanWuRemark", "*" + search.getGuanWuRemark() + "*"))
                            .mustNot(QueryBuilders.termQuery("esRecordCustomsDO.erpCommitStatus", 1)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        return boolQueryBuilder;
    }


    private void statusAndCustomsCode(GoodsRecordSearchCondition search, BoolQueryBuilder boolQueryBuilder) {

    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    public GoodsRecordEsDO getById(Long id) {
        return getById(String.valueOf(id));
    }


    public List<GoodsRecordEsDTO> findEsWaitExamineRecord() {
        List<GoodsRecordEsDTO> result = new ArrayList<>();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 1));
        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).withIndices("ccs_goods_record").build();
        long scrollTimeInMillis = 10 * 1000;
        ScrolledPage<GoodsRecordEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, GoodsRecordEsDO.class);
        while (scrolledPage.hasContent()) {
            List<GoodsRecordEsDO> content = scrolledPage.getContent();
            result.addAll(content.stream().map(this::buildDTO).collect(Collectors.toList()));
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, GoodsRecordEsDO.class);
        }
        return result;
    }

    private GoodsRecordEsDTO buildDTO(GoodsRecordEsDO goodsRecordEsDO) {
        if (Objects.isNull(goodsRecordEsDO)) {
            return null;
        }
        GoodsRecordEsDTO goodsRecordEsDTO = new GoodsRecordEsDTO();
        BeanUtils.copyProperties(goodsRecordEsDO, goodsRecordEsDTO, "id");
        String recordEsDOId = goodsRecordEsDO.getId();
        if (recordEsDOId.contains("_")) {
            String[] split = recordEsDOId.split("_");
            String realId = split[1];
            goodsRecordEsDTO.setId(Long.valueOf(realId));
        } else {
            goodsRecordEsDTO.setId(Long.valueOf(recordEsDOId));
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordCustomsDO())) {
            goodsRecordEsDTO.setEsRecordCustomsDTO(ConvertUtil.listConvert(goodsRecordEsDO.getEsRecordCustomsDO(), RecordCustomsESDTO.class));
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordWarehouseDOS())) {
            goodsRecordEsDTO.setEsRecordWarehouseDTOS(ConvertUtil.listConvert(goodsRecordEsDO.getEsRecordWarehouseDOS(), RecordWarehouseESDTO.class));
        }
        if (CollectionUtil.isNotEmpty(goodsRecordEsDO.getEsRecordProducts())) {
            goodsRecordEsDTO.setEsRecordProducts(ConvertUtil.listConvert(goodsRecordEsDO.getEsRecordProducts(), RecordProductESDTO.class));
        }
        return goodsRecordEsDTO;
    }
}
