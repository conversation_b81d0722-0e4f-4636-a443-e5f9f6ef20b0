package com.danding.cds.v2.service.base;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.v2.bean.dao.AiRecommendHsCodeDO;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeDTO;
import com.danding.cds.v2.mapper.AiRecommendHsCodeMapper;
import com.danding.core.tenant.SimpleTenantHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <p>
 * AI推荐商品编码表 baseService 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Service
public class AiRecommendHsCodeBaseService {

    @Resource
    private AiRecommendHsCodeMapper mapper;

    public AiRecommendHsCodeDO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.selectByPrimaryKey(id);
    }

    public List<AiRecommendHsCodeDO> findById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        Example example = new Example(AiRecommendHsCodeDO.class);
        example.createCriteria().andIn("id", idList);
        return this.selectByExample(example);
    }

    public AiRecommendHsCodeDO selectByPrimaryKey(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    public List<AiRecommendHsCodeDO> selectByExample(Example example) {
        return mapper.selectByExample(example);
    }

    public AiRecommendHsCodeDO selectOneByExample(Example example) {
        return mapper.selectOneByExample(example);
    }

    public void updateByPrimaryKey(AiRecommendHsCodeDO record) {
        mapper.updateByPrimaryKey(record);
    }

    public void updateByPrimaryKeySelective(AiRecommendHsCodeDO record) {
        mapper.updateByPrimaryKeySelective(record);
    }

    public void updateByExample(AiRecommendHsCodeDO record, Example example) {
        mapper.updateByExample(record, example);
    }

    public void updateByExampleSelective(AiRecommendHsCodeDO record, Example example) {
        mapper.updateByExampleSelective(record, example);
    }

    public Integer selectCountByExample(Example example) {
        return mapper.selectCountByExample(example);
    }

    public void insertSelective(AiRecommendHsCodeDO record) {
        UserUtils.setCommonData(record);
        mapper.insertSelective(record);
    }

    public void insertList(List<AiRecommendHsCodeDTO> dtoList) {
        List<AiRecommendHsCodeDO> insertList = dtoList.stream().map(dto -> {
            AiRecommendHsCodeDO insertDO = new AiRecommendHsCodeDO();
            BeanUtils.copyProperties(dto, insertDO);
            UserUtils.setCreateAndUpdateBy(insertDO);
            insertDO.setTenantryId(SimpleTenantHelper.getTenantId());
            return insertDO;
        }).collect(Collectors.toList());
        mapper.insertList(insertList);
    }
}