package com.danding.cds.v2.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.UpdateCustomsDeclareProductIdSubmit;
import com.danding.cds.item.api.dto.submit.UpdateRecordWarehouseSubmit;
import com.danding.cds.item.api.enums.GoodsRecordActionEnum;
import com.danding.cds.item.api.enums.GoodsRecordChannel;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.item.mq.AuditCallbackProducer;
import com.danding.cds.item.mq.GoodsRecordDumpProducer;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.cds.v2.bean.dao.RecordCustomsDO;
import com.danding.cds.v2.bean.dao.RecordWarehouseDO;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordAuditWayEnums;
import com.danding.cds.v2.mapper.RecordWarehouseMapper;
import com.danding.cds.v2.service.base.EntityWarehouseBaseService;
import com.danding.cds.v2.service.base.RecordCustomsBaseService;
import com.danding.cds.v2.service.base.RecordWarehouseBaseService;
import com.danding.cds.v2.service.base.RecordWarehouseProductIdBaseService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 备案-实体仓
 * @date 2022/3/9 13:41
 */
@Slf4j
@DubboService
public class RecordWarehouseServiceImpl implements RecordWarehouseService {
    @Autowired
    private RecordWarehouseBaseService recordWarehouseBaseService;
    @Autowired
    private RecordCustomsBaseService recordCustomsBaseService;
    @Autowired
    private EntityWarehouseBaseService entityWarehouseBaseService;
    @Autowired
    private RecordWarehouseMapper recordWarehouseMapper;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;
    @DubboReference
    private RecordCustomsService recordCustomsService;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private CustomsBookService customsBookService;
    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;
    @DubboReference
    private SequenceService sequenceService;
    @Autowired
    private Validator validator;
    @Autowired
    private GoodsRecordDumpProducer goodsRecordDumpProducer;
    @Autowired
    private AuditCallbackProducer auditCallbackProducer;
    @DubboReference
    private CustomsDistrictService customsDistrictService;
    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    /**
     * 根据料号、实体仓获取
     * 1.根据料号找出所有备案 数量对不上则抛异常
     * 2.找到备案关联的所有口岸和推送口岸下的实体仓
     * 3.比较推送口岸下已绑定的实体仓与推送实体仓的关系
     * 4.比较需要新增的实体仓的口岸与关联口岸的关系 如果是新口岸则待审核、如果是老口岸直接备案完成
     *
     * @param submit
     */
    @Override
    public void syncEntityWarehouse(RecordWarehouseSubmit submit) {
        if (Objects.isNull(submit)) {
            log.error("syncEntityWarehouse submit为空 不进行处理");
            throw new ArgsInvalidException("syncEntityWarehouse 提交参数为空");
        }
        List<String> submitProductIdList = submit.getProductId();
        if (CollectionUtils.isEmpty(submitProductIdList)) {
            throw new ArgsInvalidException("syncEntityWarehouse 提交修改的料号为空");
        }
        String customsCode = submit.getCustomsCode();
        if (Objects.isNull(customsCode)) {
            throw new ArgsInvalidException("syncEntityWarehouse 提交口岸为空");
        }
        List<String> submitWmsWarehouseList = submit.getWmsWarehouseList();
        if (CollectionUtils.isEmpty(submitWmsWarehouseList)) {
            throw new ArgsInvalidException("syncEntityWarehouse 提交的实体仓编码为空");
        }
        List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findByProId(submitProductIdList);
        log.info("syncEntityWarehouse 过滤前 goodsRecordDTOList={}", JSON.toJSONString(goodsRecordDTOList));
        this.paramsVerify(submitProductIdList, goodsRecordDTOList, customsCode);
        List<EntityWarehouseDTO> allSubmitWarehouse = entityWarehouseBaseService.findDTOByWmsCode(submitWmsWarehouseList);
        log.info("syncEntityWarehouse allSubmitWarehouse={}", JSON.toJSONString(allSubmitWarehouse));
        List<String> existWmsList = allSubmitWarehouse.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).collect(Collectors.toList());
        String unExistWms = CollUtil.subtract(submitWmsWarehouseList, existWmsList).stream().collect(Collectors.joining("/"));
        if (!StringUtils.isEmpty(unExistWms)) {
            throw new ArgsInvalidException("syncEntityWarehouse 实体仓:" + unExistWms + "未在CCS系统中配置");
        }
        allSubmitWarehouse.forEach(a -> {
            if (!customsCode.equals(a.getCustomsCode())) {
                CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(customsCode);
                log.error("syncEntityWarehouse 提交的实体仓:{}所属口岸:{}与提交的口岸:{}不符", a.getWmsWarehouseCode(), a.getCustoms(), districtEnum.getCustoms());
                throw new ArgsInvalidException("syncEntityWarehouse 提交的实体仓:" + a.getWmsWarehouseCode() + "所属口岸:" + a.getCustoms() + "与提交的口岸:" + districtEnum.getCustoms() + "不符");
            }
        });
        List<Long> recordIdList = goodsRecordDTOList.stream().map(GoodsRecordDTO::getId).collect(Collectors.toList());
        log.info("syncEntityWarehouse recordIdList={}", JSON.toJSONString(recordIdList));
        //找出所有已保存口岸并去重
        List<RecordCustomsDTO> existCustomsList = recordCustomsBaseService.findDTOByRecordId(recordIdList).stream().distinct().collect(Collectors.toList());
        log.info("syncEntityWarehouse existCustomsList={}", JSON.toJSONString(existCustomsList));
        //找出所有已保存实体仓
        List<RecordWarehouseDTO> existWarehouseList = recordWarehouseBaseService.findByRecordIdAndCustomsCode(recordIdList, customsCode);
        log.info("syncEntityWarehouse existWarehouseList={}", JSON.toJSONString(existWarehouseList));
        //找出推送料号关联的口岸
        final Map<Long, List<String>> recordIdCustomsMap = existCustomsList.stream().collect(Collectors.groupingBy(RecordCustomsDTO::getRecordId, Collectors.mapping(RecordCustomsDTO::getCustomsCode, Collectors.toList())));
        log.info("syncEntityWarehouse recordIdCustomsMap={}", JSON.toJSONString(recordIdCustomsMap));
        //找出推送料号关联的实体仓
        final Map<Long, List<String>> recordIdWmsCodeMap = existWarehouseList.stream().collect(Collectors.groupingBy(RecordWarehouseDTO::getRecordId, Collectors.mapping(RecordWarehouseDTO::getWmsWarehouseCode, Collectors.toList())));
        log.info("syncEntityWarehouse recordIdWmsCodeMap={}", JSON.toJSONString(recordIdWmsCodeMap));
        recordIdList.forEach(recordId -> {
            Boolean isNewCustoms = true;
            if (recordIdWmsCodeMap.containsKey(recordId)) {
                List<String> wmsCodeList = recordIdWmsCodeMap.get(recordId);
                log.info("syncEntityWarehouse wmsCodeList={}", JSON.toJSONString(wmsCodeList));
                //比较提交的实体仓与已保存实体仓 获取未保存的实体仓
                List<String> unsavedList = (List<String>) CollUtil.subtract(submitWmsWarehouseList, wmsCodeList);
                log.info("syncEntityWarehouse unsavedList={}", JSON.toJSONString(unsavedList));
                if (CollectionUtils.isEmpty(unsavedList)) {
                    return;
                }
                //直接拿到需要保存的实体仓数据
                List<EntityWarehouseDTO> unsavedWarehouse = allSubmitWarehouse.stream().filter(a -> unsavedList.contains(a.getWmsWarehouseCode())).collect(Collectors.toList());
                log.info("syncEntityWarehouse unsavedWarehouse={}", JSON.toJSONString(unsavedWarehouse));
                if (unsavedList.size() > 0) {
                    //获取备案的口岸
                    if (recordIdCustomsMap.containsKey(recordId)) {
                        List<String> customsList = recordIdCustomsMap.get(recordId);
                        log.info("syncEntityWarehouse customsList={}", JSON.toJSONString(customsList));
                        if (customsList.contains(customsCode)) {
                            isNewCustoms = false;
                        }
                    }
                    GoodsRecordDTO goodsRecordDTO = goodsRecordDTOList.stream().filter(g -> recordId.equals(g.getId())).findFirst().get();
                    log.info("syncEntityWarehouse goodsRecordDTO={}", JSON.toJSONString(goodsRecordDTO));
                    this.saveNewRelation(goodsRecordDTO, unsavedWarehouse, isNewCustoms, customsCode, GoodsRecordActionEnum.MOVE.getCode());
                } else {
                    log.info("syncEntityWarehouse 该口岸{}已包含提交全部实体仓 不进行处理", customsCode);
                }
            }
            //根据口岸状态修改备案状态
            goodsRecordService.recordStatusFix(recordId);
        });
    }

    @Override
    public void syncCentralEntityWarehouse(CentralRecordWarehouseSubmit submit) {
        if (Objects.isNull(submit)) {
            log.error("syncCentralEntityWarehouse submit为空 不进行处理");
            throw new ArgsInvalidException("syncCentralEntityWarehouse 提交参数为空");
        }
        List<Long> recordIdList = submit.getRecordId();
        log.info("syncCentralEntityWarehouse recordIdList={}", JSON.toJSONString(recordIdList));
        if (CollectionUtils.isEmpty(recordIdList)) {
            throw new ArgsInvalidException("syncCentralEntityWarehouse 提交备案id为空");
        }
        String customsCode = submit.getCustomsCode();
        if (Objects.isNull(customsCode)) {
            throw new ArgsInvalidException("syncCentralEntityWarehouse 提交口岸为空");
        }
        List<String> submitWmsWarehouseList = submit.getWmsWarehouseList();
        if (CollectionUtils.isEmpty(submitWmsWarehouseList)) {
            throw new ArgsInvalidException("syncCentralEntityWarehouse 提交的实体仓编码为空");
        }
        List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findById(recordIdList);
        log.info("syncCentralEntityWarehouse 过滤前 goodsRecordDTOList={}", JSON.toJSONString(goodsRecordDTOList));
        this.centralParamsVerify(recordIdList, goodsRecordDTOList, customsCode);
        List<EntityWarehouseDTO> allSubmitWarehouse = entityWarehouseBaseService.findDTOByWmsCode(submitWmsWarehouseList);
        log.info("syncCentralEntityWarehouse allSubmitWarehouse={}", JSON.toJSONString(allSubmitWarehouse));
        List<String> existWmsList = allSubmitWarehouse.stream().map(EntityWarehouseDTO::getWmsWarehouseCode).collect(Collectors.toList());
        submitWmsWarehouseList = submitWmsWarehouseList.stream().collect(Collectors.toList());
        String unExistWms = CollUtil.subtract(submitWmsWarehouseList, existWmsList).stream().collect(Collectors.joining("/"));
        if (!StringUtils.isEmpty(unExistWms)) {
            throw new ArgsInvalidException("syncCentralEntityWarehouse 实体仓:" + unExistWms + "未在CCS系统中配置");
        }
        allSubmitWarehouse.forEach(a -> {
            if (!customsCode.equals(a.getCustomsCode())) {
                CustomsDistrictEnum districtEnum = CustomsDistrictEnum.getEnum(customsCode);
                log.error("syncCentralEntityWarehouse 提交的实体仓:{}所属口岸:{}与提交的口岸:{}不符", a.getWmsWarehouseCode(), a.getCustoms(), districtEnum.getCustoms());
                throw new ArgsInvalidException("syncCentralEntityWarehouse 提交的实体仓:" + a.getWmsWarehouseCode() + "所属口岸:" + a.getCustoms() + "与提交的口岸:" + districtEnum.getCustoms() + "不符");
            }
        });
        //找出所有已保存口岸并去重
        List<RecordCustomsDTO> existCustomsList = recordCustomsBaseService.findDTOByRecordId(recordIdList).stream().distinct().collect(Collectors.toList());
        log.info("syncCentralEntityWarehouse existCustomsList={}", JSON.toJSONString(existCustomsList));
        //找出所有已保存实体仓
        List<RecordWarehouseDTO> existWarehouseList = recordWarehouseBaseService.findByRecordIdAndCustomsCode(recordIdList, customsCode);
        log.info("syncCentralEntityWarehouse existWarehouseList={}", JSON.toJSONString(existWarehouseList));
        //找出推送料号关联的口岸
        final Map<Long, List<String>> recordIdCustomsMap = existCustomsList.stream().collect(Collectors.groupingBy(RecordCustomsDTO::getRecordId, Collectors.mapping(RecordCustomsDTO::getCustomsCode, Collectors.toList())));
        log.info("syncCentralEntityWarehouse recordIdCustomsMap={}", JSON.toJSONString(recordIdCustomsMap));
        //找出推送料号关联的实体仓
        final Map<Long, List<String>> recordIdWmsCodeMap = existWarehouseList.stream().collect(Collectors.groupingBy(RecordWarehouseDTO::getRecordId, Collectors.mapping(RecordWarehouseDTO::getWmsWarehouseCode, Collectors.toList())));
        log.info("syncCentralEntityWarehouse recordIdWmsCodeMap={}", JSON.toJSONString(recordIdWmsCodeMap));
        for (Long recordId : recordIdList) {
            Boolean isNewCustoms = true;
            if (recordIdWmsCodeMap.containsKey(recordId)) {
                List<String> wmsCodeList = recordIdWmsCodeMap.get(recordId);
                log.info("syncCentralEntityWarehouse wmsCodeList={}", JSON.toJSONString(wmsCodeList));
                //比较提交的实体仓与已保存实体仓 获取未保存的实体仓
                List<String> unsavedList = (List<String>) CollUtil.subtract(submitWmsWarehouseList, wmsCodeList);
                log.info("syncCentralEntityWarehouse unsavedList={}", JSON.toJSONString(unsavedList));
                if (CollectionUtils.isEmpty(unsavedList)) {
                    continue;
                }
                //直接拿到需要保存的实体仓数据
                List<EntityWarehouseDTO> unsavedWarehouse = allSubmitWarehouse.stream().filter(a -> unsavedList.contains(a.getWmsWarehouseCode())).collect(Collectors.toList());
                log.info("syncCentralEntityWarehouse unsavedWarehouse={}", JSON.toJSONString(unsavedWarehouse));
                if (unsavedList.size() > 0) {
                    //获取备案的口岸
                    if (recordIdCustomsMap.containsKey(recordId)) {
                        List<String> customsList = recordIdCustomsMap.get(recordId);
                        log.info("syncCentralEntityWarehouse customsList={}", JSON.toJSONString(customsList));
                        if (customsList.contains(customsCode)) {
                            isNewCustoms = false;
                        }
                    }
                    GoodsRecordDTO goodsRecordDTO = goodsRecordDTOList.stream().filter(g -> recordId.equals(g.getId())).findFirst().get();
                    log.info("syncCentralEntityWarehouse goodsRecordDTO={}", JSON.toJSONString(goodsRecordDTO));
                    this.saveCentralGoodsNewRelation(goodsRecordDTO, null, unsavedWarehouse, isNewCustoms, customsCode, GoodsRecordActionEnum.MOVE.getCode());
                } else {
                    log.info("syncCentralEntityWarehouse 该口岸{}已包含提交全部实体仓 不进行处理", customsCode);
                }
            }
            //根据口岸状态修改备案状态
            goodsRecordService.recordStatusFix(recordId);
        }
    }

    private void paramsVerify(List<String> submitProductIdList, List<GoodsRecordDTO> goodsRecordDTOList, String customsCode) {

        if (CollectionUtils.isEmpty(goodsRecordDTOList)) {
            log.error("syncEntityWarehouse 未找到对应备案信息");
            throw new ArgsInvalidException("syncEntityWarehouse 未找到对应备案信息");
        }
        submitProductIdList = submitProductIdList.stream().distinct().collect(Collectors.toList());
        List<String> productIdList = goodsRecordDTOList.stream().map(GoodsRecordDTO::getProductId).distinct().collect(Collectors.toList());
        if (submitProductIdList.size() != productIdList.size()) {
//            List<String> recordList = goodsRecordDTOList.stream().map(GoodsRecordDTO::getProductId).collect(Collectors.toList());
            List<String> subtract = (List<String>) CollUtil.subtract(submitProductIdList, productIdList);
            String errorProductId = StringUtil.join(subtract, ",");
            log.error("syncEntityWarehouse 料号:{} 未找到备案信息", errorProductId);
            throw new ArgsInvalidException("syncEntityWarehouse 料号:" + errorProductId + "无备案信息");
        }
        Map<Long, RecordCustomsDTO> recordIdCustomsMap = recordCustomsService.findByProductIdListAndCustomsCode(submitProductIdList, customsCode);
        submitProductIdList.forEach(s -> {
            if (recordIdCustomsMap.containsKey(s)) {
                RecordCustomsDTO recordCustomsDTO = recordIdCustomsMap.get(s);
                if (!GoodsRecordStatusEnum.RECORD_SUCCESS.getCode().equals(recordCustomsDTO.getStatus())) {
                    throw new ArgsInvalidException("syncEntityWarehouse 料号:" + s + "在口岸" + recordCustomsDTO.getCustoms() + "未备案完成");
                }
            }
        });
    }

    private void centralParamsVerify(List<Long> recordIdList, List<GoodsRecordDTO> goodsRecordDTOList, String customsCode) {
        if (CollectionUtils.isEmpty(goodsRecordDTOList)) {
            log.error("syncEntityWarehouse 未找到对应备案信息");
            throw new ArgsInvalidException("syncEntityWarehouse 未找到对应备案信息");
        }
//        Map<Long, RecordCustomsDTO> recordIdCustomsMap = recordCustomsService.findByRecordIdListAndCustomsCode(recordIdList, customsCode);
//        recordIdList.forEach(s -> {
//            if (recordIdCustomsMap.containsKey(s)) {
//                RecordCustomsDTO recordCustomsDTO = recordIdCustomsMap.get(s);
//                if (!GoodsRecordStatusEnum.RECORD_SUCCESS.getCode().equals(recordCustomsDTO.getStatus())) {
//                    throw new ArgsInvalidException("备案id:" + s + "在口岸" + recordCustomsDTO.getCustoms() + "未备案完成");
//                }
//            }
//        });
    }

    @Override
    public void saveNewRelation(GoodsRecordDTO goodsRecordDTO, List<EntityWarehouseDTO> unsavedList, Boolean isNewCustoms, String customsCode) {
        this.saveNewRelation(goodsRecordDTO, unsavedList, isNewCustoms, customsCode, GoodsRecordActionEnum.NULL.getCode());
    }

    /**
     * 判断口岸是否在新增口岸中
     *
     * @param goodsRecordDTO
     * @param unsavedList    未保存实体仓列表
     * @param isNewCustoms   从已有数据得出是否为新口岸
     * @param customsCode    {@link CustomsDistrictEnum}
     * @param action         {@link GoodsRecordActionEnum#getCode()}
     */
    @Override
    public void saveNewRelation(GoodsRecordDTO goodsRecordDTO, List<EntityWarehouseDTO> unsavedList, Boolean isNewCustoms, String customsCode, Integer action) {
        log.info("saveNewRelation goodsRecordDTO={} unsavedList={} isNewCustoms={} customsCode={} action={}", JSON.toJSONString(goodsRecordDTO), JSON.toJSONString(unsavedList), isNewCustoms, customsCode, action);
        Long recordId = goodsRecordDTO.getId();
        Integer submitType = recordCustomsService.getSubmitType(recordId, customsCode);
        if (CollectionUtils.isEmpty(unsavedList)) {
            //如果没有未保存的实体仓 则直接修改口岸状态
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                recordCustomsBaseService.updateStatus(recordId, customsCode, GoodsRecordStatusEnum.WAIT_EXAMINE.getCode(), submitType);
                //根据口岸状态修改备案状态
                goodsRecordService.recordStatusFix(recordId);
                return;
            }
        }
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setRecordId(recordId);
        Long recordCustomsId;
        // 如果是新口岸 则置为待审核状态
        if (isNewCustoms) {
            recordCustomsId = sequenceService.generateId();
            recordCustomsDO.setId(recordCustomsId);
            recordCustomsDO.setCustoms(CustomsDistrictEnum.getEnum(customsCode).getDesc());
            recordCustomsDO.setCustomsCode(customsCode);
            recordCustomsDO.setHsCode(goodsRecordDTO.getHsCode());
            recordCustomsDO.setOriginCountry(goodsRecordDTO.getOriginCountry());
            recordCustomsDO.setFirstUnit(goodsRecordDTO.getFirstUnit());
            recordCustomsDO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
            recordCustomsDO.setSecondUnit(goodsRecordDTO.getSecondUnit());
            recordCustomsDO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
            recordCustomsDO.setProductId(goodsRecordDTO.getProductId());
            recordCustomsDO.setSubmitType(submitType);
            recordCustomsDO.setSubmitTime(new Date());
            recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.MANUAL.getCode());
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
            } else if (GoodsRecordActionEnum.RECEIVE.getCode().equals(action)) {
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
            } else if (GoodsRecordActionEnum.IMPORT.getCode().equals(action)) {
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
            } else if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
            }
            log.info("saveNewRelation recordCustomsDO={}", JSON.toJSONString(recordCustomsDO));
            recordCustomsBaseService.save(recordCustomsDO);
        } else {
            RecordCustomsDO recordIdAndCustomsCode = recordCustomsBaseService.findByRecordIdAndCustomsCode(recordId, customsCode);
            recordCustomsId = recordIdAndCustomsCode.getId();
            // 如果是编辑+老关区 则更新为待审核
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                log.info("saveNewRelation 更新动作");
                recordCustomsBaseService.updateStatus(recordId, customsCode, GoodsRecordStatusEnum.WAIT_EXAMINE.getCode(), RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode());
            } else if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
                // 备案erp删除后 重新下发， 自动备案更新口岸信息
                recordCustomsDO.setHsCode(goodsRecordDTO.getHsCode());
                recordCustomsDO.setOriginCountry(goodsRecordDTO.getOriginCountry());
                recordCustomsDO.setFirstUnit(goodsRecordDTO.getFirstUnit());
                recordCustomsDO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
                recordCustomsDO.setSecondUnit(goodsRecordDTO.getSecondUnit());
                recordCustomsDO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
                recordCustomsDO.setProductId(goodsRecordDTO.getProductId());
                recordCustomsDO.setSubmitType(submitType);
                recordCustomsDO.setSubmitTime(new Date());
                recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                Example example = new Example(RecordCustomsDO.class);
                example.createCriteria().andEqualTo("recordId", recordId)
                        .andEqualTo("customsCode", customsCode)
                        .andEqualTo("deleted", 0);
                recordCustomsBaseService.updateByExampleSelective(recordCustomsDO, example);
            }
        }
        List<RecordWarehouseDTO> warehouseDTOList = new ArrayList<>();
        unsavedList.forEach(e -> {
            RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
            recordWarehouseDO.setRecordId(recordId);
            recordWarehouseDO.setProductId(goodsRecordDTO.getProductId());
            recordWarehouseDO.setRecordCustomsId(recordCustomsId);
            BeanUtils.copyProperties(e, recordWarehouseDO, "id", "createTime", "updateTime", "deleted");
            recordWarehouseDO.setWarehouseSn(e.getSn());
            log.info("saveNewRelation recordWarehouseDO={}", JSON.toJSONString(recordWarehouseDO));
            recordWarehouseMapper.insertSelective(recordWarehouseDO);
            warehouseDTOList.add(ConvertUtil.beanConvert(recordWarehouseDO, RecordWarehouseDTO.class));
        });
        //根据口岸状态修改备案状态
        goodsRecordService.recordStatusFix(recordId);
        if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
            log.info("AutoRecord - 将外部料号【{}】填写到 账册id【{}】的通关料号上", goodsRecordDTO.getExternalProductId(), goodsRecordDTO.getCustomsBookId());
            // 通关料号取外部料号的值
            if (StringUtil.isNotEmpty(goodsRecordDTO.getExternalProductId())) {
                List<RecordWarehouseDTO> filterWarehouseDTOList = warehouseDTOList;
                if (Objects.nonNull(goodsRecordDTO.getCustomsBookId())) {
                    filterWarehouseDTOList = warehouseDTOList.stream()
                            .filter(i -> Objects.equals(goodsRecordDTO.getCustomsBookId(), i.getCustomsBookId()))
                            .collect(Collectors.toList());
                }
                log.info("AutoRecord - 账册id【{}】, filterWarehouseDTOList={}", goodsRecordDTO.getCustomsBookId(), JSON.toJSONString(filterWarehouseDTOList));
                recordWarehouseProductIdBaseService.insertDeclareProductIdList(filterWarehouseDTOList,
                        new ArrayList<>(Collections.singleton(goodsRecordDTO.getExternalProductId())));
            }
            // 回告erp 审核通过
            this.callBackChannel(goodsRecordDTO, customsCode);
        }
    }

    /**
     * 判断口岸是否在新增口岸中
     *
     * @param goodsRecordDTO
     * @param unsavedList    未保存实体仓列表
     * @param isNewCustoms   从已有数据得出是否为新口岸
     * @param customsCode    {@link CustomsDistrictEnum}
     * @param action         {@link GoodsRecordActionEnum#getCode()}
     */
    @Override
    public void saveCentralGoodsNewRelation(GoodsRecordDTO goodsRecordDTO, String baseInfoJson, List<EntityWarehouseDTO> unsavedList, Boolean isNewCustoms, String customsCode, Integer action) {
        log.info("saveNewRelation goodsRecordDTO={} unsavedList={} isNewCustoms={} customsCode={} action={}", JSON.toJSONString(goodsRecordDTO), JSON.toJSONString(unsavedList), isNewCustoms, customsCode, action);
        Long recordId = goodsRecordDTO.getId();
        Integer submitType = recordCustomsService.getSubmitType(recordId, customsCode);
        if (CollectionUtils.isEmpty(unsavedList)) {
            //如果没有未保存的实体仓 则直接修改口岸状态
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                recordCustomsBaseService.updateSaveInfo(recordId, customsCode, 1, baseInfoJson);
                //根据口岸状态修改备案状态
                goodsRecordService.recordStatusFix(recordId);
                return;
            }
        }
        RecordCustomsDO recordCustomsDO = new RecordCustomsDO();
        recordCustomsDO.setRecordId(recordId);
        Long recordCustomsId;
        // 如果是新口岸 则置为待审核状态
        if (isNewCustoms) {
            recordCustomsId = sequenceService.generateId();
            recordCustomsDO.setId(recordCustomsId);
            recordCustomsDO.setCustoms(CustomsDistrictEnum.getEnum(customsCode).getDesc());
            recordCustomsDO.setCustomsCode(customsCode);
            recordCustomsDO.setHsCode(goodsRecordDTO.getHsCode());
            recordCustomsDO.setOriginCountry(goodsRecordDTO.getOriginCountry());
            recordCustomsDO.setFirstUnit(goodsRecordDTO.getFirstUnit());
            recordCustomsDO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
            recordCustomsDO.setSecondUnit(goodsRecordDTO.getSecondUnit());
            recordCustomsDO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
            recordCustomsDO.setProductId(goodsRecordDTO.getProductId());
            recordCustomsDO.setSubmitType(submitType);
            recordCustomsDO.setSubmitTime(new Date());
            recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.MANUAL.getCode());
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                recordCustomsDO.setErpCommitStatus(1);
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.WAIT_COMMIT.getCode());
            } else if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
            }
            recordCustomsDO.setBaseInfoJson(baseInfoJson);
            log.info("saveNewRelation recordCustomsDO={}", JSON.toJSONString(recordCustomsDO));
            recordCustomsBaseService.save(recordCustomsDO);
        } else {
            RecordCustomsDO recordIdAndCustomsCode = recordCustomsBaseService.findByRecordIdAndCustomsCode(recordId, customsCode);
            recordCustomsId = recordIdAndCustomsCode.getId();
            // 如果是编辑+老关区
            if (GoodsRecordActionEnum.UPDATE.getCode().equals(action)) {
                log.info("saveNewRelation 更新动作 recordId={}", recordId);
                recordCustomsBaseService.updateSaveInfo(recordId, customsCode, 1, baseInfoJson);
            } else if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
                // 备案erp删除后 重新下发， 自动备案更新口岸信息
                recordCustomsDO.setHsCode(goodsRecordDTO.getHsCode());
                recordCustomsDO.setOriginCountry(goodsRecordDTO.getOriginCountry());
                recordCustomsDO.setFirstUnit(goodsRecordDTO.getFirstUnit());
                recordCustomsDO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
                recordCustomsDO.setSecondUnit(goodsRecordDTO.getSecondUnit());
                recordCustomsDO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
                recordCustomsDO.setProductId(goodsRecordDTO.getProductId());
                recordCustomsDO.setSubmitType(submitType);
                recordCustomsDO.setSubmitTime(new Date());
                recordCustomsDO.setAuditWay(GoodsRecordAuditWayEnums.SYSTEM.getCode());
                recordCustomsDO.setStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                recordCustomsDO.setBaseInfoJson(baseInfoJson);
                Example example = new Example(RecordCustomsDO.class);
                example.createCriteria().andEqualTo("recordId", recordId)
                        .andEqualTo("customsCode", customsCode)
                        .andEqualTo("deleted", 0);
                recordCustomsBaseService.updateByExampleSelective(recordCustomsDO, example);
            }
        }
        List<RecordWarehouseDTO> warehouseDTOList = new ArrayList<>();
        unsavedList.forEach(e -> {
            RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
            recordWarehouseDO.setRecordId(recordId);
            recordWarehouseDO.setProductId(goodsRecordDTO.getProductId());
            recordWarehouseDO.setRecordCustomsId(recordCustomsId);
            BeanUtils.copyProperties(e, recordWarehouseDO, "id", "createTime", "updateTime", "deleted");
            recordWarehouseDO.setWarehouseSn(e.getSn());
            log.info("saveNewRelation recordWarehouseDO={}", JSON.toJSONString(recordWarehouseDO));
            recordWarehouseMapper.insertSelective(recordWarehouseDO);
            warehouseDTOList.add(ConvertUtil.beanConvert(recordWarehouseDO, RecordWarehouseDTO.class));
        });
        //根据口岸状态修改备案状态
        goodsRecordService.recordStatusFix(recordId);
        if (GoodsRecordActionEnum.AUTO.getCode().equals(action)) {
            log.info("AutoRecord - 将外部料号【{}】填写到 账册id【{}】的通关料号上", goodsRecordDTO.getExternalProductId(), goodsRecordDTO.getCustomsBookId());
            // 通关料号取外部料号的值
            if (StringUtil.isNotEmpty(goodsRecordDTO.getExternalProductId())) {
                List<RecordWarehouseDTO> filterWarehouseDTOList = warehouseDTOList;
                if (Objects.nonNull(goodsRecordDTO.getCustomsBookId())) {
                    filterWarehouseDTOList = warehouseDTOList.stream()
                            .filter(i -> Objects.equals(goodsRecordDTO.getCustomsBookId(), i.getCustomsBookId()))
                            .collect(Collectors.toList());
                }
                List<RecordWarehouseProductIdDTO> beforeWarehouseProIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordDTO.getId());
                String beforeWarehouseProIds = beforeWarehouseProIdDTOList.stream()
                        .map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId)
                        .collect(Collectors.joining(","));
                log.info("AutoRecord - 账册id【{}】, filterWarehouseDTOList={}", goodsRecordDTO.getCustomsBookId(), JSON.toJSONString(filterWarehouseDTOList));
                recordWarehouseProductIdBaseService.insertDeclareProductIdList(filterWarehouseDTOList,
                        new ArrayList<>(Collections.singleton(goodsRecordDTO.getExternalProductId())));
                List<RecordWarehouseProductIdDTO> afterWarehouseProIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordDTO.getId());
                String afterWarehouseProIds = afterWarehouseProIdDTOList.stream()
                        .map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId)
                        .collect(Collectors.joining(","));
                GoodsRecordCompareDTO before = new GoodsRecordCompareDTO();
                before.setWarehouseExternalProductIds(beforeWarehouseProIds);
                GoodsRecordCompareDTO after = new GoodsRecordCompareDTO();
                after.setWarehouseExternalProductIds(afterWarehouseProIds);
                CustomsDistrictDTO customsDistrictDTO = customsDistrictService.getByCode(customsCode);
                goodsRecordService.buildTrackLogDiffAndSend(goodsRecordDTO.getId(), before, after, customsDistrictDTO.getName(), ItemTrackLogConfig.UPDATE_GOODS);
            }
            // 回告erp 审核通过
            this.callBackChannel(goodsRecordDTO, customsCode);
        }
    }


    private void callBackChannel(GoodsRecordDTO recordDTO, String customsCode) {
        if (recordDTO != null && GoodsRecordChannel.LOGISTICS.getValue().equals(recordDTO.getChannel())) {
            GoodsRecordAuditSubmitDTO auditSubmitDTO = new GoodsRecordAuditSubmitDTO();
            BeanUtils.copyProperties(recordDTO, auditSubmitDTO);
            auditSubmitDTO.setOpinion(1); // 审核通过
            auditSubmitDTO.setChannel(GoodsRecordChannel.LOGISTICS);
            auditSubmitDTO.setCustomsCode(customsCode);
            auditSubmitDTO.setSku(recordDTO.getSkuId());
            CustomsBookResVo customsBookDTO = customsBookService.findByIdV2(recordDTO.getCustomsBookId());
            if (Objects.nonNull(customsBookDTO)) {
                auditSubmitDTO.setBookNo(customsBookDTO.getBookNo());
            }
            log.info("[op:GoodsRecordServiceImpl-audit auditSubmitDto={}]", JSON.toJSONString(auditSubmitDTO));
            auditCallbackProducer.send(auditSubmitDTO);
        }
    }

    /**
     * 对比该备案在口岸下已关联实体仓 返回未保存的实体仓列表
     *
     * @param recordId
     * @param customsCode
     * @param wmsWarehouseList
     */
    @Override
    public List<EntityWarehouseDTO> getUnsavedWarehouseList(Long recordId, String customsCode, List<String> wmsWarehouseList) {
        log.info("getUnsavedWarehouseList recordId={} customsCode={} wmsWarehouseList={}", recordId, customsCode, JSON.toJSONString(wmsWarehouseList));
        RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(recordId, customsCode);
        log.info("getUnsavedWarehouseList recordCustomsDTO={}", JSON.toJSONString(recordCustomsDTO));
        List<RecordWarehouseDO> recordWarehouseDOList = recordWarehouseBaseService.findByRecordCustomsId(Collections.singletonList(recordCustomsDTO.getId()));
        log.info("getUnsavedWarehouseList recordWarehouseDOList={}", JSON.toJSONString(recordWarehouseDOList));
        List<String> existWmsList = recordWarehouseDOList.stream().map(RecordWarehouseDO::getWmsWarehouseCode).collect(Collectors.toList());
        List<String> unsavedWmsCodeList = wmsWarehouseList.stream().filter(w -> !existWmsList.contains(w)).collect(Collectors.toList());
        log.info("getUnsavedWarehouseList unsavedWmsCodeList={}", JSON.toJSONString(unsavedWmsCodeList));
        return entityWarehouseService.findDTOByWmsCode(unsavedWmsCodeList, customsCode);
    }

    @Override
    public List<RecordWarehouseDTO> getRecordWarehouseInfo(Long recordCustomsId) {
        List<RecordWarehouseDO> recordWarehouseDOList = recordWarehouseBaseService.findByRecordCustomsId(recordCustomsId);
        return ConvertUtil.listConvert(recordWarehouseDOList, RecordWarehouseDTO.class);
    }

    @Override
    public List<CustomsBookDTO> recordCustomsBookList(Long recordId) {
        List<RecordWarehouseDO> recordWarehouseDOList = recordWarehouseBaseService.findByRecordId(recordId);
        if (CollectionUtils.isEmpty(recordWarehouseDOList)) {
            return new ArrayList<>();
        }
        List<Long> customsBookIdList = recordWarehouseDOList.stream().map(RecordWarehouseDO::getCustomsBookId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customsBookIdList)) {
            throw new ArgsInvalidException("未找到关联账册号");
        }
        List<CustomsBookDTO> customsBookDTOS = customsBookService.listBooksByIds(customsBookIdList);
        return customsBookDTOS;
    }

    @Override
    public void clearRelation(Long recordId) {
        Example example = new Example(RecordWarehouseDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("deleted", 0);
        RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
        recordWarehouseDO.setDeleted(true);
        UserUtils.setUpdateBy(recordWarehouseDO);
        recordWarehouseDO.setUpdateTime(new Date());
        recordWarehouseMapper.updateByExampleSelective(recordWarehouseDO, example);
    }


    /**
     * 编辑外部料号/通关料号
     *
     * @param submit
     */
    @Deprecated
    @Override
    public void updateByExternalProductId(UpdateRecordWarehouseSubmit submit) {
        if (CollectionUtils.isEmpty(submit.getRecordWarehouseDTOList())) {
            return;
        }
        List<RecordWarehouseDTO> warehouseDTOList = submit.getRecordWarehouseDTOList();
        //单个备案提交的外部料号在同账册唯一
        warehouseDTOList.stream().collect(Collectors.groupingBy(RecordWarehouseDTO::getCustomsBookNo, Collectors.toList()))
                .forEach((k, v) -> {
                    if (v.size() > 1) {
                        Set<String> collect = v.stream().map(r -> {
                            if (StringUtils.isEmpty(r.getExternalProductId())) {
                                r.setExternalProductId(null);
                            }
                            return r.getExternalProductId();
                        }).collect(Collectors.toSet());
                        if (collect.size() != 1) {
                            throw new ArgsInvalidException("账册:" + k + "提交的通关料号不一致");
                        }
                    }
                });
        //做一个账册下外部料号唯一的判断
        warehouseDTOList.stream().filter(w -> StringUtils.hasText(w.getExternalProductId()))
                .collect(Collectors.toMap(RecordWarehouseDTO::getCustomsBookNo, Function.identity(), (v1, v2) -> v1))
                .values().forEach(w -> {
                    Example example = new Example(RecordWarehouseDO.class);
                    example.createCriteria().andEqualTo("deleted", 0).andEqualTo("externalProductId", w.getExternalProductId())
                            .andEqualTo("customsBookId", w.getCustomsBookId()).andNotEqualTo("recordId", w.getRecordId());
                    List<RecordWarehouseDO> existExternalList = recordWarehouseMapper.selectByExample(example);
                    if (!CollectionUtils.isEmpty(existExternalList)) {
                        RecordWarehouseDO recordWarehouseDO = existExternalList.get(0);
                        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(recordWarehouseDO.getRecordId());
                        log.error("设置的通关料号" + w.getExternalProductId() + "在备案(海关备案料号:" + goodsRecordDTO.getProductId() + ")已存在");
                        throw new ArgsInvalidException("设置的通关料号" + w.getExternalProductId() + "在备案(海关备案料号:" + goodsRecordDTO.getProductId() + ")已存在");
                    }
                });
        for (RecordWarehouseDTO recordWarehouseDTO : warehouseDTOList) {
            RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
            recordWarehouseDO.setId(recordWarehouseDTO.getId());
            recordWarehouseDO.setExternalProductId(recordWarehouseDTO.getExternalProductId());
            recordWarehouseMapper.updateByPrimaryKeySelective(recordWarehouseDO);
        }
    }


    /**
     * 提交的料号分组
     * 1.更新的
     * 2.新增的
     * 3.删除的 利用库里有的减去回传的
     *
     * @param submit
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomsDeclareProductId(UpdateCustomsDeclareProductIdSubmit submit) {
        String inputError = ValidatorUtils.doValidator(validator, submit);
        if (StringUtils.hasText(inputError)) {
            throw new ArgsInvalidException(inputError);
        }
        Long recordId = submit.getRecordId();
        Long customsBookId = submit.getCustomsBookId();
        List<String> productIdSubmitList = submit.getProductIdSubmitList();
        if (CollectionUtils.isNotEmpty(productIdSubmitList)) {
            this.checkAnyExist(recordId, productIdSubmitList, customsBookId);
        }
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsBookId);
        List<RecordWarehouseProductIdDTO> beforeWarehouseProIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(submit.getRecordId());
        String beforeWarehouseProIds = beforeWarehouseProIdDTOList.stream()
                .map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId)
                .collect(Collectors.joining(","));

        recordWarehouseProductIdBaseService.deletedByRecordIdAndCustomsBookId(recordId, customsBookId);
        if (CollectionUtils.isNotEmpty(productIdSubmitList)) {
            List<RecordWarehouseDTO> warehouseDTOList = recordWarehouseBaseService.findByRecordIdAndCustomsBookId(recordId, customsBookId);
            recordWarehouseProductIdBaseService.insertDeclareProductIdList(warehouseDTOList, productIdSubmitList);
        }
        List<RecordWarehouseProductIdDTO> afterWarehouseProIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(submit.getRecordId());
        String afterWarehouseProIds = afterWarehouseProIdDTOList.stream()
                .map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId)
                .collect(Collectors.joining(","));

        // 生成变动日志
        GoodsRecordCompareDTO before = new GoodsRecordCompareDTO();
        before.setWarehouseExternalProductIds(beforeWarehouseProIds);
        GoodsRecordCompareDTO after = new GoodsRecordCompareDTO();
        after.setWarehouseExternalProductIds(afterWarehouseProIds);
        CustomsDistrictDTO customsDistrictDTO = customsDistrictService.getByCode(customsBookResVo.getCustomsDistrictCode());
        goodsRecordService.buildTrackLogDiffAndSend(submit.getRecordId(), before, after, customsDistrictDTO.getName(), ItemTrackLogConfig.UPDATE_GOODS);
        // 同步es
        goodsRecordDumpProducer.send(submit.getRecordId());
    }

    /**
     * 检查通关料号是否在其他备案中出现过
     *
     * @param recordId
     * @param productIdSubmitList
     * @param customsBookId
     */
    public void checkAnyExist(Long recordId, List<String> productIdSubmitList, Long customsBookId) {
        GoodsRecordDTO currentRecord = goodsRecordService.findById(recordId);
        // 通关料号相同，备案id不同
        List<RecordWarehouseProductIdDTO> otherList = recordWarehouseProductIdBaseService.findAnyExist(recordId, productIdSubmitList, customsBookId);
        if (CollectionUtils.isNotEmpty(otherList)) {
            List<Long> goodsRecordId = otherList.stream().map(RecordWarehouseProductIdDTO::getRecordId).distinct().collect(Collectors.toList());
            List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordService.findById(goodsRecordId);
            List<String> tenantIdList = goodsRecordDTOS.stream().map(GoodsRecordDTO::getTenantId).distinct().collect(Collectors.toList());
            Map<String, String> tenantNameMap = taxesTenantAccountService.getTenantNameById(tenantIdList);
            Map<Long, GoodsRecordDTO> goodsRecordDTOMap = goodsRecordDTOS.stream()
                    .collect(Collectors.toMap(GoodsRecordDTO::getId, Function.identity(), (v1, v2) -> v1));
            StringBuffer sb = new StringBuffer();
            Set<String> tenantIdVisited = new HashSet<>();
            otherList.forEach(o -> {
                if (goodsRecordDTOMap.containsKey(o.getRecordId())) {
                    GoodsRecordDTO goodsRecordDTO = goodsRecordDTOMap.get(o.getRecordId());
                    if (!goodsRecordDTO.getTenantId().equals(currentRecord.getTenantId()) && !tenantIdVisited.contains(goodsRecordDTO.getTenantId())) {
                        sb.append(String.format("通关料号：%s，已存在用户：%s的备案中", o.getCustomsDeclareProductId(), tenantNameMap.get(goodsRecordDTO.getTenantId())));
                        tenantIdVisited.add(goodsRecordDTO.getTenantId());
                    }
                }
            });
            if (sb.length() > 0) {
                throw new ArgsInvalidException(sb.toString());
            }
        }
    }

    /**
     * 根据备案id查询
     *
     * @param recordId
     * @return
     */
    @Override
    public List<RecordWarehouseESDTO> findByRecordId(Long recordId) {
        Example example = new Example(RecordWarehouseDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("recordId", recordId).andEqualTo("deleted", false);
        List<RecordWarehouseDO> recordWarehouseDOS = recordWarehouseMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(recordWarehouseDOS)) {
            List<RecordWarehouseESDTO> recordWarehouseDTOList = JSON.parseArray(JSON.toJSONString(recordWarehouseDOS), RecordWarehouseESDTO.class);
            return recordWarehouseDTOList;
        }
        return new ArrayList<>();
    }

    @Override
    public List<RecordWarehouseDTO> findByRecordId(List<Long> recordIdList) {
        if (CollectionUtils.isEmpty(recordIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RecordWarehouseDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("recordId", recordIdList).andEqualTo("deleted", false);
        List<RecordWarehouseDO> recordWarehouseDOS = recordWarehouseMapper.selectByExample(example);
        return ConvertUtil.listConvert(recordWarehouseDOS, RecordWarehouseDTO.class);
    }

    @Override
    public void deleteByRecordId(Long recordId) {
        RecordWarehouseDO recordWarehouseDO = new RecordWarehouseDO();
        recordWarehouseDO.setDeleted(true);
        UserUtils.setUpdateBy(recordWarehouseDO);
        recordWarehouseDO.setUpdateTime(new Date());
        Example example = new Example(RecordWarehouseDO.class);
        example.createCriteria().andEqualTo("recordId", recordId).andEqualTo("deleted", false);
        recordWarehouseMapper.updateByExampleSelective(recordWarehouseDO, example);
        recordWarehouseProductIdBaseService.deletedByRecordId(recordId);
        log.info("RecordWarehouseServiceImpl deleteByRecordId recordId={}", recordId);
    }

    @Override
    public synchronized void syncRecordWarehouseCustomsBook(EntityWarehouseDTO entityWarehouseDTO) {
        log.info("syncRecordWarehouseCustomsBook warehouse={}", JSON.toJSONString(entityWarehouseDTO));
        String wmsWarehouseCode = entityWarehouseDTO.getWmsWarehouseCode();
        String customsBookNo = entityWarehouseDTO.getCustomsBookNo();
        Long customsBookId = entityWarehouseDTO.getCustomsBookId();
        List<RecordWarehouseDO> recordWarehouseDOList = recordWarehouseMapper.selectUnSaveRecord(wmsWarehouseCode, customsBookId);
        log.info("syncRecordWarehouseCustomsBook recordWarehouseDOList size={}", recordWarehouseDOList.size());
        List<RecordWarehouseDO> warehouseDOS = recordWarehouseDOList.stream().map(recordWarehouseDO -> {
            RecordWarehouseDO newRecordWarehouse = ConvertUtil.beanConvert(recordWarehouseDO, RecordWarehouseDO.class, "id");
            newRecordWarehouse.setCustomsBookId(customsBookId);
            newRecordWarehouse.setCustomsBookNo(customsBookNo);
            UserUtils.setCreateAndUpdateBy(newRecordWarehouse);
            return newRecordWarehouse;
        }).collect(Collectors.toList());
        recordWarehouseBaseService.saveBatch(warehouseDOS);
    }

    @Test
    public void test() {
        List<RecordWarehouseDTO> list = new ArrayList<>();
        RecordWarehouseDTO recordWarehouseDTO = new RecordWarehouseDTO();
        recordWarehouseDTO.setCustomsBookNo("111");
        recordWarehouseDTO.setExternalProductId("11123123");
        list.add(recordWarehouseDTO);
        RecordWarehouseDTO recordWarehouseDTO1 = new RecordWarehouseDTO();
        recordWarehouseDTO1.setCustomsBookNo("111");
        recordWarehouseDTO1.setExternalProductId("null");
        list.add(recordWarehouseDTO1);
        list.stream().collect(Collectors.groupingBy(RecordWarehouseDTO::getCustomsBookNo, Collectors.toList()))
                .forEach((k, v) -> {
                    if (v.size() > 1) {
                        Set<String> collect = v.stream().map(r -> {
                            if (StringUtils.isEmpty(r.getExternalProductId())) {
                                r.setExternalProductId(null);
                            }
                            return r.getExternalProductId();
                        }).collect(Collectors.toSet());
                        if (collect.size() != 1) {
                            throw new ArgsInvalidException("账册:" + k + "提交的通关料号不一致");
                        }
                    }
                });
    }
}
