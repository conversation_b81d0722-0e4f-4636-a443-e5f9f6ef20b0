package com.danding.cds.v2.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.config.facade.EntityWarehouseRpcFacade;
import com.danding.business.client.rpc.config.param.EntityWarehouseRpcParam;
import com.danding.business.client.rpc.config.result.EntityWarehouseRpcResult;
import com.danding.business.common.ares.enums.common.OpenStatus;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.dao.EntityWarehouseDO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.bean.vo.req.*;
import com.danding.cds.v2.bean.vo.res.ERPEntityWarehouseResVO;
import com.danding.cds.v2.bean.vo.res.EntityWarehouseResVO;
import com.danding.cds.v2.mapper.EntityWarehouseMapper;
import com.danding.cds.v2.service.base.EntityWarehouseBaseService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.*;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/7
 */
@Slf4j
@DubboService
public class EntityWarehouseServiceImpl implements EntityWarehouseService {

    @Autowired
    private EntityWarehouseMapper entityWarehouseMapper;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private EntityWarehouseRpcFacade facade;

    @Autowired
    private EntityWarehouseBaseService entityWarehouseBaseService;

    @DubboReference
    private RecordWarehouseService recordWarehouseService;

    @Override
    public List<EntityWarehouseDTO> findDTOByWmsCode(List<String> wmsWarehouseList) {
        return entityWarehouseBaseService.findDTOByWmsCode(wmsWarehouseList);
    }

    @Override
    public List<EntityWarehouseDTO> findDTOByWmsCode(String wmsWarehouse) {
        return entityWarehouseBaseService.findDTOByWmsCode(wmsWarehouse);
    }

    @Override
    public List<EntityWarehouseDTO> findDTOByWmsCode(List<String> wmsWarehouseList, String customsCode) {
        return entityWarehouseBaseService.findDTOByWmsCode(wmsWarehouseList, customsCode);
    }

    @Override
    public List<EntityWarehouseDTO> findDTOByErpCode(List<String> erpWarehouseCodeList) {
        List<EntityWarehouseDO> entityWarehouseDOList = entityWarehouseBaseService.findByErpWarehouseCode(erpWarehouseCodeList);
        return ConvertUtil.listConvert(entityWarehouseDOList, EntityWarehouseDTO.class);
    }

    @Override
    public List<EntityWarehouseDTO> findDTOByErpCode(String erpWarehouseCode) {
        if (Objects.isNull(erpWarehouseCode)) {
            return new ArrayList<>();
        }
        return this.findDTOByErpCode(Collections.singletonList(erpWarehouseCode));
    }

    /**
     * 根据账册ID，获取实体仓
     *
     * @return
     */
    @Override
    public List<EntityWarehouseDTO> findByCustomsBookId(Long bookId) {
        return entityWarehouseBaseService.findByCustomsBookId(bookId);
    }

    @Override
    public List<EntityWarehouseDTO> findByCustomsBookId(List<Long> bookIdList) {
        return entityWarehouseBaseService.findByCustomsBookId(bookIdList);
    }

    /**
     * 实体仓配置分页查询
     *
     * @param search
     * @return
     */
    @Override
    @PageSelect
    public ListVO<EntityWarehouseResVO> paging(EntityWarehouseSearch search) {
        Example example = new Example(EntityWarehouseDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(search.getErpWarehouseCode())) {
            criteria.andEqualTo("erpWarehouseCode", search.getErpWarehouseCode());
        }
        if (Objects.nonNull(search.getErpWarehouseName())) {
            criteria.andEqualTo("erpWarehouseName", search.getErpWarehouseName());
        }
        if (Objects.nonNull(search.getCustomsBookId())) {
            criteria.andEqualTo("customsBookId", search.getCustomsBookId());
        }
        if (Objects.nonNull(search.getWmsWarehouseCode())) {
            criteria.andEqualTo("wmsWarehouseCode", search.getWmsWarehouseCode());
        }
        if (Objects.nonNull(search.getWarehouseTag())) {
            criteria.andCondition("warehouse_tag & " + search.getWarehouseTag() + " = ", search.getWarehouseTag());
        }
        example.and(criteria);
        example.setOrderByClause("create_time DESC");
        List<EntityWarehouseDO> warehouseDOList = entityWarehouseMapper.selectByExample(example);
        List<EntityWarehouseResVO> warehouseResVOS = new ArrayList<>();
        ListVO<EntityWarehouseResVO> result = new ListVO<>();
        for (EntityWarehouseDO warehouseDO : warehouseDOList) {
            EntityWarehouseResVO warehouseResVO = BeanUtil.copyProperties(warehouseDO, EntityWarehouseResVO.class);
            warehouseResVO.setId(warehouseDO.getId().toString());
            warehouseResVO.setWarehouseTagList(EntityWarehouseTagEnums.getTags(warehouseDO.getWarehouseTag()));
            warehouseResVOS.add(warehouseResVO);
        }
        result.setDataList(warehouseResVOS);
        // 分页
        PageInfo<EntityWarehouseResVO> pageInfo = new PageInfo(warehouseDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    /**
     * 新增，编辑
     *
     * @param reqVO
     */
    @Override
    public int upsetEntityWarehouse(EntityWarehouseReqVO reqVO) throws ArgsErrorException {
        EntityWarehouseDO warehouseDO = new EntityWarehouseDO();
        Integer userId = UserUtils.getUserId();
        int result = 0;

        //如果没有id就是编辑
        if (Objects.isNull(reqVO.getId())) {
            EntityWarehouseDO entityWarehouseDO = findEntityWarehouse(reqVO);
            if (entityWarehouseDO != null) {
                throw new ArgsErrorException("实体仓已绑定该本-" + entityWarehouseDO.getCustomsBookNo() + "-账册");
            }
            warehouseDO = BeanUtil.copyProperties(reqVO, EntityWarehouseDO.class);
            warehouseDO.setEnable(1);
            warehouseDO.setSn(sequenceService.generateEntityWarehouseSn());
            warehouseDO.setId(sequenceService.generateId());
            warehouseDO.setCreateBy(userId);
            warehouseDO.setUpdateBy(userId);
            warehouseDO.setCreateTime(new Date());
            warehouseDO.setUpdateTime(new Date());
            result = entityWarehouseMapper.insert(warehouseDO);
        } else {
            EntityWarehouseDO entityWarehouseDO = findEntityWarehouse(reqVO);
            reqVO.setCustomsBookNo(entityWarehouseDO.getCustomsBookNo());
            reqVO.setErpWarehouseCode(entityWarehouseDO.getErpWarehouseCode());
            warehouseDO.setId(Long.valueOf(reqVO.getId()));
            warehouseDO.setEnable(reqVO.getEnable());
            warehouseDO.setUpdateBy(userId);
            warehouseDO.setUpdateTime(new Date());
            result = entityWarehouseMapper.updateByPrimaryKeySelective(warehouseDO);
        }
        //新增修改通知ERP
        boolean res = facade.updateEntityWarehouseAccountCode(reqVO.getErpWarehouseCode(), reqVO.getCustomsBookNo(), reqVO.getCustomsCode());
        log.warn("[- op EntityWarehouseServiceImpl] 新增编辑回告结果 - {}", res);
        return result;
    }

    private EntityWarehouseDO findEntityWarehouse(EntityWarehouseReqVO reqVO) {
        Example example = new Example(EntityWarehouseDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(reqVO.getId())) {
            criteria.andEqualTo("id", reqVO.getId());
        } else {
            criteria.andEqualTo("customsBookId", reqVO.getCustomsBookId());
            criteria.andEqualTo("wmsWarehouseCode", reqVO.getWmsWarehouseCode());
        }
        example.and(criteria);
        EntityWarehouseDO entityWarehouseDO = entityWarehouseMapper.selectOneByExample(example);
        return entityWarehouseDO;
    }


    /**
     * 获取ERP实体仓列表
     *
     * @return
     */
    @Override
    public List<ERPEntityWarehouseResVO> queryERPEntityWarehouse() {
        EntityWarehouseRpcParam param = new EntityWarehouseRpcParam();
        param.setOpenStatus(OpenStatus.OPEN);
//        param.setTradeType(TradeType.BONDED); 保税完税实体仓都要查询
        //获取ERP实体仓信息
        List<EntityWarehouseRpcResult> list = facade.getAllEntityWarehouseList(param);
        log.info("queryERPEntityWarehouse list={}", JSON.toJSONString(list));
        List<ERPEntityWarehouseResVO> resVOList = new ArrayList<>();
        for (EntityWarehouseRpcResult result : list) {
            ERPEntityWarehouseResVO resVO = new ERPEntityWarehouseResVO();
            resVO.setId(result.getEntityWarehouseName());
            resVO.setName(result.getEntityWarehouseName());
            resVO.setEntityWarehouseCode(result.getEntityWarehouseCode());
            resVO.setWarehouseCode(result.getWarehouseCode());
            resVO.setPort(result.getPort());
            resVO.setCustoms(CustomsDistrictEnum.getEnum(result.getPort()).getDesc());
            resVO.setTradeType(result.getTradeType().getValue());
            resVOList.add(resVO);
        }
        return resVOList;
    }

    @Override
    public List<EntityWarehouseDTO> listWarehouse() {
        List<EntityWarehouseDO> doList = entityWarehouseBaseService.selectAll();
        return ConvertUtil.listConvert(doList, EntityWarehouseDTO.class);
    }

    @Override
    public List<EntityWarehouseDTO> listWarehouse(String customsCode) {
        List<EntityWarehouseDO> doList = entityWarehouseBaseService.findByCustomsCode(customsCode);
        return ConvertUtil.listConvert(doList, EntityWarehouseDTO.class);
    }


    /**
     * 根据WERP仓库编码，WMS编码获取账册号（提供ERP使用
     *
     * @param reqVo
     * @return
     */
    @Override
    public List<EntityWarehouseDTO> queryBook(ERPEntityWarehouseReqVO reqVo) {
        Example example = new Example(EntityWarehouseDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(reqVo.getErpWarehouseCodes())) {
            criteria.andIn("erpWarehouseCode", reqVo.getWmsWarehouseCodes());
        }
        if (Objects.nonNull(reqVo.getWmsWarehouseCodes())) {
            criteria.andIn("wmsWarehouseCode", reqVo.getWmsWarehouseCodes());
        }
        criteria.andEqualTo("enable", 1);
        example.and(criteria);
        List<EntityWarehouseDO> warehouseDOList = entityWarehouseMapper.selectByExample(example);
        List<EntityWarehouseDTO> warehouseDTOList = new ArrayList<>();
        for (EntityWarehouseDO warehouseDO : warehouseDOList) {
            EntityWarehouseDTO warehouseDTO = BeanUtil.copyProperties(warehouseDO, EntityWarehouseDTO.class);
            warehouseDTOList.add(warehouseDTO);
        }
        return warehouseDTOList;
    }

    /**
     * 删除实体仓
     *
     * @param reqVO
     */
    @Override
    public void delEntityWarehouse(DelEntityWarehouseReqVO reqVO) {
        EntityWarehouseDO warehouseDO = new EntityWarehouseDO();
        warehouseDO.setId(Long.valueOf(reqVO.getId()));
        entityWarehouseMapper.delete(warehouseDO);
    }

    @Override
    public EntityWarehouseDTO findBySn(String warehouseSn) {
        if (Objects.isNull(warehouseSn)) {
            return null;
        }
        EntityWarehouseDO entityWarehouseDO = new EntityWarehouseDO();
        entityWarehouseDO.setSn(warehouseSn);
        EntityWarehouseDO warehouseDO = entityWarehouseMapper.selectOne(entityWarehouseDO);
        return ConvertUtil.beanConvert(warehouseDO, EntityWarehouseDTO.class);
    }

    @Override
    public void syncRecordWarehouseCustomsBook(Long id) {
        EntityWarehouseDTO entityWarehouseDTO = entityWarehouseBaseService.findById(id);
        if (Objects.isNull(entityWarehouseDTO)) {
            throw new ArgsInvalidException("实体仓不存在");
        }
        recordWarehouseService.syncRecordWarehouseCustomsBook(entityWarehouseDTO);
    }

    @Override
    public void updateTag(UpdEntityWarehouseTagReqVo reqVo) {
        if (Objects.isNull(reqVo.getId())) {
            throw new ArgsInvalidException("实体仓id不能为空");
        }
        EntityWarehouseDTO entityWarehouseDTO = entityWarehouseBaseService.findById(reqVo.getId());
        if (Objects.isNull(entityWarehouseDTO)) {
            throw new ArgsInvalidException("实体仓不存在");
        }
        if (CollUtil.isNotEmpty(reqVo.getTagList())) {
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.CW_INVENTORY.getCode())) {
                // 添加CW清关标签校验
                checkTagDistinct(entityWarehouseDTO.getId(), entityWarehouseDTO.getWmsWarehouseCode(), EntityWarehouseTagEnums.CW_INVENTORY);
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.FB_RELATE_WAREHOUSE.getCode())) {
                // 添加非保关仓标签校验
                checkTagDistinct(entityWarehouseDTO.getId(), entityWarehouseDTO.getWmsWarehouseCode(), EntityWarehouseTagEnums.FB_RELATE_WAREHOUSE);
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.TAO_TIAN.getCode())) {
                // 添加淘天清关标签校验
                checkTagDistinct(entityWarehouseDTO.getId(), entityWarehouseDTO.getWmsWarehouseCode(), EntityWarehouseTagEnums.TAO_TIAN);
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.TAO_TIAN_FB.getCode())) {
                // 添加淘天非保标签校验
                checkTagDistinct(entityWarehouseDTO.getId(), entityWarehouseDTO.getWmsWarehouseCode(), EntityWarehouseTagEnums.TAO_TIAN_FB);
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.COMMON_BOOKS_QG.getCode())
                    && reqVo.getTagList().contains(EntityWarehouseTagEnums.SPECIAL_BOOKS_QG.getCode())) {
                throw new ArgsInvalidException("同实体仓同账册无法同时打上【普通账册清关】和【专用账册清关】标记");
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.SPECIAL_BOOKS_QG.getCode())) {
                // 添加专用账册清关标签校验
                checkTagDistinct(entityWarehouseDTO.getId(), entityWarehouseDTO.getWmsWarehouseCode(), EntityWarehouseTagEnums.SPECIAL_BOOKS_QG);
            }
            if (reqVo.getTagList().contains(EntityWarehouseTagEnums.TAO_TIAN.getCode())
                    && reqVo.getTagList().contains(EntityWarehouseTagEnums.TAO_TIAN_FB.getCode())) {
                throw new ArgsInvalidException("同实体仓同账册无法同时打上【淘天清关】和【淘天非保】标记");
            }
        }
        EntityWarehouseDO entityWarehouseDO = new EntityWarehouseDO();
        entityWarehouseDO.setId(reqVo.getId());
        entityWarehouseDO.setWarehouseTag(EntityWarehouseTagEnums.build(reqVo.getTagList()));
        entityWarehouseMapper.updateByPrimaryKeySelective(entityWarehouseDO);
    }

    /**
     * wms实体仓 + 实体仓标记查询
     *
     * @param wmsWarehouseCode wms实体仓编码
     * @param warehouseTag     实体仓标记
     * @return
     */
    @Override
    public List<EntityWarehouseDTO> findDTOByWmsCodeAndTag(String wmsWarehouseCode, Integer warehouseTag) {
        if (StringUtil.isEmpty(wmsWarehouseCode)) {
            return new ArrayList<>();
        }
        Example example = new Example(EntityWarehouseDO.class);
        example.createCriteria()
                .andEqualTo("deleted", false)
                .andEqualTo("wmsWarehouseCode", wmsWarehouseCode)
                .andCondition("warehouse_tag & " + warehouseTag + " = ", warehouseTag);
        List<EntityWarehouseDO> entityWarehouseDOS = entityWarehouseMapper.selectByExample(example);
        return ConvertUtil.listConvert(entityWarehouseDOS, EntityWarehouseDTO.class);
    }

    private void checkTagDistinct(Long id, String wmsWarehouseCode, EntityWarehouseTagEnums warehouseTag) {
        Example example = new Example(EntityWarehouseDO.class);
        example.createCriteria()
                .andEqualTo("deleted", false)
                .andEqualTo("wmsWarehouseCode", wmsWarehouseCode)
                .andCondition("warehouse_tag & " + warehouseTag.getCode() + " = ", warehouseTag.getCode())
                .andNotEqualTo("id", id);
        List<EntityWarehouseDO> entityWarehouseDOS = entityWarehouseMapper.selectByExample(example);
        if (CollUtil.isNotEmpty(entityWarehouseDOS)) {
            throw new ArgsInvalidException("该实体仓已存在" + warehouseTag.getDesc() + "的账册编码");
        }
    }


}
