package com.danding.cds.v2.service.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.bean.dao.ProcessTradeBookDO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.cds.v2.mapper.ProcessTradeBookMapper;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <p>
 * 加工贸易账册 baseService 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Service
public class ProcessTradeBookBaseService {

    @Resource
    private ProcessTradeBookMapper mapper;

    public ProcessTradeBookDO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookDO> findById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return null;
        }
        Example example = new Example(ProcessTradeBookDO.class);
        example.createCriteria().andIn("id", idList);
        return this.selectByExample(example);
    }

    public ProcessTradeBookDO selectByPrimaryKey(Long id) {
        return mapper.selectByPrimaryKey(id);
    }

    public List<ProcessTradeBookDO> selectByExample(Example example) {
        return mapper.selectByExample(example);
    }

    public ProcessTradeBookDO selectOneByExample(Example example) {
        return mapper.selectOneByExample(example);
    }

    public void updateByPrimaryKey(ProcessTradeBookDO record) {
        mapper.updateByPrimaryKey(record);
    }

    public void updateByPrimaryKeySelective(ProcessTradeBookDO record) {
        mapper.updateByPrimaryKeySelective(record);
    }

    public void updateByExample(ProcessTradeBookDO record, Example example) {
        mapper.updateByExample(record, example);
    }

    public void updateByExampleSelective(ProcessTradeBookDO record, Example example) {
        mapper.updateByExampleSelective(record, example);
    }

    public Integer selectCountByExample(Example example) {
        return mapper.selectCountByExample(example);
    }

    public void insertSelective(ProcessTradeBookDO record) {
        UserUtils.setCommonData(record);
        mapper.insertSelective(record);
    }

    @PageSelect
    public ListVO<ProcessTradeBookDTO> paging(ProcessTradeBookSearch search) {
        Example example = this.buildExample(search);
        List<ProcessTradeBookDO> list = mapper.selectByExample(example);
        // 分页
        ListVO<ProcessTradeBookDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, ProcessTradeBookDTO.class));
        PageInfo<ProcessTradeBookDTO> pageInfo = new PageInfo(list);
        pageInfo.setPageSize(search.getPageSize());
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example buildExample(ProcessTradeBookSearch search) {
        Example example = new Example(ProcessTradeBookDO.class);
        example.setOrderByClause("create_time DESC");
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (CollUtil.isNotEmpty(search.getSnList())) {
            criteria.andIn("sn", search.getSnList());
        }
        if (CollUtil.isNotEmpty(search.getPreNoList())) {
            criteria.andIn("preNo", search.getPreNoList());
        }
        if (CollUtil.isNotEmpty(search.getProcessTradeBookNoList())) {
            criteria.andIn("processTradeBookNo", search.getProcessTradeBookNoList());
        }
        if (StrUtil.isNotBlank(search.getCompanyName())) {
            criteria.andLike("companyName", "%" + search.getCompanyName() + "%");
        }
        if (Objects.nonNull(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (Objects.nonNull(search.getEnable())) {
            criteria.andEqualTo("enable", search.getEnable());
        }
        if (Objects.nonNull(search.getCreateDateFrom()) && Objects.nonNull(search.getCreateDateTo())) {
            criteria.andBetween("createTime", new Date(search.getCreateDateFrom()), new Date(search.getCreateDateTo()));
        }
        return example;
    }
}