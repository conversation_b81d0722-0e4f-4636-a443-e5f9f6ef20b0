package com.danding.cds.v2.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordBaseInfoSubmitDTO;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordSubmit;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordWarehouseInfoDTO;
import com.danding.cds.item.api.enums.GoodsRecordChannel;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.item.entity.es.GoodsRecordEsDO;
import com.danding.cds.item.es.CentralGoodsRecordEsDao;
import com.danding.cds.out.api.CentralGoodsRecordRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.*;
import com.danding.cds.out.bean.vo.res.*;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.v2.service.RecordItemAssociateInfoService;
import com.danding.cds.v2.service.base.RecordWarehouseProductIdBaseService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 备案中心化服务
 * @date 2023/8/15 11:29
 */
@Slf4j
@DubboService
public class CentralGoodsRecordRpcImpl implements CentralGoodsRecordRpc {
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private RecordItemAssociateInfoService recordItemAssociateInfoService;
    @Autowired
    private RecordCustomsService recordCustomsService;
    @Autowired
    private RecordWarehouseService recordWarehouseService;
    @Autowired
    private CentralGoodsRecordEsDao centralGoodsRecordEsDao;
    @DubboReference
    private CustomsHsService customsHsService;
    @DubboReference
    private CustomsCountryService customsCountryService;
    @DubboReference
    private CustomsUomService customsUomService;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;
    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;
    @Resource
    private Validator validator;
    @DubboReference
    private CustomsDictionaryService dictionaryService;
    @DubboReference
    private CustomsBookService customsBookService;

    @Override
    public RpcResult<Long> saveGoodsRecord(GoodsRecordSubmitReqVO submit) throws Exception {
        log.info("CentralGoodsRecordRpcImpl saveGoodsRecord submit={}", JSON.toJSONString(submit));
        try {
            Long recordId = saveGoodsRecordCore(submit);
            return RpcResult.success(recordId);
        } catch (Exception e) {
            log.error("保存商品备案失败 error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    private Long saveGoodsRecordCore(GoodsRecordSubmitReqVO submit) {
        CentralGoodsRecordSubmit centralGoodsRecordSubmitDTO = buildCentralSubmit(submit);
        log.info("CentralGoodsRecordRpcImpl saveGoodsRecord centralGoodsRecordSubmitDTO={}", JSON.toJSONString(centralGoodsRecordSubmitDTO));
        Long recordId = goodsRecordService.saveCentralCustomsRecord(centralGoodsRecordSubmitDTO);
        return recordId;
    }

    private CentralGoodsRecordSubmit buildCentralSubmit(GoodsRecordSubmitReqVO submit) {
        //将请求转为内部dto
        CentralGoodsRecordSubmit centralGoodsRecordSubmitDTO = new CentralGoodsRecordSubmit();
        this.buildCentralBaseInfo(submit, centralGoodsRecordSubmitDTO);
        this.buildCentralWarehouseInfo(submit, centralGoodsRecordSubmitDTO);
        centralGoodsRecordSubmitDTO.setRecordType(submit.getRecordType());
        centralGoodsRecordSubmitDTO.setAutoRecordFlag(submit.getAutoRecordFlag());
        centralGoodsRecordSubmitDTO.setSource(submit.getSource());
        return centralGoodsRecordSubmitDTO;
    }

    private void buildCentralWarehouseInfo(GoodsRecordSubmitReqVO submit, CentralGoodsRecordSubmit centralGoodsRecordSubmitDTO) {
        CentralGoodsRecordWarehouseInfoDTO centralGoodsRecordWarehouseInfoDTO = new CentralGoodsRecordWarehouseInfoDTO();
        GoodsRecordWarehouseInfoReqVO goodsRecordWarehouseInfoReqVO = submit.getGoodsRecordWarehouseInfo();
        if (goodsRecordWarehouseInfoReqVO != null) {
            BeanUtils.copyProperties(goodsRecordWarehouseInfoReqVO, centralGoodsRecordWarehouseInfoDTO);
        }
        centralGoodsRecordSubmitDTO.setCentralGoodsRecordWarehouseInfoDTO(centralGoodsRecordWarehouseInfoDTO);
    }

    private void buildCentralBaseInfo(GoodsRecordSubmitReqVO submit, CentralGoodsRecordSubmit centralGoodsRecordSubmitDTO) {
        CentralGoodsRecordBaseInfoSubmitDTO centralGoodsRecordBaseInfoDTO = new CentralGoodsRecordBaseInfoSubmitDTO();
        GoodsRecordBaseInfoReqVO goodsRecordBaseInfoReqVO = submit.getGoodsRecordBaseInfo();
        if (goodsRecordBaseInfoReqVO != null) {
            BeanUtils.copyProperties(goodsRecordBaseInfoReqVO, centralGoodsRecordBaseInfoDTO);
        }
        centralGoodsRecordBaseInfoDTO.setUserId(submit.getGoodsRecordBaseInfo().getTenantId());
        centralGoodsRecordBaseInfoDTO.setUserName(submit.getGoodsRecordBaseInfo().getTenantName());
        centralGoodsRecordBaseInfoDTO.setEnable(1);
        centralGoodsRecordBaseInfoDTO.setChannel(GoodsRecordChannel.LOGISTICS.getValue());
        centralGoodsRecordSubmitDTO.setCentralGoodsRecordBaseInfoDTO(centralGoodsRecordBaseInfoDTO);
    }

    @Override
    public RpcResult<Long> auditGoodsRecord(Long goodsRecordId, String customsCode) throws Exception {
        log.info("CentralGoodsRecordRpcImpl auditGoodsRecord goodsRecordId={} customsCode={}", goodsRecordId, customsCode);
        try {
            goodsRecordService.auditCentralGoodsRecord(goodsRecordId, customsCode);
            return RpcResult.success("提交审核成功");
        } catch (Exception e) {
            log.error("提交审核失败 error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public RpcResult<Long> saveAndAuditGoodsRecord(GoodsRecordSubmitReqVO submit) throws Exception {
        try {
            CentralGoodsRecordSubmit centralGoodsRecordSubmit = this.buildCentralSubmit(submit);
            Long recordId = goodsRecordService.saveAndAuditGoodsRecord(centralGoodsRecordSubmit);

            return RpcResult.success(recordId);
        } catch (Exception e) {
            log.error("提交审核失败 error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    public RpcResult<ListVO<CentralGoodsRecordPagingResVO>> paging(CentralGoodsRecordSearchReqVo reqVo) throws Exception {
        log.info("CentralGoodsRecordRpc paging reqVo={}", JSON.toJSONString(reqVo));
        try {
            //查询es内容
            CentralGoodsRecordSearchCondition condition = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordSearchCondition.class);
            if (StrUtil.isNotBlank(reqVo.getCustomsBookNo())) {
                CustomsBookDTO customsBookDTO = customsBookService.findByCode(reqVo.getCustomsBookNo());
                condition.setCustomsBookId(customsBookDTO == null ? -1L : customsBookDTO.getId());
            }
            Page<GoodsRecordEsDO> esDOPage = centralGoodsRecordEsDao.paging(condition);
            ListVO<CentralGoodsRecordPagingResVO> goodsRecordDTOListVO = new ListVO<>();
            PageResult pageResult = new PageResult();
            pageResult.setTotalCount((int) esDOPage.getTotalElements());
            pageResult.setTotalPage(esDOPage.getTotalPages());
            pageResult.setCurrentPage(condition.getCurrentPage());
            pageResult.setPageSize(condition.getPageSize());
            goodsRecordDTOListVO.setPage(pageResult);

            List<GoodsRecordEsDO> content = esDOPage.getContent();
            List<CentralGoodsRecordPagingResVO> centralGoodsRecordPagingResVOS = content.stream().map(g -> {
                //拆分id
                CentralGoodsRecordPagingResVO resVo = ConvertUtil.beanConvert(g, CentralGoodsRecordPagingResVO.class);
                String recordEsDOId = g.getId();
                if (recordEsDOId.contains("_")) {
                    String[] split = recordEsDOId.split("_");
                    String realId = split[1];
                    resVo.setId(Long.valueOf(realId));
                } else {
                    resVo.setId(Long.valueOf(recordEsDOId));
                }
                resVo.setUserId(g.getTenantId());
                resVo.setCreateTime(new Date(g.getCreateTime()));
                resVo.setUpdateTime(new Date(g.getUpdateTime()));
                return resVo;
            }).collect(Collectors.toList());
            List<Long> idList = centralGoodsRecordPagingResVOS.stream().map(CentralGoodsRecordPagingResVO::getId).distinct().collect(Collectors.toList());
            List<RecordCustomsDTO> recordCustomsDTOS = recordCustomsService.findByRecordId(idList);
            Map<Long, List<RecordCustomsDTO>> idCustomsMap = recordCustomsDTOS.stream().collect(Collectors.groupingBy(RecordCustomsDTO::getRecordId));
            List<RecordWarehouseDTO> recordWarehouseDTOList = recordWarehouseService.findByRecordId(idList);
            Map<Long, List<RecordWarehouseDTO>> idWarehouseMap = recordWarehouseDTOList.stream().collect(Collectors.groupingBy(RecordWarehouseDTO::getRecordId));
            centralGoodsRecordPagingResVOS.forEach(resVo -> {
                Long id = resVo.getId();
                if (idCustomsMap.containsKey(id)) {
                    List<RecordCustomsDTO> recordCustomsDTOList = idCustomsMap.get(id);
                    // 展示关务备注
                    recordCustomsDTOList.stream()
                            .filter(i -> StrUtil.isNotEmpty(i.getGuanWuRemark()))
                            .max(Comparator.comparing(RecordCustomsDTO::getUpdateTime))
                            .ifPresent(newerCustomsDTO -> resVo.setGuanWuRemark(newerCustomsDTO.getGuanWuRemark()));
                    //过滤待申报口岸 erp提交状态为1
                    String waitCommitCustoms = recordCustomsDTOList.stream().filter(r -> Objects.equals(r.getErpCommitStatus(), 1)
                            || Objects.equals(r.getStatus(), GoodsRecordStatusEnum.WAIT_COMMIT.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                    String examining = recordCustomsDTOList.stream().filter(r -> !Objects.equals(r.getErpCommitStatus(), 1) && Objects.equals(r.getStatus(), GoodsRecordStatusEnum.WAIT_EXAMINE.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                    String success = recordCustomsDTOList.stream().filter(r -> !Objects.equals(r.getErpCommitStatus(), 1) && Objects.equals(r.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                    resVo.setWaitCommitCustoms(waitCommitCustoms);
                    resVo.setExaminingCustoms(examining);
                    resVo.setExaminedCustoms(success);
                    String reason = recordCustomsDTOList.stream().filter(r -> Objects.equals(r.getStatus(), GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode()))
                            .sorted(Comparator.comparing(RecordCustomsDTO::getSubmitTime)).map(RecordCustomsDTO::getReason).findFirst().orElse(null);
                    resVo.setRejectReason(reason);
                    if (idWarehouseMap.containsKey(id)) {
                        List<RecordWarehouseDTO> recordWarehouseDTOS = idWarehouseMap.get(id);
                        String bookNoList = recordWarehouseDTOS.stream().map(RecordWarehouseDTO::getCustomsBookNo).distinct().collect(Collectors.joining(","));
                        resVo.setCustomsBookNo(bookNoList);
                    }
                }
                //如果存在erp待提交的标识 就将状态设为待提交
                if (Objects.equals(resVo.getErpCommitStatus(), 1)) {
                    resVo.setRecordStatus(GoodsRecordStatusEnum.WAIT_COMMIT.getCode());
                }
                GoodsRecordStatusEnum goodsRecordStatusEnum = GoodsRecordStatusEnum.getEnum(resVo.getRecordStatus());
                if (Objects.nonNull(goodsRecordStatusEnum)) {
                    resVo.setRecordStatusDesc(goodsRecordStatusEnum.getDesc());
                }
            });
            goodsRecordDTOListVO.setDataList(centralGoodsRecordPagingResVOS);
            return RpcResult.success(goodsRecordDTOListVO);
        } catch (Exception e) {
            log.error("paging error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取分页数据失败");
    }

    @Override
    public RpcResult<CentralRecordStatusCountResVO> getStatusCount(CentralGoodsRecordSearchReqVo reqVo, List<Integer> statusList) throws Exception {
        log.debug("getStatusCount reqVo={} statusList={}", JSON.toJSONString(reqVo), JSON.toJSONString(statusList));
        try {
            CentralGoodsRecordSearchCondition condition = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordSearchCondition.class);
            CentralGoodsRecordStatusCount centralGoodsRecordStatusCount = centralGoodsRecordEsDao.countGoodsRecord(condition, statusList);
            CentralRecordStatusCountResVO centralRecordStatusCountResVO = ConvertUtil.beanConvert(centralGoodsRecordStatusCount, CentralRecordStatusCountResVO.class);
            return RpcResult.success(centralRecordStatusCountResVO);
        } catch (Exception e) {
            log.error("getStatusCount error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取状态计数失败");
    }

    @Override
    public RpcResult<List<CentralGoodsRecordPagingResVO>> search(CentralGoodsRecordSearchReqVo reqVo) throws Exception {
        log.debug("search reqVo={}", JSON.toJSONString(reqVo));
        try {
            reqVo.setPageIgnore(1);
            CentralGoodsRecordSearchCondition condition = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordSearchCondition.class);
            Page<GoodsRecordEsDO> esDOPage = centralGoodsRecordEsDao.paging(condition);
            List<GoodsRecordEsDO> content = esDOPage.getContent();
            List<CentralGoodsRecordPagingResVO> centralGoodsRecordPagingResVOS = ConvertUtil.listConvert(content, CentralGoodsRecordPagingResVO.class);
            centralGoodsRecordPagingResVOS = content.stream().map(g -> {
                //拆分id
                CentralGoodsRecordPagingResVO resVo = ConvertUtil.beanConvert(g, CentralGoodsRecordPagingResVO.class);
                String recordEsDOId = g.getId();
                if (recordEsDOId.contains("_")) {
                    String[] split = recordEsDOId.split("_");
                    String realId = split[1];
                    resVo.setId(Long.valueOf(realId));
                } else {
                    resVo.setId(Long.valueOf(recordEsDOId));
                }
                resVo.setUserId(g.getTenantId());
                resVo.setCreateTime(new Date(g.getCreateTime()));
                resVo.setUpdateTime(new Date(g.getUpdateTime()));
                return resVo;
            }).collect(Collectors.toList());
            return RpcResult.success(centralGoodsRecordPagingResVOS);
        } catch (Exception e) {
            log.error("search error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询备案失败");
    }

    @Override
    public RpcResult<List<CentralGoodsRecordPagingResVO>> searchByDB(CentralGoodsRecordSearchReqVo reqVo) throws Exception {
        log.info("searchByDB reqVo={}", JSON.toJSONString(reqVo));
        try {
            reqVo.setPageIgnore(1);
            CentralGoodsRecordSearchCondition condition = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordSearchCondition.class);
            List<GoodsRecordDTO> resultList = goodsRecordService.searchByDB(condition);
            List<CentralGoodsRecordPagingResVO> centralGoodsRecordPagingResVOS = resultList.stream().map(g -> {
                //拆分id
                CentralGoodsRecordPagingResVO resVo = ConvertUtil.beanConvert(g, CentralGoodsRecordPagingResVO.class);
                resVo.setUserId(g.getTenantId());
                resVo.setCreateTime(new Date(g.getCreateTime()));
                resVo.setUpdateTime(new Date(g.getUpdateTime()));
                return resVo;
            }).collect(Collectors.toList());
            return RpcResult.success(centralGoodsRecordPagingResVOS);
        } catch (Exception e) {
            log.error("searchByDB error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询备案失败");
    }

    @Override
    public RpcResult<CentralGoodsRecordDetailResVo> detail(CentralGoodsRecordDetailReqVo reqVo) throws Exception {
        try {
            CentralGoodsRecordDetailSearchCondition searchDTO = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordDetailSearchCondition.class);
            CentralGoodsRecordDetailDTO centralGoodsRecordDetailDTO = goodsRecordService.getCentralGoodsRecordDetail(searchDTO, searchDTO.getCustomsCode());
            CentralGoodsRecordDetailResVo resVo = this.convertDTO2VO(centralGoodsRecordDetailDTO);
            return RpcResult.success(resVo);
        } catch (Exception e) {
            log.error("detail 获取失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取详情失败");
    }

    private CentralGoodsRecordDetailResVo convertDTO2VO(CentralGoodsRecordDetailDTO centralGoodsRecordDetailDTO) {
        if (Objects.isNull(centralGoodsRecordDetailDTO)) {
            return null;
        }
        CentralGoodsRecordDetailResVo resVo = new CentralGoodsRecordDetailResVo();
        CentralGoodsRecordBaseInfoDTO baseInfoVO = centralGoodsRecordDetailDTO.getBaseInfoVO();
        CentralGoodsRecordBaseInfoResVO infoResVO = ConvertUtil.beanConvert(baseInfoVO, CentralGoodsRecordBaseInfoResVO.class);
        if (Objects.nonNull(infoResVO) && StrUtil.isNotBlank(infoResVO.getDeclareCurrency())) {
            List<CustomsDictionaryDTO> currencyErpList = dictionaryService.findByType(DataDictionaryTypeEnums.CURRENCY_ERP.getValue());
            Map<String, String> currencyMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
            String declareCurrencyDesc = currencyMap.get(infoResVO.getDeclareCurrency());
            if (StrUtil.isNotBlank(declareCurrencyDesc)) {
                infoResVO.setDeclareCurrencyDesc(declareCurrencyDesc);
                currencyErpList.stream()
                        .filter(c -> c.getName().equals(declareCurrencyDesc))
                        .findFirst().ifPresent(c -> {
                            infoResVO.setDeclareCurrencyCodeErp(c.getCode());
                        });
            }
        }
        resVo.setBaseInfoVO(infoResVO);
        List<CentralGoodsRecordCustomsInfoDTO> customsWarehouseInfoVOList = centralGoodsRecordDetailDTO.getCustomsWarehouseInfoVOList();
        List<CentralGoodsRecordCustomsInfoResVO> customsInfoResVOS = customsWarehouseInfoVOList.stream().filter(Objects::nonNull).map(c -> {
            CentralGoodsRecordCustomsInfoResVO customsInfoResVO = ConvertUtil.beanConvert(c, CentralGoodsRecordCustomsInfoResVO.class);
            if (Objects.nonNull(customsInfoResVO.getFirstUnit())) {
                CustomsUomDTO customsUomDTO = customsUomService.findByCode(customsInfoResVO.getFirstUnit());
                if (Objects.nonNull(customsUomDTO)) {
                    customsInfoResVO.setFirstUnitDesc(customsUomDTO.getName());
                }
            }
            if (Objects.nonNull(customsInfoResVO.getSecondUnit())) {
                CustomsUomDTO customsUomDTO = customsUomService.findByCode(customsInfoResVO.getSecondUnit());
                if (Objects.nonNull(customsUomDTO)) {
                    customsInfoResVO.setSecondUnitDesc(customsUomDTO.getName());
                }
            }
            if (Objects.nonNull(customsInfoResVO.getOriginCountry())) {
                CustomsCountryDTO customsCountryDTO = customsCountryService.findByCode(customsInfoResVO.getOriginCountry());
                if (Objects.nonNull(customsCountryDTO)) {
                    customsInfoResVO.setOriginCountryName(customsCountryDTO.getName());
                }
            }
            List<CentralWarehouseDTO> warehouseResVOList = c.getWarehouseResVOList();
            if (!CollectionUtils.isEmpty(warehouseResVOList)) {
                List<CentralWarehouseResVO> centralWarehouseResVOS = ConvertUtil.listConvert(warehouseResVOList, CentralWarehouseResVO.class);
                customsInfoResVO.setWarehouseResVOList(centralWarehouseResVOS);
            }
            return customsInfoResVO;
        }).collect(Collectors.toList());

        resVo.setCustomsWarehouseInfoVOList(customsInfoResVOS);
        return resVo;
    }

    @Override
    public RpcResult<CentralGoodsRecordDetailResVo> selectOne(CentralGoodsRecordDetailReqVo reqVo) throws Exception {
        try {
            CentralGoodsRecordDetailSearchCondition searchDTO = ConvertUtil.beanConvert(reqVo, CentralGoodsRecordDetailSearchCondition.class);
            CentralGoodsRecordDetailDTO centralGoodsRecordDetailDTO = goodsRecordService.getCentralGoodsRecordDetail(searchDTO, null);
            CentralGoodsRecordDetailResVo resVo = this.convertDTO2VO(centralGoodsRecordDetailDTO);
            return RpcResult.success(resVo);
        } catch (
                Exception e) {
            log.error("detail 获取失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取详情失败");
    }

    @Override
    public RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListById(Long goodsRecordId) {
        try {
            List<RecordCustomsDTO> customsDTOList = goodsRecordService.recordCustomsDTOList(goodsRecordId);
            List<CentralGoodsRecordCustomsInfoResVO> customsInfoResVOS = ConvertUtil.listConvert(customsDTOList, CentralGoodsRecordCustomsInfoResVO.class);
            populateCustomsInfo(customsInfoResVOS);
            return RpcResult.success(customsInfoResVOS);
        } catch (Exception e) {
            log.error("getCustomsListById error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取口岸列表失败");
    }

    @Override
    public RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListByUserId(String userId) {
        try {
            List<RecordCustomsDTO> customsDTOList = goodsRecordService.getCustomsListByUserId(userId);
            log.info("getCustomsListByUserId customsDTOList={}", JSON.toJSONString(customsDTOList));
            List<CentralGoodsRecordCustomsInfoResVO> customsInfoResVOS = ConvertUtil.listConvert(customsDTOList, CentralGoodsRecordCustomsInfoResVO.class);
            populateCustomsInfo(customsInfoResVOS);
            return RpcResult.success(customsInfoResVOS);
        } catch (Exception e) {
            log.error("getCustomsListById error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取口岸列表失败");
    }

    @Override
    public RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListByUserIdAndGoodsCode(String userId, String goodsCode) {
        try {
            List<RecordCustomsDTO> customsDTOList = goodsRecordService.getCustomsListByUserIdAndGoodsCode(userId, goodsCode);
            List<CentralGoodsRecordCustomsInfoResVO> customsInfoResVOS = ConvertUtil.listConvert(customsDTOList, CentralGoodsRecordCustomsInfoResVO.class);
            populateCustomsInfo(customsInfoResVOS);
            return RpcResult.success(customsInfoResVOS);
        } catch (Exception e) {
            log.error("getCustomsListById error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取口岸列表失败");
    }

    @Override
    public RpcResult<String> syncCentralEntityWarehouse(CentralRecordSyncWarehouseSubmitReqVO submit) {
        try {
            log.info("syncCentralEntityWarehouse submit={}", JSON.toJSONString(submit));
            if (Objects.isNull(submit)) {
                throw new ArgsInvalidException("备案中心化搬仓同步失败 入参为空");
            }
            if (Objects.isNull(submit.getRecordId())) {
                throw new ArgsInvalidException("备案搬仓同步失败 参数:备案id为空");
            }
            if (Objects.isNull(submit.getCustomsCode())) {
                throw new ArgsInvalidException("备案搬仓同步失败 参数:口岸为空");
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(submit.getWmsWarehouseList())) {
                throw new ArgsInvalidException("备案搬仓同步失败 参数:实体仓为空");
            }
            CentralRecordWarehouseSubmit submitList = ConvertUtil.beanConvert(submit, CentralRecordWarehouseSubmit.class);
            recordWarehouseService.syncCentralEntityWarehouse(submitList);
            return RpcResult.success("同步成功");
        } catch (Exception e) {
            log.error("syncCentralEntityWarehouse error={}", e.getMessage(), e);
            return RpcResult.error("同步失败 " + e.getMessage());
        }
    }

    private void populateCustomsInfo(List<CentralGoodsRecordCustomsInfoResVO> customsInfoResVOS) {
        customsInfoResVOS.forEach(customs -> {
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(customs.getRecordId());
            if (Objects.nonNull(goodsRecordDTO)) {
                if (Objects.nonNull(goodsRecordDTO.getOriginCountry())) {
                    CustomsCountryDTO customsCountryDTO = customsCountryService.findByCode(goodsRecordDTO.getOriginCountry());
                    if (Objects.nonNull(customsCountryDTO)) {
                        customs.setOriginCountry(customsCountryDTO.getCode());
                        customs.setOriginCountryName(customsCountryDTO.getName());
                    }
                }
            }
            String hsCode = customs.getHsCode();
            if (Objects.nonNull(hsCode)) {
                CustomsHsDTO hsDTO = customsHsService.findByCode(hsCode);
                if (Objects.nonNull(hsDTO)) {
                    BigDecimal vat = hsDTO.getVat();
                    customs.setVat(vat);
                    BigDecimal consumption = new BigDecimal("0");
                    if (hsDTO.getConsumptionFlag() == 5) {
                        consumption = hsDTO.getConsumptionNumTax();
                    }
                    if (hsDTO.getConsumptionFlag() == 10) {
                        consumption = hsDTO.getConsumptionTax();
                    }
                    customs.setConsumption(consumption);
                }
            }
        });
    }


    @Override
    public RpcResult<String> delete(Long id) throws Exception {
        log.info("删除备案 reqVO={}", JSON.toJSONString(id));
        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(id);
        if (Objects.isNull(goodsRecordDTO)) {
            throw new ArgsInvalidException("未查询到备案信息");
        }
        try {
            goodsRecordService.addDeleteFlag(goodsRecordDTO);
            return RpcResult.success("删除成功");
        } catch (ArgsInvalidException ex) {
            log.error("删除备案 error={}", ex.getMessage(), ex);
            return RpcResult.error("删除失败 " + ex.getMessage());
        } catch (Exception ex) {
            log.error("删除备案 error={}", ex.getMessage(), ex);
            return RpcResult.error("删除失败");
        }
    }

    @Override
    public RpcResult<String> delete(List<Long> ids) throws Exception {
        List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordService.findById(ids);
        for (GoodsRecordDTO goodsRecordDTO : goodsRecordDTOS) {
            try {
                goodsRecordService.addDeleteFlag(goodsRecordDTO);
            } catch (ArgsInvalidException ex) {
                log.error("删除备案 id={} error={}", goodsRecordDTO.getId(), ex.getMessage(), ex);
            } catch (Exception ex) {
                log.error("删除备案 id={} error={}", goodsRecordDTO.getId(), ex.getMessage(), ex);
            }
        }
        return RpcResult.success("删除备案提交成功");
    }

    @Override
    public RpcResult<String> updateCentralImgInfo(List<CentralUpdateImgInfoReqVo> reqVoList) {
        try {
            List<CentralUpdateImgInfoSubmit> updateImgInfoSubmitList = ConvertUtil.listConvert(reqVoList, CentralUpdateImgInfoSubmit.class);
            goodsRecordService.updateCentralImgInfo(updateImgInfoSubmitList);
            return RpcResult.success("更新图片信息成功");
        } catch (Exception e) {
            log.error("updateCentralImgInfo error={}", e.getMessage(), e);
            return RpcResult.error("更新图片信息失败 " + e.getMessage());
        }
    }

    @Override
    public RpcResult<String> updateCentralNameAndBarCode(List<CentralUpdateNameAndBarCodeReqVo> reqVoList) {
        try {
            List<CentralUpdateNameAndBarCodeSubmit> updateImgInfoSubmitList = ConvertUtil.listConvert(reqVoList, CentralUpdateNameAndBarCodeSubmit.class);
            goodsRecordService.updateCentralNameAndBarCode(updateImgInfoSubmitList);
            return RpcResult.success("更新备案名称与条码信息成功");
        } catch (Exception e) {
            log.error("updateCentralNameAndBarCode error={}", e.getMessage(), e);
            return RpcResult.error("更新备案名称与条码信息失败 " + e.getMessage());
        }

    }

    @Override
    public RpcResult<Map<String, String>> queryAssociateProductIdByUnifiedProductId(List<String> unifiedProductIds) {
        log.info("queryAssociateProductIdByUnifiedProductId unifiedProductIds={}", JSON.toJSONString(unifiedProductIds));
        try {
            if (CollectionUtils.isEmpty(unifiedProductIds)) {
                return RpcResult.success(new HashMap<>());
            }
            List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordService.findByProId(unifiedProductIds);
            Map<String, List<GoodsRecordDTO>> productIdCountMap = goodsRecordDTOS.stream()
                    .filter(r -> r.getTenantId() != null)
                    .filter(r -> {
                        Integer goodsRecordTag = r.getGoodsRecordTag();
                        List<Integer> goodsRecordTags = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordTag);
                        return !goodsRecordTags.contains(GoodsRecordTagEnums.ERP_DELETE.getCode());
                    })
                    .collect(Collectors.groupingBy(GoodsRecordDTO::getProductId));

            List<Long> filteredGoodsRecordIds = new ArrayList<>();
            productIdCountMap.forEach((productId, records) -> {
                // 获取最新的记录
                GoodsRecordDTO latestRecord = getLatestGoodsRecord(records);
                if (latestRecord != null) {
                    filteredGoodsRecordIds.add(latestRecord.getId());
                }
            });
            // 构建 productId 到 recordWarehouseProductId 的映射
            List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(filteredGoodsRecordIds);
            if (CollectionUtils.isEmpty(recordWarehouseProductIdDTOList)) {
                return RpcResult.success(new HashMap<>());
            }
            Map<Long, List<RecordWarehouseProductIdDTO>> recordWarehouseProductIdDTOMap = recordWarehouseProductIdDTOList.stream()
                    .collect(Collectors.groupingBy(RecordWarehouseProductIdDTO::getRecordId));
            Map<String, String> result = new HashMap<>();
            goodsRecordDTOS.forEach(goodsRecordDTO -> {
                if (!recordWarehouseProductIdDTOMap.containsKey(goodsRecordDTO.getId())) {
                    result.put(goodsRecordDTO.getProductId(), "");
                    return;
                }
                List<RecordWarehouseProductIdDTO> customsProductIdDTOList = recordWarehouseProductIdDTOMap.get(goodsRecordDTO.getId());
                if (CollUtil.isNotEmpty(customsProductIdDTOList)) {
                    String customsProductIds = customsProductIdDTOList.stream()
                            .map(RecordWarehouseProductIdDTO::getCustomsDeclareProductId)
                            .distinct().collect(Collectors.joining(","));
                    result.put(goodsRecordDTO.getProductId(), customsProductIds);
                }
            });
            log.info("queryAssociateProductIdByUnifiedProductId res={}", JSON.toJSONString(result));
            return RpcResult.success(result);

        } catch (ArgsInvalidException ex) {
            log.error("queryAssociateProductIdByUnifiedProductId error={}", ex.getMessage(), ex);
        }
        return RpcResult.error("查询失败");
    }

    @Override
    public RpcResult<PoizonGoodsRecordDetailResVo> queryByPoizon(PoizonGoodsRecordDetailReqVo reqVo) {
        log.info("queryByPoizon reqVo={}", JSON.toJSONString(reqVo));
        String errorMsg = ValidatorUtils.doValidator(validator, reqVo);
        if (StrUtil.isNotBlank(errorMsg)) {
            return RpcResult.error(errorMsg);
        }
        try {
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(reqVo.getErpEntityWarehouseCode());
            EntityWarehouseDTO entityWarehouseDTO = entityWarehouseDTOList.stream()
                    .filter(e -> Objects.equals(e.getEnable(), 1))
                    .max(Comparator.comparing(EntityWarehouseDTO::getCreateTime))
                    .orElse(null);
            log.info("queryByPoizon ErpEntityWarehouseCode={} entityWarehouseDTO={}", reqVo.getErpEntityWarehouseCode(), JSON.toJSONString(entityWarehouseDTO));
            if (entityWarehouseDTO == null) {
                return RpcResult.error("未查询到实体仓信息");
            }
            String customsCode = entityWarehouseDTO.getCustomsCode();

            CentralGoodsRecordDetailSearchCondition condition = new CentralGoodsRecordDetailSearchCondition();
            condition.setSku(reqVo.getSku());
            condition.setCustomsCode(customsCode);
            condition.setUserId(reqVo.getUserId());
            log.info("queryByPoizon getCentralGoodsRecordDetail condition={}", JSON.toJSONString(condition));
            CentralGoodsRecordDetailDTO centralGoodsRecordDetail = goodsRecordService.getCentralGoodsRecordDetail(condition, customsCode);
            log.info("queryByPoizon getCentralGoodsRecordDetail centralGoodsRecordDetail={}", JSON.toJSONString(centralGoodsRecordDetail));
            if (centralGoodsRecordDetail == null) {
                return RpcResult.error("未查询到备案信息");
            }
            Map<String, String> uomMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
            CentralGoodsRecordBaseInfoDTO baseInfoVO = centralGoodsRecordDetail.getBaseInfoVO();
            PoizonGoodsRecordDetailResVo result = new PoizonGoodsRecordDetailResVo();
            result.setHsCode(baseInfoVO.getHsCode());
            List<PoizonGoodsRecordDetailResVo.FilingRecordInfo> list = centralGoodsRecordDetail.getCustomsWarehouseInfoVOList().stream().map(c -> {
                PoizonGoodsRecordDetailResVo.FilingRecordInfo filingRecordInfo = new PoizonGoodsRecordDetailResVo.FilingRecordInfo();
                filingRecordInfo.setRecordStatus(c.getStatus());
                filingRecordInfo.setHsCode(c.getHsCode());
                filingRecordInfo.setFirstQuantity(c.getFirstUnitAmount());
                filingRecordInfo.setFirstUnit(uomMap.getOrDefault(c.getFirstUnit(), ""));
                filingRecordInfo.setSecondQuantity(c.getSecondUnitAmount());
                if (c.getSecondUnit() != null) {
                    filingRecordInfo.setSecondUnit(uomMap.getOrDefault(c.getSecondUnit(), ""));
                }
                filingRecordInfo.setBarCode(baseInfoVO.getBarCode());
                filingRecordInfo.setItemName(baseInfoVO.getGoodsRecordName());
                filingRecordInfo.setSpecification(baseInfoVO.getModel());
                return filingRecordInfo;
            }).collect(Collectors.toList());
            result.setRecordList(list);
            log.info("queryByPoizon req={} result={}", JSON.toJSONString(reqVo), JSON.toJSONString(result));
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("queryByPoizon req={} error={}", JSON.toJSONString(reqVo), e.getMessage(), e);
            return RpcResult.error("查询备案失败");
        }
    }

    private GoodsRecordDTO getLatestGoodsRecord(List<GoodsRecordDTO> records) {
        if (records.isEmpty()) {
            return null;
        }
        return records.stream()
                .max(Comparator.comparingLong(GoodsRecordDTO::getId))
                .orElse(null);
    }
}
