package com.danding.cds.v2.mapper;

import com.danding.cds.v2.bean.dao.AiRecommendHsCodeDO;
import com.danding.logistics.mybatis.common.Mapper;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;

/**
 * <p>
 * AI推荐商品编码表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface AiRecommendHsCodeMapper extends Mapper<AiRecommendHsCodeDO>, InsertListMapper<AiRecommendHsCodeDO>, BatchUpdateMapper<AiRecommendHsCodeDO>, AggregationPlusMapper<AiRecommendHsCodeDO> {

}
