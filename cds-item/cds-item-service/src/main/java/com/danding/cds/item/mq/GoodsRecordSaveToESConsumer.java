package com.danding.cds.item.mq;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.sdk.utils.DateUtils;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.item.entity.es.GoodsRecordEsDO;
import com.danding.cds.item.entity.es.RecordCustomsEsDO;
import com.danding.cds.item.entity.es.RecordProductEsDO;
import com.danding.cds.item.entity.es.RecordWarehouseEsDO;
import com.danding.cds.item.es.RecordEsDao;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.v2.service.base.RecordProductBaseService;
import com.danding.cds.v2.service.base.RecordWarehouseProductIdBaseService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.dt.component.canal.mq.AbstractCanalMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 商品备案ES操作
 * @date 2022/6/7
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "danding_ccs_goods_record_topic", consumerGroup = "ccs_goods_record_save_to_es", consumeMode = ConsumeMode.ORDERLY)
public class GoodsRecordSaveToESConsumer extends AbstractCanalMQService<GoodsRecordEsDTO> implements RocketMQListener<FlatMessage>, ApplicationRunner {

    @Autowired
    private RecordEsDao goodsRecordEsDao;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private RecordCustomsService recordCustomsService;

    @DubboReference
    private RecordWarehouseService recordWarehouseService;

    @Autowired
    private RecordWarehouseProductIdBaseService recordWarehouseProductIdBaseService;

    @Autowired
    private RecordProductBaseService recordProductBaseService;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    @Override
    protected void insert(GoodsRecordEsDTO goodsRecordDTO) {
        try {
            initGoodsRecordToEs(goodsRecordDTO);
        } catch (Exception e) {
            log.error("GoodsRecordSaveToESConsumer insert error: " + e.getMessage(), e);
            throw e;
        }
    }

    @Override
    protected void update(GoodsRecordEsDTO before, GoodsRecordEsDTO after) {
        Long tenantryId = after.getTenantryId();
        SimpleTenantHelper.setTenantId(tenantryId);
        GoodsRecordDTO goodsRecord = goodsRecordService.findById(after.getId());
        if (Objects.isNull(goodsRecord)) {
            log.error("根据商品备案Id - {} 未找到", after.getId());
            return;
        }
        // 判断下载
        goodsRecordService.updateGoodsRecordTagByCountryCode(goodsRecord.getId(), goodsRecord.getOriginCountry(), goodsRecord.getGoodsRecordTag());

        this.delete(after);
        log.info("删除ES原备案 - {}", after.getGoodsRecordName());
        saveGoodsRecordES(after);
    }

    @Override
    protected void delete(GoodsRecordEsDTO goodsRecordDTO) {
        log.info("删除 - {}", goodsRecordDTO.getGoodsRecordName());
        goodsRecordEsDao.delete(SimpleTenantHelper.getTenantIdStr() + "_" + goodsRecordDTO.getId().toString());
    }

    /**
     * 初始化数据
     *
     * @param goodsRecordEsDTO
     */
    public void initGoodsRecordToEs(GoodsRecordEsDTO goodsRecordEsDTO) {
        Long tenantryId = goodsRecordEsDTO.getTenantryId();
        SimpleTenantHelper.setTenantId(tenantryId);
        GoodsRecordDTO goodsRecord = goodsRecordService.findById(goodsRecordEsDTO.getId());
        if (Objects.isNull(goodsRecord)) {
            log.warn("根据商品备案Id - {} 未找到", goodsRecordEsDTO.getId());
            return;
        }

        // 判断下载
        goodsRecordService.updateGoodsRecordTagByCountryCode(goodsRecord.getId(), goodsRecord.getOriginCountry(), goodsRecord.getGoodsRecordTag());
        saveGoodsRecordES(goodsRecordEsDTO);
    }

    /**
     * 暂时不切换此方法 等全部改完再切换
     *
     * @param goodsRecordEsDTO
     */
    // TODO: 2022/7/21 别删除以下方法
//    private void buildAndSave(GoodsRecordEsDTO goodsRecordEsDTO) {
//        GoodsRecordEsDO goodsRecordEsDO = buildGoodsRecordEs(goodsRecordEsDTO);
//        goodsRecordEsDao.save(goodsRecordEsDO);
//    }


    // TODO: 2022/7/21 别删除以下方法
//    private GoodsRecordEsDO buildGoodsRecordEs(GoodsRecordEsDTO goodsRecordEsDTO) {
//        Long goodsRecordId = goodsRecordEsDTO.getId();
//        GoodsRecordEsDO goodsRecordEsDO = ConvertUtil.beanConvert(goodsRecordEsDTO, GoodsRecordEsDO.class);
//        goodsRecordEsDO.setDeleted(goodsRecordEsDTO.getDeleted() ? 1 : 0);
//        //获取口岸信息
//        List<RecordCustomsESDTO> recordCustomsESDTOList = recordCustomsService.queryByRecordId(goodsRecordId);
//        //获取商品列表
//        List<RecordProductDTO> customsProductDTOList = recordProductBaseService.findByRecordId(goodsRecordId);
//        Map<Long, List<RecordProductDTO>> customsProductMap = customsProductDTOList.stream().collect(Collectors.groupingBy(RecordProductDTO::getRecordCustomsId));
//        //获取实体仓信息 按照口岸分组
//        List<RecordWarehouseESDTO> recordWarehouseESDTOS = recordWarehouseService.findByRecordId(goodsRecordId);
//        Map<Long, List<RecordWarehouseESDTO>> customsWarehouseMap = recordWarehouseESDTOS.stream().collect(Collectors.groupingBy(RecordWarehouseESDTO::getRecordCustomsId));
//        //获取通关料号
//        List<RecordWarehouseProductIdDTO> warehouseProductIdDTOList = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordId);
//        Map<Long, List<String>> warehouseProductIdMap = warehouseProductIdDTOList.stream().collect(Collectors.groupingBy(RecordWarehouseProductIdDTO::getRecordWarehouseId, Collectors.mapping(RecordWarehouseProductIdDTO::getCustomsDeclareProductId, Collectors.toList())));
//        List<RecordCustomsEsDO> recordCustomsEsDOList = recordCustomsESDTOList.stream().map(r -> {
//            Long recordCustomsId = r.getId();
//            RecordCustomsEsDO recordCustomsEsDO = ConvertUtil.beanConvert(r, RecordCustomsEsDO.class);
//            recordCustomsEsDO.setDeleted(r.getDeleted() ? 1 : 0);
//            recordCustomsEsDO.setSubmitDateTime(DateUtils.dateToTimeMillis(recordCustomsEsDO.getSubmitTime()));
//            //构造商品列表
//            if (customsProductMap.containsKey(recordCustomsId)) {
//                List<RecordProductDTO> recordProductDTOList = customsProductMap.get(recordCustomsId);
//                List<RecordProductEsDO> recordProductEsDOList = ConvertUtil.listConvert(recordProductDTOList, RecordProductEsDO.class);
//                recordCustomsEsDO.setRecordCustomsProducts(recordProductEsDOList);
//            }
//            //构造实体仓信息
//            if (customsWarehouseMap.containsKey(recordCustomsId)) {
//                List<RecordWarehouseESDTO> warehouseESDTOList = customsWarehouseMap.get(recordCustomsId);
//                List<RecordWarehouseEsDO> recordWarehouseEsDOList = warehouseESDTOList.stream().map(w -> {
//                    RecordWarehouseEsDO recordWarehouseEsDO = ConvertUtil.beanConvert(w, RecordWarehouseEsDO.class);
//                    Long warehouseId = w.getId();
//                    //构造通关料号
//                    if (warehouseProductIdMap.containsKey(warehouseId)) {
//                        List<String> productIdList = warehouseProductIdMap.get(warehouseId);
//                        recordWarehouseEsDO.setCustomsDeclareProductId(productIdList);
//                    }
//                    return recordWarehouseEsDO;
//                }).collect(Collectors.toList());
//                recordCustomsEsDO.setEsRecordWarehouseDOS(recordWarehouseEsDOList);
//            }
//            return recordCustomsEsDO;
//        }).collect(Collectors.toList());
//        goodsRecordEsDO.setEsRecordCustomsDO(recordCustomsEsDOList);
//        return goodsRecordEsDO;
//    }

    /**
     * 目前的平级结构需要改造
     * 备案----
     * ｜- 商品列表
     * ｜- 口岸
     * ｜- 实体仓
     * ｜- 通关料号
     *
     * @param goodsRecordEsDTO
     */
    // TODO: 2022/7/18 等有时间需要将这个方法废弃 使用#buildGoodsRecordEs
    public void saveGoodsRecordES(GoodsRecordEsDTO goodsRecordEsDTO) {
        GoodsRecordEsDO goodsRecordEsDO = new GoodsRecordEsDO();
        BeanUtil.copyProperties(goodsRecordEsDTO, goodsRecordEsDO);
        goodsRecordEsDO.setId(goodsRecordEsDTO.getTenantryId() + "_" + goodsRecordEsDO.getId());
        if (Objects.isNull(goodsRecordEsDTO.getDeleted())) {
            goodsRecordEsDO.setDeleted(0);
        } else {
            if (goodsRecordEsDTO.getDeleted()) {
                goodsRecordEsDO.setDeleted(1);
            } else {
                goodsRecordEsDO.setDeleted(0);
            }
        }
        if (Objects.nonNull(goodsRecordEsDTO.getGoodsRecordTag())) {
            List<Integer> goodsRecordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordEsDTO.getGoodsRecordTag());
            goodsRecordEsDO.setGoodsRecordTagList(goodsRecordTag);
        }
        List<RecordCustomsESDTO> recordCustomsDTOList = recordCustomsService.queryByRecordId(goodsRecordEsDTO.getId());
        if (!CollectionUtils.isEmpty(recordCustomsDTOList)) {
            List<RecordCustomsEsDO> recordCustomsEsDOS = new ArrayList<>();
            for (RecordCustomsESDTO recordCustomsEsDO : recordCustomsDTOList) {
                RecordCustomsEsDO esDO = new RecordCustomsEsDO();
                BeanUtil.copyProperties(recordCustomsEsDO, esDO);
                if (recordCustomsEsDO.getDeleted()) {
                    esDO.setDeleted(1);
                } else {
                    esDO.setDeleted(0);
                }
                esDO.setSubmitDateTime(DateUtils.dateToTimeMillis(recordCustomsEsDO.getSubmitTime()));
                recordCustomsEsDOS.add(esDO);
            }
            log.warn("备案预口岸 - {}", JSON.toJSONString(recordCustomsEsDOS));
            goodsRecordEsDO.setEsRecordCustomsDO(recordCustomsEsDOS);
        }
        //构造商品列表数据
        List<RecordProductDTO> recordProductList = recordProductBaseService.findByRecordId(goodsRecordEsDTO.getId());
        if (!CollectionUtils.isEmpty(recordProductList)) {
            List<RecordProductEsDO> recordProductEsDOList = recordProductList.stream().map(r -> {
                RecordProductEsDO recordProductEsDO = ConvertUtil.beanConvert(r, RecordProductEsDO.class);
                recordProductEsDO.setDeleted(r.getDeleted() ? 1 : 0);
                return recordProductEsDO;
            }).collect(Collectors.toList());
            goodsRecordEsDO.setEsRecordProducts(recordProductEsDOList);
        }

        List<RecordWarehouseESDTO> recordWarehouseDTOList = recordWarehouseService.findByRecordId(goodsRecordEsDTO.getId());
        List<RecordWarehouseProductIdDTO> productIdDTOS = recordWarehouseProductIdBaseService.findByRecordId(goodsRecordEsDTO.getId());
        Map<Long, List<String>> warehouseIdProductIdMap = productIdDTOS.stream().collect(
                Collectors.groupingBy(
                        RecordWarehouseProductIdDTO::getRecordWarehouseId,
                        Collectors.mapping(RecordWarehouseProductIdDTO::getCustomsDeclareProductId, Collectors.toList())
                ));
        if (!CollectionUtils.isEmpty(recordWarehouseDTOList)) {
            List<RecordWarehouseEsDO> warehouseEsDOS = new ArrayList<>();
            for (RecordWarehouseESDTO recordWarehouseESDTO : recordWarehouseDTOList) {
                RecordWarehouseEsDO esDO = new RecordWarehouseEsDO();
                BeanUtil.copyProperties(recordWarehouseESDTO, esDO);
                if (warehouseIdProductIdMap.containsKey(recordWarehouseESDTO.getId())) {
                    List<String> productIdList = warehouseIdProductIdMap.get(recordWarehouseESDTO.getId());
                    esDO.setCustomsDeclareProductId(productIdList);
                }
                if (recordWarehouseESDTO.getDeleted()) {
                    esDO.setDeleted(1);
                } else {
                    esDO.setDeleted(0);
                }
                warehouseEsDOS.add(esDO);
            }
            goodsRecordEsDO.setEsRecordWarehouseDOS(warehouseEsDOS);
        }
        log.warn("替换新备案 - {}", JSON.toJSONString(goodsRecordEsDO));
        goodsRecordEsDao.save(goodsRecordEsDO);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        String name = this.getClass().getName();
        log.info("MessageHandlerAfterInit-run class:{}", name);
    }
}
