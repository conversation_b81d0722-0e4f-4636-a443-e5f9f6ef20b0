package com.danding.cds.v2.service;

import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.bean.dto.RecordCustomsESDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/9 17:07
 */
public interface RecordCustomsService {
    Boolean checkIsNewCustoms(Long recordId, String customsCode);

    RecordCustomsDTO findByRecordIdAndCustomsCode(Long recordId, String customsCode);

    List<RecordCustomsDTO> findByRecordId(Long recordId);

    RecordCustomsDTO findById(Long id);

    List<RecordCustomsDTO> findById(List<Long> idList);

    List<RecordCustomsESDTO> queryByRecordId(Long recordId);

    List<RecordCustomsDTO> findByRecordId(List<Long> recordIdList);

    void updateStatus(Long recordId, String customsCode, Integer statusCode);

    void updateStatus(Long recordId, String customsCode, Integer statusCode, String reason);

    /**
     * @param recordId
     * @param customsCode
     * @param statusCode
     * @param reason
     * @param submitType  {@link com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum}
     */
    void updateStatus(Long recordId, String customsCode, Integer statusCode, String reason, Integer submitType);

    void clearRelation(Long recordId);

    void updateConfigProperty(Long recordId, String productId, String customsCode, Integer status, String hsCode, String firstUnit, BigDecimal firstUnitAmount, String secondUnit, BigDecimal secondUnitAmount);

    Map<Long, RecordCustomsDTO> findByProductIdListAndCustomsCode(List<String> submitProductIdList, String customsCode);

    Map<Long, RecordCustomsDTO> findByRecordIdListAndCustomsCode(List<Long> recordIdList, String customsCode);

    /**
     * 获取本次备案提交的提交类型
     * 1.备案+口岸只有一条 且口岸相当 -> 新品
     * 2.备案+口岸已有一或多条 一条的时候不为自己的时候 -> 更新
     *
     * @param recordId
     * @param customsCode
     * @return {@link com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum}
     */
    Integer getSubmitType(Long recordId, String customsCode);

    void deleteByRecordId(Long id);

    void updateBaseJson(Long record, String customsCode, String jsonString);

    void updateGuanWuRemark(Long recordId, String customsCode, String guanWuRemark);

    List<RecordCustomsDTO> findByStatus(Integer status);

    RecordCustomsDTO getNextWaitAuditRecord(List<Long> accountBookIdList, String customsCode, Long recordId);
}
