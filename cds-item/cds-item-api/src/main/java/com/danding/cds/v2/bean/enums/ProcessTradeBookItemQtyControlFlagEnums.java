package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 数量控制标记代码
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookItemQtyControlFlagEnums {

    // 1-控制数量 2-不控制数量
    CONTROL_QTY(1, "控制数量"),
    NO_CONTROL_QTY(2, "不控制数量");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookItemQtyControlFlagEnums getEnums(Integer code) {
        for (ProcessTradeBookItemQtyControlFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookItemQtyControlFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
