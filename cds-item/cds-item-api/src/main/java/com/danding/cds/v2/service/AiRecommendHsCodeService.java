package com.danding.cds.v2.service;


import com.danding.cds.v2.bean.dto.AiRecommendHsCodeDTO;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeRefreshDTO;

import java.util.List;

/**
 * <p>
 * AI推荐商品编码表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
public interface AiRecommendHsCodeService {

    void init(Long recordId);

    List<AiRecommendHsCodeDTO> recommendHsCodeList(Long recordId, String customsCode, String recordGoodsName, String recordComposition);

    void refreshCustomsRecommend(AiRecommendHsCodeRefreshDTO refreshDTO);

    void choose(Long recordId, String customsCode, String hsCode);

    void refresh(AiRecommendHsCodeRefreshDTO refreshDTO);

    void clearHistory(Long recordId);
}

