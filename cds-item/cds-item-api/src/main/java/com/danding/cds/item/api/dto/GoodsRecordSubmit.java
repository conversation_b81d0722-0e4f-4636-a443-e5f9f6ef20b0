package com.danding.cds.item.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel
public class GoodsRecordSubmit implements Serializable {
    @ApiModelProperty("ID 更新时必填")
    private Long id;

    @ApiModelProperty("SKU")
    @NotBlank(message = "SKU不能为空")
    private String skuId;

    @ApiModelProperty("料号")
//    @NotBlank(message = "料号不能为空")
    private String productId;

    @ApiModelProperty("账册id")
    private Long customsBookId;

    @ApiModelProperty("条码")
//    @NotBlank(message = "条码不能为空")
    private String barCode;

    @ApiModelProperty("备案名称")
    @NotBlank(message = "备案名称不能为空")
    private String goodsRecordName;

    @ApiModelProperty("型号")
    @NotBlank(message = "型号不能为空")
    private String model;

    @ApiModelProperty("品牌（中文）")
//    @NotBlank(message = "品牌（中文）不能为空")
    private String brand;

    @ApiModelProperty("品牌（英文）")
//    @NotBlank(message = "品牌（中文）不能为空")
    private String brandEn;

    @ApiModelProperty("申报单价")
    @DecimalMin(value = "0.000001", message = "申报单价不能小于0")
    @NotNull(message = "申报单价不能为空")
    private BigDecimal declarePrice;

    @ApiModelProperty("申报单位")
    @NotBlank(message = "申报单位不能为空")
    private String declareUnit;

    @ApiModelProperty("申报币制")
    private String declareCurrency;

    @ApiModelProperty("净重")
    @DecimalMin(value = "0.000001", message = "净重不能小于0")
    @NotNull(message = "净重不能为空")
    private BigDecimal netWeight;

    @ApiModelProperty("毛重")
    @DecimalMin(value = "0.000001", message = "毛重不能小于0")
    @NotNull(message = "毛重不能为空")
    private BigDecimal grossWeight;

    @ApiModelProperty("长")
    @Min(value = 1, message = "长不能小于1MM")
//    @NotNull(message = "长不能为空")
    private Integer length;

    @ApiModelProperty("宽")
    @Min(value = 1, message = "宽不能小于1MM")
//    @NotNull(message = "宽不能为空")
    private Integer width;

    @ApiModelProperty("高")
    @Min(value = 1, message = "高不能小于1MM")
//    @NotNull(message = "高不能为空")
    private Integer height;

    @ApiModelProperty("HS编码")
    @NotBlank(message = "HS编码不能为空")
    private String hsCode;

    @ApiModelProperty("增值税率")
    private BigDecimal vatRate;

    @ApiModelProperty("消费税率")
    private BigDecimal taxRate;

    @ApiModelProperty("成分")
    private String composition;

    @ApiModelProperty("海关申报要素")
    private String hgsbys;

    @ApiModelProperty("海关原产国")
    @NotBlank(message = "海关原产国不能为空")
    private String originCountry;

    @ApiModelProperty("功能")
    private String recordFunction;

    @ApiModelProperty("用途")
    private String recordUsage;

    @ApiModelProperty("仓库")
    private String warehouseId;

    @ApiModelProperty("租户")
    private String tenantId;

    @ApiModelProperty("法定第一计量单位")
//    @NotBlank(message = "法定第一计量单位不能为空")
    private String firstUnit;

    @ApiModelProperty("法定第一计量数量")
//    @NotNull(message = "法定第一计量数量不能为空")
//    @DecimalMin(value = "0.*********", message = "法定第一计量数量不能小于0")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("法定第二计量单位")
    private String secondUnit;

    @ApiModelProperty("法定第二计量数量")
    private BigDecimal secondUnitAmount;

    @ApiModelProperty("0:停用;1:启用(默认)")
    @Range(min = 0, max = 1, message = "启用状态标识不合法")
    private Integer enable;

    @ApiModelProperty("审核通过:1/驳回:0")
    private Integer opinion;

    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("审核状态（1：待审核  2：备案完成  4：审核驳回）")
    private Integer recordStatus;

    @ApiModelProperty("备案完成时间")
    private Long recordFinishTime;

    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("上游渠道（1：出入库系统）")
    private Integer channel;

    @ApiModelProperty("出区进口商品流水号")
    private String ioGoodsSerialNo;
    @ApiModelProperty("商品备案号(检验检疫）")
    private String goodsRegNo;
    @ApiModelProperty("进口入区申报号")
    private String importEntryDeclareNo;
    @ApiModelProperty("国检原产国")
    private String ciqOriginCountry;
    @ApiModelProperty("生产企业名称")
    private String productCompanyName;
    @ApiModelProperty("生产企业注册编号")
    private String productCompanyRegisterNumber;
    @ApiModelProperty("生产企业地址")
    private String productCompanyAddress;
    @ApiModelProperty("商品链接")
    private String productLink;
    @ApiModelProperty("正面图片")
    private String frontImage;
    @ApiModelProperty("侧面图片")
    private String sideImage;
    @ApiModelProperty("背面图片")
    private String backImage;
    @ApiModelProperty("标签图片")
    private String labelImage;
    @ApiModelProperty("保质期")
    private Integer shelfLife;
    @ApiModelProperty("外部料号")
    private String externalProductId;
    @ApiModelProperty("口岸名称")
    private String customs;
    @ApiModelProperty("口岸编码")
    private String customsCode;
    @ApiModelProperty("货品ID")
    private String goodsCode;
    @ApiModelProperty("附件名称")
    private String attachmentName;
    @ApiModelProperty("附件url")
    private String attachmentUrl;
    //来源
    private String source;
    //备案完成 允许更新
    private Boolean allowEdit;
    /**
     * 外部货品id
     */
    private String externalGoodsId;

    /**
     * 菜鸟货品id
     */
    private String cainiaoGoodsId;

    /**
     * 关务备注
     */
    private String guanWuRemark;

    /**
     * ai推荐HsCode；
     */
    private String aiRecommendHsCode;
}
