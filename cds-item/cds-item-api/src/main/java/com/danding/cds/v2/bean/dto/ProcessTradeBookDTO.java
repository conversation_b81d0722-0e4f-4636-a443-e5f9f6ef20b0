package com.danding.cds.v2.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 加工贸易账册
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProcessTradeBook对象", description = "加工贸易账册")
public class ProcessTradeBookDTO implements Serializable {


    /**
     * 企业内部编号
     */
    @ApiModelProperty(value = "企业内部编号")
    private String sn;

    /**
     * 账册id（物流账册id）
     * todo 保税加工 命名改一下实际上是加贸账册不是物流账册 id取自账册管理
     */
    private Long logisticsBookId;

    /**
     * 预录入统一编号
     */
    @ApiModelProperty(value = "预录入统一编号")
    private String preNo;

    /**
     * 加工贸易账册编号
     */
    @ApiModelProperty(value = "加工贸易账册编号")
    private String processTradeBookNo;

    @ApiModelProperty(value = "数据状态")
    private Integer status;

    @ApiModelProperty(value = "账册企业名称")
    private String companyName;

    /**
     * 主管海关
     */
    @ApiModelProperty(value = "主管海关")
    private String masterCustoms;

    /**
     * 经营单位id
     */
    @ApiModelProperty(value = "经营单位编码")
    private Long operateCompanyId;

    /**
     * 加工单位编码
     */
    @ApiModelProperty(value = "加工单位编码")
    private Long processCompanyId;

    /**
     * 申报单位编码
     */
    @ApiModelProperty(value = "申报单位编码")
    private Long declareCompanyId;

    /**
     * 录入单位编码
     */
    @ApiModelProperty(value = "录入单位编码")
    private Long inputCompanyId;

    /**
     * 申报单位类型，1-企业、2-代理公司、3-报关行
     */
    @ApiModelProperty(value = "申报单位类型，1-企业、2-代理公司、3-报关行")
    private Integer declareCompanyType;

    /**
     * 申报类型
     */
    @ApiModelProperty(value = "申报类型")
    private Integer declareType;

    /**
     * 账册类型
     */
    @ApiModelProperty(value = "账册类型")
    private Integer bookType;

    /**
     * 批准证编号
     */
    @ApiModelProperty(value = "批准证编号")
    private String approvalCertNo;

    /**
     * 企业档案库编号
     */
    @ApiModelProperty(value = "企业档案库编号")
    private String enterpriseArchiveNo;

    /**
     * 实际进口总金额
     */
    @ApiModelProperty(value = "实际进口总金额")
    private BigDecimal actualImportTotalAmount;

    /**
     * 实际出口总金额
     */
    @ApiModelProperty(value = "实际出口总金额")
    private BigDecimal actualExportTotalAmount;

    /**
     * 最大周转金额（万美金）
     */
    @ApiModelProperty(value = "最大周转金额（万美金）")
    private BigDecimal maxTurnoverAmount;

    /**
     * 备案批准日期
     */
    @ApiModelProperty(value = "备案批准日期")
    private Date recordApprovalDate;

    /**
     * 变更批准日期
     */
    @ApiModelProperty(value = "变更批准日期")
    private Date changeApprovalDate;

    /**
     * 最近核销日期
     */
    @ApiModelProperty(value = "最近核销日期")
    private Date lastVoucherClearanceDate;

    /**
     * 单耗申报环节，1-出口前 2-报核前
     */
    @ApiModelProperty(value = "单耗申报环节，1-出口前 2-报核前")
    private Integer consumptionDeclarationLink;

    /**
     * 单耗版本号控制标志
     */
    @ApiModelProperty(value = "单耗版本号控制标志")
    private Integer consumptionVersionControlFlag;

    /**
     * 最大进口金额（美元）
     */
    @ApiModelProperty(value = "最大进口金额（美元）")
    private BigDecimal maxImportAmount;

    /**
     * 核销周期
     */
    @ApiModelProperty(value = "核销周期")
    private Integer voucherClearanceCycle;

    /**
     * 核销类型
     */
    @ApiModelProperty(value = "核销类型")
    private Integer voucherClearanceType;

    /**
     * 账册变更次数
     */
    @ApiModelProperty(value = "账册变更次数")
    private Integer bookChangeCount;

    /**
     * 账册结束有效期
     */
    @ApiModelProperty(value = "账册结束有效期")
    private Date bookEndValidity;

    /**
     * 账册执行标志
     */
    @ApiModelProperty(value = "账册执行标志")
    private Integer bookExecutionFlag;

    /**
     * 账册用途，1:一般纳税人，2：特殊行业,3:保税维修, 4：委托内加工，5：保税研发，6：零关税-1（全免），7：零关税-2（自愿缴税）
     */
    @ApiModelProperty(value = "账册用途，1:一般纳税人，2：特殊行业,3:保税维修, 4：委托内加工，5：保税研发，6：零关税-1（全免），7：零关税-2（自愿缴税）")
    private Integer bookUsage;

    /**
     * 核销方式，1-企业自核 2-海关核算
     */
    @ApiModelProperty(value = "核销方式，1-企业自核 2-海关核算")
    private Integer voucherClearanceMethod;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 启用-1/禁用-0
     */
    @ApiModelProperty(value = "启用-1/禁用-0")
    private Integer enable;

    /**
     * 海关状态
     */
    private String customsStatus;

    /**
     * 海关回执
     */
    private String customsReceipt;

    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("id")
    private Long id;
}
