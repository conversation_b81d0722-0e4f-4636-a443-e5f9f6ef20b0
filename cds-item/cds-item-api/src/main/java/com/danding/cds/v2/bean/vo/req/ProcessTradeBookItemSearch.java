package com.danding.cds.v2.bean.vo.req;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Data
public class ProcessTradeBookItemSearch extends Page implements Serializable {

    /**
     * 关联账册id
     */
    @NotNull(message = "关联账册id不能为空")
    private Long refBookId;
    /**
     * 表体类型 1-料件 2-成品
     */
    @NotNull(message = "表体类型不能为空")
    private Integer goodsType;
    /**
     * 料号
     */
    private String productId;
    /**
     * 商品编码
     */
    private String hsCode;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 序号
     */
    private Integer seqNo;
}
