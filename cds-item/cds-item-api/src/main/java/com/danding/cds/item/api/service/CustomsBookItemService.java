package com.danding.cds.item.api.service;


import com.danding.cds.inventory.api.dto.InventoryChangeSqlDTO;
import com.danding.cds.inventory.api.dto.ItemLogsIdDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.v2.bean.dto.CustomsBookItemAdjustLogDTO;
import com.danding.cds.v2.bean.es.ItemTrackLogEsDTO;
import com.danding.cds.v2.bean.es.ItemTrackLogSaveDTO;
import com.danding.cds.v2.bean.vo.req.CustomsBookItemAdjustLogSearch;
import com.danding.cds.v2.bean.vo.req.ItemTrackLogSearch;
import com.danding.cds.v2.bean.vo.res.AssociateCustomsBookItemResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * @Author: shenhui
 * @Date: 2020/5/11 14:35
 */
public interface CustomsBookItemService {

    void updateGoodsSourceTokeyMaterialById(Long id);

    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    ListVO<CustomsBookItemDTO> paging(CustomsBookItemSearchCondition condition);

    /**
     * 详情
     *
     * @param id
     * @return
     */
    CustomsBookItemDTO findById(Long id);


    List<CustomsBookItemDTO> findById(List<Long> idList);

    /**
     * 编辑（新增/更新）
     */
    Long upset(CustomsBookItemSubmit customsBook) throws ArgsErrorException;

    /**
     * 直接内部调用的方法
     *
     * @param customsBook
     * @return
     * @throws ArgsErrorException
     */
    Long upsetCore(CustomsBookItemSubmitDTO customsBook) throws ArgsErrorException;

    void submitUpdateList(List<CustomsBookItemSubmit> submitList) throws Exception;

    /**
     * 启用/禁用
     *
     * @param customsBook
     * @return
     * @throws ArgsErrorException
     */
    Long upsetEnable(CustomsBookItemSubmit customsBook) throws Exception;

    void upsetEnable(List<Long> idList, Integer enable) throws Exception;

    /**
     * 更新账册
     *
     * @param submit
     * @return
     * @throws Exception
     */
    Long updBook(CustomsBookItemUpdBookSubmit submit) throws ArgsErrorException;

    /**
     * 根据账册编码以及料号进行查询
     *
     * @param customsBookNo
     * @param productId
     * @return
     * @throws ArgsErrorException
     */
    List<CustomsBookItemDTO> findByBookNoAndProId(String customsBookNo, String productId) throws ArgsErrorException;

    /**
     * 根据账册id以及料号进行查询
     *
     * @param customsBookId
     * @param productId
     * @return
     * @throws ArgsErrorException
     */
    List<CustomsBookItemDTO> findByBookIdAndProId(Long customsBookId, String productId) throws ArgsErrorException;


    List<CustomsBookItemDTO> findByProductId(String productId);

    List<CustomsBookItemDTO> findByProductIdList(List<String> productId);

    /**
     * 根据料号列表获取料号详情
     *
     * @param customsBookId 账册ID
     * @param productIds    料号
     * @return
     * @throws ArgsErrorException
     */
    List<CustomsBookItemDTO> findByBookIdAndProIds(Long customsBookId, List<String> productIds) throws ArgsErrorException;


    /**
     * 根据账册id以及商品序号进行查询
     *
     * @param customsBookId
     * @param goodsSeqNo
     * @return
     * @throws ArgsErrorException
     */
    CustomsBookItemDTO findByBookIdAndSeqNoAndProId(Long customsBookId, String goodsSeqNo, String productId) throws ArgsErrorException;

    /**
     * 根据账册id以及商品序号列表进行查询
     *
     * @param customsBookId  账册ID
     * @param goodsSeqNoList 序号列表
     * @return
     * @throws ArgsErrorException
     */
    List<CustomsBookItemDTO> findByBookIdAndSeqNoList(Long customsBookId, List<String> goodsSeqNoList);

    CustomsBookItemDTO findByBookIdAndSeqNo(Long customsBookId, String goodsSeqNo);

    /**
     * 更新库存
     *
     * @param list
     * @return
     */
    List<ItemLogsIdDTO> updateInventory(List<UpdateInventoryDTO> list);

    String updateInventory(UpdateInventoryDTO updateInventoryDTO);

    /**
     * 查询主键id
     *
     * @param customsBookId
     * @param productId
     * @param goodsSeqNo
     * @return
     */
    CustomsBookItemDTO findIdByBookIdAndProIdAndSeq(Long customsBookId, String productId, String goodsSeqNo);

    String checkAvailableNum(Long customsBookId, Map<String, BigDecimal> bookItemIdCount);


    /**
     * 查找有效的账册
     *
     * @param customsBookId 账册ID
     * @param productId     料号
     * @param goodsSeqNo    序号
     * @return
     */
    CustomsBookItemDTO findEffectiveBook(Long customsBookId, String productId, String goodsSeqNo);

    Map<Long, CustomsBookItemDTO> findByIdList(List<Long> collect);

    void updateBookItem(InventoryChangeSqlDTO inventoryChangeSqlDTO);

    void updateBookItemList(List<CustomsBookItemDTO> itemDTOS,Integer pushStatus,String pushMsgId,String pushMsg);

    void updateByPush(Integer pushStatus, String pushMsgId, String pushMsg);

    List<AssociateCustomsBookItemResVO> viewItemListByProductId(String productId, Long accountBookNoId);

    ListVO<ItemTrackLogEsDTO> pagingTrackLog(ItemTrackLogSearch search);

    void trackLogEsSave(ItemTrackLogSaveDTO itemTrackLogSaveDTO);

    void updateUnifiedProductId(Long id, Long productStockId, String unifiedProductId);

    List<CustomsBookItemDTO> findByCustomsBookRowBounds(Long accountBookId, Integer offset, Integer limit);

    void clearProductStock(Long customsBookItemId, Long productStockId);

    List<CustomsBookItemDTO> findByBookId(List<Long> bookIdList);

    CustomsBookItemDTO findByinvtNoAndProductId(String invtNo, String productId);

    void updateTotalInOutQty(CustomsBookItemDTO customsBookItemDTO, String inventoryInOutFlag, Integer changeNum);


    void updLawAmountByProIdAndBookId(String productId, Long customsBookId, String firstUnit, BigDecimal firstUnitAmount,
                                      String secondUnit, BigDecimal secondUnitAmount);

    List<CustomsBookItemDTO> findEnableByBookIdAndProIds(Long customsBookId, List<String> productIds);

    ListVO<CustomsBookItemAdjustLogDTO> pagingAdjustLog(CustomsBookItemAdjustLogSearch search);
    

//    void updateDiffType(CustomsBookItemDTO customsBookItemDTO);
}
