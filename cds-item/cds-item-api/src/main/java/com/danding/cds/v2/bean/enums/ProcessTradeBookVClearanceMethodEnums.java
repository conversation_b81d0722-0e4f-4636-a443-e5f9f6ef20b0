package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核销方式
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookVClearanceMethodEnums {

    //1-企业自核 2-海关核算
    ENTERPRISE(1, "企业自核"),
    CUSTOMS(2, "海关核算");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookVClearanceMethodEnums getEnums(Integer code) {
        for (ProcessTradeBookVClearanceMethodEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookVClearanceMethodEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
