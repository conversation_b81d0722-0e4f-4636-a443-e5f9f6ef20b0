package com.danding.cds.item.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 商品备案中心化
 * @date 2023/8/26 14:52
 */
@Data
public class CentralGoodsRecordSearchCondition extends Page {
    @ApiModelProperty("备案名称")
    private String goodsRecordName;

    @ApiModelProperty("备案时间（开始）")
    private Long recordTimeFrom;

    @ApiModelProperty("备案时间（结束）")
    private Long recordTimeTo;

    /**
     * 审核状态(0:全部/1:待审核/2:备案完成/4:备案驳回/8:待提交)
     */
    @ApiModelProperty("审核状态(0:全部/1:待审核/2:备案完成/4:备案驳回/8:待提交)")
    private Integer recordStatus;

    @ApiModelProperty("口岸")
    private String customDistrict;

    @ApiModelProperty("账册id")
    private Long customsBookId;

    @ApiModelProperty("货品ID")
    private List<String> goodsCodeList;

    @ApiModelProperty("统一料号")
    private List<String> productIdList;

    @ApiModelProperty("SKU")
    private List<String> skuList;

    @ApiModelProperty("条码")
    private List<String> barCodeList;

    @ApiModelProperty("外部料号")
    private List<String> externalProductIdList;

    @ApiModelProperty("已申报口岸数据")
    private List<String> examinedCustomsCodeList;

    @ApiModelProperty("已提交口岸数据")
    private List<String> submittedCustomsCodeList;

    @ApiModelProperty("待提交申报口岸数据")
    private List<String> waitExamineCustomsCodeList;

    @ApiModelProperty("审核中口岸数据")
    private List<String> underReviewCustomsCodeList;

    private List<String> customsCodeList;

    /**
     * true 为 过滤
     * false 为 不过滤
     */
    @ApiModelProperty("是否过滤已删除的erp数据")
    private boolean isFilterErpDeleted;

    /**
     * 备案idList
     */
    private List<Long> recordIdList;
    /**
     * 关务备注
     */
    private String guanWuRemark;
}
