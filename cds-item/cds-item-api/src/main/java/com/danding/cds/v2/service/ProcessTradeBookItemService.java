package com.danding.cds.v2.service;


import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.component.common.api.common.response.ListVO;

import java.util.List;

/**
 * <p>
 * 加工贸易账册表体 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ProcessTradeBookItemService {

    ListVO<ProcessTradeBookItemDTO> paging(ProcessTradeBookItemSearch param);

    List<ProcessTradeBookItemDTO> pagingList(ProcessTradeBookItemSearch param);

    void createOrEdit(ProcessTradeBookItemDTO param);

    void deleteItem(Long bookId, List<Long> idList);

    ProcessTradeBookItemDTO findById(Long id);

    List<ProcessTradeBookItemDTO> findById(List<Long> id);

    List<ProcessTradeBookItemDTO> matchItem(Long refBookId, Long bookId, Integer goodsType, String productId);

    ProcessTradeBookItemDTO findByProductIdAndBookId(Integer goodsType, String productId, Long bookId);

    List<ProcessTradeBookItemDTO> findByGoodsTypeAndBookId(Integer goodsType, Long bookId);

    void importExcel(Long bookId, Integer goodsType, List<ProcessTradeBookItemDTO> itemDTOList);

    List<ProcessTradeBookItemDTO> listItemAllById(Long refBookId);
}

