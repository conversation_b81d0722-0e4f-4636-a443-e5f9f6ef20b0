package com.danding.cds.v2.bean.vo.req;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;

@Data
public class ProcessTradeBookConsumptionSearch extends Page implements Serializable {

    /**
     * refBookId 关联账册id
     */
    @NotNull(message = "关联账册id不能为空")
    private Long refBookId;

    /**
     * 成品id
     */
    private List<Long> endPrdIdList;

    /**
     * 料件id
     */
    private List<Long> mtpckIdList;

    /**
     * 成品申报序号
     */
    private Integer endPrdSeqNo;

    /**
     * 料件申报序号
     */
    private Integer mtpckSeqNo;

    /**
     * 成品商品名称
     */
    private String endPrdName;

    /**
     * 料件商品名称
     */
    private String mtpckName;

    /**
     * 单耗申报状态
     */
    private Integer declareStatus;
}
