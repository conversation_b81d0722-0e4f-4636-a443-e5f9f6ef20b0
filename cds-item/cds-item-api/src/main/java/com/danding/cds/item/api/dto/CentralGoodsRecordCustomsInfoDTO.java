package com.danding.cds.item.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/8/28 15:41
 */
@Data
public class CentralGoodsRecordCustomsInfoDTO implements Serializable {
    private List<CentralWarehouseDTO> warehouseResVOList;
    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;

    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 状态 待审核0 /通过 1 /驳回 4
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * hs编码
     */
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Boolean deleted;


    /**
     * 审核方式
     */
    private Integer auditWay;
    private String auditWayDesc;

    /**
     * 口岸json快照
     */
    private String goodsRecordJson;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
