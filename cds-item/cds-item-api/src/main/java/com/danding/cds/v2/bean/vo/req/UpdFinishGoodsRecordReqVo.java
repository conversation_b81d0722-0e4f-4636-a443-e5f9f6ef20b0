package com.danding.cds.v2.bean.vo.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 更新完成的备案 ReqVo
 * (更新备案)
 */
@NoArgsConstructor
@Data
public class UpdFinishGoodsRecordReqVo implements Serializable {

    /**
     * 备案id
     */
    private Long id;

    /**
     * 口岸编码
     */
    private String customsCode;

    /**
     * 备案名称
     */
    @NotBlank(message = "备案名称不能为空")
    @Size(max = 255, message = "备案名称内容超出最大上限(255)")
    private String goodsName;
    /**
     * HsCode
     */
    @NotBlank(message = "HsCode不能为空")
    private String hsCode;
    /**
     * 法定第一单位数量
     */
    @NotNull(message = "法定第一单位数量不能为空")
    private BigDecimal firstUnitAmount;

    /**
     * 法定第一单位
     */
    @NotBlank(message = "法定第一单位不能为空")
    private String firstUnit;
    /**
     * 法定第二单位数量
     */
    private BigDecimal secondUnitAmount;
    /**
     * 法定第一单位
     */
    private String secondUnit;
    /**
     * 原产国
     */
    @NotBlank(message = "原产国不能为空")
    private String originCountry;
    /**
     * 申报单位
     */
    @NotBlank(message = "申报单位不能为空")
    private String declareUnit;
    /**
     * 申报单价
     */
    @NotNull(message = "申报单价不能为空")
//    @DecimalMin(value = "0.000001", message = "申报单价不能小于等于0")
    private BigDecimal declarePrice;
    /**
     * 申报币制
     */
    @NotBlank(message = "申报币制不能为空")
    private String declareCurrency;
    /**
     * 规格型号
     */
    @NotBlank(message = "规格型号不能为空")
    @Size(max = 512, message = "规格型号内容超出最大上限(512)")
    private String model;
    /**
     * 成分
     */
    @Size(max = 1024, message = "成分内容超出最大上限(1024)")
    private String composition;
    /**
     * 申报要素
     */
    @Size(max = 1024, message = "申报要素内容超出最大上限(1024)")
    private String hgsbys;

    /**
     * 净重
     */
    @DecimalMin(value = "0.000001", message = "净重不能小于等于0")
    @NotNull(message = "净重不能为空")
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    @DecimalMin(value = "0.000001", message = "毛重不能小于等于0")
    @NotNull(message = "毛重不能为空")
    private BigDecimal grossWeight;

    /**
     * 国检备案号
     */
    private String countryRecordNo;

    /**
     * 条码
     */
    @Size(max = 32, message = "条码内容超出最大上限(32)")
    private String barCode;

    /**
     * 外部货品id
     */
    @Size(max = 32, message = "外部货品id内容超出最大上限(32)")
    private String externalGoodsId;

    /**
     * 菜鸟货品id
     */
    @Size(max = 32, message = "菜鸟货品id内容超出最大上限(32)")
    private String cainiaoGoodsId;

    /**
     * 关务备注
     */
    @Size(max = 512, message = "关务备注内容超出最大上限(512)")
    private String guanWuRemark;

    /**
     * ai推荐hsCode
     */
    private String aiRecommendHsCode;
}
