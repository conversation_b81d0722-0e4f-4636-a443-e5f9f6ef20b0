package com.danding.cds.item.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 修改账册信息对象
 *
 * <AUTHOR>
 */
@Data
public class CustomsBookItemUpdBookSubmit implements Serializable {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("HS编码")
    private String hsCode;

    @ApiModelProperty("规格型号")
    private String goodsModel;

    @ApiModelProperty("法定第一计量单位")
    private String firstUnit;

    @ApiModelProperty("法一数量")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("法定第二计量单位")
    private String secondUnit;

    @ApiModelProperty("法二数量")
    private BigDecimal secondUnitAmount;

    @ApiModelProperty("申报单价金额")
    private BigDecimal declarePrice;

    @ApiModelProperty("币制")
    private String currCode;

    @ApiModelProperty("申报单位")
    private String goodsUnit;

    @ApiModelProperty("原产国")
    private String originCountry;

    @ApiModelProperty("金二序号")
    private String goodsSeqNo;

    @ApiModelProperty("账册ID")
    private Long customsBookId;

    /**
     * 商品来源标识:1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件
     */
    @ApiModelProperty("商品来源标识")
    private String goodsSource;

}
