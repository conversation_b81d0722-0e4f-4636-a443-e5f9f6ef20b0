package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 海关执行标记
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookCustomsExecutionFlagEnums {

    // 1-正常执行 2-恢复执行 3-暂停变更 4-暂停进出口 5-暂停进口 6-暂停出口 7-全部暂停
    NORMAL(1, "正常执行"),
    RECOVERY(2, "恢复执行"),
    CHANGE(3, "暂停变更"),
    STOP_IMPORT_EXPORT(4, "暂停进出口"),
    STOP_IMPORT(5, "暂停进口"),
    STOP_EXPORT(6, "暂停出口"),
    STOP_ALL(7, "全部暂停");


    private final Integer code;
    private final String desc;

    public static ProcessTradeBookCustomsExecutionFlagEnums getEnums(Integer code) {
        for (ProcessTradeBookCustomsExecutionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookCustomsExecutionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
