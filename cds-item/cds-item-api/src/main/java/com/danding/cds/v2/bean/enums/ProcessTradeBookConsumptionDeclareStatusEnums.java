package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单耗申报状态
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookConsumptionDeclareStatusEnums {

    // 1-未申报 2-已申报 3-已确定
    NOT_DECLARE(1, "未申报"),
    DECLARED(2, "已申报"),
    CONFIRMED(3, "已确定");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookConsumptionDeclareStatusEnums getEnums(Integer code) {
        for (ProcessTradeBookConsumptionDeclareStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookConsumptionDeclareStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
