package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品来源枚举
 * 商品来源标识:1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件
 */
@Getter
@AllArgsConstructor
public enum GoodsSourceEnums {
    OUTER_KEY_MATERIAL("1","境外重点料件"),
    OUTER_NORMAL_MATERIAL("2","境外普通料件"),
    INNER_PURCHASE_MATERIAL("3","国内采购料件"),
    PRODUCT_TRANSFER_MATERIAL("4","专账成品转入料件"),
    ;
    private String code;
    private String desc;

    public static GoodsSourceEnums getEnums(String code) {
        for (GoodsSourceEnums o : GoodsSourceEnums.values()){
            if (o.getCode().equals(code)){
                return o;
            }
        }
        return null;
    }
}
