package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 申报企业类型
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookDeclareCompanyTypeEnums {

    ENTERPRISE(1, "企业"),
    AGENT_COMPANY(2, "代理公司"),
    CUSTOMS_CLEARANCE_OFFICE(3, "报关行");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookDeclareCompanyTypeEnums getEnums(Integer code) {
        for (ProcessTradeBookDeclareCompanyTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookDeclareCompanyTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
