package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账册用途
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookUsageEnums {

    GENERAL_TAXPAYER(1, "一般纳税人"),
    SPECIAL_INDUSTRY(2, "特殊行业"),
    BONDED_REPAIR(3, "保税维修"),
    COMMISSIONED_INNER_PROCESSING(4, "委托内加工"),
    BONDED_RESEARCH_AND_DEVELOPMENT(5, "保税研发"),
    ZERO_TARIFF_1(6, "零关税-1（全免）"),
    ZERO_TARIFF_2(7, "零关税-2（自愿缴税）");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookUsageEnums getEnums(Integer code) {
        for (ProcessTradeBookUsageEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookUsageEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
