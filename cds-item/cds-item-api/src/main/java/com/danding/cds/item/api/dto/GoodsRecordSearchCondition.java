package com.danding.cds.item.api.dto;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class GoodsRecordSearchCondition extends Page {
    private static final long serialVersionUID = -8630134226479239911L;
    @ApiModelProperty("备案名称")
    private String goodsRecordName;

    @ApiModelProperty("备案时间（开始）")
    private Long recordTimeFrom;

    @ApiModelProperty("备案时间（结束）")
    private Long recordTimeTo;

    private Date recordTimeFromDate;
    private Date recordTimeToDate;

    @ApiModelProperty("完成时间（开始）")
    private Long finishedTimeFrom;

    @ApiModelProperty("完成时间（开始）")
    private Long finishedTimeTo;

    @ApiModelProperty("搜索下拉类型 1:sku 2:条码 3:料号")
    private String searchType;

    @ApiModelProperty("搜索关键字")
    private String searchKey;

    @ApiModelProperty("租户")
    private String lesseeNo;

    @ApiModelProperty("租户id")
    private String tenantId;

    /**
     * {@link GoodsRecordSearchStatusEnum}
     * ALL(0,"全部"),
     * WAIT_NEW(1,"待审核新品"),
     * WAIT_UPDATE(2,"待审核更新"),
     * WAIT_OVERTIME(3,"待审核超时"),
     * FINISH(4,"备案完成"),
     * REJECT(5,"备案驳回");
     */
    @ApiModelProperty("审核状态(0:全部/1:待审核新品/2:待审核更新/3:待审核超时/4:备案完成/5:备案驳回)")
    private Integer recordStatus;

    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    private Integer enable;

    @ApiModelProperty("口岸")
    private String customDistrict;

    @ApiModelProperty("账册id")
    private Long customsBookId;

    @ApiModelProperty("货品ID")
    private String goodsCode;

    @ApiModelProperty("货品ID")
    private List<String> goodsCodeList;

    @ApiModelProperty("统一料号")
    private String productId;

    @ApiModelProperty("统一料号")
    private List<String> productIdList;

    @ApiModelProperty("海关备案料号")
    private String customsRecordProductId;

    @ApiModelProperty("海关备案料号")
    private List<String> customsRecordProductIdList;

    @ApiModelProperty("SKU")
    private List<String> skuList;

    @ApiModelProperty("条码")
    private List<String> barCodeList;

    @ApiModelProperty("通关料号")
    private String warehouseExternalProductId;

    @ApiModelProperty("通关料号")
    private List<String> warehouseExternalProductIdList;

    @ApiModelProperty("外部料号")
    private String externalProductId;

    @ApiModelProperty("外部料号")
    private List<String> externalProductIdList;

    @ApiModelProperty("wms仓编码")
    private String warehouseSn;

    @ApiModelProperty("角色账册ID列表")
    @UcRoleAccountBookIdList
    private List<Long> roleAccountBookIdList;

    @ApiModelProperty("来源")
    public Integer source;

    @ApiModelProperty("商品标记")
    private Integer goodsRecordTag;

    @ApiModelProperty("商品备案ID列表")
    private List<Long> ids;

    @ApiModelProperty("审核方式")
    private Integer auditWay;

    /**
     * 外部货品idList
     */
    private String externalGoodsIdList;

    /**
     * 更新时间
     */
    private Long updateTimeFrom;
    private Long updateTimeTo;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
