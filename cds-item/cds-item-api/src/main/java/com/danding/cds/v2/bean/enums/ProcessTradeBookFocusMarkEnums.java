package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 重点商品标识
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookFocusMarkEnums {

    //0-非重点商品，1-目录重点商品，2-连带重点商品
    NOT_FOCUS_GOODS(0, "非重点商品"),
    CATALOGUE_FOCUS_GOODS(1, "目录重点商品"),
    LINKED_FOCUS_GOODS(2, "连带重点商品");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookFocusMarkEnums getEnums(Integer code) {
        for (ProcessTradeBookFocusMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookFocusMarkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
