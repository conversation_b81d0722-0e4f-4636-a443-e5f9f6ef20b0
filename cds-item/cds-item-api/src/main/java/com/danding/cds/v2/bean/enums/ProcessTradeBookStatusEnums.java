package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账册状态
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookStatusEnums {

    CREATED(1, "已创建"),
    RECORDING(2, "备案中"),
    RECORDED(3, "已备案"),
    RECORD_EXCEPTION(4, "备案异常");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookStatusEnums getEnums(Integer code) {
        for (ProcessTradeBookStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookStatusEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
