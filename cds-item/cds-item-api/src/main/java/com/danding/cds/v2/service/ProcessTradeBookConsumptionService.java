package com.danding.cds.v2.service;


import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.component.common.api.common.response.ListVO;

import java.util.List;

/**
 * <p>
 * 加工贸易账册单耗信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ProcessTradeBookConsumptionService {

    ListVO<ProcessTradeBookConsumptionDTO> paging(ProcessTradeBookConsumptionSearch param);

    List<ProcessTradeBookConsumptionDTO> findByEndPrdId(Long endPrdId);

    void createOrEdit(ProcessTradeBookConsumptionDTO consumptionDTO);

    void deleteById(List<Long> idList);

    void deleteByItemId(Long itemId);

    void deleteByItemId(List<Long> itemId);

    List<ProcessTradeBookConsumptionDTO> findByRefBookId(Long refBookId);

    void importExcel(Long refBookId, String operator, List<ProcessTradeBookConsumptionDTO> successList);

    ProcessTradeBookItemDTO findById(Long consumptionId);
}

