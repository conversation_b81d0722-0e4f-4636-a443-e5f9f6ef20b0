package com.danding.cds.v2.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 加工贸易账册单耗信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProcessTradeBookConsumption对象", description = "加工贸易账册单耗信息表")
public class ProcessTradeBookConsumptionDTO implements Serializable {


    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    private Long refBookId;

    /**
     * 成品id
     */
    @ApiModelProperty(value = "成品id")
    private Long endPrdId;

    /**
     * 料件id
     */
    @ApiModelProperty(value = "料件id")
    private Long mtpckId;

    /**
     * 单耗版本号 成品序号-料件序号
     */
    @ApiModelProperty(value = "单耗版本号 成品序号-料件序号")
    private String consumptionVersionNo;

    /**
     * 单耗有效期
     */
    @ApiModelProperty(value = "单耗有效期")
    private Date consumptionValidity;

    /**
     * 净耗
     */
    @ApiModelProperty(value = "净耗")
    private Integer netConsumption;

    /**
     * 有形损耗率（%），默认0
     */
    @ApiModelProperty(value = "有形损耗率（%），默认0")
    private BigDecimal tangibleLossRate;

    /**
     * 无形损耗率（%），默认0
     */
    @ApiModelProperty(value = "无形损耗率（%），默认0")
    private BigDecimal intangibleLossRate;

    /**
     * 单耗申报状态 1-未申报 2-已申报 3-已确定
     */
    @ApiModelProperty(value = "单耗申报状态 1-未申报 2-已申报 3-已确定")
    private Integer declareStatus;

    /**
     * 保税料件比例（%），默认100
     */
    @ApiModelProperty(value = "保税料件比例（%），默认100")
    private BigDecimal bondedMaterialRatio;

    /**
     * 修改标记
     */
    private String modifyFlag;

    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("id")
    private Long id;
}
