package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 账册标签 枚举类
 * @date 2023/7/6 14:22
 */
@Getter
@AllArgsConstructor
public enum CustomsBookTagEnums {

    NULL(0, "无"),
    INSUFFICIENT_LEGER_LIMIT(2 >> 1, "底账不足限制"),
    COLLABORATE_ENABLE(2, "协同单"),
    STOCK_AUTO_SWITCH_ENABLE(2 << 1, "库存自动切换"),
    NEGATIVE_STOCK_CHECK_ENABLE(2 << 2, "负库存校验"),
    CARRY_OVER_ENABLE(2 << 3, "自动结转"),
    FB_ZJPORT(2 << 4, "分类监管（浙电）"),
    FB_HZDC(2 << 5, "分类监管（杭州数据分中心）"),
    JD_RECEIVE_OUT_REGIN(2 << 6, "京东出区回传"),
    HANDOVER_ENABLE(2 << 7, "关联交接单"),
    CANCEL_ORDER_NON_LIMIT(2 << 8, "取消单无限制"),
    FB_ZJPORT_NEW(2 << 9, "分类监管（浙电新版）"),
    TRANSIT_BOOK(2 << 10, "中转账册"),
    LOCK_STOCK_ENABLE(2 << 11, "锁定库存校验"),
    AUTO_SWITCH_SPECIAL_BOOKS(2 << 12, "自动切换专用账册"),
    ;
    /**
     * 值
     */
    private Integer code;
    /**
     * 描述
     */
    private String desc;


    public static Boolean contains(Integer tag, CustomsBookTagEnums tagEnums) {

        if (tag == null || tagEnums == null) {
            return false;
        }
        return (tag & tagEnums.getCode()) == tagEnums.getCode();
    }

    public static boolean checkData(Integer bookTag) {
        if (Objects.isNull(bookTag)) {
            return false;
        }
        for (CustomsBookTagEnums e : CustomsBookTagEnums.values()) {
            if (Objects.equals(e.getCode(), bookTag)) {
                return true;
            }
        }
        return false;
    }

    public static List<Integer> getBookTag(Integer bookTag) {
        List<Integer> bookTagList = new ArrayList<>();
        if (bookTag == null || bookTag == 0) {
            return bookTagList;
        }
        for (CustomsBookTagEnums value : CustomsBookTagEnums.values()) {
            // 判断下bookTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & bookTag) == code) {
                bookTagList.add(code);
            }
        }
        return bookTagList;
    }

    public static List<String> getBookTagDesc(Integer useTag) {
        List<String> bookTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return bookTagList;
        }
        for (CustomsBookTagEnums value : CustomsBookTagEnums.values()) {
            // 判断下bookTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                bookTagList.add(value.getDesc());
            }
        }
        return bookTagList;
    }

    public static Integer removeBookTag(Integer bookTag, CustomsBookTagEnums enums) {
        if (Objects.isNull(bookTag)) {
            bookTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & bookTag) == code) {
            bookTag -= code;
        }
        return bookTag;
    }

    public static Integer addBookTag(Integer bookTag, CustomsBookTagEnums enums) {
        if (Objects.isNull(bookTag)) {
            bookTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & bookTag) != code) {
            bookTag += code;
        }
        return bookTag;
    }


}
