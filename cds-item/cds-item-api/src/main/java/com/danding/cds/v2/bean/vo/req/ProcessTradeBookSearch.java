package com.danding.cds.v2.bean.vo.req;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ProcessTradeBookSearch extends Page implements Serializable {

    /**
     * 企业内部编号（多值查询）
     */
    @ApiModelProperty("企业内部编号（多值查询）")
    private List<String> snList;

    /**
     * 预录入统一编号（多值查询）
     */
    @ApiModelProperty("预录入统一编号（多值查询）")
    private List<String> preNoList;

    /**
     * 加工贸易账册编号（多值查询）
     */
    @ApiModelProperty("加工贸易账册编号（多值查询）")
    private List<String> processTradeBookNoList;

    /**
     * 账册企业名称（模糊查询）
     */
    @ApiModelProperty("账册企业名称（模糊查询）")
    private String companyName;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Long createDateFrom;
    private Long createDateTo;

    /**
     * 数据状态枚举
     */
    @ApiModelProperty("数据状态")
    private Integer status;

    /**
     * 启用状态枚举
     */
    @ApiModelProperty("启用状态")
    private Integer enable;
}
