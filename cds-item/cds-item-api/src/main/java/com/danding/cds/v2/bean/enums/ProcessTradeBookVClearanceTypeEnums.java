package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 核销类型
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookVClearanceTypeEnums {

    CONSUMPTION(1, "单耗"),
    MATERIAL(2, "耗料"),
    WORK_ORDER(3, "工单");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookVClearanceTypeEnums getEnums(Integer code) {
        for (ProcessTradeBookVClearanceTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookVClearanceTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
