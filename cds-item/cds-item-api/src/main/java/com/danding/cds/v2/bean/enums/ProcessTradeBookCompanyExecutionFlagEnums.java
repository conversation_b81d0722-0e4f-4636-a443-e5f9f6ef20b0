package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企业执行标记
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookCompanyExecutionFlagEnums {

    // 1-运行 2-停用
    RUNNING(1, "运行"),
    STOP(2, "停用");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookCompanyExecutionFlagEnums getEnums(Integer code) {
        for (ProcessTradeBookCompanyExecutionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookCompanyExecutionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
