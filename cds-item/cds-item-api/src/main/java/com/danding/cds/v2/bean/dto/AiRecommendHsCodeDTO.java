package com.danding.cds.v2.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * AI推荐商品编码表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-24
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "AiRecommendHsCode对象", description = "AI推荐商品编码表")
public class AiRecommendHsCodeDTO implements Serializable {


    /**
     * 备案id
     */
    @ApiModelProperty(value = "备案id")
    private Long recordId;

    /**
     * 口岸编码
     */
    @ApiModelProperty(value = "口岸编码")
    private String customsCode;

    /**
     * 备案商品名称
     */
    private String recordGoodsName;

    /**
     * 备案成分
     */
    private String recordComposition;

    /**
     * 口岸编码
     */
    @ApiModelProperty(value = "口岸编码")
    private String recommendHsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 所属章节
     */
    @ApiModelProperty(value = "所属章节")
    private String goodsChapter;

    /**
     * 推荐理由
     */
    @ApiModelProperty(value = "推荐理由")
    private String recommendReason;

    /**
     * 选用标记 0-未选用 1-选用
     */
    @ApiModelProperty(value = "选用标记 0-未选用 1-选用")
    private Integer chooseFlag;

    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("id")
    private Long id;
}
