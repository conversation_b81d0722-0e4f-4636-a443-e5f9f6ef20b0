package com.danding.cds.v2.bean.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessTradeBookDetailEditReq implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 申报单位类型，枚举 1-企业、2-代理公司、3-报关行
     */
    private Integer declareCompanyType;


    /**
     * 批准证编号
     */
    private String approvalCertNo;

    /**
     * 企业档案库编号
     */
    private String enterpriseArchiveNo;

    /**
     * 最大周转金额（万美金），整数部分最大 14 位，小数最大 5 位
     */
    private BigDecimal maxTurnoverAmount;

    /**
     * 单耗申报环节，枚举，1-出口前 2-报核前
     */
    @NotNull(message = "单耗申报环节不能为空")
    private Integer consumptionDeclarationLink;

    /**
     * 最大进口金额（美元）
     */
    private BigDecimal maxImportAmount;

    /**
     * 核销周期，整数不超过 4 位，系统默认初始化，180
     */
    private Integer voucherClearanceCycle;

    /**
     * 账册结束有效期
     */
    @NotNull(message = "账册结束有效期不能为空")
    private Long bookEndValidity;

    /**
     * 账册执行标志
     */
    private Integer bookExecutionFlag;

    /**
     * 账册用途
     */
    private Integer bookUsage;

    /**
     * 核销方式，枚举，1-企业自核 2-海关核算
     */
    private Integer voucherClearanceMethod;

    /**
     * 备注
     */
    private String remark;

}
