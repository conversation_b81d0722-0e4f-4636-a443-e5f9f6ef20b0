package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 商品备案标记
 * @date 2022/9/28 14:58
 */
@Getter
@AllArgsConstructor
public enum GoodsRecordTagEnums {
    NULL(2 >> 2, "无"),
    ERP_DELETE(2 >> 1, "ERP删除"),
    GTIN_NO_RULE(2, "条码不符合规则"),
    FOUR_CATEGORY_GOODS(2<<1, "四类措施商品"),
    ;

    private Integer code;
    private String desc;

    public static List<Integer> getGoodsRecordTag(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (GoodsRecordTagEnums value : GoodsRecordTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getGoodsRecordDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (GoodsRecordTagEnums value : GoodsRecordTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static Integer removeGoodsRecordTag(Integer orderTag, GoodsRecordTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) == code) {
            orderTag -= code;
        }
        return orderTag;
    }

    public static Integer addGoodsRecordTag(Integer orderTag, GoodsRecordTagEnums enums) {
        if (Objects.isNull(orderTag)) {
            orderTag = 0;
        }
        Integer code = enums.getCode();
        if ((code & orderTag) != code) {
            orderTag += code;
        }
        return orderTag;
    }

    public static void main(String[] args) {

        Integer i = GoodsRecordTagEnums.addGoodsRecordTag(null, GoodsRecordTagEnums.GTIN_NO_RULE);

        List<Integer> intList = GoodsRecordTagEnums.getGoodsRecordTag(6);
        if (intList.contains(GoodsRecordTagEnums.GTIN_NO_RULE.getCode())) {

        }

    }
}
