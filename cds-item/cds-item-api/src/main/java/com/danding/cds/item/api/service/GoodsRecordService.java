package com.danding.cds.item.api.service;

import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.GoodsAutoRecordImportVo;
import com.danding.cds.item.api.dto.submit.GoodsRecordAuditSubmitV2;
import com.danding.cds.item.api.dto.submit.GoodsRecordImportUpdateReqVo;
import com.danding.cds.item.api.dto.submit.GoodsRecordSyncSubmitV2;
import com.danding.cds.item.api.dto.submit.central.CentralGoodsRecordSubmit;
import com.danding.cds.item.api.enums.GoodsRecordActionEnum;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.es.RecordTrackLogEsDTO;
import com.danding.cds.v2.bean.es.RecordTrackLogSaveDTO;
import com.danding.cds.v2.bean.param.DeleteGoodsRecordSubmit;
import com.danding.cds.v2.bean.param.RecordModifyAssociateInfoSubmit;
import com.danding.cds.v2.bean.vo.req.RecordTrackLogSearch;
import com.danding.cds.v2.bean.vo.req.UpdFinishGoodsRecordReqVo;
import com.danding.cds.v2.bean.vo.res.AssociateCustomsBookItemResVO;
import com.danding.cds.v2.bean.vo.res.GoodsRecordAuditPassResVO;
import com.danding.cds.v2.bean.vo.res.GoodsRecordCustomsWarehouseResVO;
import com.danding.cds.v2.bean.vo.res.RecordItemAssociateInfoResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;

import java.util.Date;
import java.util.List;
import java.util.Map;

public interface GoodsRecordService {


    Long updateGoodsRecordTagByCountryCode(Long id, String originCountryCode, Integer goodsRecordTag);

    /**
     * 商品备案实体仓
     *
     * @param warehouseSn  实体仓
     * @param recordStatus
     * @return
     */
    List<GoodsRecordWarehouseDTO> goodRecordWarehouseInfo(String warehouseSn, Integer recordStatus, Date startDate, Date endDate);

    /**
     * 分页
     *
     * @param condition
     * @return
     */
    ListVO<GoodsRecordDTO> paging(GoodsRecordSearchCondition condition);

    List<GoodsRecordDTO> searchByDB(CentralGoodsRecordSearchCondition condition);

    List<Long> findByName(String name);

    ListVO<GoodsRecordDTO> warehouseRecordPaging(GoodsRecordSearchCondition condition);

    /**
     * 编辑（新增/更新）
     */
    Long update(GoodsRecordSubmit goodsRecordSubmit, String operateType) throws ArgsErrorException;

    Long upset(GoodsRecordSubmit goodsRecordSubmit) throws ArgsErrorException;

    Long upsetCore(GoodsRecordSubmitDTO goodsRecordSubmit) throws ArgsErrorException;

    /**
     * 接口同步（新增/更新）
     */
    Long syncUpset(GoodsRecordSyncSubmit submit) throws ArgsErrorException;

    Long syncUpset(GoodsRecordSyncSubmitV2 submit) throws ArgsErrorException;

    Long syncUpsetCore(GoodsRecordSyncSubmitDTO submit) throws ArgsErrorException;

    CustomsBookDTO getCustomsBookByWareCode(String wareCdoe);

    GoodsRecordDTO queryCustomsRecord(GoodsRecordQuerySubmit submit);

    /**
     * 根据id查找
     *
     * @param id
     * @return
     */
    GoodsRecordDTO findById(Long id);

    List<GoodsRecordDTO> findById(List<Long> idList);

    /**
     * TODO:根据料号查找 废弃
     *
     * @param productId
     * @return
     */
    @Deprecated
    GoodsRecordDTO findByProId(String productId);

    List<GoodsRecordDTO> findDescListByProId(String productId);

    List<GoodsRecordDTO> findByProId(List<String> productId);

    List<GoodsRecordDTO> findByBookIdAndProId(Long customsBookId, List<String> productIdList);

    List<GoodsRecordDTO> findSuccessByProId(List<String> productId);

    GoodsRecordDTO findByBookIdAndProId(Long customsBookId, String productId);

    GoodsRecordDTO findByBookIdAndSkuId(Long customsBookId, String skuId);

    GoodsRecordDTO findByWareCodeAndSku(String wareCode, String skuCode);


    /**
     * 启用/禁用
     *
     * @param id
     * @param enable
     * @return
     */
    Long updateEnable(Long id, Integer enable);

    /**
     * 审核
     *
     * @return
     */
    @Deprecated
    Long audit(GoodsRecordSubmit submit);

    Long audit(GoodsRecordAuditSubmit submit);

    Long auditV2(GoodsRecordAuditSubmitV2 submit);

    void recordStatusFix(Long recordId);

    /**
     * 根据id删除
     *
     * @param id
     * @return
     */
    Long deleteById(Long id) throws ArgsErrorException;

    void deleteRecordSyncErp(Long goodsRecordId);

    /**
     * 审核通过/审核驳回
     *
     * @param id
     * @param statusEnum
     * @return
     */
    Long updateRecordStatus(Long id, GoodsRecordStatusEnum statusEnum);

    Long updateRecordStatus(Long id, GoodsRecordStatusEnum statusEnum, String reason);

    /**
     * 同步商品备案
     *
     * @param submit
     * @return
     */
    Long syncCustomsRecord(GoodsRecordSyncSubmit submit);

    Long syncCustomsRecordV2(GoodsRecordSyncSubmitV2 submit);

    GoodsRecordDTO findByCainiaoGoodsId(String cainiaoGoodsId);

    /**
     * 建立下商品备案和口岸和账册对应关系
     *
     * @param recordId 商品记录ID
     * @param submit   提交数据
     */
    void handleRecordCustomsWarehouse(Long recordId, GoodsRecordSyncSubmitV2 submit);

    void handleRecordCustomsWarehouse(Long recordId, GoodsRecordSyncSubmitV2 submit, GoodsRecordActionEnum actionEnum);

    void submitUpdateList(List<GoodsRecordSubmit> updateList) throws ArgsErrorException, ArgsInvalidException;

    ImportResultResVo submitUpdateListNew(GoodsRecordSubmit submit) throws ArgsErrorException, ArgsInvalidException;

    void modifyAssociatedInfo(RecordModifyAssociateInfoSubmit submit) throws ArgsErrorException;

    RecordItemAssociateInfoResVO viewAssociatedInfoVO(Long id) throws ArgsErrorException;

    RecordItemAssociateInfoDTO viewAssociatedInfoDTO(Long id) throws ArgsErrorException;

    GoodsRecordAuditPassResVO auditPassPreCheck(Long id) throws ArgsErrorException;

    GoodsRecordAuditPassResVO auditPassPreCheck(Long id, String customsCode) throws ArgsErrorException;

    List<AssociateCustomsBookItemResVO> viewItemListByGoodsRecordId(Long id) throws ArgsErrorException;

    RecordItemAssociateInfoDTO getAssociateInfo(Long id);

    RecordItemAssociateInfoDTO getAssociateInfo(Long customsBookId, String productId);

    String getAssociateProductId(Long id);

    String getAssociateProductId(Long customsBookId, String productId);

    ListVO<RecordTrackLogEsDTO> pagingTrackLog(RecordTrackLogSearch search);

    void trackLogEsSave(RecordTrackLogSaveDTO recordTrackLogSaveDTO);

    List<GoodsRecordCustomsWarehouseResVO> getCustomsWarehouseInfo(Long id);

    List<RecordCustomsDTO> recordCustomsDTOList(Long recordId);

    List<RecordWarehouseDTO> getRecordWarehouseInfo(Long recordCustomsId);

    void buildTrackLogDiffAndSend(GoodsRecordDTO before, GoodsRecordDTO after, String customs, String operateType);

    void buildTrackLogNewAndSend(Long goodsRecordId, String source, String customs);

    GoodsRecordStatusCount getRecordStatusCount(List<Long> accountBookList);

    GoodsRecordStatusCount getRecordStatusCount(GoodsRecordSearchCondition condition, List<Long> accountBookList);

    /**
     * 备案数量统计
     *
     * @return
     */
    GoodsRecordBusinessCount getRecordBusinessCount(String warehouseCode);

    Map<String, GoodsRecordBusinessCountDetail> getRecordBusinessCountDetailES(Long beginTime, Long endTime);

    Map<String, GoodsRecordBusinessCountDetail> getRecordBusinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    List<RecordProductDTO> getRecordCustomsProduct(Long recordId, Long recordCustomsId);

    /**
     * 根据账册+料号 生成商品列表
     *
     * @param customsBookId
     * @param productId
     * @param goodsSeqNo
     */
    void generateRecordProduct(CustomsBookItemDTO customsBookId, String productId, String goodsSeqNo);

    /**
     * 更新商品列表
     *
     * @param customsBookItemId
     */
    void updateRecordCustomsProduct(Long customsBookItemId);

    /**
     * 删除商品列表
     *
     * @param recordCustomsProductId
     */
    void deleteRecordCustomsProduct(Long recordCustomsProductId);

    /**
     * 获取备案的海关备案料号列表
     *
     * @param recordIdList
     * @return
     */
    Map<Long, List<String>> getCustomsProductIdByRecordId(List<Long> recordIdList);

    List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId);

    List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId, Long recordWarehouseId);

    List<RecordWarehouseProductIdDTO> getRecordWarehouseProductIdList(Long recordId, List<Long> recordWarehouseId);

    /**
     * 账册库存-导入统一料号
     *
     * @param productStockImportDTO
     * @return
     */
    ImportResultResVo importProductStock(ProductStockImportDTO productStockImportDTO);

    /**
     * 根据统一料号和账册获取可以替换的料号
     *
     * @param unifiedProductId 统一料号
     * @param customsBookId    账册号
     * @return
     */
    List<String> getProductIdListByUnifiedProductId(String unifiedProductId, Long customsBookId);

    /**
     * 根据统一料号获取所有申报用的海关备案料号列表
     *
     * @param customsBookId
     * @param unifiedProductIdList
     * @return Map<统一料号, List < 海关备案料号>>
     */
    Map<String, List<CustomsBookItemDTO>> getCustomsDeclareProductIdList(Long customsBookId, List<String> unifiedProductIdList);

    List<RecordWarehouseProductIdDTO> findWarehouseProductIdByRecordIdAndCustomsBookId(Long recordId, Long customsBookId);

    void saveNewWarehouseInfo(Long recordCustomsId, String entityWarehouseSn);

    void addDeleteFlag(DeleteGoodsRecordSubmit deleteGoodsRecordSubmit);

    void addDeleteFlag(GoodsRecordDTO goodsRecordDTO);

    List<GoodsRecordEsDTO> findEsWaitExamineRecord();

    void syncEsByUpdateTimeAcc(List<Long> goodsRecordIdList);

    GoodsRecordDTO findByBookIdAndProIdAndMappingWay(Long customsBookId, String productId, String mappingWay);


    ImportResultResVo importAutoRecord(GoodsAutoRecordImportVo vo, Long operatorId);

    ImportResultResVo goodsRecordImportUpdate(GoodsRecordImportUpdateReqVo vo);

    void updateFinishGoodsRecord(UpdFinishGoodsRecordReqVo reqVo);

    void warehouseFix(String addBookNo, String queryBookNo, String wmsWarehouseCode);

    void retryAuditMsg(List<Long> idList);

    List<GoodsRecordDTO> findByProductIdAndCustomsBookId(String productId, Long customsBookId);

    List<GoodsRecordDTO> findByProductIdAndErpWarehouseCode(String productId, String erpWarehouseCode);


    Long saveCentralCustomsRecord(CentralGoodsRecordSubmit submit);

    void auditCentralGoodsRecord(Long goodsRecordId, String customsCode);

    CentralGoodsRecordDetailDTO getCentralGoodsRecordDetail(CentralGoodsRecordDetailSearchCondition searchDTO, String customsCode);

    List<RecordCustomsDTO> getCustomsListByUserId(String userId);

    List<RecordCustomsDTO> getCustomsListByUserIdAndGoodsCode(String userId, String goodsCode);

    Long saveAndAuditGoodsRecord(CentralGoodsRecordSubmit centralGoodsRecordSubmit);

    void updateCentralImgInfo(List<CentralUpdateImgInfoSubmit> updateImgInfoSubmitList);

    void updateCentralNameAndBarCode(List<CentralUpdateNameAndBarCodeSubmit> centralUpdateNameAndBarCodeSubmitList);

    GoodsRecordEsDTO buildGoodsRecordEsDTO(Long goodsRecordId);

    GoodsRecordEsDTO buildGoodsRecordEsDTO(GoodsRecordDTO goodsRecordDTO);

    GoodsRecordDTO findByExternalGoodsId(String externalGoodsId);

    List<GoodsRecordDTO> findByExternalGoodsIds(List<String> externalGoodsIds);

    void buildTrackLogDiffAndSend(Long recordId, GoodsRecordCompareDTO before, GoodsRecordCompareDTO after, String customs, String operatorType);

    List<RecordWarehouseProductIdDTO> findByCustomsDeclareProductIdAndCustomsBook(String customsDeclareProductId, Long customsBookId);

    void batchAuditByImport(GoodsRecordAuditSubmitV2 submit, Long operatorId);

    void createGoodsRecordByJdGoodsRecord(Long jdGoodsRecordId);

}
