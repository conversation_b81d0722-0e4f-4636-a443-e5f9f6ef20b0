package com.danding.cds.v2.bean.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 实体仓标记 位运算处理
 */
@AllArgsConstructor
@Getter
public enum EntityWarehouseTagEnums {

    NULL(1 >> 1, ""),
    CW_INVENTORY(1, "CW清关"),
    FB_RELATE_WAREHOUSE(1 << 1, "非保关仓"),
    TAO_TIAN(1 << 2, "淘天清关"),
    TAO_TIAN_FB(1 << 3, "淘天非保"),
    COMMON_BOOKS_QG(1 << 4, "普通账册清关"),
    SPECIAL_BOOKS_QG(1 << 5, "专用账册清关"),
    ;

    private final Integer code;
    private final String desc;

    public static List<Integer> getTags(java.lang.Integer useTag) {
        java.util.List<java.lang.Integer> tagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return tagList;
        }
        for (EntityWarehouseTagEnums value : EntityWarehouseTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            java.lang.Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                tagList.add(code);
            }
        }
        return tagList;
    }

    public static List<String> getTagsDesc(Integer tags) {
        List<String> tagDescList = new ArrayList<>();
        if (tags == null || tags == 0) {
            return tagDescList;
        }
        for (EntityWarehouseTagEnums value : EntityWarehouseTagEnums.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & tags) == code) {
                tagDescList.add(value.getDesc());
            }
        }
        return tagDescList;
    }

    public static Integer remove(Integer tag, EntityWarehouseTagEnums enums) {
        if (Objects.isNull(tag)) {
            tag = 0;
        }
        Integer code = enums.getCode();
        if ((code & tag) == code) {
            tag -= code;
        }
        return tag;
    }

    public static Integer add(Integer tag, EntityWarehouseTagEnums enums) {
        if (Objects.isNull(tag)) {
            tag = 0;
        }
        Integer code = enums.getCode();
        if ((code & tag) != code) {
            tag += enums.getCode();
        }
        return tag;
    }

    public static Boolean contains(Integer tags, EntityWarehouseTagEnums enums) {
        if (Objects.isNull(tags) || tags.equals(0)) {
            return false;
        }
        return Objects.equals(enums.getCode(), tags & enums.getCode());
    }

    public static Boolean containsAll(Integer tags, EntityWarehouseTagEnums... enums) {
        if (Objects.isNull(tags) || tags.equals(0)) {
            return false;
        }
        return Arrays.stream(enums).allMatch(e -> Objects.equals(e.getCode(), tags & e.getCode()));
    }

    public static Boolean containsAny(Integer tags, EntityWarehouseTagEnums... enums) {
        if (Objects.isNull(tags) || tags.equals(0)) {
            return false;
        }
        return Arrays.stream(enums).anyMatch(e -> Objects.equals(e.getCode(), tags & e.getCode()));
    }

    public static Integer build(List<Integer> tags) {
        if (CollUtil.isEmpty(tags)) {
            return 0;
        }
        return tags.stream().mapToInt(Integer::intValue).sum();
    }

    public static List<EntityWarehouseTagEnums> valuesV2() {
        return Arrays.stream(EntityWarehouseTagEnums.values()).filter(i ->
                !i.equals(EntityWarehouseTagEnums.NULL)
        ).collect(Collectors.toList());
    }
}
