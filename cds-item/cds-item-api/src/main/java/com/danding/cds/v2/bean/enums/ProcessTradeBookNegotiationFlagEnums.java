package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企业执行标记
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookNegotiationFlagEnums {

    // 0-表示未磋商  1-表示磋商中
    NOT_NEGOTIATION(0, "未磋商"),
    NEGOTIATION(1, "磋商中");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookNegotiationFlagEnums getEnums(Integer code) {
        for (ProcessTradeBookNegotiationFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookNegotiationFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
