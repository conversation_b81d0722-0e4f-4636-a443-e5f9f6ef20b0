package com.danding.cds.item.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class CustomsGoodsItemInfoDTO implements Serializable {
    private static final long serialVersionUID = -2433500850499947700L;
    @ApiModelProperty("是否新旧")
    private String oldOrNew;
    @ApiModelProperty("sku")
    private String skuId;
    @ApiModelProperty("原始料号")
    private String originProductId;
    @ApiModelProperty("商品料号")
    private String productId;
    @ApiModelProperty("备案序号")
    private String goodsSeqNo;
    @ApiModelProperty("报关单商品序号")
    private String declareCustomsGoodsSeqNo;//add
    @ApiModelProperty("通关料号")
    private String externalProductId;

    @ApiModelProperty("商品名称")
    private String goodsName;
    @ApiModelProperty("备案品名")
    private String recordProductName;

    @ApiModelProperty("HS编码，商品编码")
    private String hsCode;
    @ApiModelProperty("规格型号")//add
    private String goodsModel;

    @ApiModelProperty("申报计量单位")
    private String unit;
    @ApiModelProperty("申报计量单位")
    private String unitDesc;
    @ApiModelProperty("申报数量")
    private BigDecimal declareUnitQfy;

    @ApiModelProperty("法定计量单位")
    private String firstUnit;
    @ApiModelProperty("法定计量单位")
    private String firstUnitDesc;
    @ApiModelProperty("法定数量")
    private BigDecimal firstUnitQfy;

    @ApiModelProperty("法定第二计量")
    private String secondUnit;
    @ApiModelProperty("法定第二计量")
    private String secondUnitDesc;
    @ApiModelProperty("第二法定数量")
    private BigDecimal secondUnitQfy;

    @ApiModelProperty("申报单价")//add
    private BigDecimal declarePrice;
    @ApiModelProperty("净重")
    private BigDecimal netweight;
    @ApiModelProperty("毛重")
    private BigDecimal grossWeight;
    @ApiModelProperty("申报总价")//add
    private BigDecimal declareTotalPrice;
    @ApiModelProperty("申报要素")
    private String declareFactor;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("原产国描述")
    private String originCountryDesc;

    @ApiModelProperty("最终目的国")
    private String destinationCountry;//add
    @ApiModelProperty("最终目的国")
    private String destinationCountryDesc;//add
    @ApiModelProperty("生产企业")
    private String productCompany;
    @ApiModelProperty("商品条码")
    private String goodsBar;

    @ApiModelProperty("国检备案号")
    private String countryRecordNo;
    @ApiModelProperty("币制")//add
    private String currency;
    @ApiModelProperty("币制描述")//add
    private String currencyDesc;//
    @ApiModelProperty("免征方式")//add
    private String avoidTaxMethod = "3";
    @ApiModelProperty("单号版本号")//add
    private String orderVersion;
    @ApiModelProperty("申报表序号")//add
    private Integer declareFormItemSeqNo;
    /**
     * {@link com.danding.cds.invenorder.api.dto.InventoryVerifyResult}
     */
    private List<Object> verifyResultList;

    /**
     * 是否危化品标志
     */
    private String dangerousFlag;
    @ApiModelProperty("来源标识")
    private String goodsSource;
    private String goodsSourceDesc;

    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;
}
