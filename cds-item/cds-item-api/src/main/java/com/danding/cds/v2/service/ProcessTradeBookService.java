package com.danding.cds.v2.service;


import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookDetailEditReq;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.component.common.api.common.response.ListVO;

import java.util.List;

/**
 * <p>
 * 加工贸易账册 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
public interface ProcessTradeBookService {

    ListVO<ProcessTradeBookDTO> paging(ProcessTradeBookSearch search);

    void create(Long bookId, Integer bookType);

    void enable(List<Long> idList, Integer enable);

    void updateStatus(List<Long> idList, Integer status);

    ProcessTradeBookDTO findById(Long id);

    List<ProcessTradeBookDTO> findById(List<Long> idList);

    void updateDetail(ProcessTradeBookDetailEditReq editReq);

    ListVO<ProcessTradeBookItemDTO> itemPaging(ProcessTradeBookItemSearch param);

    void createOrEditItem(ProcessTradeBookItemDTO param);

    void deleteItem(List<Long> idList);

    ListVO<ProcessTradeBookConsumptionDTO> pagingConsumption(ProcessTradeBookConsumptionSearch param);

    void createOrEditConsumption(ProcessTradeBookConsumptionDTO consumptionDTO);

    void deleteConsumption(List<Long> idList);

    List<ProcessTradeBookItemDTO> findItemByIdList(List<Long> itemIdList);

    List<ProcessTradeBookItemDTO> matchItem(Long id, Long bookId, Integer goodsType, String productId);

    ProcessTradeBookItemDTO findItemByProductIdAndBookId(Integer goodsType, String productId, Long bookId);

    List<ProcessTradeBookConsumptionDTO> findConsumptionByProductIdAndBookId(Integer goodsType, String productId, Long bookId);

    ProcessTradeBookDTO findByLogisticsBookId(Long bookId);

    ProcessTradeBookDTO findAllByLogisticsBookId(Long bookId);

    List<ProcessTradeBookDTO> findByLogisticsBookId(List<Long> bookIdList);

    void importItemExcel(Long bookId, Integer goodsType, String operator, List<ProcessTradeBookItemDTO> successList);

    List<ProcessTradeBookItemDTO> listEndprdSeqById(Long id);

    List<ProcessTradeBookItemDTO> listMtpckSeqById(Long id);

    List<ProcessTradeBookItemDTO> listItemAllById(Long id);

    List<ProcessTradeBookConsumptionDTO> listConsumption(Long refBookId);

    void importConsumptionExcel(Long refBookId, String operator, List<ProcessTradeBookConsumptionDTO> successList);
}
