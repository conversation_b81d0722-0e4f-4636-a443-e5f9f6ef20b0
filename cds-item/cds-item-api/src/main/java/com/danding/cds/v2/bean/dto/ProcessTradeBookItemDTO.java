package com.danding.cds.v2.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 加工贸易账册表体
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-19
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "ProcessTradeBookItem对象", description = "加工贸易账册表体")
public class ProcessTradeBookItemDTO implements Serializable {


    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    private Long refBookId;

    /**
     * 表体类型 1-料件 2-成品
     */
    @ApiModelProperty(value = "表体类型 1-料件 2-成品")
    private Integer goodsType;

    /**
     * 序号
     */
    private Integer seqNo;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String productId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String hsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String goodsModel;

    /**
     * 法定计量单位
     */
    @ApiModelProperty(value = "法定计量单位")
    private String legalUnit;

    /**
     * 法定第二计量单位
     */
    @ApiModelProperty(value = "法定第二计量单位")
    private String legalSecondUnit;

    /**
     * 修改标志，默认3-增加
     */
    @ApiModelProperty(value = "修改标志，默认3-增加")
    private String modifyFlag;

    /**
     * 申报单价
     */
    @ApiModelProperty(value = "申报单价")
    private BigDecimal declareUnitPrice;

    /**
     * 币制
     */
    @ApiModelProperty(value = "币制")
    private String currency;

    /**
     * 申报计量单位
     */
    @ApiModelProperty(value = "申报计量单位")
    private String declareUnit;

    /**
     * 申报数量
     */
    @ApiModelProperty(value = "申报数量")
    private Long declareQty;

    /**
     * 征免方式，默认3-全免
     */
    @ApiModelProperty(value = "征免方式，默认3-全免")
    private String dutyExemptionMethod;

    /**
     * 企业执行标志，默认1-运行
     */
    @ApiModelProperty(value = "企业执行标志，默认1-运行")
    private Integer companyExecutionFlag;

    /**
     * 重点商品标识，默认0-非重点商品
     */
    @ApiModelProperty(value = "重点商品标识，默认0-非重点商品")
    private Integer focusMark;

    /**
     * 国别(地区)
     */
    @ApiModelProperty(value = "国别(地区)")
    private String countryRegion;

    /**
     * 海关执行标志，默认1-正常执行
     */
    @ApiModelProperty(value = "海关执行标志，默认1-正常执行")
    private Integer customsExecutionFlag;

    /**
     * 期初数量，默认0
     */
    @ApiModelProperty(value = "期初数量，默认0")
    private Integer initialQty;

    /**
     * 数量控制标志，默认2-不控制数量
     */
    @ApiModelProperty(value = "数量控制标志，默认2-不控制数量")
    private Integer qtyControlFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 批准最大余数量
     */
    @ApiModelProperty(value = "批准最大余数量")
    private BigDecimal maxRemainQty;

    /**
     * 单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0
     */
    @ApiModelProperty(value = "单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0")
    private Integer consumptionQuestionFlag;

    /**
     * 磋商标志 0-表示未磋商  1-表示磋商中 默认为0
     */
    @ApiModelProperty(value = "磋商标志 0-表示未磋商  1-表示磋商中 默认为0")
    private Integer negotiationFlag;

    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;
    @ApiModelProperty("id")
    private Long id;
}
