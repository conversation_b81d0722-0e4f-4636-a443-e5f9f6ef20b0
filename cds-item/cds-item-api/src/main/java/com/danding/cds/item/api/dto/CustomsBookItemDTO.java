package com.danding.cds.item.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: shenhui
 * @Date: 2020/5/11 17:13
 */
@Data
public class CustomsBookItemDTO implements Serializable {
    private static final long serialVersionUID = 6717968890062470117L;
    private Long id;
    /**
     * 商品序号
     */
    private String goodsSeqNo;
    /**
     * 料号
     */
    private String productId;
    /**
     * HS编码
     */
    private String hsCode;
    /**
     * 料号库存id
     */
    private Long productStockId;
    /**
     * 统一料号
     */
    private String unifiedProductId;
    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品来源标识:1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件
     */
    private String goodsSource;
    /**
     * 申报币制
     */
    private String currCode;
    /**
     * 申报单价金额
     */
    private BigDecimal declarePrice;
    /**
     * 商品规格型号
     */
    private String goodsModel;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 申报计量单位
     */
    private String goodsUnit;
    /**
     * 法定第一计量单位
     */
    private String firstUnit;
    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;
    /**
     * 法定第二计量单位
     */
    private String secondUnit;
    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;
    /**
     * 账册id
     */
    private Long customsBookId;
    /**
     * 记账清单商品序号
     */
    private String invtGoodsNo;
    /**
     * 记账清单编号
     */
    private String invtNo;
    /**
     * 入仓数量
     */
    private Integer inQty;
    /**
     * 账册数量
     */
    private Integer accountNum;
    /**
     * 占用库存数量
     */
    private Integer occupiedNum;
    /**
     * 已用库存数量
     */
    private Integer usedNum;
    /**
     * 可用库存数量
     */
    private Integer availableNum;
    /**
     * 锁定库存数量
     */
    private Integer lockedNum;

    private BigDecimal inLegalQty;//入仓法定数量
    private BigDecimal inSecondLegalQty;//第二入仓法定数量
    private Long inDate = 0L;//最近入仓（核增）日期
    private BigDecimal avgPrice;//平均美元单价
    private BigDecimal totalAmt;//库存美元总价
    private String remark;
    private Integer enable;
    private Long createTime;
    private Long updateTime;

    /**
     * 总入数量
     */
    private Integer totalInQty;

    /**
     * 总出数量
     */
    private Integer totalOutQty;

    /**
     * 总差异数量
     */
    private Integer totalDiffQty;

    /**
     * 差异类型
     */
    private String diffType;
}
