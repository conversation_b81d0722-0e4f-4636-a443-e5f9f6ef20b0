package com.danding.cds.item.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel
public class GoodsRecordAuditSubmit implements Serializable {
//    @ApiModelProperty("id组成的list")
//    private List<Long> ids = new ArrayList<>();
    @ApiModelProperty("ID 更新时必填")
    private Long id;

    @ApiModelProperty("口岸")
    private String customsCode;

    @ApiModelProperty("审核通过:1/驳回:0")
    private Integer opinion;

    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("料号")
    private String productId;

    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("外部料号")
    private String externalProductId;

    @ApiModelProperty("HSCode")
    private String hsCode;

    @ApiModelProperty("法定第一数量")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("法定第一计量单位")
    private String firstUnit;

    @ApiModelProperty("法定第二数量")
    private BigDecimal secondUnitAmount;

    @ApiModelProperty("法定第二计量单位")
    private String secondUnit;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 关务备注
     */
    private String guanWuRemark;

    /**
     * ai推荐hsCode
     */
    private String aiRecommendHsCode;
}
