package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单耗申报环节代码
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookConsumptionDeclareLinkEnums {

    // 1-出口前 2-报核前
    EXPORT_BEFORE(1, "出口前"),
    REPORT_BEFORE(2, "报核前");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookConsumptionDeclareLinkEnums getEnums(Integer code) {
        for (ProcessTradeBookConsumptionDeclareLinkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookConsumptionDeclareLinkEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
