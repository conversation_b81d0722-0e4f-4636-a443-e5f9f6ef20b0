package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 账册类型
 *
 * <AUTHOR>
 */
@AllArgsConstructor
@Getter
public enum CustomsBookTypeEnums {

    NULL(0, ""),
    IMPORT_BONDED_BOOKS(1, "进口-保税账册(普通)"),
    IMPORT_UNBONDED_BOOKS(2, "进口-非保账册"),
    PROCESSING_TRADE_BOOKS(3, "加工贸易账册"),
    IMPORT_BONDED_SPECIAL_BOOKS(4, "进口-保税账册(专用)");

    private final Integer code;
    private final String desc;

    public static CustomsBookTypeEnums getEnums(Integer code) {
        for (CustomsBookTypeEnums o : CustomsBookTypeEnums.values()) {
            if (o.getCode().equals(code)) {
                return o;
            }
        }
        return NULL;
    }
}
