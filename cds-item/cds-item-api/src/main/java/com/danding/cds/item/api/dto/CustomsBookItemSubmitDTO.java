package com.danding.cds.item.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.DecimalMin;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
public class CustomsBookItemSubmitDTO implements Serializable {
    @ApiModelProperty("ID 更新时必填")
    private Long id;

    @ApiModelProperty("商品序号")
    @NotBlank(message = "商品序号不能为空")
    private String goodsSeqNo;

    @ApiModelProperty("商品料号")
    @NotBlank(message = "商品料号不能为空")
    private String productId;

    @ApiModelProperty("HS编码")
    @NotBlank(message = "HS编码不能为空")
    private String hsCode;

    @ApiModelProperty("商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    @ApiModelProperty("申报币制")
    @NotBlank(message = "申报币制不能为空")
    private String currCode;

    @ApiModelProperty("申报单价金额")
    @DecimalMin(value = "0.000001", message = "申报单价金额不能小于0")
    @NotNull(message = "申报单价金额不能为空")
    private BigDecimal declarePrice;

    @ApiModelProperty("商品规格型号")
    @NotBlank(message = "商品规格型号不能为空")
    private String goodsModel;

    @ApiModelProperty("国别")
    @NotBlank(message = "国别不能为空")
    private String originCountry;

    @ApiModelProperty("申报计量单位")
    @NotBlank(message = "申报计量单位不能为空")
    private String goodsUnit;

    @ApiModelProperty("法定计量单位")
    @NotBlank(message = "法定计量单位不能为空")
    private String firstUnit;

    @ApiModelProperty("法一数量")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("第二法定计量编码")
    private String secondUnit;

    @ApiModelProperty("法二数量")
    private BigDecimal secondUnitAmount;

    @ApiModelProperty("账册id")
    private Long customsBookId;

    @ApiModelProperty("记账清单商品序号")
    private String invtGoodsNo;

    @ApiModelProperty("记账清单编号")
    private String invtNo;

    @ApiModelProperty("入仓数量")
    @Min(value = 1, message = "入仓数量不能小于1")
    private Integer inQty;

    @ApiModelProperty("账册库存")
    @Min(value = 0, message = "账册不能小于0")
    private Integer accountNum;

    @ApiModelProperty("占用数量")
    @Min(value = 0, message = "占用数量不能小于0")
    private Integer occupiedNum;

//    @ApiModelProperty("在途库存数量")
//    @Min(value = 0, message = "在途库存数量不能小于0")
//    private Integer ongoingNum;
//
//    @ApiModelProperty("锁定库存数量")
//    @Min(value = 0, message = "锁定库存数量不能小于0")
//    private Integer lockedNum;

    @ApiModelProperty("已用库存数量")
    @Min(value = 0, message = "已用库存数量不能小于0")
    private Integer usedNum;

    @ApiModelProperty("可用库存数量")
    @Min(value = 0, message = "可用库存数量不能小于0")
    private Integer availableNum;

    @ApiModelProperty("入仓法定数量")
    @DecimalMin(value = "0.000001", message = "入仓法定数量不能小于1")
    private BigDecimal inLegalQty;

    @ApiModelProperty("第二入仓法定数量")
    @DecimalMin(value = "0.000001", message = "第二入仓法定数量不能小于1")
    private BigDecimal inSecondLegalQty;

    @ApiModelProperty("最近入仓（核增）日期")
    private Long inDate;

    @ApiModelProperty("平均美元单价")
    //@DecimalMin(value = "0.000001", message = "平均美元单价不能小于0")
    private BigDecimal avgPrice;

    @ApiModelProperty("库存美元总价")
    //@DecimalMin(value = "0.000001", message = "库存美元总价不能小于0")
    private BigDecimal totalAmt;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("0:停用;1:启用(默认)")
    @Range(min = 0, max = 1, message = "启用状态标识不合法")
    private Integer enable;

    /**
     * import表示导入
     */
    @ApiModelProperty("来源")
    private String source;
    /**
     * 商品来源标识: 1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件，默认为空
     */
    private String goodsSource;
}
