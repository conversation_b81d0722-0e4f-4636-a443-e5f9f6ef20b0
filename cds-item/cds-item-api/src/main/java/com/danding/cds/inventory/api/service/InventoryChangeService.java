package com.danding.cds.inventory.api.service;


import com.danding.cds.inventory.api.dto.InventoryChangeDTO;
import com.danding.cds.inventory.api.dto.InventoryChangeDetailSearch;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

public interface InventoryChangeService {

    /**
     * 保存库存变动记录
     * @param updateInventoryDTO
     */
    Long saveLogs(UpdateInventoryDTO updateInventoryDTO);

    /**
     * 查找变动记录
     *
     * @param inventoryChangeDetailSearch
     * @return
     */
    ListVO<InventoryChangeDTO> paging(InventoryChangeDetailSearch inventoryChangeDetailSearch);


    void insertByStockOccupiedCount(List<StockOccupiedCountResDTO> stockOccupiedCountResDTOList);

    List<Long> batchSaveLogs(List<UpdateInventoryDTO> batchLogsList);
}
