package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 单耗质疑标志
 */
@Getter
@AllArgsConstructor
public enum ProcessTradeBookConsumptionQuestionFlagEnums {

    NOT_QUESTION(0, "不质疑"),
    QUESTION(1, "质疑");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookConsumptionQuestionFlagEnums getEnums(Integer code) {
        for (ProcessTradeBookConsumptionQuestionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookConsumptionQuestionFlagEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
