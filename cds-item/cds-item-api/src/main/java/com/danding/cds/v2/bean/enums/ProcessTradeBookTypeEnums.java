package com.danding.cds.v2.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ProcessTradeBookTypeEnums {

    E_BOOK(1, "E账册"),
    H_BOOK(2, "H账册"),
    CONSUMPTION(3, "耗料"),
    WORK_ORDER(4, "工单"),
    ENTERPRISE(5, "企业为单元"),
    OUTSIDE_WORK_ORDER(6, "区外工单账册");

    private final Integer code;
    private final String desc;

    public static ProcessTradeBookTypeEnums getEnums(Integer code) {
        for (ProcessTradeBookTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }

    public static String getDesc(Integer code) {
        for (ProcessTradeBookTypeEnums enums : values()) {
            if (enums.getCode().equals(code)) {
                return enums.getDesc();
            }
        }
        return "";
    }
}
