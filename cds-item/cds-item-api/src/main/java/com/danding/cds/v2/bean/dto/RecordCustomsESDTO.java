package com.danding.cds.v2.bean.dto;

import com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum;
import lombok.Data;

import javax.persistence.Transient;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 备案-口岸
 * @date 2022/3/7 15:12
 */
@Data
public class RecordCustomsESDTO implements Serializable {

    private Long id;

    private Date createTime;

    private Date updateTime;

    private Integer createBy;

    private Integer updateBy;

    private Boolean deleted;
    /**
     * 商品备案id
     */
    private Long recordId;
    /**
     * 料号
     */
    private String productId;
    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;
    /**
     * hs编码
     */
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;

    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 状态 待审核0 /通过 1
     * 1, "待审核";2, "备案完成";4, "审核驳回"  GoodsRecordStatusEnum
     */
    private Integer status;

    /**
     * 状态 新品1 /更新 2
     * {@link RecordCustomsSubmitTypeEnum}
     */
    private Integer submitType;

    /**
     * 备案提交时间
     */
    private Date submitTime;

    /**
     * 当前货主编码
     */
    @Transient
    private String currentOwnerCode;
    /**
     * 当前仓库编码
     */
    @Transient
    private String currentWareCode;

    /**
     * 审核方式 0-人工审核， 1-系统审核
     */
    private Integer auditWay;
    /**
     * ERP提交审核状态
     */
    private Integer erpCommitStatus;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
