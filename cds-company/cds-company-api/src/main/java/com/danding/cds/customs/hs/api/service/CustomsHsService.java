package com.danding.cds.customs.hs.api.service;

import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.customs.hs.api.dto.CustomsHsSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Auther: shenfeng
 * @Date: 2020-04-29 09:50
 * @Description:
 */
public interface CustomsHsService {
    /**
     * 根据ID查询
     */
    CustomsHsDTO findById(Long id);

    /**
     * 根据ID删除
     */
    Long deleteById(Long id);

    /**
     * 根据编码查询
     */
    CustomsHsDTO findByCode(String code);

    List<CustomsHsDTO> findByCode(List<String> code);

    /**
     * 筛选可用
     * @return
     */
    List<CustomsHsDTO> listEnable();

    /**
     * 获取所有
     * @return
     */
    public List<CustomsHsDTO> getCustomsHsAll();

    /**
     * 分页查询
     * @param customsHsSearchCondition
     * @return
     */
    ListVO<CustomsHsDTO> paging(CustomsHsSearchCondition customsHsSearchCondition);

    /**
     * 启用/禁用
     * @param id
     * @param enable
     * @return
     */
    Long updateEnable(Long id,Integer enable);

    /**
     * 编辑
     * @param submit
     * @return
     */
    Long upset(CustomsHsSubmit submit) throws ArgsErrorException;
}
