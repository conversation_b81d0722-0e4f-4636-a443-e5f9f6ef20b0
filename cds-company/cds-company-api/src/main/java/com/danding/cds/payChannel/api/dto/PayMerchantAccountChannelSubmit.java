package com.danding.cds.payChannel.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 15:17
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountChannelSubmit implements Serializable {
    @ApiModelProperty("ID,更新时必传")
    private Long id;
    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码")
    private String merchantSn;

    /**
     * 渠道
     */
    @ApiModelProperty("渠道（wechatpay/alipay）")
    private String channel;

    /**
     * 收款账号
     */
    @ApiModelProperty("收款账号")
    private String recpAccount;

    /**
     * 收款企业编号
     */
    @ApiModelProperty("收款企业编号")
    private String recpCode;

    /**
     * 收款企业名称
     */
    @ApiModelProperty("收款企业名称")
    private String recpName;


    @ApiModelProperty("微信商户号")
    private String mchId;

    @ApiModelProperty("微信商户秘钥")
    private String partnerKey;

    /**
     * 老版合作者身份ID
     */
    @ApiModelProperty("支付宝-老版合作者身份partnerID")
    private String partner;

    /**
     * 支付宝接口的密钥(md5), 签名和验签的时候使用
     */
    @ApiModelProperty("支付宝-密钥")
    private String key;



    @ApiModelProperty("APP_ID")
    private String appId;

    /**
     * 商户Id
     */
    @ApiModelProperty("商户Id")
    private String clientId;

    /**
     * 商户密钥
     */
    @ApiModelProperty("商户密钥")
    private String clientSecret;
}
