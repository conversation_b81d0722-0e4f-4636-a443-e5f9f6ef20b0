package com.danding.cds.message.api.dto;

import com.danding.cds.message.api.enums.MessageType;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/04/22
 * @Description:
 */
@Data
public class MessageCreateDTO implements Serializable {

    private MessageType type;

    private String businessCode;

    private List<String> tagList;

    private String activeData;

    private String notifyUrl;

    private Integer delayLevel;

    /**
     * 是否多任务消息
     */
    private Boolean isMultiTask;
}
