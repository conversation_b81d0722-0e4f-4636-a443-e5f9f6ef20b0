package com.danding.cds.service;

import com.danding.cds.bean.dto.CallbackLogDTO;
import com.danding.cds.callback.api.dto.CallbackLogSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/8/31  16:08
 * @Describe
 **/
public interface CustomsCallbackLogService {
    void saveLogs(List<CallbackLogDTO> list);

    void retryCallback(Long id);

    void retryBatchByIds(List<Long> ids);

    void retryBatchByCondition(CallbackLogSearch search);

    ListVO<CallbackLogDTO> paging(CallbackLogSearch search);
}
