package com.danding.cds.callback.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/20 15:58
 * @Description:
 */
@Data
public class PaymentActive implements Serializable {

    private String orderSn;

    /**
     * 申报是否成功
     */
    private Boolean success;

    /**
     * 身份证验证是否失败
     */
    private Boolean identityCheck = true;

    /**
     * 验核机构
     */
    private String verDept;

    /**
     * 验核交易流水号
     */
    private String payTransactionId;

    /**
     * 第三方子订单流水号
     */
    private String subBankNo;

    /**
     * 备注信息
     */
    private String extra;

    private String postMsg;

    private String customs;

    private String outRequestNo;

    /**
     * 失败信息
     */
    private String errorMsg;
}
