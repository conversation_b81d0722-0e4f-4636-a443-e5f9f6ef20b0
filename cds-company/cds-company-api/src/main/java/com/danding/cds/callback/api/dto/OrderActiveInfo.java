package com.danding.cds.callback.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class OrderActiveInfo implements Serializable {

    private static final long serialVersionUID = -7013009062016281661L;
    private Long id;

    private String customsInventorySn;

    /**
     * 清关状态
     */
    private String customsStatus;

    /**
     * 清关回执描述
     */
    private String customsDetail;

    /**
     * 海关回执报文
     */
    private String responseMsg;

    private Date customsTime;

    private String companyCode;
    private String companyName;


    public OrderActiveInfo() {
    }

    public OrderActiveInfo(Long id) {
        this.id = id;
    }

    public OrderActiveInfo(Long id, String customsInventorySn) {
        this.id = id;
        this.customsInventorySn = customsInventorySn;
    }

    public OrderActiveInfo buildCustomsInfo(String customsStatus, String customsDetail, Date customsTime) {
        this.customsStatus = customsStatus;
        this.customsDetail = customsDetail;
        this.customsTime = customsTime;
        return this;
    }

    public OrderActiveInfo buildCustomsInfo(String customsStatus, String customsDetail, Date customsTime, String companyCode, String companyName) {
        this.customsStatus = customsStatus;
        this.customsDetail = customsDetail;
        this.customsTime = customsTime;
        this.companyCode = companyCode;
        this.companyName = companyName;
        return this;
    }

    public OrderActiveInfo buildResponseMsg(String responseMsg) {
        this.responseMsg = responseMsg;
        return this;
    }
}
