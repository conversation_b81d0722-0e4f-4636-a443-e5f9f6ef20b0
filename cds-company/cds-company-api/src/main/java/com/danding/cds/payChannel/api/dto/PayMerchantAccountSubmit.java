package com.danding.cds.payChannel.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 11:23
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountSubmit implements Serializable {

    @ApiModelProperty("ID,更新时必传")
    private Long id;

    @ApiModelProperty("商户编码")
    private String sn;

    @ApiModelProperty("商户名称")
    private String name;

    @ApiModelProperty("备注")
    private String note;
}

