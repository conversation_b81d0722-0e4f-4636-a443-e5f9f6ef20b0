package com.danding.cds.express.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@ApiModel
public class ExpressFilterSubmit implements Serializable {
    private static final long serialVersionUID = -1408190079231653222L;
    @ApiModelProperty("refExpressId,创建相关联的快递公司ID")
    @NotBlank(message = "refExpressId不能为空")
    private Long refExpressId;

    @ApiModelProperty("省")
    @NotBlank(message = "省不能为空")
    private String prov;
    /**
     * 市
     */
    @ApiModelProperty("市")
    @NotBlank(message = "市不能为空")
    private String city;
    /**
     * 区
     */
    @ApiModelProperty("区")
    @NotBlank(message = "区不能为空")
    private String area;

    @ApiModelProperty("替换的快递公司")
    @NotBlank(message = "替换的快递公司为空")
    private Long replaceExpressId;
}
