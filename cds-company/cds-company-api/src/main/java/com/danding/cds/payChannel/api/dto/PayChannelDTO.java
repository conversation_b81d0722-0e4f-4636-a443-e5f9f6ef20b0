package com.danding.cds.payChannel.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 09:35
 * @Description:
 */
@Data
public class PayChannelDTO implements Serializable {
    private static final long serialVersionUID = -6938841887823174502L;
    private Long id;

    /**
     * 支付渠道标识
     */
    private String code;

    /**
     * 支付渠道名称
     */
    private String name;

    /**
     * 支付企业ID
     */
    private Long payCompanyId;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;
    /**
     * 创建时间
     */
    private String createTime;
}
