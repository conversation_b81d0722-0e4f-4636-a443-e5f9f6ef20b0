package com.danding.cds.callback.api.enums;

/**
 * @Author: gxj
 * @Date: 2019/06/21 17:30
 * @Description:
 */
public enum CallbackStatus {
    NULL(0,"空"),
    DEC_WAIT(1,"待回传"),
    DEC_ING(10,"回传成功"),
    FAIL(-10,"回传失败");

    private Integer value;

    private String desc;

    CallbackStatus(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public Integer getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CallbackStatus getEnum(Integer value){
        for (CallbackStatus CallbackStatus : CallbackStatus.values()) {
            if (CallbackStatus.getValue() == value){
                return CallbackStatus;
            }
        }
        return NULL;
    }
}
