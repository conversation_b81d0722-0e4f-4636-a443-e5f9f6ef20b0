package com.danding.cds.vehicle.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@ApiModel
public class CheckListVehicleDTO implements Serializable {
    private static final long serialVersionUID = 6622208802723182539L;

    private Long id;

    @ApiModelProperty("车牌")
    private String vehicleLicensePlate;

    @ApiModelProperty("车重")
    private BigDecimal vehicleWeight;

    @ApiModelProperty("车架")
    private String vehicleFrameNo;

    @ApiModelProperty("车架重")
    private BigDecimal vehicleFrameWeight;
}
