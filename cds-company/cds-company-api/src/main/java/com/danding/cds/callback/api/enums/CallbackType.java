package com.danding.cds.callback.api.enums;

public enum CallbackType {
    NULL("","空"),
    ORDER("ORDER","申报单回传")
    ;

    private String code;

    private String desc;

    CallbackType(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CallbackType getEnum(String code){
        for (CallbackType value : CallbackType.values()) {
            if (value.getCode().equals(code)){
                return value;
            }
        }
        return NULL;
    }
}
