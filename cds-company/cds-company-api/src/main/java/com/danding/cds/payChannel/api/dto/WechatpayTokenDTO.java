package com.danding.cds.payChannel.api.dto;



import lombok.Data;

import java.io.Serializable;
/**
 * @Author: Raymond
 * @Date: 2020/8/21 11:12
 * @Description:
 */
@Data
public class WechatpayTokenDTO  implements Serializable {

    private static final long serialVersionUID = 4241240454224578672L;

    /**
     * 应用ID
     */
    private String appId;

    /**
     * 秘钥
     */
    private String secret;

    /**
     * 商户号
     */
    private String mchId;

    /**
     * 商户秘钥
     */
    private String partnerKey;

    /**
     * todo:??加密文件路径
     */
    private String caFilePath;

    /**
     * todo:??解密文件路径
     */
    private String certFilePath;
}
