package com.danding.cds.payChannel.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:06
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantCustomsInfoSearch extends Page {
    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码")
    private String merchantSn;

    @ApiModelProperty("商户海关备案编号")
    private String merchantCustomsCode;

    /**
     * 申报海关编号
     */
    @ApiModelProperty("申报海关编号")
    private String customs;
}
