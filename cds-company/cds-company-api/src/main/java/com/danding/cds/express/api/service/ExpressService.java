package com.danding.cds.express.api.service;

import com.danding.cds.express.api.dto.ExpressConfigParam;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.dto.ExpressSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 10:45
 * @Description:
 */
public interface ExpressService {

    ExpressDTO findByCode(String code);

    ListVO<ExpressDTO> paging(ExpressSearch search);

    Long upset(ExpressSubmit submit) throws ArgsErrorException;

    ExpressDTO findById(Long id);

    List<ExpressDTO> findByIds(List<Long> idList);

    List<ExpressDTO> listEnable();

    List<ExpressDTO> listAll();

    public ExpressDTO findOneByName(String name);

    List<ExpressDTO> findByName(List<String> nameList);

    void config(ExpressConfigParam param);
}
