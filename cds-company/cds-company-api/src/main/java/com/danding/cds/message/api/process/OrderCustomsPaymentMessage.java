package com.danding.cds.message.api.process;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/20 16:04
 * @Description:
 */
@Data
public class OrderCustomsPaymentMessage implements Serializable {

    private String orderSn;

    private String outOrderNo;

    private String declareOrderNo;
    /**
     * 申报是否成功
     */
    private Boolean success;

    /**
     * 身份证验证是否失败
     */
    private Boolean identityCheck = true;

    /**
     * 验核机构
     */
    private String verDept;

    /**
     * 验核交易流水号
     */
    private String payTransactionId;

    /**
     * 第三方子订单流水号
     */
    private String subBankNo;

    private String errorMsg;

    private String extra;
}
