package com.danding.cds.service;

import com.danding.cds.bean.dto.DeclareWayDTO;
import com.danding.cds.bean.vo.req.DeclareWaySearch;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

public interface DeclareWayService {
    ListVO<DeclareWayDTO> paging(DeclareWaySearch search);

    void changeEnable(Long id) throws ArgsErrorException;

    List<DeclareWayDTO> listAll();

    List<DeclareWayDTO> listByType(String type, String property);

    List<DeclareWayDTO> listByTypeAndNode(String type);

    List<DeclareWayDTO> listByTypeAndNode(String type, Integer customsTransferNode);

}
