package com.danding.cds.payChannel.api.service;

import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:21
 * @Description: 整块废弃
 */
public interface PayMerchantCustomsInfoService {
    ListVO<PayMerchantCustomsInfoDTO> paging(PayMerchantCustomsInfoSearch search) ;


    Long upset(PayMerchantCustomsInfoSubmit submit) throws ArgsErrorException ;

    List<PayMerchantCustomsInfoDTO> queryListPayMerchantCustomsInfoExport(PayMerchantCustomsInfoSearch search);


    PayMerchantCustomsInfoDTO findById(long id);

    PayMerchantCustomsInfoDTO findByCodeAndCustoms(String merchantSn, String customs);
    PayMerchantCustomsInfoDTO findByMerchantAndCustoms(Long id, String customs);

    List<SelectItemVO> listPayCustoms();
}
