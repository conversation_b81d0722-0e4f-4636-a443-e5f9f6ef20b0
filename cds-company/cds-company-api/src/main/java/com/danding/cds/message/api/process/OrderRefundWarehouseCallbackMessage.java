package com.danding.cds.message.api.process;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.io.Serializable;

/**
 * 退货仓 消息回传请求体
 */
@Data
@AllArgsConstructor
public class OrderRefundWarehouseCallbackMessage implements Serializable {

    /**
     * 运单号
     */
    private String logisticsNo;

    /**
     * 逆向运单号
     */
    private String refundLogisticsNo;

    /**
     * 回传状态
     * 1 - 发起申报， 2 - 申报成功， 3 - 申报失败
     */
    private Integer callbackStatus;

    /**
     * wms实体仓编码
     */
    private String wmsWarehouseCode;
}
