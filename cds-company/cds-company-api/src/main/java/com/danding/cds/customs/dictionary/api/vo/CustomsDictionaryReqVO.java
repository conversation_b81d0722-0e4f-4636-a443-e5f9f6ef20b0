package com.danding.cds.customs.dictionary.api.vo;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

@Data
public class CustomsDictionaryReqVO extends Page {
    /**
     * 数据值
     */
    private String code;

    /**
     * 编码名称
     */
    private String name;

    /**
     * 类型
     */
    private String type;

    /**
     * 启用状态
     */
    private Integer enable;

    /**
     * 数据类型
     */
    private Integer mainType;

    /**
     * 创建时间
     */
    private Long createTimeFrom;
    private Long createTimeTo;
}