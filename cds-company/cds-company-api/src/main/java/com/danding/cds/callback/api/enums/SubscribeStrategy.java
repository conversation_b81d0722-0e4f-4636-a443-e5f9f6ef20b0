package com.danding.cds.callback.api.enums;

public enum SubscribeStrategy {
    NULL("NULL","空"),
    INCREASING("INCREASING","递增"),
    LINEAR("LINEAR","线性")
    ;

    private String code;

    private String desc;

    SubscribeStrategy(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static SubscribeStrategy getEnum(String code){
        for (SubscribeStrategy value : SubscribeStrategy.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return NULL;
    }
}
