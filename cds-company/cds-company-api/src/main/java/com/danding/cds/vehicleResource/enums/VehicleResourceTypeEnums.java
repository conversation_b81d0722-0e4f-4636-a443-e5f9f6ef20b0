package com.danding.cds.vehicleResource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆资源-车辆类型
 */
@Getter
@AllArgsConstructor
public enum VehicleResourceTypeEnums {

    BOX_TRUCK("BOX_TRUCK", "箱型货车"),
    CONTAINER_TRUCK("CONTAINER_TRUCK", "集装箱车");

    private final String value;
    private final String desc;

    public static VehicleResourceTypeEnums getEnum(String value) {
        VehicleResourceTypeEnums[] vehicleResourceTypeEnums = VehicleResourceTypeEnums.values();
        for (VehicleResourceTypeEnums vehicleResourceTypeEnum : vehicleResourceTypeEnums) {
            if (vehicleResourceTypeEnum.getValue().equals(value)) {
                return vehicleResourceTypeEnum;
            }
        }
        return null;
    }

    public static VehicleResourceTypeEnums getEnumByDesc(String desc) {
        VehicleResourceTypeEnums[] vehicleResourceTypeEnums = VehicleResourceTypeEnums.values();
        for (VehicleResourceTypeEnums vehicleResourceTypeEnum : vehicleResourceTypeEnums) {
            if (vehicleResourceTypeEnum.getDesc().equals(desc)) {
                return vehicleResourceTypeEnum;
            }
        }
        return null;
    }
}
