package com.danding.cds.message.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MessageTaskDTO implements Serializable {
    private Long id;

    /**
     * 订阅ID
     */
    private Long subscribeId;

    /**
     * 订阅ID
     */
    private Long messageId;

    /**
     * 消息类型|冗余
     */
    private String type;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 业务编号|冗余
     */
    private String businessCode;

    /**
     * 执行回传地址
     */
    private String notifyUrl;

    /**
     * 发送记录
     */
    private String sendRecordJson;

    /**
     * 通知次数
     */
    private Integer count;

    /**
     * 最后回传时间
     */
    private Date lastNotifyTime;

    /**
     * 下次回传时间
     */
    private Date nextNotifyTime;

    /**
     * 执行状态
     */
    private Integer status;

    /**
     * 请求数据
     */
    private String requestData;

    private Date createTime;

    private Long preTaskId;

    private Long nextTaskId;

    private Long nextMessageId;

    private Long groupId;

    private Integer delayLevel;
}
