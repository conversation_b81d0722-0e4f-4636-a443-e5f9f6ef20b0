package com.danding.cds.customs.manager.api.vo;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@Data
@ApiModel
public class CustomsDistrictEnableParam implements Serializable {

    private static final long serialVersionUID = -5449683921277047198L;

    @NotNull(message = "id不能为空")
    private Long id;

    @NotNull(message = "状态不能为空")
    private Integer status;
}
