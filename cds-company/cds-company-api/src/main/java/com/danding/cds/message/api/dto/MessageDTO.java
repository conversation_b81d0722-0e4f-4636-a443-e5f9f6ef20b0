package com.danding.cds.message.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class MessageDTO implements Serializable {

    private Long id;

    /**
     * 精准回传地址
     */
    private String notifyUrl;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 多个消息标签
     */
    private String tagJson;

    /**
     * 消息状态
     */
    private Integer status;

    /**
     * 业务编号
     */
    private String businessCode;

    /**
     * 触发回传的初始参数
     */
    private String activeData;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 多消息任务标识
     */
    private Boolean multiTask;

    /**
     * 消息组id 当多条消息之间有关联时设置
     */
    private Long groupId;

    /**
     * 前一条消息id
     */
    private Long preMessageId;

    /**
     * 后一条消息id
     */
    private Long nextMessageId;

    /**
     * 创建时间
     */
    private Date createTime;
}
