package com.danding.cds.callback.api.service;

import com.danding.cds.callback.api.dto.CallbackRecordDTO;
import com.danding.cds.callback.api.dto.CallbackSearch;
import com.danding.cds.callback.api.enums.CallbackType;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

public interface CallbackRecordService {

    /**
     * 创建回传任务
     * @return
     */
    Boolean createTask(CallbackType type, String businessCode,Object info);

    /**
     * 根据ID查找
     * @param id
     * @return
     */
    CallbackRecordDTO findById(Long id);

    List<CallbackRecordDTO> listByTypeAndBusinessCode(String type, String businessValue);

    /**
     * 按照状态查询
     * @param status
     * @return
     */
    List<CallbackRecordDTO> listByStatus(Integer status);

    /**
     * 根据ID更新
     * @param id
     * @param status
     * @return
     */
    Integer updateStatusById( Long id,  Integer status);

    Integer updateDataById( Long id,  String requestData, String responseData);

    ListVO<CallbackRecordDTO> paging(CallbackSearch search);

}
