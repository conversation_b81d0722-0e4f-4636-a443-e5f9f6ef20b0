package com.danding.cds.vehicleResource.service;

import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.vehicleResource.dto.VehicleResourceDTO;
import com.danding.cds.vehicleResource.dto.VehicleResourcePagingRequest;
import com.danding.component.common.api.common.response.ListVO;

import java.util.List;

public interface VehicleResourceService {

    List<VehicleResourceDTO> selectAll();

    VehicleResourceDTO findById(Long id);

    List<VehicleResourceDTO> findById(List<Long> idList);

    List<VehicleResourceDTO> findByCode(List<String> vehicleResourceCodeList);

    List<VehicleResourceDTO> findByCode(String vehicleResourceCode);

    List<VehicleResourceDTO> findByVehiclePlate(String vehiclePlate);

    List<VehicleResourceDTO> findByVehiclePlate(List<String> vehiclePlateList);

    ListVO<VehicleResourceDTO> paging(VehicleResourcePagingRequest request);

    void enable(List<Long> id, Integer enable);

    ImportResultResVo importExcel(VehicleResourceDTO vehicleResourceDTO);

    VehicleResourceDTO findByEnableVehicleNo(String vehiclePlate);

    List<VehicleResourceDTO> findEnableByCode(String vehicleResourceCode);

    void updateVehiclePlateById(Long id, String truckNo);

    void insert(VehicleResourceDTO dbTruck);
}
