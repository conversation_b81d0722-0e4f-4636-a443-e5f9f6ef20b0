package com.danding.cds.customs.manager.api.service;

import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictAddParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictEnableParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictSearch;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictUpdateParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
public interface CustomsDistrictManagerService {


    /**
     * 分页
     * @param search
     * @return
     */
    ListVO<CustomsDistrictDTO> paging(CustomsDistrictSearch search);

    /**
     * 新增
     * @param param
     */
    void insert(CustomsDistrictAddParam param) throws ArgsErrorException;

    /**
     * 修改
     * @param param
     */
    void update(CustomsDistrictUpdateParam param) throws ArgsErrorException;

    /**
     * 删除
     * @param id
     */
    void delete(Long id) throws ArgsErrorException;

    /**
     * 启用停用
     * @param param
     */
    void enableSwitch(CustomsDistrictEnableParam param) throws ArgsErrorException;

    /**
     * 集合
     * @param search
     * @return
     */
    List<CustomsDistrictDTO> list();
}
