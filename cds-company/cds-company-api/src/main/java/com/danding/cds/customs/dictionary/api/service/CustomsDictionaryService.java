package com.danding.cds.customs.dictionary.api.service;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.customs.country.api.dto.DataDictionaryReport;
import com.danding.cds.customs.country.api.dto.ImportExcelDataDictionaryDTO;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryReqVO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryResVO;
import com.danding.cds.customs.dictionary.api.vo.DataDictionaryEnableSwitchVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;
import java.util.Map;

public interface CustomsDictionaryService {

    ListVO<CustomsDictionaryResVO> paging(CustomsDictionaryReqVO search);

    void insert(CustomsDictionaryOperateParam param);

    void insert(List<CustomsDictionaryOperateParam> param);

    void update(CustomsDictionaryOperateParam param);

    void delete(Long id);

    CustomsDictionaryDTO findById(Long id);

    CustomsDictionaryDTO findByCodeAndType(String code, String type);

    CustomsDictionaryDTO findByCodeAndType(String code, List<String> type);

    List<CustomsDictionaryDTO> findByType(String type);

    Map<String, String> getMapByType(String type);

    List<CustomsDictionaryDTO> findByTypeList(List<String> typeList);

    Integer getCountByType(String type);

    List<SelectOptionVO<String>> listByType(String type);

    List<SelectOptionVO<String>> listByTypeBatch(List<String> type);

    /**
     * 更新字典类型下的数据（删除原有数据多余，插入新增数据，自动生成code）
     */
    void updateBatchByType(List<CustomsDictionaryOperateParam> dataList, String type);

    void deleteByCodeType(CustomsDictionaryOperateParam param);

    /**
     * 采用调接口方式 导入数据
     *
     * @param list
     * @param type
     * @param isSave
     * @return
     * @throws ArgsErrorException
     */
    DataDictionaryReport importExcel(List<ImportExcelDataDictionaryDTO> list, String type, boolean isSave) throws ArgsErrorException;

    /**
     * 采用注解方式 导入数据
     *
     * @param dictionaryDTO
     * @param type
     * @return
     */
    ImportResultResVo importExcel(CustomsDictionaryDTO dictionaryDTO, String type);

    void enableSwitch(DataDictionaryEnableSwitchVO switchVO);

    List<CustomsDictionaryDTO> findByTypeAndName(String type, String name);
}
