package com.danding.cds.customs.country.api.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class DataDictionaryReport implements Serializable {

    @ApiModelProperty("导入总数")
    private int totalCount;

    @ApiModelProperty("成功数量")
    private int successCount;

    @ApiModelProperty("失败数量")
    private int failCount;

    @ApiModelProperty("成功列表")
    private List<ImportExcelDataDictionaryDTO> successRecordList;

    @ApiModelProperty("失败列表")
    private List<ImportExcelDataDictionaryDTO> failRecordList;
}
