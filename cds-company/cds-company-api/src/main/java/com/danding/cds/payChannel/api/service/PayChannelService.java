package com.danding.cds.payChannel.api.service;

import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayChannelSearch;
import com.danding.cds.payChannel.api.dto.PayChannelSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 09:29
 * @Description:
 */
public interface PayChannelService {

    ListVO<PayChannelDTO> paging(PayChannelSearch search);

    Long upset(PayChannelSubmit submit) throws ArgsErrorException;

    PayChannelDTO findByCode(String code);

    PayChannelDTO findById(Long id);

    List<PayChannelDTO> listEnable();

    List<PayChannelDTO> listAll();
}
