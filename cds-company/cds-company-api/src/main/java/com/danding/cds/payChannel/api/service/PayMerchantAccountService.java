package com.danding.cds.payChannel.api.service;

import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 13:27
 * @Description:
 */
public interface PayMerchantAccountService {
    /**
     * 分页
     * @param search
     * @return
     */
    ListVO<PayMerchantAccountDTO> paging(PayMerchantAccountSearch search);

    /**
     * 保存
     * @param submit
     * @return
     * @throws ArgsErrorException
     */
    Long upset(PayMerchantAccountSubmit submit) throws ArgsErrorException;

    /**
     * 获取
     * @param id
     * @return
     * @throws ArgsErrorException
     */
    PayMerchantAccountDTO findById(long id);
    PayMerchantAccountDTO findBySn(String sn);


    List<PayMerchantAccountDTO> queryListPayMerchantAccountExport(PayMerchantAccountSearch search);
}
