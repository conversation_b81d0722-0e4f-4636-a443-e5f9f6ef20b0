package com.danding.cds.payChannel.api.service;

import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 15:34
 * @Description:
 */
public interface PayMerchantAccountChannelService {

    ListVO<PayMerchantAccountChannelDTO> paging(PayMerchantAccountChannelSearch search);

    List<PayMerchantAccountChannelDTO> queryListPayMerchantAccountChannelExport(PayMerchantAccountChannelSearch search);

    Long upset(PayMerchantAccountChannelSubmit submit) throws ArgsErrorException;

    PayMerchantAccountChannelDTO findByCodeAndChannel(String merchantSn,  String channel);
    PayMerchantAccountChannelDTO findByMerchantAndChannel(Long id,  String channel);

    PayMerchantAccountChannelDTO findById(long id);
}
