package com.danding.cds.message.api.dto;


import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/04/18
 * @Description: 请使用 com.danding.cds.message.impl.utils.MultiActiveDataBuilder#getMultiActiveData 进行构建
 */
@Data
public class MultiActiveDataDTO implements Serializable {

    private List<ActiveDataDTO> activeDataList;

    /**
     * 是否使用细粒度的tag，若为true，则不会使用message上的tag来创建多个消息组
     */
    private Boolean useActiveTag;

    @Data
    public static class ActiveDataDTO implements Serializable {

        /**
         * 任务类型，且不能重复
         * @link com.danding.cds.message.api.enums.MessageTaskType
         */
        private String taskType;

        /**
         * 顺序
         */
        private Integer seq;

        private Object data;

        /**
         * 这里的tag只支持一个
         */
        private String tag;

        /**
         * 延迟级别
         */
        private Integer delayLevel;

    }
}
