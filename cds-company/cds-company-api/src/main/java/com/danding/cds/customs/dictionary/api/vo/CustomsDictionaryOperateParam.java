package com.danding.cds.customs.dictionary.api.vo;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Accessors(chain = true)
public class CustomsDictionaryOperateParam implements Serializable {

    /**
     * id
     */
    private String id;

    /**
     * 编码名称
     */
    private String code;

    /**
     * 数据值(数据标识)
     */
    private String name;

    /**
     * 类型（系统内的类型）
     */
    private String type;

    /**
     * 数据类型
     *
     * @link: DataDictionaryMainTypeEnums
     */
    private Integer mainType;
}
