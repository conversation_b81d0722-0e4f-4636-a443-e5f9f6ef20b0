package com.danding.cds.customs.manager.api.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: yousx
 * @Date: 2023/11/28
 * @Description:
 */
@Data
@EqualsAndHashCode
@ApiModel
public class CustomsDistrictDTO implements Serializable {

    private static final long serialVersionUID = 8014438606417907691L;

    private Long id;

    @ApiModelProperty("编码")
    private String code;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("所属海关")
    private String customs;

    @ApiModelProperty("海关编码")
    private String portCode;

    @ApiModelProperty("租户id")
    private Long tenantryId;

    @ApiModelProperty("状态：1:开启 0:关闭 默认1")
    private Integer status;

    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(hidden = true)
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @ApiModelProperty(hidden = true)
    private Date updateTime;

    @ApiModelProperty(hidden = true)
    private Integer createBy;

    @ApiModelProperty(hidden = true)
    private Integer updateBy;
}
