package com.danding.cds.vehicleResource.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class VehicleResourceDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 车辆资源code
     */
    private String vehicleResourceCode;

    /**
     * 车型名称
     */
    private String vehicleTypeName;

    /**
     * 车辆类型
     * {@link com.danding.cds.vehicleResource.enums.VehicleResourceTypeEnums}
     */
    private String vehicleType;

    /**
     * 车牌号
     */
    private String vehiclePlate;

    /**
     * 运输次数
     */
    private Integer transportTimes;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 货品运输类型
     */
    private String goodsTransportType;

    /**
     * 可装载集装箱类型
     * {@link com.danding.cds.vehicleResource.enums.LoadableContainerTypeEnums}
     */
    private String loadableContainerType;

    /**
     * 车长/车宽/车高（cm）
     */
    private String vehicleDimension;

    /**
     * 额定载重（kg）
     */
    private BigDecimal loadCapacity;

    /**
     * 额定载货体积（m³）
     */
    private BigDecimal loadVolume;

    /**
     * 启用状态
     */
    private Integer enable;

    /**
     * 车辆用途
     * {@link com.danding.cds.transwork.api.enums.TransOrderTypeEnum}
     */
    private String vehiclePurpose;

    /**
     * 车长（cm）
     */
    private BigDecimal length;

    /**
     * 车宽（cm）
     */
    private BigDecimal width;

    /**
     * 车高（cm）
     */
    private BigDecimal height;

}
