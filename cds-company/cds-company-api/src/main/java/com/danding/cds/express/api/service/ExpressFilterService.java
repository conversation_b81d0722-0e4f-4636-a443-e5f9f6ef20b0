package com.danding.cds.express.api.service;

import com.danding.cds.express.api.dto.ExpressFilterDTO;
import com.danding.cds.express.api.dto.ExpressFilterSearch;
import com.danding.logistics.api.common.response.ListVO;


public interface ExpressFilterService {

    boolean createExpressFilter(ExpressFilterDTO entity);
    boolean deleteExpressFilter(Long id);
    ListVO<ExpressFilterDTO> paging(ExpressFilterSearch entity);
    boolean checkUnquie(ExpressFilterDTO entity);
}
