package com.danding.cds.message.api.process;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;

@Data
public class OrderCustomsInventoryMessage implements Serializable {

    @TenantHttpField
    /**
     * 电商平台代码
     */
    private String ebpCode;

    /**
     * 供应链订单全局单号
     */
    private String systemGlobalSn;

    @TenantHttpField
    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 上游单号（若申报需要和某些平台直接交互，可能需要）
     */
    private String outOrderNo;

    /**
     * 清单号
     */
    private String inventoryNo;

    /**
     * 海关状态
     */
    private String customsStatus;

    /**
     * 海关描述
     */
    private String customsDetail;

    /**
     * 发生时间
     */
    private Long time;

    /**
     * 运单号
     */
    private String logisticsNo;

    /**
     * 原始报文
     */
    private String origMessage;

    /**
     * 企业海关注册编号
     */
    private String companyCode;

    /**
     * 企业名称
     */
    private String companyName;
}
