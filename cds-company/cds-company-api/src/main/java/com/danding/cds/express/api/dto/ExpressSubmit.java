package com.danding.cds.express.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 10:55
 * @Description:
 */
@Data
@ApiModel
public class ExpressSubmit implements Serializable {

    @ApiModelProperty("ID,更新时必传")
    private Long id;

    /**
     * 快递标识
     */
    @ApiModelProperty("快递标识")
    @NotBlank(message = "快递标识不能为空")
    private String code;

    /**
     * 快递名称
     */
    @NotBlank(message = "快递名称不能为空")
    @ApiModelProperty("快递名称")
    private String name;

    /**
     * 物流企业ID
     */
    @NotNull(message = "物流企业ID不能为空")
    @ApiModelProperty("物流企业ID")
    private Long expressCompanyId;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    @Range(min = 0,max = 1, message = "启用状态标识不合法")
    private Integer enable;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
}
