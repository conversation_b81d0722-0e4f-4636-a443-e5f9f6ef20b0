package com.danding.cds.payChannel.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:03
 * @Description:
 */
@Data
public class PayMerchantCustomsInfoDTO implements Serializable {
    private static final long serialVersionUID = -6938841887823174505L;
    private Long id;
    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户编码
     */
    private String merchantSn;

    /**
     * 申报海关编号
     */
    private String customs;

    /**
     * 商户海关备案编号
     */
    private String merchantCustomsCode;

    /**
     * 商户海关备案编号
     */
    private String merchantCustomsName;

    private Date createTime;
}
