package com.danding.cds.customs.manager.api.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@Data
@ApiModel
public class CustomsDistrictUpdateParam implements Serializable {

    private static final long serialVersionUID = -2796748366661897738L;

    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty("口岸名称")
    @NotBlank(message = "口岸名称不能为空")
    private String name;

    @ApiModelProperty("口岸编码")
    @NotBlank(message = "口岸编码不能为空")
    private String code;

    @ApiModelProperty("所属海关")
    @NotBlank(message = "所属海关不能为空")
    private String customs;

    @ApiModelProperty("关区编码")
    @NotBlank(message = "关区编码不能为空")
    @Length(max = 4, message = "关区编码长度不能超过4")
    private String portCode;
}
