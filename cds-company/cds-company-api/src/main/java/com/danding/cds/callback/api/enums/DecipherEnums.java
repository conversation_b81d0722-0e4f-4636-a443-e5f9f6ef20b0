package com.danding.cds.callback.api.enums;

public enum DecipherEnums {
    YES(1, "是"),
    NO(0, "否");

    private Integer code;

    private String desc;

    DecipherEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static DecipherEnums getEnum(Integer code) {
        for (DecipherEnums value : DecipherEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NO;
    }
}
