package com.danding.cds.message.api.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

import java.io.Serializable;

@Data
public class MessageSearch extends Page implements Serializable {

    /**
     * 消息id
     */
    private Long id;

    /**
     * 消息类型
     */
    private String type;

    /**
     * 业务编码
     */
    private String businessCode;

    /**
     * 消息状态
     */
    private Integer status;

    //创建时间(开始) 传入的是时间戳
    private Long createTimeFrom;

    //创建时间(结束)
    private Long createTimeTo;
}
