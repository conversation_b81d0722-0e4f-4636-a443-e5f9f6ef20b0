package com.danding.cds.customs.dictionary.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据字典DTO
 */
@Data
public class CustomsDictionaryDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 编码
     */
    private String code;

    /**
     * 编码名称
     */
    private String name;

    /**
     * 简称
     */
    private String shortName;

    /**
     * 类型
     */
    private String type;

    /**
     * 数据类型
     */
    private Integer mainType;

    /**
     * 数据类型
     */
    private Integer enable;

    /**
     * 可编辑
     */
    private Boolean enableModify;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
