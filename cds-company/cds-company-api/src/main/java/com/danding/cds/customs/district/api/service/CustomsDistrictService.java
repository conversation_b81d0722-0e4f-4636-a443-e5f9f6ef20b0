package com.danding.cds.customs.district.api.service;

import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;

import java.util.List;

/**
 * @Author: yousx
 * @Date: 2023/11/28
 * @Description:
 */
public interface CustomsDistrictService {


    /**
     * 通过编码获取口岸
     * @param code
     * @return
     */
    CustomsDistrictDTO getByCode(String code);


    CustomsDistrictDTO getByName(String name);

    /**
     * 获取口岸列表
     * @return
     */
    List<CustomsDistrictDTO> listAll();

    List<CustomsDistrictDTO> getByNameList(List<String> nameList);
}
