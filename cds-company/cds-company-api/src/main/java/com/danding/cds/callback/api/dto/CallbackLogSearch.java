package com.danding.cds.callback.api.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;


@Data
public class CallbackLogSearch extends Page {

    /**
     * 回执海关类型
     *
     * @link: CallbackCustomsTypeEnums
     */
    private String customsType;

    /**
     * 回执类型
     *
     * @link: CallbackTypeEnums
     */
    private String callbackType;

    /**
     * 是否需要解密
     *
     * @link: DecipherEnums
     */
    private Integer decipher;

    /**
     * 业务编号
     */
    private String businessCode;

    /**
     * 创建时间
     */
    private Long createTimeFrom;
    private Long createTimeTo;

    /**
     * 海关回执时间
     */
    private Long customsTimeFrom;
    private Long customsTimeTo;

    /**
     * 回执性质
     */
    private Integer callbackProperty;
}
