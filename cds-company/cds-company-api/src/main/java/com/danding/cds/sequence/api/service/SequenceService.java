package com.danding.cds.sequence.api.service;

/**
 * 生成ID服务
 */
public interface SequenceService {

	Long getTenantIdBySn(String sn);

	/**
	 * 根据CODE生成顺序号
	 * @param code 唯一字符串，用于区分不同的来源
	 * @param reset 是否每天重新计数
	 * @return 顺序号
	 */
	Long nextSequence(String code, boolean reset);

	/**
	 * 不考虑每天重新计数根据CODE生成顺序号
	 * @param code 唯一字符串，用于区分不同的来源
	 * @return 顺序号
	 */
	Long nextSequence(String code);
	String generateSn(String filter,String code);

	String generateExportOrderSn();

	String generateOrderSn();

    String generateExternalOrderSn();

	String generateCustomsInventorySn();
	String generateCustomsOrderSn();
	String generateCustomsLogisticsSn();

	String generateChecklistSn();

	String generateEndorsementSn();

	String generateRefundOrderSn();

	String generateInventoryCancelOrderSn();

	String generateInventoryOrderSn();

	String generateTaxesRechargeOrderSn();

	String generateMerchantAccountSn();

	String generatePaymentDeclareSn();

	String generateCalloffOrderSn();

	String generateCustomsDeclareSn();

	String generateFbChecklistSn(String companyCode);

	String generateInventoryOrderOverdueExceptionId();

    String generateReconciliationSn();

	String generateBizDeclareFormSn();

	Long generateId();

	/**
	 * 生成实体仓sn
	 *
	 * @return
	 */
	String generateEntityWarehouseSn();

    /**
     * 生成协同单号
     *
     * @return
     */
    String generateCollaborateOrderSn();

    /**
     * 生成数据字典的Code
     *
     * @return
     */
    String generateDataDictionaryCode();

    /**
     * 生成出入库单号sn
     */
    String generateStockInOutCode();

	String generateStockInOutCode(String customsBookNo);

	String generateHZDCStockInOutCode(String customsBookNo);

	/**
	 * 生成海关出入库删单编号
	 *
	 * @param wmsCode
	 * @return
	 */
	String generateStockInOutDeleteCode(String wmsCode);


	String generateFbInventoryAdjustSn();

    String generateChecklistAuthSn();

    String generateProcessTradeBookSn();

	/**
	 * 批量步长申报单号
	 *
	 * @param num 数量
	 * @return index开始最小编号, 使用位置需要自己计算数据
	 */
	Long batchStepOrder(Long num);

    Long batchStepExternalOrder(Long num);

	/**
	 * 批量获取清单sn-index
	 * @param num 数量
	 * @return index开始最小编号,使用位置需要自己计算数据
	 */
	Long batchStepCustomsInventory(Long num);

	/**
	 * 批量获取海关订单sn-index
	 * @param num 数量
	 * @return index开始最小编号,使用位置需要自己计算数据
	 */
	Long batchStepCustomsOrder(Long num);

	/**
	 * 批量获取海关运单sn-index
	 * @param num 数量
	 * @return index开始最小编号,使用位置需要自己计算数据
	 */
	Long batchStepCustomsLogistics(Long num);

	/**
	 * 批量获取海关支付单sn-index
	 * @param num 数量
	 * @return index开始最小编号,使用位置需要自己计算数据
	 */
	Long batchStepCustomsPayment(Long num);


}
