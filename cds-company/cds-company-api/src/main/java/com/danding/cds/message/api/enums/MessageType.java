package com.danding.cds.message.api.enums;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;

@AllArgsConstructor
@Getter
public enum MessageType {
    NULL("", "空", null),
    ORDER_RESULT("ORDER_RESULT", "订单申报事件节点", null),
    ORDER_CUSTOMS_PAYMENT("ORDER_CUSTOMS_PAYMENT", "海关支付单回执", null),
    ORDER_CUSTOMS_ORDER("ORDER_CUSTOMS_ORDER", "海关订单回执", null),
    ORDER_CUSTOMS_SHIPMENT("ORDER_CUSTOMS_SHIPMENT", "海关运单回执", null),
    ORDER_CUSTOMS_INVENTORY("ORDER_CUSTOMS_INVENTORY", "海关清单回执", null),
    ORDER_CUSTOMS_CANCEL("ORDER_CUSTOMS_CANCEL", "海关撤单回执", null),
    ORDER_REFUND("ORDER_REFUND", "海关退单回执", null),
    ORDER_REFUND_NEW("ORDER_REFUND_NEW", "海关退单回执_新", null),
    ORDER_TAX("ORDER_TAX", "税单回执", null),
    ORDER_REFUND_TAX("ORDER_REFUND_TAX", "逆向税单回执", null),
    JDL_ORDER_TAX("JDL_ORDER_TAX", "京东税单回执", null),
    ORDER_DELIVER("ORDER_DELIVER", "发货回执", null),
    GOODS_RECORD_EXAMINE("GOODS_RECORD_EXAMINE", "商品备案审核回执", null),
    //回传erp
    ORDER_CUSTOMS_INVENTORY_TO_ERP("ORDER_CUSTOMS_INVENTORY_TO_ERP", "小巨人-海关清单回执回传ERP", null),
    ORDER_CUSTOMS_CANCEL_TO_ERP("ORDER_CUSTOMS_CANCEL_TO_ERP", "小巨人-海关撤单回执ERP", null),
    ORDER_CUSTOMS_REFUND_TO_ERP("ORDER_CUSTOMS_REFUND_TO_ERP", "海关退货回执ERP", null),
    ORDER_TAX_TO_ERP("ORDER_TAX_TO_ERP", "小巨人-税单回执回传ERP", null),
    ORDER_CUSTOMS_TO_JIEZ_TECH("ORDER_CUSTOMS_TO_JIEZ_TECH", "芥舟自申报海关回执",null),
    RECEIVE_OUT_REGION_TO_JD("RECEIVE_OUT_REGION_TO_JD", "京东出区回传", null),
    ORDER_REFUND_REFUND_WAREHOUSE("ORDER_REFUND_REFUND_WAREHOUSE", "海关退货回执_退货仓", null),
    TAOTIAN_REPORT("TAOTIAN_REPORT", "淘天回告", null),
    CHECKLIST_CALLBACK_RETRY_FINISH("CHECKLIST_CALLBACK_RETRY_FINISH", "核放单回执重试（过卡）", null),
    CHECKLIST_CALLBACK_RETRY_PASS("CHECKLIST_CALLBACK_RETRY_PASS", "核放单回执重试（审批）", null),
    JD_REFUND_CUSTOMS_CALLBACK("JD_REFUND_CUSTOMS_CALLBACK", "京东退货回执", null),
    JD_INVENTORY_CUSTOMS_CALLBACK("JD_INVENTORY_CUSTOMS_CALLBACK", "京东电商清单回传", null),
//    MULTI_TASK_TEST("MULTI_TASK_TEST", "多任务测试", Lists.newArrayList(MessageTaskType.MULTI_TASK_TEST_1, MessageTaskType.MULTI_TASK_TEST_2, MessageTaskType.MULTI_TASK_TEST_3)),
    ;


    private final String code;

    private final String desc;

    /**
     * 若多任务情况下想创建多任务执行器，请使用此映射
     */
    private final List<MessageTaskType> messageTaskTypes;

    public static MessageType getEnum(String code) {
        for (MessageType value : MessageType.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NULL;
    }

    public static List<MessageTaskType> getTaskTypeByMessageType(String messageType) {
        for (MessageType value : MessageType.values()) {
            if (value.getCode().equals(messageType)) {
                return value.getMessageTaskTypes();
            }
        }
        return Lists.newArrayList();
    }

    @AllArgsConstructor
    @Getter
    public enum MessageTaskType {
        NULL("", "空"),

//        MULTI_TASK_TEST_1("MULTI_TASK_TEST_1", "多任务测试1"),
//        MULTI_TASK_TEST_2("MULTI_TASK_TEST_2", "多任务测试2"),
//        MULTI_TASK_TEST_3("MULTI_TASK_TEST_3", "多任务测试3"),
        ;
        private final String code;

        private final String desc;

        public static MessageTaskType getEnum(String code) {
            for (MessageTaskType value : MessageTaskType.values()) {
                if (value.getCode().equals(code)) {
                    return value;
                }
            }
            return NULL;
        }
    }
}
