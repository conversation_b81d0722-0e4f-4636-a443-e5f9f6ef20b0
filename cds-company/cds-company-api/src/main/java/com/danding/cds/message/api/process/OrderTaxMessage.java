package com.danding.cds.message.api.process;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class OrderTaxMessage implements Serializable {

    /**
     * 电商企业代码
     */
    private String ebcCode;

    private String declareOrderNo;

    private String invtNo;

    /**
     * 回执时间
     */
    private Long returnTime;

    /**
     * 税单号
     */
    private String taxNo;

    /**
     * 应征关税
     */
    private BigDecimal customsTax;

    /**
     * 应征增值税
     */
    private BigDecimal valueAddedTax;

    /**
     * 应征消费税
     */
    private BigDecimal consumptionTax;

    /**
     * 税单状态（(1-已生成，2-已汇总   3-作废)）
     */
    private String status;

    /**
     * 缴款书编号
     */
    private String entDutyNo;

    private String logisticsNo;

    private List<TaxItem> itemList;

    /**
     * 税单类型 0:正向 1:逆向
     */
    private String sendType;

    @Data
    public static class TaxItem implements Serializable {
        /**
         * 商品项号,从1开始连续序号
         */
        protected int gnum;

        /**
         * 海关商品编码（10位） HS?
         */
        protected String gcode;

        /**
         * 完税总价格
         */
        protected BigDecimal taxPrice;

        /**
         * 应征关税
         */
        protected BigDecimal customsTax;

        /**
         * 应征增值税
         */
        protected BigDecimal valueAddedTax;

        /**
         * 应征消费税
         */
        protected BigDecimal consumptionTax;
    }
}
