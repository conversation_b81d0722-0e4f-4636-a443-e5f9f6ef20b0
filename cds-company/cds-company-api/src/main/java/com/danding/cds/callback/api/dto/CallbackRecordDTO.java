package com.danding.cds.callback.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Deprecated
@Data
public class CallbackRecordDTO implements Serializable {
    private static final long serialVersionUID = -5449620825410194804L;

    private Long id;
    /**
     * 回传地址
     */
    private String notifyUrl;

    /**
     * 业务编号，用于查询关联的回传记录
     */
    private String businessCode;

    /**
     * 通知类型
     */
    private String type;

    /**
     * 通知状态,1:待回传，10:回传成功，-10:回传失败
     */
    private Integer status;

    /**
     * 通知次数
     */
    private Integer count;

    /**
     * 触发回传的初始参数
     */
    private String activeData;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 响应数据
     */
    private String responseData;

    /**
     * 最后回传时间
     */
    private Date lastNotifyTime;

    private Date createTime;
}
