package com.danding.cds.customs.uom.api.service;

import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;

import java.util.List;

/**
 * @Auther: shenfeng
 * @Date: 2020-04-28 17:54
 * @Description:
 */
public interface CustomsUomService {

    /**
     * 根据ID查询
     */
    CustomsUomDTO findById(Long id);

    /**
     * 根据编码查询
     */
    CustomsUomDTO findByCode(String code);

    /**
     * 查询所有
     */
    List<CustomsUomDTO> listAll();

    /**
     * 根据名称查询
     */
    CustomsUomDTO findByName(String name);

}
