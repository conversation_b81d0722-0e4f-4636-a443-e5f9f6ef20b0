package com.danding.cds.payChannel.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:14
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantCustomsInfoSubmit implements Serializable {
    @ApiModelProperty("ID,更新时必传")
    private Long id;
    /**
     * 商户id
     */
    @ApiModelProperty("商户id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ApiModelProperty("商户编码")
    private String merchantSn;

    /**
     * 申报海关编号
     */
    @ApiModelProperty("申报海关编号")
    @NotBlank(message = "商品信息id不能为空")
    private String customs;

    /**
     * 商户海关备案编号
     */
    @ApiModelProperty("商户海关备案编号")
    @NotBlank(message = "商品信息id不能为空")
    private String merchantCustomsCode;

    /**
     * 商户海关备案名称
     */
    @ApiModelProperty("商户海关备案名称")
    @NotBlank(message = "商品信息id不能为空")
    private String merchantCustomsName;
}
