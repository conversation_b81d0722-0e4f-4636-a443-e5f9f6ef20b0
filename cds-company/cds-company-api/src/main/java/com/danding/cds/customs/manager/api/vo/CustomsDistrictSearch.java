package com.danding.cds.customs.manager.api.vo;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel
public class CustomsDistrictSearch extends Page {

    private static final long serialVersionUID = 5131208996403322969L;

    @ApiModelProperty("口岸名称")
    private String name;

    @ApiModelProperty("口岸编码")
    private String code;

    @ApiModelProperty("所属海关")
    private String customs;

    @ApiModelProperty("关区编码")
    private String portCode;
}
