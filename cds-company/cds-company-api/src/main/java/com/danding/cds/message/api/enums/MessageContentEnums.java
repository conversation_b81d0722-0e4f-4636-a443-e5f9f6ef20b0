package com.danding.cds.message.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息管理-消息内容枚举
 */
@Getter
@AllArgsConstructor
public enum MessageContentEnums {

    ORDER_RESULT_DECLARE_START("1", "开始申报", MessageType.ORDER_RESULT),
    JD_INVENTORY_CUSTOMS_CALLBACK("2", "电商清单回传", MessageType.JD_INVENTORY_CUSTOMS_CALLBACK),
//    MULTI_TASK_TEST("MULTI_TASK_TEST", "多任务测试", MessageType.MULTI_TASK_TEST),
    ;
    private final String code;
    private final String desc;
    private final MessageType messageType;

    public static MessageContentEnums getEnums(String code) {
        for (MessageContentEnums enums : MessageContentEnums.values()) {
            if (enums.getCode().equals(code)) {
                return enums;
            }
        }
        return null;
    }
}
