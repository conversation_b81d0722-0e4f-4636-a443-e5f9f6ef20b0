package com.danding.cds.express.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 10:45
 * @Description:
 */
@Data
public class ExpressDTO implements Serializable {
    private static final long serialVersionUID = -1408190079231653062L;
    private Long id;

    /**
     * 快递标识
     */
    private String code;

    /**
     * 快递名称
     */
    private String name;

    /**
     * 物流企业ID
     */
    private Long expressCompanyId;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 运单申报系统
     */
    private String logisticDeclareSystem;

}
