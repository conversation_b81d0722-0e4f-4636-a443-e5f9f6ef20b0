package com.danding.cds.express.api.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ExpressConfigParam implements Serializable {

    /**
     * id
     */
    @NotNull(message = "快递id不能为空")
    private Long id;

    /**
     * 运单申报系统
     */
    @NotBlank(message = "运单申报系统不能为空")
    private String logisticDeclareSystem;
}
