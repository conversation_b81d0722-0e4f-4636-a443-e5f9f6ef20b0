package com.danding.cds.message.api.enums;

public enum MessageStatus {

    WAITING_CREATE(0,"消息待推送"),
    INIT(1,"初始化"),
    READY(2,"已就绪")


            ;

    private final Integer code;

    private final String desc;

    MessageStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MessageStatus getEnum(Integer code){
        for (MessageStatus value : MessageStatus.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return null;
    }
}
