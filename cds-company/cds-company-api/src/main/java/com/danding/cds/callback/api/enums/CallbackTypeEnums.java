package com.danding.cds.callback.api.enums;

public enum CallbackTypeEnums {
    NULL("", "空"),
    PRODUCT("PRODUCT", "商品备案回执"),
    SHIPMENT("SHIPMENT", "运单回执"),
    INVENTORY("INVENTORY", "清单回执"),
    REFUND("REFUND", "退货回执"),
    CANCEL("CANCEL", "撤单回执"),
    TAX("TAX", "税单回执"),
    TAXSTATUS("TAXSTATUS", "税单状态回执"),
    ORDER("ORDER", "订单回执"),

    DELIVER("DELIVER", "发货回执");

    private String code;

    private String desc;

    CallbackTypeEnums(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static CallbackTypeEnums getEnum(String code) {
        for (CallbackTypeEnums value : CallbackTypeEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return NULL;
    }
}

