package com.danding.cds.message.api.enums;

public enum MessageTaskStatus {
    NULL(0,"空"),
    WAITING_CREATE(-2,"消息待推送"),
    INIT(1,"初始化"),
    SUCCESS(2,"成功"),
    FAIL(-1,"失败"),

    ;

    private Integer code;

    private String desc;

    MessageTaskStatus(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MessageTaskStatus getEnum(Integer code){
        for (MessageTaskStatus value : MessageTaskStatus.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return NULL;
    }
}
