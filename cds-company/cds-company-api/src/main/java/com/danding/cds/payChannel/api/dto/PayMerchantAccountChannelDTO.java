package com.danding.cds.payChannel.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 14:52
 * @Description:
 */
@Data
public class PayMerchantAccountChannelDTO implements Serializable {
    private Long id;
    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 商户编码
     */
    private String merchantSn;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 收款账号
     */
    private String recpAccount;

    /**
     * 收款企业编号
     */
    private String recpCode;

    /**
     * 收款企业名称
     */
    private String recpName;


    /**
     * 验签令牌Json字符串，根据渠道由不同类转化而来
     */
    private String tokenJson;

    private Date createTime;

}
