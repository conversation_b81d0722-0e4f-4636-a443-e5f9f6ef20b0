package com.danding.cds.payChannel.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 09:36
 * @Description:
 */
@Data
@ApiModel
public class PayChannelSearch extends Page {

    @ApiModelProperty("支付渠道ID")
    private Long payChannelId;

    @ApiModelProperty("支付企业ID")
    private Long payCompanyId;

    @ApiModelProperty("创建时间起始时间戳")
    private Long createFrom;

    @ApiModelProperty("创建时间终止时间戳")
    private Long createTo;

    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    private Integer enable;
}
