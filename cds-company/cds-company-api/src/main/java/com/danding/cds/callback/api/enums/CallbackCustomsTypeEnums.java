package com.danding.cds.callback.api.enums;

import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

/**
 * 回执海关类型枚举
 *
 * <AUTHOR>
 */
public enum CallbackCustomsTypeEnums {
    CUSTOMS_DECLARE_RESULT_CALLBACK("CUSTOMS_DECLARE_RESULT_CALLBACK", "跨境电商平台回执报文"),
    CUSTOMS_DECLARE_GOODS_CALLBACK("CUSTOMS_DECLARE_GOODS_CALLBACK", "清单审核结果"),
    CUSTOMS_BILL_CALLBACK("CUSTOMS_BILL_CALLBACK", "进口运单出区回执"),
    CUSTOMS_TAX_CALLBACK("CUSTOMS_TAX_CALLBACK", "税款回传"),
    PRODUCT_RECORD("PRODUCT_RECORD", "产品备案信息"),
    CUSTOMS("<PERSON><PERSON><PERSON><PERSON>", "报关申请单"),
    CUSTOMS_CEB_CALLBACK("CUSTOMS_CEB_CALLBACK", "总署版回执");

    private String value;

    private String desc;

    CallbackCustomsTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static CallbackCustomsTypeEnums getEnum(String value) {
        for (CallbackCustomsTypeEnums callbackCustomsTypeEnums : CallbackCustomsTypeEnums.values()) {
            if (callbackCustomsTypeEnums.getValue().equals(value)) {
                return callbackCustomsTypeEnums;
            }
        }
        throw new ArgsErrorException("回执海关类型不存在");
    }
}
