package com.danding.cds.customs.dictionary.api.enums;


import lombok.Getter;

@Getter
public enum DataDictionaryMainTypeEnums {

    BASE(1, "基础数据"),
    BUSINESS(2, "业务数据"),
    OTHER(3, "其他数据");
    private Integer value;

    private String desc;

    DataDictionaryMainTypeEnums(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static DataDictionaryMainTypeEnums getEnumByValue(Integer value) {
        for (DataDictionaryMainTypeEnums type : DataDictionaryMainTypeEnums.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return OTHER;
    }
}
