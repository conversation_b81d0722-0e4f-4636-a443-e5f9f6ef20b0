package com.danding.cds.customs.currency.api.service;

import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;

import java.util.List;

public interface CustomsCurrencyService {

    /**
     * 根据ID查询
     */
    CustomsCurrencyDTO findById(Long id);

    /**
     * 根据编码查询
     */
    CustomsCurrencyDTO findByCode(String code);

    CustomsCurrencyDTO findByName(String name);

    /**
     * 查询所有
     */
    List<CustomsCurrencyDTO> listAll();
}
