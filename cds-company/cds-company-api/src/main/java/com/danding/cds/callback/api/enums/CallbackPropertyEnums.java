package com.danding.cds.callback.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 回执性质
 */
@Getter
@AllArgsConstructor
public enum CallbackPropertyEnums {

    ORIGIN(0, "原始"),
    SIMULATE(1, "模拟");

    private final Integer code;
    private final String desc;

    public static CallbackPropertyEnums getEnums(Integer code) {
        for (CallbackPropertyEnums value : CallbackPropertyEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

}
