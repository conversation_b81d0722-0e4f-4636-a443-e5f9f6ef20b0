package com.danding.cds.message.api.process;

import lombok.Data;

import java.io.Serializable;

@Data
public class OrderResultMessage implements Serializable {

    /**
     * 供应链订单全局单号
     */
    private String systemGlobalSn;

    private String declareOrderNo;

    private String outOrderNo;

    /**
     * start-开始申报
     * fail
     */
    private String status;

    private String detail;


    /**
     * 电商平台
     */
    public String ebpCode;
    public String ebpName;

    /**
     * 电商企业
     */
    public String ebcCode;
    public String ebcName;

    /**
     * 物流企业
     */
    public String logisticsCode;
    public String logisticsName;

    /**
     * 担保企业
     */
    public String assureCode;
    public String assureName;

    /**
     * 支付企业
     */
    public String payCompanyCode;
    public String payCompanyName;

    /**
     * 时间
     */
    public Long time;

}
