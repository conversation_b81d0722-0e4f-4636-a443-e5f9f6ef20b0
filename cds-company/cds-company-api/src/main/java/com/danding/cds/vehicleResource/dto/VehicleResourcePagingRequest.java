package com.danding.cds.vehicleResource.dto;

import com.danding.logistics.api.common.page.Page;
import lombok.Data;

/**
 * 车辆资源分页请求
 */
@Data
public class VehicleResourcePagingRequest extends Page {

    /**
     * 车辆资源code ‘模糊查询’
     */
    private String vehicleResourceCode;

    /**
     * 车辆类型
     */
    private String vehicleType;

    /**
     * 车牌号
     */
    private String vehiclePlate;

    /**
     * 车型名称 '模糊查询'
     */
    private String vehicleTypeName;

    /**
     * 启用
     */
    private Integer enable;

    /**
     * 车辆用途
     */
    private String vehiclePurpose;
}
