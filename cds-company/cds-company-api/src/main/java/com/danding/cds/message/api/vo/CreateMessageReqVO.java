package com.danding.cds.message.api.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 消息管理-新增消息ReqVO
 */
@Data
public class CreateMessageReqVO implements Serializable {

    /**
     * 消息类型
     */
    @NotBlank(message = "消息类型不能为空")
    private String messageType;

    /**
     * 消息内容
     */
    @NotNull(message = "消息内容不能为空")
    private String messageContentType;

    /**
     * 业务代码（多条用','分割）
     */
    @NotBlank(message = "业务编码不能为空")
    private String businessCodes;

}
