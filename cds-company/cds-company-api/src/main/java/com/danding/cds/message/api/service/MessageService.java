package com.danding.cds.message.api.service;

import com.danding.cds.message.api.dto.*;
import com.danding.cds.message.api.enums.MessageStatus;
import com.danding.cds.message.api.enums.MessageTaskStatus;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.vo.CreateMessageReqVO;
import com.danding.logistics.api.common.response.ListVO;

import java.util.Date;
import java.util.List;

public interface MessageService {

    /**
     * 建立订阅
     */
    void createSubscribe(MessageSubscribeDTO messageSubscribeDTO);

    MessageSubscribeDTO findSubscribeById(Long id);

    ListVO<MessageSubscribeDTO> subscribePaging(MessageSubscribeSearch search);

    ListVO<MessageTaskDTO> taskPaging(MessageTaskSearch search);

    List<MessageSubscribeDTO> listSubscribeByTag(String tag);

    List<MessageSubscribeDTO> listSubscribeByTagAndType(String tag, String type);

    List<MessageSubscribeDTO> listSubscribeAll();

    /**
     * 建立消息
     */
    void createMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl);

    void createMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, Integer delayLevel);

    /**
     * 建立多任务消息
     * @param multiActiveData 可使用MultiActiveDataBuilder#getMultiActiveData创建
     */
    void createMultiTaskMessage(MessageType type, List<String> tagList, String businessCode, MultiActiveDataDTO multiActiveData, Integer delayLevel);

    /**
     * 建立多顺序消息
     * @param createDTOS 需要有序
     */
    void createMultiMessage(List<MessageCreateDTO> createDTOS);

    /**
     * 建立消息
     * <p>
     * 不抛异常
     */
    void createMessageNotThrowEx(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl);

    void createMessageNotThrowEx(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, Integer delayLevel);

    void createMessageCallBackERP(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl, String routeCode);

    /**
     * 建立调用订单申报异常消息
     *
     * @param mainOrderId  申报主单ID
     * @param declareNos   申报单号
     * @param tagList      业务标签
     * @param exceptionMsg 异常信息
     */
    void createInvokeOrderExceptionMsg(Long mainOrderId, String declareNos, List<String> tagList, String exceptionMsg);

    /**
     * 建立调用订单申报异常消息
     *
     * @param mainOrderId  申报主单ID
     * @param declareNos   申报单号
     * @param tagList      业务标签
     * @param exceptionMsg 异常信息
     */
    void createInvokeInventoryExceptionMsg(Long mainOrderId, String declareNos, List<String> tagList, String exceptionMsg);

    /**
     * 创建退货消息回传
     *
     * @param businessCode 业务编码
     * @param activeData   数据
     */
    void createRefundMessage(String businessCode, String activeData);

    void createRefundMessage(String businessCode, String activeData, MessageType messageType);

    void createOrRetryMessage(MessageType type, List<String> tagList, String businessCode, String activeData, String notifyUrl);

    List<MessageDTO> listByStatus(MessageStatus status);

    List<MessageDTO> listByCreateTime(Date from, Date to, Integer limit);

    MessageDTO findById(Long id);

    MessageTaskDTO findTaskById(Long id);

    List<MessageTaskDTO> listTaskByStatusAndTime(MessageTaskStatus status, Long startTime, Long endTime);

    /**
     * 生成回传任务
     */
    void generateTask(MessageDTO messageDTO);

    /**
     * 执行回传任务
     */
    void executeTask(MessageTaskDTO messageTaskDTO);

    void migrationHistory(Long id);

    /**
     * 手动重试消息
     *
     * @param messageId 待重试的 消息id
     */
    void retry(Long messageId);

    /**
     * 任务重试
     * @param messageTaskId 消息任务id
     */
    void taskRetry(Long messageTaskId);

    /**
     * 消息管理页面的分页查询
     *
     * @param search 检索字段
     * @return 搜索内容
     */
    ListVO<MessageDTO> paging(MessageSearch search);

    void regenerateReqData(MessageTaskDTO messageTaskDTO);

    void manualCreateMessage(CreateMessageReqVO reqVO);

    List<MessageDTO> findByStatusAndType(Integer status, String type);

    void listInitTaskAndExecute(String type, Long start, Long end, String limit);
}
