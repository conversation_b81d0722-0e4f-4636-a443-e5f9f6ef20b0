package com.danding.cds.callback.api.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * @Auther: Dante-GXJ
 * @Date: 2021/3/1 17:04
 * @Description:
 */
@Data
public class OrderEventActive implements Serializable {

    private Long id;

    private String status;

    private String detail;

    public OrderEventActive() {
    }

    public OrderEventActive(Long id, String status, String detail) {
        this.id = id;
        this.status = status;
        this.detail = detail;
    }
}
