package com.danding.cds.message.api.process;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class OrderDeliverMessage implements Serializable {
    @TenantHttpField
    private String ebpCode;

    @TenantHttpField
    private String declareOrderNo;

    private String outOrderNo;

    private String depotOrderNo;

    private String logisticsNo;

    private BigDecimal weight;

    private Long shipmentTime;
}
