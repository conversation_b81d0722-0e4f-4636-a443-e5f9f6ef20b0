package com.danding.cds.express.api.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class ExpressFilterDTO implements Serializable {
    private static final long serialVersionUID = -1408190079231653062L;
    private Integer idx;
    /**
     * ID
     */
    private Long id;

    /**
     *相关快递ID
     */

    private Long refExpressId;
    /**
     *相关快递代码
     */

    private String refExpressCode;


    /**
     * 省
     */

    private String prov;
    /**
     * 市
     */

    private String city;
    /**
     * 区
     */

    private String area;
    /*
    替换的快递ID
     */

    private Long replaceExpressId;
    /**
     * 替换的快递代码
     */
    private String replaceExpressName;
    private String replaceExpressCode;


    private Date createTime;


    private Date updateTime;


    private Integer createBy;


    private Integer updateBy;

    private Boolean deleted = false;
}
