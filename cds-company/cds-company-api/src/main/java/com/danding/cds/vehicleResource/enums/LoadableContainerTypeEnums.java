package com.danding.cds.vehicleResource.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 车辆资源-可装载集装箱类型
 */
@Getter
@AllArgsConstructor
public enum LoadableContainerTypeEnums {

    EMPTY("", ""),
    SINGLE_20_FEET_CABINET_PALLET("1", "20尺柜单托"),
    SINGLE_40_FEET_CABINET_PALLET("2", "40尺柜单托"),
    DOUBLE_20_FEET_CABINET_PALLET("3", "20尺柜双托"),
    ;

    private final String value;
    private final String desc;

    public static LoadableContainerTypeEnums getEnum(String value) {
        LoadableContainerTypeEnums[] vehicleResourceTypeEnums = LoadableContainerTypeEnums.values();
        for (LoadableContainerTypeEnums vehicleResourceTypeEnum : vehicleResourceTypeEnums) {
            if (vehicleResourceTypeEnum.getValue().equals(value)) {
                return vehicleResourceTypeEnum;
            }
        }
        return EMPTY;
    }
}
