package com.danding.cds.message.api.dto;

import lombok.Data;

import javax.persistence.Column;
import java.io.Serializable;

@Data
public class MessageSubscribeDTO implements Serializable {
    private Long id;
    /**
     * 订阅标签
     */
    @Column(name = "subscribe_tag")
    private String subscribeTag;

    /**
     * 接收的消息类型列表
     */
    @Column(name = "message_type_json")
    private String messageTypeJson;

    /**
     * 回传地址
     */
    @Column(name = "notify_url")
    private String notifyUrl;

    /**
     * 订阅说明
     */
    private String detail;

    /**
     * 报警邮箱
     */
    @Column(name = "alert_emails")
    private String alertEmails;

    /**
     * 最大通知次数
     */
    @Column(name = "max_count")
    private Integer maxCount;

    /**
     * 重试策略
     */
    @Column(name = "retry_strategy")
    private String retryStrategy;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Boolean enable;
}
