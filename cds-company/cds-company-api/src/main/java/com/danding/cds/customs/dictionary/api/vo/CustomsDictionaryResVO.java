package com.danding.cds.customs.dictionary.api.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class CustomsDictionaryResVO implements Serializable {

    private String id;

    /**
     * 数据标识
     */
    private String code;

    /**
     * 数据名称
     */
    private String name;

    /**
     * 数据类型
     */
    private String mainTypeDesc;

    private Integer mainType;

    /**
     * 启用状态
     */
    private Integer enable;

    /**
     * 创建时间
     */
    private Date createTime;
}
