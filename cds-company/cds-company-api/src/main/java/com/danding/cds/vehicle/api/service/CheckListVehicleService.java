package com.danding.cds.vehicle.api.service;

import com.danding.cds.vehicle.api.dto.CheckListVehicleDTO;
import com.danding.cds.vehicle.api.dto.CheckListVehicleSubmit;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

public interface CheckListVehicleService {

    CheckListVehicleDTO findByVehicleLicense(String vehicleLicense);

    CheckListVehicleDTO findById(Long id);

    Long upset(CheckListVehicleSubmit submit) throws ArgsErrorException;

    List<CheckListVehicleDTO> listAll();
}
