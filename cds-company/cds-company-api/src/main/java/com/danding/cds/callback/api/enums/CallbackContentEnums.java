package com.danding.cds.callback.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 订单查询-回执内容
 */
@Getter
@AllArgsConstructor
public enum CallbackContentEnums {

    INVENTORY_PASS("INVENTORY_PASS", "清单放行", CallbackTypeEnums.INVENTORY),
    INVENTORY_MANUAL_CHARGEBACK("INVENTORY_MANUAL_CHARGEBACK", "清单人工退单", CallbackTypeEnums.INVENTORY),
    CANCEL_AUDIT_PASS("CANCEL_AUDIT_PASS", "撤单审核通过", CallbackTypeEnums.CANCEL),
    REFUND_PASS("REFUND_PASS", "退货放行", CallbackTypeEnums.REFUND),
    REFUND_MANUAL_AUDIT("REFUND_MANUAL_AUDIT", "退货人工审核", CallbackTypeEnums.REFUND),
    ;


    private final String code;
    private final String desc;
    private final CallbackTypeEnums callbackType;

    public static CallbackContentEnums getEnums(String code) {
        for (CallbackContentEnums value : CallbackContentEnums.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
