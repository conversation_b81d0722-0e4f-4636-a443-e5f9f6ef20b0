package com.danding.cds.payChannel.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
/**
 * @Author: Raymond
 * @Date: 2020/8/21 11:20
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountSearch extends Page {

    @ApiModelProperty("商户编码")
    private String sn;


    @ApiModelProperty("商户名称")
    private String name;


    @ApiModelProperty("收款企业")
    private String companyName;
}
