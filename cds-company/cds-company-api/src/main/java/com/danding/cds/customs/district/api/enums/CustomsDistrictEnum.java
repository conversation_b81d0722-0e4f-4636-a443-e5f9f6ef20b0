package com.danding.cds.customs.district.api.enums;

import com.danding.cds.company.api.dto.CompanySubmitDistrict;

import java.util.Comparator;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 15:15
 * @Description: 口岸枚举
 */
public enum CustomsDistrictEnum {
    NULL("", "", "", "空"),
    JINYI("zhejiang", "2924", "JINYI", "金义"),
    YIWU("zhejiang", "2925", "YIWU", "义乌"),
    CHONGQING("chongqing", "8013", "CHONG<PERSON><PERSON>", "重庆"),
    GUANGZHOU_NS("guangzhou", "5165", "GUANGZHOU_NS", "广州南沙"),
    GUANGZHOU_HP("guangzhou", "5219", "GUANGZHOU_HP", "广州黄埔"),
    TIANJIN("tianjin", "0215", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "天津"),
    SHANGHAI("shanghai", "2232", "SHANGHAI", "上海"),
    HAIKOU("haikou", "6409", "HAIKOU", "海口"),
    HANGZHOU("hangzhou", "2900", "HANGZHOU", "杭州"),
    KUNMING("kunming", "8634", "KUNMING", "昆明"),
    QINGDAO_JIMO("qingdao_jimo", "4260", "QINGDAO_JIMO", "青岛即墨"),
    YANTAI("yantai", "4261", "YANTAI", "烟台"),
    WEIHAI("weihai", "4262", "WEIHAI", "威海"),
    HU_ZHOU("huzhou", "2979", "HUZHOU", "湖州保B"),
    QINGDAO_HETAO("qingdao_hetao", "4272", "QINGDAO_HETAO", "青岛河套"),
    SHAOXING("shaoxing", "2964", "SHAOXING", "绍兴"),
    HANGZHOU_XIASHA("xiasha", "2991", "XIASHA", "杭州下沙"),
    DEQING("deqing", "2973", "DEQING", "德清B保"),
    YANCHENG("yancheng", "2372", "YANCHENG", "盐城");

    /**
     * 所属海关[关区] 浙江海关||重庆海关||天津海关
     *
     * @link: CustomsType
     */
    private String customs;

    /**
     * 申报海关代码[口岸][保税区] 杭州关区||金义综保
     * PS:这里杭州关区只是海关那边叫法有点混乱，杭州关区即可以表示口岸，也可指代浙江海关
     */
    private String portCode;

    /**
     * 系统内编码
     */
    private String code;

    /**
     * 描述
     */
    private String desc;

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getCustoms() {
        return customs;
    }

    public String getPortCode() {
        return portCode;
    }

    /**
     * [保税仓]
     * 这个监管场所代码，有点问题后面要改的
     */
    public String getLoctNo() {
        return portCode;
    }

    CustomsDistrictEnum(String customs, String portCode, String code, String desc) {
        this.customs = customs;
        this.portCode = portCode;
        this.code = code;
        this.desc = desc;
    }

    public static CustomsDistrictEnum getEnum(String code){
        for (CustomsDistrictEnum value : CustomsDistrictEnum.values()) {
            if (value.code.equals(code)){
                return value;
            }
        }
        return NULL;
    }

    public static CustomsDistrictEnum getEnumByName(String name) {
        for (CustomsDistrictEnum value : CustomsDistrictEnum.values()) {
            if (value.desc.equals(name)) {
                return value;
            }
        }
        return NULL;
    }

    public  static  Boolean checkCustomCompanyDistrictRepeat(List<CompanySubmitDistrict> orderList) {
        Set<CompanySubmitDistrict> set = new TreeSet<>(new Comparator<CompanySubmitDistrict>() {
            public int compare(CompanySubmitDistrict a, CompanySubmitDistrict b) {
                // 字符串则按照asicc码升序排列
                return a.getCustoms().compareTo(b.getCustoms());
            }
        });
        set.addAll(orderList);
        if (set.size() < orderList.size()) {
            return true;
        }
        return false;
    }
}
