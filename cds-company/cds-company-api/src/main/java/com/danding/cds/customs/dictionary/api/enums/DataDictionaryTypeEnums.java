package com.danding.cds.customs.dictionary.api.enums;

public enum DataDictionaryTypeEnums {
    NULL("", "空"),
    COUNTRY("country", "原产国"),
    PORT("port", "关区"),
    GUARANTEE_CUSTOMS("guaranteeCustoms", "保函关区"),
    CURRENCY("currency", "币制"),
    UOM("uom", "申报单位"),
    TYPE("type", "类型"),
    BUSINESS_TYPE("businessType", "业务类型"),
    HANDLER("handler", "处理人员(关务)"),
    DECLARE_QUEUE("declareQueue", "隔离队列"),
    GOODS_RECORD_REJECT_REASON("rejectReason", "驳回原因"),
    CUSTOMS_INVT_TYPE("customsInvtType", "清单类型"),
    SUPERVISE_MODE("superviseMode", "监管方式"),
    FREIGHT_FORWARDING_COMPANY("freightForwardingCompany", "货代公司"),
    ARRIVE_PORT("arrivePort", "到货港口/机场"),
    INTRY_WHERE_HARBOR("intryWhere_harbor", "入关地点(港口)"),
    INTRY_WHERE_AIRPORT("intryWhere_airport", "入关地点(机场)"),
    INTRY_WHERE_RAILWAY("intryWhere_railway", "入关地点(火车站)"),
    INTRY_WHERE_CARTAGE("intryWhere_cartage", "入关地点(中港车)"),
    JD_WAREHOUSE_JINYI("JD_WAREHOUSE_JINYI", "京东库房(金义)"),
    JD_WAREHOUSE_YIWU("JD_WAREHOUSE_YIWU", "京东库房(义乌)"),
    TRANSPORT_MODE("TRANSPORT_MODE", "运输方式"),
    TT_COUNTRY("ttcountry", "国家"),
    TT_CURRENCY("ttcurrency", "货币编码（英文）"),
    ROUTE_TAG("routeTag", "路径标签"),
    RECORD_RULE_TYPE("recordRuleType", "备案规则类型"),
    TO_B_OVERDUE("toBOverdue", "toB超时"),
    TO_B_OVERDUE_REASON("toBOverdueReason", "toB超时原因"),
    BIZ_DECLARE_FORM_BIZ_TYPE("bizDeclareFormBizType", "业务申报表业务类型"),
    CUSTOMS_DEC_TYPE("customsDecType", "海关报关单类型"),
    INV_ORDER_ATTACH_TYPE("invOrderAttachType", "清关单附件类型"),
    INV_ORDER_ATTACH_TYPE_ERP("invOrderAttachTypeErp", "清关单附件类型（ERP）"),
    CURRENCY_ERP("currencyErp", "币制（ERP）"),
    TEMPLATE_PURPOSE("templatePurpose", "模板用途"),
    ;
    private String value;

    private String desc;

    DataDictionaryTypeEnums(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public String getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static DataDictionaryTypeEnums getEnumByValue(String value) {
        for (DataDictionaryTypeEnums type : DataDictionaryTypeEnums.values()) {
            if (type.value.equals(value)) {
                return type;
            }
        }
        return NULL;
    }

    public static DataDictionaryTypeEnums getEnumByDesc(String desc) {
        for (DataDictionaryTypeEnums type : DataDictionaryTypeEnums.values()) {
            if (type.desc.equals(desc)) {
                return type;
            }
        }
        return NULL;
    }

}
