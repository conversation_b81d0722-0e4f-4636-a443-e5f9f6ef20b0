package com.danding.cds.payChannel.api.dto;

import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Raymond
 * @Date: 2020/8/21 15:25
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountChannelSearch extends Page {

    @ApiModelProperty("商户编码")
    private String merchantSn;


    @ApiModelProperty("商户名称")
    private String name;


    @ApiModelProperty("收款账户")
    private String recpAccount;

    @ApiModelProperty("商户id")
    private Long merchantId;
}
