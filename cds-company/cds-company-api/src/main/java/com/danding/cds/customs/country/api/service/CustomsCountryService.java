package com.danding.cds.customs.country.api.service;

import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;

import java.util.List;

/**
 * @Auther: shenfeng
 * @Date: 2020/04/28 17:28
 * @Description:
 */
public interface CustomsCountryService {
    /**
     * 根据ID查询
     */
    CustomsCountryDTO findById(Long id);

    /**
     * 根据编码查询
     */
    CustomsCountryDTO findByCode(String code);

    CustomsCountryDTO findByName(String name);

    List<CustomsCountryDTO> findByName(List<String> name);

    /**
     * 查询所有
     *
     * @return
     */
    List<CustomsCountryDTO> listAll();
}
