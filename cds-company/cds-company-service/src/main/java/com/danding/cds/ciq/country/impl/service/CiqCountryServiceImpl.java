package com.danding.cds.ciq.country.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.country.api.dto.CiqCountryDTO;
import com.danding.cds.ciq.country.api.service.CiqCountryService;
import com.danding.cds.ciq.country.impl.entity.CiqCountryDO;
import com.danding.cds.ciq.country.impl.mapper.CiqCountryMapper;
import com.danding.cds.common.utils.LongUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 检验检疫 国家
 */
@DubboService
public class CiqCountryServiceImpl implements CiqCountryService {

    @Autowired
    CiqCountryMapper ciqCountryMapper;

    @Override
    public CiqCountryDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CiqCountryDO ciqCountryDO = ciqCountryMapper.selectByPrimaryKey(id);
            return this.buildDTO(ciqCountryDO);
        }
    }

    @Override
    public CiqCountryDTO findByCode(String code) {
        CiqCountryDO condition = new CiqCountryDO();
        condition.setCode(code);
        return this.buildDTO(ciqCountryMapper.selectOne(condition));
    }

    @Override
    public List<CiqCountryDTO> listAll() {
        CiqCountryDO condition = new CiqCountryDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(ciqCountryMapper.select(condition)),CiqCountryDTO.class);
    }

    private CiqCountryDTO buildDTO(CiqCountryDO ciqCountryDO){
        if (ciqCountryDO == null){
            return null;
        }else {
            CiqCountryDTO dto = new CiqCountryDTO();
            BeanUtils.copyProperties(ciqCountryDO,dto);
            return dto;
        }
    }
}
