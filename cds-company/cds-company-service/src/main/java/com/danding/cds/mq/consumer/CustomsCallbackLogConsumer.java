package com.danding.cds.mq.consumer;


import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.CallbackLogDTO;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb624.InvtCancelReturn;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb626.InvtRefundReturn;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.clear.base.callback.CallbackType;
import com.danding.cds.declare.sdk.clear.chongqing.CQCustomsClient;
import com.danding.cds.declare.sdk.clear.tianjin.TJCustomsClient;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJCustomsClient;
import com.danding.cds.declare.sdk.enums.CustomsType;
import com.danding.cds.declare.zjport.domain.response.Response;
import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.service.CustomsCallbackLogService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
@Slf4j
@RocketMQMessageListener(
        consumerGroup = "ccs-customs-callback-log-consumer",
        topic = "ccs-customs-callback-log-topic"
)
public class CustomsCallbackLogConsumer extends MessageHandlerAfterInit {

    @Autowired
    private CustomsCallbackLogService customsCallbackLogService;

    @Value("${pdd.host:}")
    private String PDD_HOST;

    private static final String JMKEY = "qZe60QZFxuirub2ey4+7+Q==";
    private static final String DECLARE_NO = "declareNo";
    private static final String INVT_NO = "inventoryNo";
    private static final String SN = "sn";
    private static final String LOGISTICS_NO = "logisticsNo";
    private static final String CANCEL_INVT_NO = "cancel_inventoryNo";
    private static final String REFUND_INVT_NO = "refund_inventoryNo";

    @Override
    public void handle(Object message) throws RuntimeException {
        try {
            if (StringUtils.isEmpty(message)) {
                log.warn("message为空");
                return;
            }
            doHandle((CallbackLogDTO) message);
        } catch (Exception e) {
            log.error("CustomsCallbackLogConsumer-handle exception:{}", e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doHandle(CallbackLogDTO message) {
        if (StringUtils.isEmpty(message.getContent())) {
            log.error("CustomsCallbackLogConsumer content为空 直接返回");
            return;
        }
        CallbackLogDTO callbackLogDTO = message;
        callbackLogDTO.setCreateTime(new Date());
        callbackLogDTO.setDeleted(false);
        callbackLogDTO.setRetryTime(0);
        List<CallbackLogDTO> list;
        if (callbackLogDTO.getDecipher()) {
            try {
                getDecryptContent(callbackLogDTO);
            } catch (Exception e) {
                log.error("CustomsCallbackLogConsumer-getDecryptContent error ,message={}", e.getMessage(), e);
                return;
            }
        }
        try {
            list = getBusinessCode(callbackLogDTO);
        } catch (Exception e) {
            log.error("CustomsCallbackLogConsumer-getBusinessCode error , message={}", e.getMessage(), e);
            return;
        }
        customsCallbackLogService.saveLogs(list);
    }

    private void getDecryptContent(CallbackLogDTO callbackLogDTO) {
        StringBuffer contentBuffer = new StringBuffer();
        ZJCustomsClient.doHandDecipher(callbackLogDTO.getContent(), contentBuffer, JMKEY);
        String decryptedContent = contentBuffer.toString();
        log.info("回执记录test decryptedContent={}", decryptedContent);
        callbackLogDTO.setContent(decryptedContent);
    }

    private List<CallbackLogDTO> getBusinessCode(CallbackLogDTO callbackLogDTO) {
        log.info("getBusinessCode callbackLogDTO={}", JSONUtils.toJSONString(callbackLogDTO));
        List<CallbackLogDTO> list = new ArrayList<>();
        CallbackType callbackType;
        try {
            CustomsType customsType = CustomsType.getEnum(callbackLogDTO.getCustoms());
            switch (customsType) {
                case ZHE_JIANG:
                case HANG_ZHOU_DATA_CENTER:
                    callbackType = ZJCustomsClient.getCallbackType(callbackLogDTO.getContent(), callbackLogDTO.getCustomsType());
                    break;
                case TIAN_JIN:
                    callbackType = TJCustomsClient.getEventKey(callbackLogDTO.getContent());
                    break;
                case CHONG_QING:
                    callbackType = CQCustomsClient.getEventKey(callbackLogDTO.getContent());
                    break;
                default:
                    log.info("回执记录test customs没匹配的 略过");
                    return list;
            }
            log.info("回执记录test callbackType={}", JSON.toJSONString(callbackType));
            callbackLogDTO.setCallbackType(callbackType.getCode());
            list = populateBusinessCode(callbackLogDTO, list, callbackType);
        } catch (Exception e) {
            log.error("解析业务单号错误,message={}", e.getMessage(), e);
        }
        return list;
    }

    private List<CallbackLogDTO> populateBusinessCode(CallbackLogDTO callbackLogDTO, List<CallbackLogDTO> list, CallbackType callbackType) throws Exception {
        log.info("populateBusinessCode callbackLogDTO={}", JSONUtils.toJSONString(callbackLogDTO));
        switch (callbackType) {
            case ORDER:
                getMessageByContent(DECLARE_NO, callbackLogDTO, "OrderReturn", "orderNo");
                log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                list.add(callbackLogDTO);
                break;
            case TAX:
                getMessageByContent(INVT_NO, callbackLogDTO, "Tax", "TaxHeadRd", "invtNo");
                //获取租户查询单号
                getTenantQueryCode(LOGISTICS_NO, callbackLogDTO, "Tax", "TaxHeadRd", "logisticsNo");
                log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                list.add(callbackLogDTO);
                break;
            case TAXSTATUS:
                getMessageByContent(INVT_NO, callbackLogDTO, "Tax", "TaxHeadStatus", "invtNo");
                log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                list.add(callbackLogDTO);
                break;
            case INVENTORY:
                getMessageByContent(SN, callbackLogDTO, "InventoryReturn", "copNo");
                log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                list.add(callbackLogDTO);
                break;
            case SHIPMENT:
                getMessageByContent(LOGISTICS_NO, callbackLogDTO, "LogisticsReturn", "logisticsNo");
                log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                list.add(callbackLogDTO);
                break;
            case CANCEL:
                CEB624Message cancelResponse = XMLUtil.converyToJavaBean(callbackLogDTO.getContent().replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", ""), CEB624Message.class);
                for (InvtCancelReturn invtCancelReturn : cancelResponse.getInvtCancelReturn()) {
                    callbackLogDTO.setBusinessCode(invtCancelReturn.getInvtNo());
                    callbackLogDTO.setBusinessType(CANCEL_INVT_NO);
                    CallbackLogDTO logDTO = new CallbackLogDTO();
                    BeanUtils.copyProperties(callbackLogDTO, logDTO);
                    log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                    list.add(logDTO);
                }
                break;
            case REFUND:
                CEB626Message refundResponse = XMLUtil.converyToJavaBean(callbackLogDTO.getContent().replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", ""), CEB626Message.class);
                for (InvtRefundReturn invtRefundReturn : refundResponse.getInvtRefundReturn()) {
                    callbackLogDTO.setBusinessCode(invtRefundReturn.getInvtNo());
                    callbackLogDTO.setBusinessType(REFUND_INVT_NO);
                    CallbackLogDTO logDTO = new CallbackLogDTO();
                    BeanUtils.copyProperties(callbackLogDTO, logDTO);
                    log.info("回执记录test callbackLogDTO={}", JSON.toJSONString(callbackLogDTO));
                    list.add(logDTO);
                }
                break;
            case DELIVER:
                // same ↑
                break;
            case NULL:
                // same ↑
                break;
            default:
                break;
        }
        return list;
    }

    private static final String CUSTOMS_CEB_CALLBACK = "CUSTOMS_CEB_CALLBACK";

    private String getMessageByContent(String codeType, CallbackLogDTO callbackLogDTO, String... route) {
        try {
            String content = callbackLogDTO.getContent();
            String businessCode;
            if (CUSTOMS_CEB_CALLBACK.equals(callbackLogDTO.getCustomsType())) {
                content = content.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                businessCode = getBusinessCode(content, route);
            } else {
                Response response = XMLUtil.converyToJavaBean(content, Response.class);
                if (Objects.nonNull(response.getBody().getJkfSign())) {
                    businessCode = response.getBody().getJkfSign().getBusinessNo();
                } else {
                    businessCode = response.getBody().getList().get(0).getBusinessNo();
                }
            }
            log.info("getMessageByContent businessCode={} codeType={}", businessCode, codeType);
            if (Objects.nonNull(businessCode) && businessCode.startsWith("XP") && Objects.equals(codeType, "declareNo")) {
                try {
                    String pddUrl = PDD_HOST + "/xhr/order/findByRealDeclareSn";
                    log.info("getMessageByContent PDD proxy, url={}, param={}", pddUrl, businessCode);
                    // 拼多多转发至云内应用申报
                    HttpRequest httpRequest = HttpRequest.get(pddUrl, true, "realDeclareSn", businessCode).header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr());
                    if (httpRequest.ok()) {
                        String encodeStr = httpRequest.body();
                        if (!org.apache.commons.lang3.StringUtils.isEmpty(encodeStr)) {
                            log.info("getMessageByContent PDD succ, res={}", encodeStr);
                            businessCode = encodeStr;
                        }
                    } else {
                        log.error("请求pdd申报单号失败 xp单号={}", businessCode);
                    }
                } catch (Exception e) {
                    log.error("请求pdd申报单号失败 xp单号={}", businessCode);
                }
            }
            callbackLogDTO.setBusinessCode(businessCode);
            callbackLogDTO.setBusinessType(codeType);
        } catch (Exception e) {
            log.error("getMessageByContent error,message={}", e.getMessage(), e);
        }
        return null;
    }


    private void getTenantQueryCode(String codeType, CallbackLogDTO callbackLogDTO, String... route) {
        try {
            String content = callbackLogDTO.getContent();
            String tenantQueryCode;
            if (CUSTOMS_CEB_CALLBACK.equals(callbackLogDTO.getCustomsType())) {
                content = content.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
                tenantQueryCode = getBusinessCode(content, route);
            } else {
                Response response = XMLUtil.converyToJavaBean(content, Response.class);
                if (Objects.nonNull(response.getBody().getJkfSign())) {
                    tenantQueryCode = response.getBody().getJkfSign().getBusinessNo();
                } else {
                    tenantQueryCode = response.getBody().getList().get(0).getBusinessNo();
                }
            }
            callbackLogDTO.setTenantQueryCode(tenantQueryCode);
            callbackLogDTO.setTenantQueryCodeType(codeType);
        } catch (Exception e) {
            log.error("getMessageByContent error,message={}", e.getMessage(), e);
        }
    }

    private static String getBusinessCode(String xml, String... strings) {
        Document document;
        Element ele;
        try {
            document = DocumentHelper.parseText(xml);
            Element rootElement = document.getRootElement();
            int length = strings.length;
            ele = rootElement;
            // 根据路径找出指定元素
            for (int i = 0; i < length; i++) {
                ele = ele.element(strings[i]);
            }
            if (ele != null) {
                log.info("getBusinessCode element-code={}", ele.getText());
                return ele.getText();
            }
        } catch (DocumentException e) {
            log.error("getBusinessCode element-error,messge={}", e.getMessage(), e);
        }
        return null;
    }
}
