package com.danding.cds.mq.consumer;

import com.danding.cds.mq.handler.MessageHandlerAfterInit;
import com.danding.cds.service.CustomsCallbackLogService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-customs-callback-log-retry-consumer",
        topic = "ccs-customs-callback-log-retry-topic"
)
public class CustomsCallbackLogRetryMQConsumer extends MessageHandlerAfterInit {

    @DubboReference
    CustomsCallbackLogService customsCallbackLogService;

    @Override
    public void handle(Object message) throws RuntimeException {
        if (Objects.isNull(message)) {
            log.warn("message 为空");
            return;
        }
        Long id = (Long) message;
        try {
            customsCallbackLogService.retryCallback(id);
        } catch (ArgsErrorException ex) {
            log.info("[op:CustomsCallbackLogRetryMQConsumer] exception, id={}, cause={}", id, ex.getErrorMessage(), ex);
            throw ex;
        } catch (Exception e) {
            log.info("[op:CustomsCallbackLogRetryMQConsumer] exception, id={}, cause={}", id, e.getMessage(), e);
            throw e;
        }
    }

}
