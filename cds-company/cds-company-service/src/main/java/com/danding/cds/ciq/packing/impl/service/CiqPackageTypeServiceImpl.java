package com.danding.cds.ciq.packing.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.packageType.api.dto.CiqPackageTypeDTO;
import com.danding.cds.ciq.packageType.api.service.CiqPackageTypeService;
import com.danding.cds.ciq.packing.impl.entity.CiqPackingDO;
import com.danding.cds.ciq.packing.impl.mapper.CiqPackageTypeMapper;
import com.danding.cds.common.utils.LongUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 检验检疫 包装类型
 */
@DubboService
public class CiqPackageTypeServiceImpl implements CiqPackageTypeService {

    @Autowired
    CiqPackageTypeMapper ciqPackageTypeMapper;

    @Override
    public CiqPackageTypeDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CiqPackingDO ciqPackingDO = ciqPackageTypeMapper.selectByPrimaryKey(id);
            return this.buildDTO(ciqPackingDO);
        }
    }

    @Override
    public CiqPackageTypeDTO findByCode(String code) {
        return null;
    }

    @Override
    public List<CiqPackageTypeDTO> listAll() {
        CiqPackingDO condition = new CiqPackingDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(ciqPackageTypeMapper.select(condition)),CiqPackageTypeDTO.class);
    }

    private CiqPackageTypeDTO buildDTO(CiqPackingDO entity){
        if (entity == null) {
            return null;
        }else {
            CiqPackageTypeDTO dto = new CiqPackageTypeDTO();
            BeanUtils.copyProperties(entity,dto);
            return dto;
        }
    }
}
