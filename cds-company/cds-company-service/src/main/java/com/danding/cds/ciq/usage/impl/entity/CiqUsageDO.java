package com.danding.cds.ciq.usage.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 检验检疫 用途表(CiqUsageDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-29 13:21
 */
@Data
@Table(name = "ccs_ciq_usage")
public class CiqUsageDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -6416362018497322661L;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称
     */
    private String name;
    /**
     * 创建时间
     */
    private Timestamp createTime;
    /**
     * 更新时间
     */
    private Timestamp updateTime;
}
