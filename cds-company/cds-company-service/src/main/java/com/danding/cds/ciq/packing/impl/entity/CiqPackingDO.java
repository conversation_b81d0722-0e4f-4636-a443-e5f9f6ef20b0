package com.danding.cds.ciq.packing.impl.entity;


import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 检验检疫 包装类型表(CiqPackageTypeDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-29 09:56
 */
@Data
@Table(name = "ccs_ciq_packing")
public class CiqPackingDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -1339149290737979980L;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称
     */
    private String name;
}
