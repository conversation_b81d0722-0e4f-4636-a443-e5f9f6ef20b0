package com.danding.cds.ciq.uom.impl.entity;


import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 检验检疫 计量单位表(CiqUomDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-29 13:21
 */
@Data
@Table(name = "ccs_ciq_uom")
public class CiqUomDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 3366065458882224158L;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称
     */
    private String name;
}
