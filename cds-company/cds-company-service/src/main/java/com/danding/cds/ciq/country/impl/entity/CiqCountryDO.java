package com.danding.cds.ciq.country.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;
import java.io.Serializable;

/**
 * 检验检疫 国家表(CiqCountryDO)实体类
 *
 * <AUTHOR>
 * @since 2020-04-29 11:12
 */
@Data
@Table(name = "ccs_ciq_country")
public class CiqCountryDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = 4468909102824073060L;

    /**
     * 编号
     */
    private String code;
    /**
     * 名称
     */
    private String name;
}
