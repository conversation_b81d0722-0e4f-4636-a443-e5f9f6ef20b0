package com.danding.cds.es;

import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: ccs内部编码租户映射
 * @date 2022/12/12 09:31
 */
@Data
@Document(indexName = "ccs_order_sn_tenant_mapping", type = "sn")
public class OrderSnEsDO extends EsModel implements Serializable {
    @Field(type = FieldType.Keyword, store = true)
    private String sn;

    @Field(type = FieldType.Keyword, store = true)
    private String preNo;

    @Field(type = FieldType.Keyword, store = true)
    private String realOrderNo;

    @Field(type = FieldType.Long, store = true)
    private Long tenantId;
}
