package com.danding.cds.mq.producer;

import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.Serializable;

@Slf4j
@Component
@RestController
public class CustomsCallbackLogRetryMQProducer implements Serializable {

    @Autowired
    private MessageSender messageSender;

    @RequestMapping("/customsCallbackLogRetryMQProducer")
    public void send(Long id) {
        try {
            messageSender.sendMsg(id, "ccs-customs-callback-log-retry-topic");
        } catch (Exception e) {
            log.error("[op:CustomsCallbackLogRetryMQProducer] sendMeg exception, cause={}", e.getMessage(), e);
        }
    }

}
