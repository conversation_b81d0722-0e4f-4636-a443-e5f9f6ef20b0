package com.danding.cds.ciq.uom.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.uom.api.dto.CiqUomDTO;
import com.danding.cds.ciq.uom.api.serivce.CiqUomService;
import com.danding.cds.ciq.uom.impl.entity.CiqUomDO;
import com.danding.cds.ciq.uom.impl.mapper.CiqUomMapper;
import com.danding.cds.common.utils.LongUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 检验检疫 单位
 */
@DubboService
public class CiqUomServiceImpl implements CiqUomService {

    @Autowired
    CiqUomMapper ciqUomMapper;
    @Override
    public CiqUomDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CiqUomDO ciqUomDO = ciqUomMapper.selectByPrimaryKey(id);
            return this.buildDTO(ciqUomDO);
        }
    }

    @Override
    public CiqUomDTO findByCode(String code) {
        CiqUomDO condition = new CiqUomDO();
        condition.setCode(code);
        return this.buildDTO(ciqUomMapper.selectOne(condition));
    }

    @Override
    public List<CiqUomDTO> listAll() {
        CiqUomDO condition = new CiqUomDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(ciqUomMapper.select(condition)),CiqUomDTO.class);
    }


    private CiqUomDTO buildDTO(CiqUomDO entity){
        if (entity == null){
            return null;
        }else {
            return JSON.parseObject(JSON.toJSONString(entity),CiqUomDTO.class);
        }
    }
}
