package com.danding.cds.ciq.usage.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.usage.api.dto.CiqUsageDTO;
import com.danding.cds.ciq.usage.api.service.CiqUsageService;
import com.danding.cds.ciq.usage.impl.entity.CiqUsageDO;
import com.danding.cds.ciq.usage.impl.mapper.CiqUsageMapper;
import com.danding.cds.common.utils.LongUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 检验检疫用途
 */
@DubboService
public class CiqUsageServiceImpl implements CiqUsageService {

    @Autowired
    CiqUsageMapper ciqUsageMapper;

    @Override
    public CiqUsageDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CiqUsageDO ciqUsageDO=ciqUsageMapper.selectByPrimaryKey(id);
            return this.buildDTO(ciqUsageDO);
        }
    }

    @Override
    public CiqUsageDTO findByCode(String code) {
        CiqUsageDO condition = new CiqUsageDO();
        condition.setCode(code);
        return this.buildDTO(ciqUsageMapper.selectOne(condition));
    }

    @Override
    public List<CiqUsageDTO> listAll() {
        CiqUsageDO condition = new CiqUsageDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(ciqUsageMapper.select(condition)),CiqUsageDTO.class);
    }

    private CiqUsageDTO buildDTO(CiqUsageDO entity){
        if (entity == null){
            return null;
        }else {
            return JSON.parseObject(JSON.toJSONString(entity),CiqUsageDTO.class);
        }
    }
}
