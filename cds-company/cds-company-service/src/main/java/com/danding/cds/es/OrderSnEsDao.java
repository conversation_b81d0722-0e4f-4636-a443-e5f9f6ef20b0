package com.danding.cds.es;

import com.danding.common.es.annotations.EsDao;
import com.danding.common.es.dao.AbstractEsDao;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/12/12 09:45
 */
@EsDao
@Slf4j
@Repository
@RefreshScope
public class OrderSnEsDao extends AbstractEsDao<OrderSnEsDO> {
    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    @Value("${saas.defaultFlag:false}")
    private boolean defaultFlag;

    @Value("${saas.defaultValue:1001}")
    private Long defaultValue;

    public void savePreNo(String sn, String preNo) throws Exception {
        log.info("savePreNo sn={} pre={}", sn, preNo);
        UpdateByQueryRequest request = new UpdateByQueryRequest("ccs_order_sn_tenant_mapping");
        request.setQuery(new TermQueryBuilder("sn", sn));
        String script = "ctx._source['preNo']= params.preNo;ctx._source['updateTime']= params.updateTime";
        Map<String, Object> params = new HashMap<>();
        params.put("preNo", preNo);
        params.put("updateTime", new Date().getTime());
        request.setScript(new Script(ScriptType.INLINE, "painless", script, params));
        request.setRefresh(true);
        BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
        long total = response.getTotal();
        log.info("savePreNo sn={} pre={} total={}", sn, preNo, total);
    }

    public void saveRealNo(String sn, String realOrderNo) throws Exception {
        log.info("savePreNo sn={} realOrderNo={}", sn, realOrderNo);
        UpdateByQueryRequest request = new UpdateByQueryRequest("ccs_order_sn_tenant_mapping");
        request.setQuery(new TermQueryBuilder("sn", sn));
        String script = "ctx._source['realOrderNo']=params.realOrderNo;ctx._source['updateTime']= params.updateTime";
        Map<String, Object> params = new HashMap<>();
        params.put("realOrderNo", realOrderNo);
        params.put("updateTime", new Date().getTime());
        request.setScript(new Script(ScriptType.INLINE, "painless", script, params));
        request.setRefresh(true);
        BulkByScrollResponse response = restHighLevelClient.updateByQuery(request, RequestOptions.DEFAULT);
        long total = response.getTotal();
        log.info("savePreNo sn={} realOrderNo={} total={}", sn, realOrderNo, total);
    }

    public void saveMapping(String sn, Long tenantId) {
        if (Objects.isNull(sn) || Objects.isNull(tenantId)) {
            return;
        }
        OrderSnEsDO orderSnEsDO = new OrderSnEsDO();
        orderSnEsDO.setId(sn);
        orderSnEsDO.setSn(sn);
        orderSnEsDO.setTenantId(tenantId);
        orderSnEsDO.setCreateTime(new Date().getTime());
        orderSnEsDO.setUpdateTime(new Date().getTime());
        this.save(orderSnEsDO);
    }


    /**
     * 根据sn获取租户id
     *
     * @param sn
     * @return
     */
    public Long getTenantIdBySn(String sn) {
        log.info("OrderSnEsDao getTenantIdBySn sn={}", sn);
        if (Objects.isNull(sn)) {
            return null;
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("sn", sn));
        // 创建时间倒序排
        Long tenantId = getResult(boolQueryBuilder);
        log.info("OrderSnEsDao getTenantIdBySn sn={} tenantId={}", sn, tenantId);
        return tenantId;
    }

    public Long getTenantIdByPreNo(String preNo) {
        log.info("OrderSnEsDao getTenantIdByPreNo preNo={}", preNo);
        if (Objects.isNull(preNo)) {
            return null;
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("preNo", preNo));
        // 创建时间倒序排
        Long tenantId = getResult(boolQueryBuilder);
        log.info("OrderSnEsDao getTenantIdByPreNo preNo={} tenantId={}", preNo, tenantId);
        return tenantId;
    }

    private Long getResult(BoolQueryBuilder boolQueryBuilder) {
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(0, 1)).build();
        Page<OrderSnEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, OrderSnEsDO.class);
        if (page.hasContent()) {
            List<OrderSnEsDO> content = page.getContent();
            if (!CollectionUtils.isEmpty(content)) {
                return content.get(0).getTenantId();
            }
        } else if (defaultFlag) {
            log.info("OrderSnEsDao getResult为空 取defaultValue={}", defaultValue);
            return defaultValue;
        }
        return null;
    }

    public Long getTenantIdByRealNo(String realOrderNo) {
        log.info("OrderSnEsDao getTenantIdByRealNo realOrderNo={}", realOrderNo);
        if (Objects.isNull(realOrderNo)) {
            return null;
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("realOrderNo", realOrderNo));
        // 创建时间倒序排
        Long tenantId = getResult(boolQueryBuilder);
        log.info("OrderSnEsDao getTenantIdByRealNo realOrderNo={} tenantId={}", realOrderNo, tenantId);
        return tenantId;
    }
}
