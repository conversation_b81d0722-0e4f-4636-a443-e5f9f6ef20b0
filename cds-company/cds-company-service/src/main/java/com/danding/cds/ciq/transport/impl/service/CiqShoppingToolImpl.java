package com.danding.cds.ciq.transport.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.shoppingTool.api.dto.CiqShoppingToolDTO;
import com.danding.cds.ciq.shoppingTool.api.service.CiqShoppingToolService;
import com.danding.cds.ciq.transport.impl.entity.CiqTransportDO;
import com.danding.cds.ciq.transport.impl.mapper.CiqShoppingToolMapper;
import com.danding.cds.common.utils.LongUtil;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * 检验检疫 运输方式
 */
@DubboService
public class CiqShoppingToolImpl implements CiqShoppingToolService {
    @Autowired
    CiqShoppingToolMapper ciqShoppingToolMapper;
    @Override
    public CiqShoppingToolDTO findById(Long id) {
        if(LongUtil.isNone(id)){
            return null;
        }else{
            CiqTransportDO ciqTransportDO = ciqShoppingToolMapper.selectByPrimaryKey(id);
            return this.buildDTO(ciqTransportDO);
        }
    }

    @Override
    public CiqShoppingToolDTO findByCode(String code) {
        CiqTransportDO condition =  new CiqTransportDO();
        condition.setCode(code);
        return this.buildDTO(ciqShoppingToolMapper.selectOne(condition));
    }

    @Override
    public List<CiqShoppingToolDTO> listAll() {
        CiqTransportDO condition =  new CiqTransportDO();
        condition.setDeleted(false);
        return JSON.parseArray(JSON.toJSONString(ciqShoppingToolMapper.select(condition)),CiqShoppingToolDTO.class);
    }

    private CiqShoppingToolDTO buildDTO(CiqTransportDO entity){
        if (entity == null){
            return null;
        }else {
            return JSON.parseObject(JSON.toJSONString(entity),CiqShoppingToolDTO.class);
        }
    }
}
