package com.danding.cds.thirdsys;


import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

@Component
public class WmsSysRegister {
    private HashMap<String, ThirdSys> sysHashMap = Maps.newHashMap();

    public boolean regSys(String sysName, ThirdSys thirdSys) {
        if(findSys(sysName) != null) {
            throw new IllegalArgumentException("wms系统"+sysName+"已注册");
        }
        sysHashMap.put(sysName, thirdSys);
        return true;
    }

    public ThirdSys findSys(String sysName) {
        return sysHashMap.get(sysName);
    }

    public Set<String> getsRegistered(){
        return new HashSet<>(sysHashMap.keySet());
    }
}
