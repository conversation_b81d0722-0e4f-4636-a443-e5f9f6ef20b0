package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class KeyInfo implements Serializable {
    private static final long serialVersionUID = -5456574045910752539L;

    @XmlElement(name = "ds:KeyName")
    private String keyName;

    @XmlElement(name = "ds:X509Data")
    private X509Data x509Data;
}
