package com.danding.cds.thirdsys;

import java.util.HashMap;

public abstract class AbstractThirdSys implements ThirdSys {

    private HashMap<String, ThirdModule> moduleHashMap = new HashMap<>();

    @Override
    public abstract void configure();

    @Override
    public boolean regMogule(String name, ThirdModule thirdModule) {
        if(moduleHashMap.get(name) != null) {
            throw new IllegalArgumentException("模块"+name+"已注册");
        }
        moduleHashMap.put(name, thirdModule);
        return true;
    }

    @Override
    public boolean supportModule(String name) {
        return moduleHashMap.containsKey(name);
    }

    @Override
    public ThirdModule getModule(String name) {
        return moduleHashMap.get(name);
    }

    @Override
    public String getName() {
        return "third";
    }
}
