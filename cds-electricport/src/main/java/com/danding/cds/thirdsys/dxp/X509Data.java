package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class X509Data implements Serializable {
    private static final long serialVersionUID = 3981750679600186616L;

    @XmlElement(name = "ds:X509Certificate")
    private byte[] x509Certificate;
}
