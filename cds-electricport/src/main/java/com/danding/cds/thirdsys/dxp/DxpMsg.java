package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.io.Serializable;

@Data
@XmlAccessorType(value = XmlAccessType.FIELD)
@XmlRootElement(name = "DxpMsg")
public class DxpMsg implements Serializable {

    private static final long serialVersionUID = -4943517261465973757L;

    @XmlAttribute(name = "ver")
    private String ver;

    @XmlAttribute(name = "TransInfo")
    private TransInfo transInfo;

    @XmlElement(name = "Data")
    private String data;

    @XmlElement(name = "Signature")
    private Signature signature;
}
