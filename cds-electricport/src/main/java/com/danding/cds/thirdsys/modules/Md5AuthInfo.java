package com.danding.cds.thirdsys.modules;

import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import lombok.Data;

import java.util.Arrays;
import java.util.Map;
import java.util.Set;

@Data
public class Md5AuthInfo implements ThirdAuthInfo {

    private String appKey;

    private String appSecret;

    public String sign(Map<String, String> params) {
        String sign = genSign(params);
        return sign;
    }

    protected String genSign(Map<String, String> params) {
        try {
            Set<String> keySet = params.keySet();
            String[] keyArray = keySet.toArray(new String[keySet.size()]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            for (String k : keyArray) {
                if (k.equals("sign")) {
                    continue;
                }
                if (params.get(k).toString().trim().length() > 0) {
                    sb.append(k).append("=").append(params.get(k).toString().trim()).append("&");
                }
            }
            String s = sb.substring(0, sb.length() - 1) + appSecret;
            String sign = Hashing.sha1().newHasher().putString(s, Charsets.UTF_8).hash().toString().toUpperCase();
            return sign;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }
}
