package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class ReceiverIds implements Serializable {
    private static final long serialVersionUID = 6238683750158590503L;

    @XmlElement(name = "ReceiverId")
    private String receiverId;
}
