package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class SignedInfo implements Serializable {
    private static final long serialVersionUID = 6969185464650757067L;

    @XmlElement(name = "ds:CanonicalizationMethod")
    private CanonicalizationMethod canonicalizationMethod;

    @XmlElement(name = "ds:SignatureMethod")
    private SignatureMethod signatureMethod;

    @XmlElement(name = "ds:Reference")
    private Reference reference;
}
