package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Reference implements Serializable {
    private static final long serialVersionUID = 6877093373645216488L;

    @XmlElement(name = "ds:Transforms")
    private Transforms transforms;

    @XmlElement(name = "ds:DigestMethod")
    private DigestMethod digestMethod;

    @XmlElement(name = "ds:DigestValue")
    private String digestValue;
}
