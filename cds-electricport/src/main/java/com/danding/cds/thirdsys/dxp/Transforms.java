package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Transforms implements Serializable {
    private static final long serialVersionUID = -2958925889496516561L;
    @XmlAttribute(name = "ds:Algorithm")
    private String algorithm;
}
