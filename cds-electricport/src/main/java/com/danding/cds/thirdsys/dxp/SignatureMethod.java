package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class SignatureMethod implements Serializable {
    private static final long serialVersionUID = -3791180998589440758L;

    @XmlAttribute(name = "ds:Algorithm")
    private String algorithm;
}
