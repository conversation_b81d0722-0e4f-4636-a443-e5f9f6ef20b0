package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlAttribute;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class Signature implements Serializable {
    private static final long serialVersionUID = -1876506034042714462L;

    @XmlAttribute(name = "xmlns:ds")
    private String xmlnsXsi = "http://www.w3.org/2000/09/xmldsig#";

    @XmlElement(name = "ds:SignedInfo")
    private SignedInfo signedInfo;

    @XmlElement(name = "ds:SignatureValue")
    private byte[] signatureValue;

    @XmlElement(name = "ds:KeyInfo")
    private KeyInfo keyInfo;
}
