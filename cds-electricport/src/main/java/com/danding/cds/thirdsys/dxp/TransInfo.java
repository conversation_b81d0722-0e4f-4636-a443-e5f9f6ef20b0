package com.danding.cds.thirdsys.dxp;

import lombok.Data;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
public class TransInfo implements Serializable {

    private static final long serialVersionUID = -5199499516612749728L;

    @XmlElement(name = "CopMsgId")
    private String copMsgId;

    @XmlElement(name = "SenderId")
    private String senderId;

    @XmlElement(name = "ReceiverIds")
    private List<ReceiverIds> receiverIds;

    @XmlElement(name = "CreatTime")
    private Date creatTime;

    @XmlElement(name = "MsgType")
    private String msgType;
}
