package com.danding.cds.download.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 清关单表体导出EXCEL
 * @date 2022/4/11 14:04
 */
@Data
public class InventoryOrderItemExportVo implements Serializable {
    @Excel(name = "报关单商品序号")
    private String declareCustomsGoodsSeqNo;
    /**
     * 是否是新的(old:旧,new:新)
     */
    @Excel(name = "是否新品")
    private String isNew;
    @Excel(name = "商品sku")
    private String skuId;
    @Excel(name = "统一料号")
    private String originProductId;
    @Excel(name = "海关备案料号")
    private String productId;
    @Excel(name = "备案序号")
    private String goodsSeqNo;
    @Excel(name = "商品名称")
    private String goodsName;
    @Excel(name = "申报表序号")
    private Integer declareFormItemSeqNo;
    @Excel(name = "HS编码")
    private String hsCode;
    @Excel(name = "规格型号")
    private String goodsModel;
    @Excel(name = "申报计量单位代码")
    private String unit;
    @Excel(name = "申报数量")
    private BigDecimal declareUnitQfy;
    @Excel(name = "法定计量单位代码")
    private String firstUnit;
    @Excel(name = "法定数量(单)")
    private BigDecimal firstUnitQfy;
    @Excel(name = "法定数量(总)")
    private BigDecimal firstUnitQfyTotal;
    @Excel(name = "第二法定计量单位代码")
    private String secondUnit;
    @Excel(name = "第二法定数量(单)")
    private BigDecimal secondUnitQfy;
    @Excel(name = "第二法定数量(总)")
    private BigDecimal secondUnitQfyTotal;
    //    @Excel(name = "毛重(KG)")
//    private BigDecimal grossWeight;
//    @Excel(name = "净重(KG)")
//    private BigDecimal netweight;
    @Excel(name = "总毛重(KG)")
    private BigDecimal totalGrossWeight;
    @Excel(name = "总净重(KG)")
    private BigDecimal totalNetWeight;
    @Excel(name = "申报单价")
    private BigDecimal declarePrice;
    @Excel(name = "申报总价")
    private BigDecimal declareTotalPrice;
    @Excel(name = "原产国代码")
    private String originCountry;
    @Excel(name = "最终目的国(地区)")
    private String destinationCountry;
    @Excel(name = "商品条码")
    private String goodsBar;
    @Excel(name = "币制代码")
    private String currency;
    @Excel(name = "关联一线入境报关单号")
    private String customsEntryNo;
    @Excel(name = "免征方式")
    private String avoidTaxMethod;
    @Excel(name = "危化品标志")
    private String dangerousFlag;
}
