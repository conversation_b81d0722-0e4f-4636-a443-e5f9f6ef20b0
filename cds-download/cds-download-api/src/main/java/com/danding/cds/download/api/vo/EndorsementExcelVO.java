package com.danding.cds.download.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class EndorsementExcelVO implements Serializable {
    @Excel(name = "序号")
    private String lineNo;
    @Excel(name = "备案序号")
    private String goodsSeqNo;
    @Excel(name = "商品料号")
    private String productId;
    @Excel(name = "报关单商品序号")
    private String orderSeqNo;
    @Excel(name = "流转申报表序号")
    private String lineSeqNo;
    @Excel(name = "商品编码")
    private String hsCode;//HS编码
    @Excel(name = "商品名称")
    private String goodsName;//商品名称
    @Excel(name = "规格型号")
    private String goodsModel;
    @Excel(name = "币制")
    private String currCode;
    @Excel(name = "申报计量单位代码")
    private String goodsUnit;
    @Excel(name = "法定计量单位代码")
    private String firstUnit;
    @Excel(name = "法定第二计量单位代码")
    private String secondUnit;
    @Excel(name = "申报数量")
    private Integer totalCount;
    @Excel(name = "法定数量")
    private BigDecimal firstUnitAmount;
    @Excel(name = "第二法定数量")
    private BigDecimal secondUnitAmount;
    @Excel(name = "企业申报单价")
    private BigDecimal unitPrice;
    @Excel(name = "企业申报总价")
    private BigDecimal totalAmount;
    @Excel(name = "原产国(地区）")
    private String originCountry;
    @Excel(name = "最终目的国（地区）")
    private String aimCountry = "142";
    @Excel(name = "重量比例因子")
    private String weightModule;
    @Excel(name = "第一比例因子")
    private String firstModule;
    @Excel(name = "第二比例因子")
    private String secondModule;
    @Excel(name = "毛重")
    private BigDecimal grossWeight;
    @Excel(name = "净重")
    private BigDecimal netWeight;
    // TODO:根据HS判断,一般是3
    @Excel(name = "征免方式")
    private String freeType = "3";
    @Excel(name = "单耗版本号")
    private String version;
    @Excel(name = "危化品标志")
    private String dangerousFlag;
    @Excel(name = "备注")
    private String note;
    @Excel(name = "来源标识")
    private String goodsSource;
}
