package com.danding.cds.download.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.io.Serializable;

@Data
@ApiModel
public class ExportItemExcelVO implements Serializable {

    /**
     * 申报单号
     */
    @Excel(name = "申报单号")
    private String declareOrderNo;
    /**
     * 运单号
     */
    @Excel(name = "运单号")
    private String mailNo;

    /**
     * 清单编号
     */
    @Excel(name = "清单编号")
    private String inventoryNo;

    /**
     * 账册编号
     */
    @Excel(name = "账册编号")
    private String accountBookNo;

    /**
     * 快递公司
     */
    @Excel(name = "快递名称")
    private String expressName;
}
