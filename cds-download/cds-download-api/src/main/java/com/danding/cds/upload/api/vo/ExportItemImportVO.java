package com.danding.cds.upload.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class ExportItemImportVO implements Serializable {

    @Excel(name = "*快递名称")
    @ExcelProperty(value = "*快递名称")
    private String expressName;

    @Excel(name = "*运单号")
    @ExcelProperty(value = "*运单号")
    private String mailNo;
}
