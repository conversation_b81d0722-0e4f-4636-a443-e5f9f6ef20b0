package com.danding.cds.download.api.enums;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public class ReportType extends Enumerable4StringValue {
    private static volatile transient Map<String, ReportType> allbyvalue = new HashMap<String, ReportType>();
    private static volatile transient Map<String, ReportType> allbyname = new HashMap<String, ReportType>();
    private static final Lock lock = new ReentrantLock();

    public static ReportType TAXES_COMPANY_FOR_EXCEL = ReportType.valueOf("TAXES_COMPANY_FOR_EXCEL", "企业税金账户");
    public static ReportType TAXES_COMPANY_RECHARGE_FOR_EXCEL = ReportType.valueOf("TAXES_COMPANY_RECHARGE_FOR_EXCEL", "企业税金账户充值记录");
    public static ReportType TAXES_COMPANY_TAX_FOR_EXCEL = ReportType.valueOf("TAXES_COMPANY_TAX_FOR_EXCEL", "企业税金账户电子税单");

    public static ReportType TAXES_TENANT_FOR_EXCEL = ReportType.valueOf("TAXES_TENANT_FOR_EXCEL", "租户税金账户");
    public static ReportType TAXES_TENANT_RECHARGE_FOR_EXCEL = ReportType.valueOf("TAXES_TENANT_RECHARGE_FOR_EXCEL", "租户税金账户充值记录");
    public static ReportType TAXES_TENANT_TAX_FOR_EXCEL = ReportType.valueOf("TAXES_TENANT_TAX_FOR_EXCEL", "租户账户税金电子税单");

    public static ReportType CUSTOMS_HS_CODE_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_HS_CODE_LIST_FOR_EXCEL", "HS编码");
    public static ReportType EXPORT_ORDER_FOR_EXCEL = ReportType.valueOf("EXPORT_ORDER_FOR_EXCEL", "申报出库单");
    public static ReportType EXPORT_ORDER_ITEM_FOR_EXCEL = ReportType.valueOf("EXPORT_ORDER_ITEM_FOR_EXCEL", "申报出库单表体导出");
    public static ReportType ENDORSEMENT_FOR_EXCEL = ReportType.valueOf("ENDORSEMENT_FOR_EXCEL", "核注清单");
    public static ReportType CHECKLIST_ITEM_FOR_EXCEL = ReportType.valueOf("CHECKLIST_ITEM_FOR_EXCEL", "核放单表体");
    public static ReportType INVENTORY_ITEM_FOR_EXCEL = ReportType.valueOf("INVENTORY_ITEM_FOR_EXCEL", "清关单表体");
    public static ReportType INVENTORY_ITEM_GOODS_FOR_EXCEL = ReportType.valueOf("INVENTORY_ITEM_GOODS_FOR_EXCEL", "清关单料件表体");
    public static ReportType INVENTORY_FOR_EXCEL = ReportType.valueOf("INVENTORY_FOR_EXCEL", "清关单");

    public static ReportType BUSINESS_CENTER_CUSTOMS_COMPLETE_FOR_EXCEL = ReportType.valueOf("BUSINESS_CENTER_CUSTOMS_COMPLETE_FOR_EXCEL", "业务中台-清关完成数");

    public static ReportType GOODS_RECORD_FOR_EXCEL = ReportType.valueOf("GOODS_RECORD_FOR_EXCEL", "商品备案");

    public static ReportType BUSINESS_CENTER_GOODS_RECORD_FOR_EXCEL = ReportType.valueOf("BUSINESS_CENTER_GOODS_RECORD_FOR_EXCEL", "业务中台-商品备案");
    public static ReportType JD_GOODS_RECORD_FOR_EXCEL = ReportType.valueOf("JD_GOODS_RECORD_FOR_EXCEL", "京东商品备案");
    public static ReportType CUSTOMS_BOOK_ITEM_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_BOOK_ITEM_LIST_FOR_EXCEL", "账册库存");
    public static ReportType CUSTOMS_BOOK_ITEM_CHANGE_LOG_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_BOOK_ITEM_CHANGE_LOG_LIST_FOR_EXCEL", "账册库存变动明细");
    public static ReportType CUSTOMS_DECLARE_ORDER_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_DECLARE_ORDER_LIST_FOR_EXCEL", "申报单");
    public static ReportType CUSTOMS_INVENTORY_ORDER_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_INVENTORY_ORDER_LIST_FOR_EXCEL", "海关清单");

    public static ReportType CUSTOMS_INVENTORY_CALLOFF_FOR_EXCEL = ReportType.valueOf("CUSTOMS_INVENTORY_CALLOFF_FOR_EXCEL", "取消单");
    public static ReportType CUSTOMS_INVENTORY_CANCEL_FOR_EXCEL = ReportType.valueOf("CUSTOMS_INVENTORY_CANCEL_FOR_EXCEL", "清单撤单");
    public static ReportType CUSTOMS_INVENTORY_REFUND_FOR_EXCEL = ReportType.valueOf("CUSTOMS_INVENTORY_REFUND_FOR_EXCEL", "清单退货");
    public static ReportType CUSTOMS_ENDORSEMENT_REFUND_FOR_EXCEL = ReportType.valueOf("CUSTOMS_ENDORSEMENT_REFUND_FOR_EXCEL", "清单退货核注");

    public static ReportType BUSINESS_CENTER_BONDED_POST_SALE_FOR_EXCEL = ReportType.valueOf("BUSINESS_CENTER_BONDED_POST_SALE_FOR_EXCEL", "业务中台-保税售后");

    public static ReportType LOAD_ORDER_FOR_EXCEL = ReportType.valueOf("LOAD_ORDER_FOR_EXCEL", "装载单");
    public static ReportType LOAD_ORDER_DETAIL_FOR_EXCEL = ReportType.valueOf("LOAD_ORDER_DETAIL_FOR_EXCEL", "装载单明细");

    public static ReportType CUSTOMS_BOOK_ITEM_EXIT_REGION_LIST_FOR_EXCEL = ReportType.valueOf("CUSTOMS_BOOK_ITEM_EXIT_REGION_LIST_FOR_EXCEL", "料号出区明细");


    public static ReportType HONDOVER_ORDER_FOR_EXCEL = ReportType.valueOf("HONDOVER_ORDER_FOR_EXCEL", "交接单");
    public static ReportType HONDOVER_ORDER_DETAIL_FOR_EXCEL = ReportType.valueOf("HONDOVER_ORDER_DETAIL_FOR_EXCEL", "交接单明细");

    public static ReportType HANDOVER_ORDER_PACKAGE_FOR_EXCEL = ReportType.valueOf("HANDOVER_ORDER_PACKAGE_FOR_EXCEL", "交接单包裹数据");

    public static ReportType CULL_ORDER_FOR_EXCEL = ReportType.valueOf("CULL_ORDER_FOR_EXCEL", "剔除单");

    public static ReportType RECORD_BASE_EXCEL = ReportType.valueOf("RECORD_BASE_EXCEL", "备案库");

    public static ReportType MAIL_CONT_EXECL = ReportType.valueOf("MAIL_CONT_EXECL", "邮件事项统计");

    public static ReportType COLLABORATE_ORDER_FOR_EXCEL = ReportType.valueOf("COLLABORATE_ORDER_FOR_EXCEL", "协同单");

    public static ReportType JD_PURCHASE_ORDER_EXCEL = ReportType.valueOf("JD_PURCHASE_ORDER_EXCEL", "京东采购单");

    public static ReportType JD_PURCHASE_ORDER_TALLY_EXCEL = ReportType.valueOf("JD_PURCHASE_ORDER_TALLY_EXCEL", "京东采购单理货报告");

    public static ReportType JD_PURCHASE_ORDER_DETAIL_EXCEL = ReportType.valueOf("JD_PURCHASE_ORDER_DETAIL_EXCEL", "京东采购单商品明细");

    public static ReportType CUSTOMS_INVENTORY_ORDER_CUSTOMS_PERSON_FOR_EXCEL = ReportType.valueOf("CUSTOMS_INVENTORY_ORDER_CUSTOMS_PERSON_FOR_EXCEL", "清单-人工审核");

    public static ReportType SUPERVISION_EXPORT_ORDER_FOR_EXCEL = ReportType.valueOf("SUPERVISION_EXPORT_ORDER_FOR_EXCEL", "包裹出区模板导出");
    public static ReportType SUPERVISION_PRODUCT_FOR_EXCEL = ReportType.valueOf("SUPERVISION_PRODUCT_FOR_EXCEL", "料号结转模板导出");
    public static ReportType SUPERVISION_EXCEPTION_FOR_EXCEL = ReportType.valueOf("SUPERVISION_EXCEPTION_FOR_EXCEL", "时效监管异常导出");

    public static ReportType FB_CHECKLIST_FOR_EXCEL = ReportType.valueOf("FB_CHECKLIST_FOR_EXCEL", "非保核放单");
    public static ReportType MERGE_RELATION_FOR_EXCEL = ReportType.valueOf("MERGE_RELATION_FOR_EXCEL", "归并关系");
    public static ReportType FB_INVENTORY_FOR_EXCEL = ReportType.valueOf("FB_INVENTORY_FOR_EXCEL", "非保库存记录");
    public static ReportType FB_STOCK_IN_OUT_FOR_EXCEL = ReportType.valueOf("FB_STOCK_IN_OUT_FOR_EXCEL", "非保海关出入库记录");

    public static ReportType ITEMSTOCKLIST_FOR_EXCEL = ReportType.valueOf("ITEMSTOCKLIST_FOR_EXCEL", "料号库存流水");
    public static ReportType ITEMSTOCKSUMMARY_FOR_EXCEL = ReportType.valueOf("ITEMSTOCKSUMMARY_FOR_EXCEL", "料号库存汇总");
    public static ReportType PROMOTION_ORDER_FOR_EXCEL = ReportType.valueOf("PROMOTION_ORDER_FOR_EXCEL", "促销单导出");
    public static ReportType TO_B_OVERDUE_FOR_EXCEL = ReportType.valueOf("TO_B_OVERDUE_FOR_EXCEL", "toB超时");

    public static ReportType RECONCILIATION_ITEM_FOR_EXCEL = ReportType.valueOf("RECONCILIATION_ITEM_FOR_EXCEL", "对账订单导出");
    public static ReportType BIZ_DECLARE_FORM_FOR_EXCEL = ReportType.valueOf("BIZ_DECLARE_FORM_FOR_EXCEL", "业务申报表导出");
    public static ReportType BIZ_DECLARE_FORM_ITEM_FOR_EXCEL = ReportType.valueOf("BIZ_DECLARE_FORM_ITEM_FOR_EXCEL", "业务申报表表体导出");
    public static ReportType PROCESS_BOOK_ITEM_FOR_EXCEL = ReportType.valueOf("PROCESS_BOOK_ITEM_FOR_EXCEL", "加贸账册表体导出");
    private ReportType(String value, String name) {
        super(value, name);
    }

    public static ReportType valueOf(String value, String name) {
        ReportType e = allbyvalue.get(value);
        if (e != null) {
            if (e.name.equals(name) || undefined.equals(name))
            //undefined可以更新， 其他的name不可以更新？ No, 所有值都可以更新; 但是不能用undefined覆盖已有值
            {
                return e;
            } else {
                //命名不相同
                //log.warn("Name to be change. value:" + value + ", old name:" + e.name + ", new name:" + name);
            }
        }

        Map<String, ReportType> allbyvalue_new = new HashMap<String, ReportType>();
        Map<String, ReportType> allbyname_new = new HashMap<String, ReportType>();
        e = new ReportType(value, name);
        lock.lock();
        try {
            allbyvalue_new.putAll(allbyvalue);
            allbyname_new.putAll(allbyname);
            allbyvalue_new.put(value, e);
            allbyname_new.put(name, e);
            allbyvalue = allbyvalue_new;
            allbyname = allbyname_new;
        } finally {
            lock.unlock();
        }
        return e;
    }


    public static ReportType valueOf(String value) {
        ReportType e = allbyvalue.get(value);
        if (e != null) {
            return e;
        } else {
            return valueOf(value, undefined);
        }
    }

    public static boolean containValue(String value) {
        ReportType e = allbyvalue.get(value);
        if (e != null) {
            return true;
        } else {
            return false;
        }
    }

    public static ReportType[] values() {
        return allbyvalue.values().toArray(new ReportType[0]);
    }
}
