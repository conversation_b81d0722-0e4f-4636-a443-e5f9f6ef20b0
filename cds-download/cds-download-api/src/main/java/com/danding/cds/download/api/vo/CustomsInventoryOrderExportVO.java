package com.danding.cds.download.api.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Data
public class CustomsInventoryOrderExportVO implements Serializable {

    @ExcelIgnore
    private String id;
    @ExcelIgnore
    private long updateTime;

    @Excel(name = "申报单号", needMerge = true)
    @ExcelProperty(value = "申报单号")
    private String declareOrderNo;
    @Excel(name = "用户", needMerge = true)
    @ExcelProperty(value = "用户")
    private String tenantName;
    @Excel(name = "租户ID", needMerge = true)
    @ExcelProperty(value = "租户ID")
    private String tenantId;
    @Excel(name = "海关状态", needMerge = true)
    @ExcelProperty(value = "海关状态")
    private String customsStatusDesc;
    @ExcelProperty(value = "回执信息")
    @Excel(name = "回执信息", needMerge = true)
    private String customsDetail;
    @ExcelProperty(value = "电子税单状态")
    @Excel(name = "电子税单状态", needMerge = true)
    private String taxBillStatusDesc;
    @ExcelProperty(value = "是否出区")
    @Excel(name = "是否出区", needMerge = true)
    private String exitRegionStatusDesc;
    @ExcelProperty(value = "售后状态")
    @Excel(name = "售后状态", needMerge = true)
    private String afterSalesStatusDesc;
    @ExcelProperty(value = "申报时间")
    @Excel(name = "申报时间", needMerge = true)
    private String declareTime;
    @ExcelProperty(value = "回执时间")
    @Excel(name = "回执时间", needMerge = true)
    private String receiveTime;
    @ExcelProperty(value = "创建时间")
    @Excel(name = "创建时间", needMerge = true)
    private String createTime;
    @ExcelProperty(value = "出区时间")
    @Excel(name = "出区时间", needMerge = true)
    private String exitRegionTime;
    @ExcelProperty(value = "清单编号")
    @Excel(name = "清单编号", needMerge = true)
    private String inventoryNo;
    @Excel(name = "运单号", needMerge = true)
    @ExcelProperty(value = "运单号")
    private String logisticsNo;
    @Excel(name = "快递名称", needMerge = true)
    @ExcelProperty(value = "快递名称")
    private String expressName;
    @Excel(name = "订单金额", needMerge = true)
    @ExcelProperty(value = "订单金额")
    private BigDecimal totalPrice;
    @Excel(name = "总税金", needMerge = true)
    @ExcelProperty(value = "总税金")
    private BigDecimal orderTax;
    @Excel(name = "应征关税", needMerge = true)
    @ExcelProperty(value = "应征关税")
    private BigDecimal customsTax;
    @Excel(name = "应征增值税", needMerge = true)
    @ExcelProperty(value = "应征增值税")
    private BigDecimal valueAddedTax;
    @Excel(name = "应征消费税", needMerge = true)
    @ExcelProperty(value = "应征消费税")
    private BigDecimal consumptionTax;
    @ExcelCollection(name = "商品信息")
    @ExcelIgnore
    @ExcelProperty(value = "商品信息")
    private List<CustomsInventoryOrderItemExportVO> itemExportVOS = new ArrayList<>();
    @Excel(name = "是否非保赠品")
    @ExcelProperty({"商品信息", "是否非保赠品"})
    private String fbGifts;
    @Excel(name = "金二序号")
    @ExcelProperty({"商品信息", "金二序号"})
    private String goodsSeqNo;
    @Excel(name = "料号")
    @ExcelProperty({"商品信息", "料号"})
    private String productId;
    @Excel(name = "商品名称")
    @ExcelProperty({"商品信息", "商品名称"})
    private String goodsName;
    @Excel(name = "单价")
    @ExcelProperty({"商品信息", "单价"})
    private BigDecimal unitPrice;
    @Excel(name = "数量")
    @ExcelProperty({"商品信息", "数量"})
    private Integer count;
    @Excel(name = "原产国")
    @ExcelProperty({"商品信息", "原产国"})
    private String originCountryName;
    @Excel(name = "条码")
    @ExcelProperty({"商品信息", "条码"})
    private String barCode;
    @Excel(name = "HS编码")
    @ExcelProperty({"商品信息", "HS编码"})
    private String hsCode;
    @Excel(name = "商品项总和", needMerge = true)
    @ExcelProperty(value = "商品项总和")
    private Integer totalCount;
    @Excel(name = "电商企业", width = 50, needMerge = true)
    @ExcelProperty(value = "电商企业")
    private String ebcName;
    @Excel(name = "电商平台", width = 50, needMerge = true)
    @ExcelProperty(value = "电商平台")
    private String ebpName;
    @ExcelProperty(value = "清关企业")
    @Excel(name = "清关企业", width = 50, needMerge = true)
    private String agentCompanyName;
    @ExcelProperty(value = "实体仓名称")
    @Excel(name = "实体仓名称", width = 50, needMerge = true)
    private String erpPhyWarehouseName;

}
