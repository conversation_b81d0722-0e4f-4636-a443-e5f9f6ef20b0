<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.download.mapper.DownloadMapper">
    <resultMap id="BaseResultMap" type="com.danding.cds.download.entity.DownloadDO">
        <!--
          WARNING - @mbg.generated
        -->
        <result column="report_type" jdbcType="VARCHAR" property="reportType" />
        <result column="report_name" jdbcType="VARCHAR" property="reportName" />
        <result column="download_count" jdbcType="INTEGER" property="downloadCount" />
        <result column="file_path" jdbcType="VARCHAR" property="filePath" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
        <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
        <result column="condition" jdbcType="VARCHAR" property="condition" />
        <result column="remark" jdbcType="VARCHAR" property="remark" />
    </resultMap>
</mapper>