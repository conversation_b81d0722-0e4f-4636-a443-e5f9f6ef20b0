CREATE TABLE `ccs_download` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `report_type` varchar(50) NOT NULL COMMENT '报表类型',
  `report_name` varchar(255) NOT NULL COMMENT '报表名称',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数 默认0',
  `file_path` varchar(255) DEFAULT NULL COMMENT '文件路径',
  `status` tinyint(4) NOT NULL COMMENT '状态',
  `start_time` datetime DEFAULT NULL COMMENT '开始时间',
  `finish_time` datetime DEFAULT NULL COMMENT '完成时间',
  `conditions` varchar(1024) NOT NULL COMMENT '筛选条件',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_template` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `template_name` varchar(255) NOT NULL COMMENT '模板名称',
  `file_path` varchar(255) NOT NULL COMMENT '文件路径',
  `download_count` int(11) NOT NULL DEFAULT '0' COMMENT '下载次数默认0',
  `remark` varchar(255) DEFAULT NULL COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4;