package com.danding.cds.download.engine.customs.inventory;

import cn.afterturn.easypoi.handler.inter.IExcelExportServer;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IGoodsRpcFacade;
import com.danding.business.client.rpc.goods.result.GoodsRpcResult;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsSingleInventoryEsDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryAfterStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.download.api.vo.CustomsInventoryOrderExportVO;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.download.entity.es.CustomsInventoryItemEsDO;
import com.danding.cds.download.entity.es.CustomsSingleInventoryEsDO;
import com.danding.cds.download.service.FutureService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.taxes.api.dto.TaxesTenantTaxListDTO;
import com.danding.cds.taxes.api.service.TaxesTenantTaxListService;
import com.danding.cds.util.DeclareOrderEsUtil;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.enums.TaxBillStatusEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.common.response.ListVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
@RefreshScope
public class CustomsInventoryExportService extends BaseExcelService {

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private TaxesTenantTaxListService taxListService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Autowired
    private FutureService futureService;

    @Autowired
    protected ElasticsearchOperations elasticsearchTemplate;

    @DubboReference
    private IGoodsRpcFacade erpIGoodsRpcFacade;

    @Value("${export.PageSize:}")
    private String PAGE_SIZE;

    @Value("${noDisplayMailNo.ebpId:309}")
    private Long noDisplayMailNoEbpId;

    @Resource
    private OrderCCallConfig orderCCallConfig;

    @Override
    protected boolean isExportByEsScroll() {
        return true;
    }

    /**
     * 通过es滚动导出数据
     *
     * @param pojoClass
     * @param server      实现对象
     * @param queryParams 查询参数
     * @return
     */
    @Override
    protected ByteArrayOutputStream exportByEsScroll(Class<?> pojoClass, IExcelExportServer server, Object queryParams) {
        SingleInvtOrderSearch search = (SingleInvtOrderSearch) queryParams;
        int pageSize = 1000;
        long scrollTimeInMillis = 10 * 1000;
        if (StrUtil.isNotEmpty(search.getErpPhyWarehouseSn())) {
            List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(search.getErpPhyWarehouseSn());
            if (CollectionUtil.isEmpty(dtoByErpCode)) {
                search.setAccountBookIdList(Lists.newArrayList(-1L));
            } else {
                List<Long> list = dtoByErpCode.stream().map(EntityWarehouseDTO::getCustomsBookId).collect(Collectors.toList());
                search.setAccountBookIdList(list);
            }
        }
        SearchQuery searchQuery = DeclareOrderEsUtil.getSearchQueryBySingleInvtOrderSearch(search, pageSize);
        log.info("海关清单导出查询语句:{}", JSON.toJSONString(searchQuery));
        ScrolledPage<CustomsSingleInventoryEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, CustomsSingleInventoryEsDO.class);
        long total = scrolledPage.getTotalElements();
        log.info("申报单导出总数据:{}", total);
        if (total > ES_SCOLL_NUM_LIMIT) {
            throw new RuntimeException("申报单导出数据超过最大值" + ES_SCOLL_NUM_LIMIT);
        }
        ConcurrentHashMap<Long, CompanyDTO> companyDTOMap = new ConcurrentHashMap<>();
        Vector<CustomsInventoryOrderExportVO> codeExportVOS = new Vector<>();
        ConcurrentHashMap<Long, EndorsementDTO> endorsementDTOMap = new ConcurrentHashMap<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        while (scrolledPage.hasContent()) {
            final List<CustomsSingleInventoryEsDO> content = scrolledPage.getContent();
            log.info("海关清单导出滚动获取数据:{}", JSON.toJSONString(content));
            Runnable runnable = new CustomsInventoryDownTask(content, companyDTOMap, endorsementDTOMap, codeExportVOS);
            futureList.add(futureService.runAsync(runnable));
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, CustomsSingleInventoryEsDO.class);
        }
        log.debug("申报单导出任务放置完成");

        // 组合CompletableFuture结果，返回数据
        try {
            futureService.allOfAndGet(futureList);
        } catch (Exception e) {
            log.error("海关清单导出组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("海关清单导出组合CompletableFuture结果异常" + e.getMessage(), e);
        }
        // 这里做下去重
        // 这里做下去重，es不同月份索引数据可能会数据重复，需要保证顺序
        List<CustomsInventoryOrderExportVO> finalInventoryOrderExportVo = new ArrayList<>();
        Map<String, String> inventoryOrderIdMap = new HashMap<>();
        for (CustomsInventoryOrderExportVO codeExportVO : codeExportVOS) {
            String id = codeExportVO.getId();
            if (!inventoryOrderIdMap.containsKey(id)) {
                inventoryOrderIdMap.put(id, id);
                finalInventoryOrderExportVo.add(codeExportVO);
            }
        }
//
//        Map<String, CustomsInventoryOrderExportVO> inventoryOrderExportVOMap = codeExportVOS.stream()
//                .collect(Collectors.toMap(CustomsInventoryOrderExportVO::getId, Function.identity(), (v1, v2) -> {
//                    // 最新的更新时间
//                    if (v2.getUpdateTime() > v1.getUpdateTime()) {
//                        return v2;
//                    }
//                    // 出区时间
//                    if (StringUtils.isEmpty(v1.getExitRegionTime()) && StringUtils.hasText(v2.getExitRegionTime())) {
//                        return v2;
//                    }
//                    return v1;
//                }));
        // 输出到excle
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        EasyExcel.write(byteArrayOutputStream, pojoClass).sheet("海关清单数据导出").doWrite(finalInventoryOrderExportVo);
        return byteArrayOutputStream;
    }
//
//    public static void main(String[] args) {
//
//        Vector<CustomsInventoryOrderExportVO> codeExportVOS = new Vector<>();
//        CustomsInventoryOrderExportVO orderExportVO1 = new CustomsInventoryOrderExportVO();
//        orderExportVO1.setId("setDeclareOrderNo1");
//        codeExportVOS.add(orderExportVO1);
//
//        CustomsInventoryOrderExportVO orderExportVO2 = new CustomsInventoryOrderExportVO();
//        orderExportVO2.setId("setDeclareOrderNo2");
//        orderExportVO2.setUpdateTime(1122L);
//        codeExportVOS.add(orderExportVO2);
//
//        CustomsInventoryOrderExportVO orderExportVO3 = new CustomsInventoryOrderExportVO();
//        orderExportVO3.setId("setDeclareOrderNo2");
//        orderExportVO3.setUpdateTime(2222L);
//        codeExportVOS.add(orderExportVO3);
//
//
//        // 这里做下去重，es不同月份索引数据可能会数据重复
//        Map<String, CustomsInventoryOrderExportVO> inventoryOrderExportVOMap = codeExportVOS.stream().collect(Collectors.toMap(CustomsInventoryOrderExportVO::getId, Function.identity(), (v1, v2) -> v1));
//
////
////                Map < String, CustomsInventoryOrderExportVO > inventoryOrderExportVOMap = new HashMap<>();
////        codeExportVOS.forEach(z -> {
////            String id = z.getId();
////            CustomsInventoryOrderExportVO inventoryOrderExportVO = inventoryOrderExportVOMap.get(id);
////            if (inventoryOrderExportVO == null) {
////                inventoryOrderExportVOMap.put(id, z);
////            } else {
////                // 判断下更新时间最新的一个
////                Long updateTime = inventoryOrderExportVO.getUpdateTime();
////                Long updateTimeCurr = z.getUpdateTime();
////                if (updateTime != null && updateTimeCurr != null) {
////                    if (updateTimeCurr > updateTime) {
////                        inventoryOrderExportVOMap.put(id, z);
////                    }
////                }
////            }
////        });
//
//        List<Object> inventoryOrderExportVOS = Arrays.asList(inventoryOrderExportVOMap.values().toArray());
//        System.out.println(inventoryOrderExportVOS);
//    }

    @Override
    public List selectListForExcelExport(Object queryParams, int page) {
        SingleInvtOrderSearch search = (SingleInvtOrderSearch) queryParams;
        log.info("清单导出数量-{}", PAGE_SIZE);
        int exportPageSize = Integer.parseInt(PAGE_SIZE);
        search.setPageSize(exportPageSize);
        search.setCurrentPage(page);
        ListVO<CustomsSingleInventoryEsDTO> paging = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.pagingES(search) : customsInventoryService.pagingES(search);
        List<CustomsSingleInventoryEsDO> exportItems = ConvertUtil.listConvert(paging.getDataList(), CustomsSingleInventoryEsDO.class);
        return buildExportVO(exportItems);
    }

    private List buildExportVO(List<CustomsSingleInventoryEsDO> list) {
        ConcurrentHashMap<Long, CompanyDTO> companyDTOMap = new ConcurrentHashMap<>();
        Vector<CustomsInventoryOrderExportVO> codeExportVOS = new Vector<>();
        ConcurrentHashMap<Long, EndorsementDTO> endorsementDTOMap = new ConcurrentHashMap<>();
        Runnable runnable = new CustomsInventoryDownTask(list, companyDTOMap, endorsementDTOMap, codeExportVOS);
        futureService.runAsync(runnable);
        return codeExportVOS;
    }

    @Override
    public Class<?> getModelClazz() {
        return CustomsInventoryOrderExportVO.class;
    }


    class CustomsInventoryDownTask extends TraceDataRunnable {

        private List<CustomsSingleInventoryEsDO> inventoryEsDOS;
        private ConcurrentHashMap<Long, CompanyDTO> companyDTOMap;
        private ConcurrentHashMap<Long, EndorsementDTO> endorsementDTOMap;
        private Vector<CustomsInventoryOrderExportVO> codeExportVos;

        public CustomsInventoryDownTask(List<CustomsSingleInventoryEsDO> inventoryEsDOS, ConcurrentHashMap<Long, CompanyDTO> companyDTOMap,
                                        ConcurrentHashMap<Long, EndorsementDTO> endorsementDTOMap, Vector<CustomsInventoryOrderExportVO> codeExportVOS) {
            super();
            this.companyDTOMap = companyDTOMap;
            this.inventoryEsDOS = inventoryEsDOS;
            this.endorsementDTOMap = endorsementDTOMap;
            this.codeExportVos = codeExportVOS;
        }

        @Override
        public void proxy() {
            doRun();
        }

        private void doRun() {
            Map<Long, EntityWarehouseDTO> warehouseMap = new HashMap<>();
            if (CollUtil.isNotEmpty(inventoryEsDOS)) {
                List<Long> accountBookIdList = inventoryEsDOS.stream()
                        .map(CustomsSingleInventoryEsDO::getAccountBookId)
                        .distinct()
                        .collect(Collectors.toList());
                List<EntityWarehouseDTO> warehouseList = entityWarehouseService.findByCustomsBookId(accountBookIdList);
                warehouseMap = warehouseList.stream().collect(Collectors.toMap(EntityWarehouseDTO::getCustomsBookId, Function.identity(), (k1, k2) -> k2));
            }
            // 查询数据库清单表体信息
            List<CustomsInventoryDTO> getItemParam = inventoryEsDOS.stream().map(i -> {
                CustomsInventoryDTO customsInventoryDTO = new CustomsInventoryDTO();
                customsInventoryDTO.setId(Long.valueOf(i.getId()));
                String dateStr = i.getSn().substring(2, 12);
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
                Date date = dateTimeFormatter.parseDateTime(dateStr).toDate();
                customsInventoryDTO.setCreateTime(date);
                return customsInventoryDTO;
            }).collect(Collectors.toList());
            Map<Long, List<CustomsInventoryItemDTO>> customsInvetoryItemMap = customsInventoryRpc.listItemByInventoryS(getItemParam);
            for (CustomsSingleInventoryEsDO inventoryEsDO : inventoryEsDOS) {
                //拼接数据
                CustomsInventoryOrderExportVO exportVO = new CustomsInventoryOrderExportVO();
                BeanUtil.copyProperties(inventoryEsDO, exportVO);
//                if (Objects.equals(inventoryEsDO.getEbpId(), noDisplayMailNoEbpId)) {
//                    exportVO.setLogisticsNo("");
//                }
                exportVO.setCustomsStatusDesc(inventoryEsDO.getCustomsStatus() != null ? CustomsStat.getEnum(inventoryEsDO.getCustomsStatus()).getDesc() : "");
                exportVO.setCustomsDetail(!StringUtils.isEmpty(inventoryEsDO.getCustomsDetail()) ? inventoryEsDO.getCustomsDetail() : "");
                if (Objects.nonNull(inventoryEsDO.getTaxBillStatus())) {
                    exportVO.setTaxBillStatusDesc(TaxBillStatusEnums.getEnums(inventoryEsDO.getTaxBillStatus()).getDesc());
                }
                if (Objects.nonNull(warehouseMap.get(inventoryEsDO.getAccountBookId()))) {
                    exportVO.setErpPhyWarehouseName(warehouseMap.get(inventoryEsDO.getAccountBookId()).getErpWarehouseName());
                }
                exportVO.setAfterSalesStatusDesc(InventoryAfterStatus.getEnum(inventoryEsDO.getAfterSalesStatus()).getDesc());
                exportVO.setTenantId(inventoryEsDO.getTenantOuterId());
                exportVO.setCreateTime(new DateTime(inventoryEsDO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
                if (inventoryEsDO.getLastDeclareTime() != null) {
                    exportVO.setDeclareTime(new DateTime(inventoryEsDO.getLastDeclareTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                if (!StringUtils.isEmpty(inventoryEsDO.getCustomsStatus())) {
                    exportVO.setReceiveTime(new DateTime(inventoryEsDO.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                log.info("CustomsInventoryDownTask buildExportVO customsInventoryDTO={}", JSON.toJSONString(inventoryEsDO));
                String sn = inventoryEsDO.getSn();
                ExportItemDTO exportItemDTO = null;
                if (StringUtils.hasText(sn)) {
                    exportItemDTO = exportOrderService.findItemByCustomInventory(sn);
                }
                if (exportItemDTO != null) {
                    EndorsementDTO endorsementDTO = null;
                    if (Objects.nonNull(exportItemDTO.getEndorsementOrderId()) && endorsementDTOMap.containsKey(exportItemDTO.getEndorsementOrderId())) {
                        endorsementDTO = endorsementDTOMap.get(exportItemDTO.getEndorsementOrderId());
                    } else {
                        Long endorsementOrderId = exportItemDTO.getEndorsementOrderId();
                        if (endorsementOrderId != null) {
                            endorsementDTO = endorsementService.findById(endorsementOrderId);
                            endorsementDTOMap.put(endorsementDTO.getId(), endorsementDTO);
                        }
                    }
                    Date finishTime = Optional.ofNullable(endorsementDTO).map(EndorsementDTO::getFinishTime).orElse(null);
                    if (finishTime != null) {
                        exportVO.setExitRegionStatusDesc("已出区");
                        exportVO.setExitRegionTime(new DateTime(endorsementDTO.getFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
                    } else {
                        exportVO.setExitRegionStatusDesc("未出区");
                    }
                } else {
                    exportVO.setExitRegionStatusDesc("未出区");
                }
                if (!StringUtils.isEmpty(inventoryEsDO.getInventoryNo())) {
                    TaxesTenantTaxListDTO tenantTaxListDTO = taxListService.findByInvtNo(inventoryEsDO.getInventoryNo());
                    exportVO.setOrderTax(tenantTaxListDTO == null ? BigDecimal.ZERO : tenantTaxListDTO.getAmount());
                    if (tenantTaxListDTO != null) {
                        exportVO.setCustomsTax(tenantTaxListDTO.getCustomsTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getCustomsTax());
                        exportVO.setValueAddedTax(tenantTaxListDTO.getValueAddedTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getValueAddedTax());
                        exportVO.setConsumptionTax(tenantTaxListDTO.getConsumptionTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getConsumptionTax());
                    }
                } else {
                    exportVO.setOrderTax(BigDecimal.ZERO);
                    exportVO.setCustomsTax(BigDecimal.ZERO);
                    exportVO.setValueAddedTax(BigDecimal.ZERO);
                    exportVO.setConsumptionTax(BigDecimal.ZERO);
                }
                if (Objects.nonNull(inventoryEsDO.getEbcId())) {
                    CompanyDTO ebcCompany = companyDTOMap.get(inventoryEsDO.getEbcId());
                    if (ebcCompany == null) {
                        ebcCompany = companyService.findUnifiedCrossInfoById(inventoryEsDO.getEbcId());
                        companyDTOMap.put(ebcCompany.getId(), ebcCompany);
                    }
                    if (ebcCompany != null) {
                        exportVO.setEbcName(ebcCompany.getName());
                    }
                }
                if (Objects.nonNull(inventoryEsDO.getEbpId())) {
                    CompanyDTO ebpCompany = companyDTOMap.get(inventoryEsDO.getEbpId());
                    if (ebpCompany == null) {
                        ebpCompany = companyService.findUnifiedCrossInfoById(inventoryEsDO.getEbpId());
                        companyDTOMap.put(ebpCompany.getId(), ebpCompany);
                    }
                    if (ebpCompany != null) {
                        exportVO.setEbpName(ebpCompany.getName());
                    }
                }
                if (Objects.nonNull(inventoryEsDO.getAgentCompanyId())) {
                    CompanyDTO agentCompany = companyDTOMap.get(inventoryEsDO.getAgentCompanyId());
                    if (agentCompany == null) {
                        agentCompany = companyService.findUnifiedCrossInfoById(inventoryEsDO.getAgentCompanyId());
                        companyDTOMap.put(agentCompany.getId(), agentCompany);
                    }
                    if (agentCompany != null) {
                        exportVO.setAgentCompanyName(agentCompany.getName());
                    }
                }

                List<CustomsInventoryItemEsDO> itemDOS = inventoryEsDO.getItemExtras();
                double totalPrice = itemDOS.stream()
                        .filter(c -> Objects.nonNull(c.getUnitPrice()) && Objects.nonNull(c.getCount()))
                        .mapToDouble(c -> c.getCount() * c.getUnitPrice()).sum();
                int countSum = itemDOS.stream()
                        .filter(c -> Objects.nonNull(c.getCount()))
                        .mapToInt(CustomsInventoryItemEsDO::getCount).sum();
                exportVO.setTotalCount(countSum);
                exportVO.setTotalPrice(BigDecimal.valueOf(totalPrice));
                Map<String, CustomsBookItemDTO> customsBookItemMap = new HashMap<>();
                //多个商品信息通过多行展示，其他信息只在第一条商品中展示
                List<CustomsInventoryOrderExportVO> tmpOrderExportVo = new ArrayList<>();
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInvetoryItemMap.get(Long.valueOf(exportVO.getId()));
                if (CollectionUtils.isEmpty(customsInventoryItemDTOS)) {
                    log.info("CustomsInventoryDownTask buildExportVO customsInventoryItemDTOS is empty,customsInventoryDTO={}", JSON.toJSONString(inventoryEsDO));
                    continue;
                }
                for (int i = 0; i < customsInventoryItemDTOS.size(); i++) {
                    CustomsInventoryItemDTO itemDO = customsInventoryItemDTOS.get(i);
                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDO.getExtraJson(), CustomsInventoryItemExtra.class);
                    String idLink = exportVO.getId() + "_" + itemDO.getId();
                    CustomsInventoryOrderExportVO orderExportVO = new CustomsInventoryOrderExportVO();
                    if (i == 0) {
                        orderExportVO = ConvertUtil.beanConvert(exportVO, CustomsInventoryOrderExportVO.class);
                    }
                    if (Objects.nonNull(itemDO.getUnitPrice())) {
                        orderExportVO.setUnitPrice(itemDO.getUnitPrice());
                    }
                    if (Objects.nonNull(itemDO.getCount())) {
                        orderExportVO.setCount(itemDO.getCount());
                    }
                    if (Objects.nonNull(itemExtra.getHsCode())) {
                        orderExportVO.setHsCode(itemExtra.getHsCode());
                    }
                    // 这替换下id，方便后续多条数据去重
                    orderExportVO.setId(idLink);
                    orderExportVO.setProductId(itemExtra.getProductId());
                    orderExportVO.setGoodsSeqNo(itemExtra.getGoodsSeqNo());
                    orderExportVO.setGoodsName(itemExtra.getGoodsName());
                    orderExportVO.setBarCode(itemExtra.getBarCode());
                    //原产国不走ES，走ES的话custom_inventoy_item没有这个字段，也要重新查边数据库推到ES，这个只有导出用，所以直接走数据库
                    if (Objects.nonNull(itemDO.getItemTag()) && OrderItemTagEnum.containsFbGifts(itemDO.getItemTag())) {
                        // 非保赠品就不查账册库存了
                        orderExportVO.setFbGifts("是");
                        if (StrUtil.isBlank(orderExportVO.getBarCode()) && StrUtil.isNotBlank(inventoryEsDO.getTenantOuterId())) {
                            GoodsRpcResult goodsRpcResult = erpIGoodsRpcFacade.getRpcSingleGoodsByParam(Long.valueOf(inventoryEsDO.getTenantOuterId()), itemDO.getItemNo());
                            orderExportVO.setBarCode(goodsRpcResult.getBarcode());
                        }
                    } else {
                        orderExportVO.setFbGifts("否");
                        String key = inventoryEsDO.getAccountBookId() + "_" + itemExtra.getProductId() + "_" + itemExtra.getGoodsSeqNo();
                        CustomsBookItemDTO customsBookItemDTO;
                        if (customsBookItemMap.containsKey(key)) {
                            customsBookItemDTO = customsBookItemMap.get(key);
                        } else {
                            customsBookItemDTO = customsBookItemService.findIdByBookIdAndProIdAndSeq(inventoryEsDO.getAccountBookId(), itemExtra.getProductId(), itemExtra.getGoodsSeqNo());
                            if (Objects.nonNull(customsBookItemDTO)) {
                                customsBookItemMap.put(key, customsBookItemDTO);
                            }
                        }
                        if (Objects.nonNull(customsBookItemDTO)) {
                            if (Objects.nonNull(customsBookItemDTO.getOriginCountry())) {
                                CustomsCountryDTO customsCountryDTO = customsCountryService.findByCode(customsBookItemDTO.getOriginCountry());
                                if (Objects.nonNull(customsCountryDTO)) {
                                    orderExportVO.setOriginCountryName(customsCountryDTO.getName());
                                }
                            }
                        }
                    }

                    tmpOrderExportVo.add(orderExportVO);
                }
                codeExportVos.addAll(tmpOrderExportVo);
            }
        }
    }
}