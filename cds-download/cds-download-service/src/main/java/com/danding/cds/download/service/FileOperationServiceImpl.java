package com.danding.cds.download.service;

import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.ObjectMetadata;
import com.aliyun.oss.model.PutObjectResult;
import com.danding.cds.download.api.service.FileOperationService;
import org.apache.dubbo.config.annotation.DubboService;

import java.io.ByteArrayInputStream;
import java.util.Date;
import java.util.Map;

@DubboService
public class FileOperationServiceImpl implements FileOperationService {
    @Override
    public void uploadFile2OSS(String fileName,  byte[] byteArr, long contentSize, String contentType, Map<String, String> metaInfo) {
        ObjectMetadata meta = new ObjectMetadata();
        if (metaInfo != null) {
            for (Map.Entry<String, String> entry : metaInfo.entrySet()) {
                meta.addUserMetadata(entry.getKey(), entry.getValue());
                if (entry.getKey().equals("expirTime")) {
                    meta.setExpirationTime(new Date(Long.parseLong(entry.getValue())));
                }
            }
        }
        meta.setContentType(contentType);
        meta.setContentLength(contentSize);
        //byte数组转inputStream
        ByteArrayInputStream inputStream = new ByteArrayInputStream(byteArr);
        OSSClient ossClient = new OSSClient("oss-cn-hangzhou.aliyuncs.com", "LTAI5tNGqjjEfJ1n3xbV42v9", "******************************");
        PutObjectResult result = ossClient.putObject("daita-oss", fileName,
                inputStream, meta);
        result.getETag();
    }


}
