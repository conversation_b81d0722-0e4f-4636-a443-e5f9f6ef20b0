package com.danding.cds.download.engine.itemStock;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.enums.InventoryCalculationTypeEnums;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.download.api.vo.CustomsBookItemStockExportV2VO;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.inventory.api.dto.InventoryChangeDTO;
import com.danding.cds.inventory.api.dto.InventoryChangeDetailSearch;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.logistics.api.common.response.ListVO;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/7/27  9:20
 * @Describe
 **/
@Component
@Slf4j
public class CustomsBookItemStockExcelService extends BaseExcelService {
    @DubboReference
    private InventoryChangeService inventoryChangeService;

    @Resource(name = "itemStockThreadExecutor")
    private ThreadPoolTaskExecutor itemStockThreadExecutor;

    @Override
    public Class<?> getModelClazz() {
        return CustomsBookItemStockExportV2VO.class;
    }

    @Override
    public List selectListForExcelExport(Object o, int currentPage) {
        if (currentPage > 1) {
            return new ArrayList<>();
        }
        InventoryChangeDetailSearch search = (InventoryChangeDetailSearch) o;
        search.setTimeDesc(false);
        search.setPageSize(2000);
        search.setCurrentPage(1);
        ListVO<InventoryChangeDTO> paging = inventoryChangeService.paging(search);
        List<InventoryChangeDTO> list = paging.getDataList();
        Vector<CustomsBookItemStockExportV2VO> itemStockExportVOList = new Vector<>(buildExportVO(list));
        if (search.getCurrentPage() == paging.getPage().getTotalPage()) {
            // 只有一页数据直接返回
            addTotalRecord(itemStockExportVOList);
            return itemStockExportVOList;
        }
        // 多页数据，开启多线程查询
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        while (search.getCurrentPage() + 1 <= paging.getPage().getTotalPage()) {
            search.setCurrentPage(search.getCurrentPage() + 1);
            Runnable runnable = new Task(search, itemStockExportVOList);
            futureList.add(CompletableFuture.runAsync(runnable, itemStockThreadExecutor));
        }
        // 组合CompletableFuture结果，返回数据
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("账册库存明细导出组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("账册库存明细导出组合CompletableFuture结果异常" + e.getMessage(), e);
        }
        // 根据业务时间逆序
        List<CustomsBookItemStockExportV2VO> sortedList = itemStockExportVOList.stream()
                .filter(i -> Objects.nonNull(i) && Objects.nonNull(i.getBusinessTime())).sorted(Comparator.comparing(CustomsBookItemStockExportV2VO::getBusinessTime)
                        .reversed()).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(sortedList)) {
            itemStockExportVOList.clear();
            itemStockExportVOList.addAll(sortedList);
        }
        addTotalRecord(itemStockExportVOList);
        return itemStockExportVOList;
    }

    class Task extends TraceDataRunnable {

        private final InventoryChangeDetailSearch search;
        private final Vector<CustomsBookItemStockExportV2VO> dataList;

        public Task(InventoryChangeDetailSearch search, Vector<CustomsBookItemStockExportV2VO> dataList) {
            this.search = search;
            this.dataList = dataList;
        }

        @Override
        protected void proxy() {
            ListVO<InventoryChangeDTO> paging = inventoryChangeService.paging(search);
            List<InventoryChangeDTO> list = paging.getDataList();
            dataList.addAll(buildExportVO(list));
        }
    }

    private void addTotalRecord(List<CustomsBookItemStockExportV2VO> itemStockExportVOList) {
        CustomsBookItemStockExportV2VO totalVO = new CustomsBookItemStockExportV2VO();
        totalVO.setCountName("变动累计");
        int avaIncrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getAvailableNumIncrease())).mapToInt(CustomsBookItemStockExportV2VO::getAvailableNumIncrease).sum();
        int avaDecrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getAvailableNumDecrease())).mapToInt(CustomsBookItemStockExportV2VO::getAvailableNumDecrease).sum();
        int occIncrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getOccupiedNumIncrease())).mapToInt(CustomsBookItemStockExportV2VO::getOccupiedNumIncrease).sum();
        int occDecrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getOccupiedNumDecrease())).mapToInt(CustomsBookItemStockExportV2VO::getOccupiedNumDecrease).sum();
        int lockIncrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getLockedNumIncrease())).mapToInt(CustomsBookItemStockExportV2VO::getLockedNumIncrease).sum();
        int lockDecrease = itemStockExportVOList.stream().filter(i -> Objects.nonNull(i.getLockedNumDecrease())).mapToInt(CustomsBookItemStockExportV2VO::getLockedNumDecrease).sum();
        totalVO.setAvailableNumIncrease(avaIncrease);
        totalVO.setAvailableNumDecrease(avaDecrease);
        totalVO.setAvailableNum(avaIncrease - avaDecrease);
        totalVO.setOccupiedNumIncrease(occIncrease);
        totalVO.setOccupiedNumDecrease(occDecrease);
        totalVO.setLockedNumIncrease(lockIncrease);
        totalVO.setLockedNumDecrease(lockDecrease);
        itemStockExportVOList.add(totalVO);
        CustomsBookItemStockExportV2VO lastVO = new CustomsBookItemStockExportV2VO();
        lastVO.setCountName("期末余额");
        lastVO.setAvailableNum(totalVO.getAvailableNum());
        itemStockExportVOList.add(lastVO);
    }

    private List<CustomsBookItemStockExportV2VO> buildExportVO(List<InventoryChangeDTO> list) {
        List<CustomsBookItemStockExportV2VO> itemStockExportVOList = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(list)) {
            InventoryChangeDTO first = list.get(0);
            CustomsBookItemStockExportV2VO firstVO = new CustomsBookItemStockExportV2VO();
            firstVO.setCountName("期初余额");
            firstVO.setAvailableNum(first.getAvailableNum());
            itemStockExportVOList.add(firstVO);
            for (InventoryChangeDTO i : list) {
                CustomsBookItemStockExportV2VO exportVO = new CustomsBookItemStockExportV2VO();
                BeanUtils.copyProperties(i, exportVO);
                exportVO.setAvailableNumIncrease(0);
                exportVO.setAvailableNumDecrease(0);
                exportVO.setOccupiedNumIncrease(0);
                exportVO.setOccupiedNumDecrease(0);
                exportVO.setLockedNumIncrease(0);
                exportVO.setLockedNumDecrease(0);
                InventoryChangeTypeEnums changeEnum = InventoryChangeTypeEnums.getEnum(i.getUpdateType());
                if (changeEnum != null) {
                    exportVO.setBusinessType(changeEnum.getBusinessType());
                    String inveCalType = changeEnum.getInveCalType();
                    if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.ADD_OCCUPATION.getType())) {
                        exportVO.setOccupiedNumIncrease(i.getChangeNum());
                        exportVO.setAvailableNumDecrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.REDUCE_OCCUPATION.getType())) {
                        exportVO.setOccupiedNumDecrease(i.getChangeNum());
                        exportVO.setAvailableNumIncrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.OCCUPIED_TO_USED.getType())) {
                        exportVO.setOccupiedNumDecrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.ADD_AVAILABLE.getType())) {
                        exportVO.setAvailableNumIncrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.REDUCE_AVAILABLE.getType())) {
                        exportVO.setAvailableNumDecrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.USED_TO_AVAILABLE.getType())) {
                        exportVO.setAvailableNumIncrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.ADD_LOCK.getType())) {
                        exportVO.setLockedNumIncrease(i.getChangeNum());
                        exportVO.setAvailableNumDecrease(i.getChangeNum());
                    } else if (Objects.equals(inveCalType, InventoryCalculationTypeEnums.REDUCE_LOCK.getType())) {
                        exportVO.setLockedNumDecrease(i.getChangeNum());
                        exportVO.setAvailableNumIncrease(i.getChangeNum());
                    }
                }
                exportVO.setBusinessTimeStr(new DateTime(i.getBusinessTime()).toString("yyyy-MM-dd HH:mm:ss"));
                itemStockExportVOList.add(exportVO);
            }
        }
        return itemStockExportVOList;
    }
}
