package com.danding.cds.download.entity.es;

import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * <AUTHOR>
 * @description: 商品列表ES对象
 * @date 2022/7/13 21:34
 */
@Data
public class RecordProductEsDO extends EsModel {
    /**
     * 商品备案ID
     */
    @Field(type = FieldType.Long, store = true)
    private Long recordId;
    /**
     * 商品备案口岸ID
     */
    @Field(type = FieldType.Long, store = true)
    private Long recordCustomsId;
    /**
     * 商品备案口岸
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsCode;
    /**
     * 账册ID
     */
    @Field(type = FieldType.Long, store = true)
    private Long customsBookId;
    /**
     * 账册编号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsBookNo;
    /**
     * 账册库存id
     */
    @Field(type = FieldType.Long, store = true)
    private Long customsBookItemId;
    /**
     * 海关备案料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsRecordProductId;
    /**
     * 原产国编码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String originCountryCode;
    /**
     * 原产国
     */
    @Field(type = FieldType.Keyword, store = true)
    private String originCountry;
    /**
     * 金二序号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String goodsSeqNo;

    @Field(type = FieldType.Integer, store = true)
    private Integer deleted;
}
