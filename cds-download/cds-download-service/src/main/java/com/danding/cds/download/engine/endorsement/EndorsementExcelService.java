package com.danding.cds.download.engine.endorsement;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.rpc.RefundOrderRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.download.api.dto.DownloadReportTaskRequest;
import com.danding.cds.download.api.dto.DownloadSubmit;
import com.danding.cds.download.api.enums.DowmloadStatusEnum;
import com.danding.cds.download.api.vo.EndorsementExcelVO;
import com.danding.cds.download.api.vo.EndorsementOrderExcelVO;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.download.engine.base.FileModel;
import com.danding.cds.endorsement.api.dto.*;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.enums.EndorsementOrderTypeEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.export.form.ExportTaskFinishForm;
import com.danding.park.client.core.export.form.ExportTaskUpdateForm;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/11/10 19:54
 * @Description:
 */
@Slf4j
@Component
public class EndorsementExcelService extends BaseExcelService {

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private RefundOrderService refundOrderService;
    @DubboReference
    private RefundOrderRpc refundOrderRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    @Override
    public Class<?> getModelClazz() {
        return EndorsementOrderExcelVO.class;
    }

    @Override
    public void run(DownloadReportTaskRequest reportTaskRequest, DownloadSubmit submit) {
        Object condition = reportTaskRequest.getCondition();
        String uid = reportTaskRequest.getUuid();
        Long reportTaskId = submit.getId();
        updateStateDownloading(reportTaskId, condition);
        try {
            FileModel fileModel = this.build(condition);
            String fileName = "ccs/" + fileModel.getFileName();
            String path = "https://daita-oss.oss-cn-hangzhou.aliyuncs.com/" + fileName;
            ExportTaskUpdateForm exportTaskUpdateForm = ExportTaskUpdateForm.build(uid);
            exportTaskUpdateForm.setTotal(100);
            exportTaskUpdateForm.setSuccessCount(1);
            exportTaskUpdateForm.setUrl(path);
            ParkClient.exportClient().update(exportTaskUpdateForm);
            byte[] bytes = fileModel.getBytes();
            fileOperationService.uploadFile2OSS(fileName, bytes,
                    bytes.length, "application/x-download", null);
            updateDownloadState(reportTaskId, path, DowmloadStatusEnum.COMPLETED.getCode(), "下载完成..", null);
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);;
            updateDownloadState(reportTaskId, null, DowmloadStatusEnum.EXCEPTION.getCode(), null, e);
        }
        ExportTaskFinishForm exportTaskFinishForm = ExportTaskFinishForm.build(uid);
        ParkClient.exportClient().finish(exportTaskFinishForm);
    }

    @Override
    public List<Object> selectListForExcelExport(Object queryParams, int page) {
        return null;
    }

    public Workbook singleExport(Long id){
        EndorsementDTO endorsementOrderDTO = endorsementService.findById(id);
        Workbook workbook = this.buildExport(endorsementOrderDTO);
        return workbook;
    }

    private Workbook buildExport(EndorsementDTO endorsementOrderDTO){
        // 生成随机文件名
        String filename = DownLoadUtil.FilePath + endorsementOrderDTO.getSn() + ".xlsx";
        // 将文件路径保存
        List<EndorsementOrderExcelVO> inventoryList = new ArrayList<>();
        List<EndorsementExcelVO> excelVOList = new ArrayList<>();
        //if(IEType.IMPORT.getValue().equals(endorsementOrderDTO.getIeFlag()))
        if (Objects.equals(endorsementOrderDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            List<EndorsementRelationDTO> relationDTOList = endorsementService.listRelationById(endorsementOrderDTO.getId());
            for (EndorsementRelationDTO relationDTO : relationDTOList) {
                EndorsementOrderExcelVO orderExcelVO = new EndorsementOrderExcelVO();
                orderExcelVO.setInvtNo(relationDTO.getInventoryNo());
                orderExcelVO.setDeclareOrderNo(relationDTO.getDeclareOrderNo());
                orderExcelVO.setLogisticsNo(relationDTO.getLogisticsNo());
                inventoryList.add(orderExcelVO);
            }
            List<EndorsementItemDTO> itemList = endorsementService.listItemById(endorsementOrderDTO.getId());
            int lineNo = 1;
            for (EndorsementItemDTO itemDTO : itemList) {
                EndorsementExcelVO excelVO = new EndorsementExcelVO();
                excelVO.setOrderSeqNo(String.valueOf(lineNo));
                excelVO.setTotalCount(itemDTO.getDeclareUnitQfy().intValue());
                excelVO.setTotalAmount(Optional.ofNullable(itemDTO.getDeclarePrice())
                        .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                excelVO.setFirstUnitAmount(Optional.ofNullable(itemDTO.getFirstUnitQfy())
                        .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                if (Objects.nonNull(itemDTO.getSecondUnitQfy()) && itemDTO.getSecondUnitQfy().compareTo(BigDecimal.ZERO) > 0) {
                    excelVO.setSecondUnitAmount(itemDTO.getSecondUnitQfy()
                            .multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                }
                excelVO.setFirstUnit(itemDTO.getFirstUnit());
                excelVO.setSecondUnit(itemDTO.getSecondUnit());
                excelVO.setOriginCountry(itemDTO.getOriginCountry());
                excelVO.setHsCode(itemDTO.getHsCode());
                excelVO.setGoodsName(itemDTO.getGoodsName());
                excelVO.setGoodsModel(itemDTO.getGoodsModel());
                excelVO.setCurrCode(itemDTO.getCurrency());
                excelVO.setGoodsUnit(itemDTO.getDeclareUnit());
                excelVO.setProductId(itemDTO.getProductId());
                excelVO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                excelVO.setUnitPrice(itemDTO.getDeclarePrice());
                excelVO.setGoodsSource(itemDTO.getGoodsSource());
                excelVO.setLineNo(String.valueOf(itemDTO.getSerialNumber()));
                excelVO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                excelVO.setProductId(itemDTO.getProductId());
                excelVO.setOrderSeqNo(String.valueOf(itemDTO.getSerialNumber()));
                excelVO.setHsCode(itemDTO.getHsCode());
                excelVO.setGoodsName(itemDTO.getGoodsName());
                excelVO.setGoodsModel(itemDTO.getGoodsModel());
                excelVO.setCurrCode(itemDTO.getCurrency());
                excelVO.setGoodsUnit(itemDTO.getDeclareUnit());
                excelVO.setFirstUnit(itemDTO.getFirstUnit());
                excelVO.setSecondUnit(itemDTO.getSecondUnit());
                excelVO.setTotalCount(itemDTO.getDeclareUnitQfy().intValue());
                excelVO.setFirstUnitAmount(itemDTO.getFirstUnitQfy());
                excelVO.setSecondUnitAmount(itemDTO.getSecondUnitQfy());
                excelVO.setUnitPrice(itemDTO.getDeclarePrice());
                excelVO.setTotalAmount(itemDTO.getDeclareTotalPrice());
                excelVO.setOriginCountry(itemDTO.getOriginCountry());
                excelVO.setAimCountry(itemDTO.getDestinationCountry());
                excelVO.setGrossWeight(itemDTO.getGrossWeight());
                excelVO.setNetWeight(itemDTO.getNetWeight());
                excelVO.setFreeType(itemDTO.getAvoidTaxMethod());
                excelVO.setVersion(itemDTO.getVersion());
                excelVO.setDangerousFlag(itemDTO.getDangerousFlag());
                excelVO.setNote(itemDTO.getRemark());
                excelVO.setGoodsSource(itemDTO.getGoodsSource());
                excelVOList.add(excelVO);
                lineNo++;
            }
        } else if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(endorsementOrderDTO.getBussinessType()))
        {
            if(endorsementOrderDTO.getInventoryOrderId()!=null) {
                InventoryOrderInfoDTO inventoryOrderInfoDTO =  inventoryOrderInfoService.findById(endorsementOrderDTO.getInventoryOrderId());
                List<InventoryOrderItemDTO> listInventoryOrderItemDTO  =  inventoryOrderInfoService.findListByInvenOrderId(endorsementOrderDTO.getInventoryOrderId());
                if (EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode().equals(endorsementOrderDTO.getBussinessType())) {
                    List<InventoryOrderRelationDTO> relationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(inventoryOrderInfoDTO.getId());
                    List<String> mailNoList = relationDTOList.stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.toList());
                    List<RefundOrderInfoDto> refundOrderInfoDtoList = orderCCallConfig.isOrderCCall(this.getClass()) ? refundOrderRpc.findByMailNoList(mailNoList) : refundOrderService.findByMailNoList(mailNoList);
                    for (RefundOrderInfoDto refundOrderInfoDto : refundOrderInfoDtoList) {
                        EndorsementOrderExcelVO orderExcelVO = new EndorsementOrderExcelVO();
                        orderExcelVO.setInvtNo(refundOrderInfoDto.getInvtNo());
                        orderExcelVO.setDeclareOrderNo(refundOrderInfoDto.getRefDeclareNo());
                        orderExcelVO.setLogisticsNo(refundOrderInfoDto.getMailNo());
                        inventoryList.add(orderExcelVO);
                    }
                }

                int lineNo = 1;
                for(InventoryOrderItemDTO itemDTO:listInventoryOrderItemDTO) {
                    EndorsementExcelVO excelVO = new EndorsementExcelVO();
                    excelVO.setOrderSeqNo(String.valueOf(lineNo));
                    excelVO.setTotalCount(itemDTO.getDeclareUnitQfy().intValue());
                    excelVO.setTotalAmount(Optional.ofNullable(itemDTO.getDeclarePrice())
                            .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                    excelVO.setFirstUnitAmount(Optional.ofNullable(itemDTO.getFirstUnitQfy())
                            .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                    if (Objects.nonNull(itemDTO.getSecondUnitQfy()) && itemDTO.getSecondUnitQfy().compareTo(BigDecimal.ZERO) > 0) {
                        excelVO.setSecondUnitAmount(itemDTO.getSecondUnitQfy()
                                .multiply(itemDTO.getDeclareUnitQfy()).setScale(5, RoundingMode.HALF_UP));
                    }
                    excelVO.setFirstUnit(itemDTO.getFirstUnit());
                    excelVO.setSecondUnit(itemDTO.getSecondUnit());
                    excelVO.setOriginCountry(itemDTO.getOriginCountry());
                    excelVO.setHsCode(itemDTO.getHsCode());
                    excelVO.setProductId(itemDTO.getProductId());
                    excelVO.setGoodsUnit(itemDTO.getUnit());
                    excelVO.setGoodsName(itemDTO.getRecordProductName());
                    if (Objects.nonNull(itemDTO.getTotalNetWeight())) {
                        excelVO.setNetWeight(itemDTO.getTotalNetWeight());
                    } else {
                        excelVO.setNetWeight(Optional.ofNullable(itemDTO.getNetweight())
                                .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                    }
                    if (Objects.nonNull(itemDTO.getTotalGrossWeight())) {
                        excelVO.setGrossWeight(itemDTO.getTotalGrossWeight());
                    } else {
                        excelVO.setGrossWeight(Optional.ofNullable(itemDTO.getGrossWeight())
                                .orElse(BigDecimal.ZERO).multiply(itemDTO.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP));
                    }
                    excelVO.setCurrCode(itemDTO.getCurrency());
                    excelVO.setGoodsModel(itemDTO.getGoodsModel());
                    excelVO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                    excelVO.setUnitPrice(itemDTO.getDeclarePrice());
                    //增加危化品标志
                    if (!StringUtils.isEmpty(itemDTO.getDangerousFlag()))
                    {
                        if (itemDTO.getDangerousFlag().equals("0"))
                        {
                            excelVO.setDangerousFlag("否");
                        }
                        else if (itemDTO.getDangerousFlag().equals("1"))
                        {
                            excelVO.setDangerousFlag("是");
                        }
                    }
                    excelVO.setGoodsSource(itemDTO.getGoodsSource());
                    //增加序号
                    excelVO.setLineNo(String.valueOf(lineNo));
                    excelVOList.add(excelVO);
                    lineNo++;
                }
            }
        }
        else {

            List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(endorsementOrderDTO.getId());
            for (ExportItemDTO itemDTO : itemDTOList) {
                CustomsInventoryDTO customsInventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.findBySnSection(itemDTO.getCustomsInventorySn()) : customsInventoryService.findBySnSection(itemDTO.getCustomsInventorySn());
                EndorsementOrderExcelVO orderExcelVO = new EndorsementOrderExcelVO();
                orderExcelVO.setInvtNo(customsInventoryDTO.getInventoryNo());
                orderExcelVO.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                orderExcelVO.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryList.add(orderExcelVO);
            }
            List<EndorsementItemDTO> endorsementItemDTOList;
            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                    || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())){
                endorsementItemDTOList = endorsementService.preItemView(endorsementOrderDTO.getId());
            }else{
                endorsementItemDTOList = endorsementService.listItemById(endorsementOrderDTO.getId());
            }
            int lineNo = 1;
            for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(endorsementOrderDTO.getAccountBookId(), endorsementItemDTO.getProductId());
                if (goodsRecordDTO == null) {
                    continue;
                }
                //过滤非保赠品
                List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(endorsementItemDTO.getItemTag());
                if (orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode())) {
                    continue;
                }
                EndorsementExcelVO excelVO = new EndorsementExcelVO();
                excelVO.setUnitPrice(goodsRecordDTO.getDeclarePrice());
                excelVO.setTotalCount(endorsementItemDTO.getDeclareUnitQfy().intValue());
                excelVO.setTotalAmount(excelVO.getUnitPrice().multiply(new BigDecimal(excelVO.getTotalCount())));
                excelVO.setGrossWeight(endorsementItemDTO.getGrossWeight());
                excelVO.setNetWeight(endorsementItemDTO.getNetWeight());
                excelVO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount().multiply(endorsementItemDTO.getDeclareUnitQfy()));
                if (Objects.nonNull(goodsRecordDTO.getSecondUnitAmount())) {
                    excelVO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount().multiply(endorsementItemDTO.getDeclareUnitQfy()));
                }
                excelVO.setGoodsName(endorsementItemDTO.getGoodsName());
                InventoryOrderItemDTO inventoryOrderItemDTOtemp = inventoryOrderInfoService.findByEndorsementIdAndProductId(endorsementItemDTO.getEndorsementId(),endorsementItemDTO.getProductId());
                if (inventoryOrderItemDTOtemp != null && !StringUtils.isEmpty(inventoryOrderItemDTOtemp.getDangerousFlag())) {
                    //增加危化品标志
                    if (inventoryOrderItemDTOtemp.getDangerousFlag().equals("0")) {
                        excelVO.setDangerousFlag("否");
                    } else if (inventoryOrderItemDTOtemp.getDangerousFlag().equals("1")) {
                        excelVO.setDangerousFlag("是");
                    }
                }
                //增加序号
                excelVO.setLineNo(String.valueOf(lineNo));

                CustomsBookItemDTO bookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(
                        endorsementOrderDTO.getAccountBookId(),
                        endorsementItemDTO.getGoodsSeqNo(),
                        endorsementItemDTO.getProductId());
                if (bookItemDTO == null) {
                    excelVO.setOriginCountry(goodsRecordDTO.getOriginCountry());
                    excelVO.setFirstUnit(goodsRecordDTO.getFirstUnit());
                    excelVO.setSecondUnit(goodsRecordDTO.getSecondUnit());
                    excelVO.setGoodsUnit(goodsRecordDTO.getDeclareUnit());
                    excelVO.setGoodsModel(goodsRecordDTO.getModel());
                    excelVO.setProductId(goodsRecordDTO.getProductId());
                    excelVO.setHsCode(goodsRecordDTO.getHsCode());
//                    excelVO.setGoodsName(goodsRecordDTO.getGoodsRecordName());
                    excelVO.setGoodsSource(goodsRecordDTO.getGoodsSource() == null ? "" : String.valueOf(goodsRecordDTO.getGoodsSource()));
                } else {
                    excelVO.setOriginCountry(bookItemDTO.getOriginCountry());
                    excelVO.setFirstUnit(bookItemDTO.getFirstUnit());
                    excelVO.setSecondUnit(bookItemDTO.getSecondUnit());
                    excelVO.setGoodsUnit(bookItemDTO.getGoodsUnit());
                    excelVO.setCurrCode(bookItemDTO.getCurrCode());
                    excelVO.setGoodsModel(bookItemDTO.getGoodsModel());
                    excelVO.setProductId(bookItemDTO.getProductId());
                    excelVO.setGoodsSeqNo(bookItemDTO.getGoodsSeqNo());
                    excelVO.setHsCode(bookItemDTO.getHsCode());
                    excelVO.setUnitPrice(bookItemDTO.getDeclarePrice());
//                    excelVO.setGoodsName(bookItemDTO.getGoodsName());
                    excelVO.setGoodsSource(bookItemDTO.getGoodsSource());
                }
                excelVOList.add(excelVO);
                lineNo++;
            }
        }
        List<Map<String, Object>> lists = new ArrayList<>();
        //sheet1
        Map<String, Object> temp1 = createOneSheet("表体(成品)", EndorsementExcelVO.class, excelVOList);
        lists.add(temp1);
        //sheet2
        if (!CollectionUtils.isEmpty(inventoryList)) {
            Map<String, Object> temp2 = createOneSheet("保税电商清单", EndorsementOrderExcelVO.class, inventoryList);
            lists.add(temp2);
        }
        Workbook workbook = mutiSheet(lists);
        return workbook;
    }

    public static Workbook mutiSheet(List<Map<String, Object>> mapListList){
        return ExcelExportUtil.exportExcel(mapListList, ExcelType.XSSF);
    }

    public static Map<String, Object> createOneSheet(String sheetName,Class<?> clazz,List<?> data){
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(sheetName);
        exportParams.setType(ExcelType.XSSF);

        Map<String, Object> map = new HashMap<>();
        map.put("title",exportParams);//new ExportParams("title"+i, "sheetName"+i, ExcelType.XSSF)
        map.put("entity", clazz);
        map.put("data",data);
        return map;
    }

    public FileModel build(Object param) {
        EndorsementSearch search = (EndorsementSearch) param;
        search.setPageSize(30000);
//        ListVO<EndorsementDTOV2> paging = null;
//        if (!StringUtils.isEmpty(search.getMailNo())){
//            List<ExportItemDTO> checklistOrderDTOList = exportOrderService.listItemByMailNos(new HashSet<>(Lists.newArrayList(search.getMailNo())));
//            Set<Long> idSet = checklistOrderDTOList.stream().map(ExportItemDTO::getEndorsementOrderId).collect(Collectors.toSet());
//            if (CollectionUtils.isEmpty(idSet)){
//                paging = ListVOBuilder.buildEmptyListVO(EndorsementDTOV2.class);
//            }else {
//                search.setIdSet(idSet);
//            }
//        }
//        if (paging == null){
        ListVO<EndorsementDTOV2> paging = endorsementService.paging(search);
//        }

        List<String> idList = paging.getDataList().stream().map((EndorsementDTO dto) -> {return dto.getId().toString();}).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idList) && idList.size() == 1){
            String filename = "核注清单导出" + DateTime.now().toString("yyMMddHHmm") + ".xlsx";
            Workbook workbook = this.singleExport(Long.valueOf(idList.get(0)));
            ByteArrayOutputStream outputStream = null;
            outputStream = new ByteArrayOutputStream();
            try {
                workbook.write(outputStream);
            } catch (IOException e) {
                throw new ArgsErrorException("导出异常");
            }
            return new FileModel(filename,outputStream.toByteArray());
        }
        List<String> filePaths = new ArrayList<>();
        //生成的ZIP文件名为Demo.zip
        String tmpFileName = "核注清单导出" + DateTime.now().toString("yyMMddHHmm") +".zip";
        // zip文件路径
        String strZipPath = DownLoadUtil.FilePath + tmpFileName;
        filePaths.add(strZipPath);
        try {
            //生成excel文件集合
            File targetFile = new File(DownLoadUtil.FilePath);
            if(!targetFile.exists()){
                targetFile.mkdirs();
            }
            //创建zip输出流
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(strZipPath));
            //声明文件集合用于存放excel文件
            List<File> fileList = new ArrayList<File>();
            for (String idStr : idList) {
                Long id = Long.valueOf(idStr);
                EndorsementDTO endorsementOrderDTO = endorsementService.findById(id);
                // 生成随机文件名
                String filename = DownLoadUtil.FilePath + endorsementOrderDTO.getSn() + ".xlsx";
                // 将文件路径保存
                fileList.add(DownLoadUtil.creatFile(filename));
                filePaths.add(filename);
                {
                    Workbook workbook = this.buildExport(endorsementOrderDTO);
                    // 导出
                    FileOutputStream excelOut = new FileOutputStream(filename);
                    workbook.write(excelOut);
                    excelOut.flush();
                    excelOut.close();
                }
            }
            byte[] buffer = new byte[1024];
            //将excel文件放入zip压缩包
            for (int i = 0; i < fileList.size(); i++) {
                File file = fileList.get(i);
                FileInputStream fis = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(file.getName()));
                //设置压缩文件内的字符编码，不然会变成乱码
//                out.setEncoding("GBK");
                int len;
                // 读入需要下载的文件的内容，打包到zip文件
                while ((len = fis.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
                out.closeEntry();
                fis.close();
            }
            out.close();
            //下载zip文件
            return new FileModel(tmpFileName,getBytes(strZipPath));
        } catch (Exception e) {
            // 下载失败删除生成的文件
            DownLoadUtil.deleteFile(filePaths);
            log.error("文件下载出错", e);
        }
        return null;
    }
}
