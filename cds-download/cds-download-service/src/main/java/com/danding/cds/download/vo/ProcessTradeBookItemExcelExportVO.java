package com.danding.cds.download.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 业务申报表分页VO
 */
@Data
public class ProcessTradeBookItemExcelExportVO implements Serializable {

    @Excel(name = "序号")
    private Integer seqNo;

    @Excel(name = "料号")
    private String productId;

    @Excel(name = "商品编码")
    private String hsCode;

    @Excel(name = "商品名称")
    private String goodsName;

    @Excel(name = "规格型号")
    private String goodsModel;

    @Excel(name = "申报计量单位")
    private String declareUnitDesc;
    private String declareUnit;

    @Excel(name = "法定量单位")
    private String legalUnitDesc;
    private String legalUnit;

    @Excel(name = "法定第二计量单位")
    private String legalSecondUnitDesc;
    private String legalSecondUnit;

    @Excel(name = "申报数量")
    private Long declareQty;

    @Excel(name = "申报单价")
    private BigDecimal declareUnitPrice;

    @Excel(name = "币制")
    private String currencyDesc;
    private String currency;

    @Excel(name = "征免方式")
    private String dutyExemptionMethodDesc;
    private String dutyExemptionMethod;

    @Excel(name = "企业执行标志")
    private String companyExecutionFlagDesc;
    private Integer companyExecutionFlag;

    /**
     * 修改标志，默认3-增加
     */
    @Excel(name = "修改标志")
    private String modifyFlagDesc;
    private String modifyFlag;

    @Excel(name = "批准最大余数量")
    private BigDecimal maxRemainQty;

    @Excel(name = "备注")
    private String remark;

    @Excel(name = "重点商品标识")
    private String focusMarkDesc;
    private Integer focusMark;

    @Excel(name = "国别(地区)")
    private String countryRegionDesc;
    private String countryRegion;
}
