package com.danding.cds;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.danding.cds.download.api.vo.GoodsRecordExportVO;

import java.io.File;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class ExcelTemplateTest implements Serializable {
    public static void main(String[] args) {
        // 测试模板，将excel文件生成到本地
        File file = new File("D:\\商品备案导出.xlsx");
        // 如果文件不存在创建文件
        if (!file.exists()) {
            try {
                file.createNewFile();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        ExcelWriter excelWriter = EasyExcel.write(file).build();
        WriteSheet writeSheet = EasyExcel.writerSheet("商品备案").head(GoodsRecordExportVO.class).build();
        List<GoodsRecordExportVO> list = new ArrayList<>();
        excelWriter.write(list, writeSheet);
        excelWriter.finish();


    }
}
