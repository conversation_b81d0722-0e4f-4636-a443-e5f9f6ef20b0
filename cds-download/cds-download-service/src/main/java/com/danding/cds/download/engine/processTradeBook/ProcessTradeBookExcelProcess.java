package com.danding.cds.download.engine.processTradeBook;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.download.api.dto.DownloadReportTaskRequest;
import com.danding.cds.download.api.dto.DownloadSubmit;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.engine.base.BaseDownloadProcess;
import com.danding.cds.download.engine.base.DownloadRunnable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ProcessTradeBookExcelProcess extends BaseDownloadProcess {

    @Autowired
    private ProcessTradeBookExcelService processTradeBookExcelService;

    @Override
    protected TraceDataRunnable getTask(DownloadReportTaskRequest reportTaskRequest, DownloadSubmit submit) {
        return new DownloadRunnable(processTradeBookExcelService, reportTaskRequest, submit);
    }

    @Override
    public ReportType getReportType() {
        return ReportType.PROCESS_BOOK_ITEM_FOR_EXCEL;
    }
}
