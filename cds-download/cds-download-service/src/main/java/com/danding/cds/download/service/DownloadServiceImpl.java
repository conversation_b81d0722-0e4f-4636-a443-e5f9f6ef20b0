package com.danding.cds.download.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.download.api.dto.DownloadDTO;
import com.danding.cds.download.api.dto.DownloadSearchCondition;
import com.danding.cds.download.api.dto.DownloadSubmit;
import com.danding.cds.download.api.service.DownloadService;
import com.danding.cds.download.entity.DownloadDO;
import com.danding.cds.download.mapper.DownloadMapper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@DubboService
@RefreshScope
public class DownloadServiceImpl implements DownloadService {

    @Autowired
    private DownloadMapper downloadMapper;

    @Value("${download.condition.sub.size:}")
    private Integer downloadConditionSubSize;

    @Override
    @PageSelect
    public ListVO<DownloadDTO> paging(DownloadSearchCondition condition) throws ArgsErrorException {
        Example example = new Example(DownloadDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(condition.getReportName())) {
            criteria.andLike("reportName", "%" + condition.getReportName() + "%");
        }
        if (!LongUtil.isNone(condition.getFinishTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("finishTime", new DateTime(condition.getFinishTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(condition.getFinishTimeTo())) {
            criteria.andLessThanOrEqualTo("finishTime", new DateTime(condition.getFinishTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        criteria.andEqualTo("deleted", 0);
        //排序
        example.setOrderByClause("create_time DESC");
        List<DownloadDO> list = downloadMapper.selectByExample(example);
        ListVO<DownloadDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), DownloadDTO.class));
        // 分页
        PageInfo<DownloadDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public Long upset(DownloadSubmit submit) throws ArgsErrorException {
        DownloadDO downloadDO = new DownloadDO();
        BeanUtils.copyProperties(submit, downloadDO);
        if (Objects.nonNull(downloadDO.getConditions()) && Objects.nonNull(downloadConditionSubSize)
                && downloadDO.getConditions().length() > downloadConditionSubSize) {
            String conditions = downloadDO.getConditions();
            downloadDO.setConditions(conditions.substring(0, downloadConditionSubSize));
        }
        if (submit.getId() == null || submit.getId() == 0) {
            downloadDO.setId(null);
            UserUtils.setCreateAndUpdateBy(downloadDO);
            downloadMapper.insertSelective(downloadDO);
            return downloadDO.getId();
        } else {
            DownloadDTO old = this.findById(submit.getId());
            if (old == null) {
                throw new ArgsErrorException("ID不正确");
            }
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(downloadDO);
            }
            downloadDO.setUpdateTime(new Date());
            downloadMapper.updateByPrimaryKeySelective(downloadDO);
            return old.getId();
        }
    }

    @Override
    public Long updateDeleted(Long id) {
        DownloadDO downloadDO = new DownloadDO();
        downloadDO.setId(id);
        downloadDO.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(downloadDO);
        }
        downloadDO.setUpdateTime(new Date());
        downloadMapper.updateByPrimaryKeySelective(downloadDO);
        return id;
    }

    @Override
    public Long updateDownLoadCount(Long id, Integer count) {
        DownloadDO downloadDO = new DownloadDO();
        downloadDO.setId(id);
        downloadDO.setDownloadCount(count);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(downloadDO);
        }
        downloadDO.setUpdateTime(new Date());
        downloadMapper.updateByPrimaryKeySelective(downloadDO);
        return id;
    }

    @Override
    public Long updateDownloadStatus(Long id, String downloadPath, Integer status, String remark, Exception e) {
        DownloadDO taskForUpdate = new DownloadDO();
        return null;
    }

    @Override
    public DownloadDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            DownloadDO downloadDO = downloadMapper.selectByPrimaryKey(id);
            return this.buildDTO(downloadDO);
        }
    }

    private DownloadDTO buildDTO(DownloadDO entity) {
        if (entity == null) {
            return null;
        } else {
            return JSON.parseObject(JSON.toJSONString(entity), DownloadDTO.class);
        }
    }

    @Override
    public List<DownloadDTO> queryByStatus(Long userId, String reportType, Integer downLoadStatus) {
        DownloadDO downloadDO = new DownloadDO();
        //TODO userId等网关接入
        //downloadDO.setCreateBy();
        downloadDO.setStatus(downLoadStatus);
        downloadDO.setReportType(reportType);
        List<DownloadDO> list = downloadMapper.select(downloadDO);
        if (list.size() > 0) {
            return JSON.parseArray(JSON.toJSONString(list), DownloadDTO.class);
        } else {
            return new ArrayList<>();
        }
    }
}
