package com.danding.cds.download.service;

import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Supplier;

/**
 * @program: cds-center
 * @description: future
 * @author: 潘本乐（Belep）
 * @create: 2021-08-30 17:16
 **/
@Service
public class FutureService {

    @Resource(name = "downloadThreadExecutor")
    private ThreadPoolTaskExecutor downloadThreadExecutor;

    public void setThreadPoolTaskExecutor(ThreadPoolTaskExecutor threadPoolTaskExecutor) {
        downloadThreadExecutor = threadPoolTaskExecutor;
    }

    /**
     * Returns a new CompletableFuture that is asynchronously completed
     * by a task running in the given executor after it runs the given
     * action.
     *
     * @param runnable the action to run before completing the
     *                 returned CompletableFuture
     * @return the new CompletableFuture
     */
    public CompletableFuture<Void> runAsync(Runnable runnable) {
        return CompletableFuture.runAsync(runnable, downloadThreadExecutor);
    }

    /**
     * Returns a new CompletableFuture that is asynchronously completed
     * by a task running in the given executor with the value obtained
     * by calling the given Supplier.
     *
     * @param supplier a function returning the value to be used
     *                 to complete the returned CompletableFuture
     * @param <U>      the function's return type
     * @return the new CompletableFuture
     */
    public <U> CompletableFuture<U> supplyAsync(Supplier<U> supplier) {
        return CompletableFuture.supplyAsync(supplier, downloadThreadExecutor);
    }

    /**
     * 拼接Future任务，并获取
     *
     * @param futures
     * @throws ExecutionException
     * @throws InterruptedException
     */
    public void allOfAndGet(List<CompletableFuture<Void>> futures) throws ExecutionException, InterruptedException {

        if (CollectionUtils.isEmpty(futures)) {
            return;
        }
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[futures.size()])).get();
    }

}
