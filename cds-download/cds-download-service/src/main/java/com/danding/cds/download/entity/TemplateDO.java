package com.danding.cds.download.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;

@EqualsAndHashCode(callSuper = true)
@Table(name = "ccs_template")
@Data
public class TemplateDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -4984397672275899725L;

    /**
     * 下载次数 默认0
     */
    @Column(name = "template_name")
    private String templateName;
    /**
     * 下载次数 默认0
     */
    @Column(name = "download_count")
    private Integer downloadCount;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;
    /**
     * 备注
     */
    private String remark;
}
