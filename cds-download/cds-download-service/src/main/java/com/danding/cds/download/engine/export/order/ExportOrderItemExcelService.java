package com.danding.cds.download.engine.export.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.download.api.vo.ExportItemExcelVO;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.exportorder.api.dto.ExportOrderItemSearch;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.logistics.api.common.response.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 出库单表体导出
 */
@Component
@Slf4j
public class ExportOrderItemExcelService extends BaseExcelService {

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private CustomsInventoryRpc customsInventoryService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private ExpressService expressService;

    @Override
    public Class<?> getModelClazz() {
        return ExportItemExcelVO.class;
    }

    @Override
    public List selectListForExcelExport(Object o, int i) {
        log.info("export param={}, currentPage={}", JSON.toJSONString(o), i);
        ExportOrderItemSearch param = (ExportOrderItemSearch) o;
        param.setCurrentPage(i);
        param.setPageSize(5000);
        ListVO<ExportItemDTO> paging = exportOrderService.itemPaging(param);
        if (paging.getDataList().isEmpty()) {
            return new ArrayList<>();
        }
        log.info("export param={}, currentPage={}, total={}", JSON.toJSONString(o), i, paging.getPage().getTotalCount());

        Map<Long, String> expressMap = new HashMap<>();
        Map<Long, String> bookMap = new HashMap<>();
        List<ExportItemDTO> dataList = paging.getDataList();
        List<String> customsInventorySnList = dataList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
        List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.findBySnList(customsInventorySnList);
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = customsInventoryDTOList.stream()
                .collect(Collectors.toMap(CustomsInventoryDTO::getSn, Function.identity(), (v1, v2) -> v1));
        return dataList.stream().map((ExportItemDTO dto) -> {
            ExportItemExcelVO vo = new ExportItemExcelVO();
            vo.setMailNo(dto.getMailNo());
            vo.setExpressName(expressMap.computeIfAbsent(dto.getExpressId(), (Long expressId) -> expressService.findById(expressId).getName()));
            if (customsInventoryDTOMap.containsKey(dto.getCustomsInventorySn())) {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOMap.get(dto.getCustomsInventorySn());
                vo.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                vo.setInventoryNo(customsInventoryDTO.getInventoryNo());
            }
            vo.setAccountBookNo(bookMap.computeIfAbsent(dto.getAccountBookId(),
                    (Long accountBookId) -> customsBookService.findByIdV2(accountBookId).getBookNo()));
            return vo;
        }).collect(Collectors.toList());
    }
}
