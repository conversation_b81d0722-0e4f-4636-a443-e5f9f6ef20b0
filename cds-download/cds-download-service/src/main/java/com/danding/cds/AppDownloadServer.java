package com.danding.cds;

import com.danding.component.mybatis.common.interceptor.plus.MybatisPlusTenantLineInterceptorConfiguration;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.context.annotation.Import;
import org.springframework.util.StringUtils;
import tk.mybatis.spring.annotation.MapperScan;

@EnableDubbo
@MapperScan(basePackages = {"com.danding.cds.**.mapper"})
@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class},
        scanBasePackages = {
                "com.danding.cds",
                "com.danding.logistics" // 这个包扫描是为了添加分页查询APO支持
        })
@Import(MybatisPlusTenantLineInterceptorConfiguration.class)
public class AppDownloadServer {
    public static void main(String[] args) throws Exception {
        if (!StringUtils.isEmpty(System.getProperty("env")) && "DEV".equals(System.getProperty("env").toUpperCase())) {
            if (StringUtils.isEmpty(System.getProperty("local"))) {
                System.setProperty("local", "true");
            }
        }
        // 解决SpringBoot netty与ES netty 相关jar冲突
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        SpringApplication.run(AppDownloadServer.class, args);
    }
}
