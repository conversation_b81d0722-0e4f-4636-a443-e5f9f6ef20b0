package com.danding.cds.download.entity.es;

import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.List;

/**
 * <AUTHOR>
 * @description 备案实体仓关联
 * @date 2022/6/2
 */
@Data
public class RecordWarehouseEsDO extends EsModel {
    /**
     * 商品备案id
     */
    @Field(type = FieldType.Long, store = true)
    private Long recordId;

    /**
     * 料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productId;

    /**
     * 实体仓sn
     */
    @Field(type = FieldType.Keyword, store = true)
    private String warehouseSn;

    /**
     * 账册id
     */
    @Field(type = FieldType.Long, store = true)
    private Long customsBookId;

    /**
     * 账册号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsBookNo;

    /**
     * 关联备案-口岸表id
     */
    @Field(type = FieldType.Long, store = true)
    private Long recordCustomsId;

    /**
     * 口岸code
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsCode;

    /**
     * 口岸
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customs;

    /**
     * erp实体仓名称
     */
    @Field(type = FieldType.Keyword, store = true)
    private String erpWarehouseName;

    /**
     * wms仓编码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String wmsWarehouseCode;

    /**
     * 外部料号 (已作废)
     */
    @Deprecated
    @Field(type = FieldType.Keyword, store = true)
    private String externalProductId;

    /**
     * 通关料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private List<String> customsDeclareProductId;

    @Field(type = FieldType.Integer, store = true)
    private Integer deleted;
}
