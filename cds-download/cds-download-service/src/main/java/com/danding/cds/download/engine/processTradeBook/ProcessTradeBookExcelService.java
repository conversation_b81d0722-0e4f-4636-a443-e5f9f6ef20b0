package com.danding.cds.download.engine.processTradeBook;

import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.download.vo.ProcessTradeBookItemExcelExportVO;
import com.danding.cds.endorsement.api.enums.EndorsementModfMarkEnums;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.enums.ProcessTradeBookCompanyExecutionFlagEnums;
import com.danding.cds.v2.bean.enums.ProcessTradeBookFocusMarkEnums;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.component.common.api.common.response.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ProcessTradeBookExcelService extends BaseExcelService {

    @DubboReference
    private ProcessTradeBookService processTradeBookService;

    @DubboReference
    private CustomsDictionaryService dictionaryService;

    @Override
    public Class<?> getModelClazz() {
        return ProcessTradeBookItemExcelExportVO.class;
    }

    @Override
    public List selectListForExcelExport(Object o, int i) {
        ProcessTradeBookItemSearch search = (ProcessTradeBookItemSearch) o;
        search.setCurrentPage(i);
        search.setPageSize(500);
        try {
            ListVO<ProcessTradeBookItemDTO> paging = processTradeBookService.itemPaging(search);
            log.debug("BizDeclareFormExcelService export={}", paging);
            return buildExportVO(paging.getDataList());
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<ProcessTradeBookItemExcelExportVO> buildExportVO(List<ProcessTradeBookItemDTO> list) {
        Map<String, String> uomMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        Map<String, String> currencyMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> countryMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        return list.stream().map(dto -> {
            ProcessTradeBookItemExcelExportVO vo = ConvertUtil.beanConvert(dto, ProcessTradeBookItemExcelExportVO.class);
            vo.setLegalUnitDesc(uomMap.getOrDefault(dto.getLegalUnit(), ""));
            vo.setLegalSecondUnitDesc(uomMap.getOrDefault(dto.getLegalSecondUnit(), ""));
            vo.setDeclareUnitDesc(uomMap.getOrDefault(dto.getDeclareUnit(), ""));
            vo.setCountryRegionDesc(countryMap.getOrDefault(dto.getCountryRegion(), ""));
            vo.setCurrencyDesc(currencyMap.getOrDefault(dto.getCurrency(), ""));
            vo.setDutyExemptionMethodDesc(Objects.equals("3", dto.getDutyExemptionMethod()) ? "全免" : "");
            vo.setCompanyExecutionFlagDesc(ProcessTradeBookCompanyExecutionFlagEnums.getDesc(dto.getCompanyExecutionFlag()));
            vo.setModifyFlagDesc(EndorsementModfMarkEnums.getDesc(dto.getModifyFlag()));
            vo.setFocusMarkDesc(ProcessTradeBookFocusMarkEnums.getDesc(dto.getFocusMark()));
            return vo;
        }).collect(Collectors.toList());
    }
}
