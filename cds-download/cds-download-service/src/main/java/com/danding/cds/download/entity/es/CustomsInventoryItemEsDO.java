package com.danding.cds.download.entity.es;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 海关清单项ES-DO
 * @author: 潘本乐（Belep）
 * @create: 2022-06-06 17:53
 **/
@Data
public class CustomsInventoryItemEsDO implements Serializable {

    private Long id;
    /**
     * 料号
     */
    private String productId;
    /**
     * 序号
     */
    private String goodsSeqNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 数量
     */
    private Integer count;
    /**
     * 单价
     */
    private Double unitPrice;

    private Integer itemTag;

    private String hsCode;
}
