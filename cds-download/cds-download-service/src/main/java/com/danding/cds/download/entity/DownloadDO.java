package com.danding.cds.download.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Table(name = "ccs_download")
@Data
public class DownloadDO extends BaseDO implements Serializable {
    private static final long serialVersionUID = -2572855694771817438L;

    /**
     * 报表类型
     */
    @Column(name = "report_type")
    private String reportType;

    /**
     * 报表名称
     */
    @Column(name = "report_name")
    private String reportName;

    /**
     * 下载次数 默认0
     */
    @Column(name = "download_count")
    private Integer downloadCount;

    /**
     * 文件路径
     */
    @Column(name = "file_path")
    private String filePath;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 完成时间
     */
    @Column(name = "start_time")
    private Date startTime;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private Date finishTime;

    /**
     * 筛选条件
     */
    private String conditions;

    /**
     * 备注
     */
    private String remark;
}
