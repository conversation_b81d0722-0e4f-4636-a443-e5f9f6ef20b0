package com.danding.cds.download.entity.es;

import com.danding.cds.item.api.dto.RecordCustomsSubmitTypeEnum;
import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 备案口岸关联
 * @date 2022/6/2
 */
@Data
public class RecordCustomsEsDO extends EsModel {

    /**
     * 商品备案id
     */
    @Field(type = FieldType.Long, store = true)
    private Long recordId;
    /**
     * 料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productId;
    /**
     * 口岸code
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customsCode;

    /**
     * 口岸
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customs;
    /**
     * hs编码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    @Field(type = FieldType.Keyword, store = true)
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    @Field(type = FieldType.Double, store = true)
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    @Field(type = FieldType.Keyword, store = true)
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    @Field(type = FieldType.Double, store = true)
    private BigDecimal secondUnitAmount;

    /**
     * 驳回原因
     */
    @Field(type = FieldType.Keyword, store = true)
    private String reason;

    /**
     * 状态 待审核0 /通过 1
     * 1, "待审核";2, "备案完成";4, "审核驳回"  GoodsRecordStatusEnum
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer status;

    /**
     * 状态 新品1 /更新 2
     * {@link RecordCustomsSubmitTypeEnum}
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer submitType;

    /**
     * 备案提交时间
     */
    @Field(type = FieldType.Date, store = true)
    private Date submitTime;

    /**
     * 备案提交时间
     */
    @Field(type = FieldType.Long, store = true)
    private Long submitDateTime;

    @Field(type = FieldType.Integer, store = true)
    private Integer deleted;

}
