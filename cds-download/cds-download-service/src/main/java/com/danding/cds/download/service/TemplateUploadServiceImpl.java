package com.danding.cds.download.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.download.api.dto.TemplateSearchCondition;
import com.danding.cds.download.api.dto.TemplateSubmit;
import com.danding.cds.download.api.dto.TemplateUploadDTO;
import com.danding.cds.download.api.service.TemplateUploadService;
import com.danding.cds.download.entity.TemplateDO;
import com.danding.cds.download.mapper.TemplateMapper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Objects;

@DubboService
public class TemplateUploadServiceImpl implements TemplateUploadService {

    @Autowired
    private TemplateMapper templateMapper;

    @Override
    @PageSelect
    public ListVO<TemplateUploadDTO> paging(TemplateSearchCondition condition) {
        Example example = new Example(TemplateDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotEmpty(condition.getTemplateName())) {
            criteria.andLike("templateName", "%" + condition.getTemplateName() + "%");
        }
        criteria.andEqualTo("deleted", 0);
        //排序
        example.setOrderByClause("create_time DESC");
        List<TemplateDO> list = templateMapper.selectByExample(example);
        ListVO<TemplateUploadDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), TemplateUploadDTO.class));
        // 分页
        PageInfo<TemplateDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public Long upset(TemplateSubmit submit) {
        TemplateDO downloadDO = new TemplateDO();
        BeanUtils.copyProperties(submit, downloadDO);
        downloadDO.setId(null);
        UserUtils.setCreateAndUpdateBy(downloadDO);
        templateMapper.insertSelective(downloadDO);
        return downloadDO.getId();
    }

    @Override
    public Long updateDeleted(Long id) {
        TemplateDO downloadDO = new TemplateDO();
        downloadDO.setId(id);
        downloadDO.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(downloadDO);
        }
        downloadDO.setUpdateTime(new Date());
        templateMapper.updateByPrimaryKeySelective(downloadDO);
        return id;
    }

    @Override
    public Long updateDownLoadCount(Long id, Integer count) {
        TemplateDO downloadDO = new TemplateDO();
        downloadDO.setId(id);
        downloadDO.setDownloadCount(count);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(downloadDO);
        }
        downloadDO.setUpdateTime(new Date());
        templateMapper.updateByPrimaryKeySelective(downloadDO);
        return id;
    }

    @Override
    public TemplateUploadDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        } else {
            TemplateDO templateDO = templateMapper.selectByPrimaryKey(id);
            return this.buildDTO(templateDO);
        }
    }

    @Override
    public TemplateUploadDTO findByTemplateName(String templateName) {
        if (StringUtils.isEmpty(templateName)) {
            return null;
        } else {
            TemplateDO condition = new TemplateDO();
            condition.setTemplateName(templateName);
            condition.setDeleted(false);
            TemplateDO templateDO = templateMapper.selectOne(condition);
            return this.buildDTO(templateDO);
        }
    }

    private TemplateUploadDTO buildDTO(TemplateDO entity) {
        if (entity == null) {
            return null;
        } else {
            return JSON.parseObject(JSON.toJSONString(entity), TemplateUploadDTO.class);
        }
    }
}
