package com.danding.cds.upload.engine.book.item;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.download.vo.CustomsBookItemExportVO;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemSearchCondition;
import com.danding.cds.item.api.dto.CustomsBookItemSubmitDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.base.BaseUploadProcess;
import com.danding.cds.upload.common.AccountBookAuthHelper;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.*;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/11/11 20:32
 * @Description:
 */
@Component
@Slf4j
public class CustomsBookItemUploadProcess extends BaseUploadProcess {
    @Autowired
    private Validator validator;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;


    @DubboReference
    private EndorsementService endorsementService;

    @Override
    public UploadType getType() {
        return UploadType.CUSTOMS_BOOK_ITEM;
    }

    @Override
    public Class<?> getModelClazz() {
        return CustomsBookItemExportVO.class;
    }

    @Override
    public Integer getHeadRow() {
        return 1;
    }

    @Override
    public Integer getDataRow() {
        return 2;
    }

    @Override
    public String read(Object condition, Object dataItem, Object lib, Long userId) {
        try {
            return readSelf(condition, dataItem, lib);
        } catch (ArgsErrorException e) {
            return e.getErrorMessage();
        } catch (Exception e) {
            return e.getMessage();
        }
    }

    private String readSelf(Object condition, Object dataItem, Object lib) {
        Set<String> set;
        if (lib == null) {
            set = new HashSet<>();
        } else {
            set = (Set<String>) lib;
        }
        CustomsBookItemExportVO demoExcel = (CustomsBookItemExportVO) dataItem;
        if (StrUtil.isNotBlank(demoExcel.getGoodsName())) {
            demoExcel.setGoodsName(StringUtils.trimWhitespace(demoExcel.getGoodsName()));
        }
        String productId = demoExcel.getProductId().trim();
        String goodsSeqNo = demoExcel.getGoodsSeqNo().trim();
        demoExcel.setProductId(productId);
        demoExcel.setGoodsSeqNo(goodsSeqNo);
        String key = demoExcel.getProductId() + demoExcel.getGoodsSeqNo() + demoExcel.getCustomsBookNo();
        if (set.contains(key)) {
            throw new ArgsErrorException("料号:" + demoExcel.getProductId() + ",账册编码:" + demoExcel.getCustomsBookNo() + ",序号:" + demoExcel.getGoodsSeqNo() + "导入重复数据！");
        }
        set.add(key);
        //校验参数必填项
        String inputError = ValidatorUtils.doValidator(validator, demoExcel);
        if (!StringUtils.isEmpty(inputError)) {
            throw new ArgsErrorException(inputError);
        }
        if (Objects.nonNull(demoExcel.getSecondUnit())) {
            if (Objects.isNull(demoExcel.getSecondUnitAmount())) {
                throw new ArgsErrorException("法定第二计量单位数量不能为空");
            }
        }
        if (Objects.nonNull(demoExcel.getSecondUnitAmount())) {
            if (Objects.isNull(demoExcel.getSecondUnit())) {
                throw new ArgsErrorException("法定第二计量单位不能为空");
            }
        }
        //数据校验
        CustomsBookDTO customsBookDTO = customsBookService.findByCode(demoExcel.getCustomsBookNo());
        if (customsBookDTO == null) {
            throw new ArgsErrorException("账册编号错误");
        }

        List<Long> accBookList = Optional.ofNullable(condition).map(z -> {
            return (CustomsBookItemSearchCondition) z;
        }).map(CustomsBookItemSearchCondition::getRoleAccountBookIdList).orElse(null);

        // 账册权限校验
        AccountBookAuthHelper.accBookAuthCheck(customsBookDTO.getId(), accBookList);

        CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(demoExcel.getCurrCode());
        if (currencyDTO == null) {
            throw new ArgsErrorException("申报币制错误");
        }
        if (!StringUtils.isEmpty(demoExcel.getWarehousingDate())) {
            try {
                Long inDate = DateTime.parse(demoExcel.getWarehousingDate(), DateTimeFormat.forPattern("yyyy-MM-dd")).getMillis();
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                throw new ArgsErrorException("最近入仓（核增）日期格式错误");
            }
        }

        CustomsBookItemSubmitDTO submit = new CustomsBookItemSubmitDTO();
        BeanUtil.copyProperties(demoExcel, submit);
        submit.setOriginCountry(demoExcel.getOriginCountry());
        submit.setGoodsUnit(demoExcel.getGoodsUnit());
        submit.setFirstUnit(demoExcel.getFirstUnit());
        if (Objects.nonNull(demoExcel.getFirstUnitAmount())) {
            submit.setFirstUnitAmount(new BigDecimal(demoExcel.getFirstUnitAmount()));
        }
        submit.setSecondUnit(demoExcel.getSecondUnit());
        if (Objects.nonNull(demoExcel.getSecondUnitAmount())) {
            submit.setSecondUnitAmount(new BigDecimal(demoExcel.getSecondUnitAmount()));
        }
        submit.setCustomsBookId(customsBookDTO.getId());
        if (!StringUtils.isEmpty(demoExcel.getWarehousingDate())) {
            submit.setInDate(DateTime.parse(demoExcel.getWarehousingDate(), DateTimeFormat.forPattern("yyyy-MM-dd")).getMillis());
        }
        submit.setSource(ItemTrackLogConfig.ITEM_TYPE_IMPORT);
        customsBookItemService.upsetCore(submit);
        inventoryOrderInfoService.updateItemSeqCallbackAndAutoCreate(submit.getProductId(), submit.getGoodsSeqNo(), submit.getInvtNo(), submit.getInvtGoodsNo());
        endorsementService.updateEndorsementItemSeqCallback(submit.getProductId(), submit.getGoodsSeqNo(), submit.getInvtNo(), submit.getInvtGoodsNo());
        return null;
    }

    @Override
    public String readAll(Object condition, List<Object> dataList, List<Object> successList, List<Object> failLIst, Object lib) {
//        if (!CollectionUtils.isEmpty(failLIst)){
//            return "账册库存导入需要全部成功才能导入";
//        }
//        try {
//            List<CustomsBookItemSubmit> submitList = new ArrayList<>();
//            for (Object object : successList) {
//                CustomsBookItemExportVO demoExcel = (CustomsBookItemExportVO) object;
//                CustomsBookItemSubmit submit = new CustomsBookItemSubmit();
//                BeanUtil.copyProperties(demoExcel, submit);
//                submit.setOriginCountry(demoExcel.getOriginCountry());
//                submit.setGoodsUnit(demoExcel.getGoodsUnit());
//                submit.setFirstUnit(demoExcel.getFirstUnit());
//                submit.setSecondUnit(demoExcel.getSecondUnit());
//                CustomsBookDTO customsBookDTO = customsBookService.findByCode(demoExcel.getCustomsBookNo());
//                submit.setCustomsBookId(customsBookDTO.getId());
//                if (!StringUtils.isEmpty(demoExcel.getWarehousingDate())) {
//                    submit.setInDate(DateTime.parse(demoExcel.getWarehousingDate(), DateTimeFormat.forPattern("yyyy-MM-dd")).getMillis());
//                }
//                submitList.add(submit);
//            }
//            customsBookItemService.submitUpdateList(submitList);
//        } catch (ArgsErrorException e) {
//            log.warn("处理异常：{}", e.getMessage(), e);
//            return "数据保存失败";
//        }catch (Exception e){
//            log.warn("处理异常：{}", e.getMessage(), e);
//            return "系统异常";
//        }
        return null;
    }
}
