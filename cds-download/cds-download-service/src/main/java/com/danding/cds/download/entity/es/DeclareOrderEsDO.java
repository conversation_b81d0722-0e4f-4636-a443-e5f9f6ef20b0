package com.danding.cds.download.entity.es;

import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

/**
 * @program: cds-center
 * @description: 申报单es
 * @author: 潘本乐（Belep）
 * @create: 2022-05-20 13:44
 **/
@Data
@Document(indexName = "ccs_declare_order", type = "order")
public class DeclareOrderEsDO extends EsModel {

    /**
     * 用户ID 预留
     */
    @Field(type = FieldType.Long, store = true)
    private Integer userId;

    /**
     * 申报单状态
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer status;

    /**
     * 上游单号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String outOrderNo;

    /**
     * 申报单号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String declareOrderNo;

    /**
     * 路径代码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String routeCode;

    /**
     * 租户ID
     */
    @Field(type = FieldType.Keyword, store = true)
    private String tenantOuterId;

    /**
     * 收货人姓名
     */
    @Field(type = FieldType.Keyword, store = true)
    private String consigneeName;

    /**
     * 收货人手机
     */
    @Field(type = FieldType.Keyword, store = true)
    private String consigneeTel;

    /**
     * 订购人身份证
     */
    @Field(type = FieldType.Keyword, store = true)
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    @Field(type = FieldType.Keyword, store = true)
    private String buyerName;

    /**
     * 清单编号 申请单编号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String inventoryNo;

    /**
     * 物流运单编号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String logisticsNo;

    /**
     * 电商平台
     */
    @Field(type = FieldType.Long, store = true)
    private Long ebpId;

    /**
     * 电商企业
     */
    @Field(type = FieldType.Long, store = true)
    private Long ebcId;

    /**
     * 担保企业
     */
    @Field(type = FieldType.Long, store = true)
    private Long assureCompanyId;

    /**
     * 物流公司
     */
    @Field(type = FieldType.Long, store = true)
    private Long logisticsCompanyId;

    /**
     * 快递
     */
    @Field(type = FieldType.Long, store = true)
    private Long expressId;

    /**
     * 申报成功时间
     */
    @Field(type = FieldType.Long, store = true)
    private Long customsPassTime;

    /**
     * 1 清关异常标识
     */
    @Field(type = FieldType.Boolean, store = true)
    private Boolean exceptionFlag;

    /**
     * 清关异常类型
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer exceptionType;

    /**
     * 异常描述
     */
    @Field(type = FieldType.Keyword, store = true)
    private String exceptionDetail;

    /**
     * 所有包含的申报项
     */
    @Field(type = FieldType.Keyword, store = true)
    private String actionJson;

    /**
     * 账册ID
     */
    @Field(type = FieldType.Long, store = true)
    private Long accountBookId;

    /**
     * 税金
     */
    @Field(type = FieldType.Double, store = true)
    private Double taxPrice;

    /**
     * 订单下单时间，毫秒
     */
    @Field(type = FieldType.Long, store = true)
    private Long orderTime;
}