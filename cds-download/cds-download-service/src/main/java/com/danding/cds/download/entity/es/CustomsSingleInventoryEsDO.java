package com.danding.cds.download.entity.es;

import com.danding.common.es.model.EsModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.util.Date;
import java.util.List;

/**
 * 功能描述:  申报清单列表索引
 * 创建时间:  2021/8/25 5:09 下午
 *
 * <AUTHOR>
 */
@Data
@Document(indexName = "#{@autoOrderConfig.customsInventoryIndex}", type = "customsSingleInventory", shards = 1)
public class CustomsSingleInventoryEsDO extends EsModel {

    @ApiModelProperty("清单编号")
    private String sn;

    @ApiModelProperty("订单ID")
    private String orderId;

    @ApiModelProperty("订单ID")
    @Field(type = FieldType.Keyword, store = true)
    private String orderSn;

    @ApiModelProperty("申报单号")
    private String declareOrderNo;

    @ApiModelProperty("运单号")
    private String logisticsNo;

    @ApiModelProperty("清单编号")
    private String inventoryNo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("出区状态描述")
    private String exitRegionStatusDesc;

    @ApiModelProperty(value = "清关回执")
    private String customsStatusDesc;

    @ApiModelProperty(value = "出区状态")
    private Integer exitRegionStatus;

    @ApiModelProperty(value = "海关状态")
    private String customsStatus;

    @ApiModelProperty(value = "电商企业")
    private Long ebcId;

    @ApiModelProperty(value = "电商平台")
    @Field(type = FieldType.Keyword)
    private Long ebpId;

    @ApiModelProperty(value = "账册ID")
    private Long accountBookId;

    @ApiModelProperty(value = "申报企业")
    private Long agentCompanyId;

    @ApiModelProperty(value = "区内企业")
    private Long areaCompanyId;

    @ApiModelProperty(value = "清关回执详情")
    private String customsDetail;

    @ApiModelProperty(value = "申报时间")
    private Long declareTime;

    @ApiModelProperty(value = "回执时间")
    private Long receiveTime;

    @ApiModelProperty("申报成功时间")
    private Long customsPassTime;

    @ApiModelProperty("最后一次申报时间")
    private Date lastDeclareTime;

    @ApiModelProperty("最后一次清关回执时间")
    private Date lastCustomsTime;

    @ApiModelProperty("电商企业")
    private String ebcName;

    @ApiModelProperty(value = "电商平台")
    private String ebpName;

    @ApiModelProperty("申报企业")
    private String agentCompanyName;

    @ApiModelProperty("区内企业")
    private String areaCompanyName;

    @Field(type = FieldType.Nested)
    private List<CustomsInventoryItemEsDO> itemExtras;

    /**
     * 售后状态:1-暂无,2-发起售后成功 默认状态1
     */
    @ApiModelProperty(name = "售后状态")
    private Integer afterSalesStatus;

    /**
     * 是否关联交接单
     * 0:否 1:是
     * {@link com.danding.cds.v2.enums.InventoryHandoverStatusEnums}
     */
    @ApiModelProperty(name = "是否关联交接单")
    private Integer handoverStatus;

    /**
     * 核注状态:0：未关联核注；1：已关联核注；2：核注异常；3：核注完成 默认为0
     */
    @ApiModelProperty(name = "核注状态")
    @Field(type = FieldType.Keyword)
    private Integer reviewStatus;

    /**
     * 用户
     */
    @ApiModelProperty(name = "用户")
    @Field(type = FieldType.Keyword)
    private Long userId;

    /**
     * 租户名称
     */
    @ApiModelProperty(name = "租户名称")
    @Field(type = FieldType.Keyword, store = true)
    private String tenantName;
    /**
     * 租户ID
     */
    @ApiModelProperty(name = "租户ID")
    @Field(type = FieldType.Keyword, store = true)
    private String tenantOuterId;
    /**
     * 税金(海关回执税金)，单位为分
     */
    @Field(type = FieldType.Double, store = true)
    private Double totalTax;

    /**
     * 计算税金，即预扣税金(平台计算的税金，非海关回执税金)，单位分
     */
    @Field(type = FieldType.Integer)
    private Integer calculateTaxFee;

    /**
     * 商品总价（分）
     */
    @Field(type = FieldType.Integer)
    private Integer totalFee;

    /**
     * 快递方式ID
     */
    private Long expressId;
    private String expressName;

    /**
     * 物流企业
     */
    private Long logisticsCompanyId;
    private String logisticsCompanyName;

    /**
     * 预录入编号
     */
    private String preNo;

    /**
     * 担保企业
     */
    private Long assureCompanyId;
    private String assureCompanyName;

    /**
     * 关区口岸
     */
    private String customs;

    /**
     * 租户id
     */
    @Field(type = FieldType.Long, store = true)
    private Long tenantryId;

    @ApiModelProperty(value = "撤单审核状态")
    @Field(type = FieldType.Keyword)
    private String cancelStatus;

    @ApiModelProperty(value = "退货审核状态")
    @Field(type = FieldType.Keyword)
    private String refundStatus;

    @ApiModelProperty(value = "撤单创建时间")
    @Field(type = FieldType.Long)
    private Long cancelCreateTime;

    @ApiModelProperty(value = "退货创建时间")
    @Field(type = FieldType.Long)
    private Long refundCreateTime;

//    /**
//     * 逻辑删除
//     */
//    @Field(type = FieldType.Integer, store = true)
//    private Integer deleted;

    /**
     * 电子税单状态
     */
    @Field(type = FieldType.Keyword, store = true)
    private Integer taxBillStatus;


    /**
     * 出区状态
     */
    @Field(type = FieldType.Integer)
    private Integer checkOutStatus;

    /**
     * 出区时间
     */
    @Field(type = FieldType.Long)
    private Long checkOutTime;

    private String note;
}
