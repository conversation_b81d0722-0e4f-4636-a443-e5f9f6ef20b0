package com.danding.cds.download.mapper;

import com.danding.cds.download.entity.DownloadDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface DownloadMapper extends Mapper<DownloadDO>, InsertListMapper<DownloadDO>, BatchUpdateMapper<DownloadDO>, AggregationPlusMapper<DownloadDO> {
}
