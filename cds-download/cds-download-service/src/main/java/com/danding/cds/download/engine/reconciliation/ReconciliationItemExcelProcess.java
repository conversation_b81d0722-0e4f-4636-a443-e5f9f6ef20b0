package com.danding.cds.download.engine.reconciliation;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.download.api.dto.DownloadReportTaskRequest;
import com.danding.cds.download.api.dto.DownloadSubmit;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.engine.base.BaseDownloadProcess;
import com.danding.cds.download.engine.base.DownloadRunnable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class ReconciliationItemExcelProcess extends BaseDownloadProcess {

    @Autowired
    private ReconciliationItemExcelService reconciliationItemExcelService;

    @Override
    protected TraceDataRunnable getTask(DownloadReportTaskRequest reportTaskRequest, DownloadSubmit submit) {
        return new DownloadRunnable(reconciliationItemExcelService, reportTaskRequest, submit);
    }

    @Override
    public ReportType getReportType() {
        return ReportType.RECONCILIATION_ITEM_FOR_EXCEL;
    }
}
