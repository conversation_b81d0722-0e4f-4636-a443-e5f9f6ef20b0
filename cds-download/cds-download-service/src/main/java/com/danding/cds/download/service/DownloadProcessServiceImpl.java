package com.danding.cds.download.service;

import com.danding.cds.download.api.dto.DownloadReportTaskRequest;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.engine.base.DownloadOperation;
import com.danding.cds.download.engine.base.DownloadProcessFactory;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.export.dto.ExportTaskDTO;
import com.danding.park.client.core.export.form.ExportTaskCreateForm;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import javax.annotation.Resource;

@DubboService
@RefreshScope
public class DownloadProcessServiceImpl implements DownloadProcessService {

    @Resource
    private DownloadProcessFactory downloadProcessFactory;

    @Value("${park.sdk.systemCode}")
    private String systemCode;

    @Override
    public void submitDownloadProcess(Object t, ReportType reportType) throws ServiceException {
        // Step::发起下载任务
        DownloadOperation reportDownloadProcess = downloadProcessFactory.getReportDownloadProcess(reportType);
        DownloadReportTaskRequest reportTaskRequest = new DownloadReportTaskRequest();
        reportTaskRequest.setReportType(reportType);
        reportTaskRequest.setCondition(t);
        reportTaskRequest.setUserId(null);
        reportTaskRequest.setUuid(null);
        reportDownloadProcess.process(reportTaskRequest);
    }

    //解决调用一个 通过 type 导出不同文件
    @Override
    public void submitDownloadProcess(Long userId, Object t, ReportType reportType, String type) throws ServiceException {
        // Step::创建下载记录
        ExportTaskCreateForm exportTaskCreateForm = new ExportTaskCreateForm();
        exportTaskCreateForm.setSystemCode(systemCode);
        exportTaskCreateForm.setFuncCode(reportType.value);
        exportTaskCreateForm.setMasterUserId(userId);
        exportTaskCreateForm.setUserId(userId);
        String prefix = reportType.name;
        if (StringUtils.isNotEmpty(type)) {
            // 明确导出文件名
            prefix = prefix + type;
        }
        exportTaskCreateForm.setName(prefix + "CCS" + DateTime.now().toString("yyyyMMddHHmmss"));
        ExportTaskDTO exportTaskDTO = ParkClient.exportClient().create(exportTaskCreateForm);
        // Step::发起下载任务
        DownloadOperation reportDownloadProcess = downloadProcessFactory.getReportDownloadProcess(reportType);
        DownloadReportTaskRequest reportTaskRequest = new DownloadReportTaskRequest();
        reportTaskRequest.setReportType(reportType);
        reportTaskRequest.setCondition(t);
        reportTaskRequest.setUserId(null);
        reportTaskRequest.setUuid(exportTaskDTO.getUid());
        reportDownloadProcess.process(reportTaskRequest);
    }

    @Override
    public void submitDownloadProcess(Long userId, Object t, ReportType reportType) throws ServiceException {
        submitDownloadProcess(userId, t, reportType, null);
    }
}
