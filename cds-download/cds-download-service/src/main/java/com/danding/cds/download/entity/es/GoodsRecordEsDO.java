package com.danding.cds.download.entity.es;

import com.danding.common.es.model.EsModel;
import lombok.Data;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description 商品备案对象
 * @date 2022/6/2
 */
@Data
@Document(indexName = "ccs_goods_record", type = "goods_record")
public class GoodsRecordEsDO extends EsModel {
    /**
     * sku
     */
    @Field(type = FieldType.Keyword, store = true)
    private String skuId;

    /**
     * 料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productId;

    /**
     * 账册主键id
     */
    @Field(type = FieldType.Long, store = true)
    private Long customsBookId;

    /**
     * 条码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String barCode;

    /**
     * 备案名称
     */
    @Field(type = FieldType.Text, store = true)
    private String goodsRecordName;

    /**
     * 申报单价
     */
    @Field(type = FieldType.Double, store = true)
    private Double declarePrice;

    /**
     * 申报单位
     */
    @Field(type = FieldType.Keyword, store = true)
    private String declareUnit;

    /**
     * 申报币值
     */
    @Field(type = FieldType.Keyword, store = true)
    private String declareCurrency;
    /**
     * 净重
     */
    @Field(type = FieldType.Double, store = true)
    private Double netWeight;

    /**
     * 毛重
     */
    @Field(type = FieldType.Double, store = true)
    private Double grossWeight;

    /**
     * 图片
     */
    @Field(type = FieldType.Keyword, store = true)
    private String skuPicture;

    /**
     * 长
     */
    @Field(type = FieldType.Double, store = true)
    private Double length;

    /**
     * 宽
     */
    @Field(type = FieldType.Double, store = true)
    private Double width;

    /**
     * 高
     */
    @Field(type = FieldType.Double, store = true)
    private Double height;

    /**
     * 型号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String model;

    /**
     * 品牌（中文）
     */
    @Field(type = FieldType.Keyword, store = true)
    private String brand;

    /**
     * 品牌（英文）
     */
    @Field(type = FieldType.Keyword, store = true)
    private String brandEn;

    /**
     * 租户
     */
    @Field(type = FieldType.Keyword, store = true)
    private String lesseeNo;

    /**
     * 仓库
     */
    @Field(type = FieldType.Keyword, store = true)
    private String warehouseId;

    /**
     * 租户
     */
    @Field(type = FieldType.Keyword, store = true)
    private String tenantId;

    /**
     * 上游渠道
     */
    @Field(type = FieldType.Keyword, store = true)
    private Integer channel;

    /**
     * 审核状态
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer recordStatus;

    /**
     * 驳回原因
     */
    @Field(type = FieldType.Keyword, store = true)
    private String reason;

    /**
     * HS编码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String hsCode;

    /**
     * 增值税率
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer vatRate;

    /**
     * 消费税率
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer taxRate;

    /**
     * 成分
     */
    @Field(type = FieldType.Keyword, store = true)
    private String composition;

    /**
     * 海关申报要素
     */
    @Field(type = FieldType.Keyword, store = true)
    private String hgsbys;

    /**
     * 原产国
     */
    @Field(type = FieldType.Keyword, store = true)
    private String originCountry;

    /**
     * 功能
     */
    @Field(type = FieldType.Keyword, store = true)
    private String recordFunction;

    /**
     * 用途
     */
    @Field(type = FieldType.Keyword, store = true)
    private String recordUsage;

    /**
     * 法定第一计量单位
     */
    @Field(type = FieldType.Keyword, store = true)
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    @Field(type = FieldType.Double, store = true)
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    @Field(type = FieldType.Keyword, store = true)
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    @Field(type = FieldType.Double, store = true)
    private BigDecimal secondUnitAmount;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer enable;

    /**
     * 备案完成时间
     */
    @Field(type = FieldType.Date, store = true)
    private Date recordFinishTime;

    /**
     * 国检备案号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String countryRecordNo;

    /**
     * 出区进口商品流水号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String ioGoodsSerialNo;

    /**
     * 商品备案号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String goodsRegNo;

    /**
     * 进口入区申报号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String importEntryDeclareNo;

    /**
     * 国检原产国编码
     */
    @Field(type = FieldType.Keyword, store = true)
    private String ciqOriginCountry;

    /**
     * 口岸 erp下发
     */
    @Field(type = FieldType.Keyword, store = true)
    private String customs;

    /**
     * 货品id
     */
    @Field(type = FieldType.Keyword, store = true)
    private String goodsCode;

    //补充信息
    /**
     * 生产企业名称
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productCompanyName;

    /**
     * 生产企业注册编号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productCompanyRegisterNumber;

    /**
     * 生产企业地址
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productCompanyAddress;

    /**
     * 商品链接
     */
    @Field(type = FieldType.Keyword, store = true)
    private String productLink;

    /**
     * 附件
     */
    @Field(type = FieldType.Keyword, store = true)
    private String attachmentName;

    @Field(type = FieldType.Keyword, store = true)
    private String attachmentUrl;

    /**
     * 正面图片
     */
    @Field(type = FieldType.Keyword, store = true)
    private String frontImage;
    /**
     * 侧面图片
     */
    @Field(type = FieldType.Keyword, store = true)
    private String sideImage;
    /**
     * 背面图片
     */
    @Field(type = FieldType.Keyword, store = true)
    private String backImage;
    /**
     * 标签图片
     */
    @Field(type = FieldType.Keyword, store = true)
    private String labelImage;

    /**
     * 保质期
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer shelfLife;

    /**
     * 外部料号
     */
    @Field(type = FieldType.Keyword, store = true)
    private String externalProductId;



    /**
     * 备案类型
     * NEW("NEW","新备案"),
     * OLD("OLD","老备案");
     * {@link com.danding.cds.v2.bean.enums.GoodsRecordTypeEnums}
     */
    @Field(type = FieldType.Keyword, store = true)
    private String recordType;

    /**
     * 来源
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer goodsSource;

    /**
     * 删除标记
     */
    @Field(type = FieldType.Integer, store = true)
    private Integer deleted;

    /**
     * 备案口岸关联
     */
    @Field(type = FieldType.Nested, store = true)
    private List<RecordCustomsEsDO> esRecordCustomsDO;

    /**
     * 备案实体仓关联
     */
    @Field(type = FieldType.Nested, store = true)
    private List<RecordWarehouseEsDO> esRecordWarehouseDOS;

    /**
     * 商品列表
     */
    @Field(type = FieldType.Nested, store = true)
    private List<RecordProductEsDO> esRecordProducts;

    /**
     * 外部货品id
     */
    @Field(type = FieldType.Keyword, store = true)
    private String externalGoodsId;

    /**
     * 菜鸟货品id
     */
    @Field(type = FieldType.Keyword, store = true)
    private String cainiaoGoodsId;

}
