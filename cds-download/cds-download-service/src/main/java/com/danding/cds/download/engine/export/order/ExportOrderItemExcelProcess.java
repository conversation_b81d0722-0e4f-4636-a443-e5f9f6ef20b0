package com.danding.cds.download.engine.export.order;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.download.api.dto.DownloadReportTaskRequest;
import com.danding.cds.download.api.dto.DownloadSubmit;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.engine.base.BaseDownloadProcess;
import com.danding.cds.download.engine.base.DownloadRunnable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 出库单表体导出
 */
@Component
public class ExportOrderItemExcelProcess extends BaseDownloadProcess {

    @Autowired
    private ExportOrderItemExcelService exportOrderItemExcelService;

    @Override
    protected TraceDataRunnable getTask(DownloadReportTaskRequest reportTaskRequest, DownloadSubmit submit) {
        return new DownloadRunnable(exportOrderItemExcelService, reportTaskRequest, submit);
    }

    @Override
    public ReportType getReportType() {
        return ReportType.EXPORT_ORDER_ITEM_FOR_EXCEL;
    }

}
