package com.danding.cds.download.engine.reconciliation;

import com.danding.cds.c.api.rpc.ReconciliationOrderItemRpc;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.download.api.vo.ReconciliationItemExportVO;
import com.danding.cds.download.engine.base.BaseExcelService;
import com.danding.cds.v2.bean.dto.ReconciliationOrderItemDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemSearch;
import com.danding.cds.v2.enums.ReconciliationItemException;
import com.danding.cds.v2.enums.ReconciliationItemStatusEnums;
import com.danding.logistics.api.common.response.ListVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class ReconciliationItemExcelService extends BaseExcelService {

    @DubboReference
    private ReconciliationOrderItemRpc reconciliationOrderItemRpc;

    @DubboReference
    private CompanyService companyService;

    @Override
    public Class<?> getModelClazz() {
        return ReconciliationItemExportVO.class;
    }

    @Override
    public List selectListForExcelExport(Object o, int i) {
        ReconciliationItemSearch search = (ReconciliationItemSearch) o;
        search.setCurrentPage(i);
        search.setPageSize(500);
        try {
            ListVO<ReconciliationOrderItemDTO> paging = reconciliationOrderItemRpc.paging(search);
            log.debug("InventoryOrderOverdueExcelService export={}", paging);
            return buildExportVO(paging.getDataList());
        } catch (Exception e) {
            throw new RuntimeException(e.getMessage());
        }
    }

    private List<ReconciliationItemExportVO> buildExportVO(List<ReconciliationOrderItemDTO> list) {
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        return list.stream().map(dto -> {
            ReconciliationItemExportVO vo = new ReconciliationItemExportVO();
            BeanUtils.copyProperties(dto, vo);
            ReconciliationItemStatusEnums statusEnums = ReconciliationItemStatusEnums.getEnums(dto.getStatus());
            vo.setStatusDesc(statusEnums == null ? "" : statusEnums.getDesc());
            ReconciliationItemException.ExceptionTypeEnums exceptionTypeEnums = ReconciliationItemException.ExceptionTypeEnums.getEnums(dto.getExceptionType());
            vo.setExceptionTypeDesc(exceptionTypeEnums == null ? "" : exceptionTypeEnums.getDesc());
            ReconciliationItemException.ExceptionDescEnums exceptionDescEnums = ReconciliationItemException.ExceptionDescEnums.getEnums(dto.getExceptionDesc());
            vo.setExceptionDesc(exceptionDescEnums == null ? "" : exceptionDescEnums.getDesc());
            CompanyDTO companyDTO = companyDTOMap.computeIfAbsent(dto.getAreaCompanyId(), i -> companyService.findById(dto.getAreaCompanyId()));
            if (Objects.nonNull(companyDTO)) {
                vo.setAreaCompanyName(companyDTO.getName());
            }
            return vo;
        }).collect(Collectors.toList());
    }
}
