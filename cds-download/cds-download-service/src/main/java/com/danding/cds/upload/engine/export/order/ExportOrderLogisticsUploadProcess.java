package com.danding.cds.upload.engine.export.order;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import com.danding.cds.exportorder.api.dto.ExportItemWritingReport;
import com.danding.cds.exportorder.api.dto.ExportOrderDTO;
import com.danding.cds.exportorder.api.dto.ExportSkuInfo;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.vo.ExportItemImportVO;
import com.danding.cds.upload.base.BaseUploadProcess;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/11/12 09:22
 * @Description:
 */
@Slf4j
@Component
public class ExportOrderLogisticsUploadProcess extends BaseUploadProcess {

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private RouteService routeService;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Override
    public UploadType getType() {
        return UploadType.EXPORT_ORDER_LOGISTICS;
    }

    @Override
    public Class<?> getModelClazz() {
        return ExportItemImportVO.class;
    }

    @Override
    public Object preLib(List<Object> dataList) {
        int i = 1; // 行号，从2开始
        List<ExportItemRecord> preList = new ArrayList<>();
        for (Object dataItem : dataList) {
            ExportItemImportVO demoExcel = (ExportItemImportVO) dataItem;
            i++;
            if(StringUtils.isEmpty(demoExcel.getExpressName())
                    && StringUtils.isEmpty(demoExcel.getMailNo())){
                continue;
            }
            ExportItemRecord record = new ExportItemRecord();
            BeanUtils.copyProperties(demoExcel,record);
            record.setIdx(i);
            preList.add(record);
        }
        this.fillInfo(preList);

        Map<String,ExportItemRecord>  recordMap = new HashMap<>();
        for (ExportItemRecord record : preList) {
            recordMap.put(record.getMailNo(), record);
        }
        return recordMap;
    }

    @Override
    public String read(Object condition, Object dataItem, Object lib, Long userId) {
        Long id = (Long) condition;
        ExportItemImportVO importVO = (ExportItemImportVO) dataItem;
        Map<String,ExportItemRecord> recordMap = (Map<String,ExportItemRecord>) lib;
        ExportItemRecord record = recordMap.get(importVO.getMailNo());
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(id);

        String errorMsg = "";
        List<Long> customsBookIdList = new ArrayList<>();
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            errorMsg += ("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在");
        } else {
            entityWarehouseDTOList = entityWarehouseDTOList.stream()
                    .filter(entityWarehouseDTO -> entityWarehouseDTO.getEnable() == 1
                            && EntityWarehouseTagEnums.containsAny(entityWarehouseDTO.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(entityWarehouseDTOList)) {
                errorMsg += ("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在可用清关账册");
            } else {
                customsBookIdList = entityWarehouseDTOList.stream()
                        .map(EntityWarehouseDTO::getCustomsBookId)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }
        List<Long> expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(),Long.class);

        if(StringUtils.isEmpty(record.getMailNo()))
        {
            errorMsg += "运单号为空;";
        }
        else if(StringUtils.isEmpty(record.getCustomsInventorySn())&&!StringUtils.isEmpty(record.getMailNo()))
        {
            errorMsg += "运单号系统不存在;";
        }
        else if(StringUtils.isEmpty(record.getExpressName()))
        {
            errorMsg += "快递公司为空;";
        }else {
            CustomsInventoryDTO customsInventoryDTO = record.getCustomsInventoryDTO();
            log.info("[op:ExportOrderManager-writing] step 3-1, customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
            if(!exportOrderDTO.getDeclareCompanyId().equals(customsInventoryDTO.getDeclareCompanyId()))
            {
                errorMsg += "运单和申报出库单不属于同一个清关企业";
            }
            else if (LongUtil.isNone(record.getExpressId())){
                errorMsg += "快递公司填写不正确;";
            }else if(!expressIdList.contains(record.getExpressId())){
                errorMsg += "当前申报出库单不支持该快递公司;";
            }else if (StringUtils.isEmpty(record.getCustomsInventorySn()) || customsInventoryDTO == null){
                errorMsg += "清单未找到;";
            }else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())){
                errorMsg += "清单未放行;";
            }else if (exportOrderService.findItemByCustomInventory(customsInventoryDTO.getSn()) != null) {
                errorMsg += "该运单已关联出库单;";
            } else if (!customsBookIdList.contains(customsInventoryDTO.getAccountBookId())) {
                errorMsg += "对应放行清单的账册与申报出库单的账册不一致;";
            }else if (StringUtils.isBlank(customsInventoryDTO.getInventoryNo())){
                errorMsg += "对应清单编号为空;";
            }
            log.info("[op:ExportOrderManager-writing] step 3-2, customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
        }
        return errorMsg;
    }

    @Override
    public String readAll(Object condition, List<Object> dataList, List<Object> successList, List<Object> failLIst,Object lib) {
        Long id = (Long) condition;
        ExportItemWritingReport report = new ExportItemWritingReport();
        report.setTotalCount(dataList.size());
        report.setSuccessCount(successList.size());
        report.setFailCount(failLIst.size());
        report.setSuccessRecordList(new ArrayList<>());
        report.setFailRecordList(new ArrayList<>());
        for (Object  o: successList) {
            ExportItemImportVO importVO = (ExportItemImportVO) o;
            Map<String,ExportItemRecord> recordMap = (Map<String,ExportItemRecord>) lib;
            ExportItemRecord record = recordMap.get(importVO.getMailNo());
            report.getSuccessRecordList().add(record);
        }
        for (Object o : failLIst) {
            ExportItemImportVO importVO = (ExportItemImportVO) o;
            Map<String,ExportItemRecord> recordMap = (Map<String,ExportItemRecord>) lib;
            ExportItemRecord record = recordMap.get(importVO.getMailNo());
            report.getFailRecordList().add(record);
        }
        try {
            exportOrderService.writingByItem(id,report);
        } catch (ArgsErrorException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return e.getErrorMessage();
        }catch (Exception e){
            log.warn("处理异常：{}", e.getMessage(), e);
            return "系统异常，导入失败";
        }
        return null;
    }

    private Map<String, CustomsInventoryDTO> fillInfo(List<ExportItemRecord> origList){
        Map<String, ExpressDTO> expressDTOMap = new HashMap<>();
        Map<Long, ExpressDTO> expressDTOIdMap = new HashMap<>();
        Map<String, RouteDTO> declareDTOMap = new HashMap<>();
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = new HashMap<>();
        for (ExportItemRecord record : origList) {
            log.info("fillInfo-step {}, time={}", 1, DateTime.now().toString("yyyy/MM/dd HH:mm:ss SSS"));
            if (StringUtils.isEmpty(record.getMailNo())){
                continue;
            }
            ExpressDTO expressDTO = null;
            if (!StringUtils.isEmpty(record.getExpressName())){
                expressDTO = expressDTOMap.get(record.getExpressName());
                if (expressDTO == null){
                    expressDTO = expressService.findOneByName(record.getExpressName());
                    expressDTOMap.put(record.getExpressName(), expressDTO);
                }
            }
            log.info("fillInfo-step {}, time={}", 2, DateTime.now().toString("yyyy/MM/dd HH:mm:ss SSS"));
            CustomsInventoryDTO customsInventoryDTO = null;
            if (expressDTO != null && StringUtils.isEmpty(record.getCustomsInventorySn())){
                // TODO:这里有优化空间，可以改成批量查询
                customsInventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.findByLogistics90Days(expressDTO.getId(), record.getMailNo()) : customsInventoryService.findByLogistics90Days(expressDTO.getId(), record.getMailNo());
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn()))
            {
                List<CustomsInventoryDTO> list  = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.listByLogistics90Days(record.getMailNo()) : customsInventoryService.listByLogistics90Days(record.getMailNo());
                if(!CollectionUtils.isEmpty(list)) {
                    customsInventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.listByLogistics90Days(record.getMailNo()).get(0) : customsInventoryService.listByLogistics90Days(record.getMailNo()).get(0);
                    expressDTO = expressDTOIdMap.get(customsInventoryDTO.getExpressId());
                    if (expressDTO == null){
                        expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
                        expressDTOIdMap.put(customsInventoryDTO.getExpressId(), expressDTO);
                    }
                }
            }else {
                customsInventoryDTO = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.findBySnSection(record.getCustomsInventorySn()) : customsInventoryService.findBySnSection(record.getCustomsInventorySn());
            }
            log.info("fillInfo step-{}, time={}", 3, DateTime.now().toString("yyyy/MM/dd HH:mm:ss SSS"));
            if (expressDTO != null){
                record.setExpressId(expressDTO.getId());
            }
            if (customsInventoryDTO != null){
                try {
                    if(!StringUtils.isEmpty(customsInventoryDTO.getExtraJson())) {
                        CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
                        if(!StringUtils.isEmpty(extra.getRouteCode())) {
                            RouteDTO routeDTO =  declareDTOMap.get(extra.getRouteCode());
                            if(routeDTO==null) {
                                routeDTO = routeService.findByCode(extra.getRouteCode());
                                if (routeDTO != null) {
                                    declareDTOMap.put(extra.getRouteCode(), routeDTO);
                                }
                            }
                            if(routeDTO != null){
                                customsInventoryDTO.setDeclareCompanyId(routeDTO.getListDeclareCompanyId());
                            }
                        }
                    }
                }catch (Exception ex) {

                }
                log.info("fillInfo-step {}, time={}", 4, DateTime.now().toString("yyyy/MM/dd HH:mm:ss SSS"));
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = orderCCallConfig.isOrderCCall(this.getClass()) ? customsInventoryRpc.listItemByIdSection(customsInventoryDTO.getId(),customsInventoryDTO.getCreateTime()) : customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(),customsInventoryDTO.getCreateTime());
                List<ExportSkuInfo> skuInfoList = new ArrayList<>();
                for (CustomsInventoryItemDTO customsInventoryItemDTO : customsInventoryItemDTOS) {
                    ExportSkuInfo skuInfo = new ExportSkuInfo();
                    skuInfo.setBookItemId(customsInventoryItemDTO.getBookItemId());
                    skuInfo.setCount(customsInventoryItemDTO.getCount());
                    skuInfoList.add(skuInfo);
                }
                record.setSkuInfoList(skuInfoList);

                record.setGrossWeight(customsInventoryDTO.getGrossWeight() == null ? BigDecimal.ZERO : customsInventoryDTO.getGrossWeight());
                record.setNetWeight(customsInventoryDTO.getNetWeight());
                record.setCustomsInventorySn(customsInventoryDTO.getSn());
                record.setBizId(customsInventoryDTO.getDeclareOrderNo());
                customsInventoryDTOMap.put(customsInventoryDTO.getSn(),customsInventoryDTO);
                record.setCustomsInventoryDTO(customsInventoryDTO);
            }
            log.info("fillInfo-step {}, time={}", 5, DateTime.now().toString("yyyy/MM/dd HH:mm:ss SSS"));
        }
        return customsInventoryDTOMap;
    }
}
