package com.danding.cds.download.mapper;


import com.danding.cds.download.entity.TemplateDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface TemplateMapper extends Mapper<TemplateDO>, InsertListMapper<TemplateDO>, BatchUpdateMapper<TemplateDO>, AggregationPlusMapper<TemplateDO> {
}
