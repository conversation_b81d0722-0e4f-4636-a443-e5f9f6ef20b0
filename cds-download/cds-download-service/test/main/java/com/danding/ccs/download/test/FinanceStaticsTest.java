package com.danding.ccs.download.test;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.fastjson.JSON;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.junit.Test;

import java.io.File;
import java.util.List;

@Slf4j
public class FinanceStaticsTest {

    @Test
    public void easypoi2() {
        log.info("start, time={}", DateTime.now().toString("yyyy/MM/dd mm:HH:ss SSS"));
        File file = new File("C:\\Users\\<USER>\\Desktop\\代塔缺失数据.xlsx");
        log.info("file ready, time={}", DateTime.now().toString("yyyy/MM/dd mm:HH:ss SSS"));
        ImportParams importParams = new ImportParams();
        importParams.setHeadRows(1);
        importParams.setTitleRows(0);
        List<FinanceExcelDemo> successList;
        try {
            log.info("wait read, time={}", DateTime.now().toString("yyyy/MM/dd mm:HH:ss SSS"));
            ExcelImportResult<FinanceExcelDemo> result = ExcelImportUtil.importExcelMore(file, FinanceExcelDemo.class,
                    importParams);
            log.info("read finish, time={}", DateTime.now().toString("yyyy/MM/dd mm:HH:ss SSS"));
            successList = result.getList();
            for (FinanceExcelDemo hsExcelDemo : successList) {
                System.out.println("update pangu_finance_statics SET declare_order_sn = '" + hsExcelDemo.getDeclareOrderNo() + "' , `status` = 1 , groups = '第三批' WHERE customs_serial_no = '" + hsExcelDemo.getCustomsSerialNo() + "';");
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);;
            throw new ArgsErrorException("上传失败");
        }

    }

    @Test
    public void repeatedRead() {
        String fileName = "C:\\Users\\<USER>\\Desktop\\订单详情查询.xlsx";
        // 读取全部sheet
        // 这里需要注意 DemoDataListener的doAfterAllAnalysed 会在每个sheet读取完毕后调用一次。然后所有sheet都会往同一个DemoDataListener里面写
        // EasyExcel.read(fileName, FinanceExcelDemo.class, new FinanceListener()).doReadAll();

        // 读取部分sheet
        ExcelReader excelReader = null;
        try {
            List<FinanceExcelDemo> list = EasyExcel.read(fileName).headRowNumber(2).head(FinanceExcelDemo.class).sheet().doReadSync();
            for (FinanceExcelDemo financeExcelDemo : list) {
                log.info("读取{}", JSON.toJSONString(financeExcelDemo));
            }
            log.info("总数={}",list.size());

            String fileName2 = "C:\\Users\\<USER>\\Desktop\\代塔缺失数据.xlsx";
            // 这里 需要指定写用哪个class去写，然后写到第一个sheet，名字为模板 然后文件流会自动关闭
            EasyExcel.write(fileName2, FinanceExcelDemo.class).sheet("模板").doWrite(list);
        } finally {
            if (excelReader != null) {
                // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
                excelReader.finish();
            }
        }
    }
}
