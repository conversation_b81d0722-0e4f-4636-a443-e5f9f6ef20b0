package com.danding.ccs.download.test;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.Workbook;
import org.junit.Test;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
@Slf4j
public class HSExcelTest {

    /**
     * 获取指定文件夹中所有的文件
     * @return
     */
    public static File[] listFiles(String filePath) {
        File file = new File(filePath);
        boolean directory = file.isDirectory();
        if (directory) {
            return file.listFiles();
        }{
            return new File[0];
        }
    }

    @Test
    public void name() {
        File[] files = HSExcelTest.listFiles("C:\\Users\\<USER>\\Desktop\\hs200831");
        List<HSExcelDemo> hsExcelDemos = new ArrayList<>();
        for (File file : files) {
            System.out.println(file.getName());
            ImportParams importParams = new ImportParams();
            importParams.setHeadRows(1);
            importParams.setTitleRows(0);
            List<HSExcelDemo> successList;
            try {
//                InputStream in = new FileInputStream(file);
                ExcelImportResult<HSExcelDemo> result = ExcelImportUtil.importExcelMore(file, HSExcelDemo.class,
                        importParams);
                successList = result.getList();
                for (HSExcelDemo hsExcelDemo : successList) {
                    System.out.println(hsExcelDemo.getHs());
                    hsExcelDemos.add(hsExcelDemo);
                }
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                throw new ArgsErrorException("上传失败");
            }
        }

        try {
            File folder = new File("C:\\Users\\<USER>\\Desktop\\hs200831\\allHs.xlsx").getParentFile();
            if (!folder.exists()){
                folder.mkdirs();
            }
            OutputStream outputStream = new FileOutputStream("C:\\Users\\<USER>\\Desktop\\hs200831\\allHs.xlsx");

            ExportParams exportParams = new ExportParams();
            exportParams.setType( ExcelType.XSSF);
            Workbook workbook = ExcelExportUtil.exportExcel(exportParams, HSExcelDemo.class, hsExcelDemos);
            workbook.write(outputStream);
            outputStream.flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }
}
