package com.danding.ccs.download.test;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

@Data
public class FinanceExcelDemo {

    @Excel(name = "清单编号")
    @ExcelProperty("清单编号")
    private String customsSerialNo;

    @Excel(name = "订单编号")
    @ExcelProperty("订单编号")
    private String declareOrderNo;

    @Excel(name = "运单编号")
    @ExcelProperty("运单编号")
    private String shipmentSerialNo;
}
