package com.danding.ccs.download.test;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.ByteArrayOutputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutionException;

/**
 * @program: cds-center
 * @description: s
 * @author: 潘本乐（Belep）
 * @create: 2021-08-31 14:52
 **/
@Slf4j
public class FutureTest {

    @Test
    public void futureTask() throws ExecutionException, InterruptedException {

        long start = System.currentTimeMillis();
        log.info("开始");
        CopyOnWriteArrayList<ExcelData> excelDataList = new CopyOnWriteArrayList<>();
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (int i = 0; i < 5; i++) {
            CompletableFuture future = CompletableFuture.runAsync(new Task(i, excelDataList));
            futureList.add(future);
        }
        log.info("任务放置结束，耗时：{}", System.currentTimeMillis() - start);
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        long end = System.currentTimeMillis();

        OutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, ExcelData.class).sheet("数据").doWrite(excelDataList);

        log.info("结束，耗时：{}", end - start);
        System.out.println(excelDataList);
    }

    class Task implements Runnable {

        List<ExcelData> integerList;
        int index = 0;

        public Task(int index, CopyOnWriteArrayList<ExcelData> integerList) {
            this.index = index;
            this.integerList = integerList;
        }

        /**
         * When an object implementing interface <code>Runnable</code> is used
         * to create a thread, starting the thread causes the object's
         * <code>run</code> method to be called in that separately executing
         * thread.
         * <p>
         * The general contract of the method <code>run</code> is that it may
         * take any action whatsoever.
         *
         * @see Thread#run()
         */
        @Override
        public void run() {
            for (int i = 0; i < 1000; i++) {
                ExcelData excelData = new ExcelData("name:" + i, "年龄:" + i, "school:" + i);
                integerList.add(excelData);
                log.info("线程：" + Thread.currentThread().getName() + "，" + index);
            }
        }
    }


}

@AllArgsConstructor
@Data
class ExcelData {

    @ExcelProperty(value = "姓名", index = 0)
    private String name;
    @ExcelProperty(value = "年龄", index = 1)
    private String age;
    @ExcelProperty(value = "学校", index = 2)
    private String school;
}
