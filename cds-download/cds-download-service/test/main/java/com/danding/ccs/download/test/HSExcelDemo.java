package com.danding.ccs.download.test;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class HSExcelDemo {

    @Excel(name = "商品编码")
    private String hs;

    @Excel(name = "编码状态")
    private String name;

    @Excel(name = "商品名称")
    private String status;

    @Excel(name = "计量单位")
    private String unit;

    @Excel(name = "增值税率")
    private String addTax;

    @Excel(name = "消费税率")
    private String costTax;
}
