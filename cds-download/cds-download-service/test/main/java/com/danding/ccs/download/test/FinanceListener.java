package com.danding.ccs.download.test;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Data
public class FinanceListener extends AnalysisEventListener<FinanceExcelDemo> {
    /**
     * 每隔5条存储数据库，实际使用中可以3000条，然后清理list ，方便内存回收
     */
    private static final int BATCH_COUNT = 3000;
    List<FinanceExcelDemo> list = new ArrayList<FinanceExcelDemo>();

    /**
     * 加上存储数据库
     */
    private void saveData() {
        log.info("{}条数据，开始存储数据库！", list.size());
        log.info("存储数据库成功！");
    }


    @Override
    public void invoke(FinanceExcelDemo financeExcelDemo, AnalysisContext analysisContext) {
        log.info("解析到一条数据:{}", JSON.toJSONString(financeExcelDemo));
        list.add(financeExcelDemo);
        if (list.size() >= BATCH_COUNT) {
            saveData();
            list.clear();
        }
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("所有数据解析完成！");
    }
}
