package com.danding.cds.declare.base.component.util;

import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.SpringContextUtil;
import com.danding.cds.declare.base.component.Declare;
import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.base.component.payment.PaymentDeclareAbstract;
import com.danding.cds.declare.base.component.refund.InventoryRefundDeclareAbstrat;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @program: cds-center
 * @description: 申报工具
 * @author: 潘本乐（Belep）
 * @create: 2021-11-17 11:21
 **/
@Slf4j
public class DeclareUtils {

    /**
     * 申报的实现
     */
    private static final Map<String, Declare> DECLARE_BEAN_MAP = new ConcurrentHashMap(1 << 4);

    private static Object defaultSync = new Object();
    /**
     * 订单同步锁
     */
    private static Object orderSync = new Object();
    /**
     * 运单同步锁
     */
    private static Object logisticsSync = new Object();

    /**
     * 清单同步锁
     */
    private static Object inventorySync = new Object();
    /**
     * 清单取消同步锁
     */
    private static Object inventoryCancelSync = new Object();
    /**
     * 清单退货同步锁
     */
    private static Object inventoryRefundSync = new Object();

    /**
     * 支付单申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static PaymentDeclareResult paymentDeclare(WrapPaymentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)支付单申报", info.getOrderNo());
        return (PaymentDeclareResult) proxyOrDirectDeclare(info, DeclareEnum.PAYMENT);
    }

    /**
     * 支付单申报(不走代理模式)
     * 1. 不走代理申报，如路径配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static PaymentDeclareResult paymentDeclareNoProxy(WrapPaymentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)支付单申报(无代理)", info.getOrderNo());
        return (PaymentDeclareResult) noProxyDeclare(info, DeclareEnum.PAYMENT);
    }

    /**
     * 支付单申报(代理模式)
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static PaymentDeclareResult paymentDeclareProxy(WrapPaymentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)支付单申报(代理)", info.getOrderNo());
        return (PaymentDeclareResult) proxyDeclare(info, DeclareEnum.PAYMENT);
    }

    public static PaymentDeclareResult paymentDeclareDirect(WrapPaymentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)支付单申报(默认申报方式)", info.getOrderNo());
        return (PaymentDeclareResult) directDeclare(info, DeclareEnum.PAYMENT);
    }

    /**
     * 订单申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static OrderDeclareResult orderDeclare(WrapOrderDeclareInfo info) {
        log.info("申报单号: {} , (自定义申报配置)订单申报", info.getDeclareOrderNo());
        return (OrderDeclareResult) proxyOrDirectDeclare(info, DeclareEnum.CUSTOMS_ORDER);
    }

    /**
     * 订单申报（（不走代理申报）
     * 1. 路径高级配置中有重新定义申报实现，则用此申报实现，否则用企业配置的申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static OrderDeclareResult orderDeclareNoProxy(WrapOrderDeclareInfo info) {
        log.info("申报单号: {} , (自定义申报配置)订单申报(无代理)", info.getDeclareOrderNo());
        return (OrderDeclareResult) noProxyDeclare(info, DeclareEnum.CUSTOMS_ORDER);
    }

    /**
     * 订单申报 取declareCode
     *
     * @param info 申报信息
     * @return
     */
    public static OrderDeclareResult orderDeclareDirect(WrapOrderDeclareInfo info) {
        log.info("申报单号: {} ,订单申报(默认申报方式)", info.getDeclareOrderNo());
        return (OrderDeclareResult) directDeclare(info, DeclareEnum.CUSTOMS_ORDER);
    }

    /**
     * 订单申报（代理申报）
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static OrderDeclareResult orderDeclareProxy(WrapOrderDeclareInfo info) {
        log.info("申报单号: {} , (自定义申报配置)订单申报(代理)", info.getDeclareOrderNo());
        return (OrderDeclareResult) proxyDeclare(info, DeclareEnum.CUSTOMS_ORDER);
    }

    /**
     * 运单申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static ShipmentDeclareResult shipmentDeclare(WrapShipmentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)运单申报", info.getDeclareOrderNo());
        return (ShipmentDeclareResult) proxyOrDirectDeclare(info, DeclareEnum.SHIPMENT);
    }

    /**
     * 运单申报（不走代理申报）
     * 1. 路径高级配置中有重新定义申报实现，则用此申报实现，否则用企业配置的申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static ShipmentDeclareResult shipmentDeclareNoProxy(WrapShipmentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)运单申报(无代理)", info.getDeclareOrderNo());
        return (ShipmentDeclareResult) noProxyDeclare(info, DeclareEnum.SHIPMENT);
    }

    /**
     * 运单申报（代理申报）
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static ShipmentDeclareResult shipmentDeclareProxy(WrapShipmentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)运单申报(代理)", info.getDeclareOrderNo());
        return (ShipmentDeclareResult) proxyDeclare(info, DeclareEnum.SHIPMENT);
    }

    public static ShipmentDeclareResult shipmentDeclareDirect(WrapShipmentInfo info) {
        log.info("申报单号: {} , (自定义申报配置)运单申报(默认申报方式)", info.getDeclareOrderNo());
        return (ShipmentDeclareResult) directDeclare(info, DeclareEnum.SHIPMENT);
    }

    /**
     * 清单申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单申报", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryDeclareResult) proxyOrDirectDeclare(info, DeclareEnum.INVENTORY);
    }

    public static InventoryDeclareResult inventoryDeclare(WrapInventoryOrderInfo info, String finalImpl) {
        log.info("申报单号:{} finalImpl:{} 指定申报配置清单申报", info.getCustomsInventoryDto().getOrderNo());
        if (StringUtils.isEmpty(finalImpl)) {
            return inventoryDeclare(info);
        } else {
            return (InventoryDeclareResult) declareHandlerFinal(info, DeclareEnum.INVENTORY, finalImpl);
        }
    }

    /**
     * 清单申报（不走代理申报）
     * 1. 路径高级配置中有重新定义申报实现，则用此申报实现，否则用企业配置的申报实现
     *
     * @param info 申报信息
     * @return
     */
    @Deprecated
    public static InventoryDeclareResult inventoryDeclareNoProxy(WrapInventoryOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单申报(无代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryDeclareResult) noProxyDeclare(info, DeclareEnum.INVENTORY);
    }

    /**
     * 清单申报（代理申报）
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryDeclareResult inventoryDeclareProxy(WrapInventoryOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单申报(代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryDeclareResult) proxyDeclare(info, DeclareEnum.INVENTORY);
    }

    public static InventoryDeclareResult inventoryDeclareDirect(WrapInventoryOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单申报(默认申报方式)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryDeclareResult) directDeclare(info, DeclareEnum.INVENTORY);
    }

    /**
     * 清单取消申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryCancelResult inventoryCancelDeclare(WarpCancelOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单取消申报", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryCancelResult) proxyOrDirectDeclare(info, DeclareEnum.INVENTORY_CANCEL);
    }

    /**
     * 清单取消申报(指定申报方式)
     * 如果指定了finalImpl最终申报实现方式 则直接申报
     *
     * @param info      申报信息
     * @param finalImpl 最终申报方式
     * @return
     */
    public static InventoryCancelResult inventoryCancelDeclare(WarpCancelOrderInfo info, String finalImpl) {
        log.info("申报单号: {} finalImpl={}, (自定义申报配置)清单取消申报", info.getCustomsInventoryDto().getOrderNo());
        if (StringUtils.isEmpty(finalImpl)) {
            return inventoryCancelDeclare(info);
        } else {
            return (InventoryCancelResult) declareHandlerFinal(info, DeclareEnum.INVENTORY_CANCEL, finalImpl);
        }
    }

    /**
     * 清单取消申报（不走代理申报）
     * 1. 路径高级配置中有重新定义申报实现，则用此申报实现，否则用企业配置的申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryCancelResult inventoryCancelDeclareNoProxy(WarpCancelOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单取消申报(无代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryCancelResult) noProxyDeclare(info, DeclareEnum.INVENTORY_CANCEL);
    }

    /**
     * 清单取消申报（代理申报）
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryCancelResult inventoryCancelDeclareProxy(WarpCancelOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单取消申报(代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryCancelResult) proxyDeclare(info, DeclareEnum.INVENTORY_CANCEL);
    }

    public static InventoryCancelResult inventoryCancelDeclareDirect(WarpCancelOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单取消申报(默认申报方式)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryCancelResult) directDeclare(info, DeclareEnum.INVENTORY_CANCEL);
    }

    /**
     * 清单退货申报
     * 1. 如果路径配置有代理申报，则直接代理申报
     * 2. 如果路径配置没有代理申报，但是配置有重新定义申报实现，则用此申报实现，
     * 否则用申报企业配置的申报实现，如果申报企业没有配置对应的申报实现，则提示错误信息
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryRefundResult inventoryRefundDeclare(WarpRefundOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单退货申报", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryRefundResult) proxyOrDirectDeclare(info, DeclareEnum.INVENTORY_REFUND);
    }

    /**
     * 清单退货申报(指定申报方式)
     * 如果指定了finalImpl最终申报实现方式 则直接申报
     *
     * @param info      申报信息
     * @param finalImpl 最终申报方式
     * @return
     */
    public static InventoryRefundResult inventoryRefundDeclare(WarpRefundOrderInfo info, String finalImpl) {
        log.info("申报单号: {} finalImpl={}, (自定义申报配置)清单退货申报", info.getCustomsInventoryDto().getOrderNo());
        if (StringUtils.isEmpty(finalImpl)) {
            return inventoryRefundDeclare(info);
        } else {
            return (InventoryRefundResult) declareHandlerFinal(info, DeclareEnum.INVENTORY_REFUND, finalImpl);
        }
    }


    /**
     * 清单退货申报（不走代理申报）
     * 1. 路径高级配置中有重新定义申报实现，则用此申报实现，否则用企业配置的申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryRefundResult inventoryRefundDeclareNoProxy(WarpRefundOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单退货申报(无代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryRefundResult) noProxyDeclare(info, DeclareEnum.INVENTORY_REFUND);
    }

    /**
     * 清单退货申报（代理申报）
     * 1. 使用路径高级配置中重新定义的代理申报实现
     *
     * @param info 申报信息
     * @return
     */
    public static InventoryRefundResult inventoryRefundDeclareProxy(WarpRefundOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单退货申报(代理)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryRefundResult) proxyDeclare(info, DeclareEnum.INVENTORY_REFUND);
    }

    public static InventoryRefundResult inventoryRefundDeclareDirect(WarpRefundOrderInfo info) {
        log.info("申报单号: {} , (自定义申报配置)清单退货申报(默认申报方式)", info.getCustomsInventoryDto().getOrderNo());
        return (InventoryRefundResult) directDeclare(info, DeclareEnum.INVENTORY_REFUND);
    }

    /**
     * 代理或直接申报申报
     * 先找代理实现方式，如果没有则找直接申报的方式，都没有就报错
     *
     * @param info        申报信息
     * @param declareEnum 申报类型
     * @return
     */
    private static DeclareResultAbstract proxyOrDirectDeclare(WrapBeanInfo info, DeclareEnum declareEnum) {
        return declareHandler(info, declareEnum, null);
    }

    /**
     * 代理申报
     *
     * @param info        申报信息
     * @param declareEnum 申报类型
     * @return
     */
    private static DeclareResultAbstract proxyDeclare(WrapBeanInfo info, DeclareEnum declareEnum) {
        return declareHandler(info, declareEnum, true);
    }

    /**
     * 无代理申报
     *
     * @param info        申报信息
     * @param declareEnum 申报类型
     * @return
     */
    @Deprecated
    private static DeclareResultAbstract noProxyDeclare(WrapBeanInfo info, DeclareEnum declareEnum) {
        return declareHandler(info, declareEnum, null);
    }

    private static DeclareResultAbstract directDeclare(WrapBeanInfo info, DeclareEnum declareEnum) {
        return declareHandler(info, declareEnum, false);
    }

    /**
     * 指定了最终申报方式
     * 直接获取申报对象去申报
     *
     * @param info
     * @param declareEnum
     * @param finalImpl   最终申报方式
     * @return
     */
    private static DeclareResultAbstract declareHandlerFinal(WrapBeanInfo info, DeclareEnum declareEnum, String finalImpl) {
        return getDeclareResultCore(info, declareEnum, finalImpl);
    }

    /**
     * 申报处理
     *
     * @param info        申报信息
     * @param declareEnum 申报类型
     * @param invokeProxy null：表示先找代理，没找到则走直接申报；true：代理申报：false：直接申报
     * @return
     */
    private static DeclareResultAbstract declareHandler(WrapBeanInfo info, DeclareEnum declareEnum, Boolean invokeProxy) {

        String routeCode = info.getRouteInfo().getCode();
        try {
            String declareImpl;
            // null：表示先找代理，没找到则走直接申报；true：代理申报：false：直接申报；
            if (invokeProxy == null) {
                declareImpl = getProxyOrDirectDeclareImpl(info, declareEnum);
            } else if (invokeProxy) {
                declareImpl = getProxyDeclareImpl(info, declareEnum);
            } else {
                declareImpl = getDirectDeclareImpl(info, declareEnum);
            }
            if (StringUtils.isEmpty(declareImpl)) {
                throw new RuntimeException(String.format("申报路径: %s ,配置的申报方式实现为空(是否走代理标识：%s)", routeCode, invokeProxy));
            }
            return getDeclareResultCore(info, declareEnum, declareImpl);
        } catch (Exception ex) {
            log.error("申报单号: {} , 申报路径: {} ,申报异常: {}", info.getDeclareNos(), routeCode, ex.getMessage(), ex);
            throw ex;
        }
    }

    /**
     * 进行申报的核心方法
     *
     * @param info
     * @param declareEnum
     * @param declareImpl
     * @return
     */
    public static DeclareResultAbstract getDeclareResultCore(WrapBeanInfo info, DeclareEnum declareEnum, String declareImpl) {
        Declare declareObj = getDeclare(declareImpl, declareEnum.getType());
        if (declareObj == null) {
            throw new RuntimeException(String.format("服务中未找到,申报实现方式:%s", declareImpl));
        }
        if (declareEnum == DeclareEnum.CUSTOMS_ORDER) {
            return ((OrderDeclareAbstract) declareObj).invoke((WrapOrderDeclareInfo) info);
        } else if (declareEnum == DeclareEnum.SHIPMENT) {
            return ((ShipmentDeclareAbstract) declareObj).invoke((WrapShipmentInfo) info);
        } else if (declareEnum == DeclareEnum.INVENTORY) {
            return ((InventoryDeclareAbstract) declareObj).invoke((WrapInventoryOrderInfo) info);
        } else if (declareEnum == DeclareEnum.INVENTORY_CANCEL) {
            return ((InventoryCancelDeclareAbstract) declareObj).invoke((WarpCancelOrderInfo) info);
        } else if (declareEnum == DeclareEnum.INVENTORY_REFUND) {
            return ((InventoryRefundDeclareAbstrat) declareObj).invoke((WarpRefundOrderInfo) info);
        } else if (declareEnum == DeclareEnum.PAYMENT) {
            return ((PaymentDeclareAbstract) declareObj).invoke((WrapPaymentInfo) info);
        } else {
            return null;
        }
    }


    /**
     * 获取代理申报实现或直接申报实现
     * 先查代理申报实现，代理申报实现存在，在找直接申报实现
     *
     * @param info 申报信息
     * @return
     */
    private static String getProxyOrDirectDeclareImpl(WrapBeanInfo info, DeclareEnum declareEnum) {
        String impl = null;
        RouteDeclareConfig declareHighConfig = info.getRouteDeclareHighConfig(declareEnum);
        if (declareHighConfig == null) {
            String declareCompanyDeclareImpl = info.getDeclareCompanyDeclareImpl(declareEnum);
            if (StringUtils.hasText(declareCompanyDeclareImpl)) {
                impl = declareCompanyDeclareImpl;
            }
        } else {
            // 1. 代理申报
            String proxyImpl = declareHighConfig.getProxyImpl();
            if (!StringUtils.isEmpty(proxyImpl)) {
                impl = proxyImpl;
            } else {
                // 2. 直接申报，
                impl = declareHighConfig.getDeclareImpl();
                if (StringUtils.isEmpty(impl)) {
                    String declareCompanyDeclareImpl = info.getDeclareCompanyDeclareImpl(declareEnum);
                    if (StringUtils.hasText(declareCompanyDeclareImpl)) {
                        impl = declareCompanyDeclareImpl;
                    }
                }
            }
        }
        return impl;
    }

    /**
     * 获取代理申报实现，代理申报只会存在与路径的申报配置中
     *
     * @param info        申报信息
     * @param declareEnum 申报类型
     * @return
     */
    private static String getProxyDeclareImpl(WrapBeanInfo info, DeclareEnum declareEnum) {

        String impl = null;
        // 1. 路由设置是否有设置申报配置
        RouteDeclareConfig declareHighConfig = info.getRouteDeclareHighConfig(declareEnum);
        if (declareHighConfig != null) {
            // 2. 代理申报
            impl = declareHighConfig.getProxyImpl();
        }
        return impl;
    }


    public static RouteDeclareConfig getDeclareWayFromConfig(WrapBeanInfo info, DeclareEnum declareEnum) {
        if (info.dynamicDeclareEnable()) {
            RouteDeclareConfig routeDeclareHighConfig = info.getRouteDeclareHighConfig(declareEnum);
            if (Objects.nonNull(routeDeclareHighConfig)) {
                String impl = DeclareUtils.getProxyOrDirectDeclareImpl(info, declareEnum);
                RouteDeclareConfig routeDeclareConfig = ConvertUtil.beanConvert(routeDeclareHighConfig, RouteDeclareConfig.class);
                routeDeclareConfig.setFinalImpl(impl);
                if (Objects.equals(impl, routeDeclareConfig.getFinalImpl())) {
                    routeDeclareConfig.setFinalCode(routeDeclareConfig.getFinalCode());
                } else if (Objects.equals(impl, routeDeclareConfig.getProxyImpl())) {
                    routeDeclareConfig.setFinalCode(routeDeclareConfig.getProxyCode());
                } else if (Objects.equals(impl, routeDeclareConfig.getDeclareImpl())) {
                    routeDeclareConfig.setFinalCode(routeDeclareConfig.getDeclareCode());
                }
                return routeDeclareConfig;
            }
        }
        return null;
    }

    /**
     * 获取直接申报实现
     *
     * @param info 申报信息
     * @return
     */
    private static String getDirectDeclareImpl(WrapBeanInfo info, DeclareEnum declareEnum) {
        String impl = null;
        // 1. 路由设置是否有设置申报配置
        RouteDeclareConfig declareHighConfig = info.getRouteDeclareHighConfig(declareEnum);
        if (declareHighConfig == null) {
            String declareCompanyDeclareImpl = info.getDeclareCompanyDeclareImpl(declareEnum);
            if (StringUtils.hasText(declareCompanyDeclareImpl)) {
                impl = declareCompanyDeclareImpl;
            }
        } else {
            // 2. 直接申报
            impl = declareHighConfig.getDeclareImpl();
            if (StringUtils.isEmpty(impl)) {
                String declareCompanyDeclareImpl = info.getDeclareCompanyDeclareImpl(declareEnum);
                if (StringUtils.hasText(declareCompanyDeclareImpl)) {
                    impl = declareCompanyDeclareImpl;
                }
            }
        }
        return impl;
    }

    /**
     * 获取申报实现
     *
     * @param beanName Bean名称
     * @return
     */
    private static Declare getDeclare(String beanName, String type) {

        if (DECLARE_BEAN_MAP.containsKey(beanName)) {
            return DECLARE_BEAN_MAP.get(beanName);
        } else {
            synchronized (syncMinirro(type)) {
                if (DECLARE_BEAN_MAP.containsKey(beanName)) {
                    return DECLARE_BEAN_MAP.get(beanName);
                }
                Declare declareObj = (Declare) SpringContextUtil.getBean(beanName);
                if (declareObj != null) {
                    DECLARE_BEAN_MAP.putIfAbsent(beanName, declareObj);
                }
                return declareObj;
            }
        }
    }

    private static Object syncMinirro(String type) {
        Object sync = defaultSync;
        if (Objects.equals(DeclareEnum.CUSTOMS_ORDER.getType(), type)) {
            sync = orderSync;
        } else if (Objects.equals(DeclareEnum.SHIPMENT.getType(), type)) {
            sync = logisticsSync;
        } else if (Objects.equals(DeclareEnum.INVENTORY.getType(), type)) {
            sync = inventorySync;
        } else if (Objects.equals(DeclareEnum.INVENTORY_CANCEL.getType(), type)) {
            sync = inventoryCancelSync;
        } else if (Objects.equals(DeclareEnum.INVENTORY_REFUND.getType(), type)) {
            sync = inventoryRefundSync;
        }
        return sync;
    }

}
