package com.danding.cds.track.log.interfaces;

import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogInfoDto;

/**
 * <AUTHOR>
 * @description: 构建轨迹日志
 * @date 2022/6/6 09:57
 */
public interface TrackLogParametersHandler {

    TrackLogInfoDto handle(TrackLog trackLog, Object[] args) throws Exception;

    TrackLogInfoDto afterProcessHandle(TrackLog trackLog, TrackLogInfoDto trackLogInfoDto, Object proceed) throws Exception;

}
