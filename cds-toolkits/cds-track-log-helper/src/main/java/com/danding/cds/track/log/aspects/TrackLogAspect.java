package com.danding.cds.track.log.aspects;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.TrackLogEsRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.SpringContextUtil;
import com.danding.cds.log.api.service.TrackLogEsService;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.track.log.interfaces.TrackLogParametersHandler;
import com.danding.cds.track.log.utils.TrackLogUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 *
 */
@Aspect
@Component
@Slf4j
public class TrackLogAspect {
    @DubboReference
    private OrderService orderService;
    @DubboReference
    private TrackLogEsService trackLogEsService;
    @DubboReference
    private TrackLogEsRpc trackLogEsRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;

    /**
     * 任何持有@TrackLog注解的方法
     */
    @Pointcut(value = "@annotation(com.danding.cds.track.log.annotations.TrackLog)")
    private void trackLogPointcut() {
    }

    /**
     * @param pjp 切面
     * @return
     * @throws Throwable
     */
    @Around("trackLogPointcut()")
    public Object doTrackLogAround(ProceedingJoinPoint pjp) throws Throwable {
        Object proceed;
        log.info("#TrackLogAspect doTrackLogAround 进入切面");
        MethodSignature signature = (MethodSignature) pjp.getSignature();
        TrackLog trackLog = signature.getMethod().getAnnotation(TrackLog.class);
        TrackLogInfoDto trackLogInfoDto;
        TrackLogParametersHandler parametersHandler;
        try {
            Object[] args = pjp.getArgs();
            Class<? extends TrackLogParametersHandler> handler = trackLog.handler();
            if (Objects.isNull(handler)) {
                log.warn("#TrackLogAspect 获取handler失败 handler={}", trackLog.handler());
                proceed = pjp.proceed();
                TrackLogUtils.remove();
                return proceed;
            }
            parametersHandler = SpringContextUtil.getBean(handler);
            if (Objects.isNull(parametersHandler)) {
                log.warn("#TrackLogAspect 获取handler Bean对象失败 handler={}", trackLog.handler());
                proceed = pjp.proceed();
                TrackLogUtils.remove();
                return proceed;
            }
            trackLogInfoDto = parametersHandler.handle(trackLog, args);
        } catch (Exception e) {
            log.warn("#TrackLogAspect 前置处理失败 error={}", e.getMessage(), e);
            proceed = pjp.proceed();
            TrackLogUtils.remove();
            return proceed;
        }
        //执行
        try {
            proceed = pjp.proceed();
        } catch (Exception e) {
            log.warn("#TrackLogAspect 业务代码执行失败", e);
            this.recordFailLog(trackLog, trackLogInfoDto, e);
            TrackLogUtils.remove();
            throw e;
        }
        try {
            parametersHandler.afterProcessHandle(trackLog, trackLogInfoDto, proceed);
            if (Objects.nonNull(trackLogInfoDto)) {
                if (Objects.nonNull(trackLogInfoDto.getOrderSn())) {
                    //不想在切面中更新内部流转状态了 无法保证顺序执行
//                    updateOrderInternalStatus(trackLogInfoDto.getOrderId(), trackLogInfoDto.getOrderSn(), trackLogInfoDto.getInternalStatus());
                    if (orderCCallConfig.isOrderCCall(this.getClass())) {
                        trackLogEsRpc.sendMsg(JSON.toJSONString(trackLogInfoDto));
                    } else {
                        trackLogEsService.sendMsg(JSON.toJSONString(trackLogInfoDto));
                    }
                }
            }
        } catch (Exception e) {
            log.warn("#TrackLogAspect 后置处理失败 error={}", e.getMessage(), e);
        }
        log.debug("#TrackLogAspect 保存异常轨迹日志完成 info={}", JSON.toJSONString(trackLogInfoDto));
        TrackLogUtils.remove();
        return proceed;
    }

    private void updateOrderInternalStatus(Long orderId, String internalStatus) {
        if (Objects.isNull(orderId)) {
            log.warn("#TrackLogAspect 修改订单内部状态失败 orderId为空");
            return;
        }
        if (Objects.isNull(internalStatus)) {
            log.warn("#TrackLogAspect 修改订单内部状态失败 内部状态为空");
            return;
        }
        orderService.updateOrderInternalStatus(orderId, internalStatus);
    }

    private void updateOrderInternalStatus(Long orderId, String orderSn, String internalStatus) {
        if (Objects.isNull(orderSn)) {
            log.warn("#TrackLogAspect 修改订单内部状态失败 orderSn为空");
            return;
        }
        if (Objects.isNull(internalStatus)) {
            log.warn("#TrackLogAspect 修改订单内部状态失败 内部状态为空");
            return;
        }
        orderService.updateOrderInternalStatus(orderId, orderSn, internalStatus);
    }

    private void recordFailLog(TrackLog trackLog, TrackLogInfoDto trackLogInfoDto, Exception e) {
        try {
            if (Objects.isNull(trackLogInfoDto.getOrderSn())) {
                log.warn("#TrackLogAspect 未记录orderSn");
            }
            trackLogInfoDto.setResult(TrackLogConstantMixAll.FAIL);
            trackLogInfoDto.setCustomsReceipt(e.getMessage());
            if (Objects.nonNull(trackLogInfoDto.getOrderId())
                    && Objects.nonNull(trackLogInfoDto.getOrderSn())
                    && Objects.nonNull(trackLogInfoDto.getDeclareOrderNo())) {
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    trackLogEsRpc.sendMsg(JSON.toJSONString(trackLogInfoDto));
                } else  {
                    trackLogEsService.sendMsg(JSON.toJSONString(trackLogInfoDto));
                }
            }
            log.debug("#TrackLogAspect 保存异常轨迹日志完成 info={}", JSON.toJSONString(trackLogInfoDto));
        } catch (Exception ex) {
            log.warn("#TrackLogAspect 保存异常轨迹日志失败 error={}", e.getMessage(), e);
        }
    }

}
