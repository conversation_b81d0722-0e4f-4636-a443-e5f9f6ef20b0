package com.danding.cds.track.log.utils;

import com.danding.cds.track.log.bean.TrackLogBaseInfo;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/6/7 16:19
 */
public class TrackLogUtils {
    private static ThreadLocal<TrackLogBaseInfo> baseInfoThreadLocal = new ThreadLocal<>();

    public static void setBaseInfoThreadLocal(Long orderId) {
        setTrackLogBaseInfoThreadLocal(orderId, null, null);
    }

    public static void setTrackLogBaseInfoThreadLocal(Long orderId, String orderSn, String declareOrderNo) {
        setTrackLogBaseInfoThreadLocal(orderId, orderSn, declareOrderNo, null, null);
    }

    public static void setTrackLogBaseInfoThreadLocal(Integer statusCode) {
        setTrackLogBaseInfoThreadLocal(null, null, null, statusCode, null);
    }

    public static void setTrackLogBaseInfoThreadLocal(String statusDesc) {
        setTrackLogBaseInfoThreadLocal(null, null, null, null, statusDesc);
    }

    public static void setTrackLogBaseInfoThreadLocal(Long orderId, String orderSn, String declareOrderNo, Integer statusCode, String statusDesc) {
        TrackLogBaseInfo baseInfo = new TrackLogBaseInfo();
        baseInfo.setOrderId(orderId);
        baseInfo.setOrderSn(orderSn);
        baseInfo.setDeclareOrderNo(declareOrderNo);
        baseInfo.setStatusCode(statusCode);
        baseInfo.setStatusDesc(statusDesc);
        setTrackLogBaseInfoThreadLocal(baseInfo);
    }

    public static TrackLogBaseInfo getBaseInfoAndRemove() {
        TrackLogBaseInfo trackLogBaseInfo = baseInfoThreadLocal.get();
        baseInfoThreadLocal.remove();
        return trackLogBaseInfo;
    }

    public static void setTrackLogBaseInfoThreadLocal(TrackLogBaseInfo baseInfo) {
        baseInfoThreadLocal.set(baseInfo);
    }

    public static TrackLogBaseInfo getBaseInfoThreadLocal() {
        return baseInfoThreadLocal.get();
    }

    public static void remove() {
        baseInfoThreadLocal.remove();
    }

}
