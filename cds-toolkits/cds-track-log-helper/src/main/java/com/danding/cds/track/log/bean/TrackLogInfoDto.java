package com.danding.cds.track.log.bean;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/6/6 09:58
 */
@Data
public class TrackLogInfoDto implements Serializable {
    /**
     * 申报单id
     */
    private Long orderId;
    /**
     * 申报单sn
     */
    private String orderSn;
    /**
     * 申报单号
     */
    private String declareOrderNo;
    /**
     * 内部流转状态
     */
    private String internalStatus;
    /**
     * 发送方
     */
    private String sender;
    /**
     * 接收方
     */
    private String receiver;
    /**
     * 结果
     */
    private String result;
    /**
     * 节点概述
     */
    private String eventDesc;
    /**
     * 节点时间
     */
    private Date eventTime;
    /**
     * 海关回执
     */
    private String customsReceipt;
    /**
     * 请求报文
     */
    private String requestMessage;
    /**
     * 操作人
     */
    private String operator;
}
