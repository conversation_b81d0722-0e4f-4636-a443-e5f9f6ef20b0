package com.danding.cds.track.log.annotations;

import com.danding.cds.track.log.interfaces.TrackLogParametersHandler;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * <AUTHOR>
 * @description: 轨迹日志的切面注解
 * @date 2022/6/6 09:53
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ElementType.TYPE, ElementType.METHOD})
public @interface TrackLog {
    /**
     * 是否是总署报文
     *
     * @return
     */
    boolean cebMessage() default false;

    /**
     * 报文下标
     *
     * @return
     */
    int requestMessageIndex() default -1;

    /**
     * 构建轨迹日志info下标
     *
     * @return
     */
    int infoIndex() default -1;

    /**
     * 回执类型
     */
    String receiptType() default "";

    /**
     * 发送方
     *
     * @return
     */
    String sender() default "";

    /**
     * 接收方
     *
     * @return
     */
    String receiver() default "";

    Class<? extends TrackLogParametersHandler> handler();
}
