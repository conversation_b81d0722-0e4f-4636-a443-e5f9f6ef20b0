package com.danding.cds.http.saas.enums;

/**
 * <AUTHOR>
 * @since 2022-12-02 09:11
 */
public enum TenantHttpType {
    /**
     * 不需要任何处理
     */
    NONE,
    /**
     * 回执解析 必须指定一个key 并且编写好与这个key对应的解析器
     */
    CALLBACK,
    /**
     * ES查询 必须指定一个key 并且编写好与这个key对应的查询器
     */
    ES_QUERY,
    /**
     * 默认租户ID 默认租户ID为1001
     */
    DEFAULT,
    /**
     * 请求头中携带租户ID 也不需要处理
     */
    HEADER,
    /**
     * 从sn中切割
     */
    PARSE_SN,
}
