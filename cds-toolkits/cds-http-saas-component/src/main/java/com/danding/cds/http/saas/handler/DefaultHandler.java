package com.danding.cds.http.saas.handler;

//import org.elasticsearch.index.query.BoolQueryBuilder;
//import org.elasticsearch.index.query.QueryBuilders;
//import org.elasticsearch.search.SearchHit;
//import org.springframework.data.domain.PageRequest;
//import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
//import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
//import org.springframework.data.elasticsearch.core.query.SearchQuery;

import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-12-02 16:32
 * 默认租户处理器 直接返回默认租户ID:1001
 */
@Component
public class DefaultHandler implements TenantHandler {

    @Override
    public Long queryTenantId(Map<String, String> queryMap) {
        return 1001L;
    }

    @Override
    public Long parseTenantId(TenantHttpMethod tenantHttpMethod, Object[] param) {
        return 1001L;
    }
}
