package com.danding.cds.http.saas.handler;

import com.danding.cds.http.saas.annotation.TenantHttpMethod;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-12-02 16:30
 * <p>非必要情况下 不要实现此接口 通过继承两个抽象类去实现对应的方法
 * @see TenantParser
 * @see ElasticSearchQuerier
 */
public interface TenantHandler {
    /**
     * 从ES中查询租户ID
     *
     * @param queryMap es字段名-参数值 Map
     * @return
     */
    Long queryTenantId(Map<String, String> queryMap);

    /**
     * 从参数中解析租户ID
     *
     * @param tenantHttpMethod
     * @param param            指定索引位置的参数
     * @return
     */
    Long parseTenantId(TenantHttpMethod tenantHttpMethod, Object[] param);
}
