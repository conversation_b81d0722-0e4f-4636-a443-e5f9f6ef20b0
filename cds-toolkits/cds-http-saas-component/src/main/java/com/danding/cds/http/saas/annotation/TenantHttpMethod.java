package com.danding.cds.http.saas.annotation;

import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.http.saas.handler.DefaultHandler;
import com.danding.cds.http.saas.handler.TenantHandler;

import java.lang.annotation.*;

/**
 * <AUTHOR>
 * @since 2022-12-02 09:09
 * 标注此HTTP接口需要进行前置 租户ID 获取操作
 */
@Target({ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TenantHttpMethod {
    /**
     * 必须指定获取租户ID方式的类型
     */
    TenantHttpType type();

    /**
     * 方法参数位置索引 默认第一个参数 从0开始 仅在回执类型下生效
     */
    int[] index() default {};

    /**
     * 指定解析租户id索引
     * 仅在sn解析类型下生效
     */
    int snIndex() default 0;

    /**
     * 指定处理器Class 默认处理器只返回 租户ID 1001 <p>
     * 在Web模块提供了ES的查询实现
     *
     * @see com.danding.cds.web.v2.saas.DeclareOrderEsQuerier
     */
    Class<? extends TenantHandler> handler() default DefaultHandler.class;

}
