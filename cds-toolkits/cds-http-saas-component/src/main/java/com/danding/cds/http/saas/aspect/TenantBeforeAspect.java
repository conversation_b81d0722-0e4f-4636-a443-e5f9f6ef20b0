package com.danding.cds.http.saas.aspect;

import com.danding.cds.http.saas.annotation.TenantHttpField;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.config.TenantBaseConfig;
import com.danding.cds.http.saas.handler.TenantHandler;
import com.danding.core.chain.mdc.ChainHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-12-02 09:15
 */
@Aspect
@Component
@Slf4j
/**
 * 设置order=1 保证级别最高
 */
@Order(value = 1)
//@RefreshScope
public class TenantBeforeAspect implements ApplicationContextAware {

    @Autowired
    private TenantBaseConfig tenantBaseConfig;

    private ApplicationContext applicationContext;

    @Override
    public void setApplicationContext(@NonNull ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }


    /**
     * private 修饰的方法不会被切面拦截 所以需要确认被拦截的方法是否为private 并且该类的实例对象是在Spring容器中存在的
     */
    @Pointcut(value = "@annotation(com.danding.cds.http.saas.annotation.TenantHttpMethod)")
    public void pointCut() {
        // point cut
    }

    @After(value = "pointCut()")
    public void after() {
        log.debug("TenantBeforeAspect after 解绑 当前租户id={}", SimpleTenantHelper.getTenantIdStr());
        ChainHelper.unbinding();
    }

    @Before(value = "pointCut()")
    public void before(JoinPoint joinPoint) {
        if (Objects.nonNull(SimpleTenantHelper.getTenantId())) {
            log.info("TenantBeforeAspect before tenantId={}", SimpleTenantHelper.getTenantId());
            return;
        }
        Boolean enableTenantParse = tenantBaseConfig.getEnableTenantParse();
        Long defaultTenantValue = tenantBaseConfig.getDefaultTenantValue();
        if (!enableTenantParse) {
            log.info("TenantBeforeAspect 未开启租户解析 取defaultValue={}", defaultTenantValue);
            setTenantIdIfNotPresent(defaultTenantValue);
            return;
        }
        Object[] args = joinPoint.getArgs();
        TenantHttpMethod annotation = getAnnotation(joinPoint);
        TenantHandler handler = applicationContext.getBean(annotation.handler());
        switch (annotation.type()) {
            case ES_QUERY: {
                Map<String, String> queryMap = extractTenantFields(args);
                Long tenantId = handler.queryTenantId(queryMap);
                setTenantIdIfNotPresent(tenantId);
            }
            break;
            case CALLBACK: {
                Long tenantId = handler.parseTenantId(annotation, args);
                setTenantIdIfNotPresent(tenantId);
            }
            break;
//            case PARSE_SN:
//                String sn = (String) args[annotation.snIndex()];
//                Long tenantId = parseBySn(sn);
//                break;
            case DEFAULT: {
                setTenantIdIfNotPresent(1001L);
            }
            break;
            default: // do nothing
        }
    }

    private Long parseBySn(String sn) {
        int t = sn.lastIndexOf("T");
        if (t == -1) {
            log.info("ZJCallBackHandler sn :{} not contain tenantId ", sn);
        } else {
            String tenantId = sn.substring(2, t);
            log.info("ZJCallBackHandler sn :{} contain tenantId :{}", sn, tenantId);
            return Long.valueOf(tenantId);
        }
        return 1001L;
    }

    private Map<String, String> extractTenantFields(Object[] args) {
        Map<String, String> queryMap = new HashMap<>(8);
        for (Object arg : args) {
            Field[] fields = arg.getClass().getDeclaredFields();
            for (Field field : fields) {
                field.setAccessible(true);
                if (field.isAnnotationPresent(TenantHttpField.class)) {
                    TenantHttpField annotation = field.getAnnotation(TenantHttpField.class);
                    try {
                        String value = (String) field.get(arg);
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        String alias = annotation.alias();
                        if (StringUtils.isEmpty(alias)) {
                            // 为空则使用字段名称
                            alias = field.getName();
                        }
                        queryMap.put(alias, value);
                    } catch (Exception e) {
                        log.error("获取属性值失败,类名: {} 字段名: {}",
                                arg.getClass().getSimpleName(), field.getName(), e);
                    }
                }
            }
        }
        return queryMap;
    }

    /**
     * 设置租户ID 如果当前线程没有租户ID
     *
     * @param tenantId
     */
    private void setTenantIdIfNotPresent(Long tenantId) {
        // 以当前线程传递的租户ID为主 如果存在 就不设置值
        if (Objects.isNull(SimpleTenantHelper.getTenantId())) {
            ChainHelper.bindingTenantId(tenantId);
        }
    }

    /**
     * 获取注解
     *
     * @param joinPoint
     * @return
     */
    private TenantHttpMethod getAnnotation(JoinPoint joinPoint) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method targetMethod = signature.getMethod();
        return targetMethod.getAnnotation(TenantHttpMethod.class);
    }


}
