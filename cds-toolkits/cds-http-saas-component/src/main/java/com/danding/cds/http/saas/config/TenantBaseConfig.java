package com.danding.cds.http.saas.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/23 17:24
 */
@Component
@RefreshScope
@Data
public class TenantBaseConfig implements Serializable {
    @Value("${saas.enableTenantParse:true}")
    private Boolean enableTenantParse;
    @Value("${saas.defaultTenantValue:1001}")
    private Long defaultTenantValue;
}
