package com.danding.cds.stock.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.ExceptionCodeEnum;
import com.danding.cds.common.utils.StockException;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.stock.StockContextUtil;
import com.danding.cds.stock.bean.OrderInfoDto;
import com.danding.cds.stock.bean.OrderStoreCacheDto;
import com.danding.logistics.cache.common.config.RedisLockUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * @program: cds-center
 * @description: Redis基础
 * @author: 潘本乐（Belep）
 * @create: 2021-07-12 19:55
 **/
@Service
@Slf4j
public class StoreRedisBaseService {

    /**
     * 占用
     */
    private static final String occupy = "occupy";
    /**
     * 取消占用
     */
    private static final String cancelOccupy = "cancelOccupy";
    /**
     * 占用转已用
     */
    private static final String occupyToUsed = "occupyToUsed";
    /**
     * 已用转占用
     */
    private static final String usedToOccupy = "usedToOccupy";
    /**
     * 已用转可用
     */
    private static final String usedToAvailable = "usedToAvailable";
    /**
     * 减可用
     */
    private static final String availableToUsed = "availableToUsed";
    /**
     * 直接增加库存
     */
    private static final String incrementStockDirect = "incrementStockDirect";
    /**
     * 直接减库存
     */
    private static final String decrementStockDirect = "decrementStockDirect";

    /**
     * 账册商品锁库前缀
     */
    private static final String ACCOUNT_GOODS_LOCK_PRE = "ccs:stock:goods_";

    /**
     * 账册商品缓存初始化前缀
     */
    private static final String ACCOUNT_GOODS_INIT_LOCK_PRE = "ccs:stock:goods:init_";
    /**
     * 7天缓存
     */
    private static final Long TTL_SEVEN_DAYS_SECOND = 7 * 24 * 60 * 60L;

    @Resource(name = "storeGoodsRedisTemplate")
    private RedisTemplate storeRedisTemplate;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @Autowired
    private DefaultRedisScript defaultRedisScript;
    @Autowired
    private RedisLockUtils redisLockUtils;


    /**
     * 初始化料号库存数据
     *
     * @param orderInfoDto 订单信息
     */
    public void initLock(OrderInfoDto orderInfoDto) throws Exception {

        Long customsBookId = orderInfoDto.getAccountBookId();
        String productId = orderInfoDto.getProductId();
        String goodsSeqNo = orderInfoDto.getGoodsSeqNo();
        /**
         * No.0 缓存存在则直接返回
         */
        String key = this.getKey(customsBookId, productId, goodsSeqNo);
        log.info("查询key:{} 是否存在", key);
        Boolean exist = storeRedisTemplate.hasKey(key);
        if (exist) {
            log.info("key:{} 存在", key);
            return;
        }
        log.info("key:{} 不存在 初始化一个", key);
        // 初始化数据
        final CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findIdByBookIdAndProIdAndSeq(customsBookId, productId, goodsSeqNo);
        if (customsBookItemDTO == null) {
            String message = "账册：" + customsBookId + "，料号：" + productId + "，序号：" + goodsSeqNo + "，不存在或未启用";
            throw new StockException(ExceptionCodeEnum.STOCK_INVALID.getCode(), message);
        }
        /**
         * No.1 初始化下账册库存缓存
         */
        String initKey = ACCOUNT_GOODS_INIT_LOCK_PRE + key;
        // 这里处理1分钟吧，这个已经够长了
        Long timeout = System.currentTimeMillis() + 1 * 60 * 1000L;
        try {
            if (redisLockUtils.lock(initKey, timeout)) {
                log.info("初始化账册库存缓存开始：key：{}", initKey);
                OrderStoreCacheDto orderStoreCacheDto = new OrderStoreCacheDto();
                orderStoreCacheDto.setTotalNum(Optional.ofNullable(customsBookItemDTO.getAccountNum()).orElse(0));
                orderStoreCacheDto.setOccupyNum(Optional.ofNullable(customsBookItemDTO.getOccupiedNum()).orElse(0));
                orderStoreCacheDto.setUsedNum(Optional.ofNullable(customsBookItemDTO.getUsedNum()).orElse(0));
                orderStoreCacheDto.setAvailableNum(Optional.ofNullable(customsBookItemDTO.getAvailableNum()).orElse(0));
                orderStoreCacheDto.setLockedNum(Optional.ofNullable(customsBookItemDTO.getLockedNum()).orElse(0));
                // 更新redis库存
                this.initRedisKey(key, orderStoreCacheDto);
                log.info("初始化账册库存缓存完成：key：{}", initKey);
                return;
            } else {
                log.info("等待初始化下账册库存缓存完成：key：{}", initKey);
                /**
                 * No.2 既然有程序在处理初始化库存缓存，这里就等着就好，等处理好了key就返回完事
                 * 这里做下判断，等会还没有获取到缓存key(这个时间已经很长了)，就直接返回
                 */
                int times = 1;
                // 这里如果
                Thread.sleep(60);
                while (times <= 3 && !storeRedisTemplate.hasKey(key)) {
                    Thread.sleep(60);
                    times++;
                    continue;
                }
            }
        } catch (Exception ex) {
            log.error("初始化账册缓存数据，异常：{}，堆栈：", ex.getMessage(), ex);
            throw ex;
        } finally {
            redisLockUtils.unlock(initKey, timeout);
        }
    }

    /**
     * 初始化redis，这里保持7天的有效期
     *
     * @param key                key
     * @param orderStoreCacheDto 库存信息
     */
    public void initRedisKey(String key, OrderStoreCacheDto orderStoreCacheDto) {

        storeRedisTemplate.opsForValue().set(key, orderStoreCacheDto, TTL_SEVEN_DAYS_SECOND, TimeUnit.SECONDS);
    }

    /**
     * 获取缓存key
     *
     * @param customsBookId 账册ID
     * @param productId     料号
     * @param goodsSeqNo    序号
     * @return
     */
    public String getKey(Long customsBookId, String productId, String goodsSeqNo) {
        return ACCOUNT_GOODS_LOCK_PRE + customsBookId + "_" + productId + "_" + goodsSeqNo;
    }

    /**
     * 占用商品库存数量
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto occupyStore(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, occupy), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("occupyStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));
        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }


    /**
     * 删除缓存key
     *
     * @param orderInfoDto
     * @return
     */
    public void delKey(OrderInfoDto orderInfoDto) {
        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());
        storeRedisTemplate.delete(key);
    }

    /**
     * 取消占用商品库存数量
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto cancelOccupyStore(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, cancelOccupy), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("cancelOccupyStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }


    /**
     * 占用转已用，商品库存数量
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto occupyToUsedStore(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, occupyToUsed), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("occupyToUsedStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }


    /**
     * 已用转占用，商品库存数量
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto usedToOccupyStore(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, usedToOccupy), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("usedToOccupyStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }

    /**
     * 直接增加库存
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto incrementStoreDirect(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, incrementStockDirect), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("incrementStoreDirect-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }

    /**
     * 直接扣减库存
     *
     * @param orderInfoDto 订单信息
     */
    public OrderStoreCacheDto decrementStoreDirect(OrderInfoDto orderInfoDto) throws Exception {

        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, decrementStockDirect), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("decrementStoreDirect-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }


    /**
     * 可用转已用
     *
     * @param orderInfoDto
     * @return
     * @throws Exception
     */
    public OrderStoreCacheDto availableToUsedStore(OrderInfoDto orderInfoDto) throws Exception {
        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, availableToUsed), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("availableToUsedStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }

    /**
     * 已用转可用
     *
     * @param orderInfoDto
     * @return
     * @throws Exception
     */
    public OrderStoreCacheDto usedToAvailableStore(OrderInfoDto orderInfoDto) throws Exception {
        // 初始化库存
        this.initLock(orderInfoDto);

        String key = this.getKey(orderInfoDto.getAccountBookId(), orderInfoDto.getProductId(), orderInfoDto.getGoodsSeqNo());

        // Lua脚本执行，注意 keys：第一位为操作redis-key，第二位为操作类型，
        OrderStoreCacheDto orderStoreCacheDto = (OrderStoreCacheDto) storeRedisTemplate.execute(
                defaultRedisScript, Arrays.asList(key, usedToAvailable), orderInfoDto.getNum(), TTL_SEVEN_DAYS_SECOND, orderInfoDto.getNegativeStockEnable());
        log.info("usedToAvailableStore-更新缓存后的数据：key：{}，value：{}", key, JSON.toJSONString(orderStoreCacheDto));

        // 商品料号操作记录
        StockContextUtil.setGoodsOpsRec(orderInfoDto, orderStoreCacheDto);
        return orderStoreCacheDto;
    }
}
