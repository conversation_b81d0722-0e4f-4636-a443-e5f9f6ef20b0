package com.danding.cds.exception.filter;

import com.alibaba.fastjson.JSON;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;

import java.io.PrintWriter;
import java.io.StringWriter;

/**
 * @program: cds-center
 * @description: 全局异常dubbo
 * @author: 潘本乐（Belep）
 * @create: 2021-10-25 10:25
 **/
@Slf4j
@Activate(group = {"provider", "consumer"})
public class GlobalExceptionDubboFilter implements Filter {
    /**
     * Make sure call invoker.invoke() in your implementation.
     *
     * @param invoker
     * @param invocation
     */
    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {

        try {
            return invoker.invoke(invocation);
        } catch (ArgsErrorException ex) {
            printLog(invocation, ex, "业务参数校验异常");
            throw ex;
        } catch (Exception ex) {
            printLog(invocation, ex, "系统异常或业务异常");
            throw ex;
        } catch (Throwable throwable) {
            printLog(invocation, throwable, "系统异常");
            throw throwable;
        }
    }

    private void printLog(Invocation invocation, Throwable throwable, String desc) {

        String argumentsJoin = "";
        final Object[] arguments = invocation.getArguments();
        if (arguments != null) {
            argumentsJoin = JSON.toJSONString(arguments);
        }
        String exMessage = exceptionStackTraceAsString(throwable);
        log.warn("{} - 服务名 - {} ; 方法名 - {} ; 入参 - {} ; 异常 - {}", desc, invocation.getServiceName(), invocation.getMethodName(), argumentsJoin, exMessage);
    }

    private String exceptionStackTraceAsString(Throwable t) {
        StringWriter sw = new StringWriter();
        t.printStackTrace(new PrintWriter(sw));
        return sw.toString().replaceAll("\r", " ").replaceAll("\n", "--->");
    }
}
