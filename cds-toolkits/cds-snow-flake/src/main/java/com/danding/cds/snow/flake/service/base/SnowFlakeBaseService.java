package com.danding.cds.snow.flake.service.base;

import com.danding.cds.snow.flake.bean.SnowFlake;
import com.danding.cds.snow.flake.common.util.IDTypeUtils;
import com.danding.cds.snow.flake.util.IpUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: 雪花ID
 * @author: 潘本乐（Belep）
 * @create: 2021-10-15 16:11
 **/
@Slf4j
@Service
public class SnowFlakeBaseService {

    public static final String key = "ccs:MachineAllKeySet";
    public static final String machineKey = "ccs:MachineKey:";

    @Autowired
    private StringRedisTemplate stringListRedisTemplate;

    private final Map<Integer, SnowFlake> snowFlakeMap = new ConcurrentHashMap<>();

    private SnowFlake getSnowFlake(int idType) {
        SnowFlake snowFlake = snowFlakeMap.get(idType);
        if (snowFlake == null) {
            snowFlake = new SnowFlake(idType, machine_id);
            snowFlakeMap.put(idType, snowFlake);
        }
        return snowFlake;
    }

    public long getNextId(int idType) {
        SnowFlake snowFlake = getSnowFlake(idType);
        return snowFlake.nextId();
    }

    public List<Long> getBatchIds(int idType, int count) {
        SnowFlake snowFlake = getSnowFlake(idType);
        List<Long> longList = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            Long id = snowFlake.nextId();
            longList.add(id);
        }
        return longList;
    }

    /**
     * 机器ID,每个部署的服务这个ID必须不同，且小于31
     */
    private static int machine_id;

    public void getMachineId() {

        try {
            String machineIP = IpUtil.getMachineIp();
            if (machineIP == null) {
                throw new RuntimeException("未获取到本机器IP，ip为NULL");
            }
            log.info("当前机器的IP地址为：" + machineIP);
            Set<String> stringList = stringListRedisTemplate.opsForSet().members(key);
            if (stringList == null || stringList.size() < 1) {
                machine_id = 0;
                String value = machineIP + "#" + machine_id;
                stringListRedisTemplate.opsForSet().add(key, value);
                String singleMachineKey = machineKey + machineIP;
                stringListRedisTemplate.opsForValue().set(singleMachineKey, String.valueOf(System.currentTimeMillis()));
            } else {
                boolean flag = false;
                for (String string : stringList) {
                    if (string.contains(machineIP)) {
                        String value = string.substring(string.indexOf("#") + 1);
                        machine_id = Integer.valueOf(value);
                        flag = true;
                    }
                }
                if (!flag) {
                    if (stringList.size() > IDTypeUtils.MAX_MACHINE_NUM) {
                        throw new RuntimeException("雪花算法机器码已达上限，请联系管理员");
                    }
                    machine_id = this.matchMachineId(stringList);
                    String value = machineIP + "#" + machine_id;
                    stringListRedisTemplate.opsForSet().add(key, value);
                    String singleMachineKey = machineKey + machineIP;
                    stringListRedisTemplate.opsForValue().set(singleMachineKey, String.valueOf(System.currentTimeMillis()));
                }
            }

            // 添加心跳任务
            addHeartBeatTask(machineIP);
            log.info("当前机器 ip - {} ,的machine_id为：{}", machineIP, machine_id);
        } catch (Exception e) {
            log.error("雪花算法获取机器ID出错：{}", e.getMessage(), e);
            throw new RuntimeException("雪花算法获取机器ID出错" + e.getMessage());
        }
    }

    private int matchMachineId(Set<String> machineIpSet) {

        List<Integer> machineId = machineIpSet.stream()
                .map(k -> {
                    String index = k.substring(k.indexOf("#") + 1);
                    return Integer.valueOf(index);
                }).sorted().collect(Collectors.toList());
        for (int i = 0; i < IDTypeUtils.MAX_MACHINE_NUM; i++) {
            if (!machineId.contains(i)) {
                return i;
            }
        }
        return 0;
    }

    /**
     * 添加心跳任务，检查当前生成I的服务是否存活
     *
     * @param ip
     */
    public void addHeartBeatTask(String ip) {
        ScheduledThreadPoolExecutor poolExecutor = new ScheduledThreadPoolExecutor(1);
        poolExecutor.scheduleWithFixedDelay(() -> {
            String key = machineKey + ip;
            String value = String.valueOf(System.currentTimeMillis());
            stringListRedisTemplate.opsForValue().set(key, value);
            log.info("发送当前机器心跳 , IP - {}, 存储到Redis-key - {} , value : {}", ip, key, value);
        }, 2, 2, TimeUnit.MINUTES);
    }


}
