package com.danding.cds.snow.flake.util;

import java.net.InetAddress;
import java.net.InterfaceAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.List;

/**
 * @program: cds-center
 * @description: ip工具
 * @author: 潘本乐（Belep）
 * @create: 2021-10-25 22:14
 **/
public class IpUtil {

    public static String getMachineIp() throws SocketException {

        String machineIP = null;
        Enumeration<NetworkInterface> networkInterfaces = NetworkInterface.getNetworkInterfaces();
        while (networkInterfaces.hasMoreElements()) {
            NetworkInterface networkInterface = networkInterfaces.nextElement();
            List<InterfaceAddress> interfaceAddresses = networkInterface.getInterfaceAddresses();
            for (InterfaceAddress interfaceAddress : interfaceAddresses) {
                InetAddress inetAddress = interfaceAddress.getAddress();
                if (inetAddress.isLinkLocalAddress() || inetAddress.isAnyLocalAddress() || inetAddress.isLoopbackAddress()) {
                    continue;
                }
                machineIP = inetAddress.getHostAddress();
                break;
            }
        }
        return machineIP;
    }

}
