package com.danding.cds.snow.flake.config;

import com.danding.cds.snow.flake.service.base.SnowFlakeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 自动
 * @author: 潘本乐（Belep）
 * @create: 2021-10-20 21:02
 **/
@SpringBootConfiguration
public class SnowFlakeAutoConfig implements Serializable {

    public final static String ONLINE = "PRO";
    public final static String TEST = "FAT";

    /**
     * 启动后加载下雪花相关信息
     */
    @Bean
    public CommandLineRunner commandLineRunner() {
        return new CommandLineRunner() {

            @Autowired
            private SnowFlakeBaseService snowFlakeBaseService;

            @Override
            public void run(String... args) {
                if (!isOnline() && !isTest()) {
                    return;
                }
                snowFlakeBaseService.getMachineId();
            }
        };
    }

    public static Boolean isOnline() {
        return ONLINE.equalsIgnoreCase(System.getenv("ENV")) || ONLINE.equalsIgnoreCase(System.getProperty("env"));
    }

    public static Boolean isTest() {
        return TEST.equalsIgnoreCase(System.getenv("ENV")) || TEST.equalsIgnoreCase(System.getProperty("env"));
    }
}
