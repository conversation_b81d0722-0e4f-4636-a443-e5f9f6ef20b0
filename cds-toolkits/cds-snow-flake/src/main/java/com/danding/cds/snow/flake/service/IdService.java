package com.danding.cds.snow.flake.service;

import com.danding.cds.snow.flake.common.constant.IDTypeCons;
import com.danding.cds.snow.flake.service.base.SnowFlakeBaseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @program: cds-center
 * @description: ID服务
 * @author: 潘本乐（Belep）
 * @create: 2021-10-15 22:00
 **/
@Service
public class IdService {

    @Autowired
    private SnowFlakeBaseService snowFlakeBaseService;

    /**
     * 全平台不重复ID
     *
     * @return ID
     */
    public long getUniqueId() {
        return snowFlakeBaseService.getNextId(IDTypeCons.UniqueID);
    }

    /**
     * 批量获取id
     * @param count
     * @return
     */
    public List<Long> getBatchIds(int count) {
        return snowFlakeBaseService.getBatchIds(IDTypeCons.UniqueID, count);
    }

}
