package com.danding.cds.es.search;

import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.api.dto.GoodsRecordStatusEnum;
import com.danding.core.tenant.SimpleTenantHelper;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yousx
 * @Date: 2024/05/06
 * @Description:
 */
public class GoodsRecordEsBuilder implements Serializable {

    public static BoolQueryBuilder getBoolQueryBuilder(GoodsRecordSearchCondition search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long tenantryId = SimpleTenantHelper.getTenantId();
        //saas租户
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        //用户
        if (Objects.nonNull(search.getTenantId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantId", search.getTenantId()));
        }
        //来源
        if (Objects.nonNull(search.getSource())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("goodsSource", search.getSource()));
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        boolQueryBuilder.filter(QueryBuilders.boolQuery().mustNot(QueryBuilders.matchQuery("recordStatus", GoodsRecordStatusEnum.WAIT_COMMIT.getCode())));
        //状态查询与口岸查询
        statusAndCustomsCode(search, boolQueryBuilder);
        //备案名称
        if (Objects.nonNull(search.getGoodsRecordName())) {
            boolQueryBuilder.filter(QueryBuilders.wildcardQuery("goodsRecordName", "*" + search.getGoodsRecordName() + "*"));
        }
        if (Objects.nonNull(search.getGoodsRecordTag())) {
            Integer goodsRecordTag = search.getGoodsRecordTag();
            QueryBuilder orderQuery = QueryBuilders.boolQuery()
                    .must(QueryBuilders.matchQuery("goodsRecordTagList", goodsRecordTag));
            boolQueryBuilder.must(orderQuery);
        }
        //搜索下拉类型
        if (Objects.nonNull(search.getSearchKey())) {
            List<String> searchType = Lists.newArrayList(search.getSearchKey().split(","));
            List<String> list = searchType.stream().distinct().collect(Collectors.toList());
            ;
            if ("1".equals(search.getSearchType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("skuId", list));
                boolQueryBuilder.filter(outOrderNoBuilder);
            } else if ("2".equals(search.getSearchType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("barCode", list));
                boolQueryBuilder.filter(outOrderNoBuilder);
            }
        }
        //启用状态
        if (Objects.nonNull(search.getEnable())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("enable", search.getEnable()));
        }
        //货品id
        if (Objects.nonNull(search.getGoodsCode())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            List<String> goodsCode = Lists.newArrayList(search.getGoodsCode().split(","));
            outOrderNoBuilder.should(QueryBuilders.termsQuery("goodsCode", goodsCode.stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(outOrderNoBuilder);
        }
        //创建时间
        Long customsPassFrom = LongUtil.getFrom(search.getRecordTimeFrom(), search.getRecordTimeTo());
        Long customsPassTo = LongUtil.getEnd(search.getRecordTimeFrom(), search.getRecordTimeTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(customsPassFrom).to(customsPassTo));

        }
        //创建时间
        Long finishedTimeFrom = LongUtil.getFrom(search.getFinishedTimeFrom(), search.getFinishedTimeTo());
        Long finishedTimeTo = LongUtil.getEnd(search.getFinishedTimeFrom(), search.getFinishedTimeTo());
        if (!LongUtil.isNone(finishedTimeFrom) && !LongUtil.isNone(finishedTimeTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("recordFinishTime").from(finishedTimeFrom).to(finishedTimeTo));
        }
        //海关备案料号
        if (Objects.nonNull(search.getProductId())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            search.setProductIdList(Arrays.asList(search.getProductId().split(",")));
            outOrderNoBuilder.should(QueryBuilders.termsQuery("productId", search.getProductIdList().stream().distinct().collect(Collectors.toList())));
            boolQueryBuilder.filter(outOrderNoBuilder);

        }
        //外部料号
        if (Objects.nonNull(search.getExternalProductId())) {
            BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
            List<String> externalProductIdList = Lists.newArrayList(search.getExternalProductId().split(","));
            List<String> list = externalProductIdList.stream().distinct().collect(Collectors.toList());
            outOrderNoBuilder.should(QueryBuilders.termsQuery("externalProductId", list));
            boolQueryBuilder.filter(outOrderNoBuilder);

        }
        //通关料号
        if (!CollectionUtils.isEmpty(search.getWarehouseExternalProductIdList())) {
            List<String> list = search.getWarehouseExternalProductIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder declareProductQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordWarehouseDOS.customsDeclareProductId", list)),
                    ScoreMode.None);
            boolQueryBuilder.must(declareProductQuery);
        }
        //海关通关料号
        if (!CollectionUtils.isEmpty(search.getCustomsRecordProductIdList())) {
            List<String> list = search.getCustomsRecordProductIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordProducts",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordProducts.customsRecordProductId", list)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        //口岸
        if (Objects.nonNull(search.getCustomDistrict())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict())),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        //CCS实体仓编码
        if (Objects.nonNull(search.getWarehouseSn())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.matchQuery("esRecordWarehouseDOS.wmsWarehouseCode", search.getWarehouseSn())),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
            if (Objects.nonNull(search.getCustomsBookId())) {
                QueryBuilder bookQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordWarehouseDOS.customsBookId", search.getCustomsBookId())),
                        ScoreMode.None);
                boolQueryBuilder.must(bookQuery);
            }
        }
        if (Objects.nonNull(search.getCustomsBookId())) {
            List<Long> bookIds = new ArrayList<>(Arrays.asList(search.getCustomsBookId()));
            search.setRoleAccountBookIdList(bookIds);
        }
        if (Objects.nonNull(search.getRoleAccountBookIdList())) {
            List<Long> roleAccountBookIdList = search.getRoleAccountBookIdList().stream().distinct().collect(Collectors.toList());
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordWarehouseDOS",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termsQuery("esRecordWarehouseDOS.customsBookId", roleAccountBookIdList)),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        if (Objects.nonNull(search.getAuditWay())) {
            boolQueryBuilder.must(QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.termQuery("esRecordCustomsDO.auditWay", search.getAuditWay())),
                    ScoreMode.None));
        }
        // 外部货品id
        if (StringUtils.isNotBlank(search.getExternalGoodsIdList())) {
            List<String> externalGoodsIdList = Splitter.on(",").splitToList(search.getExternalGoodsIdList());
            boolQueryBuilder.filter(QueryBuilders.termsQuery("externalGoodsId", externalGoodsIdList.stream().distinct().collect(Collectors.toList())));
        }
        // 更新时间
        if (Objects.nonNull(search.getUpdateTimeFrom()) && Objects.nonNull(search.getUpdateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("updateTime").from(search.getUpdateTimeFrom()).to(search.getUpdateTimeTo()));
        }
        // 关务备注
        if (StringUtils.isNotBlank(search.getGuanWuRemark())) {
            QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.wildcardQuery("esRecordCustomsDO.guanWuRemark", "*" + search.getGuanWuRemark() + "*")),
                    ScoreMode.None);
            boolQueryBuilder.must(orderQuery);
        }
        return boolQueryBuilder;
    }

    private static void statusAndCustomsCode(GoodsRecordSearchCondition search, BoolQueryBuilder boolQueryBuilder) {

        if (Objects.nonNull(search.getRecordStatus()) && !Objects.equals(search.getRecordStatus(), 0)) {
            if (Objects.equals(search.getRecordStatus(), 1) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict()))
                                .must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 1)).must(QueryBuilders.termQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 1) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 1)).must(QueryBuilders.termQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 2) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 2)).must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 1))
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 2) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.termQuery("esRecordCustomsDO.submitType", 2)).must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 1)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }

            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DATE, -2);
            // TODO: 2022/6/10 查询大于48小时的数据
            if (Objects.equals(search.getRecordStatus(), 3) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery().must(QueryBuilders.matchQuery("esRecordCustomsDO.customsCode", search.getCustomDistrict()))
                                .filter(QueryBuilders.rangeQuery("esRecordCustomsDO.submitTime").lt(calendar.getTimeInMillis())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 3) && Objects.isNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .filter(QueryBuilders.rangeQuery("esRecordCustomsDO.submitTime").lt(calendar.getTimeInMillis())),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery).filter(QueryBuilders.termQuery("recordStatus", 1));
            }
            if (Objects.equals(search.getRecordStatus(), 4) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 2)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery);
            }
            if (Objects.equals(search.getRecordStatus(), 5) && Objects.nonNull(search.getCustomDistrict())) {
                QueryBuilder orderQuery = QueryBuilders.nestedQuery("esRecordCustomsDO",
                        QueryBuilders.boolQuery()
                                .must(QueryBuilders.matchQuery("esRecordCustomsDO.status", 4)),
                        ScoreMode.None);
                boolQueryBuilder.must(orderQuery);
            }
            if (Objects.equals(search.getRecordStatus(), 4) && Objects.isNull(search.getCustomDistrict())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 2));
            }
            if (Objects.equals(search.getRecordStatus(), 5) && Objects.isNull(search.getCustomDistrict())) {
                boolQueryBuilder.filter(QueryBuilders.termQuery("recordStatus", 4));
            }
        }
    }
}
