package com.danding.cds.es.search;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.invenorder.api.enums.InventoryOrderStepEnum;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.core.tenant.SimpleTenantHelper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: yousx
 * @Date: 2024/05/06
 * @Description:
 */
public class CustomsInventoryEsBuilder implements Serializable {
    public static BoolQueryBuilder getBoolQueryBuilder(SingleInvtOrderSearch search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long tenantryId = SimpleTenantHelper.getTenantId();
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        // 账册列表
        List<Long> accountBookIds = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIds)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountBookId", accountBookIds));
        }
        //账册编号
        if (!LongUtil.isNone(search.getAccountBookId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountBookId", search.getAccountBookId()));
        }
        //备案名称
        if (Objects.nonNull(search.getNote())) {
            boolQueryBuilder.filter(QueryBuilders.matchQuery("note", search.getNote()));
        }
        //创建时间
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createFrom).to(createTo));
        }
        //申报成功时间
        Long customsPassFrom = LongUtil.getFrom(search.getCustomsPassFrom(), search.getCustomsPassTo());
        Long customsPassTo = LongUtil.getEnd(search.getCustomsPassFrom(), search.getCustomsPassTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("customsPassTime").from(customsPassFrom).to(customsPassTo));
        }
        //申报时间
        Long lastDeclareFrom = LongUtil.getFrom(search.getLastDeclareFrom(), search.getLastDeclareTo());
        Long lastDeclareTo = LongUtil.getEnd(search.getLastDeclareFrom(), search.getLastDeclareTo());
        if (!LongUtil.isNone(lastDeclareFrom) && !LongUtil.isNone(lastDeclareTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastDeclareTime").from(lastDeclareFrom).to(lastDeclareTo));
        }
        //回执时间
        Long lastReceiveFrom = LongUtil.getFrom(search.getLastReceiveFrom(), search.getLastReceiveTo());
        Long lastReceiveTo = LongUtil.getEnd(search.getLastReceiveFrom(), search.getLastReceiveTo());
        if (!LongUtil.isNone(lastReceiveFrom) && !LongUtil.isNone(lastReceiveTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastCustomsTime").from(lastReceiveFrom).to(lastReceiveTo));
        }
        if (!LongUtil.isNone(search.getEbcId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebcId", search.getEbcId()));
        }
        if (!LongUtil.isNone(search.getEbpId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebpId", search.getEbpId()));
        }
        if (!LongUtil.isNone(search.getAgentCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("agentCompanyId", search.getAgentCompanyId()));
        }
        if (!LongUtil.isNone(search.getAreaCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("areaCompanyId", search.getAreaCompanyId()));
        }
        if (search.getStatus() != null && search.getStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", search.getStatus()));
        }
        if (!StringUtils.isEmpty(search.getCustomsStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", search.getCustomsStatus()));
        }
        if (search.getExitRegionStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", search.getExitRegionStatus()));
        }
        if (search.getHandoverStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("handoverStatus", search.getHandoverStatus()));
        }
        if (!CollectionUtils.isEmpty(search.getUser())) {
            BoolQueryBuilder tenantOuterIdBuilder = new BoolQueryBuilder();
            tenantOuterIdBuilder.should(QueryBuilders.termsQuery("tenantOuterId", search.getUser()));
            tenantOuterIdBuilder.should(QueryBuilders.termsQuery("tenantOuterId.keyword", search.getUser()));
            boolQueryBuilder.filter(tenantOuterIdBuilder);
        }
        if (search.getAfterStatus() != null && search.getAfterStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("afterSalesStatus", search.getAfterStatus()));
        }
        if (search.getReviwStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reviewStatus", search.getReviwStatus()));
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(search.getInveOrderStep())) {
            if (InventoryOrderStepEnum.STEP_CANCLE.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可撤清单：已放行，未出区
                boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", CustomsStat.CUSTOMS_PASS));
                boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 0));
            } else if (InventoryOrderStepEnum.STEP_RETURN.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可退清单：已放行，已出区
                boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", CustomsStat.CUSTOMS_PASS));
                boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 1));
            }
        }
        if (Objects.nonNull(search.getCancelStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("cancelStatus", search.getCancelStatus()));
        }
        if (Objects.nonNull(search.getRefundStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("refundStatus", search.getRefundStatus()));
        }
        if (!LongUtil.isNone(search.getCancelCreateTimeFrom()) && !LongUtil.isNone(search.getCancelCreateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("cancelCreateTime").from(search.getCancelCreateTimeFrom()).to(search.getCancelCreateTimeTo()));
        }
        if (!LongUtil.isNone(search.getRefundCreateTimeFrom()) && !LongUtil.isNone(search.getRefundCreateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("refundCreateTime").from(search.getRefundCreateTimeFrom()).to(search.getRefundCreateTimeTo()));
        }
        if (CollUtil.isNotEmpty(search.getCustomsInventorySnList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("sn.keyword", search.getCustomsInventorySnList()));
        }

        if (Objects.nonNull(search.getTaxBillStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("taxBillStatus", search.getTaxBillStatus()));
        }
        if (Objects.nonNull(search.getAssureCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("assureCompanyId", search.getAssureCompanyId()));
        }
        if (Objects.nonNull(search.getCheckOutStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("checkOutStatus", search.getCheckOutStatus()));
        }
        if (!LongUtil.isNone(search.getCheckOutTimeFrom()) && !LongUtil.isNone(search.getCheckOutTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("checkOutTime").from(search.getCheckOutTimeFrom()).to(search.getCheckOutTimeTo()));
        }
        if (CollUtil.isNotEmpty(search.getAccountBookIdList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountBookId", search.getAccountBookIdList()));
        }
        if (!StringUtils.isEmpty(search.getQueryInfo())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if (!CollectionUtils.isEmpty(noList)) {
                if ("declareOrderNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("declareOrderNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("inventoryNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("inventoryNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("logisticsNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("logisticsNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("productId".equals(search.getQueryType())) {
                    QueryBuilder orderQuery = QueryBuilders.nestedQuery("itemExtras",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery("itemExtras.productId.keyword", noList)),
                            ScoreMode.None);
                    boolQueryBuilder.must(orderQuery);
                }
            }
        }
        if (Objects.nonNull(search.getContainFbGifts())) {
            Map<String, Object> params = new HashMap(1) {{
                put("itemTag", OrderItemTagEnum.FB_GIFTS.getCode());
            }};
            String script = "return ((doc['itemExtras.itemTag'].value & params.itemTag) != 0);";
            Script orderTagsScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
            QueryBuilder orderTagQuery = QueryBuilders.nestedQuery("itemExtras",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.existsQuery("itemExtras.itemTag"))
                            .must(QueryBuilders.scriptQuery(orderTagsScript)),
                    ScoreMode.None);
            if (Objects.equals(search.getContainFbGifts(), true)) {
                boolQueryBuilder.must(orderTagQuery);
            } else {
                boolQueryBuilder.mustNot(orderTagQuery);
            }
        }
        return boolQueryBuilder;
    }
}
