package com.danding.cds.es.search;

import cn.hutool.core.collection.CollectionUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.core.tenant.SimpleTenantHelper;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.springframework.util.CollectionUtils;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @Author: yousx
 * @Date: 2024/05/06
 * @Description:
 */
public class OrderEsBuilder implements Serializable {

    public static BoolQueryBuilder getBoolQueryBuilder(OrderSearch search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long tenantryId = SimpleTenantHelper.getTenantId();
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        if (!StringUtils.isEmpty(search.getQueryInfo())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if ("outOrderNo".equals(search.getQueryType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("outOrderNo", noList));
                boolQueryBuilder.filter(outOrderNoBuilder);
            } else if ("declareOrderNo".equals(search.getQueryType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("declareOrderNo", noList));
                boolQueryBuilder.filter(outOrderNoBuilder);
            } else if ("inventoryNo".equals(search.getQueryType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("inventoryNo", noList));
                boolQueryBuilder.filter(outOrderNoBuilder);
            } else if ("logisticsNo".equals(search.getQueryType())) {
                BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                outOrderNoBuilder.should(QueryBuilders.termsQuery("logisticsNo", noList));
                boolQueryBuilder.filter(outOrderNoBuilder);
            }
        }
        if (!StringUtils.isEmpty(search.getTenantId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantOuterId", search.getTenantId()));
        }
        if (!StringUtils.isEmpty(search.getRouteCode())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("routeCode", search.getRouteCode()));
        }
        if (search.getStatus() != null && search.getStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", search.getStatus()));
        }
        List<String> manyStatus = search.getManyStatus();
        if (!CollectionUtils.isEmpty(manyStatus)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("status", manyStatus));
        }
        List<String> internalStatus = search.getInternalStatus();
        if (!CollectionUtils.isEmpty(internalStatus)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("internalStatus.keyword", internalStatus));
        }
        if (Objects.nonNull(search.getInventoryOrderStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("inventoryOrderStatus", search.getInventoryOrderStatus()));
        }
        if (!LongUtil.isNone(search.getEbpId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebpId", search.getEbpId()));
        }
        if (!LongUtil.isNone(search.getExpressId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("expressId", search.getExpressId()));
        }
        if (!LongUtil.isNone(search.getAccountBookId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountBookId", search.getAccountBookId()));
        }

        if (!LongUtil.isNone(search.getEbcId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebcId", search.getEbcId()));
        }
        if (!LongUtil.isNone(search.getAssureCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("assureCompanyId", search.getAssureCompanyId()));
        }
        if (!LongUtil.isNone(search.getLogisticsCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("logisticsCompanyId", search.getLogisticsCompanyId()));
        }

        Long customsPassFrom = LongUtil.getFrom(search.getCustomsPassFrom(), search.getCustomsPassTo());
        Long customsPassTo = LongUtil.getEnd(search.getCustomsPassFrom(), search.getCustomsPassTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("customsPassTime").from(customsPassFrom).to(customsPassTo));
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createFrom).to(createTo));
        }
        Long updateFrom = LongUtil.getFrom(search.getUpdateFrom(), search.getUpdateTo());
        Long updateTo = LongUtil.getEnd(search.getUpdateFrom(), search.getUpdateTo());
        if (!LongUtil.isNone(updateFrom) && !LongUtil.isNone(updateTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("updateTime").from(updateFrom).to(updateTo));
        }
        if (search.getExceptionFlag() != null && search.getExceptionFlag() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("exceptionFlag", search.getExceptionFlag() == 1));
        }
        if (!CollectionUtils.isEmpty(search.getExceptionTypes())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("exceptionType", search.getExceptionTypes()));
        }
        // 账册列表
        List<Long> accountBookIds = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIds)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountBookId", accountBookIds));
        }
        // 申报超时时长获查询
        String declareHour = search.getDeclareTimeOut();
        if (StringUtils.isNotBlank(declareHour)) {
            BoolQueryBuilder declareQueryBuild = getDeclareTimeOutQueryBuilder(declareHour);
            boolQueryBuilder.filter(declareQueryBuild);
        }
        //挂起标志  数组多选
        List<Integer> hangUpStatusList = search.getHangUpStatus();
        if (!CollectionUtils.isEmpty(search.getHangUpStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("hangUpStatus", hangUpStatusList));
        }
        // 履约超时时间查询
        String performanceTimeOut = search.getPerformanceTimeOut();
        if (StringUtils.isNotBlank(performanceTimeOut)) {
            BoolQueryBuilder performanceTimeOutQueryBuild = getPerformanceTimeOutQueryBuilder(performanceTimeOut);
            boolQueryBuilder.filter(performanceTimeOutQueryBuild);
        }
        //申报单标记
        if (CollectionUtil.isNotEmpty(search.getOrderTags())) {
            int orderTags = 0;
            for (Integer orderTag : search.getOrderTags()) {
                orderTags += orderTag;
            }
            BoolQueryBuilder orderTagsQueryBuilder = getOrderTagsQueryBuilder(orderTags);
            boolQueryBuilder.filter(orderTagsQueryBuilder);
        }
        if (StringUtils.isNotBlank(search.getErpPhyWarehouseSn())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("erpPhyWarehouseSn", search.getErpPhyWarehouseSn()));
        }
        return boolQueryBuilder;
    }

    /**
     * 获取申报超时查询Builder
     *
     * @param declareTimeOut 申报超时
     * @return
     */
    private static BoolQueryBuilder getDeclareTimeOutQueryBuilder(String declareTimeOut) {
        if (StringUtils.isEmpty(declareTimeOut)) {
            return null;
        }
        Integer timeOutGe = getMillisByHour(declareTimeOut);
        Map<String, Object> params = new HashMap() {{
            put("now", System.currentTimeMillis());
            put("geTime", timeOutGe);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "if(doc['inventoryOrderStatus'].value == 100){return params.geTime <= doc['customsPassTime'].value - doc['createTime'].value;} return params.geTime <= params.now - doc['createTime'].value";
        Script declareTimeScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("customsPassTime")).must(QueryBuilders.scriptQuery(declareTimeScript));
    }

    /**
     * 获取履约超时查询Builder
     *
     * @param declareTimeOut 申报超时
     * @return
     */
    private static BoolQueryBuilder getPerformanceTimeOutQueryBuilder(String declareTimeOut) {
        if (StringUtils.isEmpty(declareTimeOut)) {
            return null;
        }
        Integer timeOutGe = getMillisByHour(declareTimeOut);
        Map<String, Object> params = new HashMap() {{
            put("now", System.currentTimeMillis());
            put("geTime", timeOutGe);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "if (doc['inventoryOrderStatus'].value == 100) {" +
                "    if (doc['customsPassTime'].size() != 0) {" +
                "        return params.geTime <= doc['customsPassTime'].value - doc['orderTime'].value;" +
                "    } else {" +
                "        return false;" + // 当 customPassTime 字段不存在时的处理
                "    }" +
                "} else {" +
                "    return params.geTime <= params.now - doc['orderTime'].value;" +
                "}";
        Script performanceTimeScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("orderTime")).must(QueryBuilders.scriptQuery(performanceTimeScript));
    }

    /**
     * 获取申报单标记查询Builder
     *
     * @param orderTags 申报单标记
     * @return
     */
    private static BoolQueryBuilder getOrderTagsQueryBuilder(Integer orderTags) {
        if (Objects.isNull(orderTags)) {
            return null;
        }
        Map<String, Object> params = new HashMap() {{
            put("orderTags", orderTags);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "return ((doc['orderTags'].value & params.orderTags) != 0);";
        Script orderTagsScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("orderTags")).must(QueryBuilders.scriptQuery(orderTagsScript));
    }

    /**
     * 根据小时获取毫秒
     *
     * @param hour 小时
     * @return
     */
    private static Integer getMillisByHour(String hour) {
        if (StringUtils.isEmpty(hour)) {
            return null;
        }
        return Double.valueOf((Double.valueOf(hour) * 60 * 60 * 1000)).intValue();
    }
}
