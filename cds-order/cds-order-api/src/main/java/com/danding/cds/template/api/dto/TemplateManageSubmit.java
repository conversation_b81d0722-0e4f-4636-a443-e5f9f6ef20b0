package com.danding.cds.template.api.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 模板管理提交参数
 * @date 2025/7/31
 */
@Data
@ApiModel("模板管理提交参数")
public class TemplateManageSubmit implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID（编辑时必填）")
    private Long id;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("用途")
    private String purpose;

    @ApiModelProperty("口岸（多个以逗号分隔）")
    private String ports;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("文件URL（OSS路径）")
    private String fileUrl;

    @ApiModelProperty("文件名")
    private String fileName;
}
