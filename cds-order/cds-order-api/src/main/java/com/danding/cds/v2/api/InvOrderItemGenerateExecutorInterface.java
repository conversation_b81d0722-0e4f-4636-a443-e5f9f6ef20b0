package com.danding.cds.v2.api;

import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemParam;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.v2.enums.InvOrderItemGenTypeEnums;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 清关单表体生成
 */
public interface InvOrderItemGenerateExecutorInterface {

    void execute(InvOrderItemGenTypeEnums genType,
                 CustomsBookDTO customsBookDTO,
                 List<InventoryOrderItemDTO> listItems,
                 InventoryOrderItemParam inventoryOrderItemParam);

    InventoryOrderItemDTO execute(InvOrderItemGenTypeEnums genType,
                                  CustomsBookDTO customsBookDTO,
                                  InventoryOrderItemParam inventoryOrderItemParam);
}
