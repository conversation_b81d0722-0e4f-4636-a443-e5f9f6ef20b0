package com.danding.cds.v2.bean.dto;

import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 清关单表体校验参数
 * @date 2022/7/27 21:03
 */
@Data
public class InventoryItemVerifyParamDTO implements Serializable {
    private GoodsRecordDTO goodsRecordDTO;
    private CustomsBookItemDTO customsBookItemDTO;

    /**
     * 清关单id
     */
    private Long refInvOrderId;

    /**
     * 清关单sn
     */
    private String refInvOrderSn;

    /**
     * 账册id
     */
    private Long customsBookId;

    /**
     * 统一料号
     */
    private String productId;

    /**
     * 海关备案料号
     */
    private String customsRecordProductId;

    /**
     * 共享备案
     */
    private Boolean shareGoodsRecord;
    /**
     * 账册+料号 查询账册库存
     */
    private List<CustomsBookItemDTO> customsBookItemDTOList;

    /**
     * 清关单表体
     */
    private InventoryOrderItemDTO inventoryOrderItemDTO;

    /**
     * 业务类型
     */
    private String inveBusinessType;
}
