package com.danding.cds.v2.api;

import com.danding.cds.invenorder.api.dto.InvOrderItemCompareDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;

import java.util.List;

public interface InvItemCompareStrategyService {

    String ITEM_TYPE_BOX_INVOICE_CONTRACT = "箱单/发票/合同";
    String ITEM_TYPE_INVENTORY_ITEM = "清关单表体";
    String ITEM_TYPE_RECORD_ITEM = "商品备案/账册库存";

    List<InventoryOrderItemDTO> getCompareItemList(Long inventoryOrderId);


    List<InvOrderItemCompareDTO> compare(List<InventoryOrderItemDTO> inItemDTOList, List<InventoryOrderItemDTO> outItemDTOList);

}
