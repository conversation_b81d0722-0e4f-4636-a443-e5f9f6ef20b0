package com.danding.cds.v2.bean.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class EndorsementVO implements Serializable {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("预录入核注单号")
    private String preOrderNo;

    @ApiModelProperty("真实核注清单编号")
    private String realEndorsementOrderNo;

    @ApiModelProperty("核放单预录入编号")
    private String checklistOrderNo;

    @ApiModelProperty("企业内部编号")
    private String sn;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("海关回执状态描述")
    private String customsStatusDesc;

    @ApiModelProperty("核注清单类型")
    private String bussinessTypeDesc;

    @ApiModelProperty("账册编号")
    private String customsBookNo;

    @ApiModelProperty("清关企业")
    private String declareCompanyName;

    @ApiModelProperty("企业核放单编号")
    private String checklistSn;

    /**
     * 创建时间
     */
    private String createTime;
}
