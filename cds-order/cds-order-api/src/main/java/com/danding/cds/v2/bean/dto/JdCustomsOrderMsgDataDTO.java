package com.danding.cds.v2.bean.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JdCustomsOrderMsgDataDTO implements Serializable {
    /**
     * 订单号（单号）
     * 必填
     */
    private String orderId;

    /**
     * 运单号
     * 必填
     */
    private String waybillNo;

    /**
     * 关区编码
     * 必填
     */
    private String customsRegionId;

    /**
     * 客退类型：
     * "return"    退货类型，传商品数据
     * "rejection" 拒收类型，不传商品，服务商数据为准
     * 必填
     */
    private String type;

    /**
     * 商品信息列表：
     * 根据type区分是否必传。
     * 当type为"return"时必填，为"rejection"时可为空。
     */
    private List<RejectionReturnGoodsDto> rejectionReturnGoodsDtos;

    @Data
    public static class RejectionReturnGoodsDto implements Serializable {
        /**
         * skuID
         * 必填
         */
        private String skuId;

        /**
         * 货号
         * 必填
         */
        private String recordNo;

        /**
         * 商品名称
         * 必填
         */
        private String goodsName;

        /**
         * 商品数量
         * 必填
         */
        private Integer qty;
    }
}
