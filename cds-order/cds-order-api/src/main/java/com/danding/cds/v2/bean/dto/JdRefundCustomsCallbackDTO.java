package com.danding.cds.v2.bean.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class JdRefundCustomsCallbackDTO implements Serializable {
    /**
     * 订单号（单号）
     * 必填
     */
    private String orderId;

    /**
     * 运单号
     * 必填
     */
    private String waybillNo;


    /**
     * 客退类型：
     * "return"    退货类型，传商品数据
     * "rejection" 拒收类型，不传商品，服务商数据为准
     * 必填
     */
    private String type;

    /**
     * 清关状态：
     * 0：清关成功
     * 1：清关失败
     * 必填
     */
    private Integer clearanceStatus;

    /**
     * 清关失败原因
     * 非必填
     */
    private String clearanceFailReason;

    /**
     * 清关时间
     * 必填
     */
    private String clearanceTime;

    /**
     * 系统参数，物流开放平台 appKey
     * 非必填
     */
    private String wlOpenAppKey;
}
