package com.danding.cds.v2.bean.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 清关单料件表体
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-26
 */
@Data
@Accessors(chain = true)
@ApiModel(value = "InventoryOrderItemGoods对象", description = "清关单料件表体")
public class InventoryOrderItemGoodsDTO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 清关单ID
     */
    @ApiModelProperty(value = "清关单ID")
    private Long refInveOrderId;

    /**
     * 清关单SN
     */
    @ApiModelProperty(value = "清关单SN")
    private String refInveOrderSn;

    /**
     * 是否是新的(0:旧,1:新)
     */
    @ApiModelProperty(value = "是否是新的(0:旧,1:新)")
    private String isNew;

    /**
     * 原始料号
     */
    @ApiModelProperty(value = "原始料号")
    private String originProductId;

    /**
     * 海关备案料号
     */
    @ApiModelProperty(value = "海关备案料号")
    private String customsRecordProductId;

    /**
     * 商品料号
     */
    @ApiModelProperty(value = "商品料号")
    private String productId;

    /**
     * SKU
     */
    @ApiModelProperty(value = "SKU")
    private String skuId;

    /**
     * 账册项号
     */
    @ApiModelProperty(value = "账册项号")
    private String goodsSeqNo;

    /**
     * 记账金二序号
     */
    @ApiModelProperty(value = "记账金二序号")
    private String customsCallbackSeqNo;

    /**
     * 货品id
     */
    @ApiModelProperty(value = "货品id")
    private String goodsCode;

    /**
     * 料号名称
     */
    @ApiModelProperty(value = "料号名称")
    private String goodsName;

    /**
     * 备案名称
     */
    @ApiModelProperty(value = "备案名称")
    private String recordProductName;

    /**
     * HS代码
     */
    @ApiModelProperty(value = "HS代码")
    private String hsCode;

    /**
     * 计划申报数量
     */
    @ApiModelProperty(value = "计划申报数量")
    private BigDecimal planDeclareQty;

    /**
     * 出库单号
     */
    @ApiModelProperty(value = "出库单号")
    private String outBoundNo;

    /**
     * 实际理货数量
     */
    @ApiModelProperty(value = "实际理货数量")
    private BigDecimal actualTallyQty;

    /**
     * 申报单位数量
     */
    @ApiModelProperty(value = "申报单位数量")
    private BigDecimal declareUnitQfy;

    /**
     * 申报价格
     */
    @ApiModelProperty(value = "申报价格")
    private BigDecimal declarePrice;

    /**
     * 额外其他属性
     * {@link com.danding.cds.invenorder.api.dto.InventoryOrderItemExtra}
     */
    @ApiModelProperty(value = "额外其他属性")
    private String extraJson;

    /**
     * json 属性
     */
    @ApiModelProperty("计量单位")
    private String unit;
    @ApiModelProperty("第一单位")
    private String firstUnit;
    @ApiModelProperty("第一单位数量")
    private BigDecimal firstUnitQfy;
    @ApiModelProperty("第二单位")
    private String secondUnit;
    @ApiModelProperty("第二单位数量")
    private BigDecimal secondUnitQfy;
    @ApiModelProperty("毛重")
    private BigDecimal grossWeight;
    @ApiModelProperty("净重")
    private BigDecimal netweight;
    @ApiModelProperty("申报要素")
    private String declareFactor;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("商品条码")
    private String goodsBar;
    @ApiModelProperty("生产企业")
    private String productCompany;
    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("申报总价")//add
    private BigDecimal declareTotalPrice;

    @ApiModelProperty("报关单商品序号")
    private String declareCustomsGoodsSeqNo;//add
    @ApiModelProperty("规格型号")//add
    private String goodsModel;
    @ApiModelProperty("最终目的国")
    private String destinationCountry;//add
    @ApiModelProperty("币制")//add
    private String currency;
    @ApiModelProperty("币制")//add
    private String currencyDesc;
    @ApiModelProperty("免征方式")//add
    private String avoidTaxMethod = "3";
    @ApiModelProperty("单号版本号")//add
    private String orderVersion;

    @ApiModelProperty("计量单位描述")
    private String unitDesc;
    @ApiModelProperty("第一单位描述")
    private String firstUnitDesc;
    @ApiModelProperty("第二单位")
    private String secondUnitDesc;
    @ApiModelProperty("原产国描述")
    private String originCountryDesc;
    @ApiModelProperty("目的国描述")
    private String destinationCountryDesc;

    /**
     * 关联核注清单ID
     */
    @ApiModelProperty(value = "关联核注清单ID")
    private Long refEndorsementId;

    /**
     * 关联核注清单SN
     */
    @ApiModelProperty(value = "关联核注清单SN")
    private String refEndorsementSn;

    /**
     * 通关校验结果
     */
    @ApiModelProperty(value = "通关校验结果")
    private String verifyResult;

    /**
     * 是否涉濒危
     */
    @ApiModelProperty(value = "是否涉濒危")
    private Integer endangered;

    /**
     * 物种证明附件名称
     */
    @ApiModelProperty(value = "物种证明附件名称")
    private String speciesCertificateAttachmentName;

    /**
     * 物种证明附件URL
     */
    @ApiModelProperty(value = "物种证明附件URL")
    private String speciesCertificateAttachmentUrl;

    /**
     * 行号
     */
    @ApiModelProperty(value = "行号")
    private String idx;

    /**
     * 报关单号
     */
    @ApiModelProperty(value = "报关单号")
    private String customsEntryNo;

    /**
     * 报关日期
     */
    @ApiModelProperty(value = "报关日期")
    private Date customsEntryTime;

    /**
     * 启运国
     */
    @ApiModelProperty(value = "启运国")
    private String shipmentCountry;

    /**
     * 启运港
     */
    @ApiModelProperty(value = "启运港")
    private String fromLocation;

    /**
     * 运输方式
     */
    @ApiModelProperty(value = "运输方式")
    private String transportMode;

    /**
     * 进境口岸
     */
    @ApiModelProperty(value = "进境口岸")
    private String entryPort;

    /**
     * Sass租户ID
     */
    @ApiModelProperty(value = "Sass租户ID")
    private Long tenantryId;

    /**
     * 生化品标志  1是  0否
     */
    @ApiModelProperty(value = "生化品标志  1是  0否")
    private String dangerousFlag;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;

    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;

    @ApiModelProperty("来源标识")
    private String goodsSource;
    private String goodsSourceDesc;

    /**
     * 来源
     */
    private String source;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
}
