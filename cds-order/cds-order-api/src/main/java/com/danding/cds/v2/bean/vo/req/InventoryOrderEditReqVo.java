package com.danding.cds.v2.bean.vo.req;

import com.danding.cds.common.enums.InventoryDeclarationEnum;
import com.danding.cds.common.enums.InventoryTwoStepEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 清关单表头编辑
 * @date 2022/3/31 15:40
 */
@Data
public class InventoryOrderEditReqVo implements Serializable {
    @ApiModelProperty("主键ID")
    private Long id;
    @ApiModelProperty("业务类型")
    private String inveBusinessType;
    @ApiModelProperty("运输方式")
    private String transportMode;
    @ApiModelProperty("启运国")
    private String shipmentCountry;
    @ApiModelProperty("进出境关别")
    private String entryExitCustoms;
    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     * {@link InventoryTwoStepEnum}
     */
    @ApiModelProperty("是否两步申报")
    private Integer twoStepFlag;
    /**
     * 是否生成报关单(1:是 2:否)
     * {@link InventoryDeclarationEnum}
     */
    @ApiModelProperty("是否生成报关单")
    private Integer declarationFlag;
    @ApiModelProperty("清关方式")
    private Integer declareWay;
    @ApiModelProperty("对应报关单类型")
    private String customsEntryType;
    @ApiModelProperty("对应报关单类型")
    private String customsEntryNo;
    @ApiModelProperty("对应报关企业")
    private Long customsEntryCompany;
    @ApiModelProperty("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    private String inAccountBook;
    @ApiModelProperty("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    private String outAccountBook;
    /**
     * 指出区所需关联的外部入区核注单号
     */
    @ApiModelProperty("关联核注清单编号")
    private String associatedEndorsementNo;

    @ApiModelProperty("提单号")
    private String pickUpNo;
    @ApiModelProperty("货代公司")
    private String forwardingCompany;
    @ApiModelProperty("集装箱号")
    private String conNo;
    @ApiModelProperty("到货港口/机场")
    private String arrivalPort;
    @ApiModelProperty("品名")
    private String productName;
    @ApiModelProperty("类目")
    private String category;
    @ApiModelProperty("实际到港日期")
    private Long actualArrivalDate;
//    @ApiModelProperty("预计到港时间")
//    private Long expectedToPortTime;
//    @Deprecated
//    @ApiModelProperty("托数")
//    private Integer fightQty;
//    @ApiModelProperty("运输费")
//    private BigDecimal shippingFee;
//    @ApiModelProperty("转入实体仓")
//    private String entityInWarehouseName;
//    @ApiModelProperty("转出实体仓")
//    private String entityOutWarehouseName;
    /**
     * 前端传1==true
     * 0==false
     */
    @ApiModelProperty("货主是否自备车辆")
    private Integer selfOwnedVehicle;
    @ApiModelProperty("车牌号")
    private String licensePlate;
    @ApiModelProperty("车辆费用备注")
    private String vehicleCostRemark;
    @ApiModelProperty("转出方")
    private String transferor;
    @ApiModelProperty("转入方")
    private String transferee;

    /**
     * 清关企业(终点)
     */
    private Long finalInveCompanyId;
    /**
     * 账册ID(终点)
     */
    private Long finalBookId;
    /**
     * 实体仓编码(终点)
     */
    private String finalEntityWarehouseCode;
    /**
     * 实体仓名称(终点)
     */
    private String finalEntityWarehouseName;
    /**
     * 货主编码(终点)
     */
    private String finalOwnerCode;
    /**
     * 货主名称(终点)
     */
    private String finalOwnerName;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private Integer customsType;

    /**
     * 对应报关单申报单位
     */
    private Long corrCusDeclareCompanyId;

    /**
     * 关联报关单境内收发货人
     */
    private Long rltCusInnerSFHRCompanyId;

    /**
     * 关联报关单消费使用单位
     */
    private Long rltCusXFDYCompanyId;

    /**
     * 关联报关单申报单位
     */
    private Long rltCusDeclareCompanyId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 起运港（始发机场）
     */
    private String fromLocation;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 核注单备注
     */
    private String endorsementRemark;

    /**
     * 申报表编号
     */
    private String declareFormNo;
}
