<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>cds-order</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>

    <artifactId>cds-order-c-api</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.monitor</groupId>
            <artifactId>order-monitor-rpc</artifactId>
            <version>1.1.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-api</artifactId>
        </dependency>
    </dependencies>

</project>