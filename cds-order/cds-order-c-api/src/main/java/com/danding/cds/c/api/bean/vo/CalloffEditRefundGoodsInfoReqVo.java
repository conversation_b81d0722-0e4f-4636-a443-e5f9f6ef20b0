package com.danding.cds.c.api.bean.vo;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 申报单详情- 编辑退货商品信息
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
public class CalloffEditRefundGoodsInfoReqVo implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 申报单id
     */
    @NotNull(message = "申报单id不能为空")
    private Long orderId;

    /**
     * 条码
     */
    @NotBlank(message = "条码不能为空")
    private String barCode;

    /**
     * 数量
     */
    @NotNull(message = "数量不能为空")
    private Integer count;
}
