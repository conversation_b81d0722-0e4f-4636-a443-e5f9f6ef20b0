package com.danding.cds.c.api.rpc;

import com.danding.cds.v2.bean.dto.ReconciliationOrderDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.dto.ReconciliationTrackLogDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationOrderImportVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;
import java.util.Map;

public interface ReconciliationOrderRpc {

    ListVO<ReconciliationOrderDTO> paging(ReconciliationSearch search);

    Map<String, Integer> statusCount();

    void discard(Long id);

    void markDeal(List<Long> idList);

    List<ReconciliationTrackLogDTO> listTrackLog(Long id);

    void excelImport(ReconciliationOrderImportVO importVO);

    void refresh(List<Long> idList);

    void submitImport(ReconciliationOrderImportInfoDTO submitData);

    ReconciliationOrderDTO create(Long areaCompanyId, Long reconciliationDate, Integer size, String operator);

    void invokeDataCenterBalanceAccount(List<String> snList);
}
