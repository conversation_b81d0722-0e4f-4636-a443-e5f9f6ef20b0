package com.danding.cds.c.api.bean.dto;

import lombok.Data;

/**
 * 取消单 上游下发退款单商品信息
 *
 * <AUTHOR>
 * @date 2024/04/21
 */
@Data
public class CalloffRefundGoodsInfoDTO implements java.io.Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 货品名称
     */
    private String goodsName;
    /**
     * sku
     */
    private String sku;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 海关备案料号
     */
    private String customsProductId;
    /**
     * 正向商品数量
     */
    private Integer declareCount;
    /**
     * 退货商品数量
     */
    private Integer refundCount;
    /**
     * 异常信息
     */
    private String errorMsg;
    /**
     * 贸易类型
     * 1-保税 2-完税
     */
    private Integer tradeType;
}
