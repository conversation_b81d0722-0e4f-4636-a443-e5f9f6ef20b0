package com.danding.cds.c.api.rpc;

import com.danding.cds.v2.bean.dto.ReconciliationItemTrackLogDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderItemDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemMarkDealVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;
import java.util.Map;

public interface ReconciliationOrderItemRpc {

    List<String> findByLogisticsNo(List<String> logisticsNoList);

    ListVO<ReconciliationOrderItemDTO> paging(ReconciliationItemSearch search);


    Map<String, Integer> statusCount(String reconciliationSn);

    void discard(List<Long> idList);

    void markDeal(ReconciliationItemMarkDealVO dealVO);

    void excelImport(ReconciliationOrderImportInfoDTO dto);

    List<ReconciliationItemTrackLogDTO> listTrackLog(Long id);
}
