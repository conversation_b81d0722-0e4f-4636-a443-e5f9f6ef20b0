package com.danding.cds.c.api.rpc;


import java.util.Date;
import java.util.List;

import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.route.api.vo.RouteInfoResVo;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 17:35
 * @Description:
 */
public interface CustomsPaymentDeclareRpc {
//    List<CustomsPaymentDeclareDTO> findByCustomsPaymentId(Long customsPaymentId);

    List<CustomsPaymentDeclareDTO> findByCustomsPaymentCode(String paymentSn);

    CustomsPaymentDeclareDTO findCustomsPaymentByOutRequestNo(String customs,String outRequestNo);

    Long updateStatus(Long id, Integer status);

    Long updateStatus(Long id, Integer status, String returnMsg, String extra, String postMsg, Date returnTime);

    CustomsPaymentDeclareDTO findById(Long id);

    /**
     * 获取mq发送申报报文封装类 发送
     *
     * @param customsPayment
     * @param customsPaymentDeclare
     * @return
     */
    void createWrapPaymentInfoAndSend(CustomsPaymentDTO customsPayment, CustomsPaymentDeclareDTO customsPaymentDeclare);

    void createWrapPaymentInfoAndSend(CustomsPaymentDTO customsPayment, CustomsPaymentDeclareDTO customsPaymentDeclare, RouteInfoResVo routeDTO);

    /**
     * 申报结果回复处理 成功
     *
     * @param id
     * @param returnMsg
     * @param extra
     * @param postMsg
     */
    void doSuccessfulStatus(Long id, String returnMsg, String extra, String postMsg, String payTransactionId, String verDept, String declarePayNo);

    /**
     * 申报结果回复处理 失败
     * @param id
     * @param returnMsg
     * @param extra
     * @param postMsg
     */
    void doFailureStatus(Long id,  String returnMsg, String extra, String postMsg);

    /**
     * 手动修改支付单状态
     * @param infoDTO   支付单修改状态信息
     */
    void manualUpdStatus(CustomsPaymentDeclareDTO infoDTO);
}
