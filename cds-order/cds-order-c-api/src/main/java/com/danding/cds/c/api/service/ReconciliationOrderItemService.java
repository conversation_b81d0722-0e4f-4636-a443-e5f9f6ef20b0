package com.danding.cds.c.api.service;

import com.danding.cds.v2.bean.dto.ReconciliationItemTrackLogDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderItemDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationBalanceAccountReqVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemMarkDealVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;
import java.util.Map;

public interface ReconciliationOrderItemService {

    List<ReconciliationOrderItemDTO> findByReconciliationSn(String sn);

    List<ReconciliationOrderItemDTO> findByReconciliationSn(List<String> reconciliationSns);

    ListVO<ReconciliationOrderItemDTO> paging(ReconciliationItemSearch search);

    Map<String, Integer> statusCount(String reconciliationSn);

    void discard(List<Long> idList);

    void discardByReconciliationSn(String sn);

    void markDeal(ReconciliationItemMarkDealVO dealVO);

    List<ReconciliationItemTrackLogDTO> listTrackLog(Long id);

    void excelImport(ReconciliationOrderImportInfoDTO dto);

    void balanceAccount(ReconciliationBalanceAccountReqVO reqVO);

    List<String> findExistLogisticsNo(List<String> logisticsNoList);

    void saveRefreshLog(List<String> snList);

    void saveLog(ReconciliationItemTrackLogDTO dto);

    void saveBatchLog(List<Long> reconciliationItemIdList, String status, String logInfo, String operator);

    void saveBatchLog(List<ReconciliationItemTrackLogDTO> logDTOList);
}
