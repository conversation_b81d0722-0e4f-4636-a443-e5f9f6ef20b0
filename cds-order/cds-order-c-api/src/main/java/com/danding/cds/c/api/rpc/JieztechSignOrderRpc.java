package com.danding.cds.c.api.rpc;

import com.danding.cds.route.api.dto.RouteDTO;

/**
 * @program: cds-center
 * @description:
 * @author: 潘本乐（Belep）
 * @create: 2023-10-09 11:44
 **/
public interface JieztechSignOrderRpc {

    void saveOrUpdate(String orderNo, String logisticsNo, String copNo, String cancelCopNo, String refundCopNo, String bookNo, String buyerIdNumber, String buyerName, String buyerTelephone);

    RouteDTO getRouteByBookRouteConfig(String bookNo);

    String replaceCompanyInfo(String message, String bookNo);

    //依赖问题，传递用code先
    void saveOrUpdateAndDeclare(String message, String messageType);
}
