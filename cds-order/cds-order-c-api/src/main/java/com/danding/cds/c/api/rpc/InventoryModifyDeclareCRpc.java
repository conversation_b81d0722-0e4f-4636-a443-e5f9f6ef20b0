package com.danding.cds.c.api.rpc;

import java.util.List;

import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.v2.bean.vo.req.ModifyDeclareReqVo;
import com.danding.cds.v2.bean.vo.req.ModifyItemReqVo;
import com.danding.cds.v2.bean.vo.res.ModifyDeclareResVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

/**
 * @program: cds-center
 * @description: 变更申报RPC
 * @author: 潘本乐（Belep）
 * @create: 2021-08-16 14:23
 **/
public interface InventoryModifyDeclareCRpc {

    /**
     * 变更申报分页查询
     *
     * @param declareReqVo 请求参数
     * @return
     */
    ListVO<ModifyDeclareResVo> paging(ModifyDeclareReqVo declareReqVo);

    /**
     * 批量申报
     *
     * @param ids 变更申报主键ID列表
     */
    void batchDeclare(List<Long> ids) throws ArgsErrorException;

    /**
     * 批量删除
     *
     * @param ids 变更申报主键ID列表
     */
    void batchDelete(List<Long> ids) throws ArgsErrorException;

    /**
     * 批量取消
     *
     * @param ids 变更申报主键ID列表
     */
    void batchCancel(List<Long> ids) throws ArgsErrorException;

    /**
     * 校验运单编号，成功后导入
     *
     * @param logisticsNo 运单编号
     * @return
     */
    ImportResultResVo checkAndImport(String logisticsNo);

    void modifyItem(List<ModifyItemReqVo> collect);
}
