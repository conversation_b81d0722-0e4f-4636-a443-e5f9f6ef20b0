package com.danding.cds.c.api.rpc;

import com.danding.cds.c.api.bean.vo.CalloffEditRefundGoodsInfoReqVo;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.v2.bean.dto.JdCustomsOrderMsgDataDTO;
import com.danding.cds.v2.bean.dto.LinkCustomsRefundOrderMessageNotifyDTO;
import com.danding.cds.v2.bean.vo.req.RefundWarehouseCancelReq;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 9:24
 * @Description: 取消单管理服务
 */
public interface CustomsInventoryCalloffCRpc {
    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    CustomsInventoryCalloffDTO findById(Long id);

    List<CustomsInventoryCalloffDTO> findById(List<Long> idList);

    /**
     * 根据订单ID批量查询取消单
     *
     * @param orderId
     * @return
     */
    List<CustomsInventoryCalloffDTO> findListByOrderId(Long orderId);

    CustomsInventoryCalloffDTO findByOrderId(Long orderId);

    List<CustomsInventoryCalloffDTO> findListByOrderIdList(List<Long> orderIdList);

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    ListVO<CustomsInventoryCalloffDTOV2> paging(CustomsInventoryCalloffSearch search);


    InventoryCallOffOverTimeCountVO inventoryCalloffOverTimeCount(CustomsInventoryCalloffSearch search);

    /**
     * 生成取消单
     *
     * @param orderId
     * @param invSn
     * @return
     */
    Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason);

    /**
     * 生成取消单
     *
     * @param orderId
     * @return
     */
    Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String picJson, String refundLogisticsNo);

    /**
     * 生成取消单
     *
     * @param orderId
     * @return
     */
    Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String ownerCode, String ownerName);

    /**
     * 新增取消单
     *
     * @param customsInventoryCalloffDTO
     * @return
     */
    int create(CustomsInventoryCalloffDTO customsInventoryCalloffDTO);

    /**
     * 更新取消单
     *
     * @param customsInventoryCalloffDTO
     * @return
     */
    int update(CustomsInventoryCalloffDTO customsInventoryCalloffDTO);

    /**
     * 统计取消数据
     *
     * @param search
     * @return
     */
    CustomsInventoryCalloffCountDTO selectCountByCondition(CustomsInventoryCalloffSearch search);

    //业务中台统计售后保税
    CalloffCountDTO selectCalloffCount(String areaCompanyId);

    /**
     * 按处理人统计取消单 各状态下的数据
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    Map<String, CalloffAfterSalesCount> selectCalloffCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    /**
     * 更新
     *
     * @param id
     * @param callofStatus
     * @param calloffType
     * @param calloffReason
     * @param rejectReason
     * @return
     */
    int updateCustomsInventoryCalloffStatus(Long id, String callofStatus, String calloffType, String calloffReason, String rejectReason);

    /**
     * 批量更新
     *
     * @param idList
     * @param callofStatus
     * @param calloffType
     * @param calloffReason
     * @param rejectReason
     * @return
     */

    int updateCustomsInventoryCalloffStatus(List<Long> idList, String callofStatus, String calloffType, String calloffReason, String rejectReason);

    /**
     * 更新撤单状态
     *
     * @param orderId
     * @param cancelStatus
     * @return
     */
    int updateCancelStatusInfo(Long orderId, String cancelStatus);

    /**
     * 更新撤单和海关回执状态
     *
     * @param orderId       订单id
     * @param cancelStatus  撤单状态
     * @param customsStatus 海关回执状态
     * @return
     */
    int updateCancelStatusInfo(Long orderId, String cancelStatus, String customsStatus, String customsStatusDetail);

    /**
     * 更新退货状态
     *
     * @param orderId
     * @param refundStatus
     * @param RefundCheckStatus
     * @return
     */
    int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus);

    /**
     * 更新退货状态和海关回执状态
     *
     * @param orderId             订单id
     * @param refundStatus        退货状态
     * @param RefundCheckStatus   退货海关审核状态
     * @param refundCustomsStatus 退货海关回执状态
     * @return
     */
    int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus, String refundCustomsStatus, String refundCustomsStatusDetail);

    /**
     * 更新海关状态
     *
     * @param orderId
     * @param customsStatus
     * @param exitRegionStatus
     * @param orderStatus      申报单状态
     * @return
     */
    int updateCustomsStatus(Long orderId, String customsStatus, Integer exitRegionStatus, Integer orderStatus);

    /**
     * 更新出区状态
     *
     * @param orderId
     * @param exitRegionStatus
     * @return
     */
    int updateExitRegionStatus(Long orderId, Integer exitRegionStatus);

    /**
     * 更新
     *
     * @param orderId
     * @param customsStatus
     * @param exitRegionStatus
     * @param calloffStatus
     * @param calloffType
     * @return
     */
    int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType);

    int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType, String rejectReason);

    /**
     * 批量更新取消单状态
     *
     * @param orderIdList      订单id
     * @param customsStatus    清关状态
     * @param exitRegionStatus 进出区状态 1 - 已出区， 0 - 未出区
     * @param calloffStatus    取消单状态
     * @param calloffType      取消单类型
     */
    void updateCustomsInventoryCalloffStatusBatchByOrderId(List<Long> orderIdList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType);

    void updateCustomsInventoryCalloffStatusBatchByLogisticsNo(List<String> mailNoList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType);


    void updateCalloffStatusByLogisticsNo(String mailNoList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType);

    /**
     * 统计-取消单个数
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    List<Map<String, Object>> sumInventoryCalloff(Date beginTime, Date endTime);

    /**
     * 直接取消
     *
     * @param id
     * @param calloffReason
     * @return
     */
    void direct(Long id, String calloffReason) throws ArgsErrorException;

    /**
     * 取消单取消失败
     *
     * @param orderId
     * @return
     */
    int failCallof(Long orderId);

    List<CustomsInventoryCalloffDTO> getCallOffByDeclareNo(String ownerCode, String entityWarehouseCode, List<String> cancelTypeList, Long timeBegin, Long timeEnd);


    void delJd();

    void delJdDetail();

    /**
     * 同步取消单预览
     *
     * @param submit
     * @return
     */
    List<CustomsCancelOrderDTO> jdCancelOrderSynPreview(CustomsInventoryCalloffSubmit submit) throws ArgsErrorException;


    /**
     * 京东取消单明细查询
     *
     * @param submit
     * @return
     */
    List<CustomsCancelOrderDetailsDTO> jdCancelOrderDetails(CustomsInventoryCalloffSubmit submit);


    CustomsInventoryCalloffCountDTO2 calloffOrderCount(CustomsInventoryCalloffSearch search);


    /**
     * Link 菜鸟退货
     *
     * @param linkCustomsRefundOrderMessageNotifyDTO
     */
    void cancelByLinkCustoms(LinkCustomsRefundOrderMessageNotifyDTO linkCustomsRefundOrderMessageNotifyDTO);

    void updateLinkCustomsContent(CustomsInventoryCalloffDTO inventoryCalloffDTO);

    void updateTypeAndStatusAndExit(Long id, String calloffType, String calloffStatus, int exitRegionStatus);

    /**
     * 更新驳回原因
     *
     * @param orderId 订单id
     */
    void updateRejectReasonAndStatusByOrderId(Long orderId, String rejectReason, String status);

    /**
     * 取消单数据迁移
     */
    void migrateByOrderId(Long oldOrderId, OrderDTO newOrderDTO);

    void refundWarehouseCancel(RefundWarehouseCancelReq reqVO);

    List<String> findMissOwnerWarehouseByBookId(List<Long> customsBookIdList, Date createTimeFrom, Date createTimeTo);

    void editRefundGoodsInfo(CalloffEditRefundGoodsInfoReqVo reqVO);

    void jdRefundCancel(JdCustomsOrderMsgDataDTO reqVO);
}
