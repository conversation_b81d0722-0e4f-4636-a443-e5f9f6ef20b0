package com.danding.cds.c.api.bean.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 京东电商清单放行回执-海关核验请求主信息
 */
@Data
public class JdInventoryCustomsCallbackInfo {
    /**
     * 全局唯一标识符，企业系统生成36位唯一序号（英文字母大写）
     */
    private String guid;

    /**
     * 申请时间，企业报送时间。格式:YYYYMMDDhhmmss
     */
    private String appTime;

    /**
     * 订单号，交易平台的订单编号，同一交易平台的订单编号应唯一，长度不超过60位
     */
    private String orderId;

    /**
     * 电商平台代码，电商平台的海关注册登记编号；若未在海关注册，则使用中国电子口岸发布的编号
     */
    private String ebpCode;

    /**
     * 电商平台名称，电商平台的海关注册登记名称；若未在海关注册，则使用中国电子口岸发布的名称
     */
    private String ebpName;

    /**
     * 电商企业的海关注册登记编号
     */
    private String ebcCode;

    /**
     * 电商企业的海关注册登记名称
     */
    private String ebcName;

    /**
     * 物流单号，物流企业运单包裹面单号，6个月内不重复，长度不超过60位
     */
    private String logisticsNo;

    /**
     * 物流公司代码，物流企业的海关注册登记编号
     */
    private String logisticsCode;

    /**
     * 物流公司名称，物流企业在海关注册登记的名称
     */
    private String logisticsName;

    /**
     * 企业内部标识单证的编号（可选）
     */
    private String contractNo;

    /**
     * 预录入编号，电子口岸标识单证的编号（可选）
     */
    private String preNo;

    /**
     * 担保企业编号，担保扣税的企业海关注册登记编号
     */
    private String assureCode;

    /**
     * 账册编号，保税模式必填，填写区内仓储企业在海关备案的账册编号（可选）
     */
    private String emsNo;

    /**
     * 清单编号，海关接受申报生成的清单编号
     */
    private String invtNo;

    /**
     * 进出口标志，I-进口，E-出口
     */
    private String ieFlag;

    /**
     * 申报日期，以海关计算机系统接受清单申报数据时记录的日期为准。格式:YYYYMMDD
     */
    private String declTime;

    /**
     * 申报海关代码，接受清单申报的海关关区代码
     */
    private String customsCode;

    /**
     * 口岸海关代码，商品实际进出我国关境口岸海关的关区代码
     */
    private String portCode;

    /**
     * 进出口日期，运载所申报商品的运输工具申报进境的日期（可选）。格式:YYYYMMDD
     */
    private Date ieDate;

    /**
     * 订购人证件类型，1-身份证，2-其它
     */
    private Integer buyerIdType;

    /**
     * 订购人证件号码，订购人的身份证件号码
     */
    private String buyerIdNumber;

    /**
     * 订购人的真实姓名
     */
    private String buyerName;

    /**
     * 订购人电话
     */
    private String buyerTelephone;

    /**
     * 收件地址
     */
    private String consigneeAddress;

    /**
     * 申报企业代码，申报单位的海关注册登记编号
     */
    private String agentCode;

    /**
     * 申报企业名称，申报单位在海关注册登记的名称
     */
    private String agentName;

    /**
     * 区内企业代码，保税模式必填，区内仓储企业的海关注册登记编号（可选）
     */
    private String areaCode;

    /**
     * 区内企业名称，保税模式必填，区内仓储企业在海关注册登记的名称（可选）
     */
    private String areaName;

    /**
     * 贸易方式，直购进口填写“9610”，保税进口填写“1210”
     */
    private String tradeMode;

    /**
     * 运输方式，填写海关标准的参数代码（参照《JGS-20 海关业务代码集》）
     */
    private String transMode;

    /**
     * 运输工具编号，直购进口必填，保税进口免填
     */
    private String trafNo;

    /**
     * 航次号，直购进口必填，保税进口免填
     */
    private String flightNo;

    /**
     * 提单号，直购进口必填，保税进口免填
     */
    private String billNo;

    /**
     * 监管场所代码，针对同一申报地海关下有多个跨境电子商务的监管场所
     */
    private String loctNo;

    /**
     * 许可证号，商务主管部门及其授权发证机关签发的进出口货物许可证件的编号（可选）
     */
    private String licenseNo;

    /**
     * 起运国（地区）代码，参照《JGS-20 海关业务代码集》的国家（地区）代码表
     */
    private String country;

    /**
     * 物流企业实际收取的运输费用
     */
    private BigDecimal freight;

    /**
     * 保险费，物流企业实际收取的商品保价费用
     */
    private BigDecimal insuranceFee;

    /**
     * 币制，限定为人民币，填写“142”
     */
    private String currency;

    /**
     * 包装种类代码，海关对进出口货物实际采用的外部包装方式的标识代码（可选）
     */
    private Integer packagingType;

    /**
     * 件数，包裹数量，限定为“1”
     */
    private Integer quantity;

    /**
     * 毛重，货物及其包装材料的重量之和，计量单位为千克
     */
    private BigDecimal grossWeight;

    /**
     * 净重，货物的毛重减去外包装材料后的重量，即货物本身的实际重量，计量单位为千克
     */
    private BigDecimal netWeight;

    /**
     * 备注（可选）
     */
    private String note;
}
