package com.danding.cds.c.api.rpc;

import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022-12-05 11:17
 */
public interface TenantIdFindCRpc {
    Long getTenantIdByInventory(String inventorySn);

    Long getTenantId(String ebpCode, String orderNo, String logisticNo, String invtNo);

    Long getTenantId(Map<String, String> queryMap);

    Long getTenantId(Map<String, String> queryMap, String indices, String type);

    Long getTenantIdByCustomsLogisticsSn(String sn);

    Long getTenantIdByCustomsInventorySn(String sn);
}
