package com.danding.cds.c.api.bean.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 京东电商清单放行回执-海关核验请求中的商品信息
 */
@Data
public class JdInventoryCustomsCallbackItem {
    /**
     * 商品序号，从1开始连续序号，与关联的电子订单表体序号一一对应
     */
    private Integer gnum;

    /**
     * 账册备案料号，保税进口必填
     */
    private String itemRecordNo;

    /**
     * 商品货号，电商企业自定义的商品货号（SKU）
     */
    private String sellerRecord;

    /**
     * 企业商品名称，交易平台销售商品的中文名称
     */
    private String itemName;

    /**
     * 商品编码，按商品分类编码规则确定的进出口商品的商品编号
     */
    private String hsCode;

    /**
     * 商品名称，商品名称应据实填报，与电子订单一致
     */
    private String goodsName;

    /**
     * 规格型号，满足海关归类、审价以及监管的要求
     */
    private String spe;

    /**
     * 条形码，商品条码一般由前缀部分、制造厂商代码、商品代码和校验码组成（可选）
     */
    private String barCode;

    /**
     * 原产国（地区）代码，参照《JGS-20 海关业务代码集》
     */
    private String originCountry;

    /**
     * 交易国别（地区）代码，按海关规定的《国别（地区）代码表》选择填报相应的贸易国（地区）中文名称及代码（可选）
     */
    private String tradeCountry;

    /**
     * 币种，限定为人民币，填写“142”
     */
    private String currency;

    /**
     * 按成交计量单位的实际数量
     */
    private BigDecimal qty;

    /**
     * 计量单位，海关标准的参数代码
     */
    private String unit;

    /**
     * 法定第一数量
     */
    private BigDecimal legalAmount1;

    /**
     * 法定第一计量单位，海关标准的参数代码
     */
    private String legalUnit1;

    /**
     * 法定第二数量（可选）
     */
    private BigDecimal legalAmount2;

    /**
     * 法定第二计量单位（可选）
     */
    private String legalUnit2;

    /**
     * 单价，成交单价
     */
    private BigDecimal price;

    /**
     * 总价，成交总价
     */
    private BigDecimal totalPrice;

    /**
     * 备注（可选）
     */
    private String note;
}
