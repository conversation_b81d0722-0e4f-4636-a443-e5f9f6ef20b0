package com.danding.cds.c.api.service;

import com.danding.cds.v2.bean.dto.ReconciliationImportResult;
import com.danding.cds.v2.bean.dto.ReconciliationOrderDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.dto.ReconciliationTrackLogDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationOrderImportVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationSearch;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;
import java.util.Map;

public interface ReconciliationOrderService {

    ReconciliationOrderDTO findBySn(String sn);

    ListVO<ReconciliationOrderDTO> paging(ReconciliationSearch search);

    Map<String, Integer> statusCount();

    void discard(Long id);

    void markDeal(List<Long> id);

    void excelImport(ReconciliationOrderImportVO importVO);

    ReconciliationOrderDTO create(Long areaCompanyId, Long reconciliationTime, Integer totalCount, String operator);

    List<ReconciliationTrackLogDTO> listTrackLog(Long id);

    void refresh(List<Long> idList);

    void balanceAccountPostProcess(ReconciliationImportResult result);

    void adjustCount(String sn, Integer changeUnmatchedCount, Integer changeDifferenceCount, Integer changeNoDifferentCount, boolean isDiscard);

    void submitImport(ReconciliationOrderImportInfoDTO submitData);

    Boolean invokeDataCenterBalanceAccount(List<String> sn);
}
