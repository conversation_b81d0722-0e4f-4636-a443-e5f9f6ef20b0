package com.danding.cds.c.api.service;

import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.TrackLogSearch;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 轨迹日志ES接口
 * @date 2022/5/31 13:46
 */
public interface TrackLogEsService {
    void sendMsg(String msg);

    void sendMsg(String msg, String declareOrderNo);

    /**
     * 用于异步保存轨迹日志
     *
     * @param trackLogEsDTO
     */
    void submit(TrackLogEsDTO trackLogEsDTO);

    void submit(List<TrackLogEsDTO> trackLogEsDTOList);

    ListVO<TrackLogEsDTO> getTrackLogs(TrackLogSearch trackLogSearch);

    ListVO<TrackLogEsDTO> getTrackLogs(TrackLogSearch trackLogSearch, OrderDTO orderDTO);

    ListVO<TrackLogEsDTO> selectTrackLogList(TrackLogSearch search);

//    void migrateTrackLogByDeclareNo(String originDeclareNo, OrderDTO orderDTO);
}
