package com.danding.cds.c.api.rpc;

import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsSingleInventoryEsDTO;
import com.danding.cds.customs.inventory.api.dto.ExportCusOfficerDataReqVO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.invenorder.api.dto.InventoryTaxStatisticsDTO;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.message.api.process.OrderCustomsInventoryMessage;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.v2.bean.vo.AutoRetryTaxConfig;
import com.danding.cds.v2.bean.vo.req.JdHourlyReportReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Date: 2020/05/08 15:29
 * @Auth: Dante-gxj
 * @Description: 海关清单服务
 */
public interface CustomsInventoryRpc {
    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    CustomsInventoryDTO findById(Long id);

    /**
     * 功能描述:  初始化数据到es
     * 创建时间:  2021/8/26 11:09 上午
     *
     * @param customsInventoryDTO :
     * @return com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO
     * <AUTHOR>
     */
    void initDataToEs(List<CustomsInventoryDTO> customsInventoryDTO);

    ListVO<CustomsSingleInventoryEsDTO> pagingES(SingleInvtOrderSearch search);

    /**
     * 通过清单编号获取清单相关信息
     *
     * @param inventoryNos 清单编号,多个逗号分割
     * @return
     */
    List<CustomsInventoryDTO> getInventoryByInventoryNos(String inventoryNos);

    /**
     * 税金统计ByEs
     *
     * @param search
     * @return
     */
    InventoryTaxStatisticsDTO taxStatisticsByEs(SingleInvtOrderSearch search);

    /**
     * 分页
     *
     * @param search
     * @return
     */
    ListVO<CustomsInventoryDTO> paging(SingleInvtOrderSearch search);

    List<CustomsInventoryDTO> getInventorysByStatus(Integer status, Integer customsStatus, String detail);

    void updateSnWithFix(String sn, String newSn, Date sectionDate);

    /**
     * 根据订单查询
     *
     * @param orderId
     * @return
     */
    CustomsInventoryDTO findByOrder(Long orderId, String sn);

    List<CustomsInventoryDTO> findBySnList(List<String> snList);

    List<StockOccupiedCountResDTO> getStockOccupiedCountListByEs(List<Long> customsBookItemIdList, List<Long> bookIdList);

    CustomsInventoryDTO findByEbcAndNo(Long ebcId, String declareOrderNo);

    CustomsInventoryDTO findBySnSection(String sn);

    List<CustomsInventoryDTO> findByCustomsLogisticsNo(List<String> logisticsNo);

    CustomsInventoryDTO findByLogisticsNo(String logisticsNo);

    CustomsInventoryDTO queryByLogisticsNo(String logisticsNo, Integer exitRegionStatus);


    /**
     * 修改最后一次申报时间
     *
     * @param orderSn
     */
    void updateByLastDeclareTime(String orderSn);

    /**
     * 重置最近申报时间
     *
     * @param sn 申报单系统编号
     */
    void updateLastDeclareTime(String sn);

    CustomsInventoryDTO findByLogisticsNo90Days(String logisticsNo);

    List<CustomsInventoryDTO> listBySnsSection(List<String> sns);

    List<CustomsInventoryDTO> listBySnsSection(List<String> sns, Date sectionDate);

    CustomsInventoryItemDTO getItemById90Days(Long id);

    /**
     * 通过清关单ID获取清关单表体数据
     *
     * @param inventoryId 清关单ID
     * @return
     */
    List<CustomsInventoryItemDTO> getItemByInventoryId90Days(Long inventoryId);

    /**
     * PS:超出90天不再支持核注核放
     * 根据快递公司及运单号查询
     *
     * @param expressId
     * @param logisticsNo
     * @return
     */
    CustomsInventoryDTO findByLogistics90Days(Long expressId, String logisticsNo);


    /**
     * 获取90天内的数据,
     *
     * @param expressId   快递ID
     * @param logisticsNo 运单编号
     * @return
     */
    CustomsInventoryDTO findByLogisticsNo90Days(Long expressId, String logisticsNo);

    /**
     * 根据运单号查询所有清单[为了避免不通快递公司出现重复的运单号]
     *
     * @param logisticsNo
     * @return
     */
    List<CustomsInventoryDTO> listByLogistics90Days(String logisticsNo);

    List<CustomsInventoryDTO> listByLogistics90Days(List<String> logisticsNo);

    List<CustomsInventoryDTO> listByInventoryIdsSection(List<Long> ids, Date sectionDate);

    /**
     * 根据清单ID查询清单商品项
     *
     * @param id
     * @param sectionDate
     * @return
     */
    List<CustomsInventoryItemDTO> listItemByIdSection(Long id, Date sectionDate);

    List<CustomsInventoryItemDTO> listItemByIdSectionNoFilter(Long id, Date sectionDate);

    List<CustomsInventoryItemDTO> listItemByBookItemIdsSection(List<Long> bookItemIds, Date sectionDate);

    List<CustomsInventoryItemDTO> listItemByProductIdSection(List<String> productIds, Date sectionDate);

    Map<Long, List<CustomsInventoryItemDTO>> listItemByInventoryS(List<CustomsInventoryDTO> inventoryDTOS);

    List<CustomsInventoryDTO> listByStatus(Integer status, Integer limit, Date sectionDate);

    List<CustomsInventoryDTO> listByStatusAndCreateTime(Integer status, Integer limit, Date createFrom, Date createTo);

    /**
     * 根据清单编号查询清单商品项
     *
     * @param inventoryNo
     * @return
     */
    CustomsInventoryDTO findByInventoryNo90Days(String inventoryNo);

    /**
     * 根据申报单号查询清单商品项
     *
     * @param declareNo
     * @return
     */
    CustomsInventoryDTO findByDeclareNo90Days(String declareNo);

    /**
     * 根据申报单号查询清单商品项
     *
     * @param declareNo 申报单号
     * @param create    创建时间
     * @return
     */
    CustomsInventoryDTO findByDeclareNo(String declareNo, Date create);

    CustomsInventoryDTO findByDeclareNoAndEbpId(String declareNo, Long ebpId, Date create);

    CustomsInventoryDTO findByEbcIdAndDeclareNo90Days(Long ebcId, String declareOrderNo);

    CustomsInventoryDTO findByDeclareOrderNoFull(String declareOrderNo);

    /**
     * 更新状态
     *
     * @param id
     * @param status
     * @param sectionDate
     */
    void updateStatusSection(Long id, Integer status, Date sectionDate);

    void updateIsOccupiedStockSection(Long id, Boolean isOccupiedStock, Date sectionDate);

    void updateLogisticsSection(Long id, String logisticsNo, Date sectionDate);

    void updatePayerSection(Long id, String payerId, String payerName, Date sectionDate);

    void updateByPush(Long id, Integer status, Date sectionDate);

    void updateByPush(Long id, Integer status, Date sectionDate, Integer declareFrequency);

    void updateBuyerInfo(Long id, String buyerIdNum, String buyerName, Date sectionDate);

    void updateStatus(Long id, Integer status, Date sectionDate);

    void updateStatusResetDeclareTime(String sn, CustomsActionStatus status);


    /**
     * 修改售后状态
     *
     * @param id
     * @param afterStatus
     * @param sectionDate
     */
    void updateAfterStatus(Long id, Integer afterStatus, Date sectionDate);

    void updateAfterStatus(Long id, Integer afterStatus, Integer status, Date sectionDate);

    void updateAfterStatus(CustomsInventoryDTO customsInventoryDTO, Integer afterStatus);

    /**
     * 更新放行信息
     *
     * @param id
     * @param customsPassTime
     * @param preNo
     * @param invtNo
     * @param sectionDate
     */
    void updateByCustomsPass(Long id, Long customsPassTime, String preNo, String invtNo, Date sectionDate);

    /**
     * 更新放行信息
     *
     * @param id              清单ID
     * @param customsStatus   海关回执状态码
     * @param customsDetail   海关回执描述
     * @param customsPassTime 海关放行时间
     * @param sectionDate     清单数据分表创建时间
     */
    void updateByCustomsPass(Long id, String customsStatus, String customsDetail, Long customsPassTime, Date sectionDate);

    /**
     * 更新回执信息
     *
     * @param id
     * @param customsStatus
     * @param customsDetail
     * @param lastCustomsTime
     */
    void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate);

    /**
     * 更新基础信息
     *
     * @param id
     * @param buyerIdNumber
     * @param buyerName
     * @param consigneeAddress
     * @param extraJson
     */
    void updateByBaseinfo(Long id, String buyerIdNumber, String buyerName, String consigneeAddress, String extraJson, Date sectionDate);

    /**
     * 更新基础信息
     *
     * @param id
     * @param itemNo
     * @param extraJson
     */
    void updateByItemBaseInfo(Long id, String itemNo, String extraJson, Date sectionDate);

    void updateByItemBaseInfo(Long id, Long customsBookItemId, String itemName, String extraJson, Date sectionDate);

    /**
     * 更新出区状态
     *
     * @param id
     * @param exitRegionStatus
     * @param sectionDate
     */
    void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date sectionDate);

    void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date finishTime, Date sectionDate);


    void updateExitRegionStatusBySn(List<CustomsInventoryDTO> inventoryDTOs, Integer exitRegionStatus, Date finishTime);


    void rePush(String sn, Boolean sendNow);

    void timeOut(CustomsInventoryDTO inventoryDTO);

    void updateByItemBaseInfo(CustomsInventoryItemDTO inventoryItemDTO, Date sectionDate);

    /**
     * 临时分页 查询所有记录
     *
     * @param search
     * @return
     */
    ListVO<CustomsInventoryDTO> pagingTemp(SingleInvtOrderSearch search);

    /**
     * 组装保存到es的dto
     *
     * @param customsInventoryDTO
     * @return
     */
    CustomsInventoryDTO buildCustomsSingleInventoryEsDO(CustomsInventoryDTO customsInventoryDTO);

    /**
     * 重新生成索引
     */
    void reloadIndex();

    Object getByInventoryId(Long id);

    Object delIndexByMonth(Integer month);

    /**
     * es：查找指定表数量量
     *
     * @param tableName
     * @return
     */
    int findCount(String tableName);

    /**
     * 查询指定表的分页
     *
     * @param currentPage
     * @param pageSize
     * @param tableName
     * @return
     */
    ListVO<CustomsInventoryDTO> pagingTemp(int currentPage, Integer pageSize, String tableName);

    void getJdHourlyReportJob(JdHourlyReportReqVo reportReqVo);

    void jieztechCallback(OrderCustomsInventoryMessage message) throws Exception;

    /**
     * 根据运单号修改申报清单核注状态
     *
     * @param logisticsNo
     * @return
     */
    void updByEndorsementsStatus(List<String> logisticsNo, InventoryReviewStatus reviwStatus);

    /**
     * 根据清单数据SN修改申报清单核注状态
     *
     * @param inventorySnList
     * @return
     */
    void updateEndorsementsStatusBySn(List<String> inventorySnList, Integer endorsementStatus);

    /**
     * 更新清单表税金
     *
     * @param id         清单表id
     * @param createTime 清单表创建时间
     * @param amount     税金总额
     */
    void updateTax(Long id, Date createTime, BigDecimal amount);

    List<CustomsInventoryDTO> getNotReturnTaxOrder(AutoRetryTaxConfig autoRetryTaxConfig, List<Long> assureCompanyIdList);

    void updateInventoryItemList(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> updateList);

//    ListVO<StockOccupiedDetailDTO> getCustomsBookItemOccupiedDetail(StockOccupiedDetailSearch search);

//    ListVO<StockOccupiedDetailDTO> getStockOccupiedDetail(StockOccupiedDetailSearch search);

    /**
     * 清空最后申报时间
     *
     * @param customsInventoryDTO
     */
    void cleanLastDeclareTime(CustomsInventoryDTO customsInventoryDTO);

    /**
     * 获取 清单人审导出清单数据
     *
     * @return
     */
    List<CustomsInventoryDTO> getCusPersonInvOrderDTOListByUpdateTime(Long createTimeFrom, Long createTimeTo, Long updateTimeFrom, Long updateTimeTo);

    List<CustomsSingleInventoryEsDTO> getCusPersonInvOrderEsDTOList(ExportCusOfficerDataReqVO reqVO);

    /**
     * 检查备案是否允许删除
     *
     * @param productId
     * @return true 不能删除， false 可以删除
     */
    Boolean judgeExist30DaysInByProductId(String productId);

//    void logicDeleteBySn(String sn);

    void updateIsOccupiedStockBySnList(List<String> inventorySnList, Boolean isOccupiedStock);


    void updateTaxBillStatusSection(Integer taxBillStatus, Long id, Date sectionTime);

    List<CustomsInventoryDTO> checkTaxBillStatusByTimeRange(Date beginTime, Date endTime);

    void updateCheckOutTimeAndStatusSection(Long id,Integer checkOutStatus,Date finishDate, Date sectionTime);

    void updatTotalRefundTaxSection(BigDecimal totalRefundTax, Long id, Date sectionTime);

    void updateCheckoutStatusBySn(String sn, Integer code);

    void updateCustomsStatus(String sn, Integer status) throws ArgsErrorException;

    void receiveCainiaoInventoryExitArea(String globalSystemNo);

    void getJdHourlyReportEmailJob(JdHourlyReportReqVo reportReqVo);
}