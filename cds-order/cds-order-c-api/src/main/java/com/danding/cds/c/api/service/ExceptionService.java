package com.danding.cds.c.api.service;

import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.vo.ExceptionReqVO;
import com.danding.cds.exception.api.vo.ExceptionResVO;
import com.danding.cds.exception.api.vo.ExceptionUpsetReqVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.List;

public interface ExceptionService {

    /**
     * 异常分类分页查询
     *
     * @param reqVO
     * @return
     */
    ListVO<ExceptionResVO> paging(ExceptionReqVO reqVO);

    /**
     * 新增或编辑异常分类
     *
     * @param addReqVO
     */
    void upsetException(ExceptionUpsetReqVO addReqVO) throws ArgsErrorException;

    /**
     * 异常名称下拉
     *
     * @return
     */
    List<ExceptionDTO> getExceptionDOList();

    /**
     * 获取异常分类
     *
     * @param id
     * @return
     */
    ExceptionDTO findById(Long id);

    /**
     * 通过用途标签，获取异常数据
     *
     * @param useTag 用途
     * @return
     */
    List<ExceptionDTO> findByUseTag(Integer useTag);
}
