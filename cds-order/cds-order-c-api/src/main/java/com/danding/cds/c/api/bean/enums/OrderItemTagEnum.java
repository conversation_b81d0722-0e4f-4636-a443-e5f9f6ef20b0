package com.danding.cds.c.api.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/25 13:49
 */
@Getter
@AllArgsConstructor
public enum OrderItemTagEnum {
    NULL(2 >> 2, "无"),
    FB_GIFTS(2 >> 1, "非保赠品");

    private Integer code;

    private String desc;

    public static OrderItemTagEnum getEnum(Integer code) {
        if (Objects.isNull(code)) {
            return NULL;
        }
        for (OrderItemTagEnum orderItemTagEnum : OrderItemTagEnum.values()) {
            if (Objects.equals(orderItemTagEnum.getCode(), code)) {
                return orderItemTagEnum;
            }
        }
        return NULL;
    }

    public static List<Integer> getOrderItemTags(Integer useTag) {
        List<Integer> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (OrderItemTagEnum value : OrderItemTagEnum.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(code);
            }
        }
        return orderTagList;
    }

    public static List<String> getOrderItemTagsDesc(Integer useTag) {
        List<String> orderTagList = new ArrayList<>();
        if (useTag == null || useTag == 0) {
            return orderTagList;
        }
        for (OrderItemTagEnum value : OrderItemTagEnum.values()) {
            // 判断下orderTag是否包含有数据
            Integer code = value.getCode();
            // 排除下没有用途的占位
            if (Objects.equals(code, 0)) {
                continue;
            }
            if ((code & useTag) == code) {
                orderTagList.add(value.getDesc());
            }
        }
        return orderTagList;
    }

    public static boolean containsFbGifts(Integer itemTag) {
        List<Integer> orderItemTags = getOrderItemTags(itemTag);
        return orderItemTags.contains(FB_GIFTS.getCode());
    }
}
