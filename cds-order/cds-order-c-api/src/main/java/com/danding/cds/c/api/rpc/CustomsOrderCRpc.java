package com.danding.cds.c.api.rpc;

import java.util.Date;
import java.util.List;

import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderReceive;
import com.danding.cds.customs.order.api.dto.CustomsOrderSearch;
import com.danding.logistics.api.common.response.ListVO;

/**
 * 海关订单
 */
public interface CustomsOrderCRpc {
    CustomsOrderDTO findById(Long id);

    CustomsOrderDTO findByOrder(Long orderId, String sn);

    List<CustomsOrderDTO> findBySnList(List<String> snList);

    CustomsOrderDTO findBySnSection(String sn);

    CustomsOrderDTO findByDeclareNo(String declareNo);

    List<CustomsOrderDTO> listByStatus(Integer status, Integer limit, Date sectionDate);

    void updateBuyerInfo(Long id, String buyerIdNum, String buyerName, Date sectionDate);

    void updateSnWithFix(String sn, String newSn, Date sectionDate);

    void updateStatusSection(Long id, Integer status, Date sectionDate);

    void updateStatusResetDeclareTime(String sn, CustomsActionStatus status);

    void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate);

    void updateByBaseInfo(Long id, String buyerName, String consigneeAddress, String buyerIdNumber, String itemJson, String extraJson, Date sectionDate);

    void updateByCustomsPass(Long id, Date sectionDate);

    void updateByPush(Long id, Integer status, Date sectionDate);

    void updateByPush(Long id, Integer status, Date sectionDate, Integer declareFrequency);

    String receive(CustomsOrderReceive receive);

    ListVO<CustomsOrderDTO> paging(CustomsOrderSearch search);

    void rePush(String sn, Boolean sendNow);

    List<CustomsOrderDTO> findByEbpIdAndDate(String ebpId, String begin, String end, String offset);

    void pddRepair(CustomsOrderDTO l);

    void pddRepairConsignee(CustomsOrderDTO l);

    /**
     * 重置最近申报时间
     *
     * @param orderSn 申报单号
     */
    void updateByLastDeclareTime(String orderSn);

    /**
     * 重置最近申报时间
     *
     * @param sn 申报单系统编号
     */
    void updateLastDeclareTime(String sn);

//    void logicDeleteBySn(String sn);
}