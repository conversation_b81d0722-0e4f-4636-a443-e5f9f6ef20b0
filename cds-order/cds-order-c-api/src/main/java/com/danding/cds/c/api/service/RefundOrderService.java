package com.danding.cds.c.api.service;

import com.danding.cds.customs.inventory.api.dto.CancelAndRefundAfterSaleCount;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.refund.api.dto.*;
import com.danding.cds.message.api.process.OrderRefundWarehouseCallbackMessage;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 退货订单
 */
public interface RefundOrderService {


    Integer selectRefundOrderInfoCount(String areaCompanyId);

    void createRefundOrderInfo(List<RefundOrderInfoDto> listInfoDtos, List<OrderDTO> orderList, List<CustomsInventoryDTO> customsInventoryList) throws ArgsErrorException;

    boolean deleteRefundOrderInfo(RefundOrderInfoDto InfoDtos) throws ArgsErrorException;

    ListVO<RefundOrderInfoDto> paging(RefundOrderInfoSearch search);

    List<RefundOrderInfoDto> querylist(RefundOrderInfoSearch search);

    public RefundOrderInfoDto findBy(long id);

    List<RefundOrderInfoDto> findById(List<Long> idList);

    public RefundOrderInfoDto findBySn(String sn);

    public RefundOrderInfoDto findByBillSn(String billSn);

    /**
     * 通过运单查询 退货单
     *
     * @param mailNo 运单号
     * @return
     */
    RefundOrderInfoDto findByMailNo(String mailNo);

    List<RefundOrderInfoDto> findByMailNoList(List<String> mailNoList);

    public boolean cancelRefund(List<RefundOrderInfoDto> listInfoDtos) throws ArgsErrorException;

    Response<Boolean> applyRefundOrder(List<RefundOrderInfoDto> listInfoDtos);

    void declareRefundOrder(List<Long> idList);

    void doDeclareInventoryRefund(Long id);

    void submitRefundMailNo(List<MailNoInfoRecord> importList);

    RefundResponseExcelReport excelImport(List<MailNoInfoRecord> importList, boolean isSave) throws ArgsErrorException;

    public void resetDeclare(long id);

    String receive(CustomsReceive receive);

    /**
     * 判断退货单是否已经存在
     *
     * @param orderId
     * @return
     */
    boolean checkRefundOrderInfoIsExist(Long orderId);

    boolean checkRefundOrderInfoIsCreate(Long orderId, boolean filter);

    public List<RefundOrderInfoDto> findRefundOrderByMailNo(String mailNo);

    public void updateStatus(Long id, Integer status, String checkStatus);

    void updateByCustomsPass(Long id, Long customsPassTime, String preNo, String invtNo, Date sectionDate);

    void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate);

    void testDeclare(Long id);

    RefundOrderInfoDto buildDTO(Long declareId, String refundReason);

    List<Map<String, Object>> sumRefundOrder(Date beginDate, Date endTime);

    RefundOrderInfoDto findByDeclareNo(String declareNo);

    List<RefundOrderInfoDto> findByDeclareNo(List<String> declareNoList);

    void updateAuditStatus(RefundOrderInfoDto infoDto);

    void createLog(RefundOrderInfoDto infoDto, Integer userId, Integer operateDes);


    /**
     * 统计状态下的订单数
     * {核注待创建，核注待完成，清关待完成}
     *
     * @param search
     * @return
     */
    Map<Integer, Integer> listRefundStatusCount(RefundOrderInfoSearch search);

    /**
     * 通过运单号 更新退货单状态
     *
     * @param mailNoList  运单
     * @param status      退货状态
     * @param InveStatus  清关单状态
     * @param InveOrderSn 清关单编号
     */
    void updateStatusBatchByMailNo(List<String> mailNoList, Integer status, String InveStatus, String InveOrderSn);

    void updateStatusByMailNo(String mailNoList, Integer status, String InveStatus, String InveOrderSn);

    /**
     * 更新清关单状态
     *
     * @param refundInCustomsSn    退货入区清关单编号
     * @param inventoryOrderStatus 清关单状态
     */
    void updateCustomsStatusByInveSn(String refundInCustomsSn, String inventoryOrderStatus);

    Map<String, CancelAndRefundAfterSaleCount> selectRefundOrderInfoCountDetail(List<String> handlerIdList, Long beginTime, Long endTime);

    List<RefundOrderInfoDto> findByOrderId(List<Long> orderIdList);

    int selectRefundInWarehouseCount(String areaCompanyId);

    void updateRefundCustomCode(List<Long> idList, String refundCustomCode);

    List<RefundOrderInfoDto> findRefundListByIdList(List<Long> idList);

    void submitRefundOrderPart(List<RefundOrderPartInfoDTO> importList);

    /**
     * 字节销退单部分退导入
     * @param importList
     */
    void submitRefundOrderPartByByte(List<RefundOrderPartInfoDTO> importList);

    void manualEndorsement(List<Long> idList);

    List<RefundOrderInfoDto> buildDTOList(Map<CustomsInventoryDTO, OrderDTO> customsInventoryOrderDTOMap, Map<String, CustomsInventoryCalloffDTO> calloffDTOMap, String reason);

    void callbackRefundWarehouse(OrderRefundWarehouseCallbackMessage message);

    void createMessageToRefundWarehouse(CustomsInventoryCalloffDTO calloffDto, Integer messageType);

    void updateExceptionInfo(String declareOrderNo, String exceptionInfo);

    List<RefundOrderInfoDto> findException();
}