package com.danding.cds.c.api.rpc;

import java.util.Date;
import java.util.List;

import com.danding.cds.customs.payment.api.service.dto.PaymentSearch;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.v2.bean.dto.TongLianCallBackDTO;
import com.danding.logistics.api.common.response.ListVO;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 17:18
 * @Description:
 */
public interface CustomsPaymentRpc {
    CustomsPaymentDTO findByOrder(Long orderId, String sn);

    CustomsPaymentDTO findBySn(String sn);

    CustomsPaymentDTO findByOutOrderNo(String outOrderNo);

    CustomsPaymentDTO findByDeclareOrderNo(String declareOrderNo);

    Long updateStatus(Long id, Integer status);

    Long updateBySuccess(Long id, Integer status, String payTransactionId, String verDept, String declarePayNo);

    void updateBuyerInfo(Long id, String buyerIdNum, String buyerName);

    Long updateBaseInfo(Long id, String buyerIdType, String buyerIdNo, String buyerName);

    CustomsPaymentDTO findById(Long id);

    /**
     * 支付申报单重推
     */
    void rePush(String sn,Boolean sendNow);

    ListVO<CustomsPaymentDTO> paging(PaymentSearch search);

    /**
     *
     * @param ebpId
     * @param status
     * @param page
     * @param queryDays
     * @return
     */
    List<CustomsPaymentDTO> findByEbpIdAndStatusPaymentOrder(Long ebpId, List<Integer> status, Integer page, Integer queryDays);

    List<CustomsPaymentDTO> findRedeclareOrderByChannelId(String payChannel, Date startTime);
    void handleTongLianCallBack(TongLianCallBackDTO tongLianCallBackDTO);
}
