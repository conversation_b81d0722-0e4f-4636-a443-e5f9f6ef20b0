package com.danding.cds.c.api.service;

import java.util.List;

import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.order.api.dto.TrackLogSearch;
import com.danding.logistics.api.common.response.ListVO;

/**
 * <AUTHOR>
 * @Create 2021/6/8  16:07
 * @Describe
 **/
public interface TrackLogService {
    /**
     * 用于异步保存轨迹日志
     * @param trackLog
     */
    void submit(TrackLogDTO trackLog);

    List<TrackLogDTO> getTrackLogs(String declareNo);

    List<TrackLogDTO> getByKeyWord(String declaredOrderNoKeyWord,String operateType, String beginDate, String endDate);

    ListVO<TrackLogDTO> selectTrackLogList(TrackLogSearch search);
}
