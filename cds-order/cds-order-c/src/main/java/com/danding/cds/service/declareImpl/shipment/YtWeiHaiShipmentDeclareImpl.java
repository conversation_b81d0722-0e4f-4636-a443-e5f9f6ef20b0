package com.danding.cds.service.declareImpl.shipment;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.yuantong.YtDeclareReqVo;
import com.danding.cds.bean.dto.yuantong.YtOrderItemsBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtReceiveInfoBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtSendInfoBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtTradeOrderInfoBeanReqVo;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.common.utils.PddOrderUtils;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.enums.CustomsLogisticsStatus;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.base.CustomsInventoryItemBaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;

import lombok.extern.slf4j.Slf4j;

/**
 * 圆通 威海地区的实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service("YT_WEIHAI_SHIPMENT_DECLARE")
public class YtWeiHaiShipmentDeclareImpl extends ShipmentDeclareAbstract {
    @Autowired
    private CustomsInventoryBaseService customsInventoryBaseService;
    @Autowired
    private CustomsInventoryItemBaseService customsInventoryItemBaseService;
    @Autowired
    private CustomsLogisticsService customsLogisticsService;
    @Autowired
    private OrderService orderService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("YtWeiHaiShipmentDeclareImpl mock接口 威海 圆通运单申报 info={}", JSON.toJSON(info));
        try {
            this.logisticsDeclare(info, "http://************:8090/test/api/uploadwaybill");
        } catch (Exception e) {
            log.error("YtWeiHaiShipmentDeclareImpl mock接口 威海 圆通运单申报 {}", info.getLogisticsNo(), e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("YtWeiHaiShipmentDeclareImpl 威海 圆通运单申报 info={}", JSON.toJSON(info));
        try {
            this.logisticsDeclare(info);
        } catch (Exception e) {
            log.error("YtWeiHaiShipmentDeclareImpl 威海 圆通运单申报 {}", info.getLogisticsNo(), e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }

    private void logisticsDeclare(WrapShipmentInfo info) throws Exception {
        this.logisticsDeclare(info, null);
    }

    private void logisticsDeclare(WrapShipmentInfo info, String requestUrl) throws Exception {
        CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findByLogisticsNo(info.getLogisticsNo());
        OrderDTO orderDTO = orderService.findBySnSection(logisticsDTO.getOrderSn());
        YtDeclareReqVo ytDeclareReqVo = new YtDeclareReqVo();
        ytDeclareReqVo.setOwnerCode("");
        ytDeclareReqVo.setStockCode("DT_WHYZWMS0209");
        ytDeclareReqVo.setPlatformCode("YTO");
        ytDeclareReqVo.setCpCode("YTO");

        YtReceiveInfoBeanReqVo receiveInfoBeanReqVo = new YtReceiveInfoBeanReqVo();
        receiveInfoBeanReqVo.setProvince(info.getConsigneeProvince());
        receiveInfoBeanReqVo.setCity(info.getConsigneeCity());
        receiveInfoBeanReqVo.setArea(info.getConsigneeDistrict());
        String consigneeStreet = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(info.getConsigneeStreet())
                && !PddOrderUtils.isPddOrder(orderDTO.getEbpId(), orderDTO.getDeclareOrderNo())) {
            consigneeStreet = info.getConsigneeStreet();
        }
        receiveInfoBeanReqVo.setAddressDetail(consigneeStreet + info.getConsigneeAddress());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        receiveInfoBeanReqVo.setReceiverCardNo(orderExtra.getSubmit().getBuyerIdNumber());
        receiveInfoBeanReqVo.setReceiverCardType("1");
        receiveInfoBeanReqVo.setReceivePhone(info.getConsigneeTel());
        receiveInfoBeanReqVo.setReceiveName(info.getConsignee());
        ytDeclareReqVo.setReceiveInfo(receiveInfoBeanReqVo);

        YtSendInfoBeanReqVo sendInfo = new YtSendInfoBeanReqVo();
        sendInfo.setProvince(info.getConsigneeProvince());
        sendInfo.setCity(info.getConsigneeCity());
        sendInfo.setArea(info.getConsigneeDistrict());
        sendInfo.setAddressDetail(info.getConsigneeAddress());
        sendInfo.setSendName("崔昭君");
        sendInfo.setSendPhone("0631-8641688");
        ytDeclareReqVo.setSendInfo(sendInfo);

        String logisticsNo = info.getLogisticsNo();
        YtTradeOrderInfoBeanReqVo orderInfoBeanReqVo = new YtTradeOrderInfoBeanReqVo();
        orderInfoBeanReqVo.setWaybillNo(logisticsNo);
        orderInfoBeanReqVo.setApplyType("1");
        orderInfoBeanReqVo.setEcRegCid("");
        orderInfoBeanReqVo.setEcRegName("");
        orderInfoBeanReqVo.setEcGjCid("");
        orderInfoBeanReqVo.setEcGjName("");
        orderInfoBeanReqVo.setPortCode("4262");
        orderInfoBeanReqVo.setCustomsCode("HZCUSTOMSNEW");
        orderInfoBeanReqVo.setGrossWeight(info.getWeight());
        orderInfoBeanReqVo.setNetWeight(info.getWeight());
        orderInfoBeanReqVo.setOrderNo(info.getDeclareOrderNo());
        orderInfoBeanReqVo.setCurrenyCode("RMB");
        orderInfoBeanReqVo.setFreight("0.00");
        List<YtOrderItemsBeanReqVo> itemList = new ArrayList<>();
        CustomsInventoryDTO inventoryDTO = customsInventoryBaseService.findByLogisticsNo(logisticsNo);
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryItemBaseService.selectByInventoryIdIn90Days(inventoryDTO.getId());
        for (CustomsInventoryItemDTO item : customsInventoryItemDTOList) {
            YtOrderItemsBeanReqVo itemsBeanReqVo = new YtOrderItemsBeanReqVo();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(item.getExtraJson(), CustomsInventoryItemExtra.class);
            itemsBeanReqVo.setItemName(item.getItemName());
            itemsBeanReqVo.setItemCode(itemExtra.getProductId());
            itemsBeanReqVo.setItemPrice(itemExtra.getDeclarePrice().toString());
            itemsBeanReqVo.setItemQty(item.getCount().toString());
            itemsBeanReqVo.setItemWeight(itemExtra.getGrossWeight().toString());
            itemList.add(itemsBeanReqVo);
        }
        orderInfoBeanReqVo.setOrderItems(itemList);

        ytDeclareReqVo.setTradeOrderInfo(orderInfoBeanReqVo);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(ytDeclareReqVo);
        log.info("圆通运单申报 json={}", jsonString);
        try {
            if (requestUrl == null) {
                // 生产url
                requestUrl = "http://tms.yang800.com:8090/v2/api/uploadwaybill";
            }
            HttpRequest httpRequest = HttpRequestUtil.post(requestUrl, jsonString);
            if (httpRequest.ok()) {
                String body = httpRequest.body();
                log.info("圆通运单申报成功 body={}", body);
                JSONObject jsonObject = JSON.parseObject(body);
                LogisticsReceive receive = new LogisticsReceive();
                receive.setLogisticsNo(info.getLogisticsNo());
                receive.setCustomsTime(new DateTime().getMillis());
                String customsDetail;
                if (Objects.equals(jsonObject.get("success").toString(), "true")) {
                    receive.setCustomsStatus("2");
                    customsDetail = "新增申报成功";
                } else {
                    receive.setCustomsStatus(CustomsLogisticsStatus.CUSTOMS_RECEIVE.getValue());
                    customsDetail = jsonObject.get("message").toString();
                }
                receive.setCustomsDetail(customsDetail);
                customsLogisticsService.receiveLogistics(receive);
            } else {
                String body = httpRequest.body();
                log.info("圆通运单申报失败 body={}", body);
            }
        } catch (Exception e) {
            log.error("圆通运单申报异常 error={}", e.getMessage());
        }
    }
}
