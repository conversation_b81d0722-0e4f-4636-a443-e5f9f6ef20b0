package com.danding.cds.bean.dto;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: cds-center
 * @description: OrderSubmitItem dto
 * @author: 潘本乐（Belep）
 * @create: 2021-12-20 10:08
 **/
@Data
public class OrderSubmitItemDto implements Serializable {

    private static final long serialVersionUID = 222329585672876674L;
    /**
     * 商品备案料号
     */
    private String recordNo;

    /**
     * 商品备案序号 非必填，未填时随机分配序号
     */
    private String recordGnum;

    /**
     * 商品货号
     */
    private String itemNo;

    /**
     * 商品品名
     */
    private String itemName;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 海关备案料号
     */
    private String customsRecordProductId;

    /**
     * 指定原产国
     */
    private String assignOriginCountry;

    /**
     * 表体标记
     */
    private Integer itemTag;


    /**
     * 条码
     */
    private String barCode;
}
