package com.danding.cds.service.log;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.c.api.service.TrackLogService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.UserServiceUtil;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.order.api.dto.TrackLogSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsOrderDO;
import com.danding.cds.order.base.bean.dao.TrackLogDO;
import com.danding.cds.order.base.mapper.TrackLogMapper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/6/8  16:08
 * @Describe
 **/
@Deprecated
@Slf4j
@Service
public class TrackLogServiceImpl implements TrackLogService {
    @Autowired
    private TrackLogMapper trackLogMapper;

    @Autowired
    private UserServiceUtil userServiceUtil;

    private static ExecutorService trackLogThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    @Override
    public void submit(TrackLogDTO trackLog) {
        if (trackLog==null){
            return;
        }
        TrackLogDO trackLogDO= new TrackLogDO();
        BeanUtils.copyProperties(trackLog, trackLogDO);
        Long tenantId = SimpleTenantHelper.getTenantId();
        trackLogThread.submit(() -> {
            SimpleTenantHelper.setTenantId(tenantId);
            this.saveTrackLog(trackLogDO);
        });
    }

    public void saveTrackLog(TrackLogDO trackLog) {
        try{
            UserUtils.setCreateAndUpdateBy(trackLog);
            trackLogMapper.insertSelective(trackLog);
            log.info("[TrackLogServiceImpl-saveTrackLog do={}]", JSON.toJSONString(trackLog));
        }catch (Exception e){
            log.error("[TrackLogServiceImpl-saveTrackLog declareNo={} error={}]", JSON.toJSONString(trackLog.getDeclareOrderNo()), e.getMessage(), e);
        }
    }

    @Override
    public List<TrackLogDTO> getTrackLogs(String declareNo) {
        Example example = new Example(TrackLogDO.class);
        example.orderBy("createTime").asc();
        example.createCriteria().andEqualTo("declareOrderNo", declareNo).andNotEqualTo("deleted", 1);
        List<TrackLogDO> trackLogDOS = trackLogMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(trackLogDOS)){
            return trackLogDOS.stream().map(t -> {
                TrackLogDTO dto = new TrackLogDTO();
                BeanUtils.copyProperties(t,dto);
                return dto;
            }).collect(Collectors.toList());
        }else{
            return null;
        }
    }

    @Override
    public List<TrackLogDTO> getByKeyWord(String declaredOrderNoKeyWord, String operateType, String beginDate, String endDate) {
        Example example = new Example(TrackLogDO.class);
        Example.Criteria criteria = example.createCriteria().andLike("declareOrderNo", "%" + declaredOrderNoKeyWord + "%")
                .andEqualTo("operateDes",operateType);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        try {
            if (beginDate != null) {
                criteria.andGreaterThanOrEqualTo("createTime", sdf.parse(beginDate));
            }
            if (endDate != null) {
                criteria.andLessThanOrEqualTo("createTime", sdf.parse(endDate));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        List<TrackLogDO> trackLogDOS = trackLogMapper.selectByExample(example);
        List<TrackLogDTO> list = trackLogDOS.stream().map(t -> {
            TrackLogDTO dto = new TrackLogDTO();
            BeanUtils.copyProperties(t, dto);
            return dto;
        }).collect(Collectors.toList());
        return list;
    }


    /**
     * 轨迹日志查询
     * @param search
     * @return
     */
    @Override
    @PageSelect
    public ListVO<TrackLogDTO> selectTrackLogList(TrackLogSearch search){
//        TimeRangeParam timeRangeParam = new TimeRangeParam();
        //设置查询维度-->季度查询
//        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
//        timeRangeParam.setEndDate(new Date());
        //设置查询条件
        Example example = new Example(TrackLogDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", search.getDeclareOrderNo());
//        if (Objects.nonNull(search.getOrderType())) {
//            criteria.andEqualTo("orderType", search.getOrderType());
//        }
        example.and(criteria);
        //执行查询
        List<TrackLogDO> trackLogDOS = trackLogMapper.selectByExample(example);
        //去获取操作人名称
        List<Long> userIdList = trackLogDOS.stream().map(c -> c.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userServiceUtil.listByIds(userIdList);
        List<TrackLogDTO> trackLogDTOS = trackLogDOS.stream().map(trackLogDO -> {
            TrackLogDTO trackLogDTO = BeanUtil.copyProperties(trackLogDO, TrackLogDTO.class);
//            if (Objects.nonNull(trackLogDO.getAuditStatus())) {
//                trackLogDTO.setAuditStatus(InventoryCancelEnum.getEnum(trackLogDO.getAuditStatus()).getDesc());
//            }
            //防止报文明文泄露 暂时屏蔽推送报文展示 等后续方案 2022-02-18
            if (TrackLogEnums.INVENTORY_DECLARE.getCode().equals(trackLogDTO.getOperateDes())) {
                trackLogDTO.setContent(null);
            }
            if (Objects.nonNull(trackLogDO.getOldStatus())) {
                trackLogDTO.setOldStatusStr(OrderStatus.getEnum(trackLogDO.getOldStatus()).getDesc());
            }
            if (Objects.nonNull(trackLogDO.getNewStatus())) {
                trackLogDTO.setNewStatusStr(OrderStatus.getEnum(trackLogDO.getNewStatus()).getDesc());
            }
            if (Objects.nonNull(trackLogDO.getNewStatus())) {
                trackLogDTO.setOperateDesStr(TrackLogEnums.getEnum(trackLogDO.getOperateDes()).getDesc());
            }
//            if (!Objects.equals(trackLogDO.getOrderType(), 0)) {
//                trackLogDTO.setOrderTypeStr(InventoryOrderType.fromInt(trackLogDO.getOrderType()).getDesc());
//            }
            //判断下操作人ID
            if (Objects.nonNull(trackLogDO.getCreateBy())) {
                if (userRpcResultMap.containsKey(trackLogDO.getCreateBy().longValue())) {
                    trackLogDTO.setCreateByName(userRpcResultMap.get(trackLogDO.getCreateBy().longValue()).getUserName());
                }
            }
            return trackLogDTO;
        }).collect(Collectors.toList());
        ListVO<TrackLogDTO> result = new ListVO<>();
        result.setDataList(trackLogDTOS);
        // 分页
        PageInfo<CustomsOrderDO> pageInfo = new PageInfo(trackLogDTOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }
}
