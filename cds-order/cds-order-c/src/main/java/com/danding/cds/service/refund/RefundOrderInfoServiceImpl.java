package com.danding.cds.service.refund;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.danding.cds.bean.config.JdlCallbackTaxConfigDTO;
import com.danding.cds.c.api.bean.dto.CalloffRefundGoodsInfoDTO;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.service.*;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.bean.dto.JdlPostDTO;
import com.danding.cds.common.bean.dto.RefundMessageDto;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.customs.refund.api.dto.*;
import com.danding.cds.customs.refund.api.enums.CustomsRefundOrderStatus;
import com.danding.cds.customs.refund.api.enums.RefundOrderAction;
import com.danding.cds.customs.refund.api.enums.RefundOrderEnum;
import com.danding.cds.customs.refund.api.enums.RefundPartFlagEnum;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handler.CustomsRefundOrderHandler;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.OperateReasonEnums;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderRefundWarehouseCallbackMessage;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryItemDO;
import com.danding.cds.order.base.bean.dao.RefundOrderInfo;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsInventoryItemMapper;
import com.danding.cds.order.base.mapper.RefundOrderInfoMapper;
import com.danding.cds.order.base.service.TrackLogEsProducer;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.IWMSRefundWarehouseCallbackService;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.es.OrderEsDao;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.dto.JdRefundCustomsCallbackDTO;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.component.uc.model.CurrentUserInfo;
import com.danding.component.uc.model.RealUserInfo;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageInfo;
import com.google.gson.Gson;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class RefundOrderInfoServiceImpl implements RefundOrderService {
    @Resource
    private RefundOrderInfoMapper refundOrderInfoMapper;
    @Resource
    private CustomsInventoryService customsInventoryService;
    @Resource
    private OrderService orderService;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private SequenceService sequenceService;
    @Autowired
    @Lazy
    private CustomsRefundOrderHandler customsRefundOrderHandler;
    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;
    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @DubboReference
    private MessageService messageService;
    @Resource
    private TrackLogService trackLogService;
    @Resource
    private TrackLogEsService trackLogEsService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @Autowired
    private TrackLogEsProducer trackLogEsProducer;

    @Autowired
    private OrderEsDao orderEsDao;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;

    @Resource(name = "refundCountThreadExecutor")
    private ThreadPoolTaskExecutor refundCountThreadExecutor;
    @Resource
    private CustomsInventoryBaseService customsInventoryBaseService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @Autowired
    private RefundOrderInfoServiceImpl refundOrderInfoService;

    /**
     * 启用 撤单&退货申报校验清单编号重复
     */
    @Value("${enable.cancel.refund.check.invNo.declare:false}")
    private boolean enableCancelRefundCheckInvNoDeclare;

    @Value("${little_giant_route_code_list:[]}")
    private String[] littleGiantRouteCodeList;

    @Autowired
    private IWMSRefundWarehouseCallbackService iwmsRefundWarehouseCallbackService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @Value("${jdl.callback.tax.config:}")
    private String jdlCallbackTaxConfig;

//    @Autowired
//    private LogComponent logComponent;

    //    @Autowired
//    private LogService logService;

    /**
     * 统计初始化+待总署审核总数
     *
     * @return
     */
    @Override
    public Integer selectRefundOrderInfoCount(String areaCompanyId) {
        try {
            List<String> list = Arrays.asList(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue(),
                    RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT.getValue());
            Example example = new Example(RefundOrderInfo.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("refundCheckStatus", list).andEqualTo("deleted", false);
            if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
                criteria.andEqualTo("areaCompanyId", areaCompanyId);
            }
            List<RefundOrderInfo> orderInfoList = refundOrderInfoMapper.selectByExample(example);
            return orderInfoList.size();
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public RefundOrderInfoDto findBy(long id) {
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("id", id);
        RefundOrderInfoDto _info = new RefundOrderInfoDto();
        BeanUtils.copyProperties(refundOrderInfoMapper.selectOneByExample(example), _info);
        return _info;
    }

    @Override
    public List<RefundOrderInfoDto> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andIn("id", idList);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        return ConvertUtil.listConvert(refundOrderInfos, RefundOrderInfoDto.class);
    }

    @Override
    public RefundOrderInfoDto findBySn(String sn) {
        if (sn == null) {
            return null;
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("refundNo", sn);
        RefundOrderInfoDto _info = new RefundOrderInfoDto();
        BeanUtils.copyProperties(refundOrderInfoMapper.selectOneByExample(example), _info);
        return _info;
    }

    @Override
    public RefundOrderInfoDto findByBillSn(String billSn) {
        if (StringUtils.isEmpty(billSn)) {
            return null;
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("refListBillSn", billSn)
                .andEqualTo("deleted", false);
        RefundOrderInfo refundOrderInfo = refundOrderInfoMapper.selectOneByExample(example);
        if (Objects.isNull(refundOrderInfo)) return null;
        RefundOrderInfoDto refundOrderInfoDto = new RefundOrderInfoDto();
        BeanUtils.copyProperties(refundOrderInfo, refundOrderInfoDto);
        return refundOrderInfoDto;
    }

    @Override
    public RefundOrderInfoDto findByMailNo(String mailNo) {
        if (StringUtils.isEmpty(mailNo)) {
            log.warn("RefundService findByInveCustomsSn - mailNo is null");
            return null;
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("mailNo", mailNo);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(refundOrderInfos)) {
            log.info("未找到退货单");
            return null;
        }
        RefundOrderInfoDto refundOrderInfo = new RefundOrderInfoDto();
        BeanUtils.copyProperties(refundOrderInfos.get(0), refundOrderInfo);
        return refundOrderInfo;
    }

    @Override
    public List<RefundOrderInfoDto> findByMailNoList(List<String> mailNoList) {
        if (CollectionUtils.isEmpty(mailNoList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("mailNo", mailNoList);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        return ConvertUtil.listConvert(refundOrderInfos, RefundOrderInfoDto.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean cancelRefund(List<RefundOrderInfoDto> listInfoDtos) throws ArgsErrorException {
        int count = 0;
        for (RefundOrderInfoDto info : listInfoDtos) {
            Example example = new Example(RefundOrderInfo.class);
            example.createCriteria().andEqualTo("id", info.getId());
            /**
             * 设置为取消状态
             */
            info.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue());
            info.setRefundStatusTime(new Date());
            RefundOrderInfo to_info = new RefundOrderInfo();
            BeanUtils.copyProperties(info, to_info);
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(to_info);
            }
            to_info.setUpdateTime(new Date());
            count = refundOrderInfoMapper.updateByExampleSelective(to_info, example);
            //更新取消单信息
//            customsInventoryCalloffService.updateRefundStatusInfo(info.getRefDeclareId(), RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue(), null);
//            logComponent.logOpertion(LogCode.LOG_REFUND, info.getRefDeclareNo(), info.getRefundNo(),
//                    RefundOrderEnum.STATUS_ENUM.getEnum(info.getRefundStatus()).getDesc(),
//                    RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getDesc(),
//                    "操作取消退货单"
//            );
//            logService.logNormal(LogCode.LOG_REFUND,LogOperation.CANCEL,
//                    to_info.getRefundNo(),to_info.getRefDeclareNo(),"操作取消退货单");
        }
        return count > 0;
    }

    @Override
    public boolean checkRefundOrderInfoIsExist(Long orderId) {
        Example example = new Example(RefundOrderInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refDeclareId", orderId);
        criteria.andEqualTo("deleted", false);
        int count = refundOrderInfoMapper.selectCountByExample(example);
        return count > 0;
    }

    @Override
    public boolean checkRefundOrderInfoIsCreate(Long orderId, boolean filter) {
        Example example = new Example(RefundOrderInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refDeclareId", orderId);
        criteria.andEqualTo("deleted", false);
        List<RefundOrderInfo> list = Optional.ofNullable(refundOrderInfoMapper.selectByExample(example))
                .orElse(new ArrayList<>());
        if (list.isEmpty()) {
            return false;
        }
        long count = list.size();
        if (filter) {
            Set<String> setCheckStatus = new HashSet<>(Arrays.asList(
                    RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue(),
                    RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue()
            ));
            count = list.stream().filter(s ->
                    !RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue().equals(s.getRefundStatus()) &&
                            !setCheckStatus.contains(s.getRefundCheckStatus())
            ).count();
        }
        return count > 0;
    }

    //@LogNormalDescription(logType="创建",logCode = LogCode.LOG_REFUND,referenceSn = "DN2006232011264794",sn="TH20070810080073",description = "普通申报退货单信息#{#id}")
    //@LogChangedDescription(targetId = "#{#id}",logCode = LogCode.LOG_REFUND,logType="申报",logChangeFields={"审核状态:refundCheckStatus","回执状态:refundStatus"},description = "申报退货单信息")
    @Override
    public void testDeclare(Long id) {
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("id", id);
        RefundOrderInfo refundOrderInfo = refundOrderInfoMapper.selectOneByExample(example);
        refundOrderInfo.setRefundCheckTime(null);
        refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue());
        refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INIT.getValue());
        refundOrderInfo.setRefundStatusTime(null);
        refundOrderInfo.setRefundCustomTime(null);
        refundOrderInfo.setRefundCustomStatus(null);
        refundOrderInfo.setRefundCustomCheckDetail(null);
        refundOrderInfo.setDeclareSuccessTime(null);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(refundOrderInfo);
        }
        refundOrderInfo.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByPrimaryKey(refundOrderInfo);
        RefundOrderInfoDto infoDto = this.findBy(id);
        customsRefundOrderHandler.handle(infoDto);
    }

    @Test
    public void t() {
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        RefundOrderInfoDto refundOrderInfoDto = new RefundOrderInfoDto();
        BeanUtils.copyProperties(refundOrderInfoDto, refundOrderInfo);
        System.out.println(JSON.toJSONString(refundOrderInfo));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createRefundOrderInfo(List<RefundOrderInfoDto> listInfoDtos, List<OrderDTO> orderList, List<CustomsInventoryDTO> customsInventoryList) throws ArgsErrorException {
        try {
            Map<String, CustomsInventoryDTO> snInvMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getSn, Function.identity(), (k1, k2) -> k1));
            Map<Long, OrderDTO> idOrderMap = orderList.stream().collect(Collectors.toMap(OrderDTO::getId, Function.identity(), (k1, k2) -> k1));
            for (RefundOrderInfoDto infoDto : listInfoDtos) {
                RefundOrderInfo info = buildRefundOrderInfo(infoDto);
                if (insertRefundOrderInfo(info)) {
                    handleRelatedBusinessLogic(info, idOrderMap, snInvMap);
                }
            }
        } catch (Exception ex) {
            log.error("createRefundOrderInfo error={}", ex.getMessage(), ex);
            throw ex;
        }
    }

    private RefundOrderInfo buildRefundOrderInfo(RefundOrderInfoDto infoDto) {
        RefundOrderInfo info = new RefundOrderInfo();
        BeanUtils.copyProperties(infoDto, info);
        info.setCreateTime(new Date());
        info.setUpdateTime(new Date());
        info.setDeleted(false);
        UserUtils.setCreateAndUpdateBy(info);
        return info;
    }


    private boolean insertRefundOrderInfo(RefundOrderInfo info) {
        try {
            return refundOrderInfoMapper.insert(info) > 0;
        } catch (Exception e) {
            log.error("insertRefundOrderInfo error={}", e.getMessage(), e);
            return false;
        }
    }

    private void handleRelatedBusinessLogic(RefundOrderInfo info, Map<Long, OrderDTO> idOrderMap, Map<String, CustomsInventoryDTO> snInventoryMap) {
        OrderDTO orderDTO = idOrderMap.get(info.getRefDeclareId());
        CustomsInventoryDTO customsInventoryDTO = snInventoryMap.get(info.getRefListBillSn());
        if (Objects.nonNull(orderDTO) && Objects.nonNull(customsInventoryDTO)) {
            customsInventoryCalloffService.upset(orderDTO, customsInventoryDTO, InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode(), InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), info.getReason());
            orderService.updateOrderInternalStatus(orderDTO.getId(), orderDTO.getSn(), OrderInternalEnum.REFUNDING.getCode());
            refundOrderInfoService.updateRefundQty(customsInventoryDTO);
        }
        try {
            buildTrackLogEsAndSend(info, orderDTO);
        } catch (Exception ex) {
            log.error("buildTrackLogEsAndSend error={}", ex.getMessage(), ex);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public void updateRefundQty(CustomsInventoryDTO customsInventoryDTO) {
        if (customsInventoryDTO == null) {
            log.error("updateRefundQty 清单不存在");
            throw new ArgsErrorException("清单不存在");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(customsInventoryDTO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(customsInventoryDTO.getCreateTime()));
//        // Step::根据时间区间检索更新
//        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("customsInventoryId", customsInventoryDTO.getId());
//        example.and(criteria);
        List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());

        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffService.findByOrderId(customsInventoryDTO.getOrderId());
        Map<String, Integer> refundQtyMap = new HashMap<>();
        boolean isRefundWarehouse = false;
        boolean isJdRefund = false;
        if (Objects.nonNull(calloffDTO) && StrUtil.isNotBlank(calloffDTO.getRefundGoodsInfoJson())) {
            if (InventoryCalloffOrderTagEnums.contains(calloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE)) {
                isRefundWarehouse = true;
                JSON.parseArray(calloffDTO.getRefundGoodsInfoJson(), CalloffRefundGoodsInfoDTO.class).forEach(s -> {
                    refundQtyMap.merge(s.getBarCode(), s.getRefundCount(), Integer::sum);
                });
            } else if (InventoryCalloffOrderTagEnums.contains(calloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.JD_REFUND)) {
                isJdRefund = true;
                List<CalloffRefundGoodsInfoDTO> refundGoodsInfoDTOS = JSON.parseArray(calloffDTO.getRefundGoodsInfoJson(), CalloffRefundGoodsInfoDTO.class);
                refundGoodsInfoDTOS.forEach(s -> {
                    refundQtyMap.merge(s.getCustomsProductId(), s.getRefundCount(), Integer::sum);
                });
            }
        }
        if (isRefundWarehouse) {
            Map<String, List<CustomsInventoryItemDTO>> barCodeGroupMap = customsInventoryItemDTOS.stream()
                    .filter(i -> !OrderItemTagEnum.containsFbGifts(i.getItemTag()))
                    .collect(Collectors.groupingBy(i -> {
                        CustomsInventoryItemExtra itemExtra = JSON.parseObject(i.getExtraJson(), CustomsInventoryItemExtra.class);
                        return itemExtra.getBarCode();
                    }, Collectors.toList()));

            //修改一下退货申报数量取值
            for (Map.Entry<String, List<CustomsInventoryItemDTO>> entry : barCodeGroupMap.entrySet()) {
                String barCode = entry.getKey();
                List<CustomsInventoryItemDTO> itemList = entry.getValue();
                //根据sort进行排序
                itemList.sort(Comparator.comparing(CustomsInventoryItemDTO::getSort));
                Integer refundCount = refundQtyMap.getOrDefault(barCode, 0);
                for (CustomsInventoryItemDTO s : itemList) {
                    Example itemExample = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
                    Example.Criteria itemExampleCriteria = itemExample.createCriteria();
                    itemExampleCriteria.andEqualTo("id", s.getId());
                    itemExample.and(itemExampleCriteria);
                    CustomsInventoryItemDO template = new CustomsInventoryItemDO();
                    template.setRefundDeclareQty(Math.min(s.getCount(), refundCount));
                    refundCount = Math.max(0, refundCount - s.getCount());
                    template.setUpdateTime(new Date());
                    template.setUpdateBy(UserUtils.getUserId());
                    customsInventoryItemMapper.updateByExampleSelective(template, itemExample);
                }
            }
        } else if (isJdRefund) {
            Map<String, List<CustomsInventoryItemDTO>> productIdGroupMap = customsInventoryItemDTOS.stream()
                    .filter(i -> !OrderItemTagEnum.containsFbGifts(i.getItemTag()))
                    .collect(Collectors.groupingBy(i -> {
                        CustomsInventoryItemExtra itemExtra = JSON.parseObject(i.getExtraJson(), CustomsInventoryItemExtra.class);
                        return itemExtra.getProductId();
                    }, Collectors.toList()));

            //修改一下退货申报数量取值
            for (Map.Entry<String, List<CustomsInventoryItemDTO>> entry : productIdGroupMap.entrySet()) {
                String barCode = entry.getKey();
                List<CustomsInventoryItemDTO> itemList = entry.getValue();
                //根据业务时间逆序
                itemList = itemList.stream()
                        .peek(item -> item.setTotalPrice(item.getUnitPrice().multiply(BigDecimal.valueOf(item.getCount()))))
                        .sorted(Comparator.comparing(CustomsInventoryItemDTO::getTotalPrice)
                                .thenComparing(CustomsInventoryItemDTO::getCount).reversed()).collect(Collectors.toList());

                Integer refundCount = refundQtyMap.getOrDefault(barCode, 0);
                for (CustomsInventoryItemDTO s : itemList) {
                    Example itemExample = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
                    Example.Criteria itemExampleCriteria = itemExample.createCriteria();
                    itemExampleCriteria.andEqualTo("id", s.getId());
                    itemExample.and(itemExampleCriteria);
                    CustomsInventoryItemDO template = new CustomsInventoryItemDO();
                    template.setRefundDeclareQty(Math.min(s.getCount(), refundCount));
                    refundCount = Math.max(0, refundCount - s.getCount());
                    template.setUpdateTime(new Date());
                    template.setUpdateBy(UserUtils.getUserId());
                    customsInventoryItemMapper.updateByExampleSelective(template, itemExample);
                }
            }
        } else {
            for (CustomsInventoryItemDTO s : customsInventoryItemDTOS) {
                Example itemExample = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
                Example.Criteria itemExampleCriteria = itemExample.createCriteria();
                itemExampleCriteria.andEqualTo("id", s.getId());
                itemExample.and(itemExampleCriteria);
                CustomsInventoryItemDO template = new CustomsInventoryItemDO();
                template.setRefundDeclareQty(s.getCount());
                template.setUpdateTime(new Date());
                template.setUpdateBy(UserUtils.getUserId());
                customsInventoryItemMapper.updateByExampleSelective(template, itemExample);
            }
        }
    }

    private void buildTrackLogEsAndSend(RefundOrderInfo info, OrderDTO orderDTO) {
        this.buildTrackLogEsAndSend(info, orderDTO, null);
    }

    private void buildTrackLogEsAndSend(RefundOrderInfo info, String internalStatus, String detail, String operateDesc) {
        TrackLogEsDTO trackLogEsDTO = new TrackLogEsDTO();
        trackLogEsDTO.setOrderId(info.getRefDeclareId());
        OrderDTO orderDTO = orderService.findByIdFull(info.getRefDeclareId());
        if (Objects.isNull(orderDTO)) {
            return;
        }
        trackLogEsDTO.setOrderSn(orderDTO.getSn());
        trackLogEsDTO.setDeclareOrderNo(info.getRefDeclareNo());
        trackLogEsDTO.setSender(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setReceiver(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDTO.setInternalStatus(internalStatus);
        trackLogEsDTO.setRequestMessage(detail);
        trackLogEsDTO.setEventDesc(operateDesc);
        trackLogEsDTO.setEventTime(new Date());
        trackLogEsDTO.setOperator(UserUtils.getUserRealName());
        trackLogEsProducer.sendMsg(JSON.toJSONString(trackLogEsDTO), info.getRefDeclareNo());
    }

    private void buildTrackLogEsAndSend(RefundOrderInfo info, OrderDTO orderDTO, String operateDesc) {
        TrackLogEsDTO trackLogEsDTO = new TrackLogEsDTO();
        trackLogEsDTO.setOrderId(info.getRefDeclareId());
        if (Objects.isNull(orderDTO)) {
            orderDTO = orderService.findByIdFull(info.getRefDeclareId());
        }
        if (Objects.isNull(orderDTO)) {
            return;
        }
        trackLogEsDTO.setOrderSn(orderDTO.getSn());
        trackLogEsDTO.setDeclareOrderNo(info.getRefDeclareNo());
        trackLogEsDTO.setInternalStatus(OrderInternalEnum.REFUNDING.getCode());
        if (Objects.equals(operateDesc, TrackLogEnums.REFUND_DECLARE_SUCCESS.getDesc())) {
            trackLogEsDTO.setInternalStatus(OrderInternalEnum.REFUND_SUCCESS.getCode());
        } else if (Objects.equals(operateDesc, TrackLogEnums.REFUND_DECLARE_OVERRULE.getDesc())) {
            trackLogEsDTO.setInternalStatus(OrderInternalEnum.REFUND_FAIL.getCode());
        }
        trackLogEsDTO.setSender(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setReceiver(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setResult(TrackLogConstantMixAll.SUCCESS);
        if (Objects.nonNull(operateDesc)) {
            trackLogEsDTO.setEventDesc(operateDesc);
        } else {
            trackLogEsDTO.setEventDesc(TrackLogConstantMixAll.REFUND_CREATE);
        }
        trackLogEsDTO.setEventTime(new Date());
        if (Objects.nonNull(info.getRefundCustomCheckDetail())) {
            trackLogEsDTO.setCustomsReceipt(info.getRefundCustomCheckDetail());
        }
        trackLogEsDTO.setOperator(UserUtils.getUserRealName());
        trackLogEsProducer.sendMsg(JSON.toJSONString(trackLogEsDTO), info.getRefDeclareNo());
    }

    @Override
    public void updateStatus(Long id, Integer status, String checkStatus) {
        Integer userId = UserUtils.getUserId();
        RefundOrderInfo info = new RefundOrderInfo();
        info.setId(id);
        info.setRefundStatus(status);
        info.setRefundCheckStatus(checkStatus);
        info.setUpdateBy(userId);
        if (Objects.equals(status, RefundOrderEnum.STATUS_ENUM.STATUS_COMPLETE.getValue())) {
            info.setRefundStatusTime(new Date());
        }
        info.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByPrimaryKeySelective(info);
    }

    private Example buildExample(RefundOrderInfoSearch search) {
        Example example = new Example(RefundOrderInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (Objects.isNull(search)) {
            return example;
        }
        // 账册Id列表
        if (!CollectionUtils.isEmpty(search.getRoleAccountBookIdList())) {
            criteria.andIn("accountBookId", search.getRoleAccountBookIdList());
        }
        /**
         * 审核状态
         */
        if (!CollectionUtils.isEmpty(search.getRefundCheckStatusList())) {
            criteria.andIn("refundCheckStatus", search.getRefundCheckStatusList());
        }
        /**
         * 退货单状态
         */
        if (Objects.nonNull(search.getRefundStatus())) {
            criteria.andEqualTo("refundStatus", search.getRefundStatus());
        }
        /**
         *  物流企业代码
         */
        if (!StringUtils.isEmpty(search.getLogisticsCode())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(NumberUtils.createLong(search.getLogisticsCode()));
            if (companyDTO != null)
                criteria.andEqualTo("logisticsCode", companyDTO.getCode());
        }
        if (search.getBeginCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCreateTime());
            criteria.andGreaterThanOrEqualTo("createTime", tempDate);
        }
        if (search.getEndCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCreateTime());
            criteria.andLessThanOrEqualTo("createTime", tempDate);
        }
        if (search.getBeginCompleteTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCompleteTime());
            criteria.andGreaterThanOrEqualTo("refundStatusTime", tempDate);
        }
        if (search.getEndCompleteTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCompleteTime());
            criteria.andLessThanOrEqualTo("refundStatusTime", tempDate);
        }
        //电商企业查询
        if (!StringUtils.isEmpty(search.getEbcCode())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(NumberUtils.createLong(search.getEbcCode()));
            if (companyDTO != null)
                criteria.andEqualTo("ebcCode", companyDTO.getCode());
        }
        if (Objects.nonNull(search.getAgentCompanyCode())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(NumberUtils.createLong(search.getAgentCompanyCode()));
            if (companyDTO != null) {
                criteria.andEqualTo("agentCode", companyDTO.getCode());
            }
        }
        if (Objects.nonNull(search.getEbpCode())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(NumberUtils.createLong(search.getEbpCode()));
            if (companyDTO != null) {
                criteria.andEqualTo("ebpCode", companyDTO.getCode());
            }
        }
        //担保企业查询
        if (!StringUtils.isEmpty(search.getAssureCompanyCode())) {
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(NumberUtils.createLong(search.getAssureCompanyCode()));
            if (companyDTO != null)
                criteria.andEqualTo("assureNo", companyDTO.getCode());
        }
        if ((!StringUtils.isEmpty(search.getQueryType())) && (!StringUtils.isEmpty(search.getQueryInfo()))) {
            String[] nos = StringUtils.split(search.getQueryInfo(), ",");
            if (nos != null && nos.length > 0) {
                /**
                 * 退货入库
                 */
                if ("instock".equalsIgnoreCase(search.getQueryType())) {

                } else {
                    OrderSearch orderSearch = new OrderSearch();
                    orderSearch.setQueryType(search.getQueryType());
                    orderSearch.setQueryInfo(search.getQueryInfo());
                    orderSearch.setPageSize(nos.length);
                    orderSearch.setCurrentPage(1);
                    Page<OrderEsDO> page = orderEsDao.paging(orderSearch);
                    List<Long> declareIds = page.getContent().stream().map(s -> {
                        return NumberUtils.createLong(s.getId());
                    }).collect(Collectors.toList());
                    if (declareIds == null) declareIds = new ArrayList<Long>();
                    if (CollectionUtils.isEmpty(declareIds)) {
                        declareIds.add(0l);
                    }
                    criteria.andIn("refDeclareId", declareIds);
                }
            }
        }
        if (Objects.nonNull(search.getDeclareSuccessTimeFrom()) && Objects.nonNull(search.getDeclareSuccessTimeTo())) {
            criteria.andBetween("declareSuccessTime", new Date(search.getDeclareSuccessTimeFrom()), new Date(search.getDeclareSuccessTimeTo()));
        }
        if (Objects.nonNull(search.getAreaCompanyId())) {
            criteria.andEqualTo("areaCompanyId", search.getAreaCompanyId());
        }
        if (Objects.nonNull(search.getRefundPartFlag())) {
            criteria.andEqualTo("refundPartFlag", search.getRefundPartFlag());
        }
        if (Objects.nonNull(search.getRefundCrossCustomsFlag())) {
            criteria.andEqualTo("refundCrossCustomsFlag", search.getRefundCrossCustomsFlag());
        }
        if (Objects.nonNull(search.getRefundExceptionFlag())) {
            criteria.andEqualTo("refundExceptionFlag", search.getRefundExceptionFlag());
        }
        return example;
    }

    @PageSelect
    public ListVO<RefundOrderInfoDto> paging(RefundOrderInfoSearch search) {
        Example example = buildExample(search);
        example.orderBy("createTime").desc();
        List<RefundOrderInfo> list = refundOrderInfoMapper.selectByExample(example);
        ListVO<RefundOrderInfoDto> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), RefundOrderInfoDto.class));
        // 分页
        PageInfo<RefundOrderInfo> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void resetDeclare(long id) {
        RefundOrderInfoDto refundOrderInfoDto = this.findBy(id);
        RefundOrderInfo info = new RefundOrderInfo();
        BeanUtils.copyProperties(refundOrderInfoDto, info);
        info.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue());
        info.setRefundCheckTime(null);
        info.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue());
        info.setRefundStatusTime(null);
        info.setDeclareSuccessTime(null);
        info.setRefundExceptionInfo(null);
        info.setRefundExceptionFlag(0);
        info.setUpdateTime(new Date());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(info);
        }
        refundOrderInfoMapper.updateByPrimaryKey(info);
        BeanUtils.copyProperties(info, refundOrderInfoDto);
        List<RefundOrderInfoDto> list = new ArrayList<RefundOrderInfoDto>();
        list.add(refundOrderInfoDto);
        applyRefundOrder(list);
    }

    @Override
    public Response<Boolean> applyRefundOrder(List<RefundOrderInfoDto> listInfoDtos) {
        if (CollUtil.isEmpty(listInfoDtos)) {
            return new Response<Boolean>(-1, "数据为空");
        }
        Response<Boolean> oret;
        if (enableCancelRefundCheckInvNoDeclare) {
            List<String> declareOrderNoList = listInfoDtos.stream().map(RefundOrderInfoDto::getRefDeclareNo).collect(Collectors.toList());
            List<RefundOrderInfoDto> refundDTOByDeclareNo = this.findByDeclareNo(declareOrderNoList);
            Map<String, List<RefundOrderInfoDto>> refundDTOMapByInvNo = refundDTOByDeclareNo.stream()
                    .collect(Collectors.groupingBy(RefundOrderInfoDto::getInvtNo));
            List<String> duplInvNoList = new ArrayList<>();
            refundDTOMapByInvNo.forEach((invNo, refundDTOs) -> {
                if (CollUtil.isNotEmpty(refundDTOs) && refundDTOs.size() > 1) {
                    duplInvNoList.add(invNo);
                }
            });
            if (CollUtil.isNotEmpty(duplInvNoList)) {
                oret = new Response<Boolean>(false);
                oret.setCode(-1);
                oret.setErrorMessage("退货存在重复的清单编号[" + String.join(",", duplInvNoList) + "]，请处理后再推送");
                return oret;
            }
        }
        List<Long> orderIdList = listInfoDtos.stream().map(RefundOrderInfoDto::getRefDeclareId).collect(Collectors.toList());
        List<CustomsInventoryCalloffDTO> calloffDTOList = customsInventoryCalloffService.findListByOrderIdList(orderIdList);
        Map<Long, CustomsInventoryCalloffDTO> refundWarehouseOrderIdMap = calloffDTOList.stream().filter(i -> {
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(i.getOrderTag());
            return orderTags.contains(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode());
        }).collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderId, Function.identity(), (i, j) -> i));
        List<String> orderSnList = calloffDTOList.stream().map(CustomsInventoryCalloffDTO::getOrderSn).collect(Collectors.toList());
        List<OrderDTO> orderDTOList = orderService.findBySnSection(orderSnList);
        Map<Long, OrderDTO> orderDTOMap = orderDTOList.stream().collect(Collectors.toMap(OrderDTO::getId, i -> i));
        for (RefundOrderInfoDto infoDto : listInfoDtos) {
            if (!RefundOrderEnum.refundOrder.contains(infoDto.getRefundCheckStatus(), infoDto.getRefundStatus(), RefundOrderAction.REFUND_APPALY)) {
                oret = new Response<Boolean>(false);
                oret.setCode(-1);
                oret.setErrorMessage("退货单状态不允许申报");
                return oret;
            }
        }
        for (RefundOrderInfoDto infoDto : listInfoDtos) {
            infoDto.setUpdateTime(new Date());
            customsRefundOrderHandler.handle(infoDto);
            if (refundWarehouseOrderIdMap.containsKey(infoDto.getRefDeclareId())) {
                // 退货仓 退货申报回传状态
                CustomsInventoryCalloffDTO calloffDTO = refundWarehouseOrderIdMap.get(infoDto.getRefDeclareId());
                createMessageToRefundWarehouse(calloffDTO, RefundCallbackMessageStatus.DECLARE_START.getCode());
            }
        }
        oret = new Response<Boolean>();
        oret.setCode(0);
        oret.setResult(true);
        return oret;
    }

    private static final String DECLARE_INVENTORY_REFUND_LIST_KEY = "ccs:declare:inventory:refund";

    private static final String DECLARE_INVENTORY_REFUND_TOPIC = "ccs-declare-inventory-refund-topic";

    @Override
    public void declareRefundOrder(List<Long> idList) {
        List<RefundOrderInfoDto> refundOrderInfoDtoList = this.findById(idList);
        if (enableCancelRefundCheckInvNoDeclare) {
            List<String> declareOrderNoList = refundOrderInfoDtoList.stream().map(RefundOrderInfoDto::getRefDeclareNo).collect(Collectors.toList());
            List<RefundOrderInfoDto> refundDTOByDeclareNo = this.findByDeclareNo(declareOrderNoList);
            Map<String, List<RefundOrderInfoDto>> refundDTOMapByInvNo = refundDTOByDeclareNo.stream()
                    .collect(Collectors.groupingBy(RefundOrderInfoDto::getInvtNo));
            List<String> duplInvNoList = new ArrayList<>();
            refundDTOMapByInvNo.forEach((invNo, refundDTOs) -> {
                if (CollUtil.isNotEmpty(refundDTOs) && refundDTOs.size() > 1) {
                    duplInvNoList.add(invNo);
                }
            });
            if (CollUtil.isNotEmpty(duplInvNoList)) {
                throw new ArgsInvalidException("退货存在重复的清单编号[" + String.join(",", duplInvNoList) + "]，请处理后再推送");
            }
        }
        RefundOrderInfoDto errorStatusRefundOrder = refundOrderInfoDtoList.stream()
                .filter(infoDto -> !RefundOrderEnum.refundOrder.contains(infoDto.getRefundCheckStatus(), infoDto.getRefundStatus(), RefundOrderAction.REFUND_APPALY))
                .findAny().orElse(null);
        if (Objects.nonNull(errorStatusRefundOrder)) {
            throw new ArgsInvalidException("退货单:" + errorStatusRefundOrder.getRefundNo() + "状态不允许申报");
        }
        checkRefundHs(refundOrderInfoDtoList);
        for (RefundOrderInfoDto refundOrderInfoDto : refundOrderInfoDtoList) {
            String sn = (String) redisTemplate.opsForValue().get(DECLARE_INVENTORY_REFUND_LIST_KEY + refundOrderInfoDto.getId());
            if (Objects.nonNull(sn)) {
                log.info("退货:{}正在申报中,请两分钟后刷新页面重试!", refundOrderInfoDto.getRefundNo());
                throw new ArgsInvalidException("退货:" + refundOrderInfoDto.getRefundNo() + "正在申报中,请两分钟后刷新页面重试!");
            } else {
                redisTemplate.opsForValue().set(DECLARE_INVENTORY_REFUND_LIST_KEY + refundOrderInfoDto.getId(),
                        refundOrderInfoDto.getRefundNo() + "," + SimpleUserHelper.getRealUserName(), 120, TimeUnit.SECONDS);
                stringRedisTemplate.opsForValue().set("ccs:inventory:refund:declare:operator:" + refundOrderInfoDto.getRefDeclareId(), UserUtils.getUserRealName(), 120, TimeUnit.SECONDS);
                log.info("declareRefundOrder 发送消息 topic={} id={}", DECLARE_INVENTORY_REFUND_TOPIC, refundOrderInfoDto.getId());
                messageSender.sendMsg(String.valueOf(refundOrderInfoDto.getId()), DECLARE_INVENTORY_REFUND_TOPIC);
            }
        }
    }

    public void checkRefundHs(List<RefundOrderInfoDto> refundOrderInfoDtoList) {
        List<String> inventorySnList = refundOrderInfoDtoList.stream().map(RefundOrderInfoDto::getRefListBillSn).distinct().collect(Collectors.toList());
        Map<CustomsInventoryDTO, List<CustomsInventoryItemDTO>> fullInfoBySnList = customsInventoryBaseService.findFullInfoBySnList(inventorySnList);
        List<Long> bookItemIds = fullInfoBySnList.values() // 获取所有 List<CustomsInventoryItemDTO>
                .stream()              // 转换为 Stream
                .flatMap(List::stream) // 将多个 List 展平为单个 Stream
                .map(CustomsInventoryItemDTO::getBookItemId) // 提取 bookItemId
                .distinct()           // 去重
                .collect(Collectors.toList()); // 收集为 List
        List<CustomsBookItemDTO> bookItemDTOS = customsBookItemService.findById(bookItemIds);
        Map<Long, CustomsBookItemDTO> itemDTOMap = bookItemDTOS.stream().collect(Collectors.toMap(CustomsBookItemDTO::getId, Function.identity(), (v1, v2) -> v1));
        Set<Map.Entry<CustomsInventoryDTO, List<CustomsInventoryItemDTO>>> entries = fullInfoBySnList.entrySet();
        for (Map.Entry<CustomsInventoryDTO, List<CustomsInventoryItemDTO>> entry : entries) {
            CustomsInventoryDTO key = entry.getKey();
            List<CustomsInventoryItemDTO> value = entry.getValue();
            for (CustomsInventoryItemDTO customsInventoryItemDTO : value) {
                Long bookItemId = customsInventoryItemDTO.getBookItemId();
                CustomsBookItemDTO bookItemDTO = itemDTOMap.get(bookItemId);
                if (Objects.isNull(bookItemDTO)) {
                    log.info("bookItemDTO is null,bookItemId={}", bookItemId);
                } else {
                    if (Objects.nonNull(customsInventoryItemDTO.getRefundDeclareQty()) && customsInventoryItemDTO.getRefundDeclareQty() <= 0) {
                        log.info("逆向申报数量<=0 declareOrderNo={} productId={} seqNo={}", key.getDeclareOrderNo(), bookItemDTO.getProductId(), bookItemDTO.getGoodsSeqNo());
                        continue;
                    }
                    CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                    String oldHsCode = customsInventoryItemExtra.getHsCode();
                    String hsCode = bookItemDTO.getHsCode();
                    if (!Objects.equals(oldHsCode, hsCode)) {
                        throw new ArgsInvalidException("申报单:" + key.getDeclareOrderNo() + "金二序号:" + bookItemDTO.getGoodsSeqNo() + "，hs编码和账册库存的hs编码不一致");
                    }
                }
            }
        }
    }

    @Override
    public void doDeclareInventoryRefund(Long id) {
        if (Objects.isNull(id)) {
            log.info("doDeclareInventoryRefund id为空 返回");
            return;
        }
        RefundOrderInfoDto refundOrderInfoDto = this.findBy(id);
        if (Objects.isNull(refundOrderInfoDto)) {
            log.info("doDeclareInventoryRefund refundOrderInfoDto为空 返回");
            return;
        }
        String s = (String) redisTemplate.opsForValue().get(DECLARE_INVENTORY_REFUND_LIST_KEY + refundOrderInfoDto.getId());
        if (StrUtil.isNotBlank(s)) {
            String[] split = s.split(",");
            CurrentUserInfo currentUserInfo = new CurrentUserInfo();
            RealUserInfo realUserInfo = new RealUserInfo();
            realUserInfo.setUserName(split[1]);
            currentUserInfo.setRealUser(realUserInfo);
            SimpleUserHelper.setCurrentUserInfo(currentUserInfo);
        }
        customsRefundOrderHandler.handle(refundOrderInfoDto);
        //退货发起申报 退货仓消息回传
        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffService.findByOrderId(refundOrderInfoDto.getRefDeclareId());
        if (Objects.nonNull(calloffDTO)) {
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDTO.getOrderTag());
            if (orderTags.contains(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode())) {
                // 退货仓 退货申报回传状态
                this.createMessageToRefundWarehouse(calloffDTO, RefundCallbackMessageStatus.DECLARE_START.getCode());
            }
        }
    }

    private RefundOrderInfoDto buildItemDTO(RefundOrderInfo model) {
        if (model == null) {
            return null;
        } else {
            RefundOrderInfoDto result = new RefundOrderInfoDto();
            BeanUtils.copyProperties(model, result);
            return result;
        }
    }

    @Override
    public List<RefundOrderInfoDto> findRefundOrderByMailNo(String mailNo) {
        List<RefundOrderInfoDto> list = new ArrayList<RefundOrderInfoDto>();
        Example example = new Example(RefundOrderInfo.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("mailNo", mailNo);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<RefundOrderInfo> listItemDO = refundOrderInfoMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(listItemDO)) {
            for (RefundOrderInfo info : listItemDO) {
                list.add(this.buildItemDTO(info));
            }
        }
        return list;
    }

    private CustomsInventoryDTO findMailNo(MailNoInfoRecord record) {
        List<CustomsInventoryDTO> listCustomsInventoryDTO = customsInventoryService.listByLogistics90Days(record.getMailNo());
        if (CollectionUtils.isEmpty(listCustomsInventoryDTO)) {
            return null;
        }
        return listCustomsInventoryDTO.get(0);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitRefundMailNo(List<MailNoInfoRecord> importList) {
        for (MailNoInfoRecord record : importList) {
            List<RefundOrderInfoDto> list = this.findRefundOrderByMailNo(record.getMailNo());
            if (CollectionUtils.isEmpty(list)) {
                List<CustomsInventoryDTO> listCustomsInventory = this.customsInventoryService.listByLogistics90Days(record.getMailNo());
                RefundOrderInfoDto refundOrderInfoDto = buildDTO(listCustomsInventory.get(0).getOrderId(), "无理由退货");

                RefundOrderInfo itemDO = new RefundOrderInfo();
                BeanUtils.copyProperties(refundOrderInfoDto, itemDO);
                itemDO.setRefundMailNo(record.getRefundMailNo());
                itemDO.setRefundExpressName(record.getRefundExpressName());
                UserUtils.setCreateAndUpdateBy(itemDO);
                refundOrderInfoMapper.insert(itemDO);
//                logComponent.logOpertion(LogCode.LOG_REFUND, itemDO.getRefDeclareNo(), itemDO.getRefundNo(), RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(itemDO.getRefundCheckStatus()).getDesc(), RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(itemDO.getRefundCheckStatus()).getDesc(), "操作导入创建退货单");
            } else {
                RefundOrderInfoDto oldRefundOrderInfo = list.get(0);
                RefundOrderInfo itemDO = new RefundOrderInfo();
                BeanUtils.copyProperties(record, itemDO);
                ExpressDTO expressDTO = expressService.findOneByName(record.getRefundExpressName());
                if (expressDTO != null) {
                    itemDO.setRefundExpressName(record.getRefundExpressName());
                    itemDO.setRefundMailNo(record.getRefundMailNo());
                    itemDO.setRefundExpressCode(expressDTO.getCode());
                }
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(itemDO);
                }
                itemDO.setUpdateTime(new Date());
                refundOrderInfoMapper.updateByPrimaryKey(itemDO);
//                logComponent.logOpertion(LogCode.LOG_REFUND, oldRefundOrderInfo.getRefDeclareNo(), oldRefundOrderInfo.getRefundNo(),
//                        RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(oldRefundOrderInfo.getRefundCheckStatus()).getDesc(),
//                        RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(oldRefundOrderInfo.getRefundCheckStatus()).getDesc(),
//                        "操作导入更新退货单", "更新退货快递公司,运单号"
//                );
            }
        }
    }

    @Override
    @Transactional
    public RefundResponseExcelReport excelImport(List<MailNoInfoRecord> importList, boolean isSave) throws ArgsErrorException {
        RefundResponseExcelReport oret = new RefundResponseExcelReport();
        List<MailNoInfoRecord> successRecordList = new ArrayList<MailNoInfoRecord>(), failRecordList = new ArrayList<MailNoInfoRecord>(),
                successUpdateRecordList = new ArrayList<MailNoInfoRecord>();
        List<RefundOrderInfoDto> newList = new ArrayList<>();
        Set<String> mailNoSet = new HashSet<>();
        Map<String, RefundOrderInfoDto> tempMap = new HashMap<String, RefundOrderInfoDto>();
        int idx = 1;
        for (MailNoInfoRecord record : importList) {
            idx++;
            if (StringUtils.isEmpty(record.getMailNo()) ||
                    StringUtils.isEmpty(record.getRefundMailNo()) ||
                    StringUtils.isEmpty(record.getRefundExpressName())) {
                //throw new ArgsErrorException("第["+idx+"]行记录存在字段为空");
                record.setErrorMsg("记录存在字段为空");
                failRecordList.add(record);
                continue;
            }
            if (mailNoSet.contains(record.getMailNo())) {
                //throw new ArgsErrorException("第["+idx+"]行运单号重复");
                record.setErrorMsg("运单号重复");
                failRecordList.add(record);
                continue;
            }
            /**
             * 通过运单号查询相关的退货订单
             */
            List<RefundOrderInfoDto> list = this.findRefundOrderByMailNo(record.getMailNo());
            if (CollectionUtils.isEmpty(list)) {

                List<CustomsInventoryDTO> listCustomsInventory = this.customsInventoryService.listByLogistics90Days(record.getMailNo());
                if (listCustomsInventory == null || listCustomsInventory.size() == 0) {
                    //throw new ArgsErrorException("第["+idx+"]行运单号无法找到相关清单信息");
                    record.setErrorMsg("运单号无法找到相关清单信息");
                    failRecordList.add(record);
                    continue;
                }
                if (!CustomsActionStatus.DEC_SUCCESS.getValue().equals(listCustomsInventory.get(0).getStatus())) {
                    //throw new ArgsErrorException("第["+idx+"]行运单号对应的清单未放行");
                    record.setErrorMsg("运单号对应的清单未放行");
                    failRecordList.add(record);
                    continue;
                }
                if (customsInventoryCancelService.checkCustomsInventoryCancelIsCreate(listCustomsInventory.get(0).getOrderId())) {
                    //throw new ArgsErrorException("第["+idx+"]行运单号对应清单撤单存在，不能创建退货单");
                    record.setErrorMsg("运单号对应清单存在撤单审核通过，不能创建退货单");
                    failRecordList.add(record);
                    continue;
                }
                RefundOrderInfoDto refundOrderInfoDto = buildDTO(listCustomsInventory.get(0).getOrderId(), "无理由退货");
                refundOrderInfoDto.setRefundMailNo(record.getRefundMailNo());
                refundOrderInfoDto.setRefundExpressName(record.getRefundExpressName());
                mailNoSet.add(record.getMailNo());
                newList.add(refundOrderInfoDto);
                successRecordList.add(record);
            } else {
                RefundOrderInfoDto infoDto = list.get(0);
                if (!StringUtils.isEmpty(infoDto.getRefundMailNo()))//从清单这边推过来的，做更新
                {
                    record.setErrorMsg("运单号存在二次导入");
                    failRecordList.add(record);
                } else {
                    ExpressDTO expressDTO = expressService.findOneByName(record.getRefundExpressName());
                    if (expressDTO != null) {
                        infoDto.setRefundExpressName(record.getRefundExpressName());
                        infoDto.setRefundMailNo(record.getRefundMailNo());
                        infoDto.setRefundExpressCode(expressDTO.getCode());
                        mailNoSet.add(record.getMailNo());
                        tempMap.put(record.getMailNo(), infoDto);
                        successUpdateRecordList.add(record);
                    } else {
                        //throw new ArgsErrorException("第["+idx+"]行运单号存在二次导入");
                        record.setErrorMsg("退货快递名称无效");
                        failRecordList.add(record);
                    }
                }
            }
        }
        if (isSave) {
            try {
                if (!CollectionUtils.isEmpty(tempMap.values())) {
                    for (RefundOrderInfoDto itemDTO : tempMap.values()) {
                        RefundOrderInfoDto oldRefundOrderInfo = this.findBy(itemDTO.getId());
                        RefundOrderInfo itemDO = new RefundOrderInfo();
                        BeanUtils.copyProperties(itemDTO, itemDO);
                        if (!Objects.equals(UserUtils.getUserId(), 0)) {
                            UserUtils.setUpdateBy(itemDO);
                        }
                        itemDO.setUpdateTime(new Date());
                        refundOrderInfoMapper.updateByPrimaryKey(itemDO);
//                        logComponent.logOpertion(LogCode.LOG_REFUND, oldRefundOrderInfo.getRefDeclareNo(), oldRefundOrderInfo.getRefundNo(),
//                                RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(oldRefundOrderInfo.getRefundCheckStatus()).getDesc(),
//                                RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(oldRefundOrderInfo.getRefundCheckStatus()).getDesc(),
//                                "操作导入更新退货单", "更新退货快递公司,运单号"
//                        );
                        /*
                        String[] changedFields = {"退货快递公司:refundExpressName", "运单号:refundMailNo"};
                        logService.logChangedRefund(LogOperation.EDIT,
                                oldRefundOrderInfo, itemDTO, changedFields, "操作导入创建退货单");
                                */

                    }
                }
                if (!CollectionUtils.isEmpty(newList)) {
                    for (RefundOrderInfoDto itemDTO : newList) {
                        RefundOrderInfo itemDO = new RefundOrderInfo();
                        BeanUtils.copyProperties(itemDTO, itemDO);
                        UserUtils.setCreateAndUpdateBy(itemDO);
                        refundOrderInfoMapper.insert(itemDO);
                    }
                }
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                oret.setCode(-1);
                oret.setErrosMessage("导入出现异常");
                return oret;
            }
        }
        oret.setCode(0);
        oret.setSuccessNewRecordList(successRecordList);
        oret.setSuccessUpdateRecordList(successUpdateRecordList);
        oret.setFailRecordList(failRecordList);
        oret.setFailCount(failRecordList.size());
        oret.setSuccessUpdateCount(successUpdateRecordList.size());
        oret.setSuccessNewCount(successRecordList.size());
        oret.setTotalCount(successRecordList.size() + successUpdateRecordList.size() + failRecordList.size());
        return oret;
    }

    @Override
    public RefundOrderInfoDto buildDTO(Long declareId, String refundReason) {
        OrderDTO orderDTO = orderService.findByIdFull(declareId);
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        RefundOrderInfoDto infoDto = new RefundOrderInfoDto();
        List<CustomsInventoryCalloffDTO> customsInventoryCalloffDTOList = customsInventoryCalloffService.findListByOrderId(orderDTO.getId());
        String refundMailNo = "";
        for (CustomsInventoryCalloffDTO customsInventoryCalloffDTO : customsInventoryCalloffDTOList) {
            if (StringUtils.isNotEmpty(customsInventoryCalloffDTO.getRefundLogisticsNo())) {
                refundMailNo = customsInventoryCalloffDTO.getRefundLogisticsNo();
                break;
            }
        }
        infoDto.setRefundMailNo(refundMailNo);
        infoDto.setCreateBy(UserUtils.getUserId());
        infoDto.setUpdateBy(UserUtils.getUserId());
        infoDto.setRefundNo(sequenceService.generateRefundOrderSn());
        infoDto.setId(NumberUtils.createLong(StringUtils.replacePattern(infoDto.getRefundNo(), "[a-zA-Z]", "")));
        infoDto.setRefDeclareId(declareId);
        infoDto.setReason(refundReason);
        OrderDTO orderDto = orderService.findByIdFull(declareId);
        infoDto.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue());
        infoDto.setRefundCheckTime(null);
        infoDto.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INIT.getValue());
        infoDto.setRefundStatusTime(null);
        if (orderDto != null) {
            infoDto.setRefDeclareNo(orderDto.getDeclareOrderNo());
            infoDto.setChannelOrderNo(orderDto.getOutOrderNo());
        }
        if (customsInventoryDTO != null) {
            infoDto.setAccountBookId(customsInventoryDTO.getAccountBookId());
            infoDto.setCustomsCode(customsInventoryDTO.getCustoms());
            infoDto.setRefListBillId(customsInventoryDTO.getId());
            infoDto.setRefListBillSn(customsInventoryDTO.getSn());
            infoDto.setInvtNo(customsInventoryDTO.getInventoryNo());
            Long expressId = customsInventoryDTO.getExpressId();
            infoDto.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
            infoDto.setBuyerIdType(customsInventoryDTO.getBuyerIdType());
            infoDto.setBuyerName(customsInventoryDTO.getBuyerName());
            infoDto.setBuyerTelephone(customsInventoryDTO.getBuyerTelNumber());
            infoDto.setMailNo(customsInventoryDTO.getLogisticsNo());
            infoDto.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
            if (Objects.nonNull(customsInventoryDTO.getAgentCompanyId())) {
                CompanyDTO agentCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAgentCompanyId());
                if (Objects.nonNull(agentCompany)) {
                    infoDto.setAgentCode(agentCompany.getCode());
                    infoDto.setAgentName(agentCompany.getName());
                }
            }

            Long logisticsCompanyId = customsInventoryDTO.getLogisticsCompanyId();
            if (logisticsCompanyId != null) {
                CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(logisticsCompanyId);
                infoDto.setLogisticsCode(companyDTO.getCode());
                infoDto.setLogisticsName(companyDTO.getName());
            }
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getEbpId());
            if (companyDTO != null) {
                infoDto.setEbpCode(companyDTO.getCode());
                infoDto.setEbpName(companyDTO.getName());
            }
            companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getEbcId());
            if (companyDTO != null) {
                infoDto.setEbcCode(companyDTO.getCode());
                infoDto.setEbcName(companyDTO.getName());
            }
            infoDto.setPreNo(customsInventoryDTO.getPreNo());
            infoDto.setCustomsCode(customsInventoryDTO.getCustoms());
            infoDto.setInvtNo(customsInventoryDTO.getInventoryNo());
            companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getAssureCompanyId());
            if (companyDTO != null) {
                infoDto.setAssureNo(companyDTO.getCode());
                infoDto.setAssureName(companyDTO.getName());
            }
        }
        return infoDto;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteRefundOrderInfo(RefundOrderInfoDto InfoDtos) throws ArgsErrorException {
        boolean _boolean = false;
        RefundOrderInfo info = new RefundOrderInfo();
        info.setId(InfoDtos.getId());
        info.setDeleted(true);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(info);
        }
        info.setUpdateTime(new Date());
        _boolean = refundOrderInfoMapper.updateByPrimaryKeySelective(info) > 0;
        if (_boolean) {
            customsInventoryCalloffService.failCallof(InfoDtos.getRefDeclareId());
//            logComponent.logOpertion(LogCode.LOG_REFUND, InfoDtos.getRefDeclareNo(), InfoDtos.getRefundNo(),
//                    RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(InfoDtos.getRefundCheckStatus()).getDesc(),
//                    RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(InfoDtos.getRefundCheckStatus()).getDesc(),
//                    "操作删除退货单");
//            logService.logNormal(LogCode.LOG_REFUND,LogOperation.DELETE,
//                    InfoDtos.getRefundNo(),InfoDtos.getRefDeclareNo(),"操作删除退货单");
        }
        return _boolean;
    }

    private RefundOrderInfo findBydeclareOrderNo(String declareOrderNO) {
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("refDeclareNo", declareOrderNO)
                .andEqualTo("deleted", false);
        return refundOrderInfoMapper.selectOneByExample(example);
    }

    @Override
    public List<RefundOrderInfoDto> querylist(RefundOrderInfoSearch search) {
        Example example = buildExample(search);
        List<RefundOrderInfo> list = refundOrderInfoMapper.selectByExample(example);
        List<RefundOrderInfoDto> oretList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (RefundOrderInfo info : list) {
                RefundOrderInfoDto infoDto = new RefundOrderInfoDto();
                BeanUtils.copyProperties(info, infoDto);
                oretList.add(infoDto);
            }
        }
        return oretList;
    }

    @Override
    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.REFUND_RECEIPT
    )
    public String receive(CustomsReceive receive) {
        log.info("[op:OrderServiceImpl-receive] receive,receive={}", JSON.toJSONString(receive));
        Integer userId = UserUtils.getUserId();
        String customsStatus = receive.getCustomsStatus();
        String ebpCode = receive.getEbpCode();
        String declareOrderNo = receive.getDeclareOrderNo();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
//        CompanyDTO ebp = companyService.findByCode(ebpCode);
        CompanyDTO ebp = companyService.findByUnifiedCrossBroderCode(ebpCode);
        OrderDTO orderDTO = null;
        if (StringUtils.isEmpty(declareOrderNo)) {
            return "订单参数为空";
        }
        if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            // 浙电新接口回执无电商平台
            OrderSearch search = new OrderSearch();
            search.setCurrentPage(1);
            search.setPageSize(20);
            search.setQueryType("declareOrderNo");
            search.setQueryInfo(declareOrderNo);
            Page<OrderEsDO> paging = orderEsDao.paging(search);
            List<OrderEsDO> content = paging.getContent();
            if (CollectionUtils.isEmpty(content)) {
                return "订单不存在";
            }
            OrderEsDO next = content.iterator().next();
            orderDTO = orderService.findBySnSection(next.getSn());
        } else {
            if (ebp == null) {
                return "电商平台不存在";
            }
            orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        }
        if (orderDTO == null) {
            return "订单不存在";
        }
        RefundOrderInfo refundOrderInfo = findBydeclareOrderNo(orderDTO.getDeclareOrderNo());
        if (refundOrderInfo == null) return "相关退货单不存在";
        if (refundOrderInfo.getRefundCustomTime() != null && new DateTime(customsTime).isBefore(new DateTime(refundOrderInfo.getRefundCustomTime()))) {
            log.warn("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
            return "当前回执时间迟于最后一次回执时间，略过不做清关状态更新";
        }
        log.info("[op:RefundOrderInfoServiceImpl-receive] refundOrderInfo,refundOrderInfo={}", JSON.toJSONString(refundOrderInfo));
        log.info("[op:RefundOrderInfoServiceImpl RefundOrderFlowBook.callBackRefundOrder.operationAllowed] = {}", RefundOrderEnum.refundOrder.contains(refundOrderInfo.getRefundCheckStatus(), refundOrderInfo.getRefundStatus(), RefundOrderAction.REFUND_CALLBACK));
        Date date = new Date();
        date.setTime(customsTime);
        refundOrderInfo.setRefundCustomCheckDetail(customsDetail);
        refundOrderInfo.setRefundCustomStatus(customsStatus);
        refundOrderInfo.setRefundCustomTime(date);
        refundOrderInfo.setRefundExceptionInfo(null);
        refundOrderInfo.setRefundExceptionFlag(0);
        RefundMessageDto refundMessageDto = null;
        if (RefundOrderEnum.refundOrder.contains(refundOrderInfo.getRefundCheckStatus(), refundOrderInfo.getRefundStatus(), RefundOrderAction.REFUND_CALLBACK)) {
            // 海关返回电子口岸申报中
            if (Objects.equals("2", customsStatus)) {
                refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAITING.getValue());
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue());
                refundOrderInfo.setRefundCheckTime(date);
            }
            /**
             * 通过更新当前的时间,待人工审核
             */
            //if("300".equalsIgnoreCase(customsStatus))//待人工审核
            String WATI_STATUS[] = {"1", "3", "4", "120", "300", "399", "400", "500", "505", "501", "502", "503", "599", "600", "700", "899"};
            if (ArrayUtils.indexOf(WATI_STATUS, customsStatus) >= 0) {
                //待人工审核
                refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT.getValue());
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue());
                refundOrderInfo.setRefundCheckTime(date);
//                trackLogEnums = TrackLogEnums.REFUND_DECLARE_PUSH_SUCCESS.getCode();
            } else if ("800".equalsIgnoreCase(customsStatus)) {
                refundMessageDto = new RefundMessageDto();
                refundMessageDto.setCustomsStatus("1");
                //海关审结
                refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue());
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INVENTORY_WAIT_CREATE.getValue());
                refundOrderInfo.setRefundCheckTime(date);
                refundOrderInfo.setDeclareSuccessTime(date);
//                trackLogEnums = TrackLogEnums.REFUND_DECLARE_SUCCESS.getCode();
            } else if ("100".equalsIgnoreCase(customsStatus)) {
                refundMessageDto = new RefundMessageDto();
                refundMessageDto.setCustomsStatus("2");
                refundMessageDto.setMsg(customsDetail);
                //海关退单
                refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue());
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_FAIL.getValue());
                refundOrderInfo.setRefundCheckTime(date);
//                trackLogEnums = TrackLogEnums.REFUND_DECLARE_OVERRULE.getCode();
            }
//
//            else if("4".equalsIgnoreCase(customsStatus)) //发送海关失败
//            {
//                refundOrderInfo.setRefundCheckStatus(RefundOrderInfoEnum.CHECK_STATUS_ENUM.CHECK_STATUS_FAIL.getValue());
//                refundOrderInfo.setRefundStatus(RefundOrderInfoEnum.STATUS_ENUM.STATUS_WAIT.getValue());
//                refundOrderInfo.setRefundCheckTime(date);
//            }
            else {
                if (StringUtils.isEmpty(customsStatus) || CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
                    customsStatus = "0";
                }
                if (NumberUtils.createInteger(customsStatus).intValue() <= 0) {
                    //申报异常了
                    refundMessageDto = new RefundMessageDto();
                    refundMessageDto.setCustomsStatus("2");
                    refundMessageDto.setMsg(customsDetail);
                    //refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_FAIL.getValue());
                    refundOrderInfo.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue());
                    refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue());
                    refundOrderInfo.setRefundCheckTime(date);
//                    trackLogEnums = TrackLogEnums.REFUND_DECLARE_OVERRULE.getCode();
                } else {
                    log.info("退货单 中间状态不做处理");
                }
            }

            TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo(), null, refundOrderInfo.getRefundCheckStatus());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            refundOrderInfo.setUpdateBy(userId);
        }
        refundOrderInfo.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByPrimaryKey(refundOrderInfo);
        this.updateOrderInternalStatus(orderDTO, refundOrderInfo.getRefundCheckStatus());
        //更新取消单信息
        customsInventoryCalloffService.updateRefundStatusInfo(refundOrderInfo.getRefDeclareId(), null, refundOrderInfo.getRefundCheckStatus()
                , refundOrderInfo.getRefundCustomStatus(), refundOrderInfo.getRefundCustomCheckDetail());
        if ("800".equalsIgnoreCase(customsStatus)) {
            messageSender.sendMsg(orderDTO.getSn(), "ccs-company-tax-cancel-pass-topic");
            //更新售后状态
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(refundOrderInfo.getRefListBillSn());
            if (Objects.nonNull(customsInventoryDTO)) {
                customsInventoryService.updateAfterStatus(customsInventoryDTO, InventoryAfterStatus.REFUND_SUCCESS.getValue());
            }
        }
//        createLog(BeanUtil.copyProperties(refundOrderInfo, RefundOrderInfoDto.class), userId, trackLogEnums);

        Boolean callbackNew = false;
        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffService.findByOrderId(refundOrderInfo.getRefDeclareId());
        if (Objects.nonNull(calloffDTO)) {
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDTO.getOrderTag());
            if (orderTags.contains(InventoryCalloffOrderTagEnums.XIAOJUREN.getCode())) {
                //小巨人由ERP回传海关回执
                callbackNew = true;
                String activeData = JSON.toJSONString(new OrderActiveInfo(refundOrderInfo.getRefDeclareId()).buildResponseMsg(receive.getResponseMsg()));
                List<String> subscribeList = Arrays.asList("CHANNEL-1");
                messageService.createMessage(MessageType.ORDER_CUSTOMS_REFUND_TO_ERP, subscribeList, orderDTO.getDeclareOrderNo(), activeData, "");
            }
            if (orderTags.contains(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode())) {
                Integer messageType = null;
                if (customsDetail.contains("[Code:13105;Desc:退货申请：原清单已经生成缴款书]")) {
                    messageType = RefundCallbackMessageStatus.DECLARE_FAILED.getCode();
                } else if (customsDetail.contains("[Code:2600;Desc:放行]")) {
                    messageType = RefundCallbackMessageStatus.DECLARE_SUCCESS.getCode();
                }
                createMessageToRefundWarehouse(calloffDTO, messageType);
            }
            if (orderTags.contains(InventoryCalloffOrderTagEnums.JD_REFUND.getCode())) {
                createMessageToJdRefund(calloffDTO, customsStatus, customsDetail);
            }
        }
        // 退货单回传下上游 区分业务
        if (refundMessageDto != null) {
            String systemGlobalSn = orderDTO.getSystemGlobalSn();
            refundMessageDto.setGlobalSystemSn(systemGlobalSn);
            refundMessageDto.setTime(customsTime);
            if (callbackNew) {
                refundMessageDto.setBusinessType("2");
                messageService.createRefundMessage(orderDTO.getDeclareOrderNo(), JSON.toJSONString(refundMessageDto), MessageType.ORDER_REFUND_NEW);
            } else {
                messageService.createRefundMessage(orderDTO.getDeclareOrderNo(), JSON.toJSONString(refundMessageDto));
            }
        }
        return "回传成功";
    }

    private void createMessageToJdRefund(CustomsInventoryCalloffDTO calloffDto, String customsStatus, String customsDetail) {
        // 防止接受回执时异常回滚导致轨迹日志没记录，这边回告消息失败try catch一下
        try {
            if (Objects.isNull(calloffDto) || StrUtil.isBlank(customsStatus)) {
                log.info("createMessageToJdRefund 异常， 参数为空");
                return;
            }
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDto.getOrderTag());
            if (!orderTags.contains(InventoryCalloffOrderTagEnums.JD_REFUND.getCode())) {
                log.info("取消单不含退货仓标记，无需回传退货仓 declareOrderNo={}", calloffDto.getDeclareOrderNo());
                return;
            }
            Integer callbackStatus;
            if (customsStatus.equals("800")) {
                callbackStatus = 0;
            } else if (customsStatus.equals("100")) {
                callbackStatus = 1;
            } else {
                log.info("createMessageToJdRefund 海关状态={} 不处理， declareOrderNo={}", customsStatus, calloffDto.getDeclareOrderNo());
                return;
            }
            List<String> subscribeList = Collections.singletonList("CHANNEL-1");
            JdlCallbackTaxConfigDTO jdlCallbackTaxConfigDTO = JSON.parseObject(jdlCallbackTaxConfig, JdlCallbackTaxConfigDTO.class);
            JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(calloffDto.getAccountBookId());
            if (Objects.isNull(jdServProviderDTO)) {
                log.info("createMessageToJdRefund jdServProviderDTO为空 declareOrderNo={}", calloffDto.getDeclareOrderNo());
                return;
            }
            // 创建最外层的JSONArray
            JSONArray param1 = new JSONArray();
            JdRefundCustomsCallbackDTO jdRefundCustomsCallbackDTO = new JdRefundCustomsCallbackDTO();
            jdRefundCustomsCallbackDTO.setOrderId(calloffDto.getDeclareOrderNo());
            jdRefundCustomsCallbackDTO.setWaybillNo(calloffDto.getLogisticsNo());
            jdRefundCustomsCallbackDTO.setClearanceStatus(callbackStatus);
            jdRefundCustomsCallbackDTO.setClearanceTime(DateUtil.getCurrentDateTime());
            if (StrUtil.isEmpty(calloffDto.getRefundGoodsInfoJson())) {
                jdRefundCustomsCallbackDTO.setType("rejection");
            } else {
                jdRefundCustomsCallbackDTO.setType("return");
            }
            param1.add(jdRefundCustomsCallbackDTO);
            if (Objects.equals(1, callbackStatus)) {
                jdRefundCustomsCallbackDTO.setClearanceFailReason(customsDetail);
            }
            JdlPostDTO jdlPostDTO = new JdlPostDTO();
            jdlPostDTO.setUri(jdlCallbackTaxConfigDTO.getJdlUrl());
            jdlPostDTO.setPath("/rejectionreturnjsfservice/clearanceresultcallback");
            jdlPostDTO.setBody(JSON.toJSONString(param1));
            jdlPostDTO.setAppKey(jdServProviderDTO.getJdlAppKey());
            jdlPostDTO.setAppSecret(jdServProviderDTO.getJdlAppSecret());
            String activeData = JSON.toJSONString(jdlPostDTO);
            log.info("sendMessageToRefundWarehouse 发送回执 - {}", activeData);
            messageService.createMessage(MessageType.JD_REFUND_CUSTOMS_CALLBACK, subscribeList, calloffDto.getDeclareOrderNo(), activeData, "");
        } catch (Exception e) {
            log.error("sendMessageToRefundWarehouse 异常 declareOrderNo={}", calloffDto.getDeclareOrderNo(), e);
        }
    }

    private void updateOrderInternalStatus(OrderDTO orderDTO, String refundStatus) {
        if (Objects.isNull(refundStatus)) {
            return;
        }
        if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT.getValue().equals(refundStatus)) {
            refundStatus = OrderInternalEnum.REFUNDING.getCode();
        } else if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(refundStatus)) {
            refundStatus = OrderInternalEnum.REFUND_SUCCESS.getCode();
        } else if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equals(refundStatus)) {
            refundStatus = OrderInternalEnum.REFUND_FAIL.getCode();
        } else {
            return;
        }
        orderService.updateOrderInternalStatus(orderDTO, refundStatus);
    }

    @Override
    public void updateByCustomsPass(Long id, Long customsPassTime, String preNo, String invtNo, Date sectionDate) {
        RefundOrderInfo template = new RefundOrderInfo();
        template.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue());
        template.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue());
        template.setRefundCheckTime(new DateTime(customsPassTime).toDate());
        template.setPreNo(preNo);
        template.setInvtNo(invtNo);
        updateById(id, template);
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate) {
        RefundOrderInfo template = new RefundOrderInfo();
        template.setRefundCustomStatus(customsStatus);
        template.setRefundCustomCheckDetail(customsDetail);
        template.setRefundCustomTime(lastCustomsTime);
        updateById(id, template);
    }

    @Override
    public List<Map<String, Object>> sumRefundOrder(Date beginDate, Date endTime) {
        return refundOrderInfoMapper.sumRefundOrder(beginDate, endTime);
    }

    private void updateById(Long id, RefundOrderInfo template) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }

        Example example = new Example(RefundOrderInfo.class);
        // Step::根据时间区间检索更新
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByExampleSelective(template, example);
    }

    @Override
    public RefundOrderInfoDto findByDeclareNo(String declareNo) {
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setRefDeclareNo(declareNo);
        refundOrderInfo = refundOrderInfoMapper.selectOne(refundOrderInfo);
        return ConvertUtil.beanConvert(refundOrderInfo, RefundOrderInfoDto.class);
    }

    @Override
    public List<RefundOrderInfoDto> findByDeclareNo(List<String> declareNoList) {
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andIn("refDeclareNo", declareNoList)
                .andEqualTo("deleted", 0);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        return refundOrderInfos.stream().map(r -> {
            RefundOrderInfoDto infoDto = new RefundOrderInfoDto();
            BeanUtils.copyProperties(r, infoDto);
            return infoDto;
        }).collect(Collectors.toList());
    }

    @Autowired
    private MessageSender messageSender;

    @Override
    public void updateAuditStatus(RefundOrderInfoDto reqDto) {
        //先获取到当前用户
        Integer userId = UserUtils.getUserId();
        List<RefundOrderInfoDto> refundOrderInfoDtoList = this.findRefundListByIdList(reqDto.getIds());
        if (CollUtil.isEmpty(refundOrderInfoDtoList)) {
            return;
        }
        List<CustomsInventoryCalloffDTO> calloffDTOList = customsInventoryCalloffService.findListByOrderIdList(
                refundOrderInfoDtoList.stream().map(RefundOrderInfoDto::getRefDeclareId).collect(Collectors.toList()));
        Map<Long, CustomsInventoryCalloffDTO> calloffDTOMap = calloffDTOList.stream()
                .collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderId, Function.identity(), (k, v) -> v));
        //遍历ids执行修改操作
        for (RefundOrderInfoDto orderInfoDto : refundOrderInfoDtoList) {
            //先修改退货单
            Long id = orderInfoDto.getId();
            RefundOrderInfo orderInfo = new RefundOrderInfo();
            orderInfo.setId(id);
            //RefundCheckStatus这个是海关回执状态对应页面审核状态
            orderInfo.setRefundCheckStatus(reqDto.getRefundCheckStatus());
            orderInfo.setReason(OperateReasonEnums.getEnum(reqDto.getOperateReason()).getDesc());
            if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue())) {
                orderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INIT.getValue());
            }
            if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
                orderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INVENTORY_WAIT_CREATE.getValue());
            }
            if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) {
                //这个是回执状态
                orderInfo.setRefundCustomStatus(CustomsRefundOrderStatus.getEnum(reqDto.getManualReceipt()).getValue());
            }
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                orderInfo.setUpdateBy(userId);
            }
            orderInfo.setUpdateTime(new Date());
            refundOrderInfoMapper.updateByPrimaryKeySelective(orderInfo);

            // 更新清关单状态
            orderInfo.setRefListBillSn(orderInfoDto.getRefListBillSn());
            updateCustomsInventoryStatus(orderInfo);
            //同步取消单
            customsInventoryCalloffService.updateRefundStatusInfo(orderInfoDto.getRefDeclareId(), null, orderInfo.getRefundCheckStatus());
            OrderDTO orderDTO = orderService.findByIdFull(orderInfoDto.getRefDeclareId());
            if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(reqDto.getRefundCheckStatus())) {
                messageSender.sendMsg(orderDTO.getSn(), "ccs-company-tax-cancel-pass-topic");

                // 构建 海关回执 生成消息 审核通过
                RefundMessageDto refundMessageDto = new RefundMessageDto();
                refundMessageDto.setCustomsStatus("1");
                refundMessageDto.setGlobalSystemSn(orderDTO.getSystemGlobalSn());
                refundMessageDto.setTime((new Date()).getTime());
                messageService.createRefundMessage(orderDTO.getDeclareOrderNo(), JSON.toJSONString(refundMessageDto));
            }
            if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equals(reqDto.getRefundCheckStatus())) {
                // 构建 海关回执 生成消息 总署驳回
                RefundMessageDto refundMessageDto = new RefundMessageDto();
                refundMessageDto.setCustomsStatus("2");
                refundMessageDto.setGlobalSystemSn(orderDTO.getSystemGlobalSn());
                refundMessageDto.setTime((new Date()).getTime());
                CustomsRefundOrderStatus statusEnum = CustomsRefundOrderStatus.getEnum(reqDto.getManualReceipt());
                switch (statusEnum) {
                    case PAYMENT_BOOK: //原清单已经生成缴款书
                        refundMessageDto.setMsg("[Code:13105;Desc:退货申请：原清单已经生成缴款书]");
                        break;
                    case NOT_ENTERED: // 货未到区内
                    case NOT_ARRIVED: // 未运抵
                    case UNKNOWN_ADDRESS: // 收件地址不详
                    case DECLARATION_ERROR: // 企业申报错误
                    case DAMAGED: // 影响二次销售
                        refundMessageDto.setMsg("货未到区内");
                        break;
                }
                if (StringUtils.isNotBlank(refundMessageDto.getMsg())) {
                    messageService.createRefundMessage(orderDTO.getDeclareOrderNo(), JSON.toJSONString(refundMessageDto));
                }
            }
            //去日志描述
            //日志说明
            orderInfoDto.setRefundCustomCheckDetail("手动操作:" + OperateReasonEnums.getEnum(reqDto.getOperateReason()).getDesc());
            String internalStatus = null;
            String operateDes = TrackLogEnums.NULL.getDesc();
            if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue())) {
                operateDes = TrackLogEnums.REFUND_DECLARE_SUCCESS_INIT.getDesc();
                internalStatus = OrderInternalEnum.REFUNDING.getCode();
            } else if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAIT.getValue())) {
                operateDes = TrackLogEnums.REFUND_DECLARE_SUCCESS_PENDING_REVIEW.getDesc();
                internalStatus = OrderInternalEnum.REFUNDING.getCode();
            } else if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) { // 审核通过
                operateDes = TrackLogEnums.REFUND_DECLARE_SUCCESS.getDesc();
                internalStatus = OrderInternalEnum.REFUND_SUCCESS.getCode();
            } else if (Objects.equals(reqDto.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) { // 审核驳回
                operateDes = TrackLogEnums.REFUND_DECLARE_OVERRULE.getDesc();
                orderInfoDto.setRefundCustomCheckDetail("手动操作:" + CustomsRefundOrderStatus.getEnum(reqDto.getManualReceipt()).getDesc());
                internalStatus = OrderInternalEnum.REFUND_FAIL.getCode();
            }
            if (Objects.nonNull(internalStatus)) {
                orderService.updateOrderInternalStatus(orderInfoDto.getRefDeclareId(), internalStatus);
            }
            this.doExtraCallBack(orderInfoDto, reqDto);

            orderInfoDto.setRefundCheckStatus(reqDto.getRefundCheckStatus());
            //添加操作日志
//            createLog(orderInfoDto, userId, operateDes);
            RefundOrderInfo info = ConvertUtil.beanConvert(orderInfoDto, RefundOrderInfo.class);
            buildTrackLogEsAndSend(info, orderDTO, operateDes);
        }
    }

    /**
     * @param refundOrder
     */
    private void updateCustomsInventoryStatus(RefundOrderInfo refundOrder) {
        // 审核通过才更新
        if (!InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(refundOrder.getRefundCheckStatus())) {
            return;
        }
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findBySnSection(refundOrder.getRefListBillSn());
        if (Objects.isNull(inventoryDTO)) {
            log.warn("[退货更新清单状态]对应清单不存在，撤单id：{}", refundOrder.getId());
            return;
        }
        customsInventoryBaseService.updateAfterStatus(inventoryDTO, InventoryAfterStatus.REFUND_SUCCESS.getValue());
    }

    /**
     * 额外的回传消息
     *
     * @param orderInfoDto
     * @param reqVo
     */
    private void doExtraCallBack(RefundOrderInfoDto orderInfoDto, RefundOrderInfoDto reqVo) {
        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffService.findByOrderId(orderInfoDto.getRefDeclareId());
        List<Integer> orderTags;
        //不是小巨人的业务直接返回
        if (Objects.isNull(calloffDTO)) {
            return;
        }
        orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDTO.getOrderTag());
        if (orderTags.contains(InventoryCalloffOrderTagEnums.XIAOJUREN.getCode())) {
            log.info("修改退回状态-小巨人业务回传创建消息");
            OrderDTO orderDTO = orderService.findBySnSection(calloffDTO.getOrderSn());
            if (Objects.isNull(orderDTO)) {
                log.error("doExtraCallBack 根据申报单sn:{} 未查询到申报单", calloffDTO.getOrderSn());
                return;
            }
            RefundMessageDto refundMessageDto = new RefundMessageDto();
            refundMessageDto.setGlobalSystemSn(orderDTO.getSystemGlobalSn());
            refundMessageDto.setTime(new Date().getTime());
            refundMessageDto.setBusinessType("2");
            String refundCallBack = this.buildManualRefundCallBack(orderInfoDto, reqVo);
            if (Objects.nonNull(refundCallBack)) {
                String activeData = JSON.toJSONString(new OrderActiveInfo(orderInfoDto.getRefDeclareId()).buildResponseMsg(refundCallBack));
                List<String> subscribeList = Arrays.asList("CHANNEL-1");
                OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
                List<String> littleGiantRouteCodes = Arrays.asList(littleGiantRouteCodeList);
                if (DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags()).contains(DeclareOrderTagEnums.CAINIAO_WMS.getCode())
                        || (CollectionUtil.isNotEmpty(littleGiantRouteCodes) && littleGiantRouteCodes.contains(orderExtra.getSubmit().getRouteCode()))
                ) {
                    messageService.createMessageNotThrowEx(MessageType.ORDER_CUSTOMS_REFUND_TO_ERP, subscribeList, orderInfoDto.getRefDeclareNo(), activeData, "");
                }
            }
            if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
                refundMessageDto.setCustomsStatus("1");
            } else if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) {
                refundMessageDto.setCustomsStatus("2");
                refundMessageDto.setMsg(orderInfoDto.getManualReceipt());
            }
            if (StringUtils.isNotBlank(refundMessageDto.getCustomsStatus())) {
                messageService.createRefundMessage(orderInfoDto.getRefDeclareNo(), JSON.toJSONString(refundMessageDto), MessageType.ORDER_REFUND_NEW);
            }
        }
        if (orderTags.contains(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode())) {
            log.info("手动修改退回状态 - 退货仓业务回传创建消息");
            Integer messageType = null;
            if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
                messageType = RefundCallbackMessageStatus.DECLARE_SUCCESS.getCode();
            } else if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())
                    && Objects.equals(reqVo.getManualReceipt(), CustomsRefundOrderStatus.PAYMENT_BOOK.getValue())) {
                messageType = RefundCallbackMessageStatus.DECLARE_FAILED.getCode();
            }
            this.createMessageToRefundWarehouse(calloffDTO, messageType);
        }
        if (InventoryCalloffOrderTagEnums.contains(calloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.JD_REFUND)) {
            if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
                createMessageToJdRefund(calloffDTO, CustomsStat.CUSTOMS_PASS.getValue(), null);
            } else if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) {
                createMessageToJdRefund(calloffDTO, CustomsStat.CUSTOMS_REFUSE.getValue(), CustomsRefundOrderStatus.getEnum(reqVo.getManualReceipt()).getDesc());
            }
        }
    }

    private String buildManualRefundCallBack(RefundOrderInfoDto old, RefundOrderInfoDto reqVo) {
        String returnStatus;
        String returnInfo;
        if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
            returnStatus = CustomsStat.CUSTOMS_PASS.getValue();
            returnInfo = "[Code:2600;Desc:放行]null";
        } else if (Objects.equals(reqVo.getRefundCheckStatus(), RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) {
            returnStatus = CustomsStat.CUSTOMS_REFUSE.getValue();
            if (CustomsRefundOrderStatus.PAYMENT_BOOK.getValue().equals(reqVo.getManualReceipt())) {
                returnInfo = "[Code:13105;Desc:退货申请：原清单已经生成缴款书]";
            } else {
                CustomsRefundOrderStatus refundOrderStatus = CustomsRefundOrderStatus.getEnum(reqVo.getManualReceipt());
                if (Objects.isNull(refundOrderStatus)) {
                    return null;
                }
                returnInfo = refundOrderStatus.getDesc();
            }
        } else {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String data = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>" +
                "<CEB626Message xmlns=\"http://www.chinaport.gov.cn/ceb\" version=\"1.0\" guid=\"d0cdf79f-0ab5-4322-b6ab-1045a9ccf6fa\">" +
                "<InvtRefundReturn>" +
//                "<guid>43e6e694-84e5-4e82-af23-2104f9c1d8b4</guid>" +
                "<customsCode>2925</customsCode>" +
                "<agentCode>" + old.getAgentCode() + "</agentCode>" +
                "<ebpCode>" + old.getEbpCode() + "</ebpCode>" +
                "<ebcCode>" + old.getEbcCode() + "</ebcCode>" +
                "<copNo>" + old.getCopNo() + "</copNo>" +
                "<preNo>" + old.getPreNo() + "</preNo>" +
                "<invtNo>" + old.getInvtNo() + "</invtNo>" +
                "<returnStatus>" + returnStatus + "</returnStatus>" +
                "<returnTime>" + sdf.format(new Date()) + "</returnTime>" +
                "<returnInfo>" + returnInfo + "</returnInfo>" +
                "</InvtRefundReturn>" +
                "</CEB626Message>";
        return data;

    }


    @Deprecated
    @Override
    public void createLog(RefundOrderInfoDto infoDto, Integer userId, Integer operateDes) {
        TrackLogDTO trackLogDTO = new TrackLogDTO();
        trackLogDTO.setCreateBy(userId);
        trackLogDTO.setDeclareOrderNo(infoDto.getRefDeclareNo());
        trackLogDTO.setAuditStatus(infoDto.getRefundCheckStatus());
        trackLogDTO.setInventoryNo(infoDto.getInvtNo());
        trackLogDTO.setOperateDes(operateDes);
        trackLogDTO.setOrderType(InventoryOrderType.RETURNED_PURCHASE_ORDER.getCode());
        trackLogDTO.setLogDes(infoDto.getRefundCustomCheckDetail());
        trackLogDTO.setCreateTime(new Date());
        trackLogDTO.setNewStatus(OrderStatus.NULL.getValue());
        trackLogDTO.setOldStatus(OrderStatus.NULL.getValue());
        trackLogService.submit(trackLogDTO);
    }

    @Override
    public Map<Integer, Integer> listRefundStatusCount(RefundOrderInfoSearch search) {
        Map<Integer, Integer> map = new HashMap<>();
        for (RefundOrderEnum.STATUS_ENUM status : RefundOrderEnum.STATUS_ENUM.values()) {
            Example example = new Example(RefundOrderInfo.class);
            example.createCriteria()
                    .andEqualTo("deleted", false)
                    .andEqualTo("refundStatus", status.getValue());
            map.put(status.getValue(), refundOrderInfoMapper.selectCountByExample(example));
        }
        return map;
    }

    @Override
    public void updateStatusBatchByMailNo(List<String> mailNoList, Integer status, String inveStatus, String inveOrderSn) {
        log.info("RefundOrderInfoService updateStatusBatchByMailNo - mailNoList={}, RefundStatus={}, inveStatus={}, inveOrderSn={}",
                JSONUtils.toJSONString(mailNoList), status, inveStatus, inveOrderSn);
        if (CollectionUtils.isEmpty(mailNoList) || Objects.isNull(status)) {
            log.error("updateStatusByMailNo 参数为空, mailNo={}, status={}", JSONUtils.toJSONString(mailNoList), status);
            return;
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("mailNo", mailNoList);
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setRefundStatus(status);
        refundOrderInfo.setRefundInCustomsSn(inveOrderSn);
        refundOrderInfo.setCustomsStatus(inveStatus);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(refundOrderInfo);
        }
        refundOrderInfo.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByExampleSelective(refundOrderInfo, example);

    }

    @Override
    public List<RefundOrderInfoDto> findByOrderId(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andIn("refDeclareId", orderIdList).andEqualTo("deleted", false);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        return ConvertUtil.listConvert(refundOrderInfos, RefundOrderInfoDto.class);
    }

    /**
     * 退货入仓统计
     *
     * @param areaCompanyId
     * @return
     */
    @Override
    public int selectRefundInWarehouseCount(String areaCompanyId) {
        try {
            List<Integer> statusList = Arrays.asList(RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_CREATE.getValue(),
                    RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_FINISH.getValue(),
                    RefundOrderEnum.STATUS_ENUM.STATUS_INVENTORY_WAIT_CREATE.getValue());
            Example example = new Example(RefundOrderInfo.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("refundStatus", statusList).andEqualTo("deleted", false);
            if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
                criteria.andEqualTo("areaCompanyId", areaCompanyId);
            }
            return refundOrderInfoMapper.selectCountByExample(example);
        } catch (Exception e) {
            return 0;
        }
    }

    @Override
    public void updateStatusByMailNo(String mailNo, Integer status, String inveStatus, String inveOrderSn) {
        if (StringUtils.isEmpty(mailNo) || Objects.isNull(status)) {
            log.error("updateStatusByMailNo 参数为空, mailNo={}, status={}", mailNo, status);
            return;
        }
        List<String> mailNoList = new ArrayList<>();
        this.updateStatusBatchByMailNo(mailNoList, status, inveStatus, inveOrderSn);
    }

    @Override
    public Map<String, CancelAndRefundAfterSaleCount> selectRefundOrderInfoCountDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result = new ConcurrentHashMap<>();

        //时间段的搜索条件
        Example example = new Example(RefundOrderInfo.class);
        Example.Criteria timeCriteria = example.createCriteria();
        timeCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        timeCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        //遍历操作人统计数量
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, timeCriteria, result);
            futureList.add(CompletableFuture.runAsync(runnable, refundCountThreadExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("售后统计-退货单组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("售后统计-退货单组合CompletableFuture结果异常" + e.getMessage(), e);
        }

        return result;
    }


    class Task extends TraceDataRunnable {

        private final String handlerId;

        private final Example.Criteria timeCriteria;

        private ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result;

        public Task(String handlerId, Example.Criteria timeCriteria, ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result) {
            super();
            this.handlerId = handlerId;
            this.timeCriteria = timeCriteria;
            this.result = result;
        }


        @Override
        public void proxy() {
            CancelAndRefundAfterSaleCount refundAfterSaleCount = new CancelAndRefundAfterSaleCount();
            refundAfterSaleCount.setUserId(handlerId);

            //需申报 - 所有状态+时间段
            Example example1 = new Example(RefundOrderInfo.class);
            example1.createCriteria().andEqualTo("deleted", false);
            example1.and(timeCriteria);
            refundAfterSaleCount.setInitDeclare(refundOrderInfoMapper.selectCountByExample(example1));

            //处理申报 - 待总署审核（审核状态）+时间段
            Example example2 = new Example(RefundOrderInfo.class);
            example2.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("refundCheckStatus", RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_WAITING.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example2.and(timeCriteria);
            refundAfterSaleCount.setProcessingDeclare(refundOrderInfoMapper.selectCountByExample(example2));

            //申报成功 - 审核通过（审核状态）+时间段
            Example example3 = new Example(RefundOrderInfo.class);
            example3.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("refundCheckStatus", InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(timeCriteria);
            refundAfterSaleCount.setSuccessfulDeclare(refundOrderInfoMapper.selectCountByExample(example3));

            //申报失败 - 总署驳回（审核状态）+待退货（退货状态）+时间段
            Example example4 = new Example(RefundOrderInfo.class);
            example4.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("refundCheckStatus", RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())
                    .andEqualTo("refundStatus", RefundOrderEnum.STATUS_ENUM.STATUS_WAIT.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example4.and(timeCriteria);
            refundAfterSaleCount.setFailedDeclare(refundOrderInfoMapper.selectCountByExample(example4));

            //退货关闭 - 退货关闭（退货状态）+时间段
            Example example5 = new Example(RefundOrderInfo.class);
            example5.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("refundStatus", RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example5.and(timeCriteria);
            refundAfterSaleCount.setCancel(refundOrderInfoMapper.selectCountByExample(example5));

            result.put(handlerId, refundAfterSaleCount);
        }
    }

    private final List<Integer> countStatus = Arrays.asList(
            RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_FINISH.getValue(),
            RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_CREATE.getValue(),
            RefundOrderEnum.STATUS_ENUM.STATUS_INVENTORY_WAIT_CREATE.getValue()
    );

    @Override
    public void updateCustomsStatusByInveSn(String refundInCustomsSn, String inventoryOrderStatus) {
        log.info("refundOrderInfoService updateCustomsStatusByInveSn , refundInCustomsSn={}, status={}", refundInCustomsSn, inventoryOrderStatus);
        if (StringUtils.isEmpty(refundInCustomsSn)) {
            log.error("updateInventoryStatusByInveSn 参数为空, refundInCustomsSn={}, status={}", refundInCustomsSn, inventoryOrderStatus);
            return;
        }
        Example oldExample = new Example(RefundOrderInfo.class);
        oldExample.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("refundInCustomsSn", refundInCustomsSn);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(oldExample);
        if (CollectionUtils.isEmpty(refundOrderInfos)) {
            log.warn("updateCustomsStatusByInveSn 关联清关单号[{}] 未关联退货单", refundInCustomsSn);
            return;
        }
        List<Long> idList = refundOrderInfos.stream().map(RefundOrderInfo::getId).collect(Collectors.toList());
        log.info("updateInventoryStatusByInveSn 待更新idList={}", JSONUtils.toJSONString(idList));
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andIn("id", idList);
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        if (Objects.equals(InventoryOrderEnum.STATUS_DISCARD.getCode(), inventoryOrderStatus)) {
            refundInCustomsSn = "";
            inventoryOrderStatus = "";
        }
        InventoryOrderEnum inventoryOrderEnum = InventoryOrderEnum.getEnum(inventoryOrderStatus);
        switch (inventoryOrderEnum) {
            case STATUS_CREATED:
            case STATUS_PERFECT:
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_CREATE.getValue());
                break;
            case STATUS_ENDORSEMENT:
            case STATUS_START:
            case STATUS_START_STORAGED:
            case STATUS_SERVERING:
            case STATUS_FAILURE:
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_FINISH.getValue());
                break;
            case STATUS_FINISH:
                refundOrderInfo.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_COMPLETE.getValue());
                refundOrderInfo.setRefundStatusTime(new Date());
                break;
        }
        refundOrderInfo.setRefundInCustomsSn(refundInCustomsSn);
        refundOrderInfo.setCustomsStatus(inventoryOrderStatus);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(refundOrderInfo);
        }
        refundOrderInfo.setUpdateTime(new Date());
        log.info("refundOrderInfoService updateCustomsStatusByInveSn , refundOrderInfo={}", JSONUtils.toJSONString(refundOrderInfo));
        refundOrderInfoMapper.updateByExampleSelective(refundOrderInfo, example);
    }

    @Override
    public void updateRefundCustomCode(List<Long> idList, String refundCustomCode) {

        if (CollectionUtils.isEmpty(idList)) {
            log.error("RefundOrderInfoServiceImpl updateRefundCustomCode idList 参数为空");
            return;
        }
        if (StringUtils.isEmpty(refundCustomCode)) {
            log.error("RefundOrderInfoServiceImpl updateRefundCustomCode CustomCode 参数为空");
            return;
        }
        // 判断跨关区状态
        List<RefundOrderInfoDto> refundOrderInfoDTOList = this.findRefundListByIdList(idList);
        List<String> invSnList = refundOrderInfoDTOList.stream().map(RefundOrderInfoDto::getRefListBillSn).collect(Collectors.toList());
        List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.findBySnList(invSnList);
        Map<String, String> invSnCustomsMap = inventoryDTOList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getSn, CustomsInventoryDTO::getCustoms));
        Map<Boolean, List<RefundOrderInfoDto>> crossCustomsMap = refundOrderInfoDTOList.stream()
                .collect(Collectors.groupingBy(i -> !Objects.equals(invSnCustomsMap.get(i.getRefListBillSn()), refundCustomCode)));

        for (Map.Entry<Boolean, List<RefundOrderInfoDto>> crossCustomsEntry : crossCustomsMap.entrySet()) {
            boolean isCrossCustoms = crossCustomsEntry.getKey();
            List<RefundOrderInfoDto> subOrderList = crossCustomsEntry.getValue();
            List<Long> subIdList = subOrderList.stream().map(RefundOrderInfoDto::getId).collect(Collectors.toList());
            Example example = new Example(RefundOrderInfo.class);
            example.createCriteria().andEqualTo("deleted", false).andIn("id", subIdList);
            RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
            refundOrderInfo.setCustomsCode(refundCustomCode);
            refundOrderInfo.setRefundCrossCustomsFlag(isCrossCustoms ? 1 : 0);
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(refundOrderInfo);
            }
            refundOrderInfo.setUpdateTime(new Date());
            refundOrderInfoMapper.updateByExampleSelective(refundOrderInfo, example);
            for (RefundOrderInfoDto refundOrderInfoDto : subOrderList) {
                RefundOrderInfo origin = new RefundOrderInfo();
                BeanUtils.copyProperties(refundOrderInfoDto, origin);
                CustomsDistrictEnum newDistrictEnum = CustomsDistrictEnum.getEnum(refundCustomCode);
                CustomsDistrictEnum oldDistrictEnum = CustomsDistrictEnum.getEnum(origin.getCustomsCode());
                this.buildTrackLogEsAndSend(origin,
                        OrderInternalEnum.REFUNDING.getCode(),
                        oldDistrictEnum.getDesc() + "->" + newDistrictEnum.getDesc(),
                        TrackLogConstantMixAll.REFUND_MODIFY_CUSTOMS);
            }
        }
    }

    @Override
    public List<RefundOrderInfoDto> findRefundListByIdList(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        List<RefundOrderInfo> list = refundOrderInfoMapper.selectByExample(example);
        return JSON.parseArray(JSON.toJSONString(list), RefundOrderInfoDto.class);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void submitRefundOrderPart(List<RefundOrderPartInfoDTO> importList) {
        if (CollectionUtils.isEmpty(importList)) {
            throw new ArgsErrorException("请检查导入的数据是否正确");
        }
        Map<String, List<RefundOrderPartInfoDTO>> refundMailMap = importList.stream()
                .collect(Collectors.groupingBy(refund -> refund.getMailNo() + "#" + refund.getRefundExpressName()));
        for (Map.Entry<String, List<RefundOrderPartInfoDTO>> entry : refundMailMap.entrySet()) {
            String key = entry.getKey();
            String[] split = key.split("#");
            String mailNo = split[0];
            String expressName = split[1];
            List<RefundOrderPartInfoDTO> importRefundInfos = entry.getValue();
            ExpressDTO expressDTO = expressService.findOneByName(expressName);
            if (Objects.isNull(expressDTO)) {
                throw new ArgsErrorException("退货快递名称无效：" + expressName);
            }
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByLogistics90Days(expressDTO.getId(), mailNo);
            if (Objects.isNull(customsInventoryDTO)) {
                throw new ArgsErrorException("运单号无法找到相关清单信息,运单号:" + mailNo);
            }
            List<CustomsInventoryItemDTO> customsInventoryItemList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            if (CollectionUtils.isEmpty(customsInventoryItemList)) {
                throw new ArgsErrorException("运单号无法找到相关清单商品信息,运单号:" + mailNo);
            }
            Map<Long, CustomsInventoryItemDTO> itemMap = customsInventoryItemList.stream()
                    .collect(Collectors.toMap(CustomsInventoryItemDTO::getId, Function.identity(), (v1, v2) -> v1));
            Example example = updateRefundOrderStatus(importRefundInfos, customsInventoryDTO, customsInventoryItemList);
            RefundOrderInfo refundOrder = refundOrderInfoMapper.selectOneByExample(example);
            //更新清单逆向数量
            // 清单项数据处理，获取料号和数量的映射
            Map<String, List<String>> productIdItemInfoMap = buildProductIdMap(customsInventoryItemList);
            List<RefundPartDetailLogDTO> changeList = importDataDeductions(importRefundInfos, itemMap, productIdItemInfoMap);
            // 日志记录
            recordLog(customsInventoryDTO, refundOrder, changeList);
        }
    }

    @Override
    public void submitRefundOrderPartByByte(List<RefundOrderPartInfoDTO> importList) {
        if (CollectionUtils.isEmpty(importList)) {
            throw new ArgsErrorException("请检查导入的数据是否正确");
        }
        // 根据申报单号进行分组
        Map<String, List<RefundOrderPartInfoDTO>> refundMap = importList.stream().collect(Collectors.groupingBy(RefundOrderPartInfoDTO::getOriginOrderNo));
        // 根据导入单据进行部分退数据的处理
        refundMap.forEach((declareNo, importRefundInfos) -> {
            // 清单主体
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByDeclareNo90Days(declareNo);
            if (Objects.isNull(customsInventoryDTO)) {
                throw new ArgsErrorException("申报单号无法找到相关清单信息,申报单号:" + declareNo);
            }
            // 清单明细
            List<CustomsInventoryItemDTO> customsInventoryItemList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            Map<Long, CustomsInventoryItemDTO> itemMap = customsInventoryItemList.stream()
                    .collect(Collectors.toMap(CustomsInventoryItemDTO::getId, Function.identity(), (v1, v2) -> v1));
            if (CollectionUtils.isEmpty(customsInventoryItemList)) {
                throw new ArgsErrorException("申报单号无法找到相关清单商品信息,申报单号:" + declareNo);
            }
            // 更新退货单的状态
            Example example = updateRefundOrderStatus(importRefundInfos, customsInventoryDTO, customsInventoryItemList);
            RefundOrderInfo refundOrder = refundOrderInfoMapper.selectOneByExample(example);
            // 料号map组装
            Map<String, List<String>> productIdItemInfoMap = buildProductIdMap(customsInventoryItemList);
            List<RefundPartDetailLogDTO> changeList = importDataDeductions(importRefundInfos, itemMap, productIdItemInfoMap);
            // 日志记录
            recordLog(customsInventoryDTO, refundOrder, changeList);
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void manualEndorsement(List<Long> idList) {
        List<RefundOrderInfoDto> refundOrderInfoDtoList = this.findRefundListByIdList(idList);
        refundOrderInfoDtoList.forEach(refundOrderInfoDto -> {
            if (!Objects.equals(refundOrderInfoDto.getRefundCrossCustomsFlag(), 1)) {
                throw new ArgsInvalidException("非跨关区的退货单无法手动核注,请建立清关单");
            }
            if (!Objects.equals(refundOrderInfoDto.getRefundStatus(), RefundOrderEnum.STATUS_ENUM.STATUS_INVENTORY_WAIT_CREATE.getValue())) {
                throw new ArgsInvalidException("仅清关待创建状态下，允许手动核扣");
            }
        });
        refundOrderInfoDtoList.forEach(refundOrderInfoDto -> {
            this.updateStatus(refundOrderInfoDto.getId(), RefundOrderEnum.STATUS_ENUM.STATUS_COMPLETE.getValue(), null);
            orderService.updateOrderInternalStatus(refundOrderInfoDto.getRefDeclareId(), OrderInternalEnum.REFUND_INTO_WAREHOUSE.getCode());
            customsInventoryCalloffService.updateCalloffStatusByLogisticsNo(refundOrderInfoDto.getMailNo(), null, 1,
                    InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
            RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
            BeanUtils.copyProperties(refundOrderInfoDto, refundOrderInfo);
            buildTrackLogEsAndSend(refundOrderInfo, OrderInternalEnum.REFUND_INTO_WAREHOUSE.getCode(),
                    "跨关区：手动核注",
                    TrackLogConstantMixAll.REFUND_CROSS_CUSTOMS_MANUAL_ENDORSEMENT);
        });
    }

    @Override
    public List<RefundOrderInfoDto> buildDTOList(Map<CustomsInventoryDTO, OrderDTO> customsInventoryOrderDTOMap, Map<String, CustomsInventoryCalloffDTO> calloffDTOMap, String reason) {
        List<RefundOrderInfoDto> refundOrderInfoDtoList = new ArrayList<>();
        customsInventoryOrderDTOMap.forEach((customsInventoryDTO, orderDTO) -> {
            RefundOrderInfoDto infoDto = new RefundOrderInfoDto();
            CustomsInventoryCalloffDTO customsInventoryCalloffDTO = calloffDTOMap.get(orderDTO.getSn());
            if (Objects.isNull(customsInventoryCalloffDTO)) {
                return;
            }
            if (StringUtils.isNotEmpty(customsInventoryCalloffDTO.getRefundLogisticsNo())) {
                String refundMailNo = customsInventoryCalloffDTO.getRefundLogisticsNo();
                infoDto.setRefundMailNo(refundMailNo);
            }
            infoDto.setCreateBy(UserUtils.getUserId());
            infoDto.setUpdateBy(UserUtils.getUserId());
            infoDto.setRefundNo(sequenceService.generateRefundOrderSn());
            infoDto.setId(NumberUtils.createLong(StringUtils.replacePattern(infoDto.getRefundNo(), "[a-zA-Z]", "")));
            infoDto.setRefDeclareId(orderDTO.getId());
            infoDto.setRefDeclareNo(orderDTO.getDeclareOrderNo());
            infoDto.setChannelOrderNo(orderDTO.getOutOrderNo());
            infoDto.setReason(reason);
            infoDto.setRefundCheckStatus(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue());
            infoDto.setRefundCheckTime(null);
            infoDto.setRefundStatus(RefundOrderEnum.STATUS_ENUM.STATUS_INIT.getValue());
            infoDto.setRefundStatusTime(null);
            infoDto.setAccountBookId(customsInventoryDTO.getAccountBookId());
            infoDto.setCustomsCode(customsInventoryDTO.getCustoms());
            infoDto.setRefListBillId(customsInventoryDTO.getId());
            infoDto.setRefListBillSn(customsInventoryDTO.getSn());
            infoDto.setInvtNo(customsInventoryDTO.getInventoryNo());
            infoDto.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
            infoDto.setBuyerIdType(customsInventoryDTO.getBuyerIdType());
            infoDto.setBuyerName(customsInventoryDTO.getBuyerName());
            infoDto.setBuyerTelephone(customsInventoryDTO.getBuyerTelNumber());
            infoDto.setMailNo(customsInventoryDTO.getLogisticsNo());
            infoDto.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
            infoDto.setPreNo(customsInventoryDTO.getPreNo());
            infoDto.setCustomsCode(customsInventoryDTO.getCustoms());
            infoDto.setInvtNo(customsInventoryDTO.getInventoryNo());
            if (Objects.nonNull(customsInventoryDTO.getAgentCompanyId())) {
                CompanyDTO agentCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAgentCompanyId());
                if (Objects.nonNull(agentCompany)) {
                    infoDto.setAgentCode(agentCompany.getCode());
                    infoDto.setAgentName(agentCompany.getName());
                }
            }
            Long logisticsCompanyId = customsInventoryDTO.getLogisticsCompanyId();
            if (logisticsCompanyId != null) {
                CompanyDTO companyDTO = baseDataService.getUnifiedCrossCodeCompanyById(logisticsCompanyId);
                infoDto.setLogisticsCode(companyDTO.getCode());
                infoDto.setLogisticsName(companyDTO.getName());
            }
            CompanyDTO companyDTO = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbpId());
            if (companyDTO != null) {
                infoDto.setEbpCode(companyDTO.getCode());
                infoDto.setEbpName(companyDTO.getName());
            }
            companyDTO = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbcId());
            if (companyDTO != null) {
                infoDto.setEbcCode(companyDTO.getCode());
                infoDto.setEbcName(companyDTO.getName());
            }
            companyDTO = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAssureCompanyId());
            if (companyDTO != null) {
                infoDto.setAssureNo(companyDTO.getCode());
                infoDto.setAssureName(companyDTO.getName());
            }
            // 判断是否为部分退
            infoDto.setRefundPartFlag(Objects.equals(customsInventoryCalloffDTO.getPartRefundFlag(), 1) ?
                    RefundPartFlagEnum.REFUND_PART_YES.getValue() : RefundPartFlagEnum.REFUND_PART_NO.getValue());
            refundOrderInfoDtoList.add(infoDto);
        });
        log.info("RefundOrderInfoServiceImpl buildDTOList list={}", JSON.toJSONString(refundOrderInfoDtoList));
        return refundOrderInfoDtoList;
    }

    @Override
    public void callbackRefundWarehouse(OrderRefundWarehouseCallbackMessage message) {
        RefundCallbackMessageStatus refundCallbackMessageStatus = RefundCallbackMessageStatus.getEnum(message.getCallbackStatus());
        switch (refundCallbackMessageStatus) {
            case DECLARE_START:
                iwmsRefundWarehouseCallbackService.startCallback(message.getLogisticsNo(), message.getRefundLogisticsNo(), message.getWmsWarehouseCode(), 1);
                break;
            case DECLARE_SUCCESS:
                iwmsRefundWarehouseCallbackService.callback(message.getLogisticsNo(), message.getRefundLogisticsNo(), message.getWmsWarehouseCode(), 1);
                break;
//            case CALLOFF_REJECT: 驳回不回传退货仓了
            case DECLARE_FAILED:
                iwmsRefundWarehouseCallbackService.callback(message.getLogisticsNo(), message.getRefundLogisticsNo(), message.getWmsWarehouseCode(), 2);
                break;
            default:
                break;
        }
    }

    /**
     * 创建回传退货仓消息
     *
     * @param calloffDto
     * @param messageType
     */
    @Override
    public void createMessageToRefundWarehouse(CustomsInventoryCalloffDTO calloffDto, Integer messageType) {
        // 防止接受回执时异常回滚导致轨迹日志没记录，这边回告消息失败try catch一下
        try {
            if (Objects.isNull(calloffDto) || Objects.isNull(messageType)) {
                log.info("sendMessageToRefundWarehouse 异常， 参数为空");
                return;
            }
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDto.getOrderTag());
            if (!orderTags.contains(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode())) {
                log.info("取消单不含退货仓标记，无需回传退货仓 declareOrderNo={}", calloffDto.getDeclareOrderNo());
                return;
            }
            // 退货仓 退货申报回传状态
            OrderDTO orderDTO = orderService.findBySnSection(calloffDto.getOrderSn());
            if (Objects.isNull(orderDTO)) {
                log.error("sendMessageToRefundWarehouse 申报单不存在， orderSn={}, declareOrderNo={}", calloffDto.getOrderSn(), calloffDto.getDeclareOrderNo());
                return;
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(orderExtra.getSubmit().getErpPhyWarehouseSn());
            EntityWarehouseDTO entityWarehouseDTO = entityWarehouseDTOList.stream()
                    .filter(i -> Objects.equals(i.getCustomsBookId(), calloffDto.getAccountBookId())).findAny().orElse(null);
            String wmsWarehouseCode = null;
            if (Objects.nonNull(entityWarehouseDTO)) {
                wmsWarehouseCode = entityWarehouseDTO.getWmsWarehouseCode();
            }
            List<String> subscribeList = Collections.singletonList("CHANNEL-1");
            String activeData = JSON.toJSONString(
                    new OrderRefundWarehouseCallbackMessage(calloffDto.getLogisticsNo(), calloffDto.getRefundLogisticsNo(), messageType, wmsWarehouseCode));
            log.info("sendMessageToRefundWarehouse 发送回执 - {}", activeData);
            messageService.createMessage(MessageType.ORDER_REFUND_REFUND_WAREHOUSE, subscribeList, calloffDto.getDeclareOrderNo(), activeData, "");
        } catch (Exception e) {
            log.error("sendMessageToRefundWarehouse 异常 declareOrderNo={}", calloffDto.getDeclareOrderNo(), e);
        }
    }

    @Override
    public void updateExceptionInfo(String declareOrderNo, String exceptionInfo) {
        if (StringUtil.isEmpty(declareOrderNo)) {
            return;
        }
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        refundOrderInfo.setRefundExceptionFlag(1);
        refundOrderInfo.setRefundExceptionInfo(exceptionInfo);
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria()
                .andEqualTo("deleted", false)
                .andEqualTo("refDeclareNo", declareOrderNo);
        refundOrderInfoMapper.updateByExampleSelective(refundOrderInfo, example);
    }

    @Override
    public List<RefundOrderInfoDto> findException() {
        Example example = new Example(RefundOrderInfo.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("refundExceptionFlag", 1);
        List<RefundOrderInfo> refundOrderInfos = refundOrderInfoMapper.selectByExample(example);
        return ConvertUtil.listConvert(refundOrderInfos, RefundOrderInfoDto.class);
    }

    private Example updateRefundOrderStatus(List<RefundOrderPartInfoDTO> importRefundInfos, CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> customsInventoryItemList) {
        RefundOrderInfo refundOrderInfo = new RefundOrderInfo();
        Example example = new Example(RefundOrderInfo.class);
        // 通过申报单号更新退货单
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("refListBillId", customsInventoryDTO.getId());
        int itemSum = customsInventoryItemList.stream().mapToInt(CustomsInventoryItemDTO::getCount).sum();
        int importSum = importRefundInfos.stream().mapToInt(RefundOrderPartInfoDTO::getDeclareRefundQty).sum();
        if (Objects.equals(itemSum, importSum)) {
            refundOrderInfo.setRefundPartFlag(RefundPartFlagEnum.REFUND_PART_NO.getValue());
        } else {
            refundOrderInfo.setRefundPartFlag(RefundPartFlagEnum.REFUND_PART_YES.getValue());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(refundOrderInfo);
        }
        refundOrderInfo.setUpdateTime(new Date());
        refundOrderInfoMapper.updateByExampleSelective(refundOrderInfo, example);
        return example;
    }

    private void recordLog(CustomsInventoryDTO customsInventoryDTO, RefundOrderInfo
            refundOrder, List<RefundPartDetailLogDTO> changeList) {
        RefundPartLogDTO refundPartLogDTO = new RefundPartLogDTO();
        refundPartLogDTO.setRefundNo(refundOrder.getRefundNo());
        refundPartLogDTO.setRefundId(refundOrder.getId());
        refundPartLogDTO.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
        refundPartLogDTO.setInventoryNo(customsInventoryDTO.getInventoryNo());
        refundPartLogDTO.setAccountBookId(customsInventoryDTO.getAccountBookId());
        refundPartLogDTO.setSn(customsInventoryDTO.getSn());
        refundPartLogDTO.setChangeList(changeList);
        recordTrackLog(customsInventoryDTO, refundPartLogDTO);
    }

    private static Map<String, List<String>> buildProductIdMap
            (List<CustomsInventoryItemDTO> customsInventoryItemList) {
        Map<String, List<String>> productIdItemInfoMap = new HashMap<>();
        // 清单项数据处理，获取料号和数量的映射
        for (CustomsInventoryItemDTO inventoryItemDTO : customsInventoryItemList) {
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(inventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            if (Objects.isNull(itemExtra)) {
                log.error("RefundOrderInfoServiceImpl submitRefundOrderPart 无法找到清单明细的拓展信息 id={}", inventoryItemDTO.getId());
                throw new ArgsErrorException("无法找到清单明细的拓展信息:" + inventoryItemDTO.getId());
            }
            // value：itemId#sort#count
            String itemInfo = Optional.ofNullable(inventoryItemDTO.getId())
                    .orElse(-1L) + "#" +
                    Optional.ofNullable(inventoryItemDTO.getSort()).orElse(0) + "#" +
                    Optional.ofNullable(inventoryItemDTO.getCount()).orElse(0);

            if (StrUtil.isNotEmpty(itemExtra.getProductId())) {
                List<String> list = productIdItemInfoMap.computeIfAbsent(itemExtra.getProductId(), k -> new ArrayList<>());
                list.add(itemInfo);
            }
        }
        return productIdItemInfoMap;
    }

    private List<RefundPartDetailLogDTO> importDataDeductions
            (List<RefundOrderPartInfoDTO> importRefundInfos, Map<Long, CustomsInventoryItemDTO> itemMap, Map<String, List<String>> productIdItemInfoMap) {
        List<RefundPartDetailLogDTO> changeList = new ArrayList<>();
        // 导入数据合并
        Map<String, Integer> importItemCountMap = importRefundInfos.stream()
                .collect(Collectors.toMap(RefundOrderPartInfoDTO::getItemNo, RefundOrderPartInfoDTO::getDeclareRefundQty, Integer::sum));
        // 导入数据遍历扣减
        for (Map.Entry<String, Integer> entry : importItemCountMap.entrySet()) {
            String importItemNo = entry.getKey();
            Integer importItemSumCount = entry.getValue();
            List<String> list = productIdItemInfoMap.get(importItemNo);
            if (list == null) {
                continue;
            }
            sort(list);
            for (String itemInfo : list) {
                String[] split = itemInfo.split("#");
                Long itemId = Long.parseLong(split[0]);
                Integer itemCount = Integer.parseInt(split[2]);
                if (importItemSumCount > 0 && importItemSumCount <= itemCount) {
                    // 导入数量小于等于本行总数，则扣减剩余数量
                    RefundPartDetailLogDTO refundPartDetailLogDTO = updateItemRefundQty(itemMap, importItemSumCount, itemId);
                    changeList.add(refundPartDetailLogDTO);
                    // 剩余数量为0
                    importItemSumCount = 0;
                } else if (importItemSumCount > 0) {
                    // 导入数量大于本行总数，则扣减本行总数，剩余数量继续遍历
                    importItemSumCount -= itemCount;
                    RefundPartDetailLogDTO refundPartDetailLogDTO = updateItemRefundQty(itemMap, itemCount, itemId);
                    changeList.add(refundPartDetailLogDTO);
                } else if (importItemSumCount == 0) {
                    // 若还有行，则更新剩余行数量的逆向数量为0
                    RefundPartDetailLogDTO refundPartDetailLogDTO = updateItemRefundQty(itemMap, 0, itemId);
                    changeList.add(refundPartDetailLogDTO);
                }
            }
            // 删除已处理的行
            productIdItemInfoMap.remove(importItemNo);
        }
        // 将剩余的行逆向数量更新为0
        for (Map.Entry<String, List<String>> entry : productIdItemInfoMap.entrySet()) {
            List<String> value = entry.getValue();
            for (String s : value) {
                String[] split = s.split("#");
                Long itemId = Long.parseLong(split[0]);
                RefundPartDetailLogDTO refundPartDetailLogDTO = updateItemRefundQty(itemMap, 0, itemId);
                changeList.add(refundPartDetailLogDTO);
            }
        }
        return changeList;
    }

    private RefundPartDetailLogDTO updateItemRefundQty(Map<Long, CustomsInventoryItemDTO> itemMap, Integer
            declareRefundQty, Long itemId) {
        CustomsInventoryItemDO customsInventoryItemDO = new CustomsInventoryItemDO();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        CustomsInventoryItemDTO itemDTO = itemMap.get(itemId);
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(itemDTO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(itemDTO.getCreateTime()));
        // Step::根据时间区间检索更新
        Example exampleItem = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = exampleItem.createCriteria();
        criteria.andEqualTo("id", itemId);
        exampleItem.and(criteria);
        UserUtils.setUpdateBy(customsInventoryItemDO);
        customsInventoryItemDO.setUpdateTime(new Date());
        customsInventoryItemDO.setRefundDeclareQty(declareRefundQty);
        customsInventoryItemMapper.updateByExampleSelective(customsInventoryItemDO, exampleItem);

        RefundPartDetailLogDTO detailLogDTO = new RefundPartDetailLogDTO();
        detailLogDTO.setItemId(itemId);
        detailLogDTO.setItemNo(itemDTO.getItemNo());
        detailLogDTO.setOldRefundDeclareQty(itemDTO.getRefundDeclareQty());
        detailLogDTO.setNewRefundDeclareQty(declareRefundQty);
        return detailLogDTO;
    }

    private void sort(List<String> value) {
        value.sort((o1, o2) -> {
            String[] split1 = o1.split("#");
            String[] split2 = o2.split("#");
            int i = Integer.parseInt(split1[1]);
            int i1 = Integer.parseInt(split2[1]);
            if (i > i1) {
                return 1;
            } else if (i < i1) {
                return -1;
            } else {
                return 0;
            }
        });
    }

    private void recordTrackLog(CustomsInventoryDTO customsInventoryDTO, RefundPartLogDTO refundPartLogDTO) {
        TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
        trackLogEsDO.setEventTime(new Date());
        trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDO.setOrderId(customsInventoryDTO.getOrderId());
        trackLogEsDO.setOrderSn(customsInventoryDTO.getOrderSn());
        trackLogEsDO.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
        trackLogEsDO.setRequestMessage(new Gson().toJson(refundPartLogDTO));
        trackLogEsDO.setSender(TrackLogConstantMixAll.NULL);
        trackLogEsDO.setReceiver(TrackLogConstantMixAll.NULL);
        trackLogEsDO.setEventDesc(TrackLogConstantMixAll.PART_REFUND_IMPORT);
        trackLogEsDO.setOperator(UserUtils.getUserRealName());
        trackLogEsDO.setInternalStatus(OrderInternalEnum.REFUNDING.getCode());
        trackLogEsService.submit(trackLogEsDO);
    }
}