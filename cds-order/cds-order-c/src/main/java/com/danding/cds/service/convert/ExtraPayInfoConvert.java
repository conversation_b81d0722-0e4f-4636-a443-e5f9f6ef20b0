package com.danding.cds.service.convert;

import java.math.BigDecimal;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.base.bean.dao.ExtraPayInfoDO;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.ExtraPayInfoMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.bean.dto.ExtraPayInfoSubmitDto;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

@Component
public class ExtraPayInfoConvert {

    @Resource
    private ExtraPayInfoMapper extraPayInfoMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Deprecated
    public ExtraPayInfoDO preExtraPayInfo(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {
        OrderSubmitDto orderSubmitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        return this.preExtraPayInfo(userId, orderSubmitDto, workSpace);
    }

    public ExtraPayInfoDO preExtraPayInfo(Long userId, OrderSubmitDto submit, OrderConvertWorkSpace workSpace) {
        ExtraPayInfoSubmitDto extraPayInfoSubmit = new ExtraPayInfoSubmitDto();
        extraPayInfoSubmit.setDeclareOrderNo(submit.getDeclareOrderNo());
        extraPayInfoSubmit.setTradePayNo(submit.getTradePayNo());
        extraPayInfoSubmit.setTradeTime(submit.getTradeTime());
        extraPayInfoSubmit.setPayChannel(submit.getPayChannel());
        extraPayInfoSubmit.setRouteCode(submit.getRouteCode());
        extraPayInfoSubmit.setPayTransactionId(submit.getPayTransactionId());
        extraPayInfoSubmit.setVerDept(submit.getVerDept());
        extraPayInfoSubmit.setPayWay(submit.getPayWay());
        extraPayInfoSubmit.setPayTransactionAmount(submit.getPayTransactionAmount());
        extraPayInfoSubmit.setRecpCode(submit.getRecpCode());
        extraPayInfoSubmit.setRecpName(submit.getRecpName());
        extraPayInfoSubmit.setRecpAccount(submit.getRecpAccount());
        extraPayInfoSubmit.setPayRequestMessage(submit.getPayRequestMessage());
        extraPayInfoSubmit.setPayResponseMessage(submit.getPayResponseMessage());
        extraPayInfoSubmit.setOrigGoodsInfoList(submit.getOrigGoodsInfoList());
        return this.preExtraPayInfo(extraPayInfoSubmit);
    }

    public void saveExtraPayInfo(String ebpCode, ExtraPayInfoSubmitDto submit) {

        CompanyDTO companyDTO = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
        if (companyDTO == null) {
            throw new ArgsErrorException("海关编码:" + ebpCode + "企业不存在");
        }
        ExtraPayInfoDO extraPayInfoDO = this.preExtraPayInfo(submit, companyDTO.getId());
        save(extraPayInfoDO);
    }

    public ExtraPayInfoDO preExtraPayInfo(ExtraPayInfoSubmitDto submit) {
        // 这个位置先校验路由
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        if (routeDTO == null || routeDTO.getEnable() != 1) {
            throw new ArgsErrorException("申报路径不存在或未启用,路径标识：" + submit.getRouteCode());
        }
        return this.preExtraPayInfo(submit, routeDTO.getEbpId());
    }

    public ExtraPayInfoDO preExtraPayInfo(ExtraPayInfoSubmitDto submit, Long ebpId) {
        ExtraPayInfoDO extraPayInfoDO = new ExtraPayInfoDO();
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getTradePayNo())) {
            throw new ArgsErrorException("交易流水号不能为空");
        }
        if (LongUtil.isNone(submit.getTradeTime())) {
            throw new ArgsErrorException("交易时间不能为空");
        }
        if (StringUtils.isEmpty(submit.getPayChannel())) {
            throw new ArgsErrorException("支付渠道编码不能为空");
        }
//        PayChannelDTO payChannelDTO = workSpace.getPayChannelDTO(payChannelService, submit.getPayChannel());
        PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOByCode(submit.getPayChannel());
        if (payChannelDTO == null || payChannelDTO.getEnable() != 1) {
            throw new ArgsErrorException("支付渠道编码未配置或未启用:" + submit.getPayChannel());
        }
//        RouteDTO routeDTO = workSpace.getRouteDTO(routeService, submit.getRouteCode());
//        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
//        if (routeDTO == null || routeDTO.getEnable() != 1) {
//            throw new ArgsErrorException("申报路径不存在或未启用,路径标识：" + submit.getRouteCode());
//        }
        if (StringUtils.isEmpty(submit.getPayWay())) {
            throw new ArgsErrorException("支付方式不能为空");
        }
        if (submit.getPayTransactionAmount() == null || submit.getPayTransactionAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new ArgsErrorException("支付总金额不能小于等于0");
        }
        if (StringUtils.isEmpty(submit.getRecpCode()) || StringUtils.isEmpty(submit.getRecpName())) {
            throw new ArgsErrorException("工商备案信息不能为空");
        }
        if (StringUtils.isEmpty(submit.getRecpAccount())) {
            throw new ArgsErrorException("企业收款账号不能为空");
        }
        if (CollectionUtils.isEmpty(submit.getOrigGoodsInfoList())) {
            throw new ArgsErrorException("商品信息不能为空");
        }

        ExtraPayInfoDO condition = new ExtraPayInfoDO();
        condition.setEbpId(ebpId);
        condition.setDeclareOrderSn(submit.getDeclareOrderNo());
        ExtraPayInfoDO old = extraPayInfoMapper.selectOne(condition);
        if (old != null) {
            return old;
        }
        extraPayInfoDO.setDeclareOrderSn(submit.getDeclareOrderNo());
        extraPayInfoDO.setTradePayNo(submit.getTradePayNo());
        extraPayInfoDO.setTradeTime(new DateTime(submit.getTradeTime()).toDate());
        extraPayInfoDO.setEbpId(ebpId);
        extraPayInfoDO.setPayCompanyId(payChannelDTO.getPayCompanyId());
        extraPayInfoDO.setPayTransactionId(submit.getPayTransactionId());
        extraPayInfoDO.setVerDept(submit.getVerDept());
        extraPayInfoDO.setPayChannel(submit.getPayChannel());
        extraPayInfoDO.setPayWay(submit.getPayWay());
        extraPayInfoDO.setPayTransactionAmount(submit.getPayTransactionAmount());
        extraPayInfoDO.setRecpCode(submit.getRecpCode());
        extraPayInfoDO.setRecpName(submit.getRecpName());
        extraPayInfoDO.setRecpAccount(submit.getRecpAccount());
        extraPayInfoDO.setPayRequestMessage(submit.getPayRequestMessage());
        extraPayInfoDO.setPayResponseMessage(submit.getPayResponseMessage());
        extraPayInfoDO.setItemJson(JSON.toJSONString(submit.getOrigGoodsInfoList()));
        return extraPayInfoDO;
    }

    public void save(ExtraPayInfoDO extraPayInfoDO) {
        if (extraPayInfoDO != null && LongUtil.isNone(extraPayInfoDO.getId())) {
            ExtraPayInfoDO condition = new ExtraPayInfoDO();
            condition.setEbpId(extraPayInfoDO.getEbpId());
            condition.setDeclareOrderSn(extraPayInfoDO.getDeclareOrderSn());
            ExtraPayInfoDO old = extraPayInfoMapper.selectOne(condition);
            if (old == null) {
                UserUtils.setCreateAndUpdateBy(extraPayInfoDO);
                extraPayInfoMapper.insertSelective(extraPayInfoDO);
            } else {
                extraPayInfoDO.setId(old.getId());
            }
        }
    }
}
