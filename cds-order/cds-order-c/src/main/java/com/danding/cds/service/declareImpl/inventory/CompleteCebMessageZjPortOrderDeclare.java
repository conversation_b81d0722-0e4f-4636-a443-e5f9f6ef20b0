package com.danding.cds.service.declareImpl.inventory;

import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/11 13:10
 */
@Service("COMPLETE_CEB_MESSAGE_ZJPORT_ORDER")
public class CompleteCebMessageZjPortOrderDeclare extends CompleteCebMessageZjPortDeclare {
    @Override
    protected void buildTrackLogEsDO(TrackLogEsDTO trackLogEsDO) {
        trackLogEsDO.setEventDesc(TrackLogConstantMixAll.CEB_CUSTOMS_ORDER_DECLARE);
    }
}
