package com.danding.cds.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.config.OrderCBaseConfig;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCancelDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCancelEnum;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.declare.base.component.util.DeclareUtils;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.cancel.CustomsInventoryCancelInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryInfo;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSensitivePlainText;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.service.JdSecurityLogService;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.cds.service.mq.producer.CustomsDeclareMQProducer;
import com.danding.cds.utils.*;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@RefreshScope
public class CustomsInventoryCancelHandler {
    @Autowired
    private CustomsDeclareMQProducer producer;
    @Resource
    private CustomsInventoryService customsInventoryService;
    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;
    @DubboReference
    private CompanyService companyService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Resource
    private OrderService orderService;

    @Value("${pdd.host:}")
    private String PDD_HOST;

    @Autowired
    private CustomsSupport support;

    @Autowired
    private PddDeclareService pddDeclareService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private RouteService routeService;
    @Autowired
    private JdSecurityLogService jdSecurityLogService;
    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;
    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;
    @Autowired
    private OrderCBaseConfig orderCBaseConfig;

    public void handle(CustomsInventoryCancelDTO request) {
        log.info("CustomsInventoryCancelHandler handle={}", JSON.toJSONString(request));
        if (Objects.isNull(request) || Objects.isNull(request.getId())) {
            log.error("请求信息为空或id为空 不处理");
            throw new ArgsErrorException("请求信息为空或id为空");
        }
        if (checkHandle(request)) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(request.getRefInvoId(), request.getRefInvoSn());
            if (Objects.isNull(customsInventoryDTO)) {
                log.error("根据sn" + request.getRefInvoSn() + "未查询到清单数据");
                throw new ArgsErrorException("根据sn" + request.getRefInvoSn() + "未查询到清单数据");
            }
            OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
            if (Objects.isNull(orderDTO)) {
                log.error("根据sn" + request.getRefInvoSn() + "未查询到申报单数据");
                throw new ArgsErrorException("根据sn" + request.getRefInvoSn() + "未查询到清单数据");
            }
            Long ebpId = customsInventoryDTO.getEbpId();
            CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(ebpId);
            //fixme 考虑一下是否还需要再查询一次
            CustomsInventoryCancelDTO customsInventoryCancelDTO = customsInventoryCancelService.findById(request.getId());
            if (Objects.isNull(customsInventoryCancelDTO)) {
                throw new ArgsErrorException("根据id" + request.getId() + "未查询到撤单数据");
            }
            WarpCancelOrderInfo model = this.buildModel(customsInventoryDTO, customsInventoryCancelDTO, orderDTO);
            Integer orderTags = orderDTO.getOrderTags();
            List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
            if (orderTag.contains(DeclareOrderTagEnums.JIEZHOU_ENCRYPT.getCode())) {
                replaceJieZhouEncrypt(model, orderDTO.getDeclareOrderNo());
            }
//            CustomsDeclareOperEnum keyEnum = CustomsDeclareOperEnum.LIST_ORDER_CANCEL_APPLY;
            log.info("CustomsInventoryCancelHandler WarpCancelOrderInfo={}", JSON.toJSONString(model));
            if (model.dynamicDeclareEnable()) {
                log.info("CustomsInventoryCancelHandler dynamicDeclareEnable true");
                String finalDeclareImpl;
                if (orderCBaseConfig.getDeclareRouteRecordNewType()) {
                    finalDeclareImpl = CustomsDeclareUtils.getFinalDeclareImplNew(orderDTO, model.getDeclareCompanyCebCode(), DeclareEnum.INVENTORY_CANCEL);
                } else {
                    finalDeclareImpl = CustomsDeclareUtils.getFinalDeclareImpl(orderDTO, DeclareEnum.INVENTORY_CANCEL);
                }
                log.info("CustomsInventoryCancelHandler dynamicDeclareEnable finalDeclareImpl={}", finalDeclareImpl);
                String dxpId = CustomsDeclareUtils.getCancelRefundDxpId(orderDTO, DeclareEnum.INVENTORY_CANCEL);
                log.info("CustomsInventoryCancelHandler dynamicDeclareEnable dxpId={}", dxpId);
                if (StringUtils.isNotBlank(dxpId)) {
                    model.setDxpId(dxpId);
                }
                DeclareUtils.inventoryCancelDeclare(model, finalDeclareImpl);
            } else if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), customsInventoryDTO.getDeclareOrderNo())) {
                pddDeclareService.cloudCancelDeclare(model);
            } else if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
                byteDanceDeclareService.cloudCancelDeclare(model);
            } else {
                support.clearInventoryCancel(model);
            }

            customsInventoryCancelService.updateStatus(request.getId(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAITING.getValue(), new Date());
            customsInventoryCalloffService.updateCustomsInventoryCalloffStatusByOrderId(
                    request.getRefOrderId(), null, null, InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), null);
            // 给下京东安全追踪日志
            jdSecurityLogService.jdSecurityLogSend(orderDTO.getEbpId(), orderDTO.getSystemGlobalSn(), DeclareEnum.INVENTORY_CANCEL);
        }
    }

    private void replaceJieZhouEncrypt(WarpCancelOrderInfo model, String declareOrderNo) {
        ExternalDeclareOrderDo declareOrderDo = externalDeclareOrderBaseService.getByOrderNo(declareOrderNo);
        log.info("CustomsInventoryCancelHandler replaceJieZhouEncrypt declare={}", JSON.toJSONString(declareOrderDo));
        if (Objects.nonNull(declareOrderDo)) {
            if (Objects.nonNull(declareOrderDo.getBuyerTelephone())) {
                log.info("CustomsInventoryCancelHandler replaceJieZhouEncrypt buyerTelephone替换 前={} 后={}", model.getCustomsInventoryDto().getBuyerTelNumber(), declareOrderDo.getBuyerTelephone());
                model.getCustomsInventoryDto().setBuyerTelNumber(declareOrderDo.getBuyerTelephone());
            }
            if (Objects.nonNull(declareOrderDo.getBuyerName())) {
                log.info("CustomsInventoryCancelHandler replaceJieZhouEncrypt buyerName替换 前={} 后={}", model.getCustomsInventoryDto().getBuyerName(), declareOrderDo.getBuyerName());
                model.getCustomsInventoryDto().setBuyerName(declareOrderDo.getBuyerName());
            }
            if (Objects.nonNull(declareOrderDo.getBuyerIdNumber())) {
                log.info("CustomsInventoryCancelHandler replaceJieZhouEncrypt buyerIdNumber替换 前={} 后={}", model.getCustomsInventoryDto().getBuyerIdNumber(), declareOrderDo.getBuyerName());
                model.getCustomsInventoryDto().setBuyerIdNumber(declareOrderDo.getBuyerIdNumber());
            }
        }
    }

    protected Boolean checkHandle(CustomsInventoryCancelDTO request) {
        String arrayStatus[] = {InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue()
        };
        return ArrayUtils.indexOf(arrayStatus, request.getStatus()) >= 0;
        //return (request.getStatus().equalsIgnoreCase(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue()));
    }

    protected WarpCancelOrderInfo buildModel(CustomsInventoryDTO customsInventoryDTO, CustomsInventoryCancelDTO customsInventoryCancelDTO, OrderDTO orderDTO) {
        List<CustomsInventoryItemDTO> itemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        // 这里做下排序，由于有历史数据，所以子项中所有的sort都有值的时候才排序
        itemDTOList = SortUtil.inventoryItemSortByAsc(itemDTOList);

        // Step::清单信息
        CustomsInventoryInfo customsInventoryInfo = new CustomsInventoryInfo();
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        customsInventoryInfo.setBookNo(inventoryExtra.getCustomsBookNo());
        customsInventoryInfo.setBookId(customsInventoryDTO.getAccountBookId());
        customsInventoryInfo.setId(customsInventoryDTO.getId());
        customsInventoryInfo.setSn(customsInventoryDTO.getSn());
        customsInventoryInfo.setOrderNo(customsInventoryDTO.getDeclareOrderNo());
        customsInventoryInfo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        customsInventoryInfo.setCustoms(customsInventoryDTO.getCustoms());
        customsInventoryInfo.setCreateTime(customsInventoryDTO.getCreateTime());
        customsInventoryInfo.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
        customsInventoryInfo.setBuyerName(customsInventoryDTO.getBuyerName());
        customsInventoryInfo.setBuyerIdType(customsInventoryDTO.getBuyerIdType());
        customsInventoryInfo.setBuyerTelNumber(customsInventoryDTO.getBuyerTelNumber());
        customsInventoryInfo.setPreNo(customsInventoryDTO.getPreNo());
        customsInventoryInfo.setInventoryNo(customsInventoryDTO.getInventoryNo());
        customsInventoryInfo.setClientCustoms(CustomsDistrictEnum.getEnum(customsInventoryDTO.getCustoms()).getCustoms());
        String consigneeStreet = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(inventoryExtra.getConsigneeStreet())
                && !PddEncodeUtil.isPddOrder(customsInventoryInfo.getEbpCode(), customsInventoryDTO.getDeclareOrderNo())) {
            consigneeStreet = inventoryExtra.getConsigneeStreet();
        }
        customsInventoryInfo.setConsigneeAddress(
                inventoryExtra.getConsigneeProvince()
                        + inventoryExtra.getConsigneeCity()
                        + inventoryExtra.getConsigneeDistrict()
                        + consigneeStreet
                        + customsInventoryDTO.getConsigneeAddress());
        customsInventoryInfo.setCustomsField(CustomsDistrictEnum.getEnum(customsInventoryDTO.getCustoms()).getLoctNo());
        customsInventoryInfo.setFreight(customsInventoryDTO.getFeeAmount());
        customsInventoryInfo.setMainGName(itemDTOList.get(0).getItemName());
        CompanyDTO ebp = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbpId());
        CompanyDTO ebc = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbcId());
        CompanyDTO area = baseDataService.getSpecialClientCodeCompanyById(customsInventoryDTO.getAreaCompanyId());
        customsInventoryInfo.setEbpCode(ebp.getCode());
        CustomsInventoryCancelInfo _customsInventoryCancelInfo = new CustomsInventoryCancelInfo();
        _customsInventoryCancelInfo.setId(customsInventoryCancelDTO.getId());
        _customsInventoryCancelInfo.setSn(customsInventoryCancelDTO.getOrderCancelSn());
        _customsInventoryCancelInfo.setStatus(customsInventoryCancelDTO.getStatus());
        //判断是否进行运单申报 需要更换物流企业
        CompanyDTO logisticsCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getLogisticsCompanyId());
        if (JSON.parseArray(orderDTO.getActionJson(), String.class).contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
            logisticsCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsLogisticsDTO.getAgentCompanyId());
        }
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        WarpCancelOrderInfo model = new WarpCancelOrderInfo(
                _customsInventoryCancelInfo,
                customsInventoryInfo,
                this.buildCompany(area, customsInventoryDTO.getCustoms()),
                this.buildCompany(logisticsCompany, customsInventoryDTO.getCustoms()),
                this.buildCompany(ebp, customsInventoryDTO.getCustoms()),
                this.buildCompany(ebc, customsInventoryDTO.getCustoms()),
                this.buildCompany(companyService.findUnifiedCrossInfoByIdV2(customsInventoryDTO.getAgentCompanyId()), customsInventoryDTO.getCustoms()));
        OrderSensitivePlainText orderSensitivePlainText = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getSensitivePlainText).orElse(null);
        model.setPlainText(orderSensitivePlainText);
        String routeCode = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getRouteCode).orElse(null);
        if (StringUtils.isNotBlank(routeCode)) {
            RouteInfoResVo routeDTO = routeService.findRouteByCode(routeCode);
            BuildInfoUtil.setWrapRouteInfo(model, routeDTO);
        }
        model.setMainOrderId(customsInventoryDTO.getOrderId());
        model.setMainOrderSn(customsInventoryDTO.getOrderSn());
        // 获取下账册信息
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
        AccountBookDto accountBookDto = new AccountBookDto();
        if (customsBookResVo != null) {
            BeanUtils.copyProperties(customsBookResVo, accountBookDto);
        }
        model.setAccountBookDto(accountBookDto);
        String dxpId = RouteDxpUtils.getConfigDxpIdByType(model);
        model.setDxpId(dxpId);
        return model;
    }

    private CompanyInfo buildCompany(CompanyDTO companyDTO, String customs) {
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getCode());
        ebpInfo.setCebName(companyDTO.getName());
        ebpInfo.setCode(companyDTO.getCode());
        ebpInfo.setName(companyDTO.getName());
//        CompanyDistrictDTO ebpDis = companyDTO.getDistrict(CustomsDistrictEnum.getEnum(customs));
//        if (ebpDis == null) {
//            ebpInfo.setCode(companyDTO.getCode());
//            ebpInfo.setName(companyDTO.getName());
//        } else {
//            ebpInfo.setCode(ebpDis.getCode());
//            ebpInfo.setName(ebpDis.getName());
//        }
        JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
        String dxpId = agentExtra.getString("dxpId");
        if (!StringUtils.isEmpty(dxpId)) {
            ebpInfo.setDxpId(dxpId);
        }

        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }
        return ebpInfo;
    }

}
