package com.danding.cds.service.mq.producer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.enums.CustomsDeclareOperEnum;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/12 14:37
 * @Description: 海关申报操作生产者
 */
@Component
@Slf4j
public class CustomsDeclareMQProducer {

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private RedisTemplate redisTemplate;


    public Boolean send(String copCode, String customs, CustomsDeclareOperEnum operEnum, String bizId, Object bizModel){
        // Step::数据存储
        String bizData = JSON.toJSONString(bizModel);

        log.info("[op:CustomsDeclareMQProducer-send] bizData={}",bizData);
        redisTemplate.boundHashOps(operEnum.getKey()).put(bizId,bizData);

        // Step::任务发起
        messageSender.sendMsg(bizId,"ccs-"  + operEnum.getKey() + "-declare-topic:" + customs + "-" + copCode);
        return true;
    }

    /**
     *
     * @param copCode
     * @param customs
     * @param operEnum
     * @param bizId
     * @param bizModel
     * @return
     */
    public Boolean send(String copCode, String customs, CustomsDeclareOperEnum operEnum, String bizId,String declareNoBizId, Object bizModel){
        // Step::数据存储
        String bizData = JSON.toJSONString(bizModel);

        log.info("[op:CustomsDeclareMQProducer-send] bizData={}",bizData);
        redisTemplate.boundHashOps(operEnum.getKey()).put(bizId,bizData);
        /**
         * 重庆中间数据,回执中间过度数据
         */
        redisTemplate.boundHashOps(operEnum.getKey()).put(declareNoBizId,bizData);
        // Step::任务发起
        messageSender.sendMsg(bizId,"ccs-"  + operEnum.getKey() + "-declare-topic:" + customs + "-" + copCode);
        return true;
    }
}
