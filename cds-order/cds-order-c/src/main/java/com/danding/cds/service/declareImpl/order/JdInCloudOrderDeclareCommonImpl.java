package com.danding.cds.service.declareImpl.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.service.customs.declare.InCloudDeclareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 京东CEB清单代理申报
 **/
@Service("JD_IN_CLOUD_ORDER_DECLARE_COMMON")
@Slf4j
public class JdInCloudOrderDeclareCommonImpl extends OrderDeclareAbstract {
    /**
     * 测试用
     */
    @Autowired
    private InCloudDeclareService inCloudDeclareService;

    @Override
    protected OrderDeclareResult mockDeclareTest(WrapOrderDeclareInfo info) {
        log.info("京东CEB订单代理申报 测试环境-MOCK info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }

    /**
     * @param info 数据
     * @return
     */
    @Override
    protected OrderDeclareResult declare(WrapOrderDeclareInfo info) {
        log.info("京东CEB订单代理申报 info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }
}
