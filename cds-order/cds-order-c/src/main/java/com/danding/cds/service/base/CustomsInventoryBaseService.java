package com.danding.cds.service.base;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.DateHelper;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.QuarterUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.base.bean.dao.CustomsInventoryDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.bean.dto.InventoryOutAreaDetailDTO;
import com.danding.cds.order.base.bean.dto.InventoryOutAreaDto;
import com.danding.cds.order.base.bean.dto.SelectCountRes;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsInventoryMapper;
import com.danding.cds.order.base.util.InventoryBuilder;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.encrypt.annotation.DataSecurity;
import com.danding.logistics.api.common.page.TimeRangeParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.session.RowBounds;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/8/17  10:09
 * @Describe
 **/
@Service
@Slf4j
@RefreshScope
public class CustomsInventoryBaseService {

    private static final String TABLE_NAME = "ccs_customs_inventory";

    @Autowired
    private CustomsInventoryMapper customsInventoryMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private CustomsInventoryItemBaseService customsInventoryItemBaseService;

    /**
     * 需要回传的账册标签
     */
    @Value("${platCallback.needCallbackBookTag:}")
    private List<Integer> needCallbackBookTag;

    /**
     * 需要回传的跨境进口统一编码
     */
    @Value("${platCallback.needCallbackCode:}")
    private List<String> needCallbackCode;


    /**
     * 更新
     *
     * @param snList       清单SN数据编码
     * @param reviewStatus 核注状态
     * @param quarter      季度，如 2022q1;
     * @return
     */
    public Integer updteEndorsementsStatusBySn(List<String> snList, Integer reviewStatus, String quarter) {
        Integer userId = UserUtils.getUserId();
        return customsInventoryMapper.updteEndorsementsStatusBySn(snList, reviewStatus, new Date(), userId, TABLE_NAME + "_" + quarter);
    }

    /**
     * 获取清单出区结果
     *
     * @param exitRegionStatus 出区状态
     * @param customsStatus    海关状态
     * @param createStart      开始时间
     * @param createEnd        结束时间
     * @param tableName        表名
     * @return
     */
    public List<InventoryOutAreaDto> findInventoryOutAreaResult(Integer exitRegionStatus, Integer customsStatus,
                                                                Date createStart, Date createEnd, String tableName) {

        return customsInventoryMapper.findInventoryOutAreaResult(exitRegionStatus, customsStatus, createStart, createEnd, tableName);
    }

    public List<InventoryOutAreaDetailDTO> findInventoryOutAreaDetailResult(
            Integer exitRegionStatus, Integer customsStatus, Date createStart, Date createEnd, String tableName, Long areaCompanyId) {
        List<InventoryOutAreaDetailDTO> result = customsInventoryMapper.findInventoryOutAreaDetailResult(exitRegionStatus, customsStatus, createStart, createEnd, tableName, areaCompanyId);
        result.forEach(r -> {
            if (Objects.nonNull(r.getExpressId())) {
                ExpressDTO expressDTO = baseDataService.getExpressDTOById(r.getExpressId());
                r.setExpressName(expressDTO.getName());
            }
        });
        return result;
    }

    /**
     * 二线出区统计（通过交接单 取运单 查清单[待出区 + 区内企业]）
     *
     * @param exitRegionStatus
     * @param createStart
     * @param createEnd
     * @param tableName
     * @return
     */
    public List<InventoryOutAreaDto> findInventoryOutAreaResultV2(Integer exitRegionStatus, Date createStart, Date createEnd, String tableName, List<Long> areaCompanyIdList) {
        return customsInventoryMapper.findInventoryOutAreaResultV2(exitRegionStatus, createStart, createEnd, tableName, areaCompanyIdList);
    }

    /**
     * 二线出区统计 导出详情（通过交接单 取运单 查清单[待出区 + 区内企业]）
     *
     * @param exitRegionStatus 出区状态
     * @param createStart      时间
     * @param createEnd        时间
     * @param tableName        表名
     * @param areaCompanyId    区内企业id
     * @return
     */
    public List<InventoryOutAreaDetailDTO> findInventoryOutAreaDetailResultV2(Integer exitRegionStatus, Date createStart, Date createEnd, String tableName, Long areaCompanyId) {
        List<InventoryOutAreaDetailDTO> resultV2 = customsInventoryMapper.findInventoryOutAreaDetailResultV2(exitRegionStatus, createStart, createEnd, tableName, areaCompanyId);
        resultV2.forEach(r -> {
            if (Objects.nonNull(r.getExpressId())) {
                ExpressDTO expressDTO = baseDataService.getExpressDTOById(r.getExpressId());
                r.setExpressName(expressDTO.getName());
            }
        });
        return resultV2;
    }

    /**
     * 获取数据
     *
     * @param id 主键
     * @return
     */
    @DataSecurity
    public CustomsInventoryDO selectByPrimaryKey(Long id) {
        return customsInventoryMapper.selectByPrimaryKey(id);
    }

    /**
     * 获取实体数据列表
     *
     * @param inventoryDO 查询条件
     * @return
     */
    @DataSecurity
    public List<CustomsInventoryDO> select(CustomsInventoryDO inventoryDO) {
        return customsInventoryMapper.select(inventoryDO);
    }

    /**
     * 获取一个实体数据
     *
     * @param inventoryDO 查询条件
     * @return
     */
    @DataSecurity
    public CustomsInventoryDO selectOne(CustomsInventoryDO inventoryDO) {
        return customsInventoryMapper.selectOne(inventoryDO);
    }

    /**
     * 通过sn列表获取数据
     *
     * @param snList  sn列表
     * @param endOfQuarter 季度，如2022q1
     * @return
     */
    @DataSecurity
    public List<CustomsInventoryDO> selectBySnList(List<String> snList, Date beginOfQuarter, Date endOfQuarter) {

        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(beginOfQuarter);
        timeRangeParam.setEndDate(endOfQuarter);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sn", snList);
        example.and(criteria);
        return customsInventoryMapper.selectByExample(example);
    }

    /**
     * 获取数据
     *
     * @param example 查询条件
     * @return
     */
    @DataSecurity
    public CustomsInventoryDO selectOneByExample(Example example) {
        CustomsInventoryDO customsInventoryDO = customsInventoryMapper.selectOneByExample(example);
        return customsInventoryDO;
    }

    /**
     * 获取数据
     *
     * @param example 查询条件
     * @return
     */
    @DataSecurity
    public List<CustomsInventoryDO> selectByExample(Example example) {
        return customsInventoryMapper.selectByExample(example);
    }

    /**
     * 获取实体
     *
     * @param example
     * @param rowBounds
     * @return
     */
    @DataSecurity
    public List<CustomsInventoryDO> selectByExampleAndRowBounds(Example example, RowBounds rowBounds) {
        return customsInventoryMapper.selectByExampleAndRowBounds(example, rowBounds);
    }

    /**
     * 分页查询
     *
     * @param offset    偏移
     * @param pageSize  页码
     * @param tableName 表名
     * @return
     */
    @DataSecurity
    public List<CustomsInventoryDO> selectPage(int offset, Integer pageSize, String tableName) {
        return customsInventoryMapper.selectPage(offset, pageSize, tableName);
    }

    public int findCount(String tableName) {
        return customsInventoryMapper.findCount(tableName);
    }

    public int updateByPrimaryKeySelective(CustomsInventoryDO inventoryDO) {
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(inventoryDO);
        }
        inventoryDO.setUpdateTime(new Date());
        return customsInventoryMapper.updateByPrimaryKeySelective(inventoryDO);
    }

    public int selectCountByExample(Example example) {
        return customsInventoryMapper.selectCountByExample(example);
    }

    public int updateByExampleSelective(CustomsInventoryDO inventoryDO, Example example) {
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(inventoryDO);
        }
        inventoryDO.setUpdateTime(new Date());
        return customsInventoryMapper.updateByExampleSelective(inventoryDO, example);
    }

    public int updateStatusResetDeclareTime(String sn, String tableName, Integer status) {
        return customsInventoryMapper.updateStatusResetDeclareTime(sn, status, tableName);
    }

    public void syncEsByUpdateTimeAcc(List<String> snList) {
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        int step = 500;
        quarterMap.forEach((quarter, subSnList) -> {
            if (subSnList.size() <= step) {
                customsInventoryMapper.syncEsByUpdateTimeAcc(TABLE_NAME + "_" + quarter, subSnList);
                return;
            }
            for (int i = 0; i < subSnList.size(); i += step) {
                int next = Math.min((i + step), subSnList.size());
                List<String> stepSnList = subSnList.subList(i, next);
                customsInventoryMapper.syncEsByUpdateTimeAcc(TABLE_NAME + "_" + quarter, stepSnList);
            }
        });
    }


    /**
     * 通过运单编号查找清单
     *
     * @param logisticsNo 运单编号
     * @return
     */
    public CustomsInventoryDTO findByLogisticsNo(String logisticsNo) {
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andEqualTo("logisticsNo", logisticsNo));
        CustomsInventoryDO customsInventoryDO = customsInventoryMapper.selectOneByExample(example);
        if (customsInventoryDO == null) {
            return null;
        } else {
            return InventoryBuilder.buildDTO(customsInventoryDO);
        }

    }

    /**
     * 通过主订单ID查找清单
     *
     * @param orderId 主订单ID
     * @return
     */
    public CustomsInventoryDTO findByOrderId(Long orderId) {
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andEqualTo("orderId", orderId));
        CustomsInventoryDO customsInventoryDO = this.selectOneByExample(example);
        if (customsInventoryDO == null) {
            return null;
        } else {
            return InventoryBuilder.buildDTO(customsInventoryDO);
        }
    }

    public void updateCustomsInventoryHandoverStatusCore(List<String> snList, Integer handoverStatus, String quarter) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        customsInventoryMapper.updateHandoverStatus(snList, handoverStatus, TABLE_NAME + "_" + quarter);
    }

    /**
     * 根据清单sn更新 是否关联交接单 状态
     *
     * @param snList
     * @param handoverStatus
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomsInventoryHandoverStatus(List<String> snList, Integer handoverStatus) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        // 同一季度放在一起
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        quarterMap.forEach((k, v) -> {
            // 按量切割
            try {
                // 限制下批量数据
                int index = 500;
                int totalSize = v.size();
                log.info("CustomsInventoryBaseService 更新清单是否关联交接单状态，限制批量处理数据步长：{}，总数量：{}", index, totalSize);
                if (index >= totalSize) {
                    this.updateCustomsInventoryHandoverStatusCore(v, handoverStatus, k);
                } else {
                    int times = totalSize % index > 0 ? totalSize / index + 1 : totalSize / index;
                    for (int i = 1; i <= times; i++) {
                        int fromIndex = (i - 1) * index;
                        int toIndex = index * i;
                        if (i == times) {
                            toIndex = totalSize;
                        }
                        List<String> subSnList = v.subList(fromIndex, toIndex);
                        this.updateCustomsInventoryHandoverStatusCore(subSnList, handoverStatus, k);
                    }
                }
            } catch (Exception e) {
                log.error("CustomsInventoryBaseService 更新清单是否关联交接单状态 error={}", e.getMessage(), e);
            }
        });
    }

    public void updateHandoverStatus(CustomsInventoryDTO customsInventoryDTO) {
        String tableName = QuarterUtil.getInventroyTableBySn(customsInventoryDTO.getSn());
        customsInventoryMapper.cleanLastDeclareTime(customsInventoryDTO.getSn(), tableName);
    }

    public Boolean judgeExist30DaysInByProductId(List<Integer> orderStatusList, String productId, Integer inveStatus, Date createTimeFrom) {
        Date now = new Date();
        String quarterNow = DateHelper.getQuarter(now);
        String quarterFrom = DateHelper.getQuarter(createTimeFrom);
        int count = 0;
        SelectCountRes nowCountRes = customsInventoryMapper.judgeExist30DaysInByProductId(orderStatusList, productId, inveStatus, createTimeFrom, now, quarterNow);
        count += nowCountRes.getCount();
        if (!Objects.equals(quarterFrom, quarterNow)) {
            SelectCountRes fromCountRes = customsInventoryMapper.judgeExist30DaysInByProductId(orderStatusList, productId, inveStatus, createTimeFrom, now, quarterFrom);
            count += fromCountRes.getCount();
        }
        return count > 0;
    }

    /**
     *
     * @param snList sn列表
     * @param checkoutStatus 平台回传状态
     * @param quarter 季度
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateCheckoutStatusBySn(List<String> snList, Integer checkoutStatus, String quarter) {
        customsInventoryMapper.updateCheckoutStatusBySn(snList, checkoutStatus, new Date(), TABLE_NAME + "_" + quarter);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateCustomsStatusBySn(List<String> snList, Integer customsStatus, String customsDetail, String quarter) {
        customsInventoryMapper.updateCustomsStatusBySn(snList, customsStatus, customsDetail, new Date(), UserUtils.getUserId(), TABLE_NAME + "_" + quarter);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAfterStatus(CompanyDTO ebp, CustomsBookResVo book, CustomsInventoryDTO inventoryDTO, Integer afterStatus, Integer checkoutStatus) {
        if (ebp != null && book != null && inventoryDTO != null) {
            CustomsInventoryDO template = new CustomsInventoryDO();
            template.setAfterSalesStatus(afterStatus);
            template.setUpdateTime(new Date());
            // 关联账册是否有需要回传标签
            if (CollUtil.contains(needCallbackCode, ebp.getUnifiedCrossBroderCode())) {
                List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(book.getBookTag());
                if (CollUtil.containsAny(bookTagList, needCallbackBookTag)) {
                    template.setCheckOutStatus(checkoutStatus);
                }
            }
            this.updateByIdSection(inventoryDTO.getId(), template, inventoryDTO.getCreateTime());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateAfterStatus(CustomsInventoryDTO inventoryDTO, Integer afterStatus) {
        if (inventoryDTO != null) {
            CustomsInventoryDO template = new CustomsInventoryDO();
            template.setAfterSalesStatus(afterStatus);
            template.setUpdateTime(new Date());
            this.updateByIdSection(inventoryDTO.getId(), template, inventoryDTO.getCreateTime());
        }
    }


    private void updateByIdSection(Long id, CustomsInventoryDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        updateByExampleSelective(template, example);
    }

    public CustomsInventoryDTO findBySn(String customsInventorySn) {
        if (StrUtil.isNotBlank(customsInventorySn)) {
            String quarter = QuarterUtil.getQuarterBySn(customsInventorySn);
            CustomsInventoryDO customsInventoryDO = customsInventoryMapper.selectBySn(customsInventorySn, TABLE_NAME + "_" + quarter);
            if (customsInventoryDO!= null) {
                CustomsInventoryDTO customsInventoryDTO = new CustomsInventoryDTO();
                BeanUtil.copyProperties(customsInventoryDO, customsInventoryDTO);
                return customsInventoryDTO;
            }
        }
        return null;
    }

    public Map<CustomsInventoryDTO, List<CustomsInventoryItemDTO>> findFullInfoBySnList(List<String> snList) {
        Map<CustomsInventoryDTO, List<CustomsInventoryItemDTO>> resultMap = new HashMap<>();
        // 同一季度放在一起
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<CustomsInventoryDO> inventoryDOList = selectBySnList(v, beginOfQuarter, endOfQuarter);
            //取决哪种转dto的方式
//            List<CustomsInventoryDTO> inventoryDTOS = InventoryBuilder.buildDTO(inventoryDOList);
            List<CustomsInventoryDTO> customsInventoryDTOS = ConvertUtil.listConvert(inventoryDOList, CustomsInventoryDTO.class);
            List<Long> idList = inventoryDOList.stream().map(CustomsInventoryDO::getId).distinct().collect(Collectors.toList());
            List<CustomsInventoryItemDTO> inventoryItemDTOS = customsInventoryItemBaseService.findByInventoryId(idList, beginOfQuarter, endOfQuarter);
            Map<Long, List<CustomsInventoryItemDTO>> idItemMap = inventoryItemDTOS.stream().collect(Collectors.groupingBy(CustomsInventoryItemDTO::getCustomsInventoryId));
            customsInventoryDTOS.forEach(customsInventoryDTO -> {
                List<CustomsInventoryItemDTO> itemDTOList = idItemMap.get(customsInventoryDTO.getId());
                resultMap.put(customsInventoryDTO, itemDTOList);
            });
        });
        return resultMap;
    }
}
