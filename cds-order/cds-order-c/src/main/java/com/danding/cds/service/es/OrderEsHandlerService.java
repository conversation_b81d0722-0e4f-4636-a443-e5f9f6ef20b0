package com.danding.cds.service.es;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.order.base.bean.dao.CustomsInventoryDO;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.mapper.CustomsInventoryMapper;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description:
 * @author: 潘本乐（Belep）
 * @create: 2021-06-11 13:25
 **/
@Service
@Slf4j
public class OrderEsHandlerService {

    @Autowired
    private OrderEsDao orderEsDao;
    @Autowired
    private CustomsInventoryMapper customsInventoryMapper;

    private static ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque(), new ThreadFactoryBuilder().setNameFormat("esAccBookDeal%d").build());


    /**
     * 订单Es,账册ID添加
     */
    public void orderEsHandler() {

        AtomicLong atomicLong = new AtomicLong(1);
        AtomicLong noAccBookAtomicLong = new AtomicLong(1);

        log.info("Es索引账册字段，历史数据处理任务，放入线程池开始。");
        long scrollTimeInMillis = 10 * 1000;
        Integer pageSize = 500;

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        BoolQueryBuilder accBookBuilder = new BoolQueryBuilder();
        // 字段不存在
        accBookBuilder.should(new BoolQueryBuilder().mustNot(QueryBuilders.existsQuery("accountBookId")));
        // 等于-1L，是某次维护时账册不存在
        accBookBuilder.should(QueryBuilders.termQuery("accountBookId", -1L));
        boolQueryBuilder.filter(accBookBuilder);
        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, pageSize))
                .build();
        ScrolledPage<OrderEsDO> scrolledPage = orderEsDao.startScroll(scrollTimeInMillis, nativeSearchQuery);

        long total = scrolledPage.getTotalElements();
        log.info("Es索引账册字段，历史数据处理任务，数据总条数：{}", total);

        while (scrolledPage.hasContent()) {
            final List<OrderEsDO> content = scrolledPage.getContent();
            log.info("es滚动数据：{}", content.size());
            // 执行处理
            taskExecutor.execute(new EsAccBookHandle(orderEsDao, customsInventoryMapper, content, atomicLong, total, noAccBookAtomicLong));
            // 滚动获取
            scrolledPage = orderEsDao.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis);
        }
        log.info("Es索引账册字段，历史数据处理任务，放入线程池成功。");
    }
}

@Slf4j
class EsAccBookHandle extends TraceDataRunnable {

    private OrderEsDao orderEsDao;
    private CustomsInventoryMapper customsInventoryMapper;
    private List<OrderEsDO> orderEsDOList;
    private AtomicLong atomicLong;
    private AtomicLong noAccBookAtomicLong;
    private Long total;

    public EsAccBookHandle(OrderEsDao orderEsDao, CustomsInventoryMapper customsInventoryMapper,
                           List<OrderEsDO> orderEsDOList, AtomicLong atomicLong, Long total, AtomicLong noAccBookAtomicLong) {
        super();
        this.orderEsDao = orderEsDao;
        this.customsInventoryMapper = customsInventoryMapper;
        this.orderEsDOList = orderEsDOList;
        this.atomicLong = atomicLong;
        this.total = total;
        this.noAccBookAtomicLong = noAccBookAtomicLong;
    }

    @Override
    public void proxy() {
        if (CollectionUtils.isEmpty(orderEsDOList)) {
            log.info("orderEsDOList，任务为空");
            return;
        }
        List<Long> orderIds = orderEsDOList.stream()
                .filter(z -> z.getAccountBookId() == null || Objects.equals(z.getAccountBookId(), -1L))
                .map(z -> Long.valueOf(z.getId()))
                .distinct()
                .collect(Collectors.toList());

        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andIn("orderId", orderIds));
        final List<CustomsInventoryDO> customsInventoryDOS = customsInventoryMapper.selectByExample(example);
        Map<Long, CustomsInventoryDO> customsInventoryDOMap = customsInventoryDOS.stream()
                .collect(Collectors.toMap(CustomsInventoryDO::getOrderId, Function.identity(), (v1, v2) -> v1));
        log.info("查询的清单表数据列表为：{}", customsInventoryDOMap.keySet());
        // 更新ES索引
        for (OrderEsDO orderEsDO : orderEsDOList) {
            Long orderId = Long.valueOf(orderEsDO.getId());
            CustomsInventoryDO customsInventoryDO = customsInventoryDOMap.get(orderId);
            Long accountId = Optional.ofNullable(customsInventoryDO).map(CustomsInventoryDO::getAccountBookId).orElse(null);
            // 账册ID不为空才更新ES
            if (accountId != null) {
                orderEsDO.setAccountBookId(accountId);
                log.info("订单 {}，Es账册ID字段更新成功！成功条数：{}/{}", orderId, atomicLong.getAndIncrement(), total);
            } else {
                orderEsDO.setAccountBookId(-1L);
                log.info("订单 {}，清单中没有取到，账册ID为-1L，数量：{}", orderId, noAccBookAtomicLong.getAndIncrement());
            }
        }
        orderEsDao.bulkIndex(orderEsDOList);
    }
}
