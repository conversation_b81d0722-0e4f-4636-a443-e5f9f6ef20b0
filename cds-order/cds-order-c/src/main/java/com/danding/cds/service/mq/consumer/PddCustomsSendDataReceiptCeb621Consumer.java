package com.danding.cds.service.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.service.customs.declare.PddCustomsSendDataReceiptService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/7 13:31
 */
@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(topic = "ccs-pdd-customs-send-data-receipt-topic",
        consumerGroup = "ccs-pdd-customs-send-data-receipt-CEB621-consumer", selectorExpression = "CEB621")
public class PddCustomsSendDataReceiptCeb621Consumer extends MessageHandler {
    @Autowired
    private PddCustomsSendDataReceiptService pddCustomsSendDataReceiptService;

    @Override
    public void handle(Object o) throws RuntimeException {
        if (Objects.isNull(o)) {
            return;
        }
        try {
            String beanInfo = o.toString();
            WrapInventoryOrderInfo wrapInventoryOrderInfo = JSON.parseObject(beanInfo, WrapInventoryOrderInfo.class);
            pddCustomsSendDataReceiptService.sendCeb621RequestXml(wrapInventoryOrderInfo);
        } catch (Exception e) {
            log.error("PddCustomsSendDataReceiptCeb621Consumer handle error", e);
        }
    }
}
