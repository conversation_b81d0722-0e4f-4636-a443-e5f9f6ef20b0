package com.danding.cds.v2.consumer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryCalloffCRpc;
import com.danding.cds.v2.bean.dto.JdCustomsOrderMsgDataDTO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

/**
 * 京东退货取消
 *
 * <AUTHOR>
 **/
@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-jd-refund-cancel-consumer",
        topic = "ccs-jd-refund-cancel-topic"
)
public class JdRefundCancelConsumer extends MessageHandler {


    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;

    @Override
    public void handle(Object param) throws RuntimeException {
        log.info("JdRefundCancelConsumer param={}", param);
        JdCustomsOrderMsgDataDTO jdCustomsOrderMsgDataDTO = JSON.parseObject((String) param, JdCustomsOrderMsgDataDTO.class);
        try {
            customsInventoryCalloffCRpc.jdRefundCancel(jdCustomsOrderMsgDataDTO);
        } catch (ArgsInvalidException e) {
            log.error("JdRefundCancelConsumer param={}, error={}", param, e.getErrorMessage());
        } catch (Exception e) {
            log.error("JdRefundCancelConsumer param={}, error={}", param, e.getMessage(), e);
        }
    }

}