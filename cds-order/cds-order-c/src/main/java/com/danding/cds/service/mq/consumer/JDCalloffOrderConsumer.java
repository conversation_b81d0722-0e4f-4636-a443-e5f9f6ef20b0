package com.danding.cds.service.mq.consumer;


import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsInventoryCalloffService;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.inventory.api.dto.CustomsCancelJdOrderVO;
import com.danding.cds.customs.inventory.api.dto.CustomsCancelOrderDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsCancelOrderDetailsDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffSubmit;
import com.danding.cds.customs.inventory.api.enums.CustomsCancelOrderStatus;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.service.job.JdOrderConfirmJob;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description 京东取消单消费者
 * @date 2022/1/7
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "ccs-jd-calloff-c-orderTopic",
        consumerGroup = "ccs-jd-calloff-c-orderConsumer")
@RefreshScope
public class JDCalloffOrderConsumer extends MessageHandler {


    @Autowired
    private RedisTemplate redisTemplate;

    private final static String JD_CANCEL_ORDER_KEY_DETATEILS = "CCS:JdCancelOrder:JD:details";
    private final static String JD_CANCEL_ORDER_KEY = "CCS:JdCancelOrder";

    private final static String JD_CANCEL_ORDER_TASK_LOCK_PREFIX = "CCS:JdCancelOrder:task:lOCK_";

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Resource
    private OrderService orderService;

    @Autowired
    private JdOrderConfirmJob jdOrderConfirmJob;

    @Value("${stop.get.jd.cancel.order:true}")
    private Boolean stopJdCancelOrder;

    @Override
    public void handle(Object o) throws RuntimeException {

        log.warn("[op:JDCalloffOrderConsumer] MQ监听事件启动-----------");
        String msg = o.toString();
        log.warn("[op:JDCalloffOrderConsumer] 反序列化入参 - {}", msg);
        CustomsInventoryCalloffSubmit submit = JSONUtil.toBean(msg, CustomsInventoryCalloffSubmit.class);
        log.warn("[op:JDCalloffOrderConsumer：dataSource] 数据源 - {}", submit);

        String taskId = submit.getTaskId();
        try {
            if (stopJdCancelOrder) {
                log.info("京东取消单任务，暂停处理。taskId={}", taskId);
                return;
            }
            // 判断是否有锁
            if (taskLocked(taskId)) {
                log.info("京东取消单任务正在处理，当前无需重复执行。taskId={}", taskId);
                return;
            }
            //京东取消单明细redis写入
            CustomsCancelOrderDetailsDTO detailsDTO = jdCancelOrderDetailSave(submit);
            //更新取消单
            Map<String, Object> entriesMap = redisTemplate.opsForHash().entries(JD_CANCEL_ORDER_KEY);
            JSONObject jsonObject = JSONObject.parseObject((String) entriesMap.get(submit.getTaskId()));
            if (Objects.nonNull(jsonObject)) {
                CustomsCancelOrderDTO cancelOrderDTO = JSONObject.parseObject(jsonObject.toJSONString(), CustomsCancelOrderDTO.class);
                cancelOrderDTO.setStatusStr(CustomsCancelOrderStatus.PROCESSING_COMPLETE.getDesc());
                cancelOrderDTO.setStatus(CustomsCancelOrderStatus.PROCESSING_COMPLETE.getCode());
                cancelOrderDTO.setEndCreateTime(DateUtil.getDateForLong(DateUtil.DATE_DEF_PATTERN, submit.getEndCalloffTime()));
                if (Objects.equals(detailsDTO.getQueryType(), 1)) {
                    cancelOrderDTO.setRemark(detailsDTO.getSubtasksId());
                } else {
                    cancelOrderDTO.setRemark(detailsDTO.getCount().toString());
                }
                cancelOrderDTO.setQueryType(detailsDTO.getQueryType());
                //删除掉这条数据
                entriesMap.remove(submit.getTaskId());
                log.warn("[op:JDCalloffOrderConsumer：redisSave] 替换数据 - {}", cancelOrderDTO);
                //重新在新增一条
                Map<String, Object> map = new HashMap<>();
                map.put(submit.getTaskId(), JSONObject.toJSONString(cancelOrderDTO));
                entriesMap.putAll(map);
                redisTemplate.opsForHash().putAll(JD_CANCEL_ORDER_KEY, entriesMap);
                redisTemplate.expire(JD_CANCEL_ORDER_KEY, 30, TimeUnit.DAYS);
            }
            log.info("[op:JDCalloffOrderConsumer] 京东取消单消息处理完成");
        } catch (Exception ex) {
            log.error("[op:JDCalloffOrderConsumer] 京东取消单消息处理失败，需要关注。异常信息；{}", ex.getMessage(), ex);
        } finally {
            removeTaskLock(taskId);
            log.info("京东取消单任务锁移除成功，taskId={}", taskId);
        }
    }

    private void removeTaskLock(String taskId) {
        String lockKey = JD_CANCEL_ORDER_TASK_LOCK_PREFIX + taskId;
        redisTemplate.delete(lockKey);
    }

    private Boolean taskLocked(String taskId) {

        boolean locked = true;
        String lockKey = JD_CANCEL_ORDER_TASK_LOCK_PREFIX + taskId;
        Object value = redisTemplate.opsForValue().get(lockKey);
        if (value == null) {
            locked = false;
            // 没有锁，加把锁
            redisTemplate.opsForValue().set(lockKey, 1);
        }
        return locked;
    }


    public CustomsCancelOrderDetailsDTO jdCancelOrderDetailSave(CustomsInventoryCalloffSubmit submit) {
        log.warn("[op:RocketMQMessageListener：DetailSave] 明细写入-----------");
        List<String> detailsDTOS = new ArrayList<>();
        CustomsCancelOrderDetailsDTO cancelOrderDetailsDTO = new CustomsCancelOrderDetailsDTO();

        //申报单号-时间查询 如果同时存在就以申报单号为主查询条件
        if (Objects.nonNull(submit.getDeclareOrderNo())) {
            cancelOrderDetailsDTO.setQueryType(1);
        } else {
            cancelOrderDetailsDTO.setQueryType(2);
        }
        CustomsCancelJdOrderVO jdOrderVO = jdOrderConfirmJob.findJdOrder(submit.getBeginCalloffTime(), submit.getEndCalloffTime(), submit.getDeclareOrderNo(), submit.getBookId());
        for (CustomsCancelOrderDetailsDTO detailsDTO : jdOrderVO.getDetailsDTOS()) {
            if (detailsDTO == null) {
                return null;
            }
            String detail = detailsDTO.getDeclareOrderNo() + "_" + detailsDTO.getStatusStr();
            detailsDTOS.add(detail);
        }
        cancelOrderDetailsDTO.setCount(jdOrderVO.getCount());
        log.warn("[op:JdOrderConfirmJob] 取消单新增结果 - {} ", cancelOrderDetailsDTO);
        //如果集合没有数据就不用写入redis,订单查询时才存入明细
        if (!CollectionUtils.isEmpty(detailsDTOS) && Objects.equals(cancelOrderDetailsDTO.getQueryType(), 1)) {
            String subTasksId = submit.getTaskId() + "_Detail";
            cancelOrderDetailsDTO.setSubtasksId(subTasksId);
            Map<String, Object> map = new HashMap<>();
            map.put(subTasksId, detailsDTOS);
            redisTemplate.opsForHash().putAll(JD_CANCEL_ORDER_KEY_DETATEILS, map);
            redisTemplate.expire(JD_CANCEL_ORDER_KEY_DETATEILS, 30, TimeUnit.DAYS);
        } else {
            String subTasksId = submit.getTaskId() + "_Detail";
            cancelOrderDetailsDTO.setSubtasksId(subTasksId);
            Map<String, Object> map = new HashMap<>();
            map.put(subTasksId, detailsDTOS);
            redisTemplate.opsForHash().putAll(JD_CANCEL_ORDER_KEY_DETATEILS, map);
            redisTemplate.expire(JD_CANCEL_ORDER_KEY_DETATEILS, 1, TimeUnit.DAYS);
        }
        return cancelOrderDetailsDTO;
    }

}
