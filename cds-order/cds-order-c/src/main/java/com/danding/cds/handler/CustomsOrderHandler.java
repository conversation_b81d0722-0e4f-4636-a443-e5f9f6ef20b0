package com.danding.cds.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.common.utils.FiltrerSpecialAndEmojUtil;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.config.OrderCBaseConfig;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderItem;
import com.danding.cds.declare.base.component.util.DeclareUtils;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.order.DeclareOrderItem;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.service.JdSecurityLogService;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.cds.service.mq.producer.ByteDanceDeclareProducer;
import com.danding.cds.service.mq.producer.PddDeclareProducer;
import com.danding.cds.utils.BuildInfoUtil;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.PddEncodeUtil;
import com.danding.cds.utils.RouteDxpUtils;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@RefreshScope
public class CustomsOrderHandler extends CustomsDeclareOrderHandler<CustomsLogisticsHandler> {
    /**
     * 要注入要Autowire
     *
     * @param next
     */
    @DubboReference
    private RouteService routeService;

    @Resource
    private CustomsOrderService customsOrderService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CompanyService companyService;

    @Resource
    private OrderService orderService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @DubboReference
    private PayChannelService payChannelService;

    @DubboReference
    private CustomsBookService customsBookService;
    @Autowired
    private JdSecurityLogService jdSecurityLogService;

    @Value("${pdd.host:}")
    private String PDD_HOST;


    @Value("${filter.special.char.config:}")
    private String filterSpecialCharConfig;

    @Autowired
    public CustomsOrderHandler(CustomsLogisticsHandler next) {
        super(next);
    }


    @Autowired
    private CustomsSupport customsSupport;

    @Autowired
    private PddDeclareProducer pddDeclareProducer;

    @Autowired
    private ByteDanceDeclareProducer byteDanceDeclareProducer;

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Autowired
    private OrderCBaseConfig orderCBaseConfig;

    @Override
    protected DeclareEnum declareEnum() {
        return DeclareEnum.CUSTOMS_ORDER;
    }

    @Override
    protected Boolean checkHandle(OrderDTO request) {
        String actionJson = request.getActionJson();
        if (!actionJson.contains(RouteActionEnum.DECLARE_ORDER.getCode())) {
            log.info("[op:CustomsInventoryHandler-check] declareNo={} actionJson 不包含 DECLARE_ORDER", request.getDeclareOrderNo());
            return false;
        }
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(request.getId(), request.getCustomsOrderSn());
        if (customsOrderDTO == null) {
            return false;
        }
        return CustomsActionStatus.DEC_WAIT.getValue().equals(customsOrderDTO.getStatus())
                || (CustomsActionStatus.DEC_ING.getValue().equals(customsOrderDTO.getStatus()) && !request.getExceptionFlag());
    }

    @Autowired
    private PddDeclareService pddDeclareService;

    @Autowired
    private BaseDataService baseDataService;

    @Override
    protected void handle(OrderDTO orderDTO) {
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        if (customsOrderDTO.getLastDeclareTime() != null && (DateTime.now().minusMinutes(10)).isBefore(new DateTime(customsOrderDTO.getLastDeclareTime()))) {
            log.info("[op:CustomsOrderHandler] time jump, declareOrderNo={}", orderDTO.getDeclareOrderNo());
            return;
        }
        if (CustomsActionStatus.DEC_ING.getValue().equals(customsOrderDTO.getStatus())) {
            return;
        }
        log.info("[op:CustomsOrderHandler] start, declareOrderNo={}", orderDTO.getDeclareOrderNo());
        try {
            // Step::各类配置有效性校验
            String configError = "";
            CompanyDTO payCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsOrderDTO.getPayCompanyId());
            if (Objects.isNull(payCompany)) {
                configError += "支付企业不存在";
            }
            if (payCompany.getEnable() != 1) {
                configError += "支付企业未启用;";
            }
            PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOById(customsOrderDTO.getPayChannelId());
            if (Objects.isNull(payCompany)) {
                configError += "支付渠道不存在";
            }
            if (payChannelDTO.getEnable() != 1) {
                configError += "支付渠道未启用;";
            }
            CompanyDTO logisCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsOrderDTO.getLogisticsCompanyId());
            if (Objects.isNull(logisCompany)) {
                configError += "物流企业不存在;";
            }
            if (logisCompany.getEnable() != 1) {
                configError += "物流企业未启用;";
            }
            CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsOrderDTO.getEbpId());
            if (Objects.isNull(ebpCompany)) {
                configError += "电商平台不存在;";
            }
            if (ebpCompany.getEnable() != 1) {
                configError += "电商平台未启用;";
            }
            CompanyDTO ebcCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsOrderDTO.getEbcId());
            if (Objects.isNull(ebcCompany)) {
                configError += "电商企业不存在;";
            }
            if (ebcCompany.getEnable() != 1) {
                configError += "电商企业未启用;";
            }
//        CompanyDTO agentCompany = companyService.findById(customsOrderDTO.getAgentCompanyId());
            CompanyResVo agentCompany = companyService.findUnifiedCrossInfoByIdV2(customsOrderDTO.getAgentCompanyId());
//            JSONObject agentExtra = JSON.parseObject(agentCompany.getExtraJson());

//            String dxpId = agentExtra.getString("orderDxpId");
            if (agentCompany.getEnable() != 1) {
                configError += "申报企业未启用;";
            }
            if (!StringUtils.isEmpty(configError)) {
                // Step::添加配置异常
                CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("CONFIG_ERROR");
                if (Objects.nonNull(mappingDTO)) {
                    orderService.addExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO.getId(), configError, orderDTO.getCreateTime());
                }
                return;
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            WrapOrderDeclareInfo info = new WrapOrderDeclareInfo();
            info.setMainOrderId(orderDTO.getId());
            info.setMainOrderSn(orderDTO.getSn());
            Integer channel = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getChannel).orElse(null);
            info.setMainChannel(channel);
            info.setSn(customsOrderDTO.getSn());
            info.setStatus(customsOrderDTO.getStatus());
            info.setCustoms(CustomsDistrictEnum.getEnum(customsOrderDTO.getCustoms()).getCustoms());
            info.setDeclareCompanyDTO(buildCompany(agentCompany, customsOrderDTO.getCustoms()));
            info.setEbcCompanyDTO(buildCompany(ebcCompany, customsOrderDTO.getCustoms()));
            info.setEbpCompanyDTO(buildCompany(ebpCompany, customsOrderDTO.getCustoms()));
            info.setPayCompany(buildCompany(payCompany, customsOrderDTO.getCustoms()));
//            info.setDxpId(dxpId);
            info.setPayTransactionId(customsOrderDTO.getDeclarePayNo());
            info.setDeclareOrderNo(customsOrderDTO.getDeclareOrderNo());
            info.setOrderTaxAmount(customsOrderDTO.getTax().toString());
            info.setDiscount(customsOrderDTO.getDiscount().toString());
            info.setFreight(customsOrderDTO.getFreight().toString());
            info.setTradeTime(new DateTime(customsOrderDTO.getTradeTime()).getMillis());
            info.setConsigneeEmail(customsOrderDTO.getConsigneeEmail());
            info.setConsigneeTel(customsOrderDTO.getConsigneeTel());
            info.setConsignee(customsOrderDTO.getConsigneeName());
            CustomsOrderExtra extra = JSON.parseObject(customsOrderDTO.getExtraJson(), CustomsOrderExtra.class);
            String consigneeStreet = "";
            if (StringUtil.isNotEmpty(extra.getConsigneeStreet()) && !PddEncodeUtil.isPddOrder(ebpCompany.getCode(), orderDTO.getDeclareOrderNo())) {
                consigneeStreet = extra.getConsigneeStreet();
            }
            info.setConsigneeAddress(
                    extra.getConsigneeProvince() + "_"
                            + extra.getConsigneeCity() + "_"
                            + extra.getConsigneeDistrict() + "_"
                            + consigneeStreet
                            + customsOrderDTO.getConsigneeAddress());
//        info.setConsigneeAddress(customsOrderDTO.getConsigneeAddress());
            info.setSenderName(ebcCompany.getName());
            info.setLogisticsCompany(buildCompany(logisCompany, customsOrderDTO.getCustoms()));
            info.setPayerIdNumber(customsOrderDTO.getBuyerIdNumber());
            info.setPayerName(customsOrderDTO.getBuyerName());
            info.setPayerTelNumber(customsOrderDTO.getBuyerTelNumber());
            //过滤特殊字符
            if (!judgeIsLegalAndReplace(info, ebpCompany)) return;
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(extra.getBookId());
            if (Objects.nonNull(customsBookResVo)) {
                AccountBookDto accountBookDto = new AccountBookDto();
                BeanUtils.copyProperties(customsBookResVo, accountBookDto);
                // 设置账册
                info.setAccountBookDto(accountBookDto);
            }
            // 字节订购人、收件人修改信息明文替换
            if (Objects.nonNull(orderExtra) && (Objects.nonNull(orderExtra.getSubmit().getSensitivePlainText()))) {
                OrderSensitivePlainText sensitivePlainText = orderExtra.getSubmit().getSensitivePlainText();
                if (StringUtils.isNotEmpty(sensitivePlainText.getBuyerName())) {
                    info.setPayerName(sensitivePlainText.getBuyerName());
                }
                if (StringUtils.isNotEmpty(sensitivePlainText.getBuyerTelNumber())) {
                    info.setPayerTelNumber(sensitivePlainText.getBuyerTelNumber());
                }
                if (StringUtils.isNotEmpty(sensitivePlainText.getBuyerIdNumber())) {
                    info.setPayerIdNumber(sensitivePlainText.getBuyerIdNumber());
                }
                if (StringUtils.isNotEmpty(sensitivePlainText.getConsigneeAddress())) {
                    info.setConsigneeAddress(sensitivePlainText.getConsigneeAddress());
                }
                if (StringUtils.isNotEmpty(sensitivePlainText.getConsigneeName())) {
                    info.setConsignee(sensitivePlainText.getConsigneeName());
                }
                if (StringUtils.isNotEmpty(sensitivePlainText.getConsigneeTel())) {
                    info.setConsigneeTel(sensitivePlainText.getConsigneeTel());
                }
            }
            List<DeclareOrderItem> itemList = new ArrayList<>();
            List<CustomsOrderItem> orderItemList = JSON.parseArray(customsOrderDTO.getItemJson(), CustomsOrderItem.class);
            for (CustomsOrderItem orderItem : orderItemList) {
                DeclareOrderItem item = new DeclareOrderItem();
                item.setGoodsName(orderItem.getGoodsName());
                item.setGoodsModel(orderItem.getGoodsModel());
                item.setHsCode(orderItem.getHsCode());
                item.setUnitPrice(orderItem.getUnitPrice().toString());
                item.setGoodsUnit(orderItem.getGoodsUnit());
                item.setGoodsCount(orderItem.getGoodsCount());
                item.setOriginCountry(orderItem.getOriginCountry());
                itemList.add(item);
            }
            info.setItemList(itemList);
            OrderSensitivePlainText orderSensitivePlainText = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getSensitivePlainText).orElse(null);
            info.setPlainText(orderSensitivePlainText);
            // 设置下路由信息
            String routeCode = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getRouteCode).orElse(null);
            if (StringUtils.isNotBlank(routeCode)) {
                RouteInfoResVo routeDTO = routeService.findRouteByCode(routeCode);
                BuildInfoUtil.setWrapRouteInfo(info, routeDTO);
            }
            String dxpId = RouteDxpUtils.getConfigDxpIdByType(info);
            info.setDxpId(dxpId);
//        customsOrderService.updateByPush(customsOrderDTO.getId(), CustomsActionStatus.DEC_ING.getValue(), customsOrderDTO.getCreateTime());
            Integer declareFrequency = customsOrderDTO.getDeclareFrequency();
            declareFrequency = declareFrequency == null ? 1 : declareFrequency + 1;
            customsOrderService.updateByPush(customsOrderDTO.getId(), CustomsActionStatus.DEC_ING.getValue(), customsOrderDTO.getCreateTime(), declareFrequency);
            //更新内部流转状态和记录申报记录
            this.updateOrderInternalStatusAndDeclareRecord(info, orderDTO);
            log.info("订单申报 info={}", JSON.toJSONString(info));
            // 根据路由信息决定是否走动态
            if (info.dynamicDeclareEnable()) {
                DeclareUtils.orderDeclare(info);
            } else if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), customsOrderDTO.getDeclareOrderNo())) {
                log.info("申报单号：{} - 发送MQ订单消息-{}", customsOrderDTO.getDeclareOrderNo(), info);
                pddDeclareProducer.orderSend(info);
            } else if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
                byteDanceDeclareProducer.orderSend(info);
            } else {
                customsSupport.clareOrderDeclare(info);
            }

        } catch (Exception ex) {
            String message = ExceptionJoinUtil.getExceptionMessage(ex);
            log.error("申报单号：{} ,订单申报Handler处理异常：{}", customsOrderDTO.getDeclareOrderNo(), message, ex);
            // 处理异常，这里更改下申报状态为带申报，并维护下
            customsOrderService.updateStatusResetDeclareTime(customsOrderDTO.getSn(), CustomsActionStatus.DEC_WAIT);
            // Step::添加配置异常
            CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("DECLARE_PROCESSING_ERROR");
            if (Objects.nonNull(mappingDTO)) {
                if (StringUtils.isNotBlank(message) && message.length() > 1024) {
                    message = message.substring(0, 1024);
                }
                orderService.addExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO.getId(), "订单申报Handler处理异常:" + message, orderDTO.getCreateTime());
            }
        }
        // 给下京东安全追踪日志
        jdSecurityLogService.jdSecurityLogSend(orderDTO.getEbpId(), orderDTO.getSystemGlobalSn(), DeclareEnum.CUSTOMS_ORDER);
        log.info("[op:CustomsOrderHandler] finish, declareOrderNo={}", orderDTO.getDeclareOrderNo());
    }

    /**
     * 更新order表内部流转状态与申报记录
     *
     * @param info
     * @param orderDTO
     */
    public void updateOrderInternalStatusAndDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO) {
        if (orderCBaseConfig.getDeclareRouteRecordNewType()) {
            CustomsDeclareUtils.updateDeclareRecord(info, orderDTO, info.getDeclareCompanyCebCode(), DeclareEnum.CUSTOMS_ORDER);
        } else {
            CustomsDeclareUtils.buildDeclareRecord(info, orderDTO, DeclareEnum.CUSTOMS_ORDER);
        }
        orderService.updateOrderInternalStatusAndDeclareRecord(orderDTO, OrderInternalEnum.DECLARING.getCode());
    }

    private CompanyInfo buildCompany(CompanyDTO companyDTO, String customs) {
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getUnifiedCrossBroderCode());
        ebpInfo.setCebName(companyDTO.getName());
        ebpInfo.setCode(companyDTO.getCode());
        ebpInfo.setName(companyDTO.getName());
//        CompanyDistrictDTO ebpDis = companyDTO.getDistrict(CustomsDistrictEnum.getEnum(customs));
//        if (ebpDis == null) {
//            ebpInfo.setCode(companyDTO.getCode());
//            ebpInfo.setName(companyDTO.getName());
//        } else {
//            ebpInfo.setCode(ebpDis.getCode());
//            ebpInfo.setName(ebpDis.getName());
//        }

        JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
        String dxpId = agentExtra.getString("orderDxpId");
        if (!StringUtils.isEmpty(dxpId)) {
            ebpInfo.setDxpId(dxpId);
        }
        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }
        return ebpInfo;
    }

//    public boolean judgeIsLegalAndReplace(WrapOrderDeclareInfo orderDTO) {
//        String buyerName = FiltrerSpecialAndEmojUtil.filterSpecialChar(orderDTO.getConsignee());
//        buyerName = FiltrerSpecialAndEmojUtil.filterEmojis(buyerName);
//        String consigneeAddress = FiltrerSpecialAndEmojUtil.filterSpecialChar(orderDTO.getConsigneeAddress());
//        consigneeAddress = FiltrerSpecialAndEmojUtil.filterEmojis(consigneeAddress);
//        if (org.apache.commons.lang3.StringUtils.isEmpty(consigneeAddress) || org.apache.commons.lang3.StringUtils.isEmpty(buyerName)) {
//            if (org.apache.commons.lang3.StringUtils.isNotEmpty(filterSpecialCharConfig)) {
//                OrderDeclareV2DTO orderDeclareDTO = JSON.parseObject(filterSpecialCharConfig, OrderDeclareV2DTO.class);
//                StringBuilder builder = new StringBuilder();
//                builder.append("<font color=\\\"warning\\\">**" + "【[订单异常】收件人或详细地址是纯特殊字符，无法过滤" + "**</font>\\n\n");
//                builder.append(String.format("申报单号：%s", orderDTO.getDeclareOrderNo()) + "\r\n");
//                String content = builder.toString();
//                log.info("content: " + content);
//                WechatNotifyUtils.wechatNotifyMd(orderDeclareDTO.getWebHook(), orderDeclareDTO.getPhoneList(), content);
//            }
//            return false;
//        }
//        orderDTO.setConsignee(buyerName);
//        orderDTO.setConsigneeAddress(consigneeAddress);
//        return true;
//    }

    public boolean judgeIsLegalAndReplace(WrapOrderDeclareInfo orderDTO, CompanyDTO ebpCompany) {
        if (StringUtils.isEmpty(filterSpecialCharConfig)) {
            log.info("judgeIsLegalAndReplace 没有配置参数");
            return true;
        }
        FilterSpecialConfigDTO orderDeclareDTO = JSON.parseObject(filterSpecialCharConfig, FilterSpecialConfigDTO.class);
        if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), orderDTO.getDeclareOrderNo())) {
            if (orderDeclareDTO.getNotFilterPlatformCodeList().contains("PDD")) {
                log.info("judgeIsLegalAndReplace设置PDD不参与过滤");
                return true;
            }
        }
        //订购人姓名
//        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤订购人前{}", orderDTO.getPayerName());
//        String payerName = FiltrerSpecialAndEmojUtil.filterSpecialChar(orderDTO.getPayerName());
//        payerName = FiltrerSpecialAndEmojUtil.filterEmojis(payerName);
//        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤订购人后{}", payerName);
        //收件人姓名
        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤收件人前{}", orderDTO.getConsignee());
        String consigneeName = FiltrerSpecialAndEmojUtil.filterEmojis(orderDTO.getConsignee());
        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤收件人后{}", consigneeName);
        //收件人地址
        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤收件人地址前{}", orderDTO.getConsigneeAddress());
        String consigneeAddress = FiltrerSpecialAndEmojUtil.filterEmojis(orderDTO.getConsigneeAddress());
        log.debug("CustomsOrderHandler judgeIsLegalAndReplace过滤收件人地址后{}", consigneeAddress);
        if (StringUtils.isEmpty(consigneeAddress) || StringUtils.isEmpty(consigneeName)) {
            StringBuilder builder = new StringBuilder();
            builder.append("<font color=\\\"warning\\\">**" + "【订单异常】收件人或详细地址是纯特殊字符，无法过滤" + "**</font>\\n\n");
            builder.append(String.format("申报单号：%s", orderDTO.getDeclareOrderNo()) + "\r\n");
            String content = builder.toString();
            WechatNotifyUtils.wechatNotifyMd(orderDeclareDTO.getWebHook(), orderDeclareDTO.getPhoneList(), content);
            return false;
        }
        orderDTO.setConsignee(consigneeName);
        orderDTO.setConsigneeAddress(consigneeAddress);
        return true;
    }
}
