package com.danding.cds.service.mq.consumer.log;


import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.TrackLogEsService;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
@RocketMQMessageListener(
        consumerGroup = "ccs-trackLog-es-c-consumer",
        topic = "ccs-trackLog-es-c-topic"
)
public class TrackLogEsReceiver extends MessageHandler {

    @Resource
    private TrackLogEsService trackLogEsService;

    @Override
    public void handle(Object message) throws RuntimeException {
        try {
            if (ObjectUtils.isEmpty(message)) {
                return;
            }
            TrackLogEsDTO trackLogEsDTO = JSON.parseObject((String) message, TrackLogEsDTO.class);
            trackLogEsService.submit(trackLogEsDTO);
        } catch (ArgsInvalidException e) {
            log.error("TrackLogEsReceiver 轨迹日志保存错误 error={}", e.getMessage(), e);
        } catch (Exception e) {
            log.error("TrackLogEsReceiver 轨迹日志保存错误 error={}", e.getMessage(), e);
        }
    }
}
