package com.danding.cds.service.declareImpl.refund;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.refund.InventoryRefundDeclareAbstrat;
import com.danding.cds.declare.sdk.clear.base.result.InventoryRefundResult;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.service.customs.declare.InCloudDeclareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 京东CEB清单退货代理申报
 **/
@Service("JD_IN_CLOUD_REFUND_DECLARE_COMMON")
@Slf4j
public class JdInCloudIRefundDeclareCommonImpl extends InventoryRefundDeclareAbstrat {
    /**
     * 测试用
     */
    @Autowired
    private InCloudDeclareService inCloudDeclareService;

    @Override
    protected InventoryRefundResult mockDeclareTest(WarpRefundOrderInfo info) {
        log.info("京东CEB清单退货 代理申报 测试环境-MOCK info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }

    /**
     * @param info 数据
     * @return
     */
    @Override
    protected InventoryRefundResult declare(WarpRefundOrderInfo info) {
        log.info("京东CEB清单退货 代理申报 info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }
}
