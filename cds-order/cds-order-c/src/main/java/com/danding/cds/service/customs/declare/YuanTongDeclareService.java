package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.common.utils.PddOrderUtils;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.enums.CustomsLogisticsStatus;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.bean.dto.yuantong.YtDeclareReqVo;
import com.danding.cds.bean.dto.yuantong.YtOrderItemsBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtReceiveInfoBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtSendInfoBeanReqVo;
import com.danding.cds.bean.dto.yuantong.YtTradeOrderInfoBeanReqVo;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.base.CustomsInventoryItemBaseService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * @program: cds-center
 * @description: 圆通申报
 * @author: 潘本乐（Belep）
 * @create: 2021-09-29 14:26
 **/
@Slf4j
@Service
public class YuanTongDeclareService {
    @Autowired
    private CustomsInventoryBaseService customsInventoryBaseService;
    @Autowired
    private CustomsInventoryItemBaseService customsInventoryItemBaseService;
    @Autowired
    private CustomsLogisticsService customsLogisticsService;

    public void logisticsDeclare(WrapShipmentInfo info, CustomsLogisticsDTO customsLogisticsDTO, OrderDTO orderDTO) throws Exception {

        YtDeclareReqVo ytDeclareReqVo = new YtDeclareReqVo();
        ytDeclareReqVo.setOwnerCode("");
        ytDeclareReqVo.setStockCode("DT_KMYMBSC0926");
        ytDeclareReqVo.setPlatformCode("YTO");
        ytDeclareReqVo.setCpCode("YTO");

        YtReceiveInfoBeanReqVo receiveInfoBeanReqVo = new YtReceiveInfoBeanReqVo();
        receiveInfoBeanReqVo.setProvince(info.getConsigneeProvince());
        receiveInfoBeanReqVo.setCity(info.getConsigneeCity());
        receiveInfoBeanReqVo.setArea(info.getConsigneeDistrict());
        String consigneeStreet = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(info.getConsigneeStreet())
                && !PddOrderUtils.isPddOrder(orderDTO.getEbpId(), orderDTO.getDeclareOrderNo())) {
            consigneeStreet = info.getConsigneeStreet();
        }
        receiveInfoBeanReqVo.setAddressDetail(consigneeStreet + info.getConsigneeAddress());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        receiveInfoBeanReqVo.setReceiverCardNo(orderExtra.getSubmit().getBuyerIdNumber());
        receiveInfoBeanReqVo.setReceiverCardType("1");
        receiveInfoBeanReqVo.setReceivePhone(info.getConsigneeTel());
        receiveInfoBeanReqVo.setReceiveName(info.getConsignee());
        ytDeclareReqVo.setReceiveInfo(receiveInfoBeanReqVo);

        YtSendInfoBeanReqVo sendInfo = new YtSendInfoBeanReqVo();
        sendInfo.setProvince(customsLogisticsDTO.getConsigneeProvince());
        sendInfo.setCity(customsLogisticsDTO.getConsigneeCity());
        sendInfo.setArea(customsLogisticsDTO.getConsigneeDistrict());
        sendInfo.setAddressDetail(customsLogisticsDTO.getConsigneeAddress());
        sendInfo.setSendName("马栋麟");
        sendInfo.setSendPhone("15305325313");
        ytDeclareReqVo.setSendInfo(sendInfo);

        String logisticsNo = customsLogisticsDTO.getLogisticsNo();
        YtTradeOrderInfoBeanReqVo orderInfoBeanReqVo = new YtTradeOrderInfoBeanReqVo();
        orderInfoBeanReqVo.setWaybillNo(logisticsNo);
        orderInfoBeanReqVo.setApplyType("1");
        orderInfoBeanReqVo.setEcRegCid("530166052A");
        orderInfoBeanReqVo.setEcRegName("昆明云免实业有限公司");
        orderInfoBeanReqVo.setEcGjCid("3301964J31");
        orderInfoBeanReqVo.setEcGjName("330766K00Q");
        orderInfoBeanReqVo.setPortCode("8634");
        orderInfoBeanReqVo.setCustomsCode("HZCUSTOMSNEW");
        orderInfoBeanReqVo.setGrossWeight(info.getWeight());
        orderInfoBeanReqVo.setNetWeight(info.getWeight());
        orderInfoBeanReqVo.setOrderNo(info.getDeclareOrderNo());
        orderInfoBeanReqVo.setCurrenyCode("RMB");
        orderInfoBeanReqVo.setFreight("0.00");
        List<YtOrderItemsBeanReqVo> itemList = new ArrayList<>();
        CustomsInventoryDTO inventoryDTO = customsInventoryBaseService.findByLogisticsNo(logisticsNo);
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryItemBaseService.selectByInventoryIdIn90Days(inventoryDTO.getId());
        for (CustomsInventoryItemDTO item : customsInventoryItemDTOList) {
            YtOrderItemsBeanReqVo itemsBeanReqVo = new YtOrderItemsBeanReqVo();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(item.getExtraJson(), CustomsInventoryItemExtra.class);
            itemsBeanReqVo.setItemName(item.getItemName());
            itemsBeanReqVo.setItemCode(itemExtra.getProductId());
            itemsBeanReqVo.setItemPrice(itemExtra.getDeclarePrice().toString());
            itemsBeanReqVo.setItemQty(item.getCount().toString());
            itemsBeanReqVo.setItemWeight(itemExtra.getGrossWeight().toString());
            itemList.add(itemsBeanReqVo);
        }
        orderInfoBeanReqVo.setOrderItems(itemList);

        ytDeclareReqVo.setTradeOrderInfo(orderInfoBeanReqVo);
        ObjectMapper objectMapper = new ObjectMapper();
        String jsonString = objectMapper.writeValueAsString(ytDeclareReqVo);
        log.info("圆通运单申报 json={}", jsonString);
        try {
            HttpRequest httpRequest = HttpRequestUtil.post("http://tms.yang800.com:8090/v2/api/uploadwaybill", jsonString);
            if (httpRequest.ok()) {
                String body = httpRequest.body();
                log.info("圆通运单申报成功 body={}", body);
                JSONObject jsonObject = JSON.parseObject(body);
                LogisticsReceive receive = new LogisticsReceive();
                receive.setLogisticsNo(customsLogisticsDTO.getLogisticsNo());
                receive.setCustomsTime(new DateTime().getMillis());
                String customsDetail;
                if (Objects.equals(jsonObject.get("success").toString(), "true")) {
                    receive.setCustomsStatus("2");
                    customsDetail = "新增申报成功";
                } else {
                    receive.setCustomsStatus(CustomsLogisticsStatus.CUSTOMS_RECEIVE.getValue());
                    customsDetail = jsonObject.get("message").toString();
                }
                receive.setCustomsDetail(customsDetail);
                customsLogisticsService.receiveLogistics(receive);
            } else {
                String body = httpRequest.body();
                log.info("圆通运单申报失败 body={}", body);
            }
        } catch (Exception e) {
            log.error("圆通运单申报异常 error={}", e.getMessage());
        }

    }


}
