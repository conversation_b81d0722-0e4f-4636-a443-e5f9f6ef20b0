package com.danding.cds.service;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @Create 2021/9/22  9:54
 * @Describe
 **/
@Service
@Slf4j
@RefreshScope
public class PddRedisService {

    @Value("${PddCount:3}")
    private Integer pddCount;

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String PDD_ORDER_CONTROL = "ccs:pdd:order_control";

    public Boolean weatherOfficialDeclare(String declareNo) {
        List<Object> values = redisTemplate.opsForHash().values(PDD_ORDER_CONTROL);
        if (values.size() < pddCount) {
            redisTemplate.opsForHash().put(PDD_ORDER_CONTROL, declareNo, declareNo);
            log.info("PddRedisService declareNo={}走代报模式 存入redis", declareNo);
            return true;
        } else if (redisTemplate.opsForHash().hasKey(PDD_ORDER_CONTROL, declareNo)) {
            log.info("PddRedisService declareNo={}在redis中 走代报模式", declareNo);
            return true;
        } else {
            log.info("PddRedisService declareNo={}不在redis中 走云内申报模式", declareNo);
            return false;
        }
    }

}
