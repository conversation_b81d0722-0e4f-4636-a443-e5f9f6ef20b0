package com.danding.cds.service;

import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.JdSecrityLogDto;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.utils.IpUtil;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.google.common.base.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @program: cds-center
 * @description: 京东安全日志
 * @author: 潘本乐（Belep）
 * @create: 2022-04-20 14:37
 **/
@Service
@Slf4j
@RefreshScope
public class JdSecurityLogService {

    /**
     * 设备ID
     */
    private static final String DEVICE_ID_DEFAULT = "CCS_CUSTOMS_DECLARE_DEVICE";
    /**
     * 日志回传TOPIC
     */
    private static final String SECRITY_LOG_TOPIC = "channel-business-log-upload";
    /**
     * 原始URL, 根据时间段抓单地址
     */
    private static final String CLIENT_URL = "http://out.order.yang800.com/outshop/listWmsOrderByTime";
    /**
     * 订单推送的目的地URL- 默认浙江电子口岸
     */
    private static final String SENDTO_URL = "http://impl.ws.newyork.zjport.gov.cn/";

    @Value("${jd.ebp.id:}")
    private Long jdEbpId;

    @Autowired
    private MessageSender messageSender;


    /**
     * 获取IP
     *
     * @return IP地址
     */
    private String getIp() {
        return IpUtil.getMachineIp();
    }

    /**
     * 确认是否是京东的电商平台
     *
     * @param ebpId 电商平台
     * @return
     */
    private boolean isJdEbp(Long ebpId) {
        if (ebpId == null) {
            return false;
        }
        return Objects.equal(ebpId, jdEbpId);
    }

    /**
     * 安全日志发送
     *
     * @param ebpId          电商平台
     * @param globalSystemSn 全局订单号
     */
    public void jdSecurityLogSend(Long ebpId, String globalSystemSn, DeclareEnum declareEnum) {

        if (StringUtils.isEmpty(globalSystemSn)) {
            return;
        }

        if (!isJdEbp(ebpId)) {
            return;
        }
        log.info("globalSystemSn= {} ,[{}]京东安全日志回传-开始", globalSystemSn, declareEnum.getDesc());
        try {
            JdSecrityLogDto jdSecrityLogDto = new JdSecrityLogDto();
            jdSecrityLogDto.setIp(this.getIp());
            jdSecrityLogDto.setType("thirdSystem");
            jdSecrityLogDto.setDeviceId(DEVICE_ID_DEFAULT);
            jdSecrityLogDto.setTimeStamp(DateUtil.date().getTime());
            JdSecrityLogDto.BusinessInfoDto businessInfoDto = new JdSecrityLogDto.BusinessInfoDto();
            businessInfoDto.setGlobalSystemSnList(Stream.of(globalSystemSn).collect(Collectors.toList()));
            // 浙江电子口岸域名
            businessInfoDto.setSendtoUrl(SENDTO_URL);
            businessInfoDto.setUrl(CLIENT_URL);
            jdSecrityLogDto.setBusinessJson(JSON.toJSONString(businessInfoDto));
            String businessData = JSON.toJSONString(jdSecrityLogDto);
            log.info("globalSystemSn= {} ,[{}]京东安全日志回传-数据：{}", globalSystemSn, declareEnum.getDesc(), businessData);
            messageSender.sendMsg(businessData, SECRITY_LOG_TOPIC);
        } catch (Exception ex) {
            log.error("globalSystemSn= {} ,[{}]京东安全日志回传失败={}", globalSystemSn, declareEnum.getDesc(), ex.getMessage(), ex);
        }
        log.info("globalSystemSn= {} ,[{}]京东安全日志回传-结束", globalSystemSn, declareEnum.getDesc());
    }
}
