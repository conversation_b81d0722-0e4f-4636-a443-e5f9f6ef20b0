package com.danding.cds.monitor;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.order.api.dto.CustomsReceiptDTO;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.enums.DeclareItemStatusEnums;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.google.common.eventbus.EventBus;
import com.google.common.eventbus.Subscribe;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

@Slf4j
@Component
@RefreshScope
public class MappingEmptyListener {

    @Autowired
    private EventBus eventBus;

    @PostConstruct
    public void init() {
        eventBus.register(this);
    }

    @Value("${qw.webHookAddress.customs-status-mapping.monitor:}")
    private String webHookAddress;

    @Resource
    private CustomsStatusMappingService mappingService;

    @Subscribe
    public void execute(MappingEmptyEvent event) {
//        String webHookAddress = config.getProperty("qw.webHookAddress.customs-status-mapping.monitor", "");
        log.warn("[op:MappingEmptyListener-execute] not-mapping, fullMsg={}", event.getFullMsg());
        if (StringUtils.isEmpty(webHookAddress)) {
            webHookAddress = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a36c3f74-ec51-4fbb-9002-dca146bd6fad";
        }
        String env = EnvironmentConfig.getEnv();
        String code = event.getCode();
        //发送文本消息
        chatPhoneCall(webHookAddress, null, env, code, event.getFullMsg());
    }

    @Test
    public void send() {

        String message = "{\"customsDetail\":\"存在已申报清单[电商企业编码：330136803H,订单编号：228904863131,清单编号：29242021I057667637,清单状态：100-海关退单],短时间内不能重复新增申报操作;;\",\"customsStatus\":\"-621041\",\"customsTime\":1635266108649,\"declareOrderNo\":\"228904863131\",\"ebpCode\":\"31149679BZ\"}";
        String code = "DECLARE_INVENTORY:10:2600";
        String webHookAddress = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=a36c3f74-ec51-4fbb-9002-dca146bd6fad";

        //发送文本消息
        chatPhoneCall(webHookAddress, null, EnvironmentConfig.getEnv(), code, message);
    }


    boolean chatPhoneCall(String webHookAddress, List<String> chatPhoneList, String env, String code, String fullMessage) {
        log.warn(" chatPhoneCall 创建映射回执 - {}  回执报文 - {}", code, fullMessage);
        //创建映射
        StringBuffer result = createReceipt(code, fullMessage);
        HttpClient httpclient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(webHookAddress);
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");
        String message = String.format("【%s】-CCS-未定义的海关回传编码 \r\n【映射编码】：%s \r\n【完整报文】：%s \n" +
                "【回执映射】：%s", env, code, fullMessage, result);
        QwMessaage qwMessaage = new QwMessaage();
        qwMessaage.setMsgtype("text");
        qwMessaage.setText(new Content(message));
        StringEntity se = new StringEntity(JSON.toJSONString(qwMessaage), "utf-8");
        httppost.setEntity(se);
        try {
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("消息发送成功!");
                return true;
            } else {
                log.error("消息发送失败！异常码如下：{}", response.getStatusLine().getStatusCode());
                return false;
            }
        } catch (IOException e) {
            log.error("消息发送失败, 异常信息如下: {}", e.getMessage());
            return false;
        }
    }

    private StringBuffer createReceipt(String code, String fullMessage) {
        StringBuffer stringBuffer = new StringBuffer();
        Long receiptCode = null;
        if (Objects.nonNull(code)) {
            log.warn("createReceipt 海关回执映射编码 - {}  回执报文 - {}", code, fullMessage);
            try {
                String[] array = code.split(":");
                CustomsStatusMappingDTO mappingDTO = new CustomsStatusMappingDTO();
                if (array.length == 1) {
                    mappingDTO.setAction(array[0]);
                }
                if (array.length == 2) {
                    mappingDTO.setAction(array[0]);
                    mappingDTO.setCustomsStatusCode(array[1]);
                    String enumStr = CustomsStat.getEnum(array[1]).getDesc();
                    if (Objects.equals(enumStr, "")) {
                        stringBuffer.append("海关主编码未找到");
                    }
                    return stringBuffer;
                }
                if (array.length == 3) {
                    mappingDTO.setAction(array[0]);
                    mappingDTO.setCustomsStatusCode(array[1]);
                    mappingDTO.setDetailCode(array[2]);
                }
                if (Objects.nonNull(fullMessage)) {
                    CustomsReceiptDTO receipt = JSON.parseObject(fullMessage, CustomsReceiptDTO.class);
                    mappingDTO.setNote(receipt.getCustomsDetail());
                    mappingDTO.setDetail(fullMessage);
                }
                mappingDTO.setStatus(DeclareItemStatusEnums.DECLARE_IN.getValue());
                mappingDTO.setExceptionFlag(false);
                mappingDTO.setCode(code);

                receiptCode = mappingService.create(BeanUtil.copyProperties(mappingDTO, CustomsStatusMappingDTO.class));
                log.warn("createReceipt 回执映射成功编码 - {} ", receiptCode);
                stringBuffer.append("- 映射回执保存-成功 - 【映射编码】 - " + receiptCode);
            } catch (ArgsErrorException e) {
                stringBuffer.append("- 映射回执保存-失败").append(e.getErrorMessage());
            }
            log.warn("createReceipt 创建结果 - {} ", receiptCode);
        }
        return stringBuffer;
    }


    @Data
    class QwMessaage {

        private String msgtype;
        private Content text;
    }

    @Data
    @AllArgsConstructor
    class Content {
        private String content;
    }

}
