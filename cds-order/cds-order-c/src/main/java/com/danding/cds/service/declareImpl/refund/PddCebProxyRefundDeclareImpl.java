package com.danding.cds.service.declareImpl.refund;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.declare.base.component.refund.InventoryRefundDeclareAbstrat;
import com.danding.cds.declare.sdk.clear.base.result.InventoryRefundResult;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 拼多多CEB代理退单申报
 * @author: 潘本乐（Belep）
 * @create: 2022-02-16 10:33
 **/
@Service("PDD_CEB_PROXY_INVENTORY_REFUND_DECLARE")
@Slf4j
public class PddCebProxyRefundDeclareImpl extends InventoryRefundDeclareAbstrat {

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    protected InventoryRefundResult mockDeclareTest(WarpRefundOrderInfo info) {
        log.info("拼多多CEB退货代理申报，测试环境MOCK");
        pddDeclareService.cloudOfficialDeclareTest(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB625);

        return null;
    }

    @Override
    protected InventoryRefundResult declare(WarpRefundOrderInfo info) {

        pddDeclareService.cloudOfficialDeclare(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB625);
        return null;
    }
}
