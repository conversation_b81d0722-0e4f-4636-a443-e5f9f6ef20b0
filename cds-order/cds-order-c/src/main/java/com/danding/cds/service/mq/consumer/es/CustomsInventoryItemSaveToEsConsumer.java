package com.danding.cds.service.mq.consumer.es;

import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.order.base.bean.dao.es.CustomsInventoryItemEsDO;
import com.danding.cds.order.base.bean.dao.es.CustomsSingleInventoryEsDO;
import com.danding.cds.service.es.CustomsSingleInventoryEsDao;
import com.dt.component.canal.mq.AbstractCanalMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * 功能描述:  清单申报
 * 创建时间:  2021/4/2
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "danding_ccs_customs_inventory_item_c_topic", consumerGroup = "ccs_customs_inventory_item_c_save_to_es")
public class CustomsInventoryItemSaveToEsConsumer extends AbstractCanalMQService<CustomsInventoryItemDTO> implements RocketMQListener<FlatMessage> {

    @Autowired
    private CustomsSingleInventoryEsDao customsSingleInventoryEsDao;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    /**
     * 删除
     *
     * @param customsInventoryItemDTO
     */
    @Override
    public void delete(CustomsInventoryItemDTO customsInventoryItemDTO) {
        try {
            deleteAction(customsInventoryItemDTO);
        } catch (Exception e) {
            log.error("CustomsInventoryItemSaveToEsConsumer delete error: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 新增操作
     *
     * @param customsInventoryItemDTO
     */
    @Override
    public void insert(CustomsInventoryItemDTO customsInventoryItemDTO) {
        try {
            insertAction(customsInventoryItemDTO);
        } catch (Exception e) {
            log.error("CustomsInventoryItemSaveToEsConsumer insert error: " + e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 对于更新操作来讲，before 中的属性只包含变更的属性，after 包含所有属性，通过对比可发现那些属性更新了
     *
     * @param before 只有修改字段才会有值
     * @param after
     */
    @Override
    public void update(CustomsInventoryItemDTO before, CustomsInventoryItemDTO after) {
        try {
            updateAction(before, after);
        } catch (Exception e) {
            log.error("CustomsInventoryItemSaveToEsConsumer update error: " + e.getMessage(), e);
            throw e;
        }
    }

    private void insertAction(CustomsInventoryItemDTO customsInventoryItemDTO) {
        if (customsInventoryItemDTO.getExtraJson() == null) {
            log.error("CustomsInventoryItemSaveToEsConsumer extraJson 为空");
            return;
        }
        // 1. 单独某个item新增
        CustomsInventoryItemExtra extra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
        //
        String goodsSeqNo = extra.getGoodsSeqNo();
        String productId = extra.getProductId();
        Integer count = customsInventoryItemDTO.getCount();
        // 2. 查询出原有记录 有则修改 无则插入
        CustomsSingleInventoryEsDO esDO = customsSingleInventoryEsDao.getByInventoryId(customsInventoryItemDTO.getCustomsInventoryId());
        if (esDO == null) {
            // es没有清单数据、新增
            initCustomsInventoryToEs(customsInventoryItemDTO);
        } else {
            // es 已有该清单
            boolean update = true;
            if (CollectionUtils.isEmpty(esDO.getItemExtras())) {
                esDO.setItemExtras(new ArrayList<>());
            } else {
                for (CustomsInventoryItemEsDO extraEsDTO : esDO.getItemExtras()) {
                    // 料号与序号 equals都相等 或者 都是null 则不修改
                    if (equalsParam(extraEsDTO.getGoodsSeqNo(), goodsSeqNo)
                            && equalsParam(extraEsDTO.getProductId(), productId)) {
                        // 如果序号与料号的组合已存在 则不添加
                        update = false;
                        log.warn("danding_ccs_customs_inventory_item_topic：料号序号组合已存在不再添加");
                    }
                }
            }

            if (update) {
                CustomsInventoryItemEsDO extraEsDTO = new CustomsInventoryItemEsDO();
                extraEsDTO.setId(customsInventoryItemDTO.getId());
                extraEsDTO.setGoodsSeqNo(goodsSeqNo);
                extraEsDTO.setProductId(productId);
                extraEsDTO.setCount(count);
                esDO.getItemExtras().add(extraEsDTO);
                // 覆盖
                customsSingleInventoryEsDao.save(esDO);
            }
        }
    }

    private boolean equalsParam(String str1, String str2) {
        if (str1 == null && str2 == null || str1.equals(str2)) {
            return true;
        }
        return false;
    }

    private void updateAction(CustomsInventoryItemDTO before, CustomsInventoryItemDTO after) {
        if (StringUtils.isEmpty(before.getExtraJson())
                || StringUtils.isEmpty(after.getExtraJson())
                || after.getExtraJson().equals(before.getExtraJson())) {
            // extraJson未更新则不处理
            return;
        }

        // 2. 查询出原有记录 有则修改 无则插入
        CustomsSingleInventoryEsDO esDO = customsSingleInventoryEsDao.getByInventoryId(after.getCustomsInventoryId());
        if (esDO == null) {
            // es没有清单数据、新增
            initCustomsInventoryToEs(after);
        } else {
            // es 已有该清单
            boolean update = false;

            // 修改后的序号、料号
            CustomsInventoryItemExtra afterExtra = JSON.parseObject(after.getExtraJson(), CustomsInventoryItemExtra.class);
            String afterGoodsSeqNo = afterExtra.getGoodsSeqNo();
            String afterProductId = afterExtra.getProductId();

            // 修改前的序号、料号
            CustomsInventoryItemExtra beforeExtra = JSON.parseObject(before.getExtraJson(), CustomsInventoryItemExtra.class);
            String beforeGoodsSeqNo = beforeExtra.getGoodsSeqNo();
            String beforeProductId = beforeExtra.getProductId();

            // 是否有变更 结果都为空则不修改
            String goodsSeqNo = afterGoodsSeqNo.equals(beforeGoodsSeqNo) ? "" : afterGoodsSeqNo;
            String productId = afterProductId.equals(beforeProductId) ? "" : afterProductId;
            Integer count = after.getCount();
            Long itemId = after.getId();
            if (CollectionUtils.isEmpty(esDO.getItemExtras())) {
                if (!StringUtils.isEmpty(goodsSeqNo) || !StringUtils.isEmpty(productId)) {
                    CustomsInventoryItemEsDO extraEsDTO = new CustomsInventoryItemEsDO();
                    extraEsDTO.setId(after.getId());
                    extraEsDTO.setProductId(productId);
                    extraEsDTO.setGoodsSeqNo(goodsSeqNo);
                    extraEsDTO.setCount(count);
                    List<CustomsInventoryItemEsDO> list = new ArrayList<>();
                    list.add(extraEsDTO);
                    esDO.setItemExtras(list);
                    update = true;
                }
            } else {
                for (CustomsInventoryItemEsDO extraEsDTO : esDO.getItemExtras()) {
                    if (itemId != null && itemId.equals(extraEsDTO.getId())) {
                        // 找到对应的item
                        if (!StringUtils.isEmpty(goodsSeqNo)) {
                            extraEsDTO.setGoodsSeqNo(goodsSeqNo);
                            update = true;
                        }
                        if (!StringUtils.isEmpty(productId)) {
                            extraEsDTO.setProductId(productId);
                            update = true;
                        }
                    }
                }
            }

            if (update) {
                // 覆盖
                customsSingleInventoryEsDao.save(esDO);
            }
        }
    }

    private void initCustomsInventoryToEs(CustomsInventoryItemDTO customsInventoryItemDTO) {
        // 查询清单数据
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findById(customsInventoryItemDTO.getCustomsInventoryId());
        if (inventoryDTO == null) {
            log.warn("根据清单ID={}，没有找到对应数据", customsInventoryItemDTO.getCustomsInventoryId());
            return;
        }
        // 查询清单item数据
        List<CustomsInventoryItemDTO> list = customsInventoryService.listItemByIdSectionNoFilter(inventoryDTO.getId(), inventoryDTO.getCreateTime());
        // 保存
        customsSingleInventoryEsDao.esSave(inventoryDTO, list);
    }

    private void deleteAction(CustomsInventoryItemDTO customsInventoryItemDTO) {
        // 1. 单独某个item新增
        CustomsInventoryItemExtra extra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
        //
        String goodsSeqNo = extra.getGoodsSeqNo();
        String productId = extra.getProductId();

        // 2. 查询出原有记录 有则修改 无则插入
        CustomsSingleInventoryEsDO esDO = customsSingleInventoryEsDao.getByInventoryId(customsInventoryItemDTO.getCustomsInventoryId());
        if (esDO == null) {
            log.error("监听到customs_inventory_item.delete ES中查无数据 customsInventoryId：{}", customsInventoryItemDTO.getCustomsInventoryId());
        } else {
            if (CollectionUtils.isEmpty(esDO.getItemExtras())) {
                log.info("监听到customs_inventory_item.delete ES中拓展字段为空 customsInventoryId：{}", customsInventoryItemDTO.getCustomsInventoryId());
            } else {
                boolean update = false;
                Iterator<CustomsInventoryItemEsDO> it = esDO.getItemExtras().iterator();
                while (it.hasNext()) {
                    CustomsInventoryItemEsDO extraEsDTO = it.next();
                    // 料号与序号 equals都相等 或者 都是null
                    if (equalsParam(extraEsDTO.getGoodsSeqNo(), goodsSeqNo)
                            && equalsParam(extraEsDTO.getProductId(), productId)) {
                        it.remove();
                        update = true;
                    }
                }
                if (update) {
                    customsSingleInventoryEsDao.save(esDO);
                }
            }
        }
    }


}
