package com.danding.cds.service.declareImpl.inventory;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.domain.ceb816.Tax;
import com.danding.cds.declare.ceb.domain.ceb816.TaxListRd;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.declare.sdk.clear.base.callback.module.TaxResult;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.tax.TaxItem;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 跳过清单申报节点
 * @date 2023/10/10 16:29
 */
@Service("EXTERNAL_DECLARED_JUMP_INVENTORY_DECLARE")
@Slf4j
public class JumpInventoryDeclareImpl extends InventoryDeclareAbstract {
    @Autowired
    private CustomsInventoryService customsInventoryService;
    @Autowired
    private OrderService orderService;
    @DubboReference
    private MessageService messageService;
    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;


    @Override
    protected InventoryDeclareResult mockDeclareTest(WrapInventoryOrderInfo info) {
        log.info("申报单: {} ,【清单申报】", info.getDeclareNos());
        return declare(info);
    }

    @Override
    protected InventoryDeclareResult declare(WrapInventoryOrderInfo info) {
        log.info("清单开始申报，申报单: {} 清单选择跳过申报 系统直接申报成功", info.getDeclareNos());
        CustomsInventoryInfo customsInventoryDto = info.getCustomsInventoryDto();
        // 维护下ccs清单申报自己的企业内部编码
        ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByOrderNo(info.getDeclareNos());
        if (Objects.isNull(singleSignOrderDO)) {
            throw new ArgsInvalidException("未查询到外部订单信息");
        }
        externalDeclareOrderBaseService.updateCcsCopNoByOrderNo(info.getDeclareNos(), customsInventoryDto.getSn());
        customsInventoryService.updateByCustomsPass(customsInventoryDto.getId(), System.currentTimeMillis(), "跳过清单申报，直接放行", null, singleSignOrderDO.getInventoryNo(), customsInventoryDto.getCreateTime());
        OrderDTO orderDTO = orderService.findBySnSection(info.getMainOrderSn());
        if (Objects.nonNull(orderDTO)) {
            orderService.updateStatusSection(orderDTO.getId(), orderDTO.getSn(), OrderStatus.DEC_SUCCESS.getValue(), OrderInternalEnum.ORDER_PASS.getCode(), orderDTO.getCreateTime());

            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
            ArrayList<String> subscribeList = Lists.newArrayList("PLAT-PANGU", submitChannel);

            messageService.createMessage(
                    MessageType.ORDER_CUSTOMS_INVENTORY,
                    subscribeList,
                    orderDTO.getDeclareOrderNo(),
                    JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo("800", "[Code:2600;Desc:放行]", new DateTime(new Date()).toDate())),
                    "");
            if (Objects.nonNull(singleSignOrderDO.getTaxCallback())) {
                try {
                    TaxResult taxResult = this.buildTaxMessage(singleSignOrderDO.getTaxCallback());
                    messageService.createMessage(
                            MessageType.ORDER_TAX,
                            subscribeList,
                            orderDTO.getDeclareOrderNo(),
                            JSON.toJSONString(taxResult),
                            "");
                } catch (Exception e) {
                    log.error("回传税金失败 error={}", e.getMessage(), e);
                }
            }
        }
        log.info("处理完成，申报单: {} 系统直接申报成功", info.getDeclareNos());
        return null;
    }


    private TaxResult buildTaxMessage(String message) throws Exception {
        CEB816Message ceb816Message = XMLUtil.converyToJavaBean(message, CEB816Message.class);
        List<Tax> taxList = ceb816Message.getTax();
        Tax tax = taxList.get(0);
        TaxResult taxResult = new TaxResult();
        taxResult.setOrderNo(tax.getTaxHeadRd().getOrderNo());
        taxResult.setLogisticsNo(tax.getTaxHeadRd().getLogisticsNo());
        Date returnDate = DateUtils.parseDate(tax.getTaxHeadRd().getReturnTime(), "yyyyMMddHHmmssSSS");
        taxResult.setReturnTime(returnDate);
        taxResult.setInvtNo(tax.getTaxHeadRd().getInvtNo());
        taxResult.setTaxNo(tax.getTaxHeadRd().getTaxNo());
        taxResult.setCustomsTax(tax.getTaxHeadRd().getCustomsTax());
        taxResult.setValueAddedTax(tax.getTaxHeadRd().getValueAddedTax());
        taxResult.setConsumptionTax(tax.getTaxHeadRd().getConsumptionTax());
        taxResult.setStatus(tax.getTaxHeadRd().getStatus());
        taxResult.setEntDutyNo(tax.getTaxHeadRd().getEntDutyNo());
        taxResult.setNote(tax.getTaxHeadRd().getNote());
        taxResult.setEbcCode(tax.getTaxHeadRd().getEbcCode());
        taxResult.setAssureCode(tax.getTaxHeadRd().getAssureCode());
        taxResult.setLogisticsCode(tax.getTaxHeadRd().getLogisticsCode());
        taxResult.setAgentCode(tax.getTaxHeadRd().getAgentCode());
        taxResult.setCustomsCode(tax.getTaxHeadRd().getCustomsCode());
        List<TaxItem> itemList = new ArrayList<>();
        List<TaxListRd> taxListRdList = tax.getTaxListRd();
        if (!CollectionUtils.isEmpty(taxListRdList)) {
            for (TaxListRd taxListRd : taxListRdList) {
                TaxItem item = new TaxItem();
                item.setGnum(taxListRd.getGnum());
                item.setGcode(taxListRd.getGcode());
                item.setTaxPrice(taxListRd.getTaxPrice());
                item.setCustomsTax(taxListRd.getCustomsTax());
                item.setValueAddedTax(taxListRd.getValueAddedTax());
                item.setConsumptionTax(taxListRd.getConsumptionTax());
                itemList.add(item);
            }
        }
        taxResult.setItemList(itemList);
        return taxResult;
    }
}
