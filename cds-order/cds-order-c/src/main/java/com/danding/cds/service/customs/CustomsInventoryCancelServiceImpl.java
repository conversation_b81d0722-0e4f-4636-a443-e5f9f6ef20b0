package com.danding.cds.service.customs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.enums.InventoryCalculationTypeEnums;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.customs.logistics.api.enums.CustomsLogisticsStatus;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handler.CustomsInventoryCancelHandler;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.handler.StockInventoryCancelHandler;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.OperateReasonEnums;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryCancelDO;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.mapper.CustomsInventoryCancelMapper;
import com.danding.cds.order.base.service.TrackLogEsProducer;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.es.OrderEsDao;
import com.danding.cds.service.mq.CancelOrderNotifyService;
import com.danding.cds.stock.annotations.StockVerify;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.enums.InventoryCheckOutStatusEnum;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.component.uc.model.CurrentUserInfo;
import com.danding.component.uc.model.RealUserInfo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Transformer;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Page;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.sql.*;
import java.util.Date;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import java.util.stream.Collectors;


@Slf4j
@Service
@RefreshScope
public class CustomsInventoryCancelServiceImpl implements CustomsInventoryCancelService {

    @Autowired
    private CustomsInventoryCancelServiceImpl inventoryCancelService;

    @Autowired
    private CustomsInventoryCancelMapper customsInventoryCancelMapper;
    @Autowired
    @Lazy
    private CustomsInventoryCancelHandler customsInventoryCancelHandler;
    @Resource
    private CustomsInventoryService customsInventoryService;
    @Resource
    private OrderService orderService;
    @DubboReference
    private SequenceService sequenceService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private CustomsBookService customsBookService;
    @Resource
    private RefundOrderService refundOrderService;
    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @Resource
    private StockInventoryService stockInventoryService;
    @Resource
    private TrackLogService trackLogService;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private OrderEsDao orderEsDao;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private CancelOrderNotifyService cancelOrderNotifyService;

    @Resource
    private SupervisionMonitorService supervisionMonitorService;

    @Resource(name = "cacnelCountThreadExecutor")
    private ThreadPoolTaskExecutor cacnelCountThreadExecutor;

    @Resource
    private CustomsInventoryBaseService customsInventoryBaseService;

    @Value("${jd.ebp.id:}")
    private Long jdEbpId;

    /**
     * 京东撤单回执发送  成功和失败的正则校验
     */
    @Value("${jd.cancel.send.success.regex.json:}")
    private String jdCancelSendSuccessRegexJson;

    @Value("${jd.cancel.send.fail.regex.json:}")
    private String jdCancelSendFailRegexJson;
    /**
     * 需要回传的账册标签
     */
    @Value("${platCallback.needCallbackBookTag:}")
    private List<Integer> needCallbackBookTag;

    /**
     * 需要回传的跨境进口统一编码
     */
    @Value("${platCallback.needCallbackCode:}")
    private List<String> needCallbackCode;


    /**
     * 启用 撤单&退货申报校验清单编号重复
     */
    @Value("${enable.cancel.refund.check.invNo.declare:false}")
    private boolean enableCancelRefundCheckInvNoDeclare;


    /**
     * 获取：初始化+申报中+待总署审核总数
     *
     * @return
     */
    @Override
    public Integer selectCancelInfoCount(String areaCompanyId) {
        try {
            List<String> list = Arrays.asList(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue()
                    , InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAITING.getValue()
                    , InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue());
            Example example = new Example(CustomsInventoryCancelDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("status", list).andEqualTo("deleted", false);
            if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
                criteria.andEqualTo("areaCompanyId", areaCompanyId);
            }
            List<CustomsInventoryCancelDO> cancelDOS = customsInventoryCancelMapper.selectByExample(example);
            return cancelDOS.size();
        } catch (Exception e) {
            return 0;
        }

    }


    @Override
    public CustomsInventoryCancelDTO findById(Long id) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andEqualTo("id", id);
        CustomsInventoryCancelDTO _info = new CustomsInventoryCancelDTO();
        BeanUtils.copyProperties(customsInventoryCancelMapper.selectOneByExample(example), _info);
        return _info;
    }


    @Override
    public List<CustomsInventoryCancelDTO> findById(List<Long> idList) {
        if (idList == null || idList.isEmpty()) {
            return new ArrayList<>();
        } else {
            Example example = new Example(CustomsInventoryCancelDO.class);
            example.createCriteria().andIn("id", idList).andEqualTo("deleted", 0);
            List<CustomsInventoryCancelDO> cancelDOS = customsInventoryCancelMapper.selectByExample(example);
            return ConvertUtil.listConvert(cancelDOS, CustomsInventoryCancelDTO.class);
        }
    }

    @Override
    public List<CustomsInventoryCancelDTO> findByIdList(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andIn("id", idList);
        return ConvertUtil.listConvert(customsInventoryCancelMapper.selectByExample(example), CustomsInventoryCancelDTO.class);
    }

    @Override
    public CustomsInventoryCancelDTO findBySn(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andEqualTo("orderCancelSn", sn);
        CustomsInventoryCancelDTO _info = new CustomsInventoryCancelDTO();
        BeanUtils.copyProperties(customsInventoryCancelMapper.selectOneByExample(example), _info);
        return _info;
    }


    @Override
    public CustomsInventoryCancelDTO findByOrderId(Long orderId) {
        if (orderId == null) return null;
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria()
                .andEqualTo("refOrderId", orderId)
                .andEqualTo("deleted", 0);
        CustomsInventoryCancelDTO _info = new CustomsInventoryCancelDTO();
        CustomsInventoryCancelDO cancel = customsInventoryCancelMapper.selectOneByExample(example);
        if (cancel == null) return null;
        BeanUtils.copyProperties(cancel, _info);
        return _info;
    }

    @Override
    public List<CustomsInventoryCancelDTO> findByOrderId(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria()
                .andIn("refOrderId", orderIdList)
                .andEqualTo("deleted", 0);
        List<CustomsInventoryCancelDO> result = customsInventoryCancelMapper.selectByExample(example);
        return ConvertUtil.listConvert(result, CustomsInventoryCancelDTO.class);
    }

    public CustomsInventoryCancelDTO findByInventoryId(Long inventoryId) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refInvoId", inventoryId);
        criteria.andEqualTo("deleted", false);
        CustomsInventoryCancelDO customsInventoryCancelDO = customsInventoryCancelMapper.selectOneByExample(example);
        if (customsInventoryCancelDO == null) return null;
        CustomsInventoryCancelDTO customsInventoryCancelDTO = new CustomsInventoryCancelDTO();
        BeanUtils.copyProperties(customsInventoryCancelDO, customsInventoryCancelDTO);
        return customsInventoryCancelDTO;
    }

    @Override
    public CustomsInventoryCancelDTO findByInvoSn(String refInvoSn) {
        if (StringUtils.isEmpty(refInvoSn)) {
            return null;
        }
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andEqualTo("refInvoSn", refInvoSn)
                .andEqualTo("deleted", 0);
        CustomsInventoryCancelDO customsInventoryCancelDO = customsInventoryCancelMapper.selectOneByExample(example);
        if (Objects.isNull(customsInventoryCancelDO)) return null;
        CustomsInventoryCancelDTO customsInventoryCancelDTO = new CustomsInventoryCancelDTO();
        BeanUtils.copyProperties(customsInventoryCancelDO, customsInventoryCancelDTO);
        return customsInventoryCancelDTO;
    }

    @Override
    public List<CustomsInventoryCancelDTO> findByInvoSn(List<String> refInvoSn) {
        if (CollUtil.isEmpty(refInvoSn)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andIn("refInvoSn", refInvoSn)
                .andEqualTo("deleted", 0);
        List<CustomsInventoryCancelDO> customsInventoryCancelDO = customsInventoryCancelMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsInventoryCancelDO, CustomsInventoryCancelDTO.class);
    }


    private Example buildExample(InventoryCancelSearch search) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        // 账册Id列表
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("customsBookId", accountBookIdList);
        }
        //账册编号查询
        if (!LongUtil.isNone(search.getAccountBookId())) {
            criteria.andEqualTo("customsBookId", search.getAccountBookId());
        }
        //用户名称查询
        if (StringUtil.isNotEmpty(search.getTenantId())) {
            criteria.andEqualTo("tenantId", search.getTenantId());
        }
        /**
         * 审核状态
         */
        if (!CollectionUtils.isEmpty(search.getStatusList())) {
            criteria.andIn("status", search.getStatusList());
        }
        /**
         *  区内企业
         */
        if (search.getAreaCompanyId() != null) {
            criteria.andEqualTo("areaCompanyId", search.getAreaCompanyId());
        }
        if (search.getBeginCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCreateTime());
            criteria.andGreaterThanOrEqualTo("createTime", tempDate);
        }
        if (search.getEndCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCreateTime());
            criteria.andLessThanOrEqualTo("createTime", tempDate);
        }
        if (search.getBeginCompleteTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCompleteTime());
            criteria.andGreaterThanOrEqualTo("completeTime", tempDate);
        }
        if (search.getEndCompleteTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCompleteTime());
            criteria.andLessThanOrEqualTo("completeTime", tempDate);
        }
        //0:运单编号，1：渠道编号,2:清单编号
        if ((!StringUtils.isEmpty(search.getQueryType())) && (!StringUtils.isEmpty(search.getQueryInfo()))) {
            String[] nos = StringUtils.split(search.getQueryInfo(), ",");
            if (nos != null && nos.length > 0) {
                OrderSearch orderSearch = new OrderSearch();
                orderSearch.setQueryType(search.getQueryType());
                orderSearch.setQueryInfo(search.getQueryInfo());
                orderSearch.setPageSize(nos.length);
                orderSearch.setCurrentPage(1);
                Page<OrderEsDO> page = orderEsDao.paging(orderSearch);
                List<Long> declareIds = page.getContent().stream().map(s -> {
                    return NumberUtils.createLong(s.getId());
                }).collect(Collectors.toList());
                if (declareIds == null) declareIds = new ArrayList<Long>();
                if (org.apache.commons.collections.CollectionUtils.isEmpty(declareIds)) {
                    declareIds.add(0l);
                }
                criteria.andIn("refOrderId", declareIds);
            }
        }
        return example;
    }

    @Override
    public CustomsInventoryCancelDTO buildCustomsInventoryCancelDTO(CustomsInventoryDTO customsInventoryDTO, String reason) throws ArgsErrorException {
        OrderDTO orderDTO = orderService.findByIdFull(customsInventoryDTO.getOrderId());
        CustomsInventoryCancelDTO _info = new CustomsInventoryCancelDTO();
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        _info.setLesseeNo(inventoryExtra.getTenantName());
        _info.setTenantId(inventoryExtra.getTenantOuterId());
        _info.setDeleted(false);
        _info.setCustomsLastTime(null);
        _info.setCustomsCheckDetail(null);
        _info.setCustomsStatus(null);
        _info.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue());
        _info.setStatusTime(new Date());
        _info.setCancelReason(reason);
        _info.setUpdateBy(UserUtils.getUserId());
        _info.setCreateBy(UserUtils.getUserId());
        _info.setChannelNo(orderDTO.getOutOrderNo());
        _info.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
        _info.setCustomsBookId(customsInventoryDTO.getAccountBookId());
        _info.setCompleteTime(null);
        //这三个字段后面再确认下

        _info.setExpressId(customsInventoryDTO.getExpressId());
        _info.setMailNo(customsInventoryDTO.getLogisticsNo());
        /*
        if(customsInventoryDTO.getExpressId()!=null) {
            ExpressDTO expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
            if(expressDTO!=null)
            _info.setExpressName(expressDTO.getName());
        }
        */
        //
        _info.setCreateTime(new Date());
        _info.setUpdateTime(new Date());
        _info.setMailNo(customsInventoryDTO.getLogisticsNo());
        _info.setInvoNo(customsInventoryDTO.getInventoryNo() == null ? "" : customsInventoryDTO.getInventoryNo());
        _info.setRefOrderId(customsInventoryDTO.getOrderId());
        _info.setRefOrderSn(customsInventoryDTO.getDeclareOrderNo());
        _info.setRefInvoId(customsInventoryDTO.getId());
        _info.setRefInvoSn(customsInventoryDTO.getSn());
        _info.setOrderCancelSn(sequenceService.generateInventoryCancelOrderSn());
        //replaceAll("[a-zA-Z]","")
        _info.setId(NumberUtils.createLong(StringUtils.replacePattern(_info.getOrderCancelSn(), "[a-zA-Z]", "")));
        return _info;
    }

    @Override
    public List<CustomsInventoryCancelDTO> batchBuildCustomsInventoryCancelDTO(Map<CustomsInventoryDTO, OrderDTO> customsInventoryOrderDTOMap, String reason) {
        List<CustomsInventoryCancelDTO> res = new ArrayList<>();
        customsInventoryOrderDTOMap.forEach((customsInventoryDTO, orderDTO) -> {
            CustomsInventoryCancelDTO cancel = new CustomsInventoryCancelDTO();
            CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
            cancel.setLesseeNo(inventoryExtra.getTenantName());
            cancel.setTenantId(inventoryExtra.getTenantOuterId());
            cancel.setDeleted(false);
            cancel.setCustomsLastTime(null);
            cancel.setCustomsCheckDetail(null);
            cancel.setCustomsStatus(null);
            cancel.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue());
            cancel.setStatusTime(new Date());
            cancel.setCancelReason(reason);
            cancel.setUpdateBy(UserUtils.getUserId());
            cancel.setCreateBy(UserUtils.getUserId());
            cancel.setChannelNo(orderDTO.getOutOrderNo());
            cancel.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
            cancel.setCustomsBookId(customsInventoryDTO.getAccountBookId());
            cancel.setCompleteTime(null);
            cancel.setExpressId(customsInventoryDTO.getExpressId());
            cancel.setMailNo(customsInventoryDTO.getLogisticsNo());
            cancel.setCreateTime(new Date());
            cancel.setUpdateTime(new Date());
            cancel.setMailNo(customsInventoryDTO.getLogisticsNo());
            cancel.setInvoNo(customsInventoryDTO.getInventoryNo() == null ? "" : customsInventoryDTO.getInventoryNo());
            cancel.setRefOrderId(customsInventoryDTO.getOrderId());
            cancel.setRefOrderSn(customsInventoryDTO.getDeclareOrderNo());
            cancel.setRefInvoId(customsInventoryDTO.getId());
            cancel.setRefInvoSn(customsInventoryDTO.getSn());
            cancel.setOrderCancelSn(sequenceService.generateInventoryCancelOrderSn());
            cancel.setId(NumberUtils.createLong(StringUtils.replacePattern(cancel.getOrderCancelSn(), "[a-zA-Z]", "")));
            res.add(cancel);
        });
        return res;
    }

    @Override
    public void updateOrderInfoByOldInfo(OrderDTO orderDTO, OrderDTO newOrderDTO) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andEqualTo("refOrderId", orderDTO.getId()).andEqualTo("refOrderSn", orderDTO.getSn()).andEqualTo("deleted", false);
        customsInventoryCancelMapper.updateByExampleSelective(new CustomsInventoryCancelDO().setRefOrderId(newOrderDTO.getId()).setRefOrderSn(newOrderDTO.getSn()), example);
    }

    @Override
    public void customsInventoryCancelInsert(CustomsInventoryCancelDTO customsInventoryCancelDTO) {
        CustomsInventoryCancelDO customsInventoryCancelDO = new CustomsInventoryCancelDO();
        BeanUtils.copyProperties(customsInventoryCancelDTO, customsInventoryCancelDO);
        UserUtils.setCreateAndUpdateBy(customsInventoryCancelDO);
        customsInventoryCancelMapper.insertSelective(customsInventoryCancelDO);
//        logComponent.logOpertion(LogCode.lOG_INVENTORY_CANCEL, customsInventoryCancelDO.getRefOrderSn(),
//                customsInventoryCancelDO.getOrderCancelSn(), InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(),
//                InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(), "创建撤单");
        //添加日志
//         createLog(customsInventoryCancelDO,UserUtils.getUserId(), TrackLogEnums.CANCEL_CREATE.getCode());
        if (UserUtils.getUserRealName().equalsIgnoreCase("_SYSTEM_")) {
            // 可能是因为多线程，获取用户中心的用户名丢失
            String operatorName = stringRedisTemplate.opsForValue().get("ccs:inventory:cancel:create:operator:" + customsInventoryCancelDTO.getRefOrderId());
            if (StrUtil.isNotBlank(operatorName)) {
                CurrentUserInfo currentUserInfo = new CurrentUserInfo();
                RealUserInfo realUserInfo = new RealUserInfo();
                realUserInfo.setUserName(operatorName);
                currentUserInfo.setRealUser(realUserInfo);
                SimpleUserHelper.setCurrentUserInfo(currentUserInfo);
            }
        }
        buildTrackLogEsAndSend(customsInventoryCancelDO);
    }

    @Autowired
    private TrackLogEsProducer trackLogEsProducer;

    private void buildTrackLogEsAndSend(CustomsInventoryCancelDO cancelDO) {
        this.buildTrackLogEsAndSend(cancelDO, null);
    }

    private void buildTrackLogEsAndSend(CustomsInventoryCancelDO cancelDO, String operateDesc) {
        TrackLogEsDTO trackLogEsDTO = new TrackLogEsDTO();
        trackLogEsDTO.setOrderId(cancelDO.getRefOrderId());
        OrderDTO orderDTO = orderService.findByIdFull(cancelDO.getRefOrderId());
        if (Objects.isNull(orderDTO)) {
            return;
        }
        trackLogEsDTO.setOrderSn(orderDTO.getSn());
        trackLogEsDTO.setDeclareOrderNo(cancelDO.getRefOrderSn());
        trackLogEsDTO.setInternalStatus(OrderInternalEnum.CANCELING.getCode());
        if (Objects.equals(operateDesc, TrackLogEnums.CANCEL_DECLARE_SUCCESS.getDesc())) {
            trackLogEsDTO.setInternalStatus(OrderInternalEnum.CANCEL_SUCCESS.getCode());
        } else if (Objects.equals(operateDesc, TrackLogEnums.CANCEL_DECLARE_PUSH_FAIL.getDesc()) ||
                Objects.equals(operateDesc, TrackLogEnums.CANCEL_DECLARE_OVERRULE.getDesc())) {
            trackLogEsDTO.setInternalStatus(OrderInternalEnum.CANCEL_FAIL.getCode());
        }
        trackLogEsDTO.setSender(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setReceiver(TrackLogConstantMixAll.NULL);
        trackLogEsDTO.setResult(TrackLogConstantMixAll.SUCCESS);
        if (Objects.nonNull(operateDesc)) {
            trackLogEsDTO.setEventDesc(operateDesc);
        } else {
            trackLogEsDTO.setEventDesc(TrackLogConstantMixAll.CANCEL_CREATE);
        }
        trackLogEsDTO.setEventTime(new Date());
        if (Objects.nonNull(cancelDO.getCustomsCheckDetail())) {
            trackLogEsDTO.setCustomsReceipt(cancelDO.getCustomsCheckDetail());
        }
        trackLogEsDTO.setOperator(UserUtils.getUserRealName());
        trackLogEsProducer.sendMsg(JSON.toJSONString(trackLogEsDTO), cancelDO.getRefOrderSn());
    }


    @Override
    @PageSelect
    public ListVO<CustomsInventoryCancelDTO> paging(InventoryCancelSearch search) {
        Example example = buildExample(search);
        example.orderBy("createTime").desc();
        List<CustomsInventoryCancelDO> list = customsInventoryCancelMapper.selectByExample(example);
        ListVO<CustomsInventoryCancelDTO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(list), CustomsInventoryCancelDTO.class));
        // 分页
        PageInfo<CustomsInventoryCancelDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    public List<CustomsInventoryCancelDTO> queryList(InventoryCancelSearch search) {
        Example example = buildExample(search);
        example.orderBy("createTime").desc();
        List<CustomsInventoryCancelDO> list = customsInventoryCancelMapper.selectByExample(example);
        if (list == null) list = new ArrayList<CustomsInventoryCancelDO>();
        ArrayList<CustomsInventoryCancelDTO> outOret = new ArrayList<CustomsInventoryCancelDTO>();
        Transformer<CustomsInventoryCancelDO, CustomsInventoryCancelDTO> transformer = new Transformer<CustomsInventoryCancelDO, CustomsInventoryCancelDTO>() {
            public CustomsInventoryCancelDTO transform(CustomsInventoryCancelDO input) {
                CustomsInventoryCancelDTO outObj = new CustomsInventoryCancelDTO();
                BeanUtils.copyProperties(input, outObj);
                return outObj;
            }
        };
        CollectionUtils.collect(list, transformer, outOret);
        return outOret;
    }

    @Override
    public void updateStatus(Long id, String status, Date statusTime) {
        CustomsInventoryCancelDO _info = new CustomsInventoryCancelDO();
        _info.setId(id);
        _info.setStatus(status);
        _info.setStatusTime(statusTime);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            _info.setUpdateBy(UserUtils.getUserId());
        }
        _info.setUpdateTime(new Date());
        customsInventoryCancelMapper.updateByPrimaryKeySelective(_info);
    }


    private static final String DECLARE_INVENTORY_CANCEL_LIST_KEY = "ccs:declare:inventory:cancel";

    private static final String DECLARE_INVENTORY_CANCEL_TOPIC = "ccs-declare-inventory-cancel-topic";

    @Override
    public void declareInventoryCancel(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<CustomsInventoryCancelDTO> customsInventoryCancelDTOList = this.findById(ids);
        if (CollectionUtils.isEmpty(customsInventoryCancelDTOList)) {
            return;
        }
        if (enableCancelRefundCheckInvNoDeclare) {
            // 校验是否存在重复清单编号的情况
            List<CustomsInventoryCancelDTO> customsInventoryCancelDTOS = this.findByIdList(ids);
            List<String> invSnList = customsInventoryCancelDTOS.stream().map(CustomsInventoryCancelDTO::getRefInvoSn).collect(Collectors.toList());
            List<CustomsInventoryCancelDTO> cancelDTOByInvSn = this.findByInvoSn(invSnList);
            Map<String, List<CustomsInventoryCancelDTO>> cancelDTOMapByInvNo = cancelDTOByInvSn.stream()
                    .collect(Collectors.groupingBy(CustomsInventoryCancelDTO::getInvoNo));
            List<String> duplInvoNoList = new ArrayList<>();
            cancelDTOMapByInvNo.forEach((invNo, cancelDTOList) -> {
                if (CollUtil.isNotEmpty(cancelDTOList) && cancelDTOList.size() > 1) {
                    duplInvoNoList.add(invNo);
                }
            });
            if (CollectionUtils.isNotEmpty(duplInvoNoList)) {
                throw new ArgsInvalidException("撤单存在重复的清单编号[" + String.join(",", duplInvoNoList) + "]，请处理后再推送");
            }
        }
        List<String> allowStatus = Arrays.asList(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAITING.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue());
        CustomsInventoryCancelDTO inventoryCancelDTO = customsInventoryCancelDTOList.stream()
                .filter(dto -> !allowStatus.contains(dto.getStatus())).findAny().orElse(null);
        if (Objects.nonNull(inventoryCancelDTO)) {
            throw new ArgsInvalidException("取消单号:" + inventoryCancelDTO.getOrderCancelSn() + "清单状态不能撤单");
        }
        for (CustomsInventoryCancelDTO customsInventoryCancelDTO : customsInventoryCancelDTOList) {
            String sn = (String) redisTemplate.opsForValue().get(DECLARE_INVENTORY_CANCEL_LIST_KEY + customsInventoryCancelDTO.getId());
            if (Objects.nonNull(sn)) {
                log.info("撤单号:{}正在申报中,请两分钟后刷新页面重试!", customsInventoryCancelDTO.getOrderCancelSn());
                throw new ArgsInvalidException("撤单号:" + customsInventoryCancelDTO.getOrderCancelSn() + "正在申报中,请两分钟后刷新页面重试!");
            } else {
                redisTemplate.opsForValue().set(DECLARE_INVENTORY_CANCEL_LIST_KEY + customsInventoryCancelDTO.getId(),
                        customsInventoryCancelDTO.getOrderCancelSn(), 120, TimeUnit.SECONDS);
                stringRedisTemplate.opsForValue().set("ccs:inventory:cancel:declare:operator:" + customsInventoryCancelDTO.getRefOrderId(),
                        UserUtils.getUserRealName(), 120, TimeUnit.SECONDS);
                log.info("declareInventoryCancel 发送消息 topic={} id={}", DECLARE_INVENTORY_CANCEL_TOPIC, customsInventoryCancelDTO.getId());
                messageSender.sendMsg(String.valueOf(customsInventoryCancelDTO.getId()), DECLARE_INVENTORY_CANCEL_TOPIC);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doDeclareInventoryCancel(Long id) {
        if (Objects.isNull(id)) {
            return;
        }
        CustomsInventoryCancelDTO customsInventoryCancelDTO = this.findById(id);
        customsInventoryCancelMapper.clearCustomsDetail(id);
        customsInventoryCancelHandler.handle(customsInventoryCancelDTO);
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime) {
        CustomsInventoryCancelDO _info = new CustomsInventoryCancelDO();
        _info.setId(id);
        _info.setCustomsStatus(customsStatus);
        _info.setCustomsCheckDetail(customsDetail);
        _info.setCustomsLastTime(lastCustomsTime);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        customsInventoryCancelMapper.updateByPrimaryKeySelective(_info);
    }

    @Override
    public boolean deleteByIds(List<Long> ids) {
        int count = 0;
        for (Long id : ids) {
            CustomsInventoryCancelDO _info = new CustomsInventoryCancelDO();
            _info.setId(id);
            _info.setDeleted(true);
            CustomsInventoryCancelDTO customsInventoryCancelDTO = this.findById(id);
            _info.setUpdateTime(new Date());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(_info);
            }
            count = customsInventoryCancelMapper.updateByPrimaryKeySelective(_info);
            customsInventoryCalloffService.failCallof(customsInventoryCancelDTO.getRefOrderId());
        }
        return count > 0;
    }

    @Override
    public boolean deleteByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return false;
        }
        log.info("CustomsInventoryCancelServiceImpl deleteByOrderId:{}", orderId);
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andEqualTo("refOrderId", orderId).andEqualTo("deleted", false);
        customsInventoryCancelMapper.deleteByExample(example);
        return true;
    }

    @Override
    public void retryDeclare(Long id) {
        CustomsInventoryCancelDO customsInventoryCancelDO = customsInventoryCancelMapper.selectByPrimaryKey(id);
        customsInventoryCancelDO.setCustomsStatus("2");
        customsInventoryCancelDO.setCustomsCheckDetail("申报");

        customsInventoryCancelDO.setCustomsLastTime(null);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCancelDO);
        }
        customsInventoryCancelDO.setUpdateTime(new Date());
        customsInventoryCancelMapper.updateByPrimaryKey(customsInventoryCancelDO);
        CustomsInventoryCancelDTO customsInventoryCancelDTO = new CustomsInventoryCancelDTO();
        BeanUtils.copyProperties(customsInventoryCancelDO, customsInventoryCancelDTO);
        customsInventoryCancelHandler.handle(customsInventoryCancelDTO);
    }

    /**
     * SEND_WAIT("1","电子口岸已暂存"),
     * SEND_ING("2","电子口岸申报中"),
     * SEND_SUCCESS("3","发送海关成功"),
     * SEND_FAIL("4","发送海关失败"),
     * CUSTOMS_REFUSE("100","海关退单"),
     * CUSTOMS_RECEIVE("120","海关入库"),
     * CUSTOMS_PERSON("300","人工审核"),
     * CUSTOMS_FINISH("399","海关审结"),
     * CUSTOMS_PASS("800","放行"),
     * CUSTOMS_HANG_UP("600","挂起"),
     * CLEAR_FINISH("899","结关"),
     * CLEAR_CHECK("500","查验"),
     * CLEAR_LOCK_TG("501","扣留移送通关"),
     * CLEAR_LOCK_JS("502","扣留移送缉私"),
     * CLEAR_LOCK_FG("503","扣留移送法规"),
     * CLEAR_LOCK_OT("599","其它扣留"),
     * CLEAR_REFUSE("700","退运"),
     * DEAL_EXCEPTION("-1","处理异常");
     *
     * @param receive
     * @return
     */
    @Override
    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.CANCEL_RECEIPT
    )
    public String receive(CustomsReceive receive) {
        log.info("[op:CustomsInventoryCancelServiceImpl-receive] receive,receive={}", JSON.toJSONString(receive));
        String customsStatus = receive.getCustomsStatus();
        String ebpCode = receive.getEbpCode();
        String declareOrderNo = receive.getDeclareOrderNo();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
//        CompanyDTO ebp = companyService.findByCode(ebpCode);
        CompanyDTO ebp = companyService.findByUnifiedCrossBroderCode(ebpCode);

        if (StringUtils.isEmpty(declareOrderNo)) {
            return "订单参数为空";
        }
        OrderDTO orderDTO = null;
        if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            // 浙电新接口回执无电商平台
            OrderSearch search = new OrderSearch();
            search.setCurrentPage(1);
            search.setPageSize(20);
            search.setQueryType("declareOrderNo");
            search.setQueryInfo(declareOrderNo);
            Page<OrderEsDO> paging = orderEsDao.paging(search);
            List<OrderEsDO> content = paging.getContent();
            if (CollectionUtils.isEmpty(content)) {
                return "订单不存在";
            }
            OrderEsDO next = content.iterator().next();
            orderDTO = orderService.findBySnSection(next.getSn());
        } else {
            if (ebp == null) {
                return "电商平台不存在";
            }
            orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        }
        if (orderDTO == null) {
            return "订单不存在";
        }
        boolean isJdOrder = false;
        if (Objects.nonNull(jdEbpId)) {
            isJdOrder = Objects.equals(ebp.getId(), jdEbpId);
        }

        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
        CustomsInventoryCancelDO customsInventoryCancelDO = findBydeclareOrderNo(orderDTO.getDeclareOrderNo());
        if (customsInventoryCancelDO == null) return "清单相关的撤单不存在";
        // FIXME: 2023/5/10 有严重的精度丢失问题 需要解决
        if (customsInventoryCancelDO.getCustomsLastTime() != null && new DateTime(customsTime).isBefore(new DateTime(customsInventoryCancelDO.getCustomsLastTime()))) {
            log.warn("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
            return "当前回执时间迟于最后一次回执时间，略过不做清关状态更新";
        }
        log.info("CustomsInventoryCancelServiceImpl receive customsInventoryCancelDO={}", JSON.toJSONString(customsInventoryCancelDO));
        Date date = new Date();
        date.setTime(customsTime);
        customsInventoryCancelDO.setCustomsCheckDetail(customsDetail);
        customsInventoryCancelDO.setCustomsStatus(customsStatus);
        customsInventoryCancelDO.setCustomsLastTime(date);
        //日志操作描述
//        Integer operateDes = TrackLogEnums.NULL.getCode();
        if (customsInventoryCancelDO.getStatus().equalsIgnoreCase(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue())
                || customsInventoryCancelDO.getStatus().equalsIgnoreCase(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAITING.getValue())
                || customsInventoryCancelDO.getStatus().equalsIgnoreCase(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue())
                || customsInventoryCancelDO.getStatus().equalsIgnoreCase(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue())) {
            String WATI_STATUS[] = {"1", "2", "3", "4", "120", "300", "400", "500", "505", "501", "502", "503", "599", "600", "700", "899"};
            String PASS_STATUS[] = {"399", "800"};
            if (ArrayUtils.indexOf(WATI_STATUS, customsStatus) >= 0) {
                //待人工审核
                customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue());
                customsInventoryCancelDO.setStatusTime(date);
//                operateDes = TrackLogEnums.CANCEL_DECLARE_PUSH_SUCCESS.getCode();
            } else if (ArrayUtils.indexOf(PASS_STATUS, customsStatus) >= 0) {
                //放行
                customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue());
                customsInventoryCancelDO.setStatusTime(date);
                customsInventoryCancelDO.setCompleteTime(new Date());
                CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findById(customsInventoryCancelDO.getRefInvoId());
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                if (Boolean.TRUE.equals(customsInventoryDTO.getIsOccupiedStock()) || Objects.isNull(customsInventoryDTO.getIsOccupiedStock())) {
                    //清单有占用库存 才释放
                    inventoryCancelService.handleCancel(customsInventoryItemDTOS, orderDTO.getId(), customsInventoryCancelDO, date);
                }
                //更新售后状态
                updateAfterSalesStatus(ebp, customsInventoryDTO);
                //撤单审核通过发送回执状态
                if (isJdOrder && checkJdSkipSendMsg(customsDetail, InventoryCalloffStatus.CANCEL_SUCCESS.getCode())) {
                    log.info("京东订单 declareOrderNo={}，略过京东回执发送 customsDetail={}", declareOrderNo, customsDetail);
                } else {
                    cancelOrderNotifyService.callOffNotify(orderDTO.getId(), declareOrderNo, submitChannel, InventoryCalloffStatus.CANCEL_SUCCESS, customsDetail, customsTime);
                }
                try {
                    supervisionMonitorService.changeCancelByGsNo(orderDTO.getSystemGlobalSn());
                } catch (Exception e) {
                    log.error("回传时效监管系统撤单完成失败 error={}", e.getMessage(), e);
                }
                //撤单完成回调税金回退
                messageSender.sendMsg(orderDTO.getSn(), "ccs-company-tax-cancel-pass-topic");
                log.info("[op:createMessage] 回执：海关审结or结关 撤单成功 申报单号:{} 回执:{}", orderDTO.getDeclareOrderNo(), customsDetail);
            } else if ("100".equalsIgnoreCase(customsStatus)) //海关退单
            {
                customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue());
                customsInventoryCancelDO.setStatusTime(date);
//                customsInventoryCancelDO.setCompleteTime(date);
//                operateDes = TrackLogEnums.CANCEL_DECLARE_OVERRULE.getCode();
                //撤单发送回执状态
                log.warn("[op:createMessage] 回执：总署驳回 ---- 撤单失败 主订单id - {} 回执 - {}", orderDTO.getId(), customsDetail);
                if (isJdOrder && checkJdSkipSendMsg(customsDetail, InventoryCalloffStatus.CANCEL_FAILED.getCode())) {
                    log.info("京东订单 declareOrderNo={}，略过京东回执发送  customsDetail={}", declareOrderNo, customsDetail);
                } else {
                    cancelOrderNotifyService.callOffNotify(orderDTO.getId(), declareOrderNo, submitChannel, InventoryCalloffStatus.CANCEL_FAILED, customsDetail, customsTime);
                }
            }
//            else if("4".equalsIgnoreCase(customsStatus)) //发送海关失败
//            {
//                 customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue());
//                 customsInventoryCancelDO.setStatusTime(date);
//                 customsInventoryCancelDO.setCompleteTime(date);
//            }
            else {
                if (StringUtils.isEmpty(customsStatus) || CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
                    customsStatus = "0";
                }
                if (NumberUtils.createInteger(customsStatus).intValue() <= 0) //申报异常了
                {
                    customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue());
                    customsInventoryCancelDO.setStatusTime(date);
//                    customsInventoryCancelDO.setCompleteTime(date);
//                    operateDes = TrackLogEnums.CANCEL_DECLARE_PUSH_FAIL.getCode();
                    log.warn("[op:createMessage] 回执：申报失败 ---- 撤单失败");
                    //撤单发送回执状态
                    if (isJdOrder && checkJdSkipSendMsg(customsDetail, InventoryCalloffStatus.CANCEL_FAILED.getCode())) {
                        log.info("京东订单 declareOrderNo={}，略过京东回执发送 customsDetail={}", declareOrderNo, customsDetail);
                    } else {
                        cancelOrderNotifyService.callOffNotify(orderDTO.getId(), declareOrderNo, submitChannel, InventoryCalloffStatus.CANCEL_FAILED, customsDetail, customsTime);
                    }
                } else
                    log.info("退货单 中间状态不做处理");
            }
            TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo(), null, customsInventoryCancelDO.getStatus());
        }
        this.updateOrderInternalStatus(orderDTO, customsInventoryCancelDO.getStatus());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCancelDO);
        }
        customsInventoryCancelDO.setUpdateTime(new Date());
        customsInventoryCancelMapper.updateByPrimaryKeySelective(customsInventoryCancelDO);
        //审核通过则去修改剔除单
        if (Objects.equals(customsInventoryCancelDO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
//            cullOrderService.updFinishStatus(customsInventoryCancelDO.getMailNo());
        }
        //更新取消状态
        customsInventoryCalloffService.updateCancelStatusInfo(orderDTO.getId(), customsInventoryCancelDO.getStatus(),
                customsInventoryCancelDO.getCustomsStatus(), customsInventoryCancelDO.getCustomsCheckDetail());
        // 创建操作日志
//        createLog(customsInventoryCancelDO,userId,operateDes);
        return "回传成功";
    }


    /**
     * 检查京东回执是否需要跳过发送
     *
     * @param customsDetail
     * @param status
     * @return
     */
    private boolean checkJdSkipSendMsg(String customsDetail, Integer status) {
        List<String> regexList = new ArrayList<>();
        try {
            List<String> failRegex = JSON.parseArray(jdCancelSendFailRegexJson, String.class);
            List<String> passRegex = JSON.parseArray(jdCancelSendSuccessRegexJson, String.class);
            regexList = Objects.equals(status, InventoryCalloffStatus.CANCEL_SUCCESS.getCode()) ? passRegex : failRegex;
        } catch (Exception e) {
            log.error("获取京东撤单回执跳过发送的成功和失败的正则校验失败, 请检查是否在apollo上配置！ 默认发送回执 error={}", e.getMessage(), e);
            return false;
        }
        if (CollUtil.isEmpty(regexList)) {
            log.error("获取京东撤单回执跳过发送的成功和失败的正则校验失败, 请检查是否在apollo上配置！ 默认发送回执");
            return false;
        }
        if (StringUtils.isEmpty(customsDetail)
                || !Arrays.asList(InventoryCalloffStatus.CANCEL_SUCCESS.getCode(),
                InventoryCalloffStatus.CANCEL_FAILED.getCode()).contains(status)) {
            return true;
        }
        for (String regex : regexList) {
            if (Pattern.matches(regex, customsDetail)) {
                return false;
            }
        }
        return true;
    }

    private void updateAfterSalesStatus(CompanyDTO ebp, CustomsInventoryDTO customsInventoryDTO) {
        CustomsBookResVo book = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
        if (CollUtil.contains(needCallbackCode, ebp.getUnifiedCrossBroderCode()) && book != null) {
            // 关联账册是否有需要回传标签
            List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(book.getBookTag());
            if (CollUtil.containsAny(bookTagList, needCallbackBookTag)) {
                customsInventoryService.updateAfterStatusAndCheckoutStatus(customsInventoryDTO, InventoryAfterStatus.CANCEL_SUCCESS.getValue(), InventoryCheckOutStatusEnum.REGIN_NOT_NEED.getCode());
                return;
            }
        }
        customsInventoryService.updateAfterStatus(customsInventoryDTO, InventoryAfterStatus.CANCEL_SUCCESS.getValue());
    }

    private void updateOrderInternalStatus(OrderDTO orderDTO, String cancelStatus) {
        if (Objects.isNull(cancelStatus)) {
            return;
        }
        if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue().equals(cancelStatus)) {
            cancelStatus = OrderInternalEnum.CANCELING.getCode();
        } else if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(cancelStatus)) {
            cancelStatus = OrderInternalEnum.CANCEL_SUCCESS.getCode();
        } else if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue().equals(cancelStatus)) {
            cancelStatus = OrderInternalEnum.CANCEL_FAIL.getCode();
        } else if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equals(cancelStatus)) {
            cancelStatus = OrderInternalEnum.CANCEL_FAIL.getCode();
        } else {
            return;
        }
        orderService.updateOrderInternalStatus(orderDTO, cancelStatus);
    }

    // 撤单申报完成
    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.REDUCE_OCCUPATION, handler = StockInventoryCancelHandler.class)
    public void handleCancel(List<CustomsInventoryItemDTO> customsInventoryItemDTOS, Long orderId, CustomsInventoryCancelDO customsInventoryCancelDO, Date date) {
        log.info("[CustomsInventoryCancelServiceImpl-handleCancel customsInventoryCancelDO={} date={} customsInventoryItemDTOS={}]", JSON.toJSONString(customsInventoryCancelDO), JSON.toJSONString(date), JSON.toJSONString(customsInventoryItemDTOS));
        customsInventoryCancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue());
        customsInventoryCancelDO.setStatusTime(date);
        customsInventoryCancelDO.setCompleteTime(date);

        List<UpdateInventoryDTO> list = new ArrayList<>();
        customsInventoryItemDTOS.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(customsInventoryCancelDO.getCustomsBookId())
                    .setChangeType(InventoryChangeTypeEnums.CANCEL_LIST_ORDER_APPLY_FINISH)
                    .setBusinessNo(customsInventoryCancelDO.getRefOrderSn());
            //状态设置为 撤单申报完成
            CustomsInventoryItemExtra extra = JSON.parseObject(i.getExtraJson(), CustomsInventoryItemExtra.class);
            updateInventoryDTO.setCustomsBookItemId(i.getBookItemId()).setDeclareUnitQfy(i.getCount()).setProductId(extra.getProductId()).setGoodsSeqNo(extra.getGoodsSeqNo());
            list.add(updateInventoryDTO);
        });
        stockInventoryService.updateInventory(list);
    }

    private CustomsInventoryCancelDO findBydeclareOrderNo(String declareOrderNO) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refOrderSn", declareOrderNO);
        criteria.andEqualTo("deleted", false);
        return customsInventoryCancelMapper.selectOneByExample(example);
    }

    private CustomsInventoryCancelDO findByMailNoExpressId(String mailNo, Long expressId) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("mailNo", mailNo);
        criteria.andEqualTo("expressId", expressId);
        criteria.andEqualTo("deleted", false);
        return customsInventoryCancelMapper.selectOneByExample(example);
    }

    @Transactional(propagation = Propagation.REQUIRED)
    public InventoryCancelReport importExcel(List<ImportExcelInventoryCancelDTO> list, boolean isSave) throws ArgsErrorException {
        //java.util.List<CustomsInventoryDTO> list  = new java.util.ArrayList<CustomsInventoryDTO>();

        List<ImportExcelInventoryCancelDTO> successRecordList = new ArrayList<ImportExcelInventoryCancelDTO>(),
                failRecordList = new ArrayList<ImportExcelInventoryCancelDTO>();
        List<CustomsInventoryDTO> customsInventoryDTOlist = new ArrayList<CustomsInventoryDTO>();

        List<String> moreMailNo = new ArrayList<>();
        //list.stream().map(ImportExcelInventoryCancelDTO::getMailNo).collect(Collectors.groupingBy(Function.identity(), Collectors.counting())).forEach((k, v)->{
        //     if(v>1) moreMailNo.add(k);
        //});
        int rowIdx = 1;
        for (ImportExcelInventoryCancelDTO cell : list) {
            String mailNo = cell.getMailNo();
            String expressName = cell.getExpressName();
            rowIdx++;
            cell.setIdx(rowIdx);
            if (moreMailNo.contains(mailNo)) {
                cell.setMessage("第[" + cell.getIdx() + "]行运单号重复");
                continue;
            }
            if (StringUtils.isEmpty(mailNo) || StringUtils.isEmpty(expressName)) {
                // throw new ArgsErrorException("第["+rowIdx+"]行有空值");
                cell.setMessage("第[" + cell.getIdx() + "]行有空值");
                continue;
            }
            moreMailNo.add(mailNo);
            ExpressDTO expressDTO = expressService.findOneByName(expressName);
            if (expressDTO == null) {
                cell.setMessage("第[" + cell.getIdx() + "]行快递名称查询为空");
                continue;
                //throw new ArgsErrorException("第["+rowIdx+"]行快递查询为空");
            }
            CustomsInventoryDTO inventoryDTO = customsInventoryService.findByLogistics90Days(expressDTO.getId(), mailNo);
            if (inventoryDTO == null) {
                List<CustomsInventoryDTO> listCustomsInventoryDTO = customsInventoryService.listByLogistics90Days(mailNo);
                if (CollectionUtils.isEmpty(listCustomsInventoryDTO))
                    cell.setMessage("第[" + cell.getIdx() + "]行运单号系统内不存在");
                    // throw new ArgsErrorException("第["+rowIdx+"]行无法查询到对应的清单记录");
                else
                    cell.setMessage("第[" + cell.getIdx() + "]行快递名称与运单号匹配清单记录不存在");
                continue;
            }

            if (refundOrderService.checkRefundOrderInfoIsCreate(inventoryDTO.getOrderId(), true)) {
                //throw new ArgsErrorException("申报单["+customsInventoryDTO.getDeclareOrderNo()+"]相关清单退货单已存在,不能创建清单撤单");
                cell.setMessage("第[" + cell.getIdx() + "]行存在相关有效的退货单,不能导入创建撤单");
                continue;
            }

            if (!InventoryCancelEnum.contains(inventoryDTO.getCustomsStatus(), CancelInventoryAction.INVENTORY_CANCEL_APPALY)) {
                //throw new ArgsErrorException("第["+rowIdx+"]行匹配的清单当前状态不能撤单");
                cell.setMessage("第[" + cell.getIdx() + "]行匹配的清单当前状态不能撤单");
                continue;
            }
            CustomsInventoryCancelDO customsInventoryCancelDO = findByMailNoExpressId(mailNo, expressDTO.getId());
            if (customsInventoryCancelDO != null) {
                // throw new ArgsErrorException("第["+rowIdx+"]行匹配运单号，已存在撤单记录");
                cell.setMessage("第[" + cell.getIdx() + "]行匹配运单号，已存在撤单记录");
                continue;
            }
            if (StringUtils.isEmpty(cell.getMessage())) {
                cell.setInveNo(inventoryDTO.getInventoryNo());
            }
        }
        for (ImportExcelInventoryCancelDTO cell : list) {
            if (StringUtils.isEmpty(cell.getMessage())) {
                successRecordList.add(cell);
            } else {
                failRecordList.add(cell);
            }
            if (isSave) {
                String mailNo = cell.getMailNo();
                String expressName = cell.getExpressName();
                ExpressDTO expressDTO = expressService.findOneByName(expressName);
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByLogistics90Days(expressDTO.getId(), mailNo);
                customsInventoryDTOlist.add(inventoryDTO);
            }
        }

        if (isSave && CollectionUtils.isEmpty(failRecordList)) {
            for (CustomsInventoryDTO inventoryDTO : customsInventoryDTOlist) {
                CustomsInventoryCancelDTO customsInventoryCancelDTO = buildCustomsInventoryCancelDTO(inventoryDTO, "");
                CustomsInventoryCancelDO _customsInventoryCancelDO = new CustomsInventoryCancelDO();
                BeanUtils.copyProperties(customsInventoryCancelDTO, _customsInventoryCancelDO);
                UserUtils.setCreateAndUpdateBy(_customsInventoryCancelDO);
                customsInventoryCancelMapper.insertSelective(_customsInventoryCancelDO);
//                logComponent.logOpertion(LogCode.lOG_INVENTORY_CANCEL, _customsInventoryCancelDO.getRefOrderSn(),
//                        _customsInventoryCancelDO.getOrderCancelSn(), InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(),
//                        InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(), "导入创建撤单");
                /*
                logService.logNormal(LogCode.lOG_INVENTORY_CANCEL, LogOperation.CREATE_IMPORT,
                        _customsInventoryCancelDO.getOrderCancelSn(), _customsInventoryCancelDO.getRefOrderSn(), "导入创建撤单");
                */
            }
        }
        InventoryCancelReport inventoryCancelReport = new InventoryCancelReport();
        inventoryCancelReport.setFailCount(failRecordList.size());
        inventoryCancelReport.setFailRecordList(failRecordList);
        inventoryCancelReport.setSuccessCount(successRecordList.size());
        inventoryCancelReport.setSuccessRecordList(successRecordList);
        inventoryCancelReport.setTotalCount(successRecordList.size() + failRecordList.size());
        return inventoryCancelReport;
    }

    @Override
    public boolean checkCustomsInventoryCancelIsCreate(Long orderId) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refOrderId", orderId);
        criteria.andEqualTo("deleted", false);
        criteria.andEqualTo("status", InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue());
        int count = customsInventoryCancelMapper.selectCountByExample(example);
        return count > 0;
    }

    @Override
    public boolean checkCustomsInventoryCancelIsExist(Long orderId) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria()
                .andEqualTo("refOrderId", orderId)
                .andEqualTo("deleted", false);
        int count = customsInventoryCancelMapper.selectCountByExample(example);
        return count > 0;
    }

    @Override
    public void cancelInventoryCancelOrder(CustomsInventoryCancelDTO customsInventoryCancelDTO) {
        CustomsInventoryCancelDO oldCustomsInventoryCancelDTO = customsInventoryCancelMapper.selectByPrimaryKey(customsInventoryCancelDTO.getId());
        CustomsInventoryCancelDO customsInventoryCancelDO = new CustomsInventoryCancelDO();
        BeanUtils.copyProperties(customsInventoryCancelDTO, customsInventoryCancelDO);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCancelDO);
        }
        customsInventoryCancelDO.setUpdateTime(new Date());
        customsInventoryCancelMapper.updateByPrimaryKey(customsInventoryCancelDO);
        //更新取消单状态
//        customsInventoryCalloffService.updateCancelStatusInfo(oldCustomsInventoryCancelDTO.getRefOrderId(), customsInventoryCancelDO.getStatus());
//        logComponent.logOpertion(LogCode.lOG_INVENTORY_CANCEL, customsInventoryCancelDO.getRefOrderSn(),
//                customsInventoryCancelDO.getOrderCancelSn(), InventoryCancelEnum.getEnum(oldCustomsInventoryCancelDTO.getStatus()).getDesc(),
//                InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(), "取消撤单操作");
//        createLog(customsInventoryCancelDO, UserUtils.getUserId(), TrackLogEnums.CANCEL_SUCCESS.getCode());
    }

    @Override
    public List<Map<String, Object>> sumInventoryCancelInfo(Date beginTime, Date endTime) {
        List<Map<String, Object>> list = customsInventoryCancelMapper.sumInventoryCancelInfo(beginTime, endTime);
        return list;
    }

//    @DubboReference
//    private CullOrderService cullOrderService;

    @Override
    public Response<Boolean> manualReview(List<Long> list) {
        Response<Boolean> oret;
        List<CustomsInventoryCancelDTO> tempList = new ArrayList<>();
        List<String> status = Arrays.asList(new String[]{InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue(),
                InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue()}).stream().collect(Collectors.toList());
        for (Long id : list) {
            CustomsInventoryCancelDTO customsInventoryCancelDTO = this.findById(id);
            if (!status.contains(customsInventoryCancelDTO.getStatus())) {
                oret = new Response<>(false);
                oret.setCode(-1);
                oret.setErrorMessage("申报单号: " + customsInventoryCancelDTO.getRefOrderSn() + "; 清单状态不能申报撤单.");
                return oret;
            }
            tempList.add(customsInventoryCancelDTO);
        }
        if (!CollectionUtils.isEmpty(tempList)) {
            CustomsInventoryCancelDO cancelDO = new CustomsInventoryCancelDO();
            cancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue());
            cancelDO.setCustomsStatus(CustomsStat.CUSTOMS_MANUAL_REVIEW.getValue());
            for (CustomsInventoryCancelDTO c : tempList) {
                cancelDO.setCustomsCheckDetail("清单[" + c.getInvoNo() + "]手动审核通过");
                cancelDO.setId(c.getId());
                cancelDO.setCustomsLastTime(new Date());
                cancelDO.setUpdateTime(new Date());
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(cancelDO);
                }
                customsInventoryCancelMapper.updateByPrimaryKeySelective(cancelDO);
                //撤单成功 修改取消单状态
                customsInventoryCalloffService.updateCancelStatusInfo(c.getRefOrderId(), cancelDO.getStatus(), cancelDO.getCustomsStatus(), cancelDO.getCustomsCheckDetail());
                if (Objects.nonNull(c.getMailNo())) {
                    //修改剔除单状态
//                    cullOrderService.updFinishStatus(c.getMailNo());
                }
                //放行后退回税金
                CustomsInventoryCancelDO inventoryCancelDO = customsInventoryCancelMapper.selectByPrimaryKey(c.getId());
                messageSender.sendMsg(inventoryCancelDO.getRefOrderSn(), "ccs-company-tax-cancel-pass-topic");
            }
        }
        return new Response<>(true);
    }

    @Override
    public List<CustomsInventoryCancelDTO> getCancelListByDeclareNoList(List<String> declareNoList) {
        Example example = new Example(CustomsInventoryCancelDO.class);
        example.createCriteria().andIn("refOrderSn", declareNoList);
        List<CustomsInventoryCancelDO> cancelDOS = customsInventoryCancelMapper.selectByExample(example);
        return cancelDOS.stream().map(c -> {
            CustomsInventoryCancelDTO dto = new CustomsInventoryCancelDTO();
            BeanUtils.copyProperties(c, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, CancelAndRefundAfterSaleCount> selectCancelDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result = new ConcurrentHashMap<>();

        //时间段的搜索条件
        Example example = new Example(CustomsInventoryCancelDO.class);
        Example.Criteria timeCriteria = example.createCriteria();
        timeCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        timeCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        //遍历操作人统计数量
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, timeCriteria, result);
            futureList.add(CompletableFuture.runAsync(runnable, cacnelCountThreadExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("售后统计-撤单组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("售后统计-撤单组合CompletableFuture结果异常" + e.getMessage(), e);
        }

        return result;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public Response<Boolean> editReviewStatus(CustomsInventoryCancelDTO cancelDTO) {
        Integer userId = UserUtils.getUserId();
        CustomsInventoryCancelDTO inventoryCancelDTO = this.findById(cancelDTO.getId());
        CustomsInventoryCancelDO cancelDO = BeanUtil.copyProperties(inventoryCancelDTO, CustomsInventoryCancelDO.class);
        cancelDO.setId(cancelDTO.getId());
        cancelDO.setStatus(cancelDTO.getStatus());
        cancelDO.setUpdateBy(userId);
        cancelDO.setCancelReason(OperateReasonEnums.getEnum(Integer.valueOf(cancelDTO.getCancelReason())).getDesc());
        if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL_FAIL.getValue())) {
            CustomsLogisticsStatus customsLogisticsStatus = CustomsLogisticsStatus.getEnum(cancelDTO.getManualReceipt());
            cancelDO.setCustomsStatus(customsLogisticsStatus.getValue());
            switch (customsLogisticsStatus) {
                case INVENTORY_ORDER_NO:
                case MANUAL_CHARGEBACK:
                case CANNOT_WITHDRAW_ORDER:
                    cancelDO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue());
                    //用于修改下方描述的匹配
                    cancelDTO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue());
                    break;
                default:
                    break;
            }
            customsInventoryCalloffService.updateCustomsInventoryCalloffStatusByOrderId(inventoryCancelDTO.getRefOrderId(), null, null,
                    InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
        }
        cancelDO.setUpdateTime(new Date());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(cancelDO);
        }
        // 修改清单状态
        updateCustomsInventoryStatus(cancelDO);
        //去修改撤单审核状态操作
        customsInventoryCancelMapper.updateByPrimaryKeySelective(cancelDO);
        //撤单成功 修改取消单状态
        customsInventoryCalloffService.updateCancelStatusInfo(inventoryCancelDTO.getRefOrderId(), cancelDTO.getStatus(),
                cancelDTO.getCustomsStatus(), cancelDTO.getCustomsCheckDetail());
        //手动审核通过 退还用户税金
        OrderDTO orderDTO = orderService.findByIdFull(inventoryCancelDTO.getRefOrderId());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(cancelDTO.getStatus())) {
            if (Objects.nonNull(orderDTO)) {
                messageSender.sendMsg(orderDTO.getSn(), "ccs-company-tax-cancel-pass-topic");
            }
            //修改剔除单状态撤单完成
//            cullOrderService.updFinishStatus(inventoryCancelDTO.getMailNo());

            // 构建 海关回执 生成消息 审核通过
            String declareOrderNo = orderDTO.getDeclareOrderNo();
            String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
            String customsDetail = String.format("[Code:2300;Desc:审核通过],撤单申请审核通过,清单[%s]已成功撤单", inventoryCancelDTO.getInvoNo());
            Long customsTime = (new Date()).getTime();
            cancelOrderNotifyService.callOffNotify(orderDTO.getId(), declareOrderNo, submitChannel, InventoryCalloffStatus.CANCEL_SUCCESS, customsDetail, customsTime);
        }
        if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL_FAIL.getValue().equals(cancelDTO.getStatus())
                || InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equals(cancelDTO.getStatus())) {
            // 构建 海关回执 生成消息 撤单失败
            String declareOrderNo = orderDTO.getDeclareOrderNo();
            String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
            Long customsTime = (new Date()).getTime();
            CustomsLogisticsStatus statusEnum = CustomsLogisticsStatus.getEnum(cancelDTO.getManualReceipt());
            String customsDetail = "";
            switch (statusEnum) {
                case CANNOT_WITHDRAW_ORDER: // 缴款书已生成的清单不能撤单
                    customsDetail = "[Code:13017;Desc:撤单：缴款书已生成的清单不能撤单]";
                    break;
                case INVENTORY_ORDER_NO: // 清单号不存在系统中
                    customsDetail = String.format("系统中不存在清单编号为[%s]的清单！", inventoryCancelDTO.getInvoNo());
                    break;
                case MANUAL_CHARGEBACK: // 人工退单
                    customsDetail = "[Code:1300;Desc:人工退单]";
                    break;
                case REPORTED_NUCLEAR_BET: // 该撤销申请单对应清单已报核注清单，不允许撤单
                    customsDetail = String.format("存在已申报撤销申请单[%s]，无法完成申报操作", inventoryCancelDTO.getInvoNo());
                    break;
            }
            if (StringUtils.isNotBlank(customsDetail)) {
                cancelOrderNotifyService.callOffNotify(orderDTO.getId(), declareOrderNo, submitChannel, InventoryCalloffStatus.CANCEL_FAILED, customsDetail, customsTime);
            }
        }

        cancelDO.setCustomsCheckDetail("手动操作 ：" + OperateReasonEnums.getEnum(Integer.valueOf(cancelDTO.getCancelReason())).getDesc());
        //根据提交审核状态判断操作描述
        String internalStatus = null;
        String operateDes = TrackLogEnums.NULL.getDesc();
        if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue())) {
            operateDes = TrackLogEnums.CANCEL_DECLARE_SUCCESS_INIT.getDesc();
            internalStatus = OrderInternalEnum.CANCELING.getCode();
        } else if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue())) {
            operateDes = TrackLogEnums.CANCEL_DECLARE_PUSH_SUCCESS.getDesc();
            internalStatus = OrderInternalEnum.CANCELING.getCode();
        } else if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue())) {
            operateDes = TrackLogEnums.CANCEL_DECLARE_SUCCESS.getDesc();
            internalStatus = OrderInternalEnum.CANCEL_SUCCESS.getCode();
        } else if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL_FAIL.getValue())) {
            cancelDO.setCustomsCheckDetail("手动操作:" + CustomsLogisticsStatus.getEnum(cancelDTO.getManualReceipt()).getDesc());
            operateDes = TrackLogEnums.CANCEL_DECLARE_PUSH_FAIL.getDesc();
            internalStatus = OrderInternalEnum.CANCEL_FAIL.getCode();
        } else if (Objects.equals(cancelDTO.getStatus(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue())) {
            cancelDO.setCustomsCheckDetail("手动操作:" + CustomsLogisticsStatus.getEnum(cancelDTO.getManualReceipt()).getDesc());
            operateDes = TrackLogEnums.CANCEL_DECLARE_OVERRULE.getDesc();
            internalStatus = OrderInternalEnum.CANCEL_FAIL.getCode();
        }
        if (Objects.nonNull(internalStatus)) {
            orderService.updateOrderInternalStatus(cancelDO.getRefOrderId(), internalStatus);
        }
        // 添加日志
//        createLog(cancelDO, userId, operateDes);
        this.buildTrackLogEsAndSend(cancelDO, operateDes);
        return new Response<>(true);
    }

    private void updateCustomsInventoryStatus(CustomsInventoryCancelDO cancelDO) {
        // 审核通过才更新
        if (!InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equals(cancelDO.getStatus())) {
            return;
        }
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findBySnSection(cancelDO.getRefInvoSn());
        if (Objects.isNull(inventoryDTO)) {
            log.warn("[撤单更新清单状态]对应清单不存在，撤单id：{}", cancelDO.getId());
            return;
        }
        CompanyDTO ebpCompany = companyService.findById(inventoryDTO.getEbpId());
        if (Objects.isNull(ebpCompany)) {
            log.warn("[撤单更新清单状态]清单对应电商平台不存在，清单sn：{}, 平台id：{}", inventoryDTO.getSn(), inventoryDTO.getEbpId());
            return;
        }
        CustomsBookResVo book = customsBookService.findByIdV2(inventoryDTO.getAccountBookId());
        customsInventoryBaseService.updateAfterStatus(ebpCompany, book, inventoryDTO, InventoryAfterStatus.CANCEL_SUCCESS.getValue(), InventoryCheckOutStatusEnum.REGIN_NOT_NEED.getCode());
    }


    class Task extends TraceDataRunnable {
        private final String handlerId;

        private final Example.Criteria timeCriteria;

        private ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result;

        public Task(String handlerId, final Example.Criteria timeCriteria, final ConcurrentHashMap<String, CancelAndRefundAfterSaleCount> result) {
            super();
            this.handlerId = handlerId;
            this.timeCriteria = timeCriteria;
            this.result = result;
        }


        @Override
        public void proxy() {

            CancelAndRefundAfterSaleCount cancelAfterSaleCount = new CancelAndRefundAfterSaleCount();
            cancelAfterSaleCount.setUserId(handlerId);

            //需申报 - 所有状态+时间段
            Example example1 = new Example(CustomsInventoryCancelDO.class);
            example1.createCriteria().andEqualTo("deleted", false);
            example1.and(timeCriteria);
            cancelAfterSaleCount.setInitDeclare(customsInventoryCancelMapper.selectCountByExample(example1));

            //处理申报 - 申报中+待总署审核+时间段
            List<String> processingStatus = Arrays.asList(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAITING.getValue()
                    , InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_WAIT.getValue());
            Example example2 = new Example(CustomsInventoryCancelDO.class);
            example2.createCriteria().andEqualTo("deleted", false)
                    .andIn("status", processingStatus)
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example2.and(timeCriteria);
            cancelAfterSaleCount.setProcessingDeclare(customsInventoryCancelMapper.selectCountByExample(example2));

            //申报成功 - 审核通过+时间段
            Example example3 = new Example(CustomsInventoryCancelDO.class);
            example3.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("status", InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(timeCriteria);
            cancelAfterSaleCount.setSuccessfulDeclare(customsInventoryCancelMapper.selectCountByExample(example3));

            //申报失败 - 申报失败+总署驳回+时间段
            List<String> failedDeclare = Arrays.asList(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue()
                    , InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue());
            Example example4 = new Example(CustomsInventoryCancelDO.class);
            example4.createCriteria().andEqualTo("deleted", false)
                    .andIn("status", failedDeclare)
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example4.and(timeCriteria);
            cancelAfterSaleCount.setFailedDeclare(customsInventoryCancelMapper.selectCountByExample(example4));

            //取消撤单 - 取消撤单+时间段
            Example example5 = new Example(CustomsInventoryCancelDO.class);
            example5.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("status", InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example5.and(timeCriteria);
            cancelAfterSaleCount.setCancel(customsInventoryCancelMapper.selectCountByExample(example5));

            result.put(handlerId, cancelAfterSaleCount);
        }
    }

    @Deprecated
    private void createLog(CustomsInventoryCancelDO cancelDO, Integer userId, Integer operateDes) {

        TrackLogDTO trackLogDTO = new TrackLogDTO();
        trackLogDTO.setCreateBy(userId);
        trackLogDTO.setDeclareOrderNo(cancelDO.getRefOrderSn());
        trackLogDTO.setAuditStatus(cancelDO.getStatus());
        trackLogDTO.setInventoryNo(cancelDO.getInvoNo());
        trackLogDTO.setOperateDes(operateDes);
        trackLogDTO.setOrderType(InventoryOrderType.CANCEL_AN_ORDER.getCode());
        trackLogDTO.setLogDes(cancelDO.getCustomsCheckDetail());
        trackLogDTO.setCreateTime(new Date());
        trackLogDTO.setNewStatus(OrderStatus.NULL.getValue());
        trackLogDTO.setOldStatus(OrderStatus.NULL.getValue());
        trackLogService.submit(trackLogDTO);
    }

    /**
     * 将所有测单数据同步到清单es上
     */
    @Test
    public void syncAllCancelInfo2ES() {
        Connection connection = null;
        Statement statement = null;
        ResultSet resultSet = null;

        try {
            // 1. 创建数据库连接
            connection = DriverManager.getConnection("******************************************", "query", "Dd82727893!p");

            // 2. 创建 Statement
            statement = connection.createStatement(ResultSet.TYPE_FORWARD_ONLY, ResultSet.CONCUR_READ_ONLY);
            statement.setFetchSize(2000);

            // 3. 执行查询
            resultSet = statement.executeQuery("SELECT * FROM ccs_customs_inventory_cancel_info");
            RestHighLevelClient client = getClient();
            // 4. 处理结果集
            while (resultSet.next()) {
                int row = resultSet.getRow();
                if (row % 1000 == 0) {
                    System.out.println("休息一会儿");
                    Thread.sleep(500L);
                }
                System.out.println("row=" + row);
                // 读取每行数据
                Long id = resultSet.getLong("id");
                String refInvoSn = resultSet.getString("ref_invo_sn");
                String status = resultSet.getString("status");
                Timestamp createTime = resultSet.getTimestamp("create_time");
                // 处理数据...
                System.out.println(id + "---" + refInvoSn);
                try {
                    UpdateByQueryRequest request = new UpdateByQueryRequest("ccs_customs_inventory_index_*");
                    request.setQuery(new TermQueryBuilder("sn.keyword", refInvoSn));
                    request.setScript(new Script("ctx._source['cancelStatus']='" + status + "'" + ";ctx._source['cancelCreateTime']='" + createTime.getTime() + "'"));
                    request.setRefresh(true);
                    BulkByScrollResponse response = client.updateByQuery(request, RequestOptions.DEFAULT);
                    long total = response.getTotal();
                    System.out.println("total=" + total);
                } catch (Exception e) {
                    log.error("error={}", e.getMessage(), e);
                }
                // 判断是否还有下一行
                if (!resultSet.isLast()) {
                    // 还有下一行，继续处理
                    System.out.println("next..");
                } else {
                    // 没有下一行了，退出循环
                    break;
                }
            }
            System.out.println("处理完毕");
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        } finally {
            // 5. 关闭资源
            try {
                if (resultSet != null) {
                    resultSet.close();
                }
                if (statement != null) {
                    statement.close();
                }
                if (connection != null) {
                    connection.close();
                }
            } catch (SQLException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 获取esClient
     *
     * @return
     */
    public RestHighLevelClient getClient() {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "Qw$12345678"));
        RestClientBuilder builder = RestClient.builder(HttpHost.create("http://es716.yang800.com.cn"))
                .setRequestConfigCallback(request -> request.setConnectTimeout(15 * 1000).setSocketTimeout(60 * 1000))
                .setHttpClientConfigCallback(client -> client.setDefaultCredentialsProvider(credentialsProvider));
        return new RestHighLevelClient(builder);
    }

}
