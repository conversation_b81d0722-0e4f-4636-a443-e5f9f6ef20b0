package com.danding.cds.service.customs;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.ExceptionService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.declare.sdk.utils.DateUtils;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.enums.DeclareItemStatusEnums;
import com.danding.cds.order.api.enums.MapStatusEnums;
import com.danding.cds.order.api.vo.CustomsStatusMappingAddReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingResVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingUpdReqVO;
import com.danding.cds.order.base.bean.dao.CustomsStatusMappingDO;
import com.danding.cds.order.base.data.service.BaseDataInitService;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsStatusMappingMapper;
import com.danding.cds.monitor.MappingEmptyEvent;
import com.danding.cds.utils.CustomsStatusMappingUtil;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.google.common.eventbus.EventBus;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.NumberUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

@Slf4j
@Service
public class CustomsStatusMappingServiceImpl implements CustomsStatusMappingService {


    @Autowired
    private CustomsStatusMappingMapper customsStatusMappingMapper;

    @Resource
    private ExceptionService exceptionService;

    @Autowired
    private EventBus eventBus;

    @Override
    public List<CustomsStatusMappingDTO> listByException(Boolean exceptionFlag) {
        CustomsStatusMappingDO condition = new CustomsStatusMappingDO();
        condition.setExceptionFlag(exceptionFlag);
        return customsStatusMappingMapper.select(condition).stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public CustomsStatusMappingDTO findByCode(String code) {
        return this.buildDTO(customsStatusMappingMapper.findByCode(code));
    }

    @Override
    public ExceptionDTO findExceptionByCustomsDetail(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum) {
        CustomsStatusMappingDTO statusMappingDTO = findOnlyByCustomsCallback(customsStatus, customsDetail, routeActionEnum);
        if (statusMappingDTO == null) {
            return null;
        }
        Long exceptionId = statusMappingDTO.getExceptionId();
        if (exceptionId == null) {
            return null;
        }
        return exceptionService.findById(exceptionId);
    }


    @Override
    public CustomsStatusMappingDTO findOnlyByCustomsCallback(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum) {

        String mappingCode = CustomsStatusMappingUtil.getStatusMappingCode(customsStatus, customsDetail, routeActionEnum);
        CustomsStatusMappingDO mappingDO = customsStatusMappingMapper.findByCode(mappingCode);
        if (mappingDO == null) {
            return null;
        }
        return this.buildDTO(mappingDO);
    }

    @Override
    public CustomsStatusMappingDTO findByCustomsCallback(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum) {

        String mappingCode = CustomsStatusMappingUtil.getStatusMappingCode(customsStatus, customsDetail, routeActionEnum);
        CustomsStatusMappingDTO mappingDTO = this.findByCode(mappingCode);
        // Step::mapping不存在报警
        if (mappingDTO == null) {
            Map<String, String> receive = new HashMap<>();
            receive.put("customsStatus", customsStatus);
            receive.put("customsDetail", customsDetail);
            eventBus.post(new MappingEmptyEvent(mappingCode, JSON.toJSONString(receive)));
            mappingDTO = this.findByCode("UN_KNOW");
        } else {
            mappingDTO = this.getFinalMappingCode(mappingDTO, customsDetail);
        }
        // Step::申报单及申报项状态处理
        return mappingDTO;
    }


    @Override
    public CustomsStatusMappingDTO findById(Long id) {
        return this.buildDTO(customsStatusMappingMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<CustomsStatusMappingDTO> findByIds(List<Long> idList) {

        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        Example example = new Example(CustomsStatusMappingDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", idList);
        List<CustomsStatusMappingDO> statusMappingDOS = customsStatusMappingMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(statusMappingDOS)) {
            return Collections.emptyList();
        }
        return statusMappingDOS.stream()
                .map(z -> this.buildDTO(z)).collect(Collectors.toList());
    }

    /**
     * 查询待处理数量
     *
     * @return
     */
    @Override
    public Integer findPendingCount() {
        Example example = new Example(CustomsStatusMappingDO.class);
        example.createCriteria().andEqualTo("mapStatus", MapStatusEnums.PENDING.getValue());
        List<CustomsStatusMappingDO> list = customsStatusMappingMapper.selectByExample(example);
        return list.size();
    }

    /**
     * 根据海关状态获取回执
     *
     * @param code
     * @return
     */
    @Override
    public List<CustomsStatusMappingDTO> findByCustomsCodeAndException(String code, Long exception) {
        Example example = new Example(CustomsStatusMappingDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(code)) {
            criteria.andEqualTo("customsStatusCode", code);
        }
        if (Objects.nonNull(exception)) {
            criteria.andEqualTo("exceptionId", exception);
        }
        List<CustomsStatusMappingDO> mappingDOList = customsStatusMappingMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(mappingDOList)) {
            return JSON.parseArray(JSON.toJSONString(mappingDOList), CustomsStatusMappingDTO.class);
        }
        return new ArrayList<>();
    }

    @Override
    public List<CustomsStatusMappingDTO> findByCustomsCodeAndException(List<String> code, List<Long> exception) {
        Example example = new Example(CustomsStatusMappingDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(code)) {
            criteria.andIn("customsStatusCode", code);
        }
        if (!CollectionUtils.isEmpty(exception)) {
            criteria.andIn("exceptionId", exception);
        }
        List<CustomsStatusMappingDO> mappingDOList = customsStatusMappingMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(mappingDOList)) {
            return JSON.parseArray(JSON.toJSONString(mappingDOList), CustomsStatusMappingDTO.class);
        }
        return new ArrayList<>();
    }

    /**
     * 回执映射分页查询
     *
     * @param reqVO
     * @return
     */
    @Override
    @PageSelect
    public ListVO<CustomsStatusMappingResVO> paging(CustomsStatusMappingReqVO reqVO) {
        Example example = new Example(CustomsStatusMappingDO.class);
        example.orderBy("id").desc();
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(reqVO.getCode())) {
            criteria.andEqualTo("code", reqVO.getCode());
        }
        if (Objects.nonNull(reqVO.getAction())) {
            criteria.andEqualTo("action", reqVO.getAction());
        }
        if (Objects.nonNull(reqVO.getCustomsStatusCode())) {
            criteria.andEqualTo("customsStatusCode", reqVO.getCustomsStatusCode());
        }
        if (Objects.nonNull(reqVO.getDetailCode())) {
            criteria.andEqualTo("detailCode", reqVO.getDetailCode());
        }
        if (Objects.nonNull(reqVO.getMapStatus())) {
            criteria.andEqualTo("mapStatus", reqVO.getMapStatus());
        }
        if (Objects.nonNull(reqVO.getNote())) {
            criteria.andLike("note", "%" + reqVO.getNote() + "%");
        }
        if (Objects.nonNull(reqVO.getStatus())) {
            criteria.andEqualTo("status", reqVO.getStatus());
        }
        if (Objects.nonNull(reqVO.getExceptionFlag())) {
            criteria.andEqualTo("exceptionFlag", reqVO.getExceptionFlag());
        }
        if (Objects.nonNull(reqVO.getStaTime())) {

            criteria.andBetween("createTime", DateUtils.timeMillisToDate(reqVO.getStaTime()), DateUtils.timeMillisToDate(reqVO.getEndTime()));
        }
        if (Objects.nonNull(reqVO.getFinalReceiptFlag())) {
            criteria.andEqualTo("finalReceiptFlag", reqVO.getFinalReceiptFlag());
        }
        example.and(criteria);
        List<CustomsStatusMappingDO> mappingDOList = customsStatusMappingMapper.selectByExample(example);

        List<CustomsStatusMappingResVO> resVOS = mappingDOList.stream().map(i -> {
            CustomsStatusMappingResVO resVO = new CustomsStatusMappingResVO();
            BeanUtils.copyProperties(i, resVO);
            resVO.setId(i.getId().toString());
            if (Objects.nonNull(i.getCustomsStatusCode())) {
                if (NumberUtil.isNumber(i.getCustomsStatusCode())) {
                    resVO.setCustomsStatusCodeStr(i.getCustomsStatusCode() + "-" + CustomsStat.getEnum(i.getCustomsStatusCode()).getDesc());
                }
            }
            if (Objects.nonNull(i.getAction())) {
                resVO.setActionStr(RouteActionEnum.getEnum(i.getAction()).getDesc());
            }
            if (Objects.nonNull(i.getStatus())) {
                resVO.setStatusStr(DeclareItemStatusEnums.getEnum(i.getStatus()).getDesc());
            }
            if (Objects.nonNull(i.getMapStatus())) {
                resVO.setMapStatusStr(MapStatusEnums.getEnum(i.getMapStatus()).getDesc());
            }
            if (Objects.nonNull(i.getExceptionId()) && !Objects.equals(i.getExceptionId(), 0L)) {
                resVO.setExceptionId(i.getExceptionId());
                ExceptionDTO exceptionDTO = exceptionService.findById(i.getExceptionId());
                if (exceptionDTO != null) {
                    resVO.setExceptionName(exceptionDTO.getExceptionName());
                }
            }
            if (StringUtils.isNotBlank(i.getRegexCheckListJSON())) {
                List<CustomsStatusMappingDTO.RegexCheck> regexChecks = JSON.parseArray(i.getRegexCheckListJSON(), CustomsStatusMappingDTO.RegexCheck.class);
                resVO.setRegexCheckList(regexChecks);
            }
            return resVO;
        }).collect(Collectors.toList());
        ListVO<CustomsStatusMappingResVO> result = new ListVO<>();
        result.setDataList(resVOS);
        // 分页
        PageInfo<CustomsStatusMappingDO> pageInfo = new PageInfo(mappingDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    /**
     * 回执映射下拉
     *
     * @return
     */
    public List<CustomsStatusMappingDTO> getCustomsReceipt() {
        Example example = new Example(CustomsStatusMappingDO.class);
        example.createCriteria().andIsNotNull("note").andNotEqualTo("note", "").andEqualTo("deleted", false);
        List<CustomsStatusMappingDO> mappingDOList = customsStatusMappingMapper.selectByExample(example);
        List<CustomsStatusMappingDTO> customsStatusMappingDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(mappingDOList)) {
            customsStatusMappingDTOList = mappingDOList.stream().map(i -> {
                CustomsStatusMappingDTO mappingDTO = new CustomsStatusMappingDTO();
                BeanUtils.copyProperties(i, mappingDTO);
                return mappingDTO;
            }).collect(Collectors.toList());
        }
        return customsStatusMappingDTOList;
    }

    /**
     * 新增回执映射
     *
     * @param reqVO
     */
    @Override
    public void save(CustomsStatusMappingAddReqVO reqVO) throws ArgsErrorException {
        CustomsStatusMappingDTO mappingDTO = BeanUtil.copyProperties(reqVO, CustomsStatusMappingDTO.class);
        mappingDTO.setCode(reqVO.getAction() + ":" + reqVO.getCustomsStatusCode() + ":" + reqVO.getDetailCode());
        create(mappingDTO);
    }

    public CustomsStatusMappingDO findByActionAndCustomsStatusCodeAndDetailCode(String action, String customsStatusCode, String detailCode) {
        Example example = new Example(CustomsStatusMappingDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(action)) {
            criteria.andEqualTo("action", action);
        }
        if (Objects.nonNull(customsStatusCode)) {
            criteria.andEqualTo("customsStatusCode", customsStatusCode);
        }
        if (Objects.nonNull(detailCode)) {
            criteria.andEqualTo("detailCode", detailCode);
        }
        return customsStatusMappingMapper.selectOneByExample(example);
    }

    @Override
    public Long create(CustomsStatusMappingDTO customsStatusMappingDTO) throws ArgsErrorException {
        log.warn("创建回执映射--");
        try {
            CustomsStatusMappingDO customsStatusMappingDO = new CustomsStatusMappingDO();
            BeanUtils.copyProperties(customsStatusMappingDTO, customsStatusMappingDO);
            Integer userId = UserUtils.getUserId();
            customsStatusMappingDO.setCreateBy(userId);
            customsStatusMappingDO.setUpdateBy(userId);
            customsStatusMappingDO.setCreateTime(new Date());
            customsStatusMappingDO.setMapStatus(MapStatusEnums.PENDING.getValue());
            if (CollectionUtil.isNotEmpty(customsStatusMappingDTO.getRegexCheckList())) {
                customsStatusMappingDO.setRegexCheckListJSON(JSON.toJSONString(customsStatusMappingDTO.getRegexCheckList()));
            }
            log.warn("customsStatusMappingDTO 创建回执映射 - {}", customsStatusMappingDTO);
            customsStatusMappingMapper.insertSelective(customsStatusMappingDO);
            return customsStatusMappingDO.getId();
        } catch (Exception e) {
            Throwable cause = e.getCause();
            if (cause instanceof java.sql.SQLIntegrityConstraintViolationException) {
                //错误信息
                String errMsg = ((java.sql.SQLIntegrityConstraintViolationException) cause).getMessage();
                if (StringUtils.isNotBlank(errMsg) && errMsg.indexOf("ccs_code") != -1) {
                    log.error("申报项 - {} - 重复", customsStatusMappingDTO.getAction());
                    throw new ArgsErrorException("申报项 - {} - 重复" + customsStatusMappingDTO.getAction());
                }
            }
            return 0L;
        }

    }

    @Autowired
    private BaseDataInitService baseDataInitService;
    @Autowired
    private BaseDataService baseDataService;

    /**
     * 修改回执映射
     *
     * @param reqVO
     * @throws ArgsErrorException
     */
    @Override
    public void edit(CustomsStatusMappingUpdReqVO reqVO) throws ArgsErrorException {
        if (Objects.isNull(reqVO.getReceiptExplain())) {
            throw new ArgsErrorException("映射说明不能为空！");
        }
        if (Objects.isNull(reqVO.getStatus())) {
            throw new ArgsErrorException("请选择申报项状态！");
        }
        if (Objects.isNull(reqVO.getExceptionFlag())) {
            throw new ArgsErrorException("请选择异常状态！");
        }
        CustomsStatusMappingDTO mappingDTO = BeanUtil.copyProperties(reqVO, CustomsStatusMappingDTO.class);
        String newCode = reqVO.getAction() + ":" + reqVO.getCustomsStatusCode() + ":" + reqVO.getDetailCode();
        mappingDTO.setCode(newCode);
        mappingDTO.setMapStatus(MapStatusEnums.FINISH.getValue());
        if (Objects.nonNull(reqVO.getExceptionFlag())) {
            mappingDTO.setExceptionFlag(reqVO.getExceptionFlag());
            mappingDTO.setExceptionId(0L);
            if (Objects.nonNull(reqVO.getExceptionId()) && Boolean.TRUE.equals(reqVO.getExceptionFlag())) {
                mappingDTO.setExceptionId(reqVO.getExceptionId());
            }
        }
        update(mappingDTO);
    }

    @Override
    public Long update(CustomsStatusMappingDTO customsStatusMappingDTO) {
        CustomsStatusMappingDO customsStatusMappingDO = new CustomsStatusMappingDO();
        BeanUtils.copyProperties(customsStatusMappingDTO, customsStatusMappingDO);
        Integer userId = UserUtils.getUserId();
        customsStatusMappingDO.setUpdateBy(userId);
        customsStatusMappingDO.setUpdateTime(new Date());
        if (CollectionUtil.isNotEmpty(customsStatusMappingDTO.getRegexCheckList())) {
            customsStatusMappingDO.setRegexCheckListJSON(JSON.toJSONString(customsStatusMappingDTO.getRegexCheckList()));
        }
        customsStatusMappingMapper.updateByPrimaryKeySelective(customsStatusMappingDO);
        CustomsStatusMappingDTO statusMappingDTO = baseDataService.getCustomsStatusMappingDTOById(customsStatusMappingDTO.getId());
        if (statusMappingDTO != null) {
            CustomsStatusMappingDTO mappingDTO = this.findById(customsStatusMappingDO.getId());

            //同步缓存
            baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING, mappingDTO);
        }
        return customsStatusMappingDO.getId();
    }


    /**
     * 删除回执映射
     *
     * @param reqVO
     */
    @Override
    public void delete(CustomsStatusMappingUpdReqVO reqVO) {
        Example example = new Example(CustomsStatusMappingDO.class);
        example.createCriteria().andEqualTo("id", reqVO.getId());
        customsStatusMappingMapper.deleteByPrimaryKey(example);
    }

    @Override
    public CustomsStatusMappingDTO getFinalMappingCode(CustomsStatusMappingDTO mappingDTO, String customsDetail) {
        if (Objects.isNull(mappingDTO)) {
            return null;
        }
        if (Boolean.TRUE.equals(mappingDTO.getFinalReceiptFlag())) {
            List<CustomsStatusMappingDTO.RegexCheck> regexCheckList = mappingDTO.getRegexCheckList();
            if (CollectionUtil.isNotEmpty(regexCheckList)) {
                for (CustomsStatusMappingDTO.RegexCheck regexCheck : regexCheckList) {
                    if (Pattern.compile(regexCheck.getRegex()).matcher(customsDetail).find()) {
                        log.info("回执映射正则匹配成功,新映射mappingCode={}", regexCheck.getMappingCode());
                        mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode(regexCheck.getMappingCode());
                        break;
                    }
                }
            }
        }
        return mappingDTO;
    }

    @Override
    public List<CustomsStatusMappingDTO> listAll() {
        CustomsStatusMappingDO template = new CustomsStatusMappingDO();
        List<CustomsStatusMappingDO> doList = customsStatusMappingMapper.select(template);
        if (CollectionUtils.isEmpty(doList)) {
            return new ArrayList<>();
        }
        return doList.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    public CustomsStatusMappingDTO buildDTO(CustomsStatusMappingDO model) {
        if (model == null) {
            return null;
        } else {
            CustomsStatusMappingDTO result = new CustomsStatusMappingDTO();
            BeanUtils.copyProperties(model, result);
            if (StringUtils.isNotBlank(model.getRegexCheckListJSON())) {
                result.setRegexCheckList(JSON.parseArray(model.getRegexCheckListJSON(), CustomsStatusMappingDTO.RegexCheck.class));
            }
            return result;
        }
    }
}
