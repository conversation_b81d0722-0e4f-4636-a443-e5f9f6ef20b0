package com.danding.cds.service.declareImpl.order;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 拼多多CEB代理订单申报
 * @author: 潘本乐（Belep）
 * @create: 2022-02-16 10:33
 **/
@Service("PDD_CEB_PROXY_ORDER_DECLARE")
@Slf4j
public class PddCebProxyOrderDeclareImpl extends OrderDeclareAbstract {

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    protected OrderDeclareResult mockDeclareTest(WrapOrderDeclareInfo info) {
        log.info("拼多多CEB订单代理申报，测试环境MOCK");
        pddDeclareService.cloudOfficialDeclareTest(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB311);
        return null;
    }

    @Override
    protected OrderDeclareResult declare(WrapOrderDeclareInfo info) {

        pddDeclareService.cloudOfficialDeclare(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB311);
        return null;
    }
}
