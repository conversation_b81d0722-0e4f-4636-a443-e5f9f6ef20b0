package com.danding.cds.service.declareImpl.cancel;

import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.base.component.cancel.impl.HzCenterInventoryCancelDeclareImpl;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 字节CEB撤单代理申报杭数版
 **/
@Service("BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC")
@Slf4j
public class ByteDanceCebProxyCancelDeclareHZDCImpl extends InventoryCancelDeclareAbstract {

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;
    @Autowired
    private HzCenterInventoryCancelDeclareImpl hzCenterInventoryCancelDeclare;
    @Resource
    private OrderService orderService;

    @Override
    protected InventoryCancelResult mockDeclareTest(WarpCancelOrderInfo info) {
        log.info("字节CEB撤单代理申报 测试环境MOCK info={}", JSON.toJSONString(info));
        CompanyInfo ebpCompany = info.getEbpCompanyDTO();
        if (Objects.isNull(ebpCompany)) {
            throw new ArgsInvalidException("电商平台信息为空");
        }
        String mainOrderSn = info.getMainOrderSn();
        OrderDTO orderDTO = orderService.findBySnSection(mainOrderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("申报单信息不存在");
        }
        //取出申报记录
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        if (Objects.nonNull(declareWayRecord)) {
            List<RouteDeclareConfig> declareWayRecordList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            RouteDeclareConfig declareConfig = declareWayRecordList.stream().filter(d -> Objects.equals(d.getType(), DeclareEnum.INVENTORY_REFUND.getType())).findAny().orElse(null);
            if (Objects.nonNull(declareConfig)) {
                RouteInfo routeInfo = info.getRouteInfo();
                if (Objects.nonNull(routeInfo)) {
                    //替换路径上的配置
                    List<RouteDeclareConfig> routeDeclareConfigList = routeInfo.getRouteDeclareConfigList();
                    for (RouteDeclareConfig config : routeDeclareConfigList) {
                        if (Objects.equals(config.getType(), DeclareEnum.INVENTORY_REFUND.getType())) {
                            log.info("申报单:{} 此处替换declareImpl 从{} 换成 {}", orderDTO.getDeclareOrderNo(), config.getDeclareImpl(), declareConfig.getDeclareImpl());
                            //云内指定使用的是declareImpl
                            config.setDeclareImpl(declareConfig.getDeclareImpl());
                        }
                    }
                }
            }
        }
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
            log.info("ByteDanceCebProxyCancelDeclareHZDCImpl 测试环境MOCK 撤单 申报单为加密订单走云内申报 单号:{}", orderDTO.getDeclareOrderNo());
            byteDanceDeclareService.cloudDeclareMock(info);
        } else {
            log.info("ByteDanceCebProxyCancelDeclareHZDCImpl 测试环境MOCK 撤单 申报单不走云内申报 走杭州数据分中心 单号:{}", orderDTO.getDeclareOrderNo());
            hzCenterInventoryCancelDeclare.invoke(info);
        }
        return null;
    }

    @Override
    protected InventoryCancelResult declare(WarpCancelOrderInfo info) {
        log.info("字节CEB撤单代理申报杭数 info={}", JSON.toJSONString(info));
        CompanyInfo ebpCompany = info.getEbpCompanyDTO();
        if (Objects.isNull(ebpCompany)) {
            throw new ArgsInvalidException("电商平台信息为空");
        }
        String mainOrderSn = info.getMainOrderSn();
        OrderDTO orderDTO = orderService.findBySnSection(mainOrderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("申报单信息不存在");
        }
        //取出申报记录
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        if (Objects.nonNull(declareWayRecord)) {
            List<RouteDeclareConfig> declareWayRecordList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            RouteDeclareConfig declareConfig = declareWayRecordList.stream().filter(d -> Objects.equals(d.getType(), DeclareEnum.INVENTORY_REFUND.getType())).findAny().orElse(null);
            if (Objects.nonNull(declareConfig)) {
                RouteInfo routeInfo = info.getRouteInfo();
                if (Objects.nonNull(routeInfo)) {
                    //替换路径上的配置
                    List<RouteDeclareConfig> routeDeclareConfigList = routeInfo.getRouteDeclareConfigList();
                    for (RouteDeclareConfig config : routeDeclareConfigList) {
                        if (Objects.equals(config.getType(), DeclareEnum.INVENTORY_REFUND.getType())) {
                            log.info("申报单:{} 此处替换declareImpl 从{} 换成 {}", orderDTO.getDeclareOrderNo(), config.getDeclareImpl(), declareConfig.getDeclareImpl());
                            //云内指定使用的是declareImpl
                            config.setDeclareImpl(declareConfig.getDeclareImpl());
                        }
                    }
                }
            }
        }
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
            log.info("ByteDanceCebProxyCancelDeclareHZDCImpl 撤单 申报单为加密订单走云内申报 单号:{}", orderDTO.getDeclareOrderNo());
            byteDanceDeclareService.cloudCancelDeclare(info);
        } else {
            log.info("ByteDanceCebProxyCancelDeclareHZDCImpl 撤单 申报单不走云内申报 走杭州数据分中心 单号:{}", orderDTO.getDeclareOrderNo());
            hzCenterInventoryCancelDeclare.invoke(info);
        }
        return null;
    }
}
