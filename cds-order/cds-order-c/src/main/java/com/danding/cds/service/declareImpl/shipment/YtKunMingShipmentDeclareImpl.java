package com.danding.cds.service.declareImpl.shipment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.service.customs.declare.YuanTongDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * 圆通 昆明地区的实现
 * <AUTHOR>
 */
@Slf4j
@Service("YT_KUNMING_SHIPMENT_DECLARE")
public class YtKunMingShipmentDeclareImpl extends ShipmentDeclareAbstract {
    @Autowired
    private YuanTongDeclareService yuanTongDeclareService;
    @Autowired
    private CustomsLogisticsService customsLogisticsService;
    @Autowired
    private OrderService orderService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("YtKunMingShipmentDeclareImpl mock接口 昆明 圆通运单申报 info={}", JSON.toJSON(info));
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("YtKunMingShipmentDeclareImpl 昆明 圆通运单申报 info={}", JSON.toJSON(info));
        try {
            CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findByLogisticsNo(info.getLogisticsNo());
            OrderDTO orderDTO = orderService.findBySnSection(logisticsDTO.getOrderSn());
            yuanTongDeclareService.logisticsDeclare(info,logisticsDTO,orderDTO);
        } catch (Exception e) {
            log.error("YtKunMingShipmentDeclareImpl 昆明 圆通运单申报 {}",info.getLogisticsNo(),e);
            throw new RuntimeException(e.getMessage());
        }
        return null;
    }
}
