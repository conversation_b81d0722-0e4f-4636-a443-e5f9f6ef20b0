package com.danding.cds.handler;

import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.track.log.interfaces.TrackLogParametersHandler;
import com.danding.cds.track.log.utils.TrackLogUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 取消的轨迹日志记录
 * @date 2022/6/8 15:22
 */
@Component
public class CancelTrackLogParametersHandler implements TrackLogParametersHandler {
    @Resource
    private CustomsInventoryService customsInventoryService;

    @Override
    public TrackLogInfoDto handle(TrackLog trackLog, Object[] args) throws Exception {
        TrackLogInfoDto trackLogInfoDto = new TrackLogInfoDto();
        int infoIndex = trackLog.infoIndex();
        Object info = args[infoIndex];
        OrderDTO orderDTO = (OrderDTO) info;
        trackLogInfoDto.setOrderId(orderDTO.getId());
        trackLogInfoDto.setOrderSn(orderDTO.getSn());
        trackLogInfoDto.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        trackLogInfoDto.setSender(trackLog.sender());
        trackLogInfoDto.setReceiver(TrackLogConstantMixAll.DT_CCS);
        trackLogInfoDto.setInternalStatus(OrderInternalEnum.DECLARE_CANCEL.getCode());
        if (trackLog.sender().equals(TrackLogConstantMixAll.CAINIAO) || trackLog.sender().equals(TrackLogConstantMixAll.JD)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.REVERSE_DECLARE_NOTIFY);
        } else {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT);
        }
        trackLogInfoDto.setEventTime(new Date());
        trackLogInfoDto.setOperator(UserUtils.getUserRealName());
        return trackLogInfoDto;
    }

    @Override
    public TrackLogInfoDto afterProcessHandle(TrackLog trackLog, TrackLogInfoDto trackLogInfoDto, Object proceed) throws Exception {
        TrackLogBaseInfo trackLogBaseInfo = TrackLogUtils.getBaseInfoAndRemove();
        if (Objects.isNull(trackLogBaseInfo)) {
            return trackLogInfoDto;
        }
        String statusString = trackLogBaseInfo.getStatusDesc();
        if (Objects.equals(TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_FAIL, statusString)) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByDeclareNo90Days(trackLogBaseInfo.getDeclareOrderNo());
            if (Objects.isNull(customsInventoryDTO)) {
                customsInventoryDTO = customsInventoryService.findByOrder(trackLogBaseInfo.getOrderId(), null);
            }
            if (Objects.nonNull(customsInventoryDTO) && Objects.equals(CustomsActionStatus.DEC_SUCCESS.getValue(), customsInventoryDTO.getStatus())) {
                trackLogInfoDto.setCustomsReceipt(customsInventoryDTO.getCustomsDetail());
            }
        }
        if (trackLog.sender().equals(TrackLogConstantMixAll.CAINIAO)
                || trackLog.sender().equals(TrackLogConstantMixAll.JD)
                || trackLog.sender().equals(TrackLogConstantMixAll.REFUND_WAREHOUSE)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.REVERSE_DECLARE_NOTIFY);
        } else {
            trackLogInfoDto.setEventDesc(statusString);
        }
        trackLogInfoDto.setResult(TrackLogConstantMixAll.SUCCESS);
        return trackLogInfoDto;
    }
}
