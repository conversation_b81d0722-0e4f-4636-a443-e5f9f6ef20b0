package com.danding.cds.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.Tuple;
import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.ttl.TransmittableThreadLocal;
import com.danding.cds.bean.vo.DeclareIntervalSecondParam;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.enums.ExceptionUseTagEnum;
import com.danding.cds.monitor.MappingEmptyEvent;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderDeclareV2DTO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.util.RouteActionUtil;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.mq.producer.OrderExamineMQProducer;
import com.danding.cds.utils.CustomsStatusMappingUtil;
import com.danding.cds.utils.flow.OrderAction;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.google.common.eventbus.EventBus;
import com.xxl.job.core.log.XxlJobFileAppender;
import com.xxl.job.core.log.XxlJobLogger;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.DelayQueue;
import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: 申报重推
 * @author: 潘本乐（Belep）
 * @create: 2022-03-28 13:15
 **/
@Service
@Slf4j
@RefreshScope
public class DeclareRepushService {

    @Resource
    private CustomsOrderService customsOrderService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private OrderService orderService;

    @Autowired
    private EventBus eventBus;

    @Resource
    private CustomsPaymentService customsPaymentService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;


    @Autowired
    private BaseDataService baseDataService;

    @Value("${customs.status.cannot.repush.list:}")
    private String[] customsStatusCannotRepushList;

    @Value("${assure_company_wait_seconds_map_json:}")
    private String assureCompanyWaitSecondsMapJSON;

    @Resource(name = "inveDelayDeclareTaskExecutor")
    private ThreadPoolTaskExecutor inveDelayDeclareTaskExecutor;

    @Autowired
    private OrderExamineMQProducer orderExamineMQProducer;


    static final Integer CUSTOMS_INVENTORY_STATUS = -1;

    static final Integer DECLARING = 20;

    static final Integer CUSTOMS_SYSTEM_ERROR = 42;

    static final List<String> customsStatusNotRepushList = Arrays.asList(
            CustomsStat.CUSTOMS_PASS.getValue(),
//            CustomsStat.CUSTOMS_REFUSE.getValue(),
            CustomsStat.CUSTOMS_PERSON.getValue());

    private static final String NO_PAY_INFO = "支付信息不存在";

    /**
     * 延迟申报单据
     */
    private Map<RouteActionEnum, List<?>> delayDeclareOrderMap;

    /**
     * 申报单重推
     *
     * @param orderList 申报信息列表
     * @param source    重推来源
     */
    public void reDeclarePushAndNotify(List<OrderDTO> orderList, OrderDeclareV2DTO orderDeclareDTO, String source) {
        List<String> rePushLimitedList = new ArrayList<>();
        delayDeclareOrderMap = new HashMap<>();
        Boolean maxLimitedEnable = orderDeclareDTO.getMaxLimitedEnable() == null ? true : orderDeclareDTO.getMaxLimitedEnable();
        for (OrderDTO orderDTO : orderList) {
            boolean rePushLimited = this.reDeclarePush(orderDTO, maxLimitedEnable, orderDeclareDTO.getOrderDeclareMax(), orderDeclareDTO.getLogisticDeclareMax(), orderDeclareDTO.getInventoryDeclareMax());
            if (rePushLimited) {
                rePushLimitedList.add(orderDTO.getDeclareOrderNo());
            }
        }
        // 根据担保企业批量重推清单延迟申报
        rePushLimitedList = this.delayDeclare(orderDeclareDTO, rePushLimitedList);
        orderReMsgNotice(orderDeclareDTO, source, rePushLimitedList);
    }

    /**
     * 根据担保企业批量重推清单延迟申报
     */
    private List<String> delayDeclare(OrderDeclareV2DTO orderDeclareDTO, List<String> rePushLimitedList) {
        Vector<String> result = new Vector<>(rePushLimitedList);
        List<CustomsInventoryDTO> delayInventoryDTOList = (List<CustomsInventoryDTO>) delayDeclareOrderMap.get(RouteActionEnum.DECLARE_INVENTORY);
        List<CustomsOrderDTO> delayCustomsOrderDTOList = (List<CustomsOrderDTO>) delayDeclareOrderMap.get(RouteActionEnum.DECLARE_ORDER);
        TransmittableThreadLocal<Object> threadLocal = new TransmittableThreadLocal<>();
        if (CollUtil.isNotEmpty(delayInventoryDTOList)) {
            //清单延迟推送
            Map<Tuple, List<CustomsInventoryDTO>> assureCompanyInventoryMap = delayInventoryDTOList.stream()
                    .collect(Collectors.groupingBy(i -> new Tuple(i.getAssureCompanyId(), i.getEbpId())));
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            assureCompanyInventoryMap.forEach((tuple, inventoryDTOList) -> {
                CompanyDTO assureCompanyDTO = baseDataService.getUnifiedCrossCodeCompanyById(tuple.get(0));
                Long ebpId = tuple.get(1);
                if (StringUtil.isNotBlank(assureCompanyWaitSecondsMapJSON)) {
                    DeclareIntervalSecondParam.EbpDelayConfig ebpDelayConfig = this.getCompanyAndEbpDelayConfig(assureCompanyDTO.getId(), ebpId);
                    if (Objects.nonNull(ebpDelayConfig)) {
                        Long milliseconds = ebpDelayConfig.getDelayMillSec();
                        XxlJobLogger.log("担保企业={},电商平台id={},间隔{}毫秒,清单待延迟推送{}单", assureCompanyDTO.getCode(), ebpId, milliseconds, inventoryDTOList.size());
                        Runnable runnable = new DelayDeclareTask(RouteActionEnum.DECLARE_INVENTORY, inventoryDTOList, milliseconds, orderDeclareDTO, result);
                        futureList.add(CompletableFuture.runAsync(runnable, inveDelayDeclareTaskExecutor));
                    }
                }
            });
            if (!CollectionUtils.isEmpty(futureList)) {
                try {
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
                } catch (Exception e) {
                    log.error("清单延迟申报-CompletableFuture结果异常：{}", e.getMessage(), e);
                    throw new RuntimeException("清单延迟申报-CompletableFuture结果异常" + e.getMessage(), e);
                }
            }
        }
        if (CollUtil.isNotEmpty(delayCustomsOrderDTOList)) {
            //订单延迟推送
            Map<Tuple, List<CustomsOrderDTO>> tupleListMap = delayCustomsOrderDTOList.stream()
                    .collect(Collectors.groupingBy(i -> new Tuple(i.getAgentCompanyId(), i.getEbpId())));
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            tupleListMap.forEach((tuple, customsOrderDTOList) -> {
                CompanyDTO agentCompanyDTO = baseDataService.getCompanyDTOById(tuple.get(0));
                Long ebpId = tuple.get(1);
                if (StringUtil.isNotBlank(assureCompanyWaitSecondsMapJSON)) {
                    DeclareIntervalSecondParam.EbpDelayConfig ebpDelayConfig = this.getCompanyAndEbpDelayConfig(agentCompanyDTO.getId(), ebpId);
                    if (Objects.nonNull(ebpDelayConfig)) {
                        Long milliseconds = ebpDelayConfig.getDelayMillSec();
                        XxlJobLogger.log("传输企业={},电商平台id={},间隔{}毫秒,订单待延迟推送{}单", agentCompanyDTO.getCode(), ebpId, milliseconds, customsOrderDTOList.size());
                        TraceDataRunnable runnable = new DelayDeclareTask(RouteActionEnum.DECLARE_ORDER, customsOrderDTOList, milliseconds, orderDeclareDTO, result);
                        futureList.add(CompletableFuture.runAsync(runnable, inveDelayDeclareTaskExecutor));
                    }
                }
            });
            if (!CollectionUtils.isEmpty(futureList)) {
                try {
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
                } catch (Exception e) {
                    log.error("订单延迟申报-CompletableFuture结果异常：{}", e.getMessage(), e);
                    throw new RuntimeException("订单延迟申报-CompletableFuture结果异常" + e.getMessage(), e);
                }
            }
        }
        return Collections.list(result.elements());
    }


    @Data
    static class DelayElement implements Delayed {

        private Object orderDTO;

        private Long delayTime;

        public DelayElement(Object orderDTO, Long delayTime) {
            this.delayTime = System.currentTimeMillis() + delayTime;
            this.orderDTO = orderDTO;
        }

        @Override
        public long getDelay(TimeUnit unit) {
            long diff = delayTime - System.currentTimeMillis();
            return unit.convert(diff, TimeUnit.MILLISECONDS);
        }

        @Override
        public int compareTo(Delayed other) {
            if (this == other) {
                return 0;
            }
            long diff = this.getDelay(TimeUnit.MILLISECONDS) - other.getDelay(TimeUnit.MILLISECONDS);
            return Long.compare(diff, 0);
        }
    }

    class DelayDeclareTask extends TraceDataRunnable {

        private final RouteActionEnum routeActionEnum;

        private final List<?> orderDTOList;

        private final Long delayMilliseconds;
        private final OrderDeclareV2DTO orderDeclareDTO;
        private Vector<String> rePushLimitedList;

        private final String xxlJobLogFileName;

        public DelayDeclareTask(RouteActionEnum routeActionEnum, List<?> orderDTOList, Long delayMilliseconds, OrderDeclareV2DTO orderDeclareDTO, Vector<String> rePushLimitedList) {
            this.routeActionEnum = routeActionEnum;
            this.orderDTOList = orderDTOList;
            this.delayMilliseconds = delayMilliseconds;
            this.orderDeclareDTO = orderDeclareDTO;
            this.rePushLimitedList = rePushLimitedList;
            xxlJobLogFileName = XxlJobFileAppender.contextHolder.get();
        }

        @Override
        public void proxy() {
            // 获取主线程的xxlJob文件名称
            XxlJobFileAppender.contextHolder.set(xxlJobLogFileName);
            DelayQueue<DelayElement> delayQueue = new DelayQueue<>();
            for (Object orderDTO : orderDTOList) {
                int idx = orderDTOList.indexOf(orderDTO);
                delayQueue.add(new DelayElement(orderDTO, idx * delayMilliseconds));
            }
            while (!delayQueue.isEmpty()) {
                boolean rePushLimited = false;
                DelayElement delayElement;
                try {
                    delayElement = delayQueue.take();
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
                String declareOrderNo = null;
                Long orderId = null;
                Integer status = null;
                String customsStatus = null;
                String customsDetail = null;
                Date lastDeclareTime = null;
                Integer declareFrequency = null;

                if (RouteActionEnum.DECLARE_INVENTORY.equals(routeActionEnum)) {
                    CustomsInventoryDTO inventoryDTO = (CustomsInventoryDTO) delayElement.getOrderDTO();
                    declareOrderNo = inventoryDTO.getDeclareOrderNo();
                    orderId = inventoryDTO.getOrderId();
                    status = inventoryDTO.getStatus();
                    customsStatus = inventoryDTO.getCustomsStatus();
                    customsDetail = inventoryDTO.getCustomsDetail();
                    lastDeclareTime = inventoryDTO.getLastDeclareTime();
                    declareFrequency = inventoryDTO.getDeclareFrequency();
                }
                if (RouteActionEnum.DECLARE_ORDER.equals(routeActionEnum)) {
                    CustomsOrderDTO customsOrderDTO = (CustomsOrderDTO) delayElement.getOrderDTO();
                    declareOrderNo = customsOrderDTO.getDeclareOrderNo();
                    orderId = customsOrderDTO.getOrderId();
                    status = customsOrderDTO.getStatus();
                    customsStatus = customsOrderDTO.getCustomsStatus();
                    customsDetail = customsOrderDTO.getCustomsDetail();
                    lastDeclareTime = customsOrderDTO.getLastDeclareTime();
                    declareFrequency = customsOrderDTO.getDeclareFrequency();
                }
                try {
                    rePushLimited = repushHandle(status, routeActionEnum, orderId, declareOrderNo, customsStatus, customsDetail,
                            lastDeclareTime, orderDeclareDTO.getMaxLimitedEnable(), orderDeclareDTO.getOrderDeclareMax(), declareFrequency);
                } catch (ArgsErrorException e) {
                    XxlJobLogger.log("清单申报异常-" + e.getErrorMessage(), e);
                } catch (Exception e) {
                    XxlJobLogger.log("申报单号【" + declareOrderNo + "】" + routeActionEnum.getDesc() + "申报异常-" + e.getMessage(), e);
                    log.error("申报单号【" + declareOrderNo + "】" + routeActionEnum.getDesc() + "申报异常-" + e.getMessage(), e);
                }
                if (rePushLimited) {
                    rePushLimitedList.add(declareOrderNo);
                }
            }
        }
    }

    /**
     * 订单重推消息通知
     *
     * @param orderDeclareDTO
     * @param source
     * @param rePushLimitedList
     */
    public void orderReMsgNotice(OrderDeclareV2DTO orderDeclareDTO, String source, List<String> rePushLimitedList) {
        if (!CollectionUtils.isEmpty(rePushLimitedList)) {
            String declareNos = rePushLimitedList.stream().collect(Collectors.joining("\n"));
            String message = WechatNotifyUtils.mdWarnMessage(source + "申报单重推次数已达上限", declareNos, "已卡单，请处理", "是否开启重推最大次数限制开关：" + orderDeclareDTO.getMaxLimitedEnable());
            String _message = "";
            if (message.length() > 1024) {
                _message = message.substring(1024);
            }
            XxlJobLogger.log("消息通知:{}", _message);
            WechatNotifyUtils.wechatNotifyMd(orderDeclareDTO.getWebHook(), orderDeclareDTO.getPhoneList(), message);
        }
    }


    /**
     * 申报单重推
     *
     * @param orderDTO            申报信息
     * @param orderDeclareMax     订单最大次数
     * @param logisticDeclareMax  运单最大次数
     * @param inventoryDeclareMax 清单最大次数
     * @return 是否达到重推限制
     */
    private Boolean reDeclarePush(OrderDTO orderDTO, boolean maxLimitedEnable, Integer orderDeclareMax, Integer logisticDeclareMax, Integer inventoryDeclareMax) {

        Boolean rePushLimited = false;
        String declareNo = orderDTO.getDeclareOrderNo();
        try {

            Integer orderTags = Optional.ofNullable(orderDTO).map(OrderDTO::getOrderTags).orElse(null);
            List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
            if (orderTag.contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
                XxlJobLogger.log("申报单号:{}已挂起", orderDTO.getDeclareOrderNo());
                return rePushLimited;
            }

            Long mainOrderId = orderDTO.getId();
            // 如果是待申报，则直接发送，有可能下单接口发送MQ时异常，导致整个申报单还是待申报状态
            if (Objects.equals(OrderAction.EXAMINE.getValue(), orderDTO.getStatus())) {
                XxlJobLogger.log("申报单号:{}，申报单状态：{}，发送ccs-order-examine-topic处理", declareNo, "待申报");
                orderExamineMQProducer.send(orderDTO.getSn());
                return rePushLimited;
            }
            // order处于申报中
            if (!DECLARING.equals(orderDTO.getStatus())) {
                return rePushLimited;
            }
            String actionJson = orderDTO.getActionJson();
            // 订单重推
            boolean containCustomsOrder = RouteActionUtil.containCustomsOrder(actionJson);
            if (containCustomsOrder) {
                CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), null);
                if (Objects.nonNull(customsOrderDTO)) {
                    if (!Objects.equals(CustomsActionStatus.DEC_SUCCESS.getValue(), customsOrderDTO.getStatus())
                            && !Objects.equals(CustomsActionStatus.DEC_CANCEL.getValue(), customsOrderDTO.getStatus())) {
                        DeclareIntervalSecondParam.EbpDelayConfig ebpDelayConfig = this.getCompanyAndEbpDelayConfig(customsOrderDTO.getAgentCompanyId(), customsOrderDTO.getEbpId());
                        if (Objects.nonNull(ebpDelayConfig)) {
                            Long milliseconds = ebpDelayConfig.getDelayMillSec();
                            CompanyDTO agentCompanyDTO = baseDataService.getCompanyDTOById(customsOrderDTO.getAgentCompanyId());
                            log.info("订单重推延迟{}毫秒申报 - 担保企业编码={}, 申报单号={}, 电商平台={}",
                                    milliseconds, agentCompanyDTO.getCode(), declareNo, customsOrderDTO.getEbpId());
                            XxlJobLogger.log("订单重推延迟{}毫秒申报 - 担保企业编码={}, 申报单号={}, 电商平台={}",
                                    milliseconds, agentCompanyDTO.getCode(), declareNo, customsOrderDTO.getEbpId());
                            if (!delayDeclareOrderMap.containsKey(RouteActionEnum.DECLARE_ORDER)) {
                                List<CustomsOrderDTO> orderDTOList = new ArrayList<>();
                                delayDeclareOrderMap.put(RouteActionEnum.DECLARE_ORDER, orderDTOList);
                            }
                            List<CustomsOrderDTO> customsOrderList = (List<CustomsOrderDTO>) delayDeclareOrderMap.get(RouteActionEnum.DECLARE_ORDER);
                            customsOrderList.add(customsOrderDTO);

                            return false;
                        } else {
                            return this.repushHandle(customsOrderDTO.getStatus(), RouteActionEnum.DECLARE_ORDER, mainOrderId, declareNo, customsOrderDTO.getCustomsStatus(), customsOrderDTO.getCustomsDetail(),
                                    customsOrderDTO.getLastDeclareTime(), maxLimitedEnable, orderDeclareMax, customsOrderDTO.getDeclareFrequency());
                        }
                    }
                }
            }

            boolean containShipment = RouteActionUtil.containShipment(actionJson);
            if (containShipment) {
                CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), null);
                if (Objects.nonNull(logisticsDTO)) {
                    if (!Objects.equals(CustomsActionStatus.DEC_SUCCESS.getValue(), logisticsDTO.getStatus())
                            && !Objects.equals(CustomsActionStatus.DEC_CANCEL.getValue(), logisticsDTO.getStatus())) {
                        return this.repushHandle(logisticsDTO.getStatus(), RouteActionEnum.DECLARE_LOGISTICS, mainOrderId, declareNo, logisticsDTO.getCustomsStatus(), logisticsDTO.getCustomsDetail(),
                                logisticsDTO.getLastDeclareTime(), maxLimitedEnable, orderDeclareMax, logisticsDTO.getDeclareFrequency());
                    }
                }
            }

            boolean containInventory = RouteActionUtil.containInventory(actionJson);
            if (containInventory) {
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findBySnSection(orderDTO.getCustomsInventorySn());
                if (Objects.isNull(inventoryDTO)) {
                    return rePushLimited;
                }
                if (Objects.equals(CustomsActionStatus.DEC_SUCCESS.getValue(), inventoryDTO.getStatus())
                        || Objects.equals(CustomsActionStatus.DEC_CANCEL.getValue(), inventoryDTO.getStatus())) {
                    return rePushLimited;
                }

                // todo 申报中， 申报完成和人工审核重推，依赖回执
//                if (DECLARING.equals(inventoryDTO.getStatus())) {
//                    if (cannotRepush(inventoryDTO.getCustomsStatus())) {
//                        return rePushLimited;
//                    }
//                }
                // todo 申报终止，海关异常需要重推，依赖回执
//                if (CUSTOMS_INVENTORY_STATUS.equals(inventoryDTO.getStatus())) {
//                    if (!CUSTOMS_SYSTEM_ERROR.equals(orderDTO.getExceptionType())) {
//                        return rePushLimited;
//                    }
//                }
                // 判断下是不是支付信息不存在，如果不存在且是自己报的支付单，则先重推下支付单
                String customsDetail = inventoryDTO.getCustomsDetail();
                if (StringUtils.hasText(customsDetail) && customsDetail.contains(NO_PAY_INFO)) {
                    CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), null);
                    if (Objects.nonNull(customsPaymentDTO)) {
                        customsPaymentService.rePush(customsPaymentDTO.getSn(), true);
                        XxlJobLogger.log("申报单号:{}，清单申报-支付信息不存在，推下支付单", declareNo);
                    }
                }

                DeclareIntervalSecondParam.EbpDelayConfig ebpDelayConfig = this.getCompanyAndEbpDelayConfig(inventoryDTO.getAssureCompanyId(), inventoryDTO.getEbpId());
                if (Objects.nonNull(ebpDelayConfig)) {
                    Long milliseconds = ebpDelayConfig.getDelayMillSec();
                    CompanyDTO assureCompanyDTO = baseDataService.getUnifiedCrossCodeCompanyById(inventoryDTO.getAssureCompanyId());
                    log.info("清单重推延迟{}毫秒申报 - 担保企业编码={}, 申报单号={}, 电商平台={}",
                            milliseconds, assureCompanyDTO.getCode(), declareNo, inventoryDTO.getEbpId());
                    XxlJobLogger.log("清单重推延迟{}毫秒申报 - 担保企业编码={}, 申报单号={}, 电商平台={}",
                            milliseconds, assureCompanyDTO.getCode(), declareNo, inventoryDTO.getEbpId());
                    if (!delayDeclareOrderMap.containsKey(RouteActionEnum.DECLARE_INVENTORY)) {
                        List<CustomsInventoryDTO> inventoryDTOList = new ArrayList<>();
                        delayDeclareOrderMap.put(RouteActionEnum.DECLARE_INVENTORY, inventoryDTOList);
                    }
                    List<CustomsInventoryDTO> customsInventoryDTOList = (List<CustomsInventoryDTO>) delayDeclareOrderMap.get(RouteActionEnum.DECLARE_INVENTORY);
                    customsInventoryDTOList.add(inventoryDTO);
                    return false;
                } else {
//                XxlJobLogger.log("申报单号:{}，清单状态满足重推条件", declareNo);
                    return this.repushHandle(inventoryDTO.getStatus(), RouteActionEnum.DECLARE_INVENTORY, mainOrderId, declareNo, inventoryDTO.getCustomsStatus(), inventoryDTO.getCustomsDetail(),
                            inventoryDTO.getLastDeclareTime(), maxLimitedEnable, orderDeclareMax, inventoryDTO.getDeclareFrequency());
                }
            }
//            return rePushLimited;
        } catch (ArgsErrorException errorException) {
            XxlJobLogger.log("[[op:DeclareRepushService] 待放行重推异常 Message={}]", errorException.getErrorMessage());
        } catch (Exception e) {
            log.error("[[op:DeclareRepushService] 申报单号= {} 待放行重推异常 Message={}", declareNo, e.getMessage(), e);
            XxlJobLogger.log("[[op:DeclareRepushService] 待放行重推异常 Message={}", e.getMessage(), e);
        }
        return rePushLimited;
    }

    /**
     * 根据apollo上配置，判断 电商企业+担保企业 是否需要做延迟间隔推送
     *
     * @param assureCompanyId 担保企业(清单)、申报企业(订单)
     * @param ebpId           电商平台
     * @return
     */
    private DeclareIntervalSecondParam.EbpDelayConfig getCompanyAndEbpDelayConfig(Long assureCompanyId, Long ebpId) {
        CompanyDTO assureCompanyDTO = baseDataService.getCompanyDTOById(assureCompanyId);
        if (Objects.isNull(assureCompanyDTO)) {
            return null;
        }
        if (StringUtil.isNotBlank(assureCompanyWaitSecondsMapJSON)) {
            List<DeclareIntervalSecondParam> declareIntervalSecondParams = JSON.parseArray(assureCompanyWaitSecondsMapJSON, DeclareIntervalSecondParam.class);
            if (CollUtil.isNotEmpty(declareIntervalSecondParams)) {
                for (DeclareIntervalSecondParam param : declareIntervalSecondParams) {
                    if (!Objects.equals(assureCompanyDTO.getCode(), param.getAssureCompanyCode())) {
                        continue;
                    }
                    List<DeclareIntervalSecondParam.EbpDelayConfig> ebpDelayConfigs = param.getEbpDelayConfigs();
                    return ebpDelayConfigs.stream().filter(i -> Objects.equals(i.getEbpId(), ebpId)).findFirst().orElse(null);
                }
            }
        }
        return null;
    }

    private boolean repushHandle(Integer status, RouteActionEnum routeActionEnum, Long mainOrderId, String declareNo,
                                 String customsStauts, String customsDetail, Date lastDeclareTime,
                                 boolean maxLimitedEnable, Integer declareMaxTimes, Integer repushNum) {

        boolean rePushMaxLimited = false;
        // 如果带申报，直接推掉
        if (Objects.equals(CustomsActionStatus.DEC_WAIT.getValue(), status)) {
            XxlJobLogger.log("{},申报单号:{}，状态:待申报,直接重推", routeActionEnum.getDesc(), declareNo);
            orderService.resetDeclare(mainOrderId, routeActionEnum);
            return rePushMaxLimited;
        }

//        XxlJobLogger.log("{},申报单号:{}，状态:申报中,满足重推条件", routeActionEnum.getDesc(), declareNo);
        // 判断下是否有回执异常，然后判断下是否需要重推
        boolean orderExceptionRepushEnable = this.customsExceptionRepushNable(customsStauts, customsDetail, routeActionEnum);
        if (!orderExceptionRepushEnable) {
            XxlJobLogger.log("{},申报单号:{}，海关回执映射配置的异常类型中，能否重推标识为:{}，不重推", routeActionEnum.getDesc(), declareNo, orderExceptionRepushEnable);
            return rePushMaxLimited;
        }
        // 如果上一次申报时间为空，可能是申报handler的时候有异常，导致根本没有推送成功，所以直接推送就好
        if (lastDeclareTime == null) {
            XxlJobLogger.log("{},申报单号:{},上次申报时间为空，直接重推", routeActionEnum.getDesc());
            orderService.resetDeclare(mainOrderId, routeActionEnum);
        } else if (new DateTime(lastDeclareTime).isBefore(DateTime.now().minusMinutes(10))) {
            // 10分钟推一次
            if (declareMaxTimes != null) {
                repushNum = repushNum == null ? 0 : repushNum;
                if (repushNum > declareMaxTimes) {
                    if (!maxLimitedEnable) {
                        XxlJobLogger.log("{},申报单号:{}，已达重试次数上限，但限制上限开关没开，仍旧重推", routeActionEnum.getDesc(), declareNo);
                        orderService.resetDeclare(mainOrderId, routeActionEnum);
                    } else {
                        rePushMaxLimited = true;
                        XxlJobLogger.log("{},申报单号:{}，已重试{}次已达上限{}次 ，且限制上限开关开启，不重推", routeActionEnum.getDesc(), declareNo, repushNum, declareMaxTimes);
                    }
                } else {
                    XxlJobLogger.log("{},申报单号:{}，未达到重试次数上限，直接重推", routeActionEnum.getDesc(), declareNo);
                    orderService.resetDeclare(mainOrderId, routeActionEnum);
                }
            } else {
                XxlJobLogger.log("{},申报单号:{}，没有设置重试次数上限，直接重推", routeActionEnum.getDesc(), declareNo);
                orderService.resetDeclare(mainOrderId, routeActionEnum);
            }
        } else {
            XxlJobLogger.log("{},申报单号:{},距离上次申报时间不足10分钟，不重推 ", routeActionEnum.getDesc(), declareNo);
        }
        return rePushMaxLimited;
    }

    private Boolean customsExceptionRepushNable(String customsStatus, String customsDetail, RouteActionEnum routeActionEnum) {

        if (StringUtils.isEmpty(customsStatus) && StringUtils.isEmpty(customsDetail)) {
            return true;
        }
        String mappingCode = CustomsStatusMappingUtil.getStatusMappingCode(customsStatus, customsDetail, routeActionEnum);
        CustomsStatusMappingDTO customsStatusMappingDTO = baseDataService.getCustomsStatusMappingDTOByCode(mappingCode);
        // 如果没有找到映射的Mapping数据，说明没有解析成功，这里重推的逻辑就直接用异常文本匹配，
        if (customsStatusMappingDTO == null) {
            // 这里没有找到就报下问题
            Map<String, String> receive = new HashMap<>();
            receive.put("customsStatus", customsStatus);
            receive.put("customsDetail", customsDetail);
            receive.put("remark", "定时任务重推解析海关回执，没有找到对应映射数据 ");
            eventBus.post(new MappingEmptyEvent(mappingCode, JSON.toJSONString(receive)));
            List<ExceptionDTO> exceptionDTOList = baseDataService.getExceptionDTOByUseTag(ExceptionUseTagEnum.CUSTOMS_CALLBACK_MATCH.getCode());
            if (CollectionUtils.isEmpty(exceptionDTOList)) {
                return true;
            }
            // 匹配下海关回执，是否满足异常配置的某个重推规则，取第一个匹配的情况
            for (ExceptionDTO dto : exceptionDTOList) {
                String exceptionDesc = dto.getExceptionDescribe();
                if (StringUtils.isEmpty(exceptionDesc)) {
                    continue;
                }
                if (customsDetail.contains(exceptionDesc)) {
                    return Objects.equals(0, dto.getRepushEnable()) ? false : true;
                }
            }
            return true;
        } else {
            customsStatusMappingDTO = customsStatusMappingService.getFinalMappingCode(customsStatusMappingDTO, customsDetail);
            Long exceptionId = customsStatusMappingDTO.getExceptionId();
            if (exceptionId == null) {
                return true;
            }
            ExceptionDTO exceptionDTO = baseDataService.getExceptionDTOById(exceptionId);
            return Optional.ofNullable(exceptionDTO).map(z -> {
                return Objects.equals(0, z.getRepushEnable()) ? false : true;
            }).orElse(true);
        }
    }


    /**
     * 判断某些海关回执不用重推
     *
     * @param customsStatus 海关状态
     * @return
     */
    private boolean cannotRepush(String customsStatus) {

        if (ArrayUtil.isEmpty(customsStatusCannotRepushList)) {
            return customsStatusNotRepushList.contains(customsStatus);
        } else {
            return ArrayUtil.contains(customsStatusCannotRepushList, customsStatus);
        }
    }

    public Boolean paymentReDeclarePush(CustomsPaymentDTO customsPaymentDTO) {
        // 排除支付单申报成功和取消状态
        if (!Objects.equals(customsPaymentDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue())
                && !Objects.equals(customsPaymentDTO.getStatus(), CustomsActionStatus.DEC_CANCEL.getValue())) {
            XxlJobLogger.log("申报单号:{}，支付单状态满足重推条件", customsPaymentDTO.getDeclareOrderNo());
            if (customsPaymentDTO.getLastDeclareTime() != null && new DateTime(customsPaymentDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                orderService.resetDeclare(customsPaymentDTO.getOrderId(), RouteActionEnum.DECLARE_PAYMENT);
                XxlJobLogger.log("待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_PAYMENT.getDesc(), customsPaymentDTO.getDeclareOrderNo());
                return true;
            }
        }
        return false;
    }


}
