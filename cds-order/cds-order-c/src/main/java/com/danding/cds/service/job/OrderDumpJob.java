package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.mq.OrderEsDumpProducer;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class OrderDumpJob extends IJobHandler {
    /**
     * 单个模式
     * 增量模式
     * 全量模式
     */

    @Resource
    private OrderService orderService;

    @Autowired
    private OrderEsDumpProducer orderEsDumpProducer;

    @Override
    @XxlJob(value = "OrderCDumpJob", enableTenant = true)
    public ReturnT<String> execute(String param) throws Exception {
        JSONObject config = JSON.parseObject(param);
        String dataFormat = "yyyy/MM/dd HH:mm:ss";
        if ("single".equals(config.getString("mode"))){
            String sn = config.getString("sn");
            XxlJobLogger.log("[op:OrderDumpJob] mode=single, sn={}", sn);
            if (!StringUtils.isEmpty(sn)){
                orderService.dumpBySn(sn);
            }
        }else if ("full".equals(config.getString("mode"))){
            Date createFrom = new DateTime(946656000000L).toDate();
            Date createTo = new Date();
            String from = config.getString("from");
            if (!StringUtils.isEmpty(from)){
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(dataFormat);
                createFrom = dateTimeFormatter.parseDateTime(from).toDate();
            }
            String to = config.getString("to");
            if (!StringUtils.isEmpty(to)){
                DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(dataFormat);
                createTo = dateTimeFormatter.parseDateTime(to).toDate();
            }
            XxlJobLogger.log("[op:OrderDumpJob] mode=full, from {} to {}", from, to);
            List<OrderDTO> orderList = orderService.listByCreateTimeSection(createFrom, createTo);
            XxlJobLogger.log("[op:OrderDumpJob] mode=full, total = {}", orderList.size());
            for (OrderDTO orderDTO : orderList) {
                orderEsDumpProducer.send(orderDTO.getSn());
            }
        }else {
            Date updateFrom = DateTime.now().minusMinutes(10).toDate();
            Date updateTo = new Date();
            XxlJobLogger.log("[op:OrderDumpJob] mode=add, from {} to {}", new DateTime(updateFrom).toString(dataFormat), new DateTime(updateTo).toString(dataFormat));
            List<OrderDTO> orderList = orderService.listByUpdateTimeSection(updateFrom,updateTo,new Date());
            XxlJobLogger.log("[op:OrderDumpJob] mode=add, total = {}", orderList.size());
            for (OrderDTO orderDTO : orderList) {
                orderEsDumpProducer.send(orderDTO.getSn());
            }
        }
        return ReturnT.SUCCESS;
    }
}
