package com.danding.cds.service.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.TransferDto;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @description: 字节云内申报消费者
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = "ccs-byteDance-order-declare-c-topic",
        consumerGroup = "ccs-byteDance-order-declare-c-consumer")
public class ByteDanceDeclareConsumer extends MessageHandler {

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Override
    public void handle(Object o) throws RuntimeException {
        try {
            String sn = o.toString();
            TransferDto dto = JSONUtil.toBean(sn, TransferDto.class);
            log.info("ccs-cloud-order-declareTopic 数据接收 -{}", dto);
            //处理清单
            if (Objects.equals(dto.getType(), "inventory")) {
                String data = dto.getData().toString();
                WrapInventoryOrderInfo info = JSON.parseObject(data, WrapInventoryOrderInfo.class);
                byteDanceDeclareService.cloudInventoryDeclareAndLog(info);
            }
            //处理订单
            if (Objects.equals(dto.getType(), "order")) {
                String data = dto.getData().toString();
                WrapOrderDeclareInfo info = JSONUtil.toBean(data, WrapOrderDeclareInfo.class);
                byteDanceDeclareService.cloudOrderDeclare(info);
            }
            //处理运单
            if (Objects.equals(dto.getType(), "logistics")) {
                String data = dto.getData().toString();
                WrapShipmentInfo info = JSONUtil.toBean(data, WrapShipmentInfo.class);
                byteDanceDeclareService.cloudLogisticsDeclare(info);
            }
            log.info("处理完成 -{}", dto);
        } catch (Exception e) {
            log.error("字节申报消费端-数据处理异常 -{}", e.getMessage(), e);
        }

    }


}
