package com.danding.cds.monitor;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.config.JdlCallbackTaxConfigDTO;
import com.danding.cds.bean.dto.InventoryModifyDeclareDto;
import com.danding.cds.bean.vo.CallbackDealResultDto;
import com.danding.cds.c.api.service.*;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.callback.api.dto.PaymentActive;
import com.danding.cds.common.bean.dto.JdlPostDTO;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCancelDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryOrderType;
import com.danding.cds.customs.inventory.api.enums.ModifyDeclareStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderReceive;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.declare.ceb.internal.enums.CebMessageEnum;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.clear.base.callback.module.*;
import com.danding.cds.declare.sdk.clear.base.result.*;
import com.danding.cds.declare.sdk.enums.CustomsPaymentStatus;
import com.danding.cds.declare.sdk.listener.CustomsListener;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.declare.sdk.model.tax.TaxItem;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.enums.TrackLogEnums;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.service.TrackLogEsProducer;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.ExternalDeclareOrderService;
import com.danding.cds.service.InventoryModifyDeclareBaseService;
import com.danding.cds.taxes.api.dto.TaxesCompanyTaxListSubmit;
import com.danding.cds.taxes.api.dto.TaxesItemDTO;
import com.danding.cds.taxes.api.service.TaxesCompanyTaxListService;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.DeclareOrderEsQuerier;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.TaxBillStatusEnums;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.common.utils.CopyUtil;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.Lists;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Component
@RefreshScope
public class CustomsSupportListener extends CustomsListener {
    @Autowired
    private MessageSender messageSender;

    @Autowired
    private CustomsSupportListener customsSupportListener;

    @Autowired
    @Lazy
    private CustomsPaymentService customsPaymentService;

    @Autowired
    @Lazy
    private CustomsPaymentDeclareService customsPaymentDeclareService;

    @Resource
    private CustomsOrderService customsOrderService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Resource
    private OrderService orderService;

    @DubboReference
    private TaxesCompanyTaxListService taxesCompanyTaxListService;

    @Resource
    private RefundOrderService refundOrderService;

    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;

    //    @Autowired
//    private LogComponent logComponent;
    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private MessageService messageService;

    @Resource
    private TrackLogService trackLogService;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private EventBus eventBus;

    @Autowired
    private TrackLogEsProducer trackLogEsProducer;

    @Value("${pdd.host:}")
    private String PDD_HOST;

    @Value("${pdd.data.send.enable:false}")
    private boolean PDD_DATA_SEND_ENABLE;

    @Value("${little_giant_route_code_list:[]}")
    private String[] littleGiantRouteCodeList;

    @Value("${jdl.callback.tax.config:}")
    private String jdlCallbackTaxConfig;

    @Autowired
    private ExternalDeclareOrderService externalDeclareOrderService;

    @DubboReference
    private JdServProviderService jdServProviderService;

    private static final String CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC = "ccs-pdd-customs-send-data-receipt-topic";


    @Override
    public void sendClearPaymentDeclare(CustomsReport report, CustomsPayDeclareResult resultInfo) {
        customsSupportListener.sendClearPaymentDeclareCore(report, resultInfo);
    }

    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.PAYMENT_DECLARE_RESULT
    )
    public void sendClearPaymentDeclareCore(CustomsReport report, CustomsPayDeclareResult resultInfo) {
        log.info("[op:CustomsSupportListener] receive info={}", JSON.toJSONString(resultInfo));
        CustomsPaymentDeclareDTO customsPaymentDeclare = customsPaymentDeclareService.findCustomsPaymentByOutRequestNo(resultInfo.getCustoms(), resultInfo.getOutRequestNo());
        if (Objects.isNull(customsPaymentDeclare)) {
            log.error("回执异常支付申报表不存在 customs={} , outRequestNo={}", resultInfo.getCustoms(), resultInfo.getOutRequestNo());
            return;
        }
        if (CustomsPaymentStatus.SUCCESS.getValue().equals(customsPaymentDeclare.getStatus())) {
            return;
        }
        if (resultInfo.getSuccess()) {
            if (CustomsPaymentStatus.WAIT_DECLARE.getValue().equals(customsPaymentDeclare.getStatus()) ||
                    CustomsPaymentStatus.WAIT_RE_PUSH.getValue().equals(customsPaymentDeclare.getStatus())) {
                customsPaymentDeclareService.doSuccessfulStatus(customsPaymentDeclare.getId(), resultInfo.getErrorMsg(), resultInfo.getExtra(), resultInfo.getPostMsg(),
                        resultInfo.getPayTransactionId(), resultInfo.getVerDept(), resultInfo.getSubBankNo());
            }
        } else {
            customsPaymentDeclareService.doFailureStatus(customsPaymentDeclare.getId(), resultInfo.getErrorMsg(), resultInfo.getExtra(), resultInfo.getPostMsg());
        }

        // 支付申报回执
        CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findById(customsPaymentDeclare.getPaymentId());
        OrderDTO orderDTO = orderService.findBySnSection(customsPaymentDTO.getOrderSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        PaymentActive paymentActive = CopyUtil.copy(resultInfo, PaymentActive.class);
        paymentActive.setOrderSn(orderDTO.getSn());
        messageService.createMessage(MessageType.ORDER_CUSTOMS_PAYMENT
                , Lists.newArrayList("CHANNEL-" + orderExtra.getSubmit().getChannel())
                , customsPaymentDTO.getDeclareOrderNo()
                , JSON.toJSONString(paymentActive)
                , ""
        );
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
        /**后续自己去添加吧**/
//        logComponent.logCustomsReport(LogCode.LOG_PAYMENT,customsPaymentDeclare,customsPaymentDeclare.getSn(),
//                CustomsActionStatus.getEnum(result.getStatus()).getDesc(),
//                CustomsActionStatus.getEnum(customsOrderDTO.getStatus()).getDesc(),"支付单申报",report);
    }

    @Override
    public void sendClearOrderDeclare(CustomsReport report, OrderDeclareResult result) {
        try {
            CustomsOrderDTO customsOrderDTO = customsOrderService.findBySnSection(result.getSn());
            OrderDTO orderDTO = orderService.findBySnSection(customsOrderDTO.getOrderSn());
            CompanyDTO ebp = companyService.findUnifiedCrossInfoById(customsOrderDTO.getEbpId());
            if ("3105961682".equals(ebp.getCode()) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                ExpressDTO expressDTO = expressService.findById(inventoryDTO.getExpressId());
                eventBus.post(new PddUpInfoEvent(customsOrderDTO.getDeclareOrderNo(), 1, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                        orderDTO.getOutOrderNo(), report.getRequestMsg(), 0L, "", "", ""
                ));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendClearInventoryDeclare(CustomsReport report, InventoryDeclareResult result) {
        customsSupportListener.sendClearInventoryDeclareProxy(report, result);
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void sendClearInventoryDeclareProxy(CustomsReport report, InventoryDeclareResult result) {
        try {
            CustomsInventoryDTO inventoryDTO = customsInventoryService.findBySnSection(result.getSn());
            OrderDTO orderDTO = orderService.findByIdFull(inventoryDTO.getOrderId());
            CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
            if ("3105961682".equals(ebp.getCode()) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                ExpressDTO expressDTO = expressService.findById(inventoryDTO.getExpressId());
                eventBus.post(new PddUpInfoEvent(orderDTO.getDeclareOrderNo(), 1, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                        orderDTO.getOutOrderNo(), report.getRequestMsg(), 0L, "", "", ""
                ));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @Override
    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void sendClearLogisticsDeclare(CustomsReport report, ShipmentDeclareResult result) {
        customsSupportListener.sendClearLogisticsDeclareProxy(report, result);
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void sendClearLogisticsDeclareProxy(CustomsReport report, ShipmentDeclareResult result) {
        try {
            CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findBySn(result.getSn());
            if (logisticsDTO == null) {
                return;
            }
            OrderDTO orderDTO = orderService.findByIdFull(logisticsDTO.getOrderId());
            CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
            if ("3105961682".equals(ebp.getCode()) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                ExpressDTO expressDTO = expressService.findById(inventoryDTO.getExpressId());
                eventBus.post(new PddUpInfoEvent(logisticsDTO.getDeclareOrderNo(), 1, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                        orderDTO.getOutOrderNo(), report.getRequestMsg(), 0L, "", "", ""
                ));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @Override
    public void sendInventoryCancelDeclare(CustomsReport report, InventoryCancelResult result) {
        //老逻辑在查询后只做了日志记录 没做业务操作
//        CustomsInventoryCancelDTO customsInventoryCancelDTO = customsInventoryCancelService.findBySn(result.getSn());
//        if (customsInventoryCancelDTO == null) return;
//        logComponent.logCustomsReport(LogCode.lOG_INVENTORY_CANCEL, result.getOrderNo(), result.getSn(),
//                InventoryCancelEnum.getEnum(result.getStatus()).getDesc(),
//                InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus()).getDesc(), "撤单申报", report);
    }

    @Override
    public void sendInventoryRefundDeclare(CustomsReport report, InventoryRefundResult result) {
        //老逻辑在查询后只做了日志记录 没做业务操作
//        RefundOrderInfoDto refundOrderInfoDto = refundOrderService.findBySn(result.getSn());
//        if (refundOrderInfoDto == null) return;
//        logComponent.logCustomsReport(LogCode.LOG_REFUND, result.getOrderNo(), result.getSn(),
//                RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(result.getStatus()).getDesc(),
//                RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(refundOrderInfoDto.getRefundCheckStatus()).getDesc(), "退单申报", report);
    }

    @Override
    public void acceptOrderCallback(CustomsReport report, OrderCallback callback) {
        String ebpCode = callback.getEbpCode();
        if (isPddOrder(ebpCode)) {
            // 这里海关返回的是明文（XP开头的单号），但是数据库存储的是密文，所以要对orderNo进行加密
            String pddUrl = PDD_HOST + "/xhr/order/findByRealDeclareSn";
            String declareOrderNo = callback.getOrderNo();
            log.info("[op:PddDeclare-findByRealDeclareSn] PDD proxy, url={}, param={}", pddUrl, declareOrderNo);
            // 拼多多转发至云内应用申报
            HttpRequest httpRequest = HttpRequest.get(pddUrl, true, "realDeclareSn", declareOrderNo).header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr());
            String encodeStr = httpRequest.body();
            if (httpRequest.ok()) {
                if (!StringUtils.isEmpty(encodeStr)) {
                    callback.setOrderNo(encodeStr);
//                    receive.setDeclareOrderNo(encodeStr);
                    log.info("[op:PddDeclare-logisticsDeclare] PDD succ, res={}", encodeStr);
                }
            } else {
                String body = StringUtils.isNotBlank(encodeStr) ? encodeStr.replace("\r\n", "") : "";
                log.info("[op:PddDeclare-acceptOrderCallback] PDD fail,exception  declareOrderNo={} body={}", declareOrderNo, body);
                throw new RuntimeException("获取pdd解密申报单号失败");
            }
        }
        customsSupportListener.acceptOrderCallbackProxy(report, callback);
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void acceptOrderCallbackProxy(CustomsReport report, OrderCallback callback) {
//        CompanyDTO ebp = baseDataService.getCompanyDTOByCode(callback.getEbpCode());

        // 判断下是不是芥舟的单子，如果是则直接回调芥舟，不做业务处理
        // 新浙电给的回执不需要处理这个
        if (!CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(callback.getReturnStatus())) {
            CallbackDealResultDto callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(callback.getOrderNo(), report.getResponseMsg(), CebMessageEnum.CEB_ORDER_CALLBACK.getTag());
            if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
                log.info("申报单={} ,海关回执, ccs系统跳过消费", callback.getOrderNo());
                return;
            }
        }
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(callback.getEbpCode());
        CustomsOrderReceive receive = new CustomsOrderReceive();
        receive.setCustomsStatus(callback.getReturnStatus());
        receive.setEbpCode(callback.getEbpCode());
        receive.setEbcCode(callback.getEbcCode());
        receive.setDeclareOrderNo(callback.getOrderNo());
        receive.setCustomsDetail(callback.getReturnInfo());
        receive.setCustomsTime(new DateTime(callback.getReturnTime()).getMillis());
        receive.setSender(report.getSender());
        receive.setResponseMsg(report.getResponseMsg());

        customsOrderService.receive(receive);

        if (isPddOrder(ebp.getCode())) {
            try {
                OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), receive.getDeclareOrderNo(), 0L);
                if (Objects.nonNull(orderDTO) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                    CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                    ExpressDTO expressDTO = baseDataService.getExpressDTOById(inventoryDTO.getExpressId());
                    eventBus.post(new PddUpInfoEvent(receive.getDeclareOrderNo(), 2, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                            orderDTO.getOutOrderNo(), "", new DateTime(callback.getReturnTime()).getMillis()
                            , report.getResponseMsg(), callback.getReturnStatus(), callback.getReturnInfo()
                    ));
                }
            } catch (Exception e) {
                log.warn("处理异常:{}", e.getMessage(), e);
            }
            if (PDD_DATA_SEND_ENABLE) {
                //消息回执上报
                log.info("pdd消息回执上报 topic:{} tag:{} data:{} ", CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB312", JSON.toJSONString(receive));
                messageSender.sendMsg(JSON.toJSONString(receive), CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB312");
            }
        }
    }

    private boolean isPddOrder(String ebpCode) {
        return "3105961682".equals(ebpCode);
    }

    @Autowired
    private InventoryModifyDeclareBaseService inventoryModifyDeclareBaseService;

    @Override
    public void acceptInventoryCallback(CustomsReport report, InventoryCallback inventoryCallback) {
        customsSupportListener.acceptInventoryCallbackProxy(report, inventoryCallback);
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void acceptInventoryCallbackProxy(CustomsReport report, InventoryCallback inventoryCallback) {

        // 判断下是不是芥舟的单子，如果是则直接回调芥舟，不做业务处理
        // 新浙电给的回执不需要处理这个
        if (!CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(inventoryCallback.getReturnStatus())) {
            CallbackDealResultDto callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(inventoryCallback.getSn(), report.getResponseMsg(), CebMessageEnum.CEB_INVENTORY_CALLBACK.getTag());
            if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
                log.info("清单编号={} ,海关回执, ccs系统跳过消费", inventoryCallback.getInvtNo());
                return;
            }
        }

        CustomsReceive receive = new CustomsReceive();
        CustomsInventoryDTO customsInventoryDTO;
        String invtNo = inventoryCallback.getInvtNo();

        receive.setCustomsStatus(inventoryCallback.getReturnStatus());
        receive.setEbpCode(inventoryCallback.getEbpCode());
        receive.setAgentCode(inventoryCallback.getAgentCode());
        receive.setCustomsDetail(inventoryCallback.getReturnInfo());
        receive.setCustomsTime(new DateTime(inventoryCallback.getReturnTime()).getMillis());
        receive.setInvtNo(inventoryCallback.getInvtNo());
        receive.setPreNo(inventoryCallback.getPreNo());
        receive.setSender(report.getSender());
        receive.setResponseMsg(report.getResponseMsg());
//        CompanyDTO ebp = baseDataService.getCompanyDTOByCode(inventoryCallback.getEbpCode());
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(inventoryCallback.getEbpCode());
        InventoryModifyDeclareDto inventoryModifyDeclareDto = null;

        // 查变更申报的单子
        if (StringUtils.isEmpty(invtNo)) {
            customsInventoryDTO = customsInventoryService.findBySnSection(inventoryCallback.getSn());
            if (customsInventoryDTO != null) {
                inventoryModifyDeclareDto = inventoryModifyDeclareBaseService.getOrderByInvtNo(customsInventoryDTO.getInventoryNo());
            }
        } else {
            inventoryModifyDeclareDto = inventoryModifyDeclareBaseService.getOrderByInvtNo(invtNo);
        }
        if (inventoryModifyDeclareDto != null) {
            handleModifyDeclare(report, receive, inventoryModifyDeclareDto);
            return;
        }

        if (StringUtils.isEmpty(inventoryCallback.getOrderNo())) {
            customsInventoryDTO = customsInventoryService.findBySnSection(inventoryCallback.getSn());
            if (customsInventoryDTO == null) {
                return;
            } else {
                receive.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
            }
        } else {
            // 只有CQCustomsClient会走这个逻辑 但是重庆口岸已不走业务
            receive.setDeclareOrderNo(inventoryCallback.getOrderNo());
            OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), receive.getDeclareOrderNo(), 0L);
            customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        }

        orderService.receiveInventory(receive);

        if (isPddOrder(ebp.getCode())) {
            try {
                OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
                if (orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                    ExpressDTO expressDTO = baseDataService.getExpressDTOById(customsInventoryDTO.getExpressId());
                    eventBus.post(new PddUpInfoEvent(orderDTO.getDeclareOrderNo(), 2, expressDTO.getName(), customsInventoryDTO.getLogisticsNo(),
                            orderDTO.getOutOrderNo(), "", new DateTime(inventoryCallback.getReturnTime()).getMillis()
                            , report.getResponseMsg(), inventoryCallback.getReturnStatus(), inventoryCallback.getReturnInfo()
                    ));
                }
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            if (PDD_DATA_SEND_ENABLE) {
                //消息回执上报
                log.info("pdd消息回执上报 topic:{} tag:{} data:{} ", CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB622", JSON.toJSONString(receive));
                messageSender.sendMsg(JSON.toJSONString(receive), CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB622");
            }
        }
//        saveTrackLog(report, receive.getDeclareOrderNo());
    }

    private void saveTrackLog(CustomsReport report, String declareOrderNo) {
        try {
            TrackLogDTO trackLogDTO = new TrackLogDTO();
            trackLogDTO.setOrderType(InventoryOrderType.INVENTORY_ORDER.getCode());
            trackLogDTO.setDeclareOrderNo(declareOrderNo)
                    .setOldStatus(OrderStatus.DEC_ING.getValue()).setNewStatus(OrderStatus.DEC_ING.getValue())
                    .setOperateDes(TrackLogEnums.INVENTORY_RECEIPT.getCode()).setLogDes(report.getProcessData())
                    .setContent(report.getResponseMsg()).setHasXmlMessage(1);
            InventoryCallback result = JSON.parseObject(report.getProcessData(), InventoryCallback.class);
            if (CustomsStat.CUSTOMS_PASS.getValue().equals(result.getReturnStatus())) {
                trackLogDTO.setNewStatus(OrderStatus.DEC_SUCCESS.getValue());
            }
            trackLogService.submit(trackLogDTO);
            log.info("[op:CustomsSupportListener-acceptInventoryCallBack trackLog={}]", JSON.toJSONString(trackLogDTO));
        } catch (Exception e) {
            log.info("[op:CustomsSupportListener-acceptInventoryCallBack trackLog error={}]", e.getMessage());
        }
    }

    /**
     * 更新变更申报状态 记录
     *
     * @param report
     * @param receive
     * @param inventoryModifyDeclareDto
     */
    private void handleModifyDeclare(CustomsReport report, CustomsReceive receive, InventoryModifyDeclareDto inventoryModifyDeclareDto) {
        Boolean needHandle = updateOrderCustomsStatus(receive, inventoryModifyDeclareDto);
        if (needHandle) {
            updateOrderStatus(receive, inventoryModifyDeclareDto);
//            saveTrackLog(report, inventoryModifyDeclareDto.getDeclareNo());
        }
    }

    private Boolean updateOrderCustomsStatus(CustomsReceive receive, InventoryModifyDeclareDto inventoryModifyDeclareDto) {
        String returnStatus = receive.getCustomsStatus();
        Long customsTime = receive.getCustomsTime();
        // 放行回执不受约束 这里需要用海关审结来处理
        if (!CustomsStat.CUSTOMS_FINISH.getValue().equals(returnStatus)
                && inventoryModifyDeclareDto.getLastCustomsTime() != null
                && new DateTime(customsTime).isBefore(new DateTime(inventoryModifyDeclareDto.getLastCustomsTime()))) {
            log.info("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
            return false;
        } else if (CustomsStat.CUSTOMS_FINISH.getValue().equals(inventoryModifyDeclareDto.getCustomsStatus())) {
            log.info("当前变更清单海关已审结，略过不做清关状态更新，{}", JSON.toJSONString(receive));
            return false;
        } else if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(returnStatus)) {
            return false;
        } else {
            // 修改订单海关状态
            inventoryModifyDeclareBaseService.updateOrderCustomsStatus(inventoryModifyDeclareDto.getId(), receive);
            return true;
        }
    }

    private void updateOrderStatus(CustomsReceive receive, InventoryModifyDeclareDto inventoryModifyDeclareDto) {
        CustomsStatusMappingDTO mappingDTO = orderService.getCustomsStatusMappingDTO(receive);
        Integer resultStatus = mappingDTO.getStatus();
        Boolean exceptionFlag = mappingDTO.getExceptionFlag();
        Boolean updateFlag = false;
        Integer modifyDeclareStat = ModifyDeclareStat.FAIL.getValue();
        // 通过映射表的状态做一个switch case处理
        switch (CustomsActionStatus.getEnum(resultStatus)) {
            case DEC_ING:
                /**
                 * {
                 *     "ebpCode":"31149679BZ",
                 *     "invtNo":"29242021I601650106",
                 *     "preNo":"B20210711154257639",
                 *     "returnInfo":"清单变更申报成功[93F50787-9519-4D71-8B6E-15039A85E5D4][电商企业编码：330766K00Q订单编号：212550572474]",
                 *     "returnStatus":"2",
                 *     "returnTime":1627536901438,
                 *     "sn":"CI2107110012625741"
                 * }
                 */
                if (CustomsStat.SEND_ING.getValue().equals(receive.getCustomsStatus()) && receive.getCustomsDetail().contains("清单变更申报成功")) {
                    updateFlag = true;
                    modifyDeclareStat = ModifyDeclareStat.ZS_VERIFY_PENDING.getValue();
                    log.info("updateOrderStatus 变更清单{}清单变更申报成功", receive.getDeclareOrderNo());
                }
                break;
            case DEC_SUCCESS:
                // 订单转完成
                if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(inventoryModifyDeclareDto.getStatus())) {
                    log.info("updateOrderStatus 变更清单{}已放行不进行处理", receive.getDeclareOrderNo());
                    break;
                } else if (CustomsStat.CUSTOMS_FINISH.getValue().equals(receive.getCustomsStatus())) {
                    modifyDeclareStat = ModifyDeclareStat.SUCCESS.getValue();
                    updateFlag = true;
                    log.info("updateOrderStatus 变更清单{}海关审结", receive.getDeclareOrderNo());
                }
                break;
            case DEC_FAIL:
                //清单申报失败
                if (!inventoryModifyDeclareDto.getStatus().equals(ModifyDeclareStat.ZS_VERIFY_PENDING.getValue())) {
                    log.info("updateOrderStatus 变更清单{}状态非申报中，不接收异常或失败信息", receive.getDeclareOrderNo());
                }
                updateFlag = true;
                if (receive.getCustomsDetail().contains("已生税无法改单")) {
                    modifyDeclareStat = ModifyDeclareStat.FAIL.getValue();
                    log.info("updateOrderStatus 变更清单{} 已生税无法改单", receive.getDeclareOrderNo());
                } else {
                    modifyDeclareStat = ModifyDeclareStat.ZS_REJECT.getValue();
                    log.info("updateOrderStatus 变更清单{} 订单设为异常", receive.getDeclareOrderNo());
                }
                break;
            default:
                log.info("updateOrderStatus 变更清单{}状态码{} 不进行处理", receive.getDeclareOrderNo(), receive.getCustomsStatus());
                break;
        }
        if (updateFlag) {
            inventoryModifyDeclareBaseService.updateOrderStatus(inventoryModifyDeclareDto.getId(), modifyDeclareStat);
        }
    }

    @Test
    public void test() {
        Date toDate = new DateTime(1627536901438L).toDate();
        Date date = new Date(1627536901438L);
        System.out.println(toDate);
        System.out.println(date);
    }

    @Override
    public void acceptLogisticsCallback(CustomsReport report, ShipmentCallback shipmentCallback) {
        customsSupportListener.acceptLogisticsCallbackProxy(report, shipmentCallback);
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void acceptLogisticsCallbackProxy(CustomsReport report, ShipmentCallback shipmentCallback) {

        // 判断下是不是芥舟的单子，如果是则直接回调芥舟，不做业务处理
        // 新浙电给的回执不需要处理这个
        if (!CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(shipmentCallback.getReturnStatus())) {
            CallbackDealResultDto callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(shipmentCallback.getLogisticsNo(), report.getResponseMsg(), CebMessageEnum.CEB_SHIPMENT_CALLBACK.getTag());
            if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
                log.info("运单号={} ，海关回执, ccs系统跳过消费", shipmentCallback.getLogisticsNo());
                return;
            }
        }

        LogisticsReceive receive = new LogisticsReceive();
        receive.setSender(report.getSender());
        CustomsLogisticsDTO customsLogisticsDTO;
        if (StringUtils.isNotEmpty(shipmentCallback.getSn())) {
            customsLogisticsDTO = customsLogisticsService.findBySn(shipmentCallback.getSn());
        } else {
            if (StringUtils.isEmpty(shipmentCallback.getLogisticsNo())) {
                return;
            }
            customsLogisticsDTO = customsLogisticsService.findByLogisticsNo(shipmentCallback.getLogisticsNo());
        }
        if (customsLogisticsDTO == null) {
            return;
        }
        receive.setLogisticsNo(customsLogisticsDTO.getLogisticsNo());
        receive.setCustomsStatus(shipmentCallback.getReturnStatus());
        receive.setCustomsDetail(shipmentCallback.getReturnInfo());
        receive.setCustomsTime(new DateTime(shipmentCallback.getReturnTime()).getMillis());
        receive.setResponseMsg(report.getResponseMsg());
        receive.setLogisticsCode(shipmentCallback.getLogisticsCode());
        customsLogisticsService.receiveLogistics(receive);

        try {
            OrderDTO orderDTO = orderService.findByIdFull(customsLogisticsDTO.getOrderId());
            CompanyDTO ebp = baseDataService.getUnifiedCrossCodeCompanyById(orderDTO.getEbpId());
            if ("3105961682".equals(ebp.getCode()) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                ExpressDTO expressDTO = baseDataService.getExpressDTOById(inventoryDTO.getExpressId());
                eventBus.post(new PddUpInfoEvent(orderDTO.getDeclareOrderNo(), 2, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                        orderDTO.getOutOrderNo(), "", new DateTime(shipmentCallback.getReturnTime()).getMillis()
                        , report.getResponseMsg(), shipmentCallback.getReturnStatus(), shipmentCallback.getReturnInfo()
                ));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }


    @Override
    public void acceptTaxCallback(CustomsReport report, List<TaxResult> taxResults) {
        for (TaxResult taxResult : taxResults) {
            customsSupportListener.handleTax(report, taxResult);
        }
    }

    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.CUSTOMS_TAX_CALLBACK
    )
    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void handleTax(CustomsReport report, TaxResult taxResult) {
        log.info("acceptTaxCallback receive info={}", JSON.toJSONString(taxResult));

        // 判断下是不是芥舟的单子，如果是则直接回调芥舟，不做业务处理
        CallbackDealResultDto callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(taxResult.getOrderNo(), report.getResponseMsg(), CebMessageEnum.CEB_TAX_CALLBACK.getTag());
        if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
            log.info("申报单号={} 税单编号={} ,海关回执, ccs系统跳过消费", taxResult.getOrderNo(), taxResult.getInvtNo());
            return;
        }

        TaxesCompanyTaxListSubmit submit = new TaxesCompanyTaxListSubmit();
        submit.setStatus(taxResult.getStatus());
        submit.setInvtNo(taxResult.getInvtNo());
        submit.setTaxNo(taxResult.getTaxNo());
        submit.setReturnTime(taxResult.getReturnTime().getTime());
        submit.setOrderNo(taxResult.getOrderNo());
        submit.setEbcCode(taxResult.getEbcCode());
        BigDecimal amount = new BigDecimal("0.0");
        BigDecimal declarePrice = new BigDecimal("0.0");
        List<TaxItem> itemList = taxResult.getItemList();
        if (!CollectionUtils.isEmpty(itemList)) {
            for (TaxItem item : itemList) {
                declarePrice = declarePrice.add(item.getTaxPrice());
                amount = amount.add(item.getConsumptionTax()).add(item.getCustomsTax()).add(item.getValueAddedTax());
            }
        }
        //tax-api加不了declare-sdk 复制一个
        List<TaxesItemDTO> taxesItemDTOList = new ArrayList<>();
        itemList.forEach(i -> {
            TaxesItemDTO itemDTO = new TaxesItemDTO();
            BeanUtils.copyProperties(i, itemDTO);
            taxesItemDTOList.add(itemDTO);
        });
        submit.setItemList(taxesItemDTOList);
        submit.setAmount(amount);
        submit.setCustomsTax(taxResult.getCustomsTax());
        submit.setValueAddedTax(taxResult.getValueAddedTax());
        submit.setConsumptionTax(taxResult.getConsumptionTax());
        submit.setDeclarePrice(declarePrice);
//        CompanyDTO company = baseDataService.getCompanyDTOByCode(taxResult.getEbcCode());
        CompanyDTO company = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(taxResult.getEbcCode());
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findByEbcIdAndDeclareNo90Days(company.getId(), taxResult.getOrderNo());
        OrderDTO orderDTO = null;
        log.info("[op:CustomsSupportListener-acceptTaxCallback] findByDeclareNo inventoryDTO={}", JSON.toJSONString(inventoryDTO));
        try {
            if (inventoryDTO == null) {
                inventoryDTO = customsInventoryService.findByInventoryNo90Days(taxResult.getInvtNo());
                log.info("[op:CustomsSupportListener-acceptTaxCallback] findByDeclareNo找不到清单 改用清单编号查询 inventoryDTO={}", JSON.toJSONString(inventoryDTO));
            }
            if (inventoryDTO == null) {
                inventoryDTO = customsInventoryService.findByLogisticsNo90Days(taxResult.getLogisticsNo());
                log.info("[op:CustomsSupportListener-acceptTaxCallback] findByDeclareNo找不到清单 改用运单编号查询 inventoryDTO={}", JSON.toJSONString(inventoryDTO));
            }
//            if (inventoryDTO == null) {
//                // 经查询 一周内没有到达此处的单子
//                inventoryDTO = this.getRealDeclareOrderNoFromPdd(company.getId(), taxResult.getInvtNo());
//                log.info("[op:CustomsSupportListener-acceptTaxCallback] findByDeclareNo找不到清单 改用加密后的单号去PDD查询 inventoryDTO={}", JSON.toJSONString(inventoryDTO));
//            }
            if (inventoryDTO != null) {
                //pdd明文单号换成加密单号
                submit.setOrderNo(inventoryDTO.getDeclareOrderNo());
                taxesCompanyTaxListService.save(submit);
                if (Objects.isNull(inventoryDTO.getTotalTax())) {
                    customsSupportListener.updateInventoryTotalTax(inventoryDTO, amount);
                    orderService.updateOrderInternalStatus(inventoryDTO.getOrderId(), inventoryDTO.getOrderSn(), OrderInternalEnum.ORDER_PASS.getCode());
                    messageSender.sendMsg(inventoryDTO.getOrderSn(), "ccs-order-dump-c-topic");
                }
                orderDTO = orderService.findBySnSection(inventoryDTO.getOrderSn());
                try {
                    determineFinalDeclareRecordByTax(orderDTO, taxResult.getAgentCode(), taxResult.getSender());
                } catch (Exception e) {
                    log.error("税金锁定申报方式处理异常：{}", e.getMessage(), e);
                    return;
                }
                OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);

                if (Objects.equals(taxResult.getStatus(), "1") && taxResult.getTaxNo().contains("_1")) {
                    messageService.createMessage(MessageType.ORDER_REFUND_TAX
                            , Lists.newArrayList("CHANNEL-" + orderExtra.getSubmit().getChannel())
                            , inventoryDTO.getDeclareOrderNo()
                            , JSON.toJSONString(taxResult)
                            , ""
                    );
                } else {
                    // 税单回执
                    messageService.createMessage(MessageType.ORDER_TAX
                            , Lists.newArrayList("CHANNEL-" + orderExtra.getSubmit().getChannel())
                            , inventoryDTO.getDeclareOrderNo()
                            , JSON.toJSONString(taxResult)
                            , ""
                    );
                }
                taxResult.setResponseMsg(report.getResponseMsg());
                List<String> littleGiantRouteCodes = Arrays.asList(littleGiantRouteCodeList);
                List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags());
                if (orderTag.contains(DeclareOrderTagEnums.CAINIAO_WMS.getCode())
                        || (CollectionUtil.isNotEmpty(littleGiantRouteCodes) && littleGiantRouteCodes.contains(orderExtra.getSubmit().getRouteCode()))
                ) {
                    messageService.createMessageNotThrowEx(MessageType.ORDER_TAX_TO_ERP
                            , Lists.newArrayList("CHANNEL-" + orderExtra.getSubmit().getChannel())
                            , inventoryDTO.getDeclareOrderNo()
                            , JSON.toJSONString(taxResult)
                            , ""
                    );
                }
                CompanyDTO ebpCompany = baseDataService.getCompanyDTOById(inventoryDTO.getEbpId());
                //回传京东物流平台税金
                this.doCallbackJdlTax(taxResult, itemList, ebpCompany, inventoryDTO);
                // 维护清单-电子税单状态
                customsInventoryService.updateTaxBillStatusSection(TaxBillStatusEnums.SENT.getCode(), inventoryDTO.getId(), inventoryDTO.getCreateTime());
                //更新退货税金金额
                updatTotalRefundTax(taxResult, inventoryDTO);


            }
            TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
            // 轨迹日志
//                saveTaxTrackLog(report,taxResult, inventoryDTO);
//                    buildTaxTrackLogAndSend(report, submit, orderDTO);
        } catch (Exception e) {
            log.info("[op:CustomsSupportListener-acceptTaxCallback] exception, cause={}", e.getMessage(), e);
            throw e;
        }

        try {
            if (Objects.isNull(orderDTO)) {
                return;
            }
            CompanyDTO ebp = baseDataService.getUnifiedCrossCodeCompanyById(orderDTO.getEbpId());
            if ("3105961682".equals(ebp.getCode()) && orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                ExpressDTO expressDTO = baseDataService.getExpressDTOById(inventoryDTO.getExpressId());
                eventBus.post(new PddUpInfoEvent(orderDTO.getDeclareOrderNo(), 2, expressDTO.getName(), inventoryDTO.getLogisticsNo(),
                        orderDTO.getOutOrderNo(), "", new DateTime(taxResult.getReturnTime()).getMillis()
                        , report.getResponseMsg(), taxResult.getStatus(), "[放行]"
                ));
            }
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    private void determineFinalDeclareRecordByTax(OrderDTO orderDTO, String agentCode, String sender) throws Exception {
        if (Objects.isNull(agentCode)) {
            log.info("CustomsSupportListener acceptTaxCallback 获取agentCode为空");
        }
        boolean updated = CustomsDeclareUtils.determineFinalDeclareRecord(orderDTO, agentCode, sender, DeclareEnum.INVENTORY);
        if (updated) {
            log.info("CustomsSupportListener acceptTaxCallback 申报方式不一致 进行更新 ");
            orderService.updateOrderDeclareRecord(orderDTO);
        }
    }

    /**
     * 回调京东物流税金回执
     *
     * @param taxResult
     * @param itemList
     * @param company
     * @param inventoryDTO
     */
    public void doCallbackJdlTax(TaxResult taxResult, List<TaxItem> itemList, CompanyDTO company, CustomsInventoryDTO inventoryDTO) {
        try {
            JdlCallbackTaxConfigDTO jdlCallbackTaxConfigDTO = JSON.parseObject(jdlCallbackTaxConfig, JdlCallbackTaxConfigDTO.class);
            log.info("CustomsSupportListener acceptTaxCallback jdlCallbackTaxConfig={}", JSON.toJSONString(jdlCallbackTaxConfigDTO));
            if (Objects.isNull(jdlCallbackTaxConfigDTO)) {
                log.info("CustomsSupportListener acceptTaxCallback jdlCallbackTaxConfigDTO为空");
                return;
            }
            if (!jdlCallbackTaxConfigDTO.getEnable()) {
                log.info("CustomsSupportListener acceptTaxCallback jdlCallbackTaxConfigDTO未启用");
                return;
            }
            List<String> ebpCodeList = jdlCallbackTaxConfigDTO.getEbpCodeList();
            if (CollectionUtil.isNotEmpty(ebpCodeList) && ebpCodeList.contains(company.getUnifiedCrossBroderCode())) {
                // 公共请求参数
                JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(inventoryDTO.getAccountBookId());
                if (Objects.isNull(jdServProviderDTO)) {
                    log.info("CustomsSupportListener acceptTaxCallback doCallbackJdlTax jdServProviderDTO为空");
                    return;
                }
                // 创建最外层的JSONArray
                JSONArray param1 = new JSONArray();
                // 创建内层的JSONObject
                JSONObject innerJsonObject = new JSONObject();
                innerJsonObject.put("accessKey", "");

                // 创建taxReceiptHeader的JSONObject并添加属性
                JSONObject taxReceiptHeader = new JSONObject();
                taxReceiptHeader.put("guid", taxResult.getGuid());
                taxReceiptHeader.put("version", "1.0");
                innerJsonObject.put("taxReceiptHeader", taxReceiptHeader);

                // 创建taxReceiptBody的JSONObject并添加属性
                JSONObject taxReceiptBody = new JSONObject();
                taxReceiptBody.put("guid", taxResult.getGuid());
                taxReceiptBody.put("returnTime", taxResult.getReturnTime());
                taxReceiptBody.put("invtNo", taxResult.getInvtNo());
                taxReceiptBody.put("taxNo", taxResult.getTaxNo());
                taxReceiptBody.put("customsTax", taxResult.getCustomsTax());
                taxReceiptBody.put("valueAddedTax", taxResult.getValueAddedTax());
                taxReceiptBody.put("consumptionTax", taxResult.getConsumptionTax());
                taxReceiptBody.put("status", taxResult.getStatus());
                taxReceiptBody.put("entDutyNo", taxResult.getEntDutyNo());
                taxReceiptBody.put("note", taxResult.getNote());
                taxReceiptBody.put("assureCode", taxResult.getAssureCode());
                taxReceiptBody.put("ebcCode", taxResult.getEbcCode());
                taxReceiptBody.put("logisticsCode", taxResult.getLogisticsCode());
                taxReceiptBody.put("agentCode", taxResult.getAgentCode());
                taxReceiptBody.put("customsCode", taxResult.getCustomsCode());
                taxReceiptBody.put("orderNo", taxResult.getOrderNo());
                taxReceiptBody.put("logisticNo", taxResult.getLogisticsNo());
                taxReceiptBody.put("sellPlatformCode", "1");
                JSONArray taxReceiptItemList = new JSONArray();
                for (TaxItem taxItem : itemList) {
                    JSONObject taxReceiptItem = new JSONObject();
                    taxReceiptItem.put("gnum", taxItem.getGnum());
                    taxReceiptItem.put("gcode", taxItem.getGcode());
                    taxReceiptItem.put("taxPrice", taxItem.getTaxPrice());
                    taxReceiptItem.put("customsTax", taxItem.getCustomsTax());
                    taxReceiptItem.put("valueAddedTax", taxItem.getValueAddedTax());
                    taxReceiptItem.put("consumptionTax", taxItem.getConsumptionTax());
                    taxReceiptItemList.add(taxReceiptItem);
                }
                taxReceiptBody.put("taxReceiptItemList", taxReceiptItemList);

                innerJsonObject.put("taxReceiptBody", taxReceiptBody);
                param1.add(innerJsonObject);


                JdlPostDTO jdlPostDTO = new JdlPostDTO();
                jdlPostDTO.setUri(jdlCallbackTaxConfigDTO.getJdlUrl());
                jdlPostDTO.setPath("/ordertaxcallbackservice/receiveordertax");
                jdlPostDTO.setBody(JSON.toJSONString(param1));
                jdlPostDTO.setAppKey(jdServProviderDTO.getJdlAppKey());
                jdlPostDTO.setAppSecret(jdServProviderDTO.getJdlAppSecret());
                log.info("CustomsSupportListener acceptTaxCallback jdlPostDTO={}", JSON.toJSONString(jdlPostDTO));
                messageService.createMessageNotThrowEx(MessageType.JDL_ORDER_TAX
                        , Lists.newArrayList("PLAT-JD")
                        , inventoryDTO.getDeclareOrderNo()
                        , JSON.toJSONString(jdlPostDTO)
                        , ""
                );
            }
        } catch (Exception e) {
            log.error("CustomsSupportListener acceptTaxCallback 回调京东物流税金回执异常 error={}", e.getMessage(), e);
        }
    }

    private void updatTotalRefundTax(TaxResult taxResult, CustomsInventoryDTO inventoryDTO) {
        try {
            if (Objects.equals(taxResult.getStatus(), "1") && taxResult.getTaxNo().contains("_1")) {
                BigDecimal totalRefundTax = new BigDecimal("0.0");
                totalRefundTax = totalRefundTax.add(taxResult.getValueAddedTax()).add(taxResult.getConsumptionTax());
                customsInventoryService.updatTotalRefundTaxSection(totalRefundTax, inventoryDTO.getId(), inventoryDTO.getCreateTime());
            }
        } catch (Exception e) {
            log.error("处理退货总税金异常：{}", e.getMessage(), e);
        }
    }

    private void buildTaxTrackLogAndSend(CustomsReport report, TaxesCompanyTaxListSubmit submit, OrderDTO orderDTO) {
        TrackLogEsDTO trackLogEsDTO = new TrackLogEsDTO();
        trackLogEsDTO.setOrderId(orderDTO.getId());
        trackLogEsDTO.setOrderSn(orderDTO.getSn());
        trackLogEsDTO.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        trackLogEsDTO.setInternalStatus(OrderInternalEnum.DECLARE_CANCEL_FAIL.getCode());
        trackLogEsDTO.setSender(report.getSender());
        trackLogEsDTO.setReceiver(TrackLogConstantMixAll.DT_CCS);
        trackLogEsDTO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDTO.setEventDesc(TrackLogConstantMixAll.CUSTOMS_TAX_CALLBACK);
        trackLogEsDTO.setEventTime(new Date(submit.getReturnTime()));
        trackLogEsDTO.setCustomsReceipt(report.getResponseMsg());
        trackLogEsDTO.setOperator(UserUtils.getUserRealName());
        log.info("税金-轨迹日志开始记录 msg={}", JSON.toJSONString(trackLogEsDTO));
        trackLogEsProducer.sendMsg(JSON.toJSONString(trackLogEsDTO), orderDTO.getDeclareOrderNo());
    }

    /**
     * 更新清单表的税金
     * 开启新事物
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public void updateInventoryTotalTax(CustomsInventoryDTO inventoryDTO, BigDecimal amount) {
        customsInventoryService.updateTax(inventoryDTO.getId(), inventoryDTO.getCreateTime(), amount);
    }

    @Deprecated
    private CustomsInventoryDTO getRealDeclareOrderNoFromPdd(Long ebcId, String declareOrderNo) {
        CustomsInventoryDTO inventoryDTO = null;
        // TODO:这里海关返回的是明文，但是数据库存储的是密文，所以要对orderNo进行加密
        String pddUrl = PDD_HOST + "/xhr/order/findByRealDeclareSn";
        log.info("[op:PddDeclare-findByRealDeclareSn] PDD proxy, url={}, param={}", pddUrl, declareOrderNo);
        // 拼多多转发至云内应用申报
        HttpRequest httpRequest = HttpRequest.get(pddUrl, true, "realDeclareSn", declareOrderNo).header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr());
        if (httpRequest.ok()) {
            String encodeStr = httpRequest.body();
            if (!StringUtils.isEmpty(encodeStr)) {
                log.info("[op:PddDeclare-logisticsDeclare] PDD succ, res={}", encodeStr);
                inventoryDTO = customsInventoryService.findByEbcIdAndDeclareNo90Days(ebcId, declareOrderNo);
            }
        } else {
            log.info("[op:PddDeclare-logisticsDeclare] PDD fail");
        }
        return inventoryDTO;
    }

    private void saveTaxTrackLog(CustomsReport report, TaxResult taxResult, CustomsInventoryDTO inventoryDTO) {
        try {
            TrackLogDTO trackLogDTO = new TrackLogDTO();
            trackLogDTO.setOrderType(InventoryOrderType.TAX_ORDER.getCode());
            if (inventoryDTO == null) {
                trackLogDTO.setDeclareOrderNo(taxResult.getOrderNo());
            } else {
                trackLogDTO.setDeclareOrderNo(inventoryDTO.getDeclareOrderNo());
            }
            trackLogDTO.setOldStatus(OrderStatus.DEC_SUCCESS.getValue()).setNewStatus(OrderStatus.DEC_SUCCESS.getValue())
                    .setOperateDes(TrackLogEnums.TAX_RECEIPT.getCode()).setLogDes(report.getProcessData())
                    .setContent(report.getResponseMsg()).setHasXmlMessage(1);
            trackLogService.submit(trackLogDTO);
        } catch (Exception e) {
            log.info("[op:CustomsSupportListener-acceptInventoryCallBack trackLog error={}]", e.getMessage());
        }
    }

    @Override
    public void acceptTaxStatusCallback(CustomsReport report, List<TaxStatus> taxStatuses) {
        for (TaxStatus taxStatus : taxStatuses) {
            log.info("[op:CustomsTaxStatusReceiver] receive info={}", JSON.toJSONString(taxStatus));
            TaxesCompanyTaxListSubmit submit = new TaxesCompanyTaxListSubmit();
            submit.setStatus(taxStatus.getStatus());
            submit.setInvtNo(taxStatus.getInvtNo());
            submit.setTaxNo(taxStatus.getTaxNo());
            submit.setReturnTime(taxStatus.getReturnTime().getTime());
            taxesCompanyTaxListService.opsTaxStatus(submit);
            this.postTaxesStatusCallbackProcess(taxStatus);
        }
    }

    private void postTaxesStatusCallbackProcess(TaxStatus taxStatus) {

        this.doCallbackJdlTaxStatus(taxStatus);
    }

    private void doCallbackJdlTaxStatus(TaxStatus taxStatus) {
        try {
            JdlCallbackTaxConfigDTO jdlCallbackTaxConfigDTO = JSON.parseObject(jdlCallbackTaxConfig, JdlCallbackTaxConfigDTO.class);
            log.info("CustomsSupportListener postTaxesStatusCallbackProcess doCallbackJdlTaxStatus={}", JSON.toJSONString(jdlCallbackTaxConfigDTO));
            if (Objects.isNull(jdlCallbackTaxConfigDTO)) {
                log.info("CustomsSupportListener postTaxesStatusCallbackProcess jdlCallbackTaxConfigDTO为空");
                return;
            }
            if (!jdlCallbackTaxConfigDTO.getEnable()) {
                log.info("CustomsSupportListener postTaxesStatusCallbackProcess jdlCallbackTaxConfigDTO未启用");
                return;
            }
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByInventoryNoEs(taxStatus.getInvtNo());
            if (Objects.isNull(customsInventoryDTO)) {
                log.info("CustomsSupportListener postTaxesStatusCallbackProcess customsInventoryDTO为空");
                return;
            }
            List<String> ebpCodeList = jdlCallbackTaxConfigDTO.getEbpCodeList();
            CompanyDTO ebpCompany = baseDataService.getCompanyDTOById(customsInventoryDTO.getEbpId());
            if (CollectionUtil.isNotEmpty(ebpCodeList) && ebpCodeList.contains(ebpCompany.getUnifiedCrossBroderCode())) {
                // 公共请求参数
                JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(customsInventoryDTO.getAccountBookId());
                if (Objects.isNull(jdServProviderDTO)) {
                    log.info("CustomsSupportListener postTaxesStatusCallbackProcess doCallbackJdlTaxStatus jdServProviderDTO为空");
                    return;
                }
                // 创建最外层的JSONArray
                JSONArray param1 = new JSONArray();
                // 创建内层的JSONObject
                JSONObject innerJsonObject = new JSONObject();
                innerJsonObject.put("accessKey", "");
                innerJsonObject.put("returnTime", taxStatus.getReturnTime());
                innerJsonObject.put("taxNo", taxStatus.getTaxNo());
                innerJsonObject.put("status", taxStatus.getStatus());
                innerJsonObject.put("entDutyNo", taxStatus.getEntDutyNo());
                innerJsonObject.put("assureCode", taxStatus.getAssureCode());
                innerJsonObject.put("orderNo", customsInventoryDTO.getDeclareOrderNo());
                innerJsonObject.put("platformId", "1");

                param1.add(innerJsonObject);


                JdlPostDTO jdlPostDTO = new JdlPostDTO();
                jdlPostDTO.setUri(jdlCallbackTaxConfigDTO.getJdlUrl());
                jdlPostDTO.setPath("/ordertaxcallbackservice/receivetaxstatus");
                jdlPostDTO.setBody(JSON.toJSONString(param1));
                jdlPostDTO.setAppKey(jdServProviderDTO.getJdlAppKey());
                jdlPostDTO.setAppSecret(jdServProviderDTO.getJdlAppSecret());
                log.info("CustomsSupportListener postTaxesStatusCallbackProcess doCallbackJdlTaxStatus jdlPostDTO={}", JSON.toJSONString(jdlPostDTO));
                messageService.createMessageNotThrowEx(MessageType.JDL_ORDER_TAX
                        , Lists.newArrayList("PLAT-JD")
                        , customsInventoryDTO.getDeclareOrderNo()
                        , JSON.toJSONString(jdlPostDTO)
                        , ""
                );
            }
        } catch (Exception e) {
            log.error("CustomsSupportListener postTaxesStatusCallbackProcess 回调京东物流税金状态回执异常 error={}", e.getMessage(), e);
        }
    }

    @Override
    public void acceptInventoryCancelCallback(CustomsReport report, List<InventoryCancel> cancelList) {
        for (InventoryCancel cancel : cancelList) {
            try {
                customsSupportListener.cancelCallbackCore(report, cancel);
            } catch (Exception e) {
                log.error("acceptInventoryCancelCallback cancel id={} error={}", cancel.getID(), e.getMessage(), e);
            }
        }
    }

    @TenantHttpMethod(handler = DeclareOrderEsQuerier.class, type = TenantHttpType.ES_QUERY)
    public void cancelCallbackCore(CustomsReport report, InventoryCancel cancel) {

        // 判断下是不是芥舟的单子，如果是则直接回调芥舟，不做业务处理
        // 新浙电给的回执不需要处理这个
        CallbackDealResultDto callbackDealResultDto = new CallbackDealResultDto();
        if (!CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(cancel.getReturnStatus())) {
            callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(cancel.getInvtNo(), report.getResponseMsg(), CebMessageEnum.CEB_INVENTORY_CANCEL_CALLBACK.getTag());
        }
        if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
            log.info("取消单id={} ,海关回执, ccs系统跳过消费", cancel.getID());
            return;
        }
        // 如果是芥舟的单子，需要获取下自己的取消单企业内部编码
        String ccsCancelCopNo = callbackDealResultDto.getCcsCancelCopNo();
        if (StringUtils.isNotBlank(ccsCancelCopNo)) {
            log.info("设置下对应的CcsCancelCopNo={}", ccsCancelCopNo);
            cancel.setID(ccsCancelCopNo);
        }

        CustomsInventoryCancelDTO oldInfoDto;
        if (isLong(cancel.getID())) {
            Long id = Long.parseLong(cancel.getID());
            oldInfoDto = customsInventoryCancelService.findById(id);
        } else {
            oldInfoDto = customsInventoryCancelService.findByInvoSn(cancel.getID());
        }
        if (Objects.isNull(oldInfoDto)) {
            log.error("CustomsSupportListener cancelCallbackCore id={} inventoryCancel not found", cancel.getID());
            return;
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(oldInfoDto.getRefInvoSn());
        if (Objects.isNull(customsInventoryDTO)) {
            log.error("CustomsSupportListener cancelCallbackCore inveSn={} customsInventoryDTO not found", oldInfoDto.getRefInvoSn());
            return;
        }
        CustomsReceive receive = new CustomsReceive();
        receive.setCustomsStatus(cancel.getReturnStatus());
        receive.setEbpCode(cancel.getEbpCode());
        receive.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
        receive.setCustomsDetail(cancel.getReturnInfo());
        receive.setCustomsTime(new DateTime(cancel.getReturnTime()).getMillis());
        receive.setSender(report.getSender());
        receive.setResponseMsg(report.getResponseMsg());
        String receiveResult = customsInventoryCancelService.receive(receive);
        log.info("CustomsSupportListener cancelCallbackCore receiveResult={}", receiveResult);
        //回传erp原始报文
        String activeData = JSON.toJSONString(new OrderActiveInfo(oldInfoDto.getRefOrderId()).buildResponseMsg(report.getResponseMsg()));
        ArrayList<String> subscribeList = Lists.newArrayList("CHANNEL-1");
        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        List<String> littleGiantRouteCodes = Arrays.asList(littleGiantRouteCodeList);
        if (DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags()).contains(DeclareOrderTagEnums.CAINIAO_WMS.getCode())
                || (CollectionUtil.isNotEmpty(littleGiantRouteCodes) && littleGiantRouteCodes.contains(orderExtra.getSubmit().getRouteCode()))
        ) {
            messageService.createMessageNotThrowEx(MessageType.ORDER_CUSTOMS_CANCEL_TO_ERP, subscribeList, oldInfoDto.getRefOrderSn(), activeData, "");
        }
    }

    public static boolean isLong(String str) {
        Pattern pattern = Pattern.compile("^-?\\d+$");
        Matcher matcher = pattern.matcher(str);
        if (matcher.matches()) {
            try {
                Long.parseLong(str);
                return true;
            } catch (NumberFormatException e) {
                return false;
            }
        } else {
            return false;
        }
    }


    @Override
    public void acceptInventoryRefundCallback(CustomsReport report, List<InventoryRefund> refundList) {
        for (InventoryRefund refund : refundList) {
            try {
                String id = refund.getID();
                if (Objects.nonNull(id) && id.startsWith("CB")) {
                    log.info("退单id={} 海关回执 ccs系统跳过消费", refund.getID());
                    continue;
                }
                customsSupportListener.refundCallbackCore(report, refund);
            } catch (Exception e) {
                log.error("acceptInventoryRefundCallback refund id={} error={}", refund.getID(), e.getMessage(), e);
            }
        }
    }

    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void refundCallbackCore(CustomsReport report, InventoryRefund refund) {

        // 新浙电给的回执不需要处理这个
        CallbackDealResultDto callbackDealResultDto = new CallbackDealResultDto();
        if (!CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(refund.getReturnStatus())) {
            callbackDealResultDto = externalDeclareOrderService.externalDeclareOrderMsgDeal(refund.getInvtNo(), report.getResponseMsg(), CebMessageEnum.CEB_INVENTORY_REFUND_CALLBACK.getTag());
        }
        if (!callbackDealResultDto.getCcsConsumerMsgEnable()) {
            log.info("退货单id={} ,海关回执, ccs系统跳过消费", refund.getID());
            return;
        }
        // 如果是芥舟自申报单，需要转换下ccs系统自己的企业内部编码
        String ccsRefundCopNo = callbackDealResultDto.getCcsRefundCopNo();
        if (StringUtils.isNotBlank(ccsRefundCopNo)) {
            log.info("设置下对应的ccsRefundCopNo={}", ccsRefundCopNo);
            refund.setID(ccsRefundCopNo);
        }

        RefundOrderInfoDto oldRefundOrderInfoDto;
        if (isLong(refund.getID())) {
            Long id = Long.parseLong(refund.getID());
            oldRefundOrderInfoDto = refundOrderService.findBy(id);
        } else {
            oldRefundOrderInfoDto = refundOrderService.findByBillSn(refund.getID());
        }
        CustomsReceive receive = new CustomsReceive();
        receive.setCustomsStatus(refund.getReturnStatus());
        receive.setEbpCode(refund.getEbpCode());
        receive.setDeclareOrderNo(oldRefundOrderInfoDto.getRefDeclareNo());
        receive.setCustomsDetail(refund.getReturnInfo());
        receive.setCustomsTime(refund.getReturnTime().getTime());
        receive.setSender(report.getSender());
        receive.setResponseMsg(report.getResponseMsg());
        String receiveResult = refundOrderService.receive(receive);
        log.info("CustomsSupportListener refundCallbackCore receiveResult={}", receiveResult);
    }

    @Override
    public void acceptDeliverCallback(CustomsReport report, DeliverResult result) {
//        CompanyDTO ebp = companyService.findByCode(result.getEbpCode());
        CompanyDTO ebp = companyService.findByUnifiedCrossBroderCode(result.getEbpCode());
        OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), result.getDeclareOrderNo(), 0L);
        orderService.deliver(orderDTO, result.getLogisticsNo(), result.getDepotOrderNo(), result.getWeight(), result.getShipmentTime());
    }
}
