package com.danding.cds.service.mq.consumer.es;

import cn.hutool.core.date.DateTime;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.otter.canal.protocol.FlatMessage;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.service.es.CustomsSingleInventoryEsDao;
import com.danding.cds.v2.bean.enums.StorageAttrEnums;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.monitor.packageout.dto.data.SupervisionNodeCommonDTO;
import com.dt.component.canal.mq.AbstractCanalMQService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.annotation.ConsumeMode;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 功能描述:  清单申报
 * 创建时间:  2021/4/2
 *
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(topic = "danding_ccs_customs_inventory_c_topic", consumerGroup = "ccs_customs_inventory_c_save_to_es", consumeMode = ConsumeMode.ORDERLY)
public class CustomsInventorySaveToEsConsumer extends AbstractCanalMQService<CustomsInventoryDTO> implements RocketMQListener<FlatMessage> {

    @Autowired
    private CustomsSingleInventoryEsDao customsSingleInventoryEsDao;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private OrderService orderService;

    @Autowired
    private BaseDataService baseDataService;


    @Autowired
    private RocketMQTemplate rocketMQTemplate;

    @Override
    public void onMessage(FlatMessage message) {
        process(message);
    }

    /**
     * 新增操作
     *
     * @param customsInventoryDTO
     */
    @Override
    public void insert(CustomsInventoryDTO customsInventoryDTO) {
        try {
            Long tenantryId = customsInventoryDTO.getTenantryId();
            SimpleTenantHelper.setTenantId(tenantryId);
            List<CustomsInventoryItemDTO> list = customsInventoryService.listItemByIdSectionNoFilter(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            customsSingleInventoryEsDao.esSave(customsInventoryDTO, list);
        } catch (Exception e) {
            log.error("CustomsInventorySaveToEsConsumer insert error: " + e.getMessage(), e);
        }
    }

    /**
     * 对于更新操作来讲，before 中的属性只包含变更的属性，after 包含所有属性，通过对比可发现那些属性更新了
     *
     * @param before
     * @param after
     */
    @Override
    public void update(CustomsInventoryDTO before, CustomsInventoryDTO after) {
        try {
            log.info("before={} after={}", JSON.toJSONString(before), JSON.toJSONString(after));
            Long tenantryId = after.getTenantryId();
            SimpleTenantHelper.setTenantId(tenantryId);
            List<CustomsInventoryItemDTO> list = customsInventoryService.listItemByIdSectionNoFilter(after.getId(), after.getCreateTime());
            customsSingleInventoryEsDao.esSave(after, list);
            this.supervisionNodeReport(before, after);
        } catch (Exception e) {
            log.error("CustomsInventorySaveToEsConsumer update error: " + e.getMessage(), e);
        }
    }

    /**
     * 删除操作
     *
     * @param customsInventoryDTO
     */
    @Override
    public void delete(CustomsInventoryDTO customsInventoryDTO) {
        Long tenantryId = customsInventoryDTO.getTenantryId();
        SimpleTenantHelper.setTenantId(tenantryId);
        log.error("删除 {}", JSONUtil.toJsonStr(customsInventoryDTO));
    }


    /**
     * 时效监管系统节点上报
     *
     * @param before
     * @param after
     */
    public void supervisionNodeReport(CustomsInventoryDTO before, CustomsInventoryDTO after) {
        OrderDTO orderDTO = orderService.findBySnSection(after.getOrderSn());
        try {
            if (orderDTO != null && before != null) {
                if (Objects.isNull(orderDTO.getSystemGlobalSn())) {
                    log.info("supervisionNodeReport gs单号为空 不处理");
                    return;
                }
                CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(after.getAccountBookId());
                StorageAttrEnums storageAttrEnums = StorageAttrEnums.getEnums(customsBookDTO.getStorageAttr());
                List<StorageAttrEnums> allowStorageAttrEnumList = Arrays.asList(StorageAttrEnums.SELF_STORAGE, StorageAttrEnums.PLATFORM_STORAGE);
                if (!allowStorageAttrEnumList.contains(storageAttrEnums)) {
                    log.warn("仓储性质=【{}】, 不进行节点上报", storageAttrEnums.getDesc());
                    return;
                }
                SupervisionNodeCommonDTO supervisionNodeCommonDTO = new SupervisionNodeCommonDTO();
                supervisionNodeCommonDTO.setGlobalNo(orderDTO.getSystemGlobalSn());
                supervisionNodeCommonDTO.setNodeTime(DateTime.now().getTime());
                supervisionNodeCommonDTO.setTenantId(orderDTO.getTenantryId());
                //清单放行
                if (Objects.nonNull(before.getStatus()) && !Objects.equals(before.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue()) && Objects.equals(after.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    if (Objects.equals(customsBookDTO.getStorageAttr(), StorageAttrEnums.PLATFORM_STORAGE.getCode())) {
                        supervisionNodeCommonDTO.setIsPlatform(true);
                    } else if (Objects.equals(customsBookDTO.getStorageAttr(), StorageAttrEnums.SELF_STORAGE.getCode())) {
                        supervisionNodeCommonDTO.setIsPlatform(false);
                    }
                    supervisionNodeCommonDTO.setNodeId(205L);
                    //上报清单编号、放行时间、账册编号、区内企业
                    supervisionNodeCommonDTO.setClearOrderNo(after.getInventoryNo());
                    supervisionNodeCommonDTO.setCustomsBookId(after.getAccountBookId());
                    supervisionNodeCommonDTO.setAreaCompanyId(after.getAreaCompanyId());
                    supervisionNodeCommonDTO.setNodeTime(after.getCustomsPassTime().getTime());

                    SendResult sendResult = rocketMQTemplate.syncSendOrderly("dt_monitor_common_node_topic", JSON.toJSONString(supervisionNodeCommonDTO), orderDTO.getSystemGlobalSn());
                    log.info("清单状态轨迹上报 单号:{} 节点:{} 结果：{}", orderDTO.getSystemGlobalSn(), "申报放行", JSON.toJSONString(sendResult));
                }
                // 核注出区上报
                if (Objects.nonNull(before.getExitRegionStatus()) && !Objects.equals(before.getExitRegionStatus(), after.getExitRegionStatus()) && Objects.equals(after.getExitRegionStatus(), 1)) {
                    supervisionNodeCommonDTO.setNodeId(204L);
                    SendResult sendResult = rocketMQTemplate.syncSendOrderly("dt_monitor_common_node_topic", JSON.toJSONString(supervisionNodeCommonDTO), orderDTO.getSystemGlobalSn());
                    log.info("清单状态轨迹上报 单号:{} 节点:{} 结果：{}", orderDTO.getSystemGlobalSn(), "申报出区", JSON.toJSONString(sendResult));
                }
                // 交接关联
                if (Objects.nonNull(before.getHandoverStatus())
                        && !Objects.equals(before.getHandoverStatus(), after.getHandoverStatus())
                        && Objects.equals(after.getHandoverStatus(), 1)
                        && Objects.equals(customsBookDTO.getStorageAttr(), StorageAttrEnums.SELF_STORAGE.getCode())
                ) {
                    supervisionNodeCommonDTO.setNodeId(203L);
                    SendResult sendResult = rocketMQTemplate.syncSendOrderly("dt_monitor_common_node_topic", JSON.toJSONString(supervisionNodeCommonDTO), orderDTO.getSystemGlobalSn());
                    log.info("清单状态轨迹上报 单号:{} 节点:{} 结果：{}", orderDTO.getSystemGlobalSn(), "交接", JSON.toJSONString(sendResult));
                }
                if (Objects.nonNull(before.getReviewStatus()) && !Objects.equals(before.getReviewStatus(), after.getReviewStatus())) {
                    InventoryReviewStatus reviewStatus = InventoryReviewStatus.getEnum(after.getReviewStatus());
                    switch (reviewStatus) {
                        case LINKED_EXPORT_ORDER:
                            supervisionNodeCommonDTO.setNodeId(207L);
                            break;
                        case LINKED_REVIEW:
                            supervisionNodeCommonDTO.setNodeId(208L);
                            break;
                        case DECLARING:
                            supervisionNodeCommonDTO.setNodeId(209L);
                            break;
                        case PASS_NOT_COMPLETED:
                            supervisionNodeCommonDTO.setNodeId(210L);
                            break;
                        case VERIFICATION_COMPLETED:
                            supervisionNodeCommonDTO.setNodeId(204L);
                            break;
                        case UNLINKED_ENDORSEMENT: // 逆向节点回退
                            if (StorageAttrEnums.PLATFORM_STORAGE.equals(storageAttrEnums)) { // 平台仓储
                                supervisionNodeCommonDTO.setNodeId(205L);
                            }
                            if (StorageAttrEnums.SELF_STORAGE.equals(storageAttrEnums)) { // 自营
                                supervisionNodeCommonDTO.setNodeId(203L);
                            }
                            break;
                        default:
                            break;
                    }
                    if (Objects.nonNull(reviewStatus) && Objects.nonNull(supervisionNodeCommonDTO.getNodeId())) {
                        SendResult sendResult = rocketMQTemplate.syncSendOrderly("dt_monitor_common_node_topic", JSON.toJSONString(supervisionNodeCommonDTO), orderDTO.getSystemGlobalSn());
                        log.info("清单状态轨迹上报 单号:{} 节点:{} 结果：{}", orderDTO.getSystemGlobalSn(), reviewStatus.getDesc(), JSON.toJSONString(sendResult));
                    }
                }
            }
        } catch (Exception e) {
            log.error("CustomsInventorySaveToEsConsumer mqSend error: " + orderDTO.getSystemGlobalSn() + "  " + e.getMessage(), e);
        }
    }

    public static void main(String[] args) {
        String beforeString = "{\"exitRegionStatus\":0,\"updateTime\":*************}";
        String afterString = "{\"accountBookId\":22,\"afterSalesStatus\":1,\"agentCompanyId\":2,\"areaCompanyId\":2,\"assureCompanyId\":2,\"buyerIdNumber\":\"B5xJYBgT0EMFHTZqE1r1fa5+ZQ1Nz3GVqiE/+ogJCTw=\",\"buyerIdType\":\"1\",\"buyerName\":\"ZgcVx6i4B0MXa1KUy70Xfw==\",\"buyerTelNumber\":\"Qbk0xdT5BqEiqcfL8eDYXQ==\",\"consigneeAddress\":\"NUaTesWhTb3WQP6pl92ONK9uFPViN8RSZBHyREIkzeg=\",\"createTime\":*************,\"customs\":\"HANGZHOU\",\"customsDetail\":\"[Code:2600;Desc:放行]\",\"customsPassTime\":*************,\"customsStatus\":\"800\",\"declareFrequency\":1,\"declareOrderNo\":\"*******************\",\"ebcId\":2,\"ebpId\":1,\"exitRegionStatus\":1,\"expressId\":14,\"extraJson\":\"{\\\"consigneeCity\\\":\\\"昌都市\\\",\\\"consigneeDistrict\\\":\\\"丁青县\\\",\\\"consigneeProvince\\\":\\\"西藏自治区\\\",\\\"consigneeStreet\\\":\\\"\\\",\\\"customsBookNo\\\":\\\"L2992B22A001\\\",\\\"discountFee\\\":0.00,\\\"expressCode\\\":\\\"VIRTUAL\\\",\\\"notItem\\\":false,\\\"payCompanyCode\\\":\\\"3301964J31\\\",\\\"payTransactionId\\\":\\\"DPN20230116095709600\\\",\\\"routeCode\\\":\\\"PathErpV2\\\",\\\"taxFee\\\":0.00,\\\"tenantName\\\":\\\"Wangtao007\\\",\\\"tenantOuterId\\\":\\\"1003715\\\"}\",\"feeAmount\":0.0,\"grossWeight\":150.0,\"handoverStatus\":1,\"id\":821683676851994625,\"insureAmount\":0.0,\"inventoryNo\":\"OD20230113153229718\",\"logisticsCompanyId\":239,\"logisticsNo\":\"DDY167383425168791032\",\"netWeight\":100.0,\"note\":\"\",\"orderId\":821683676327706624,\"orderSn\":\"OS2301160957781401\",\"preNo\":\"B20200615494000227\",\"reviewStatus\":0,\"sn\":\"CI2301160957100963\",\"status\":100,\"tenantryId\":1001,\"updateTime\":1673839982000}\n";
        CustomsInventoryDTO customsInventoryDTO1 = JSON.parseObject(beforeString, CustomsInventoryDTO.class);
        CustomsInventoryDTO customsInventoryDTO2 = JSON.parseObject(afterString, CustomsInventoryDTO.class);
        //mqSend(customsInventoryDTO1,customsInventoryDTO2);

    }

}
