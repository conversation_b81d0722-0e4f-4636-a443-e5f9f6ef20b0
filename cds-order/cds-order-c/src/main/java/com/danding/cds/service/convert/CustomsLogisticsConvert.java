package com.danding.cds.service.convert;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.dto.OrderSubmitItemDto;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsItem;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsSubmit;
import com.danding.cds.customs.logistics.api.enums.LogisticsStatus;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.base.bean.dao.CustomsLogisticsDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.data.service.SequenceServiceBaseService;
import com.danding.cds.order.base.mapper.CustomsLogisticsMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
public class CustomsLogisticsConvert {

    @Autowired
    private SequenceServiceBaseService sequenceServiceBaseService;

    @Autowired
    private CustomsLogisticsMapper customsLogisticsMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Deprecated
    public CustomsLogisticsDO preCustomsLogistics(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {
        OrderSubmitDto orderSubmitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        return this.preCustomsLogistics(userId, orderSubmitDto, workSpace);
    }

    public CustomsLogisticsDO preCustomsLogistics(Long userId, OrderSubmitDto submit, OrderConvertWorkSpace workSpace) {
        CustomsLogisticsSubmit logisticsSubmit = new CustomsLogisticsSubmit();
        logisticsSubmit.setRouteCode(submit.getRouteCode());
        logisticsSubmit.setOutOrderNo(submit.getOutOrderNo());
        logisticsSubmit.setDeclareOrderNo(submit.getDeclareOrderNo());
        logisticsSubmit.setExpressCode(submit.getExpressCode());
        logisticsSubmit.setCustoms(submit.getCustomsCode());
        logisticsSubmit.setConsigneeName(submit.getConsigneeName());
        logisticsSubmit.setConsigneeProvince(submit.getConsigneeProvince());
        logisticsSubmit.setConsigneeCity(submit.getConsigneeCity());
        logisticsSubmit.setConsigneeDistrict(submit.getConsigneeDistrict());
        if (org.springframework.util.StringUtils.isEmpty(submit.getConsigneeStreet())) {
            logisticsSubmit.setConsigneeStreet("");
        } else {
            logisticsSubmit.setConsigneeStreet(submit.getConsigneeStreet());
        }
        logisticsSubmit.setConsigneeAddress(submit.getConsigneeAddress());
        logisticsSubmit.setConsigneeTel(submit.getConsigneeTel());
        logisticsSubmit.setLogisticsNo(submit.getLogisticsNo());
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (OrderSubmitItemDto item : submit.getItemList()) {
            goodsAmount = goodsAmount.add(item.getUnitPrice().multiply(new BigDecimal(item.getCount())));
        }
        logisticsSubmit.setGoodsTotalAmount(goodsAmount);
        logisticsSubmit.setFeeAmount(submit.getFeeAmount());
        logisticsSubmit.setGrossWeight(submit.getGrossWeight());
        logisticsSubmit.setNetWeight(submit.getNetWeight());
        logisticsSubmit.setInsureAmount(submit.getInsureAmount());
        if (logisticsSubmit.getGrossWeight() == null || BigDecimal.ZERO.compareTo(logisticsSubmit.getGrossWeight()) == 0) {
            logisticsSubmit.setGrossWeight(BigDecimal.ONE);
        }
        if (logisticsSubmit.getNetWeight() == null || BigDecimal.ZERO.compareTo(logisticsSubmit.getNetWeight()) == 0) {
            logisticsSubmit.setNetWeight(BigDecimal.ONE);
        }
        logisticsSubmit.setTenantId(submit.getTenantOuterId());
        logisticsSubmit.setItemList(submit.getItemList().stream().map((OrderSubmitItemDto item) -> {
            CustomsLogisticsItem logisticsItem = new CustomsLogisticsItem();
            logisticsItem.setName(item.getItemName());
            logisticsItem.setCount(item.getCount());
            logisticsItem.setPrice(item.getUnitPrice());
            return logisticsItem;
        }).collect(Collectors.toList()));
        if (userId == null || userId.equals(0L)) {
            userId = 1L;
        }
        return this.preCustomsLogistics(userId, logisticsSubmit, workSpace);
    }


    public CustomsLogisticsDO preCustomsLogistics(Long userId, CustomsLogisticsSubmit submit, OrderConvertWorkSpace workSpace) {
        if (LongUtil.isNone(userId)) {
            throw new ArgsErrorException("用户ID不能为空");
        }
        if (StringUtils.isEmpty(submit.getOutOrderNo())) {
            throw new ArgsErrorException("上游单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getExpressCode())) {
            throw new ArgsErrorException("快递编码不能为空");
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(submit.getExpressCode());
        if (expressDTO == null || expressDTO.getEnable() != 1) {
            throw new ArgsErrorException("对应快递方式不存在或未启用");
        }
        CompanyDTO companyDTO = baseDataService.getUnifiedCrossCodeCompanyById(expressDTO.getExpressCompanyId());
        if (companyDTO.getEnable() != 1) {
            throw new ArgsErrorException("对应物流企业未启用");
        }
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        if (routeDTO == null || routeDTO.getEnable() != 1) {
            throw new ArgsErrorException("申报路径不存在或未启用,路径标识：" + submit.getRouteCode());
        }
        if (CustomsDistrictEnum.NULL.equals(CustomsDistrictEnum.getEnum(submit.getCustoms()))) {
            throw new ArgsErrorException("口岸代码不合法，请参数文档传输");
        }
        if (StringUtils.isEmpty(submit.getConsigneeName()) || StringUtils.isEmpty(submit.getConsigneeTel())) {
            throw new ArgsErrorException("收件人姓名或电话不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeProvince()) || StringUtils.isEmpty(submit.getConsigneeCity())
                || StringUtils.isEmpty(submit.getConsigneeDistrict()) || StringUtils.isEmpty(submit.getConsigneeAddress())
        ) {
            throw new ArgsErrorException("收件人省、市、区、地址不能为空");
        }
        if (submit.getGrossWeight() == null || BigDecimal.ZERO.compareTo(submit.getGrossWeight()) == 0) {
            throw new ArgsErrorException("毛重不能为0");
        }
        if (CollectionUtils.isEmpty(submit.getItemList())) {
            throw new ArgsErrorException("商品项不能为空");
        }
        CustomsLogisticsDO logisticsDO = new CustomsLogisticsDO();
        logisticsDO.setUserId(userId);
        logisticsDO.setSn(sequenceServiceBaseService.generateCustomsLogisticsSn());
        logisticsDO.setOutOrderNo(submit.getOutOrderNo());
        logisticsDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        logisticsDO.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        if (StringUtils.isEmpty(submit.getLogisticsNo())) {
            // 需前置获取运单号
            logisticsDO.setLogisticsStatus(LogisticsStatus.INIT.getValue());
        } else {
            logisticsDO.setLogisticsNo(submit.getLogisticsNo());
            logisticsDO.setLogisticsStatus(LogisticsStatus.READY.getValue());
        }
        logisticsDO.setExpressId(expressDTO.getId());
        logisticsDO.setExpressCode(expressDTO.getCode());
        logisticsDO.setLogisticsCompanyId(companyDTO.getId());
        logisticsDO.setAgentCompanyId(routeDTO.getLogisticsDeclareCompanyId());
        logisticsDO.setCustoms(submit.getCustoms());
        logisticsDO.setCustomsStatus(CustomsStat.NULL.getValue());
        logisticsDO.setConsigneeName(submit.getConsigneeName());
        logisticsDO.setConsigneeProvince(submit.getConsigneeProvince());
        logisticsDO.setConsigneeCity(submit.getConsigneeCity());
        logisticsDO.setConsigneeDistrict(submit.getConsigneeDistrict());
        logisticsDO.setConsigneeStreet(submit.getConsigneeStreet());
        logisticsDO.setConsigneeAddress(submit.getConsigneeAddress());
        logisticsDO.setConsigneeTel(submit.getConsigneeTel());
        logisticsDO.setGoodsTotalAmount(submit.getGoodsTotalAmount());
        logisticsDO.setFeeAmount(submit.getFeeAmount());
        logisticsDO.setGrossWeight(submit.getGrossWeight());
        logisticsDO.setNetWeight(submit.getNetWeight());
        logisticsDO.setInsureAmount(submit.getInsureAmount());
        //过滤下非保数据
        List<CustomsLogisticsItem> filterFbItem = submit.getItemList().stream().filter(item -> !OrderItemTagEnum.containsFbGifts(item.getItemTag())).collect(Collectors.toList());
        submit.setItemList(filterFbItem);
        List<CustomsLogisticsItem> itemList = new ArrayList<>();
        for (CustomsLogisticsItem item : submit.getItemList()) {
            CustomsLogisticsItem logisticsItem = new CustomsLogisticsItem();
            logisticsItem.setName(item.getName());
            logisticsItem.setCount(item.getCount());
            logisticsItem.setPrice(item.getPrice());
            logisticsItem.setItemTag(item.getItemTag());
            itemList.add(logisticsItem);
        }
        logisticsDO.setItemJson(JSON.toJSONString(itemList));
        if (!StringUtils.isEmpty(submit.getTenantId())) {
            logisticsDO.setTenantId(submit.getTenantId());
        }
        return logisticsDO;
    }

    public void save(OrderDO orderDO, RichOrder richOrder) {
        CustomsLogisticsDO customsLogisticsDO = richOrder.getCustomsLogisticsDO();
        if (customsLogisticsDO != null) {
            if (richOrder.getDeclareIsNew()) {
                this.add(orderDO, customsLogisticsDO);
            } else {
                // 这里通过路径判断，新的申报路径是否已经存在老的申报路径里，减少查库，存在则认为修改，否则新增
                Boolean inOldAction = richOrder.inOldAction(RouteActionEnum.DECLARE_LOGISTICS.getCode());
                if (inOldAction) {
                    this.update(orderDO, customsLogisticsDO);
                } else {
                    this.add(orderDO, customsLogisticsDO);
                }
            }
        }
    }

    private void update(OrderDO orderDO, CustomsLogisticsDO customsLogisticsDO) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setOrderId(orderDO.getId());
        CustomsLogisticsDO old = customsLogisticsMapper.selectOne(condition);
        CustomsLogisticsDO template = new CustomsLogisticsDO();
        template.setId(old.getId());
        if (!CustomsActionStatus.DEC_SUCCESS.getValue().equals(old.getStatus())) {
            template.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        }
        if (!StringUtils.isEmpty(customsLogisticsDO.getLogisticsNo())) {
            template.setLogisticsNo(customsLogisticsDO.getLogisticsNo());
        }
        template.setItemJson(customsLogisticsDO.getItemJson());
        template.setConsigneeTel(customsLogisticsDO.getConsigneeTel());
        template.setConsigneeName(customsLogisticsDO.getConsigneeName());
        template.setConsigneeProvince(customsLogisticsDO.getConsigneeProvince());
        template.setConsigneeCity(customsLogisticsDO.getConsigneeCity());
        template.setConsigneeDistrict(customsLogisticsDO.getConsigneeDistrict());
        template.setConsigneeAddress(customsLogisticsDO.getConsigneeAddress());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        customsLogisticsMapper.updateByPrimaryKeySelective(template);
    }

    private void add(OrderDO orderDO, CustomsLogisticsDO customsLogisticsDO) {
        customsLogisticsDO.setOrderId(orderDO.getId());
        customsLogisticsDO.setCreateTime(new Date());
        customsLogisticsDO.setOrderSn(orderDO.getSn());
        customsLogisticsDO.setId(sequenceServiceBaseService.getUniqueId());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setCreateAndUpdateBy(customsLogisticsDO);
        }
        customsLogisticsMapper.insertSelective(customsLogisticsDO);
    }


}
