package com.danding.cds.utils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.declare.base.component.util.DeclareUtils;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.route.CompanyRouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 申报方式工具类
 * @date 2022/9/1 22:35
 */
@Slf4j
public class CustomsDeclareUtils {
    public static RouteDeclareConfig getDeclareRecord(WrapBeanInfo info, DeclareEnum declareEnum) {
        RouteDeclareConfig routeDeclareConfig = DeclareUtils.getDeclareWayFromConfig(info, declareEnum);
        log.info("getDeclareRecord routeDeclareConfig={}", JSON.toJSON(routeDeclareConfig));
        return routeDeclareConfig;
    }

    public static OrderDTO populateDeclareRecord(List<RouteDeclareConfig> routeDeclareConfigList, OrderDTO orderDTO, DeclareEnum declareEnum) {
        routeDeclareConfigList.forEach(r ->
                populateDeclareRecord(r, orderDTO, declareEnum)
        );
        return orderDTO;
    }

    public static RouteDeclareConfig populateDeclareRecord(RouteDeclareConfig routeDeclareConfig, OrderDTO orderDTO, DeclareEnum declareEnum) {
        if (Objects.isNull(routeDeclareConfig)) {
            return null;
        }
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        List<RouteDeclareConfig> routeDeclareConfigList = new ArrayList<>();
        if (StringUtils.isBlank(declareWayRecord)) {
            routeDeclareConfigList.add(routeDeclareConfig);
        } else {
            routeDeclareConfigList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            boolean changeFlag = false;
            for (RouteDeclareConfig d : routeDeclareConfigList) {
                if (Objects.equals(d.getType(), declareEnum.getType())) {
                    BeanUtils.copyProperties(routeDeclareConfig, d);
                    changeFlag = true;
                }
            }
            //如果之前没有这个类型 则直接添加
            if (!changeFlag) {
                routeDeclareConfigList.add(routeDeclareConfig);
            }
        }
        String result = JSON.toJSONString(routeDeclareConfigList);
        orderDTO.setDeclareWayRecord(result);
        return routeDeclareConfig;
    }

    public static RouteDeclareConfig buildDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO, DeclareEnum declareEnum) {
        RouteDeclareConfig routeDeclareConfig = getDeclareRecord(info, declareEnum);
        log.info("buildDeclareRecord routeDeclareConfig={}", JSON.toJSON(routeDeclareConfig));
        return populateDeclareRecord(routeDeclareConfig, orderDTO, declareEnum);
    }

    /**
     * 如果申报单中记录了申报方式 则取出替换
     * 目前只有撤单退货有用
     *
     * @param orderDTO
     * @param model
     */
    public static void replaceOrderDeclareRecordWay(OrderDTO orderDTO, WrapBeanInfo model, DeclareEnum declareEnum) {
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        if (Objects.nonNull(declareWayRecord)) {
            List<RouteDeclareConfig> routeDeclareConfigList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            RouteDeclareConfig routeDeclareConfig = routeDeclareConfigList.stream().filter(d -> Objects.equals(d.getType(), declareEnum.getType())).findAny().orElse(null);
            if (Objects.nonNull(routeDeclareConfig)) {
                RouteInfo routeInfo = model.getRouteInfo();
                List<RouteDeclareConfig> routeDeclareConfigs = routeInfo.getRouteDeclareConfigList();
                Boolean changeFlag = false;
                for (RouteDeclareConfig r : routeDeclareConfigList) {
                    if (Objects.equals(r.getType(), declareEnum.getType())) {
                        BeanUtils.copyProperties(routeDeclareConfigs, r);
                        changeFlag = true;
                    }
                }
                if (!changeFlag) {
                    routeDeclareConfigList.add(routeDeclareConfig);
                }
                routeInfo.setRouteDeclareConfigList(routeDeclareConfigList);
                model.setRouteInfo(routeInfo);
            }
        }
    }

    /**
     * 用于获取最终申报方式
     * 目前只有撤单和退货使用
     *
     * @param orderDTO
     * @param declareEnum
     * @return
     */
    public static String getFinalDeclareImpl(OrderDTO orderDTO, DeclareEnum declareEnum) {
        String finalImpl = null;
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        if (Objects.nonNull(declareWayRecord)) {
            List<RouteDeclareConfig> routeDeclareConfigList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            RouteDeclareConfig routeDeclareConfig = routeDeclareConfigList.stream().filter(d -> Objects.equals(d.getType(), declareEnum.getType())).findAny().orElse(null);
            if (Objects.nonNull(routeDeclareConfig)) {
                if (Objects.nonNull(routeDeclareConfig.getFinalImpl())) {
                    finalImpl = routeDeclareConfig.getFinalImpl();
                }
            }
        }
        return finalImpl;
    }

    public static String getCancelRefundDxpId(OrderDTO orderDTO, DeclareEnum declareEnum) {
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        if (Objects.nonNull(declareWayRecord)) {
            List<RouteDeclareConfig> routeDeclareConfigList = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
            RouteDeclareConfig routeDeclareConfig = routeDeclareConfigList.stream().filter(d -> Objects.equals(d.getType(), declareEnum.getType())).findAny().orElse(null);
            if (Objects.nonNull(routeDeclareConfig)) {
                if (Objects.nonNull(routeDeclareConfig.getFinalImpl())) {
                    return routeDeclareConfig.getDxpId();
                }
            }
        }
        return null;
    }


    public static RouteDeclareConfig updateDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO, String agentCode, DeclareEnum declareEnum) {
        List<CompanyRouteDeclareConfig> companyRouteDeclareConfigs = getDeclareOrderConfigs(orderDTO, agentCode, declareEnum);
        RouteDeclareConfig newRouteDeclareConfig = getDeclareRecord(info, declareEnum);
        if (Objects.isNull(newRouteDeclareConfig)) {
            return null;
        }
        CompanyRouteDeclareConfig targetConfig = findOrCreateTargetConfig(agentCode, companyRouteDeclareConfigs, newRouteDeclareConfig, declareEnum);
        // 5. 更新或添加RouteDeclareConfig
        buildNewConfig(declareEnum, targetConfig, newRouteDeclareConfig);

        // 6. 序列化并更新OrderDTO
        String result = JSON.toJSONString(companyRouteDeclareConfigs);
        orderDTO.setDeclareWayRecord(result);
        return newRouteDeclareConfig;
    }

    public static RouteDeclareConfig updateInventoryDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO, String agentCode) {
        // 1. 解析declareWayRecord
        List<CompanyRouteDeclareConfig> companyRouteDeclareConfigs = getDeclareOrderConfigs(orderDTO, agentCode, DeclareEnum.INVENTORY);
        RouteDeclareConfig declareConfig = getDeclareRecord(info, DeclareEnum.INVENTORY);
        CompanyRouteDeclareConfig targetConfig = findOrCreateTargetConfig(agentCode, companyRouteDeclareConfigs, declareConfig, DeclareEnum.INVENTORY);
        List<DeclareEnum> declareEnums = Arrays.asList(DeclareEnum.INVENTORY_CANCEL, DeclareEnum.INVENTORY_REFUND);
        // 3. 处理每个DeclareEnum
        for (DeclareEnum declareEnum : declareEnums) {
            // 创建新的RouteDeclareConfig
            RouteDeclareConfig newConfig = getDeclareRecord(info, declareEnum);
            buildNewConfig(declareEnum, targetConfig, newConfig);
        }
        // 6. 序列化并更新OrderDTO
        String result = JSON.toJSONString(companyRouteDeclareConfigs);
        orderDTO.setDeclareWayRecord(result);
        return declareConfig;
    }

    private static void buildNewConfig(DeclareEnum declareEnum, CompanyRouteDeclareConfig targetConfig, RouteDeclareConfig newConfig) {
        // 5. 更新或添加RouteDeclareConfig
        boolean found = false;
        for (RouteDeclareConfig existingConfig : targetConfig.getConfigList()) {
            if (Objects.equals(existingConfig.getType(), declareEnum.getType())) {
                BeanUtils.copyProperties(newConfig, existingConfig);
                found = true;
                break;
            }
        }
        if (!found) {
            targetConfig.getConfigList().add(newConfig);
        }
    }

    private static CompanyRouteDeclareConfig findOrCreateTargetConfig(String companyCode, List<CompanyRouteDeclareConfig> companyRouteDeclareConfigs, RouteDeclareConfig newRouteDeclareConfig, DeclareEnum declareEnum) {
        // 2. 找到或创建对应的CompanyRouteDeclareConfig
        CompanyRouteDeclareConfig targetConfig = null;
        List<CompanyRouteDeclareConfig> sameCompanyConfig = companyRouteDeclareConfigs.stream()
                .filter(c -> Objects.equals(c.getCompanyCode(), companyCode) && Objects.equals(c.getDeclareEnum(), declareEnum))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameCompanyConfig)) {
            log.info("未找到companyCode={}的CompanyRouteDeclareConfig，使用默认配置", companyCode);
        } else if (sameCompanyConfig.size() == 1) {
            targetConfig = sameCompanyConfig.get(0);
            List<RouteDeclareConfig> configList = targetConfig.getConfigList();
            RouteDeclareConfig routeDeclareConfig = configList.stream().filter(c -> Objects.equals(c.getType(), declareEnum.getType())).findFirst().orElse(null);
            if (Objects.isNull(routeDeclareConfig)) {
                return targetConfig;
            }
            if (!Objects.equals(routeDeclareConfig.getFinalImpl(), newRouteDeclareConfig.getFinalImpl())) {
                targetConfig = null;
            }
        } else {
            for (CompanyRouteDeclareConfig companyRouteDeclareConfig : sameCompanyConfig) {
                List<RouteDeclareConfig> configList = companyRouteDeclareConfig.getConfigList();
                for (RouteDeclareConfig routeDeclareConfig : configList) {
                    if (Objects.equals(routeDeclareConfig, newRouteDeclareConfig)) {
                        targetConfig = companyRouteDeclareConfig;
                        break;
                    }
                }
            }
        }
        if (Objects.isNull(targetConfig)) {
            targetConfig = new CompanyRouteDeclareConfig();
            targetConfig.setCompanyCode(companyCode);
            List<RouteDeclareConfig> configList = new ArrayList<>();
            configList.add(newRouteDeclareConfig);
            targetConfig.setConfigList(configList);
            targetConfig.setDeclareEnum(declareEnum);
            companyRouteDeclareConfigs.add(targetConfig);
        }
        return targetConfig;
    }

    private static List<CompanyRouteDeclareConfig> getDeclareOrderConfigs(OrderDTO orderDTO, String agentCode, DeclareEnum declareEnum) {
        String declareWayRecord = orderDTO.getDeclareWayRecord();
        List<CompanyRouteDeclareConfig> companyRouteDeclareConfigs = new ArrayList<>();
        if (StringUtils.isBlank(declareWayRecord)) {
            companyRouteDeclareConfigs = new ArrayList<>();
        } else {
            try {
                // 尝试按新结构解析
                companyRouteDeclareConfigs = JSON.parseArray(declareWayRecord, CompanyRouteDeclareConfig.class);
            } catch (Exception e) {
                log.info("按新结构解析失败，尝试按老结构解析");
            }
            if (Objects.isNull(companyRouteDeclareConfigs) || companyRouteDeclareConfigs.stream().allMatch(c -> Objects.isNull(c.getCompanyCode()))) {
                // 兼容老数据：如果是List<RouteDeclareConfig>格式
                List<RouteDeclareConfig> oldConfigs = JSON.parseArray(declareWayRecord, RouteDeclareConfig.class);
                if (!Objects.equals(declareEnum, DeclareEnum.INVENTORY)) {
                    oldConfigs = oldConfigs.stream().filter(c -> Objects.equals(c.getType(), declareEnum.getType())).collect(Collectors.toList());
                }
                CompanyRouteDeclareConfig defaultConfig = new CompanyRouteDeclareConfig();
                defaultConfig.setCompanyCode(agentCode); // 可根据业务逻辑指定默认companyCode
                defaultConfig.setConfigList(oldConfigs);
                defaultConfig.setDeclareEnum(declareEnum);
                companyRouteDeclareConfigs = new ArrayList<>();
                companyRouteDeclareConfigs.add(defaultConfig);
            }
        }
        return companyRouteDeclareConfigs;
    }


    /**
     * 通过新版的数据结构去获取对应的申报方式
     * 如果不存在则返回null
     * 如果有多条默认取同企业的第一条，这种属于特殊情况，一般情况下会移除无效数据
     *
     * @param orderDTO
     * @param agentCode
     * @param declareEnum
     * @return
     */
    public static String getFinalDeclareImplNew(OrderDTO orderDTO, String agentCode, DeclareEnum declareEnum) {
        List<CompanyRouteDeclareConfig> companyRouteDeclareConfigs = getDeclareOrderConfigs(orderDTO, agentCode, declareEnum);
        List<CompanyRouteDeclareConfig> sameCompanyConfig = companyRouteDeclareConfigs.stream().filter(c -> Objects.equals(c.getCompanyCode(), agentCode)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(sameCompanyConfig)) {
            log.info("未找到companyCode={}的CompanyRouteDeclareConfig，使用默认配置", agentCode);
            return null;
        }
        CompanyRouteDeclareConfig targetConfig = sameCompanyConfig.get(0);
        List<RouteDeclareConfig> configList = targetConfig.getConfigList();
        for (RouteDeclareConfig routeDeclareConfig : configList) {
            if (Objects.equals(routeDeclareConfig.getType(), declareEnum.getType())) {
                return routeDeclareConfig.getFinalImpl();
            }
        }
        return null;
    }

    private static final Map<String, List<String>> SENDER_NODE_MAPPING = new HashMap<>();

    static {
        List<String> zjPortNodes = new ArrayList<>();
        zjPortNodes.add("ZJ_PORT");
        zjPortNodes.add("ZJPORT");
        SENDER_NODE_MAPPING.put(TrackLogConstantMixAll.ZJ_PORT, zjPortNodes);

        List<String> hzDataCenterNodes = new ArrayList<>();
        hzDataCenterNodes.add("HZ_CENTER");
        hzDataCenterNodes.add("HZDC");
        SENDER_NODE_MAPPING.put(TrackLogConstantMixAll.HZ_DATA_CENTER, hzDataCenterNodes);

        List<String> jiezTechNodes = new ArrayList<>();
        jiezTechNodes.add("JIEZ_TECH");
        jiezTechNodes.add("JIEZ");
        SENDER_NODE_MAPPING.put(TrackLogConstantMixAll.JIEZ_TECH, jiezTechNodes);
    }

    /**
     * 根据回执的接受方与agentCode确定实际申报的方式
     *
     * @param orderDTO
     * @param agentCode
     * @param sender
     * @param declareEnum
     */
    public static boolean determineFinalDeclareRecord(OrderDTO orderDTO, String agentCode, String sender, DeclareEnum declareEnum) throws Exception {
        List<CompanyRouteDeclareConfig> originOrderConfigs = getDeclareOrderConfigs(orderDTO, agentCode, declareEnum);
        List<CompanyRouteDeclareConfig> declareOrderConfigs = ConvertUtil.listConvert(originOrderConfigs, CompanyRouteDeclareConfig.class);
        if (CollectionUtils.isEmpty(declareOrderConfigs)) {
            log.info("determineFinalDeclareRecord 未找到companyCode={}的CompanyRouteDeclareConfig，使用默认配置", agentCode);
            return false;
        }

        // 过滤同申报类型的配置
        List<CompanyRouteDeclareConfig> filteredByDeclareEnum = filterByDeclareEnum(declareOrderConfigs, declareEnum);
        if (CollectionUtils.isEmpty(filteredByDeclareEnum)) {
            log.info("determineFinalDeclareRecord 未找到申报类型为 {} 的 CompanyRouteDeclareConfig，使用默认配置", declareEnum);
            return false;
        }

        CompanyRouteDeclareConfig target;
        if (filteredByDeclareEnum.size() == 1) {
            log.info("determineFinalDeclareRecord 找到唯一一条申报类型为 {} 的 CompanyRouteDeclareConfig 使用该条配置", declareEnum);
            target = filteredByDeclareEnum.get(0);
            String companyCode = target.getCompanyCode();
            if (!Objects.equals(companyCode, agentCode)) {
                log.error("记录的企业:{} 回执的企业:{} 不一致，不处理回执", companyCode, agentCode);
                throw new Exception("记录的企业:{} 回执的企业:{} 不一致，不处理回执");
            }
            return false;
        } else {
            log.info("determineFinalDeclareRecord 找到多条申报类型为 {} 的 CompanyRouteDeclareConfig 继续过滤", declareEnum);
            // 过滤同担保企业的配置
            List<CompanyRouteDeclareConfig> filteredByCompanyCode = filterByCompanyCode(filteredByDeclareEnum, agentCode);
            if (CollectionUtils.isEmpty(filteredByCompanyCode)) {
                log.error("determineFinalDeclareRecord 未找到担保企业为 {} 的 CompanyRouteDeclareConfig", agentCode);
                throw new Exception("未找到担保企业的申报记录");
            }
            target = selectTargetConfig(filteredByCompanyCode, sender, declareEnum);
            if (target == null) {
                log.info("determineFinalDeclareRecord 未找到符合条件的 CompanyRouteDeclareConfig，使用默认配置");
                return false;
            }
        }

        List<CompanyRouteDeclareConfig> finalResult = buildFinalResult(originOrderConfigs, target, declareEnum);
        orderDTO.setDeclareWayRecord(JSON.toJSONString(finalResult));
        log.info("确定最终的申报方式：{}", JSON.toJSONString(finalResult));
        return true;
    }

    private static List<CompanyRouteDeclareConfig> filterByDeclareEnum(List<CompanyRouteDeclareConfig> configs, DeclareEnum declareEnum) {
        return configs.stream()
                .filter(d -> Objects.equals(d.getDeclareEnum(), declareEnum))
                .collect(Collectors.toList());
    }

    private static List<CompanyRouteDeclareConfig> filterByCompanyCode(List<CompanyRouteDeclareConfig> configs, String agentCode) {
        return configs.stream()
                .filter(d -> Objects.equals(d.getCompanyCode(), agentCode))
                .collect(Collectors.toList());
    }

    /**
     * 确定实际申报成功的配置方式
     *
     * @param configs
     * @param sender
     * @param declareEnum
     * @return
     */
    private static CompanyRouteDeclareConfig selectTargetConfig(List<CompanyRouteDeclareConfig> configs, String sender, DeclareEnum declareEnum) {
        if (configs.size() == 1) {
            return configs.get(0);
        } else if (configs.size() > 1) {
            List<String> nodeList = SENDER_NODE_MAPPING.get(sender);
            if (CollectionUtils.isEmpty(nodeList)) {
                log.info("determineFinalDeclareRecord 未找到sender={}的节点 使用默认配置", sender);
                return null;
            }
            for (CompanyRouteDeclareConfig config : configs) {
                RouteDeclareConfig routeConfig = findRouteConfig(config, declareEnum);
                if (routeConfig != null && containsNode(routeConfig.getDeclareImpl(), nodeList)) {
                    return config;
                }
            }
        }
        return null;
    }

    private static RouteDeclareConfig findRouteConfig(CompanyRouteDeclareConfig config, DeclareEnum declareEnum) {
        List<RouteDeclareConfig> configList = config.getConfigList();
        return configList.stream()
                .filter(c -> Objects.equals(c.getType(), declareEnum.getType()))
                .findFirst()
                .orElse(null);
    }

    private static boolean containsNode(String finalImpl, List<String> nodeList) {
        for (String node : nodeList) {
            if (finalImpl.contains(node)) {
                return true;
            }
        }
        return false;
    }

    private static List<CompanyRouteDeclareConfig> buildFinalResult(List<CompanyRouteDeclareConfig> declareOrderConfigs, CompanyRouteDeclareConfig target, DeclareEnum declareEnum) {
        List<CompanyRouteDeclareConfig> finalResult = new ArrayList<>();
        for (CompanyRouteDeclareConfig config : declareOrderConfigs) {
            if (!Objects.equals(config.getDeclareEnum(), declareEnum)) {
                finalResult.add(config);
            }
        }
        finalResult.add(target);
        return finalResult;
    }

    public static void main(String[] args) {
        String modelJson = "{\"accountBookDto\":{\"areaCompanyId\":202,\"bookNo\":\"T2925W000355\",\"customsAreaCode\":\"2925\",\"customsAreaName\":\"义乌综保\",\"customsDistrictCode\":\"YIWU\",\"id\":74,\"remark\":\"字节跨境保税\"},\"assureCompanyDTO\":{\"cebCode\":\"331868001G\",\"cebName\":\"义乌溯塔供应链管理有限公司\",\"code\":\"331868001G\",\"name\":\"义乌溯塔供应链管理有限公司\"},\"customsAreaCode\":\"2925\",\"customsBookId\":74,\"customsInventoryDto\":{\"bookId\":74,\"bookNo\":\"T2925W000355\",\"buyerIdNumber\":\"~7/EaR6LxvS5nUMAIy4o/erfVI9J2+CYC1EtVZp3gPao=~+X4jx0RHQlKH5kFANuts60Kyuw3slG0CiB7c5D7OreX2mHC4/2WhTjaeZQG0Joe5XNbldhgPn/MJcDHxkNyJw/ijOy2aIKdzQosgCsDaVogr*CgYIASAHKAESPgo85YYGIO9EwY0dVsENkvaCe8XVJKS3Sga4TnuAK0b71NP7WpC6WomOaen8jzamVWMA4QSx5+6IzwbRjkQCGgA=~1~~\",\"buyerIdType\":\"1\",\"buyerName\":\"#YXMuSVsW#4qOLuvsbwcRi/IbnbAijK7eSk009VeNtBquMEInM9+6iVLMjqlXDfKX6oU+d3CKo2euSeM/XVs5DD3aotVtDAqkHuH1lAjy2*CgYIASAHKAESPgo88XuepmvzcH2aVkXcMYkt91V1ln2ICyOKhg4Z1vhzeKDJt50uGYvE1iuYEt41ztmj6ZI8iUm73B23tcM5GgA=#1##\",\"buyerTelNumber\":\"$+IxqtGZE+Zi73JX/6RhrHtaZNHvt760REfckxi19CB0=$wtCpxDYp/WmCr4L5qSQ0T+VwVCERd/KvW0dFs9nllAqdCVXeFUkMTKBNSCYKCGJImUSMtyCvqMnw1LoDbfMJ4G0XpYqqaw+mBro=*CgYIASAHKAESPgo8KqYDAYuAER15svLIYACyQhmDX7xY82ntUQUiPYPxWgxAIs9ewjFnouMhUW6en/gJeI8tz5iCugZozDR2GgA=$1$$\",\"clientCustoms\":\"zhejiang\",\"consigneeAddress\":\"*_*_*_*_#81eX4uaecD5fmwIpXRtesb+HDvXNMz4S7xq9umH6KgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534ixAM5nIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9EEfof/5wTLCAEcub+dCQ++6YeDeZBDJ5v/0P/AsvbafvLsOjKgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqCcU4+cU4+cU4+w3iknIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9EEfof/5wTLCAEcub+dCQ++6YeDeZphcLR7HB0b6St6uGx4gFKgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqC64XR91wUdOqyDkHgnIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9mBvXJ6fd3qzkOdF7+dCQ++6YeDeZ8ClqqBmtGObK1JrjIXsYOF86QTa9KgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqC64XR91wUdOqyVYmAumqCwgp89e1cnIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9XQisxH0I/OL21Qnz+dCQ++6YeDeZTdHKv2PHOF86+6FrQPlqOrTKCbYGKgYHKGn9QR7xwtFPAH0NywLavE83hWnBJXMoIxQzxH0IYpr1InhANalkg3yeFKJHoUEgiSXUJM4iUcLqS/Ku91wUumqC64XRxAM5+dCQ#qwZQJwFSs6T++lLHngqJk8CwFD8u6tLc2rW73cIQPOweS+pEUIr02yKKkbClAHV3U+/f1izwJvSHEb04hV5L8VnGFDk6Tkdf2Fj/9glczUYWhyyUqsiLz2o8cUTChHUgSKij9SHYbuw+Ka2Cc86DHhyL7UtYOqsJT2HlIQT6c/NHxEK4waT2JZ5n9i/Fzwnxh/YJXCfOANIKQZ1btibEG3EgxgWzbM8M7PSBQM9+ZhT0pCYXbuYCme4DZ5YW4/Hdi2jSLCNCjopv5lJ13k37jEK3RLZhJd9LJA+1pCY7aCSYRlmIwzWS0vkrZMWAjGnG8SmyNi/J/KpRoQ0XUeff4jRuCKAronCPE+Gfy043179CxBhurjfmOtIQHpUaO4+wc2z8x3SFWw==*CgYIASAHKAESPgo8PvS2zO+67/pkxy8qoS04N/yzFah0DsZ8Eh1IKWHA/MecaPOXvBmLBM/DDTdJtyz59s5o7sd7zcD9UBcoGgA=#1##\",\"createTime\":1743557421000,\"customs\":\"YIWU\",\"customsField\":\"2925\",\"discountFee\":15.00,\"ebpCode\":\"311096102D\",\"freight\":0.0000,\"grossWeight\":0.37800,\"id\":1012532384773574959,\"insureAmount\":0.0000,\"logisticsNo\":\"76484964214007\",\"mainGName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"netWeight\":0.01560,\"note\":\"[非保信息]:初和Ddrops保温杯 700ml-1个;\",\"orderNo\":\"6918987460206558637\",\"payTransactionId\":\"2025040211082843825195296\",\"sn\":\"CI2504020930738358\",\"status\":10,\"taxFee\":57.54},\"declareCompanyCebCode\":\"331868001G\",\"declareCompanyDTO\":{\"cebCode\":\"331868001G\",\"cebName\":\"义乌溯塔供应链管理有限公司\",\"code\":\"331868001G\",\"name\":\"义乌溯塔供应链管理有限公司\"},\"declareLogisticsInSystem\":false,\"declareNos\":\"6918987460206558637\",\"declareWay\":\"auto\",\"dxpId\":\"DXPENT2024649416\",\"ebcCompanyDTO\":{\"cebCode\":\"331866K00G\",\"cebName\":\"义乌鸿塔供应链管理有限公司\",\"code\":\"331866K00G\",\"name\":\"义乌鸿塔供应链管理有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"311096102D\",\"cebName\":\"上海格物致品网络科技有限公司\",\"code\":\"311096102D\",\"name\":\"上海格物致品网络科技有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"331866K00D\",\"cebName\":\"义乌南波万供应链管理有限公司\",\"code\":\"331866K00D\",\"name\":\"义乌南波万供应链管理有限公司\"},\"listCustomsInventoryItemInfo\":[{\"count\":3,\"country\":\"501\",\"firstCount\":\"0.00280\",\"gmodle\":\"4|3|维生素D3/10%,椰子油90%|2.8ml/瓶|Ddrops/无中文品牌|||无其他\",\"hsCode\":\"2106909090\",\"id\":1012532384807125712,\"itemName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"itemNo\":\"7254106457876644135\",\"itemRecordNo\":\"3364\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":89.4700,\"weight\":0.048},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00050\",\"gmodle\":\"4|3|维生素10%，椰子油90%|0.5ml/瓶|Ddrops/无中文名\",\"hsCode\":\"2106909090\",\"id\":1012532384832299367,\"itemName\":\"婴幼儿维生素D3滴剂600IU小样0.5ml/瓶\",\"itemNo\":\"7254128386044363066\",\"itemRecordNo\":\"3363\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":31.3200,\"weight\":0.041},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00050\",\"gmodle\":\"4|3|维生素10%，椰子油90%|0.5ml/瓶|Ddrops/无中文名\",\"hsCode\":\"2106909090\",\"id\":1012532384861663485,\"itemName\":\"婴幼儿维生素D3滴剂600IU小样0.5ml/瓶\",\"itemNo\":\"7254128386044363066\",\"itemRecordNo\":\"3363\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":30.4800,\"weight\":0.041},{\"count\":2,\"country\":\"501\",\"firstCount\":\"0.00170\",\"gmodle\":\"4|3|MCT椰子油95.8%，维生素D（胆钙化醇）0.063%，维生素A4.037%，生育酚0.1%|1.7ML/瓶|Ddrops/无中文名称\",\"hsCode\":\"2106909090\",\"id\":1012532384891015967,\"itemName\":\"儿童维生素A+D滴剂60滴1.7ml/瓶\",\"itemNo\":\"7314538304882622720\",\"itemRecordNo\":\"3372\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":107.5900,\"weight\":0.052},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00280\",\"gmodle\":\"4|3|维生素D3/10%,椰子油90%|2.8ml/瓶|Ddrops/无中文品牌|||无其他\",\"hsCode\":\"2106909090\",\"id\":1012532384920380374,\"itemName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"itemNo\":\"7254106457876644135\",\"itemRecordNo\":\"3364\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":87.0700,\"weight\":0.048}],\"logisticDeclareCompany\":{},\"logisticsCompanyDTO\":{\"cebCode\":\"3318960HYU\",\"cebName\":\"义乌市纯曦快递有限公司\",\"code\":\"3318960HYU\",\"name\":\"义乌市纯曦快递有限公司\"},\"mainChannel\":1,\"mainOrderId\":1012532384735834766,\"mainOrderSn\":\"OS2504020930707136\",\"payCompanyDTO\":{\"cebCode\":\"31222699S7\",\"cebName\":\"支付宝(中国)网络技术有限公司\",\"code\":\"31222699S7\",\"name\":\"支付宝(中国)网络技术有限公司\"},\"routeInfo\":{\"code\":\"311096102DYWNBWNNY4PL\",\"declareWay\":\"auto\",\"extraJson\":\"{\\\"assureCompanyId\\\":698,\\\"customsBookId\\\":74,\\\"ebcId\\\":203,\\\"ebpId\\\":309,\\\"listDeclareCompanyId\\\":698,\\\"listDeclareDxpId\\\":\\\"DXPENT2024649416\\\"}\",\"name\":\"义乌南波万-上海格物（清）\",\"routeDeclareConfigList\":[{\"declareCode\":\"HZ_CENTER_ORDER_DECLARE\",\"declareImpl\":\"HZ_CENTER_ORDER_DECLARE\",\"type\":\"customsOrder\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"type\":\"inventoryRefund\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"type\":\"inventory\"}],\"routeExtraDto\":{\"assureCompanyId\":698,\"customsBookId\":74,\"ebcId\":203,\"ebpId\":309,\"listDeclareCompanyId\":698}},\"step\":1,\"type\":\"ADD\"}";
        WrapInventoryOrderInfo info = JSON.parseObject(modelJson, WrapInventoryOrderInfo.class);
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeclareWayRecord("[{\"declareCode\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"type\":\"inventory\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"type\":\"inventoryRefund\"}]");
        List<CompanyRouteDeclareConfig> declareOrderConfigs = getDeclareOrderConfigs(orderDTO, "COMPANYCODE", DeclareEnum.INVENTORY);
        RouteDeclareConfig inventoryConfig = CustomsDeclareUtils.updateInventoryDeclareRecord(info, orderDTO, info.getDeclareCompanyCebCode());
        RouteDeclareConfig orderConfig = CustomsDeclareUtils.updateDeclareRecord(info, orderDTO, "COMPANYCODE-ORDER", DeclareEnum.CUSTOMS_ORDER);
//        RouteDeclareConfig shipConfig = CustomsDeclareUtils.updateDeclareRecord(info, orderDTO, "COMPANYCODE-LOGISTIC", DeclareEnum.SHIPMENT);
//        RouteDeclareConfig payConfig = CustomsDeclareUtils.updateDeclareRecord(info, orderDTO, "COMPANYCODE-LOGISTIC", DeclareEnum.PAYMENT);
        WrapInventoryOrderInfo newInfo = ConvertUtil.beanConvert(info, WrapInventoryOrderInfo.class);
        List<RouteDeclareConfig> routeDeclareConfigList = newInfo.getRouteInfo().getRouteDeclareConfigList();
        for (RouteDeclareConfig routeDeclareConfig : routeDeclareConfigList) {
            if (Objects.equals(routeDeclareConfig.getType(), DeclareEnum.INVENTORY.getType())) {
                routeDeclareConfig.setDeclareImpl("Test-111");
                routeDeclareConfig.setDeclareCode("Test-111");
                routeDeclareConfig.setProxyCode("Test-111");
                routeDeclareConfig.setProxyImpl("Test-111");
                routeDeclareConfig.setFinalImpl("Test-111");
                routeDeclareConfig.setFinalCode("Test-111");
            }
        }
        RouteDeclareConfig inventory1Config = CustomsDeclareUtils.updateInventoryDeclareRecord(newInfo, orderDTO, "COMPANY-INVENTORY-1");
        WrapInventoryOrderInfo newInfo1 = ConvertUtil.beanConvert(info, WrapInventoryOrderInfo.class);
        List<RouteDeclareConfig> routeDeclareConfigList1 = newInfo1.getRouteInfo().getRouteDeclareConfigList();
        for (RouteDeclareConfig routeDeclareConfig : routeDeclareConfigList1) {
            if (Objects.equals(routeDeclareConfig.getType(), DeclareEnum.INVENTORY.getType())) {
                routeDeclareConfig.setDeclareImpl("Test-22");
                routeDeclareConfig.setDeclareCode("Test-22");
                routeDeclareConfig.setProxyCode("Test-22");
                routeDeclareConfig.setProxyImpl("Test-222");
                routeDeclareConfig.setFinalImpl("ZJ_PORT-2222");
                routeDeclareConfig.setFinalCode("ZJ_PORT-2222");
            }
        }
        RouteDeclareConfig inventory2Config = CustomsDeclareUtils.updateInventoryDeclareRecord(newInfo1, orderDTO, "COMPANY-INVENTORY-1");
        System.out.println("------------------");
        System.out.println(orderDTO.getDeclareWayRecord());
        try {
            determineFinalDeclareRecord(orderDTO, "COMPANY-INVENTORY-1", TrackLogConstantMixAll.ZJ_PORT, DeclareEnum.INVENTORY);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void test() {
        String modelJson = "{\"accountBookDto\":{\"areaCompanyId\":202,\"bookNo\":\"T2925W000355\",\"customsAreaCode\":\"2925\",\"customsAreaName\":\"义乌综保\",\"customsDistrictCode\":\"YIWU\",\"id\":74,\"remark\":\"字节跨境保税\"},\"assureCompanyDTO\":{\"cebCode\":\"331868001G\",\"cebName\":\"义乌溯塔供应链管理有限公司\",\"code\":\"331868001G\",\"name\":\"义乌溯塔供应链管理有限公司\"},\"customsAreaCode\":\"2925\",\"customsBookId\":74,\"customsInventoryDto\":{\"bookId\":74,\"bookNo\":\"T2925W000355\",\"buyerIdNumber\":\"~7/EaR6LxvS5nUMAIy4o/erfVI9J2+CYC1EtVZp3gPao=~+X4jx0RHQlKH5kFANuts60Kyuw3slG0CiB7c5D7OreX2mHC4/2WhTjaeZQG0Joe5XNbldhgPn/MJcDHxkNyJw/ijOy2aIKdzQosgCsDaVogr*CgYIASAHKAESPgo85YYGIO9EwY0dVsENkvaCe8XVJKS3Sga4TnuAK0b71NP7WpC6WomOaen8jzamVWMA4QSx5+6IzwbRjkQCGgA=~1~~\",\"buyerIdType\":\"1\",\"buyerName\":\"#YXMuSVsW#4qOLuvsbwcRi/IbnbAijK7eSk009VeNtBquMEInM9+6iVLMjqlXDfKX6oU+d3CKo2euSeM/XVs5DD3aotVtDAqkHuH1lAjy2*CgYIASAHKAESPgo88XuepmvzcH2aVkXcMYkt91V1ln2ICyOKhg4Z1vhzeKDJt50uGYvE1iuYEt41ztmj6ZI8iUm73B23tcM5GgA=#1##\",\"buyerTelNumber\":\"$+IxqtGZE+Zi73JX/6RhrHtaZNHvt760REfckxi19CB0=$wtCpxDYp/WmCr4L5qSQ0T+VwVCERd/KvW0dFs9nllAqdCVXeFUkMTKBNSCYKCGJImUSMtyCvqMnw1LoDbfMJ4G0XpYqqaw+mBro=*CgYIASAHKAESPgo8KqYDAYuAER15svLIYACyQhmDX7xY82ntUQUiPYPxWgxAIs9ewjFnouMhUW6en/gJeI8tz5iCugZozDR2GgA=$1$$\",\"clientCustoms\":\"zhejiang\",\"consigneeAddress\":\"*_*_*_*_#81eX4uaecD5fmwIpXRtesb+HDvXNMz4S7xq9umH6KgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534ixAM5nIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9EEfof/5wTLCAEcub+dCQ++6YeDeZBDJ5v/0P/AsvbafvLsOjKgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqCcU4+cU4+cU4+w3iknIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9EEfof/5wTLCAEcub+dCQ++6YeDeZphcLR7HB0b6St6uGx4gFKgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqC64XR91wUdOqyDkHgnIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9mBvXJ6fd3qzkOdF7+dCQ++6YeDeZ8ClqqBmtGObK1JrjIXsYOF86QTa9KgYHzo7n81eXNICm2nYM9TA2KgYHKGn9UNJ2534iumqC64XR91wUdOqyVYmAumqCwgp89e1cnIlKeDeZ4zP48kaTkFv1kO7vumH6KgYHKGn9XQisxH0I/OL21Qnz+dCQ++6YeDeZTdHKv2PHOF86+6FrQPlqOrTKCbYGKgYHKGn9QR7xwtFPAH0NywLavE83hWnBJXMoIxQzxH0IYpr1InhANalkg3yeFKJHoUEgiSXUJM4iUcLqS/Ku91wUumqC64XRxAM5+dCQ#qwZQJwFSs6T++lLHngqJk8CwFD8u6tLc2rW73cIQPOweS+pEUIr02yKKkbClAHV3U+/f1izwJvSHEb04hV5L8VnGFDk6Tkdf2Fj/9glczUYWhyyUqsiLz2o8cUTChHUgSKij9SHYbuw+Ka2Cc86DHhyL7UtYOqsJT2HlIQT6c/NHxEK4waT2JZ5n9i/Fzwnxh/YJXCfOANIKQZ1btibEG3EgxgWzbM8M7PSBQM9+ZhT0pCYXbuYCme4DZ5YW4/Hdi2jSLCNCjopv5lJ13k37jEK3RLZhJd9LJA+1pCY7aCSYRlmIwzWS0vkrZMWAjGnG8SmyNi/J/KpRoQ0XUeff4jRuCKAronCPE+Gfy043179CxBhurjfmOtIQHpUaO4+wc2z8x3SFWw==*CgYIASAHKAESPgo8PvS2zO+67/pkxy8qoS04N/yzFah0DsZ8Eh1IKWHA/MecaPOXvBmLBM/DDTdJtyz59s5o7sd7zcD9UBcoGgA=#1##\",\"createTime\":1743557421000,\"customs\":\"YIWU\",\"customsField\":\"2925\",\"discountFee\":15.00,\"ebpCode\":\"311096102D\",\"freight\":0.0000,\"grossWeight\":0.37800,\"id\":1012532384773574959,\"insureAmount\":0.0000,\"logisticsNo\":\"76484964214007\",\"mainGName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"netWeight\":0.01560,\"note\":\"[非保信息]:初和Ddrops保温杯 700ml-1个;\",\"orderNo\":\"6918987460206558637\",\"payTransactionId\":\"2025040211082843825195296\",\"sn\":\"CI2504020930738358\",\"status\":10,\"taxFee\":57.54},\"declareCompanyCebCode\":\"331868001G\",\"declareCompanyDTO\":{\"cebCode\":\"331868001G\",\"cebName\":\"义乌溯塔供应链管理有限公司\",\"code\":\"331868001G\",\"name\":\"义乌溯塔供应链管理有限公司\"},\"declareLogisticsInSystem\":false,\"declareNos\":\"6918987460206558637\",\"declareWay\":\"auto\",\"dxpId\":\"DXPENT2024649416\",\"ebcCompanyDTO\":{\"cebCode\":\"331866K00G\",\"cebName\":\"义乌鸿塔供应链管理有限公司\",\"code\":\"331866K00G\",\"name\":\"义乌鸿塔供应链管理有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"311096102D\",\"cebName\":\"上海格物致品网络科技有限公司\",\"code\":\"311096102D\",\"name\":\"上海格物致品网络科技有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"331866K00D\",\"cebName\":\"义乌南波万供应链管理有限公司\",\"code\":\"331866K00D\",\"name\":\"义乌南波万供应链管理有限公司\"},\"listCustomsInventoryItemInfo\":[{\"count\":3,\"country\":\"501\",\"firstCount\":\"0.00280\",\"gmodle\":\"4|3|维生素D3/10%,椰子油90%|2.8ml/瓶|Ddrops/无中文品牌|||无其他\",\"hsCode\":\"2106909090\",\"id\":1012532384807125712,\"itemName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"itemNo\":\"7254106457876644135\",\"itemRecordNo\":\"3364\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":89.4700,\"weight\":0.048},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00050\",\"gmodle\":\"4|3|维生素10%，椰子油90%|0.5ml/瓶|Ddrops/无中文名\",\"hsCode\":\"2106909090\",\"id\":1012532384832299367,\"itemName\":\"婴幼儿维生素D3滴剂600IU小样0.5ml/瓶\",\"itemNo\":\"7254128386044363066\",\"itemRecordNo\":\"3363\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":31.3200,\"weight\":0.041},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00050\",\"gmodle\":\"4|3|维生素10%，椰子油90%|0.5ml/瓶|Ddrops/无中文名\",\"hsCode\":\"2106909090\",\"id\":1012532384861663485,\"itemName\":\"婴幼儿维生素D3滴剂600IU小样0.5ml/瓶\",\"itemNo\":\"7254128386044363066\",\"itemRecordNo\":\"3363\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":30.4800,\"weight\":0.041},{\"count\":2,\"country\":\"501\",\"firstCount\":\"0.00170\",\"gmodle\":\"4|3|MCT椰子油95.8%，维生素D（胆钙化醇）0.063%，维生素A4.037%，生育酚0.1%|1.7ML/瓶|Ddrops/无中文名称\",\"hsCode\":\"2106909090\",\"id\":1012532384891015967,\"itemName\":\"儿童维生素A+D滴剂60滴1.7ml/瓶\",\"itemNo\":\"7314538304882622720\",\"itemRecordNo\":\"3372\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":107.5900,\"weight\":0.052},{\"count\":1,\"country\":\"501\",\"firstCount\":\"0.00280\",\"gmodle\":\"4|3|维生素D3/10%,椰子油90%|2.8ml/瓶|Ddrops/无中文品牌|||无其他\",\"hsCode\":\"2106909090\",\"id\":1012532384920380374,\"itemName\":\"婴幼儿维生素d3滴剂600IU2.8ml/瓶\",\"itemNo\":\"7254106457876644135\",\"itemRecordNo\":\"3364\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":87.0700,\"weight\":0.048}],\"logisticDeclareCompany\":{},\"logisticsCompanyDTO\":{\"cebCode\":\"3318960HYU\",\"cebName\":\"义乌市纯曦快递有限公司\",\"code\":\"3318960HYU\",\"name\":\"义乌市纯曦快递有限公司\"},\"mainChannel\":1,\"mainOrderId\":1012532384735834766,\"mainOrderSn\":\"OS2504020930707136\",\"payCompanyDTO\":{\"cebCode\":\"31222699S7\",\"cebName\":\"支付宝(中国)网络技术有限公司\",\"code\":\"31222699S7\",\"name\":\"支付宝(中国)网络技术有限公司\"},\"routeInfo\":{\"code\":\"311096102DYWNBWNNY4PL\",\"declareWay\":\"auto\",\"extraJson\":\"{\\\"assureCompanyId\\\":698,\\\"customsBookId\\\":74,\\\"ebcId\\\":203,\\\"ebpId\\\":309,\\\"listDeclareCompanyId\\\":698,\\\"listDeclareDxpId\\\":\\\"DXPENT2024649416\\\"}\",\"name\":\"义乌南波万-上海格物（清）\",\"routeDeclareConfigList\":[{\"declareCode\":\"HZ_CENTER_ORDER_DECLARE\",\"declareImpl\":\"HZ_CENTER_ORDER_DECLARE\",\"type\":\"customsOrder\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"type\":\"inventoryRefund\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"type\":\"inventory\"}],\"routeExtraDto\":{\"assureCompanyId\":698,\"customsBookId\":74,\"ebcId\":203,\"ebpId\":309,\"listDeclareCompanyId\":698}},\"step\":1,\"type\":\"ADD\"}";
        WrapInventoryOrderInfo info = JSON.parseObject(modelJson, WrapInventoryOrderInfo.class);
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setDeclareWayRecord("[{\"declareCode\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC\",\"type\":\"inventory\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_CANCEL_DECLARE_HZDC\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE_BYTE_DANCE\",\"dxpId\":\"DXPENT2024649416\",\"finalImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_REFUND_DECLARE_HZDC\",\"type\":\"inventoryRefund\"}]");
        List<CompanyRouteDeclareConfig> declareOrderConfigs = getDeclareOrderConfigs(orderDTO, "COMPANYCODE", DeclareEnum.INVENTORY);
        List<CompanyRouteDeclareConfig> declareOrderConfigs1 = getDeclareOrderConfigs(orderDTO, "COMPANYCODE", DeclareEnum.CUSTOMS_ORDER);
        List<CompanyRouteDeclareConfig> declareOrderConfigs2 = getDeclareOrderConfigs(orderDTO, "COMPANYCODE", DeclareEnum.SHIPMENT);
        System.out.println(JSON.toJSONString(declareOrderConfigs));
        System.out.println("-------------------------");
        System.out.println(JSON.toJSONString(declareOrderConfigs1));
        System.out.println("-------------------------");
        System.out.println(JSON.toJSONString(declareOrderConfigs2));
    }

}
