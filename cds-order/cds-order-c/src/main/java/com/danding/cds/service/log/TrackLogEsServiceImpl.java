package com.danding.cds.service.log;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.TrackLogEsService;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.TrackLogSearch;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.bean.dao.TrackLogEsDO;
import com.danding.cds.order.base.mapper.TrackLogEsMapper;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.order.base.service.TrackLogBaseService;
import com.danding.cds.order.base.service.TrackLogEsProducer;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 轨迹日志ES版
 * fixme
 * 2022-07-04 轨迹日志放在es上 数据量太大 暂时还是存数据库吧
 * @date 2022/5/31 13:47
 */
@Slf4j
@Service
@RefreshScope
public class TrackLogEsServiceImpl implements TrackLogEsService {
    @Autowired
    private TrackLogEsProducer trackLogEsProducer;

    @Autowired
    private TrackLogBaseService trackLogBaseService;

    @Autowired
    private TrackLogEsMapper trackLogEsMapper;

    @Value("${offlineUrl:}")
    private String offlineUrl;
    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @Override
    public void sendMsg(String msg) {
        trackLogEsProducer.sendMsg(msg);
    }

    @Override
    public void sendMsg(String msg, String declareOrderNo) {
        trackLogEsProducer.sendMsg(msg, declareOrderNo);
    }

    /**
     * 保存轨迹日志
     *
     * @param trackLogEsDTO
     */
    @Override
    public void submit(TrackLogEsDTO trackLogEsDTO) {
        if (Objects.isNull(trackLogEsDTO)) {
            return;
        }
        TrackLogEsDO trackLogEsDO = ConvertUtil.beanConvert(trackLogEsDTO, TrackLogEsDO.class);
        trackLogBaseService.insertSelective(trackLogEsDO);
    }

    @Override
    public void submit(List<TrackLogEsDTO> trackLogEsDTOList) {
        if (CollectionUtils.isEmpty(trackLogEsDTOList)) {
            return;
        }
        List<TrackLogEsDO> trackLogEsDOList = ConvertUtil.listConvert(trackLogEsDTOList, TrackLogEsDO.class);
        trackLogBaseService.insertBatch(trackLogEsDOList);
    }

    @Override
    @PageSelect
    public ListVO<TrackLogEsDTO> getTrackLogs(TrackLogSearch trackLogSearch) {
        if (Objects.isNull(trackLogSearch)) {
            return null;
        }
        if (Objects.isNull(trackLogSearch.getOrderSn())) {
            return null;
        }
        Example example = new Example(TrackLogEsDO.class);
        example.orderBy("id").desc();
        example.createCriteria().andEqualTo("orderSn", trackLogSearch.getOrderSn()).andEqualTo("deleted", false);
        List<TrackLogEsDO> trackLogEsDOList = trackLogEsMapper.selectByExample(example);
        ListVO<TrackLogEsDTO> result = new ListVO<>();
        //************************ 取线下备份日志信息
        if (!StringUtils.isEmpty(offlineUrl)) {
            Map<String, String> paramMap = new HashMap<>();
            String strDate = "20" + trackLogSearch.getOrderSn().substring(2, 6);
            paramMap.put("createTime", strDate);
            paramMap.put("orderSn", trackLogSearch.getOrderSn());
            String paramInput = JSON.toJSONString(paramMap);
            List<TrackLogEsDO> offlineTrackLogEsDOList = GetOfflineData(offlineUrl + "/getOrderTraceLogList", paramInput);
            if (Objects.nonNull(offlineTrackLogEsDOList) && offlineTrackLogEsDOList.size() > 0) {
                trackLogEsDOList.addAll(offlineTrackLogEsDOList);
                trackLogEsDOList = trackLogEsDOList.stream().sorted(Comparator.comparing(TrackLogEsDO::getEventTime).reversed()).collect(Collectors.toList());
            }
        }
        //************************
        result.setDataList(ConvertUtil.listConvert(trackLogEsDOList, TrackLogEsDTO.class));
        PageInfo<TrackLogEsDTO> pageInfo = new PageInfo(trackLogEsDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(trackLogSearch.getCurrentPage());
        pageResult.setPageSize(trackLogSearch.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @PageSelect
    public ListVO<TrackLogEsDTO> getTrackLogs(TrackLogSearch trackLogSearch, OrderDTO orderDTO) {
        if (Objects.isNull(trackLogSearch)) {
            return null;
        }
        if (Objects.isNull(trackLogSearch.getOrderSn())) {
            return null;
        }
        Integer orderTags = orderDTO.getOrderTags();
        List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
        List<String> orderSnList = new ArrayList<>();
        orderSnList.add(orderDTO.getSn());
        if (orderTag.contains(DeclareOrderTagEnums.JIEZHOU_ENCRYPT.getCode())) {
            ExternalDeclareOrderDo declareOrderDo = externalDeclareOrderBaseService.getByOrderNo(orderDTO.getDeclareOrderNo());
            if (Objects.nonNull(declareOrderDo)) {
                orderSnList.add(declareOrderDo.getSn());
            }
        }
        Example example = new Example(TrackLogEsDO.class);
        example.orderBy("id").desc();
        example.createCriteria().andIn("orderSn", orderSnList).andEqualTo("deleted", false);
        List<TrackLogEsDO> trackLogEsDOList = trackLogEsMapper.selectByExample(example);
        ListVO<TrackLogEsDTO> result = new ListVO<>();
        //************************ 取线下备份日志信息
        if (!StringUtils.isEmpty(offlineUrl)) {
            Map<String, String> paramMap = new HashMap<>();
            String strDate = "20" + trackLogSearch.getOrderSn().substring(2, 6);
            paramMap.put("createTime", strDate);
            paramMap.put("orderSn", trackLogSearch.getOrderSn());
            String paramInput = JSON.toJSONString(paramMap);
            List<TrackLogEsDO> offlineTrackLogEsDOList = GetOfflineData(offlineUrl + "/getOrderTraceLogList", paramInput);
            if (Objects.nonNull(offlineTrackLogEsDOList) && offlineTrackLogEsDOList.size() > 0) {
                trackLogEsDOList.addAll(offlineTrackLogEsDOList);
                trackLogEsDOList = trackLogEsDOList.stream().sorted(Comparator.comparing(TrackLogEsDO::getEventTime).reversed()).collect(Collectors.toList());
            }
        }
        //************************
        result.setDataList(ConvertUtil.listConvert(trackLogEsDOList, TrackLogEsDTO.class));
        PageInfo<TrackLogEsDTO> pageInfo = new PageInfo(trackLogEsDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(trackLogSearch.getCurrentPage());
        pageResult.setPageSize(trackLogSearch.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    @Override
    public ListVO<TrackLogEsDTO> selectTrackLogList(TrackLogSearch search) {
        return null;
    }

    private List<TrackLogEsDO> GetOfflineData(String postUrl, String paramInput) {
        try {
            log.info("postDandingOffline : {}", paramInput);
            HttpRequest httpRequest = HttpRequestUtil.post(postUrl, paramInput);
            if (httpRequest.ok()) {
                String body = httpRequest.body();
                JSONObject jsonObject = JSON.parseObject(body);
                if (Objects.equals(jsonObject.get("code").toString(), "200")) {
                    return JSON.parseArray(jsonObject.get("data").toString(), TrackLogEsDO.class);
                } else {
                    log.info("调用offline接口失败 body={}", body);
                    return null;
                }

            } else {
                String body = httpRequest.body();
                log.info("调用offline接口失败 body={}", body);
                return null;
            }
        } catch (Exception e) {
            log.error("GetOfflineData exception:{}", e.getMessage(), e);
            return null;
        }
    }

}
