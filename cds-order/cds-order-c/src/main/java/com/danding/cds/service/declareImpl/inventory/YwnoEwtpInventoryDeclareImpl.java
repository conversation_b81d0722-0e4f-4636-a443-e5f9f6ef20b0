package com.danding.cds.service.declareImpl.inventory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.common.constants.DxpCons;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.service.customs.declare.EwtpDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@Service("YWNO_EWTP_INVENTORY_DECLARE")
public class YwnoEwtpInventoryDeclareImpl extends InventoryDeclareAbstract {

    @Autowired
    private EwtpDeclareService ewtpDeclareService;

    @Override
    protected InventoryDeclareResult mockDeclareTest(WrapInventoryOrderInfo info) {
        log.info("【测试】EWTP申报单: {} ，发往麦德龙DXP报文-MOCK", info.getDeclareNos());
        return ewtpDeclareService.ewtpInventoryDeclareTest(info, DxpCons.YWNO_EWTP_DXP);
    }

    @Override
    protected InventoryDeclareResult declare(WrapInventoryOrderInfo info) {

        return ewtpDeclareService.ewtpInventoryDeclare(info, DxpCons.YWNO_EWTP_DXP);
    }

}
