package com.danding.cds.service.customs;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsSubmit;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.dto.LogisticsSearch;
import com.danding.cds.customs.logistics.api.enums.LogisticsStatus;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.monitor.MappingEmptyEvent;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsLogisticsDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsStatusMappingMapper;
import com.danding.cds.order.base.service.CustomsLogisticsBaseService;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.DepotOrderManage;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.flow.OrderAction;
import com.danding.cds.utils.flow.OrderFlowBook;
import com.danding.common.utils.CopyUtil;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class CustomsLogisticsServiceImpl implements CustomsLogisticsService {

    @Resource
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Resource
    private CustomsLogisticsBaseService customsLogisticsBaseService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CompanyService companyService;

    @Resource
    private DepotOrderManage depotOrderManage;

    @Resource
    private OrderService orderService;

    @Autowired
    private CustomsSupport support;

    @Resource
    private CustomsStatusMappingMapper customsStatusMappingMapper;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @Autowired
    private EventBus eventBus;

    @Override
    public void submit(Long userId, OrderDTO orderDTO, CustomsLogisticsSubmit submit) {
        if (LongUtil.isNone(userId)) {
            throw new ArgsErrorException("用户ID不能为空");
        }
        if (orderDTO == null) {
            throw new ArgsErrorException("申报单信息不能为空");
        }
        if (StringUtils.isEmpty(submit.getOutOrderNo())) {
            throw new ArgsErrorException("上游单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getExpressCode())) {
            throw new ArgsErrorException("快递编码不能为空");
        }
        ExpressDTO expressDTO = expressService.findByCode(submit.getExpressCode());
        if (expressDTO == null || expressDTO.getEnable() != 1) {
            throw new ArgsErrorException("对应快递方式不存在或未启用");
        }
        CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(expressDTO.getExpressCompanyId());
        if (companyDTO.getEnable() != 1) {
            throw new ArgsErrorException("对应物流企业未启用");
        }
        if (CustomsDistrictEnum.NULL.equals(CustomsDistrictEnum.getEnum(submit.getCustoms()))) {
            throw new ArgsErrorException("口岸代码不合法，请参数文档传输");
        }
        if (StringUtils.isEmpty(submit.getConsigneeName()) || StringUtils.isEmpty(submit.getConsigneeTel())) {
            throw new ArgsErrorException("收件人姓名或电话不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeProvince()) || StringUtils.isEmpty(submit.getConsigneeCity())
                || StringUtils.isEmpty(submit.getConsigneeDistrict()) || StringUtils.isEmpty(submit.getConsigneeAddress())
        ) {
            throw new ArgsErrorException("收件人省、市、区、地址不能为空");
        }
        if (submit.getGrossWeight() == null || BigDecimal.ZERO.equals(submit.getGrossWeight())) {
            throw new ArgsErrorException("毛重不能为0");
        }
        if (CollectionUtils.isEmpty(submit.getItemList())) {
            throw new ArgsErrorException("商品项不能为空");
        }

        CustomsLogisticsDO logisticsDO = new CustomsLogisticsDO();
        logisticsDO.setUserId(userId);
        logisticsDO.setOrderId(orderDTO.getId());
        logisticsDO.setOrderSn(orderDTO.getSn());
        logisticsDO.setSn(sequenceService.generateCustomsLogisticsSn());
        logisticsDO.setOutOrderNo(submit.getOutOrderNo());
        logisticsDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        logisticsDO.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        if (StringUtils.isEmpty(submit.getLogisticsNo())) {
            // 需前置获取运单号
            logisticsDO.setLogisticsStatus(LogisticsStatus.INIT.getValue());
        } else {
            logisticsDO.setLogisticsNo(submit.getLogisticsNo());
            logisticsDO.setLogisticsStatus(LogisticsStatus.READY.getValue());
        }
        logisticsDO.setExpressId(expressDTO.getId());
        logisticsDO.setExpressCode(expressDTO.getCode());
        logisticsDO.setLogisticsCompanyId(companyDTO.getId());
        logisticsDO.setCustoms(submit.getCustoms());
        logisticsDO.setCustomsStatus(CustomsStat.NULL.getValue());
        logisticsDO.setConsigneeName(submit.getConsigneeName());
        logisticsDO.setConsigneeProvince(submit.getConsigneeProvince());
        logisticsDO.setConsigneeCity(submit.getConsigneeCity());
        logisticsDO.setConsigneeDistrict(submit.getConsigneeDistrict());
        logisticsDO.setConsigneeStreet(submit.getConsigneeStreet());
        logisticsDO.setConsigneeAddress(submit.getConsigneeAddress());
        logisticsDO.setConsigneeTel(submit.getConsigneeTel());
        logisticsDO.setFeeAmount(submit.getFeeAmount());
        logisticsDO.setGrossWeight(submit.getGrossWeight());
        logisticsDO.setItemJson(JSON.toJSONString(submit.getItemList()));
        if (!StringUtils.isEmpty(submit.getTenantId())) {
            logisticsDO.setTenantId(submit.getTenantId());
        }
        customsLogisticsBaseService.insertSelective(logisticsDO);
    }

    @Override
    public void obtainLogisticsNo(CustomsLogisticsDTO logisticsDTO) {
        if (!LogisticsStatus.INIT.equals(LogisticsStatus.getEnum(logisticsDTO.getLogisticsStatus()))) {
            throw new ArgsErrorException("当前运单状态不可获取运单号");
        }
        OrderDTO orderDTO = orderService.findBySnSection(logisticsDTO.getOrderSn());
        if (depotOrderManage.orderPush(orderDTO)) {
            this.updateLogisticsStatus(logisticsDTO.getId(), LogisticsStatus.WAIT);
        }
    }

    @Override
    public void recordLogisticsNo(Long id, String logisticsNo) throws ArgsErrorException {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(id);
        condition.setLogisticsNo(logisticsNo);
        condition.setLogisticsStatus(LogisticsStatus.READY.getValue());
        condition.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);

        CustomsLogisticsDTO logisticsDTO = this.findById(id);
        OrderDTO orderDTO = orderService.findByIdFull(logisticsDTO.getOrderId());
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        customsInventoryService.updateLogisticsSection(customsInventoryDTO.getId(), logisticsNo, customsInventoryDTO.getCreateTime());

        orderDeclareMQProducer.send(orderDTO);
        this.customsDeclare(logisticsDTO);
    }

    @Override
    public void customsDeclare(CustomsLogisticsDTO logisticsDTO) {
        if (!LogisticsStatus.READY.equals(LogisticsStatus.getEnum(logisticsDTO.getLogisticsStatus()))) {
            throw new ArgsErrorException("运单状态未就绪不可申报");
        }
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(logisticsDTO.getId());
        condition.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        condition.setLastDeclareTime(new Date());
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);
        // TODO:实现运单申报 金义无需申报，直接成功
        //this.customsReceiver(logisticsDTO);
    }


    @Override
    public void customsReceiver(CustomsLogisticsDTO logisticsDTO) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        /*condition.setId(logisticsDTO.getId());
        condition.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
        condition.setCustomsStatus(CustomsStat.CUSTOMS_RECEIVE.getValue());
        condition.setLastCustomsTime(new Date());
        condition.setFinishTime(new Date());
        customsLogisticsMapper.updateByPrimaryKeySelective(condition);*/
        OrderDTO orderDTO = orderService.findBySnSection(logisticsDTO.getOrderSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
//        orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
        orderDeclareMQProducer.send(orderDTO);
    }

    @Override
    public void updateLogisticsStatus(Long id, LogisticsStatus logisticsStatus) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(id);
        condition.setLogisticsStatus(logisticsStatus.getValue());
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);
    }

    @Override
    public void updateStatus(Long id, CustomsActionStatus status) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(id);
        condition.setStatus(status.getValue());
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);
        log.info("[CustomsLogisticsServiceImpl-updateStatus updateCondition={}]", JSON.toJSONString(condition));
    }

    @Override
    public void updateStatusResetDeclareTime(Long id, CustomsActionStatus status) {
        customsLogisticsBaseService.updateStatusResetDeclareTime(id, status.getValue());
    }

    @Override
    public void updateByPush(Long id, CustomsActionStatus status, Integer declareFrequency) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(id);
        condition.setStatus(status.getValue());
        condition.setLastDeclareTime(new Date());
        condition.setDeclareFrequency(declareFrequency);
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);
        log.info("[CustomsLogisticsServiceImpl-updateByPush updateCondition={}]", JSON.toJSONString(condition));
    }

    @Override
    public void updateByPush(Long id, CustomsActionStatus status) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setId(id);
        condition.setStatus(status.getValue());
        condition.setLastDeclareTime(new Date());
        customsLogisticsBaseService.updateByPrimaryKeySelective(condition);
        log.info("[CustomsLogisticsServiceImpl-updateByPush updateCondition={}]", JSON.toJSONString(condition));
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime) {
        CustomsLogisticsDO logisticsDO = new CustomsLogisticsDO();
        logisticsDO.setId(id);
        logisticsDO.setCustomsStatus(customsStatus);
        logisticsDO.setCustomsDetail(customsDetail);
        logisticsDO.setLastCustomsTime(lastCustomsTime);
        customsLogisticsBaseService.updateByPrimaryKeySelective(logisticsDO);
    }

    @Override
    public CustomsLogisticsDTO findById(Long id) {
        return CopyUtil.copy(customsLogisticsBaseService.selectByPrimaryKey(id), CustomsLogisticsDTO.class);
    }

    @Override
    public CustomsLogisticsDTO findByOrder(Long id, String sn) {
        if (StringUtils.isEmpty(sn)) {
            CustomsLogisticsDO condition = new CustomsLogisticsDO();
            condition.setOrderId(id);
            return CopyUtil.copy(customsLogisticsBaseService.selectOne(condition), CustomsLogisticsDTO.class);
        } else {
            return this.findBySn(sn);
        }
    }

    @Override
    public CustomsLogisticsDTO findBySn(String sn) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setSn(sn);
        return CopyUtil.copy(customsLogisticsBaseService.selectOne(condition), CustomsLogisticsDTO.class);
    }

    @Override
    public CustomsLogisticsDTO findByLogisticsNo(String logisticsNo) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setLogisticsNo(logisticsNo);
        return CopyUtil.copy(customsLogisticsBaseService.selectOne(condition), CustomsLogisticsDTO.class);
    }


    @Override
    public List<CustomsLogisticsDTO> listByLogisticsStatus(CustomsActionStatus status, LogisticsStatus logisticsStatus) {
        CustomsLogisticsDO condition = new CustomsLogisticsDO();
        condition.setStatus(status.getValue());
        condition.setLogisticsStatus(logisticsStatus.getValue());
        return CopyUtil.copyList(customsLogisticsBaseService.select(condition), CustomsLogisticsDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<CustomsLogisticsDTO> paging(LogisticsSearch search) {
        Example example = new Example(CustomsLogisticsDO.class);
        Example.Criteria criteria = example.createCriteria();

        if (!LongUtil.isNone(search.getExpressId())) {
            criteria.andEqualTo("expressId", search.getExpressId());
        }
        if (search.getStatus() != null && search.getStatus() != 0) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!StringUtils.isEmpty(search.getCustoms())) {
            criteria.andEqualTo("customs", search.getCustoms());
        }
        if (!StringUtils.isEmpty(search.getLogisticsCompanyId())) {
            criteria.andEqualTo("logisticsCompanyId", search.getLogisticsCompanyId());
        }
        if (!StringUtils.isEmpty(search.getQueryType())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if (!CollectionUtils.isEmpty(noList)) {
                switch (search.getQueryType()) {
                    case "declareOrderNo":
                        criteria.andIn("declareOrderNo", noList);
                        break;
                    case "outOrderNo":
                        criteria.andIn("outOrderNo", noList);
                        break;
                    case "logisticsNo":
                        criteria.andIn("logisticsNo", noList);
                        break;
                }
            }
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getBeginCreateTime())) {
            criteria.andGreaterThanOrEqualTo("createTime", new Date(search.getBeginCreateTime()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getEndCreateTime())) {
            criteria.andLessThanOrEqualTo("createTime", new Date(search.getEndCreateTime()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getBeginLastDeclareTime())) {
            criteria.andGreaterThanOrEqualTo("lastDeclareTime", new Date(search.getBeginLastDeclareTime()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getEndFinishDeclareTime())) {
            criteria.andLessThanOrEqualTo("lastDeclareTime", new Date(search.getEndFinishDeclareTime()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getBeginFinishDeclareTime())) {
            criteria.andGreaterThanOrEqualTo("finishTime", new Date(search.getBeginFinishDeclareTime()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(search.getEndFinishDeclareTime())) {
            criteria.andLessThanOrEqualTo("finishTime", new Date(search.getEndFinishDeclareTime()));
        }
        example.setOrderByClause("create_time DESC");
        List<CustomsLogisticsDO> list = customsLogisticsBaseService.selectByExample(example);
        ListVO<CustomsLogisticsDTO> result = new ListVO<>();
        result.setDataList(CopyUtil.copyList(list, CustomsLogisticsDTO.class));
        // 分页
        PageInfo<CustomsLogisticsDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void rePush(String sn, Boolean sendNow) {
        CustomsLogisticsDTO logisticsDTO = this.findBySn(sn);
        if (logisticsDTO != null) {
            if (!LogisticsStatus.READY.getValue().equals(logisticsDTO.getLogisticsStatus())) {
                throw new ArgsErrorException("对应运单号未就绪，请等待运单号生成");
            }
            OrderDTO orderDTO = orderService.findBySnSection(logisticsDTO.getOrderSn());
            if (!OrderStatus.DEC_ING.getValue().equals(orderDTO.getStatus())) {
                throw new ArgsErrorException("申报单状态未在申报中");
            }
            if (logisticsDTO.getLastDeclareTime() != null && new DateTime(logisticsDTO.getLastDeclareTime()).isAfter(DateTime.now().minusMinutes(10))) {
                log.info("[op:CustomsLogisticsServiceImpl-rePush] not reset. last declare time={}", logisticsDTO.getLastDeclareTime());
                // 申报中，且更新时间小于十分钟，则不允许重推
                throw new ArgsErrorException("重推间隔需大于十分钟");
            }
            if (!logisticsDTO.getStatus().equals(CustomsActionStatus.DEC_WAIT.getValue())) {
                this.updateStatus(logisticsDTO.getId(), CustomsActionStatus.DEC_WAIT);
            }
            if (sendNow) {
                orderService.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
                orderDeclareMQProducer.send(orderDTO);
            }
        }
    }

    @Override
    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.CUSTOMS_LOGISTIC_RECEIPT
    )
    public String receiveLogistics(LogisticsReceive receive) {
        log.info("[op:CustomsLogisticsServiceImpl-receiveLogistics] receiveCustoms,info={}", JSON.toJSONString(receive));
        // Step::基础参数解析
        String customsStatus = receive.getCustomsStatus();
        Long customsTime = receive.getCustomsTime();
        String customsDetail = receive.getCustomsDetail();
        CustomsLogisticsDTO customsLogisticsDTO = this.findByLogisticsNo(receive.getLogisticsNo());
        if (customsLogisticsDTO == null) {
            return "运单不存在";
        }
        OrderDTO orderDTO = orderService.findBySnSection(customsLogisticsDTO.getOrderSn());
        if (Objects.isNull(orderDTO)) {
            log.info("receiveLogistics 运单:{}根据创建时间查不到单号 查全部申报单", receive.getLogisticsNo());
            orderDTO = orderService.findByIdFull(customsLogisticsDTO.getOrderId());
            if (Objects.isNull(orderDTO)) {
                return "订单不存在";
            }
        }
        if (customsLogisticsDTO.getLastCustomsTime() != null && new DateTime(customsTime).isBefore(new DateTime(customsLogisticsDTO.getLastCustomsTime()))) {
            log.warn("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
        } else if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            // 更新异常内容
            this.updateByCustomsActive(
                    customsLogisticsDTO.getId()
                    , null
                    , customsDetail
                    , new DateTime(customsTime).toDate()
            );

        } else {
            // 更新清关状态
            this.updateByCustomsActive(
                    customsLogisticsDTO.getId()
                    , customsStatus
                    , customsDetail
                    , new DateTime(customsTime).toDate()
            );
        }
        String mappingCode = RouteActionEnum.DECLARE_LOGISTICS.getCode() + ":" + receive.getCustomsStatus();
        boolean examineFlag = receive.getCustomsDetail().length() >= 5 && "Code".equals(receive.getCustomsDetail().substring(1, 5));
        if (examineFlag) {
            mappingCode += ":";
            if (receive.getCustomsDetail().length() >= 11 && ";".equals(receive.getCustomsDetail().substring(10, 11))) {
                mappingCode += receive.getCustomsDetail().substring(6, 10);
            } else {
                mappingCode += receive.getCustomsDetail().substring(6, 11);
            }
        } else {
            examineFlag = receive.getCustomsDetail().length() >= 6 && ":".equals(receive.getCustomsDetail().substring(5, 6));
            if (examineFlag) {
                mappingCode += ":";
                mappingCode += receive.getCustomsDetail().substring(0, 5);
            }
        }
        CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode(mappingCode);
        // Step::mapping不存在报警
        if (mappingDTO == null) {
            eventBus.post(new MappingEmptyEvent(mappingCode, JSON.toJSONString(receive)));
            mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("UN_KNOW");
        } else {
            mappingDTO = customsStatusMappingService.getFinalMappingCode(mappingDTO, customsDetail);
        }

        Integer resultStatus = mappingDTO.getStatus();
        Boolean exceptionFlag = mappingDTO.getExceptionFlag();
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
        if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(resultStatus)) {
            // 运单转完成
            if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(customsLogisticsDTO.getStatus())) {
                return "运单已申报，忽略重复回传";
            } else {
                this.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_SUCCESS);
                String logisticsCode = receive.getLogisticsCode();
                if (Objects.nonNull(logisticsCode)) {
                    try {
                        log.info("logisticsCode={} sender={} 确定最终申报方式", logisticsCode, receive.getSender());
                        boolean updated = CustomsDeclareUtils.determineFinalDeclareRecord(orderDTO, logisticsCode, receive.getSender(), DeclareEnum.SHIPMENT);
                        if (updated) {
                            log.info("logisticsCode={} sender={} 确定最终申报方式成功", logisticsCode, receive.getSender());
                            orderService.updateOrderDeclareRecord(orderDTO);
                        }
                    } catch (Exception e) {
                        log.error("logisticsCode={} sender={} 确定最终申报方式异常", logisticsCode, receive.getSender(), e);
                        return null;
                    }
                }
//                orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
                orderDeclareMQProducer.send(orderDTO);
            }
        } else if (CustomsActionStatus.DEC_FAIL.getValue().equals(resultStatus)) {
            if (!customsLogisticsDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                return "运单状态非申报中，不接收异常或失败信息";
            }
            // 订单转失败
            if (exceptionFlag) {
                if (!customsLogisticsDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    return "运单状态非申报中，不接收异常信息";
                }
                // 订单转异常处理
                if (!orderDTO.getStatus().equals(OrderStatus.DEC_ING.getValue())) {
                    return "申报单状态不允许清关异常变更";
                }
                //TODO
                this.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_FAIL);
                orderService.addExceptionSection(customsLogisticsDTO.getOrderId(), mappingDTO.getId(), customsDetail, orderDTO.getCreateTime());
            } else {
                if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.FAIL.getOperation())) {
                    //TODO
                    this.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_FAIL);
                    orderService.updateStatusSection(customsLogisticsDTO.getOrderId(), OrderFlowBook.order.target(orderDTO.getStatus(), OrderAction.FAIL.getOperation()), orderDTO.getCreateTime());
                    orderService.updateExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO, customsDetail, orderDTO.getCreateTime());
                } else {
                    // 例如已取消，已完成等状态，不处理清关不可逆失败
                    return "申报单状态不允许清关失败变更";
                }
            }
        } else if (CustomsActionStatus.DEC_ING.getValue().equals(resultStatus)) {
            return "中间状态，不做处理";
        } else {
            return "映射状态定义不合法";
        }
        return "回传成功";
    }


    @Override
    public void updateBylastDeclareTime(String orderSn) {
        Example example = new Example(CustomsLogisticsDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", orderSn);
        CustomsLogisticsDO customsLogisticsDO = customsLogisticsBaseService.selectOneByExample(example);
        if (Objects.nonNull(customsLogisticsDO.getLastDeclareTime())) {
            CustomsLogisticsDO logisticsDO = new CustomsLogisticsDO();
            logisticsDO.setId(customsLogisticsDO.getId());
            Date nowBeforeFiveMinutes = new Date(customsLogisticsDO.getLastDeclareTime().getTime() - 600000);
            logisticsDO.setLastDeclareTime(nowBeforeFiveMinutes);
            customsLogisticsBaseService.updateByPrimaryKeySelective(logisticsDO);
        }

    }

    @Override
    public void updateLastDeclareTime(String orderSn) {
        if (StringUtils.isEmpty(orderSn)) {
            throw new ArgsErrorException("updateLastDeclareTime sn 为空");
        }
        Example example = new Example(CustomsLogisticsDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderSn", orderSn);
        Date fifteenMinusAgo = new Date(System.currentTimeMillis() - 900000);
        CustomsLogisticsDO customsLogisticsDO = new CustomsLogisticsDO();
        customsLogisticsDO.setLastDeclareTime(fifteenMinusAgo);
        customsLogisticsBaseService.updateByExampleSelective(customsLogisticsDO, example);
    }

//    @Override
//    public void logicDeleteBySn(String sn) {
//        if (StringUtils.isEmpty(sn)) {
//            throw new ArgsErrorException("deleteBySn sn 为空");
//        }
//        Example example = new Example(CustomsLogisticsDO.class);
//        Example.Criteria criteria = example.createCriteria();
//        criteria.andEqualTo("sn", sn);
//        CustomsLogisticsDO customsLogisticsDO = new CustomsLogisticsDO();
//        customsLogisticsDO.setDeleted(true);
//        customsLogisticsBaseService.updateByExampleSelective(customsLogisticsDO, example);
//    }

    @Override
    public void updateLogisticsNo(Long orderId, String newLogisticsNo) {
        if (Objects.isNull(orderId) || StringUtils.isBlank(newLogisticsNo)) {
            return;
        }
        Example example = new Example(CustomsLogisticsDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("orderId", orderId);
        CustomsLogisticsDO customsLogisticsDO = new CustomsLogisticsDO();
        customsLogisticsDO.setLogisticsNo(newLogisticsNo);
        customsLogisticsBaseService.updateByExampleSelective(customsLogisticsDO, example);
    }

    @Override
    public void updateAgentCompanyId(Long id, Long logisticsCompanyId) {
        if (Objects.isNull(id) || Objects.isNull(logisticsCompanyId)) {
            return;
        }
        Example example = new Example(CustomsLogisticsDO.class);
        example.createCriteria().andEqualTo("id", id);
        CustomsLogisticsDO customsLogisticsDO = new CustomsLogisticsDO();
        customsLogisticsDO.setAgentCompanyId(logisticsCompanyId);
        customsLogisticsBaseService.updateByExampleSelective(customsLogisticsDO, example);
    }


}
