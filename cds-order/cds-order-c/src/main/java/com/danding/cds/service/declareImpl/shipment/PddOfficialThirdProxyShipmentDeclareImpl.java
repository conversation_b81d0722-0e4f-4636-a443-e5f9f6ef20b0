package com.danding.cds.service.declareImpl.shipment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.bean.vo.PddOfficialDeclareShipReqVo;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.service.TmsDeclareLogisticsService;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.cds.utils.BuildInfoUtil;
import com.dt.tms.rpc.waybill.client.ITmsCustomsClient;
import com.dt.tms.rpc.waybill.param.customs.RpcCustomsWaybillReqVO;
import com.dt.tms.rpc.waybill.result.customs.RpcCustomsRequestBodyResVO;
import com.dt.tms.tool.common.TmsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 拼多多第三方运单申报
 * @date 2023/12/1 11:19
 */
@Slf4j
@Service("PDD_OFFICIAL_THIRD_SHIPMENT_DECLARE")
public class PddOfficialThirdProxyShipmentDeclareImpl extends ShipmentDeclareAbstract {

    @Autowired
    private TmsDeclareLogisticsService tmsDeclareLogisticsService;

    @Autowired
    private PddDeclareService pddDeclareService;

    @Autowired
    private PddCebProxyShipmentDeclareImpl pddCebProxyShipmentDeclare;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @DubboReference
    private ITmsCustomsClient iTmsCustomsClient;

    @DubboReference
    private ExpressService expressService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("申报单: {} 拼多多第三方运单官方代报【运单申报】MOCK", info.getDeclareOrderNo());
        String expressCode = info.getExpressCode();
        if (tmsDeclareLogisticsService.isPddTmsDeclare(expressCode)) {
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByLogisticsNo(info.getLogisticsNo());
            info.setDeclareCompanyDTO(info.getLogisticsCompanyDTO());
            customsLogisticsService.updateAgentCompanyId(customsLogisticsDTO.getId(), customsLogisticsDTO.getLogisticsCompanyId());
            RpcCustomsWaybillReqVO rpcCustomsWaybillReqVO = tmsDeclareLogisticsService.getRpcCustomsWaybillReqVO(info, customsLogisticsDTO);
            try {
                log.info("iTmsCustomsClient requestBody req={}", JSON.toJSONString(rpcCustomsWaybillReqVO));
                TmsResult<RpcCustomsRequestBodyResVO> result = iTmsCustomsClient.requestBody(rpcCustomsWaybillReqVO);
                log.info("iTmsCustomsClient requestBody result={}", JSON.toJSONString(result));
                // 申报轨迹日志
                tmsDeclareLogisticsService.buildAndSendTrackLog(customsLogisticsDTO, JSON.toJSONString(result));
                if (result.isSuccess()) {
                    RpcCustomsRequestBodyResVO data = result.getData();
                    //获取申报报文
                    String requestInfo = data.getData();
                    PddOfficialDeclareShipReqVo declareReqVo = new PddOfficialDeclareShipReqVo();
                    declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.CEB511.getKey());
                    declareReqVo.setInfo(JSON.toJSONString(info));
                    declareReqVo.setDeclareOrderSn(info.getDeclareNos());
                    declareReqVo.setThirdMessage(requestInfo);
                    log.info("申报单:{} 拼多多第三方运单官方代报 运单申报 调用拼多多开始", info.getDeclareOrderNo());
                    pddDeclareService.cloudOfficialShipmentThirdMessageDeclare(declareReqVo, customsLogisticsDTO);
                } else {
                    String message = result.getMessage();
                    log.error("iTmsCustomsClient 获取TMS申报报文异常 req={}", message);
                }
            } catch (Exception e) {
                throw new RuntimeException("获取TMS申报报文异常");
            }
        } else {
            log.info("申报单:{} 拼多多第三方运单官方代报 走自有资质模式 MOCK", info.getDeclareOrderNo());
            pddCebProxyShipmentDeclare.declare(info);
        }
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("申报单:{} 拼多多第三方运单官方代报 运单申报 ", info.getDeclareOrderNo());
        String expressCode = info.getExpressCode();
        if (tmsDeclareLogisticsService.isPddTmsDeclare()) {
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByLogisticsNo(info.getLogisticsNo());
            info.setDeclareCompanyDTO(info.getLogisticsCompanyDTO());
            customsLogisticsService.updateAgentCompanyId(customsLogisticsDTO.getId(), customsLogisticsDTO.getLogisticsCompanyId());
            RpcCustomsWaybillReqVO rpcCustomsWaybillReqVO = tmsDeclareLogisticsService.getRpcCustomsWaybillReqVO(info, customsLogisticsDTO);
            try {
                log.info("iTmsCustomsClient requestBody req={}", JSON.toJSONString(rpcCustomsWaybillReqVO));
                TmsResult<RpcCustomsRequestBodyResVO> result = iTmsCustomsClient.requestBody(rpcCustomsWaybillReqVO);
                log.info("iTmsCustomsClient requestBody result={}", JSON.toJSONString(result));
                // 申报轨迹日志
                tmsDeclareLogisticsService.buildAndSendTrackLog(customsLogisticsDTO, JSON.toJSONString(result));
                if (result.isSuccess()) {
                    RpcCustomsRequestBodyResVO data = result.getData();
                    //获取申报报文
                    String requestInfo = data.getData();
                    PddOfficialDeclareShipReqVo declareReqVo = new PddOfficialDeclareShipReqVo();
                    declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.CEB511.getKey());
                    declareReqVo.setInfo(JSON.toJSONString(info));
                    declareReqVo.setDeclareOrderSn(info.getDeclareNos());
                    declareReqVo.setThirdMessage(requestInfo);
                    log.info("申报单:{} 拼多多第三方运单官方代报 运单申报 调用拼多多开始", info.getDeclareOrderNo());
                    pddDeclareService.cloudOfficialShipmentThirdMessageDeclare(declareReqVo, customsLogisticsDTO);
                } else {
                    String message = result.getMessage();
                    log.error("iTmsCustomsClient 获取TMS申报报文异常 req={}", message);
                }
            } catch (Exception e) {
                throw new RuntimeException("获取TMS申报报文异常");
            }
        } else {
            log.info("申报单:{} 拼多多第三方运单官方代报 走自有资质模式", info.getDeclareOrderNo());
            pddCebProxyShipmentDeclare.declare(info);
        }
        return null;
    }

    private CompanyInfo buildCompany(CompanyDTO companyDTO, String customs) {
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getUnifiedCrossBroderCode());
        ebpInfo.setCebName(companyDTO.getName());
        ebpInfo.setCode(companyDTO.getCode());
        ebpInfo.setName(companyDTO.getName());
//        CompanyDistrictDTO ebpDis = companyDTO.getDistrict(CustomsDistrictEnum.getEnum(customs));
//        if (ebpDis == null) {
//            ebpInfo.setCode(companyDTO.getCode());
//            ebpInfo.setName(companyDTO.getName());
//        } else {
//            ebpInfo.setCode(ebpDis.getCode());
//            ebpInfo.setName(ebpDis.getName());
//        }

        JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
        String dxpId = agentExtra.getString("orderDxpId");
        if (!org.apache.commons.lang3.StringUtils.isEmpty(dxpId)) {
            ebpInfo.setDxpId(dxpId);
        }

        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }
        return ebpInfo;
    }
}
