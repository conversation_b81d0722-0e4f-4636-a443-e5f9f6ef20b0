package com.danding.cds.service.mq.producer;

import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class OrderExamineMQProducer {

    @Autowired
    private MessageSender messageSender;

    public void send(String sn){
        try {
            messageSender.sendMsg(sn,"ccs-order-examine-c-topic");
        } catch (Exception e) {
            log.error("[op:OrderExamineProducer] sendMeg exception, cause={}", e.getMessage(), e);
        }
    }
}
