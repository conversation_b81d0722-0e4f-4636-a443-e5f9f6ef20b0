package com.danding.cds.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.vo.ReconciliationJDImportCsvVO;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.ReconciliationOrderItemService;
import com.danding.cds.c.api.service.ReconciliationOrderService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.order.base.bean.dao.ReconciliationOrderDO;
import com.danding.cds.order.base.bean.dao.ReconciliationTrackLogDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.ReconciliationOrderMapper;
import com.danding.cds.order.base.service.ReconciliationOrderBaseService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.dto.ReconciliationImportResult;
import com.danding.cds.v2.bean.dto.ReconciliationOrderDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.dto.ReconciliationTrackLogDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationJDImportExcelVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationOrderImportVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationSearch;
import com.danding.cds.v2.enums.ReconciliationStatusEnums;
import com.danding.cds.v2.enums.ReconciliationTagEnums;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.opencsv.CSVReader;
import com.opencsv.bean.CsvToBean;
import com.opencsv.bean.CsvToBeanBuilder;
import com.opencsv.bean.HeaderColumnNameMappingStrategy;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.io.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RefreshScope
@Slf4j
@Service
public class ReconciliationOrderServiceImpl implements ReconciliationOrderService {

    @Resource
    private ReconciliationOrderBaseService reconciliationOrderBaseService;

    @Resource
    private ReconciliationOrderItemService reconciliationOrderItemService;

    @Resource
    private ReconciliationOrderMapper reconciliationOrderMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Resource
    private SequenceService sequenceService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Autowired
    private Validator validator;

    @Autowired
    private MessageSender messageSender;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${data.center.url:}")
    private String dataCenterUrl;

    @Value("${reconciliation.order.areaCompany.warehouseCode.map:{}}")
    private String areaCompanyAndWarehouseMap;

    @Resource(name = "reconciliationThreadExecutor")
    private ThreadPoolTaskExecutor reconciliationThreadExecutor;


    @Override
    public ReconciliationOrderDTO findBySn(String sn) {
        if (StrUtil.isBlank(sn)) {
            return null;
        }
        ReconciliationOrderDO reconciliationOrderDO = reconciliationOrderBaseService.findBySn(sn);
        return ConvertUtil.beanConvert(reconciliationOrderDO, ReconciliationOrderDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<ReconciliationOrderDTO> paging(ReconciliationSearch search) {
        Example example = getPagingExample(search);
        List<ReconciliationOrderDO> list = reconciliationOrderMapper.selectByExample(example);
        ListVO<ReconciliationOrderDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, ReconciliationOrderDTO.class));
        // 分页
        PageInfo<ReconciliationOrderDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example getPagingExample(ReconciliationSearch search) {
        Example example = new Example(ReconciliationOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        if (StrUtil.isNotBlank(search.getSn())) {
            criteria.andIn("sn", Splitter.on(",").splitToList(search.getSn()));
        }
        if (CollUtil.isNotEmpty(search.getEntityWarehouseCode())) {
            criteria.andIn("entityWarehouseCode", search.getEntityWarehouseCode());
        }
        if (CollUtil.isNotEmpty(search.getAreaCompanyId())) {
            criteria.andIn("areaCompanyId", search.getAreaCompanyId());
        }
        if (Objects.nonNull(search.getCreateTimeFrom()) && Objects.nonNull(search.getCreateTimeTo())) {
            criteria.andBetween("createTime", new Date(search.getCreateTimeFrom()), new Date(search.getCreateTimeTo()));
        }
        if (StrUtil.isNotBlank(search.getOperator())) {
            criteria.andLike("operator", "%" + search.getOperator() + "%");
        }
        if (StrUtil.isNotBlank(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (Objects.nonNull(search.getReconciliationTag())) {
            criteria.andCondition("reconciliation_tag & " + search.getReconciliationTag() + " = " + search.getReconciliationTag());
        }
        example.orderBy("id").desc();
        return example;
    }

    @Override
    public Map<String, Integer> statusCount() {
        Map<String, Integer> result = new HashMap<>();
        List<String> statisticStatus = new ArrayList<>();
        statisticStatus.add("ALL");
        Stream.of(ReconciliationStatusEnums.values()).forEach(e -> statisticStatus.add(e.getCode()));
        for (String status : statisticStatus) {
            if (ReconciliationStatusEnums.DISCARD.getCode().equalsIgnoreCase(status)) {
                result.put(status, 0);
                continue;
            }
            Example example = new Example(ReconciliationOrderDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("deleted", false);
            if (!"ALL".equalsIgnoreCase(status)) {
                criteria.andEqualTo("status", status);
            }
            Integer count = reconciliationOrderBaseService.selectCountByExample(example);
            result.put(status, count);
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void discard(Long id) {
        ReconciliationOrderDO reconciliationOrderDO = reconciliationOrderBaseService.selectByPrimaryKey(id);
        if (Objects.isNull(reconciliationOrderDO)) {
            throw new ArgsInvalidException("出区对账单不存在");
        }
        List<String> allowStatusList = Arrays.asList(ReconciliationStatusEnums.ANALYSIS_ING.getCode(),
                ReconciliationStatusEnums.ANALYSIS_FAIL.getCode(),
                ReconciliationStatusEnums.EXCEPTION.getCode()
        );
        if (!allowStatusList.contains(reconciliationOrderDO.getStatus())) {
            throw new ArgsInvalidException("状态不允许作废");
        }
        if (!RedissLockUtil.tryLock("ccs:reconciliation:order:discard:" + id, TimeUnit.SECONDS, 3, 5)) {
            throw new ArgsInvalidException("操作过于频繁，请稍后重试");
        }
        ReconciliationOrderDO updateDO = new ReconciliationOrderDO();
        updateDO.setId(id);
        updateDO.setStatus(ReconciliationStatusEnums.DISCARD.getCode());
        updateDO.setReconciliationTag(ReconciliationTagEnums.remove(reconciliationOrderDO.getReconciliationTag(), ReconciliationTagEnums.PROCESSING));
        reconciliationOrderBaseService.updateByPrimaryKeySelective(updateDO);
        saveLog(id, ReconciliationStatusEnums.DISCARD.getCode(), "作废对账单");
        // 作废关联的对账订单
        reconciliationOrderItemService.discardByReconciliationSn(reconciliationOrderDO.getSn());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markDeal(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("id不能为空");
        }
        Example example = new Example(ReconciliationOrderDO.class);
        example.createCriteria().andIn("id", idList);
        List<ReconciliationOrderDO> reconciliationOrderDOList = reconciliationOrderBaseService.selectByExample(example);
        List<String> allowStatusList = Arrays.asList(ReconciliationStatusEnums.ANALYSIS_ING.getCode(),
                ReconciliationStatusEnums.EXCEPTION.getCode()
        );
        if (reconciliationOrderDOList.stream().anyMatch(r -> !allowStatusList.contains(r.getStatus()))) {
            throw new ArgsInvalidException("当前对账状态无法标记处理");
        }
        for (ReconciliationOrderDO reconciliationOrderDO : reconciliationOrderDOList) {
            ReconciliationOrderDO updateDO = new ReconciliationOrderDO();
            updateDO.setId(reconciliationOrderDO.getId());
            updateDO.setReconciliationTag(ReconciliationTagEnums
                    .add(reconciliationOrderDO.getReconciliationTag(), ReconciliationTagEnums.PROCESSING));
            reconciliationOrderBaseService.updateByPrimaryKeySelective(updateDO);
            saveLog(reconciliationOrderDO.getId(), reconciliationOrderDO.getStatus(), "对账跟进中");
        }
    }

    @Override
    public void excelImport(ReconciliationOrderImportVO importVO) {
        if (StrUtil.isBlank(importVO.getImportUrl())) {
            throw new ArgsInvalidException("导入文件url不能为空");
        }
        if (!importVO.getImportUrl().endsWith(".csv")
                && !importVO.getImportUrl().endsWith(".xlsx")
                && !importVO.getImportUrl().endsWith(".xls")) {
            throw new ArgsInvalidException("文件格式不正确，只支持csv/xlsx/xls格式");
        }
        List<ReconciliationJDImportExcelVO> excelVOList = null;
        try {
            if (importVO.getImportUrl().endsWith(".csv")) {
                List<ReconciliationJDImportCsvVO> csvVOList = readCsvFile(importVO.getImportUrl(), ReconciliationJDImportCsvVO.class);
                if (CollUtil.isNotEmpty(csvVOList)) {
                    excelVOList = csvVOList.stream().map(e -> {
                        ReconciliationJDImportExcelVO excelVO = new ReconciliationJDImportExcelVO();
                        BeanUtils.copyProperties(e, excelVO);
                        // csv导出时 纯数字的字符串会有 =" 这种前缀去除一下
                        if (StrUtil.isNotBlank(e.getOrderNumber()) && e.getOrderNumber().startsWith("=\"")) {
                            excelVO.setOrderNumber(e.getOrderNumber().replace("=\"", ""));
                        }
                        if (StrUtil.isNotBlank(e.getProductCode()) && e.getProductCode().startsWith("=\"")) {
                            excelVO.setProductCode(e.getProductCode().replace("=\"", ""));
                        }
                        if (StrUtil.isNotBlank(e.getMerchantOrderNumber()) && e.getMerchantOrderNumber().startsWith("=\"")) {
                            excelVO.setMerchantOrderNumber(e.getMerchantOrderNumber().replace("=\"", ""));
                        }
                        return excelVO;
                    }).collect(Collectors.toList());
                }
            } else if (importVO.getImportUrl().endsWith(".xlsx")
                    || importVO.getImportUrl().endsWith(".xls")) {
                excelVOList = readExcelFile(importVO.getImportUrl());
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        if (CollUtil.isEmpty(excelVOList)) {
            throw new ArgsInvalidException("文件内容为空");
        }
        // 过滤运单号已存在的单据
        List<ReconciliationJDImportExcelVO> importList = new ArrayList<>();

        List<String> logisticsNoList = excelVOList.stream().map(ReconciliationJDImportExcelVO::getWaybillNumber).collect(Collectors.toList());
        List<String> existLogisticsNo = reconciliationOrderItemService.findExistLogisticsNo(logisticsNoList);
        importList = excelVOList.stream()
                .filter(e -> CollUtil.isEmpty(importCheck(e, existLogisticsNo)))
                .collect(Collectors.toList());

        if (CollUtil.isNotEmpty(importList)) {
            // 创建出区对账单
            log.info("ReconciliationOrderJDImportHandler 创建出区对账单");
            ReconciliationJDImportExcelVO excelVO = importList.get(0);
            String declareOrderNo = StrUtil.isNotBlank(excelVO.getCargoOwnerCode()) && excelVO.getCargoOwnerCode().startsWith("EBU")
                    ? excelVO.getMerchantOrderNumber() : excelVO.getOrderNumber();
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByDeclareOrderNoFull(declareOrderNo);
            Long areaCompanyId = null;
            if (Objects.nonNull(customsInventoryDTO)) {
                areaCompanyId = customsInventoryDTO.getAreaCompanyId();
            }
            Long finalAreaCompanyId = areaCompanyId;

            ReconciliationOrderDTO reconciliationOrderDTO = create(finalAreaCompanyId,
                    importVO.getReconciliationTime(), excelVOList.size(), SimpleUserHelper.getRealUserName());
            String sn = reconciliationOrderDTO.getSn();

            // 提交任务到线程池
            log.info("ReconciliationOrderJDImportHandler 提交导入表体任务到线程池 sn={}", sn);
            List<List<ReconciliationJDImportExcelVO>> partitionedLists = new ArrayList<>();
            int step = 2000;
            for (int i = 0; i < excelVOList.size(); i += step) {
                partitionedLists.add(new ArrayList<>(excelVOList.subList(i, Math.min(i + step, excelVOList.size()))));
            }
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            for (List<ReconciliationJDImportExcelVO> partition : partitionedLists) {
                futureList.add(CompletableFuture.runAsync(() -> {
                    ReconciliationOrderImportInfoDTO importInfoDTO = new ReconciliationOrderImportInfoDTO();
                    importInfoDTO.setExcelVOList(partition);
                    importInfoDTO.setReconciliationDate(importVO.getReconciliationTime());
                    importInfoDTO.setAreaCompanyId(finalAreaCompanyId);
//                importInfoDTO.setOperator(operator);
                    importInfoDTO.setReconciliationSn(sn);
                    reconciliationOrderItemService.excelImport(importInfoDTO);
                }, reconciliationThreadExecutor));
            }
            try {
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
            } catch (Exception e) {
                log.error("ReconciliationOrderJDImportHandler 表体导入CompletableFuture结果异常：{}", e.getMessage(), e);
            }
            log.info("ReconciliationOrderJDImportHandler 导入完成 调用数据中台进行对账 sn={}", sn);
            this.invokeDataCenterBalanceAccount(Collections.singletonList(sn));
            log.info("ReconciliationOrderJDImportHandler 导入完成 调用数据中台成功 sn={}", sn);
        }
    }

    public List<String> importCheck(ReconciliationJDImportExcelVO vo, List<String> existLogisticsNoGoodsCodeList) {
        List<String> errorMsg = new ArrayList<>();
        if (StringUtils.isEmpty(vo.getProductCode())) {
            errorMsg.add("商品编码存在字段为空！");
        }
        if (Objects.isNull(vo.getCheckQuantity())) {
            errorMsg.add("复核数量存在字段为空！");
        }
        if (StringUtils.isEmpty(vo.getCargoOwnerCode())) {
            errorMsg.add("货主编码存在字段为空！");
        } else {
            if (vo.getCargoOwnerCode().startsWith("EBU")) {
                // 京东pop
                if (StrUtil.isBlank(vo.getMerchantOrderNumber())) {
                    errorMsg.add("京东POP商家订单号不能为空！");
                }
            } else {
                // 京东直营
                if (StringUtils.isEmpty(vo.getOrderNumber())) {
                    errorMsg.add("订单号存在字段为空！");
                }
            }
        }
//        if (StringUtils.isEmpty(vo.getCargoOwnerName())) {
//            errorMsg.add("货主名称存在字段为空！");
//        }
        if (StringUtils.isEmpty(vo.getWaybillNumber())) {
            errorMsg.add("运单号存在字段为空！");
        }
        if (existLogisticsNoGoodsCodeList.contains(vo.getWaybillNumber() + "_" + vo.getProductCode())) {
            errorMsg.add("存在非作废的运单号+商品编码组合（过滤）");
        }
        if (CollUtil.isNotEmpty(errorMsg)) {
            log.info("reconciliation importCheck errorMsg={}", JSON.toJSONString(errorMsg));
        }
        return errorMsg;
    }

    @Override
    public ReconciliationOrderDTO create(Long areaCompanyId, Long reconciliationTime, Integer totalCount, String operator) {
        ReconciliationOrderDO reconciliationOrderDO = new ReconciliationOrderDO();
        reconciliationOrderDO.setSn(sequenceService.generateReconciliationSn());
        reconciliationOrderDO.setAreaCompanyId(areaCompanyId);
        CompanyDTO areaCompanyDTO = baseDataService.getCompanyDTOById(reconciliationOrderDO.getAreaCompanyId());
        String entityWarehouseCode = "";
        if (Objects.nonNull(areaCompanyDTO)) {
            Map map = JSON.parseObject(areaCompanyAndWarehouseMap, Map.class);
            if (map.containsKey(areaCompanyDTO.getName())) {
                entityWarehouseCode = (String) map.get(areaCompanyDTO.getName());
            } else if (areaCompanyDTO.getName().equalsIgnoreCase("义乌楠欧供应链管理有限公司")) {
                entityWarehouseCode = "DT_YWZBSANWMS0111";
            } else if (areaCompanyDTO.getName().equalsIgnoreCase("金华垂直云供应链管理有限公司")) {
                entityWarehouseCode = "DT_YWKJWMS0312";
            }
        }
        reconciliationOrderDO.setEntityWarehouseCode(entityWarehouseCode);
        reconciliationOrderDO.setStatus(ReconciliationStatusEnums.ANALYSIS_ING.getCode());
        reconciliationOrderDO.setReconciliationDate(new Date(reconciliationTime));
        reconciliationOrderDO.setOperator(operator == null ? UserUtils.getUserRealName() : operator);
        reconciliationOrderDO.setTotalOrderCount(totalCount);
        reconciliationOrderBaseService.insertSelective(reconciliationOrderDO);
        saveLog(reconciliationOrderDO.getId(), ReconciliationStatusEnums.ANALYSIS_ING.getCode(), "新建对账", operator);
        return ConvertUtil.beanConvert(reconciliationOrderDO, ReconciliationOrderDTO.class);
    }


    @Override
    public List<ReconciliationTrackLogDTO> listTrackLog(Long id) {
        List<ReconciliationTrackLogDO> logDOList = reconciliationOrderBaseService.findLogByOrderId(id);
        return logDOList.stream().map(i -> {
            ReconciliationTrackLogDTO dto = new ReconciliationTrackLogDTO();
            BeanUtils.copyProperties(i, dto);
            ReconciliationStatusEnums statusEnums = ReconciliationStatusEnums.getEnums(i.getStatus());
            dto.setStatusDesc(statusEnums == null ? "" : statusEnums.getDesc());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refresh(List<Long> idList) {
        List<ReconciliationOrderDO> doList = reconciliationOrderBaseService.findById(idList);
        if (CollUtil.isEmpty(doList)) {
            return;
        }
        List<String> notAllowStatus = Arrays.asList(ReconciliationStatusEnums.ANALYSIS_ING.getCode(),
                ReconciliationStatusEnums.DISCARD.getCode(), ReconciliationStatusEnums.FINISH.getCode());
        if (doList.stream().anyMatch(i -> notAllowStatus.contains(i.getStatus()))) {
            throw new ArgsInvalidException("当前状态无法刷新");
        }
        // 调用数据中台接口重新对比数据
        List<String> snList = doList.stream().map(ReconciliationOrderDO::getSn).collect(Collectors.toList());

        for (String sn : snList) {
            if (!RedissLockUtil.tryLock("ccs:reconciliation:order:refresh:" + sn, TimeUnit.SECONDS, 3, 5)) {
                throw new ArgsInvalidException("操作过于频繁，请稍后重试");
            }
        }
        Boolean result = invokeDataCenterBalanceAccount(snList);
        if (!result) {
            throw new ArgsInvalidException("请求数据中台接口失败");
        }
        Example example = new Example(ReconciliationOrderDO.class);
        example.createCriteria().andIn("id", idList);
        ReconciliationOrderDO updateDO = new ReconciliationOrderDO();
        updateDO.setStatus(ReconciliationStatusEnums.ANALYSIS_ING.getCode());
        reconciliationOrderBaseService.updateByExampleSelective(updateDO, example);
        doList.forEach(i -> {
            saveLog(doList.get(0).getId(), i.getStatus(), "对账单重新计算中");
        });
        reconciliationOrderItemService.saveRefreshLog(snList);
    }

    /**
     * 数据中台对账结果 后置处理
     */
    @Override
    public void balanceAccountPostProcess(ReconciliationImportResult result) {
        if (StrUtil.isBlank(result.getReconciliationSn())) {
            throw new ArgsInvalidException("对账ID不能为空");
        }
        ReconciliationOrderDO orderDO = reconciliationOrderBaseService.findBySn(result.getReconciliationSn());
        if (Objects.isNull(orderDO)) {
            throw new ArgsInvalidException("对账ID不存在 - " + result.getReconciliationSn());
        }
        if (Objects.equals(ReconciliationStatusEnums.DISCARD.getCode(), orderDO.getStatus())
                || Objects.equals(ReconciliationStatusEnums.FINISH.getCode(), orderDO.getStatus())) {
            log.info("[op:ReconciliationOrderServiceImpl] 数据中台对账结果 后置处理 sn {} - 对账单状态为：{}，不处理", orderDO.getSn(), orderDO.getStatus());
            return;
        }
        ReconciliationOrderDO orderUpdateDO = new ReconciliationOrderDO();
        BeanUtil.copyProperties(result, orderUpdateDO);
        Integer differenceOrderCount = result.getDifferenceOrderCount();
        Integer unmatchedCount = result.getUnmatchedCount();

        orderUpdateDO.setId(orderDO.getId());
        String logInfo = "";
        if (StrUtil.isEmpty(result.getErrorMsg())) {
            if (unmatchedCount == 0 && differenceOrderCount == 0) {
                orderUpdateDO.setStatus(ReconciliationStatusEnums.FINISH.getCode());
                orderUpdateDO.setReconciliationTag(ReconciliationTagEnums.remove(orderDO.getReconciliationTag(), ReconciliationTagEnums.PROCESSING));
                logInfo = "对账完成";
            } else {
                orderUpdateDO.setStatus(ReconciliationStatusEnums.EXCEPTION.getCode());
                logInfo = "解析成功：对账数据存在差异";
            }
        } else {
            orderUpdateDO.setStatus(ReconciliationStatusEnums.ANALYSIS_FAIL.getCode());
            logInfo = "对账解析失败：" + result.getErrorMsg();
            orderUpdateDO.setExceptionReason(result.getErrorMsg());
            // 先前统计数量保持不变
            orderUpdateDO.setUnmatchedCount(null);
            orderUpdateDO.setDifferenceOrderCount(null);
            orderUpdateDO.setNoDifferenceOrderCount(null);
            orderUpdateDO.setTotalOrderCount(null);
        }
        Example example = new Example(ReconciliationOrderDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("sn", result.getReconciliationSn());
        reconciliationOrderBaseService.updateByExampleSelective(orderUpdateDO, example);
        saveLog(orderDO.getId(), orderUpdateDO.getStatus(), logInfo);
    }

    /**
     * 调整数量 正数为加 负数为减
     * <p>
     * 总数 = 无差异数量 + 差异数量 + 未匹配数量 + 作废数量
     *
     * @param sn                     对账ID
     * @param changeUnmatchedCount   变更未匹配数量
     * @param changeDifferenceCount  变更差异数量
     * @param changeNoDifferentCount 变更无差异数量
     * @param isDiscard              作废操作
     */
    @Override
    public void adjustCount(String sn, Integer changeUnmatchedCount, Integer changeDifferenceCount, Integer changeNoDifferentCount, boolean isDiscard) {
        log.info("对账单调整数量，sn：{}，unmatchedCount：{}，differenceOrderCount：{}", sn, changeUnmatchedCount, changeDifferenceCount);
        ReconciliationOrderDO orderDO = reconciliationOrderBaseService.findBySn(sn);
        if (Objects.isNull(orderDO)) {
            throw new ArgsInvalidException("对账ID不存在 - " + sn);
        }
        ReconciliationOrderDO orderUpdateDO = new ReconciliationOrderDO();
        orderUpdateDO.setId(orderDO.getId());
        Integer totalOrderCount = orderDO.getTotalOrderCount();
        int unmatchedCount = orderDO.getUnmatchedCount() == null ? 0 : orderDO.getUnmatchedCount() + changeUnmatchedCount;
        int differenceOrderCount = orderDO.getDifferenceOrderCount() == null ? 0 : orderDO.getDifferenceOrderCount() + changeDifferenceCount;
        int noDifferenceOrderCount = orderDO.getNoDifferenceOrderCount() == null ? 0 : orderDO.getNoDifferenceOrderCount() + changeNoDifferentCount;

        orderUpdateDO.setTotalOrderCount(totalOrderCount);
        orderUpdateDO.setUnmatchedCount(unmatchedCount);
        orderUpdateDO.setDifferenceOrderCount(differenceOrderCount);
        orderUpdateDO.setNoDifferenceOrderCount(noDifferenceOrderCount);
        String logInfo = "";
        if (noDifferenceOrderCount != 0 && unmatchedCount == 0 && differenceOrderCount == 0) {
            orderUpdateDO.setStatus(ReconciliationStatusEnums.FINISH.getCode());
            orderUpdateDO.setReconciliationTag(ReconciliationTagEnums.remove(orderDO.getReconciliationTag(), ReconciliationTagEnums.PROCESSING));
            logInfo = "对账完成";
        } else if (isDiscard) {
            if (Objects.equals(orderDO.getStatus(), ReconciliationStatusEnums.DISCARD.getCode())) {
                log.info("对账单已作废，无需修改状态");
            } else if (noDifferenceOrderCount == 0 && unmatchedCount == 0 && differenceOrderCount == 0) {
                orderUpdateDO.setStatus(ReconciliationStatusEnums.DISCARD.getCode());
                orderUpdateDO.setReconciliationTag(ReconciliationTagEnums.remove(orderDO.getReconciliationTag(), ReconciliationTagEnums.PROCESSING));
                logInfo = "对账订单已全部作废";
            }
        } else {
            orderUpdateDO.setStatus(ReconciliationStatusEnums.EXCEPTION.getCode());
//            logInfo = "解析成功：对账数据存在差异";
        }
        Example example = new Example(ReconciliationOrderDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("sn", sn);
        reconciliationOrderBaseService.updateByExampleSelective(orderUpdateDO, example);
        if (StrUtil.isNotBlank(logInfo)) {
            saveLog(orderDO.getId(), orderUpdateDO.getStatus(), logInfo);
        }
    }

    @Override
    public void submitImport(ReconciliationOrderImportInfoDTO submitData) {
        log.info("对账单提交导入，submitData：{}", JSON.toJSONString(submitData));
        List<ReconciliationJDImportExcelVO> excelVOList = submitData.getExcelVOList();
        if (CollUtil.isEmpty(excelVOList)) {
            return;
        }
        reconciliationOrderItemService.excelImport(submitData);
    }

    @Override
    public Boolean invokeDataCenterBalanceAccount(List<String> snlist) {
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("reconciliationSnList", snlist);
        log.info("对账单提交对账平台请求：{}", JSON.toJSONString(paramMap));
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(dataCenterUrl + "/inOut/balanceAccount/ccsInvoke", JSON.toJSONString(paramMap));
            String body = httpRequest.body();
            log.info("对账单提交对账平台结果：{} , snList={}", body, JSON.toJSONString(snlist));
            if (httpRequest.ok()) {
                return true;
            } else {
                for (String sn : snlist) {
                    this.balanceAccountPostProcess(ReconciliationImportResult.isFail(sn, "请求数据中台接口失败"));
                }
            }
        } catch (Exception e) {
            log.error("对账单提交数据中台异常", e);
            for (String sn : snlist) {
                this.balanceAccountPostProcess(ReconciliationImportResult.isFail(sn, "请求数据中台接口失败"));
            }
        }
        return false;
    }

    public void saveLog(Long reconciliationItemId, String status, String logInfo) {
        saveLog(reconciliationItemId, status, logInfo, null);
    }

    public void saveLog(Long reconciliationId, String status, String logInfo, String operator) {
        ReconciliationTrackLogDO logDO = new ReconciliationTrackLogDO();
        logDO.setReconciliationId(reconciliationId);
        logDO.setStatus(status);
        logDO.setLogInfo(logInfo);
        logDO.setOperator(StrUtil.isNotBlank(operator) ? operator : UserUtils.getUserRealName());
        reconciliationOrderBaseService.insertLogSelective(logDO);
    }

    @Test
    public void test() {
        String fileName = "https://bgd-local.oss-cn-hangzhou.aliyuncs.com/jd-wms-export/客户订单复核结果查询********.csv";
//        String fileName = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E5%AE%A2%E6%88%B7%E8%AE%A2%E5%8D%95%E5%A4%8D%E6%A0%B8%E7%BB%93%E6%9E%9C%E6%9F%A5%E8%AF%A220241018.csv";

        Date startTime = new Date();
        log.info("start - " + startTime);
        List<ReconciliationJDImportExcelVO> excelVOList = null;
        try {
            if (fileName.endsWith(".csv")) {
                excelVOList = readCsvFile(fileName, ReconciliationJDImportCsvVO.class).stream().map(e -> {
                    ReconciliationJDImportExcelVO excelVO = new ReconciliationJDImportExcelVO();
                    BeanUtils.copyProperties(e, excelVO);
                    if (StrUtil.isNotBlank(e.getOrderNumber()) && e.getOrderNumber().startsWith("=\"")) {
                        excelVO.setOrderNumber(e.getOrderNumber().replace("=\"", ""));
                    }
                    if (StrUtil.isNotBlank(e.getProductCode()) && e.getProductCode().startsWith("=\"")) {
                        excelVO.setProductCode(e.getProductCode().replace("=\"", ""));
                    }
                    return excelVO;
                }).collect(Collectors.toList());
//                excelVOList = ConvertUtil.listConvert(readCsvFile(fileName, ReconciliationJDImportCsvVO.class), ReconciliationJDImportExcelVO.class);
            } else if (fileName.endsWith(".xlsx")) {
                excelVOList = readExcelFile(fileName);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        Date endTime = new Date();
        log.info("finish - " + endTime);
        int i = 1;
        for (ReconciliationJDImportExcelVO vo : excelVOList) {
            log.info(String.valueOf(i++) + " - " + vo.getBatchNumber());
        }
        System.out.println("总耗时：" + (endTime.getTime() - startTime.getTime()));
    }

    private static <T> List<T> readCsvFile(String filePath, Class<T> clazz) throws IOException {
        try (InputStream inputStream = DownLoadUtil.downloadNet(filePath);
             BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, "GBK"));
        ) {
            CSVReader csvReader = new CSVReader(reader);
            HeaderColumnNameMappingStrategy<T> strategy = new HeaderColumnNameMappingStrategy<>();
            strategy.setType(clazz);

            CsvToBean<T> csvToBean = new CsvToBeanBuilder<T>(csvReader)
                    .withType(clazz)
                    //将第一行视为标题行
                    .withMappingStrategy(strategy)
                    .build();
            return csvToBean.parse();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
    }


    private static List<ReconciliationJDImportExcelVO> readCsvFile(String filePath) throws IOException {
        List<ReconciliationJDImportExcelVO> orderDataList = new ArrayList<>();
        String[] header = new String[]{
                "波次号", "集合单号", "任务单号", "订单号", "生产单号", "商家订单号", "包裹号",
                "青流箱号", "订单类型", "站点编码", "站点名称", "单多品类型", "商品编码",
                "商家商品编码", "商品名称", "复核数量", "是否rebin", "商品等级", "箱规",
                "药品批号", "复核台号", "周转筐号", "Rebin格号", "截单时间", "增票标识",
                "订单状态", "操作人", "操作人姓名", "操作时间", "是否满箱", "栈板号",
                "是否满板", "有效期至", "运单号", "货主编码", "三方运单号", "货主名称",
                "销售平台", "唯品会入库单编号", "生产方式", "是否直通车", "商家订单类型",
                "复核设备", "打包设备", "目的配送中心", "目的仓"
        };
        InputStream inputStream = DownLoadUtil.downloadNet(filePath);
        if (inputStream == null) {
            return orderDataList;
        }
        try (BufferedReader fileReader = new BufferedReader(new InputStreamReader(inputStream, "GBK"));
             CSVParser csvParser = new CSVParser(fileReader, CSVFormat.DEFAULT.builder()
                     .setHeader(header)
                     .setSkipHeaderRecord(true)
                     .build())) {

            for (CSVRecord csvRecord : csvParser) {
                ReconciliationJDImportExcelVO excelVO = new ReconciliationJDImportExcelVO();
                excelVO.setBatchNumber(csvRecord.get("波次号"));
                excelVO.setCollectionOrderNumber(csvRecord.get("集合单号"));
                excelVO.setTaskOrderNumber(csvRecord.get("任务单号"));
                excelVO.setOrderNumber(csvRecord.get("订单号"));
                excelVO.setProductionOrderNumber(csvRecord.get("生产单号"));
                excelVO.setMerchantOrderNumber(csvRecord.get("商家订单号"));
                excelVO.setPackageNumber(csvRecord.get("包裹号"));
                excelVO.setQingliuBoxNumber(csvRecord.get("青流箱号"));
                excelVO.setOrderType(csvRecord.get("订单类型"));
                excelVO.setSiteCode(csvRecord.get("站点编码"));
                excelVO.setSiteName(csvRecord.get("站点名称"));
                excelVO.setSingleMultipleType(csvRecord.get("单多品类型"));
                excelVO.setProductCode(csvRecord.get("商品编码"));
                excelVO.setMerchantProductCode(csvRecord.get("商家商品编码"));
                excelVO.setProductName(csvRecord.get("商品名称"));
                excelVO.setCheckQuantity(Integer.parseInt(csvRecord.get("复核数量")));
                excelVO.setIsRebin(Boolean.parseBoolean(csvRecord.get("是否rebin")));
                excelVO.setProductGrade(csvRecord.get("商品等级"));
                excelVO.setBoxSpecification(csvRecord.get("箱规"));
                excelVO.setDrugBatchNumber(csvRecord.get("药品批号"));
                excelVO.setCheckStationNumber(csvRecord.get("复核台号"));
                excelVO.setTurnoverBasketNumber(csvRecord.get("周转筐号"));
                excelVO.setRebinSlotNumber(csvRecord.get("Rebin格号"));
                excelVO.setCutoffTime(csvRecord.get("截单时间"));
                excelVO.setInvoiceFlag(csvRecord.get("增票标识"));
                excelVO.setOrderStatus(csvRecord.get("订单状态"));
                excelVO.setOperator(csvRecord.get("操作人"));
                excelVO.setOperatorName(csvRecord.get("操作人姓名"));
                excelVO.setOperationTime(csvRecord.get("操作时间"));
                excelVO.setFullBox(Boolean.parseBoolean(csvRecord.get("是否满箱")));
                excelVO.setPalletNumber(csvRecord.get("栈板号"));
                excelVO.setFullPallet(Boolean.parseBoolean(csvRecord.get("是否满板")));
                excelVO.setExpirationDate(csvRecord.get("有效期至"));
                excelVO.setWaybillNumber(csvRecord.get("运单号"));
                excelVO.setCargoOwnerCode(csvRecord.get("货主编码"));
                excelVO.setThirdPartyWaybillNumber(csvRecord.get("三方运单号"));
                excelVO.setCargoOwnerName(csvRecord.get("货主名称"));
                excelVO.setSalesPlatform(csvRecord.get("销售平台"));
                excelVO.setVipInboundOrderNumber(csvRecord.get("唯品会入库单编号"));
                excelVO.setProductionMethod(csvRecord.get("生产方式"));
                excelVO.setDirectTrain(Boolean.parseBoolean(csvRecord.get("是否直通车")));
                excelVO.setMerchantOrderType(csvRecord.get("商家订单类型"));
                excelVO.setCheckEquipment(csvRecord.get("复核设备"));
                excelVO.setPackingEquipment(csvRecord.get("打包设备"));
                excelVO.setDestinationDistributionCenter(csvRecord.get("目的配送中心"));
                excelVO.setDestinationWarehouse(csvRecord.get("目的仓"));

                orderDataList.add(excelVO);
            }
        }
        return orderDataList;
    }

    private static List<ReconciliationJDImportExcelVO> readExcelFile(String filePath) throws IOException {
        return EasyExcel.read(DownLoadUtil.downloadNet(filePath))
                .headRowNumber(1).head(ReconciliationJDImportExcelVO.class).sheet(0).doReadSync();
    }

}
