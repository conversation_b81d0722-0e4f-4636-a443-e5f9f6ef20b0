package com.danding.cds.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/23 16:02
 */
@Component
@RefreshScope
@Data
public class OrderCBaseConfig implements Serializable {

    @Value("${pdd.host:}")
    private String PDD_HOST;
    @Value("${jieztech.host:}")
    private String jieztechHost;
    @Value("${jieztech.appId:}")
    private String jieztechAppId;
    @Value("${jieztech.privateKey:}")
    private String jieztechPrivateKey;
    @Value("${jieztech.subMerchantId:}")
    private String jieztechSubMerchantId;
    @Value("${jieztech.subMerchantName:}")
    private String jieztechSubMerchantName;
    @Value("${jieztech.merchantId:}")
    private String jieztechMerchantId;
    @Value("${jieztech.merchantName:}")
    private String jieztechMerchantName;
    @Value("${jieztech.declare.appId:}")
    private String jieztechAppDeclareId;
    @Value("${jieztech.declare.privateKey:}")
    private String jieztechDeclarePrivateKey;
    @Value("${filter.special.char.config:}")
    private String filterSpecialCharConfig;
    @Value("${jieztech.haikouRoute:HAIKOU-JZXLS}")
    private String haikouRoute;
    @Value("${jieztech.haikouCode:6409}")
    private String haikouCode;
    @Value("${jieztech.kunmingRoute:KUNMING-JZXLS}")
    private String kunmingRoute;
    @Value("${jieztech.kunmingCode:8634}")
    private String kunmingCode;
    @Value("${jieztech.taoBaoGlobalFx:}")
    private String[] taoBaoGlobalFx;
    @Value("${jieztech.weatherForwardJieztech:false}")
    private Boolean weatherForwardJieztech;
    /**
     * {@link com.danding.cds.v2.bean.vo.req.BarCodeDeclareReqVo}
     */
    @Value("${inventory.gray.barCode.config:}")
    private String barCodeConfig;
    /**
     * 电商平台ebpCode
     */
    @Value("${byteDance.ebpCodes:}")
    private String BYTE_DANCE_EBP_CODES;
    /**
     * 截单配置
     */
    @Value("${stop.accept.order:false}")
    private Boolean STOP_ACCEPT_ORDER;
    @Value("${merchant.replace.config:}")
    private String merchantReplaceConfig;
    @Value("${goodsRecord.mapping.map.json:}")
    private String goodsRecordMappingMapJson;
    /**
     * 需要回传的跨境进口统一编码
     */
    @Value("${platCallback.needCallbackCode:}")
    private List<String> needCallbackCode;
    /**
     * 需要回传的账册标签
     */
    @Value("${platCallback.needCallbackBookTag:}")
    private List<Integer> needCallbackBookTag;
    @Value("${little_giant_route_code_list:[]}")
    private String[] littleGiantRouteCodeList;
    @Value("${jieztech.callback.routeNameList:}")
    private String[] routeNames;
    @Value("${pdd.edpId:}")
    private Long pddEbpId;
    @Value("${jd.itemName.set.check.regex.json:}")
    private String jdItemNameSetCheckJson;

    @Value("${declare.route.record.newType:false}")
    private Boolean declareRouteRecordNewType;

    @Value("${message_alert_config:{}}")
    private String messageAlertConfig;
}
