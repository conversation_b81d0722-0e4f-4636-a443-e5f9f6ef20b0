package com.danding.cds.service.mq.consumer;

import com.danding.cds.c.api.service.CustomsInventoryCancelService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description: 撤单申报MQ消费者
 * @date 2024/1/19 14:56
 */
@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(topic = "ccs-declare-inventory-cancel-topic",
        consumerGroup = "ccs-declare-inventory-cancel-consumer")
public class CustomsInventoryCancelDeclareConsumer extends MessageHandler {

    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;
//    @Autowired
//    private RedisTemplate redisTemplate;

    @Override
    public void handle(Object o) throws RuntimeException {
        if (o == null) {
            return;
        }
        String id = String.valueOf(o);
        log.info("CustomsInventoryCancelDeclareConsumer id = {}", id);
        try {
            customsInventoryCancelService.doDeclareInventoryCancel(Long.valueOf(id));
        } catch (Exception e) {
            log.error("CustomsInventoryCancelDeclareConsumer id = {} 失败", id, e);
        } finally {
            //申报完成不删key了 等key自动过期控制
            log.info("CustomsInventoryCancelDeclareConsumer 申报结束 id={}", id);
//            log.info("CustomsInventoryCancelDeclareConsumer 删key跑路 id={}", id);
//            try {
//                Boolean hasKey = redisTemplate.hasKey("ccs:declare:inventory:cancel:" + id);
//                if (Boolean.TRUE.equals(hasKey)) {
//                    redisTemplate.delete("ccs:declare:inventory:cancel:" + id);
//                }
//            } catch (Exception e) {
//                log.info("删除rediskey失败 key={}", "ccs:declare:inventory:cancel:" + id);
//            }
        }
    }
}
