package com.danding.cds.service.declareImpl.order;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 字节CEB订单代理申报
 **/
@Service("BYTE_DANCE_DANCE_CEB_PROXY_ORDER_DECLARE")
@Slf4j
public class ByteDanceCebProxyOrderDeclareImpl extends OrderDeclareAbstract {

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Override
    protected OrderDeclareResult mockDeclareTest(WrapOrderDeclareInfo info) {
        log.info("字节CEB订单代理申报，测试环境MOCK");
        byteDanceDeclareService.cloudDeclareMock(info);
        return null;
    }

    @Override
    protected OrderDeclareResult declare(WrapOrderDeclareInfo info) {
        log.info("字节CEB订单代理申报，info={}", JSON.toJSONString(info));
        byteDanceDeclareService.cloudOrderDeclare(info);
        return null;
    }
}
