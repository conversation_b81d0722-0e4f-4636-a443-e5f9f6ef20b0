package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.bean.vo.PddOfficialDeclareResVo;
import com.danding.cds.bean.vo.PddOfficialDeclareShipReqVo;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.RefundOrderService;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.handler.PddDeclareTrackLogParametersHandler;
import com.danding.cds.handler.PddThirdDeclareTrackLogParametersHandler;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.service.PddRedisService;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Objects;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.net.HttpURLConnection;
import java.util.Arrays;
import java.util.List;

/**
 * @program: cds-center
 * @description: 拼多多申报
 * @author: 潘本乐（Belep）
 * @create: 2021-09-13 10:38
 **/
@Service
@RefreshScope
@Slf4j
public class PddDeclareService {

    @Value("${pdd.host:}")
    private String PDD_HOST;

    @Value("${pdd.data.send.enable:false}")
    private boolean PDD_DATA_SEND_ENABLE;

    @Value("${pdd.official.declare.exception.codes:}")
    private String[] pddCloundExceptionCodes;

    @Value("${pdd.official.declare.exception.notify.phones:}")
    private String[] pddCloundExceptionNotifyPhones;

    @Value("${pdd.official.declare.exception.webhook:}")
    private String pddCloundExceptionWebhook;

    @Value("${pdd.pddOfficial:false}")
    private Boolean pddOfficial;

    @Value("${pdd.refund.pddOfficial:false}")
    private Boolean pddRefundOfficial;

    @Value("${pdd.pddOfficialSwitch:false}")
    private Boolean pddOfficialSwitch;

    @Autowired
    private PddDeclareService pddDeclareService;
    @Autowired
    private PddRedisService pddRedisService;
    @DubboReference
    private MessageService messageService;
    @Autowired
    private MessageSender messageSender;

    @Resource
    private RefundOrderService refundOrderService;

    private static final String CLOUD_OFFICIAL_DECLARE_URI = "/pdd/controller/declare/official/substitute";
    private static final String CLOUD_OFFICIAL_THIRD_MESSAGE_DECLARE_URI = "/pdd/controller/declare/official/thirdMessageDeclare";
    /**
     * 拼多多云内申报地址
     */
    @Value("${pdd.cloudDeclare.inventory:/pdd/controller/declare/inventory}")
    private String CLOUD_INVENTORY_DECLARE_URI;
    @Value("${pdd.cloudDeclare.logistics:/pdd/controller/declare/logistics}")
    private String CLOUD_LOGISTICS_DECLARE_URI;
    @Value("${pdd.cloudDeclare.order:/pdd/controller/declare/order}")
    private String CLOUD_ORDER_DECLARE_URI;
    @Value("${pdd.cloudDeclare.cancel:/pdd/controller/declare/inventoryCancel}")
    private String CLOUD_CANCEL_DECLARE_URI;
    @Value("${pdd.cloudDeclare.refund:/pdd/controller/declare/inventoryRefundV2}")
    private String CLOUD_REFUND_DECLARE_URI;

    private boolean invokeOfficialDeclare(String declareNo) {
        // 拼多多总开关
        if (pddOfficialSwitch) {
            return true;
        }
        // 拼多多子开关 在总开关关闭的情况下控制条数
        if (!pddOfficial) {
            return false;
        }
        return pddRedisService.weatherOfficialDeclare(declareNo);
    }

    /**
     * 拼多多云内清单申报
     *
     * @param orderInfo   数据
     * @param orderInfo 日志
     */
    public void cloudInventoryDeclareAndLog(WrapInventoryOrderInfo orderInfo) {
        if (!EnvironmentConfig.isOnline()) {
            return;
        }
        String declareNo = orderInfo.getCustomsInventoryDto().getOrderNo();
        if (this.invokeOfficialDeclare(declareNo)) {
            this.cloudOfficialInventoryDeclare(orderInfo);
        } else {
            this.cloudAssembleInventoryDeclareInfo(orderInfo);
        }
    }

    /**
     * 拼多多云内运单申报
     *
     * @param info 数据
     */
    public void cloudLogisticsDeclare(WrapShipmentInfo info) {
        if (!EnvironmentConfig.isOnline()) {
            return;
        }
        if (this.invokeOfficialDeclare(info.getDeclareOrderNo())) {
            this.cloudOfficialLogisticsDeclare(info);
        } else {
            this.cloudAssembleDeclareInfo(info, CLOUD_LOGISTICS_DECLARE_URI);
        }
    }

    public void cloudOrderDeclare(WrapOrderDeclareInfo info) {
        if (!EnvironmentConfig.isOnline()) {
            return;
        }
        if (this.invokeOfficialDeclare(info.getDeclareOrderNo())) {
            this.cloudOfficialOrderDeclare(info);
        } else {
            this.cloudAssembleDeclareInfo(info, CLOUD_ORDER_DECLARE_URI);
        }
    }

    public void cloudCancelDeclare(WarpCancelOrderInfo info) {
        if (!EnvironmentConfig.isOnline()) {
            return;
        }
        if (this.invokeOfficialDeclare(info.getCustomsInventoryDto().getOrderNo())) {
            this.cloudOfficialCancelOrder(info);
        } else {
            this.cloudAssembleDeclareInfo(info, CLOUD_CANCEL_DECLARE_URI);
        }
    }

    public void cloudRefundDeclare(WarpRefundOrderInfo info) {
        if (!EnvironmentConfig.isOnline()) {
            return;
        }
        if (pddRefundOfficial) {
            this.cloudOfficialRefundOrder(info);
        } else {
            this.cloudAssembleDeclareInfo(info, CLOUD_REFUND_DECLARE_URI);
        }
    }

    /**
     * 拼多多官方代报清单
     *
     * @param model 信息
     */
    private void cloudOfficialInventoryDeclare(WrapInventoryOrderInfo model) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.ZD_PERSONAL_GOODS_DECLARE.getKey());
        declareReqVo.setInfo(model);
        declareReqVo.setDeclareOrderSn(model.getCustomsInventoryDto().getOrderNo());
        cloudOfficialDeclare(declareReqVo);
        pddCustomsSendDataReceipt(model);
    }

    private static final String CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC = "ccs-pdd-customs-send-data-receipt-topic";

    private void pddCustomsSendDataReceipt(WrapBeanInfo model) {
        if (PDD_DATA_SEND_ENABLE) {
            if (model instanceof WrapInventoryOrderInfo) {
                log.info("pddCustomsSendDataReceipt topic:{} tag:{} data:{}", CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB621", model);
                messageSender.sendMsg(JSON.toJSONString(model), CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB621");
            } else if (model instanceof WrapOrderDeclareInfo) {
                log.info("pddCustomsSendDataReceipt topic:{} tag:{} data:{}", CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB311", model);
                messageSender.sendMsg(JSON.toJSONString(model), CCS_PDD_CUSTOMS_SEND_DATA_RECEIPT_TOPIC, "CEB311");
            }
        } else {
            log.info("pddCustomsSendDataReceipt PDD_DATA_SEND_ENABLE is false");
        }
    }
    /**
     * 拼多多官方代报订单
     *
     * @param info
     */
    private void cloudOfficialOrderDeclare(WrapOrderDeclareInfo info) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.ZD_IMPORT_ORDER.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getDeclareOrderNo());
        cloudOfficialDeclare(declareReqVo);
        pddCustomsSendDataReceipt(info);
    }

    /**
     * 拼多多云内官方代报
     *
     * @param info                 申报信息
     * @param clearanceMessageType 申报类型
     */
    public void cloudOfficialDeclare(WrapBeanInfo info, PddOfficialDeclareReqVo.ClearanceMessageType clearanceMessageType) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(clearanceMessageType.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getDeclareNos());
        pddDeclareService.cloudOfficialDeclare(declareReqVo);
    }


    /**
     * 测试环境走的方法
     *
     * @param info
     * @param clearanceMessageType
     */
    public void cloudOfficialDeclareTest(WrapBeanInfo info, PddOfficialDeclareReqVo.ClearanceMessageType clearanceMessageType) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(clearanceMessageType.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getDeclareNos());
        pddDeclareService.cloudOfficialDeclareTest(declareReqVo);
    }

    /**
     * 拼多多官方代报运单
     *
     * @param info
     */
    private void cloudOfficialLogisticsDeclare(WrapShipmentInfo info) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.CEB511.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getDeclareOrderNo());
        cloudOfficialDeclare(declareReqVo);
    }

    private void cloudOfficialCancelOrder(WarpCancelOrderInfo info) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.CEB623.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getCustomsInventoryDto().getOrderNo());
        cloudOfficialDeclare(declareReqVo);
    }

    private void cloudOfficialRefundOrder(WarpRefundOrderInfo info) {
        PddOfficialDeclareReqVo declareReqVo = new PddOfficialDeclareReqVo();
        declareReqVo.setClearanceMessageType(PddOfficialDeclareReqVo.ClearanceMessageType.CEB625.getKey());
        declareReqVo.setInfo(info);
        declareReqVo.setDeclareOrderSn(info.getCustomsInventoryDto().getOrderNo());
        cloudOfficialDeclare(declareReqVo);
    }

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsOrderService customsOrderService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @TrackLog(infoIndex = 0, handler = PddDeclareTrackLogParametersHandler.class)
    public String cloudOfficialDeclare(PddOfficialDeclareReqVo declareReqVo) {
        TrackLogBaseInfo logBaseInfo = new TrackLogBaseInfo();
        logBaseInfo.setStatusDesc(TrackLogConstantMixAll.FAIL);
        String pddUrl = PDD_HOST + CLOUD_OFFICIAL_DECLARE_URI;
        log.info("拼多多官方代报-调用云内请求信息：PDD proxy, url={}, param={}", pddUrl, JSON.toJSONString(declareReqVo));
        // 拼多多转发至云内应用申报
        try {
            HttpRequest httpRequest = HttpRequest.post(pddUrl)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                    .readTimeout(30000)
                    .connectTimeout(15000)
                    .send(JSON.toJSONString(declareReqVo));
            String reponseBody = httpRequest.body();
            int code = httpRequest.code();
            log.info("拼多多官方代报-调用云内响应信息：Md5DeclareSn: {} ,Response-Body：{},HttpRequest-code: {}", declareReqVo.getDeclareOrderSn(), JSON.toJSONString(reponseBody), code);
            if (HttpURLConnection.HTTP_GATEWAY_TIMEOUT == code) {
                // 将对应的表的last declare time -10分钟
                switch (declareReqVo.getClearanceMessageType()) {
                    case "ZD_PERSONAL_GOODS_DECLARE":
                        // : 2021/10/15 qugai 清单表 ccs_customs_Inventory
                        customsInventoryService.updateByLastDeclareTime(declareReqVo.getDeclareOrderSn());
                        break;
                    case "ZD_IMPORT_ORDER":
                        customsOrderService.updateByLastDeclareTime(declareReqVo.getDeclareOrderSn());
                        break;

                    case "CEB511":
                        customsLogisticsService.updateBylastDeclareTime(declareReqVo.getDeclareOrderSn());
                        break;
                }
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
            }
            if (httpRequest.ok()) {
                PddOfficialDeclareResVo declareResVo = JSON.parseObject(reponseBody, PddOfficialDeclareResVo.class);
                this.exceptionNotify(declareReqVo, declareResVo);
                logBaseInfo.setStatusDesc(TrackLogConstantMixAll.SUCCESS);
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
                return declareResVo.getMessage();
            } else {
                this.exceptionNotify(declareReqVo, reponseBody);
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
                return "拼多多官方代报失败";
            }

        } catch (Exception e) {
            log.error("拼多多官方代报异常 -{}", e.getMessage(), e);
            this.exceptionNotify(declareReqVo, e.getMessage());
            TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
            return "拼多多官方代报异常";
        }
    }

    @TrackLog(infoIndex = 0, handler = PddThirdDeclareTrackLogParametersHandler.class)
    public String cloudOfficialShipmentThirdMessageDeclare(PddOfficialDeclareShipReqVo declareReqVo, CustomsLogisticsDTO customsLogisticsDTO) {
        TrackLogBaseInfo logBaseInfo = new TrackLogBaseInfo();
        logBaseInfo.setStatusDesc(TrackLogConstantMixAll.FAIL);
        String pddUrl = PDD_HOST + CLOUD_OFFICIAL_THIRD_MESSAGE_DECLARE_URI;
        log.info("拼多多官方代报 调用云内第三方报文请求信息 url={} param={}", pddUrl, JSON.toJSONString(declareReqVo));
        // 拼多多转发至云内应用申报
        try {
            HttpRequest httpRequest = HttpRequest.post(pddUrl)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                    .readTimeout(30000)
                    .connectTimeout(15000)
                    .send(JSON.toJSONString(declareReqVo));
            String reponseBody = httpRequest.body();
            int code = httpRequest.code();
            log.info("拼多多官方代报 调用云内第三方报文请求信息 申报单号:{} ,Response-Body:{},HttpRequest-code:{}", declareReqVo.getDeclareOrderSn(), JSON.toJSONString(reponseBody), code);
            if (HttpURLConnection.HTTP_GATEWAY_TIMEOUT == code) {
                // 将对应的表的last declare time -10分钟
                customsLogisticsService.updateBylastDeclareTime(declareReqVo.getDeclareOrderSn());
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
            }
            if (httpRequest.ok()) {
                PddOfficialDeclareResVo declareResVo = JSON.parseObject(reponseBody, PddOfficialDeclareResVo.class);
                //判断pdd-support端的返回值
                if (Objects.equal(declareResVo.getCode(), "200")) {
                    customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_SUCCESS);
                    logBaseInfo.setStatusDesc(TrackLogConstantMixAll.SUCCESS);
                } else {
                    customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_FAIL);
                }
//                this.exceptionNotify(declareReqVo, declareResVo);
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
                return declareResVo.getMessage();
            } else {
                customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_FAIL);
//                this.exceptionNotify(declareReqVo, reponseBody);
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
                return "拼多多官方代报失败";
            }
        } catch (Exception e) {
            log.error("拼多多官方代报异常 -{}", e.getMessage(), e);
            customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_FAIL);
//            this.exceptionNotify(declareReqVo, e.getMessage());
            TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
            return "拼多多官方代报异常";
        }
    }

    @TrackLog(infoIndex = 0, handler = PddDeclareTrackLogParametersHandler.class)
    public String cloudOfficialDeclareTest(PddOfficialDeclareReqVo declareReqVo) {
        TrackLogBaseInfo logBaseInfo = new TrackLogBaseInfo();
        logBaseInfo.setStatusDesc(TrackLogConstantMixAll.SUCCESS);
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
        return null;
    }

    private void exceptionNotify(PddOfficialDeclareReqVo declareReqVo, String message) {
        if (StringUtils.isEmpty(message)) {
            return;
        }
        String declareNo = declareReqVo.getDeclareOrderSn();
        WrapBeanInfo info = declareReqVo.getInfo();
        StringBuilder builder = new StringBuilder();
        builder.append("<font color=\\\"warning\\\">**拼多多申报云内调用异常**</font>，请相关同事注意。\\n");
        builder.append(String.format("> 单号：%s\n", declareNo));
        builder.append(String.format("> 异常信息：%s\n", message));
        List<String> phoneList = pddCloundExceptionNotifyPhones == null ? null : Arrays.asList(pddCloundExceptionNotifyPhones);
        WechatNotifyUtils.wechatNotifyMd(pddCloundExceptionWebhook, phoneList, builder.toString());
        String exceptionMsgWrap = "拼多多官方代报调用异常：" + message;
        switch (declareReqVo.getClearanceMessageType()) {
            case "ZD_PERSONAL_GOODS_DECLARE":
            case "CEB621":
                messageService.createInvokeInventoryExceptionMsg(info.getMainOrderId(), info.getDeclareNos(), Lists.newArrayList("CHANNEL-" + info.getMainChannel()), exceptionMsgWrap);
                break;
            case "CEB311":
            case "ZD_IMPORT_ORDER":
                messageService.createInvokeOrderExceptionMsg(info.getMainOrderId(), info.getDeclareNos(), Lists.newArrayList("CHANNEL-" + info.getMainChannel()), exceptionMsgWrap);
                break;
            case "CEB625": // 退货申报
                refundOrderService.updateExceptionInfo(info.getDeclareNos(), exceptionMsgWrap);
        }
    }

    private void exceptionNotify(PddOfficialDeclareReqVo declareReqVo, PddOfficialDeclareResVo declareResVo) {

        String exceptionMsg = declareResVo.getMessage();
        String code = declareResVo.getCode();
        // 这里只要不是成功都发消息
        if (Objects.equal("200", code)) {
            return;
        }
        exceptionNotify(declareReqVo, exceptionMsg);
    }


    /**
     * 拼多多云内转装申报信息
     * 在申报配置上线后 从日志观察来说已废弃
     * 除非是拼多多的路径忘记配置代理申报
     *
     * @param model 数据
     */
    @Deprecated
    public void cloudAssembleInventoryDeclareInfo(WrapInventoryOrderInfo model) {
        String pddUrl = PDD_HOST + CLOUD_INVENTORY_DECLARE_URI;
        log.info("[op:PddDeclare-inventoryDeclare] PDD proxy, url={}, param={}", pddUrl, JSON.toJSONString(model));
        // 拼多多转发至云内应用申报
        HttpRequest httpRequest = HttpRequestUtil.post(pddUrl, JSON.toJSONString(model));
//        trackLogDTO.setContent(JSON.toJSONString(model)).setHasXmlMessage(1);
        String body = httpRequest.body();
        String message = StringUtils.isEmpty(body) ? "" : body.replace("\r\n", "");
        if (httpRequest.ok()) {
//            trackLogDTO.setLogDes("拼多多报文已推送");
            log.info("cloudAssembleDeclareInfo url={} PDD success, res={}", pddUrl, message);
        } else {
//            trackLogDTO.setLogDes("拼多多报文推送失败");
            log.info("cloudAssembleDeclareInfo url={} PDD fail,exception body={}", pddUrl, message);
        }
//        try {
//            trackLogService.submit(trackLogDTO);
//        } catch (Exception e) {
//            log.info("[op:PddDeclare-inventoryDeclare] trackLog error", e.getMessage());
//        }
    }

    /**
     * 拼多多云内申报订单
     *
     * @param info
     */
    private void cloudAssembleDeclareInfo(Object info, String uri) {
        String pddUrl = PDD_HOST + uri;
        log.info("cloudAssembleDeclareInfo PDD proxy, url={}, param={}", pddUrl, JSON.toJSONString(info));
        // 拼多多转发至云内应用申报
        HttpRequest httpRequest = HttpRequestUtil.post(pddUrl, JSON.toJSONString(info));
        String body = httpRequest.body();
        String message = StringUtils.isEmpty(body) ? "" : body.replace("\r\n", "");
        if (httpRequest.ok()) {
            log.info("cloudAssembleDeclareInfo url={} PDD success,  res={} , 原始信息：{}，", pddUrl, message, JSON.toJSONString(info));
        } else {
            log.info("cloudAssembleDeclareInfo url={} PDD fail,exception body={} , 原始信息：{}，", pddUrl, message, JSON.toJSONString(info));
        }
    }


    public void pddRepair(CustomsOrderDTO l) {
        String pddUrl = PDD_HOST + "/pdd/controller/declare/repairOrderData";
        WrapOrderDeclareInfo declareInfo = new WrapOrderDeclareInfo();
        declareInfo.setDeclareOrderNo(l.getDeclareOrderNo());
        declareInfo.setPayTransactionId(l.getDeclarePayNo());
        declareInfo.setConsignee(l.getConsigneeName());
        declareInfo.setConsigneeTel(l.getConsigneeTel());
        declareInfo.setConsigneeAddress(l.getConsigneeAddress());
        declareInfo.setPayerIdNumber(l.getBuyerIdNumber());
        declareInfo.setPayerName(l.getBuyerName());
        log.info("pddRepair info={}", JSON.toJSONString(l));
        // 拼多多转发至云内应用申报
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(pddUrl, JSON.toJSONString(declareInfo));
            if (httpRequest.ok()) {
                log.info("pddRepair url={} PDD success, res={}", pddUrl, httpRequest.body());
            } else {
                log.info("pddRepair url={} PDD fail res={}", pddUrl, httpRequest.body());
            }
        } catch (Exception e) {
            log.error("pddRepair error", e.getMessage(), e);
        }
    }

    public void pddRepairConsignee(CustomsOrderDTO l) {
        log.info("pddRepairConsignee customsOrderDTO={}", JSON.toJSONString(l));
        String pddUrl = PDD_HOST + "/pdd/controller/declare/repairOrderDataPayer";
        WrapOrderDeclareInfo declareInfo = new WrapOrderDeclareInfo();
        declareInfo.setDeclareOrderNo(l.getDeclareOrderNo());
        declareInfo.setPayTransactionId(l.getDeclarePayNo());
        declareInfo.setConsignee(l.getConsigneeName());
        declareInfo.setConsigneeTel(l.getConsigneeTel());
        declareInfo.setConsigneeAddress(l.getConsigneeAddress());
        declareInfo.setPayerIdNumber(l.getBuyerIdNumber());
        declareInfo.setPayerName(l.getBuyerName());
        log.info("pddRepairPayer info={}", JSON.toJSONString(l));
        // 拼多多转发至云内应用申报
        try {
            HttpRequest httpRequest = HttpRequestUtil.post(pddUrl, JSON.toJSONString(declareInfo));
            if (httpRequest.ok()) {
                log.info("pddRepairPayer url={} PDD success, res={}", pddUrl, httpRequest.body());
            } else {
                log.info("pddRepairPayer url={} PDD fail res={}", pddUrl, httpRequest.body());
            }
        } catch (Exception e) {
            log.error("pddRepairPayer error", e.getMessage(), e);
        }
    }
}
