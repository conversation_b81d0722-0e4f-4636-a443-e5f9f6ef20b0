package com.danding.cds.service.mq.producer;

import com.alibaba.fastjson.JSON;
import com.danding.business.rpc.client.ares.order.message.OrderRpcMessage;
import com.danding.logistics.business.common.dtos.order.DealOrderStatusDTO;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: Raymond
 * @Date: 2020/8/13 13:41
 * @Description:
 */
@Slf4j
@Component
public class OrderCallbackMQProducer {
    @Autowired
    private MessageSender messageSender;

    public void send(String sn){
        try {
            messageSender.sendMsg(sn,"ccs-order-logistics-callback-topic");
        } catch (Exception e) {
            log.error("[op:OrderCallbackMQProducer] sendMeg exception, cause={}", e.getMessage(), e);
            throw e;
        }
    }

    public void sendTax(String biz){
        try {
            messageSender.sendMsg(biz,"ccs-tax-logistics-callback-topic");
        } catch (Exception e) {
            log.error("[op:OrderCallbackMQProducer] sendMeg exception, cause={}", e.getMessage(), e);
            throw e;
        }
    }

    public void sendDeliver(DealOrderStatusDTO dealOrderStatusDTO){
        try {
            messageSender.sendMsg(dealOrderStatusDTO,"logistics_order_status:GONGXIAO");
        } catch (Exception e) {
            log.error("[op:OrderCallbackMQProducer] sendMeg exception, cause={}", e.getMessage(), e);
            throw e;
        }
    }

    public void sendDeliver(OrderRpcMessage dealOrderStatusDTO){
        try {
            messageSender.sendMsg(JSON.toJSONString(dealOrderStatusDTO),"ccs-order-deliver-back-topic:DEFAULT");
        } catch (Exception e) {
            log.error("[op:OrderCallbackMQProducer] sendMeg exception, cause={}", e.getMessage(), e);
            throw e;
        }
    }
}
