package com.danding.cds.service.declareImpl.inventory;

import com.danding.cds.c.api.rpc.TrackLogEsRpc;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.declare.base.component.inventory.CompleteCebMessageDeclareAbstract;
import com.danding.cds.declare.base.component.service.ZjPortDeclareService;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;
import java.util.Objects;

import static com.danding.cds.declare.sdk.clear.zhejiang.ZJCustomsClient.interfaceUrl;


@Slf4j
public class CompleteCebMessageZjPortDeclare extends CompleteCebMessageDeclareAbstract {
    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;
    @Autowired
    private ZjPortDeclareService zjPortDeclareService;
    @DubboReference
    private TrackLogEsRpc trackLogEsRpc;

    @Override
    protected InventoryDeclareResult doDeclare(String cebMessage, String orderNo, String cebCode, String dxpId) {
        log.info("CompleteCebMessageZjPortDeclare doDeclare cebMessage={} cebCode={} dxpId={}", cebMessage, cebCode, dxpId);
        ExternalDeclareOrderDo declareOrderDo = externalDeclareOrderBaseService.getByOrderNo(orderNo);
        TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
        trackLogEsDO.setOrderId(-1L);
        if (Objects.nonNull(declareOrderDo)) {
            trackLogEsDO.setOrderSn(declareOrderDo.getOrderNo());
        }
        trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setRequestMessage(cebMessage);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setSender(TrackLogConstantMixAll.DT_CCS);
        trackLogEsDO.setReceiver(TrackLogConstantMixAll.ZJ_PORT);
        trackLogEsDO.setEventTime(new Date());
        trackLogEsDO.setOperator(UserUtils.getUserRealName());
        trackLogEsDO.setInternalStatus(OrderInternalEnum.DECLARING.getCode());

        String declareResult = zjPortDeclareService.sendZjPortCebMsgAndLog(cebMessage, interfaceUrl);
        log.info("CompleteCebMessageZjPortDeclare declareResult={}", declareResult);

        this.buildTrackLogEsDO(trackLogEsDO);
        try {
            trackLogEsRpc.submit(trackLogEsDO);
        } catch (Exception e) {
            log.error("记录申报轨迹日志失败 error={}", e.getMessage(), e);
        }
        return null;
    }

    @Override
    protected InventoryDeclareResult doMockDeclare(String cebMessage, String orderNo, String cebCode, String dxpId) {
        log.info("CompleteCebMessageZjPortDeclare doMockDeclare cebMessage={} cebCode={} dxpId={}", cebMessage, cebCode, dxpId);
        return null;
    }

    @Override
    protected void buildTrackLogEsDO(TrackLogEsDTO trackLogEsDO) {
        trackLogEsDO.setEventDesc("浙电申报");
    }
}
