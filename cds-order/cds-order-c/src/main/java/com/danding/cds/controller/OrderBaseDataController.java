package com.danding.cds.controller;

import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.order.base.data.service.BaseDataInitService;
import com.danding.cds.order.base.data.util.BaseDataCacheUtil;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@Slf4j
@RequestMapping("/BaseData")
@RestController
public class OrderBaseDataController {
    @Autowired
    private BaseDataInitService baseDataInitService;

    @Resource
    private OrderService orderService;

//    @RequestMapping("/deleteTest")
//    public void test(String id) {
//        orderService.deletedById(Long.valueOf(id));
//    }

    /**
     * 获取全部缓存数据
     *
     * @return
     */
    @RequestMapping("/getAllBaseData")
    public List<String> getAllBaseData() {
        return baseDataInitService.getData();
    }

    /**
     * 刷新全部缓存数据 先删除后初始化
     *
     * @return
     */
    @RequestMapping("/refreshBaseDataProxy")
    public void refreshBaseDataProxy(String dataType) {
        log.info("OrderBaseDataController refreshBaseDataProxy dataType={}", dataType);
        baseDataInitService.refreshBaseDataProxy(dataType);
    }

    @RequestMapping("/getExceptionIdCache")
    public String getExceptionIdCache() {
        return BaseDataCacheUtil.getException();
    }

    @RequestMapping("/delExceptionIdCache")
    public String delExceptionIdCache() {
        BaseDataCacheUtil.removeException();
        return "Success";
    }

    @RequestMapping("/delStatusMappingIdCache")
    public String delStatusMappingIdCache() {
        BaseDataCacheUtil.removeStatusMapping();
        return "Success";
    }

    @RequestMapping("/getStatusMappingIdCache")
    public String getStatusMappingIdCache() {
        return BaseDataCacheUtil.getStatusMapping();
    }


    @RequestMapping("/getCompanyIdCache")
    public String getCompanyIdCache() {
        return baseDataInitService.getCompanyIdCache();
    }

    @RequestMapping("/getCompanyCodeCache")
    public String getCompanyCodeCache() {
        return baseDataInitService.getCompanyCodeCache();
    }

    @RequestMapping("/getPayChannel")
    public String getPayChannel() {
        return baseDataInitService.getPayChannel();
    }

    @RequestMapping("/getCustomsBookCache")
    public String getCustomsBookCache() {
        return baseDataInitService.getCustomsBookCache();
    }

    @RequestMapping("/getExpressCache")
    public String getExpressCache() {
        return baseDataInitService.getExpressCache();
    }

    @RequestMapping("/getRouteIdCache")
    public String getRouteIdCache() {
        return baseDataInitService.getRouteIdCache();
    }

    @RequestMapping("/getRouteCodeCache")
    public String getRouteCodeCache() {
        return baseDataInitService.getRouteCodeCache();
    }

    @RequestMapping("/getPathCache")
    public String getPathCache() {
        return baseDataInitService.getPathCache();
    }

    @RequestMapping("/getPayMerchantAccountCache")
    public String getPayMerchantAccountCache() {
        return baseDataInitService.getPayMerchantAccountCache();
    }

    @RequestMapping("/getPayMerchantAccountChannelCache")
    public String getPayMerchantAccountChannelCache() {
        return baseDataInitService.getPayMerchantAccountChannelCache();
    }


    @RequestMapping("/refreshCompany")
    public void refreshCompany() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.COMPANY);
    }

    @RequestMapping("/updateCompany")
    public void updateCompany(@RequestBody CompanyDTO companyDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.COMPANY, companyDTO);
    }

    @RequestMapping("/delCompany")
    public String delCompany(@RequestBody CompanyDTO companyDTO) {
        if (Objects.isNull(companyDTO)) {
            return "companyDTO为空";
        }
        if (Objects.isNull(companyDTO.getId())) {
            return "companyDTO id为空";
        }
        if (Objects.isNull(companyDTO.getCode())) {
            return "companyDTO code为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.COMPANY, companyDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshPayChannel")
    public void refreshPayChannel() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.PAY_CHANNEL);
    }

    @RequestMapping("/updatePayChannel")
    public void updatePayChannel(@RequestBody PayChannelDTO payChannelDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.PAY_CHANNEL, payChannelDTO);
    }

    @RequestMapping("/delPayChannel")
    public String delPayChannel(@RequestBody PayChannelDTO payChannelDTO) {
        if (Objects.isNull(payChannelDTO)) {
            return "payChannelDTO为空";
        }
        if (Objects.isNull(payChannelDTO.getCode())) {
            return "payChannelDTO code为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.PAY_CHANNEL, payChannelDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshCustomsBook")
    public void refreshCustomsBook() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.CUSTOMS_BOOK);
    }

    @RequestMapping("/updateCustomsBook")
    public void updateCustomsBook(@RequestBody CustomsBookDTO customsBookDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.CUSTOMS_BOOK, customsBookDTO);
    }

    @RequestMapping("/delCustomsBook")
    public String delCustomsBook(@RequestBody CustomsBookDTO customsBookDTO) {
        if (Objects.isNull(customsBookDTO)) {
            return "customsBookDTO为空";
        }
        if (Objects.isNull(customsBookDTO.getId())) {
            return "customsBookDTO id为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.CUSTOMS_BOOK, customsBookDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshExpress")
    public void refreshExpress() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.EXPRESS);
    }

    @RequestMapping("/updateExpress")
    public void updateExpress(@RequestBody ExpressDTO expressDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.EXPRESS, expressDTO);
    }

    @RequestMapping("/delExpress")
    public String delExpress(@RequestBody ExpressDTO expressDTO) {
        if (Objects.isNull(expressDTO)) {
            return "expressDTO为空";
        }
        if (Objects.isNull(expressDTO.getCode())) {
            return "expressDTO code为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.EXPRESS, expressDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshRoute")
    public void refreshRoute() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.ROUTE);
    }

    @RequestMapping("/updateRoute")
    public void updateRoute(@RequestBody RouteDTO routeDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.ROUTE, routeDTO);
    }

    @RequestMapping("/delRoute")
    public String delRoute(@RequestBody RouteDTO routeDTO) {
        if (Objects.isNull(routeDTO)) {
            return "routeDTO为空";
        }
        if (Objects.isNull(routeDTO.getId())) {
            return "routeDTO id为空";
        }
        if (Objects.isNull(routeDTO.getCode())) {
            return "routeDTO code为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.ROUTE, routeDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshPath")
    public void refreshPath() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.PATH);
    }

    @RequestMapping("/updatePath")
    public void updatePath(@RequestBody PathDTO pathDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.PATH, pathDTO);
    }

    @RequestMapping("/delPath")
    public String delPath(@RequestBody PathDTO pathDTO) {
        if (Objects.isNull(pathDTO)) {
            return "pathDTO为空";
        }
        if (Objects.isNull(pathDTO.getId())) {
            return "pathDTO id为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.PATH, pathDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshPayMerchantAccount")
    public void refreshPayMerchantAccount() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT);
    }

    @RequestMapping("/updatePayMerchantAccount")
    public void updatePayMerchantAccount(@RequestBody PayMerchantAccountDTO payMerchantAccountDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT, payMerchantAccountDTO);
    }

    @RequestMapping("/delPayMerchantAccount")
    public String delPayMerchantAccount(@RequestBody PayMerchantAccountDTO payMerchantAccountDTO) {
        if (Objects.isNull(payMerchantAccountDTO)) {
            return "payMerchantAccountDTO为空";
        }
        if (Objects.isNull(payMerchantAccountDTO.getSn())) {
            return "payMerchantAccountDTO sn为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT, payMerchantAccountDTO);
        return "删除成功";
    }

    @RequestMapping("/refreshPayMerchantAccountChannel")
    public void refreshPayMerchantAccountChannel() {
        baseDataInitService.refreshBaseDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL);
    }

    @RequestMapping("/updatePayMerchantAccountChannel")
    public void updatePayMerchantAccountChannel(@RequestBody PayMerchantAccountChannelDTO payMerchantAccountChannelDTO) {
        baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL, payMerchantAccountChannelDTO);
    }

    @RequestMapping("/delPayMerchantAccountChannel")
    public String delPayMerchantAccountChannel(@RequestBody PayMerchantAccountChannelDTO payMerchantAccountChannelDTO) {
        if (Objects.isNull(payMerchantAccountChannelDTO)) {
            return "payMerchantAccountChannelDTO为空";
        }
        if (Objects.isNull(payMerchantAccountChannelDTO.getMerchantId())) {
            return "payMerchantAccountChannelDTO merchantId为空";
        }
        if (Objects.isNull(payMerchantAccountChannelDTO.getChannel())) {
            return "payMerchantAccountChannelDTO channel为空";
        }
        baseDataInitService.deleteDataProxy(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL, payMerchantAccountChannelDTO);
        return "删除成功";
    }
}
