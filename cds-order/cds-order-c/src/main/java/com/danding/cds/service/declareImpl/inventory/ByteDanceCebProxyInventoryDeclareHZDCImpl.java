package com.danding.cds.service.declareImpl.inventory;

import java.util.Objects;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.base.component.inventory.impl.HzCenterInventoryDeclareImpl;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.cds.service.mq.producer.ByteDanceDeclareProducer;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 字节CEB清单代理申报
 * 判断是否走云内申报 不走的话 兜底方式为杭州数据分中心
 **/
@Service("BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE_HZDC")
@Slf4j
public class ByteDanceCebProxyInventoryDeclareHZDCImpl extends InventoryDeclareAbstract {
    /**
     * 测试用
     */
    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;
    @Autowired
    private ByteDanceDeclareProducer byteDanceDeclareProducer;
    @Resource
    private OrderService orderService;
    @Autowired
    private HzCenterInventoryDeclareImpl hzCenterInventoryDeclare;

    @Override
    protected InventoryDeclareResult mockDeclareTest(WrapInventoryOrderInfo info) {
        log.info("字节CEB清单代理申报，测试环境MOCK");
        CompanyInfo ebpCompany = info.getEbpCompanyDTO();
        if (Objects.isNull(ebpCompany)) {
            throw new ArgsInvalidException("电商平台信息为空");
        }
        String mainOrderSn = info.getMainOrderSn();
        OrderDTO orderDTO = orderService.findBySnSection(mainOrderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("申报单信息不存在");
        }
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
            log.info("ByteDanceCebProxyInventoryDeclareHZDCImpl 测试环境MOCK 清单 申报单为加密订单走云内申报 单号:{}", orderDTO.getDeclareOrderNo());
            byteDanceDeclareProducer.inventoryOrderSend(info);
        } else {
            log.info("ByteDanceCebProxyInventoryDeclareHZDCImpl 测试环境MOCK 清单 申报单不走云内申报 走杭州数据分中心 单号:{}", orderDTO.getDeclareOrderNo());
            hzCenterInventoryDeclare.invoke(info);
        }
        return null;
    }

    /**
     * 1.先区分是否云内申报
     * 2.如果是云内直接调用云内
     * 3.如果不是的话 要区分
     *
     * @param info 数据
     * @return
     */
    @Override
    protected InventoryDeclareResult declare(WrapInventoryOrderInfo info) {
        log.info("字节CEB清单代理申报杭数 info={}", JSON.toJSONString(info));
        CompanyInfo ebpCompany = info.getEbpCompanyDTO();
        if (Objects.isNull(ebpCompany)) {
            throw new ArgsInvalidException("电商平台信息为空");
        }
        String mainOrderSn = info.getMainOrderSn();
        OrderDTO orderDTO = orderService.findBySnSection(mainOrderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("申报单信息不存在");
        }
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
            log.info("ByteDanceCebProxyInventoryDeclareHZDCImpl 清单 申报单为加密订单走云内申报 单号:{}", orderDTO.getDeclareOrderNo());
            byteDanceDeclareProducer.inventoryOrderSend(info);
        } else {
            log.info("ByteDanceCebProxyInventoryDeclareHZDCImpl 清单 申报单不走云内申报 走杭州数据分中心 单号:{}", orderDTO.getDeclareOrderNo());
            hzCenterInventoryDeclare.invoke(info);
        }
        return null;
    }
}
