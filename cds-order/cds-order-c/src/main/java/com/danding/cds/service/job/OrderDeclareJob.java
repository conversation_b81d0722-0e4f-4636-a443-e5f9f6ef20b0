package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
@RestController
@RefreshScope
public class OrderDeclareJob extends IJobHandler {

    @Resource
    private OrderService orderService;

    @Autowired
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Value("${repush.excludeEbpIdList:}")
    private Long[] excludeEbpIdList;

    @Override
    @XxlJob(value = "OrderCDeclareJob", enableTenant = true)
    public ReturnT<String> execute(String ids) throws Exception {
        List<OrderDTO> orderDTOList = new ArrayList<>();
        if (!StringUtils.isEmpty(ids) && ids.contains(",")) {
            // Module::指定订单检查模式
            List<String> snList = Lists.newArrayList(ids.split(","));
            for (String sn : snList) {
                OrderDTO orderDTO = orderService.findBySnSection(sn);
                if (orderDTO != null) {
                    List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags());
                    if (orderTag.contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
                        XxlJobLogger.log("[op:OrderDeclareJob] sn：{} 的订单已挂起", sn);
                        continue;
                    }
                    orderDTOList.add(orderDTO);
                } else {
                    XxlJobLogger.log("[op:OrderDeclareJob] sn：{} 的订单不存在", sn);
                }
            }
        } else if (!StringUtils.isEmpty(ids) && "alert".equals(ids)) {
            // Module::批量自动检索检查模式
            // 申报中
            // 前5-10分钟有更新
            // 非异常状态
            // 最多检查1000单，超出上限报警
            OrderSearch search = new OrderSearch();
            search.setStatus(OrderStatus.DEC_ING.getValue());
            search.setExceptionFlag(-1);
            List<Integer> integers = new ArrayList<>();
            integers.add(0);
            search.setHangUpStatus(integers);
            Long updateFrom = DateTime.now().minusDays(14).getMillis();
            Long updateTo = DateTime.now().getMillis();
            Integer maxDeal = 1000;
            search.setCurrentPage(1);
            search.setPageSize(maxDeal);
            search.setUpdateFrom(updateFrom);
            search.setUpdateTo(updateTo);
            ListVO lib = orderService.pagingES(search);
            orderDTOList = lib.getDataList();
            XxlJobLogger.log("[op:OrderDeclareJob] 批量检索报告，更新时间段{}-{},数据总数={},本次检查数量={}",
                    new DateTime(updateFrom).toString("yyyy/MM/dd HH:mm:ss"),
                    new DateTime(updateTo).toString("yyyy/MM/dd HH:mm:ss"), lib.getPage().getTotalCount(),
                    maxDeal < lib.getPage().getTotalCount() ? maxDeal : lib.getPage().getTotalCount()
            );
            if (maxDeal < lib.getPage().getTotalCount()) {
                XxlJobLogger.log("[op:OrderDeclareJob] 待处理数量超出最大可处理数量");
                return FAIL;
            }
        } else {
            OrderSearch search = new OrderSearch();
            search.setStatus(OrderStatus.DEC_ING.getValue());
            search.setExceptionFlag(-1);
            List<Integer> integers = new ArrayList<>();
            integers.add(0);
            search.setHangUpStatus(integers);
            Integer maxDeal = 1000;
            search.setCurrentPage(1);
            search.setPageSize(maxDeal);
            ListVO lib = orderService.pagingES(search);
            orderDTOList = lib.getDataList();
        }
        List<Long> excludeList = Arrays.asList(excludeEbpIdList);
        for (OrderDTO orderDTO : orderDTOList) {
            if (!CollectionUtils.isEmpty(excludeList) && excludeList.contains(orderDTO.getEbpId())) {
                continue;
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
//            orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
            orderDeclareMQProducer.send(orderDTO);
            XxlJobLogger.log("[op:OrderDeclareJob] 申报发起完成, declareSn={} ", orderDTO.getDeclareOrderNo());
        }
        XxlJobLogger.log("[op:OrderDeclareJob] 申报检查发起完成");
        return SUCCESS;
    }
}
