package com.danding.cds.service.declareImpl.order;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.common.constants.DxpCons;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.service.customs.declare.EwtpDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description 芥州订单申报
 * @date 2022/1/21
 */
@Slf4j
@Service("YWNO_EWTP_ORDER_DECLARE")
public class YwnoEwtpOrderDeclareImpl extends OrderDeclareAbstract {

    @Autowired
    private EwtpDeclareService ewtpDeclareService;

    @Override
    protected OrderDeclareResult mockDeclareTest(WrapOrderDeclareInfo info) {

        CebMessageUtil.getCebXmlMsg(info, DxpCons.YWNO_EWTP_DXP);
        log.info("【测试】义乌南欧EWTP申报单: {} ，发往麦德龙BXP报文-MOCK", info.getDeclareOrderNo());
        return ewtpDeclareService.ewtpOrderDeclareTest(info, DxpCons.YWNO_EWTP_DXP);
    }

    @Override
    protected OrderDeclareResult declare(WrapOrderDeclareInfo declareInfo) {

        return ewtpDeclareService.ewtpOrderDeclare(declareInfo, DxpCons.YWNO_EWTP_DXP);
    }
}
