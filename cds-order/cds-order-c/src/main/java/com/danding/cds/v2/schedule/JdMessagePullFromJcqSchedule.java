package com.danding.cds.v2.schedule;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.JdCustomsOrderMsgDTO;
import com.danding.cds.jcq.http.bean.MessageResult;
import com.danding.cds.jcq.http.bean.MessageResultV2;
import com.danding.cds.jcq.http.response.HttpProxyGetMessagesV2ResultResponse;
import com.danding.cds.jcq.http.util.JCQMsgUtil;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.google.common.base.Splitter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

@Component
@Slf4j
public class JdMessagePullFromJcqSchedule {

    @DubboReference
    private JdServProviderService jdServProviderService;

    @Autowired
    private JCQMsgUtil jcqMsgUtil;

    @Autowired
    private MessageSender messageSender;

    /**
     * 京东退货取消单下发从mercury获取，这部分弃用
     *
     * @param param
     * @return
     */
    @Deprecated
    @XxlJob(value = "JdRefundMessagePullJob", enableTenant = false)
    public ReturnT<String> pullJdRefundMessage(String param) {
        XxlJobLogger.log("入参:" + param);
        log.info("pullJdRefundMessage param={}", param);
        JSONObject paramObject = JSON.parseObject(param);
        String providerIds = paramObject.getString("providerId");
        if (Objects.isNull(providerIds)) {
            return ReturnT.SUCCESS;
        }
        List<String> providerIdList = Splitter.on(",").splitToList(providerIds);
        for (String providerId : providerIdList) {
            JdServProviderDTO jdServProviderDTO = jdServProviderService.findByProviderId(providerId);
            String jcqConfig = jdServProviderDTO.getJcqConfig();
            HttpProxyGetMessagesV2ResultResponse response = jcqMsgUtil.pullMessageAutoAck(
                    jdServProviderDTO.getCloudAccessKey(), jdServProviderDTO.getCloudAccessSecret(), jcqConfig, "customs_order_type_send");
            log.info("pullJdRefundMessage providerId={} response={}", providerId, JSON.toJSONString(response));
            MessageResultV2 result = response.getResult();
            log.info("pullJdRefundMessage providerId={} result={}", providerId, JSON.toJSONString(result));
            if (Objects.isNull(result)) {
                return ReturnT.SUCCESS;
            }
            List<MessageResult.Message> messages = result.getMessages();
            log.info("pullJdRefundMessage providerId={} message={}", providerId, JSON.toJSONString(messages));
            if (CollectionUtils.isEmpty(messages)) {
                return ReturnT.SUCCESS;
            }
            for (MessageResult.Message message : messages) {
                JdCustomsOrderMsgDTO jdCustomsOrderMsgDTO = JSON.parseObject(message.getMessageBody(), JdCustomsOrderMsgDTO.class);
                log.info("pullJdRefundMessage providerId={} type={} jdCustomsOrderMsgDTO={}", providerId, jdCustomsOrderMsgDTO.getType(), JSON.toJSONString(jdCustomsOrderMsgDTO));
                if (!Objects.equals(jdCustomsOrderMsgDTO.getType(), "returnOrderDeclaration")) {
                    log.info("pullJdRefundMessage providerId={} jdCustomsOrderMsgDTO.type={} 略过不处理", providerId, jdCustomsOrderMsgDTO.getType());
                    continue;
                }
                messageSender.sendMsg(jdCustomsOrderMsgDTO.getData(), "ccs-jd-refund-cancel-topic");
            }
        }
        return ReturnT.SUCCESS;
    }
}
