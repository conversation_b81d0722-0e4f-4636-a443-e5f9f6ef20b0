package com.danding.cds.service.customs;

import com.danding.cds.c.api.service.JieztechSignOrderService;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Date;

/**
 * @program: cds-center
 * @description:
 * @author: 潘本乐（Belep）
 * @create: 2023-10-09 11:46
 **/
@DubboService
public class JieztechSignOrderServiceImpl implements JieztechSignOrderService {

    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @Override
    public void saveOrUpdate(String orderNo, String logisticsNo, String copNo, String cancelCopNo, String refundCopNo) {
        ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByOrderNo(orderNo);
        if (singleSignOrderDO == null) {
            ExternalDeclareOrderDo save = new ExternalDeclareOrderDo();
            save.setOrderNo(orderNo);
            save.setLogisticsNo(logisticsNo);
            save.setCopNo(copNo);
            externalDeclareOrderBaseService.insertSelective(save);
        } else {
            ExternalDeclareOrderDo update = new ExternalDeclareOrderDo();
            BeanUtils.copyProperties(singleSignOrderDO, update);
            if (logisticsNo != null) {
                update.setLogisticsNo(logisticsNo);
            }
            if (copNo != null) {
                update.setCopNo(copNo);
            }
            if (cancelCopNo != null) {
                update.setCancelCopNo(cancelCopNo);
            }
            if (refundCopNo != null) {
                update.setRefundCopNo(refundCopNo);
            }
            update.setUpdateTime(new Date());
            externalDeclareOrderBaseService.updateByPrimaryKeySelective(update);
        }
    }
}
