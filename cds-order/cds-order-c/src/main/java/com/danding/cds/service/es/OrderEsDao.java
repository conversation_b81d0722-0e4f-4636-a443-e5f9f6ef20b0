package com.danding.cds.service.es;

import cn.hutool.core.lang.func.VoidFunc1;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.DeclareOrderWrapDTO;
import com.danding.cds.bean.dto.OrderExtraDto;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.es.search.OrderEsBuilder;
import com.danding.cds.order.api.dto.JdMonitorParamDto;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.common.es.annotations.EsDao;
import com.danding.common.es.dao.AbstractEsDao;
import com.danding.core.tenant.SimpleTenantHelper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.joda.time.DateTime;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/13 15:14
 * @Description:
 */
@EsDao
@Slf4j
@Repository
public class OrderEsDao extends AbstractEsDao<OrderEsDO> {

    /**
     * 批量更新或新增
     * Bulk update all objects. Will do update
     *
     * @param orderEsDOList 索引数据
     */
    public void bulkIndex(List<OrderEsDO> orderEsDOList) {

        if (CollectionUtils.isEmpty(orderEsDOList)) {
            return;
        }
        List<IndexQuery> indexQueryList = orderEsDOList.stream()
                .map(z -> new IndexQueryBuilder().withObject(z).build())
                .collect(Collectors.toList());
        elasticsearchTemplate.bulkIndex(indexQueryList);
    }

    /**
     * 滚动查询
     *
     * @param scrollTimeInMillis 时间
     * @param query              查询语句
     * @return
     */
    public ScrolledPage<OrderEsDO> startScroll(long scrollTimeInMillis, SearchQuery query) {
        return elasticsearchTemplate.startScroll(scrollTimeInMillis, query, OrderEsDO.class);
    }

    /**
     * 继续滚动获取
     *
     * @param scrollId           滚动ID
     * @param scrollTimeInMillis 时间
     * @return
     */
    public ScrolledPage<OrderEsDO> continueScroll(String scrollId, long scrollTimeInMillis) {
        return elasticsearchTemplate.continueScroll(scrollId, scrollTimeInMillis, OrderEsDO.class);
    }

    /**
     * es scroll获取数据，并处理
     *
     * @param scrollTimeInMillis 滚动开始
     * @param pageSize           页尺寸
     * @param dealFunc           功能函数，真正处理逻辑的
     * @return
     */
    public void scrollApiDealAll(long scrollTimeInMillis, Integer pageSize, VoidFunc1<List<OrderEsDO>> dealFunc) throws Exception {

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withPageable(PageRequest.of(0, pageSize))
                .build();
        ScrolledPage<OrderEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, nativeSearchQuery, OrderEsDO.class);

        while (scrolledPage.hasContent()) {
            final List<OrderEsDO> content = scrolledPage.getContent();
            // 执行处理
            dealFunc.call(content);
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, OrderEsDO.class);
        }
    }

    public void updateTaxPrice(OrderEsDO orderEsDO) throws IOException {
        UpdateQuery updateQuery = new UpdateQuery();
        updateQuery.setClazz(OrderEsDO.class);
        updateQuery.setId(orderEsDO.getId());
        UpdateRequest updateRequest = new UpdateRequest();
        updateRequest.doc(XContentFactory.jsonBuilder().startObject()
                .field("taxPrice", orderEsDO.getTaxPrice()).endObject());
        updateQuery.setUpdateRequest(updateRequest);
        this.update(updateQuery);
    }

    public void update(UpdateQuery updateQuery) {
        log.info("OrderEsDao update={}", JSON.toJSONString(updateQuery));
        this.elasticsearchTemplate.update(updateQuery);
    }

    /**
     * 批量保持到ES
     *
     * @param orderWrapDTOList
     */
    public void esSaveBatch(List<DeclareOrderWrapDTO> orderWrapDTOList) {
        if (CollectionUtils.isEmpty(orderWrapDTOList)) {
            return;
        }
        List<OrderEsDO> orderEsDOList = orderWrapDTOList.stream().map(z -> {
            return getOrderEsDO(z.getOrderDTO(), z.getCustomsInventoryDTO(), z.getCustomsOrderDTO());
        }).collect(Collectors.toList());
        bulkIndex(orderEsDOList);
    }

    /**
     * ES保存
     *
     * @param orderDO             申报单总览
     * @param customsInventoryDTO 清单实体
     * @param customsOrderDTO     订单实体
     */
    public void esSave(OrderDTO orderDO, CustomsInventoryDTO customsInventoryDTO, CustomsOrderDTO customsOrderDTO) {
        this.esSave(orderDO, customsInventoryDTO, customsOrderDTO, null);
    }

    /**
     * ES保存
     *
     * @param orderDO             申报单总览
     * @param customsInventoryDTO 清单实体
     * @param customsOrderDTO     订单实体
     * @param accountBookId       账册id
     */
    public void esSave(OrderDTO orderDO, CustomsInventoryDTO customsInventoryDTO, CustomsOrderDTO customsOrderDTO, Long accountBookId) {

        OrderEsDO orderEsDO = getOrderEsDO(orderDO, customsInventoryDTO, customsOrderDTO, accountBookId);
        log.debug("esSave orderEsDO:{}", JSON.toJSONString(orderEsDO));
        this.save(orderEsDO);
    }

    private OrderEsDO getOrderEsDO(OrderDTO orderDO, CustomsInventoryDTO customsInventoryDTO, CustomsOrderDTO customsOrderDTO) {
        return getOrderEsDO(orderDO, customsInventoryDTO, customsOrderDTO, null);
    }

    private OrderEsDO getOrderEsDO(OrderDTO orderDO, CustomsInventoryDTO customsInventoryDTO, CustomsOrderDTO customsOrderDTO, Long accountBookId) {

        OrderEsDO orderEsDO = new OrderEsDO();
        orderEsDO.setId(orderDO.getId().toString());
        orderEsDO.setCreateTime(new DateTime(orderDO.getCreateTime()).getMillis());
        orderEsDO.setUpdateTime(new DateTime(orderDO.getUpdateTime()).getMillis());
        orderEsDO.setStatus(orderDO.getStatus());
        orderEsDO.setAccountBookId(accountBookId);
        orderEsDO.setInternalStatus(orderDO.getInternalStatus());
        orderEsDO.setSn(orderDO.getSn());
        orderEsDO.setCustomsPaymentSn(orderDO.getCustomsPaymentSn());
        orderEsDO.setCustomsOrderSn(orderDO.getCustomsOrderSn());
        orderEsDO.setCustomsLogisticsSn(orderDO.getCustomsLogisticsSn());
        orderEsDO.setCustomsInventorySn(orderDO.getCustomsInventorySn());
        // 订单下单时间
        OrderExtraDto orderExtra = JSON.parseObject(orderDO.getExtraJson(), OrderExtraDto.class);
        if (Objects.nonNull(orderExtra)) {
            OrderSubmitDto submit = orderExtra.getSubmit();
            if (Objects.nonNull(submit.getOrderTime())) {
                orderEsDO.setOrderTime(submit.getOrderTime());
            }
            if (Objects.nonNull(submit.getErpPhyWarehouseSn())) {
                orderEsDO.setErpPhyWarehouseSn(submit.getErpPhyWarehouseSn());
            }
            if (Objects.nonNull(submit.getErpPhyWarehouseName())) {
                orderEsDO.setErpPhyWarehouseName(submit.getErpPhyWarehouseName());
            }
        }
        if (customsInventoryDTO != null) {
            orderEsDO.setAccountBookId(customsInventoryDTO.getAccountBookId());
            orderEsDO.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
            orderEsDO.setInventoryNo(customsInventoryDTO.getInventoryNo());
            orderEsDO.setEbcId(customsInventoryDTO.getEbcId());
            orderEsDO.setAssureCompanyId(customsInventoryDTO.getAssureCompanyId());
            orderEsDO.setLogisticsCompanyId(customsInventoryDTO.getLogisticsCompanyId());
            orderEsDO.setExpressId(customsInventoryDTO.getExpressId());
            if (customsInventoryDTO.getCustomsPassTime() != null) {
                orderEsDO.setCustomsPassTime(new DateTime(customsInventoryDTO.getCustomsPassTime()).getMillis());
            }
            CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
            orderEsDO.setRouteCode(extra.getRouteCode());
            orderEsDO.setTenantOuterId(extra.getTenantOuterId());
            if (Objects.nonNull(customsInventoryDTO.getTotalTax())) {
                orderEsDO.setTaxPrice(customsInventoryDTO.getTotalTax().doubleValue());
            }
            orderEsDO.setInventoryOrderStatus(customsInventoryDTO.getStatus());
        }
        if (customsOrderDTO != null) {
            orderEsDO.setConsigneeName(customsOrderDTO.getConsigneeName());
            orderEsDO.setConsigneeTel(customsOrderDTO.getConsigneeTel());
            orderEsDO.setBuyerIdNumber(customsOrderDTO.getBuyerIdNumber());
            orderEsDO.setBuyerName(customsOrderDTO.getBuyerName());
        }
        orderEsDO.setOutOrderNo(orderDO.getOutOrderNo());
        orderEsDO.setDeclareOrderNo(orderDO.getDeclareOrderNo());
        orderEsDO.setEbpId(orderDO.getEbpId());
        orderEsDO.setExceptionFlag(orderDO.getExceptionFlag());
        orderEsDO.setExceptionType(orderDO.getExceptionType());
        orderEsDO.setExceptionDetail(orderDO.getExceptionDetail());
        if (orderDO.getUpdateTime() != null) {
            orderEsDO.setUpdateTime(orderDO.getUpdateTime().getTime());
        }
        orderEsDO.setActionJson(orderDO.getActionJson());
        orderEsDO.setTenantryId(orderDO.getTenantryId());
        orderEsDO.setHangUpStatus(orderDO.getHangUpStatus());
        orderEsDO.setOrderTags(orderDO.getOrderTags());
//        orderEsDO.setDeleted(orderDO.getDeleted() ? 1 : 0);
        return orderEsDO;
    }

    public Page<OrderEsDO> paging(OrderSearch search) {
        BoolQueryBuilder boolQueryBuilder = OrderEsBuilder.getBoolQueryBuilder(search);
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(search.getCurrentPage() - 1, search.getPageSize())).build();
        Page<OrderEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, OrderEsDO.class);
        return page;
    }

    /**
     * 获取申报超时查询Builder
     *
     * @param declareTimeOut 申报超时
     * @return
     */
    private BoolQueryBuilder getDeclareTimeOutQueryBuilder(String declareTimeOut) {
        if (StringUtils.isEmpty(declareTimeOut)) {
            return null;
        }
        Integer timeOutGe = this.getMillisByHour(declareTimeOut);
        Map<String, Object> params = new HashMap() {{
            put("now", System.currentTimeMillis());
            put("geTime", timeOutGe);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "if(doc['inventoryOrderStatus'].value == 100){return params.geTime <= doc['customsPassTime'].value - doc['createTime'].value;} return params.geTime <= params.now - doc['createTime'].value";
        Script declareTimeScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("customsPassTime"))
                .must(QueryBuilders.existsQuery("inventoryOrderStatus"))
                .must(QueryBuilders.scriptQuery(declareTimeScript));
    }

    /**
     * 获取履约超时查询Builder
     *
     * @param declareTimeOut 申报超时
     * @return
     */
    private BoolQueryBuilder getPerformanceTimeOutQueryBuilder(String declareTimeOut) {
        if (StringUtils.isEmpty(declareTimeOut)) {
            return null;
        }
        Integer timeOutGe = this.getMillisByHour(declareTimeOut);
        Map<String, Object> params = new HashMap() {{
            put("now", System.currentTimeMillis());
            put("geTime", timeOutGe);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "if (doc['inventoryOrderStatus'].value == 100) {" +
                "    if (doc['customsPassTime'].size() != 0) {" +
                "        return params.geTime <= doc['customsPassTime'].value - doc['orderTime'].value;" +
                "    } else {" +
                "        return false;" + // 当 customPassTime 字段不存在时的处理
                "    }" +
                "} else {" +
                "    return params.geTime <= params.now - doc['orderTime'].value;" +
                "}";
        Script performanceTimeScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("orderTime")).must(QueryBuilders.scriptQuery(performanceTimeScript));
    }

    /**
     * 获取申报单标记查询Builder
     *
     * @param orderTags 申报单标记
     * @return
     */
    private BoolQueryBuilder getOrderTagsQueryBuilder(Integer orderTags) {
        if (Objects.isNull(orderTags)) {
            return null;
        }
        Map<String, Object> params = new HashMap() {{
            put("orderTags", orderTags);
        }};
        // 脚本时间,如果申报完成状态，时间取完成时间和创建时间的插值，其它状态取当前时间和创建时间的差值
        String script = "return ((doc['orderTags'].value & params.orderTags) != 0);";
        Script orderTagsScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
        return QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("orderTags")).must(QueryBuilders.scriptQuery(orderTagsScript));
    }

    /**
     * 根据小时获取毫秒
     *
     * @param hour 小时
     * @return
     */
    private Integer getMillisByHour(String hour) {
        if (StringUtils.isEmpty(hour)) {
            return null;
        }
        return Double.valueOf((Double.valueOf(hour) * 60 * 60 * 1000)).intValue();
    }

    /**
     * @param jdMonitorParamDto
     */
    public void queryForList(JdMonitorParamDto jdMonitorParamDto) {
        Long accountBookId = jdMonitorParamDto.getAccountBookId();
        int minNum = jdMonitorParamDto.getMinNum();
        int minUte = jdMonitorParamDto.getMinute();
        Date now = new Date();
        //40分钟
        long time = minUte * 60 * 1000;
        //40分钟后的时间
        Date afterDate = new Date(now.getTime() - time);
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        boolQueryBuilder.must(QueryBuilders.matchQuery("accountBookId", accountBookId));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(afterDate.getTime()).to(now.getTime()));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build();
        List<OrderEsDO> list = elasticsearchTemplate.queryForList(searchQuery, OrderEsDO.class);
        //如果没有查到数据企业微信异常通告
        if (list.size() <= minNum) {
            //机器人地址
            //自测机器人
            String webHookAddress = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=ca4ca76d-e797-42dc-ab8f-b268b68a80f0";

            String errorMsg = String.format("在【%s】分钟内，订单总数为：【%s】单，请核查是否异常！！", minUte, list.size());
            //发送文本消息
            chatPhoneCall(webHookAddress, jdMonitorParamDto.getPhones(), errorMsg);
        }
    }

    boolean chatPhoneCall(String webHookAddress, List<String> chatPhoneList, String errorMsg) {
        HttpClient httpclient = HttpClients.createDefault();
        HttpPost httppost = new HttpPost(webHookAddress);
        httppost.addHeader("Content-Type", "application/json; charset=utf-8");
        String testMsg = "{\"msgtype\":\"text\",\"text\":{\"mentioned_mobile_list\":" + chatPhoneList + ",\"content\":\"" + errorMsg + "\"}}";
        StringEntity se = new StringEntity(testMsg, "utf-8");
        httppost.setEntity(se);
        try {
            HttpResponse response = httpclient.execute(httppost);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                log.info("消息发送成功!");
                return true;
            } else {
                log.error("消息发送失败！异常码如下：{}", response.getStatusLine().getStatusCode());
                return false;
            }
        } catch (IOException e) {
            log.error("消息发送失败, 异常信息如下: {}", e.getMessage(), e);
            return false;
        }
    }


//    public List<OrderEsDO> queryForList(YcOrderSearch search){I
//        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        /**
//         * 异常订单查询
//         */
//        if(search.getOrderStatus()!=null) {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("status", search.getOrderStatus()));
//        }
//        else
//        {
//            BoolQueryBuilder boolQueryBuilder0 = new BoolQueryBuilder();
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION0.getStatus()));
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION1.getStatus()));
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION2.getStatus()));
//            boolQueryBuilder.must(boolQueryBuilder0);
//        }
//
//        if(StringUtils.isEmpty(search.getApplyPath()))
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("applyPath",search.getApplyPath()));
//        }
//        if(search.getExceptionType()!=null)
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("exceptionType",search.getExceptionType()));
//        }
//        if(!org.apache.commons.lang3.StringUtils.isEmpty(search.getQueryNo()))
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("logisticsNo",search.getQueryNo()));
//        }
//
//        // 创建时间倒序排
//        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
//                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC)).build();
//
//        List<OrderEsDO> list = elasticsearchTemplate.queryForList(searchQuery, OrderEsDO.class);
//        return list;
//    }

    /**
     * 异常单查询
     * @param search
     * @return
     */
//    public Page<OrderEsDO> paging2(YcOrderPageSearch search){
//
//        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        /**
//         * 异常订单查询
//         */
//        if(search.getOrderStatus()!=null) {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("status", search.getOrderStatus()));
//        }
//        else
//        {
//            BoolQueryBuilder boolQueryBuilder0 = new BoolQueryBuilder();
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION0.getStatus()));
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION1.getStatus()));
//            boolQueryBuilder0.should(QueryBuilders.matchQuery("status", OrderExceptionEnum.ERROR_EXCEPTION2.getStatus()));
//            boolQueryBuilder.must(boolQueryBuilder0);
//        }
//
//        if(StringUtils.isEmpty(search.getApplyPath()))
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("applyPath",search.getApplyPath()));
//        }
//        if(search.getExceptionType()!=null)
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("exceptionType",search.getExceptionType()));
//        }
//        if(!org.apache.commons.lang3.StringUtils.isEmpty(search.getQueryNo()))
//        {
//            boolQueryBuilder.must(QueryBuilders.matchQuery("logisticsNo",search.getQueryNo()));
//        }
//    // 创建时间倒序排
//        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
//                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
//                .withPageable(PageRequest.of(search.getCurrentPage()-1, search.getPageSize())).build();
//        Page<OrderEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, OrderEsDO.class);
//        return page;
//    }


    /**
     * 查询租户担保企业的税金总额
     *
     * @param tenantId   租户id
     * @param createTime 起始时间
     * @param endTime    结束时间
     * @return 查询结果
     */
    public ScrolledPage<OrderEsDO> queryTaxTotalAmount(String tenantId, Date createTime, Date endTime) {
        long scrollTimeInMillis = 10 * 1000;
//        Integer pageSize = 500;

        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        //查找id
        boolQueryBuilder.should(new BoolQueryBuilder().must(QueryBuilders.existsQuery("tenantOuterId")));
        boolQueryBuilder.filter(QueryBuilders.termQuery("tenantOuterId", tenantId));
        //创建时间
        if (!Objects.isNull(createTime) && !Objects.isNull(endTime)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createTime).to(endTime));
        }
        //查询申报完成
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", OrderStatus.DEC_SUCCESS.getValue()));
        //
        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("tenantOuterId").field("tenantOuterId").size(Integer.MAX_VALUE)
                .subAggregation(AggregationBuilders.terms("assureCompanyId").field("assureCompanyId"))
                .subAggregation(AggregationBuilders.sum("taxTotalAmount").field("taxPrice"));

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .build();

        return elasticsearchTemplate.startScroll(scrollTimeInMillis, nativeSearchQuery, OrderEsDO.class);
    }

    public Long findOrderIdByDeclareOrderNo(String declareOrderNo) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        Long tenantryId = SimpleTenantHelper.getTenantId();
        if (Objects.nonNull(tenantryId)) {
            boolQuery.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        boolQuery.filter(QueryBuilders.termQuery("declareOrderNo", declareOrderNo));

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQuery)
                // 只查询一个
                .withPageable(PageRequest.of(0, 1))
                .build();
        Page<OrderEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, OrderEsDO.class);
        if (page.hasContent()) {
            List<OrderEsDO> content = page.getContent();
            if (!CollectionUtils.isEmpty(content)) {
                String id = content.get(0).getId();
                return Long.valueOf(id);
            }
        }
        return null;

    }
}
