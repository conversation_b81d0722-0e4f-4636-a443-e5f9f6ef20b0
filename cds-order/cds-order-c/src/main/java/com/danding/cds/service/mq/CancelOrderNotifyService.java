package com.danding.cds.service.mq;

/**
 * @program: cds-center
 * @description: 取消单回执通知
 * @author: 潘本乐（Belep）
 * @create: 2022-01-19 13:45
 **/

import com.alibaba.fastjson.JSON;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatus;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

@Service
@Slf4j
public class CancelOrderNotifyService {

    @DubboReference
    private MessageService messageService;

    /**
     * 取消单通知OMS
     *
     * @param orderId        申报单ID
     * @param declareOrderNo 申报NO
     * @param channel        渠道
     * @param calloffStatus  取消单状态
     * @param customsDetail  海关详情
     * @param customsTime    海关申报时间
     */
    public void callOffNotify(Long orderId, String declareOrderNo, String channel, InventoryCalloffStatus calloffStatus, String customsDetail, Long customsTime) {

        Date date = customsTime == null ? new Date() : new Date(customsTime);
        List<String> subscribeList = Arrays.asList(channel);
        String activeData = JSON.toJSONString(new OrderActiveInfo(orderId).buildCustomsInfo(calloffStatus.getCode().toString(), customsDetail, date));
        log.warn("消息回执构建 - MessageType={} , subscribeList={}, declareOrderNo= {} , activeData={}", MessageType.ORDER_CUSTOMS_CANCEL,
                subscribeList, declareOrderNo, activeData);
        messageService.createMessage(
                MessageType.ORDER_CUSTOMS_CANCEL,
                subscribeList,
                declareOrderNo,
                activeData,
                "");
    }

}
