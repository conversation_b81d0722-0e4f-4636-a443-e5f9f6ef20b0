package com.danding.cds.service.declareImpl.refund;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.common.constants.DxpCons;
import com.danding.cds.declare.base.component.refund.InventoryRefundDeclareAbstrat;
import com.danding.cds.declare.sdk.clear.base.result.InventoryRefundResult;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.service.customs.declare.EwtpDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 芥舟退单申报
 * @author: awm
 * @create: 2023-03-27 10:17
 **/
@Service("YWNO_EWTP_INVENTORY_REFUND_DECLARE")
@Slf4j
public class YwnoEwtpRefundDeclareImpl extends InventoryRefundDeclareAbstrat {

    @Autowired
    private EwtpDeclareService ewtpDeclareService;

    @Override
    protected InventoryRefundResult mockDeclareTest(WarpRefundOrderInfo info) {
        return ewtpDeclareService.ewtpInventoryRefundDeclare(info, DxpCons.YWNO_EWTP_DXP);
    }

    @Override
    protected InventoryRefundResult declare(WarpRefundOrderInfo info) {
        return ewtpDeclareService.ewtpInventoryRefundDeclare(info, DxpCons.YWNO_EWTP_DXP);

    }
}
