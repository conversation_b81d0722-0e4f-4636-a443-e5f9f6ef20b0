package com.danding.cds.handler;

import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.track.log.interfaces.TrackLogParametersHandler;
import com.danding.cds.track.log.utils.TrackLogUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 上游推送单子
 * @date 2022/6/7 10:24
 */
@Component
public class CainiaoNotifyInventoryExitAreaHandler implements TrackLogParametersHandler {
    @Override
    public TrackLogInfoDto handle(TrackLog trackLog, Object[] args) throws Exception {
        TrackLogInfoDto trackLogInfoDto = new TrackLogInfoDto();
//        int requestMessageIndex = trackLog.requestMessageIndex();
//        if (requestMessageIndex != -1) {
//            trackLogInfoDto.setRequestMessage((String) args[requestMessageIndex]);
//        }
//        int infoIndex = trackLog.infoIndex();
//        String declareOrderNo = (String) args[infoIndex];

        trackLogInfoDto.setSender(trackLog.sender());
        trackLogInfoDto.setReceiver(TrackLogConstantMixAll.DT_CCS);
        trackLogInfoDto.setInternalStatus(OrderInternalEnum.INVENTORY_DECLARE_SUCCESS.getCode());
        trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.CAINIAO_RECEIVE_INVENTORY_EXIT_AREA);
        trackLogInfoDto.setEventTime(new Date());
        trackLogInfoDto.setOperator(UserUtils.getUserRealName());
        return trackLogInfoDto;
    }

    @Override
    public TrackLogInfoDto afterProcessHandle(TrackLog trackLog, TrackLogInfoDto trackLogInfoDto, Object proceed) throws Exception {
        RpcResult result = (RpcResult) proceed;
        TrackLogBaseInfo baseInfo = TrackLogUtils.getBaseInfoAndRemove();
        if (Objects.isNull(baseInfo)) {
            throw new Exception("threadLocal中未获取到baseInfo");
        }
        trackLogInfoDto.setDeclareOrderNo(baseInfo.getDeclareOrderNo());
        trackLogInfoDto.setOrderId(baseInfo.getOrderId());
        trackLogInfoDto.setOrderSn(baseInfo.getOrderSn());
        trackLogInfoDto.setResult(result.getCode() == 200 ? TrackLogConstantMixAll.SUCCESS : TrackLogConstantMixAll.FAIL);
        trackLogInfoDto.setEventTime(new Date());
        return null;
    }
}
