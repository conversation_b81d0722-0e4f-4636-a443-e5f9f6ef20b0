package com.danding.cds.service.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.DeclareScanMqDto;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * @program: cds-center
 * @description: declareOrderConsumer
 * @author: 潘本乐（Belep）
 * @create: 2022-10-20 21:48
 **/
@Service
@Slf4j
public class DeclareOrderConsumerService {

    @Resource
    private OrderService orderService;

    /**
     * 指定申报单申报消费类型
     *
     * @param message
     */
    public void appointDeclareTypeConsumerhandle(Object message) {

        if (ObjectUtils.isEmpty(message)) {
            return;
        }
        String data = message.toString();
        log.info("appointDeclareTypeConsumerhandle data = {}", data);
        DeclareScanMqDto declareScanMqDto = null;
        boolean isJson = true;
        try {
            declareScanMqDto = JSON.parseObject(data, DeclareScanMqDto.class);
        } catch (Exception e) {
            isJson = false;
        }
        try {
            if (isJson) {
                orderService.declare(declareScanMqDto.getDeclareOrderSn(), DeclareEnum.getEnum(declareScanMqDto.getDeclareType()));
            } else {
                orderService.declare(data);
            }
        } catch (ArgsErrorException ex) {
            log.error("[op:appointDeclareTypeConsumerhandle] exception, data={}, cause={}", data, ex.getErrorMessage(), ex);
        } catch (Exception e) {
            log.error("[op:appointDeclareTypeConsumerhandle] exception, data={}, cause={}", data, e.getMessage(), e);
        }


    }

    /**
     * 申报单消费
     *
     * @param message
     */
    public void declareConsumerhandle(Object message) {

        if (ObjectUtils.isEmpty(message)) {
            return;
        }
        String sn = message.toString();
        log.info("OrderDeclareMQConsumer sn = {}", sn);
        try {
            orderService.declare(sn);
        } catch (ArgsErrorException ex) {
            log.error("[op:OrderDeclareMQConsumer] exception, sn={}, cause={}", sn, ex.getErrorMessage(), ex);
        } catch (Exception e) {
            log.error("[op:OrderDeclareMQConsumer] exception, sn={}, cause={}", sn, e.getMessage(), e);
        }
    }
}
