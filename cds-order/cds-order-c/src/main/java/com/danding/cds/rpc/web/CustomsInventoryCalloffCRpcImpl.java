package com.danding.cds.rpc.web;

import com.danding.cds.c.api.bean.vo.CalloffEditRefundGoodsInfoReqVo;
import com.danding.cds.c.api.rpc.CustomsInventoryCalloffCRpc;
import com.danding.cds.c.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.v2.bean.dto.JdCustomsOrderMsgDataDTO;
import com.danding.cds.v2.bean.dto.LinkCustomsRefundOrderMessageNotifyDTO;
import com.danding.cds.v2.bean.vo.req.RefundWarehouseCancelReq;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: yousx
 * @Date: 2023/12/12
 * @Description:
 */
@DubboService
public class CustomsInventoryCalloffCRpcImpl implements CustomsInventoryCalloffCRpc {

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Override
    public CustomsInventoryCalloffDTO findById(Long id) {
        return customsInventoryCalloffService.findById(id);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findById(List<Long> idList) {
        return customsInventoryCalloffService.findById(idList);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findListByOrderId(Long orderId) {
        return customsInventoryCalloffService.findListByOrderId(orderId);
    }

    @Override
    public CustomsInventoryCalloffDTO findByOrderId(Long orderId) {
        return customsInventoryCalloffService.findByOrderId(orderId);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findListByOrderIdList(List<Long> orderIdList) {
        return customsInventoryCalloffService.findListByOrderIdList(orderIdList);
    }

    @Override
    public ListVO<CustomsInventoryCalloffDTOV2> paging(CustomsInventoryCalloffSearch search) {
        return customsInventoryCalloffService.paging(search);
    }

    @Override
    public InventoryCallOffOverTimeCountVO inventoryCalloffOverTimeCount(CustomsInventoryCalloffSearch search) {
        return customsInventoryCalloffService.inventoryCalloffOverTimeCount(search);
    }

    @Override
    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason) {
        return customsInventoryCalloffService.upset(orderId, invSn, calloffType, calloffStatus, reason);
    }

    @Override
    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String picJson, String refundLogisticsNo) {
        return customsInventoryCalloffService.upset(orderId, invSn, calloffType, calloffStatus, reason, picJson, refundLogisticsNo);
    }

    @Override
    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String ownerCode, String ownerName) {
        return customsInventoryCalloffService.upset(orderId, invSn, calloffType, calloffStatus, reason, picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
    }

    @Override
    public int create(CustomsInventoryCalloffDTO customsInventoryCalloffDTO) {
        return customsInventoryCalloffService.create(customsInventoryCalloffDTO);
    }

    @Override
    public int update(CustomsInventoryCalloffDTO customsInventoryCalloffDTO) {
        return customsInventoryCalloffService.update(customsInventoryCalloffDTO);
    }

    @Override
    public CustomsInventoryCalloffCountDTO selectCountByCondition(CustomsInventoryCalloffSearch search) {
        return customsInventoryCalloffService.selectCountByCondition(search);
    }

    @Override
    public CalloffCountDTO selectCalloffCount(String areaCompanyId) {
        return customsInventoryCalloffService.selectCalloffCount(areaCompanyId);
    }

    @Override
    public Map<String, CalloffAfterSalesCount> selectCalloffCountDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        return customsInventoryCalloffService.selectCalloffCountDetail(handlerIdList, beginTime, endTime);
    }

    @Override
    public int updateCustomsInventoryCalloffStatus(Long id, String callofStatus, String calloffType, String calloffReason, String rejectReason) {
        return customsInventoryCalloffService.updateCustomsInventoryCalloffStatus(id, callofStatus, calloffType, calloffReason, rejectReason);
    }

    @Override
    public int updateCustomsInventoryCalloffStatus(List<Long> idList, String callofStatus, String calloffType, String calloffReason, String rejectReason) {
        return customsInventoryCalloffService.updateCustomsInventoryCalloffStatus(idList, callofStatus, calloffType, calloffReason, rejectReason);
    }

    @Override
    public int updateCancelStatusInfo(Long orderId, String cancelStatus) {
        return customsInventoryCalloffService.updateCancelStatusInfo(orderId, cancelStatus);
    }

    @Override
    public int updateCancelStatusInfo(Long orderId, String cancelStatus, String customsStatus, String customsStatusDetail) {
        return customsInventoryCalloffService.updateCancelStatusInfo(orderId, cancelStatus, customsStatus, customsStatusDetail);
    }

    @Override
    public int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus) {
        return customsInventoryCalloffService.updateRefundStatusInfo(orderId, refundStatus, RefundCheckStatus);
    }

    @Override
    public int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus, String refundCustomsStatus, String refundCustomsStatusDetail) {
        return customsInventoryCalloffService.updateRefundStatusInfo(orderId, refundStatus, RefundCheckStatus, refundCustomsStatus, refundCustomsStatusDetail);
    }

    @Override
    public int updateCustomsStatus(Long orderId, String customsStatus, Integer exitRegionStatus, Integer orderStatus) {
        return customsInventoryCalloffService.updateCustomsStatus(orderId, customsStatus, exitRegionStatus, orderStatus);
    }

    @Override
    public int updateExitRegionStatus(Long orderId, Integer exitRegionStatus) {
        return customsInventoryCalloffService.updateExitRegionStatus(orderId, exitRegionStatus);
    }

    @Override
    public int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType) {
        return customsInventoryCalloffService.updateCustomsInventoryCalloffStatusByOrderId(orderId, customsStatus, exitRegionStatus, calloffStatus, calloffType);
    }

    @Override
    public int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType, String rejectReason) {
        return customsInventoryCalloffService.updateCustomsInventoryCalloffStatusByOrderId(orderId, customsStatus, exitRegionStatus, calloffStatus, calloffType, rejectReason);
    }

    @Override
    public void updateCustomsInventoryCalloffStatusBatchByOrderId(List<Long> orderIdList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType) {
        customsInventoryCalloffService.updateCustomsInventoryCalloffStatusBatchByOrderId(orderIdList, customsStatus, exitRegionStatus, calloffStatus, calloffType);
    }

    @Override
    public void updateCustomsInventoryCalloffStatusBatchByLogisticsNo(List<String> mailNoList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType) {
        customsInventoryCalloffService.updateCustomsInventoryCalloffStatusBatchByLogisticsNo(mailNoList, customsStatus, exitRegionStatus, calloffStatus, calloffType);
    }

    @Override
    public void updateCalloffStatusByLogisticsNo(String mailNoList, String customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType) {
        customsInventoryCalloffService.updateCalloffStatusByLogisticsNo(mailNoList, customsStatus, exitRegionStatus, calloffStatus, calloffType);
    }

    @Override
    public List<Map<String, Object>> sumInventoryCalloff(Date beginTime, Date endTime) {
        return customsInventoryCalloffService.sumInventoryCalloff(beginTime, endTime);
    }

    @Override
    public void direct(Long id, String calloffReason) throws ArgsErrorException {
        customsInventoryCalloffService.direct(id, calloffReason);
    }

    @Override
    public int failCallof(Long orderId) {
        return customsInventoryCalloffService.failCallof(orderId);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> getCallOffByDeclareNo(String ownerCode, String entityWarehouseCode, List<String> cancelTypeList, Long timeBegin, Long timeEnd) {
        return customsInventoryCalloffService.getCallOffByDeclareNo(ownerCode, entityWarehouseCode, cancelTypeList, timeBegin, timeEnd);
    }

    @Override
    public void delJd() {
        customsInventoryCalloffService.delJd();
    }

    @Override
    public void delJdDetail() {
        customsInventoryCalloffService.delJdDetail();
    }

    @Override
    public List<CustomsCancelOrderDTO> jdCancelOrderSynPreview(CustomsInventoryCalloffSubmit submit) throws ArgsErrorException {
        return customsInventoryCalloffService.jdCancelOrderSynPreview(submit);
    }

    @Override
    public List<CustomsCancelOrderDetailsDTO> jdCancelOrderDetails(CustomsInventoryCalloffSubmit submit) {
        return customsInventoryCalloffService.jdCancelOrderDetails(submit);
    }

    @Override
    public CustomsInventoryCalloffCountDTO2 calloffOrderCount(CustomsInventoryCalloffSearch search) {
        return customsInventoryCalloffService.calloffOrderCount(search);
    }

    @Override
    public void cancelByLinkCustoms(LinkCustomsRefundOrderMessageNotifyDTO linkCustomsRefundOrderMessageNotifyDTO) {
        customsInventoryCalloffService.cancelByLinkCustoms(linkCustomsRefundOrderMessageNotifyDTO);
    }

    @Override
    public void updateLinkCustomsContent(CustomsInventoryCalloffDTO inventoryCalloffDTO) {
        customsInventoryCalloffService.updateLinkCustomsContent(inventoryCalloffDTO);
    }

    @Override
    public void updateTypeAndStatusAndExit(Long id, String calloffType, String calloffStatus, int exitRegionStatus) {
        customsInventoryCalloffService.updateTypeAndStatusAndExit(id, calloffType, calloffStatus, exitRegionStatus);
    }

    @Override
    public void updateRejectReasonAndStatusByOrderId(Long orderId, String rejectReason, String status) {
        customsInventoryCalloffService.updateRejectReasonAndStatusByOrderId(orderId, rejectReason, status);
    }

    @Override
    public void migrateByOrderId(Long oldOrderId, OrderDTO newOrderDTO) {
        customsInventoryCalloffService.migrateByOrderId(oldOrderId, newOrderDTO);
    }

    @Override
    public void refundWarehouseCancel(RefundWarehouseCancelReq reqVO) {
        customsInventoryCalloffService.refundWarehouseCancel(reqVO);
    }

    @Override
    public List<String> findMissOwnerWarehouseByBookId(List<Long> customsBookIdList, Date createTimeFrom, Date createTimeTo) {
        return customsInventoryCalloffService.findMissOwnerWarehouseByBookId(customsBookIdList, createTimeFrom, createTimeTo);
    }

    @Override
    public void editRefundGoodsInfo(CalloffEditRefundGoodsInfoReqVo reqVO) {
        customsInventoryCalloffService.editRefundGoodsInfo(reqVO);
    }

    @Override
    public void jdRefundCancel(JdCustomsOrderMsgDataDTO reqVO) {
        customsInventoryCalloffService.jdRefundCancel(reqVO);
    }
}
