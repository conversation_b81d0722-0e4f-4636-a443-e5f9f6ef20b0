package com.danding.cds.service.declareImpl.refund;

import com.danding.cds.declare.base.component.refund.InventoryRefundDeclareAbstrat;
import com.danding.cds.declare.sdk.clear.base.result.InventoryRefundResult;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.service.ExternalDeclareOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 芥州-保税仓入驻 退货申报
 *
 * <AUTHOR>
 */
@Service("JIEZ_TECH_WGBS_INVENTORY_REFUND_DECLARE")
@Slf4j
public class JieztechWgbsRefundDeclareImpl extends InventoryRefundDeclareAbstrat {

    @Autowired
    private ExternalDeclareOrderService jieztechSignService;

    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @Override
    protected InventoryRefundResult mockDeclareTest(WarpRefundOrderInfo info) {
        return declare(info);
    }

    @Override
    protected InventoryRefundResult declare(WarpRefundOrderInfo info) {
        // 维护下，ccs系统取消单企业内部编码
        externalDeclareOrderBaseService.updateCcsRefundCopNoByOrderNo(info.getDeclareNos(), info.getRefundOrderInfoDto().getId().toString());
        jieztechSignService.sendJieztech(info.getDeclareNos(), "REFUND");
        return null;
    }
}
