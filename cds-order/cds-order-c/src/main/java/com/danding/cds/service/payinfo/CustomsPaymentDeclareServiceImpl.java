package com.danding.cds.service.payinfo;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsPaymentDeclareService;
import com.danding.cds.c.api.service.CustomsPaymentService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.dto.CompanyDistrictDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.CustomsPaymentStatus;
import com.danding.cds.declare.sdk.enums.PayCustoms;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.payment.WrapPaymentInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSubmitItem;
import com.danding.cds.order.base.bean.dao.CustomsPaymentDeclareDO;
import com.danding.cds.order.base.mapper.CustomsPaymentDeclareMapper;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoDTO;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantCustomsInfoService;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.utils.BuildInfoUtil;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.kevinsawicki.http.HttpRequest;
import com.umf.api.payments.Item;
import com.umf.api.payments.SubOrder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 17:34
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class CustomsPaymentDeclareServiceImpl implements CustomsPaymentDeclareService {
    @Resource
    private CustomsPaymentDeclareMapper customsPaymentDeclareMapper;
    @Autowired
    @Lazy
    private CustomsPaymentService customsPaymentService;

    @DubboReference
    private PayMerchantAccountChannelService payMerchantAccountChannelService;

    @DubboReference
    private PayMerchantCustomsInfoService payMerchantCustomsInfoService;

    @Resource
    private OrderService orderService;

    @DubboReference
    private CompanyService companyService;

    @Autowired
    private CustomsSupport customsSupport;

    @Autowired
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Override
    public List<CustomsPaymentDeclareDTO> findByCustomsPaymentCode(String paymentSn) {
        Example example = new Example(CustomsPaymentDeclareDO.class);
        example.orderBy("createTime").asc();
        example.and(example.createCriteria().andEqualTo("paymentSn", paymentSn));
        List<CustomsPaymentDeclareDO> customsPaymentDeclareDOList = customsPaymentDeclareMapper.selectByExample(example);
        List<CustomsPaymentDeclareDTO> declareList = new ArrayList<CustomsPaymentDeclareDTO>();
        for (CustomsPaymentDeclareDO item : customsPaymentDeclareDOList) {
            CustomsPaymentDeclareDTO result = new CustomsPaymentDeclareDTO();
            BeanUtils.copyProperties(item, result);
            declareList.add(result);
        }
        return declareList;
    }

    @Override
    public CustomsPaymentDeclareDTO findCustomsPaymentByOutRequestNo(String customs, String outRequestNo) {
        if (StrUtil.isBlank(customs) || StrUtil.isBlank(outRequestNo)) {
            return null;
        }
        Example example = new Example(CustomsPaymentDeclareDO.class);
        example.and(example.createCriteria().andEqualTo("outRequestNo", outRequestNo).andEqualTo("customs", customs));
        CustomsPaymentDeclareDO customsPaymentDeclareDO = customsPaymentDeclareMapper.selectOneByExample(example);
        CustomsPaymentDeclareDTO result = new CustomsPaymentDeclareDTO();
        BeanUtils.copyProperties(customsPaymentDeclareDO, result);
        return result;
    }

    @Override
    public Long updateStatus(Long id, Integer status) {
        CustomsPaymentDeclareDO customsPaymentDeclareDO = new CustomsPaymentDeclareDO();
        customsPaymentDeclareDO.setId(id);
        customsPaymentDeclareDO.setStatus(status);
        UserUtils.setUpdateBy(customsPaymentDeclareDO);
        customsPaymentDeclareDO.setUpdateTime(new Date());
        customsPaymentDeclareMapper.updateByPrimaryKeySelective(customsPaymentDeclareDO);
        return customsPaymentDeclareDO.getId();
    }

    @Override
    public CustomsPaymentDeclareDTO findById(Long id) {
        CustomsPaymentDeclareDO ret = customsPaymentDeclareMapper.selectByPrimaryKey(id);
        if (ret == null) {
            return null;
        } else {
            CustomsPaymentDeclareDTO result = new CustomsPaymentDeclareDTO();
            BeanUtils.copyProperties(ret, result);
            return result;
        }
    }

    @Override
    public Long updateStatus(Long id, Integer status, String returnMsg, String extra, String postMsg, Date returnTime) {
        CustomsPaymentDeclareDO customsPaymentDeclareDO = new CustomsPaymentDeclareDO();
        customsPaymentDeclareDO.setId(id);
        customsPaymentDeclareDO.setStatus(status);
        customsPaymentDeclareDO.setReturnMsg(returnMsg);
        customsPaymentDeclareDO.setReturnTime(returnTime);
        customsPaymentDeclareDO.setExtra(extra);
        customsPaymentDeclareDO.setPostMsg(postMsg);
        UserUtils.setUpdateBy(customsPaymentDeclareDO);
        customsPaymentDeclareDO.setUpdateTime(new Date());
        customsPaymentDeclareMapper.updateByPrimaryKeySelective(customsPaymentDeclareDO);
        return customsPaymentDeclareDO.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doSuccessfulStatus(Long id, String returnMsg, String extra, String postMsg, String payTransactionId, String verDept, String declarePayNo) {
        CustomsPaymentDeclareDTO customsPaymentDeclare = findById(id);
        CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findById(customsPaymentDeclare.getPaymentId());
        log.info("[op:CustomsPaymentDeclareService] 申报订单={} 申报成功，", customsPaymentDeclare.getOutRequestNo());
        updateStatus(customsPaymentDeclare.getId(), CustomsPaymentStatus.SUCCESS.getValue(), returnMsg, extra, postMsg, new Date());

        List<CustomsPaymentDeclareDTO> declareDTOList = findByCustomsPaymentCode(customsPaymentDeclare.getPaymentSn());
        Boolean finish = true;
        for (CustomsPaymentDeclareDTO customsPaymentDeclareDTO : declareDTOList) {
            if (!CustomsPaymentStatus.SUCCESS.getValue().equals(customsPaymentDeclareDTO.getStatus())) {
                finish = false;
                break;
            }
        }
        if (finish) {
            log.info("[op:CustomsPaymentDeclareService] 主订单={} 申报完成，", customsPaymentDTO.getSn());
            customsPaymentService.updateBySuccess(customsPaymentDeclare.getPaymentId(), CustomsActionStatus.DEC_SUCCESS.getValue(),
                    payTransactionId, verDept, declarePayNo);
            orderDeclareMQProducer.send(customsPaymentDTO.getOrderSn());
        }
    }


    @Override
    public void createWrapPaymentInfoAndSend(CustomsPaymentDTO customsPayment, CustomsPaymentDeclareDTO customsPaymentDeclare) {
        this.createWrapPaymentInfoAndSend(customsPayment, customsPaymentDeclare, null);
    }

    @Override
    public void createWrapPaymentInfoAndSend(CustomsPaymentDTO customsPayment, CustomsPaymentDeclareDTO customsPaymentDeclare, RouteInfoResVo routeDTO) {
        PayMerchantAccountChannelDTO payMerchantAccountChannelDTO = payMerchantAccountChannelService.findByCodeAndChannel(customsPayment.getMerchantCode(), customsPayment.getChannel());
        OrderDTO orderDTO = orderService.findByIdFull(customsPayment.getOrderId());
        WrapPaymentInfo wrapPaymentInfo = new WrapPaymentInfo();
        if (Objects.nonNull(customsPayment.getUmfJson()) && Objects.equals(customsPayment.getChannel(), "umf")) {
            ObjectMapper mapper = new ObjectMapper();
            try {
                List<Map<String, String>> conList = mapper.readValue(customsPayment.getUmfJson(), List.class);
                JSONObject jsonObj = new JSONObject();
                for (Map map : conList) {
                    jsonObj = new JSONObject(map);
                }
                System.out.println(conList);
                SubOrder subOrder = JSONObject.parseObject(jsonObj.toJSONString(), SubOrder.class);
                List<Item> itemList = new ArrayList<>();
                for (Item items : subOrder.getItems()) {
                    Item item = new Item();
                    item.setMerItemId(items.getMerItemId());
                    itemList.add(item);
                }
                subOrder.setItems(itemList);
                wrapPaymentInfo.setSubOrder(subOrder);
            } catch (JsonProcessingException e) {
                e.printStackTrace();
            }
            wrapPaymentInfo.setExtra(customsPaymentDeclare.getExtra());

        }
        if (PayCustomsChannel.TONGLIAN_PAY.getValue().equalsIgnoreCase(customsPayment.getChannel())
                && StringUtils.isNotEmpty(customsPayment.getRelDeclareOrderNoJson())) {
            wrapPaymentInfo.setRelDeclareOrderNoList(JSON.parseArray(customsPayment.getRelDeclareOrderNoJson(), String.class));
        }
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(customsPayment.getEbpId());
        if (PayCustoms.ZONGSHU.getCode().equals(customsPaymentDeclare.getCustoms())
                || PayCustoms.GUANGZHOU.getCode().equals(customsPaymentDeclare.getCustoms())
                || (PayCustoms.GUANGZHOU_NS_GJ.getCode().equals(customsPaymentDeclare.getCustoms()) && customsPayment.getChannel().equals(PayCustomsChannel.ALIPAY.getValue()))
        ) {
            // 支付宝南沙用总署编码申报
            // GUANGZHOU是微信用的，总署申报
            wrapPaymentInfo.setMerchantCustomsCode(ebp.getCode());
            wrapPaymentInfo.setMerchantCustomsName(ebp.getName());
        } else if (PayCustoms.HANGZHOU.getCode().equals(customsPaymentDeclare.getCustoms())
                && customsPayment.getChannel().equalsIgnoreCase(PayCustomsChannel.TONGLIAN_PAY.getValue())) {
            PayMerchantCustomsInfoDTO merchantCustomsInfoDTO = payMerchantCustomsInfoService
                    .findByCodeAndCustoms(customsPayment.getMerchantCode(), customsPayment.getCustoms());
            wrapPaymentInfo.setMerchantCustomsCode(merchantCustomsInfoDTO.getMerchantCustomsCode());
            wrapPaymentInfo.setMerchantCustomsName(merchantCustomsInfoDTO.getMerchantCustomsName());
        } else {
            CompanyDistrictDTO districtDTO = ebp.getDistrict(CustomsDistrictEnum.getEnum(customsPayment.getCustoms()));
            if (districtDTO == null) {
                wrapPaymentInfo.setMerchantCustomsCode(ebp.getCode());
                wrapPaymentInfo.setMerchantCustomsName(ebp.getName());
            } else {
                wrapPaymentInfo.setMerchantCustomsCode(districtDTO.getCode());
                wrapPaymentInfo.setMerchantCustomsName(districtDTO.getName());
            }
        }
        if (Objects.isNull(wrapPaymentInfo.getEbpCompanyDTO())) {
            wrapPaymentInfo.setEbpCompanyDTO(ConvertUtil.beanConvert(ebp, CompanyInfo.class));
        }
        wrapPaymentInfo.setDeclareOrderNo(customsPayment.getDeclareOrderNo());
        wrapPaymentInfo.setAmount(customsPayment.getAmount());
        wrapPaymentInfo.setCommodityFee(customsPayment.getCommodityFee());
        wrapPaymentInfo.setTransportFee(customsPayment.getTransportFee());
        wrapPaymentInfo.setBuyerIdNo(customsPayment.getBuyerIdNo());
        wrapPaymentInfo.setBuyerIdType("IDCARD");
        wrapPaymentInfo.setBuyerName(customsPayment.getBuyerName());
        wrapPaymentInfo.setChannel(customsPayment.getChannel());
        wrapPaymentInfo.setOrderNo(customsPayment.getOutOrderNo());
        wrapPaymentInfo.setOutRequestNo(customsPaymentDeclare.getOutRequestNo());
        wrapPaymentInfo.setOutOrderNo(customsPayment.getOutOrderNo());
        wrapPaymentInfo.setSplitFlag(true);
        if (Objects.equals(customsPayment.getChannel(), "umf")) {
            wrapPaymentInfo.setTokenJson(getConfigDataByRedis());
        } else {
            wrapPaymentInfo.setTokenJson(payMerchantAccountChannelDTO.getTokenJson());
        }
        wrapPaymentInfo.setTaxFee(customsPayment.getTaxFee());
        wrapPaymentInfo.setCustoms(customsPaymentDeclare.getCustoms());
        wrapPaymentInfo.setMerchantCode(customsPayment.getMerchantCode());
        wrapPaymentInfo.setTradePayNo(customsPayment.getTradePayNo());
        wrapPaymentInfo.setOutTradeNo(customsPayment.getOutTradeNo());
        if (orderDTO.getExtraJson() != null) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            for (OrderSubmitItem orderSubmitItem : orderExtra.getSubmit().getItemList()) {
                wrapPaymentInfo.setGoodsName(orderSubmitItem.getItemName());
                wrapPaymentInfo.setGoodsCount(orderSubmitItem.getCount());
            }
            wrapPaymentInfo.setBuyerPhoneNumber(orderExtra.getSubmit().getBuyerTelNumber());
        }

        BuildInfoUtil.setWrapRouteInfo(wrapPaymentInfo, routeDTO);
        if (customsPaymentDeclare.getStatus().equals(CustomsPaymentStatus.SUCCESS.getValue())
                || customsPaymentDeclare.getStatus().equals(CustomsPaymentStatus.FAIL.getValue())) {
            wrapPaymentInfo.setStatus(CustomsPaymentStatus.WAIT_RE_PUSH.getValue());
        } else if (customsPaymentDeclare.getStatus().equals(CustomsPaymentStatus.CANCEL.getValue())) {
            wrapPaymentInfo.setStatus(CustomsPaymentStatus.WAIT_DECLARE.getValue());
        } else {
            wrapPaymentInfo.setStatus(customsPaymentDeclare.getStatus());
        }
//        if (wrapPaymentInfo.dynamicDeclareEnable()) {
//            DeclareUtils.paymentDeclare(wrapPaymentInfo);
//            return;
//        }
        wrapPaymentInfo.setRecpAccount(payMerchantAccountChannelDTO.getRecpAccount());
        if (PayCustoms.HANGZHOU.getCode().equals(customsPaymentDeclare.getCustoms())
                && customsPayment.getChannel().equalsIgnoreCase(PayCustomsChannel.TONGLIAN_PAY.getValue())
                && StringUtils.isNotEmpty(customsPayment.getPayMerchantOutNo())) {
            log.info("通联支付申报存在 外部支付商户号取上游下发的 declareOrderNo={}, PayMerchantOutNo={}",
                    customsPayment.getDeclareOrderNo(), customsPayment.getPayMerchantOutNo());
            // 通联申报存在 通商云申报，一个商户&渠道 对应对多个支付商户号，先使用上游透传的解决
            wrapPaymentInfo.setRecpAccount(customsPayment.getPayMerchantOutNo());
        }
        customsSupport.clearPaymentDeclare(wrapPaymentInfo);
        log.info("[op: CustomsPaymentDeclareService] wrapPaymentInfo={},支付申报，mq推送", JSON.toJSONString(wrapPaymentInfo));
//        customsPaymentService.updateStatus(customsPaymentDeclare.getPaymentId(), CustomsActionStatus.DEC_ING.getValue());
//        logService.logNormal(LogCode.LOG_PAYMENT, LogOperation.DECLARE,
//                customsPaymentDeclare.getSn(), customsPayment.getDeclareOrderNo(),"操作支付单申报");
    }

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${liandong.clientsecret:}")
    private String lianDongClientSecret;

    @Value("${liandong.clientid:}")
    private String lianDongClientId;

    private final static String LIAN_DONG = "CCS:LianDongUtil:lianDongOauthInfo";


    public String getConfigDataByRedis() {
        log.warn("[LianDongChannelToken]-获取token");
        String lianDongToken = null;
        try {

            String url = "https://fx.soopay.net/cberest/v1/oauth/authorize";
            Map<String, String> headMap = new HashMap<>();
            headMap.put("Content-Type", "application/json;charset=utf-8");
            headMap.put("Cache-Control", "no-cache");
            headMap.put("accept-encoding", "gzip, deflate");
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put("client_id", lianDongClientId);
            paramMap.put("client_secret", lianDongClientSecret);
            paramMap.put("grant_type", "client_credentials");
            log.warn("[LianDongChannelToken]-请求信息 ClientId{} ClientSecret{}", lianDongClientId, lianDongClientSecret);
            HttpRequest httpRequest = HttpRequest.post(url)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .headers(headMap).acceptGzipEncoding().uncompress(true).send(JSON.toJSONString(paramMap));
            String body = httpRequest.body();
            log.warn("[LianDongChannelToken]-请求响应 内容-{}", body);
            if (!body.contains("error")) {
                JSONObject authInfo = JSONObject.parseObject(body);
                long timestamp = System.currentTimeMillis() / 1000;
                Map<String, Object> tokenMap = new HashMap<>();
                tokenMap.put("access_token", authInfo.get("access_token").toString());
                tokenMap.put("createTimeStamp", timestamp);
                tokenMap.put("expires_in", Long.valueOf(authInfo.get("expires_in").toString()));
//                    redisTemplate.opsForHash().putAll(LIAN_DONG,map);
//                    redisTemplate.opsForValue().set(LIAN_DONG, tokenMap, 300, TimeUnit.SECONDS);
                JSONObject jsonObject = new JSONObject(tokenMap);
                lianDongToken = JSONObject.toJSONString(jsonObject);
            } else {
                lianDongToken = null;
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return lianDongToken;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void doFailureStatus(Long id, String returnMsg, String extra, String postMsg) {
        CustomsPaymentDeclareDTO customsPaymentDeclare = findById(id);
        log.info("[op:CustomsPaymentDeclareService] 申报订单={} 申报失败，", customsPaymentDeclare.getOutRequestNo());
        updateStatus(id, CustomsPaymentStatus.FAIL.getValue(), returnMsg, extra, postMsg, new Date());
        customsPaymentService.updateStatus(customsPaymentDeclare.getPaymentId(), CustomsActionStatus.DEC_FAIL.getValue());
    }

    @Override
    public void manualUpdStatus(CustomsPaymentDeclareDTO infoDTO) {
        Integer paymentDeclareStatus = infoDTO.getStatus();
        if (!paymentDeclareStatus.equals(CustomsPaymentStatus.WAIT_DECLARE.getValue())
                && !paymentDeclareStatus.equals(CustomsPaymentStatus.FAIL.getValue())) {
            log.error("修改的状态【 {} 】暂不处理", paymentDeclareStatus);
            return;
        }
        log.info("开始更新 : paymentId = {}, paymentDeclareStatus = {}", infoDTO.getId(), paymentDeclareStatus);
        Integer userId = UserUtils.getUserId();
        //修改customs_payment_declare 表中状态
        Example example = new Example(CustomsPaymentDeclareDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("paymentId", infoDTO.getPaymentId());
        CustomsPaymentDeclareDO customsPaymentDeclareDO = new CustomsPaymentDeclareDO();
        customsPaymentDeclareDO.setStatus(paymentDeclareStatus);
        customsPaymentDeclareDO.setUpdateBy(userId);
        customsPaymentDeclareDO.setUpdateTime(new Date());
        customsPaymentDeclareMapper.updateByExampleSelective(customsPaymentDeclareDO, example);
        //修改customs_payment 表中状态
        if (paymentDeclareStatus.equals(CustomsPaymentStatus.WAIT_DECLARE.getValue())) {   // 修改为【待申报】状态
            customsPaymentService.updateStatus(infoDTO.getPaymentId(), CustomsActionStatus.DEC_WAIT.getValue());
        }
        if (paymentDeclareStatus.equals(CustomsPaymentStatus.FAIL.getValue())) {    // 修改为【申报失败】状态
            customsPaymentService.updateStatus(infoDTO.getPaymentId(), CustomsActionStatus.DEC_FAIL.getValue());
        }
    }
}
