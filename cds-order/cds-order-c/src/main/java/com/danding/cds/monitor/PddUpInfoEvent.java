package com.danding.cds.monitor;

import lombok.Data;

@Data
public class PddUpInfoEvent {

    private String declareOrderNo;

    private Integer type;

    private String expressName; // 快递名称

    private String expressNo; // 运单编号

    // 申报必填

    private String outOrderNo;

    private String requestXml;

    // 回执必填

    private Long receiptTime;

    private String receiptXml;

    private String receiptStatus;

    private String receiptDetail;

    public PddUpInfoEvent(String declareOrderNo, Integer type, String expressName, String expressNo, String outOrderNo, String requestXml, Long receiptTime, String receiptXml, String receiptStatus, String receiptDetail) {
        this.declareOrderNo = declareOrderNo;
        this.type = type;
        this.expressName = expressName;
        this.expressNo = expressNo;
        this.outOrderNo = outOrderNo;
        this.requestXml = requestXml;
        this.receiptTime = receiptTime;
        this.receiptXml = receiptXml;
        this.receiptStatus = receiptStatus;
        this.receiptDetail = receiptDetail;
    }
}
