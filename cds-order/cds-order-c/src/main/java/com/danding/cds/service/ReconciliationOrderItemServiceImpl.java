package com.danding.cds.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.ReconciliationOrderItemService;
import com.danding.cds.c.api.service.ReconciliationOrderService;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.order.base.bean.dao.ReconciliationItemTrackLogDO;
import com.danding.cds.order.base.bean.dao.ReconciliationOrderItemDO;
import com.danding.cds.order.base.mapper.ReconciliationItemTrackLogMapper;
import com.danding.cds.order.base.mapper.ReconciliationOrderItemMapper;
import com.danding.cds.order.base.service.ReconciliationOrderItemBaseService;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.vo.req.ReconciliationBalanceAccountReqVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemMarkDealVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationItemSearch;
import com.danding.cds.v2.bean.vo.req.ReconciliationJDImportExcelVO;
import com.danding.cds.v2.enums.ReconciliationItemException;
import com.danding.cds.v2.enums.ReconciliationItemStatusEnums;
import com.danding.cds.v2.enums.ReconciliationOrderTypeEnums;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ReconciliationOrderItemServiceImpl implements ReconciliationOrderItemService {

    @Resource
    private ReconciliationOrderItemBaseService reconciliationOrderItemBaseService;

    @Resource
    private ReconciliationOrderItemMapper reconciliationOrderItemMapper;

    @Resource
    private ReconciliationItemTrackLogMapper reconciliationItemTrackLogMapper;

    @Resource
    private ReconciliationOrderService reconciliationOrderService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MessageSender messageSender;

    // 日志mq队列topic
    private final String RECONCILIATION_ITEM_TRACK_LOG_TOPIC = "ccs-reconciliation-item-track-log-topic";


    @Resource(name = "reconciliationThreadExecutor")
    private ThreadPoolTaskExecutor reconciliationThreadExecutor;

    @Override
    public List<ReconciliationOrderItemDTO> findByReconciliationSn(String reconciliationSn) {
        if (StrUtil.isBlank(reconciliationSn)) {
            return new ArrayList<>();
        }
        Example example = new Example(ReconciliationOrderItemDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("reconciliationSn", reconciliationSn);
        List<ReconciliationOrderItemDO> reconciliationOrderItemDOS = reconciliationOrderItemBaseService.selectByExample(example);
        return ConvertUtil.listConvert(reconciliationOrderItemDOS, ReconciliationOrderItemDTO.class);
    }

    @Override
    public List<ReconciliationOrderItemDTO> findByReconciliationSn(List<String> reconciliationSns) {
        if (CollUtil.isEmpty(reconciliationSns)) {
            return new ArrayList<>();
        }
        Example example = new Example(ReconciliationOrderItemDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andIn("reconciliationSn", reconciliationSns);
        List<ReconciliationOrderItemDO> reconciliationOrderItemDOS = reconciliationOrderItemBaseService.selectByExample(example);
        return ConvertUtil.listConvert(reconciliationOrderItemDOS, ReconciliationOrderItemDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<ReconciliationOrderItemDTO> paging(ReconciliationItemSearch search) {
        Example example = getPagingExample(search);
        List<ReconciliationOrderItemDO> list = new ArrayList<>();
        if (example != null) {
            list = reconciliationOrderItemMapper.selectByExample(example);
        }
        ListVO<ReconciliationOrderItemDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, ReconciliationOrderItemDTO.class));
        // 分页
        PageInfo<ReconciliationOrderItemDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example getPagingExample(ReconciliationItemSearch search) {
        Example example = new Example(ReconciliationOrderItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("status", ReconciliationItemStatusEnums.listPagingStatus());
        if (StrUtil.isNotBlank(search.getReconciliationSn())) {
            criteria.andEqualTo("reconciliationSn", search.getReconciliationSn());
        }
        if (CollUtil.isNotEmpty(search.getExceptionType())) {
            criteria.andIn("exceptionType", search.getExceptionType());
        }
        if (StrUtil.isNotBlank(search.getExceptionDesc())) {
            List<ReconciliationItemException.ExceptionDescEnums> exceptionDescEnumsList =
                    ReconciliationItemException.ExceptionDescEnums.getDescLike(search.getExceptionDesc());
            List<String> exceptionDescCodeList = exceptionDescEnumsList.stream()
                    .map(ReconciliationItemException.ExceptionDescEnums::getCode).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(exceptionDescCodeList)) {
                criteria.andIn("exceptionDesc", exceptionDescCodeList);
            } else {
                return null;
            }
        }
        if (StrUtil.isNotBlank(search.getRemark())) {
            criteria.andLike("remark", "%" + search.getRemark() + "%");
        }
        if (StrUtil.isNotBlank(search.getQueryType()) && StrUtil.isNotBlank(search.getQueryNo())) {
            criteria.andIn(search.getQueryType(), Splitter.on(",").splitToList(search.getQueryNo()));
        }
        if (StrUtil.isNotBlank(search.getOutboundCode())) {
            criteria.andIn("outboundCode", Splitter.on(",").splitToList(search.getOutboundCode()));
        }
        if (StrUtil.isNotBlank(search.getOutAreaProductId())) {
            criteria.andIn("outAreaProductId", Splitter.on(",").splitToList(search.getOutAreaProductId()));
        }
        if (StrUtil.isNotBlank(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        example.orderBy("id").desc();
        return example;
    }

    @Override
    public Map<String, Integer> statusCount(String reconciliationSn) {
        Map<String, Integer> result = new HashMap<>();
        List<String> statisticStatus = new ArrayList<>();
        statisticStatus.add("ALL");
        ReconciliationItemStatusEnums.listPagingStatus().forEach(e -> statisticStatus.add(e.getCode()));
        for (String status : statisticStatus) {
            if (ReconciliationItemStatusEnums.DISCARD.getCode().equalsIgnoreCase(status)
                    || ReconciliationItemStatusEnums.DEAL.getCode().equalsIgnoreCase(status)) {
                result.put(status, 0);
                continue;
            }
            Example example = new Example(ReconciliationOrderItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("deleted", false);
            if ("ALL".equalsIgnoreCase(status)) {
                criteria.andIn("status", ReconciliationItemStatusEnums.listPagingStatus());
            } else {
                criteria.andEqualTo("status", status);
            }
            if (StrUtil.isNotBlank(reconciliationSn)) {
                criteria.andEqualTo("reconciliationSn", reconciliationSn);
            }
            Integer count = reconciliationOrderItemBaseService.selectCountByExample(example);
            result.put(status, count);
        }
        return result;
    }

    @Override
    public void discard(List<Long> idList) {
        List<ReconciliationOrderItemDO> doList = reconciliationOrderItemBaseService.findById(idList);
        if (CollUtil.isEmpty(doList)) {
            throw new ArgsInvalidException("未找到对应对账订单");
        }
        if (doList.stream().anyMatch(e -> e.getStatus().equalsIgnoreCase(ReconciliationItemStatusEnums.DISCARD.getCode()))) {
            throw new ArgsInvalidException("作废状态无法作废");
        }
        discardCore(doList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void discardCore(List<ReconciliationOrderItemDO> doList) {
        if (CollUtil.isEmpty(doList)) {
            return;
        }
        List<Long> idList = doList.stream().map(ReconciliationOrderItemDO::getId).collect(Collectors.toList());
        updateBatchStatus(idList, ReconciliationItemStatusEnums.DISCARD.getCode());
        buildAndSendBatchTrackLog(idList, ReconciliationItemStatusEnums.DISCARD.getCode(), "作废对账订单");
        // 调整出区对账异常数量
        Map<String, List<ReconciliationOrderItemDO>> itemDOMap = doList.stream()
                .collect(Collectors.groupingBy(ReconciliationOrderItemDO::getReconciliationSn));
        itemDOMap.forEach((k, v) -> {
            int unmatchedCount = 0;
            int exceptionCount = 0;
            int changeNoDifferentCount = 0;
            for (ReconciliationOrderItemDO i : v) {
                if (Objects.equals(i.getStatus(), ReconciliationItemStatusEnums.DEAL.getCode())
                        || Objects.equals(i.getStatus(), ReconciliationItemStatusEnums.NORMAL.getCode())) {
                    changeNoDifferentCount--;
                } else {
                    if (StringUtil.isNotBlank(i.getExceptionDesc())) {
                        ReconciliationItemException.ExceptionDescEnums exceptionDescEnums =
                                ReconciliationItemException.ExceptionDescEnums.getEnums(i.getExceptionDesc());
                        if (ReconciliationItemException.ExceptionDescEnums.listUnmatched().contains(exceptionDescEnums)) {
                            unmatchedCount--;
                        } else {
                            exceptionCount--;
                        }
                    }
                }
            }
            reconciliationOrderService.adjustCount(k, unmatchedCount, exceptionCount, changeNoDifferentCount, true);
        });
    }

    private void updateBatchStatus(List<Long> idList, String status) {
        int step = 2000;
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i += step) {
            List<Long> subList = idList.subList(i, Math.min(i + step, idList.size()));
            Runnable runnable = new BatchUpdateTask(subList, status);
            futureList.add(CompletableFuture.runAsync(runnable, reconciliationThreadExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("批量更新状态CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("对账订单处理异常订单CompletableFuture结果异常" + e.getMessage(), e);
        }
    }

    class BatchUpdateTask extends TraceDataRunnable {
        private final List<Long> subList;
        private final String status;

        public BatchUpdateTask(List<Long> subList, String status) {
            this.subList = subList;
            this.status = status;
        }

        @Override
        protected void proxy() {
            Example example = new Example(ReconciliationOrderItemDO.class);
            example.createCriteria().andIn("id", subList);
            ReconciliationOrderItemDO updateDO = new ReconciliationOrderItemDO();
            updateDO.setStatus(status);
            reconciliationOrderItemBaseService.updateByExampleSelective(updateDO, example);
        }
    }

    @Override
    public void discardByReconciliationSn(String sn) {
        if (StrUtil.isEmpty(sn)) {
            return;
        }
        Example example = new Example(ReconciliationOrderItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("reconciliationSn", sn);
        criteria.andEqualTo("deleted", false);
        List<ReconciliationOrderItemDO> reconciliationOrderItemDOS = reconciliationOrderItemBaseService.selectByExample(example);
        if (CollUtil.isNotEmpty(reconciliationOrderItemDOS)) {
            discardCore(reconciliationOrderItemDOS);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markDeal(ReconciliationItemMarkDealVO dealVO) {
        List<ReconciliationOrderItemDO> doList = reconciliationOrderItemBaseService.findById(dealVO.getIdList());
        if (CollUtil.isEmpty(doList)) {
            throw new ArgsInvalidException("未找到对应对账订单");
        }
        List<String> notAllowStatus = Arrays.asList(ReconciliationItemStatusEnums.DISCARD.getCode(),
                ReconciliationItemStatusEnums.DEAL.getCode(),
                ReconciliationItemStatusEnums.NORMAL.getCode());
        if (doList.stream().anyMatch(e -> notAllowStatus.contains(e.getStatus()))) {
            throw new ArgsInvalidException("已解决、作废状态无法标记解决");
        }
        ReconciliationOrderItemDO updateDO = new ReconciliationOrderItemDO();
        Example example = new Example(ReconciliationOrderItemDO.class);
        example.createCriteria().andIn("id", dealVO.getIdList());
        updateDO.setStatus(ReconciliationItemStatusEnums.DEAL.getCode());
        updateDO.setRemark(dealVO.getRemark());
        reconciliationOrderItemBaseService.updateByExampleSelective(updateDO, example);
        buildAndSendBatchTrackLog(dealVO.getIdList(), ReconciliationItemStatusEnums.DEAL.getCode(), "标记解决");
        // 调整出区对账异常数量
        Map<String, List<ReconciliationOrderItemDO>> itemDOMap = doList.stream()
                .collect(Collectors.groupingBy(ReconciliationOrderItemDO::getReconciliationSn));
        itemDOMap.forEach((k, v) -> {
            int unmatchedCount = 0;
            int differentCount = 0;
            int noDifferentCount = v.size();
            for (ReconciliationOrderItemDO i : v) {
                ReconciliationItemException.ExceptionDescEnums exceptionDescEnums =
                        ReconciliationItemException.ExceptionDescEnums.getEnums(i.getExceptionDesc());
                if (ReconciliationItemException.ExceptionDescEnums.listUnmatched().contains(exceptionDescEnums)) {
                    unmatchedCount--;
                } else {
                    differentCount--;
                }
            }
            reconciliationOrderService.adjustCount(k, unmatchedCount, differentCount, noDifferentCount, true);
        });
    }

    @Override
    public List<ReconciliationItemTrackLogDTO> listTrackLog(Long id) {
        List<ReconciliationItemTrackLogDO> trackLogDOS = reconciliationOrderItemBaseService.findLogByOrderId(id);
        return trackLogDOS.stream().map(i -> {
            ReconciliationItemTrackLogDTO dto = new ReconciliationItemTrackLogDTO();
            BeanUtils.copyProperties(i, dto);
            ReconciliationItemStatusEnums statusEnums = ReconciliationItemStatusEnums.getEnums(i.getStatus());
            dto.setStatusDesc(statusEnums == null ? "" : statusEnums.getDesc());
            return dto;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional
    public void excelImport(ReconciliationOrderImportInfoDTO dto) {
        if (CollUtil.isEmpty(dto.getExcelVOList())) {
            return;
        }
        log.info("reconciliationItemService excelImport start sn={},size={}", dto.getReconciliationSn(), dto.getExcelVOList().size());
        List<ReconciliationJDImportExcelVO> excelVOList = dto.getExcelVOList();
        int step = 500;
        for (int i = 0; i < excelVOList.size(); i += step) {
            List<ReconciliationJDImportExcelVO> subList = excelVOList.subList(i, Math.min(i + step, excelVOList.size()));
            List<ReconciliationOrderItemDO> insertDOList = new ArrayList<>();
            for (ReconciliationJDImportExcelVO excelVO : subList) {
                ReconciliationOrderItemDO insertDO = new ReconciliationOrderItemDO();
                insertDO.setReconciliationSn(dto.getReconciliationSn());
                insertDO.setDeclareOrderNo(excelVO.getOrderNumber());
                insertDO.setLogisticsNo(excelVO.getWaybillNumber());
                insertDO.setOutboundCode(excelVO.getProductCode());
                insertDO.setOutboundCount(excelVO.getCheckQuantity());
                insertDO.setStatus(ReconciliationItemStatusEnums.INIT.getCode());
                insertDO.setAreaCompanyId(dto.getAreaCompanyId());
                insertDO.setOwnerName(excelVO.getCargoOwnerName());
                insertDO.setOwnerCode(excelVO.getCargoOwnerCode());
                if (StrUtil.isNotBlank(excelVO.getCargoOwnerCode())) {
                    if ("1".equalsIgnoreCase(excelVO.getCargoOwnerCode())) {
                        insertDO.setOrderType(ReconciliationOrderTypeEnums.JD_DIRECT.getCode());
                    } else if (excelVO.getCargoOwnerCode().startsWith("EBU")) {
                        insertDO.setOrderType(ReconciliationOrderTypeEnums.JD_POP.getCode());
                        insertDO.setDeclareOrderNo(excelVO.getMerchantOrderNumber());
                    }
                }
                UserUtils.setCommonData(insertDO);
                insertDO.setExtraJson(JSON.toJSONString(excelVO));
                insertDOList.add(insertDO);
            }
            reconciliationOrderItemMapper.insertList(insertDOList);
        }
        log.info("reconciliationItemService excelImport end sn={},size={}", dto.getReconciliationSn(), dto.getExcelVOList().size());
    }

    @Override
    @Transactional
    public void balanceAccount(ReconciliationBalanceAccountReqVO reqVO) {
        log.info("数据中台 对账差异返回结果 - {}", JSON.toJSONString(reqVO));
        if (StrUtil.isEmpty(reqVO.getReconciliationSn())) {
            throw new ArgsInvalidException("对账单ID不能为空");
        }
        List<String> reconciliationOrderSnList = Splitter.on(",").splitToList(reqVO.getReconciliationSn());
        // 更新出区对账&对账订单
        Map<Long, ReconciliationBalanceAccountReqVO.Item> exceptionMap = new HashMap<>();
        Map<String, List<ReconciliationOrderItemDO>> exceptionItemDOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(reqVO.getItemList())) {
            exceptionMap = reqVO.getItemList().stream()
                    .collect(Collectors.toMap(ReconciliationBalanceAccountReqVO.Item::getId, Function.identity(), (v1, v2) -> v1));
            List<Long> idList = new ArrayList<>(exceptionMap.keySet());
            List<ReconciliationOrderItemDO> exceptionItemDOList = reconciliationOrderItemBaseService.findById(idList);
            exceptionItemDOMap = exceptionItemDOList.stream()
                    .collect(Collectors.groupingBy(ReconciliationOrderItemDO::getReconciliationSn));
        }

        // 处理异常订单
        for (String sn : reconciliationOrderSnList) {
            log.info("step1 balanceAccount 处理异常订单 start sn={}", sn);
            List<ReconciliationOrderItemDO> itemDTOS = exceptionItemDOMap.getOrDefault(sn, Collections.emptyList());
            ReconciliationOrderDTO reconciliationOrderDTO = reconciliationOrderService.findBySn(sn);
            Integer totalCount = reconciliationOrderDTO.getTotalOrderCount();
            AtomicInteger unmatchedCount = new AtomicInteger(0);
            AtomicInteger exceptionCount = new AtomicInteger(0);
            Integer noDifferentCount = 0;

            // 异常订单处理
            List<Long> exceptionIdList = itemDTOS.stream().map(ReconciliationOrderItemDO::getId).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(itemDTOS)) {
                List<CompletableFuture<Void>> futureList = new ArrayList<>();
                int step = 500;
                for (int i = 0; i < itemDTOS.size(); i += step) {
                    List<ReconciliationOrderItemDO> subList = itemDTOS.subList(i, Math.min(i + step, itemDTOS.size()));
                    Runnable runnable = new BalanceAccountTask(exceptionCount, unmatchedCount, exceptionMap, subList);
                    futureList.add(CompletableFuture.runAsync(runnable, reconciliationThreadExecutor));
                }
                try {
                    CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
                } catch (Exception e) {
                    log.error("对账订单处理异常订单CompletableFuture结果异常：{}", e.getMessage(), e);
                    throw new RuntimeException("对账订单处理异常订单CompletableFuture结果异常" + e.getMessage(), e);
                }
            }

            // 更新正常订单状态
            log.info("step2 balanceAccount 更新正常订单状态 start sn={}", sn);
            Example example = new Example(ReconciliationOrderItemDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("reconciliationSn", sn)
                    .andNotIn("status", Arrays.asList(ReconciliationItemStatusEnums.DISCARD.getCode(), ReconciliationItemStatusEnums.DEAL.getCode()));
            example.selectProperties("id");
            List<ReconciliationOrderItemDO> reconciliationOrderItemDOS = reconciliationOrderItemBaseService.selectByExample(example);
            List<Long> waitProcessIdList = reconciliationOrderItemDOS.stream().map(ReconciliationOrderItemDO::getId).collect(Collectors.toList());

            List<Long> normalIdList = waitProcessIdList.stream().filter(id -> !exceptionIdList.contains(id)).collect(Collectors.toList());
            this.updateBatchStatus(normalIdList, ReconciliationItemStatusEnums.NORMAL.getCode());

            // 正常订单批量插入日志
            log.info("step3 balanceAccount 正常订单批量插入日志 start sn={}", sn);
            if (CollUtil.isNotEmpty(normalIdList)) {
                buildAndSendBatchTrackLog(normalIdList, ReconciliationItemStatusEnums.NORMAL.getCode(), "订单正常");
            }

            // 统计正常订单和已解决订单数量的订单数量
            log.info("step4 balanceAccount 统计正常订单和已解决订单数量的订单数量 start sn={}", sn);
            Example noDiffExample = new Example(ReconciliationOrderItemDO.class);
            noDiffExample.createCriteria().andEqualTo("reconciliationSn", sn)
                    .andIn("status", Arrays.asList(ReconciliationItemStatusEnums.NORMAL.getCode(), ReconciliationItemStatusEnums.DEAL.getCode()));
            noDifferentCount = reconciliationOrderItemBaseService.selectCountByExample(noDiffExample);

            // 出区对账单 后置处理
            log.info("step5 balanceAccount 出区对账单 后置处理 start sn={}", sn);
            reconciliationOrderService.balanceAccountPostProcess(
                    ReconciliationImportResult.isSuccess(
                            sn, totalCount, exceptionCount.intValue(), noDifferentCount, unmatchedCount.intValue()));
        }
        log.info("step5 balanceAccount end");

    }

    class BalanceAccountTask extends TraceDataRunnable {

        private AtomicInteger exceptionCount;
        private AtomicInteger unmatchedCount;
        private Map<Long, ReconciliationBalanceAccountReqVO.Item> exceptionMap;
        private List<ReconciliationOrderItemDO> itemDTOS;

        public BalanceAccountTask(AtomicInteger exceptionCount, AtomicInteger unmatchedCount,
                                  Map<Long, ReconciliationBalanceAccountReqVO.Item> exceptionMap, List<ReconciliationOrderItemDO> itemDTOS) {
            this.unmatchedCount = unmatchedCount;
            this.exceptionMap = exceptionMap;
            this.exceptionCount = exceptionCount;
            this.itemDTOS = itemDTOS;
        }

        @Override
        protected void proxy() {
            List<ReconciliationItemException.ExceptionDescEnums> unmatchedDescEnumList = Arrays.asList(
                    ReconciliationItemException.ExceptionDescEnums.JD_DIRECT_OUT_ORDER_NOT_EXIST,
                    ReconciliationItemException.ExceptionDescEnums.JD_POP_ORDER_OUT_ORDER_NOT_EXIST);

            List<ReconciliationOrderItemDO> updateDOList = new ArrayList<>();
            List<ReconciliationItemTrackLogDTO> trackLogDTOList = new ArrayList<>();
            for (ReconciliationOrderItemDO itemDTO : itemDTOS) {
                if (itemDTO.getStatus().equals(ReconciliationItemStatusEnums.DEAL.getCode())
                        || itemDTO.getStatus().equals(ReconciliationItemStatusEnums.DISCARD.getCode())) {
                    continue;
                }
                ReconciliationOrderItemDO updateDO = new ReconciliationOrderItemDO();
                BeanUtils.copyProperties(itemDTO, updateDO);
                updateDO.setId(itemDTO.getId());
                String logInfo = "";
                ReconciliationBalanceAccountReqVO.Item reqItem = exceptionMap.get(itemDTO.getId());
                updateDO.setOutAreaProductId(reqItem.getOutAreaProductId());
                updateDO.setOutAreaCount(reqItem.getOutAreaCount());
                if (StrUtil.isNotBlank(reqItem.getExceptionDesc())) {
                    updateDO.setExceptionDesc(reqItem.getExceptionDesc());
                    ReconciliationItemException.ExceptionDescEnums exceptionDescEnums =
                            ReconciliationItemException.ExceptionDescEnums.getEnums(reqItem.getExceptionDesc());
                    if (Objects.nonNull(exceptionDescEnums)) {
                        updateDO.setExceptionType(exceptionDescEnums.getTypeEnums().getCode());
                        if (unmatchedDescEnumList.contains(exceptionDescEnums)) {
                            unmatchedCount.getAndIncrement();
                        } else {
                            exceptionCount.getAndIncrement();
                        }
                        logInfo = "订单异常：" + exceptionDescEnums.getDesc();
                    }
                    updateDO.setStatus(ReconciliationItemStatusEnums.EXCEPTION.getCode());
                } else {
                    updateDO.setStatus(ReconciliationItemStatusEnums.NORMAL.getCode());
                    logInfo = "订单正常";
                }
                UserUtils.setUpdateBy(updateDO);
                updateDO.setUpdateTime(new Date());
                updateDOList.add(updateDO);
                trackLogDTOList.add(buildTrackLogDTO(itemDTO.getId(), updateDO.getStatus(), logInfo));
            }
            reconciliationOrderItemMapper.batchUpdateByPrimaryKey(updateDOList);
            saveBatchLog(trackLogDTOList);
        }
    }

    @Override
    public List<String> findExistLogisticsNo(List<String> logisticsNoList) {
        Vector<String> result = new Vector<>();
        List<List<String>> partitionedLists = new ArrayList<>();
        int step = 2000;
        for (int i = 0; i < logisticsNoList.size(); i += step) {
            partitionedLists.add(new ArrayList<>(logisticsNoList.subList(i, Math.min(i + step, logisticsNoList.size()))));
        }
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (List<String> logisticsNos : partitionedLists) {
            Runnable runnable = new FindByLogisticsNoTask(logisticsNos, result);
            futureList.add(CompletableFuture.runAsync(runnable, reconciliationThreadExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("通过运单查询对账订单CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("通过运单查询对账订单CompletableFuture结果异常" + e.getMessage(), e);
        }
        if (CollUtil.isEmpty(result)) {
            return new ArrayList<>();
        }
        return Arrays.asList(result.toArray(new String[0]));
    }

    class FindByLogisticsNoTask extends TraceDataRunnable {

        private final List<String> logisticsNos;

        private final Vector<String> result;

        public FindByLogisticsNoTask(List<String> logisticsNos, Vector<String> result) {
            super();
            this.logisticsNos = logisticsNos;
            this.result = result;
        }

        @Override
        public void proxy() {
            Example example = new Example(ReconciliationOrderItemDO.class);
            example.createCriteria()
                    .andIn("logisticsNo", logisticsNos)
                    .andNotEqualTo("status", ReconciliationItemStatusEnums.DISCARD.getCode())
                    .andEqualTo("deleted", false);
            example.selectProperties("logisticsNo", "outboundCode");
            List<ReconciliationOrderItemDO> doList = reconciliationOrderItemBaseService.selectByExample(example);
            if (CollUtil.isNotEmpty(doList)) {
                result.addAll(doList.stream().map(i -> i.getLogisticsNo() + "_" + i.getOutboundCode()).collect(Collectors.toList()));
            }
        }
    }

    @Override
    public void saveRefreshLog(List<String> snList) {
        if (CollUtil.isEmpty(snList)) {
            return;
        }
        Example example = new Example(ReconciliationOrderItemDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andIn("reconciliationSn", snList);
        example.selectProperties("id", "status");
        List<ReconciliationOrderItemDO> reconciliationOrderItemDOS = reconciliationOrderItemBaseService.selectByExample(example);
        Map<String, List<ReconciliationOrderItemDO>> statusGroup = reconciliationOrderItemDOS.stream()
                .collect(Collectors.groupingBy(ReconciliationOrderItemDO::getStatus));
        statusGroup.forEach((status, itemDOList) -> {
            List<Long> idList = itemDOList.stream().map(ReconciliationOrderItemDO::getId).collect(Collectors.toList());
            buildAndSendBatchTrackLog(idList, status, "刷新对账订单");
        });
    }

    private void buildAndSendBatchTrackLog(List<Long> reconciliationItemIdList, String status, String logInfo) {
        this.buildAndSendBatchTrackLog(reconciliationItemIdList, status, logInfo, UserUtils.getUserRealName());
    }

    private void buildAndSendBatchTrackLog(List<Long> reconciliationItemIdList, String status, String logInfo, String operator) {
        int step = 2000;
        for (int i = 0; i < reconciliationItemIdList.size(); i += step) {
            List<Long> subList = reconciliationItemIdList.subList(i, Math.min(i + step, reconciliationItemIdList.size()));
            ReconciliationItemTrackLogDTO trackLogDTO = this.buildTrackLogDTO(subList, status, logInfo, operator);
            messageSender.sendMsg(JSON.toJSONString(trackLogDTO), RECONCILIATION_ITEM_TRACK_LOG_TOPIC);
        }
    }

    private void buildAndSendTrackLog(Long reconciliationItemId, String status, String logInfo) {
        this.buildAndSendTrackLog(reconciliationItemId, status, logInfo, UserUtils.getUserRealName());
    }

    private void buildAndSendTrackLog(Long reconciliationItemId, String status, String logInfo, String operator) {
        ReconciliationItemTrackLogDTO trackLogDTO = this.buildTrackLogDTO(reconciliationItemId, status, logInfo, operator);
        messageSender.sendMsg(JSON.toJSONString(trackLogDTO), RECONCILIATION_ITEM_TRACK_LOG_TOPIC);
    }

    private ReconciliationItemTrackLogDTO buildTrackLogDTO(Long reconciliationItemId, String status, String logInfo) {
        return this.buildTrackLogDTO(reconciliationItemId, status, logInfo, UserUtils.getUserRealName());
    }

    private ReconciliationItemTrackLogDTO buildTrackLogDTO(Long reconciliationItemId, String status, String logInfo, String operator) {
        ReconciliationItemTrackLogDTO trackLogDTO = new ReconciliationItemTrackLogDTO();
        trackLogDTO.setReconciliationItemId(reconciliationItemId);
        trackLogDTO.setStatus(status);
        trackLogDTO.setLogInfo(logInfo);
        trackLogDTO.setOperator(StrUtil.isEmpty(operator) ? UserUtils.getUserRealName() : operator);
        return trackLogDTO;
    }

    private ReconciliationItemTrackLogDTO buildTrackLogDTO(List<Long> reconciliationItemId, String status, String logInfo, String operator) {
        ReconciliationItemTrackLogDTO trackLogDTO = new ReconciliationItemTrackLogDTO();
        trackLogDTO.setReconciliationItemIdList(reconciliationItemId);
        trackLogDTO.setStatus(status);
        trackLogDTO.setLogInfo(logInfo);
        trackLogDTO.setOperator(StrUtil.isEmpty(operator) ? UserUtils.getUserRealName() : operator);
        return trackLogDTO;
    }

    @Override
    public void saveLog(ReconciliationItemTrackLogDTO dto) {
        saveLog(dto.getReconciliationItemId(), dto.getStatus(), dto.getLogInfo(), dto.getOperator());
    }

    public void saveLog(Long reconciliationItemId, String status, String logInfo) {
        this.saveLog(reconciliationItemId, status, logInfo, null);
    }

    public void saveLog(Long reconciliationItemId, String status, String logInfo, String operator) {
        ReconciliationItemTrackLogDO logDO = new ReconciliationItemTrackLogDO();
        logDO.setReconciliationItemId(reconciliationItemId);
        logDO.setStatus(status);
        logDO.setLogInfo(logInfo);
        logDO.setOperator(StrUtil.isNotBlank(operator) ? operator : UserUtils.getUserRealName());
        reconciliationOrderItemBaseService.insertLogSelective(logDO);
    }

    @Override
    public void saveBatchLog(List<Long> reconciliationItemIdList, String status, String logInfo, String operator) {
        List<ReconciliationItemTrackLogDO> insertList = new ArrayList<>();
        int step = 500;
        for (int i = 0; i < reconciliationItemIdList.size(); i += step) {
            List<Long> subList = reconciliationItemIdList.subList(i, Math.min(i + step, reconciliationItemIdList.size()));
            for (Long reconciliationItemId : subList) {
                ReconciliationItemTrackLogDO logDO = new ReconciliationItemTrackLogDO();
                logDO.setReconciliationItemId(reconciliationItemId);
                logDO.setStatus(status);
                logDO.setLogInfo(logInfo);
                logDO.setOperator(StrUtil.isNotBlank(operator) ? operator : UserUtils.getUserRealName());
                UserUtils.setCommonData(logDO);
                insertList.add(logDO);
            }
            reconciliationItemTrackLogMapper.insertList(insertList);
        }
    }

    @Override
    public void saveBatchLog(List<ReconciliationItemTrackLogDTO> logDTOList) {
        int step = 500;
        for (int i = 0; i < logDTOList.size(); i += step) {
            List<ReconciliationItemTrackLogDTO> subList = logDTOList.subList(i, Math.min(i + step, logDTOList.size()));
            List<ReconciliationItemTrackLogDO> insertDOList = new ArrayList<>();
            for (ReconciliationItemTrackLogDTO item : subList) {
                ReconciliationItemTrackLogDO logDO = new ReconciliationItemTrackLogDO();
                BeanUtils.copyProperties(item, logDO);
                UserUtils.setCommonData(logDO);
                insertDOList.add(logDO);
            }
            reconciliationItemTrackLogMapper.insertList(insertDOList);
        }
    }
}
