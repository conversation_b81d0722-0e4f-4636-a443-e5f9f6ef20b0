package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.dto.OrderSubmitItemDto;
import com.danding.cds.bean.dto.SubmitGoodsInfoAndBookItemDto;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class FourCategoryTocCheckService {

    @Resource
    private BaseDataService baseDataService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;


    /**
     * 海关四类商品账册库存校验
     * 四类商品判断依据: 原产国为美国 或者 (原产国为加拿大和某些hs)的则认为是四类商品.
     * <p>
     * 首先匹配出: 路径配置下的账册 + 商品料号 + 最大序号 的账册库存, 根据账册库存来源标识判断是否为四类商品.
     * <p>
     * 情况一: 全部为四类商品(需要清理历史库存: 四类商品在普通账册库存)
     * 0. 如果路径配置下的账册为专用账册, 直接走常规申报校验逻辑.
     * 1. 如果路径配置下的账册为普通账册:
     * 剩余账册库存与下单的商品数量比较:
     * 1.1 (全部)四类商品剩余账册库存 > 下单的商品数量, 则通过(消耗历史库存)
     * 1.2 (任意)四类商品剩余账册库存 < 下单的商品数量, 则不通过. 并切换账册为该订单对应实体仓配置下的专用账册, 然后直接走常规申报校验逻辑.
     * 情况二: 全部为非四类商品
     * 0. 不用关心路径配置下的账册为专用账册或普通账册, 直接走常规申报校验逻辑.
     * 情况三: 四类商品和非四类商品混合
     * 0. 如果路径配置下的账册为专用账册
     * 0.1 直接走常规申报校验逻辑
     * 1. 如果路径配置下的账册为普通账册
     * 1.1 剩余账册库存与下单的商品数量比较:
     * 1.1.1 四类商品剩余账册库存 > 下单的商品数量, 则通过(消耗历史库存)
     * 1.1.2 四类商品剩余账册库存 < 下单的商品数量, 则不通过. 报错:"普通仓包含四类和非四类商品，请拆单申报"
     *
     * @param submit                 订单提交信息
     * @param customsBookItemDTOList 海关账册库存于订单信息的信息
     * @return Tuple<String, Boolean> 账册id, 是否切换账册
     */
    public Tuple<Long, Boolean> checkOrExchangeCustomsBook(OrderSubmitDto submit, List<SubmitGoodsInfoAndBookItemDto> customsBookItemDTOList) {

        log.info("checkOrExchangeCustomsBook customsBookItemDTOList={}", JSON.toJSONString(customsBookItemDTOList));
        if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
            return new Tuple<>(null, false);
        }

        List<OrderSubmitItemDto> submitItemDtoList = submit.getItemList();

        Set<String> submitBookItemGoodsSourceSet = customsBookItemDTOList.stream().map(SubmitGoodsInfoAndBookItemDto::getGoodsSource).collect(Collectors.toSet());
        Set<String> targetCategoryGoodsEnum = new HashSet() {{
            add(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode());
            add(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode());
        }};

        // 获取提交的订单信息中四类商品账册库存
        List<SubmitGoodsInfoAndBookItemDto> fourcustomsBookItemDTOList = customsBookItemDTOList.stream().filter(item -> {
            String goodsSource = item.getGoodsSource();
            return Objects.equals(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode(), goodsSource);
        }).collect(Collectors.toList());

        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(routeDTO.getCustomsBookId());
        log.info("路径上配置的账册id: {} ,账册编号为: {}", routeDTO.getCustomsBookId(), customsBookDTO.getBookNo());
        // 校验逻辑: 四类商品和非四类商品混合
        Boolean fourCategoryJoin = targetCategoryGoodsEnum.containsAll(submitBookItemGoodsSourceSet);
        // 默认
        Tuple<Long, Boolean> defaultTuple = new Tuple<>(routeDTO.getCustomsBookId(), false);
        // 全部为四类商品
        if (submitBookItemGoodsSourceSet.stream().allMatch(goodsSource -> Objects.equals(goodsSource, GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode()))) {
            log.info("全部为四类商品");
            //0. 如果路径配置下的账册为专用账册, 直接走常规申报校验逻辑.
            if (Objects.equals(customsBookDTO.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {
                return defaultTuple;
            } else {
                /**
                 * 1. 如果路径配置下的账册为普通账册:
                 *      剩余账册库存与下单的商品数量比较:
                 *      1.1 (全部)四类商品剩余账册库存 > 下单的商品数量, 则通过(消耗历史库存)
                 *      1.2 (任意)四类商品剩余账册库存 < 下单的商品数量, 则不通过. 并切换账册为该订单对应实体仓配置下的专用账册, 然后直接走常规申报校验逻辑.
                 */
                List<SubmitGoodsInfoAndBookItemDto> bookItemNumLtGoodsItemNum = bookItemNumLtGoodsItemNum(fourcustomsBookItemDTOList);
                // 剩余账册库存小于下单的商品数量,满足切换条件:
                if (!CollectionUtils.isEmpty(bookItemNumLtGoodsItemNum)) {
                    if (CustomsBookTagEnums.contains(customsBookDTO.getBookTag(), CustomsBookTagEnums.AUTO_SWITCH_SPECIAL_BOOKS)) {
                        log.info("满足切换专用账册条件并且申报路径上配置的账册含有[进口-保税账册(专用)]标签, 直接切换: 申报路径上配置的账册:{}", customsBookDTO.getBookNo());
                        // 不通过, 获取实体仓对应的专用账册
                        Long specialBooksId = getSpecialBooksIdByErpWarehouse(submit.getErpPhyWarehouseSn());
                        defaultTuple.setF(specialBooksId);
                        defaultTuple.setS(true);
                    } else {
                        log.info("满足切换专用账册条件但是申报路径上配置的账册没有[进口-保税账册(专用)]标签: 申报路径上配置的账册:{}", customsBookDTO.getBookNo());
                        String msg = bookItemNumLtGoodsItemNum.stream()
                                .map(z -> {
                                    return "四类商品:" + z.getProductId() + "账册库存数量不足";
                                }).collect(Collectors.joining(";"));
                        throw new ArgsErrorException(msg);
                    }
                }
            }
            return defaultTuple;
        } else if (submitBookItemGoodsSourceSet.stream().allMatch(goodsSource -> Objects.equals(goodsSource, GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode()))) {
            log.info("全部为非四类商品");
            // 全部为非四类商品
            return defaultTuple;
        } else if (fourCategoryJoin) {
            log.info("四类商品和非四类商品混合");
            // 四类商品和非四类商品混合
            if (Objects.equals(customsBookDTO.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {
                return defaultTuple;
            } else {
                boolean isPass = compareNumOfBookItemAndSubmitItem(fourcustomsBookItemDTOList);
                if (!isPass) {
                    // 不通过, 获取实体仓对应的专用账册
                    throw new ArgsErrorException("四类商品账册库存不足且普通仓包含四类和非四类商品，请拆单申报");
                }
            }
            return defaultTuple;
        } else {
            throw new ArgsErrorException("订单包含来源标识除四类和非四类以外的商品类型，请联系小二处理后重试");
        }
    }

    public boolean compareNumOfBookItemAndSubmitItem(List<SubmitGoodsInfoAndBookItemDto> fourcustomsBookItemDTOList) {
        boolean isPass = true;
        List<SubmitGoodsInfoAndBookItemDto> bookItemNumLtGoodsItemNum = bookItemNumLtGoodsItemNum(fourcustomsBookItemDTOList);
        if (!CollectionUtils.isEmpty(bookItemNumLtGoodsItemNum)) {
            isPass = false;
        }
        return isPass;
    }

    public List<SubmitGoodsInfoAndBookItemDto> bookItemNumLtGoodsItemNum(List<SubmitGoodsInfoAndBookItemDto> fourcustomsBookItemDTOList) {

        List<SubmitGoodsInfoAndBookItemDto> bookItemDtoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fourcustomsBookItemDTOList)) {
            return bookItemDtoList;
        }

        for (SubmitGoodsInfoAndBookItemDto customsBookItem : fourcustomsBookItemDTOList) {
            Integer num = customsBookItem.getAccountNum();
            Integer goodsItemCount = customsBookItem.getGoodsItemCount();
            if (goodsItemCount == null) {
                continue;
            }
            if (num == null || num < goodsItemCount) {
                bookItemDtoList.add(customsBookItem);
            }
        }
        return bookItemDtoList;
    }


    /**
     * 根据ERP物理仓编码获取专用账册ID
     *
     * @param erpPhyWarehouseSn ERP物理
     * @return 专用账册ID
     */
    public Long getSpecialBooksIdByErpWarehouse(String erpPhyWarehouseSn) {

        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(erpPhyWarehouseSn);
        if (CollectionUtils.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsErrorException("订单下发中的实体仓编码在清关实体仓配置中不存在");
        }
        String erpWarehouseName = entityWarehouseDTOList.get(0).getErpWarehouseName();
        // 获取出专用账册
        List<Long> customsBookIds = entityWarehouseDTOList.stream().filter(item -> {
                    Integer warehouseTag = item.getWarehouseTag();
                    return Objects.equals(warehouseTag, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG.getCode());
                }).map(EntityWarehouseDTO::getCustomsBookId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(customsBookIds) || customsBookIds.size() > 1) {
            throw new ArgsErrorException("四类商品账册库存不足需切换专用账册，实体仓:" + erpWarehouseName + ",未配置专用账册，请联系运维处理");
        }
        return customsBookIds.get(0);
    }
}
