package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.TenantIdFindService;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.config.AutoOrderConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2022-12-05 11:17
 */
@Slf4j
@Service
@RefreshScope
public class TenantIdFindServiceImpl implements TenantIdFindService {

    private static final String INDICES = "ccs_declare_order";

    private static final String TYPE = "order";

    @Resource(name = "elasticsearchRestTemplate")
    private ElasticsearchRestTemplate restTemplate;

    @DubboReference
    private CompanyService companyService;

    @Value("${saas.defaultFlag:false}")
    private boolean defaultFlag;

    @Value("${saas.defaultValue:1001}")
    private Long defaultValue;

    @Override
    public Long getTenantIdByInventory(String inventorySn) {
        Map<String, String> queryMap = new HashMap<>();
        queryMap.put("sn.keyword", inventorySn);
        Long tenantId = getTenantId(queryMap, AutoOrderConfig.customsInventoryIndex + "_*", "customsSingleInventory");
        if (tenantId != null) {
            return tenantId;
        } else {
            if (defaultFlag) {
                log.info("TenantIdFindRpcImpl getTenantId为空 取defaultValue={}", defaultValue);
                return defaultValue;
            }
        }
        throw new IllegalArgumentException("ES查询租户ID,请使用@TenantHttpFiled注解指定要查询的字段");
    }

    @Override
    public Long getTenantId(String ebpCode, String orderNo, String logisticNo, String invtNo) {
        if (Objects.isNull(orderNo) && Objects.isNull(logisticNo) && Objects.isNull(invtNo)) {
            return null;
        }
        Map<String, String> queryParamMap = new HashMap<>();
//        if (Objects.nonNull(ebpCode)) {
//            CompanyDTO companyDTO = companyService.findByCode(ebpCode);
//            if (Objects.nonNull(companyDTO.getId())) {
//                queryParamMap.put("ebpId", companyDTO.getId().toString());
//            }
//        }
        if (Objects.nonNull(orderNo)) {
            queryParamMap.put("declareOrderNo", orderNo);
        }
        if (Objects.nonNull(logisticNo)) {
            queryParamMap.put("logisticsNo", logisticNo);
        }
        if (Objects.nonNull(invtNo)) {
            queryParamMap.put("inventoryNo", invtNo);
        }
        return this.getTenantId(queryParamMap);
    }

    @Override
    public Long getTenantId(Map<String, String> queryMap) {
        Long tenantId = getTenantId(queryMap, INDICES, TYPE);
        if (tenantId != null) {
            return tenantId;
        } else {
            if (defaultFlag) {
                log.info("TenantIdFindRpcImpl getTenantId为空 取defaultValue={}", defaultValue);
                return defaultValue;
            }
        }
        throw new IllegalArgumentException("ES查询租户ID,请使用@TenantHttpFiled注解指定要查询的字段");
    }

    @Override
    public Long getTenantId(Map<String, String> queryMap, String indices, String type) {
        log.info("TenantIdFindRpcImpl getTenantId queryMap={} indices={} type={}", JSON.toJSONString(queryMap), indices, type);
        if (!CollectionUtils.isEmpty(queryMap)) {
            BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
            queryMap.forEach((k, v) -> boolQuery.must(QueryBuilders.termQuery(k, v)));

            SearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withQuery(boolQuery)
                    .withIndices(indices)
                    .withTypes(type)
                    // 只查询一个
                    .withPageable(PageRequest.of(0, 1))
                    .build();

            Map<String, Object> resultMap = restTemplate.query(searchQuery, response -> {
                SearchHit[] hits = response.getHits().getHits();
                if (hits == null || hits.length == 0) {
                    return new HashMap<>();
                }
                return hits[0].getSourceAsMap();
            });
            log.info("TenantIdFindRpcImpl getTenantId res={}", JSON.toJSONString(resultMap));
            Object tenantId = resultMap.get("tenantryId");
            if (Objects.isNull(tenantId)) {
                if (defaultFlag) {
                    log.info("TenantIdFindRpcImpl getTenantId为空 取defaultValue={}", defaultValue);
                    return defaultValue;
                }
                log.warn("TenantIdFindRpcImpl getTenantId为空 无默认值");
                throw new IllegalArgumentException("租户ID为空,请检查索引中是否存在租户ID");
            }
            Integer intValue = (Integer) tenantId;
            return intValue.longValue();
        }
        return null;
    }

    @Override
    public Long getTenantIdByCustomsLogisticsSn(String sn) {
        Long tenantId = null;
        if (Objects.nonNull(sn)) {
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("customsLogisticsSn", sn);
            tenantId = getTenantId(queryMap, INDICES, TYPE);
            log.info("getTenantIdByCustomsLogisticsSn tenantId={} sn={}", tenantId, sn);
        }
        if (tenantId != null) {
            return tenantId;
        } else {
            if (defaultFlag) {
                log.info("TenantIdFindRpcImpl getTenantId为空 取defaultValue={}", defaultValue);
                return defaultValue;
            }
        }
        throw new IllegalArgumentException("ES查询租户ID,请使用@TenantHttpFiled注解指定要查询的字段");
    }

    @Override
    public Long getTenantIdByCustomsInventorySn(String sn) {
        Long tenantId = null;
        if (Objects.nonNull(sn)) {
            Map<String, String> queryMap = new HashMap<>();
            queryMap.put("customsInventorySn", sn);
            tenantId = getTenantId(queryMap, INDICES, TYPE);
            log.info("getTenantIdByCustomsInventorySn tenantId={} sn={}", tenantId, sn);
        }
        if (tenantId != null) {
            return tenantId;
        } else {
            if (defaultFlag) {
                log.info("TenantIdFindRpcImpl getTenantId为空 取defaultValue={}", defaultValue);
                return defaultValue;
            }
        }
        throw new IllegalArgumentException("ES查询租户ID,根据指定清单sn未查询到租户信息 sn=" + sn);
    }
}