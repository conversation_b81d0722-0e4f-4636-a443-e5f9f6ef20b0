package com.danding.cds.service;

import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.rpc.client.rs.SalesReturnOrderDeclareCallBack;
import com.dt.platform.wms.rpc.client.rs.SalesReturnOrderDeclareCallBackParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 退货仓回告服务(调wms)
 */
@Service
@Slf4j
public class IWMSRefundWarehouseCallbackService {

    @DubboReference
    private SalesReturnOrderDeclareCallBack salesReturnOrderDeclareCallBack;
    ;

    public void startCallback(String logisticsNo, String refundLogisticsNo, String wmsWarehouseCode, Integer success) {
        SalesReturnOrderDeclareCallBackParam param = new SalesReturnOrderDeclareCallBackParam();
        param.setExpressNo(logisticsNo);
        param.setSuccess(success);
        param.setWarehouseCode(wmsWarehouseCode);
        param.setReverseExpressNo(refundLogisticsNo);
        try {
            Result<Boolean> booleanResult = salesReturnOrderDeclareCallBack.startCallback(param);
            if (booleanResult.checkSuccess()) {
                log.info("退货仓 开始申报 回调成功");
            } else {
                log.error("退货仓 开始申报 回调失败 - e={}", booleanResult.getMessage());
                throw new ArgsInvalidException("退货仓 开始申报 回调失败 - wms异常信息=" + booleanResult.getMessage());
            }
        } catch (Exception e) {
            log.error("退货仓 开始申报 回调失败 - e={}", e.getMessage(), e);
            throw new ArgsInvalidException("退货仓 开始申报 接口回调失败");
        }
    }

    public void callback(String logisticsNo, String refundLogisticsNo, String wmsWarehouseCode, Integer success) {
        SalesReturnOrderDeclareCallBackParam param = new SalesReturnOrderDeclareCallBackParam();
        param.setExpressNo(logisticsNo);
        param.setSuccess(success);
        param.setWarehouseCode(wmsWarehouseCode);
        param.setReverseExpressNo(refundLogisticsNo);
        try {
            Result<Boolean> booleanResult = salesReturnOrderDeclareCallBack.callback(param);
            if (booleanResult.checkSuccess()) {
                log.info("退货仓 退货回执 回调成功");
            } else {
                log.error("退货仓 退货回执 回调失败 - e={}", booleanResult.getMessage());
                throw new ArgsInvalidException("退货仓 退货回执 回调失败 - wms异常信息=" + booleanResult.getMessage());
            }
        } catch (Exception e) {
            log.error("退货仓 退货回执 回调失败 - e={}", e.getMessage(), e);
            throw new ArgsInvalidException("退货仓 开始申报 接口回调失败");
        }
    }
}
