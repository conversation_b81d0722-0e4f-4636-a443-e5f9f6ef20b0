package com.danding.cds.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderExtraDto;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.c.api.service.TrackLogEsService;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsItem;
import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.enums.CustomsLogisticsStatus;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.enums.LogisticsDeclareSystemEnums;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.rpc.client.WarehouseSendInfoClient;
import com.dt.platform.wms.rpc.param.WarehouseSendInfoParam;
import com.dt.tms.rpc.waybill.client.ITmsCustomsClient;
import com.dt.tms.rpc.waybill.param.customs.*;
import com.dt.tms.rpc.waybill.result.customs.RpcPushToCustomsResVO;
import com.dt.tms.tool.common.TmsResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Service
@Slf4j
@RefreshScope
public class TmsDeclareLogisticsService {

    @Autowired
    private BaseDataService baseDataService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @DubboReference
    private ITmsCustomsClient iTmsCustomsClient;

    @DubboReference
    private WarehouseSendInfoClient warehouseSendInfoClient;

    @Resource
    private OrderService orderService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Resource
    private TrackLogEsService trackLogEsService;

    @Value("${tms.logistics.declare.enable:false}")
    private boolean tmsDeclareEnable;
    @Value("${tms.logistics.declare.pdd.enable:false}")
    private boolean tmsDeclarePddEnable;

    @Value("${tms.logistics.unknown.warehouse.route:[]}")
    private String[] tmsLogisticsUnknownWarehouseRoutes;

    public boolean isTmsDeclare(String expressCode, RouteDeclareConfig routeDeclareConfig) {
        if (!tmsDeclareEnable) {
            log.info("TMS运单申报未启用");
            return false;
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(expressCode);
        return Objects.equals(LogisticsDeclareSystemEnums.TMS.getCode(), expressDTO.getLogisticDeclareSystem())
                && Objects.nonNull(routeDeclareConfig)
                && Objects.equals(routeDeclareConfig.getProxyImpl(), "TMS_SHIPMENT_DECLARE");
    }

    /**
     * pdd申报时区分是向tms请求报文还是用ccs自己的资质去申报
     *
     * @param expressCode
     * @return
     */
    public boolean isPddTmsDeclare(String expressCode) {
        if (!tmsDeclarePddEnable) {
            log.info("TMS运单申报未启用");
            return false;
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(expressCode);
        return Objects.equals(LogisticsDeclareSystemEnums.TMS.getCode(), expressDTO.getLogisticDeclareSystem());
    }

    public boolean isPddTmsDeclare() {
        if (!tmsDeclarePddEnable) {
            log.info("TMS运单申报未启用");
            return false;
        }
        return true;
    }

    public String declare(WrapShipmentInfo info) {
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByLogisticsNo(info.getLogisticsNo());
        info.setDeclareCompanyDTO(info.getLogisticsCompanyDTO());
        customsLogisticsService.updateAgentCompanyId(customsLogisticsDTO.getId(), customsLogisticsDTO.getLogisticsCompanyId());
        RpcCustomsWaybillReqVO reqVO = null;
        try {
            // 构建请求体
            reqVO = getRpcCustomsWaybillReqVO(info, customsLogisticsDTO);
        } catch (Exception e) {
            log.error("TMS运单推送失败- 请求报文拼装异常 -{}", e.getMessage(), e);
            buildAndSendTrackLog(customsLogisticsDTO, "请求报文拼装异常-" + e.getMessage(), false);
            return null;
        }
        log.info("iTmsCustomsClient.pushWaybillToCustoms req={}", JSON.toJSONString(info));
        TmsResult<RpcPushToCustomsResVO> tmsResult = null;
        buildAndSendTrackLog(customsLogisticsDTO, JSON.toJSONString(reqVO));
        try {
            // 推送tms
            tmsResult = iTmsCustomsClient.pushWaybillToCustoms(reqVO);
            // 申报轨迹日志
            log.info("iTmsCustomsClient.pushWaybillToCustoms tmsResult={}", JSON.toJSONString(tmsResult));
            // 回执处理
            if (Objects.isNull(tmsResult) || tmsResult._isFail()) {
                log.error("TmsDeclareLogisticsService.declare 接口调用失败 运单号={}", info.getLogisticsNo());
            }
        } catch (Exception e) {
            log.error("TMS运单推送失败- {}", e.getMessage(), e);
        }
        LogisticsReceive receive = getLogisticsReceive(tmsResult, customsLogisticsDTO);
        customsLogisticsService.receiveLogistics(receive);
        return null;
    }

    public void buildAndSendTrackLog(CustomsLogisticsDTO customsLogisticsDTO, String responseInfo) {
        this.buildAndSendTrackLog(customsLogisticsDTO, responseInfo, true);
    }

    public void buildAndSendTrackLog(CustomsLogisticsDTO customsLogisticsDTO, String responseInfo, boolean isSuccess) {
        TrackLogInfoDto trackLogInfoDto = new TrackLogInfoDto();
        trackLogInfoDto.setOrderId(customsLogisticsDTO.getOrderId());
        trackLogInfoDto.setOrderSn(customsLogisticsDTO.getOrderSn());
        trackLogInfoDto.setDeclareOrderNo(customsLogisticsDTO.getDeclareOrderNo());
        trackLogInfoDto.setInternalStatus(OrderInternalEnum.DECLARING.getCode());
        trackLogInfoDto.setSender(TrackLogConstantMixAll.DT_CCS);
        trackLogInfoDto.setReceiver(TrackLogConstantMixAll.DT_TMS);
        trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.TMS_LOGISTICS_DECLARE);
        trackLogInfoDto.setEventTime(new Date());
        trackLogInfoDto.setRequestMessage(responseInfo);
        trackLogInfoDto.setOperator(UserUtils.getUserRealName());
        trackLogInfoDto.setResult(isSuccess ? TrackLogConstantMixAll.SUCCESS : TrackLogConstantMixAll.FAIL);
        trackLogEsService.sendMsg(JSON.toJSONString(trackLogInfoDto));
    }

    private LogisticsReceive getLogisticsReceive(TmsResult<RpcPushToCustomsResVO> tmsResult, CustomsLogisticsDTO customsLogisticsDTO) {
        LogisticsReceive receive = new LogisticsReceive();
        receive.setLogisticsNo(customsLogisticsDTO.getLogisticsNo());
        receive.setCustomsTime(new DateTime().getMillis());
        String customsDetail;
        if (Objects.isNull(tmsResult)) {
            receive.setCustomsStatus(CustomsLogisticsStatus.DEAL_EXCEPTION.getValue());
            customsDetail = "TMS运单申报调用失败";
        } else {
            RpcPushToCustomsResVO resVO = tmsResult.getData();
            if (Objects.isNull(resVO)) {
                receive.setCustomsStatus(CustomsLogisticsStatus.DEAL_EXCEPTION.getValue());
                customsDetail = tmsResult.getMessage();
            } else {
                if (Boolean.TRUE.equals(resVO.getCustomsStatus())) {
                    receive.setCustomsStatus("2");
                    customsDetail = "TMS运单申报成功";
                } else {
                    receive.setCustomsStatus(CustomsLogisticsStatus.DEAL_EXCEPTION.getValue());
                    customsDetail = resVO.getRemark();
                }
            }
        }
        receive.setCustomsDetail(customsDetail);
        receive.setResponseMsg(customsDetail);
        receive.setSender(TrackLogConstantMixAll.DT_TMS);
        return receive;
    }

    public RpcCustomsWaybillReqVO getRpcCustomsWaybillReqVO(WrapShipmentInfo info, CustomsLogisticsDTO customsLogisticsDTO) {
        OrderDTO orderDTO = orderService.findBySnSection(customsLogisticsDTO.getOrderSn());
        OrderExtraDto orderExtraDto = JSON.parseObject(orderDTO.getExtraJson(), OrderExtraDto.class);
        String erpPhyWarehouseSn = orderExtraDto.getSubmit().getErpPhyWarehouseSn();
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(erpPhyWarehouseSn);
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            log.error("实体仓不存在");
            throw new ArgsInvalidException("实体仓信息不存在， erp仓编码=" + erpPhyWarehouseSn);
        }
        String wmsWarehouseCode = entityWarehouseDTOList.get(0).getWmsWarehouseCode();

        RpcCustomsWaybillReqVO reqVO = new RpcCustomsWaybillReqVO();
        reqVO.setSysCode("CCS");
        reqVO.setOrderNo(info.getDeclareOrderNo());
//            reqVO.setOpCode();
        reqVO.setWaybillNo(info.getLogisticsNo());
        reqVO.setTmsExpressCode(info.getExpressCode());
        RpcCustomsInfo customsInfo = new RpcCustomsInfo();
        customsInfo.setCustomsId(info.getCustoms());
        customsInfo.setPortCode(info.getPortCode());
        customsInfo.setEcRegCid(info.getEbcCompanyDTO().getCode());
        customsInfo.setEcRegName(info.getEbcCompanyDTO().getName());
        customsInfo.setEcGjCid(info.getEbpCompanyDTO().getCode());
        customsInfo.setEcGjName(info.getEbpCompanyDTO().getName());
        customsInfo.setLogisticsCode(info.getLogisticsCompanyDTO().getCode());
        customsInfo.setLogisticsName(info.getLogisticsCompanyDTO().getName());
        reqVO.setCustomsInfo(customsInfo);
        reqVO.setGlobalNo(orderDTO.getSystemGlobalSn());

        RpcCustomsReceiveInfo customsReceiveInfo = new RpcCustomsReceiveInfo();
        customsReceiveInfo.setProvince(info.getConsigneeProvince());
        customsReceiveInfo.setCity(info.getConsigneeCity());
        customsReceiveInfo.setArea(info.getConsigneeDistrict());
        customsReceiveInfo.setAddress(info.getConsigneeAddress());
        customsReceiveInfo.setName(info.getConsignee());
        customsReceiveInfo.setTelephone(info.getConsigneeTel());
        customsReceiveInfo.setIdCardType("1"); // 证件号码类型 1-身份证
        customsReceiveInfo.setIdCardNo(info.getIdCardNo());
        reqVO.setReceiveInfo(customsReceiveInfo);

        WarehouseSendInfoParam warehouseSendInfoParam = new WarehouseSendInfoParam();
        warehouseSendInfoParam.setWarehouseCode(wmsWarehouseCode);
        log.info("wms 获取发货人信息 warehouseSendInfoClient.warehouseSendInfo  req={}", JSON.toJSONString(warehouseSendInfoParam));
        String routeCode = info.getRouteInfo().getCode();
        List<String> tmsLogisticsUnknownWarehouseRouteList = Arrays.asList(tmsLogisticsUnknownWarehouseRoutes);
        if (CollectionUtil.isNotEmpty(tmsLogisticsUnknownWarehouseRouteList) && tmsLogisticsUnknownWarehouseRouteList.contains(routeCode)) {
            RpcCustomsSendInfo customsSendInfo = new RpcCustomsSendInfo();
            customsSendInfo.setName("刘俊");
            customsSendInfo.setProvince("浙江省");
            customsSendInfo.setCity("金华市");
            customsSendInfo.setArea("义乌市");
            customsSendInfo.setAddress("四季路999号义乌综合保税区B2库");
            customsSendInfo.setTelephone("0571-86731555");
            reqVO.setSendInfo(customsSendInfo);
        } else if (Objects.equals(wmsWarehouseCode, "YWZ803")) {
            RpcCustomsSendInfo customsSendInfo = new RpcCustomsSendInfo();
            customsSendInfo.setName("马思远");
            customsSendInfo.setProvince("浙江省");
            customsSendInfo.setCity("金华市");
            customsSendInfo.setArea("义乌市");
            customsSendInfo.setAddress("四季路999号义乌综合保税区B2库");
            customsSendInfo.setTelephone("0571-86731555");
            reqVO.setSendInfo(customsSendInfo);
            reqVO.setWarehouseCode("YWZ803");
        } else {
            try {
                Result<WarehouseSendInfoParam> warehouseSendInfoParamResult = warehouseSendInfoClient.warehouseSendInfo(warehouseSendInfoParam);
                log.info("wms 获取发货人信息 warehouseSendInfoClient.warehouseSendInfo  result={}", JSON.toJSONString(warehouseSendInfoParamResult));
                if (Objects.isNull(warehouseSendInfoParamResult) || !warehouseSendInfoParamResult.checkSuccess()) {
                    log.error("获取wms发货人失败");
                    throw new ArgsInvalidException("获取wms发货人失败");
                }
                WarehouseSendInfoParam wmsSendInfoResult = warehouseSendInfoParamResult.getData();
                RpcCustomsSendInfo customsSendInfo = new RpcCustomsSendInfo();
                customsSendInfo.setName(wmsSendInfoResult.getName());
                customsSendInfo.setProvince(wmsSendInfoResult.getProvince());
                customsSendInfo.setCity(wmsSendInfoResult.getCity());
                customsSendInfo.setArea(wmsSendInfoResult.getArea());
                customsSendInfo.setAddress(wmsSendInfoResult.getAddress());
                customsSendInfo.setTelephone(wmsSendInfoResult.getTelephone());
                reqVO.setSendInfo(customsSendInfo);
                reqVO.setWarehouseCode(wmsSendInfoResult.getWarehouseCode());
            } catch (Exception e) {
                log.error("获取wms发货人失败 - {}", e.getMessage(), e);
                throw new ArgsInvalidException("获取wms发货人失败");
            }
        }
        RpcCustomsOrderInfo customsOrderInfo = new RpcCustomsOrderInfo();
        customsOrderInfo.setOrderNo(info.getDeclareOrderNo());
        customsOrderInfo.setFreight(new BigDecimal(info.getFreight()));
        customsOrderInfo.setInsuredFee(customsLogisticsDTO.getInsureAmount());
        customsOrderInfo.setWorth(new BigDecimal(info.getWorth()));
        customsOrderInfo.setGrossWeight(new BigDecimal(info.getWeight()));
        customsOrderInfo.setNetWeight(customsLogisticsDTO.getNetWeight());
        customsOrderInfo.setPackNo(Integer.valueOf(info.getPackNo()));
        customsOrderInfo.setGoodsName(info.getGoodsInfo());
        List<RpcCustomsOrderItemInfo> customsOrderItemInfos = new ArrayList<>();
        List<CustomsLogisticsItem> customsLogisticsItems = JSON.parseArray(customsLogisticsDTO.getItemJson(), CustomsLogisticsItem.class);
        for (CustomsLogisticsItem item : customsLogisticsItems) {
            RpcCustomsOrderItemInfo itemInfo = new RpcCustomsOrderItemInfo();
            itemInfo.setName(item.getName());
            itemInfo.setQty(item.getCount().toString());
            itemInfo.setPrice(item.getPrice());
            customsOrderItemInfos.add(itemInfo);
        }
        customsOrderInfo.setOrderItemList(customsOrderItemInfos);
        reqVO.setOrderInfo(customsOrderInfo);
        return reqVO;
    }

    public void declareMock(WrapShipmentInfo info) {
        this.declare(info);
    }
}
