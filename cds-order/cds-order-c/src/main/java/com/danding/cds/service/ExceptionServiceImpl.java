package com.danding.cds.service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.c.api.service.ExceptionService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.enums.ExceptionTypeEnums;
import com.danding.cds.exception.api.enums.ExceptionUseTagEnum;
import com.danding.cds.exception.api.vo.ExceptionReqVO;
import com.danding.cds.exception.api.vo.ExceptionResVO;
import com.danding.cds.exception.api.vo.ExceptionUpsetReqVO;
import com.danding.cds.order.base.bean.dao.CustomsStatusMappingDO;
import com.danding.cds.order.base.bean.dao.ExceptionDO;
import com.danding.cds.order.base.data.service.BaseDataInitService;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.ExceptionMapper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;

import cn.hutool.core.bean.BeanUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/9
 */
@Service
public class ExceptionServiceImpl implements ExceptionService {

    @Autowired
    private ExceptionMapper exceptionMapper;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private BaseDataInitService baseDataInitService;


    /**
     * 根据id查询异常分类
     *
     * @param id
     * @return
     */
    @Override
    public ExceptionDTO findById(Long id) {
        ExceptionDO exceptionDO = exceptionMapper.selectByPrimaryKey(id);
        ExceptionDTO exceptionDTO = new ExceptionDTO();
        if (exceptionDO != null) {
            exceptionDTO = BeanUtil.copyProperties(exceptionDO, ExceptionDTO.class);
        }
        return exceptionDTO;
    }

    /**
     * 通过用途标签，获取异常数据
     *
     * @param useTag 用途
     * @return
     */
    @Override
    public List<ExceptionDTO> findByUseTag(Integer useTag) {
        Example example = new Example(ExceptionDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        criteria.andCondition("use_tag & " + useTag + " = ", useTag);
//        example.and(criteria);
        List<ExceptionDO> exceptionDOList = exceptionMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(exceptionDOList)) {
            return null;
        }
        return exceptionDOList.stream().map(z -> {
            return BeanUtil.copyProperties(z, ExceptionDTO.class);
        }).collect(Collectors.toList());
    }


    /**
     * 异常管理分页查询
     *
     * @param reqVO
     * @return
     */
    @Override
    @PageSelect
    public ListVO<ExceptionResVO> paging(ExceptionReqVO reqVO) {
        Example example = new Example(ExceptionDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(reqVO.getId())) {
            criteria.andEqualTo("id", reqVO.getId());
        }
        if (Objects.nonNull(reqVO.getExceptionClassify())) {
            criteria.andEqualTo("exceptionClassify", reqVO.getExceptionClassify());
        }
        if (Objects.nonNull(reqVO.getExceptionDescribe())) {
            criteria.andLike("exceptionDescribe", "%" + reqVO.getExceptionDescribe() + "%");
        }
        example.and(criteria);
        List<ExceptionDO> exceptionDOList = exceptionMapper.selectByExample(example);
        List<ExceptionResVO> listVO = exceptionDOList.stream().map(i -> {
            ExceptionResVO resVO = new ExceptionResVO();
            BeanUtil.copyProperties(i, resVO);
            resVO.setExceptionClassifyStr(ExceptionTypeEnums.getEnum(i.getExceptionClassify()).getDesc());
            // 异常用途标签
            Integer useTagBit = i.getUseTag();
            List<Integer> useTagList = ExceptionUseTagEnum.getUseTags(useTagBit);
            resVO.setUseTagList(useTagList);
            return resVO;
        }).collect(Collectors.toList());
        ListVO<ExceptionResVO> result = new ListVO<>();
        result.setDataList(listVO);
        // 分页
        PageInfo<CustomsStatusMappingDO> pageInfo = new PageInfo(exceptionDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    @Override
    public List<ExceptionDTO> getExceptionDOList() {
        Example example = new Example(ExceptionDO.class);
        example.createCriteria().andEqualTo("deleted", false);
        List<ExceptionDO> exceptionDOList = exceptionMapper.selectByExample(example);
        List<ExceptionDTO> exceptionDTOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(exceptionDOList)) {
            exceptionDTOList = JSON.parseArray(JSON.toJSONString(exceptionDOList), ExceptionDTO.class);
        }
        return exceptionDTOList;
    }


    /**
     * 新增异常分类
     *
     * @param upsetReqVO
     */
    @Override
    public void upsetException(ExceptionUpsetReqVO upsetReqVO) throws ArgsErrorException {
        if (Objects.isNull(upsetReqVO.getExceptionName())) {
            throw new ArgsErrorException("异常名称不能为空");
        }
        if (Objects.isNull(upsetReqVO.getExceptionDescribe())) {
            throw new ArgsErrorException("异常描述不能为空");
        }
        if (Objects.isNull(upsetReqVO.getExceptionClassify())) {
            throw new ArgsErrorException("异常分类为必选！");
        }
        if (Objects.isNull(upsetReqVO.getHandlePropose())) {
            throw new ArgsErrorException("处理建议不能为空");
        }

        ExceptionDO exceptionDO = BeanUtil.copyProperties(upsetReqVO, ExceptionDO.class);
        Integer userId = UserUtils.getUserId();
        exceptionDO.setUpdateBy(userId);

        // 特殊处理下用途位运算
        List<Integer> useTagList = upsetReqVO.getUseTagList();
        if (CollectionUtils.isEmpty(useTagList)) {
            exceptionDO.setUseTag(0);
        } else {
            Integer useTagBit = 0;
            for (Integer integer : useTagList) {
                useTagBit = useTagBit | integer;
            }
            exceptionDO.setUseTag(useTagBit);
        }
        if (Objects.isNull(upsetReqVO.getId())) {
            ExceptionDO result = findByName(upsetReqVO.getExceptionName());
            if (result != null) {
                throw new ArgsErrorException("异常名称重复");
            }
            UserUtils.setCreateAndUpdateBy(exceptionDO);
            exceptionDO.setCreateTime(new Date());
            exceptionDO.setUpdateTime(new Date());
            exceptionMapper.insertSelective(exceptionDO);
            baseDataService.getExceptionDTOById(exceptionDO.getId());
        } else {
            exceptionDO.setId(Long.valueOf(upsetReqVO.getId()));
            UserUtils.setUpdateBy(exceptionDO);
            exceptionDO.setUpdateTime(new Date());
            exceptionMapper.updateByPrimaryKeySelective(exceptionDO);
            baseDataInitService.updateDataProxy(BaseDataSyncTypeEnums.EXCEPTIONTYPE, exceptionDO);
        }


    }

    public ExceptionDO findByName(String exceptionName) {
        Example example = new Example(ExceptionDO.class);
        example.createCriteria().andEqualTo("exceptionName", exceptionName);
        return exceptionMapper.selectOneByExample(example);
    }


}
