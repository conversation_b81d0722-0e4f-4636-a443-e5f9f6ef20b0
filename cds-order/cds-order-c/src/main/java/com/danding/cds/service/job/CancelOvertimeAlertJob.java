package com.danding.cds.service.job;

import com.danding.cds.c.api.service.CustomsInventoryCalloffService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/6/25 14:48
 */
@Slf4j
@Service
public class CancelOvertimeAlertJob extends IJobHandler {
    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Override
    @XxlJob(value = "CancelOvertimeAlertJob", enableTenant = false)
    public ReturnT<String> execute(String s) throws Exception {
        try {
            customsInventoryCalloffService.cancelOvertimeAlert();
        } catch (Exception e) {
            XxlJobLogger.log("定时任务通知取消单超时失败:" + e.getMessage());
        }
        return ReturnT.SUCCESS;
    }
}
