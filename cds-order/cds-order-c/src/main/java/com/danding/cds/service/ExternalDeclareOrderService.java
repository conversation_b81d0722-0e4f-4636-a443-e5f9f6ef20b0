package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.google.common.collect.Lists;
import com.danding.cds.bean.vo.*;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.declare.ceb.domain.ceb312.CEB312Message;
import com.danding.cds.declare.ceb.domain.ceb512.CEB512Message;
import com.danding.cds.declare.ceb.domain.ceb622.CEB622Message;
import com.danding.cds.declare.ceb.domain.ceb624.CEB624Message;
import com.danding.cds.declare.ceb.domain.ceb626.CEB626Message;
import com.danding.cds.declare.ceb.domain.ceb816.CEB816Message;
import com.danding.cds.declare.ceb.domain.ceb816.Tax;
import com.danding.cds.declare.ceb.domain.ceb816.TaxHeadRd;
import com.danding.cds.declare.ceb.internal.enums.CebMessageEnum;
import com.danding.cds.declare.ceb.internal.utils.XMLUtil;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.github.kevinsawicki.http.HttpRequest;
import com.gvt.apollo.ApolloSdk;
import com.gvt.apollo.utils.SecurityUtils;
import jodd.util.Base64;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * @program: cds-center
 * @description:
 * @author: 潘本乐（Belep）
 * @create: 2023-10-09 16:59
 **/
@Service
@Slf4j
@RefreshScope
public class ExternalDeclareOrderService {

    @Value("${jie.zhou.wgbs.host:http://gwfat.kldidi.com/aoa}")
    private String JIE_ZHOU_HOST = "http://gwfat.kldidi.com/aoa";

    @Value("${jie.zhou.wgbs.msg.notify.uri:}")
    private String JIE_ZHOU_WGBSORDER_MSG_NOTIFY_URI = "/api/aoa/wgbs/wgbsOrder";

    @Value("${jieztech.wgbs.declare.cancel.uri:}")
    private String JIEZ_TECH_WGBS_CANCEL_DECLARE_URI = "/api/aoa/wgbs/cancelWgbsOrder";

    @Value("${jie.zhou.wgbs.app.id:}")
    private String JIE_ZHOU_WGBS_APP_ID = "6c0b855b55e34494a1d67dfb1809f34f";

    @Value("${jie.zhou.wgbs.private.key:}")
    private String JIE_ZHOU_WGBS_PRIVATE_KEY = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2vbMY24hWWPOe045622JwWc6vuuXJ419UCn6sFKhoq69iTkhN/uSmXumurOxs8CjVfR+f4uHXoRt5mHMYO5jeBe8SgfvfXARf5AVL6d41PxH9I/ncIaRfWUWACF5svRKpf+Egv6k1JRe2sCelml5m+WtMEm4Tzo4cUs/qjJDTvAgMBAAECgYAbLvi3r0XXMiCoVBQKuslwFQeerCeHcCn+LNIVADh4Z45FC2DzymoOu9/Lbk6aKJCN9YmohMxqmU8OsDOPbrMvIVRLdFHuz4K6yfPzjE7C1S8GrBAvxL7LpN4DTff3pzCsGSsqRbyYgtPOmZpyJQ0EQa2EP2xjFHYB696xyBsxgQJBAP+RnsXqSy7w1M2jhZ9raOoGl6KJiu2wVf1oSLwsQMG1TPe4QbL65ioSlw1hGchypzVbwi43KXVpYMT47sZkCoMCQQC+AVmETRDsfeIBiJwRR+nZsq3H2cdWEpYiyXfDpksv1zv+gxfod/Rwax9Nuz5npxej/LlC97JBy6Ns7wIVF5AlAkEAvNiy1RZixeXpqaaad4mb9co3RECUazyw3dawYHPmyfyZSjdaPNIPP6mK+rT0o1ytV81c+F+EgCEFA6facLi91wJADl8Rw0UE+65F0vHfRBtZX8L5C/236xW6z2THrz+7viGcgxKtU0MHdR1VH88C2Fo2Gow6AwuzKvDZKpslYC0JFQJAP9aIG+B56DJb7CEyz5tnKeepmJ9a/dLFbd+iivDYKZxsMqBmpX+kZix0+e7NCf3wT1DAWdjScdWyTdH+ojsAkw==";

    @Value("${jie.zhou.wgbs.public.key:}")
    private String JIE_ZHOU_WGBS_PUBLIC_KEY = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC9r2zGNuIVljzntOOetticFnOr7rlyeNfVAp+rBSoaKuvYk5ITf7kpl7prqzsbPAo1X0fn+Lh16EbeZhzGDuY3gXvEoH731wEX+QFSneNT8R/SP53CGkX1lFgAhebL0SqX/hIL+pNSUXtrAnpZpeZvlrTBJuE86OHFLP6oyQ07wIDAQAB";

    @Value("${jie.zhou.wgbs.supplier.username:daita}")
    private String JIE_ZHOU_WGBS_SUPPLIER_USERNAME = "";

    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @DubboReference
    private MessageService messageService;

    public void sendJieztech(String orderNo, String type) {
        try {
            JieztechCancelWgbsOrderReqVO reqVO = new JieztechCancelWgbsOrderReqVO();
            reqVO.setOrderListByOrderNo(orderNo);
            reqVO.setAppid(JIE_ZHOU_WGBS_APP_ID);
            reqVO.setSupplierUsername(JIE_ZHOU_WGBS_SUPPLIER_USERNAME);
            ApolloSdk apolloSdk = new ApolloSdk();
            String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(JIE_ZHOU_WGBS_PRIVATE_KEY), JSON.toJSONString(reqVO));
            log.info("芥州保税仓入驻 - 清单取消申报调用请求: 申报单号 {} , request:{}", orderNo, requestJson);
            HttpRequest httpRequest = HttpRequestUtil.post(JIE_ZHOU_HOST + JIEZ_TECH_WGBS_CANCEL_DECLARE_URI, requestJson);
            String body = httpRequest.body();
            log.info("芥州保税仓入驻 - 清单取消申报调用返回: 申报单号 {} , Response:{}", orderNo, body);
            if (httpRequest.ok()) {
                JieztechCancelWgbsOrderResVO jieztechCancelWgbsOrderResVO = JSON.parseObject(body, JieztechCancelWgbsOrderResVO.class);
                if (Boolean.TRUE.equals(jieztechCancelWgbsOrderResVO.getSuccess())) {
                    // 0订单取消  1订单售后 2订单不存在
                    List<JieztechCancelWgbsOrderDetailResVO> orderDetailList = JSON.parseArray(jieztechCancelWgbsOrderResVO.getData(), JieztechCancelWgbsOrderDetailResVO.class);
                    for (JieztechCancelWgbsOrderDetailResVO orderDetailResVO : orderDetailList) {
                        if (Objects.equals(type, "CANCEL")) {
                            if (Objects.equals(orderDetailResVO.getCancelStatus(), 2)
                                    || Objects.equals(orderDetailResVO.getCancelStatus(), 1)) {
                                String msg = String.format("申报单号：%s,取消单回调芥舟，响应取消状态是-%s,[0订单取消  1订单售后 2订单不存在]", orderNo, orderDetailResVO.getCancelStatus());
                                throw new RuntimeException(msg);
                            }
                        } else if (Objects.equals(type, "REFUND")) {
                            if (Objects.equals(orderDetailResVO.getCancelStatus(), 2)
                                    || Objects.equals(orderDetailResVO.getCancelStatus(), 0)) {
                                String msg = String.format("申报单号：%s,退货单回调芥舟，响应取消状态是-%s,[0订单取消  1订单售后 2订单不存在]", orderNo, orderDetailResVO.getCancelStatus());
                                throw new RuntimeException(msg);
                            }
                        }
                    }
                } else {
                    String msg = String.format("申报单号: %s ,取消单回调芥舟，响应失败，回执描述信息-%s", orderNo, jieztechCancelWgbsOrderResVO.getMessage());
                    throw new RuntimeException(msg);
                }
            }
        } catch (Exception ex) {
            throw new RuntimeException(ex);
        }
    }

    @Test
    public void testorder() {
        JieztechCancelWgbsOrderReqVO reqVO = new JieztechCancelWgbsOrderReqVO();
        reqVO.setOrderListByOrderNo("123456");
        reqVO.setAppid(JIE_ZHOU_WGBS_APP_ID);
        reqVO.setSupplierUsername(JIE_ZHOU_WGBS_SUPPLIER_USERNAME);
        try {
            String requestJson = new ApolloSdk().wrapSign(SecurityUtils.getPriKey(JIE_ZHOU_WGBS_PRIVATE_KEY), JSON.toJSONString(reqVO));
            HttpRequest httpRequest = HttpRequestUtil.post(JIE_ZHOU_HOST + JIEZ_TECH_WGBS_CANCEL_DECLARE_URI, requestJson);
            JieztechCancelWgbsOrderResVO jieztechCancelWgbsOrderResVO = JSON.parseObject(httpRequest.body(), JieztechCancelWgbsOrderResVO.class);
            System.out.println(JSON.toJSONString(jieztechCancelWgbsOrderResVO));
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    /**
     * 外部申报订单，海关消息回执处理
     *
     * @param sn
     * @param callbackXml
     * @param cebMessageType
     * @return
     */
    public CallbackDealResultDto externalDeclareOrderMsgDeal(String sn, String callbackXml, String cebMessageType) {
        Boolean callbackJieztech = false;
        Boolean ccsConsumerMsgEnable = true;

        // 返回信息
        CallbackDealResultDto callbackDealResultDto = new CallbackDealResultDto();
        String guid = "";
        String declareOrderNo = "";
        try {
            // 去除下 xmlns="http://www.chinaport.gov.cn/ceb" version="1.0"
            String _callbackXml = callbackXml.replace("xmlns=\"http://www.chinaport.gov.cn/ceb\"", "");
            if (cebMessageType == CebMessageEnum.CEB_INVENTORY_CALLBACK.getTag()) {
                CEB622Message ceb622Message = XMLUtil.converyToJavaBean(_callbackXml, CEB622Message.class);
                String copNo = ceb622Message.getInventoryReturn().getCopNo();
                String invtNo = ceb622Message.getInventoryReturn().getInvtNo();
                ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByCopNo(copNo);
                if (singleSignOrderDO != null) {
                    callbackJieztech = true;
                    ccsConsumerMsgEnable = false;
                    declareOrderNo = singleSignOrderDO.getOrderNo();
                    // todo 回写下清单编号
                    if (StringUtil.isNotBlank(invtNo)) {
                        // 维护下清单编号
                        ExternalDeclareOrderDo update = new ExternalDeclareOrderDo();
                        BeanUtils.copyProperties(singleSignOrderDO, update);
                        update.setInventoryNo(invtNo);
                        update.setUpdateTime(new Date());
                        String customsStatus = ceb622Message.getInventoryReturn().getReturnStatus();
                        if (Objects.equals(customsStatus, CustomsStat.CUSTOMS_PASS.getValue())) {
                            String returnTime = ceb622Message.getInventoryReturn().getReturnTime();
                            update.setCustomsPassTime(new DateTime(returnTime).toDate());
                        }
                        externalDeclareOrderBaseService.updateByPrimaryKeySelective(update);
                    }
                }
                guid = ceb622Message.getGuid();
            } else if (cebMessageType == CebMessageEnum.CEB_ORDER_CALLBACK.getTag()) {
                CEB312Message ceb312Message = XMLUtil.converyToJavaBean(_callbackXml, CEB312Message.class);
                String orderNo = ceb312Message.getOrderReturn().getOrderNo();
                ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByOrderNo(orderNo);
                if (singleSignOrderDO != null) {
                    callbackJieztech = true;
                    ccsConsumerMsgEnable = false;
                    declareOrderNo = singleSignOrderDO.getOrderNo();
                }
                guid = ceb312Message.getOrderReturn().getGuid();
            } else if (cebMessageType == CebMessageEnum.CEB_SHIPMENT_CALLBACK.getTag()) {
                CEB512Message ceb512Message = XMLUtil.converyToJavaBean(_callbackXml, CEB512Message.class);
                String logisticsNo = ceb512Message.getLogisticsReturn().getLogisticsNo();
                ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByLogisticsNo(logisticsNo);
                if (singleSignOrderDO != null) {
                    callbackJieztech = true;
                    ccsConsumerMsgEnable = false;
                    declareOrderNo = singleSignOrderDO.getOrderNo();
                }
                guid = ceb512Message.getLogisticsReturn().getGuid();
            } else if (cebMessageType == CebMessageEnum.CEB_INVENTORY_CANCEL_CALLBACK.getTag()) {
                CEB624Message ceb624Message = XMLUtil.converyToJavaBean(_callbackXml, CEB624Message.class);
                String invtNo = ceb624Message.getInvtCancelReturn().get(0).getInvtNo();
                ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByInvtNo(invtNo);
                if (singleSignOrderDO != null) {
                    callbackJieztech = true;
                    declareOrderNo = singleSignOrderDO.getOrderNo();
                    // 设置下ccs系统取消单企业内部编码
                    callbackDealResultDto.setCcsCancelCopNo(singleSignOrderDO.getCcsCancelCopNo());
                }
                guid = ceb624Message.getGuid();
            } else if (cebMessageType == CebMessageEnum.CEB_INVENTORY_REFUND_CALLBACK.getTag()) {
                CEB626Message ceb626Message = XMLUtil.converyToJavaBean(_callbackXml, CEB626Message.class);
                String invtNo = ceb626Message.getInvtRefundReturn().get(0).getInvtNo();
                ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByInvtNo(invtNo);
                if (singleSignOrderDO != null) {
                    callbackJieztech = true;
                    declareOrderNo = singleSignOrderDO.getOrderNo();
                    // 设置下ccs系统取消单企业内部编码
                    callbackDealResultDto.setCcsRefundCopNo(singleSignOrderDO.getCcsRefundCopNo());
                }
                guid = ceb626Message.getGuid();
            } else if (cebMessageType == CebMessageEnum.CEB_TAX_CALLBACK.getTag()) {
                CEB816Message ceb816Message = XMLUtil.converyToJavaBean(_callbackXml, CEB816Message.class);
                List<Tax> taxList = ceb816Message.getTax();
                for (Tax tax : taxList) {
                    TaxHeadRd taxHeadRd = tax.getTaxHeadRd();
                    String orderNo = taxHeadRd.getOrderNo();
                    if (StringUtil.isNotBlank(orderNo)) {
                        ExternalDeclareOrderDo singleSignOrderDO = externalDeclareOrderBaseService.getByOrderNo(orderNo);
                        if (singleSignOrderDO != null) {
                            callbackJieztech = true;
                            ccsConsumerMsgEnable = false;
                            ExternalDeclareOrderDo update = new ExternalDeclareOrderDo();
                            update.setId(singleSignOrderDO.getId());
                            update.setTaxCallback(_callbackXml);
                            update.setUpdateTime(new Date());
                            externalDeclareOrderBaseService.updateByPrimaryKeySelective(update);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析海关回执报文转对象异常：{}", e.getMessage(), e);
        }

        callbackDealResultDto.setJieztechDeclareOrder(callbackJieztech);
        callbackDealResultDto.setCcsConsumerMsgEnable(ccsConsumerMsgEnable);
        // 芥舟的单子，海关信息推送下芥舟那边
        if (callbackJieztech) {
            log.info("海关回执属于芥舟自申报类型 直接推给芥舟 海关回执：{}", callbackXml);
            JieztechMsgNotifyReqVo msgNotifyReqVo = new JieztechMsgNotifyReqVo();
            msgNotifyReqVo.setAppid(JIE_ZHOU_WGBS_APP_ID);
            msgNotifyReqVo.setSupplierUsername(JIE_ZHOU_WGBS_SUPPLIER_USERNAME);
            msgNotifyReqVo.setGuid(guid);
            msgNotifyReqVo.setMessageType(cebMessageType.replace("Message", ""));
            msgNotifyReqVo.setMessage(Base64.encodeToString(callbackXml.getBytes(StandardCharsets.UTF_8)));
            ApolloSdk apolloSdk = new ApolloSdk();
            try {
                String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(JIE_ZHOU_WGBS_PRIVATE_KEY), JSON.toJSONString(msgNotifyReqVo));
                log.info("海关回执推送芥舟 消息创建 请求报文 ：{}", requestJson);
                messageService.createMessage(MessageType.ORDER_CUSTOMS_TO_JIEZ_TECH,
                        Lists.newArrayList("JIEZTECH-DECLARE-MSG-CALLBACK"),
                        declareOrderNo, requestJson, "");
            } catch (Exception e) {
                log.error("海关回执推送芥舟 消息创建 异常：{}", e.getMessage(), e);
            }
        }
        return callbackDealResultDto;
    }

    @Test
    public void send() {

        String callbackXml = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><CEB312Message xmlns=\"http://www.chinaport.gov.cn/ceb\" version=\"1.0\" guid=\"b7bc075f-6be1-4663-81e2-89e9b0b49b18\"><OrderReturn><guid>b88cf8cd-5969-475a-967c-a85f3534749a</guid><ebpCode>1107960921</ebpCode><ebcCode>3301960URV</ebcCode><orderNo>18896116968129030000710444671886</orderNo><returnStatus>120</returnStatus><returnTime>20231009100138699</returnTime><returnInfo>[Code:1800;Desc:逻辑校验通过]</returnInfo></OrderReturn></CEB312Message>";

        String appId = "6c0b855b55e34494a1d67dfb1809f34f";
        String privateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2vbMY24hWWPOe045622JwWc6vuuXJ419UCn6sFKhoq69iTkhN/uSmXumurOxs8CjVfR+f4uHXoRt5mHMYO5jeBe8SgfvfXARf5AVL6d41PxH9I/ncIaRfWUWACF5svRKpf+Egv6k1JRe2sCelml5m+WtMEm4Tzo4cUs/qjJDTvAgMBAAECgYAbLvi3r0XXMiCoVBQKuslwFQeerCeHcCn+LNIVADh4Z45FC2DzymoOu9/Lbk6aKJCN9YmohMxqmU8OsDOPbrMvIVRLdFHuz4K6yfPzjE7C1S8GrBAvxL7LpN4DTff3pzCsGSsqRbyYgtPOmZpyJQ0EQa2EP2xjFHYB696xyBsxgQJBAP+RnsXqSy7w1M2jhZ9raOoGl6KJiu2wVf1oSLwsQMG1TPe4QbL65ioSlw1hGchypzVbwi43KXVpYMT47sZkCoMCQQC+AVmETRDsfeIBiJwRR+nZsq3H2cdWEpYiyXfDpksv1zv+gxfod/Rwax9Nuz5npxej/LlC97JBy6Ns7wIVF5AlAkEAvNiy1RZixeXpqaaad4mb9co3RECUazyw3dawYHPmyfyZSjdaPNIPP6mK+rT0o1ytV81c+F+EgCEFA6facLi91wJADl8Rw0UE+65F0vHfRBtZX8L5C/236xW6z2THrz+7viGcgxKtU0MHdR1VH88C2Fo2Gow6AwuzKvDZKpslYC0JFQJAP9aIG+B56DJb7CEyz5tnKeepmJ9a/dLFbd+iivDYKZxsMqBmpX+kZix0+e7NCf3wT1DAWdjScdWyTdH+ojsAkw==";
        JieztechMsgNotifyReqVo msgNotifyReqVo = new JieztechMsgNotifyReqVo();
        msgNotifyReqVo.setAppid(appId);
        msgNotifyReqVo.setSupplierUsername("JIE_ZHOU_WGBS_SUPPLIER_USERNAME");
        msgNotifyReqVo.setGuid("guid");
        msgNotifyReqVo.setMessageType("CEB311");
        msgNotifyReqVo.setMessage(Base64.encodeToString(callbackXml.getBytes(StandardCharsets.UTF_8)));

        ApolloSdk apolloSdk = new ApolloSdk();
        try {
            String url = "http://gwfat.kldidi.com/aoa/api/aoa/wgbsOrder/callbackMessageReceiver";
            String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(privateKey), JSON.toJSONString(msgNotifyReqVo));
            log.info("海关回执推送芥舟，请求报文：{}", requestJson);
            HttpRequest httpRequest = HttpRequestUtil.post(url, requestJson);
            String body = httpRequest.body();
            log.info("海关回执推送芥舟，响应报文：{}", body);
            JieztechMsgCallbackResVo msgCallbackResVo = JSONObject.parseObject(body, JieztechMsgCallbackResVo.class);
            if (httpRequest.ok() && msgCallbackResVo.getSuccess()) {

            } else {
                log.error("海关回执推送芥舟，调用异常，响应信息：{}", body);
            }
        } catch (Exception e) {
            log.error("海关回执推送芥舟异常：{}", e.getMessage(), e);
        }
    }

    @Test
    public void data() {
        String url = "http://gwfat.kldidi.com/aoa/api/aoa/wgbsOrder/callbackMessageReceiver";

        String request = "{\n" +
                "    \"messageType\": \"CEB311\",\n" +
                "    \"appid\": \"6c0b855b55e34494a1d67dfb1809f34f\",\n" +
                "    \"sign\": \"pBHCn2pd52cwDMT0F6xNckrwnhXktEHs9um+HoycdXwC1C/dJP6lN91JjKzEMjjSoygTvAUyTLCYKO3prG9t08Q3HQE8UahSFkz+TPU8f6v0zfyU7LKb74eN5JWAZs+FrvsYU6WHHl8u08EM5WToXVhqGNRiCEFJV9Znp9O/lf8=\",\n" +
                "    \"guid\": \"guid\",\n" +
                "    \"message\": \"PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiIHN0YW5kYWxvbmU9InllcyI/PjxDRUIzMTJNZXNzYWdlIHhtbG5zPSJodHRwOi8vd3d3LmNoaW5hcG9ydC5nb3YuY24vY2ViIiB2ZXJzaW9uPSIxLjAiIGd1aWQ9ImI3YmMwNzVmLTZiZTEtNDY2My04MWUyLTg5ZTliMGI0OWIxOCI+PE9yZGVyUmV0dXJuPjxndWlkPmI4OGNmOGNkLTU5NjktNDc1YS05NjdjLWE4NWYzNTM0NzQ5YTwvZ3VpZD48ZWJwQ29kZT4xMTA3OTYwOTIxPC9lYnBDb2RlPjxlYmNDb2RlPjMzMDE5NjBVUlY8L2ViY0NvZGU+PG9yZGVyTm8+MTg4OTYxMTY5NjgxMjkwMzAwMDA3MTA0NDQ2NzE4ODY8L29yZGVyTm8+PHJldHVyblN0YXR1cz4xMjA8L3JldHVyblN0YXR1cz48cmV0dXJuVGltZT4yMDIzMTAwOTEwMDEzODY5OTwvcmV0dXJuVGltZT48cmV0dXJuSW5mbz5bQ29kZToxODAwO0Rlc2M66YC76L6R5qCh6aqM6YCa6L+HXTwvcmV0dXJuSW5mbz48L09yZGVyUmV0dXJuPjwvQ0VCMzEyTWVzc2FnZT4=\",\n" +
                "    \"supplierUsername\": \"JIE_ZHOU_WGBS_SUPPLIER_USERNAME\",\n" +
                "    \"version\": \"1.0.0\"\n" +
                "}";

        HttpRequest httpRequest = HttpRequestUtil.post(url, request);
        String body = httpRequest.body();
        System.out.println(body);

    }

}
