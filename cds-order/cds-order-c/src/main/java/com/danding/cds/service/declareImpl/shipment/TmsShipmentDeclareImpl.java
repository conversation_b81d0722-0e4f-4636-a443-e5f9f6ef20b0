package com.danding.cds.service.declareImpl.shipment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.TmsDeclareLogisticsService;

import lombok.extern.slf4j.Slf4j;


@Service("TMS_SHIPMENT_DECLARE")
@Slf4j
public class TmsShipmentDeclareImpl extends ShipmentDeclareAbstract {

    @Autowired
    private TmsDeclareLogisticsService tmsDeclareLogisticsService;


    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("申报单: {} ,TMS【运单申报】-MOCK", info.getDeclareOrderNo());
        tmsDeclareLogisticsService.declareMock(info);
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("申报单: {} ,TMS【运单申报】", info.getDeclareOrderNo());
        tmsDeclareLogisticsService.declare(info);
        return null;
    }
}
