package com.danding.cds.service.declareImpl.cancel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.constants.DxpCons;
import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.service.customs.declare.EwtpDeclareService;

import lombok.extern.slf4j.Slf4j;

@Service("YWNO_EWTP_INVENTORY_CANCEL_DECLARE")
@Slf4j
public class YwnoEwtpInventoryCancelDeclareImpl extends InventoryCancelDeclareAbstract {

    @Autowired
    private EwtpDeclareService ewtpDeclareService;

    @Override
    protected InventoryCancelResult mockDeclareTest(WarpCancelOrderInfo info) {
        log.info("义乌南欧ewtp撤单代理申报，测试环境MOCK");
        return ewtpDeclareService.ewtpInventoryCancelDeclare(info, DxpCons.YWNO_EWTP_DXP);
    }

    @Override
    protected InventoryCancelResult declare(WarpCancelOrderInfo info) {
        return ewtpDeclareService.ewtpInventoryCancelDeclare(info, DxpCons.YWNO_EWTP_DXP);
    }


    public  static   void main(String[]args)
    {

        String Json="{\"accountBookDto\":{\"areaCompanyId\":2,\"bookNo\":\"L2992B22A001\",\"customsAreaCode\":\"2992\",\"customsDistrictCode\":\"HANGZHOU\",\"id\":22,\"remark\":\"杭州B保保税仓\"},\"customsAreaCode\":\"2992\",\"customsBookId\":22,\"customsInventoryCancelInfo\":{\"id\":****************,\"sn\":\"CD****************\",\"status\":\"INT\"},\"customsInventoryDto\":{\"bookId\":22,\"bookNo\":\"L2992B22A001\",\"buyerIdNumber\":\"330159666623012236\",\"buyerIdType\":\"1\",\"buyerName\":\"jasmine\",\"buyerTelNumber\":\"***********\",\"clientCustoms\":\"hangzhou\",\"consigneeAddress\":\"河南省周口市项城市九堡九和路科技城\",\"createTime\":*************,\"customs\":\"HANGZHOU\",\"customsField\":\"2900\",\"ebpCode\":\"3301964J31\",\"freight\":0.0000,\"id\":827859900645244928,\"inventoryNo\":\"*******************\",\"logisticsNo\":\"DDY167530660914823690\",\"mainGName\":\"库存明细\",\"orderNo\":\"*******************\",\"preNo\":\"B20200615494000227\",\"sn\":\"******************\"},\"declareCompanyCebCode\":\"330766K00Q\",\"declareCompanyDTO\":{\"cebCode\":\"330766K00Q\",\"cebName\":\"金华垂直云供应链管理有限公司\",\"code\":\"330766K00Q\",\"name\":\"金华垂直云供应链管理有限公司\"},\"declareNos\":\"*******************\",\"declareWay\":\"auto\",\"dxpId\":\"DXPDXP2020202020\",\"ebcCompanyDTO\":{\"cebCode\":\"330766K00Q\",\"cebName\":\"金华垂直云供应链管理有限公司\",\"code\":\"330766K00Q\",\"name\":\"金华垂直云供应链管理有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"3301964J31\",\"cebName\":\"杭州但丁云科技有限公司\",\"code\":\"3301964J31\",\"name\":\"杭州但丁云科技有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"330766K00Q\",\"cebName\":\"金华垂直云供应链管理有限公司\",\"code\":\"330766K00Q\",\"name\":\"金华垂直云供应链管理有限公司\"},\"logisticsCompanyDTO\":{\"cebCode\":\"330766K00Q\",\"cebName\":\"金华垂直云供应链管理有限公司\",\"code\":\"330766K00Q\",\"name\":\"金华垂直云供应链管理有限公司\"},\"mainOrderId\":827859900473278465,\"mainOrderSn\":\"OS2302021059901418\",\"routeInfo\":{\"code\":\"YWCN-PDD\",\"declareWay\":\"auto\",\"extraJson\":\"{\\\"assureCompanyId\\\":2,\\\"customsBookId\\\":22,\\\"ebcId\\\":2,\\\"ebpId\\\":1,\\\"listDeclareCompanyId\\\":2,\\\"listDeclareDxpId\\\":\\\"DXPDXP2020202020\\\",\\\"logisticsDeclareCompanyId\\\":2,\\\"logisticsDeclareDxpId\\\":\\\"DXPDXP2020202020\\\",\\\"orderDeclareCompanyId\\\":2,\\\"orderDeclareDxpId\\\":\\\"DXPDXP2020202020\\\"}\",\"name\":\"YWCN-PDD\",\"routeDeclareConfigList\":[{\"declareCode\":\"ZJ_PORT_ORDER_DECLARE\",\"declareImpl\":\"ZJ_PORT_ORDER_DECLARE\",\"dxpId\":\"DXPDXP2020202020\",\"type\":\"customsOrder\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_CANCEL_DECLARE\",\"dxpId\":\"DXPDXP2020202020\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"HZ_CENTER_SHIPMENT_DECLARE\",\"declareImpl\":\"HZ_CENTER_SHIPMENT_DECLARE\",\"dxpId\":\"DXPDXP2020202020\",\"type\":\"shipment\"},{\"declareCode\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE\",\"declareImpl\":\"HZ_CENTER_INVENTORY_REFUND_DECLARE\",\"dxpId\":\"DXPDXP2020202020\",\"type\":\"inventoryRefund\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_DECLARE\",\"dxpId\":\"DXPDXP2020202020\",\"type\":\"inventory\"}],\"routeExtraDto\":{\"assureCompanyId\":2,\"customsBookId\":22,\"ebcId\":2,\"ebpId\":1,\"listDeclareCompanyId\":2,\"logisticsDeclareCompanyId\":2,\"orderDeclareCompanyId\":2}}}";
        WarpCancelOrderInfo info = JSON.parseObject(Json, WarpCancelOrderInfo.class);
        EwtpDeclareService kk = new EwtpDeclareService();
        kk.ewtpInventoryCancelDeclare(info, DxpCons.YWNO_EWTP_DXP);

    }
}
