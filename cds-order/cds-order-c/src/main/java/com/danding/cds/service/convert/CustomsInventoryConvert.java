package com.danding.cds.service.convert;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.RandomUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.CustomsInventorySubmitDto;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.dto.OrderSubmitItemDto;
import com.danding.cds.bean.dto.SubmitGoodsInfoAndBookItemDto;
import com.danding.cds.bean.model.inventory.CustomsInventoryDOPack;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryDO;
import com.danding.cds.order.base.bean.dao.CustomsInventoryItemDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.data.service.SequenceServiceBaseService;
import com.danding.cds.order.base.mapper.CustomsInventoryItemMapper;
import com.danding.cds.order.base.mapper.CustomsInventoryMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.order.base.util.ProductSuffixUtils;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.FourCategoryTocCheckService;
import com.danding.cds.service.TaxeService;
import com.danding.cds.stock.StockContextUtil;
import com.danding.cds.stock.bean.OrderInfoDto;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.DeclareOrderTypeEnums;
import com.danding.cds.v2.enums.InventoryHandoverStatusEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

@Slf4j
@Component
public class CustomsInventoryConvert {
    /**
     * 这个注入主要是为了切面使用的，循环依赖的问题spring自身解决方案
     */
    @Autowired
    private CustomsInventoryConvert inventoryConvert;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private SequenceServiceBaseService sequenceServiceBaseService;

    @Autowired
    private CustomsInventoryMapper customsInventoryMapper;

    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private StockInventoryService stockInventoryService;

    @Resource
    private TaxeService taxeService;

    @Resource
    private BaseDataService baseDataService;
    @Resource
    private ProductSuffixUtils productSuffixUtils;
    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @Resource
    private OrderService orderService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Resource
    private TrackLogEsService trackLogEsService;

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Resource
    private CustomsInventoryConvert customsInventoryConvert;

    @Autowired
    private FourCategoryTocCheckService fourCategoryTocCheckService;

    @Deprecated
    public CustomsInventoryDOPack preCustomsInventory(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {

        OrderSubmitDto orderSubmitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        Tuple<CustomsInventoryDOPack,Boolean> tuple = this.preCustomsInventory(userId, orderSubmitDto, workSpace);
        return tuple.getF();
    }

    /**
     * 海关清单预生成
     *
     * @param userId
     * @param submit    数据传输层的订单提交对象
     * @param workSpace
     * @return 预生成的清单对象 包含清单对象和是否切换账册的标识
     */
    public Tuple<CustomsInventoryDOPack,Boolean> preCustomsInventory(Long userId, OrderSubmitDto submit, OrderConvertWorkSpace workSpace) {
        CustomsInventorySubmitDto customsInventorySubmit = new CustomsInventorySubmitDto();
        customsInventorySubmit.setDeclareOrderNo(submit.getDeclareOrderNo());
        customsInventorySubmit.setRouteCode(submit.getRouteCode());
        customsInventorySubmit.setExpressCode(submit.getExpressCode());
        customsInventorySubmit.setLogisticsNo(submit.getLogisticsNo());
        customsInventorySubmit.setFeeAmount(submit.getFeeAmount());
        customsInventorySubmit.setInsureAmount(submit.getInsureAmount());
        customsInventorySubmit.setItemList(submit.getItemList());
        customsInventorySubmit.setBuyerIdNumber(submit.getBuyerIdNumber());
        customsInventorySubmit.setBuyerName(submit.getBuyerName());
        customsInventorySubmit.setBuyerTelNumber(submit.getBuyerTelNumber());
        customsInventorySubmit.setConsigneeProvince(submit.getConsigneeProvince());
        customsInventorySubmit.setConsigneeCity(submit.getConsigneeCity());
        customsInventorySubmit.setConsigneeDistrict(submit.getConsigneeDistrict());
        if (StringUtils.isEmpty(submit.getConsigneeStreet())) {
            customsInventorySubmit.setConsigneeStreet("");
        } else {
            customsInventorySubmit.setConsigneeStreet(submit.getConsigneeStreet());
        }
        customsInventorySubmit.setConsigneeAddress(submit.getConsigneeAddress());
        customsInventorySubmit.setConsigneeTel(submit.getConsigneeTel());
        customsInventorySubmit.setTenantName(submit.getTenantName());
        customsInventorySubmit.setTenantOuterId(submit.getTenantOuterId());
        customsInventorySubmit.setDeclarePayNo(submit.getDeclarePayNo());
        customsInventorySubmit.setTaxAmount(submit.getTaxAmount());
        customsInventorySubmit.setDiscount(submit.getDiscount());
        customsInventorySubmit.setPayChannel(submit.getPayChannel());
        customsInventorySubmit.setLogisticsEnterpriseId(submit.getLogisticsEnterpriseId());
        customsInventorySubmit.setLogisticsEnterpriseCode(submit.getLogisticsEnterpriseCode());
        customsInventorySubmit.setLogisticsEnterpriseName(submit.getLogisticsEnterpriseName());
        customsInventorySubmit.setDeclareOrderTypes(submit.getDeclareOrderTypes());
        customsInventorySubmit.setIsEncrypt(submit.getIsEncrypt());
        customsInventorySubmit.setNote(submit.getNote());
        if (userId == null || userId.equals(0L)) {
            userId = 1L;
        }
        Tuple<CustomsInventoryDOPack, List<SubmitGoodsInfoAndBookItemDto>> tuple = this.preCustomsInventory(userId, customsInventorySubmit, workSpace, new Tuple<>(null, false));

        Tuple<CustomsInventoryDOPack, Boolean> resultData = new Tuple<>(tuple.getF(), false);
        // 判断下四类商品场景
        Tuple<Long, Boolean> exchangeBookTupe = fourCategoryTocCheckService.checkOrExchangeCustomsBook(submit, tuple.getS());
        log.info("清单申报 exchangeBookTupe={}", JSON.toJSONString(exchangeBookTupe));
        // 如果变换了账册, 则再次进行业务操作
        if (exchangeBookTupe.getS()) {
            Tuple<CustomsInventoryDOPack, List<SubmitGoodsInfoAndBookItemDto>> tupleData = this.preCustomsInventory(userId, customsInventorySubmit, workSpace, exchangeBookTupe);
            resultData.setF(tupleData.getF());
            resultData.setS(exchangeBookTupe.getS());
        }
        return resultData;
    }


    public Tuple<CustomsInventoryDOPack, List<SubmitGoodsInfoAndBookItemDto>> preCustomsInventory(Long userId, CustomsInventorySubmitDto submit, OrderConvertWorkSpace workSpace, Tuple<Long, Boolean> exchangeBookTupe) {
        if (userId == null || userId.equals(0L)) {
            throw new ArgsErrorException("用户不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getBuyerName()) || StringUtils.isEmpty(submit.getBuyerIdNumber())) {
            throw new ArgsErrorException("订购人信息不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeProvince())) {
            throw new ArgsErrorException("收件省不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeCity())) {
            throw new ArgsErrorException("收件市不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeTel())) {
            throw new ArgsErrorException("收件人电话不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclarePayNo())) {
            throw new ArgsErrorException("申报支付流水号不能为空");
        }
        if (CollectionUtils.isEmpty(submit.getItemList())) {
            throw new ArgsErrorException("商品项信息不能为空");
        }
        if (StringUtils.isEmpty(submit.getRouteCode())) {
            throw new ArgsErrorException("路由编码不能为空");
        }
//        RouteDTO routeDTO = workSpace.getRouteDTO(routeService, submit.getRouteCode());
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        if (routeDTO == null) {
            throw new ArgsErrorException("申报路径" + submit.getRouteCode() + "不存在");
        }
        if (routeDTO.getEnable() != 1) {
            throw new ArgsErrorException("申报路径" + submit.getRouteCode() + "未启用");
        }
//        CustomsBookDTO customsBookDTO = workSpace.getCustomsBookDTO(customsBookService, routeDTO.getCustomsBookId());
        Long customsBookId = routeDTO.getCustomsBookId();
        if (exchangeBookTupe != null && exchangeBookTupe.getS()) {
            customsBookId = exchangeBookTupe.getF();
            log.info("切换申报路径上配置的账册: 由账册:{} 切换到:{}", routeDTO.getCustomsBookId(), customsBookId);
        }
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsBookId);
        if (customsBookDTO == null) {
            throw new ArgsErrorException("海关账册不存在");
        }
        if (customsBookDTO.getEnable() != 1) {
            throw new ArgsErrorException("海关账册" + customsBookDTO.getBookNo() + "未启用");
        }
        if (StringUtils.isEmpty(submit.getExpressCode())) {
            throw new ArgsErrorException("快递编码不能为空");
        }
//        ExpressDTO expressDTO = workSpace.getExpressDTO(expressService, submit.getExpressCode());
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(submit.getExpressCode());
        if (expressDTO == null || expressDTO.getEnable() != 1) {
            throw new ArgsErrorException("快递编码未配置:" + submit.getExpressCode());
        }
        if (!routeDTO.getActionList().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            if (StringUtils.isEmpty(submit.getLogisticsNo())) {
                submit.setLogisticsNo("MOCK" + System.currentTimeMillis());
//                throw new ArgsErrorException("未勾选运单申报项，清单中的物流运单编号必填");
            }
        }
        if (StringUtils.isEmpty(submit.getPayChannel())) {
            throw new ArgsErrorException("支付渠道编码不能为空");
        }
//        PayChannelDTO payChannelDTO = workSpace.getPayChannelDTO(payChannelService, submit.getPayChannel());
        PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOByCode(submit.getPayChannel());
        if (payChannelDTO == null) {
            throw new ArgsErrorException("支付渠道编码未配置:" + submit.getPayChannel());
        }
        if (payChannelDTO.getEnable() != 1) {
            throw new ArgsErrorException("支付渠道编码未启用:" + submit.getPayChannel());
        }
        Boolean isOccupiedStock = false;
        CustomsInventoryDTO tempCustomsInventoryDTO = null;
        if (!StringUtils.isEmpty(submit.getLogisticsNo())) {
            tempCustomsInventoryDTO = customsInventoryService.findByLogisticsNo90Days(expressDTO.getId(), submit.getLogisticsNo());
//            OrderDto tempCustomsInventoryDTO = inventoryEsBaseService.getOneInventoryByLogictics(expressDTO.getId(), submit.getLogisticsNo());
            if (tempCustomsInventoryDTO != null) {
                if (!Objects.equals(routeDTO.getEbpId(), tempCustomsInventoryDTO.getEbpId())) {
                    //电商平台不一致
                    throw new ArgsErrorException("快递与运单号记录已存在");
                } else {
                    //电商平台一致
                    if (!tempCustomsInventoryDTO.getDeclareOrderNo().equals(submit.getDeclareOrderNo())) {
                        if (CustomsActionStatus.DEC_ING.getValue().equals(tempCustomsInventoryDTO.getStatus())) {
                            throw new ArgsErrorException("当前清单正在申报中，不允许申报单重推");
                        } else if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(tempCustomsInventoryDTO.getStatus())) {
                            throw new ArgsErrorException("当前清单已完成申报，不允许申报单重推");
                        }
                        //申报单号不一致：旧单号软删，是否占用库存，获取旧订单状态
                        log.info("申报单号重推-新申报单号={},旧申报单号={},旧清单={}", submit.getDeclareOrderNo(), tempCustomsInventoryDTO.getDeclareOrderNo(), JSON.toJSONString(tempCustomsInventoryDTO));
                        isOccupiedStock = tempCustomsInventoryDTO.getIsOccupiedStock();
                        //更新运单号
                        customsInventoryConvert.updateLogisticsNo(submit.getLogisticsNo(), tempCustomsInventoryDTO);
                    }
                }
            }
        }
//        CompanyDTO payCompany = workSpace.getCompanyDTO(companyService, payChannelDTO.getPayCompanyId());
        CompanyDTO payCompany = baseDataService.getUnifiedCrossCodeCompanyById(payChannelDTO.getPayCompanyId());
        // Step::清单数据构造
        CustomsInventoryDO inventoryDO = new CustomsInventoryDO();
        // Step::商品数据构造
        BigDecimal netWeight = BigDecimal.ZERO;
        BigDecimal grossWeight = BigDecimal.ZERO;
        List<CustomsInventoryItemDO> itemDOList = new ArrayList<>();
        boolean notItem = false;

        BigDecimal unitPrice = new BigDecimal(0);
        for (OrderSubmitItemDto submitItem : submit.getItemList()) {
            BigDecimal _price = submitItem.getUnitPrice();
            if (_price != null) {
                if (_price.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("申报单 ：{} , 商品单价小于等于0", submit.getDeclareOrderNo());
                }
                unitPrice = unitPrice.add(_price);
            }
        }
        if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ArgsErrorException("商品单价不能全小于等于0");
        }
        List<SubmitGoodsInfoAndBookItemDto> customsBookItemDTOList = new ArrayList<>();
        for (OrderSubmitItemDto submitItem : submit.getItemList()) {
            if (StringUtils.isEmpty(submitItem.getRecordNo())) {
                throw new ArgsErrorException("商品料号不能为空");
            }
            if (submitItem.getCount() == null || submitItem.getCount().equals(0)) {
                throw new ArgsErrorException("商品数量不能为空");
            }
//            if (submitItem.getUnitPrice() == null || submitItem.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0) {
//                throw new ArgsErrorException("商品单价不能小于等于0");
//            }
            GoodsRecordDTO recordDTO = null;
            /**
             * 引入备案-账册库存的关联关系
             */
            GoodsRecordMappingWayEnums goodsRecordMappingWay = orderService.getGoodsRecordMappingWayEnum(submit.getDeclareOrderTypes());
            log.info("申报单号={},申报单类型={}, 采用【{}】备案映射方式", submit.getDeclareOrderNo(), submit.getDeclareOrderTypes(), goodsRecordMappingWay.getDesc());
            Integer itemTag = submitItem.getItemTag();
            OrderItemTagEnum itemTagEnum = OrderItemTagEnum.getEnum(itemTag);
            if (Objects.nonNull(itemTagEnum) && Objects.equals(itemTagEnum.getCode(), OrderItemTagEnum.FB_GIFTS.getCode())) {
                String recordNo = submitItem.getRecordNo();
                recordDTO = goodsRecordService.findByBookIdAndProId(customsBookDTO.getId(), recordNo);
                if (Objects.isNull(recordDTO)) {
                    CustomsInventoryItemDO itemDO = new CustomsInventoryItemDO();
                    //赠品的统一给-1
                    itemDO.setBookItemId(-1L);
                    itemDO.setItemNo(submitItem.getItemNo());
                    itemDO.setItemName(submitItem.getItemName());
                    itemDO.setCount(submitItem.getCount());
                    itemDO.setUnitPrice(submitItem.getUnitPrice());
                    CustomsInventoryItemExtra extra = new CustomsInventoryItemExtra();
                    //默认映射时，统一料号跟上游下发料号保持一致
                    extra.setGoodsName(submitItem.getItemName());
                    extra.setUnifiedProductId(submitItem.getRecordNo());
                    extra.setProductId(submitItem.getRecordNo());
                    extra.setAssignOriginCountry(submitItem.getAssignOriginCountry());
                    extra.setBarCode(submitItem.getBarCode());
                    itemDO.setExtraJson(JSON.toJSONString(extra));
                    itemDO.setItemTag(itemTag);
                    itemDOList.add(itemDO);
                    continue;
                }
            } else {
                recordDTO = workSpace.getGoodsRecordDTO(goodsRecordService, customsBookDTO.getId(), submitItem.getRecordNo(), goodsRecordMappingWay.getValue());
                if (recordDTO == null) {
                    if (CollUtil.isNotEmpty(submit.getDeclareOrderTypes())
                            && submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.BYTE_DANCE_WMS.getCode())) {
                        // 字节wms报错提示
                        throw new ArgsErrorException("商品备案通关料号:" + submitItem.getRecordNo() + "不存在");
                    }
                    throw new ArgsErrorException("商品备案" + submitItem.getRecordNo() + "不存在;");
                }
            }
            List<RecordCustomsDTO> recordCustomsDTOS = goodsRecordService.recordCustomsDTOList(recordDTO.getId());
            Boolean existUpdateCustoms = recordCustomsDTOS.stream().anyMatch(i -> RecordCustomsSubmitTypeEnum.UPDATE_RECORD.getCode().equals(i.getSubmitType()));
            if (!Objects.equals(recordDTO.getRecordStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode()) && !existUpdateCustoms) {
                throw new ArgsErrorException("商品备案" + submitItem.getRecordNo() + "未审核成功;");
            }
            log.info("recordDTO= {}", JSON.toJSONString(recordDTO));
            String productId;
            if (Objects.nonNull(submitItem.getCustomsRecordProductId())) {
                productId = submitItem.getCustomsRecordProductId();
            } else {
                if (CollUtil.isNotEmpty(submit.getDeclareOrderTypes()) && submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.BYTE_DANCE_WMS.getCode())) {
                    // 字节wms 直接取上游下发的商品料号（通关料号）作为海关备案料号
//                    productId = goodsRecordAssociateService.getFinalProductId(recordDTO, customsBookDTO.getId(), submitItem.getRecordNo(), goodsRecordMappingWay);
                    productId = submitItem.getRecordNo();
                } else {
                    productId = goodsRecordAssociateService.getFinalProductId(recordDTO, customsBookDTO.getId(), null, goodsRecordMappingWay);
                }
            }
            CustomsBookItemDTO customsBookItemDTO = workSpace.getCustomsBookItemDTO(
                    customsBookItemService, customsBookDTO.getId(), productId, submitItem.getCount(), submitItem.getRecordGnum());
            if (customsBookItemDTO == null) {
                // 调整文案
                if (exchangeBookTupe != null && exchangeBookTupe.getS()) {
                    throw new ArgsErrorException("四类商品账册库存不足已自动切换专用账册，账册编号: " + customsBookDTO.getBookNo() + ", 账册库存" + productId + "不存在;");
                } else {
                    throw new ArgsErrorException("账册编号: " + customsBookDTO.getBookNo() + ", 账册库存" + productId + "不存在;");
                }
            }

            SubmitGoodsInfoAndBookItemDto goodsInfoAndBookItemDto = new SubmitGoodsInfoAndBookItemDto();
            BeanUtils.copyProperties(customsBookItemDTO, goodsInfoAndBookItemDto);
            goodsInfoAndBookItemDto.setGoodsItemCount(submitItem.getCount());
            customsBookItemDTOList.add(goodsInfoAndBookItemDto);
            CustomsInventoryItemDO itemDO = new CustomsInventoryItemDO();
            itemDO.setBookItemId(customsBookItemDTO.getId());
            itemDO.setItemNo(submitItem.getItemNo());
            itemDO.setItemName(submitItem.getItemName());
            itemDO.setCount(submitItem.getCount());
            itemDO.setUnitPrice(submitItem.getUnitPrice());
            CustomsInventoryItemExtra extra = new CustomsInventoryItemExtra();
            if (customsBookItemDTO == null || recordDTO == null || recordDTO.getEnable() != 1) {
                notItem = true;
                itemDOList = new ArrayList<>();
                netWeight = BigDecimal.ZERO;
                grossWeight = BigDecimal.ZERO;
                break;
            } else {
                itemDO.setBookItemId(customsBookItemDTO.getId());
                BeanUtils.copyProperties(customsBookItemDTO, extra);
                BeanUtils.copyProperties(recordDTO, extra);
                extra.setTaxPrice(BigDecimal.ZERO);
                extra.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                if (GoodsRecordMappingWayEnums.DEFAULT.equals(goodsRecordMappingWay)) {
                    //默认映射时，统一料号跟上游下发料号保持一致
                    extra.setUnifiedProductId(submitItem.getRecordNo());
                } else {
                    //特殊映射时，使用商品备案匹配的统一料号、sku
                    itemDO.setItemNo(recordDTO.getSkuId());
                    extra.setUnifiedProductId(recordDTO.getProductId());
                }
                extra.setProductId(customsBookItemDTO.getProductId());
                extra.setHsCode(customsBookItemDTO.getHsCode());
                extra.setGoodsName(customsBookItemDTO.getGoodsName());
                extra.setCurrCode(customsBookItemDTO.getCurrCode());
                extra.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                extra.setGoodsModel(customsBookItemDTO.getGoodsModel());
                extra.setOriginCountry(customsBookItemDTO.getOriginCountry());
                extra.setAssignOriginCountry(submitItem.getAssignOriginCountry());
                extra.setGoodsUnit(customsBookItemDTO.getGoodsUnit());
                extra.setFirstUnit(customsBookItemDTO.getFirstUnit());
                extra.setSecondUnit(customsBookItemDTO.getSecondUnit());
                extra.setFirstUnitAmount(customsBookItemDTO.getFirstUnitAmount());
                extra.setSecondUnitAmount(customsBookItemDTO.getSecondUnitAmount());
                extra.setBarCode(recordDTO.getBarCode());
                extra.setNetWeight(recordDTO.getNetWeight());
                extra.setGrossWeight(recordDTO.getGrossWeight());
                extra.setBrand(recordDTO.getBrand());
                extra.setBrandEn(recordDTO.getBrandEn());
                extra.setLesseeNo(recordDTO.getLesseeNo());
                extra.setVatRate(recordDTO.getVatRate());
                extra.setTaxRate(recordDTO.getTaxRate());
                extra.setComposition(recordDTO.getComposition());
                extra.setHgsbys(recordDTO.getHgsbys());
                extra.setRecordFunction(recordDTO.getRecordFunction());
                extra.setRecordUsage(recordDTO.getRecordUsage());
                //天津
                if (!StringUtils.isEmpty(recordDTO.getGoodsRegNo())) {
                    extra.setGoodsRegNo(recordDTO.getGoodsRegNo());
                }
                if (!StringUtils.isEmpty(recordDTO.getImportEntryDeclareNo())) {
                    extra.setDeclIINo(recordDTO.getImportEntryDeclareNo());
                }
                if (!StringUtils.isEmpty(recordDTO.getIoGoodsSerialNo())) {
                    extra.setIoGoodsSerialNo(recordDTO.getIoGoodsSerialNo());
                }
                if (!StringUtils.isEmpty(recordDTO.getCiqOriginCountry())) {
                    extra.setOriginCiqCountry(recordDTO.getCiqOriginCountry());
                }
                itemDO.setExtraJson(JSON.toJSONString(extra));
                itemDO.setItemName(customsBookItemDTO.getGoodsName());
                itemDO.setItemTag(itemTag);
                itemDOList.add(itemDO);
                netWeight = netWeight.add(recordDTO.getNetWeight().multiply(new BigDecimal(submitItem.getCount())));
                grossWeight = grossWeight.add(recordDTO.getGrossWeight().multiply(new BigDecimal(submitItem.getCount())));
            }
        }
        inventoryDO.setNote(submit.getNote());
        inventoryDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        inventoryDO.setLogisticsNo(submit.getLogisticsNo());
        inventoryDO.setFeeAmount(submit.getFeeAmount());
        inventoryDO.setInsureAmount(submit.getInsureAmount());
        inventoryDO.setBuyerIdNumber(submit.getBuyerIdNumber());
        inventoryDO.setBuyerName(submit.getBuyerName());
        inventoryDO.setBuyerTelNumber(submit.getBuyerTelNumber());
        BeanUtils.copyProperties(submit, inventoryDO);
        if (StringUtils.isEmpty(submit.getBuyerTelNumber())) {
            inventoryDO.setBuyerTelNumber(submit.getConsigneeTel());
        }
        inventoryDO.setSn(sequenceServiceBaseService.generateCustomsInventorySn());
        inventoryDO.setConsigneeAddress(submit.getConsigneeAddress());
        inventoryDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        inventoryDO.setCustoms(customsBookDTO.getCustomsDistrictCode());
        inventoryDO.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        inventoryDO.setHandoverStatus(InventoryHandoverStatusEnums.NO.getCode());
        inventoryDO.setCustomsStatus("");
        inventoryDO.setBuyerIdType("1");
        inventoryDO.setAccountBookId(customsBookDTO.getId());
        inventoryDO.setFeeAmount(submit.getFeeAmount());
        inventoryDO.setInsureAmount(submit.getInsureAmount());
        CustomsInventoryExtra extra = new CustomsInventoryExtra();
        inventoryDO.setExpressId(expressDTO.getId());
        extra.setExpressCode(submit.getExpressCode());
        extra.setCustomsBookNo(customsBookDTO.getBookNo());
        extra.setRouteCode(routeDTO.getCode());
        extra.setConsigneeProvince(submit.getConsigneeProvince());
        extra.setConsigneeCity(submit.getConsigneeCity());
        extra.setConsigneeDistrict(submit.getConsigneeDistrict());
        extra.setConsigneeStreet(submit.getConsigneeStreet());
        extra.setPayTransactionId(submit.getDeclarePayNo());
        extra.setTaxFee(submit.getTaxAmount());
        extra.setDiscountFee(submit.getDiscount());
        extra.setPayCompanyCode(payCompany.getCode());
        inventoryDO.setEbpId(routeDTO.getEbpId());
        inventoryDO.setEbcId(routeDTO.getEbcId());
        if (Objects.nonNull(submit.getLogisticsEnterpriseId())) {
            inventoryDO.setLogisticsCompanyId(submit.getLogisticsEnterpriseId());
        } else {
            inventoryDO.setLogisticsCompanyId(expressDTO.getExpressCompanyId());
        }
        inventoryDO.setAssureCompanyId(routeDTO.getAssureCompanyId());
        inventoryDO.setAgentCompanyId(routeDTO.getListDeclareCompanyId());
        inventoryDO.setAreaCompanyId(customsBookDTO.getAreaCompanyId());
        if (Objects.nonNull(submit.getTenantOuterId())) {
            try {
                inventoryDO.setUserId(Long.valueOf(submit.getTenantOuterId()));
            } catch (Exception ex) {
                log.warn("申报单号: {} ,tenantOuterId转换异常:{}", submit.getDeclareOrderNo(), ex.getMessage(), ex);
            }
        }
        inventoryDO.setAfterSalesStatus(InventoryAfterStatus.NO.getValue());
        inventoryDO.setReviewStatus(InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
        extra.setTenantOuterId(submit.getTenantOuterId());
        extra.setTenantName(submit.getTenantName());
        extra.setNotItem(notItem);
        inventoryDO.setExtraJson(JSON.toJSONString(extra));
        inventoryDO.setGrossWeight(grossWeight);
        inventoryDO.setNetWeight(netWeight);
        inventoryDO.setIsOccupiedStock(isOccupiedStock);
        // 提交
        CustomsInventoryDOPack doPack = new CustomsInventoryDOPack();
        doPack.setCustomsInventoryDO(inventoryDO);
        doPack.setItemDOList(itemDOList);
        doPack.setOldCustomsInventoryDTO(tempCustomsInventoryDTO);
        return new Tuple<>(doPack, customsBookItemDTOList);

    }

    /**
     * 更新 申报单、清单 运单号
     *
     * @param logisticsNo
     * @param customsInventoryDTO
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateLogisticsNo(String logisticsNo, CustomsInventoryDTO customsInventoryDTO) {
        //清单
        String newLogisticsNo = logisticsNo + '_' + RandomUtil.randomNumbers(4);
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setLogisticsNo(newLogisticsNo);
        //原清单占用库存状态继承到新清单上，占用库存状态初始化
        template.setIsOccupiedStock(Boolean.FALSE);
        log.info("申报单重推-更新运单 申报单号={}, 新运单号={}", customsInventoryDTO.getDeclareOrderNo(), newLogisticsNo);
        this.updateByIdSection(customsInventoryDTO.getId(), template, customsInventoryDTO.getCreateTime());
        //申报单
        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());

        Integer orderTags = Optional.ofNullable(orderDTO).map(OrderDTO::getOrderTags).orElse(null);
        List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
        if (orderTag.contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
            throw new ArgsErrorException("单据已挂起不支持重推:" + customsInventoryDTO.getDeclareOrderNo());
        }

        OrderDTO orderTemplate = new OrderDTO();
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        orderExtra.getSubmit().setLogisticsNo(newLogisticsNo);
        orderTemplate.setExtraJson(JSON.toJSONString(orderExtra));
        orderTemplate.setOrderTags(DeclareOrderTagEnums.add(orderDTO.getOrderTags(), DeclareOrderTagEnums.DISCARD));
        orderTemplate.setStatus(OrderStatus.DISCARD.getValue());
        orderService.updateBySn(orderTemplate, orderDTO.getSn());
        //运单
        customsLogisticsService.updateLogisticsNo(customsInventoryDTO.getOrderId(), newLogisticsNo);
    }

    //这里要做一个库存释放动作 直接使用 减占用 加可用的动作
    public void save(OrderDO orderDO, RichOrder richOrder) {
        CustomsInventoryDOPack doPack = richOrder.getCustomsInventoryDOPack();
        if (doPack != null) {
            CustomsInventoryDO inventoryDO = doPack.getCustomsInventoryDO();
            List<CustomsInventoryItemDO> customsInventoryItemDOList = doPack.getItemDOList();
            if (richOrder.getDeclareIsNew()) {
                this.add(orderDO, inventoryDO, customsInventoryItemDOList, doPack.getOldCustomsInventoryDTO(), richOrder);
            } else {
                // 这里通过路径判断，新的申报路径是否已经存在老的申报路径里，减少查库，存在则认为修改，否则新增
                Boolean inOldAction = richOrder.inOldAction(RouteActionEnum.DECLARE_INVENTORY.getCode());
                if (inOldAction) {
                    this.update(orderDO, inventoryDO, customsInventoryItemDOList, richOrder);
                } else {
                    this.add(orderDO, inventoryDO, customsInventoryItemDOList, doPack.getOldCustomsInventoryDTO(), richOrder);
                }
            }
        }
    }


    /**
     * 申报单号变更 添加轨迹日志
     *
     * @param orderDTO
     * @param originDeclareOrderNo
     */
    void buildAndSendTrackLog(OrderDTO orderDTO, String originDeclareOrderNo) {
        TrackLogEsDTO trackLogEsDTO = new TrackLogEsDTO();
        trackLogEsDTO.setOrderId(orderDTO.getId());
        trackLogEsDTO.setOrderSn(orderDTO.getSn());
        trackLogEsDTO.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        trackLogEsDTO.setRequestMessage(String.format("申报单号变更：原申报单号【%s】 ==> 现申报单号【%s】", originDeclareOrderNo, orderDTO.getDeclareOrderNo()));
        trackLogEsDTO.setSender(TrackLogConstantMixAll.DT_OMS);
        trackLogEsDTO.setReceiver(TrackLogConstantMixAll.DT_CCS);
        trackLogEsDTO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDTO.setEventDesc(TrackLogConstantMixAll.DECLARE_ORDER_CHANGE);
        trackLogEsDTO.setEventTime(new Date());
        trackLogEsDTO.setOperator(UserUtils.getUserRealName());
        trackLogEsDTO.setInternalStatus(OrderInternalEnum.CREATE_WAIT_PUSH.getCode());
        log.info("申报单变更 添加轨迹日志 - trackLogEsDTO={}", JSON.toJSONString(trackLogEsDTO));
        trackLogEsService.submit(trackLogEsDTO);
    }

    private void update(OrderDO orderDO, CustomsInventoryDO inventoryDO, List<CustomsInventoryItemDO> customsInventoryItemDOList, RichOrder richOrder) {
        CustomsInventoryDTO old;
        if (Objects.nonNull(orderDO.getCustomsInventorySn())) {
            old = customsInventoryService.findByOrder(orderDO.getId(), orderDO.getCustomsInventorySn());
        } else {
            old = customsInventoryService.findByOrder(orderDO.getId(), "");
        }
        CustomsInventoryDO template = new CustomsInventoryDO();
        if (!StringUtils.isEmpty(inventoryDO.getLogisticsNo())) {
            template.setLogisticsNo(inventoryDO.getLogisticsNo());
        }
        template.setBuyerIdNumber(inventoryDO.getBuyerIdNumber());
        template.setBuyerName(inventoryDO.getBuyerName());
        template.setBuyerTelNumber(inventoryDO.getBuyerTelNumber());
        template.setConsigneeAddress(inventoryDO.getConsigneeAddress());

        template.setFeeAmount(inventoryDO.getFeeAmount());
        template.setInsureAmount(inventoryDO.getInsureAmount());
        template.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        template.setEbpId(inventoryDO.getEbpId());
        template.setEbcId(inventoryDO.getEbcId());
        template.setAgentCompanyId(inventoryDO.getAgentCompanyId());
        template.setAssureCompanyId(inventoryDO.getAssureCompanyId());
        template.setNote(inventoryDO.getNote());

        //取消单状态调整
        if (CustomsStat.CUSTOMS_PASS.getValue().equals(inventoryDO.getCustomsStatus())) {
            customsInventoryCalloffService.updateRejectReasonAndStatusByOrderId(orderDO.getId(), "上游唤醒订单，海关放行",
                    InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode());
        }
        this.updateByIdSection(old.getId(), template, old.getCreateTime());
        List<CustomsInventoryItemDTO> oldItemDTOS = customsInventoryService.listItemByIdSection(old.getId(), old.getCreateTime());
        this.doUpdateInventoryItem(customsInventoryItemDOList, oldItemDTOS, old);

        CopyOptions options = new CopyOptions();
        options.setIgnoreNullValue(true);
        BeanUtil.copyProperties(template, old, options);
        BeanUtil.copyProperties(old, inventoryDO, options);
        richOrder.getCustomsInventoryDOPack().setCustomsInventoryDO(inventoryDO);
        richOrder.getCustomsInventoryDOPack().setItemDOList(customsInventoryItemDOList);
    }


    private void add(OrderDO orderDO, CustomsInventoryDO inventoryDO, List<CustomsInventoryItemDO> customsInventoryItemDOList,
                     CustomsInventoryDTO oldCustomsInventoryDTO, RichOrder richOrder) {
        if (Objects.nonNull(oldCustomsInventoryDTO)) {
            log.info("申报单号更新 - 原申报单号={}", oldCustomsInventoryDTO.getDeclareOrderNo());
            //旧表逻辑删除， 轨迹日志迁移
            OrderDTO orderDTO = new OrderDTO();
            BeanUtils.copyProperties(orderDO, orderDTO);
            //取消单状态调整
            customsInventoryCalloffService.migrateByOrderId(oldCustomsInventoryDTO.getOrderId(), orderDTO);
            if (CustomsStat.CUSTOMS_PASS.getValue().equals(oldCustomsInventoryDTO.getCustomsStatus())) {
                customsInventoryCalloffService.updateRejectReasonAndStatusByOrderId(orderDO.getId(), "上游唤醒订单，海关放行",
                        InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode());
            }
            if (Boolean.TRUE.equals(oldCustomsInventoryDTO.getIsOccupiedStock()) || Objects.isNull(oldCustomsInventoryDTO.getIsOccupiedStock())) {
                //占用库存调整
                List<OrderInfoDto> allOrderGoodsInfo = StockContextUtil.getAllOrderGoodsInfo();
                if (!CollectionUtils.isEmpty(allOrderGoodsInfo)) {
                    List<UpdateInventoryDTO> list = new ArrayList<>();
                    allOrderGoodsInfo.forEach(i -> {
                        UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
                        updateInventoryDTO.setCustomsBookId(oldCustomsInventoryDTO.getAccountBookId());
                        updateInventoryDTO.setBusinessNo(oldCustomsInventoryDTO.getDeclareOrderNo());
                        updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.LIST_ORDER_UPDATED);
                        updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getNum());
                        list.add(updateInventoryDTO);
                    });
                    stockInventoryService.updateInventory(list);
                }
            }
            this.buildAndSendTrackLog(orderDTO, oldCustomsInventoryDTO.getDeclareOrderNo());
        }
        inventoryDO.setOrderId(orderDO.getId());
        inventoryDO.setOrderSn(orderDO.getSn());
        inventoryDO.setCreateTime(new Date());
        inventoryDO.setId(sequenceServiceBaseService.getUniqueId());
        UserUtils.setCommonData(inventoryDO);
        customsInventoryMapper.insertSelective(inventoryDO);

        // Step::持久化
        int sort = 1;
        for (CustomsInventoryItemDO itemDO : customsInventoryItemDOList) {
            itemDO.setCustomsInventoryId(inventoryDO.getId());
            itemDO.setCreateTime(new Date());
            itemDO.setId(sequenceServiceBaseService.getUniqueId());
            itemDO.setSort(sort);
            UserUtils.setCreateAndUpdateBy(itemDO);
            customsInventoryItemMapper.insertSelective(itemDO);
            sort++;
        }
//        logComponent.asyncLogOpertion(LogCode.LOG_INVENTORY, inventoryDO.getDeclareOrderNo(), inventoryDO.getSn(),
//                CustomsActionStatus.getEnum(inventoryDO.getStatus()).getDesc(),
//                CustomsActionStatus.getEnum(inventoryDO.getStatus()).getDesc(),
//                "操作清单创建"
//        );
        // todo 发消息处理
        CustomsInventoryExtra extra = JSON.parseObject(inventoryDO.getExtraJson(), CustomsInventoryExtra.class);
        taxeService.asyncTaxesTenantSave(extra, inventoryDO.getAssureCompanyId());
        richOrder.getCustomsInventoryDOPack().setCustomsInventoryDO(inventoryDO);
        richOrder.getCustomsInventoryDOPack().setItemDOList(customsInventoryItemDOList);
    }

    private void doUpdateInventoryItem
            (List<CustomsInventoryItemDO> customsInventoryItemDOList, List<CustomsInventoryItemDTO> oldItemDTOS, CustomsInventoryDTO
                    old) {
        // Step::删除商品项
        for (CustomsInventoryItemDTO itemDTO : oldItemDTOS) {
            CustomsInventoryItemDO delTemplate = new CustomsInventoryItemDO();
            delTemplate.setId(itemDTO.getId());
            delTemplate.setCreateTime(itemDTO.getCreateTime());
            customsInventoryItemMapper.delete(delTemplate);
        }
        // Step::重建商品项
        int sort = 1;
        for (CustomsInventoryItemDO itemDO : customsInventoryItemDOList) {
            itemDO.setCustomsInventoryId(old.getId());
            itemDO.setCreateTime(old.getCreateTime());
            itemDO.setId(sequenceService.generateId());
            itemDO.setSort(sort);
            UserUtils.setCreateAndUpdateBy(itemDO);
            customsInventoryItemMapper.insertSelective(itemDO);
            sort++;
        }

        if (Boolean.TRUE.equals(old.getIsOccupiedStock()) || Objects.isNull(old.getIsOccupiedStock())) {
            //比较表体前后变化，进行库存变动， 无变化则不做库存调整
            List<OrderInfoDto> allOrderGoodsInfo = StockContextUtil.getAllOrderGoodsInfo();
            if (!CollectionUtils.isEmpty(allOrderGoodsInfo)) {
                List<UpdateInventoryDTO> list = new ArrayList<>();
                allOrderGoodsInfo.forEach(i -> {
                    UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
                    updateInventoryDTO.setCustomsBookId(old.getAccountBookId());
                    updateInventoryDTO.setBusinessNo(old.getDeclareOrderNo());
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.LIST_ORDER_UPDATED);
                    updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getNum());
                    list.add(updateInventoryDTO);
                });
                stockInventoryService.updateInventory(list);
            }
        }
    }


    private void updateByIdSection(Long id, CustomsInventoryDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        customsInventoryMapper.updateByExampleSelective(template, example);
    }
}
