package com.danding.cds.service.declareImpl.shipment;

import java.util.Objects;

import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.customs.logistics.api.dto.LogisticsReceive;
import com.danding.cds.customs.logistics.api.service.CustomsLogisticsService;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.TmsDeclareLogisticsService;

import lombok.extern.slf4j.Slf4j;

/**
 * 京东独立站点-TMS-申报
 * 调过京配申报
 */
@Service("JD_DLZ_TMS_SHIPMENT_DECLARE")
@Slf4j
public class JdDlzTmsShipmentDeclareImpl extends ShipmentDeclareAbstract {

    @DubboReference
    private CustomsLogisticsService customsLogisticsService;

    @Autowired
    private TmsDeclareLogisticsService tmsDeclareLogisticsService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("申报单: {} ,京东独立站点 TMS【运单申报】-MOCK", info.getDeclareOrderNo());
        RouteInfo routeInfo = info.getRouteInfo();
        String routeInfoCode = routeInfo.getCode();
        if (routeInfoCode.startsWith("JDDLZ") && Objects.equals(info.getExpressCode(), "JDL")) {
            LogisticsReceive receive = new LogisticsReceive();
            receive.setLogisticsNo(info.getLogisticsNo());
            receive.setCustomsTime(new DateTime().getMillis());
            receive.setCustomsStatus("2");
            receive.setCustomsDetail("京东独立站京配申报-直接申报成功");
            customsLogisticsService.receiveLogistics(receive);
        } else {
            tmsDeclareLogisticsService.declareMock(info);
        }
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("申报单: {} ,京东独立站点 TMS【运单申报】", info.getDeclareOrderNo());
        RouteInfo routeInfo = info.getRouteInfo();
        String routeInfoCode = routeInfo.getCode();
        if (routeInfoCode.startsWith("JDDLZ") && Objects.equals(info.getExpressCode(), "JDL")) {
            LogisticsReceive receive = new LogisticsReceive();
            receive.setLogisticsNo(info.getLogisticsNo());
            receive.setCustomsTime(new DateTime().getMillis());
            receive.setCustomsStatus("2");
            receive.setCustomsDetail("京东独立站京配申报-直接申报成功");
            customsLogisticsService.receiveLogistics(receive);
        } else {
            tmsDeclareLogisticsService.declare(info);
        }
        return null;
    }
}
