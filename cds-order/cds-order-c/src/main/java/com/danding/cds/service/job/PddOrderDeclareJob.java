package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderDeclareDTO;
import com.danding.cds.order.api.dto.OrderDeclareV2DTO;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.service.DeclareRepushService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class PddOrderDeclareJob extends IJobHandler {


    @Resource
    private OrderService orderService;
    @Autowired
    private DeclareRepushService declareRepushService;

    /**
     * 记录重推次数已到上限的list
     */
    public static List<String> declareMaxList = new ArrayList<>();

    public static String webHook = null;

    public static List<String> phoneList = new ArrayList<>();

    static final Integer CUSTOMS_INVENTORY_STATUS = -1;

    static final Integer DECLARING = 20;

    static final Integer CUSTOMS_SYSTEM_ERROR = 42;

    static final List<String> statusList = Arrays.asList(
            CustomsStat.CUSTOMS_PASS.getValue(),
            CustomsStat.CUSTOMS_REFUSE.getValue(),
            CustomsStat.CUSTOMS_PERSON.getValue());

    @Override
    @XxlJob(value = "OrderCPddOrderDeclareJob", enableTenant = true)
    public ReturnT<String> execute(String param) throws Exception {
        try {
            XxlJobLogger.log("参数设置 :" + param);
            OrderDeclareV2DTO orderDeclareDTO = new OrderDeclareV2DTO();
            if (StringUtils.hasText(param)) {
                orderDeclareDTO = JSON.parseObject(param, OrderDeclareV2DTO.class);
            }
            XxlJobLogger.log("[[PddOrderDeclareJob] 拼多多参数设置-{}]", orderDeclareDTO);
            XxlJobLogger.log("[[PddOrderDeclareJob] 拼多多 清单申报次数参数设置-{}]", orderDeclareDTO.getInventoryDeclareMax());
            XxlJobLogger.log("[[PddOrderDeclareJob] 拼多多 订单申报次数参数设置-{}]", orderDeclareDTO.getOrderDeclareMax());
            XxlJobLogger.log("[[PddOrderDeclareJob] 拼多多 运单申报次数参数设置-{}]", orderDeclareDTO.getLogisticDeclareMax());

            List<OrderDTO> orderDTOListBD = getDTOListPddDance(orderDeclareDTO);
            declareRepushService.reDeclarePushAndNotify(orderDTOListBD, orderDeclareDTO, "【拼多多】");
        } catch (Exception e) {
            XxlJobLogger.log("拼多多重推失败 :" + e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    private List<OrderDTO> getDTOListPddDance(OrderDeclareDTO orderDeclareDTO) {
        List<Integer> stateList = new ArrayList() {{
            add(OrderStatus.DEC_WAIT.getValue());
            add(OrderStatus.DEC_ING.getValue());
        }};
        List<OrderDTO> orderDTOList = orderService.listOrder(12L, stateList, orderDeclareDTO.getPage(), orderDeclareDTO.getQueryDays());
        XxlJobLogger.log("[op:PddOrderDeclareJob] 拼多多待放行订单数={} ", orderDTOList.size());
        return orderDTOList;
    }
}
