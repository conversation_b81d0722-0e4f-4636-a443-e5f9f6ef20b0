package com.danding.cds.bean.dto;

import com.danding.cds.order.api.dto.OrderSensitivePlainText;
import com.danding.encrypt.annotation.EncryptField;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @program: cds-center
 * @description: 订单提交Dto
 * @author: 潘本乐（Belep）
 * @create: 2021-12-20 10:07
 **/
@Data
public class OrderSubmitDto {

    /**
     * 申报结果回传地址
     */
    private String notifyUrl;

    /**
     * 申报单流入渠道（1:出入库系统 2:pangu 3:京东 4:老支付系统）
     */
    private Integer channel;

    /**
     * 渠道单号|外部单号
     */
    private String outOrderNo;

    /**
     * 外部交易流水号
     */
    private String outTradeNo;

    /**
     * 全局单号
     */
    private String systemGlobalSn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 申报路径 和 申报路由 必填二选一
     */
    /**
     * 申报路径编码
     */
    private String routeCode;

    /**
     * 申报路由标识1
     */
    private String firstIdentify;

    /**
     * 申报路由标识2
     */
    private String secondIdentify;

    /**
     * 申报路由标识3
     */
    private String thirdIdentify;

    // --- 清单基础 ---
    /**
     * 物流快递代码
     */
    private String expressCode;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 运费
     */
    private BigDecimal feeAmount = BigDecimal.ZERO;

    /**
     * 保费 非必填
     */
    private BigDecimal insureAmount = BigDecimal.ZERO;

    /**
     * 毛重（公斤） 非必填
     */
    private BigDecimal grossWeight;

    /**
     * 净重（公斤） 非必填 若未填，则会去备案信息找
     */
    private BigDecimal netWeight;

    // --- 商品 ---
    /**
     * 商品项
     */
    private List<OrderSubmitItemDto> itemList;

    // --- 清单订购人 ---
    /**
     * 购买人电话
     */
    @EncryptField
    private String buyerTelNumber;

    /**
     * 订购人证件号码
     */
    @EncryptField
    private String buyerIdNumber;

    /**
     * 订购人姓名
     */
    @EncryptField
    private String buyerName;

    /**
     * 收件人省
     */
    private String consigneeProvince;

    /**
     * 收件人市
     */
    private String consigneeCity;

    /**
     * 收件人区
     */
    private String consigneeDistrict;

    /**
     * 收件人街道（四级地址）
     */
    private String consigneeStreet;

    /**
     * 收件人地址
     */
    @EncryptField
    private String consigneeAddress;

    /**
     * 付款方式
     */
    private String payChannel;

    /**
     * 支付申报流水号
     */
    private String declarePayNo;

    /**
     * 税费
     */
    private BigDecimal taxAmount = BigDecimal.ZERO;

    /**
     * 折扣
     */
    private BigDecimal discount;

    /**
     * 交易时间|付款时间
     */
    private Long tradeTime;

    /**
     * 收件人邮箱 非必填
     */
    private String consigneeEmail;

    /**
     * 收件人电话
     */
    @EncryptField
    private String consigneeTel;

    /**
     * 收件人姓名
     */
    @EncryptField
    private String consigneeName;

    /**
     * 海关发件人姓名
     */
    @EncryptField
    private String senderName;

    // --- 海关179公告对接 ---
    /**
     * 是否开启海关179备查
     */
    private Boolean payInfoDataCheckFlag = false;

    /**
     * 支付交易流水号
     */
    private String tradePayNo;

    /**
     * 验核机构交易流水号
     */
    private String payTransactionId;

    /**
     * 验核机构名称
     */
    private String verDept;

    /**
     * 海关订单支付方式
     */
    private String payWay;

    /**
     * 支付单总金额
     */
    private BigDecimal payTransactionAmount = BigDecimal.ZERO;

    /**
     * 收款企业社会信用代码
     */
    private String recpCode;

    /**
     * 收款企业工商备案名称
     */
    private String recpName;

    /**
     * 收款渠道下的账号
     */
    private String recpAccount;

    /**
     * 支付请求原始数据
     */
    private String payRequestMessage;

    /**
     * 支付返回原始数据
     */
    private String payResponseMessage;

    /**
     * 原始商品信息 一个原始商品可能对应多个发货SKU
     */
    private List<PayInfoGoodsInfoDto> origGoodsInfoList;

    // --- 租户定制化功能 ----
    /**
     * 租户ID（此处传参不是真正意义上的租户定义，现在传的是用户）
     */
    private String tenantOuterId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 申报口岸编码
     */
    private String customsCode;

    /**
     * 商户编号
     */
    private String merchantCode;

    /**
     * ERP实体仓编码
     * 如：JHS342775
     */
    private String erpPhyWarehouseSn;

    /**
     * ERP实体仓名称
     */
    private String erpPhyWarehouseName;
    /**
     * 订单下单时间，毫秒
     */
    private Long orderTime;

    /**
     * 物流企业Id
     */
    private Long logisticsEnterpriseId;

    /**
     * 物流企业编码
     */
    private String logisticsEnterpriseCode;

    /**
     * 物流企业名称
     */
    private String logisticsEnterpriseName;

    /**
     * 申报单类型
     * eg；”字节WMS“
     */
    private List<String> declareOrderTypes;

    /**
     * 敏感信息是否加密
     */
    private Boolean isEncrypt;

    /**
     * 敏感数据明文
     */
    private OrderSensitivePlainText sensitivePlainText;


    /**
     * 支付流水号关联申报单号
     * 通联拆单申报
     */
    private List<String> payNoReldeclareOrderNoList;

    /**
     * 支付商户号(外部)
     */
    private String payMerchantOutNo;

    /**
     * 平台备注
     */
    private String note;

    /**
     * 外部关务单号 （eg:CB..）
     */
    private String outerCustomsNo;

    /**
     * 历史申报记录
     */
    private String declareWayRecord;
}
