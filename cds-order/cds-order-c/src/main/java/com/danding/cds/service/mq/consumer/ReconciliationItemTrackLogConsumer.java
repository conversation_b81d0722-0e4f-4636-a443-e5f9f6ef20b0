package com.danding.cds.service.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.ReconciliationOrderItemService;
import com.danding.cds.v2.bean.dto.ReconciliationItemTrackLogDTO;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description: 对账订单记录日志
 **/
@Slf4j
@Component
@RocketMQMessageListener(topic = "ccs-reconciliation-item-track-log-topic",
        consumerGroup = "ccs-reconciliation-item-track-log-consumer")
public class ReconciliationItemTrackLogConsumer extends MessageHandler {

    @Autowired
    private ReconciliationOrderItemService reconciliationOrderItemService;

    @Override
    public void handle(Object o) throws RuntimeException {
        String s = o.toString();
        log.info("ccs-reconciliation-item-track-log-topic 数据接收 -{}", s);
        ReconciliationItemTrackLogDTO dto = JSON.parseObject(s, ReconciliationItemTrackLogDTO.class);
        try {
            if (CollUtil.isNotEmpty(dto.getReconciliationItemIdList())) {
                reconciliationOrderItemService.saveBatchLog(dto.getReconciliationItemIdList(), dto.getStatus(), dto.getLogInfo(), dto.getOperator());
            } else {
                reconciliationOrderItemService.saveLog(dto);
            }
            log.info("处理完成 -{}", dto);
        } catch (Exception e) {
            log.error("对账订单记录日志-数据处理异常 -{}", e.getMessage(), e);
        }

    }


}
