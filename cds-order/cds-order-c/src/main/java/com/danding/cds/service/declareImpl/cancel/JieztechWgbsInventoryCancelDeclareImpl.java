package com.danding.cds.service.declareImpl.cancel;

import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.service.ExternalDeclareOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 芥州-保税仓入驻 撤单申报
 *
 * <AUTHOR>
 */
@Service("JIEZ_TECH_WGBS_INVENTORY_CANCEL_DECLARE")
@Slf4j
public class JieztechWgbsInventoryCancelDeclareImpl extends InventoryCancelDeclareAbstract {

    @Autowired
    private ExternalDeclareOrderService jieztechSignService;

    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @Override
    protected InventoryCancelResult mockDeclareTest(WarpCancelOrderInfo info) {
        return declare(info);
    }

    @Override
    protected InventoryCancelResult declare(WarpCancelOrderInfo info) {
        // 维护下，ccs系统取消单企业内部编码
        externalDeclareOrderBaseService.updateCcsCancelCopNoByOrderNo(info.getDeclareNos(), info.getCustomsInventoryCancelInfo().getId().toString());
        jieztechSignService.sendJieztech(info.getDeclareNos(), "CANCEL");
        return null;
    }
}
