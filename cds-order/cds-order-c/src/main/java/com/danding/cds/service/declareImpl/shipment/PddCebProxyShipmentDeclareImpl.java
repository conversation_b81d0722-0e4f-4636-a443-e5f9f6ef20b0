package com.danding.cds.service.declareImpl.shipment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 拼多多CEB代理运单申报
 * @author: 潘本乐（Belep）
 * @create: 2022-02-16 10:33
 **/
@Service("PDD_CEB_PROXY_SHIPMENT_DECLARE")
@Slf4j
public class PddCebProxyShipmentDeclareImpl extends ShipmentDeclareAbstract {

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("拼多多CEB运单代理申报，测试环境MOCK");
        pddDeclareService.cloudOfficialDeclareTest(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB511);

        return null;
    }


    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        pddDeclareService.cloudOfficialDeclare(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB511);
        return null;
    }
}
