package com.danding.cds.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.storage.DepotDeliverOrderDTO;
import com.danding.cds.bean.dto.storage.DepotDeliverOrderItemDTO;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.common.utils.PddOrderUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
@RefreshScope
public class DepotOrderManage {

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private PayChannelService payChannelService;

    @DubboReference
    private CompanyService companyService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsOrderService customsOrderService;

    @Value("${storage.host.url}")
    private String HOST;

    @Value("${storage.notify.url:}")
    private String NOTIFY_URL;

    @Value("${storage.warehouse.sn}")
    private String WAREHOUSE_SN;

    @Value("${storage.shipper.sn}")
    private String SHIPPER_SN;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    public DepotDeliverOrderDTO buildOrder(OrderDTO orderDTO){
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(),orderDTO.getCustomsInventorySn());
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(),orderDTO.getCustomsOrderSn());
        List<CustomsInventoryItemDTO> customsOrderItemList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(),customsInventoryDTO.getCreateTime());
        // Step 1: 组装参数
        DepotDeliverOrderDTO request = new DepotDeliverOrderDTO();
        request.setNotifyUrl(NOTIFY_URL);
        request.setWarehouseSn(WAREHOUSE_SN);
        request.setShipperSn(SHIPPER_SN);
        request.setSn(customsInventoryDTO.getDeclareOrderNo());
        request.setBusinessValue(customsInventoryDTO.getSn());
        request.setShippingAmount(customsOrderDTO.getFreight());
        request.setTaxAmount(customsOrderDTO.getTax());
        request.setDiscountAmount(customsOrderDTO.getDiscount());
        request.setTradeTime(new DateTime(customsOrderDTO.getTradeTime()).getMillis());
        PayChannelDTO payChannelDTO = payChannelService.findById(customsOrderDTO.getPayChannelId());
        request.setPayWay(payChannelDTO.getCode());
        request.setPaySerialSn(customsOrderDTO.getTradePayNo());
        request.setPayTime(new DateTime(customsOrderDTO.getTradeTime()).getMillis());
        request.setPackingMaterials("");
        request.setRemake("");
        // --- 收件人信息 ---
        request.setConsigneeMobile(customsOrderDTO.getConsigneeTel());
        request.setConsigneeName(customsOrderDTO.getConsigneeName());
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        request.setConsigneeProvince(inventoryExtra.getConsigneeProvince());
        request.setConsigneeCity(inventoryExtra.getConsigneeCity());
        request.setConsigneeDistrict(inventoryExtra.getConsigneeDistrict());
        String consigneeStreet = "";
        if (StringUtils.isNotEmpty(inventoryExtra.getConsigneeStreet())
                && !PddOrderUtils.isPddOrder(customsInventoryDTO.getEbpId(), customsInventoryDTO.getDeclareOrderNo())) {
            consigneeStreet = inventoryExtra.getConsigneeStreet();
        }
        request.setConsigneeDetailAddress(consigneeStreet + customsInventoryDTO.getConsigneeAddress());
        request.setConsigneeDetailZip("000000");
        request.setConsigneeIdNumber(customsInventoryDTO.getBuyerIdNumber());
        // --- 支付人信息 ---
        request.setPayerName(customsInventoryDTO.getBuyerName());
        request.setPayerIdNumber(customsInventoryDTO.getBuyerIdNumber());
        // --- 发件人信息 ---
        request.setSenderShopCode("");
        request.setSenderShopName("");
        // --- 发件信息 ---
        ExpressDTO expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
        request.setExpCode(expressDTO.getCode());
        // --- 跨境业务信息 ---
        request.setCustomsSenderName(customsOrderDTO.getSenderName());

        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(customsOrderDTO.getEbpId());
        request.setEbcCode(ebp.getCode());
        request.setEbcName(ebp.getName());

        CompanyDTO ebc = companyService.findUnifiedCrossInfoById(customsOrderDTO.getEbcId());
        request.setEbpCode(ebc.getCode());
        request.setEbpName(ebc.getCode());

        CompanyDTO payCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getPayCompanyId());
        request.setPayCompanyCode(payCompany.getCode());
        request.setPayCompanyName(payCompany.getName());

        CompanyDTO assure = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getAssureCompanyId());
        request.setAssureCode(assure.getCode());
        // --- 商品项 ---
        List<DepotDeliverOrderItemDTO> itemList = new ArrayList<>();
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (CustomsInventoryItemDTO customsInventoryItemDTO : customsOrderItemList) {
            DepotDeliverOrderItemDTO item = new DepotDeliverOrderItemDTO();
            CustomsBookItemDTO skuDTO = customsBookItemService.findById(customsInventoryItemDTO.getBookItemId());
            item.setSn(skuDTO.getId().toString());
            item.setSkuName(skuDTO.getGoodsName());
            item.setSkuSn(skuDTO.getProductId());
            item.setDiscount(BigDecimal.ZERO);
            item.setUnitPrice(customsInventoryItemDTO.getUnitPrice());
            item.setCount(customsInventoryItemDTO.getCount());
            // --- 跨境必填 ---
            item.setCustomsSkuSn(skuDTO.getProductId());
            item.setUnit(skuDTO.getGoodsUnit());
            item.setHsCode(skuDTO.getHsCode());
            item.setOriginCountry(skuDTO.getOriginCountry());
            item.setSkuModel(skuDTO.getGoodsModel());
            itemList.add(item);
            goodsAmount = goodsAmount.add(customsInventoryItemDTO.getUnitPrice().multiply(new BigDecimal(customsInventoryItemDTO.getCount())));
        }
        request.setGoodsAmount(goodsAmount);
        request.setItemList(itemList);
        return request;
    }

    public Boolean orderPush(OrderDTO orderDTO) {
        DepotDeliverOrderDTO request = buildOrder(orderDTO);
        // Step 2: 执行请求
        try {
            log.info("[op:DepotOrderManage-orderPush] request, param={}",JSON.toJSONString(request));
            String url = HOST + "/api/deliver/order/submit";
            HttpRequest httpRequest = HttpRequest.post(url)
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .header("Content-Type",HttpRequest.CONTENT_TYPE_JSON)
                    .send(JSON.toJSONString(request));
            if (httpRequest.ok()){
                log.info("[op:DepotOrderManage-orderPush] success, res={}",httpRequest.body());
                return true;
            }else {
                log.info("[op:DepotOrderManage-orderPush] fail, code={}",httpRequest.code());
                return false;
            }
        } catch (Exception e) {
            log.info("[op:DepotOrderManage-orderPush] exception, cause={}",e.getMessage(), e);
            return false;
        }
    }
}
