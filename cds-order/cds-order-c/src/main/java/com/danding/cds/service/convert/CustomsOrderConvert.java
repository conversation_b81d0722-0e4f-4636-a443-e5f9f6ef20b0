package com.danding.cds.service.convert;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.dto.OrderSubmitItemDto;
import com.danding.cds.bean.dto.SubmitGoodsInfoAndBookItemDto;
import com.danding.cds.bean.model.inventory.CustomsInventoryDOPack;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.order.api.dto.*;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.base.bean.dao.CustomsOrderDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.data.service.SequenceServiceBaseService;
import com.danding.cds.order.base.mapper.CustomsOrderMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.FourCategoryTocCheckService;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@Slf4j
public class CustomsOrderConvert {

    @Autowired
    private SequenceServiceBaseService sequenceServiceBaseService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @Autowired
    private CustomsOrderMapper customsOrderMapper;

    @Resource
    private CustomsOrderService customsOrderService;

    @Autowired
    private BaseDataService baseDataService;
    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @Resource
    private OrderService orderService;
    @Autowired
    private FourCategoryTocCheckService fourCategoryTocCheckService;

    @Deprecated
    public CustomsOrderDO preCustomsOrder(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {
        OrderSubmitDto submitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        Tuple<CustomsOrderDO, Boolean> result = this.preCustomsOrder(userId, submitDto, workSpace);
        return result.getF();
    }

    public Tuple<CustomsOrderDO, Boolean> preCustomsOrder(Long userId, OrderSubmitDto submit, OrderConvertWorkSpace workSpace) {
        CustomsOrderSubmit customsOrderSubmit = new CustomsOrderSubmit();
        customsOrderSubmit.setRouteCode(submit.getRouteCode());
        customsOrderSubmit.setExpressCode(submit.getExpressCode());
        customsOrderSubmit.setPayChannel(submit.getPayChannel());
        customsOrderSubmit.setDeclareOrderNo(submit.getDeclareOrderNo());
        customsOrderSubmit.setDeclarePayNo(submit.getDeclarePayNo());
        customsOrderSubmit.setTradePayNo(submit.getTradePayNo());
        customsOrderSubmit.setFeeAmount(submit.getFeeAmount());
        customsOrderSubmit.setTaxAmount(submit.getTaxAmount());
        customsOrderSubmit.setDiscount(submit.getDiscount());
        customsOrderSubmit.setTradeTime(submit.getTradeTime());
        customsOrderSubmit.setSenderName(submit.getSenderName());
        customsOrderSubmit.setBuyerIdNumber(submit.getBuyerIdNumber());
        customsOrderSubmit.setBuyerName(submit.getBuyerName());
        customsOrderSubmit.setBuyerTelNumber(submit.getBuyerTelNumber());
        customsOrderSubmit.setConsigneeProvince(submit.getConsigneeProvince());
        customsOrderSubmit.setConsigneeCity(submit.getConsigneeCity());
        customsOrderSubmit.setConsigneeDistrict(submit.getConsigneeDistrict());
        if (StringUtils.isEmpty(submit.getConsigneeStreet())) {
            customsOrderSubmit.setConsigneeStreet("");
        } else {
            customsOrderSubmit.setConsigneeStreet(submit.getConsigneeStreet());
        }
        customsOrderSubmit.setConsigneeAddress(submit.getConsigneeAddress());
        customsOrderSubmit.setConsigneeEmail(submit.getConsigneeEmail());
        customsOrderSubmit.setConsigneeTel(submit.getConsigneeTel());
        customsOrderSubmit.setConsigneeName(submit.getConsigneeName());
        customsOrderSubmit.setDeclareOrderTypes(submit.getDeclareOrderTypes());
        customsOrderSubmit.setItemList(submit.getItemList().stream().map((OrderSubmitItemDto item) -> {
            CustomsOrderSubmitItem submitItem = new CustomsOrderSubmitItem();
            submitItem.setRecordNo(item.getRecordNo());
            submitItem.setRecordGnum(item.getRecordGnum());
            submitItem.setSku(item.getItemNo());
            submitItem.setCount(item.getCount());
            submitItem.setUnitPrice(item.getUnitPrice());
            submitItem.setItemTag(item.getItemTag());
            return submitItem;
        }).collect(Collectors.toList()));
        if (userId == null || userId.equals(0L)) {
            userId = 1L;
        }
        Tuple<CustomsOrderDO, List<SubmitGoodsInfoAndBookItemDto>> tuple = this.preCustomsOrder(userId, customsOrderSubmit, workSpace, new Tuple<>(null, false));
        Tuple<CustomsOrderDO, Boolean> resultData = new Tuple<>(tuple.getF(), false);
        // 判断下四类商品场景
        Tuple<Long, Boolean> exchangeBookTupe = fourCategoryTocCheckService.checkOrExchangeCustomsBook(submit, tuple.getS());
        log.info("订单申报 exchangeBookTupe={}", JSON.toJSONString(exchangeBookTupe));
        // 如果变换了账册, 则再次进行业务操作
        if (exchangeBookTupe.getS()) {
            Tuple<CustomsOrderDO, List<SubmitGoodsInfoAndBookItemDto>> tupleData = this.preCustomsOrder(userId, customsOrderSubmit, workSpace, exchangeBookTupe);
            resultData.setF(tupleData.getF());
            resultData.setS(exchangeBookTupe.getS());
        }
        return resultData;
    }

    public Tuple<CustomsOrderDO, List<SubmitGoodsInfoAndBookItemDto>> preCustomsOrder(Long userId, CustomsOrderSubmit submit, OrderConvertWorkSpace workSpace, Tuple<Long, Boolean> exchangeBookTupe) {
        CustomsOrderDO customsOrderDO = new CustomsOrderDO();
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclarePayNo())) {
            throw new ArgsErrorException("申报交易流水号不能为空");
        }
        if (StringUtils.isEmpty(submit.getTradePayNo())) {
            throw new ArgsErrorException("交易流水号不能为空");
        }
        if (LongUtil.isNone(submit.getTradeTime())) {
            throw new ArgsErrorException("交易时间不能为空");
        }
        if (StringUtils.isEmpty(submit.getBuyerIdNumber())) {
            throw new ArgsErrorException("订购人身份证不能为空");
        }
        if (StringUtils.isEmpty(submit.getBuyerName())) {
            throw new ArgsErrorException("订购人姓名不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeProvince())) {
            throw new ArgsErrorException("收件省不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeCity())) {
            throw new ArgsErrorException("收件市不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeName())) {
            throw new ArgsErrorException("收件人姓名不能为空");
        }
        if (StringUtils.isEmpty(submit.getConsigneeTel())) {
            throw new ArgsErrorException("收件人电话不能为空");
        }
        if (StringUtils.isEmpty(submit.getRouteCode())) {
            throw new ArgsErrorException("路由编码不能为空");
        }
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        if (routeDTO == null || routeDTO.getEnable() != 1) {
            throw new ArgsErrorException("申报路径不存在或未启用,路径标识：" + submit.getRouteCode());
        }
        Long customsBookId = routeDTO.getCustomsBookId();
        if (exchangeBookTupe != null && exchangeBookTupe.getS()) {
            customsBookId = exchangeBookTupe.getF();
            log.info("切换申报路径上配置的账册: 由账册:{} 切换到:{}", routeDTO.getCustomsBookId(), customsBookId);
        }
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsBookId);
        if (customsBookDTO == null || customsBookDTO.getEnable() != 1) {
            throw new ArgsErrorException("海关账册不存在或未启用");
        }
        if (StringUtils.isEmpty(submit.getExpressCode())) {
            throw new ArgsErrorException("快递编码不能为空");
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(submit.getExpressCode());
        if (expressDTO == null || expressDTO.getEnable() != 1) {
            throw new ArgsErrorException("快递编码未配置:" + submit.getExpressCode());
        }
        if (StringUtils.isEmpty(submit.getPayChannel())) {
            throw new ArgsErrorException("支付渠道编码不能为空");
        }
        PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOByCode(submit.getPayChannel());
        if (payChannelDTO == null || payChannelDTO.getEnable() != 1) {
            throw new ArgsErrorException("支付渠道编码未配置或未启用:" + submit.getPayChannel());
        }
        customsOrderDO.setSn(sequenceServiceBaseService.generateCustomsOrderSn());
        customsOrderDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        customsOrderDO.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        customsOrderDO.setCustoms(customsBookDTO.getCustomsDistrictCode());
        customsOrderDO.setEbcId(routeDTO.getEbcId());
        customsOrderDO.setEbpId(routeDTO.getEbpId());
        customsOrderDO.setPayCompanyId(payChannelDTO.getPayCompanyId());
        customsOrderDO.setLogisticsCompanyId(expressDTO.getExpressCompanyId());
        customsOrderDO.setAgentCompanyId(routeDTO.getOrderDeclareCompanyId());
        customsOrderDO.setPayChannelId(payChannelDTO.getId());
        customsOrderDO.setExpressId(expressDTO.getId());
        customsOrderDO.setDeclarePayNo(submit.getDeclarePayNo());
        customsOrderDO.setTradePayNo(submit.getTradePayNo());
        customsOrderDO.setFreight(submit.getFeeAmount());
        customsOrderDO.setTax(submit.getTaxAmount());
        customsOrderDO.setDiscount(submit.getDiscount());
        customsOrderDO.setTradeTime(new DateTime(submit.getTradeTime()).toDate());
        customsOrderDO.setSenderName(submit.getSenderName());
        customsOrderDO.setBuyerIdNumber(submit.getBuyerIdNumber());
        customsOrderDO.setBuyerName(submit.getBuyerName());
        customsOrderDO.setBuyerTelNumber(submit.getBuyerTelNumber());
        customsOrderDO.setConsigneeName(submit.getConsigneeName());
        customsOrderDO.setConsigneeAddress(submit.getConsigneeAddress());
        customsOrderDO.setConsigneeTel(submit.getConsigneeTel());
        customsOrderDO.setConsigneeEmail(submit.getConsigneeEmail());
        customsOrderDO.setCustomsStatus(CustomsStat.NULL.getValue());
        customsOrderDO.setLastCustomsTime(null);
        //过滤下非保数据
        List<CustomsOrderSubmitItem> filterFbItem = submit.getItemList().stream().filter(item -> !OrderItemTagEnum.containsFbGifts(item.getItemTag())).collect(Collectors.toList());
        submit.setItemList(filterFbItem);
        // Step::商品
        BigDecimal unitPrice = new BigDecimal(0);
        for (CustomsOrderSubmitItem submitItem : submit.getItemList()) {
            BigDecimal _price = submitItem.getUnitPrice();
            if (_price != null) {
                if (_price.compareTo(BigDecimal.ZERO) <= 0) {
                    log.info("申报单 ：{} , 商品单价小于等于0", submit.getDeclareOrderNo());
                }
                unitPrice = unitPrice.add(_price);
            }
        }
        if (unitPrice.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ArgsErrorException("商品单价不能全小于等于0");
        }
        List<CustomsOrderItem> itemList = new ArrayList<>();
        Boolean notItem = false;
        List<SubmitGoodsInfoAndBookItemDto> customsBookItemDTOList = new ArrayList<>();
        for (CustomsOrderSubmitItem submitItem : submit.getItemList()) {
            CustomsOrderItem orderItem = new CustomsOrderItem();
            if (StringUtils.isEmpty(submitItem.getRecordNo())) {
                throw new ArgsErrorException("商品料号不能为空");
            }
            if (submitItem.getCount() == null || submitItem.getCount().equals(0)) {
                throw new ArgsErrorException("商品数量不能为空");
            }
//            if (submitItem.getUnitPrice() == null || submitItem.getUnitPrice().compareTo(BigDecimal.ZERO) <= 0){
//                throw new ArgsErrorException("商品单价不能小于等于0");
//            }
            GoodsRecordMappingWayEnums goodsRecordMappingWay = orderService.getGoodsRecordMappingWayEnum(submit.getDeclareOrderTypes());
            log.info("申报单号={},申报单类型={},采用【{}】备案映射方式", submit.getDeclareOrderNo(), submit.getDeclareOrderTypes(), goodsRecordMappingWay.getDesc());
            GoodsRecordDTO recordDTO = workSpace.getGoodsRecordDTO(goodsRecordService, customsBookDTO.getId(), submitItem.getRecordNo(), goodsRecordMappingWay.getValue());
            if (recordDTO == null) {
                throw new ArgsErrorException("商品" + submitItem.getRecordNo() + "在账册" + customsBookDTO.getBookNo() + "下未找到备案信息");
            }
            String finalProductId = goodsRecordAssociateService.getFinalProductId(recordDTO, customsBookDTO.getId());
            CustomsBookItemDTO customsBookItemDTO = workSpace.getCustomsBookItemDTO(
                    customsBookItemService, customsBookDTO.getId(), finalProductId, submitItem.getCount(), submitItem.getRecordGnum());
            if (customsBookItemDTO != null) {
                SubmitGoodsInfoAndBookItemDto submitGoodsInfoAndBookItemDto = new SubmitGoodsInfoAndBookItemDto();
                BeanUtils.copyProperties(customsBookItemDTO, submitGoodsInfoAndBookItemDto);
                submitGoodsInfoAndBookItemDto.setGoodsItemCount(submitItem.getCount());
                customsBookItemDTOList.add(submitGoodsInfoAndBookItemDto);
            }
            if (customsBookItemDTO == null || recordDTO == null || recordDTO.getEnable() != 1) {
                notItem = true;
                itemList = new ArrayList<>();
                break;
            } else {
                orderItem.setGoodsModel(customsBookItemDTO.getGoodsModel());
                orderItem.setHsCode(customsBookItemDTO.getHsCode());
                orderItem.setGoodsUnit(customsBookItemDTO.getGoodsUnit());
                orderItem.setOriginCountry(customsBookItemDTO.getOriginCountry());
                orderItem.setBookId(customsBookDTO.getId());
                //采用 跨境>外部>基础的逻辑保存
                orderItem.setRecordNo(finalProductId);
                orderItem.setGoodsNo(submitItem.getSku());
                orderItem.setGoodsName(customsBookItemDTO.getGoodsName());
                orderItem.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                orderItem.setUnitPrice(submitItem.getUnitPrice());
                orderItem.setGoodsCount(submitItem.getCount());
                itemList.add(orderItem);
            }
        }
        customsOrderDO.setItemJson(JSON.toJSONString(itemList));
        // Step::拓展信息
        CustomsOrderExtra extra = new CustomsOrderExtra();
        extra.setBookId(routeDTO.getCustomsBookId());
        extra.setNotItem(notItem);
        extra.setConsigneeProvince(submit.getConsigneeProvince());
        extra.setConsigneeCity(submit.getConsigneeCity());
        extra.setConsigneeDistrict(submit.getConsigneeDistrict());
        extra.setConsigneeStreet(submit.getConsigneeStreet());
        customsOrderDO.setExtraJson(JSON.toJSONString(extra));
        return new Tuple<>(customsOrderDO, customsBookItemDTOList);
    }

    public void save(OrderDO orderDO, RichOrder richOrder) {

        CustomsOrderDO customsOrderDO = richOrder.getCustomsOrderDO();
        if (customsOrderDO != null) {
            if (richOrder.getDeclareIsNew()) {
                this.add(orderDO, customsOrderDO);
            } else {
                // 这里通过路径判断，新的申报路径是否已经存在老的申报路径里，减少查库，存在则认为修改，否则新增
                Boolean inOldAction = richOrder.inOldAction(RouteActionEnum.DECLARE_ORDER.getCode());
                if (inOldAction) {
                    this.update(orderDO, customsOrderDO);
                } else {
                    this.add(orderDO, customsOrderDO);
                }
            }
        }
    }

    private void update(OrderDO orderDO, CustomsOrderDO customsOrderDO) {
        CustomsOrderDTO old = customsOrderService.findByOrder(orderDO.getId(), "");
        CustomsOrderDO template = new CustomsOrderDO();
        template.setId(customsOrderDO.getId());
        template.setBuyerName(customsOrderDO.getBuyerName());
        template.setBuyerIdNumber(customsOrderDO.getBuyerIdNumber());
        template.setBuyerTelNumber(customsOrderDO.getBuyerTelNumber());
        template.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        template.setConsigneeTel(customsOrderDO.getConsigneeTel());
        template.setConsigneeName(customsOrderDO.getConsigneeName());
        template.setTax(customsOrderDO.getTax());
        template.setFreight(customsOrderDO.getFreight());
        template.setDiscount(customsOrderDO.getDiscount());
        template.setItemJson(customsOrderDO.getItemJson());
        template.setPayCompanyId(customsOrderDO.getPayCompanyId());
        template.setAgentCompanyId(customsOrderDO.getAgentCompanyId());
        template.setEbpId(customsOrderDO.getEbpId());
        template.setEbcId(customsOrderDO.getEbcId());
        template.setConsigneeAddress(customsOrderDO.getConsigneeAddress());
        template.setDeclarePayNo(customsOrderDO.getDeclarePayNo());
        template.setTradePayNo(customsOrderDO.getTradePayNo());
        this.updateByIdSection(old.getId(), template, old.getCreateTime());
//        logComponent.asyncLogOpertion(LogCode.LOG_ORDER, customsOrderDO.getDeclareOrderNo(), customsOrderDO.getSn(),
//                CustomsActionStatus.getEnum(customsOrderDO.getStatus()).getDesc(),
//                CustomsActionStatus.getEnum(customsOrderDO.getStatus()).getDesc(), "操作订单重推");
    }

    private void add(OrderDO orderDO, CustomsOrderDO customsOrderDO) {
        customsOrderDO.setOrderId(orderDO.getId());
        customsOrderDO.setOrderSn(orderDO.getSn());
        customsOrderDO.setCreateTime(new Date());
        customsOrderDO.setId(sequenceServiceBaseService.getUniqueId());
        UserUtils.setCreateAndUpdateBy(customsOrderDO);
        customsOrderMapper.insertSelective(customsOrderDO);
//        logComponent.asyncLogOpertion(LogCode.LOG_ORDER, customsOrderDO.getDeclareOrderNo(), customsOrderDO.getSn(),
//                CustomsActionStatus.getEnum(customsOrderDO.getStatus()).getDesc(),
//                CustomsActionStatus.getEnum(customsOrderDO.getStatus()).getDesc(), "操作订单创建");
    }


    private void updateByIdSection(Long id, CustomsOrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        customsOrderMapper.updateByExampleSelective(template, example);
    }
}
