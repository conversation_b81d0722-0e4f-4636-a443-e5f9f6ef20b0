package com.danding.cds.service.mq.isolate;

import com.danding.cds.common.constant.DeclareTopicCons;
import com.danding.cds.service.mq.consumer.DeclareOrderConsumerService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = DeclareTopicCons.CCS_DECLARE_SWXA_HZCENTER_C,
        topic = DeclareTopicCons.CCS_DECLARE_SWXA_HZCENTER_C
)
@RestController
public class OrderDeclareSwxaHzCenterConsumer extends MessageHandler {

    @Autowired
    private DeclareOrderConsumerService orderConsumerService;

    @Override
    public void handle(Object o) throws RuntimeException {
        orderConsumerService.appointDeclareTypeConsumerhandle(o);
    }
}
