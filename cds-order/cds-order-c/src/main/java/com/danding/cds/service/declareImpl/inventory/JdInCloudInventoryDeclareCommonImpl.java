package com.danding.cds.service.declareImpl.inventory;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.service.customs.declare.InCloudDeclareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 京东CEB清单代理申报
 **/
@Service("JD_IN_CLOUD_INVENTORY_DECLARE_COMMON")
@Slf4j
public class JdInCloudInventoryDeclareCommonImpl extends InventoryDeclareAbstract {
    /**
     * 测试用
     */
    @Autowired
    private InCloudDeclareService inCloudDeclareService;

    @Override
    protected InventoryDeclareResult mockDeclareTest(WrapInventoryOrderInfo info) {
        log.info("京东CEB清单代理申报 测试环境-MOCK info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }

    /**
     * @param info 数据
     * @return
     */
    @Override
    protected InventoryDeclareResult declare(WrapInventoryOrderInfo info) {
        log.info("京东CEB清单代理申报 info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }
}
