package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.request.PddCustomsSendDataReceiptRequest;
import com.danding.cds.bean.request.PddOverseaDeclarationFailNotifyRequest;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderReceive;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/7 13:38
 */
@Slf4j
@Service
@RefreshScope
public class PddCustomsSendDataReceiptService {
    @Resource
    private OrderService orderService;
    @Resource
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;
    @Autowired
    private BaseDataService baseDataService;

    @Value("${pdd.host:}")
    private String PDD_HOST;
    @Value("${pdd.send.data.receipt.uri:/pdd/controller/declare/sendData/receipt}")
    private String PDD_SEND_DATA_RECEIPT_URI;
    @Value("${pdd.oversea.declaration.fail.notify.uri:/pdd/controller/fail/notify}")
    private String PDD_OVERSEA_DECLARATION_FAIL_NOTIFY_URI;

    public void sendCeb621RequestXml(WrapInventoryOrderInfo wrapInventoryOrderInfo) {
        PddCustomsSendDataReceiptRequest request = buildCebRequestDataRequest(wrapInventoryOrderInfo, "CEB621");
        doPostRequest(request);
    }

    public void sendCeb311RequestXml(WrapOrderDeclareInfo wrapOrderDeclareInfo) {
        PddCustomsSendDataReceiptRequest request = buildCebRequestDataRequest(wrapOrderDeclareInfo, "CEB311");
        doPostRequest(request);
    }

    /**
     * 推送清单ceb622回执
     *
     * @param receive
     */
    public void sendCeb622CustomsReceipt(CustomsReceive receive) {
        String declareOrderNo = receive.getDeclareOrderNo();
        String ebpCode = receive.getEbpCode();
        String responseMsg = receive.getResponseMsg();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
        String customsStatus = receive.getCustomsStatus();
        if (responseMsg.contains("1313") && responseMsg.contains("订购人购买超过年度限额")) {
            PddOverseaDeclarationFailNotifyRequest request = buildOverseaDeclarationFailNotifyRequest(ebpCode, declareOrderNo);
            doPostFailRequest(request);
        } else {
            PddCustomsSendDataReceiptRequest request = buildCustomsReceiptRequest(declareOrderNo, ebpCode, responseMsg, customsDetail, customsTime, customsStatus, "CEB622");
            doPostRequest(request);
        }
    }

    private PddOverseaDeclarationFailNotifyRequest buildOverseaDeclarationFailNotifyRequest(String ebpCode, String declareOrderNo) {
        log.info("buildOverseaDeclarationFailNotifyRequest declareOrderNo:{} ebpCode:{}", declareOrderNo, ebpCode);
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
        if (ebp == null) {
            throw new ArgsErrorException("电商平台不存在");
        }
        OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("电商平台订单不存在");
        }
        String outOrderNo = orderDTO.getOutOrderNo();
        PddOverseaDeclarationFailNotifyRequest req = PddOverseaDeclarationFailNotifyRequest.builder()
                .declareOrderNo(declareOrderNo)
                .failReason(1)
                .orderSn(outOrderNo)
                .build();
        log.info("buildOverseaDeclarationFailNotifyRequest req:{}", JSON.toJSONString(req));
        return req;
    }

    public void sendCeb312CustomsReceipt(CustomsOrderReceive receive) {
        String declareOrderNo = receive.getDeclareOrderNo();
        String ebpCode = receive.getEbpCode();
        String responseMsg = receive.getResponseMsg();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
        String customsStatus = receive.getCustomsStatus();
        PddCustomsSendDataReceiptRequest request = buildCustomsReceiptRequest(declareOrderNo, ebpCode, responseMsg, customsDetail, customsTime, customsStatus, "CEB312");
        doPostRequest(request);
    }

    private PddCustomsSendDataReceiptRequest buildCustomsReceiptRequest(String declareOrderNo, String ebpCode, String responseMsg, String customsDetail, Long customsTime, String customsStatus, String cebName) {
        log.info("buildCustomsReceiptRequest declareOrderNo:{} ebpCode:{} responseMsg:{} customsDetail:{} customsTime:{} customsStatus:{} cebName:{}", declareOrderNo, ebpCode, responseMsg, customsDetail, customsTime, customsStatus, cebName);
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
        if (ebp == null) {
            throw new ArgsErrorException("电商平台不存在");
        }
        OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        String extraJson = orderDTO.getExtraJson();
        OrderExtra orderExtra = JSON.parseObject(extraJson, OrderExtra.class);
        String routeCode = orderExtra.getSubmit().getRouteCode();
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(routeCode);
        String expressCode = orderExtra.getSubmit().getExpressCode();
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(expressCode);
        if (Objects.isNull(expressDTO)) {
            throw new ArgsErrorException("快递不存在");
        }
        Long expressCompanyId = expressDTO.getExpressCompanyId();
        CompanyDTO expressCompany = baseDataService.getCompanyDTOById(expressCompanyId);
        if (Objects.isNull(expressCompany)) {
            throw new ArgsErrorException("快递企业不存在");
        }
        String customsInventorySn = orderDTO.getCustomsInventorySn();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(customsInventorySn);
        Long areaCompanyId = customsInventoryDTO.getAreaCompanyId();
        CompanyDTO areaCompany = baseDataService.getCompanyDTOById(areaCompanyId);
        CompanyDTO ceb311DeclareCompany = baseDataService.getCompanyDTOById(routeDTO.getOrderDeclareCompanyId());
        CompanyDTO ceb621DeclareCompany = baseDataService.getCompanyDTOById(routeDTO.getListDeclareCompanyId());
        if (Objects.isNull(routeDTO.getLogisticsDeclareCompanyId())) {
            throw new ArgsErrorException("运单报文传输企业不存在");
        }
        CompanyDTO logisticsDeclareCompany = baseDataService.getCompanyDTOById(routeDTO.getLogisticsDeclareCompanyId());
        if (Objects.isNull(logisticsDeclareCompany)) {
            throw new ArgsErrorException("运单报文传输企业不存在");
        }
        List<EntityWarehouseDTO> warehouseDTOList = entityWarehouseService.findByCustomsBookId(routeDTO.getCustomsBookId());
        if (Objects.isNull(warehouseDTOList) || warehouseDTOList.isEmpty()) {
            throw new ArgsErrorException("实体仓不存在");
        }
        EntityWarehouseDTO entityWarehouseDTO = warehouseDTOList.get(0);
        PddCustomsSendDataReceiptRequest request = PddCustomsSendDataReceiptRequest.builder()
                .declareOrderNo(orderDTO.getDeclareOrderNo())
                .ceb311DeclareCompany(ceb311DeclareCompany.getName())
                .ceb311DeclareCompanyCode(ceb311DeclareCompany.getUnifiedCrossBroderCode())
                .ceb621DeclareCompany(ceb621DeclareCompany.getName())
                .ceb621DeclareCompanyCode(ceb621DeclareCompany.getUnifiedCrossBroderCode())
                .cebName(cebName)
                .cebReceiptTime(customsTime)
                .cebReceiptXml(responseMsg)
                .dataType(2)
                .deliveryWarehouseCode(areaCompany.getSpecialClientCode())
                .deliveryWarehouseName(entityWarehouseDTO.getErpWarehouseName())
                .expressCompany(expressCompany.getName())
                .expressDeclareCompany(logisticsDeclareCompany.getName())
                .expressDeclareCompanyCode(logisticsDeclareCompany.getUnifiedCrossBroderCode())
                .expressNo(orderExtra.getSubmit().getLogisticsNo())
                .orderSn(orderDTO.getOutOrderNo())
                .receiptStatus(customsStatus)
                .receiptStatusDesc(customsDetail)
                .traceId(UUID.randomUUID().toString())
                .tradeNo("TRADE_NO_PLACEHOLDER")
                .warehouseAddress(entityWarehouseDTO.getAddress())
                .warehouseCode(areaCompany.getSpecialClientCode())
                .warehouseCompany(areaCompany.getName())
                .warehouseName(entityWarehouseDTO.getErpWarehouseName())
                .build();
        log.info("buildCustomsReceiptRequest:{}", JSON.toJSONString(request));
        return request;
    }

    private PddCustomsSendDataReceiptRequest buildCebRequestDataRequest(WrapBeanInfo wrapBeanInfo, String cebName) {
        log.info("buildCebRequestDataRequest wrapBeanInfo:{} cebName:{}", JSON.toJSONString(wrapBeanInfo), cebName);
        String orderSn = wrapBeanInfo.getMainOrderSn();
        OrderDTO orderDTO = orderService.findBySnSection(orderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("申报单不存在");
        }
        String extraJson = orderDTO.getExtraJson();
        OrderExtra orderExtra = JSON.parseObject(extraJson, OrderExtra.class);
        String expressCode = orderExtra.getSubmit().getExpressCode();
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(expressCode);
        if (Objects.isNull(expressDTO)) {
            throw new ArgsErrorException("快递不存在");
        }
        Long expressCompanyId = expressDTO.getExpressCompanyId();
        CompanyDTO expressCompany = baseDataService.getCompanyDTOById(expressCompanyId);
        if (Objects.isNull(expressCompany)) {
            throw new ArgsErrorException("快递企业不存在");
        }
        String routeCode = wrapBeanInfo.getRouteInfo().getCode();
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(routeCode);
        if (Objects.isNull(routeDTO)) {
            throw new ArgsErrorException("路径不存在");
        }
        List<EntityWarehouseDTO> warehouseDTOList = entityWarehouseService.findByCustomsBookId(routeDTO.getCustomsBookId());
        if (Objects.isNull(warehouseDTOList) || warehouseDTOList.isEmpty()) {
            throw new ArgsErrorException("实体仓不存在");
        }
        EntityWarehouseDTO entityWarehouseDTO = warehouseDTOList.get(0);
        CompanyInfo ceb311DeclareCompany = wrapBeanInfo.getDeclareCompanyDTO();
        Long ceb621DeclareCompanyId = wrapBeanInfo.getRouteInfo().getRouteExtraDto().getListDeclareCompanyId();
        CompanyDTO ceb621DeclareCompany = baseDataService.getCompanyDTOById(ceb621DeclareCompanyId);
        if (Objects.isNull(routeDTO.getLogisticsDeclareCompanyId())) {
            throw new ArgsErrorException("运单报文传输企业不存在");
        }
        CompanyDTO logisticsDeclareCompany = baseDataService.getCompanyDTOById(routeDTO.getLogisticsDeclareCompanyId());
        if (Objects.isNull(logisticsDeclareCompany)) {
            throw new ArgsErrorException("运单报文传输企业不存在");
        }
        Long areaCompanyId = wrapBeanInfo.getAccountBookDto().getAreaCompanyId();
        CompanyDTO areaCompany = baseDataService.getCompanyDTOById(areaCompanyId);
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMsgNoSign(wrapBeanInfo);
        PddCustomsSendDataReceiptRequest request = PddCustomsSendDataReceiptRequest.builder()
                .declareOrderNo(orderDTO.getDeclareOrderNo())
                .ceb311DeclareCompany(ceb311DeclareCompany.getName())
                .ceb311DeclareCompanyCode(ceb311DeclareCompany.getCebCode())
                .ceb621DeclareCompany(ceb621DeclareCompany.getName())
                .ceb621DeclareCompanyCode(ceb621DeclareCompany.getUnifiedCrossBroderCode())
                .cebName(cebName)
                .cebRequestXml(customsMsgDto == null ? "" : customsMsgDto.getCebMsg())
                .dataType(1)
                .deliveryWarehouseCode(areaCompany.getSpecialClientCode())
                .deliveryWarehouseName(entityWarehouseDTO.getErpWarehouseName())
                .expressCompany(expressCompany.getName())
                .expressDeclareCompany(logisticsDeclareCompany.getName())
                .expressDeclareCompanyCode(logisticsDeclareCompany.getUnifiedCrossBroderCode())
                .expressNo(orderExtra.getSubmit().getLogisticsNo())
                .orderSn(orderDTO.getOutOrderNo())
                .traceId(UUID.randomUUID().toString())
                .tradeNo("TRADE_NO_PLACEHOLDER")
                .warehouseAddress(entityWarehouseDTO.getAddress())
                .warehouseCode(areaCompany.getSpecialClientCode())
                .warehouseCompany(areaCompany.getName())
                .warehouseName(entityWarehouseDTO.getErpWarehouseName())
                .build();
        log.info("buildCustomsRequestDataRequest:{}", JSON.toJSONString(request));
        return request;
    }

    private void doPostRequest(PddCustomsSendDataReceiptRequest request) {
        String pddUrl = PDD_HOST + PDD_SEND_DATA_RECEIPT_URI;
        log.info("拼多多报关数据回执上报 url={} param={}", pddUrl, JSON.toJSONString(request));
        HttpRequest httpRequest = doPost(JSON.toJSONString(request), pddUrl);
        String responseBody = httpRequest.body();
        int code = httpRequest.code();
        log.info("拼多多报关数据回执上报 Response-Body:{} HttpRequest-code: {}", JSON.toJSONString(responseBody), code);
    }

    private void doPostFailRequest(PddOverseaDeclarationFailNotifyRequest request) {
        String pddUrl = PDD_HOST + PDD_OVERSEA_DECLARATION_FAIL_NOTIFY_URI;
        log.info("拼多多报关失败上报 url={} param={}", pddUrl, JSON.toJSONString(request));
        HttpRequest httpRequest = doPost(JSON.toJSONString(request), pddUrl);
        String responseBody = httpRequest.body();
        int code = httpRequest.code();
        log.info("拼多多报关失败上报 Response-Body:{} HttpRequest-code: {}", JSON.toJSONString(responseBody), code);
    }

    private static HttpRequest doPost(String request, String pddUrl) {
        HttpRequest httpRequest = HttpRequest.post(pddUrl)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .readTimeout(30000)
                .connectTimeout(15000)
                .send(request);
        return httpRequest;
    }
}
