package com.danding.cds.service.declareImpl.cancel;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 拼多多CEB代理撤单申报
 * @author: 潘本乐（Belep）
 * @create: 2022-02-16 10:33
 **/
@Service("PDD_CEB_PROXY_INVENTORY_CANCEL_DECLARE")
@Slf4j
public class PddCebProxyCancelDeclareImpl extends InventoryCancelDeclareAbstract {

    @Autowired
    private PddDeclareService pddDeclareService;


    @Override
    protected InventoryCancelResult mockDeclareTest(WarpCancelOrderInfo info) {
        log.info("拼多多CEB撤单代理申报，测试环境MOCK");
        pddDeclareService.cloudOfficialDeclareTest(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB623);

        return null;
    }

    @Override
    protected InventoryCancelResult declare(WarpCancelOrderInfo info) {

        pddDeclareService.cloudOfficialDeclare(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB623);
        return null;
    }
}
