package com.danding.cds.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.common.utils.FiltrerSpecialAndEmojUtil;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.config.OrderCBaseConfig;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsItem;
import com.danding.cds.declare.base.component.util.DeclareUtils;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.route.api.vo.RouteResVo;
import com.danding.cds.service.JdSecurityLogService;
import com.danding.cds.service.TmsDeclareLogisticsService;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.cds.service.customs.declare.YuanTongDeclareService;
import com.danding.cds.service.mq.producer.ByteDanceDeclareProducer;
import com.danding.cds.service.mq.producer.PddDeclareProducer;
import com.danding.cds.utils.BuildInfoUtil;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.PddEncodeUtil;
import com.danding.cds.utils.RouteDxpUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
@RefreshScope
public class CustomsLogisticsHandler extends CustomsDeclareOrderHandler<CustomsInventoryHandler> {
    @Autowired
    private CustomsSupport customsSupport;
    @Autowired
    private YuanTongDeclareService yuanTongDeclareService;
    @Autowired
    private PddDeclareService pddDeclareService;
    @Autowired
    private BaseDataService baseDataService;
    @Autowired
    private PddDeclareProducer pddDeclareProducer;
    @Autowired
    private JdSecurityLogService jdSecurityLogService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private RouteService routeService;
    @Resource
    private CustomsLogisticsService customsLogisticsService;
    @DubboReference
    private CompanyService companyService;
    @Resource
    private CustomsStatusMappingService customsStatusMappingService;
    @Resource
    private OrderService orderService;

    @Autowired
    private TmsDeclareLogisticsService tmsDeclareLogisticsService;

    @Resource
    private CustomsInventoryService customsInventoryService;


    @Autowired
    private ByteDanceDeclareProducer byteDanceDeclareProducer;

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Autowired
    private OrderCBaseConfig orderCBaseConfig;

    @Value("${pdd.host:}")
    private String PDD_HOST;

    @Value("${filter.special.char.config:}")
    private String filterSpecialCharConfig;


    @Override
    protected DeclareEnum declareEnum() {
        return DeclareEnum.SHIPMENT;
    }

    /**
     * 要注入要Autowire
     *
     * @param next
     */
    @Autowired
    public CustomsLogisticsHandler(CustomsInventoryHandler next) {
        super(next);
    }

    @Override
    protected Boolean checkHandle(OrderDTO request) {
        String actionJson = request.getActionJson();
        if (!actionJson.contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            log.info("[op:CustomsInventoryHandler-check] declareNo={} actionJson 不包含 DECLARE_LOGISTICS", request.getDeclareOrderNo());
            return false;
        }
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(request.getId(), request.getCustomsLogisticsSn());
        if (customsLogisticsDTO == null) {
            return false;
        }
        return CustomsActionStatus.DEC_WAIT.getValue().equals(customsLogisticsDTO.getStatus())
                || (CustomsActionStatus.DEC_ING.getValue().equals(customsLogisticsDTO.getStatus()) && !request.getExceptionFlag());
    }

    @Override
    protected void handle(OrderDTO orderDTO) {
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
//        OrderDTO orderDTO = orderService.findBySnSection(customsLogisticsDTO.getOrderSn());
        if (CustomsActionStatus.DEC_ING.getValue().equals(customsLogisticsDTO.getStatus())) {
            return;
        }
        try {
            // Step::各类配置有效性校验
            String configError = "";
            CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(orderDTO.getEbpId());
//        CompanyDTO agentCompany = companyService.findById(customsLogisticsDTO.getAgentCompanyId());
            CompanyResVo agentCompany = companyService.findUnifiedCrossInfoByIdV2(customsLogisticsDTO.getAgentCompanyId());
//            JSONObject agentExtra = JSON.parseObject(agentCompany.getExtraJson());
//            String dxpId = agentExtra.getString("orderDxpId");
            if (agentCompany.getEnable() != 1) {
                configError += "申报企业未启用;";
            }
            CompanyDTO logisticsCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsLogisticsDTO.getLogisticsCompanyId());
            if (logisticsCompany.getEnable() != 1) {
                configError += "物流企业未启用;";
            }
            if (!StringUtils.isEmpty(configError)) {
                // Step::添加配置异常
                CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("CONFIG_ERROR");
                orderService.addExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO.getId(), configError, orderDTO.getCreateTime());
                return;
            }
            //过滤特殊字符
            if (!judgeIsLegalAndReplace(customsLogisticsDTO, orderDTO, ebpCompany)) return;
//        customsLogisticsService.updateByPush(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_ING);
            Integer declareFrequency = customsLogisticsDTO.getDeclareFrequency();
            declareFrequency = declareFrequency == null ? 1 : declareFrequency + 1;
            customsLogisticsService.updateByPush(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_ING, declareFrequency);
            // Step::申报数据模型构建
            WrapShipmentInfo info = buildWrapShipmentInfo(customsLogisticsDTO, agentCompany, orderDTO);
            // 设置下路由信息
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            OrderSensitivePlainText orderSensitivePlainText = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getSensitivePlainText).orElse(null);
            info.setPlainText(orderSensitivePlainText);
            String routeCode = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getRouteCode).orElse(null);
            if (StringUtils.isNotBlank(routeCode)) {
                RouteInfoResVo routeDTO = routeService.findRouteByCode(routeCode);
                BuildInfoUtil.setWrapRouteInfo(info, routeDTO);
                // 获取下账册信息
                Long accountId = info.getCustomsBookId();
                CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(accountId);
                if (Objects.nonNull(customsBookResVo)) {
                    AccountBookDto accountBookDto = new AccountBookDto();
                    BeanUtils.copyProperties(customsBookResVo, accountBookDto);
                    // 设置账册
                    info.setAccountBookDto(accountBookDto);
                }
            }
            String dxpId = RouteDxpUtils.getConfigDxpIdByType(info);
            info.setDxpId(dxpId);
            log.info("运单申报 info={}", JSON.toJSONString(info));
            //更新内部流转状态和记录申报记录
            this.updateOrderInternalStatusAndDeclareRecord(info, orderDTO);
            if (!StringUtils.isEmpty(customsLogisticsDTO.getLogisticsNo())) {
                if (customsLogisticsDTO.getLastDeclareTime() != null && new DateTime(customsLogisticsDTO.getLastDeclareTime()).isAfter(DateTime.now().minusMinutes(10))) {
                    // 申报中，且更新时间小于十分钟，则不允许重推
                    log.info("[op:CustomsLogisticsHandler-handle] customsLogisticsDTO = {}", JSON.toJSONString(customsLogisticsDTO));
                    return;
                }
                // 判断是否走tms快递运单申报， 若不是tms快递运单申报 但是代理申报选择了【快递运单申报】方式 则改用默认申报方式
                RouteDeclareConfig routeDeclareConfig = info.getRouteDeclareHighConfig(DeclareEnum.SHIPMENT);
                if (!tmsDeclareLogisticsService.isTmsDeclare(info.getExpressCode(), routeDeclareConfig)
                        && Objects.nonNull(routeDeclareConfig)
                        && Objects.equals(routeDeclareConfig.getProxyImpl(), "TMS_SHIPMENT_DECLARE")) {
                    routeDeclareConfig.setProxyImpl(null);
                    routeDeclareConfig.setProxyCode(null);
                }
                if (info.dynamicDeclareEnable()) {
                    DeclareUtils.shipmentDeclare(info);
                    return;
                }

                if ("KUNMING-JZXLS".equals(orderExtra.getSubmit().getRouteCode())) {
                    try {
                        yuanTongDeclareService.logisticsDeclare(info, customsLogisticsDTO, orderDTO);
                    } catch (Exception e) {
                        log.error("圆通申报异常", e.getMessage());
                    }
                    return;
                }
                if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), customsLogisticsDTO.getDeclareOrderNo())) {
//                pddDeclareService.cloudLogisticsDeclare(info);
                    log.info("申报单号：{}，发送运单MQ消息-{}", customsLogisticsDTO.getDeclareOrderNo(), info);
                    //发送运单MQ
                    pddDeclareProducer.logisticsDeclareOrderSend(info);
                } else if (byteDanceDeclareService.isDeclareWithinCloud(ebpCompany.getCode(), orderDTO.getExtraJson())) {
                    byteDanceDeclareProducer.logisticsOrderSend(info);
                } else {
                    customsSupport.clearShipmentDeclare(info);
                }
            }
        } catch (Exception ex) {
            String message = ExceptionJoinUtil.getExceptionMessage(ex);
            log.error("申报单号：{} ,运单申报Handler处理异常：{}", customsLogisticsDTO.getDeclareOrderNo(), message, ex);
            // 处理异常，这里更改下申报状态为带申报，并维护下
            customsLogisticsService.updateStatusResetDeclareTime(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_WAIT);
            // Step::添加配置异常
            CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("DECLARE_PROCESSING_ERROR");
            if (Objects.nonNull(mappingDTO)) {
                if (StringUtils.isNotBlank(message) && message.length() > 1024) {
                    message = message.substring(0, 1024);
                }
                orderService.addExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO.getId(), "运单申报Handler处理异常:" + message, orderDTO.getCreateTime());
            }
        }

        // 给下京东安全追踪日志
        jdSecurityLogService.jdSecurityLogSend(orderDTO.getEbpId(), orderDTO.getSystemGlobalSn(), DeclareEnum.SHIPMENT);
    }

    /**
     * 更新order表内部流转状态与申报记录
     *
     * @param info
     * @param orderDTO
     */
    public void updateOrderInternalStatusAndDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO) {
        if (orderCBaseConfig.getDeclareRouteRecordNewType()) {
            CustomsDeclareUtils.updateDeclareRecord(info, orderDTO, info.getDeclareCompanyCebCode(), DeclareEnum.SHIPMENT);
        } else {
            CustomsDeclareUtils.buildDeclareRecord(info, orderDTO, DeclareEnum.SHIPMENT);
        }
        orderService.updateOrderInternalStatusAndDeclareRecord(orderDTO, OrderInternalEnum.DECLARING.getCode());
    }

    /**
     * 设置路由信息
     *
     * @param wrapShipmentInfo
     * @param routeDTO
     */
    private void setRoute(WrapShipmentInfo wrapShipmentInfo, RouteResVo routeDTO) {
        if (routeDTO == null || wrapShipmentInfo == null) {
            return;
        }
        RouteInfo route = new RouteInfo();
        route.setName(routeDTO.getName());
        route.setDeclareWay(routeDTO.getDeclareWay());
        route.setCode(routeDTO.getCode());
        wrapShipmentInfo.setRouteInfo(route);
    }


    private WrapShipmentInfo buildWrapShipmentInfo(CustomsLogisticsDTO customsLogisticsDTO, CompanyDTO agentCompany, OrderDTO orderDTO) {
        WrapShipmentInfo info = new WrapShipmentInfo();
        info.setDeclareTimes(customsLogisticsDTO.getDeclareFrequency());
        info.setMainOrderId(customsLogisticsDTO.getOrderId());
        info.setMainOrderSn(customsLogisticsDTO.getOrderSn());
        info.setStatus(customsLogisticsDTO.getStatus());
        info.setConsignee(customsLogisticsDTO.getConsigneeName());
        info.setConsigneeProvince(customsLogisticsDTO.getConsigneeProvince());
        info.setConsigneeCity(customsLogisticsDTO.getConsigneeCity());
        info.setConsigneeDistrict(customsLogisticsDTO.getConsigneeDistrict());
        info.setConsigneeStreet(customsLogisticsDTO.getConsigneeStreet());
        info.setConsigneeAddress(customsLogisticsDTO.getConsigneeAddress());
        info.setConsigneeTel(customsLogisticsDTO.getConsigneeTel());
        info.setLogisticsNo(customsLogisticsDTO.getLogisticsNo());
        info.setFreight(customsLogisticsDTO.getFeeAmount().toString());
        info.setWeight(customsLogisticsDTO.getGrossWeight().toString());
        info.setPackNo("1");
        List<CustomsLogisticsItem> logisticsItemList = JSON.parseArray(customsLogisticsDTO.getItemJson(), CustomsLogisticsItem.class);
        /*StringBuilder name = new StringBuilder();
        for (CustomsLogisticsItem logisticsItem : logisticsItemList) {
            name.append(logisticsItem.getName()).append("|");
        }*/
        info.setGoodsInfo(logisticsItemList.get(0).getName());
        info.setWorth(customsLogisticsDTO.getGoodsTotalAmount().toString());
        info.setCustoms(CustomsDistrictEnum.getEnum(customsLogisticsDTO.getCustoms()).getCustoms());
        info.setPortCode(CustomsDistrictEnum.getEnum(customsLogisticsDTO.getCustoms()).getPortCode());
        info.setSn(customsLogisticsDTO.getSn());
        info.setDeclareCompanyDTO(buildCompany(agentCompany, customsLogisticsDTO.getCustoms()));
        CompanyDTO logisticsCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsLogisticsDTO.getLogisticsCompanyId());
        info.setLogisticsCompanyDTO(buildCompany(logisticsCompany, customsLogisticsDTO.getCustoms()));
        info.setExpressCode(customsLogisticsDTO.getExpressCode());
        info.setDeclareOrderNo(customsLogisticsDTO.getDeclareOrderNo());
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbpId());
        CompanyDTO ebcCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbcId());
        info.setEbpCompanyDTO(buildCompany(ebpCompany, customsLogisticsDTO.getCustoms()));
        info.setEbcCompanyDTO(buildCompany(ebcCompany, customsLogisticsDTO.getCustoms()));

        // 中通国际 tms申报 需要收件人身份证号，ccs没有收件人的身份证号 暂时用订购人身份证号代替
        info.setIdCardType(customsInventoryDTO.getBuyerIdType());
        info.setIdCardNo(customsInventoryDTO.getBuyerIdNumber());

        if (Objects.nonNull(orderDTO)) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            if (Objects.nonNull(orderExtra.getSubmit().getSensitivePlainText())) {
                OrderSensitivePlainText sensitivePlainText = orderExtra.getSubmit().getSensitivePlainText();
                info.setPlainText(sensitivePlainText);
            }
        }
        return info;
    }

    private CompanyInfo buildCompany(CompanyDTO companyDTO, String customs) {
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getUnifiedCrossBroderCode());
        ebpInfo.setCebName(companyDTO.getName());
        ebpInfo.setCode(companyDTO.getCode());
        ebpInfo.setName(companyDTO.getName());
//        CompanyDistrictDTO ebpDis = companyDTO.getDistrict(CustomsDistrictEnum.getEnum(customs));
//        if (ebpDis == null) {
//            ebpInfo.setCode(companyDTO.getCode());
//            ebpInfo.setName(companyDTO.getName());
//        } else {
//            ebpInfo.setCode(ebpDis.getCode());
//            ebpInfo.setName(ebpDis.getName());
//        }

        JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
        String dxpId = agentExtra.getString("orderDxpId");
        if (!org.apache.commons.lang3.StringUtils.isEmpty(dxpId)) {
            ebpInfo.setDxpId(dxpId);
        }

        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }
        return ebpInfo;
    }

    public boolean judgeIsLegalAndReplace(CustomsLogisticsDTO customsLogisticsDTO, OrderDTO orderDTO, CompanyDTO ebpCompany) {
        if (StringUtils.isEmpty(filterSpecialCharConfig)) {
            log.info("judgeIsLegalAndReplace 没有配置参数");
            return true;
        }
        FilterSpecialConfigDTO orderDeclareDTO = JSON.parseObject(filterSpecialCharConfig, FilterSpecialConfigDTO.class);
        if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), orderDTO.getDeclareOrderNo())) {
            if (orderDeclareDTO.getNotFilterPlatformCodeList().contains("PDD")) {
                log.info("judgeIsLegalAndReplace设置PDD不参与过滤");
                return true;
            }
        }
        //收件人
        log.debug("CustomsLogisticsHandler judgeIsLegalAndReplace过滤收件人前{}", customsLogisticsDTO.getConsigneeName());
        String consigneeName = FiltrerSpecialAndEmojUtil.filterEmojis(customsLogisticsDTO.getConsigneeName());
        log.debug("CustomsLogisticsHandler judgeIsLegalAndReplace过滤收件人后{}", consigneeName);
        //收件人地址
        log.debug("CustomsLogisticsHandler judgeIsLegalAndReplace过滤收件人地址前{}", customsLogisticsDTO.getConsigneeAddress());
        String consigneeAddress = FiltrerSpecialAndEmojUtil.filterEmojis(customsLogisticsDTO.getConsigneeAddress());
        log.debug("CustomsLogisticsHandler judgeIsLegalAndReplace过滤收件人地址后{}", consigneeAddress);
        if (StringUtils.isEmpty(consigneeAddress) || StringUtils.isEmpty(consigneeName)) {
            StringBuilder builder = new StringBuilder();
            builder.append("<font color=\\\"warning\\\">**" + "【订单异常】收件人或详细地址是纯特殊字符，无法过滤" + "**</font>\\n\n");
            builder.append(String.format("申报单号：%s", orderDTO.getDeclareOrderNo()) + "\r\n");
            String content = builder.toString();
            WechatNotifyUtils.wechatNotifyMd(orderDeclareDTO.getWebHook(), orderDeclareDTO.getPhoneList(), content);
            return false;
        }
        customsLogisticsDTO.setConsigneeName(consigneeName);
        customsLogisticsDTO.setConsigneeAddress(consigneeAddress);
        return true;
    }
}
