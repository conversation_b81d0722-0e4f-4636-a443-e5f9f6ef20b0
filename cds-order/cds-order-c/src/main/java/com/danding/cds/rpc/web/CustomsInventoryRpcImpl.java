package com.danding.cds.rpc.web;

import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsSingleInventoryEsDTO;
import com.danding.cds.customs.inventory.api.dto.ExportCusOfficerDataReqVO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.invenorder.api.dto.InventoryTaxStatisticsDTO;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.message.api.process.OrderCustomsInventoryMessage;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.v2.bean.vo.AutoRetryTaxConfig;
import com.danding.cds.v2.bean.vo.req.JdHourlyReportReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: yousx
 * @Date: 2023/12/12
 * @Description:
 */
@DubboService
public class CustomsInventoryRpcImpl implements CustomsInventoryRpc {

    @Resource
    private CustomsInventoryService customsInventoryService;


    @Override
    public CustomsInventoryDTO findById(Long id) {
        return customsInventoryService.findById(id);
    }

    @Override
    public void initDataToEs(List<CustomsInventoryDTO> customsInventoryDTO) {
        customsInventoryService.initDataToEs(customsInventoryDTO);
    }

    @Override
    public ListVO<CustomsSingleInventoryEsDTO> pagingES(SingleInvtOrderSearch search) {
        return customsInventoryService.pagingES(search);
    }

    @Override
    public List<CustomsInventoryDTO> getInventoryByInventoryNos(String inventoryNos) {
        return customsInventoryService.getInventoryByInventoryNos(inventoryNos);
    }

    @Override
    public InventoryTaxStatisticsDTO taxStatisticsByEs(SingleInvtOrderSearch search) {
        return customsInventoryService.taxStatisticsByEs(search);
    }

    @Override
    public ListVO<CustomsInventoryDTO> paging(SingleInvtOrderSearch search) {
        return customsInventoryService.paging(search);
    }

    @Override
    public List<CustomsInventoryDTO> getInventorysByStatus(Integer status, Integer customsStatus, String detail) {
        return customsInventoryService.getInventorysByStatus(status, customsStatus, detail);
    }

    @Override
    public void updateSnWithFix(String sn, String newSn, Date sectionDate) {
        customsInventoryService.updateSnWithFix(sn, newSn, sectionDate);
    }

    @Override
    public CustomsInventoryDTO findByOrder(Long orderId, String sn) {
        return customsInventoryService.findByOrder(orderId, sn);
    }

    @Override
    public List<CustomsInventoryDTO> findBySnList(List<String> snList) {
        return customsInventoryService.findBySnList(snList);
    }

    @Override
    public List<StockOccupiedCountResDTO> getStockOccupiedCountListByEs(List<Long> customsBookItemIdList, List<Long> bookIdList) {
        return customsInventoryService.getStockOccupiedCountListByEs(customsBookItemIdList, bookIdList);
    }

    @Override
    public CustomsInventoryDTO findByEbcAndNo(Long ebcId, String declareOrderNo) {
        return customsInventoryService.findByEbcAndNo(ebcId, declareOrderNo);
    }

    @Override
    public CustomsInventoryDTO findBySnSection(String sn) {
        return customsInventoryService.findBySnSection(sn);
    }

    @Override
    public List<CustomsInventoryDTO> findByCustomsLogisticsNo(List<String> logisticsNo) {
        return customsInventoryService.findByCustomsLogisticsNo(logisticsNo);
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo(String logisticsNo) {
        return customsInventoryService.findByLogisticsNo(logisticsNo);
    }

    @Override
    public CustomsInventoryDTO queryByLogisticsNo(String logisticsNo, Integer exitRegionStatus) {
        return customsInventoryService.queryByLogisticsNo(logisticsNo, exitRegionStatus);
    }

    @Override
    public void updateByLastDeclareTime(String orderSn) {
        customsInventoryService.updateByLastDeclareTime(orderSn);
    }

    @Override
    public void updateLastDeclareTime(String sn) {
        customsInventoryService.updateLastDeclareTime(sn);
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo90Days(String logisticsNo) {
        return customsInventoryService.findByLogisticsNo90Days(logisticsNo);
    }

    @Override
    public List<CustomsInventoryDTO> listBySnsSection(List<String> sns) {
        return customsInventoryService.listBySnsSection(sns);
    }

    @Override
    public List<CustomsInventoryDTO> listBySnsSection(List<String> sns, Date sectionDate) {
        return customsInventoryService.listBySnsSection(sns, sectionDate);
    }

    @Override
    public CustomsInventoryItemDTO getItemById90Days(Long id) {
        return customsInventoryService.getItemById90Days(id);
    }

    @Override
    public List<CustomsInventoryItemDTO> getItemByInventoryId90Days(Long inventoryId) {
        return customsInventoryService.getItemByInventoryId90Days(inventoryId);
    }

    @Override
    public CustomsInventoryDTO findByLogistics90Days(Long expressId, String logisticsNo) {
        return customsInventoryService.findByLogistics90Days(expressId, logisticsNo);
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo90Days(Long expressId, String logisticsNo) {
        return customsInventoryService.findByLogisticsNo90Days(expressId, logisticsNo);
    }

    @Override
    public List<CustomsInventoryDTO> listByLogistics90Days(String logisticsNo) {
        return customsInventoryService.listByLogistics90Days(logisticsNo);
    }

    @Override
    public List<CustomsInventoryDTO> listByLogistics90Days(List<String> logisticsNo) {
        return customsInventoryService.listByLogistics90Days(logisticsNo);
    }

    @Override
    public List<CustomsInventoryDTO> listByInventoryIdsSection(List<Long> ids, Date sectionDate) {
        return customsInventoryService.listByInventoryIdsSection(ids, sectionDate);
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByIdSection(Long id, Date sectionDate) {
        return customsInventoryService.listItemByIdSection(id, sectionDate);
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByIdSectionNoFilter(Long id, Date sectionDate) {
        return customsInventoryService.listItemByIdSectionNoFilter(id, sectionDate);

    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByBookItemIdsSection(List<Long> bookItemIds, Date sectionDate) {
        return customsInventoryService.listItemByBookItemIdsSection(bookItemIds, sectionDate);
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByProductIdSection(List<String> productIds, Date sectionDate) {
        return customsInventoryService.listItemByProductIdSection(productIds, sectionDate);
    }

    @Override
    public Map<Long, List<CustomsInventoryItemDTO>> listItemByInventoryS(List<CustomsInventoryDTO> inventoryDTOS) {
        return customsInventoryService.listItemByInventoryS(inventoryDTOS);
    }

    @Override
    public List<CustomsInventoryDTO> listByStatus(Integer status, Integer limit, Date sectionDate) {
        return customsInventoryService.listByStatus(status, limit, sectionDate);
    }

    @Override
    public List<CustomsInventoryDTO> listByStatusAndCreateTime(Integer status, Integer limit, Date createFrom, Date createTo) {
        return customsInventoryService.listByStatusAndCreateTime(status, limit, createFrom, createTo);
    }

    @Override
    public CustomsInventoryDTO findByInventoryNo90Days(String inventoryNo) {
        return customsInventoryService.findByInventoryNo90Days(inventoryNo);
    }

    @Override
    public CustomsInventoryDTO findByDeclareNo90Days(String declareNo) {
        return customsInventoryService.findByDeclareNo90Days(declareNo);
    }

    @Override
    public CustomsInventoryDTO findByDeclareNo(String declareNo, Date create) {
        return customsInventoryService.findByDeclareNo(declareNo, create);
    }

    @Override
    public CustomsInventoryDTO findByDeclareNoAndEbpId(String declareNo, Long ebpId, Date create) {
        return customsInventoryService.findByDeclareNoAndEbpId(declareNo, ebpId, create);
    }

    @Override
    public CustomsInventoryDTO findByEbcIdAndDeclareNo90Days(Long ebcId, String declareOrderNo) {
        return customsInventoryService.findByEbcIdAndDeclareNo90Days(ebcId, declareOrderNo);
    }

    @Override
    public CustomsInventoryDTO findByDeclareOrderNoFull(String declareOrderNo) {
        return customsInventoryService.findByDeclareOrderNoFull(declareOrderNo);
    }

    @Override
    public void updateStatusSection(Long id, Integer status, Date sectionDate) {
        customsInventoryService.updateStatusSection(id, status, sectionDate);
    }

    @Override
    public void updateIsOccupiedStockSection(Long id, Boolean isOccupiedStock, Date sectionDate) {
        customsInventoryService.updateIsOccupiedStockSection(id, isOccupiedStock, sectionDate);
    }

    @Override
    public void updateLogisticsSection(Long id, String logisticsNo, Date sectionDate) {
        customsInventoryService.updateLogisticsSection(id, logisticsNo, sectionDate);
    }

    @Override
    public void updatePayerSection(Long id, String payerId, String payerName, Date sectionDate) {
        customsInventoryService.updatePayerSection(id, payerId, payerName, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate) {
        customsInventoryService.updateByPush(id, status, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate, Integer declareFrequency) {
        customsInventoryService.updateByPush(id, status, sectionDate, declareFrequency);
    }

    @Override
    public void updateBuyerInfo(Long id, String buyerIdNum, String buyerName, Date sectionDate) {
        customsInventoryService.updateBuyerInfo(id, buyerIdNum, buyerName, sectionDate);
    }

    @Override
    public void updateStatus(Long id, Integer status, Date sectionDate) {
        customsInventoryService.updateStatus(id, status, sectionDate);
    }

    @Override
    public void updateStatusResetDeclareTime(String sn, CustomsActionStatus status) {
        customsInventoryService.updateStatusResetDeclareTime(sn, status);
    }

    @Override
    public void updateAfterStatus(Long id, Integer afterStatus, Date sectionDate) {
        customsInventoryService.updateAfterStatus(id, afterStatus, sectionDate);
    }

    @Override
    public void updateAfterStatus(Long id, Integer afterStatus, Integer status, Date sectionDate) {
        customsInventoryService.updateAfterStatus(id, afterStatus, status, sectionDate);
    }

    @Override
    public void updateAfterStatus(CustomsInventoryDTO customsInventoryDTO, Integer afterStatus) {
        customsInventoryService.updateAfterStatus(customsInventoryDTO, afterStatus);
    }

    @Override
    public void updateByCustomsPass(Long id, Long customsPassTime, String preNo, String invtNo, Date sectionDate) {
        customsInventoryService.updateByCustomsPass(id, customsPassTime, preNo, invtNo, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, String customsStatus, String customsDetail, Long customsPassTime, Date sectionDate) {
        customsInventoryService.updateByCustomsPass(id, customsStatus, customsDetail, customsPassTime, sectionDate);
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate) {
        customsInventoryService.updateByCustomsActive(id, customsStatus, customsDetail, lastCustomsTime, sectionDate);
    }

    @Override
    public void updateByBaseinfo(Long id, String buyerIdNumber, String buyerName, String consigneeAddress, String extraJson, Date sectionDate) {
        customsInventoryService.updateByBaseinfo(id, buyerIdNumber, buyerName, consigneeAddress, extraJson, sectionDate);
    }

    @Override
    public void updateByItemBaseInfo(Long id, String itemNo, String extraJson, Date sectionDate) {
        customsInventoryService.updateByItemBaseInfo(id, itemNo, extraJson, sectionDate);
    }

    @Override
    public void updateByItemBaseInfo(Long id, Long customsBookItemId, String itemName, String extraJson, Date sectionDate) {
        customsInventoryService.updateByItemBaseInfo(id, customsBookItemId, itemName, extraJson, sectionDate);
    }

    @Override
    public void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date sectionDate) {
        customsInventoryService.updateExitRegionStatus(id, exitRegionStatus, sectionDate);
    }

    @Override
    public void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date finishTime, Date sectionDate) {
        customsInventoryService.updateExitRegionStatus(id, exitRegionStatus, finishTime, sectionDate);

    }

    @Override
    public void updateExitRegionStatusBySn(List<CustomsInventoryDTO> inventoryDTOs, Integer exitRegionStatus, Date finishTime) {
        customsInventoryService.updateExitRegionStatusBySn(inventoryDTOs, exitRegionStatus, finishTime);
    }

    @Override
    public void rePush(String sn, Boolean sendNow) {
        customsInventoryService.rePush(sn, sendNow);
    }

    @Override
    public void timeOut(CustomsInventoryDTO inventoryDTO) {
        customsInventoryService.timeOut(inventoryDTO);
    }

    @Override
    public void updateByItemBaseInfo(CustomsInventoryItemDTO inventoryItemDTO, Date sectionDate) {
        customsInventoryService.updateByItemBaseInfo(inventoryItemDTO, sectionDate);
    }

    @Override
    public ListVO<CustomsInventoryDTO> pagingTemp(SingleInvtOrderSearch search) {
        return customsInventoryService.pagingTemp(search);
    }

    @Override
    public CustomsInventoryDTO buildCustomsSingleInventoryEsDO(CustomsInventoryDTO customsInventoryDTO) {
        return customsInventoryService.buildCustomsSingleInventoryEsDO(customsInventoryDTO);
    }

    @Override
    public void reloadIndex() {
        customsInventoryService.reloadIndex();
    }

    @Override
    public Object getByInventoryId(Long id) {
        return customsInventoryService.getByInventoryId(id);
    }

    @Override
    public Object delIndexByMonth(Integer month) {
        return customsInventoryService.delIndexByMonth(month);
    }

    @Override
    public int findCount(String tableName) {
        return customsInventoryService.findCount(tableName);
    }

    @Override
    public ListVO<CustomsInventoryDTO> pagingTemp(int currentPage, Integer pageSize, String tableName) {
        return customsInventoryService.pagingTemp(currentPage, pageSize, tableName);
    }

    @Override
    public void getJdHourlyReportJob(JdHourlyReportReqVo reportReqVo) {
        customsInventoryService.getJdHourlyReportJob(reportReqVo);
    }

    @Override
    public void jieztechCallback(OrderCustomsInventoryMessage message) throws Exception {
        customsInventoryService.jieztechCallback(message);
    }

    @Override
    public void updByEndorsementsStatus(List<String> logisticsNo, InventoryReviewStatus reviwStatus) {
        customsInventoryService.updByEndorsementsStatus(logisticsNo, reviwStatus);
    }

    @Override
    public void updateEndorsementsStatusBySn(List<String> inventorySnList, Integer endorsementStatus) {
        customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, endorsementStatus);
    }

    @Override
    public void updateTax(Long id, Date createTime, BigDecimal amount) {
        customsInventoryService.updateTax(id, createTime, amount);
    }

    @Override
    public List<CustomsInventoryDTO> getNotReturnTaxOrder(AutoRetryTaxConfig autoRetryTaxConfig, List<Long> assureCompanyIdList) {
        return customsInventoryService.getNotReturnTaxOrder(autoRetryTaxConfig, assureCompanyIdList);
    }

    @Override
    public void updateInventoryItemList(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> updateList) {
        customsInventoryService.updateInventoryItemList(customsInventoryDTO, updateList);
    }

    @Override
    public void cleanLastDeclareTime(CustomsInventoryDTO customsInventoryDTO) {
        customsInventoryService.cleanLastDeclareTime(customsInventoryDTO);
    }

    @Override
    public List<CustomsInventoryDTO> getCusPersonInvOrderDTOListByUpdateTime(Long createTimeFrom, Long createTimeTo, Long updateTimeFrom, Long updateTimeTo) {
        return customsInventoryService.getCusPersonInvOrderDTOListByUpdateTime(createTimeFrom, createTimeTo, updateTimeFrom, updateTimeTo);
    }

    @Override
    public List<CustomsSingleInventoryEsDTO> getCusPersonInvOrderEsDTOList(ExportCusOfficerDataReqVO reqVO) {
        return customsInventoryService.getCusPersonInvOrderEsDTOList(reqVO);
    }

    @Override
    public Boolean judgeExist30DaysInByProductId(String productId) {
        return customsInventoryService.judgeExist30DaysInByProductId(productId);
    }

    @Override
    public void updateIsOccupiedStockBySnList(List<String> inventorySnList, Boolean isOccupiedStock) {
        customsInventoryService.updateIsOccupiedStockBySnList(inventorySnList, isOccupiedStock);
    }

    @Override
    public void updateTaxBillStatusSection(Integer taxBillStatus, Long id, Date sectionTime) {
        customsInventoryService.updateTaxBillStatusSection(taxBillStatus, id, sectionTime);
    }

    @Override
    public List<CustomsInventoryDTO> checkTaxBillStatusByTimeRange(Date beginTime, Date endTime) {
        return customsInventoryService.checkTaxBillStatusByTimeRange(beginTime, endTime);
    }

    @Override
    public void updateCheckOutTimeAndStatusSection(Long id, Integer checkOutStatus, Date finishDate, Date sectionTime) {
        customsInventoryService.updateCheckOutTimeAndStatusSection(id, checkOutStatus, finishDate, sectionTime);
    }

    @Override
    public void updatTotalRefundTaxSection(BigDecimal totalRefundTax, Long id, Date sectionTime) {
        customsInventoryService.updatTotalRefundTaxSection(totalRefundTax, id, sectionTime);
    }

    @Override
    public void updateCheckoutStatusBySn(String sn, Integer code) {
        customsInventoryService.updateCheckoutStatusBySn(sn, code);
    }

    @Override
    public void updateCustomsStatus(String sn, Integer status) throws ArgsErrorException {
        customsInventoryService.updateCustomsStatus(sn, status);
    }

    @Override
    public void receiveCainiaoInventoryExitArea(String globalSystemNo) {
        customsInventoryService.receiveCainiaoInventoryExitArea(globalSystemNo);
    }

    @Override
    public void getJdHourlyReportEmailJob(JdHourlyReportReqVo reportReqVo) {
        customsInventoryService.getJdHourlyReportEmailJob(reportReqVo);
    }
}
