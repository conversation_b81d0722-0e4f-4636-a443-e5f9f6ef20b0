package com.danding.cds.service.convert;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderExtraDto;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.DateHelper;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.data.service.SequenceServiceBaseService;
import com.danding.cds.order.base.mapper.OrderMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.DeclareOrderTypeEnums;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Component
public class OrderConvert {

    @Resource
    private SequenceServiceBaseService sequenceServiceBaseService;

    @Resource
    private OrderMapper orderMapper;

    @Resource
    private BaseDataService baseDataService;

    @Deprecated
    public OrderDO preOrder(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {
        OrderSubmitDto submitDto = new OrderSubmitDto();
        BeanUtils.copyProperties(submit, submitDto);
        return preOrder(submitDto);
    }

    public OrderDO preOrder(OrderSubmitDto submit) {
        return this.preOrder(submit, null, null);
    }

    public OrderDO preOrder(OrderSubmitDto submit, RouteDTO routeDTO, CompanyDTO ebp) {
        return this.preOrder(submit, routeDTO, ebp, submit);
    }

    public OrderDO preOrder(OrderSubmitDto submit, RouteDTO routeDTO, CompanyDTO ebp, OrderSubmitDto orderSubmitDto) {
        OrderDO orderDO = new OrderDO();
        BeanUtils.copyProperties(submit, orderDO);
        orderDO.setTenantryId(SimpleTenantHelper.getTenantId());
        orderDO.setSn(sequenceServiceBaseService.generateOrderSn());
        if (Objects.isNull(routeDTO)) {
            routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
            if (Objects.isNull(routeDTO)) {
                throw new ArgsErrorException("提交的申报路径编码不存在，路径标识：" + submit.getRouteCode());
            }
        }
        if (Objects.isNull(ebp)) {
            ebp = baseDataService.getUnifiedCrossCodeCompanyById(routeDTO.getEbpId());
            if (Objects.isNull(ebp)) {
                throw new ArgsErrorException("申报路径编码对应的电商平台未找到");
            }
        }
        orderDO.setInternalStatus(OrderInternalEnum.CREATE_WAIT_PUSH.getCode());
        orderDO.setEbpId(ebp.getId());
        orderDO.setStatus(OrderStatus.DEC_WAIT.getValue());
        orderDO.setExceptionFlag(false);
        orderDO.setExceptionType(0);
        orderDO.setExceptionDetail("");
        orderDO.setVersion(0L);
        orderDO.setSystemGlobalSn(submit.getSystemGlobalSn());
        orderDO.setActionJson(JSON.toJSONString(routeDTO.getActionList()));
        List<String> declareOrderTypes = submit.getDeclareOrderTypes();
        if (CollectionUtils.isNotEmpty(declareOrderTypes)) {
            Integer orderTags = null;
            if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.BYTE_DANCE_WMS.getCode())) {
                orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.BYTE_DANCE_WMS.getOrderTag());
            }
            if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.JIEZHOU_ENCRYPT.getCode())) {
                orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.JIEZHOU_ENCRYPT.getOrderTag());
            }
            if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.CAINIAO_WMS.getCode())) {
                orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.CAINIAO_WMS.getOrderTag());
            }
            if (Objects.nonNull(orderTags)) {
                orderDO.setOrderTags(orderTags);
            }
        }
        if (Objects.nonNull(submit.getDeclareWayRecord())) {
            orderDO.setDeclareWayRecord(submit.getDeclareWayRecord());
        }
        OrderExtraDto extra = new OrderExtraDto();
        extra.setSubmit(orderSubmitDto);
        orderDO.setExtraJson(JSON.toJSONString(extra));
        return orderDO;
    }

    public OrderDO save(RichOrder richOrder) {
        OrderDO orderDO = richOrder.getOrder();
        if (StringUtils.isEmpty(orderDO.getCustomsPaymentSn()) && richOrder.getCustomsPaymentDO() != null) {
            orderDO.setCustomsPaymentSn(richOrder.getCustomsPaymentDO().getSn());
        }
        if (StringUtils.isEmpty(orderDO.getCustomsOrderSn()) && richOrder.getCustomsOrderDO() != null) {
            orderDO.setCustomsOrderSn(richOrder.getCustomsOrderDO().getSn());
        }
        if (StringUtils.isEmpty(orderDO.getCustomsLogisticsSn()) && richOrder.getCustomsLogisticsDO() != null) {
            orderDO.setCustomsLogisticsSn(richOrder.getCustomsLogisticsDO().getSn());
        }
        if (StringUtils.isEmpty(orderDO.getCustomsInventorySn()) && richOrder.getCustomsInventoryDOPack() != null) {
            orderDO.setCustomsInventorySn(richOrder.getCustomsInventoryDOPack().getCustomsInventoryDO().getSn());
        }
        if (orderDO.getId() == null || orderDO.getId().equals(0L)) {
            Date createTime = DateHelper.getCurrentDateNoMillisecond();
            orderDO.setCreateTime(createTime);
            orderDO.setUpdateTime(createTime);
            orderDO.setId(sequenceServiceBaseService.getUniqueId());
            UserUtils.setCreateAndUpdateBy(orderDO);
            orderMapper.insertSelective(orderDO);
        } else {
            OrderDO template = new OrderDO();
            template.setExceptionFlag(false);
            template.setExtraJson(orderDO.getExtraJson());
            template.setSystemGlobalSn(orderDO.getSystemGlobalSn());
            template.setOutOrderNo(orderDO.getOutOrderNo());
            template.setStatus(OrderStatus.DEC_WAIT.getValue());
            template.setActionJson(orderDO.getActionJson());
            template.setCustomsPaymentSn(orderDO.getCustomsPaymentSn());
            template.setCustomsOrderSn(orderDO.getCustomsOrderSn());
            template.setCustomsLogisticsSn(orderDO.getCustomsLogisticsSn());
            template.setCustomsInventorySn(orderDO.getCustomsInventorySn());
            template.setOrderTags(orderDO.getOrderTags());
            this.updateByIdSection(orderDO.getId(), template, orderDO.getCreateTime());
            // --------------------
            orderDO.setExceptionFlag(false);
            orderDO.setStatus(OrderStatus.DEC_WAIT.getValue());
        }
        return orderDO;
    }

    private void updateByIdSection(Long id, OrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", id));
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(template, example);
    }
}
