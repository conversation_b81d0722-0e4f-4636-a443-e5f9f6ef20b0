package com.danding.cds.service.payinfo;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.session.RowBounds;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsPaymentDeclareService;
import com.danding.cds.c.api.service.CustomsPaymentService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.payment.api.service.dto.PaymentSearch;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.enums.CustomsPaymentStatus;
import com.danding.cds.declare.sdk.model.payment.CustomsPayDeclareResult;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.bean.dao.CustomsPaymentDO;
import com.danding.cds.order.base.service.CustomsPaymentBaseService;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.v2.bean.dto.TongLianCallBackDTO;
import com.danding.common.utils.CopyUtil;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;

import cn.hutool.core.collection.CollUtil;
import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 17:17
 * @Description:
 */
@Slf4j
@Service
public class CustomsPaymentServiceImpl implements CustomsPaymentService {

    @Resource
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Autowired
    @Lazy
    private CustomsPaymentDeclareService customsPaymentDeclareService;

    @Resource
    private OrderService orderService;

    @Resource
    private CustomsPaymentBaseService customsPaymentBaseService;

    @Autowired
    private CustomsSupport customsSupport;

    @Override
    public CustomsPaymentDTO findByOrder(Long orderId, String sn) {
        if (StringUtils.isEmpty(sn)){
            Example example = new Example(CustomsPaymentDO.class);
            example.and(example.createCriteria().andEqualTo("orderId",orderId));
            CustomsPaymentDO customsPaymentDO = customsPaymentBaseService.selectOneByExample(example);
            return CopyUtil.copy(customsPaymentDO, CustomsPaymentDTO.class);
        } else {
            return this.findBySn(sn);
        }
    }

    @Override
    public CustomsPaymentDTO findBySn(String sn) {
        Example example = new Example(CustomsPaymentDO.class);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        CustomsPaymentDO customsPaymentDO = customsPaymentBaseService.selectOneByExample(example);
        return CopyUtil.copy(customsPaymentDO, CustomsPaymentDTO.class);
    }

    @Override
    public CustomsPaymentDTO findByOutOrderNo(String outOrderNo) {
        Example example = new Example(CustomsPaymentDO.class);
        example.and(example.createCriteria().andEqualTo("outOrderNo",outOrderNo));
        CustomsPaymentDO customsPaymentDO = customsPaymentBaseService.selectOneByExample(example);
        return CopyUtil.copy(customsPaymentDO,CustomsPaymentDTO.class);
    }

    @Override
    public CustomsPaymentDTO findByDeclareOrderNo(String declareOrderNo) {
        Example example = new Example(CustomsPaymentDO.class);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo));
        CustomsPaymentDO customsPaymentDO = customsPaymentBaseService.selectOneByExample(example);
        return ConvertUtil.beanConvert(customsPaymentDO, CustomsPaymentDTO.class);
    }

    @Override
    public Long updateStatus(Long id, Integer status) {
        Integer userId = UserUtils.getUserId();
        CustomsPaymentDO customsPaymentDO = new CustomsPaymentDO();
        customsPaymentDO.setId(id);
        customsPaymentDO.setStatus(status);
        customsPaymentDO.setUpdateTime(new Date());
        customsPaymentDO.setUpdateBy(userId);
        customsPaymentBaseService.updateByPrimaryKeySelective(customsPaymentDO);
        return customsPaymentDO.getId();
    }

    @Override
    public Long updateBySuccess(Long id, Integer status,String payTransactionId, String verDept, String declarePayNo) {
        CustomsPaymentDO customsPaymentDO = new CustomsPaymentDO();
        customsPaymentDO.setId(id);
        customsPaymentDO.setStatus(status);


        customsPaymentDO.setPayTransactionId(payTransactionId);
        customsPaymentDO.setVerDept(verDept);
        customsPaymentDO.setDeclarePayNo(declarePayNo);
        customsPaymentDO.setFinishTime(new Date());
        customsPaymentDO.setLastDeclareTime(new Date());
        customsPaymentDO.setLastCustomsTime(new Date());


        UserUtils.setUpdateBy(customsPaymentDO);
        customsPaymentDO.setUpdateTime(new Date());
        customsPaymentBaseService.updateByPrimaryKeySelective(customsPaymentDO);
        return customsPaymentDO.getId();
    }

    @Override
    public void updateBuyerInfo(Long id, String buyerIdNum, String buyerName) {
        CustomsPaymentDO customsPaymentDO = new CustomsPaymentDO();
        customsPaymentDO.setId(id);
        customsPaymentDO.setBuyerIdNo(buyerIdNum);
        customsPaymentDO.setBuyerName(buyerName);
        customsPaymentBaseService.updateByPrimaryKeySelective(customsPaymentDO);
    }

    @Override
    public Long updateBaseInfo(Long id, String buyerIdType, String buyerIdNo, String buyerName) {
        CustomsPaymentDO customsPaymentDO = new CustomsPaymentDO();
        customsPaymentDO.setId(id);
        customsPaymentDO.setBuyerIdType(buyerIdType);
        customsPaymentDO.setBuyerIdNo(buyerIdNo);
        customsPaymentDO.setBuyerName(buyerName);
        customsPaymentBaseService.updateByPrimaryKeySelective(customsPaymentDO);
        return customsPaymentDO.getId();
    }

    @Override
    public CustomsPaymentDTO findById(Long id) {
        CustomsPaymentDO ret = customsPaymentBaseService.selectByPrimaryKey(id);
        if (ret == null) {
            return null;
        } else {
            CustomsPaymentDTO result = new CustomsPaymentDTO();
            BeanUtils.copyProperties(ret, result);
            return result;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void rePush(String sn, Boolean sendNow) {
        CustomsPaymentDTO customsPaymentDTO = findBySn(sn);
        if (customsPaymentDTO == null || customsPaymentDTO.getStatus().equals(CustomsPaymentStatus.SUCCESS.getValue())) {
            throw new ArgsErrorException("未找到需要申报的支付单！");
        }
        this.updateStatus(customsPaymentDTO.getId(), CustomsActionStatus.DEC_WAIT.getValue());
        List<CustomsPaymentDeclareDTO>  customsPaymentDeclareList = customsPaymentDeclareService.findByCustomsPaymentCode(customsPaymentDTO.getSn());
        for (CustomsPaymentDeclareDTO customsPaymentDeclareDTO : customsPaymentDeclareList) {
            CustomsPaymentStatus status = CustomsPaymentStatus.NULL;
            switch (CustomsPaymentStatus.getEnum(customsPaymentDeclareDTO.getStatus())){
                case WAIT_RE_PUSH:
                case SUCCESS:
                case FAIL:
                    status = CustomsPaymentStatus.WAIT_RE_PUSH;
                    break;
                case CANCEL:
                case WAIT_DECLARE:
                    status = CustomsPaymentStatus.WAIT_DECLARE;
                    break;
            }
            if (CustomsPaymentStatus.NULL.equals(status)){
                continue;
            }
            customsPaymentDeclareService.updateStatus(customsPaymentDeclareDTO.getId(),status.getValue());
        }
        if (sendNow){
            OrderDTO orderDTO = orderService.findBySnSection(customsPaymentDTO.getOrderSn());
            orderService.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
            orderDeclareMQProducer.send(customsPaymentDTO.getOrderSn());
        }
    }

    @Override
    @PageSelect
    public ListVO<CustomsPaymentDTO> paging(PaymentSearch search) {
        Example example = new Example(CustomsPaymentDO.class);
        Example.Criteria criteria = example.createCriteria();

        if (!LongUtil.isNone(search.getPayChannelId())){
            criteria.andEqualTo("payChannelId",search.getPayChannelId());
        }
        if (search.getStatus() != null && search.getStatus() != 0){
            criteria.andEqualTo("status",search.getStatus());
        }
        if (!StringUtils.isEmpty(search.getTenantId())){
            criteria.andEqualTo("tenantId",search.getTenantId());
        }
        if (!LongUtil.isNone(search.getCreateTimeFrom())){
            criteria.andGreaterThanOrEqualTo("createTime", new DateTime(search.getCreateTimeFrom()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(search.getCreateTimeTo())){
            criteria.andLessThan("createTime", new DateTime(search.getCreateTimeTo()).toString("yyyy-MM-dd HH:mm:ss"));
        }

        if (!StringUtils.isEmpty(search.getQueryType())){
            log.info("[op:CustomsPaymentService paging] queryInfo = {}", search.getQueryInfo());
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if (!CollectionUtils.isEmpty(noList)){
                switch (search.getQueryType()){
                    case "declareOrderNo": criteria.andIn("declareOrderNo",noList); break;
                    case "outOrderNo": criteria.andIn("outOrderNo",noList); break;
                    case "tradePayNo": criteria.andIn("tradePayNo",noList); break;
                }
            }
        }
        example.setOrderByClause("create_time DESC");
        List<CustomsPaymentDO> list = customsPaymentBaseService.selectByExample(example);
        ListVO<CustomsPaymentDTO> result = new ListVO<>();
        result.setDataList(CopyUtil.copyList(list,CustomsPaymentDTO.class));
        // 分页
        PageInfo<CustomsPaymentDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    /**
     * 获取异常支付单
     * @param ebpId
     * @param status
     * @param page
     * @param queryDays
     * @return
     */
    public List<CustomsPaymentDTO> findByEbpIdAndStatusPaymentOrder(Long ebpId,List<Integer> status,Integer page,Integer queryDays){
        RowBounds rowBounds = new RowBounds(0, page);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(queryDays).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(CustomsPaymentDO.class,timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        if (ebpId != null) {
            criteria.andEqualTo("ebpId", ebpId);
        }
        criteria.andIn("status", status);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<CustomsPaymentDO> paymentDOList = customsPaymentBaseService.selectByExampleAndRowBounds(example,rowBounds);
        List<CustomsPaymentDTO> customsPaymentDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(paymentDOList)){
            return customsPaymentDTOList;
        }
        customsPaymentDTOList = JSON.parseArray(JSON.toJSONString(paymentDOList),CustomsPaymentDTO.class);
        return customsPaymentDTOList;
    }

    @Override
    public List<CustomsPaymentDTO> findRedeclareOrderByChannelId(String payChannel, Date startTime) {
        List<Integer> statusList = Arrays.asList(CustomsActionStatus.DEC_WAIT.getValue(),
                CustomsActionStatus.DEC_ING.getValue(),
                CustomsActionStatus.DEC_FAIL.getValue()
        );
        Example example = new Example(CustomsPaymentDO.class);
        example.createCriteria().andEqualTo("channel", payChannel)
                .andBetween("createTime", startTime, new Date())
                .andEqualTo("deleted", false)
                .andIn("status", statusList);
        List<CustomsPaymentDO> customsPaymentDOS = customsPaymentBaseService.selectByExample(example);
        return ConvertUtil.listConvert(customsPaymentDOS, CustomsPaymentDTO.class);
    }

    /**
     * 处理通联回执
     *
     * @param tongLianCallBackDTO
     */
    @Override
    public void handleTongLianCallBack(TongLianCallBackDTO tongLianCallBackDTO) {
        String visitorOrderId = tongLianCallBackDTO.getVisitorOrderId();
        CustomsPaymentDTO customsPaymentDTO = this.findByDeclareOrderNo(visitorOrderId);
        if (Objects.isNull(customsPaymentDTO)) {
            log.info("支付单为空 不处理");
            return;
        }
        String returnStatus = tongLianCallBackDTO.getReturnStatus();

        CustomsReport customsReport = new CustomsReport(CustomsReport.TYPE_SEND, CustomsReport.SYSTEM_PAY_CHANNEL, CustomsReport.PROCESS_CLEAR_DECLARE_PAYMENT);
        CustomsPayDeclareResult payDeclareResult = new CustomsPayDeclareResult();
        if (returnStatus.equals("120")) { // 海关入库（报关成功）
            payDeclareResult.setSuccess(true);
            payDeclareResult.setPostMsg("申报成功");
        } else {
            payDeclareResult.setSuccess(false);
            payDeclareResult.setPostMsg(tongLianCallBackDTO.getReturnInfo());
            log.error("申报单号: {} ,通联支付单申报失败,body:{}", tongLianCallBackDTO.getOriginalOrderNo(), JSON.toJSONString(tongLianCallBackDTO));
        }
        List<CustomsPaymentDeclareDTO> customsPaymentDeclareDTOList = customsPaymentDeclareService.findByCustomsPaymentCode(customsPaymentDTO.getSn());
        if (CollUtil.isNotEmpty(customsPaymentDeclareDTOList)) {
            CustomsPaymentDeclareDTO customsPaymentDeclareDTO = customsPaymentDeclareDTOList.get(0);
            payDeclareResult.setCustoms(customsPaymentDeclareDTO.getCustoms());
            payDeclareResult.setOutRequestNo(customsPaymentDeclareDTO.getOutRequestNo());
        }
        payDeclareResult.setPayTransactionId(customsPaymentDTO.getTradePayNo());
        payDeclareResult.setSubBankNo(customsPaymentDTO.getTradePayNo());
        customsReport.buildProcessData(payDeclareResult);
        customsSupport.accept(customsReport);
    }

}
