package com.danding.cds.service.declareImpl.inventory;

import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/11 13:10
 */
@Service("COMPLETE_CEB_MESSAGE_HZDC_INVENTORY")
public class CompleteCebMessageHZDCInventoryDeclare extends CompleteCebMessageHZDCDeclare {
    @Override
    protected void buildTrackLogEsDO(TrackLogEsDTO trackLogEsDO) {
        trackLogEsDO.setEventDesc(TrackLogConstantMixAll.CEB_INVENTORY_DECLARE);
    }
}
