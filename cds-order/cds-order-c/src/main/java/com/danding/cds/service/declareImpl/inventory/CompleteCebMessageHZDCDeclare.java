package com.danding.cds.service.declareImpl.inventory;

import com.danding.cds.c.api.rpc.TrackLogEsRpc;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.declare.base.component.inventory.CompleteCebMessageDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.declare.sdk.utils.RabbitMqUtil;
import com.danding.cds.order.base.bean.dao.ExternalDeclareOrderDo;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/5/10 16:00
 */
@Service
@Slf4j
public class CompleteCebMessageHZDCDeclare extends CompleteCebMessageDeclareAbstract {
    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;
    @DubboReference
    private TrackLogEsRpc trackLogEsRpc;

    @Override
    protected InventoryDeclareResult doDeclare(String cebMessage, String orderNo, String cebCode, String dxpId) {
        log.info("CompleteCebMessageHZDCDeclare doDeclare cebMessage={} cebCode={} dxpId={}", cebMessage, cebCode, dxpId);
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        if (Objects.isNull(customsSpecialToken)) {
            throw new RuntimeException("未找到对应海关代码的配置信息");
        }
        ExternalDeclareOrderDo declareOrderDo = externalDeclareOrderBaseService.getByOrderNo(orderNo);
        TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
        trackLogEsDO.setOrderId(-1L);
        if (Objects.nonNull(declareOrderDo)) {
            trackLogEsDO.setOrderSn(declareOrderDo.getSn());
        }
        trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setRequestMessage(cebMessage);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setSender(TrackLogConstantMixAll.DT_CCS);
        trackLogEsDO.setReceiver(TrackLogConstantMixAll.HZ_DATA_CENTER);
        trackLogEsDO.setEventTime(new Date());
        trackLogEsDO.setOperator(UserUtils.getUserRealName());
        trackLogEsDO.setInternalStatus(OrderInternalEnum.DECLARING.getCode());
        //实际申报方法
        RabbitMqUtil.hzDataCenterSendMsg(cebMessage, customsSpecialToken.getSenderId());

        this.buildTrackLogEsDO(trackLogEsDO);
        try {
            trackLogEsRpc.submit(trackLogEsDO);
        } catch (Exception e) {
            log.error("记录申报轨迹日志失败 error={}", e.getMessage(), e);
        }
        return null;
    }

    protected void buildTrackLogEsDO(TrackLogEsDTO trackLogEsDO) {
        trackLogEsDO.setEventDesc("杭数申报");
    }

    @Override
    protected InventoryDeclareResult doMockDeclare(String cebMessage, String orderNo, String cebCode, String dxpId) {
        log.info("CompleteCebMessageHZDCDeclare doMockDeclare cebMessage={} cebCode={} dxpId={}", cebMessage, cebCode, dxpId);
        final CustomsSpecialToken customsSpecialToken = CebMessageUtil.getCustomsSpecialToken(cebCode);
        if (Objects.isNull(customsSpecialToken)) {
            throw new RuntimeException("未找到对应海关代码的配置信息");
        }
        ExternalDeclareOrderDo declareOrderDo = externalDeclareOrderBaseService.getByOrderNo(orderNo);
        TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
        trackLogEsDO.setOrderId(-1L);
        if (Objects.nonNull(declareOrderDo)) {
            trackLogEsDO.setOrderSn(declareOrderDo.getSn());
        }
        trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setRequestMessage(cebMessage);
        trackLogEsDO.setDeclareOrderNo(orderNo);
        trackLogEsDO.setSender(TrackLogConstantMixAll.DT_CCS);
        trackLogEsDO.setReceiver(TrackLogConstantMixAll.HZ_DATA_CENTER);
        trackLogEsDO.setEventTime(new Date());
        trackLogEsDO.setOperator(UserUtils.getUserRealName());
        trackLogEsDO.setInternalStatus(OrderInternalEnum.DECLARING.getCode());
        //实际申报方法
        RabbitMqUtil.hzDataCenterSendMsg(cebMessage, customsSpecialToken.getSenderId());

        this.buildTrackLogEsDO(trackLogEsDO);
        try {
            trackLogEsRpc.submit(trackLogEsDO);
        } catch (Exception e) {
            log.error("记录申报轨迹日志失败 error={}", e.getMessage(), e);
        }
        return null;
    }
}
