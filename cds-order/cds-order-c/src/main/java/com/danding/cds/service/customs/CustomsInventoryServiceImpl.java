package com.danding.cds.service.customs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.email.facade.IEmailMessageRpcFacade;
import com.danding.business.client.rpc.email.param.EmailParam;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.declare.inner.component.utils.JieztechDeclareService;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.invenorder.api.dto.InventoryTaxStatisticsDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderStepEnum;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.jieztech.JieztechCallbackReq;
import com.danding.cds.message.api.process.OrderCustomsInventoryMessage;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryDO;
import com.danding.cds.order.base.bean.dao.CustomsInventoryItemDO;
import com.danding.cds.order.base.bean.dao.CustomsStatusMappingDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.bean.dao.es.CustomsInventoryItemEsDO;
import com.danding.cds.order.base.bean.dao.es.CustomsSingleInventoryEsDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsInventoryItemMapper;
import com.danding.cds.order.base.mapper.CustomsStatusMappingMapper;
import com.danding.cds.order.base.util.InventoryBuilder;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.base.CustomsInventoryItemBaseService;
import com.danding.cds.service.es.CustomsSingleInventoryEsDao;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.dto.TrackLogEsDTO;
import com.danding.cds.v2.bean.vo.AutoRetryTaxConfig;
import com.danding.cds.v2.bean.vo.req.JdHourlyReportReqVo;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.TaxBillStatusEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.common.utils.DateUtils;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.soul.client.common.result.RpcResult;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.session.RowBounds;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/9 11:38
 * @Description:
 */
@Slf4j
@Service
@RefreshScope
public class CustomsInventoryServiceImpl implements CustomsInventoryService {

    @DubboReference
    private CustomsBookService customsBookService;

    @Autowired
    private CustomsInventoryBaseService customsInventoryBaseService;

    @Autowired
    private CustomsSingleInventoryEsDao customsSingleInventoryEsDao;

    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;

    @Resource
    private OrderService orderService;

    @Autowired
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Autowired
    private CustomsStatusMappingMapper customsStatusMappingMapper;

    @Autowired
    private JieztechDeclareService jieztechDeclareService;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private CustomsInventoryItemBaseService customsInventoryItemBaseService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Resource
    private TrackLogEsService trackLogEsService;

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Resource
    private StockInventoryService stockInventoryService;

    @DubboReference
    private IEmailMessageRpcFacade iEmailMessageRpcFacade;


    @Value("${find_invOrder_by_logistics_quarters:4}")
    private Integer findInvOrderByLogisticsQuarters;

    @Value("${little_giant_route_code_list:[]}")
    private String[] littleGiantRouteCodeList;


    @Override
    public CustomsInventoryDTO findById(Long id) {
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andEqualTo("id", id));
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }


    @Override
    public void initDataToEs(List<CustomsInventoryDTO> customsInventoryDTO) {
        customsSingleInventoryEsDao.esSave(customsInventoryDTO);
    }

    @Override
    public InventoryTaxStatisticsDTO taxStatisticsByEs(SingleInvtOrderSearch search) {

        return customsSingleInventoryEsDao.taxsStatistics(search);
    }

    @Override
    public List<CustomsInventoryDTO> getInventoryByInventoryNos(String inventoryNos) {

        if (StringUtils.isEmpty(inventoryNos)) {
            return Collections.emptyList();
        }

        Long size = Arrays.stream(inventoryNos.split(",")).count();
        List<CustomsInventoryDTO> customsInventoryDTOList = new ArrayList<>();
        // 通过es 查询  todo 这里其实可以不用分页接口，自己写个也行，效率还会高点
        SingleInvtOrderSearch search = new SingleInvtOrderSearch();
        search.setQueryInfo(inventoryNos);
        search.setQueryType("inventoryNo");
        search.setCurrentPage(1);
        search.setPageSize(size.intValue());
        Page<CustomsSingleInventoryEsDO> page = customsSingleInventoryEsDao.paging(search);
        for (CustomsSingleInventoryEsDO customsSingleInventoryEsDO : page.getContent()) {
            CustomsInventoryDTO customsInventoryDTO = new CustomsInventoryDTO();
            // fixme 这个位置我只检查了我要的数据是否copy过去，其它的数据用到了需要自行检查
            BeanUtils.copyProperties(customsSingleInventoryEsDO, customsInventoryDTO);
            customsInventoryDTO.setItemExtras(new ArrayList<>());
            List<CustomsInventoryItemEsDO> itemEsDOList = customsSingleInventoryEsDO.getItemExtras();
            if (!CollectionUtils.isEmpty(itemEsDOList)) {
                List<CustomsInventoryItemExtraEsDTO> itemExtraEsDTOList = itemEsDOList.stream().map(z -> {
                    CustomsInventoryItemExtraEsDTO itemExtra = new CustomsInventoryItemExtraEsDTO();
                    itemExtra.setId(z.getId());
                    itemExtra.setGoodsSeqNo(z.getGoodsSeqNo());
                    itemExtra.setProductId(z.getProductId());
                    return itemExtra;
                }).collect(Collectors.toList());
                customsInventoryDTO.setItemExtras(itemExtraEsDTOList);
            }
            customsInventoryDTOList.add(customsInventoryDTO);
        }
        return customsInventoryDTOList;
    }

    @Override
    public ListVO<CustomsSingleInventoryEsDTO> pagingES(SingleInvtOrderSearch search) {
        log.warn("CustomsInventoryServiceImpl pagingESwarn 入参日志 - {} ", JSON.toJSONString(search));
        if (StrUtil.isNotEmpty(search.getErpPhyWarehouseSn())) {
            List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(search.getErpPhyWarehouseSn());
            if (CollectionUtil.isEmpty(dtoByErpCode)) {
                search.setAccountBookIdList(Lists.newArrayList(-1L));
            } else {
                List<Long> list = dtoByErpCode.stream().map(EntityWarehouseDTO::getCustomsBookId).collect(Collectors.toList());
                search.setAccountBookIdList(list);
            }
        }
        Page<CustomsSingleInventoryEsDO> page = customsSingleInventoryEsDao.paging(search);
        log.warn("CustomsInventoryServiceImpl pagingESwarn 查询结果 - {} ", JSON.toJSONString(page.getContent()));
        ListVO<CustomsSingleInventoryEsDTO> orderDTOListVO = new ListVO<>();
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount((int) page.getTotalElements());
        pageResult.setTotalPage(page.getTotalPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        orderDTOListVO.setPage(pageResult);
        Map<Long, EntityWarehouseDTO> warehouseMap = new HashMap<>();
        if (CollUtil.isNotEmpty(page.getContent())) {
            List<Long> accountBookIdList = page.getContent().stream()
                    .map(CustomsSingleInventoryEsDO::getAccountBookId)
                    .distinct()
                    .collect(Collectors.toList());
            List<EntityWarehouseDTO> warehouseList = entityWarehouseService.findByCustomsBookId(accountBookIdList);
            warehouseMap = warehouseList.stream().collect(Collectors.toMap(EntityWarehouseDTO::getCustomsBookId, Function.identity(), (k1, k2) -> k2));
        }
        List<CustomsSingleInventoryEsDTO> customsSingleInventoryEsDTOS = new ArrayList<>();
        for (CustomsSingleInventoryEsDO customsSingleInventoryEsDO : page.getContent()) {
            CustomsSingleInventoryEsDTO customsSingleInventoryEsDTO = new CustomsSingleInventoryEsDTO();
            BeanUtils.copyProperties(customsSingleInventoryEsDO, customsSingleInventoryEsDTO);
//            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsSingleInventoryEsDO.getAccountBookId());
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsSingleInventoryEsDO.getAccountBookId());
            if (Objects.nonNull(customsBookDTO)) {
                customsSingleInventoryEsDTO.setAccountBookNo(customsBookDTO.getBookNo());
            }
            EntityWarehouseDTO warehouse = warehouseMap.get(customsSingleInventoryEsDO.getAccountBookId());
            if (warehouse != null) {
                customsSingleInventoryEsDTO.setErpPhyWarehouseName(warehouse.getErpWarehouseName());
                customsSingleInventoryEsDTO.setErpPhyWarehouseSn(warehouse.getErpWarehouseCode());
            }
            List<CustomsInventoryItemEsDO> itemExtras = customsSingleInventoryEsDO.getItemExtras();
            boolean anyMatch = itemExtras.stream().anyMatch(item -> Objects.nonNull(item.getItemTag()) && OrderItemTagEnum.containsFbGifts(item.getItemTag()));
            if (anyMatch) {
                customsSingleInventoryEsDTO.setContainFbGifts("是");
            } else {
                customsSingleInventoryEsDTO.setContainFbGifts("否");
            }
            customsSingleInventoryEsDTOS.add(customsSingleInventoryEsDTO);
        }
        orderDTOListVO.setDataList(customsSingleInventoryEsDTOS);
        return orderDTOListVO;
    }

    @Override
    @PageSelect
    public ListVO<CustomsInventoryDTO> paging(SingleInvtOrderSearch search) {
        log.warn("CustomsInventoryServiceImpl pagingwarn 入参日志 - {} ", JSON.toJSONString(search));

        Example example = new Example(CustomsInventoryDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        // 账册Id列表
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("accountBookId", accountBookIdList);
        }
        Date now = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.set(2020, Calendar.APRIL, 1);
        Date begin = calendar.getTime();
        if (search.getCreateFrom() == null || new Date(search.getCreateFrom()).before(begin)) {
            search.setCreateFrom(begin.getTime());
        }
        if (search.getCreateTo() == null || new Date(search.getCreateTo()).after(now)) {
            search.setCreateTo(now.getTime());
        }
        criteria.andBetween("createTime", new Date(search.getCreateFrom()), new Date(search.getCreateTo()));
        //申报成功时间
        if (search.getCustomsPassFrom() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getCustomsPassFrom());
            criteria.andGreaterThanOrEqualTo("customsPassTime", tempDate);
        }
        if (search.getCustomsPassTo() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getCustomsPassTo());
            criteria.andLessThanOrEqualTo("customsPassTime", tempDate);
        }
        //申报时间
        if (search.getLastDeclareFrom() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getLastDeclareFrom());
            criteria.andGreaterThanOrEqualTo("lastDeclareTime", tempDate);
        }
        if (search.getLastDeclareTo() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getLastDeclareTo());
            criteria.andLessThanOrEqualTo("lastDeclareTime", tempDate);
        }
        //回执时间
        if (search.getLastReceiveFrom() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getLastReceiveFrom());
            criteria.andGreaterThanOrEqualTo("lastCustomsTime", tempDate);
        }
        if (search.getLastReceiveTo() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getLastReceiveTo());
            criteria.andLessThanOrEqualTo("lastCustomsTime", tempDate);
        }

        if (!LongUtil.isNone(search.getEbcId())) {
            criteria.andEqualTo("ebcId", search.getEbcId());
        }
        if (!LongUtil.isNone(search.getAgentCompanyId())) {
            criteria.andEqualTo("agentCompanyId", search.getAgentCompanyId());
        }
        if (search.getUser() != null) {
            criteria.andEqualTo("userId", search.getUser());
        }
        if (search.getAfterStatus() != null) {
            criteria.andEqualTo("afterSalesStatus", search.getAfterStatus());
        }
        if (search.getReviwStatus() != null) {
            criteria.andEqualTo("reviewStatus", search.getReviwStatus());
        }
        if (search.getStatus() != null) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!StringUtils.isEmpty(search.getCustomsStatus())) {
            criteria.andEqualTo("customsStatus", search.getCustomsStatus());
        }
        if (search.getExitRegionStatus() != null) {
            criteria.andEqualTo("exitRegionStatus", search.getExitRegionStatus());
        }

        if (!org.apache.commons.lang3.StringUtils.isEmpty(search.getInveOrderStep())) {
            if (InventoryOrderStepEnum.STEP_CANCLE.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可撤清单：已放行，未出区
                criteria.andEqualTo("customsStatus", CustomsStat.CUSTOMS_PASS);
                criteria.andEqualTo("exitRegionStatus", 0);
            } else if (InventoryOrderStepEnum.STEP_RETURN.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可退清单：已放行，已出区
                criteria.andEqualTo("customsStatus", CustomsStat.CUSTOMS_PASS);
                criteria.andEqualTo("exitRegionStatus", 1);
            }
        }
        if (!StringUtils.isEmpty(search.getQueryInfo())) {
            if ("declareOrderNo".equals(search.getQueryType())) {
                criteria.andIn("declareOrderNo", Splitter.on(",").splitToList(search.getQueryInfo()));
            } else if ("inventoryNo".equals(search.getQueryType())) {
                criteria.andIn("inventoryNo", Splitter.on(",").splitToList(search.getQueryInfo()));
            } else if ("logisticsNo".equals(search.getQueryType())) {
                criteria.andIn("logisticsNo", Splitter.on(",").splitToList(search.getQueryInfo()));
            } else if ("productId".equals(search.getQueryType())) {
                log.info("[op:CustomsInventoryServiceImpl-paging-findByProductId] search={}", JSON.toJSONString(search));
                List<CustomsInventoryItemDTO> customsInventoryItemDTOList = this.listItemByProductIdSection(Splitter.on(",").splitToList(search.getQueryInfo()), new Date());
                log.info("[op:CustomsInventoryServiceImpl-paging-findByProductId] customsInventoryItemDTOList={}", JSON.toJSONString(customsInventoryItemDTOList));
                List<Long> customsInventoryIds = customsInventoryItemDTOList.stream().map(CustomsInventoryItemDTO::getCustomsInventoryId).collect(Collectors.toList());
                log.info("[op:CustomsInventoryServiceImpl-paging-findByProductId] customsInventoryIds={}", JSON.toJSONString(customsInventoryIds));
                if (!CollectionUtils.isEmpty(customsInventoryIds)) {
                    criteria.andIn("id", customsInventoryIds);
                }
            }
        }
        List<CustomsInventoryDO> list = customsInventoryBaseService.selectByExample(example);
        ListVO<CustomsInventoryDTO> result = new ListVO<>();
        result.setDataList(list.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList()));
        // 分页
        PageInfo<CustomsInventoryDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @PageSelect
    public ListVO<CustomsInventoryDTO> pagingTemp(SingleInvtOrderSearch search) {
        List<CustomsInventoryDO> list;
        try {
            Example example = new Example(CustomsInventoryDO.class);
            Example.Criteria criteria = example.createCriteria();

            criteria.andBetween("createTime", new Date(search.getCreateFrom()), new Date(search.getCreateTo()));
            list = customsInventoryBaseService.selectByExample(example);
        } catch (Exception e) {
            log.error("list error:{}", e);
            throw e;
        }
        ListVO<CustomsInventoryDTO> result = new ListVO<>();
        result.setDataList(list.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList()));
        // 分页
        PageInfo<CustomsInventoryDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public CustomsInventoryDTO buildCustomsSingleInventoryEsDO(CustomsInventoryDTO customsInventoryDTO) {
        List<CustomsInventoryItemDTO> list = listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());

        List<CustomsInventoryItemExtraEsDTO> itemExtraDTOList = new ArrayList<>();
        for (CustomsInventoryItemDTO dto : list) {
            CustomsInventoryItemExtra extra = JSON.parseObject(dto.getExtraJson(), CustomsInventoryItemExtra.class);

            CustomsInventoryItemExtraEsDTO itemExtra = new CustomsInventoryItemExtraEsDTO();
            itemExtra.setId(dto.getId());
            itemExtra.setGoodsSeqNo(extra.getGoodsSeqNo());
            itemExtra.setProductId(extra.getProductId());
            itemExtraDTOList.add(itemExtra);
        }
        customsInventoryDTO.setItemExtras(itemExtraDTOList);
//        CustomsSingleInventoryEsDO esDO = customsSingleInventoryEsDao.buildCustomsSingleInventoryEsDO(customsInventoryDTO, list);
        return customsInventoryDTO;
    }

    @Override
    public void reloadIndex() {
        customsSingleInventoryEsDao.reloadIndex();
    }

    @Override
    public Object getByInventoryId(Long id) {
        return customsSingleInventoryEsDao.getByInventoryId(id);
    }

    @Override
    public Object delIndexByMonth(Integer month) {
        return customsSingleInventoryEsDao.delIndexByMonth(month);
    }

    @Override
    public int findCount(String tableName) {
        return customsInventoryBaseService.findCount(tableName);
    }

    @Override
    public ListVO<CustomsInventoryDTO> pagingTemp(int currentPage, Integer pageSize, String tableName) {
        int offset = (currentPage - 1) * pageSize;
        List<CustomsInventoryDO> list;

        try {
            list = customsInventoryBaseService.selectPage(offset, pageSize, tableName);
        } catch (Exception e) {
            log.error("list error:{}", e);
            throw e;
        }
        ListVO<CustomsInventoryDTO> result = new ListVO<>();
        result.setDataList(list.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList()));
        // 分页
        PageInfo<CustomsInventoryDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<CustomsInventoryDTO> getInventorysByStatus(Integer status, Integer customsStatus, String detail) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(14).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("status", status)
                .andEqualTo("customsStatus", customsStatus)
                .andLike("customsDetail", detail));
        List<CustomsInventoryDO> customsInventoryDOS = customsInventoryBaseService.selectByExample(example);
        List<CustomsInventoryDTO> inventoryDTOList = customsInventoryDOS.stream().map(c -> {
            CustomsInventoryDTO dto = new CustomsInventoryDTO();
            BeanUtils.copyProperties(c, dto);
            return dto;
        }).collect(Collectors.toList());
        return inventoryDTOList;
    }


    @Override
    public void updateSnWithFix(String sn, String newSn, Date sectionDate) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(sectionDate).millisOfDay().withMaximumValue().toDate()));
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        // Step::返回值处理
        CustomsInventoryDO result = customsInventoryBaseService.selectOneByExample(example);
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setSn(newSn);
        this.updateByIdSection(result.getId(), template, sectionDate);
    }

    @Override
    public CustomsInventoryDTO findByOrder(Long orderId, String sn) {
        if (StringUtils.isEmpty(sn)) {
            Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
            example.and(example.createCriteria().andEqualTo("orderId", orderId));
            CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
            if (customsInventoryDO == null) {
                return null;
            } else {
                return InventoryBuilder.buildDTO(customsInventoryDO);
            }
        } else {
            return this.findBySnSection(sn);
        }
    }

    @Override
    public List<CustomsInventoryDTO> findBySnList(List<String> snList) {

        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        if (CollectionUtils.isEmpty(quarterMap)) {
            return null;
        }
        List<CustomsInventoryDTO> inventoryDTOList = new ArrayList<>();
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<CustomsInventoryDO> inventoryDOList = customsInventoryBaseService.selectBySnList(v, beginOfQuarter, endOfQuarter);
            List<CustomsInventoryDTO> inventoryDTOS = InventoryBuilder.buildDTO(inventoryDOList);
            if (!CollectionUtils.isEmpty(inventoryDTOS)) {
                inventoryDTOList.addAll(inventoryDTOS);
            }
        });
        return inventoryDTOList;
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo(String logisticsNo) {
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andEqualTo("logisticsNo", logisticsNo));
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        if (customsInventoryDO == null) {
            return null;
        } else {
            return InventoryBuilder.buildDTO(customsInventoryDO);
        }

    }


    /**
     * 根据运单号获取清单
     *
     * @param logisticsNo
     * @return
     */
    @Override
    public List<CustomsInventoryDTO> findByCustomsLogisticsNo(List<String> logisticsNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        // 账册Id列表
        if (!CollectionUtils.isEmpty(logisticsNo)) {
            criteria.andIn("logisticsNo", logisticsNo);
        }
        example.and(criteria);
        List<CustomsInventoryDO> customsInventoryDO = customsInventoryBaseService.selectByExample(example);
        if (!CollectionUtils.isEmpty(customsInventoryDO)) {
            return JSON.parseArray(JSON.toJSONString(customsInventoryDO), CustomsInventoryDTO.class);
        } else {
            return new ArrayList<>();
        }
    }


    @Override
    public CustomsInventoryDTO queryByLogisticsNo(String logisticsNo, Integer exitRegionStatus) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andEqualTo("logisticsNo", logisticsNo);
        criteria.andEqualTo("exitRegionStatus", exitRegionStatus);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo90Days(String logisticsNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andEqualTo("logisticsNo", logisticsNo);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    /**
     * 修改最后一次申报时间
     *
     * @param orderSn
     */
    @Override
    public void updateByLastDeclareTime(String orderSn) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andEqualTo("declareOrderNo", orderSn);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        if (Objects.nonNull(customsInventoryDO.getLastCustomsTime())) {
            CustomsInventoryDO inventoryDO = new CustomsInventoryDO();
            inventoryDO.setId(customsInventoryDO.getId());
            Date nowBeforeFiveMinutes = new Date(customsInventoryDO.getLastDeclareTime().getTime() - 600000);
            inventoryDO.setLastDeclareTime(nowBeforeFiveMinutes);
            UserUtils.setUpdateBy(inventoryDO);
            inventoryDO.setUpdateTime(new Date());
            customsInventoryBaseService.updateByPrimaryKeySelective(inventoryDO);
        }
    }

    @Override
    public void updateLastDeclareTime(String sn) {
        if (StringUtils.isEmpty(sn)) {
            throw new ArgsErrorException("updateLastDeclareTime sn为空");
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("orderSn", sn));
        Date fifteenMinusAgo = new Date(System.currentTimeMillis() - 900000);
        CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
        customsInventoryDO.setLastDeclareTime(fifteenMinusAgo);
        customsInventoryBaseService.updateByExampleSelective(customsInventoryDO, example);

    }

    /**
     * 根据清单数据SN修改申报清单核注状态
     *
     * @param inventorySnList
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEndorsementsStatusBySn(List<String> inventorySnList, Integer endorsementStatus) {

        if (CollectionUtils.isEmpty(inventorySnList)) {
            return;
        }
        // 同一季度放在一起
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(inventorySnList);
        // 遍历每个季度，然后数据按数量进行切割
        quarterMap.forEach((k, v) -> {
            // 按量切割
            try {
                // 限制下批量数据
                int index = 1000;
                int totalSize = v.size();
                log.info("更改清单核注状态，限制批量处理数据步长：{}，总数量：{}", index, totalSize);
                if (index >= totalSize) {
                    customsInventoryBaseService.updteEndorsementsStatusBySn(v, endorsementStatus, k);
                } else {
                    int times = totalSize % index > 0 ? totalSize / index + 1 : totalSize / index;
                    for (int i = 1; i <= times; i++) {
                        int fromIndex = (i - 1) * index;
                        int toIndex = index * i;
                        if (i == times) {
                            toIndex = totalSize;
                        }
                        List<String> logisticsNoList = v.subList(fromIndex, toIndex);
                        customsInventoryBaseService.updteEndorsementsStatusBySn(logisticsNoList, endorsementStatus, k);
                    }
                }
            } catch (Exception ex) {
                log.error("更改清单核注状态，异常:{}", ex.getMessage(), ex);
            }
        });
    }

    @Override
    public void updateCheckoutStatusBySn(String sn, Integer checkoutStatus) {
        String quarter = QuarterUtil.getQuarterBySn(sn);
        customsInventoryBaseService.updateCheckoutStatusBySn(Lists.newArrayList(sn), checkoutStatus, quarter);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateCustomsStatus(String sn, Integer customsStatus) throws ArgsErrorException {
        OrderDTO orderDTO = null;
        CustomsInventoryDTO customsInventoryDTO = null;
        TrackLogEsDTO trackLog = new TrackLogEsDTO();
        try {
            orderDTO = orderService.findBySnSection(sn);
            if (Objects.isNull(orderDTO)) {
                log.error("根据sn查询申报单失败：sn = {} ", sn);
                throw new ArgsErrorException("根据sn查询申报单失败");
            }
            String customsInventorySn = orderDTO.getCustomsInventorySn();
            if (StrUtil.isBlank(customsInventorySn)) {
                throw new ArgsErrorException("申报单号：" + orderDTO.getDeclareOrderNo() + "，清单sn为空");
            }
            customsInventoryDTO = customsInventoryBaseService.findBySn(customsInventorySn);
            if (Objects.isNull(customsInventoryDTO)) {
                throw new ArgsErrorException("申报单号：" + orderDTO.getDeclareOrderNo() + "，清单不存在");
            }
            if (CustomsStat.CUSTOMS_PASS.getValue().equals(customsInventoryDTO.getCustomsStatus())) {
                throw new ArgsErrorException("申报单号：" + orderDTO.getDeclareOrderNo() + "，已放行，不允许修改");
            }
            String quarter = QuarterUtil.getQuarterBySn(customsInventorySn);
            customsInventoryBaseService.updateCustomsStatusBySn(Lists.newArrayList(customsInventorySn), customsStatus, "人工修改状态", quarter);
        } catch (Exception e) {
            log.error("清单状态修改失败！sn = {} ", sn, e);
            trackLog.setResult(TrackLogConstantMixAll.FAIL);
            submitTrackLog(customsStatus, trackLog, customsInventoryDTO, orderDTO);
            if (e instanceof ArgsErrorException) {
                throw e;
            }
            throw new ArgsErrorException("清单状态修改失败！");
        }
        trackLog.setResult(TrackLogConstantMixAll.SUCCESS);
        submitTrackLog(customsStatus, trackLog, customsInventoryDTO, orderDTO);
    }

    private void submitTrackLog(Integer customsStatus, TrackLogEsDTO trackLog, CustomsInventoryDTO customsInventoryDTO, OrderDTO orderDTO) {
        if (customsInventoryDTO != null) {
            trackLog.setOrderId(customsInventoryDTO.getOrderId());
            trackLog.setOrderSn(customsInventoryDTO.getOrderSn());
            trackLog.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
        }
        if (orderDTO != null) {
            trackLog.setInternalStatus(orderDTO.getInternalStatus());
        }
        trackLog.setSender("无");
        trackLog.setReceiver("无");
        trackLog.setEventDesc(TrackLogConstantMixAll.CUSTOMS_INVENTORY_RECEIPT);
        trackLog.setEventTime(new Date());
        trackLog.setCreateTime(new Date());
        trackLog.setCustomsReceipt("【人工修改海关状态: " + CustomsStat.getEnum(String.valueOf(customsStatus)).getDesc() + "】");
        trackLog.setOperator(UserUtils.getUserRealName());

        trackLogEsService.submit(trackLog);
    }

    @Override
    public List<CustomsSingleInventoryEsDTO> findEsByLogistics(String logisticsNo, Long expressId) {
        if (StringUtils.isEmpty(logisticsNo)) {
            return new ArrayList<>();
        }
        return customsSingleInventoryEsDao.findByLogistics(logisticsNo, expressId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveCainiaoInventoryExitArea(String globalSystemSn) {
        if (StringUtils.isEmpty(globalSystemSn)) {
            throw new ArgsInvalidException("GS单号不能不为空");
        }
        OrderDTO orderDTO = orderService.findByGlobalSnFull(globalSystemSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("GS单号不存在");
        }
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        List<String> littleGiantRouteCodes = Arrays.asList(littleGiantRouteCodeList);
        if (!DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags()).contains(DeclareOrderTagEnums.CAINIAO_WMS.getCode())
                && !(CollectionUtil.isNotEmpty(littleGiantRouteCodes) && littleGiantRouteCodes.contains(orderExtra.getSubmit().getRouteCode()))
        ) {
            throw new ArgsInvalidException("非菜鸟WMS申报单无法更新出区状态");
        }
        CustomsInventoryDTO customsInventoryDTO = this.findBySnSection(orderDTO.getCustomsInventorySn());
        if (Objects.isNull(customsInventoryDTO)) {
            throw new ArgsInvalidException("未查询到该申报单对应的清单");
        }
        if (Objects.equals(customsInventoryDTO.getExitRegionStatus(), 1)) {
            log.info("receiveCainiaoInventoryExitArea - 清单已出区");
            return;
        }
        this.updateExitRegionStatus(customsInventoryDTO.getId(), 1, new Date(), customsInventoryDTO.getCreateTime());
        customsInventoryCalloffService.updateExitRegionStatus(customsInventoryDTO.getOrderId(), 1);
        // 账册库存调整
        if (customsInventoryDTO.getIsOccupiedStock()) {
            List<CustomsInventoryItemDTO> itemDTOList = this.getItemByInventoryId90Days(customsInventoryDTO.getId());
            bookItemStockOTU(itemDTOList, customsInventoryDTO);
            // 初始化是否占用库存
            updateIsOccupiedStockSection(customsInventoryDTO.getId(), false, customsInventoryDTO.getCreateTime());
        }
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
    }

    @Override
    public void updateAgentCompanyId(CustomsInventoryDTO customsInventoryDTO, CompanyDTO declareCompanyDTO) {
        if (Objects.isNull(customsInventoryDTO)) {
            log.info("updateAgentCompanyId - 清单不存在");
            return;
        }
        if (Objects.isNull(declareCompanyDTO)) {
            log.info("updateAgentCompanyId - 申报公司不存在");
            return;
        }
        CustomsInventoryDO inventoryDO = new CustomsInventoryDO();
        inventoryDO.setAgentCompanyId(declareCompanyDTO.getId());
        updateByIdSection(customsInventoryDTO.getId(), inventoryDO, customsInventoryDTO.getCreateTime());
    }

    @Override
    public void getJdHourlyReportEmailJob(JdHourlyReportReqVo reportReqVo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(1).toDate());
        timeRangeParam.setBeginDate(DateTime.now().toDate());
        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date closestHour = instance.getTime();
        // 获取当天0点
        instance.set(Calendar.HOUR_OF_DAY, 0);
        Date todayStart = instance.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (Objects.equals(todayStart, closestHour)) {
            // 如果为0点 从前一天开始统计
            todayStart = DateUtils.addDay(todayStart, -1);
            log.info("如果为0点 从前一天开始统计");
        }
        XxlJobLogger.log("getJdHourlyReportEmailJob 当前时间:{} 查询范围 {} - {}", sdf.format(now), sdf.format(todayStart), sdf.format(closestHour));
        log.info("getJdHourlyReportEmailJob 当前时间:{} 查询范围 {} - {}", sdf.format(now), sdf.format(todayStart), sdf.format(closestHour));

        // 小时报区分账册 【金义】：T2924W000078 【义乌】：T2925W000007
        List<String> accountList = Arrays.asList("T2924W000078", "T2925W000007");
        if (!CollectionUtils.isEmpty(reportReqVo.getAccountList())) {
            accountList = reportReqVo.getAccountList();
        }

        for (String bookNo : accountList) {
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOByNo(bookNo);
            if (Objects.isNull(customsBookDTO)) {
                continue;
            }
            CustomsDistrictEnum customs = CustomsDistrictEnum.getEnum(customsBookDTO.getCustomsDistrictCode());
            Map<String, Integer> emailBackLogMap = new HashMap<>();
            Long bookId = customsBookDTO.getId();
            Integer orderTotal = getJdTodayOrderTotal(bookId);
            Integer noReplyCount = getReportResult(todayStart, closestHour, bookId);
            emailBackLogMap.put("清单无回执", noReplyCount);
            Integer declareFinishCount = getDeclareFinishResult(todayStart, new Date(), bookId);
            DecimalFormat df = new DecimalFormat("0.00");
            double rate = 1;
            if (orderTotal != 0) {
                rate = declareFinishCount.doubleValue() / orderTotal.doubleValue();
            }
            String rateStr = df.format(rate * 100) + "%";
            List<JdHourlyReportReqVo.TypeResult> dataList = reportReqVo.getDataList();
            int otherCount = noReplyCount;
            int backlogCount = noReplyCount;
            for (JdHourlyReportReqVo.TypeResult typeResult : dataList) {
                int integer = getReportResult(typeResult.getDesc(), todayStart, closestHour, bookId);
                emailBackLogMap.put(typeResult.getType(), integer);
                if (!Objects.equals(typeResult.getDesc(), "限额")) {
                    backlogCount += integer;
                }
                otherCount += integer;
            }
            int other = orderTotal - declareFinishCount - otherCount;
            emailBackLogMap.put("其他原因", other);
            int totalBacklog = backlogCount + other;

            EmailParam emailParam = new EmailParam();
            String statisticTime = DateUtil.formatDateStr(closestHour, "HH:mm");
            emailParam.setSubject(String.format("%s口岸小时报-%s", customs.getDesc(), statisticTime));
            emailParam.setContent(this.buildEmailContent(customs.getDesc(), statisticTime, orderTotal,
                    declareFinishCount, rateStr, totalBacklog, emailBackLogMap));
            emailParam.setReceiveMail(reportReqVo.getEmailList());
            log.info("getJdHourlyReportEmailJob sendEmail emailParam={}", JSON.toJSONString(emailParam));
            RpcResult rpcResult = iEmailMessageRpcFacade.sendEmail(emailParam);
            if (Objects.isNull(rpcResult) || !Objects.equals(rpcResult.getCode(), 200)) {
                log.info("Erp发送邮件失败 清关单id={} error={}", JSON.toJSONString(emailParam), rpcResult.getMessage());
            }
        }
    }

    public static Map<String, List<CustomsInventoryDTO>> splitByQuarter(List<CustomsInventoryDTO> inventorySnList) {

        Map<String, List<CustomsInventoryDTO>> quarterMap = new HashMap<>();
        for (CustomsInventoryDTO customsInventoryDTO : inventorySnList) {
            if (StringUtils.isEmpty(customsInventoryDTO)) {
                continue;
            }
            String quarter = getQuarterBySn(customsInventoryDTO.getSn());
            if (StringUtils.isEmpty(quarter)) {
                continue;
            }
            List<CustomsInventoryDTO> quarterList = quarterMap.get(quarter);
            if (CollectionUtils.isEmpty(quarterList)) {
                quarterList = new ArrayList<>();
            }
            quarterList.add(customsInventoryDTO);
            quarterMap.put(quarter, quarterList);
        }
        return quarterMap;
    }

    public void bookItemStockOTU(List<CustomsInventoryItemDTO> itemDTOList, CustomsInventoryDTO customsInventoryDTO) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        List<Long> customsBookItemIdList = itemDTOList.stream().map(CustomsInventoryItemDTO::getBookItemId).collect(Collectors.toList());
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findById(customsBookItemIdList);
        Map<Long, CustomsBookItemDTO> bookItemMap = customsBookItemDTOList.stream()
                .collect(Collectors.toMap(CustomsBookItemDTO::getId, Function.identity(), (k1, k2) -> k1));
        for (CustomsInventoryItemDTO itemDTO : itemDTOList) {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            CustomsBookItemDTO customsBookItemDTO = bookItemMap.get(itemDTO.getBookItemId());
            if (customsBookItemDTO == null) {
                continue;
            }
            updateInventoryDTO.setCustomsBookId(customsBookItemDTO.getCustomsBookId())
                    .setCustomsBookItemId(customsBookItemDTO.getId())
                    .setBusinessNo(customsInventoryDTO.getDeclareOrderNo())
                    .setProductId(customsBookItemDTO.getProductId())
                    .setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo())
                    .setChangeType(InventoryChangeTypeEnums.SECOND_OUT_FINISH)
                    .setDeclareUnitQfy(itemDTO.getCount());
            list.add(updateInventoryDTO);
        }
        stockInventoryService.updateInventory(list);
    }

    /**
     * 根据运单号修改核注状态
     *
     * @param logisticsNo
     */
    @Override
    public void updByEndorsementsStatus(List<String> logisticsNo, InventoryReviewStatus status) {

        if (CollectionUtils.isEmpty(logisticsNo)) {
            return;
        }

        try {
            // 限制下批量数据
            int index = 1500;
            int totalSize = logisticsNo.size();
            log.info("更改清单核注状态，限制批量处理数据步长：{}，总数量：{}", index, totalSize);
            if (index >= totalSize) {
                this.updateEndorsementsStatusByLogisticsNo(logisticsNo, status);
            } else {
                int times = totalSize % index > 0 ? totalSize / index + 1 : totalSize / index;
                for (int i = 1; i <= times; i++) {
                    int fromIndex = (i - 1) * index;
                    int toIndex = index * i;
                    if (i == times) {
                        toIndex = totalSize;
                    }
                    List<String> logisticsNoList = logisticsNo.subList(fromIndex, toIndex);
                    this.updateEndorsementsStatusByLogisticsNo(logisticsNoList, status);
                }
            }
        } catch (Exception ex) {
            log.error("更改清单核注状态，异常:{}", ex.getMessage(), ex);
        }
    }

    /**
     * 更新清单表总税金
     *
     * @param id         清单表id
     * @param createTime 清单表创建时间
     * @param amount     税金总额
     */
    @Override
    public void updateTax(Long id, Date createTime, BigDecimal amount) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", id).andIsNull("totalTax"));
        CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
        customsInventoryDO.setTotalTax(amount.multiply(new BigDecimal(100)));
        customsInventoryDO.setUpdateTime(new Date());
        customsInventoryBaseService.updateByExampleSelective(customsInventoryDO, example);
    }


    /**
     * 查询税金未返回的清单
     *
     * @param autoRetryTaxConfig
     * @return
     */
    @Override
    public List<CustomsInventoryDTO> getNotReturnTaxOrder(AutoRetryTaxConfig autoRetryTaxConfig, List<Long> assureCompanyIdList) {
        Date begin = DateTime.now().minusMinutes(30).toDate();
        Date end = DateTime.now().minusMinutes(15).toDate();
        if (Objects.nonNull(autoRetryTaxConfig.getBeginTime()) && Objects.nonNull(autoRetryTaxConfig.getEndTime())) {
            String df = "yyyy-MM-dd HH:mm:ss";
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(df);
            begin = dateTimeFormatter.parseDateTime(autoRetryTaxConfig.getBeginTime()).toDate();
            end = dateTimeFormatter.parseDateTime(autoRetryTaxConfig.getEndTime()).toDate();
        } else if (Objects.nonNull(autoRetryTaxConfig.getTraceBackTime())) {
            begin = new DateTime(end).minusMinutes(autoRetryTaxConfig.getTraceBackTime()).toDate();
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterEnd(DateTime.now().minusDays(90).toDate()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(DateTime.now().toDate()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIsNull("totalTax")
                .andEqualTo("status", CustomsActionStatus.DEC_SUCCESS.getValue())
                .andEqualTo("customsStatus", CustomsStat.CUSTOMS_PASS.getValue())
                .andEqualTo("deleted", false);
        if (!CollectionUtils.isEmpty(assureCompanyIdList)) {
            criteria.andIn("assureCompanyId", assureCompanyIdList);
        }
        if (Objects.nonNull(autoRetryTaxConfig.getInventoryNo())) {
            criteria.andEqualTo("inventoryNo", autoRetryTaxConfig.getInventoryNo());
        } else {
            criteria.andBetween("customsPassTime", begin, end);
        }
        example.and(criteria);
        if (Objects.nonNull(autoRetryTaxConfig.getSize())) {
            example.setOrderByClause("create_time asc limit " + autoRetryTaxConfig.getSize());
        }
        List<CustomsInventoryDO> customsInventoryDOS = customsInventoryBaseService.selectByExample(example);
        return ConvertUtil.listConvert(customsInventoryDOS, CustomsInventoryDTO.class);
    }

    @Override
    public void updateInventoryItemList(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> updateList) {
        if (CollectionUtils.isEmpty(updateList)) {
            return;
        }
        updateList.forEach(u -> {
            CustomsInventoryItemDO customsInventoryItemDO = ConvertUtil.beanConvert(u, CustomsInventoryItemDO.class);
            customsInventoryItemBaseService.updateByDOSelective(customsInventoryItemDO, customsInventoryDTO.getCreateTime());
        });
    }

    /*@Override
    public ListVO<StockOccupiedDetailDTO> getCustomsBookItemOccupiedDetail(StockOccupiedDetailSearch search) {
        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(search.getCustomsBookItemId());
        return getStockOccupiedDetailCore(search, customsBookItemDTO);
    }*/

    /*@Override
    public ListVO<StockOccupiedDetailDTO> getStockOccupiedDetail(StockOccupiedDetailSearch search) {
        List<CustomsBookItemDTO> customsBookItemDTOList = recordProductStockService.getCustomsBookItemListByProductStockId(search.getRecordProductStockId());
        return getStockOccupiedDetailCore(search, customsBookItemDTOList);
    }*/

    @Override
    public void cleanLastDeclareTime(CustomsInventoryDTO customsInventoryDTO) {
        customsInventoryBaseService.updateHandoverStatus(customsInventoryDTO);
    }

    @Override
    public Boolean judgeExist30DaysInByProductId(String productId) {
        if (StringUtils.isEmpty(productId)) {
            return false;
        }
        Date createTimeFrom = cn.hutool.core.date.DateUtil.offsetDay(new Date(), -30);
        // 查数据库太慢了，改用查es
//        return customsInventoryBaseService.judgeExist30DaysInByProductId(orderStatus, productId, CustomsActionStatus.DEC_SUCCESS.getValue(), createTimeFrom);
        return customsSingleInventoryEsDao.isExistProductIdInUse(Collections.singletonList(productId), createTimeFrom, new Date());
    }

    /*public ListVO<StockOccupiedDetailDTO> getStockOccupiedDetailCore(StockOccupiedDetailSearch search, CustomsBookItemDTO customsBookItemDTO) {
        List<CustomsBookItemDTO> customsBookItemDTOList = new ArrayList<>();
        customsBookItemDTOList.add(customsBookItemDTO);
        return this.getStockOccupiedDetailCore(search, customsBookItemDTOList);
    }*/

    /*public ListVO<StockOccupiedDetailDTO> getStockOccupiedDetailCore(StockOccupiedDetailSearch search, List<CustomsBookItemDTO> customsBookItemDTOList) {
        if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
            throw new ArgsInvalidException("未查询到关联账册库存信息");
        }
        Page<CustomsSingleInventoryEsDO> page = customsSingleInventoryEsDao.getStockOccupiedInventoryOrder(search, customsBookItemDTOList);
        ListVO<StockOccupiedDetailDTO> orderDTOListVO = new ListVO<>();
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount((int) page.getTotalElements());
        pageResult.setTotalPage(page.getTotalPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        orderDTOListVO.setPage(pageResult);
        List<CustomsSingleInventoryEsDO> content = page.getContent();
        if (CollectionUtils.isEmpty(content)) {
            log.info("未查询到关联账册库存信息");
            return orderDTOListVO;
        }
        List<String> customsInventorySnList = content.stream().map(CustomsSingleInventoryEsDO::getSn).distinct().collect(Collectors.toList());
        // 清单sn-核注单dto
        Map<String, EndorsementDTO> snEndorsementMap = exportOrderService.getEndorsementByCustomsInventorySnList(customsInventorySnList);
        List<Long> endorsementIdList = snEndorsementMap.values().stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        // 核注id-核放单dto
        Map<Long, ChecklistDTO> checklistDTOMap = checklistBaseService.getChecklistByEndorsementListSecondOut(endorsementIdList);
        Map<String, List<String>> productSeqMap = customsBookItemDTOList.stream().collect(Collectors.groupingBy(CustomsBookItemDTO::getProductId, Collectors.mapping(CustomsBookItemDTO::getGoodsSeqNo, Collectors.toList())));
        List<StockOccupiedDetailDTO> detailDTOList = content.stream().map(c -> {
            List<CustomsInventoryItemEsDO> itemExtras = c.getItemExtras();
            int count = itemExtras.stream().filter(i ->
                    productSeqMap.containsKey(i.getProductId()) && productSeqMap.get(i.getProductId()).contains(i.getGoodsSeqNo())
            ).mapToInt(CustomsInventoryItemEsDO::getCount).sum();
            StockOccupiedDetailDTO stockOccupiedDetailDTO = new StockOccupiedDetailDTO();
            stockOccupiedDetailDTO.setId(c.getId())
                    .setDeclareOrderNo(c.getDeclareOrderNo())
                    .setInventoryNo(c.getInventoryNo())
                    .setCount(count)
                    .setAfterSaleStatus(InventoryAfterStatus.getEnum(c.getAfterSalesStatus()).getDesc())
                    .setInventoryStatus(c.getStatusDesc());
            if (snEndorsementMap.containsKey(c.getSn())) {
                EndorsementDTO endorsementDTO = snEndorsementMap.get(c.getSn());
                if (Objects.isNull(endorsementDTO)) {
                    return null;
                }
                if (checklistDTOMap.containsKey(endorsementDTO.getId())) {
                    ChecklistDTO checklistDTO = checklistDTOMap.get(endorsementDTO.getId());
                    if (Objects.nonNull(checklistDTO)) {
                        stockOccupiedDetailDTO.setChecklistSn(checklistDTO.getSn());
                    }
                }
                stockOccupiedDetailDTO.setEndorsementSn(endorsementDTO.getSn());
                stockOccupiedDetailDTO.setEndorsementStatus(EndorsementOrderStatus.getEnum(endorsementDTO.getStatus()).getDesc());
            }
            return stockOccupiedDetailDTO;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        orderDTOListVO.setDataList(detailDTOList);
        return orderDTOListVO;
    }*/

    @Override
    public List<StockOccupiedCountResDTO> getStockOccupiedCountListByEs(List<Long> customsBookItemIdList, List<Long> bookIdList) {
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findById(customsBookItemIdList);
        if (CollectionUtil.isNotEmpty(bookIdList)) {
            log.info("账册id存在，不过滤原账册库存id");
            customsBookItemDTOList = customsBookItemService.findByBookId(bookIdList);
        }
        return customsSingleInventoryEsDao.getStockOccupiedCountList(customsBookItemDTOList, bookIdList);
    }


    private void updateEndorsementsStatusByLogisticsNo(List<String> logisticsNo, InventoryReviewStatus status) {

        log.warn("updByEndorsementsStatus 输入参数 - {} ", logisticsNo);
        if (CollectionUtils.isEmpty(logisticsNo)) {
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andIn("logisticsNo", logisticsNo);
        List<CustomsInventoryDO> inventoryDOList = customsInventoryBaseService.selectByExample(example);
        List<CustomsInventoryDO> customsInventoryDOList = new ArrayList<>();
        for (CustomsInventoryDO inventoryDO : inventoryDOList) {
            CustomsInventoryDO template = new CustomsInventoryDO();
            template.setReviewStatus(status.getValue());
            // Step::根据时间区间检索更新
            Example example1 = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
            Example.Criteria criteria1 = example.createCriteria();
            criteria1.andEqualTo("id", inventoryDO.getId());
            example1.and(criteria1);
            customsInventoryBaseService.updateByExampleSelective(template, example1);
        }

        log.warn("updByEndorsementsStatus 查询结果 - {} ", customsInventoryDOList);
    }


    @Override
    public CustomsInventoryDTO findByEbcAndNo(Long ebcId, String declareOrderNo) {
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(
                example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo)
                        .andEqualTo("ebcId", ebcId)
        );

        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findBySnSection(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
        //timeRangeParam.setEndDate(new DateTime(createTime).millisOfDay().withMaximumValue().toDate());
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        // Step::返回值处理
        CustomsInventoryDO result = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(result);
    }

    @Override
    public List<CustomsInventoryDTO> listBySnsSection(List<String> sns) {
        if (CollectionUtils.isEmpty(sns)) {
            return new ArrayList<>();
        }
        Date createFrom = null;
        Date createTo = null;
        for (String sn : sns) {
            String dateStr = sn.substring(2, 12);
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
            Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
            if (createFrom == null || new DateTime(createFrom).isAfter(new DateTime(createTime))) {
                createFrom = createTime;
            }
            if (createTo == null || new DateTime(createTo).isBefore(new DateTime(createTime))) {
                createTo = createTime;
            }
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTo).millisOfDay().withMaximumValue().toDate()));
        //timeRangeParam.setEndDate(new DateTime(createTime).millisOfDay().withMaximumValue().toDate());
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createFrom));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("sn", sns));
        // Step::返回值处理
        List<CustomsInventoryDO> result = customsInventoryBaseService.selectByExample(example);
        return result.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<CustomsInventoryDTO> listBySnsSection(List<String> sns, Date sectionDate) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sn", sns);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<CustomsInventoryDO> doList = customsInventoryBaseService.selectByExample(example);
        return doList.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public CustomsInventoryDTO findByLogistics90Days(Long expressId, String logisticsNo) {
        Integer minusDays = 90;
        CustomsInventoryDO customsInventoryDO = getCustomsInventoryByExpressIdAndLogisticsNo(expressId, logisticsNo, minusDays);
        if (Objects.isNull(customsInventoryDO)) {
            customsInventoryDO = getCustomsInventoryByExpressIdAndLogisticsNo(expressId, logisticsNo, minusDays * 2);
        }
        if (Objects.isNull(customsInventoryDO)) {
            customsInventoryDO = getCustomsInventoryByExpressIdAndLogisticsNo(expressId, logisticsNo, minusDays * 3);
        }
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findByLogisticsNo90Days(Long expressId, String logisticsNo) {
        Integer minusDays = 90;
        CustomsInventoryDO customsInventoryDO = getCustomsInventoryByExpressIdAndLogisticsNo(expressId, logisticsNo, minusDays);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    private CustomsInventoryDO getCustomsInventoryByExpressIdAndLogisticsNo(Long expressId, String logisticsNo, Integer minusDays) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(minusDays).toDate());
        //timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("expressId", expressId);
        criteria.andEqualTo("logisticsNo", logisticsNo);
        criteria.andEqualTo("deleted", 0);
        example.and(criteria);
        return customsInventoryBaseService.selectOneByExample(example);
    }

    @Override
    public List<CustomsInventoryDTO> listByLogistics90Days(String logisticsNo) {
        Integer minusDays = 90;
        List<CustomsInventoryDO> doList = getCustomsInventoryByLogisticsNo(logisticsNo, minusDays);
        if (CollectionUtils.isEmpty(doList)) {
            doList = getCustomsInventoryByLogisticsNo(logisticsNo, minusDays * 2);
        }
        if (CollectionUtils.isEmpty(doList)) {
            log.info("listByLogistics90Days - findInvOrderByLogisticsQuarters ={}", findInvOrderByLogisticsQuarters);
            if (Objects.nonNull(findInvOrderByLogisticsQuarters)) {
                doList = getCustomsInventoryByLogisticsNo(logisticsNo, minusDays * findInvOrderByLogisticsQuarters);
            } else {
                doList = getCustomsInventoryByLogisticsNo(logisticsNo, minusDays * 4);
            }
        }
        return doList.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<CustomsInventoryDTO> listByLogistics90Days(List<String> logisticsNo) {
        Integer minusDays = 90;
        Date beginDate = DateTime.now().minusDays(minusDays).toDate();
        Date endDate = ShardingBaseExampleBuilder.quarterEnd(new Date());
        List<CustomsInventoryDO> doList = getCustomsInventoryByLogisticsNo(logisticsNo, beginDate, endDate);
        logisticsNo = filterLogisticsNoList(logisticsNo, doList);
        if (!CollectionUtils.isEmpty(logisticsNo)) {
            endDate = ShardingBaseExampleBuilder.quarterEnd(beginDate);
            beginDate = DateTime.now().minusDays(minusDays * 2).toDate();
            List<CustomsInventoryDO> next = getCustomsInventoryByLogisticsNo(logisticsNo, beginDate, endDate);
            if (!CollectionUtils.isEmpty(next)) {
                doList.addAll(next);
                logisticsNo = filterLogisticsNoList(logisticsNo, next);
            }
        }
        if (!CollectionUtils.isEmpty(logisticsNo)) {
            List<CustomsInventoryDO> next = new ArrayList<>();
            log.info("listByLogistics90Days - findInvOrderByLogisticsQuarters ={}", findInvOrderByLogisticsQuarters);
            int x = findInvOrderByLogisticsQuarters == null ? 4 : findInvOrderByLogisticsQuarters;
            endDate = ShardingBaseExampleBuilder.quarterEnd(beginDate);
            beginDate = DateTime.now().minusDays(minusDays * x).toDate();
            next = getCustomsInventoryByLogisticsNo(logisticsNo, beginDate, endDate);
            if (!CollectionUtils.isEmpty(next)) {
                doList.addAll(next);
            }
        }
        return doList.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    /**
     * 过滤已查过的运单
     *
     * @param remains  剩余运单
     * @param selected 查出的清单
     * @return
     */
    private List<String> filterLogisticsNoList(List<String> remains, List<CustomsInventoryDO> selected) {
        List<String> logisticsNoList = selected.stream().map(CustomsInventoryDO::getLogisticsNo).collect(Collectors.toList());
        return remains.stream().filter(s -> !logisticsNoList.contains(s)).collect(Collectors.toList());
    }

    private List<CustomsInventoryDO> getCustomsInventoryByLogisticsNo(String logisticsNo, Integer minusDays) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(minusDays).toDate());
        //timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("logisticsNo", logisticsNo);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<CustomsInventoryDO> doList = customsInventoryBaseService.selectByExample(example);
        return doList;
    }

    private List<CustomsInventoryDO> getCustomsInventoryByLogisticsNo(List<String> logisticsNo, Date beginDate, Date endDate) {
        if (CollectionUtils.isEmpty(logisticsNo)) {
            return new ArrayList<>();
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(beginDate);
        //timeRangeParam.setEndDate(new Date());
        endDate = endDate == null ? new Date() : endDate;
        timeRangeParam.setEndDate(endDate);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("logisticsNo", logisticsNo);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<CustomsInventoryDO> doList = customsInventoryBaseService.selectByExample(example);
        return doList;
    }

    @Override
    public List<CustomsInventoryDTO> listByInventoryIdsSection(List<Long> ids, Date sectionDate) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<CustomsInventoryDO> doList = customsInventoryBaseService.selectByExample(example);
        return doList.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    /**
     * 底层过滤了非保赠品
     *
     * @param id
     * @param sectionDate
     * @return
     */
    @Override
    public List<CustomsInventoryItemDTO> listItemByIdSection(Long id, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
//        timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsInventoryId", id);
        example.and(criteria);
        List<CustomsInventoryItemDO> doList = customsInventoryItemMapper.selectByExample(example);
        return doList.stream().filter(item -> !OrderItemTagEnum.containsFbGifts(item.getItemTag())).map(InventoryBuilder::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByIdSectionNoFilter(Long id, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
//        timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsInventoryId", id);
        example.and(criteria);
        List<CustomsInventoryItemDO> doList = customsInventoryItemMapper.selectByExample(example);
        return doList.stream().map(InventoryBuilder::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByBookItemIdsSection(List<Long> bookItemIds, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("bookItemId", bookItemIds);
        criteria.andGreaterThanOrEqualTo("createTime", DateUtils.toDate("2020-11-09 00:00:00", DateUtils.DATE_TIME_FORMAT));
        criteria.andEqualTo("modifyDeclare", 0);
        example.and(criteria);
        List<CustomsInventoryItemDO> doList = customsInventoryItemMapper.selectByExample(example);
        return doList.stream().map(InventoryBuilder::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemByProductIdSection(List<String> productIds, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        if (CollectionUtils.isEmpty(productIds)) {
            return new ArrayList<>();
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("itemNo", productIds);
        criteria.andEqualTo("modifyDeclare", 0);
        example.and(criteria);
        log.info("[op:CustomsInventoryServiceImpl-example] example={}", JSON.toJSONString(example));
        List<CustomsInventoryItemDO> doList = customsInventoryItemMapper.selectByExample(example);
        log.info("[op:CustomsInventoryServiceImpl-listItemByProductIdSection] doList={}", JSON.toJSONString(doList));
        return doList.stream().map(InventoryBuilder::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public Map<Long, List<CustomsInventoryItemDTO>> listItemByInventoryS(List<CustomsInventoryDTO> inventoryDTOS) {
        if (CollectionUtils.isEmpty(inventoryDTOS)) {
            return new HashMap<>();
        }
        Date createFrom = null;
        Date createTo = null;
        List<Long> ids = inventoryDTOS.stream().map(CustomsInventoryDTO::getId).collect(Collectors.toList());
        for (CustomsInventoryDTO inventoryDTO : inventoryDTOS) {
            Date createTime = inventoryDTO.getCreateTime();
            if (createFrom == null || new DateTime(createFrom).isAfter(new DateTime(createTime))) {
                createFrom = createTime;
            }
            if (createTo == null || new DateTime(createTo).isBefore(new DateTime(createTime))) {
                createTo = createTime;
            }
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createFrom));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTo));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("customsInventoryId", ids);
        example.and(criteria);
        List<CustomsInventoryItemDO> doList = customsInventoryItemMapper.selectByExample(example);
        Map<Long, List<CustomsInventoryItemDTO>> result = new HashMap<>();
        for (CustomsInventoryItemDO customsInventoryItemDO : doList) {
            List<CustomsInventoryItemDTO> itemDTOS = result.get(customsInventoryItemDO.getCustomsInventoryId());
            if (itemDTOS == null) {
                itemDTOS = new ArrayList<>();
            }
            itemDTOS.add(InventoryBuilder.buildItemDTO(customsInventoryItemDO));
            result.put(customsInventoryItemDO.getCustomsInventoryId(), itemDTOS);
        }
        return result;
    }

    @Override
    public List<CustomsInventoryDTO> listByStatus(Integer status, Integer limit, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        return this.listByStatusAndCreateTime(
                status
                , limit
                , ShardingBaseExampleBuilder.quarterStart(sectionDate)
                , ShardingBaseExampleBuilder.quarterEnd(sectionDate)
        );
    }

    @Override
    public List<CustomsInventoryDTO> listByStatusAndCreateTime(Integer status, Integer limit, Date createFrom, Date createTo) {
        RowBounds rowBounds = new RowBounds(0, limit);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(createFrom);
        timeRangeParam.setEndDate(createTo);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", status);
        example.and(criteria);
        List<CustomsInventoryDO> doList = customsInventoryBaseService.selectByExampleAndRowBounds(example, rowBounds);
        return doList.stream().map(InventoryBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public CustomsInventoryItemDTO getItemById90Days(Long id) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        // timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        CustomsInventoryItemDO itemDo = customsInventoryItemMapper.selectOneByExample(example);
        return InventoryBuilder.buildItemDTO(itemDo);
    }

    @Override
    public List<CustomsInventoryItemDTO> getItemByInventoryId90Days(Long inventoryId) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("customsInventoryId", inventoryId);
        example.and(criteria);
        List<CustomsInventoryItemDO> itemDo = customsInventoryItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(itemDo)) {
            return Collections.emptyList();
        }
        return itemDo.stream()
                .map(z -> InventoryBuilder.buildItemDTO(z))
                .collect(Collectors.toList());
    }

    @Override
    public CustomsInventoryDTO findByInventoryNo90Days(String inventoryNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        //timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryNo", inventoryNo);
        example.and(criteria);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findByInventoryNoEs(String inventoryNo) {
        List<CustomsSingleInventoryEsDTO> inventoryEsDTOS = customsSingleInventoryEsDao.findByInventoryNo(inventoryNo);
        if (CollectionUtils.isEmpty(inventoryEsDTOS)) {
            return null;
        }
        CustomsSingleInventoryEsDTO customsSingleInventoryEsDTO = inventoryEsDTOS.get(0);
        if (Objects.isNull(customsSingleInventoryEsDTO)) {
            return null;
        }
        return findBySnSection(customsSingleInventoryEsDTO.getSn());
    }

    /**
     * 根据申报单号查询清单商品项
     *
     * @param declareNo
     * @return
     */
    @Override
    public CustomsInventoryDTO findByDeclareNo90Days(String declareNo) {
        if (Objects.isNull(declareNo)) {
            return null;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        //timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", declareNo);
        example.and(criteria);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findByDeclareNo(String declareNo, Date create) {
        if (Objects.isNull(declareNo) || Objects.isNull(create)) {
            return null;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(create));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(create));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", declareNo);
        example.and(criteria);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }


    @Override
    public CustomsInventoryDTO findByDeclareOrderNoFull(String declareOrderNo) {
        if (StringUtils.isEmpty(declareOrderNo)) {
            return null;
        }
        // 先当前季度查，查不到再往前追溯
        CustomsInventoryDTO customsInventoryDTO = findByDeclareNo(declareOrderNo, new Date());
        if (customsInventoryDTO != null) {
            return customsInventoryDTO;
        }
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo));
        // Step::返回值处理
        CustomsInventoryDO result = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(result);
    }

    @Override
    public CustomsInventoryDTO findByDeclareNoAndEbpId(String declareNo, Long ebpId, Date create) {
        if (Objects.isNull(declareNo) || Objects.isNull(create) || Objects.isNull(ebpId)) {
            return null;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(create));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(create));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", declareNo).andEqualTo("ebpId", ebpId);
        example.and(criteria);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    @Override
    public CustomsInventoryDTO findByEbcIdAndDeclareNo90Days(Long ebcId, String declareOrderNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        //timeRangeParam.setEndDate(new Date());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ebcId", ebcId);
        criteria.andEqualTo("declareOrderNo", declareOrderNo);
        example.and(criteria);
        CustomsInventoryDO customsInventoryDO = customsInventoryBaseService.selectOneByExample(example);
        return InventoryBuilder.buildDTO(customsInventoryDO);
    }

    private void updateByIdSection(Long id, CustomsInventoryDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        customsInventoryBaseService.updateByExampleSelective(template, example);
    }


    @Override
    public void updateStatusSection(Long id, Integer status, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setStatus(status);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateIsOccupiedStockSection(Long id, Boolean isOccupiedStock, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setIsOccupiedStock(isOccupiedStock);
        this.updateByIdSection(id, template, sectionDate);
    }

    /**
     * 修改售后状态
     * 同步es
     *
     * @param id
     * @param afterStatus
     * @param sectionDate
     */
    @Override
    public void updateAfterStatus(Long id, Integer afterStatus, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setAfterSalesStatus(afterStatus);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateAfterStatus(Long id, Integer afterStatus, Integer status, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setAfterSalesStatus(afterStatus);
        template.setStatus(status);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateAfterStatus(CustomsInventoryDTO customsInventoryDTO, Integer afterStatus) {
        if (Objects.isNull(customsInventoryDTO)) {
            return;
        }
        this.updateAfterStatus(customsInventoryDTO.getId(), afterStatus, customsInventoryDTO.getCreateTime());
    }

    @Override
    public void updateAfterStatusAndCheckoutStatus(CustomsInventoryDTO customsInventoryDTO, Integer afterSaleStatus, Integer checkoutStatus) {
        if (Objects.isNull(customsInventoryDTO)) {
            return;
        }
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setAfterSalesStatus(afterSaleStatus);
        template.setCheckOutStatus(checkoutStatus);
        this.updateByIdSection(customsInventoryDTO.getId(), template, customsInventoryDTO.getCreateTime());
    }

    @Override
    public void updatePayerSection(Long id, String payerId, String payerName, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setBuyerIdNumber(payerId);
        template.setBuyerName(payerName);
        template.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateLogisticsSection(Long id, String logisticsNo, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setLogisticsNo(logisticsNo);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setStatus(status);
        template.setLastDeclareTime(new Date());
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate, Integer declareFrequency) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setStatus(status);
        template.setLastDeclareTime(new Date());
        template.setDeclareFrequency(declareFrequency);
        this.updateByIdSection(id, template, sectionDate);
    }


    @Override
    public void updateStatus(Long id, Integer status, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setStatus(status);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateStatusResetDeclareTime(String sn, CustomsActionStatus status) {

        String tableName = QuarterUtil.getInventroyTableBySn(sn);
        if (StringUtils.isEmpty(tableName)) {
            return;
        }
        customsInventoryBaseService.updateStatusResetDeclareTime(sn, tableName, status.getValue());
    }

    @Override
    public void updateBuyerInfo(Long id, String buyerIdNum, String buyerName, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setBuyerIdNumber(buyerIdNum);
        template.setBuyerName(buyerName);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, Long customsPassTime, String preNo, String invtNo, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        if (customsPassTime != null) {
            template.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
            template.setCustomsPassTime(new DateTime(customsPassTime).toDate());
        }
        if (!StringUtils.isEmpty(preNo)) {
            template.setPreNo(preNo);
        }
        if (!StringUtils.isEmpty(invtNo)) {
            template.setInventoryNo(invtNo);
        }
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, String customsStatus, String customsDetail, Long customsPassTime, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        if (customsPassTime != null) {
            template.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
            template.setCustomsPassTime(new DateTime(customsPassTime).toDate());
        }
        if (!StringUtils.isEmpty(customsStatus)) {
            template.setCustomsStatus(customsStatus);
        }
        if (!StringUtils.isEmpty(customsDetail)) {
            template.setCustomsDetail(customsDetail);
        }
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, String customsStatus, String customsDetail, Long customsPassTime, Date sectionDate, Integer checkOutStatus) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        if (checkOutStatus != null) {
            template.setCheckOutStatus(checkOutStatus);
        }
        if (customsPassTime != null) {
            template.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
            template.setCustomsPassTime(new DateTime(customsPassTime).toDate());
        }
        if (!StringUtils.isEmpty(customsStatus)) {
            template.setCustomsStatus(customsStatus);
        }
        if (!StringUtils.isEmpty(customsDetail)) {
            template.setCustomsDetail(customsDetail);
        }
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, Long customsPassTime, String customsDetail, String preNo, String invtNo, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
        if (customsPassTime != null) {
            template.setCustomsPassTime(new DateTime(customsPassTime).toDate());
        } else {
            template.setCustomsPassTime(new Date());
        }
        if (!StringUtils.isEmpty(preNo)) {
            template.setPreNo(preNo);
        }
        if (!StringUtils.isEmpty(invtNo)) {
            template.setInventoryNo(invtNo);
        }
        template.setCustomsStatus(CustomsStat.CUSTOMS_PASS.getValue());
        if (!StringUtils.isEmpty(customsDetail)) {
            template.setCustomsDetail(customsDetail);
        }
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setCustomsStatus(customsStatus);
        template.setCustomsDetail(customsDetail);
        template.setLastCustomsTime(lastCustomsTime);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByBaseinfo(Long id, String buyerIdNumber, String buyerName, String consigneeAddress, String extraJson, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setBuyerIdNumber(buyerIdNumber);
        template.setBuyerName(buyerName);
        template.setConsigneeAddress(consigneeAddress);
        template.setExtraJson(extraJson);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByItemBaseInfo(Long id, String itemNo, String extraJson, Date sectionDate) {
        CustomsInventoryItemDO template = new CustomsInventoryItemDO();
        template.setItemNo(itemNo);
        template.setExtraJson(extraJson);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        customsInventoryItemMapper.updateByExampleSelective(template, example);
    }

    @Override
    public void updateByItemBaseInfo(Long id, Long bookItemId, String itemName, String extraJson, Date sectionDate) {
        CustomsInventoryItemDO template = new CustomsInventoryItemDO();
        template.setBookItemId(bookItemId);
        template.setItemName(itemName);
        template.setExtraJson(extraJson);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        customsInventoryItemMapper.updateByExampleSelective(template, example);
    }

    @Override
    public void updateByItemBaseInfo(CustomsInventoryItemDTO inventoryItemDTO, Date sectionDate) {
        CustomsInventoryItemDO template = new CustomsInventoryItemDO();
        template.setExtraJson(inventoryItemDTO.getExtraJson());
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", inventoryItemDTO.getId());
        example.and(criteria);
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        customsInventoryItemMapper.updateByExampleSelective(template, example);
    }

    @Override
    public void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setExitRegionStatus(exitRegionStatus);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateExitRegionStatus(Long id, Integer exitRegionStatus, Date finishTime, Date sectionDate) {
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setExitRegionStatus(exitRegionStatus);
        template.setCheckOutTime(finishTime);
        this.updateByIdSection(id, template, sectionDate);
    }

    public static String getQuarterBySn(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        String quarter = null;
        try {
            String dateStr = sn.substring(2, 12);
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
            Date date = dateTimeFormatter.parseDateTime(dateStr).toDate();
            quarter = DateHelper.getQuarter(date);
        } catch (Exception ex) {
            log.warn("编码：，获取所属季度数据异常：{}", sn, ex.getMessage(), ex);
        }
        return quarter;
    }

    @Override
    public List<CustomsInventoryItemDTO> listItemBySnSection(List<CustomsInventoryDTO> customsInventoryList) {
        Map<Long, CustomsInventoryDTO> inventoryDTOMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getId, Function.identity(), (k1, k2) -> k1));
        List<String> customsInventorySnList = customsInventoryList.stream().map(CustomsInventoryDTO::getSn).distinct().collect(Collectors.toList());
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(customsInventorySnList);
        if (CollectionUtils.isEmpty(quarterMap)) {
            return null;
        }
        List<CustomsInventoryItemDTO> itemDTOList = new ArrayList<>();
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<Long> idList = new ArrayList<>();
            v.forEach(sn -> {
                CustomsInventoryDTO inventoryDTO = inventoryDTOMap.get(sn);
                if (Objects.nonNull(inventoryDTO)) {
                    idList.add(inventoryDTO.getId());
                }
            });
            List<CustomsInventoryItemDO> itemDOList = customsInventoryItemBaseService.selectByCustomsInventorySnList(idList, beginOfQuarter, endOfQuarter);
            if (!CollectionUtils.isEmpty(itemDOList)) {
                List<CustomsInventoryItemDTO> inventoryItemDTOS = ConvertUtil.listConvert(itemDOList, CustomsInventoryItemDTO.class);
                itemDTOList.addAll(inventoryItemDTOS);
            }
        });
        return itemDTOList;
    }

    @Override
    public void updateExitRegionStatusBySn(List<CustomsInventoryDTO> inventoryDTOs, Integer exitRegionStatus, Date finishTime) {
        Map<String, List<CustomsInventoryDTO>> quarterMap = splitByQuarter(inventoryDTOs);
        if (CollectionUtils.isEmpty(quarterMap)) {
            return;
        }
        CustomsInventoryDO template = new CustomsInventoryDO();
        template.setExitRegionStatus(exitRegionStatus);
        template.setCheckOutTime(finishTime);
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<Long> idList = v.stream().map(CustomsInventoryDTO::getId).distinct().collect(Collectors.toList());
            TimeRangeParam timeRangeParam = new TimeRangeParam();
            timeRangeParam.setBeginDate(beginOfQuarter);
            timeRangeParam.setEndDate(endOfQuarter);
            // Step::根据时间区间检索更新
            Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("id", idList);
            example.and(criteria);
            customsInventoryBaseService.updateByExampleSelective(template, example);
        });
    }

    @Override
    public void rePush(String sn, Boolean sendNow) {
        log.info("[op:OrderServiceImpl-resetDeclare] reset with action sn={}, action={}", sn, RouteActionEnum.DECLARE_INVENTORY.getDesc());
        CustomsInventoryDTO customsInventoryDTO = this.findBySnSection(sn);
        if (customsInventoryDTO == null) {
            throw new ArgsErrorException("对不起，非法操作重推清单不存在");
        }
        if (!StringUtils.isEmpty(customsInventoryDTO.getCustomsDetail())) {
            if (customsInventoryDTO.getCustomsDetail().contains("待人工审核")) {
                throw new ArgsErrorException("清单状态为待人工审核，无法重推清单");
            }
            if (customsInventoryDTO.getCustomsDetail().contains("查验")) {
                throw new ArgsErrorException("清单状态为海关查验，无法重推清单");
            }
        }
        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        if (orderDTO.getStatus().equals(OrderStatus.DEC_ING.getValue()) &&
                customsInventoryDTO.getLastDeclareTime() != null &&
                new DateTime(customsInventoryDTO.getLastDeclareTime()).isAfter(DateTime.now().minusMinutes(10))
        ) {
            log.info("[op:OrderServiceImpl-resetDeclare] not reset. last update time={}", customsInventoryDTO.getUpdateTime());
            // 申报中，且更新时间小于十分钟，则不允许重推
            throw new ArgsErrorException("重推间隔需大于十分钟");
        }
        if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_WAIT.getValue())) {
            this.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_WAIT.getValue(), customsInventoryDTO.getCreateTime());
        }
        if (sendNow) {
            orderService.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
//            orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
            orderDeclareMQProducer.send(orderDTO);
        }
    }

    @Override
    public void timeOut(CustomsInventoryDTO inventoryDTO) {
        CustomsStatusMappingDO mappingDO = customsStatusMappingMapper.findByCode("TIME_OUT");
        this.updateStatusSection(inventoryDTO.getId(), CustomsActionStatus.DEC_FAIL.getValue(), inventoryDTO.getCreateTime());
        OrderDTO orderDTO = orderService.findBySnSection(inventoryDTO.getOrderSn());
        orderService.addExceptionSection(inventoryDTO.getOrderId(), mappingDO.getId(), "订单申报超时", orderDTO.getCreateTime());
    }

    @Override
    public void getJdHourlyReportJob(JdHourlyReportReqVo reportReqVo) {

        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(1).toDate());
        timeRangeParam.setBeginDate(DateTime.now().toDate());
        Calendar instance = Calendar.getInstance();
        Date now = instance.getTime();
        instance.set(Calendar.MINUTE, 0);
        instance.set(Calendar.SECOND, 0);
        Date closestHour = instance.getTime();
        // 获取当天0点
        instance.set(Calendar.HOUR_OF_DAY, 0);
        Date todayStart = instance.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        if (Objects.equals(todayStart, closestHour)) {
            todayStart = DateUtils.addDay(todayStart, -1);
            XxlJobLogger.log("如果为0点 从前一天开始统计");
        }
        XxlJobLogger.log("getJdHourlyReportJob 当前时间:{} 查询范围 {} - {}", sdf.format(now), sdf.format(todayStart), sdf.format(closestHour));
        log.info("getJdHourlyReportJob 当前时间:{} 查询范围 {} - {}", sdf.format(now), sdf.format(todayStart), sdf.format(closestHour));

        // 小时报区分账册 【金义】：T2924W000078 【义乌】：T2925W000007
        List<String> accountList = Arrays.asList("T2924W000078", "T2925W000007");
        if (!CollectionUtils.isEmpty(reportReqVo.getAccountList())) {
            accountList = reportReqVo.getAccountList();
        }

        for (String bookNo : accountList) {
            StringBuilder markdownStr = new StringBuilder();
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOByNo(bookNo);
            if (Objects.isNull(customsBookDTO)) {
                continue;
            }
            CustomsDistrictEnum customs = CustomsDistrictEnum.getEnum(customsBookDTO.getCustomsDistrictCode());
            markdownStr.append("<font color=\\\"red\\\">**").append(customs.getDesc()).append("口岸").append("**</font>\n");
            Long bookId = customsBookDTO.getId();
            Integer orderTotal = getJdTodayOrderTotal(bookId);
            markdownStr.append("京东**").append(sdf.format(todayStart)).append(" 至 ").append(sdf.format(closestHour)).append("**小时报").append("\n");
            markdownStr.append(">").append("服务商订单接收总量").append(": ").append(orderTotal).append("\n");
            Integer noReplyCount = getReportResult(todayStart, closestHour, bookId);
            markdownStr.append(">").append("清单无回执").append(": ").append(noReplyCount).append("\n");
            Integer declareFinishCount = getDeclareFinishResult(todayStart, new Date(), bookId);
            markdownStr.append(">").append("清关成功量").append(": ").append(declareFinishCount).append("\n");
            DecimalFormat df = new DecimalFormat("0.00");
            double rate = 1;
            if (orderTotal != 0) {
                rate = declareFinishCount.doubleValue() / orderTotal.doubleValue();
            }
            String rateStr = df.format(rate * 100) + "%";
            markdownStr.append(">").append("清关进度").append(": ").append(rateStr).append("\n");
            List<JdHourlyReportReqVo.TypeResult> dataList = reportReqVo.getDataList();
            int otherCount = noReplyCount;
            int backlogCount = noReplyCount;
            for (JdHourlyReportReqVo.TypeResult typeResult : dataList) {
                Integer integer = getReportResult(typeResult.getDesc(), todayStart, closestHour, bookId);
                markdownStr.append(">").append(typeResult.getType()).append(": ").append(integer).append("\n");
                if (!Objects.equals(typeResult.getDesc(), "限额")) {
                    backlogCount += integer;
                }
                otherCount += integer;
            }
            Integer other = orderTotal - declareFinishCount - otherCount;
            markdownStr.append(">").append("其他原因").append(": ").append(other).append("\n");
            markdownStr.append(">").append("目前积压").append(": ").append(backlogCount + other).append("\n");
            WechatNotifyUtils.wechatNotifyMd(reportReqVo.getWebHook(), reportReqVo.getPhones(), markdownStr.toString());
        }
    }

    private String buildEmailContent(String customs, String statisticTime, Integer orderTotal, Integer declareFinishCount,
                                     String rateStr, Integer totalBacklog, Map<String, Integer> resultMap) {
        StringBuilder blackLogContent = new StringBuilder();
        Lists.newArrayList("清单无回执", "无支付信息", "无运单信息", "人工审核", "海关查验", "挂起", "购买超限", "原产国不一致", "其他原因")
                .forEach(key -> {
                    blackLogContent.append(String.format("        <tr><td style=\"padding: 4px 0 4px 20px;\">%s</td><td style=\"padding: 4px 0; text-align: right;\">%s</td></tr>\n", key, resultMap.get(key)));
                });
        return "<div style=\"font-family: Arial, sans-serif;\">\n" +
                "    <table style=\"border-collapse: collapse; width: 100%; max-width: 600px; line-height: 1.6;\">\n" +
                "        <!-- 表头 -->\n" +
                "        <tr>\n" +
                "            <td colspan=\"2\" style=\"padding: 8px 0; border-bottom: 2px solid #ff0000;\">\n" +
                "                <span style=\"color: #ff0000; font-weight: bold; font-size: 18px;\">" + customs + "口岸订单清关状态</span>\n" +
                "                <span style=\"float: right;\">" + statisticTime + "</span>\n" +
                "            </td>\n" +
                "        </tr>\n" +
                "        \n" +
                "        <!-- 核心数据 -->\n" +
                "        <tr><td style=\"padding: 8px 0;\">服务商订单接收量</td><td style=\"padding: 8px 0; text-align: right;\">" + orderTotal + "</td></tr>\n" +
                "        <tr><td style=\"padding: 8px 0;\">清关成功量</td><td style=\"padding: 8px 0; text-align: right;\">" + declareFinishCount + "</td></tr>\n" +
                "        <tr><td style=\"padding: 8px 0;\">清关进度 (%)</td><td style=\"padding: 8px 0; text-align: right;\">" + rateStr + "</td></tr>\n" +
                "        <tr><td style=\"padding: 8px 0; font-weight: bold;\">目前积压</td><td style=\"padding: 8px 0; text-align: right; font-weight: bold;\">" + totalBacklog + "</td></tr>\n" +
                "        \n" +
                "        <!-- 积压原因标题 -->\n" +
                "        <tr>\n" +
                "            <td style=\"padding: 8px 0; font-weight: bold; border-top: 1px solid #ddd;\">积压原因</td>\n" +
                "            <td style=\"padding: 8px 0; border-top: 1px solid #ddd;\"></td>\n" +
                "        </tr>\n" +
                "        \n" +
                "        <!-- 积压原因子项 -->\n" +
                blackLogContent +
                "    </table>\n" +
                "</div>";
    }

//    private static Integer getJdTodayOrderTotal() {
//        HttpRequest getHttp = HttpRequest.get("http://out.order.yang800.com/outshop/getJdOrderCount");
//        if (getHttp.ok()) {
//            String body = getHttp.body();
//            System.out.println(body);
//            Pattern pattern = Pattern.compile("京东订单量为：[0-9]+\",\"");
//            Matcher matcher = pattern.matcher(body);
//            if (matcher.find()) {
//                String subStr = matcher.group();
//                return Integer.parseInt(subStr.substring(7, subStr.lastIndexOf("\",\"")));
//            }
//        }
//        return null;
//    }

    private Integer getJdTodayOrderTotal(Long bookId) {
        Date date = new Date();
        Example example = new Example(CustomsInventoryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ebpId", 65)
                .andBetween("createTime", cn.hutool.core.date.DateUtil.beginOfDay(date), date);
        if (Objects.nonNull(bookId)) {
            criteria.andEqualTo("accountBookId", bookId);
        }
        int count = customsInventoryBaseService.selectCountByExample(example);
        log.info("getJdHourlyReportJob getReportResult detail:{} count:{}", "接收总量", count);
        return count;
    }

    /**
     * 芥舟清单放行回执回调
     *
     * @param message
     * @throws Exception
     */
    @Override
    public void jieztechCallback(OrderCustomsInventoryMessage message) throws Exception {
        CustomsInventoryDTO inventoryDTO = this.findByInventoryNo90Days(message.getInventoryNo());
        CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(inventoryDTO.getEbpId());
        CompanyDTO ebcCompany = baseDataService.getUnifiedCrossCodeCompanyById(inventoryDTO.getEbcId());
        CompanyDTO agentCompany = baseDataService.getUnifiedCrossCodeCompanyById(inventoryDTO.getAgentCompanyId());
        JieztechCallbackReq req = new JieztechCallbackReq();
        req.setCustomsCode(inventoryDTO.getCustoms());
        req.setEbpCode(ebpCompany.getCode());
        req.setEbcCode(ebcCompany.getCode());
        req.setAgentCode(agentCompany.getCode());
        req.setCopNo(inventoryDTO.getSn());
        req.setPreNo(inventoryDTO.getPreNo());
        req.setInvtNo(message.getInventoryNo());
        req.setReturnStatus(CustomsStat.CUSTOMS_PASS.getValue());
        req.setReturnTime(DateUtil.getCDateString("yyyyMMddHHmmssSSS"));
        req.setReturnInfo(inventoryDTO.getCustomsDetail());
        // 获取下上游传下来的物流公司编码
        CustomsInventoryExtra extra = JSON.parseObject(inventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        String logisticsCode = Optional.ofNullable(extra).map(CustomsInventoryExtra::getExpressCode).orElse(null);
        req.setLogisticsCode(logisticsCode);
        String httpAddress = "/api/declare/entry/hzCrossAgentCallback";
        jieztechDeclareService.wrapSignAndRequest(req, httpAddress);
    }


    private Example getExample(Date createTime) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        return example;
    }

    private Example getExample(Date createTimeFrom, Date createTimeTo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTimeFrom));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTimeTo));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        return example;
    }

    private Integer getReportResult(Date oneHourBefore, Date now, Long bookId) {
        Example example = new Example(CustomsInventoryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ebpId", 65)
                .andEqualTo("status", OrderStatus.DEC_ING.getValue())
                .andIsNull("customsDetail")
                .andBetween("createTime", oneHourBefore, now);
        if (Objects.nonNull(bookId)) {
            criteria.andEqualTo("accountBookId", bookId);
        }
        int count = customsInventoryBaseService.selectCountByExample(example);
        log.info("getJdHourlyReportJob getReportResult detail:{} count:{}", "清单无回执", count);
        return count;
    }

    private Integer getReportResult(String keyWord, Date oneHourBefore, Date now) {
        Example example = new Example(CustomsInventoryDO.class);
        example.createCriteria().andEqualTo("ebpId", 65)
                .andLike("customsDetail", "%" + keyWord + "%")
                .andBetween("createTime", oneHourBefore, now);
        int count = customsInventoryBaseService.selectCountByExample(example);
        log.info("getJdHourlyReportJob getReportResult detail:{} count:{}", keyWord, count);
        return count;
    }

    private Integer getReportResult(String keyWord, Date oneHourBefore, Date now, Long bookId) {
        Example example = new Example(CustomsInventoryDO.class);
        example.createCriteria().andEqualTo("ebpId", 65)
                .andLike("customsDetail", "%" + keyWord + "%")
                .andBetween("createTime", oneHourBefore, now)
                .andEqualTo("accountBookId", bookId);
        int count = customsInventoryBaseService.selectCountByExample(example);
        log.info("getJdHourlyReportJob getReportResult detail:{} count:{}", keyWord, count);
        return count;
    }

    private Integer getDeclareFinishResult(Date oneHourBefore, Date now, Long bookId) {
        Date date = new Date();
        Example example = new Example(CustomsInventoryDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("ebpId", 65)
                .andEqualTo("status", CustomsActionStatus.DEC_SUCCESS.getValue())
                .andBetween("createTime", cn.hutool.core.date.DateUtil.beginOfDay(date), date);
        if (Objects.nonNull(bookId)) {
            criteria.andEqualTo("accountBookId", bookId);
        }
        int count = customsInventoryBaseService.selectCountByExample(example);
        log.info("getJdHourlyReportJob getReportResult detail:{} count:{}", "清关成功量", count);
        return count;
    }

    /**
     * 人审取消单 数据库查询
     * 用于清单es数据同步
     *
     * @return
     */
    @Override
    public List<CustomsInventoryDTO> getCusPersonInvOrderDTOListByUpdateTime(Long createTimeFrom, Long createTimeTo, Long updateTimeFrom, Long updateTimeTo) {
        Date now = new Date();
        Date before90Days = cn.hutool.core.date.DateUtil.offsetDay(now, -90);
        Date timeFrom = LongUtil.isNone(createTimeFrom) ? before90Days : new Date(createTimeFrom);
        Date timeTo = LongUtil.isNone(createTimeTo) ? now : new Date(createTimeTo);

        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(timeFrom);
        timeRangeParam.setEndDate(timeTo);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", 0);
        criteria.andBetween("updateTime", new Date(updateTimeFrom), new Date(updateTimeTo));
//        if (Objects.nonNull(reqVO.getBookId())) {
//            criteria.andEqualTo("accountBookId", reqVO.getBookId());
//        }
        criteria.andLike("customsDetail", "%人工审核%");
        example.and(criteria);
        List<CustomsInventoryDO> customsInventoryDOS = customsInventoryBaseService.selectByExample(example);
        if (CollectionUtils.isEmpty(customsInventoryDOS)) {
            return new ArrayList<>();
        }
        return ConvertUtil.listConvert(customsInventoryDOS, CustomsInventoryDTO.class);
    }

    /**
     * 人审取消单 es查询
     *
     * @param reqVO
     * @return
     */
    @Override
    public List<CustomsSingleInventoryEsDTO> getCusPersonInvOrderEsDTOList(ExportCusOfficerDataReqVO reqVO) {
        return customsSingleInventoryEsDao.getCusPersonInvOrderEsDTOList(reqVO);
    }

//    @Override
//    @Transactional(rollbackFor = Exception.class)
//    public void logicDeleteBySn(String sn) {
//        if (StringUtils.isEmpty(sn)) {
//            log.error("customsInventoryService - deleteBySn sn为空");
//            return;
//        }
//        String dateStr = sn.substring(2, 12);
//        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
//        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
//        TimeRangeParam timeRangeParam = new TimeRangeParam();
//        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
//        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
//        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
//        example.and(example.createCriteria().andEqualTo("sn", sn).andEqualTo("deleted", 0));
//
//        List<CustomsInventoryDO> customsInventoryDOS = customsInventoryBaseService.selectByExample(example);
//        if (CollectionUtils.isEmpty(customsInventoryDOS)) {
//            log.error("customsInventoryService - deleteBySn 清单不存在 sn={}", sn);
//            return;
//        }
//        if (customsInventoryDOS.size() > 1) {
//            log.error("customsInventoryService - deleteBySn 清单重复 sn={}", sn);
//            return;
//        }
//        CustomsInventoryDO target = customsInventoryDOS.get(0);
//        CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
//        customsInventoryDO.setDeleted(true);
//        customsInventoryBaseService.updateByExampleSelective(customsInventoryDO, example);
//        customsInventoryItemBaseService.batchLogicDeleteByInveOrderId(target.getId(), timeRangeParam);
//    }

    @Override
    public void updateIsOccupiedStockBySnList(List<String> inventorySnList, Boolean isOccupiedStock) {
        if (CollectionUtils.isEmpty(inventorySnList)) {
            return;
        }
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(inventorySnList);
        // 遍历每个季度，对应表进行更新
        quarterMap.forEach((k, v) -> {
            String sn = v.get(0);
            Date date = this.snToDate(sn);
            TimeRangeParam timeRangeParam = new TimeRangeParam();
            timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(date));
            timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(date));
            Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
            example.and(example.createCriteria().andIn("sn", v).andEqualTo("deleted", 0));
            CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
            customsInventoryDO.setIsOccupiedStock(isOccupiedStock);
            customsInventoryBaseService.updateByExampleSelective(customsInventoryDO, example);
        });
    }

    @Override
    public void updateTaxBillStatusSection(Integer taxBillStatus, Long id, Date sectionTime) {
        CustomsInventoryDO inventoryDO = new CustomsInventoryDO();
        inventoryDO.setTaxBillStatus(taxBillStatus);
        this.updateByIdSection(id, inventoryDO, sectionTime);
    }

    @Override
    public void updatTotalRefundTaxSection(BigDecimal totalRefundTax, Long id, Date sectionTime) {
        CustomsInventoryDO inventoryDO = new CustomsInventoryDO();
        inventoryDO.setTotalRefundTax(totalRefundTax);
        this.updateByIdSection(id, inventoryDO, sectionTime);
    }

    /**
     * 查询电子税单已生成下清单未放行的订单
     *
     * @param beginTime
     * @param endTime
     * @return
     */
    @Override
    public List<CustomsInventoryDTO> checkTaxBillStatusByTimeRange(Date beginTime, Date endTime) {
        Example example = new Example(CustomsInventoryDO.class);
        example.createCriteria()
                .andBetween("createTime", beginTime, endTime)
                .andEqualTo("taxBillStatus", TaxBillStatusEnums.SENT.getCode())
                .andNotEqualTo("customsStatus", CustomsStat.CUSTOMS_PASS.getValue());
        List<CustomsInventoryDO> customsInventoryDOS = customsInventoryBaseService.selectByExample(example);
        return ConvertUtil.listConvert(customsInventoryDOS, CustomsInventoryDTO.class);
    }

    private Date snToDate(String sn) {
        if (Objects.isNull(sn) || sn.length() < 12) {
            log.error("sn为空或格式错误");
            return null;
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        return dateTimeFormatter.parseDateTime(dateStr).toDate();
    }

    @Override
    public void updateCheckOutTimeAndStatusSection(Long id, Integer checkOutStatus, Date finishDate, Date sectionTime) {
        CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryDO);
        }
        customsInventoryDO.setUpdateTime(new Date());
        customsInventoryDO.setCheckOutStatus(checkOutStatus);
        customsInventoryDO.setCheckOutTime(finishDate);
        customsInventoryBaseService.updateByExampleSelective(customsInventoryDO, example);
    }
}
