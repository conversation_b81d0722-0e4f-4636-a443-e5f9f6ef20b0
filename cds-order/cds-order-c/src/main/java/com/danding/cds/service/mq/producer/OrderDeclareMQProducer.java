package com.danding.cds.service.mq.producer;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.DeclareScanMqDto;
import com.danding.cds.bean.dto.OrderExtraDto;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.common.constant.DeclareTopicCons;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.service.MessageSenderService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.service.customs.declare.CustomsDeclareOrderCommand;
import com.danding.cds.utils.DeclareTopicUtil;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Slf4j
@Component
@RestController
@RefreshScope
public class OrderDeclareMQProducer {

    @Autowired
    private MessageSender messageSender;
    @Autowired
    private MessageSenderService messageSenderService;
    @Autowired
    @Lazy
    private CustomsDeclareOrderCommand declareOrderCommand;
    @Autowired
    private BaseDataService baseDataService;
    @Resource
    private CustomsInventoryService customsInventoryService;
    @Resource
    private CustomsOrderService customsOrderService;
    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Value("${msg.delayLevel.config:}")
    private String msgDelayLevelConfigJson;

    @Value("${declareIsolateGrayscaleConfig:}")
    private String declareIsolateGrayscaleConfig;

    private final String ORDER_DECLARE_SCAN_TOPIC = "ccs-order-declare-scan-c-topic";
    private final String ORDER_DECLARE_SCAN_DELAY_TOPIC = "ccs-order-declare-scan-delay-c-topic";

    @RequestMapping("/OrderDeclareMQProducer")
    public void send(String sn) {
        try {
            messageSender.sendMsg(sn, ORDER_DECLARE_SCAN_TOPIC);
        } catch (Exception e) {
            log.error("[op:OrderDeclareProducer] sendMeg exception, cause={}", e.getMessage(), e);
        }
    }

    public void send(OrderDTO orderDTO) {

        try {
            OrderExtraDto orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtraDto.class);
            String routeCode = Optional.ofNullable(orderExtra).map(OrderExtraDto::getSubmit).map(OrderSubmitDto::getRouteCode).orElse(null);
            String sn = orderDTO.getSn();
            RouteDTO routeDTO = baseDataService.getRouteDTOByCode(routeCode);
            /**
             * NO.1 是否延迟发送，如果是则直接发送延迟，先固定队列好勒
             */
            MsgDelayLevelConfig msgDelayLevelConfig = getMsgDelayConfig(routeCode);
            if (msgDelayLevelConfig != null) {
                log.info("申报单号: {} ,routeCode={}, delayLevel ={}", orderDTO.getDeclareOrderNo(), routeCode, msgDelayLevelConfig.getDelayLevel());
                DeclareEnum declareEnumPresent = declareOrderCommand.getDeclareEnumPresent(orderDTO);
                // declareEnumPresent 为空，说明没有下一个满足申报要求的类型，需要尝试改变申报单状态
                if (declareEnumPresent == null) {
                    declareOrderCommand.extracted(orderDTO);
                    return;
                }
                // 重推是否忽略延迟申报
                if (Boolean.TRUE.equals(msgDelayLevelConfig.getRedeclareIgnore())) {
                    boolean isRePush = false;
                    switch (declareEnumPresent) {
                        case CUSTOMS_ORDER:
                            CustomsOrderDTO customsOrderDTO = customsOrderService.findBySnSection(orderDTO.getCustomsOrderSn());
                            isRePush = Objects.nonNull(customsOrderDTO.getDeclareFrequency()) && (customsOrderDTO.getDeclareFrequency() >= 1);
                            break;
                        case INVENTORY:
                            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(orderDTO.getCustomsInventorySn());
                            isRePush = Objects.nonNull(customsInventoryDTO.getDeclareFrequency()) && customsInventoryDTO.getDeclareFrequency() >= 1;
                            break;
                        case SHIPMENT:
                            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findBySn(orderDTO.getCustomsLogisticsSn());
                            isRePush = Objects.nonNull(customsLogisticsDTO.getDeclareFrequency()) && customsLogisticsDTO.getDeclareFrequency() >= 1;
                            break;
                    }
                    if (Boolean.TRUE.equals(isRePush)) {
                        log.info("申报单号: {} ,routeCode={}, 重推不进行延迟申报", orderDTO.getDeclareOrderNo(), routeCode);
                        this.send(sn);
                        return;
                    }
                }
                //匹配申报路径，则执行延迟异步队列
                String delayQueueTopic = DeclareTopicUtil.getDelayDeclareQueue(routeDTO, declareEnumPresent);
                try {
                    log.info("申报单号: {} ,routeCode={}, delayQueueTopic={},delayLevel={}", orderDTO.getDeclareOrderNo(), routeCode, delayQueueTopic, msgDelayLevelConfig.getDelayLevel());
                    messageSenderService.asyncSendMsgDelay(sn, delayQueueTopic, msgDelayLevelConfig.getDelayLevel());
                } catch (Exception e) {
                    log.error("[op:OrderDeclareProducer] asyncSendMsgDelay exception, sn= {} ,cause={}", sn, e.getMessage(), e);
                }
                return;
            }

            /**
             * NO.2 其它的申报隔离判断
             */
            Boolean isolateEnable = false;
            if (StringUtils.isNotBlank(declareIsolateGrayscaleConfig)) {
                JSONObject declareIsolateConfig = JSON.parseObject(declareIsolateGrayscaleConfig);
                String isolateType = declareIsolateConfig.getString("isolateType");
                if (Objects.equals(isolateType, "all")) {
                    isolateEnable = true;
                } else if (Objects.equals(isolateType, "gray")) {
                    String routeCodes = declareIsolateConfig.getString("routeCodes");
                    isolateEnable = StringUtils.isNotBlank(routeCodes) && routeCodes.contains(routeCode);
                }
            }

            // 申报隔离开启后，隔离申报
            if (isolateEnable) {
                log.info("申报单号: {} ,routeCode={}, 配置了申报隔离，走申报隔离逻辑", orderDTO.getDeclareOrderNo(), routeCode);
                this.declareIsolate(orderDTO, routeDTO);
                return;
            }
            // 没有开启,则都放到默认消费组
            this.send(orderDTO.getSn());
        } catch (Exception ex) {
            log.error("申报单号: {} ,申报发送处理队列时异常，原因:{}", orderDTO.getDeclareOrderNo(), ex.getMessage(), ex);
        }
    }

    /**
     * 申报隔离实现：
     * 之所以没有采用一个Topic多个队列的形式做隔离，原因如下：
     * 1. 一个是不同的业务DXP申报方式放在不同的队列，需要提前规划队列数量，不同的业务分配到某些队列里面(业务量决定)，同时需要考虑队列伸缩，实现较麻烦
     * 2. 现阶段业务，可以先以加签方式和海关对接方式的维度，整合到不同的Topic里面，同时特殊的业务用特殊的Topic，也方便Topic维护，从而满足隔离要求
     * <p>
     * 隔离方式：
     * Topic格式 ==>  ccs-declare-加签方式-海关对接方式
     * 注：视业务单量情况来区分定义topic
     * 1. ccs-declare-swxa-other
     * 2. ccs-declare-swxa-zjport
     * 3. ccs-declare-swxa-hzcenter
     * 4. ccs-declare-icCard-${dxpId}-hzcenter
     * 5. ccs-declare-icCard-${dxpId}-zjport
     * 6. ccs-declare-icCard-${dxpId}-other
     * 7. ccs-declare-unknown-hzcenter
     * 8. ccs-declare-unknown-zjport
     * 9. ccs-declare-unknown-other
     *
     * @param orderDTO 申报信息
     */
    private void declareIsolate(OrderDTO orderDTO, RouteDTO routeDTO) {

        // 这里在发送申报前就已经知道需要申报什么，已发送到那个DXP，做申报隔离
        DeclareEnum declareEnumPresent = declareOrderCommand.getDeclareEnumPresent(orderDTO);
        log.info("申报单号：{} ,路由：{}，当前应申报为：{}", orderDTO.getDeclareOrderNo(), routeDTO.getCode(), declareEnumPresent == null ? "空" : declareEnumPresent.getDesc());
        /**
         * NO.1 可能没有可申报的，表示都申报完成了，这里需要修改下申报单的状态，老的逻辑
         */
        if (declareEnumPresent == null) {
            declareOrderCommand.extracted(orderDTO);
            return;
        }

        // NO.1 支付单  支付单申报的业务不多，渠道目前只有，微信、支付宝、联动支付，先用默认的队列
        if (declareEnumPresent == DeclareEnum.PAYMENT) {
            messageSender.sendMsg(orderDTO.getSn(), DeclareTopicCons.CCS_DECLARE_DEFAULT_C_TOPIC);
        } else {
            // NO.2 订单、运单、清单的申报，都根据路由配置的队列隔离
            String declareTopic = DeclareTopicUtil.getDeclareQueue(routeDTO, declareEnumPresent);
            String message = orderDTO.getSn();
            // 非默认队列，发送指定消费顺序
            if (!Objects.equals(declareTopic, DeclareTopicCons.CCS_DECLARE_DEFAULT_C_TOPIC)) {
                DeclareScanMqDto declareScanMqDto = new DeclareScanMqDto();
                declareScanMqDto.setDeclareOrderSn(orderDTO.getSn());
                declareScanMqDto.setDeclareType(declareEnumPresent.getType());
                message = JSON.toJSONString(declareScanMqDto);
            }
            log.info("申报单号：{} ,路由：{}, topic：{}, message: {}", orderDTO.getDeclareOrderNo(), routeDTO.getCode(), declareTopic, message);
            messageSender.sendMsg(message, declareTopic);
        }
    }


    /**
     * 根据申报路径，延迟消息推送(清单)
     * delayLevel:  0s  1s  5s  10s 30s 1m  2m  3m  4m  5m  6m  7m  8m  9m  10m 20m 30m 1h  2h (对应0,1,2,...)
     * level        0   1   2   3   4   5   6   7   8   9   10  11  12  13  14  15  16  17  18
     *
     * @param sn
     * @param routeCode
     */
    public void send(String sn, String routeCode) {
        if (StringUtils.isEmpty(routeCode) || StringUtils.isEmpty(msgDelayLevelConfigJson)) {
            this.send(sn);
        }
        log.info("[op:OrderDeclareProducer] msgDelayLevelConfigJson={}", msgDelayLevelConfigJson);
        List<MsgDelayLevelConfig> configList = JSON.parseArray(msgDelayLevelConfigJson, MsgDelayLevelConfig.class);
        for (MsgDelayLevelConfig msgDelayLevelConfig : configList) {
            if (Objects.equals(routeCode, msgDelayLevelConfig.getRouteCode())) {
                //匹配申报路径，则执行延迟异步队列
                try {
                    log.info("[op:OrderDeclareProducer] asyncSendMsgDelay begin, sn={}, routeCode={}, delayLevel={}", sn, routeCode, msgDelayLevelConfig.getDelayLevel());
                    messageSenderService.asyncSendMsgDelay(sn, ORDER_DECLARE_SCAN_DELAY_TOPIC, msgDelayLevelConfig.getDelayLevel());
                } catch (Exception e) {
                    log.error("[op:OrderDeclareProducer] asyncSendMsgDelay exception, cause={}", e.getMessage(), e);
                }
                return;
            }
        }
        //未匹配到申报路径，采用原先方法
        this.send(sn);
    }

    private MsgDelayLevelConfig getMsgDelayConfig(String routeCode) {

        if (StringUtils.isEmpty(routeCode) || StringUtils.isEmpty(msgDelayLevelConfigJson)) {
            return null;
        }
        List<MsgDelayLevelConfig> configList = JSON.parseArray(msgDelayLevelConfigJson, MsgDelayLevelConfig.class);
        for (MsgDelayLevelConfig msgDelayLevelConfig : configList) {
            if (Objects.equals(routeCode, msgDelayLevelConfig.getRouteCode())) {
                //匹配申报路径，则返回延迟配置
                return msgDelayLevelConfig;
            }
        }
        return null;
    }
}

@Data
class MsgDelayLevelConfig {

    //申报路径
    private String routeCode;

    //mq延迟时间 0s 1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h (对应0,1,2,...)
    private Integer delayLevel;

    //再次申报是否忽略延迟
    private Boolean redeclareIgnore = false;
}
