package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.vo.ByteDanceDeclareReqVo;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.handler.ByteDanceDeclareTrackLogParametersHandler;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSensitivePlainText;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.util.StringUtil;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
@RefreshScope
public class ByteDanceDeclareService {
    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Value("${byteDance.host:}")
    private String BYTE_DANCE_HOST;
    /**
     * 字节云内申报地址
     */
    @Value("${byteDance.cloudDeclare.uri:}")
    private String CLOUD_DECLARE_URI;

    /**
     * 电商平台ebpCode
     */
    @Value("${byteDance.ebpCodes:}")
    private String byteDanceEbpCodes;

    /**
     * 校验是否走云内申报
     *
     * @param ebpCode   电商平台code
     * @param isEncrypt 敏感信息是否加密
     * @return
     */
    public boolean isDeclareWithinCloud(String ebpCode, String extraJson) {
        if (StringUtil.isEmpty(byteDanceEbpCodes)) {
            return false;
        }
        if (StringUtils.isEmpty(extraJson)) {
            return false;
        }
        OrderExtra orderExtra = JSON.parseObject(extraJson, OrderExtra.class);
        Boolean isEncrypt = orderExtra.getSubmit().getIsEncrypt();
        List<String> byteDanceEbpCodeList = Splitter.on(",").splitToList(byteDanceEbpCodes);
        return (byteDanceEbpCodeList.contains(ebpCode) && Boolean.TRUE.equals(isEncrypt));
    }

    /**
     * 字节云内清单申报
     *
     * @param info 数据
     */
    public void cloudInventoryDeclareAndLog(WrapInventoryOrderInfo info) {
        if (Boolean.FALSE.equals(EnvironmentConfig.isOnline())) {
            return;
        }
        ByteDanceDeclareReqVo reqVo = getByteDanceDeclareReqVo(info);
        byteDanceDeclareService.cloudAssembleDeclareInfo(reqVo);
    }

    @Test
    public void test() {
        String infoS = "{\"accountBookDto\":{\"areaCompanyId\":137,\"bookNo\":\"T2924W000229\",\"customsAreaCode\":\"2924\",\"customsDistrictCode\":\"JINYI\",\"id\":21,\"remark\":\"字节4PL账册\"},\"assureCompanyDTO\":{\"cebCode\":\"311096102D\",\"cebName\":\"上海格物致品网络科技有限公司\",\"code\":\"311096102D\",\"name\":\"上海格物致品网络科技有限公司\"},\"customsAreaCode\":\"2924\",\"customsBookId\":21,\"customsInventoryDto\":{\"bookId\":21,\"bookNo\":\"T2924W000229\",\"buyerIdNumber\":\"~TXUCLJXJHG2KCBLYJJIB4PPOFWQUS1ELFWEGYIOEHEI=~OXBOMQF2EE8RCCJGBSJE3XZIZYEDQRD+KTWEHWE9ZZVY1BQY97RG87MZL/OZURUTN2CRQSG1UOUU4RGEOFESM2AX8EXLN11HHIHBNHOJ*CGWIARCTHBIQICABKAESPGO8SZK3OYV4Q0UJAG7CZ7WBGFTT4AODQSNRZEDG/RELBRTEUJUQNK1T8WHCRC2VT7PIQAE9IPQCLRAVLYNAGGA=~1~~\",\"buyerIdType\":\"1\",\"buyerName\":\"#o0NGIug6#npDMOToxc6YFSkFTA5YGJz99c1Kz8ASUPiIYnQ4ecmtiHF8ACOfd4UpE7HCEFvI0W8qbeKnhvJH94JK5DUTEuT/1XurI*CgwIARCtHBiqICABKAESPgo8lw4FsbFDn9R6syFyDh1fCFUnsYleOqH0Z8PH2KlYkBho4T+pcj8YBrWRz7DMUmRXwfBxraYbm6VcXE32GgA=#1##\",\"buyerTelNumber\":\"$HFNtrsMt+weallo5PNEGlYP7DdG5HJZRWFkmdbrIt7I=$Utdz5cP2x/Xlmu4yfNxyUq+nRaTBDFY9HTcoXMUyIUZMZNWAzL5lk8I0fjfLhAFH2AhoScrVQ5e6anajPncE9ahLS2jo4lc=*CgwIARCtHBiqICABKAESPgo8q9UsJuvWJkksrBMPQt3Ewv4jH95pmpea2e8FYyiz/MIsd6SQp6jDZs1DgB2afo75sVxK7VQ7uqJYraO1GgA=$1$$\",\"clientCustoms\":\"zhejiang\",\"consigneeAddress\":\"*_*_*_*#aRrknQq9ofwPx8roDquVPHP+ouSfv0R7pvBbzvoXy0bzt9/0aRrkZl4BHFGAVXm5y0bzOg4lJ5bCKXHhiDf+21DBljipdDRcgqUKtweZe74rzvoXy0bzOg4lT9snaUO0+ZVyOq6Uo/VwfDXfljipaeVKKHnm7w0waoYFx7Nsy0bzt9/0aRrkZl4BHFGAVXm5y0bzOg4lJ5bCKXHhvirw6ikv6ikv6ikv0pEJ21DBljipdDRcgqUKtweZe74rzvoXy0bzOg4lT9snaUO0+ZVyOq6Uo/VwfDXfljippjBsCKenIyt/MhoroTwoy0bzt9/0aRrkZl4BHFGAVXm5y0bzOg4lJ5bCKXHhvirwgxxzIopPvirw0pEJ21DBljipdDRcgqUKtweZe74rzvoXy0bzOg4lzLAMPL0EuyjHEKc9o/VwfDXfljipFuaDhHU3mj7dglW/YWNVDssXcINly0bzt9/0aRrkZl4BHFGAVXm5y0bzOg4lJ5bCKXHhvirwgxxzIopPvirw6ikvnoPq1d3Y0pEJ21DBljipdDRcgqUKtweZe74rzvoXy0bzOg4lJFqaAmidmYF9eogMs1P4ouKQ3pk6o/VwfDXfljipywqUczK/DssXDF0tIbXt85OyJwXjy0bzOg4lMjyFmYF9eogMdC82Voa1fdDGkNSfvSlpX2s2+x/rj8rHgCQv2lB2PIgN2/ES0JUOG16Do/Vw#tNim/+hj6CHFtCyHW8lLCv0hh8jqP96W0UOoAPIQGwwiyEwkxLZ275IWx8K1corO+8zNLdxqD7lTk5U3WgqgqxQ6fniad1q8Fs+wQ1n6dpEM/a+GwKfpYBvwW7FytMnijdhOjWAkMRvQ97/WYIlgRzXHe8s83im8VsL5MwiC38a+kulZ3y4Q6TBepjS4RdfWcBd4cRu4whrNs+Ij9j+rE2ph41Ynuk2tZJEfzwXja36omb9S4J9eCD4Z8G4/zG71rkuu7QZWB4NqOvcbxKaxCvMpDipjgHbMGTGuTOZPU1xRgPEZWRmOhRhrDrahMWXnU1jOmL8iBMt1/zp2NPBOe9Q0xbZiFjAGzfrJD4SCJ0cmL2WgGlA1sKn94f0vMIK5lW40ZsnW3A==*CgwIARCtHBiqICABKAESPgo8WcdvmewPLE2pyB24FWuV85rlLbly5lLJ/0/YtSLkdy7uEl/MVMGhiDOBUBZtuYG5/w7dBxAk010nmzeOGgA=#1##\",\"createTime\":1695631586000,\"customs\":\"JINYI\",\"customsField\":\"2924\",\"discountFee\":0.0,\"ebpCode\":\"311096102D\",\"freight\":0.0000,\"grossWeight\":0.1650,\"id\":913108310951985153,\"insureAmount\":0.0000,\"logisticsNo\":\"75726559805588\",\"mainGName\":\"CENOVIS/圣诺无糖维生素C咀嚼片300片/瓶\",\"netWeight\":0.1500,\"orderNo\":\"6922104220090177256\",\"payTransactionId\":\"420000200920230925817525704800\",\"sn\":\"CI2309251646062083\",\"status\":10,\"taxFee\":3.63},\"declareCompanyCebCode\":\"330766K011\",\"declareCompanyDTO\":{\"cebCode\":\"330766K011\",\"cebName\":\"金华佩奇供应链管理有限公司\",\"code\":\"330766K011\",\"declareConfigList\":[{\"declareCode\":\"ZJ_PORT_ORDER_DECLARE\",\"declareImpl\":\"ZJ_PORT_ORDER_DECLARE\",\"type\":\"customsOrder\"},{\"type\":\"shipment\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_DECLARE\",\"type\":\"inventory\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_CANCEL_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_CANCEL_DECLARE\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_REFUND_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_REFUND_DECLARE\",\"type\":\"inventoryRefund\"}],\"name\":\"金华佩奇供应链管理有限公司\"},\"declareLogisticsInSystem\":false,\"declareNos\":\"6922104220090177256\",\"declareWay\":\"auto\",\"dxpId\":\"DXPENT0000463629\",\"ebcCompanyDTO\":{\"cebCode\":\"330766K011\",\"cebName\":\"金华佩奇供应链管理有限公司\",\"code\":\"330766K011\",\"name\":\"金华佩奇供应链管理有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"311096102D\",\"cebName\":\"上海格物致品网络科技有限公司\",\"code\":\"311096102D\",\"name\":\"上海格物致品网络科技有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"330766K011\",\"cebName\":\"金华佩奇供应链管理有限公司\",\"code\":\"330766K011\",\"name\":\"金华佩奇供应链管理有限公司\"},\"listCustomsInventoryItemInfo\":[{\"barCode\":\"9300705032308\",\"count\":1,\"country\":\"601\",\"firstCount\":\"0.15000\",\"gmodle\":\"300片/瓶\",\"hsCode\":\"2106909090\",\"id\":913143316512505856,\"itemName\":\"CENOVIS/圣诺无糖维生素C咀嚼片300片/瓶\",\"itemNo\":\"7143047723179082018\",\"itemRecordNo\":\"3767\",\"secondCount\":\"1\",\"unit\":\"142\",\"unit1\":\"035\",\"unitPrice\":39.8700,\"weight\":0.165}],\"logisticDeclareCompany\":{},\"logisticsCompanyDTO\":{\"cebCode\":\"3318960HYU\",\"cebName\":\"义乌市纯曦快递有限公司\",\"code\":\"3318960HYU\",\"name\":\"义乌市纯曦快递有限公司\"},\"mainChannel\":1,\"mainOrderId\":913108310897459200,\"mainOrderSn\":\"OS2309251646702342\",\"payCompanyDTO\":{\"cebCode\":\"330766K0KL\",\"cebName\":\"财付通支付科技有限公司\",\"code\":\"330766K0KL\",\"name\":\"财付通支付科技有限公司\"},\"routeInfo\":{\"code\":\"311096102DJHCZYNNY4PL\",\"declareWay\":\"auto\",\"extraJson\":\"{\\\"assureCompanyId\\\":294,\\\"customsBookId\\\":21,\\\"ebcId\\\":137,\\\"ebpId\\\":294,\\\"listDeclareCompanyId\\\":137,\\\"listDeclareDxpId\\\":\\\"DXPENT0000463629\\\"}\",\"name\":\"金华垂直云-上海格物（清）\",\"routeDeclareConfigList\":[{\"declareCode\":\"ZJ_PORT_INVENTORY_CANCEL_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_CANCEL_DECLARE\",\"dxpId\":\"DXPENT0000463629\",\"type\":\"inventoryCancel\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_REFUND_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_REFUND_DECLARE\",\"dxpId\":\"DXPENT0000463629\",\"type\":\"inventoryRefund\"},{\"declareCode\":\"ZJ_PORT_INVENTORY_DECLARE\",\"declareImpl\":\"ZJ_PORT_INVENTORY_DECLARE\",\"dxpId\":\"DXPENT0000463629\",\"proxyCode\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE\",\"proxyImpl\":\"BYTE_DANCE_CEB_PROXY_INVENTORY_DECLARE\",\"type\":\"inventory\"}],\"routeExtraDto\":{\"assureCompanyId\":294,\"customsBookId\":21,\"ebcId\":137,\"ebpId\":294,\"listDeclareCompanyId\":137}},\"step\":1,\"type\":\"ADD\"}";
        WrapInventoryOrderInfo info = JSON.parseObject(infoS, WrapInventoryOrderInfo.class);
        ByteDanceDeclareReqVo byteDanceDeclareReqVo = new ByteDanceDeclareReqVo();
        byteDanceDeclareReqVo.setInfo(info);
        byteDanceDeclareReqVo.setDeclareOrderSn(info.getDeclareNos());
        byteDanceDeclareReqVo.setClearanceMessageType(ByteDanceDeclareReqVo.ClearanceMessageType.ZD_PERSONAL_GOODS_DECLARE.getKey());
        System.out.println(JSON.toJSONString(byteDanceDeclareReqVo));
    }

    /**
     * 字节云内运单申报
     *
     * @param info 数据
     */
    public void cloudLogisticsDeclare(WrapShipmentInfo info) {
        if (Boolean.FALSE.equals(EnvironmentConfig.isOnline())) {
            return;
        }
        byteDanceDeclareService.cloudAssembleDeclareInfo(getByteDanceDeclareReqVo(info));
    }


    public void cloudOrderDeclare(WrapOrderDeclareInfo info) {
        if (Boolean.FALSE.equals(EnvironmentConfig.isOnline())) {
            return;
        }
        byteDanceDeclareService.cloudAssembleDeclareInfo(getByteDanceDeclareReqVo(info));
    }

    public void cloudCancelDeclare(WarpCancelOrderInfo info) {
        if (Boolean.FALSE.equals(EnvironmentConfig.isOnline())) {
            return;
        }
        byteDanceDeclareService.cloudAssembleDeclareInfo(getByteDanceDeclareReqVo(info));
    }

    public void cloudRefundDeclare(WarpRefundOrderInfo info) {
        if (Boolean.FALSE.equals(EnvironmentConfig.isOnline())) {
            return;
        }
        byteDanceDeclareService.cloudAssembleDeclareInfo(getByteDanceDeclareReqVo(info));
    }

    /**
     * 字节云内申报订单
     *
     * @param info
     */
    @TrackLog(infoIndex = 0, handler = ByteDanceDeclareTrackLogParametersHandler.class)
    public void cloudAssembleDeclareInfo(Object info) {
        String byteDanceUrl = BYTE_DANCE_HOST + CLOUD_DECLARE_URI;
        log.info("cloudAssembleDeclareInfo byteDance proxy, url={}, param={}", byteDanceUrl, JSON.toJSONString(info));
        // 拼多多转发至云内应用申报
        HttpRequest httpRequest = HttpRequestUtil.post(byteDanceUrl, JSON.toJSONString(info));
        String body = httpRequest.body();
        String message = StringUtils.isEmpty(body) ? "" : body.replace("\r\n", "");
        TrackLogBaseInfo logBaseInfo = new TrackLogBaseInfo();
        logBaseInfo.setStatusDesc(TrackLogConstantMixAll.FAIL);
        if (httpRequest.ok()) {
            log.info("cloudAssembleDeclareInfo url={} byteDance success,  res={} , 原始信息：{}，", byteDanceUrl, message, JSON.toJSONString(info));
            logBaseInfo.setStatusDesc(TrackLogConstantMixAll.SUCCESS);
        } else {
            log.info("cloudAssembleDeclareInfo url={} byteDance fail,exception body={} , 原始信息：{}，", byteDanceUrl, message, JSON.toJSONString(info));
        }
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
    }

    public void cloudDeclareMock(WrapBeanInfo info) {
        byteDanceDeclareService.cloudAssembleDeclareInfo(getByteDanceDeclareReqVo(info));
    }

    private ByteDanceDeclareReqVo getByteDanceDeclareReqVo(WrapBeanInfo info) {
        ByteDanceDeclareReqVo reqVo = new ByteDanceDeclareReqVo();
        reqVo.setDeclareOrderSn(info.getDeclareNos());
        reqVo.setInfo(info);
        String clearanceMessageType = null;
        Class<?> infoClz = info.getClass();
        if (Objects.equals(infoClz, WrapOrderDeclareInfo.class)) {
            clearanceMessageType = ByteDanceDeclareReqVo.ClearanceMessageType.CEB311.getKey();
        } else if (Objects.equals(infoClz, WrapShipmentInfo.class)) {
            clearanceMessageType = ByteDanceDeclareReqVo.ClearanceMessageType.CEB511.getKey();
        } else if (Objects.equals(infoClz, WrapInventoryOrderInfo.class)) {
            clearanceMessageType = ByteDanceDeclareReqVo.ClearanceMessageType.CEB621.getKey();
        } else if (Objects.equals(infoClz, WarpCancelOrderInfo.class)) {
            clearanceMessageType = ByteDanceDeclareReqVo.ClearanceMessageType.CEB623.getKey();
        } else if (Objects.equals(infoClz, WarpRefundOrderInfo.class)) {
            clearanceMessageType = ByteDanceDeclareReqVo.ClearanceMessageType.CEB625.getKey();
        }
        reqVo.setClearanceMessageType(clearanceMessageType);
        OrderSensitivePlainText plainText = info.getPlainText();
        if (Objects.nonNull(plainText)) {
            ByteDanceDeclareReqVo.PlainTextReplaceInfo plainTextReplaceInfo = new ByteDanceDeclareReqVo.PlainTextReplaceInfo();
            plainTextReplaceInfo.setConsigneeName(plainText.getConsigneeName());
            plainTextReplaceInfo.setConsigneeAddress(plainText.getConsigneeAddress());
            plainTextReplaceInfo.setConsigneeTel(plainText.getConsigneeTel());
            plainTextReplaceInfo.setBuyerName(plainText.getBuyerName());
            plainTextReplaceInfo.setBuyerTelNumber(plainText.getBuyerTelNumber());
            reqVo.setPlainTextReplaceInfo(plainTextReplaceInfo);
        }
        return reqVo;
    }
}
