package com.danding.cds.service.mq.consumer;

import com.danding.cds.c.api.service.OrderService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-order-declare-scan-delay-c-consumer",
        topic = "ccs-order-declare-scan-delay-c-topic",
        consumeThreadMax = 128
)
@RestController
public class OrderDeclareDelayMQConsumer extends MessageHandler {

    @Resource
    private OrderService orderService;

    @Override
    public void handle(Object o) throws RuntimeException {
        if (ObjectUtils.isEmpty(o)) {
            return;
        }
        String sn = o.toString();
        log.info("OrderDeclareDelayMQConsumer sn = {}", sn);
        try {
            orderService.declare(sn);
        } catch (ArgsErrorException ex) {
            log.error("[op:OrderDeclareDelayMQConsumer] exception, sn={}, cause={}", sn, ex.getErrorMessage(), ex);
        } catch (Exception e) {
            log.error("[op:OrderDeclareDelayMQConsumer] exception, sn={}, cause={}", sn, e.getMessage(), e);
        }
    }

    @GetMapping("/mq/OrderDeclareDelayMQConsumer")
    public String consumer(String orderSn) {
        this.handle(orderSn);
        return "SUCCESS";
    }
}
