package com.danding.cds.service.customs.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.model.inventory.CustomsInventoryDOPack;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.enums.InventoryCalculationTypeEnums;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderItem;
import com.danding.cds.handler.StockInventoryUpdateHandler;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.order.base.bean.dao.*;
import com.danding.cds.order.base.mapper.*;
import com.danding.cds.order.base.util.InventoryBuilder;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.order.base.util.ProductSuffixUtils;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.payinfo.api.dto.PayInfoGoodsInfo;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.convert.*;
import com.danding.cds.service.es.CustomsSingleInventoryEsDao;
import com.danding.cds.service.es.OrderEsDao;
import com.danding.cds.service.mq.producer.OrderExamineMQProducer;
import com.danding.cds.stock.annotations.StockVerify;
import com.danding.cds.utils.CustomsOrderBuilder;
import com.danding.cds.utils.OrderBuilder;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.core.chain.TraceData;
import com.danding.core.chain.mdc.ChainHelper;
import com.danding.logistics.api.common.page.TimeRangeParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.shardingsphere.core.strategy.keygen.SnowflakeShardingKeyGenerator;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 海关申报单事务管理器
 */
@Slf4j
@Service
public class OrderManager {
    protected SnowflakeShardingKeyGenerator snow = new SnowflakeShardingKeyGenerator();

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private CustomsInventoryMapper customsInventoryMapper;

    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;

    @Autowired
    private OrderEsDao orderEsDao;

    @Autowired
    private CustomsOrderMapper customsOrderMapper;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private DockerOrderDOMapper dockerOrderDOMapper;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private CustomsBookService customsBookService;

    @Resource
    private CustomsOrderService customsOrderService;

    @Autowired
    private OrderExamineMQProducer orderExamineMQProducer;

    @Autowired
    private CustomsPaymentConvert customsPaymentConvert;

    @Autowired
    private CustomsLogisticsConvert customsLogisticsConvert;

    @Autowired
    private CustomsOrderConvert customsOrderConvert;

    @Autowired
    private ExtraPayInfoConvert extraPayInfoConvert;

    @Autowired
    private CustomsInventoryConvert customsInventoryConvert;

    @Autowired
    private OrderConvert orderConvert;

    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @Resource
    private OrderService orderService;

    @Autowired
    private CustomsSingleInventoryEsDao customsSingleInventoryEsDao;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    @Transactional
    //这里要做一个库存释放动作 直接使用 减占用 加可用的动作
    @StockVerify(methodParameters = {0}, changeType = InventoryCalculationTypeEnums.REDUCE_OCCUPATION, handler = StockInventoryUpdateHandler.class)
    public Long orderCreate(RichOrder richOrder) {
        // Step::主订单创建
        OrderDO orderDO = orderConvert.save(richOrder);
        // Step::179支付信息创建
        extraPayInfoConvert.save(richOrder.getExtraPayInfoDO());
        // Step::海关订单创建
        customsOrderConvert.save(orderDO, richOrder);
        // Step::海关运单创建
        customsLogisticsConvert.save(orderDO, richOrder);
        // Step::海关支付单创建
        customsPaymentConvert.save(orderDO, richOrder);
        // Step::海关清单创建
        customsInventoryConvert.save(orderDO, richOrder);
        // Step::搜索引擎更新
        orderEsDao.esSave(OrderBuilder.buildDTO(orderDO),
                InventoryBuilder.buildDTO(richOrder.getCustomsInventoryDOPack() == null ? null : richOrder.getCustomsInventoryDOPack().getCustomsInventoryDO()),
                CustomsOrderBuilder.buildDTO(richOrder.getCustomsOrderDO()),
                richOrder.getRoute().getCustomsBookId());
        //同步新增es索引
        try {
            CustomsInventoryDOPack customsInventoryDOPack = richOrder.getCustomsInventoryDOPack();
            log.info("customsInventoryDOPack={}", JSON.toJSONString(customsInventoryDOPack));
            if (Objects.nonNull(customsInventoryDOPack)) {
                CustomsInventoryDO customsInventoryDO = customsInventoryDOPack.getCustomsInventoryDO();
                List<CustomsInventoryItemDO> itemDOList = customsInventoryDOPack.getItemDOList();
                CustomsInventoryDTO customsInventoryDTO = ConvertUtil.beanConvert(customsInventoryDO, CustomsInventoryDTO.class);
                List<CustomsInventoryItemDTO> itemDTOList = ConvertUtil.listConvert(itemDOList, CustomsInventoryItemDTO.class);
                customsSingleInventoryEsDao.esSave(customsInventoryDTO, itemDTOList);
            }
        } catch (Exception e) {
            log.error("orderCreate esSave error={}", e.getMessage(), e);
        }
        // Step::触发下一个事件
        TraceData traceData = ChainHelper.get();
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            // 在事务提交之后执行的代码块（方法）  此处使用TransactionSynchronizationAdapter，其实在Spring5后直接使用接口也很方便了~
            @Override
            public void afterCommit() {
                executorService.submit(() -> {
                    try {
                        ChainHelper.cover(traceData);
                        Thread.sleep(1000L);
                        orderExamineMQProducer.send(richOrder.getOrder().getSn());
                    } catch (InterruptedException e) {
                        log.error("afterCommit sleep error={}", e.getMessage(), e);
                    } finally {
                        ChainHelper.unbinding();
                    }
                });
            }
        });
        return orderDO.getId();
    }

    @Autowired
    private ProductSuffixUtils productSuffixUtils;

    @Transactional
    public Boolean fillItemInfo(OrderDTO orderDTO) {
        OrderConvertWorkSpace workSpace = new OrderConvertWorkSpace();
        Boolean notItem = false;
        String configError = "";
        List<CustomsOrderItem> itemList = new ArrayList<>();
        BigDecimal netWeight = BigDecimal.ZERO;
        BigDecimal grossWeight = BigDecimal.ZERO;
        List<CustomsInventoryItemDO> itemDOList = new ArrayList<>();
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        // Step::商品数据检查并补充
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        if (customsInventoryDTO != null || JSON.parseArray(orderDTO.getActionJson(), String.class).contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            CustomsBookDTO customsBookDTO = workSpace.getCustomsBookDTO(customsBookService, customsInventoryDTO.getAccountBookId());
            CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
            if (inventoryExtra.getNotItem()) {
                for (OrderSubmitItem submitItem : orderExtra.getSubmit().getItemList()) {
                    CustomsInventoryItemDO itemDO = new CustomsInventoryItemDO();
                    BeanUtils.copyProperties(submitItem, itemDO);
                    CustomsInventoryItemExtra extra = new CustomsInventoryItemExtra();
                    String productSn = submitItem.getRecordNo();

//                    productSn = productSuffixUtils.productSuffixSplit(productSn, customsBookDTO.getBookNo(), orderExtra.getSubmit().getTenantOuterId());
                    // Step::账册库存
                    List<String> declareOrderTypeList = orderExtra.getSubmit().getDeclareOrderTypes();
                    GoodsRecordMappingWayEnums goodsRecordMappingWay = orderService.getGoodsRecordMappingWayEnum(declareOrderTypeList);
                    log.info("申报单号={},申报单类型={},采用【{}】备案映射方式", orderExtra.getSubmit().getDeclareOrderNo(), orderExtra.getSubmit().getDeclareOrderTypes(), goodsRecordMappingWay.getDesc());
                    GoodsRecordDTO recordDTO = workSpace.getGoodsRecordDTO(goodsRecordService, customsBookDTO.getId(), submitItem.getRecordNo(), goodsRecordMappingWay.getValue());
                    String productId = productSn;
                    if (recordDTO != null) {
                        productId = goodsRecordAssociateService.getFinalProductId(recordDTO, customsBookDTO.getId());
                    }
                    CustomsBookItemDTO customsBookItemDTO = workSpace.getCustomsBookItemDTO(customsBookItemService, customsBookDTO.getId(), productId, submitItem.getCount(), submitItem.getRecordGnum());
                    if (customsBookItemDTO == null || recordDTO == null || recordDTO.getEnable() != 1) {
                        configError = "商品信息不存在/商品档案被禁用;";
                        notItem = true;
                        log.info("[op:OrderManager] order item null, bootItem={}, record={}", JSON.toJSONString(customsBookItemDTO), JSON.toJSONString(recordDTO));
                        break;
                    } else {
                        itemDO.setBookItemId(customsBookItemDTO.getId());
                        BeanUtils.copyProperties(customsBookItemDTO, extra);
                        BeanUtils.copyProperties(recordDTO, extra);
                        extra.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                        extra.setProductId(customsBookItemDTO.getProductId());
                        extra.setHsCode(customsBookItemDTO.getHsCode());
                        extra.setGoodsName(customsBookItemDTO.getGoodsName());
                        extra.setCurrCode(customsBookItemDTO.getCurrCode());
                        extra.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                        extra.setGoodsModel(customsBookItemDTO.getGoodsModel());
                        extra.setOriginCountry(customsBookItemDTO.getOriginCountry());
                        extra.setGoodsUnit(customsBookItemDTO.getGoodsUnit());
                        extra.setFirstUnit(customsBookItemDTO.getFirstUnit());
                        extra.setSecondUnit(customsBookItemDTO.getSecondUnit());
                        //产品设计如此 条码取料号
                        extra.setBarCode(productId);
                        extra.setNetWeight(recordDTO.getNetWeight());
                        extra.setGrossWeight(recordDTO.getGrossWeight());
                        extra.setBrand(recordDTO.getBrand());
                        extra.setBrandEn(recordDTO.getBrandEn());
                        extra.setLesseeNo(recordDTO.getLesseeNo());
                        extra.setVatRate(recordDTO.getVatRate());
                        extra.setTaxRate(recordDTO.getTaxRate());
                        extra.setComposition(recordDTO.getComposition());
                        extra.setHgsbys(recordDTO.getHgsbys());
                        extra.setRecordFunction(recordDTO.getRecordFunction());
                        extra.setRecordUsage(recordDTO.getRecordUsage());
                        extra.setFirstUnitAmount(customsBookItemDTO.getFirstUnitAmount());
                        extra.setSecondUnitAmount(customsBookItemDTO.getSecondUnitAmount());
                        extra.setTaxPrice(BigDecimal.ZERO); // TODO:预估税费，这里先计0
                        itemDO.setExtraJson(JSON.toJSONString(extra));
                        itemDO.setItemName(customsBookItemDTO.getGoodsName());
                        itemDOList.add(itemDO);
                        netWeight = netWeight.add(recordDTO.getNetWeight().multiply(new BigDecimal(submitItem.getCount())));
                        grossWeight = grossWeight.add(recordDTO.getGrossWeight().multiply(new BigDecimal(submitItem.getCount())));
                    }
                }
                if (!notItem) {
                    CustomsInventoryDO customsInventoryDO = new CustomsInventoryDO();
                    customsInventoryDO.setId(customsInventoryDTO.getId());
                    customsInventoryDO.setGrossWeight(grossWeight);
                    customsInventoryDO.setNetWeight(netWeight);
                    inventoryExtra.setNotItem(notItem);
                    customsInventoryDO.setExtraJson(JSON.toJSONString(inventoryExtra));
                    this.updateInventoryById(customsInventoryDTO.getId(), customsInventoryDO, customsInventoryDTO.getCreateTime());
                    for (CustomsInventoryItemDO itemDO : itemDOList) {
                        itemDO.setCustomsInventoryId(customsInventoryDTO.getId());
                        itemDO.setCreateTime(customsInventoryDTO.getCreateTime());
                        itemDO.setId(sequenceService.generateId());
                        UserUtils.setCreateAndUpdateBy(itemDO);
                        customsInventoryItemMapper.insertSelective(itemDO);
                    }
                }
            }
        }
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        if ((customsOrderDTO != null || JSON.parseArray(orderDTO.getActionJson(), String.class).contains(RouteActionEnum.DECLARE_ORDER.getCode())) && !notItem) {
            CustomsOrderExtra customsOrderExtra = JSON.parseObject(customsOrderDTO.getExtraJson(), CustomsOrderExtra.class);
            CustomsBookDTO customsBookDTO = workSpace.getCustomsBookDTO(customsBookService, customsOrderExtra.getBookId());
            if (customsOrderExtra.getNotItem()) {
                for (OrderSubmitItem submitItem : orderExtra.getSubmit().getItemList()) {
                    CustomsOrderItem orderItem = new CustomsOrderItem();
                    // Step::账册库存
                    String productSn = submitItem.getRecordNo();
//                    productSn = productSuffixUtils.productSuffixSplit(productSn, customsBookDTO.getBookNo(), orderExtra.getSubmit().getTenantOuterId());
                    List<String> declareOrderTypeList = orderExtra.getSubmit().getDeclareOrderTypes();
                    GoodsRecordMappingWayEnums goodRecordMappingWay = orderService.getGoodsRecordMappingWayEnum(declareOrderTypeList);
                    log.info("申报单号={},申报单类型={},采用【{}】备案映射方式", orderExtra.getSubmit().getDeclareOrderNo(), orderExtra.getSubmit().getDeclareOrderTypes(), goodRecordMappingWay.getDesc());
                    GoodsRecordDTO recordDTO = workSpace.getGoodsRecordDTO(goodsRecordService, customsBookDTO.getId(), submitItem.getRecordNo(), goodRecordMappingWay.getValue());
                    String productId = goodsRecordAssociateService.getFinalProductId(recordDTO, customsBookDTO.getId());
                    CustomsBookItemDTO customsBookItemDTO = workSpace.getCustomsBookItemDTO(customsBookItemService, customsBookDTO.getId(), productId, submitItem.getCount(), submitItem.getRecordGnum());
                    if (customsBookItemDTO == null || recordDTO == null || recordDTO.getEnable() != 1) {
                        notItem = true;
                        configError = "商品信息不存在/商品档案被禁用;";
                        log.info("[op:OrderManager] order item null, bootItem={}, record={}", JSON.toJSONString(customsBookItemDTO), JSON.toJSONString(recordDTO));
                        break;
                    } else {
                        orderItem.setGoodsModel(customsBookItemDTO.getGoodsModel());
                        orderItem.setHsCode(customsBookItemDTO.getHsCode());
                        orderItem.setGoodsUnit(customsBookItemDTO.getGoodsUnit());
                        orderItem.setOriginCountry(customsBookItemDTO.getOriginCountry());
                        orderItem.setBookId(customsBookDTO.getId());
                        orderItem.setRecordNo(customsBookItemDTO.getProductId());
                        orderItem.setGoodsNo(submitItem.getItemNo());
                        orderItem.setGoodsName(customsBookItemDTO.getGoodsName());
                        orderItem.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                        orderItem.setUnitPrice(submitItem.getUnitPrice());
                        orderItem.setGoodsCount(submitItem.getCount());
                        itemList.add(orderItem);
                    }
                }
                if (!notItem) {
                    CustomsOrderDO customsOrderDO = new CustomsOrderDO();
                    customsOrderDO.setId(customsOrderDTO.getId());
                    customsOrderDO.setItemJson(JSON.toJSONString(itemList));
                    customsOrderDO.setExtraJson(JSON.toJSONString(customsOrderExtra));
                    this.updateOrderById(customsOrderDTO.getId(), customsOrderDO, customsOrderDTO.getCreateTime());
                }
            }
        }
        if (!StringUtils.isEmpty(configError)) {
            CustomsStatusMappingDTO mappingDTO = customsStatusMappingService.findByCode("CONFIG_ERROR");
            OrderDO orderDO = new OrderDO();
            orderDO.setId(orderDTO.getId());
            orderDO.setExceptionFlag(true);
            orderDO.setExceptionType(mappingDTO.getId().intValue());
            orderDO.setExceptionDetail(configError);
            this.updateByIdSection(orderDTO.getId(), orderDO, orderDTO.getCreateTime());
            return false;
        }
        return true;
    }

    private void updateInventoryById(Long id, CustomsInventoryDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        customsInventoryMapper.updateByExampleSelective(template, example);
    }

    private void updateOrderById(Long id, CustomsOrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        customsOrderMapper.updateByExampleSelective(template, example);
    }

    private void updateByIdSection(Long id, OrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", id));
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(template, example);
    }

    public int saveDockerOrder(DockerOrderDO dockerOrder) {
        if (dockerOrder.getId() == null || dockerOrder.getId().equals(0L)) {
            UserUtils.setCreateAndUpdateBy(dockerOrder);
            dockerOrder.setUpdateTime(new Date());
            dockerOrder.setCreateTime(new Date());
            return dockerOrderDOMapper.insertSelective(dockerOrder);
        } else {
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(dockerOrder);
            }
            dockerOrder.setUpdateTime(new Date());
            return dockerOrderDOMapper.updateByPrimaryKeySelective(dockerOrder);
        }
    }


    public DockerOrderDTO getDockerOrderByGlobalSn(String GlobalSn) {
        DockerOrderDO example = new DockerOrderDO();
        example.setSystemGlobalSn(GlobalSn);
        DockerOrderDO dockerOrderDO = dockerOrderDOMapper.selectOne(example);
        if (dockerOrderDO == null) {
            return null;
        }
        DockerOrderDTO dto = new DockerOrderDTO();
        BeanUtils.copyProperties(dockerOrderDO, dto);
        dto.setTradeTime(dockerOrderDO.getTradeTime().getTime());
        dto.setOrigGoodsInfoList(JSON.parseArray(dockerOrderDO.getOrigGoodsInfoJson(), PayInfoGoodsInfo.class));
        return dto;
    }
}
