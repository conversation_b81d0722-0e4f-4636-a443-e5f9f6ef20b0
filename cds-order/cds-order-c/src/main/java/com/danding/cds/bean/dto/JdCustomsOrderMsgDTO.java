package com.danding.cds.bean.dto;

import lombok.Data;

@Data
public class JdCustomsOrderMsgDTO {

    /**
     * 订单号（平台订单号）
     * 必填
     */
    private String orderId;

    /**
     * 平台编码；1：京东主站订单
     * 必填
     */
    private String platformId;

    /**
     * 类型：
     * inventory：服务商捞单报文（只支持京东主站订单）
     * overseasWaybill：下发海外本订单（只支持独立站三方订单）
     * cancellation：撤单（只支持京东主站备货订单）
     * identification：发送订单鉴定结果（仅支持京东直邮纯配订单）
     * returnOrderDeclaration:客退单申报
     * 注意：如果type非此值或不需要的请丢弃消息
     * 必填
     */
    private String type;

    /**
     * 描述
     * 必填
     */
    private String msg;

    /**
     * 消息数据：
     * 根据type不同，data内容可能不同。
     * 当type是inventory（捞单时）时，各口岸报文不一样详见下文
     * 非必填
     */
    private String data;

    /**
     * 服务商编号
     * 示例：010010
     * 非必填
     */
    private String merchantId;

    /**
     * 保税区编号
     * 非必填
     */
    private String customsId;

    /**
     * 商家编号
     * 非必填
     */
    private Long venderId;

    /**
     * 订单扩展类型
     * 如果是云交易订单：则orderExtType=‘yunjiaoyi’，其它可能为空
     * 非必填
     */
    private String orderExtType;

    /**
     * 订单操作时间戳
     * 非必填
     */
    private Long operatingTimestamp;

    /**
     * 是否需要正品坚鉴定：
     * 1：需要正品坚鉴定
     * 非必填
     */
    private String idenType;
}
