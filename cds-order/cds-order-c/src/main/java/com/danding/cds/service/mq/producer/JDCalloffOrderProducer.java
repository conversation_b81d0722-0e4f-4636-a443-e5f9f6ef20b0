package com.danding.cds.service.mq.producer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffSubmit;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @description 京东取消单
 * @date 2022/1/7
 */
@Service
@Slf4j
public class JDCalloffOrderProducer {


    @Autowired
    private MessageSender messageSender;


    public void jdCalloffOrderSend(CustomsInventoryCalloffSubmit submit){
        log.warn("发送京东取消单MQ");
        //发送mq消息
        messageSender.sendMsg(JSON.toJSONString(submit),"ccs-jd-calloff-c-orderTopic");
    }

}
