package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.service.CustomsInventoryCalloffService;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.config.AutoOrderConfig;
import com.danding.cds.customs.inventory.api.dto.CustomsCancelJdOrderVO;
import com.danding.cds.customs.inventory.api.dto.CustomsCancelOrderDetailsDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.bean.dao.es.CustomsSingleInventoryEsDO;
import com.danding.cds.utils.JdGoodsRecordHttpUtil;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchOperations;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Slf4j
@Service
public class JdOrderConfirmJob extends IJobHandler {

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Resource
    private OrderService orderService;

    @Autowired
    protected ElasticsearchOperations elasticsearchTemplate;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @Override
    @XxlJob(value = "JdOrderCConfirmJob", enableTenant = false)
    public ReturnT<String> execute(String param) throws Exception {
        String dataFormat = "yyyy/MM/dd HH:mm:ss";
        JSONObject config = JSON.parseObject(param);
        Long createFrom = new DateTime().minusDays(7).getMillis();
        Long createTo = new DateTime().minusDays(4).getMillis();
        String from = config.getString("from");
        if (!StringUtils.isEmpty(from)) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(dataFormat);
            createFrom = dateTimeFormatter.parseDateTime(from).toDate().getTime();
        }
        String to = config.getString("to");
        if (!StringUtils.isEmpty(to)) {
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(dataFormat);
            createTo = dateTimeFormatter.parseDateTime(to).toDate().getTime();
        }
        Long bookId = config.getLong("bookId");
        bookId = bookId == null ? 10L : bookId;
        findJdOrder(createFrom, createTo, null, bookId);
        return ReturnT.SUCCESS;
    }

    public CustomsCancelJdOrderVO findJdOrder(Long createFrom, Long createTo, String declareOrderNo, Long bookId) {

        log.info("JdOrderConfirmJob 京东取消单业务，操作条件：createFrom：{},createTo：{},declareOrderNo：{},bookId：{}",
                createFrom, createTo, declareOrderNo, bookId);

        List<CustomsCancelOrderDetailsDTO> detailsDTOList = new ArrayList<>();
        CustomsCancelJdOrderVO jdOrderVO = new CustomsCancelJdOrderVO();
        Integer count = 0;

        // 这里改下从es滚动获取
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", CustomsActionStatus.DEC_SUCCESS.getValue()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 0));
        if (bookId != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountBookId", bookId));
        }
        if (Objects.nonNull(declareOrderNo)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("declareOrderNo", declareOrderNo));
        } else {
            // 时间
            if (createFrom == null) {
                // 照抄原来逻辑
                Calendar calendar = Calendar.getInstance();
                calendar.set(2020, Calendar.APRIL, 1);
                Date begin = calendar.getTime();
                createFrom = begin.getTime();
            }
            if (createTo == null) {
                Date now = new Date();
                createTo = now.getTime();
            }
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createFrom).to(createTo));
        }

        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withPageable(PageRequest.of(0, 1000))
                .build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        log.info("[op:JdOrderConfirmJob] 京东取消单 查询清单ES查询语句：{}", searchQuery.getQuery().toString());
        JdServProviderDTO provider = jdServProviderService.getServProviderByBookId(bookId);
        if (Objects.isNull(provider)) {
            log.error("根据账册未查询到服务商");
        }
        long scrollTimeInMillis = 120 * 1000;
        ScrolledPage<CustomsSingleInventoryEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, CustomsSingleInventoryEsDO.class);
        while (scrolledPage.hasContent()) {
            List<CustomsSingleInventoryEsDO> content = scrolledPage.getContent();
            // 由于数据类型不一致，所有json转一下
            List<CustomsInventoryDTO> customsInventoryDTOList = JSONObject.parseArray(JSON.toJSONString(content), CustomsInventoryDTO.class);
            log.info("[op:JdOrderConfirmJob] 京东订单取消确认发起完成, es滚动查询, 待确认订单数={} ", customsInventoryDTOList.size());
            Integer cancelSize = this.handlerJdOrder(provider, customsInventoryDTOList, detailsDTOList);
            count += cancelSize;
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, CustomsSingleInventoryEsDO.class);
        }
        jdOrderVO.setCount(count);
        jdOrderVO.setDetailsDTOS(detailsDTOList);
        return jdOrderVO;
    }

    public Integer handlerJdOrder(JdServProviderDTO provider, List<CustomsInventoryDTO> customsInventoryDTOList, List<CustomsCancelOrderDetailsDTO> detailsDTOList) {

        Integer count = 0;
        for (CustomsInventoryDTO customsInventoryDTO : customsInventoryDTOList) {
            CustomsCancelOrderDetailsDTO cancelOrderDetailsDTO = new CustomsCancelOrderDetailsDTO();
            cancelOrderDetailsDTO.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
            if (EnvironmentConfig.isOnline()) {
                log.info("[op:JdOrderConfirmJob] 京东取消单确认 declareOrderNo={} ", customsInventoryDTO.getDeclareOrderNo());
                Map<String, String> objectMap = JdGoodsRecordHttpUtil.ofBaseBody("jingdong.pop.customs.center.OrderYnJsfService.getOrderYnJsfResult", provider);
                objectMap.put("orderId", customsInventoryDTO.getDeclareOrderNo());
                objectMap.put("sign", jdSign(objectMap));
                HttpRequest httpRequest = HttpRequest.post("https://api.jd.com/routerjson")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .readTimeout(15000).connectTimeout(15000)
                        .acceptGzipEncoding().uncompress(true).form(objectMap);
                JSONObject response = (JSONObject) JSONObject.parseObject(httpRequest.body()).get("jingdong_pop_customs_center_OrderYnJsfService_getOrderYnJsfResult_responce");
                log.info("[op:JdOrderConfirmJob] declareOrderNo={} response={}", customsInventoryDTO.getDeclareOrderNo(), JSON.toJSONString(response));
                if (Objects.isNull(response)) {
                    log.info("[op:JdOrderConfirmJob] declareOrderNo={} response为空 跳过", customsInventoryDTO.getDeclareOrderNo());
                    continue;
                }
                JSONObject first = (JSONObject) response.get("orderYnJsfResult");
                String yn = first.getString("yn");
                String orderId = first.getString("orderId");
                if (!"1".equals(yn)) {
                    log.info("[op:JdOrderConfirmJob] 京东订单取消确认, 申报单号={},回执={} ", orderId, JSON.toJSONString(response));
                    count = createCalloffOrder(count, customsInventoryDTO, cancelOrderDetailsDTO, yn, orderId);
                } else {
                    cancelOrderDetailsDTO.setStatusStr("订单正常");
                }
            } else {
                log.info("[op:JdOrderConfirmJob] 测试环境 declareOrderNo={} ", customsInventoryDTO.getDeclareOrderNo());
                count = createCalloffOrder(count, customsInventoryDTO, cancelOrderDetailsDTO, null, customsInventoryDTO.getDeclareOrderNo());
            }
            detailsDTOList.add(cancelOrderDetailsDTO);
        }
        return count;
    }

    public Integer createCalloffOrder(Integer count, CustomsInventoryDTO customsInventoryDTO, CustomsCancelOrderDetailsDTO cancelOrderDetailsDTO, String yn, String orderId) {
        log.info("[op:JdOrderConfirmJob] 京东取消单处理, 申报单号={},申报单id={},创建时间={} ", orderId, customsInventoryDTO.getOrderId(), customsInventoryDTO.getCreateTime());
        OrderDTO orderDTO = orderService.findByIdSection(customsInventoryDTO.getOrderId(), customsInventoryDTO.getCreateTime());
        List<CustomsInventoryCalloffDTO> calloffDtoList = customsInventoryCalloffService.findListByOrderId(orderDTO.getId());
        if (CollectionUtils.isEmpty(calloffDtoList)) {
            log.info("[op:JdOrderConfirmJob] 京东取消单处理, 申报单号={},没有生成过取消单，开始走生成取消单逻辑", orderId);
            String calloffTypeCode = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
            if (Objects.equals(customsInventoryDTO.getExitRegionStatus(), 1)) {
                calloffTypeCode = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
                log.info("[op:JdOrderConfirmJob] 京东取消单处理 申报单号={} 订单已出区 开始走退货取消单逻辑", orderId);
            }
            customsInventoryCalloffService.upset(orderDTO.getId(), orderDTO.getCustomsInventorySn(), calloffTypeCode, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), "订单" + orderId + ":yn=" + yn + "," + "订单取消");
            orderService.cancelOrderPushOMS(orderDTO.getId(), "订单" + orderId + ":yn=" + yn + "," + "订单取消");
            log.info("[op:JdOrderConfirmJob] 京东取消单处理, 申报单号={},生成取消单并推送oms成功", orderId);
            count += 1;
        }
        cancelOrderDetailsDTO.setStatusStr("订单已取消");
        return count;
    }

    public String jdSign(Map<String, String> params) {
        try {
            Set<String> keySet = params.keySet();
            String[] keyArray = keySet.toArray(new String[keySet.size()]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            sb.append("11e19999b61e42ee80beff29d23171ac");
            for (String k : keyArray) {
                if (k.equals("sign")) {
                    continue;
                }
                if (params.get(k).toString().trim().length() > 0) {
                    sb.append(k).append(params.get(k).toString().trim());
                }
            }
            String sign = Hashing.sha1().newHasher().putString(sb.toString(), Charsets.UTF_8).hash().toString().toUpperCase();
            return sign;
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return "";
        }
    }
}
