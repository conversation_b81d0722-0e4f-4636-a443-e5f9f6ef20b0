package com.danding.cds.service.mq.producer;

import cn.hutool.json.JSONUtil;
import com.danding.cds.bean.dto.TransferDto;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.logistics.mq.common.handler.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 字节申报生产
 **/
@Service
public class ByteDanceDeclareProducer {

    @Autowired
    private MessageSender messageSender;

    /**
     * 字节云内申报消息topic
     */
    private final String DECLARE_TOPIC = "ccs-byteDance-order-declare-c-topic";

    /**
     * 发送清单信息
     *
     * @param info
     */
    public void inventoryOrderSend(WrapInventoryOrderInfo info) {
        //封装对象
        TransferDto dto = new TransferDto();
        dto.setType("inventory");
        dto.setData(JSONUtil.toJsonStr(info));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), DECLARE_TOPIC);

    }


    /**
     * 发送订单信息
     *
     * @param wrapOrderDto
     */
    public void orderSend(WrapOrderDeclareInfo wrapOrderDto) {
        TransferDto dto = new TransferDto();
        dto.setType("order");
        dto.setData(JSONUtil.toJsonStr(wrapOrderDto));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), DECLARE_TOPIC);
    }

    /**
     * 发送运单信息
     *
     * @param wrapShipmentInfo
     */
    public void logisticsOrderSend(WrapShipmentInfo wrapShipmentInfo) {
        TransferDto dto = new TransferDto();
        dto.setType("logistics");
        dto.setData(JSONUtil.toJsonStr(wrapShipmentInfo));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), DECLARE_TOPIC);
    }
}
