package com.danding.cds.service.customs.order;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.rpc.client.ares.order.message.ItemSkuRpcMessage;
import com.danding.business.rpc.client.ares.order.message.OrderRpcMessage;
import com.danding.business.rpc.client.oms.order.facade.ICancelRpcFacade;
import com.danding.cds.bean.dto.*;
import com.danding.cds.bean.model.inventory.CustomsInventoryDOPack;
import com.danding.cds.bean.model.order.DecryptSubmit;
import com.danding.cds.bean.model.order.EncryptSubmit;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.service.*;
import com.danding.cds.callback.api.dto.DeliverActiveInfo;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.callback.api.dto.OrderEventActive;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.*;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.config.OrderCBaseConfig;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderExtra;
import com.danding.cds.declare.sdk.CustomsReport;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.dto.ExceptionTypeDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.handler.CancelTrackLogParametersHandler;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.handler.StockInventoryCallOffHandler;
import com.danding.cds.handler.StockInventoryDeleteHandler;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.message.api.dto.MessageSubscribeDTO;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.process.OrderCustomsInventoryMessage;
import com.danding.cds.message.api.process.OrderDeliverMessage;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.monitor.MappingEmptyEvent;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.order.api.enums.OrderChannel;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.*;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.bean.dto.MerchantReplaceConfig;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.*;
import com.danding.cds.order.base.mq.OrderEsDumpProducer;
import com.danding.cds.order.base.service.ExternalDeclareOrderBaseService;
import com.danding.cds.order.base.service.OrderBaseService;
import com.danding.cds.order.base.service.TrackLogEsProducer;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.order.base.util.RouteActionUtil;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.ExtraPayInfoSubmit;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.InventoryCheckService;
import com.danding.cds.service.convert.*;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;
import com.danding.cds.service.customs.declare.CustomsDeclareOrderCommand;
import com.danding.cds.service.es.CustomsSingleInventoryEsDao;
import com.danding.cds.service.es.OrderEsDao;
import com.danding.cds.service.es.OrderEsHandlerService;
import com.danding.cds.service.mq.CancelOrderNotifyService;
import com.danding.cds.service.mq.producer.OrderCallbackMQProducer;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.service.mq.producer.OrderExamineMQProducer;
import com.danding.cds.stock.annotations.StockVerify;
import com.danding.cds.taxes.api.dto.TaxesTenantTaxListDTO;
import com.danding.cds.taxes.api.service.TaxesTenantTaxListService;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.DeclareOrderEsQuerier;
import com.danding.cds.utils.OrderBuilder;
import com.danding.cds.utils.flow.OrderAction;
import com.danding.cds.utils.flow.OrderFlowBook;
import com.danding.cds.utils.flow.OrderFlowPicker;
import com.danding.cds.v2.bean.dto.OrderTotalTaxEsDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.bean.vo.req.CustomsLinkCancelReqVo;
import com.danding.cds.v2.bean.vo.req.JdItemNameSetCheckNotifyVO;
import com.danding.cds.v2.bean.vo.req.RefundWarehouseCancelReq;
import com.danding.cds.v2.bean.vo.res.JdFopDetailVO;
import com.danding.cds.v2.bean.vo.res.JdFopGoodsInfoVO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.DeclareOrderTypeEnums;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.v2.enums.InventoryCheckOutStatusEnum;
import com.danding.cds.warehouse.api.WarehouseService;
import com.danding.common.utils.CopyUtil;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.chain.TraceData;
import com.danding.core.chain.mdc.ChainHelper;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.encrypt.annotation.DataSecurity;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.businessException.BaseBusinessException;
import com.danding.logistics.business.common.dtos.order.DealOrderStatusDTO;
import com.danding.logistics.business.common.enums.DealOrderStatus;
import com.danding.logistics.cache.common.annotation.Lock;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.eventbus.EventBus;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.session.RowBounds;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/8 15:33
 * @Description: 申报单服务实现
 */
@Slf4j
@RestController
@Service
//@RefreshScope
public class OrderServiceImpl implements OrderService {

    /**
     * 存在已申报清单[电商企业编码：330766K00W,订单编号：XP2021102612500158691385007711,清单编号： ,清单状态：3-发送海关成功],短时间内不能重复新增申报操作;;
     */
    private static final Pattern INVENTORY_STATUS = Pattern.compile("存在已申报清单\\[.*清单状态：.*\\].*");

    @Autowired
    private OrderServiceImpl orderService;

    @Autowired
    private OrderEsHandlerService orderEsHandlerService;

    @Autowired
    private OrderMapper orderMapper;

    @Autowired
    private CustomsInventoryMapper customsInventoryMapper;

    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;

    @Autowired
    private CustomsOrderMapper customsOrderMapper;

    @Autowired
    private CustomsLogisticsMapper customsLogisticsMapper;

    @DubboReference
    private CompanyService companyService;

    @Autowired
    private OrderEsDao orderEsDao;

    @Autowired
    private CustomsSingleInventoryEsDao customsSingleInventoryEsDao;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsOrderService customsOrderService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @DubboReference
    private MessageService messageService;

    @Resource
    private CustomsPaymentService customsPaymentService;

    @DubboReference
    private WarehouseService warehouseService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Autowired
    private OrderManager orderManager;

    @Autowired
    private CustomsDeclareOrderCommand customsDeclareOrderCommand;

    @Autowired
    private CustomsStatusMappingMapper customsStatusMappingMapper;

    @Autowired
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @Autowired
    private OrderExamineMQProducer orderExamineMQProducer;

    @Autowired
    private OrderCallbackMQProducer orderCallbackMQProducer;

    @Autowired
    private OrderEsDumpProducer orderEsDumpProducer;

    @Autowired
    private EventBus eventBus;

//    @Autowired
//    private LogComponent logComponent;

    @Autowired
    private CustomsSupport customsSupport;

    @Autowired
    private CustomsPaymentConvert customsPaymentConvert;

    @Autowired
    private CustomsLogisticsConvert customsLogisticsConvert;

    @Autowired
    private CustomsOrderConvert customsOrderConvert;

    @Autowired
    private ExtraPayInfoConvert extraPayInfoConvert;

    @Autowired
    private CustomsInventoryConvert customsInventoryConvert;

    @Autowired
    private OrderConvert orderConvert;

    @Autowired
    private BaseDataService baseDataService;

    @DubboReference
    private SequenceService sequenceService;

    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Resource
    private StockInventoryService stockInventoryService;

    @Autowired
    private CancelOrderNotifyService cancelOrderNotifyService;

    @Autowired
    private MessageSender messageSender;
    @Autowired
    private InventoryCheckService inventoryCheckService;

    @Autowired
    private OrderBaseService orderBaseService;

    @DubboReference
    private ICancelRpcFacade iCancelRpcFacade;

    @Autowired
    private TrackLogEsProducer trackLogEsProducer;

    @DubboReference
    private InventoryChangeService inventoryChangeService;
    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @DubboReference
    private CustomsDictionaryService dictionaryService;

    @DubboReference
    private TaxesTenantTaxListService taxesTenantTaxListService;

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Autowired
    private ExternalDeclareOrderBaseService externalDeclareOrderBaseService;

    @Autowired
    private OrderCBaseConfig orderCBaseConfig;

    @Override
    public void acceptCustomsSupport(String data) {
        customsSupport.accept(JSON.parseObject(data, CustomsReport.class));
    }

    @Override
    public OrderDTO findByIdFull(Long id) {
        if (id == null) {
            return null;
        }
        // 先当前季度查，查不到再往前追溯
        OrderDTO orderDTO = findByIdSection(id, new Date());
        if (orderDTO != null) {
            return orderDTO;
        }
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("id", id));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public OrderDTO findByDeclareOrderNoFull(String declareOrderNo) {
        if (declareOrderNo == null) {
            return null;
        }
        // 先当前季度查，查不到再往前追溯
        OrderDTO orderDTO = findByDeclareOrderNoSection(declareOrderNo, new Date());
        if (orderDTO != null) {
            return orderDTO;
        }
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    private OrderDTO findByDeclareOrderNoSection(String declareOrderNo, Date sectionDate) {
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }


    @Override
    public OrderDTO findByGlobalSnFull(String systemGlobalSn) {
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("systemGlobalSn", systemGlobalSn));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public OrderDTO findByOutOrderSn(String outOrderSn) {
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.createCriteria().andEqualTo("outOrderNo", outOrderSn).andEqualTo("deleted", false);
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public OrderDTO findByIdSection(Long id, Date sectionDate) {
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", id));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public List<OrderDTO> findByIdSection(List<Long> idList, Date sectionDate) {
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("id", idList));
        // Step::返回值处理
        List<OrderDO> result = orderMapper.selectByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public OrderDTO findBySnSection(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        //timeRangeParam.setEndDate(new DateTime(createTime).millisOfDay().withMaximumValue().toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public List<OrderDTO> findBySnSection(List<String> snList) {
        if (CollectionUtils.isEmpty(snList)) {
            return null;
        }
        List<OrderDTO> orderDTOList = new ArrayList<>();
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<OrderDO> orderDOList = this.selectBySnList(v, beginOfQuarter, endOfQuarter);
            // 转换下DO-->DTO
            if (CollectionUtils.isEmpty(orderDOList)) {
                return;
            }
            orderDTOList.addAll(OrderBuilder.buildDTO(orderDOList));
        });
        return orderDTOList;
    }

    private List<OrderDO> selectBySnList(List<String> snList, Date beginOfQuarter, Date endOfQuarter) {

        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(beginOfQuarter);
        timeRangeParam.setEndDate(endOfQuarter);
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sn", snList);
        example.and(criteria);
        return orderMapper.selectByExample(example);
    }

    @Override
    public List<OrderDTO> findByNo90Days(String declareOrderNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("declareOrderNo", declareOrderNo);
        criteria.andEqualTo("version", 0);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<OrderDO> orderDOList = orderMapper.selectByExample(example);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public void updateSnWithFix(Long id, String customOrderSn, String customsInventorySn, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setCustomsOrderSn(customOrderSn);
        template.setCustomsInventorySn(customsInventorySn);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public OrderDTO findByEbpAndNoAndVersionFull(Long ebpId, String declareOrderNo, Long version) {
        log.info("租户id={} findByEbpAndNoAndVersionFull ebpId={},declareOrderNo={}", SimpleTenantHelper.getTenantId(), ebpId, declareOrderNo);
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("ebpId", ebpId).andEqualTo("declareOrderNo", declareOrderNo).andEqualTo("version", version));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public OrderDTO findByDeclareOrderNo(String declareOrderNo) {
        if (Objects.isNull(declareOrderNo)) {
            return null;
        }
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo));
        // Step::返回值处理
        OrderDO result = orderMapper.selectOneByExample(example);
        return OrderBuilder.buildDTO(result);
    }

    @Override
    public List<OrderDTO> listByNoAndVersionFull(String declareOrderNo, Long version) {
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class);
        example.and(example.createCriteria().andEqualTo("declareOrderNo", declareOrderNo).andEqualTo("version", version));
        // Step::返回值处理
        List<OrderDO> result = orderMapper.selectByExample(example);
        return result.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByOutOrderNo90Days(String outOrderNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outOrderNo", outOrderNo);
        criteria.andEqualTo("version", 0);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<OrderDO> orderDOList = orderMapper.selectByExample(example);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByOutOrderNo90Days(List<String> outOrderNoList) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("outOrderNo", outOrderNoList);
        criteria.andEqualTo("version", 0);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<OrderDO> orderDOList = orderMapper.selectByExample(example);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByEbpAndStatus14days(Long ebpId, Integer status) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(14).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        if (ebpId != null) {
            criteria.andEqualTo("ebpId", ebpId);
        }
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<OrderDO> orderDOList = orderMapper.selectByExample(example);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByUpdateTimeSection(Date updateFrom, Date updateTo, Date section) {
        // Step::初始化时间区间
        if (section == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(section));
        //timeRangeParam.setEndDate(section);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(section));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andBetween("updateTime", updateFrom, updateTo));

        // Step::返回值处理
        List<OrderDO> result = orderMapper.selectByExample(example);
        return result.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByCreateTimeSection(Date createFrom, Date createTo) {
        // Step::初始化时间区间
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createFrom));
        //timeRangeParam.setEndDate(section);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createFrom));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andBetween("createTime", createFrom, createTo));
        // Step::返回值处理
        List<OrderDO> result = orderMapper.selectByExample(example);
        return result.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<OrderDTO> listByUpdateTimeSection(Date updateFrom, Date updateTo) {
        // Step::初始化时间区间
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(updateFrom));
        //timeRangeParam.setEndDate(section);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(updateTo));
        // Step::根据时间区间查询
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andBetween("updateTime", updateFrom, updateTo));

        // Step::返回值处理
        List<OrderDO> result = orderMapper.selectByExample(example);
        return result.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }

    private void updateByIdSection(Long id, OrderDO template, Date sectionDate) {
        this.updateByIdSection(id, null, template, sectionDate);
    }

    private void updateByIdSection(Long id, String orderSn, OrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", id));
        UserUtils.setUpdateBy(template);
        template.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(template, example);
        // Step::ES数据更新
        if (Objects.isNull(orderSn)) {
            OrderDTO result = this.findByIdSection(id, sectionDate);
            if (Objects.nonNull(result)) {
                orderSn = result.getSn();
            }
        }
        orderEsDumpProducer.send(orderSn);
    }

    @Override
    public void updateStatusSection(Long id, Integer status, Date sectionDate) {
        this.updateStatusSection(id, status, null, sectionDate);
    }

    @Override
    public void updateStatusSection(Long id, String orderSn, Integer status, Date sectionDate) {
        this.updateStatusSection(id, orderSn, status, null, sectionDate);
    }

    @Override
    public void updateStatusSection(Long id, Integer status, String internalStatus, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setStatus(status);
        template.setInternalStatus(internalStatus);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateStatusSection(Long id, String orderSn, Integer status, String internalStatus, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setStatus(status);
        template.setInternalStatus(internalStatus);
        this.updateByIdSection(id, orderSn, template, sectionDate);
    }

    @Override
    public void updateByFinish(Long id, Integer status, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setStatus(status);
        template.setFinishTime(new Date());
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByFinishClearException(Long id, Integer status, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setStatus(status);
        template.setExceptionFlag(false);
        template.setFinishTime(new Date());
        this.updateByIdSection(id, template, sectionDate);
    }

    public void updateExceptionSection(Long id, String sn, CustomsStatusMappingDTO customsStatusMappingDTO, String detail, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setExceptionFlag(customsStatusMappingDTO.getExceptionFlag());
        template.setExceptionType(customsStatusMappingDTO.getId().intValue());
        template.setExceptionDetail(detail);
        this.updateByIdSection(id, sn, template, sectionDate);
    }

    @Override
    public void addExceptionSection(Long id, Long typeId, String detail, Date sectionDate) {
        this.addExceptionSection(id, null, typeId, detail, sectionDate);
    }

    @Override
    public void addExceptionSection(Long id, String orderSn, Long typeId, String detail, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setExceptionFlag(true);
        template.setExceptionType(typeId.intValue());
        template.setExceptionDetail(detail);
        this.updateByIdSection(id, orderSn, template, sectionDate);
    }

    @Override
    public void clearExceptionSection(Long id, Date sectionDate) {
        OrderDO template = new OrderDO();
        template.setExceptionFlag(false);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    @Lock(lockType = Lock.LockType.METHOD, lockMethod = "getDeclareOrderNo")
    public Long submit(@Valid OrderSubmit submit) throws ArgsErrorException {

        OrderSubmitDto orderSubmitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        filterWhitespace(orderSubmitDto);
        return submit(orderSubmitDto);
    }

    /**
     * 订单提交业务实现
     *
     * @param submit 订单提交
     * @return 申报单主键ID
     */
    @DataSecurity
    public Long submit(OrderSubmitDto submit) {
        Boolean STOP_ACCEPT_ORDER = orderCBaseConfig.getSTOP_ACCEPT_ORDER();
        if (STOP_ACCEPT_ORDER) {
            throw new ArgsErrorException("清关系统截单，请稍后再试");
        }
        String declareOrderNo = submit.getDeclareOrderNo();
        try {
            log.info("申报单 : {} 提交 租户id={} , OrderSubmitDto={}", declareOrderNo, SimpleTenantHelper.getTenantId(), JSON.toJSONString(submit));
            RichOrder richOrder = new RichOrder();
            // Step::基础业务校验
            // Mod::路由模式
            RouteDTO routeDTO = checkRoute(submit.getRouteCode(), submit.getFirstIdentify(), submit.getSecondIdentify(), submit.getThirdIdentify());
            if (routeDTO == null) {
                throw new ArgsErrorException("提交的申报路径编码不存在，路径标识：" + submit.getRouteCode());
            } else {
                //检查是否是芥舟加密模式
                List<String> declareOrderTypes = submit.getDeclareOrderTypes();
                if (!CollectionUtils.isEmpty(declareOrderTypes) && declareOrderTypes.contains(DeclareOrderTypeEnums.JIEZHOU_ENCRYPT.getCode())) {
                    ExternalDeclareOrderDo externalDeclareOrderDo = externalDeclareOrderBaseService.getByOrderNo(submit.getDeclareOrderNo());
                    String newRouteCode;
                    if (Objects.nonNull(externalDeclareOrderDo) && Objects.nonNull(externalDeclareOrderDo.getRouteId())) {
                        routeDTO = baseDataService.getRouteDTOById(externalDeclareOrderDo.getRouteId());
                        newRouteCode = routeDTO.getCode();
                    } else {
                        newRouteCode = routeDTO.getCode() + "-encrypt";
                        routeDTO = baseDataService.getRouteDTOByCode(newRouteCode);
                    }
                    log.info("经过芥舟加密映射后 路径code:{} route:{}", newRouteCode, JSON.toJSONString(routeDTO));
                    if (routeDTO == null) {
                        throw new ArgsErrorException("提交的申报路径编码经过芥舟加密映射后不存在，路径标识：" + newRouteCode);
                    } else {
                        submit.setRouteCode(routeDTO.getCode());
                    }

                } else {
                    submit.setRouteCode(routeDTO.getCode());
                }
            }
            OrderSubmitDto originSubmit = ConvertUtil.beanConvert(submit, OrderSubmitDto.class);
            //复制一个原始报文
            CompanyDTO ebp = baseDataService.getUnifiedCrossCodeCompanyById(routeDTO.getEbpId());
            if (ebp == null) {
                throw new ArgsErrorException("申报路径编码对应的电商平台未找到");
            }
            List<String> actionList = routeDTO.getActionList();
            if (CollectionUtils.isEmpty(actionList)) {
                throw new ArgsErrorException("申报项为空");
            }
            // 为了兼容小程序推送支付单没有快递编码的情况
            if (!StringUtils.isEmpty(submit.getExpressCode())) {
                ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(submit.getExpressCode());
                if (expressDTO == null || expressDTO.getEnable() != 1) {
                    throw new ArgsErrorException("快递编码未配置:" + submit.getExpressCode());
                }
                String logisticsEnterpriseCode = submit.getLogisticsEnterpriseCode();
                //当路径不包含运单申报且上游指定了物流企业
                if (!RouteActionUtil.containShipment(JSON.toJSONString(actionList)) && !StringUtils.isEmpty(logisticsEnterpriseCode)) {
//                    CompanyDTO logisticsCompany = baseDataService.getCompanyDTOByCode(logisticsEnterpriseCode);
                    CompanyDTO logisticsCompany = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(logisticsEnterpriseCode);
                    if (Objects.isNull(logisticsCompany)) {
                        if (!StringUtils.isEmpty(submit.getLogisticsEnterpriseName())) {
                            throw new ArgsErrorException("物流企业未配置:" + logisticsEnterpriseCode + "-" + submit.getLogisticsEnterpriseName());
                        } else {
                            throw new ArgsErrorException("物流企业未配置:" + logisticsEnterpriseCode);
                        }
                    }
                    submit.setLogisticsEnterpriseId(logisticsCompany.getId());
                } else {
                    CompanyDTO expressCompany = baseDataService.getUnifiedCrossCodeCompanyById(expressDTO.getExpressCompanyId());
                    if (Objects.isNull(expressCompany)) {
                        throw new ArgsErrorException("快递编码映射企业未配置:" + submit.getExpressCode());
                    }
                    submit.setLogisticsEnterpriseId(expressCompany.getId());
                }
            }
            //京东校验账册信息存不存在
            if (Long.valueOf(10).equals(routeDTO.getCustomsBookId()) && routeDTO.getCode().startsWith("JDRK")) {
                StringBuilder errMsg = new StringBuilder();
                List<OrderSubmitItemDto> itemList = submit.getItemList();
                for (OrderSubmitItemDto submitItem : itemList) {
                    List<CustomsBookItemDTO> itemDTOS = customsBookItemService.findByBookIdAndProId(routeDTO.getCustomsBookId(), submitItem.getRecordNo());
                    if (CollectionUtils.isEmpty(itemDTOS)) {
                        errMsg.append("账册料号").append(submitItem.getRecordNo()).append("不存在;");
                    }
                    GoodsRecordDTO recordDTO = goodsRecordService.findByBookIdAndProId(routeDTO.getCustomsBookId(), submitItem.getRecordNo());
                    if (recordDTO == null) {
                        errMsg.append("商品备案").append(submitItem.getRecordNo()).append("不存在;");
                    }
                }
                if (!StringUtils.isEmpty(errMsg.toString())) {
                    throw new ArgsErrorException(errMsg.toString());
                }
            }
            richOrder.setRoute(routeDTO);
            richOrder.setEbp(ebp);
            // Mod::自定义模式 暂不支持
            // ## -----------------
            // Step::存在校验
            OrderConvertWorkSpace workSpace = new OrderConvertWorkSpace();
            OrderDO orderDO;
            OrderDTO old = this.findByEbpAndNoAndVersionFull(ebp.getId(), submit.getDeclareOrderNo(), 0L);
//            Integer hangUpStatus = Optional.ofNullable(old).map(OrderDTO::getHangUpStatus).orElse(null);
            Integer orderTags = Optional.ofNullable(old).map(OrderDTO::getOrderTags).orElse(null);
            List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
            if (orderTag.contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
                throw new ArgsErrorException("单据已挂起不支持重推:" + submit.getDeclareOrderNo());
            }
            TrackLogBaseInfo trackLogBaseInfo = new TrackLogBaseInfo();
            if (old == null) {
                //构造orderDO
                orderDO = orderConvert.preOrder(submit, routeDTO, ebp, originSubmit);
                //保存首次推送日志
                trackLogBaseInfo.setOrderId(orderDO.getId());
                trackLogBaseInfo.setOrderSn(orderDO.getSn());
                trackLogBaseInfo.setStatusDesc(OrderInternalEnum.CREATE_WAIT_PUSH.getCode());
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(trackLogBaseInfo);
            } else {
                // 设置申报单不是新的
                richOrder.setDeclareIsNew(false);
                trackLogBaseInfo.setOrderId(old.getId());
                trackLogBaseInfo.setOrderSn(old.getSn());
                trackLogBaseInfo.setStatusDesc(OrderInternalEnum.DECLARING.getCode());
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(trackLogBaseInfo);
                if (old.getCreateTime().after(DateTime.now().minusMillis(10).toDate())) {
                    return old.getId(); // 10S内重复调用设为幂等、不更新
                } else {
                    orderDO = CopyUtil.copy(old, OrderDO.class);
//                    String oldGsNo = old.getSystemGlobalSn();
                    List<String> actionOld = JSON.parseArray(old.getActionJson(), String.class);
                    List<String> actionNew = JSON.parseArray(JSON.toJSONString(actionList), String.class);
                    Set<String> actionSet = new HashSet<>();
                    actionSet.addAll(actionOld);
                    actionSet.addAll(actionNew);
                    // 将老的Route-Action赋值下，后面会基于此判断申报单重推，是新增还是修改
                    richOrder.setOldActionList(actionOld);
                    orderDO.setOutOrderNo(submit.getOutOrderNo());
                    orderDO.setActionJson(JSON.toJSONString(new ArrayList<>(actionSet)));
                    OrderExtraDto extra = JSON.parseObject(orderDO.getExtraJson(), OrderExtraDto.class);
                    extra.setSubmit(originSubmit);
                    orderDO.setExtraJson(JSON.toJSONString(extra));
                    orderDO.setSystemGlobalSn(submit.getSystemGlobalSn());
                    if (CollectionUtil.isNotEmpty(submit.getDeclareOrderTypes())) {
                        if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.BYTE_DANCE_WMS.getCode())) {
                            orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.BYTE_DANCE_WMS.getOrderTag());
                        }
                        if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.JIEZHOU_ENCRYPT.getCode())) {
                            orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.JIEZHOU_ENCRYPT.getOrderTag());
                        }
                        if (submit.getDeclareOrderTypes().contains(DeclareOrderTypeEnums.CAINIAO_WMS.getCode())) {
                            orderTags = DeclareOrderTagEnums.add(orderTags, DeclareOrderTypeEnums.CAINIAO_WMS.getOrderTag());
                        }
                        orderDO.setOrderTags(orderTags);
                    }
                    CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDO.getId(), orderDO.getCustomsInventorySn());
                    if (customsInventoryDTO != null) {
                        if (CustomsActionStatus.DEC_ING.getValue().equals(customsInventoryDTO.getStatus())) {
                            throw new ArgsErrorException("当前清单正在申报中，请稍后再试");
                        } else if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(customsInventoryDTO.getStatus())) {
//                            String newGsNo = submit.getSystemGlobalSn();
//                            if (!Objects.equals(oldGsNo, newGsNo)) {
//                                log.info("GS单号替换 申报单号:{} oldGs:{} newGs:{}", orderDO.getDeclareOrderNo(), oldGsNo, newGsNo);
//                                orderBaseService.updateGlobalSnAndStatus(orderDO, newGsNo, OrderStatus.DEC_SUCCESS.getValue());
//                                OrderDTO orderDTO = ConvertUtil.beanConvert(orderDO, OrderDTO.class);
//                                try {
//                                    callbackOmsInventoryPassAsync(customsInventoryDTO, orderDTO, ebp.getCode());
//                                } catch (Exception e) {
//                                    log.error("回调oms失败 error={}", e.getMessage(), e);
//                                }
//                                return orderDO.getId();
//                            }
                            throw new ArgsErrorException("当前清单已完成申报，请勿重复申报");
                        }
                    }
                }
            }
            // Step::申报单数据构建
            richOrder.setOrder(orderDO);
            Boolean exchangeBood = false;
            // Step:: 四单创建
            for (String action : actionList) {
                switch (RouteActionEnum.getEnum(action)) {
                    case DECLARE_INVENTORY: {
                        Tuple<CustomsInventoryDOPack, Boolean> customsInventoryDOPack = customsInventoryConvert.preCustomsInventory(null, submit, workSpace);
                        richOrder.setCustomsInventoryDOPack(customsInventoryDOPack.getF());
                        if (customsInventoryDOPack.getS()) {
                            exchangeBood = true;
                        }
                        break;
                    }
                    case DECLARE_ORDER: {
                        Tuple<CustomsOrderDO, Boolean> customsOrderDO = customsOrderConvert.preCustomsOrder(null, submit, workSpace);
                        richOrder.setCustomsOrderDO(customsOrderDO.getF());
                        if (customsOrderDO.getS()) {
                            exchangeBood = true;
                        }
                        break;
                    }
                    case DECLARE_PAYMENT: {
                        manualInitMerchantCode(submit);
                        CustomsPaymentDO customsPaymentDO = customsPaymentConvert.preCustomsPayment(null, submit, workSpace);
                        richOrder.setCustomsPaymentDO(customsPaymentDO);
                        break;
                    }
                    case DECLARE_LOGISTICS: {
                        CustomsLogisticsDO customsLogisticsDO = customsLogisticsConvert.preCustomsLogistics(null, submit, workSpace);
                        richOrder.setCustomsLogisticsDO(customsLogisticsDO);
                        break;
                    }
                    default: {
                        throw new ArgsErrorException("申报项" + action + "不合法");
                    }
                }
            }

            // 如果切换了申报账册, 需要把核注单打标识
            OrderDO order = richOrder.getOrder();
            Integer orderTagsTmp = order.getOrderTags();
            if (exchangeBood) {
                orderTagsTmp = DeclareOrderTagEnums.add(orderTagsTmp, DeclareOrderTagEnums.CHANGE_BOOK);
            } else {
                orderTagsTmp = DeclareOrderTagEnums.remove(orderTagsTmp, DeclareOrderTagEnums.CHANGE_BOOK);
            }
            order.setOrderTags(orderTagsTmp);

            // Step::179数据存储
            if (submit.getPayInfoDataCheckFlag()) {
                ExtraPayInfoDO customsExtraPayInfoDO = extraPayInfoConvert.preExtraPayInfo(null, submit, workSpace);
                richOrder.setExtraPayInfoDO(customsExtraPayInfoDO);
            }
            // 京东订单校验 校验表体组套申报
            richOrder.setOrder(checkJdItemNameSet(submit, routeDTO, richOrder.getOrder()));

            // Step::持久化
            Long id = orderManager.orderCreate(richOrder);

            // 这里记录下日志, id会在持久化层写入
            if (exchangeBood) {
                // 记录日志
                exchangeBookLog(orderDO.getId(), orderDO.getSn(), orderDO.getDeclareOrderNo());
            }
            return id;
        } catch (ArgsErrorException e) {
            log.info("申报单创建 参数校验异常 => 申报单号 : {} ,异常信息: {}", declareOrderNo, e.getErrorMessage(), e);
            throw new ArgsInvalidException(e.getErrorMessage());
        } catch (Exception e) {
            log.error("申报单创建 系统异常 => 申报单号 : {} ,异常信息: {}", declareOrderNo, e.getMessage(), e);
            throw e;
        }
    }

    public void exchangeBookLog(Long orderId, String orderSn, String declareOrderNo) {
        try {
            TrackLogInfoDto trackLogInfoDto = new TrackLogInfoDto();
            trackLogInfoDto.setOrderId(orderId);
            trackLogInfoDto.setOrderSn(orderSn);
            trackLogInfoDto.setDeclareOrderNo(declareOrderNo);
            trackLogInfoDto.setInternalStatus(OrderInternalEnum.DECLARING.getCode());
            trackLogInfoDto.setSender(TrackLogConstantMixAll.NULL);
            trackLogInfoDto.setReceiver(TrackLogConstantMixAll.NULL);
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.SYSTEM_CHANGE_DECLARE_BOOK);
            trackLogInfoDto.setEventTime(new Date());
            trackLogInfoDto.setOperator(UserUtils.getUserRealName());
            trackLogInfoDto.setResult(TrackLogConstantMixAll.SUCCESS);
            trackLogEsProducer.sendMsg(JSON.toJSONString(trackLogInfoDto));
        } catch (Exception e) {
            log.error("申报账册变更日志写入异常:{}", e.getMessage(), e);
        }

    }

    private OrderDO checkJdItemNameSet(OrderSubmitDto submit, RouteDTO routeDTO, OrderDO order) {
        if (StrUtil.isEmpty(orderCBaseConfig.getJdItemNameSetCheckJson())) {
            return order;
        }
        JdItemNameSetCheckNotifyVO notifyVO =
                JSON.parseObject(orderCBaseConfig.getJdItemNameSetCheckJson(), JdItemNameSetCheckNotifyVO.class);
        if (Boolean.FALSE.equals(notifyVO.getEnable())
                || CollUtil.isEmpty(notifyVO.getEbpIdList())
                || !notifyVO.getEbpIdList().contains(routeDTO.getEbpId())
                || MapUtil.isEmpty(notifyVO.getRegexMap())) {
            return order;
        }
        Map<String, String> regexMap = notifyVO.getRegexMap();
        boolean checked = false;
        String checkRegex = "";
        for (OrderSubmitItemDto submitItemDto : submit.getItemList()) {
            String itemName = submitItemDto.getItemName();
            checked = false;
            checkRegex = "";
            int x1 = 0;
            for (String regex : regexMap.keySet()) {
                // 创建Pattern对象
                Pattern pattern = Pattern.compile(regex);
                Matcher matcher = pattern.matcher(itemName);
                if (matcher.find()) {
                    // 获取匹配的值
                    String group1 = matcher.group(1);
                    x1 = group1 != null ? Integer.parseInt(group1) : 0;
                    // 检查X是否为非1的整数
                    if (x1 != 0 && x1 != 1) {
                        checkRegex = regexMap.get(regex);
                        checked = true;
                    }
                    break;
                }
            }
            // 检验账册库存
            if (checked) {
                List<CustomsBookItemDTO> itemDTOS = customsBookItemService.findByBookIdAndProId(routeDTO.getCustomsBookId(), submitItemDto.getRecordNo());
                CustomsBookItemDTO customsBookItemDTO = itemDTOS.stream().filter(item -> Objects.equals(1, item.getEnable())).max(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo)).orElse(null);
                if (Objects.nonNull(customsBookItemDTO)) {
                    String customsBookItemGoodsName = customsBookItemDTO.getGoodsName();
                    List<Integer> stockNumbers = extractNumbersFromName(customsBookItemGoodsName);
                    if (stockNumbers.contains(x1)) {
                        checked = false;
                    }
//                    for (String regex : regexMap.keySet()) {
//                        // 创建Pattern对象
//                        Pattern pattern = Pattern.compile(regex);
//                        Matcher matcher = pattern.matcher(customsBookItemGoodsName);
//                        if (matcher.find()) {
//                            // 获取匹配的值
//                            String group1 = matcher.group(1);
//                            int x2 = group1 != null ? Integer.parseInt(group1) : 0;
//                            if (x1 == x2) {
//                                checked = false;
//                            }
//                            break;
//                        }
//                    }
                }
            }
        }
        if (checked) {
            // 订单挂起
            order.setOrderTags(DeclareOrderTagEnums.add(order.getOrderTags(), DeclareOrderTagEnums.HANG_UP));
            order.setHangUpStatus(1);
            CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("COMMON:null:ITEM_NAME_ERROR");
            order.setExceptionType(mappingDTO.getId().intValue());
            order.setExceptionDetail("订单品名包含：" + checkRegex + ",账册库存品名无");
            order.setExceptionFlag(true);

            // 企微机器人提醒
            String message = "申报单：" + order.getDeclareOrderNo() + "\n" + mappingDTO.getNote() + ";[" + order.getExceptionDetail() + "]";
            WechatNotifyUtils.wechatNoteSend(notifyVO.getWebhook(), notifyVO.getPhoneList(), message);
        }
        return order;
    }

    public static List<Integer> extractNumbersFromName(String stockName) {
        // 使用正则表达式匹配所有的数字
        Pattern pattern = Pattern.compile("\\d+");
        Matcher matcher = pattern.matcher(stockName);

        List<Integer> numbers = new ArrayList<>();
        while (matcher.find()) {
            int number = Integer.parseInt(matcher.group());
            numbers.add(number);
        }
        return numbers;
    }

    private static void filterWhitespace(OrderSubmitDto submit) {
        if (StringUtil.isNotBlank(submit.getBuyerName())) {
            submit.setBuyerName(StringUtils.trimWhitespace(submit.getBuyerName()));
        }
        if (StringUtil.isNotBlank(submit.getBuyerTelNumber())) {
            submit.setBuyerTelNumber(StringUtils.trimWhitespace(submit.getBuyerTelNumber()));
        }
        if (StringUtil.isNotBlank(submit.getConsigneeName())) {
            submit.setConsigneeName(StringUtils.trimWhitespace(submit.getConsigneeName()));
        }
        if (StringUtil.isNotBlank(submit.getConsigneeTel())) {
            submit.setConsigneeTel(StringUtils.trimWhitespace(submit.getConsigneeTel()));
        }
    }

    /**
     * 兼容小程序api-v3的情况
     * 目前小程序推支付单下来锁定不了商户号 所以采用配置替换的方式暂时兼容下
     *
     * @param submit
     */
    public void manualInitMerchantCode(OrderSubmitDto submit) {
        String merchantReplaceConfig = orderCBaseConfig.getMerchantReplaceConfig();
        if (StringUtils.isEmpty(submit.getMerchantCode())) {
            log.info("manualInitMerchantCode 当前routeCode={} merchantCode为空", submit.getRouteCode());
            if (!StringUtils.isEmpty(merchantReplaceConfig)) {
                log.info("manualInitMerchantCode merchantReplaceConfig={}", merchantReplaceConfig);
                List<MerchantReplaceConfig> merchantReplaceConfigs = JSON.parseArray(merchantReplaceConfig, MerchantReplaceConfig.class);
                Map<String, String> routeMerchantMap = merchantReplaceConfigs.stream().collect(Collectors.toMap(MerchantReplaceConfig::getRouteCode, MerchantReplaceConfig::getMerchantCode));
                if (routeMerchantMap.containsKey(submit.getRouteCode())) {
                    String merchantCode = routeMerchantMap.get(submit.getRouteCode());
                    log.info("manualInitMerchantCode routeCode={} 对应merchantCode={}", submit.getRouteCode(), merchantCode);
                    submit.setMerchantCode(merchantCode);
                } else {
                    log.info("manualInitMerchantCode routeCode={} 无对应merchantCode", submit.getRouteCode());
                }
            }
        }
    }

    @Override
    public Long expInfoSubmit(Long userId, ExtraPayInfoSubmit submit) {
        log.info("expInfoSubmit :{}", JSON.toJSONString(submit));
        RouteDTO routeDTO = checkRoute(submit.getRouteCode(), submit.getFirstIdentify(), submit.getSecondIdentify(), submit.getThirdIdentify());
        if (routeDTO == null) {
            throw new ArgsErrorException("提交的申报路径编码不存在，路径标识：" + submit.getRouteCode());
        } else {
            submit.setRouteCode(routeDTO.getCode());
        }
        OrderConvertWorkSpace workSpace = new OrderConvertWorkSpace();
        ExtraPayInfoSubmitDto payInfoSubmitDto = new ExtraPayInfoSubmitDto();
        BeanUtils.copyProperties(submit, payInfoSubmitDto);
        ExtraPayInfoDO customsExtraPayInfoDO = extraPayInfoConvert.preExtraPayInfo(payInfoSubmitDto);
        extraPayInfoConvert.save(customsExtraPayInfoDO);
        return customsExtraPayInfoDO.getId();
    }

    private RouteDTO checkRoute(String routeCode, String firstIdentify, String secondIdentify, String thirdIdentify) {
        RouteDTO routeDTO;
        if (!StringUtils.isEmpty(routeCode)) {
            routeDTO = baseDataService.getRouteDTOByCode(routeCode);
        } else {
            if (!StringUtils.isEmpty(thirdIdentify)) {
                if (StringUtils.isEmpty(firstIdentify) || StringUtils.isEmpty(secondIdentify)) {
                    throw new ArgsErrorException("标识3不为空时，标识1或标识2不能为空");
                }
            }
            if (!StringUtils.isEmpty(secondIdentify)) {
                if (StringUtils.isEmpty(firstIdentify)) {
                    throw new ArgsErrorException("标识2不为空时，标识1不能为空");
                }
            }
            if (StringUtils.isEmpty(firstIdentify)) {
                throw new ArgsErrorException("标识1不能为空");
            }
            PathDTO pathDTO = baseDataService.getPathDTOByIdentify(firstIdentify, secondIdentify, thirdIdentify);
            if (pathDTO == null) {
                throw new ArgsErrorException("申报路由不存在,实体仓编码：" + firstIdentify + ",渠道编码：" + secondIdentify + ",店铺ID：" + thirdIdentify);
            }
            if (pathDTO.getEnable() == 0) {
                throw new ArgsErrorException("申报路由已禁用: " + pathDTO.getName());
            }
            routeDTO = baseDataService.getRouteDTOById(pathDTO.getRouteId());
            if (Objects.isNull(routeDTO)) {
                throw new ArgsErrorException("申报路由对应申报路径不存在: " + pathDTO.getName());
            }
        }
        return routeDTO;
    }

    @Override
    public Long syncLogsticsOrder(@Valid OrderSimpleSubmit submit) throws ArgsErrorException {
        try {
            submit.setChannel(OrderChannel.LOGISTICS.getValue());
            log.info("[op:syncLogsticsOrder] OrderSimpleSubmit={}", JSON.toJSONString(submit));
            OrderSubmit orderSubmit = new OrderSubmit();
            BeanUtils.copyProperties(submit, orderSubmit);
            DockerOrderDTO dockerOrderDTO = orderManager.getDockerOrderByGlobalSn(submit.getSystemGlobalSn());
            if (dockerOrderDTO == null) {
                throw new ArgsErrorException("未能找到上报订单，请确认全局订单号是否有误");
            }

            List<OrderSimpleSubmitItem> sItemList = submit.getItemList();
            List<OrderSubmitItem> itemList = new ArrayList<OrderSubmitItem>();
            BigDecimal totalGoodsPrice = BigDecimal.ZERO;
            for (OrderSimpleSubmitItem item : sItemList) {
                OrderSubmitItem orderSubmitItem = new OrderSubmitItem();
                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByWareCodeAndSku(submit.getWareCode(), item.getSkuCode());
                orderSubmitItem.setRecordNo(goodsRecordDTO.getProductId());
                orderSubmitItem.setCount(item.getCount());
                if (sItemList.size() == 1) {
                    orderSubmitItem.setUnitPrice(dockerOrderDTO.getGoodsSumAmount().divide(new BigDecimal(item.getCount())));
                } else {
                    orderSubmitItem.setUnitPrice(item.getUnitPrice());
                }
                orderSubmitItem.setItemName(item.getItemName());
                orderSubmitItem.setItemNo(item.getSkuCode());
                itemList.add(orderSubmitItem);
                BigDecimal itemGoodsPrice = item.getUnitPrice().multiply(new BigDecimal(item.getCount().toString()));
                totalGoodsPrice = totalGoodsPrice.add(itemGoodsPrice);
            }
            orderSubmit.setItemList(itemList);
            totalGoodsPrice = totalGoodsPrice.setScale(2, BigDecimal.ROUND_HALF_UP);

            if (!(dockerOrderDTO.getGoodsSumAmount().compareTo(totalGoodsPrice) == 0)) {
//            throw new ArgsErrorException("商品项目总金额不相符");
            }

            BeanUtils.copyProperties(dockerOrderDTO, orderSubmit);
            orderSubmit.setBuyerIdNumber(dockerOrderDTO.getBuyerIdNumber());
            orderSubmit.setOrigGoodsInfoList(dockerOrderDTO.getOrigGoodsInfoList());
            String customsBookNo = warehouseService.getBookSnByWarehouse(submit.getWareCode());
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(customsBookNo);
            if (customsBookDTO == null) {
                throw new ArgsErrorException("未能找到仓库编码对应的关区");
            }
            orderSubmit.setCustomsCode(customsBookDTO.getCustomsDistrictCode());
            log.info("[op:syncLogsticsOrder] OrderSubmit={}", JSON.toJSONString(orderSubmit));
            return submit(orderSubmit);
        } catch (ArgsErrorException e) {
            log.error("进销存申报调试 {}", e.getErrorMessage(), e);
            throw e;
        }

    }


    @Override
    public void syncOrderCallback(OrderCallbackSubmit orderCallbackSubmit) {
        OrderDTO order = findByIdFull(orderCallbackSubmit.getOrderId());
        OrderDTO orderDTO = this.findByIdFull(orderCallbackSubmit.getOrderId());
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        if (!StringUtils.isEmpty(order.getExtraJson())) {
            OrderExtra extra = JSON.parseObject(order.getExtraJson(), OrderExtra.class);
            if (extra.getSubmit() != null && extra.getSubmit().getChannel() != null && extra.getSubmit().getChannel().equals(OrderChannel.LOGISTICS.getValue())) {//回传出入库系统
                orderCallbackSubmit.setOrderNo(order.getOutOrderNo());
                orderCallbackSubmit.setSystemGlobalSn(order.getSystemGlobalSn());
                orderCallbackSubmit.setDeclareOrderNo(order.getDeclareOrderNo());
                orderCallbackSubmit.setInventoryNo(inventoryDTO.getInventoryNo());
//                orderCallbackSubmit.setLogisticsNo(inventoryDTO.getLogisticsNo());
                if (inventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    orderCallbackSubmit.setStatus(OrderStatus.DEC_SUCCESS.getValue() + "");
                } else if (inventoryDTO.getStatus().equals(CustomsActionStatus.DEC_FAIL.getValue())) {
                    orderCallbackSubmit.setStatus(OrderStatus.DEC_FAIL.getValue() + "");
                } else if (inventoryDTO.getStatus().equals(CustomsActionStatus.DEC_CANCEL.getValue())) {
                    orderCallbackSubmit.setStatus(OrderStatus.CANCEL.getValue() + "");
                }

                CompanyDTO ebp = companyService.findUnifiedCrossInfoById(inventoryDTO.getEbpId());
                orderCallbackSubmit.setEbpCode(ebp.getCode());
                orderCallbackSubmit.setEbpName(ebp.getName());

                CompanyDTO ebc = companyService.findUnifiedCrossInfoById(inventoryDTO.getEbcId());
                orderCallbackSubmit.setEbcCode(ebc.getCode());
                orderCallbackSubmit.setEbcName(ebc.getName());

                CompanyDTO assure = companyService.findUnifiedCrossInfoById(inventoryDTO.getAssureCompanyId());
                orderCallbackSubmit.setAssureCode(assure.getCode());
                orderCallbackSubmit.setAssureName(assure.getName());

                orderCallbackSubmit.setPayCompanyCode("");
                orderCallbackSubmit.setPayCompanyName("");

                orderCallbackSubmit.setLogisticsCode("");
                orderCallbackSubmit.setLogisticsCode("");

                log.info("[op:OrderService] proxyLogistics send info={}", JSON.toJSONString(orderCallbackSubmit));
                orderCallbackMQProducer.send(JSON.toJSONString(orderCallbackSubmit));
            }
            log.info("[op:OrderService] proxyLogistics not send step={}", 2);
        }
        log.info("[op:OrderService] proxyLogistics not send step={}", 1);
    }

    @Override
    public void syncDeliver(OrderDeliverMessage message) {
        // Step::供销认这个
        DealOrderStatusDTO dealOrderStatusDTO = new DealOrderStatusDTO();
//        CompanyDTO ebp = companyService.findByCode(message.getEbpCode());
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(message.getEbpCode());
        OrderDTO orderDTO = this.findByEbpAndNoAndVersionFull(ebp.getId(), message.getDeclareOrderNo(), 0L);
        dealOrderStatusDTO.setDealOrderStatus(DealOrderStatus.OUT_FINISH);
        dealOrderStatusDTO.setDeliveryTime(message.getShipmentTime());
        dealOrderStatusDTO.setExpressCode("STO");
        dealOrderStatusDTO.setExpressName("申通快递");
        dealOrderStatusDTO.setWeight(message.getWeight());
        dealOrderStatusDTO.setWmsOrderNo(message.getDepotOrderNo());
        dealOrderStatusDTO.setExpressNo(message.getLogisticsNo());
        dealOrderStatusDTO.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        orderCallbackMQProducer.sendDeliver(dealOrderStatusDTO);
        // Step::渠道订单及上游认这个
        OrderRpcMessage orderRpcMessage = new OrderRpcMessage();
        orderRpcMessage.setBackType(1);
        orderRpcMessage.setOrderNo(orderDTO.getOutOrderNo());
        orderRpcMessage.setBusinessNo(orderDTO.getSystemGlobalSn());
        orderRpcMessage.setUserId(1L);
        orderRpcMessage.setStatus(30);
        orderRpcMessage.setLogisticsNo(message.getLogisticsNo());
        orderRpcMessage.setLogisticsCompanyCode("STO");
        orderRpcMessage.setActualTime(message.getShipmentTime());
        orderRpcMessage.setWeight(message.getWeight());
        List<ItemSkuRpcMessage> skuList = new ArrayList<>();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        for (CustomsInventoryItemDTO itemDTO : customsInventoryItemDTOList) {
            ItemSkuRpcMessage skuRpcMessage = new ItemSkuRpcMessage();
            skuRpcMessage.setGoodsCode(itemDTO.getItemNo());
            skuRpcMessage.setActualQuantity(itemDTO.getCount());
            skuList.add(skuRpcMessage);
        }
        orderRpcMessage.setSkuList(skuList);
        orderCallbackMQProducer.sendDeliver(orderRpcMessage);
    }

    @Override
    public void syncTaxCallback(TaxCallbackSubmit taxCallbackSubmit) {
        orderCallbackMQProducer.sendTax(JSON.toJSONString(taxCallbackSubmit));
    }

    @Override
    public List<TaxCallbackSubmit> queryTaxByDeclareNo(List<String> declareNoList) {
        List<TaxCallbackSubmit> submitList = new ArrayList<>();
        try {
            log.info("queryTaxByDeclareNo list={}", declareNoList);
            List<TaxesTenantTaxListDTO> dtoList = taxesTenantTaxListService.queryTaxByDeclareNo(declareNoList);
            submitList = dtoList.stream().map(d -> {
                TaxCallbackSubmit submit = new TaxCallbackSubmit();
                submit.setSystemGlobalSn(d.getDeclareOrderNo());
                submit.setDeclareOrderNo(d.getDeclareOrderNo());
                submit.setInvtNo(d.getInvtNo());
                submit.setProcessTime(d.getReturnTime().getTime());
                submit.setTotalTax(d.getAmount());
                submit.setTaxNo(d.getTaxNo());
                submit.setCustomsTax(d.getCustomsTax());
                submit.setValueAddedTax(d.getValueAddedTax());
                submit.setConsumptionTax(d.getConsumptionTax());
                return submit;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryTaxByDeclareNo error={}", e.getMessage());
        }
        return submitList;
    }


    @Override
    public Integer syncDockerOrder(@Valid DockerOrderSubmit submit) throws ArgsErrorException {
        DockerOrderDO dockerOrder = new DockerOrderDO();
        BeanUtils.copyProperties(submit, dockerOrder);
        Date d = new Date();
        d.setTime(submit.getTradeTime());
        dockerOrder.setTradeTime(d);
        DockerOrderDTO dockerOrderDTO = orderManager.getDockerOrderByGlobalSn(submit.getSystemGlobalSn());
        if (dockerOrderDTO != null) {
            dockerOrder.setId(dockerOrderDTO.getId());
//            throw new ArgsErrorException("全局订单号已存在，请勿重复提交");
        }
        return orderManager.saveDockerOrder(dockerOrder);
    }

    @Override
    @Transactional
    public void examine(String sn) {
        OrderDTO orderDTO = this.findBySnSection(sn);
        if (orderDTO.getExceptionFlag()) {
            log.warn("[op:OrderExamineJob] order not allow examine, declareSn={}, status={}, exceptionFlag={}", orderDTO.getDeclareOrderNo(), orderDTO.getStatus(), orderDTO.getExceptionFlag());
            return;
        }
        if (DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags()).contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
            log.warn("[op:OrderExamineJob] order has hung up, not allow examine declareSn={}", orderDTO.getDeclareOrderNo());
            return;
        }
        if (OrderFlowPicker.pick(orderDTO).operationAllowed(orderDTO.getStatus(), OrderAction.EXAMINE.getOperation())) {
            if (!orderManager.fillItemInfo(orderDTO)) {
                orderEsDumpProducer.send(orderDTO.getSn());
                return;
            }
            this.updateStatusSection(orderDTO.getId(), OrderFlowPicker.pick(orderDTO).target(orderDTO.getStatus(), OrderAction.EXAMINE.getOperation()), orderDTO.getCreateTime());
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            try {
                if (Integer.valueOf(1).equals(orderExtra.getSubmit().getChannel())) {
                    messageService.createMessage(MessageType.ORDER_RESULT,
                            Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel()),
                            orderDTO.getDeclareOrderNo(), JSON.toJSONString(new OrderEventActive(orderDTO.getId(), "start", "开始申报")), "");
                }
            } catch (Exception e) {
                log.error("[op:OrderExamineJob] 开始清关消息创建失败 order={}, cause={}", orderDTO.getDeclareOrderNo(), e.getMessage(), e);
                throw new RuntimeException("开始清关消息创建失败");
            }
            orderEsDumpProducer.send(orderDTO.getSn());
            TraceData traceData = ChainHelper.get();
            // 事务提交后再触发下一个事件
            TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
                // 在事务提交之后执行的代码块（方法）  此处使用TransactionSynchronizationAdapter，其实在Spring5后直接使用接口也很方便了~
                @Override
                public void afterCommit() {
                    new Thread(() -> {
                        try {
                            ChainHelper.cover(traceData);
//                        orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
                            orderDeclareMQProducer.send(orderDTO);
                        } finally {
                            ChainHelper.unbinding();
                        }
                    }).start();
                }
            });
            log.info("[op:OrderExamineJob] order examine success, declareSn={}", orderDTO.getDeclareOrderNo());
        } else {
            log.warn("[op:OrderExamineJob] order not allow examine, declareSn={}, status={}", orderDTO.getDeclareOrderNo(), orderDTO.getStatus());
        }
    }

    @Override
    @Lock(lockType = Lock.LockType.SELF, keyPrefix = "orderDeclare")
    public void declare(String sn) {
        declareHandler(sn, null);
    }

    // todo 这个位置锁要测试下
    @Override
    @Lock(lockType = Lock.LockType.SELF, keyPrefix = "orderDeclareAppoint")
    public void declare(String sn, DeclareEnum declareEnum) {
        declareHandler(sn, declareEnum);
    }

    /**
     * 抽取下公共代码
     *
     * @param sn
     * @param declareEnum
     */
    private void declareHandler(String sn, DeclareEnum declareEnum) {
        OrderDTO orderDTO = this.findBySnSection(sn);
        if (orderDTO == null) {
            try {
                // 这里其实是有问题的，发消息位置应该是事务提交后发，不应该在在这里搞
                Thread.sleep(2000); // 避免前面的事务来不及提交
            } catch (InterruptedException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            orderDTO = this.findBySnSection(sn);
        }
        if (orderDTO == null) {
            log.error("申报单号SN: {} ,未找到申报单有效数据", sn);
            return;
        }
        if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.DECLARE.getOperation())) {
            log.info("[op:OrderServiceImpl-declare] order declare execute, declareSn={}", orderDTO.getDeclareOrderNo());
            if (declareEnum == null) {
                customsDeclareOrderCommand.execute(orderDTO);
            } else {
                customsDeclareOrderCommand.execute(orderDTO, declareEnum);
            }
        } else {
            log.warn("[op:OrderServiceImpl-declare] order status not allow declare, declareSn={}, status={}", orderDTO.getDeclareOrderNo(), orderDTO.getStatus());
        }
    }

    @Override
    public String receiveInventory(CustomsReceive receive) {
        String result = orderService.receiveInventoryProxy(receive);
        //账册库存: 如果申报失败会导致多次释放占用库存 暂时注掉(换成订单取消扣库存) 2022-06-23
//        handleReleaseStock(receive);
        return result;
    }

    private static final Pattern ALREADY_PASS_PATTERN = Pattern.compile("清单编号.*清单状态");
    private static final Pattern INVENTORY_NO_PATTERN = Pattern.compile("[a-zA-Z0-9]+");

    @Lock(lockType = Lock.LockType.METHOD, keyPrefix = "receiveInventory", lockMethod = "getDeclareOrderNo")
    @TrackLog(infoIndex = 0, handler = ReceiptTrackLogParametersHandler.class, receiptType = TrackLogConstantMixAll.CUSTOMS_INVENTORY_RECEIPT)
    public String receiveInventoryProxy(CustomsReceive receive) {
        log.info("[op:OrderServiceImpl-receiveInventory] receiveCustoms,info={}", JSON.toJSONString(receive));
        // Step::基础参数解析
        String customsStatus = receive.getCustomsStatus();
        String ebpCode = receive.getEbpCode();
        String declareOrderNo = receive.getDeclareOrderNo();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
        String agentCode = receive.getAgentCode();
//        CompanyDTO ebp = baseDataService.getCompanyDTOByCode(ebpCode);
        OrderDTO orderDTO = null;
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
        if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            // 浙电新接口回执无电商平台
            OrderSearch search = new OrderSearch();
            search.setCurrentPage(1);
            search.setPageSize(20);
            search.setQueryType("declareOrderNo");
            search.setQueryInfo(declareOrderNo);
            Page<OrderEsDO> paging = orderEsDao.paging(search);
            List<OrderEsDO> content = paging.getContent();
            if (CollectionUtils.isEmpty(content)) {
                return "订单不存在";
            }
            OrderEsDO next = content.iterator().next();
            orderDTO = orderService.findBySnSection(next.getSn());
        } else {
            if (ebp == null) {
                return "电商平台不存在";
            }
            orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        }
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
        if (customsDetail.contains("实货放行") && new DateTime(orderDTO.getCreateTime()).isAfter(DateTime.now().minusDays(7))) {
            customsStatus = "800";
            receive.setCustomsStatus("800");
            Matcher alreadyPattern = ALREADY_PASS_PATTERN.matcher(customsDetail);
            if (alreadyPattern.find()) {
                String message = alreadyPattern.group();
                Matcher invtPattern = INVENTORY_NO_PATTERN.matcher(message);
                if (invtPattern.find()) {
                    receive.setInvtNo(invtPattern.group());
                } else {
                    log.error("OrderServiceImpl receiveInventory 实货放行未匹配到清单编号 customsDetail={}", customsDetail);
                }
            } else {
                log.error("OrderServiceImpl receiveInventory 实货放行未匹配到清单编号 customsDetail={}", customsDetail);
            }
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        if (Objects.equals(CustomsActionStatus.DEC_SUCCESS.getValue(), customsInventoryDTO.getStatus())) {
            TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), CustomsActionStatus.DEC_SUCCESS.getValue(), null);
            return "订单已放行 不再接受回执";
        }
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
        ArrayList<String> subscribeList = Lists.newArrayList("PLAT-PANGU", submitChannel);
        // Step::打印订单轨迹日志
        log.info("[op:OrderServiceImpl-receiveInventory] order log finish, declareSn={}", receive.getDeclareOrderNo());
        // Step::更新清关状态及回执 根据更新时间来取舍 只保存最新的回执
        if (!CustomsStat.CUSTOMS_PASS.getValue().equals(customsStatus) && // 放行回执不受约束
                customsInventoryDTO.getLastCustomsTime() != null && new DateTime(customsTime).isBefore(new DateTime(customsInventoryDTO.getLastCustomsTime()))) {
            log.warn("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
        } else if (CustomsStat.CUSTOMS_PASS.getValue().equals(customsInventoryDTO.getCustomsStatus())) {
            log.warn("当前清单已放行，略过不做清关状态更新，{}", JSON.toJSONString(receive));
        } else if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            customsInventoryService.updateByCustomsActive(customsInventoryDTO.getId(), null, customsDetail, new DateTime(customsTime).toDate(), customsInventoryDTO.getCreateTime());
        } else {
            // 更新清关状态
            customsInventoryService.updateByCustomsActive(customsInventoryDTO.getId(), customsStatus, customsDetail, new DateTime(customsTime).toDate(), customsInventoryDTO.getCreateTime());
            //同步更新取消单海关状态
            // 回传下取消单的状态，退单,判断是否有取消单,有则回传关单成功
            List<CustomsInventoryCalloffDTO> inventoryCalloffDTOList = customsInventoryCalloffService.findListByOrderId(customsInventoryDTO.getOrderId());
            if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(customsStatus)) {
                if (!CollectionUtils.isEmpty(inventoryCalloffDTOList)) {
                    log.warn("orderServiceImplement-receiveInventoryProxy 构建消息 主订单id - {},回执 - {},申报单号 - {}", orderDTO.getId(), customsDetail, orderDTO.getDeclareOrderNo());
                    cancelOrderNotifyService.callOffNotify(orderDTO.getId(), orderDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, customsDetail, customsTime);
                }
                CustomsInventoryCancelDTO inventoryCancelDTO = customsInventoryCancelService.findByInventoryId(customsInventoryDTO.getId());
                if (customsDetail.contains("Code:13121") && Objects.nonNull(inventoryCancelDTO)) {
                    customsInventoryCancelService.updateStatus(inventoryCancelDTO.getId(), InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue(), new Date());
                }
            }
            customsInventoryCalloffService.updateCustomsStatus(customsInventoryDTO.getOrderId(), customsStatus, customsInventoryDTO.getExitRegionStatus(), orderDTO.getStatus());
        }
        log.info("[op:OrderServiceImpl-receiveInventory] inventory customs receive finish, declareSn={}", receive.getDeclareOrderNo());
        CustomsStatusMappingDTO mappingDTO = getCustomsStatusMappingDTO(receive);
        /**
         * 规则：
         * 申报项状态：
         * 放行：可更改待审报；申报中；申报失败
         * 申报失败：可更改申报中
         */
        Integer resultStatus = mappingDTO.getStatus();
        Boolean exceptionFlag = mappingDTO.getExceptionFlag();
        String preNo = receive.getPreNo();
        String invtNo = receive.getInvtNo();
        //把状态塞到threadLocal中用于记录日志
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), resultStatus, null);
        if (!StringUtils.isEmpty(preNo) || !StringUtils.isEmpty(invtNo)) {
            // 清单号非空 先更新
            customsInventoryService.updateByCustomsPass(customsInventoryDTO.getId(), null, preNo, invtNo, customsInventoryDTO.getCreateTime());
            // 校验下清单编号和账册关区是否一致，防止推送到不同关区
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsInventoryDTO.getAccountBookId());
            inventoryCheckService.checkInvtNoAndAccountAreaSame(customsInventoryDTO.getDeclareOrderNo(), invtNo, customsBookDTO);
        }

        // 芥舟的放行回执回调通过路径来控制订阅
        if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(resultStatus)) {
            OrderSubmit submit = orderExtra.getSubmit();
            RouteDTO routeDTO = this.checkRoute(submit.getRouteCode(), submit.getFirstIdentify(), submit.getSecondIdentify(), submit.getThirdIdentify());
            if (Objects.nonNull(routeDTO)) {
                String[] routeNames = orderCBaseConfig.getRouteNames();
                List<String> routeNameList = Lists.newArrayList(routeNames);
                if (routeNameList.contains(routeDTO.getCode())) {
                    subscribeList.add("JIEZTECH");
                } else if (EnvironmentConfig.isTest()) {
                    subscribeList.add("JIEZTECH");
                }
            }
        }
        CompanyDTO declareCompanyDTO = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(agentCode);
//        CompanyDTO declareCompanyDTO = baseDataService.getCompanyDTOById(customsInventoryDTO.getAgentCompanyId());
        messageService.createMessage(MessageType.ORDER_CUSTOMS_INVENTORY, subscribeList, orderDTO.getDeclareOrderNo(), JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(customsStatus, customsDetail, new DateTime(customsTime).toDate(), declareCompanyDTO.getCode(), declareCompanyDTO.getName())), "");
        String[] littleGiantRouteCodeList = orderCBaseConfig.getLittleGiantRouteCodeList();
        List<String> littleGiantRouteCodes = Arrays.asList(littleGiantRouteCodeList);
        if (CustomsStat.CUSTOMS_PASS.getValue().equals(customsStatus)) {
            if (DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags()).contains(DeclareOrderTagEnums.CAINIAO_WMS.getCode())
                    || (CollectionUtil.isNotEmpty(littleGiantRouteCodes) && littleGiantRouteCodes.contains(orderExtra.getSubmit().getRouteCode()))) {
                //清单放行回执 回传erp
                messageService.createMessageNotThrowEx(MessageType.ORDER_CUSTOMS_INVENTORY_TO_ERP, subscribeList, orderDTO.getDeclareOrderNo(), JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildResponseMsg(receive.getResponseMsg())), "");
            }
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsInventoryDTO.getAccountBookId());
            if (CustomsBookTagEnums.contains(customsBookDTO.getBookTag(), CustomsBookTagEnums.JD_RECEIVE_OUT_REGIN)) {
                try {
                    OrderActiveInfo orderActiveInfo = new OrderActiveInfo(orderDTO.getId(), customsInventoryDTO.getSn()).buildResponseMsg(receive.getResponseMsg());
                    orderActiveInfo.setCustomsTime(new Date(customsTime));
                    messageService.createMessage(MessageType.JD_INVENTORY_CUSTOMS_CALLBACK, subscribeList, orderDTO.getDeclareOrderNo(),
                            JSON.toJSONString(orderActiveInfo), "");
                } catch (Exception e) {
                    log.error("京东电商清单回传消息创建失败 e {}", e.getMessage(), e);
                    MessageAlertConfig config = JSON.parseObject(orderCBaseConfig.getMessageAlertConfig(), MessageAlertConfig.class);
                    if (config != null && StrUtil.isNotBlank(config.getWebhook())) {
                        WechatNotifyUtils.wechatNotifyMd(config.getWebhook(), config.getPhoneList(),
                                WechatNotifyUtils.mdWarnMessage("消息管理异常", orderDTO.getDeclareOrderNo(), "京东电商清单回传消息创建失败", "无"));
                        log.info("京东电商清单回传消息创建失败 企微通知发送成功 declareOrderNo ={}", orderDTO.getDeclareOrderNo());
                    }
                }
            }
        }
        if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(resultStatus)) {
            // 订单转完成
            if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(customsInventoryDTO.getStatus())) {
                return "订单已放行，忽略重复回传";
            } else {
                //确定最终的申报方式，移除失效的方式
                try {
                    boolean updated = this.determineFinalDeclareRecord(orderDTO, receive);
                    if (updated) {
                        orderService.updateOrderInternalStatusAndDeclareRecord(orderDTO, OrderInternalEnum.ORDER_PASS.getCode());
                    } else {
                        orderService.updateOrderInternalStatus(orderDTO, OrderInternalEnum.ORDER_PASS.getCode());
                    }
                } catch (Exception e) {
                    log.error("确定最终方式异常:{}", e.getMessage(), e);
                    return null;
                }
                //最终实际放行走的方法
//                this.clearExceptionSection(customsInventoryDTO.getOrderId(), orderDTO.getCreateTime());
                this.updateExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO, customsDetail, orderDTO.getCreateTime());
                this.updateByCustomsPass(ebp, customsInventoryDTO, customsStatus, customsDetail, customsTime);
                if (!Objects.equals(declareCompanyDTO.getCode(), customsInventoryDTO.getAgentCompanyId())) {
                    customsInventoryService.updateAgentCompanyId(customsInventoryDTO, declareCompanyDTO);
                }
                // FIXME: 2022/7/8 考虑下是否有必要再发一条mq
                // 清单都是已放行状态，就不用做延迟或是隔离申报了
                orderDeclareMQProducer.send(orderDTO.getSn());
                messageSender.sendMsg(orderDTO.getSn(), "ccs-company-tax-pass-topic");
            }
        } else if (CustomsActionStatus.DEC_FAIL.getValue().equals(resultStatus)) {

            //清单申报失败
            if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                return "清单状态非申报中，不接收异常或失败信息";
            }
            // 订单转失败
            if (exceptionFlag) {
                if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    return "清单状态非申报中，不接收异常信息";
                }
                // 订单转异常处理
                if (!orderDTO.getStatus().equals(OrderStatus.DEC_ING.getValue())) {
                    return "申报单状态不允许清关异常变更";
                }
                customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_FAIL.getValue(), customsInventoryDTO.getCreateTime());
                this.addExceptionSection(customsInventoryDTO.getOrderId(), mappingDTO.getId(), customsDetail, orderDTO.getCreateTime());

            } else {
                if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.FAIL.getOperation())) {
                    customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_FAIL.getValue(), customsInventoryDTO.getCreateTime());
                    this.updateStatusSection(customsInventoryDTO.getOrderId(), OrderFlowBook.order.target(orderDTO.getStatus(), OrderAction.FAIL.getOperation()), orderDTO.getCreateTime());
//                    this.clearExceptionSection(customsInventoryDTO.getOrderId(), orderDTO.getCreateTime());
                    this.updateExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO, customsDetail, orderDTO.getCreateTime());
                } else {
                    // 例如已取消，已完成等状态，不处理清关不可逆失败
                    return "申报单状态不允许清关失败变更";
                }
            }

        } else if (CustomsActionStatus.DEC_ING.getValue().equals(resultStatus)) {
            return "中间状态，不做处理";
        } else {
            return "映射状态定义不合法";
        }
        log.info("[op:OrderServiceImpl-receiveInventory] full finish, declareSn={}", receive.getDeclareOrderNo());
        return "回传成功";
    }

    private boolean determineFinalDeclareRecord(OrderDTO orderDTO, CustomsReceive receive) throws Exception {
        String agentCode = receive.getAgentCode();
        if (StringUtils.isEmpty(agentCode)) {
            log.info("申报单号:{} 回执agentCode为空 不处理", orderDTO.getDeclareOrderNo());
            return false;
        }
        return CustomsDeclareUtils.determineFinalDeclareRecord(orderDTO, agentCode, receive.getSender(), DeclareEnum.INVENTORY);
    }

    private void updateByCustomsPass(CompanyDTO ebp, CustomsInventoryDTO customsInventoryDTO, String customsStatus, String customsDetail, Long customsTime) {
        CustomsBookResVo book = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
        // 平台是否需要回传
        List<String> needCallbackCode = orderCBaseConfig.getNeedCallbackCode();
        if (CollUtil.isNotEmpty(needCallbackCode) && needCallbackCode.contains(ebp.getUnifiedCrossBroderCode()) && book != null) {
            // 关联账册是否有需要回传标签
            List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(book.getBookTag());
            List<Integer> needCallbackBookTag = orderCBaseConfig.getNeedCallbackBookTag();
            if (CollUtil.isNotEmpty(needCallbackBookTag) && CollUtil.isNotEmpty(bookTagList) && CollUtil.containsAny(bookTagList, needCallbackBookTag)) {
                customsInventoryService.updateByCustomsPass(customsInventoryDTO.getId(), customsStatus, customsDetail, customsTime, customsInventoryDTO.getCreateTime(), InventoryCheckOutStatusEnum.REGIN_WAIT.getCode());
                return;
            }
        }
        customsInventoryService.updateByCustomsPass(customsInventoryDTO.getId(), customsStatus, customsDetail, customsTime, customsInventoryDTO.getCreateTime());
    }

    @Override
    public CustomsStatusMappingDTO getCustomsStatusMappingDTO(CustomsReceive receive) {
        // Step::海关状态映射
        // 清单映射Code拼接规则 DECLARE_INVENTORY:[清关状态](:[详细状态码])    例：[Code:2600;Desc:放行]==>DECLARE_INVENTORY:800:2600
        // 支持以下几种格式:
        // 无详细状态码:清单新增成功
        // 4位审单码:[Code:2600;Desc:放行]
        // 5位审单码:[Code:13121;Desc:撤单申请退回清单]
        // 5位校验码:22001:申报企业需与企业备案时所填写名称一致
        String mappingCode = RouteActionEnum.DECLARE_INVENTORY.getCode() + ":" + receive.getCustomsStatus();
        boolean examineFlag = receive.getCustomsDetail().length() >= 5 && "Code".equals(receive.getCustomsDetail().substring(1, 5));
        if (examineFlag) {
            mappingCode += ":";
            if (receive.getCustomsDetail().length() >= 11 && ";".equals(receive.getCustomsDetail().substring(10, 11))) {
                mappingCode += receive.getCustomsDetail().substring(6, 10);
            } else {
                mappingCode += receive.getCustomsDetail().substring(6, 11);
            }
        } else {
            examineFlag = receive.getCustomsDetail().length() >= 6 && ":".equals(receive.getCustomsDetail().substring(5, 6));
            if (examineFlag) {
                mappingCode += ":";
                mappingCode += receive.getCustomsDetail().substring(0, 5);
            } else if (this.exceptionInventoryMatch(receive.getCustomsDetail(), receive.getCustomsStatus())) {
                mappingCode += ":";
                mappingCode += this.customsIntoryStatus(receive.getCustomsDetail());
            }
        }
        CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode(mappingCode);
        // Step::mapping不存在报警
        if (mappingDTO == null) {
            eventBus.post(new MappingEmptyEvent(mappingCode, JSON.toJSONString(receive)));
            mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("UN_KNOW");
        } else {
            mappingDTO = customsStatusMappingService.getFinalMappingCode(mappingDTO, receive.getCustomsDetail());
        }
        log.info("[op:OrderServiceImpl-receiveInventory] mapping finish, declareSn={}, mappingDTO={}", receive.getDeclareOrderNo(), JSON.toJSONString(mappingDTO));
        // Step::申报单及申报项状态处理
        return mappingDTO;
    }

    /**
     * 异常清单状态汇总
     *
     * @param customDetail
     * @param resultCode
     * @return
     */
    private boolean exceptionInventoryMatch(String customDetail, String resultCode) {

        if (!Objects.equals(resultCode, "-621041")) {
            return false;
        }
        if (StringUtils.isEmpty(customDetail)) {
            return false;
        }
        return INVENTORY_STATUS.matcher(customDetail).find();
    }

    /**
     * 存在已申报清单[电商企业编码：330766K00W,订单编号：XP2021102612500158691385007711,清单编号： ,清单状态：3-发送海关成功],短时间内不能重复新增申报操作;;
     *
     * @return
     */
    public String customsIntoryStatus(String exception) {

        Matcher matcher = INVENTORY_STATUS.matcher(exception);
        String code = null;
        if (matcher.find()) {
            String message = matcher.group();
            String inventoryNo = message.substring(message.indexOf("清单状态：") + 5, message.indexOf("]"));
            if (StringUtils.hasText(inventoryNo)) {
                code = inventoryNo.split("-")[0];
            }
        }
        return code;
    }


    public void handleReleaseStock(CustomsReceive receive) {
        String ebpCode = receive.getEbpCode();
        String declareOrderNo = receive.getDeclareOrderNo();
        String customsDetail = receive.getCustomsDetail();
//        CompanyDTO ebp = baseDataService.getCompanyDTOByCode(ebpCode);
        CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
        OrderDTO orderDTO = this.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        log.info("handleReleaseStock orderDTO={}", JSON.toJSONString(orderDTO));
        if (orderDTO == null) {
            log.warn("这个位置有问题，申报主单怎么查不到呢，handleReleaseStock, 申报单号：{}", declareOrderNo);
            return;
        }
        if (OrderStatus.DEC_ING.getValue().equals(orderDTO.getStatus())) {
            log.info("handleReleaseStock declare={} 申报中不释放库存", orderDTO.getDeclareOrderNo());
            return;
        }
        if (OrderStatus.DEC_SUCCESS.getValue().equals(orderDTO.getStatus())) {
            log.info("handleReleaseStock declare={} 放行不释放库存", orderDTO.getDeclareOrderNo());
            return;
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        log.info("handleReleaseStock customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        log.info("handleReleaseStock customsInventoryItemDTOList={}", JSON.toJSONString(customsInventoryItemDTOList));
        // 清单申报失败不需要释放库存了
//        orderService.handleInventoryStock(customsInventoryItemDTOList, customsDetail, orderDTO, customsInventoryDTO);
    }


    //清单申报释放库存
//    @StockVerify(methodParameters = {0, 3}, changeType = InventoryCalculationTypeEnums.REDUCE_OCCUPATION, handler = StockInventoryHandler.class)
//    public void handleInventoryStock(List<CustomsInventoryItemDTO> customsInventoryItemDTOList, String customsDetail, OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO) {
//        log.info("[OrderServiceImpl-handleInventory customsInventoryItemDTOList={} customsDetail={} orderDTO={} customsInventoryDTO={} ]"
//                , JSON.toJSONString(customsInventoryItemDTOList), customsDetail, JSON.toJSONString(orderDTO), JSON.toJSONString(customsInventoryDTO));
//        List<UpdateInventoryDTO> list = new ArrayList<>();
//        Map<Long, CustomsBookItemDTO> bookItemDTOMap = customsBookItemService.findByIdList(customsInventoryItemDTOList.stream().map(CustomsInventoryItemDTO::getBookItemId).collect(Collectors.toList()));
//        customsInventoryItemDTOList.forEach(i -> {
//            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
//            updateInventoryDTO.setCustomsBookId(customsInventoryDTO.getAccountBookId()).setBusinessNo(customsInventoryDTO.getDeclareOrderNo()).setChangeType(InventoryChangeTypeEnums.LIST_ORDER_APPLY_FAILED);
//
//            CustomsBookItemDTO customsBookItemDTO = bookItemDTOMap.get(i.getBookItemId());
//            if (customsBookItemDTO == null) {
//                return;
//            }
//            updateInventoryDTO.setDeclareUnitQfy(i.getCount()).setCustomsBookItemId(i.getBookItemId())
//                    .setProductId(customsBookItemDTO.getProductId()).setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
//            list.add(updateInventoryDTO);
//        });
//        stockInventoryService.updateInventory(list);
//    }

    @Override
    public String checkCompanyEnable(Long orderId, RouteActionEnum action) {
        OrderDTO orderDTO = this.findByIdFull(orderId);
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        if (customsOrderDTO != null) {
            if (customsOrderDTO.getPayCompanyId() == null) {
                return "支付企业参数为空";
            } else {
                CompanyDTO payCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getPayCompanyId());
                if (payCompany.getEnable() != 1) {
                    return "支付企业未启用";
                }
            }
            if (customsOrderDTO.getLogisticsCompanyId() == null) {
                return "物流企业参数为空";
            } else {
                CompanyDTO logisCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getLogisticsCompanyId());
                if (logisCompany.getEnable() != 1) {
                    return "物流企业未启用";
                }
            }
            if (customsOrderDTO.getEbpId() == null) {
                return "电商平台参数为空";
            } else {
                CompanyDTO ebpCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getEbpId());
                if (ebpCompany.getEnable() != 1) {
                    return "电商平台未启用";
                }
            }
            if (customsOrderDTO.getEbcId() == null) {
                return "电商企业参数为空";
            } else {
                CompanyDTO ebcCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getEbcId());
                if (ebcCompany.getEnable() != 1) {
                    return "电商企业未启用";
                }
            }
            if (customsOrderDTO.getAgentCompanyId() == null) {
                return "申报企业参数为空";
            } else {
                CompanyDTO agentCompany = companyService.findUnifiedCrossInfoById(customsOrderDTO.getAgentCompanyId());
                if (agentCompany.getEnable() != 1) {
                    return "申报企业未启用";
                }
            }
        }
        return null;
    }

    @Override
    public void payerInfoUpdate(OrderDTO orderDTO, String payerId, String payerName) {

    }

    /**
     * 异常单重新推送
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exceptionOrderDeclare(Long id) {
        //查订单
        OrderDTO orderDTO = this.findByIdFull(id);
        if (orderDTO == null) {
            throw new ArgsErrorException("申报单ID不存在！");
        }
        if (Objects.isNull(orderDTO.getCustomsInventorySn())) {
            throw new ArgsErrorException("未找到海关清单！");
        }
        if (orderDTO.getStatus() == 30) {
            this.updateStatusSection(id, 20, orderDTO.getCreateTime());
        }
        //查海关清单
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(id, orderDTO.getCustomsInventorySn());
        if (customsInventoryDTO.getStatus() == 100) {
            //去修改状态
            customsInventoryService.updateStatus(customsInventoryDTO.getId(), 20, customsInventoryDTO.getCreateTime());
        }
        log.info("开始推单-{}-{}", id, RouteActionEnum.DECLARE_INVENTORY.getDesc());
        this.resetDeclare(id, RouteActionEnum.DECLARE_INVENTORY);

    }

    @Override
    public void resetDeclare(Long id, RouteActionEnum action) {
        log.info("[op:OrderServiceImpl-resetDeclare] start id={}, action={}", id, action);
        OrderDTO orderDTO = this.findByIdFull(id);

        Integer orderTags = Optional.ofNullable(orderDTO).map(OrderDTO::getOrderTags).orElse(null);
        List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderTags);
        if (orderTag.contains(DeclareOrderTagEnums.HANG_UP.getCode())) {
            throw new ArgsErrorException("单据已挂起不支持重推");
        }
        if (orderDTO.getStatus().equals(OrderStatus.DEC_WAIT.getValue())) {
            this.clearExceptionSection(id, orderDTO.getCreateTime());
            orderExamineMQProducer.send(orderDTO.getSn());
            return;
        }
        if (!OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.DECLARE.getOperation())) {
            throw new ArgsErrorException("申报单状态不允许继续申报");
        }
        // Step::申报单重置异常标识
        if (RouteActionEnum.DECLARE_INVENTORY.equals(action)) {
            log.info("[op:OrderServiceImpl-resetDeclare] reset with action id={}, action={}", id, action);
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            if (customsInventoryDTO != null) {
                customsInventoryService.rePush(customsInventoryDTO.getSn(), true);
            }
        } else if (RouteActionEnum.DECLARE_ORDER.equals(action)) {
            log.info("[op:OrderServiceImpl-resetDeclare] reset with action id={}, action={}", id, action);
            CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
            if (customsOrderDTO != null) {
                customsOrderService.rePush(customsOrderDTO.getSn(), true);
            }
        } else if (RouteActionEnum.DECLARE_PAYMENT.equals(action)) {
            CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
            if (customsPaymentDTO != null) {
                customsPaymentService.rePush(customsPaymentDTO.getSn(), true);
            }
        } else if (RouteActionEnum.DECLARE_LOGISTICS.equals(action)) {
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
            if (customsLogisticsDTO != null) {
                customsLogisticsService.rePush(customsLogisticsDTO.getSn(), true);
            }
        } else {// 若未指定申报项，则检索所有失败的申报项
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            if (customsInventoryDTO != null) {
                customsInventoryService.rePush(customsInventoryDTO.getSn(), false);
            }
            CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
            if (customsOrderDTO != null) {
                customsOrderService.rePush(customsOrderDTO.getSn(), false);
            }
            CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
            if (customsPaymentDTO != null) {
                customsPaymentService.rePush(customsPaymentDTO.getSn(), false);
            }
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
            if (customsLogisticsDTO != null) {
                customsLogisticsService.rePush(customsLogisticsDTO.getSn(), false);
            }
            this.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
            orderDeclareMQProducer.send(orderDTO);
        }
    }

    @Override
    @Transactional
    public void orderBaseInfoUpdate(OrderBaseInfoSubmit submit) {
        OrderDTO old = this.findByIdFull(submit.getId());
        InventoryBaseInfoSubmit inventoryBaseInfo = submit.getInventoryBaseInfo();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(old.getId(), old.getCustomsInventorySn());
        CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        customsInventoryDTO.setBuyerIdNumber(inventoryBaseInfo.getBuyerIdNumber());
        customsInventoryDTO.setBuyerName(inventoryBaseInfo.getBuyerName());
        customsInventoryDTO.setConsigneeAddress(inventoryBaseInfo.getConsigneeAddress());
        extra.setConsigneeProvince(inventoryBaseInfo.getConsigneeProvince());
        extra.setConsigneeCity(inventoryBaseInfo.getConsigneeCity());
        extra.setConsigneeDistrict(inventoryBaseInfo.getConsigneeDistrict());
        String extraJson = JSON.toJSONString(extra);
        customsInventoryService.updateByBaseinfo(customsInventoryDTO.getId(), inventoryBaseInfo.getBuyerIdNumber(), inventoryBaseInfo.getBuyerName(), inventoryBaseInfo.getConsigneeAddress(), extraJson, customsInventoryDTO.getCreateTime());
        if (inventoryBaseInfo.getItemList() != null && 0 < inventoryBaseInfo.getItemList().size()) {
            for (InventoryItemInfoSubmit item : inventoryBaseInfo.getItemList()) {
                orderItemInfoUpdate(item);
            }
        }
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(old.getId(), old.getCustomsOrderSn());
        CustomsOrderBaseInfoSubmit customsOrderBaseInfo = submit.getCustomsOrderBaseInfo();
        CustomsOrderExtra eustomsOrderExtra = new CustomsOrderExtra();
        eustomsOrderExtra.setConsigneeCity(customsOrderBaseInfo.getConsigneeCity());
        eustomsOrderExtra.setConsigneeDistrict(customsOrderBaseInfo.getConsigneeDistrict());
        eustomsOrderExtra.setConsigneeProvince(customsOrderBaseInfo.getConsigneeProvince());
        String customsOrderExtraJson = JSON.toJSONString(eustomsOrderExtra);
        String itemJson = JSON.toJSONString(customsOrderBaseInfo.getItemList());
        customsOrderService.updateByBaseInfo(customsOrderDTO.getId(), customsOrderBaseInfo.getBuyerName(), customsOrderBaseInfo.getConsigneeAddress(), customsOrderBaseInfo.getBuyerIdNumber(), itemJson, customsOrderExtraJson, customsOrderDTO.getCreateTime());


        CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(old.getId(), old.getCustomsPaymentSn());
        CustomsPaymentBaseInfoSubmit customsPaymentBaseInfo = submit.getCustomsPaymentBaseInfo();
        customsPaymentService.updateBaseInfo(customsPaymentDTO.getId(), customsPaymentBaseInfo.getBuyerIdType(), customsPaymentBaseInfo.getBuyerIdNo(), customsPaymentBaseInfo.getBuyerName());
    }

    @Override
    public void customsOrderInfoUpdate(CustomsOrderBaseInfoSubmit customsOrderBaseInfo) {
        CustomsOrderDTO customsOrderDTO = customsOrderService.findById(Long.valueOf(customsOrderBaseInfo.getId()));
        CustomsOrderExtra eustomsOrderExtra = new CustomsOrderExtra();
        eustomsOrderExtra.setConsigneeCity(customsOrderBaseInfo.getConsigneeCity());
        eustomsOrderExtra.setConsigneeDistrict(customsOrderBaseInfo.getConsigneeDistrict());
        eustomsOrderExtra.setConsigneeProvince(customsOrderBaseInfo.getConsigneeProvince());
        String customsOrderExtraJson = JSON.toJSONString(eustomsOrderExtra);
        String itemJson = JSON.toJSONString(customsOrderBaseInfo.getItemList());
        customsOrderService.updateByBaseInfo(customsOrderDTO.getId(), customsOrderBaseInfo.getBuyerName(), customsOrderBaseInfo.getConsigneeAddress(), customsOrderBaseInfo.getBuyerIdNumber(), itemJson, customsOrderExtraJson, customsOrderDTO.getCreateTime());

    }

    @Override
    public void customsInventoryInfoUpdate(InventoryBaseInfoSubmit inventoryBaseInfoSubmit) throws ArgsErrorException {
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findById(Long.parseLong(inventoryBaseInfoSubmit.getId()));
        if (customsInventoryDTO == null) {
            throw new ArgsErrorException("清单不存在");
        }
        if (!(customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_WAIT.getValue()) || customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_FAIL.getValue())) && !customsInventoryDTO.getCustomsStatus().equals(CustomsStat.CUSTOMS_REFUSE.getValue())) {
            throw new ArgsErrorException("该状态下不支持修改清单信息");
        }
        // TODO: 2022/2/18 页面加密展示*** 展示下线修改收件人信息功能
//        CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
//        customsInventoryDTO.setBuyerIdNumber(inventoryBaseInfoSubmit.getBuyerIdNumber());
//        customsInventoryDTO.setBuyerName(inventoryBaseInfoSubmit.getBuyerName());
//        customsInventoryDTO.setConsigneeAddress(inventoryBaseInfoSubmit.getConsigneeAddress());
//        extra.setConsigneeProvince(inventoryBaseInfoSubmit.getConsigneeProvince());
//        extra.setConsigneeCity(inventoryBaseInfoSubmit.getConsigneeCity());
//        extra.setConsigneeDistrict(inventoryBaseInfoSubmit.getConsigneeDistrict());
//        String extraJson = JSON.toJSONString(extra);
//        customsInventoryService.updateByBaseinfo(customsInventoryDTO.getId(), inventoryBaseInfoSubmit.getBuyerIdNumber(), inventoryBaseInfoSubmit.getBuyerName(), inventoryBaseInfoSubmit.getConsigneeAddress(), extraJson, customsInventoryDTO.getCreateTime());
        if (inventoryBaseInfoSubmit.getItemList() != null && 0 < inventoryBaseInfoSubmit.getItemList().size()) {
            for (InventoryItemInfoSubmit item : inventoryBaseInfoSubmit.getItemList()) {
                orderItemInfoUpdate(item);
            }
        }
    }

    @Override
    public void orderItemInfoUpdate(InventoryItemInfoSubmit submit) {
        CustomsInventoryItemDTO customsInventoryItemDTO = customsInventoryService.getItemById90Days(Long.parseLong(submit.getId()));
        CustomsInventoryItemExtra extra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
        extra.setBarCode(submit.getBarCode());
        extra.setGoodsName(submit.getGoodsName());
        extra.setGrossWeight(submit.getGrossWeight());
        extra.setHsCode(submit.getHsCode());
        extra.setNetWeight(submit.getNetWeight());
        extra.setOriginCountry(submit.getOriginCountry());
        extra.setProductId(submit.getProductId());
        extra.setFirstUnit(submit.getFirstUnit());
        extra.setFirstUnitAmount(submit.getFirstUnitAmount());
        extra.setSecondUnit(submit.getSecondUnit());
        extra.setSecondUnitAmount(submit.getSecondUnitAmount());
        String extraJson = JSON.toJSONString(extra);
        customsInventoryService.updateByItemBaseInfo(Long.parseLong(submit.getId()), submit.getItemNo(), extraJson, customsInventoryItemDTO.getCreateTime());
    }


    public static void main(String[] args) {

        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyyy-MM-dd HH:mm:ss");
        DateTime dateTime = dateTimeFormatter.parseDateTime("2020-07-17 13:01:55");
        System.out.println(dateTime.isAfter(DateTime.now().minusMinutes(10)));
    }

    /**
     * 手动发起取消单推送OMS
     *
     * @param id
     */
    @Override
    public void cancelOrderPushOMS(Long id, String reason) throws ArgsInvalidException {
        try {
            log.warn("cancelOrderPushOMS 取消单入参 订单id - {},原因 - {} ", id, reason);
            OrderDTO orderDTO = this.findByIdFull(id);
            if (orderDTO == null) {
                return;
            }
            log.warn("cancelOrderPushOMS 主订单信息 - {} ", JSON.toJSONString(orderDTO));
            boolean result = iCancelRpcFacade.cancelOrder(orderDTO.getSystemGlobalSn(), reason);
            log.warn("cancelOrderPushOMS 取消单推送oms返回结果 - {} ", result);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long id) {
        this.cancel(id, null, null);
    }

    @Override
    public void operateHangUpOrder(Long id, Integer hangUpStatus) throws ArgsErrorException {
        if (LongUtil.isNone(id)) {
            throw new ArgsErrorException("ID不能为空");
        }
        OrderDTO orderDTO = orderService.findByIdFull(id);
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        //申报订单取消 忽略可用库存
        //当清单申报中、申报完成时不允许挂起
        if (Objects.equals(hangUpStatus, 1)) {
            //10:待申报;20:申报中;30:申报成功;-40:申报失败;-10:订单取消;-20:作废;',
            if (Objects.equals(customsInventoryDTO.getStatus(), CustomsActionStatus.DEC_ING.getValue()) || Objects.equals(customsInventoryDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue())) {
                throw new ArgsErrorException("申报单清单状态申报中或者申报完成时不允许挂起");
            }
        } else if (Objects.equals(hangUpStatus, 0) || Objects.isNull(hangUpStatus)) {
            if (!Objects.equals(orderDTO.getHangUpStatus(), 1)) {
                throw new ArgsErrorException("申报单没有挂起不能进行此操作");
            }
        }
        OrderDO orderDO = new OrderDO();
        orderDO.setHangUpStatus(hangUpStatus);
        if (Objects.equals(hangUpStatus, 1)) {
            orderDO.setOrderTags(DeclareOrderTagEnums.add(orderDTO.getOrderTags(), DeclareOrderTagEnums.HANG_UP));
        } else {
            orderDO.setOrderTags(DeclareOrderTagEnums.remove(orderDTO.getOrderTags(), DeclareOrderTagEnums.HANG_UP));
        }
        orderDO.setUpdateTime(new Date());
        this.updateByIdSection(id, orderDO, orderDTO.getCreateTime());
        //触发执行ES数据库
        //messageSender.sendMsg(orderDTO.getSn(), "ccs-order-dump-topic");
        orderEsDumpProducer.send(orderDTO.getSn());
    }


    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;

    /**
     * 菜鸟逆向指令取消
     * 时间紧、产品方案不完善 先实现功能为主 ps:别骂我 被逼无奈
     *
     * @param reqVo
     * @throws ArgsErrorException
     */
    @Override
    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareOrderEsQuerier.class)
    public void cancelByLink(CustomsLinkCancelReqVo reqVo) throws ArgsErrorException {
        log.info("cancelByLink reqVo={}", reqVo);
        String invtNo = reqVo.getInvtNo();
        String reqContent = reqVo.getReqContent();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByInventoryNo90Days(invtNo);
        if (Objects.isNull(customsInventoryDTO)) {
            log.info("cancelByLink invtNo={} 未查询到90天内清单", invtNo);
            throw new ArgsInvalidException("CP端查询无此单号" + invtNo + "，请联系CP处理");
        }
        OrderDTO orderDTO = this.findByIdFull(customsInventoryDTO.getOrderId());
        if (Objects.isNull(orderDTO)) {
            log.info("cancelByLink invtNo={} 未查询到申报单", invtNo);
            throw new ArgsInvalidException("CP端查询无此单号" + invtNo + "，请联系CP处理");
        }
        //申报订单取消 忽略可用库存
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffService.findByOrderId(orderDTO.getId());
        // 不需要了，2期根据erp出区通知判断是否已出区
//        if (Objects.nonNull(calloffDTO)) {
//            if (Objects.equals(calloffDTO.getCalloffType(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()) &&
//                    (Objects.equals(calloffDTO.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())
//                            || Objects.equals(calloffDTO.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode()))) {
//                log.info("cancelByLink invtNo={} 取消类型为:{} 取消状态为:{} 直接改成退货/已出区/待取消", invtNo, calloffDTO.getCalloffType(), calloffDTO.getCalloffStatus());
//                if (Objects.equals(calloffDTO.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode())) {
//                    Long orderId = calloffDTO.getOrderId();
//                    customsInventoryCancelService.deleteByOrderId(orderId);
//                }
//                customsInventoryCalloffService.updateTypeAndStatusAndExit(calloffDTO.getId(), InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), 1);
//                // fixme 更新出区状态时同时更新出区时间
//                customsInventoryService.updateExitRegionStatus(customsInventoryDTO.getId(), 1, new Date(), customsInventoryDTO.getCreateTime());
//            }
//        } else {
//            // fixme 更新出区状态时同时更新出区时间
//            customsInventoryService.updateExitRegionStatus(customsInventoryDTO.getId(), 1, new Date(), customsInventoryDTO.getCreateTime());
//        }
        orderService.cancelByLink(orderDTO, customsInventoryItemDTOList);
        calloffDTO = customsInventoryCalloffService.findByOrderId(orderDTO.getId());
        if (Objects.nonNull(calloffDTO)) {
            CustomsInventoryCalloffDTO inventoryCalloffDTO = new CustomsInventoryCalloffDTO();
            inventoryCalloffDTO.setId(calloffDTO.getId());
            inventoryCalloffDTO.setLinkCustomsContent(JSON.toJSONString(reqContent));
            Integer orderTag = calloffDTO.getOrderTag();
            if (orderTag == null) {
                inventoryCalloffDTO.setOrderTag(InventoryCalloffOrderTagEnums.XIAOJUREN.getCode());
            } else {
                inventoryCalloffDTO.setOrderTag(orderTag & InventoryCalloffOrderTagEnums.XIAOJUREN.getCode());
            }
            customsInventoryCalloffService.updateLinkCustomsContent(inventoryCalloffDTO);
        }
    }

    public void cancel(Long id, String picJson, String refundLogisticsNo) {
        this.cancel(id, picJson, refundLogisticsNo, null, null, null, null);
    }

    @Transactional(rollbackFor = Exception.class)
    public void cancel(Long id, String picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String ownerCode, String ownerName) {

        OrderDTO orderDTO = this.findByIdFull(id);
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        //申报订单取消 忽略可用库存
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        orderService.cancel(orderDTO, customsInventoryItemDTOList, id, picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
    }

    /**
     * 冗余orderDTO作为轨迹日志用
     *
     * @param orderDTO
     * @param customsInventoryItemDTOList
     * @param id
     * @param picJson
     * @param refundLogisticsNo
     * @param entityWarehouseCode
     * @param entityWarehouseName
     * @param ownerCode
     * @param ownerName
     */
    @TrackLog(infoIndex = 0, handler = CancelTrackLogParametersHandler.class, sender = TrackLogConstantMixAll.DT_OMS)
    @Transactional(rollbackFor = Exception.class)
    public void cancel(OrderDTO orderDTO, List<CustomsInventoryItemDTO> customsInventoryItemDTOList, Long id, String picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String ownerCode, String ownerName) {
        if (orderDTO == null && id == null) {
            throw new ArgsInvalidException("参数错误");
        }
        if (orderDTO == null) {
            orderDTO = this.findByIdFull(id);
        }
        orderService.cancel(customsInventoryItemDTOList, orderDTO, picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
    }

    @TrackLog(infoIndex = 0, handler = CancelTrackLogParametersHandler.class, sender = TrackLogConstantMixAll.CAINIAO)
    public void cancelByLink(OrderDTO orderDTO, List<CustomsInventoryItemDTO> customsInventoryItemDTOList) {
        if (Objects.isNull(orderDTO)) {
            return;
        }
        if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.CANCEL.getOperation())) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            //无论如何都关闭申报单
            this.updateStatusSection(orderDTO.getId(), OrderStatus.CANCEL.getValue(), OrderInternalEnum.DECLARE_CANCEL.getCode(), orderDTO.getCreateTime());
            if (customsInventoryDTO != null) {
                // 如果清单存在，申报成功，则不允许取消， 非申报中更新为 申报拦截类型
                if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    String calloffType = null;
                    if (customsInventoryDTO.getExitRegionStatus() == 0) {
                        calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
                    } else if (customsInventoryDTO.getExitRegionStatus() == 1) {
                        calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
                    }
                    customsInventoryCalloffService.upset(orderDTO.getId(), orderDTO.getCustomsInventorySn(), calloffType, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), "取消订单", null, null, null, null, null, null);
                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_FAIL);
                } else if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    customsInventoryCalloffService.upset(orderDTO.getId(), orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", null, null, null, null, null, null);
                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT);
                } else {
                    CustomsInventoryCalloffDTO calloffDto = customsInventoryCalloffService.findById(orderDTO.getId());
                    customsInventoryCalloffService.upset(orderDTO.getId(), orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", null, null, null, null, null, null);
                    // 产生占用库存 才释放占用库存
                    if (Boolean.TRUE.equals(customsInventoryDTO.getIsOccupiedStock()) || (Objects.isNull(customsInventoryDTO.getIsOccupiedStock()) && Objects.isNull(calloffDto))) {
                        orderService.doUpdateInventory(customsInventoryItemDTOList, orderDTO.getId(), customsInventoryDTO);
                        customsInventoryService.updateIsOccupiedStockSection(customsInventoryDTO.getId(), Boolean.FALSE, customsInventoryDTO.getCreateTime());
                    }
                    //创建退货单
                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_SUCCESS);
                }
            }
        }
    }


    @Override
    @TrackLog(infoIndex = 0, handler = CancelTrackLogParametersHandler.class, sender = TrackLogConstantMixAll.REFUND_WAREHOUSE)
    @Transactional(rollbackFor = Exception.class)
    public void cancelByRefundWarehouse(OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO, RefundWarehouseCancelReq reqVO) {
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("退货仓取消失败 申报单不存在");
        }
        Long id = orderDTO.getId();
        if (!OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.CANCEL.getOperation())) {
            throw new ArgsInvalidException("申报单状态不允许取消");
        }
        String submitChannel = "CHANNEL-1";
        //无论如何都关闭申报单
        this.updateStatusSection(orderDTO.getId(), OrderStatus.CANCEL.getValue(), OrderInternalEnum.DECLARE_CANCEL.getCode(), orderDTO.getCreateTime());
        if (customsInventoryDTO != null) {
            // 如果清单存在，申报成功，则不允许取消， 非申报中更新为 申报拦截类型
            if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                String calloffType = null;
                if (customsInventoryDTO.getExitRegionStatus() == 0) {
                    calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
                } else if (customsInventoryDTO.getExitRegionStatus() == 1) {
                    calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
                }
                customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), calloffType, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), "取消订单", null, reqVO.getRefundLogisticsNo(), reqVO.getEntityWarehouseCode(), reqVO.getEntityWarehouseName(), reqVO.getOwnerCode(), reqVO.getOwnerName());
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_FAIL);
                return;
            } else if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", null, reqVO.getRefundLogisticsNo(), reqVO.getEntityWarehouseCode(), reqVO.getEntityWarehouseName(), reqVO.getOwnerCode(), reqVO.getOwnerName());
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT);
                return;
            } else {
                List<CustomsInventoryCalloffDTO> calloffDtoList = customsInventoryCalloffService.findListByOrderId(id);
                customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", null, reqVO.getRefundLogisticsNo(), reqVO.getEntityWarehouseCode(), reqVO.getEntityWarehouseName(), reqVO.getOwnerCode(), reqVO.getOwnerName());
                // 产生占用库存 才释放占用库存
                if (Boolean.TRUE.equals(customsInventoryDTO.getIsOccupiedStock()) || (Objects.isNull(customsInventoryDTO.getIsOccupiedStock()) && CollectionUtils.isEmpty(calloffDtoList) && !CustomsActionStatus.DEC_WAIT.getValue().equals(customsInventoryDTO.getStatus()))) {
                    List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                    orderService.doUpdateInventory(customsInventoryItemDTOList, id, customsInventoryDTO);
                    customsInventoryService.updateIsOccupiedStockSection(customsInventoryDTO.getId(), Boolean.FALSE, customsInventoryDTO.getCreateTime());
                }
                if (CustomsActionStatus.DEC_WAIT.getValue().equals(customsInventoryDTO.getStatus())) {
                    //清单状态为待申报，上游取消清单状态改为取消申报
                    customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsInventoryDTO.getCreateTime());
                }
                TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_SUCCESS);
            }
            // 消息回传
            this.inventoryCalloffNotify(customsInventoryDTO, orderDTO, submitChannel);
        } else {
            log.warn("OrderServiceImpl -cancel 构建消息 主订单id - {} 申报单号 - {}", orderDTO.getId(), customsInventoryDTO.getDeclareOrderNo());
            // 没有清单的话，直接关单成功通知
            cancelOrderNotifyService.callOffNotify(orderDTO.getId(), customsInventoryDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, null, new Date().getTime());
        }
        CustomsOrderDTO customsOrderDO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        if (customsOrderDO != null) {
            customsOrderService.updateStatusSection(customsOrderDO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsOrderDO.getCreateTime());
        }
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
        if (customsLogisticsDTO != null) {
            customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_CANCEL);
        }
        CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
        if (customsPaymentDTO != null) {
            customsPaymentService.updateStatus(customsPaymentDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue());
        }
        if (orderDTO.getExceptionFlag()) {
            orderService.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
        }
    }

    @TrackLog(infoIndex = 0,
            handler = CancelTrackLogParametersHandler.class,
            sender = TrackLogConstantMixAll.JD
    )
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelByJdRefund(OrderDTO orderDTO, List<CustomsInventoryItemDTO> customsInventoryItemDTOList) {
        orderService.cancel(customsInventoryItemDTOList, orderDTO, null, null, null, null, null, null);
    }


    @Transactional(rollbackFor = Exception.class)
    // 清单申报取消
    public void cancel(List<CustomsInventoryItemDTO> customsInventoryItemDTOList, OrderDTO orderDTO, String picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String ownerCode, String ownerName) {
        Long id = orderDTO.getId();
        log.info("OrderServiceImpl cancel customsInventoryItemDTOList={} id={} entityWarehouseCode={} entityWarehouseName={} ownerCode={} ownerName={}", JSON.toJSONString(customsInventoryItemDTOList), id, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
        if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.CANCEL.getOperation())) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(id, orderDTO.getCustomsInventorySn());
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            String submitChannel = "CHANNEL-" + orderExtra.getSubmit().getChannel();
            //无论如何都关闭申报单
            orderService.updateStatusSection(id, OrderStatus.CANCEL.getValue(), OrderInternalEnum.DECLARE_CANCEL.getCode(), orderDTO.getCreateTime());
            if (customsInventoryDTO != null) {
                // 如果清单存在，申报成功，则不允许取消， 非申报中更新为 申报拦截类型
                if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    String calloffType = null;
                    if (customsInventoryDTO.getExitRegionStatus() == 0) {
                        calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
                    } else if (customsInventoryDTO.getExitRegionStatus() == 1) {
                        calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
                    }
                    Response res = customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), calloffType, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), "取消订单", picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
                    // TODO: 2022/6/8 将内部状态修改为取消申报 申报单改成取消订单
                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_FAIL);
//                    if (res.getCode()==0) {
//                        logComponent.logOpertion(LogCode.LOG_INVENTORY,customsInventoryDTO.getDeclareOrderNo(),customsInventoryDTO.getSn(),
//                                CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc(),
//                                CustomsActionStatus.DEC_CANCEL.getDesc(), "操作清单取消");
//                    }
//                    throw new ArgsErrorException("对应清单已放行，请操作取消单");
                    return;
                } else if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    Response res = customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
//                    if (res.getCode() == 0) {
//                        logComponent.logOpertion(LogCode.LOG_INVENTORY, customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getSn(),
//                                CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc(),
//                                CustomsActionStatus.DEC_CANCEL.getDesc(), "操作清单取消");
//                    }
//                    throw new ArgsErrorException("对应清单正在申报中，请操作取消单或者稍后再试");

                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT);
                    return;
                } else {
                    // FIXME: 2022/9/20 这里需要修复 用于后续库存判断
                    List<CustomsInventoryCalloffDTO> calloffDtoList = customsInventoryCalloffService.findListByOrderId(id);
//                    customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsInventoryDTO.getCreateTime());
                    Response res = customsInventoryCalloffService.upset(id, orderDTO.getCustomsInventorySn(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(), InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), "取消订单", picJson, refundLogisticsNo, entityWarehouseCode, entityWarehouseName, ownerCode, ownerName);
                    // 产生占用库存 才释放占用库存
                    if (Boolean.TRUE.equals(customsInventoryDTO.getIsOccupiedStock()) || (Objects.isNull(customsInventoryDTO.getIsOccupiedStock()) && CollectionUtils.isEmpty(calloffDtoList) && !CustomsActionStatus.DEC_WAIT.getValue().equals(customsInventoryDTO.getStatus()))) {
                        orderService.doUpdateInventory(customsInventoryItemDTOList, id, customsInventoryDTO);
                        customsInventoryService.updateIsOccupiedStockSection(customsInventoryDTO.getId(), Boolean.FALSE, customsInventoryDTO.getCreateTime());
//                        customsInventoryService.cleanLastDeclareTime(customsInventoryDTO);
                    }
                    if (CustomsActionStatus.DEC_WAIT.getValue().equals(customsInventoryDTO.getStatus())) {
                        //清单状态为待申报，上游取消清单状态改为取消申报
                        customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsInventoryDTO.getCreateTime());
                    }

                    //                    /**
//                     * 日志优化
//                     */
//                    if (res.getCode() == 0) {
//                        logComponent.logOpertion(LogCode.LOG_INVENTORY, customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getSn(),
//                                CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc(),
//                                CustomsActionStatus.DEC_CANCEL.getDesc(), "操作清单取消");
//                    }

                    TrackLogUtils.setTrackLogBaseInfoThreadLocal(customsInventoryDTO.getOrderId(), customsInventoryDTO.getOrderSn(), customsInventoryDTO.getDeclareOrderNo(), null, TrackLogConstantMixAll.DECLARE_ORDER_INTERCEPT_SUCCESS);
                }

                // 消息回传
                this.inventoryCalloffNotify(customsInventoryDTO, orderDTO, submitChannel);
            } else {
                log.warn("OrderServiceImpl -cancel 构建消息 主订单id - {} 申报单号 - {}", id, customsInventoryDTO.getDeclareOrderNo());
                // 没有清单的话，直接关单成功通知
                cancelOrderNotifyService.callOffNotify(id, customsInventoryDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, null, new Date().getTime());
            }
            CustomsOrderDTO customsOrderDO = customsOrderService.findByOrder(id, orderDTO.getCustomsOrderSn());
            if (customsOrderDO != null) {
                customsOrderService.updateStatusSection(customsOrderDO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsOrderDO.getCreateTime());
            }
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(id, orderDTO.getCustomsLogisticsSn());
            if (customsLogisticsDTO != null) {
                customsLogisticsService.updateStatus(customsLogisticsDTO.getId(), CustomsActionStatus.DEC_CANCEL);
            }
            CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(id, orderDTO.getCustomsPaymentSn());
            if (customsPaymentDTO != null) {
                customsPaymentService.updateStatus(customsPaymentDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue());
            }
            if (orderDTO.getExceptionFlag()) {
                orderService.clearExceptionSection(id, orderDTO.getCreateTime());
            }
//            this.updateStatusSection(orderDTO.getId(), OrderFlowBook.order.target(orderDTO.getStatus(), OrderAction.CANCEL.getOperation()), orderDTO.getCreateTime());
        } else {
            throw new ArgsErrorException("申报单状态不允许取消");
        }
    }


    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.REDUCE_OCCUPATION, handler = StockInventoryCallOffHandler.class)
    public void doUpdateInventory(List<CustomsInventoryItemDTO> customsInventoryItemDTOList, Long id, CustomsInventoryDTO customsInventoryDTO) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        customsInventoryItemDTOList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(customsInventoryDTO.getAccountBookId()).setChangeType(InventoryChangeTypeEnums.LIST_ORDER_APPLY_CANCEL).setBusinessNo(customsInventoryDTO.getDeclareOrderNo());
            //状态设置为 撤单申报完成
            CustomsInventoryItemExtra extra = JSON.parseObject(i.getExtraJson(), CustomsInventoryItemExtra.class);
            updateInventoryDTO.setCustomsBookItemId(i.getBookItemId()).setDeclareUnitQfy(i.getCount()).setProductId(extra.getProductId()).setGoodsSeqNo(extra.getGoodsSeqNo());
            list.add(updateInventoryDTO);
        });
        stockInventoryService.updateInventory(list);
    }

    private void inventoryCalloffNotify(CustomsInventoryDTO inventoryDTO, OrderDTO orderDTO, String submitChannel) {
        // 已出区是退货，不用回传取消通知
        if (Objects.equals(inventoryDTO.getExitRegionStatus(), 1)) {
            return;
        }
        String activeData = "";
        try {
            //1如果未出区,除了申报成功和申报中都发送关单成功通知
            if (!Objects.equals(inventoryDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue()) && !Objects.equals(inventoryDTO.getStatus(), CustomsActionStatus.DEC_ING.getValue())) {
                //发送关单成功通知
                Long customsTime = Optional.ofNullable(inventoryDTO).map(CustomsInventoryDTO::getLastDeclareTime).map(Date::getTime).orElse(null);
                log.warn("OrderServiceImpl - inventoryCalloffNotify 消息回执构建 - 申报单号 - {}, 主订单id - {} , 清单回执 - {}", inventoryDTO.getDeclareOrderNo(), orderDTO.getId(), inventoryDTO.getCustomsDetail());
                Date date = customsTime == null ? new Date() : new Date(customsTime);
                activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.CLOSE_SUCCESS.getCode().toString(), inventoryDTO.getCustomsDetail(), date));
                cancelOrderNotifyService.callOffNotify(orderDTO.getId(), inventoryDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, inventoryDTO.getCustomsDetail(), customsTime);
            }
            //2.如果未出区，申报中，海关回执异常，此时可能是多种原因，重推的时候也可能回执正常，所以此处不做立马回传处理
//        else if (Objects.equals(inventoryDTO.getStatus(), CustomsActionStatus.DEC_ING.getValue()) && orderDTO.getExceptionFlag()) {
//            //发送关单成功通知
//            cancelOrderNotifyService.callOffNotify(orderDTO.getId(), inventoryDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, inventoryDTO.getCustomsDetail(), inventoryDTO.getLastCustomsTime().getTime());
//        }
        } catch (Exception ex) {
            log.info("申报单号: {} , 取消单发送消息通知异常 :, MessageType={} , subscribeList=[{}], activeData={}", inventoryDTO.getDeclareOrderNo(), MessageType.ORDER_CUSTOMS_CANCEL.getCode(), submitChannel, activeData);
            log.error("申报单号: {} , 取消单发送消息通知异常 :{} ", inventoryDTO.getDeclareOrderNo(), ex.getMessage(), ex);
        }
    }

    /**
     * 发送关单
     *
     * @param orderDTO
     * @param customsInventoryDTO
     * @param submitChannel
     */
    private void sendOffOrder(OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO, String submitChannel) {
        log.warn("[createMessage] 订单取消且是未出区----关单成功");
        //订单取消且是未出区----关单成功
        cancelOrderNotifyService.callOffNotify(orderDTO.getId(), customsInventoryDTO.getDeclareOrderNo(), submitChannel, InventoryCalloffStatus.CLOSE_SUCCESS, customsInventoryDTO.getCustomsDetail(), customsInventoryDTO.getLastCustomsTime().getTime());
//        messageService.createMessage(
//                MessageType.ORDER_CUSTOMS_CANCEL,
//                subscribeList,
//                customsInventoryDTO.getDeclareOrderNo(),
//                JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.CLOSE_SUCCESS.getDesc(), customsInventoryDTO.getCustomsDetail(), customsInventoryDTO.getLastCustomsTime())),
//                "");
    }

    @Override
    public Boolean cancelByGlobalSn(String systemGlobalSn, String picJson, String refundLogisticsNo) {
        OrderDTO orderDTO = this.findByGlobalSnFull(systemGlobalSn);
        if (OrderStatus.CANCEL.getValue() == orderDTO.getStatus()) {
            return true;
        } else {
            try {
                this.cancel(orderDTO.getId(), picJson, refundLogisticsNo);
                log.info("[op:OrderServiceImpl-cancelByGlobalSn] success, GlobalSn={}", systemGlobalSn);
                return true;
            } catch (BaseBusinessException e) {
                log.error("[op:OrderServiceImpl-cancelByGlobalSn] exception, GlobalSn={}, cause={}", systemGlobalSn, e.getErrorMessage(), e);
                return false;
            } catch (Exception e) {
                log.error("[op:OrderServiceImpl-cancelByGlobalSn] exception, GlobalSn={}, cause={}", systemGlobalSn, e.getMessage(), e);
                return false;
            }
        }
    }

    /**
     * 目前OMS取消单调用这个方法
     *
     * @param cancelOrderSubmit
     * @return
     * @throws ArgsInvalidException 异常不再返回false 而是直接改成异常抛出
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelByGlobalSnV2(CancelOrderSubmit cancelOrderSubmit) throws ArgsInvalidException {
        log.info("OrderServiceImpl cancelByGlobalSn GlobalSn={} submit={}", cancelOrderSubmit.getSystemGlobalSn(), JSON.toJSONString(cancelOrderSubmit));
        OrderDTO orderDTO = this.findByGlobalSnFull(cancelOrderSubmit.getSystemGlobalSn());
        if (orderDTO == null) {
            log.info("OrderServiceImpl cancelByGlobalSn GlobalSn={} 申报单信息不存在", cancelOrderSubmit.getSystemGlobalSn());
            throw new ArgsInvalidException("申报单信息不存在");
        }
        if (OrderStatus.CANCEL.getValue().equals(orderDTO.getStatus())) {
            log.info("OrderServiceImpl cancelByGlobalSn GlobalSn={} 状态为取消", cancelOrderSubmit.getSystemGlobalSn());
            return true;
        } else {
            try {
                log.info("OrderServiceImpl cancelByGlobalSn success, GlobalSn={} ", cancelOrderSubmit.getSystemGlobalSn());
                orderService.cancel(orderDTO.getId(), cancelOrderSubmit.getPicJson(), cancelOrderSubmit.getRefundLogisticsNo(), cancelOrderSubmit.getEntityWarehouseCode(), cancelOrderSubmit.getEntityWarehouseName(), cancelOrderSubmit.getOwnerCode(), cancelOrderSubmit.getOwnerName());
                return true;
            } catch (BaseBusinessException e) {
                log.error("[op:OrderServiceImpl-cancelByGlobalSn] exception, GlobalSn={}, cause={}", cancelOrderSubmit.getSystemGlobalSn(), e.getErrorMessage(), e);
                throw new ArgsInvalidException(e.getErrorMessage());
            } catch (Exception e) {
                log.error("[op:OrderServiceImpl-cancelByGlobalSn] exception, GlobalSn={}, cause={}", cancelOrderSubmit.getSystemGlobalSn(), e.getMessage(), e);
                throw new ArgsInvalidException(e.getMessage());
            }
        }
    }

    @Override
    public void cacelByGlobalSn(String SystemGlobalSn) {
        OrderDTO orderDTO = this.findByGlobalSnFull(SystemGlobalSn);
        if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.CANCEL.getOperation())) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            if (customsInventoryDTO != null) {
                // 如果清单存在，申报中，或申报成功，则不允许取消
                if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    throw new ArgsErrorException("对应清单已放行，请操作撤单或退货");
                } else if (customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    throw new ArgsErrorException("对应清单正在申报中，请稍后再试");
                } else {
                    customsInventoryService.updateStatusSection(customsInventoryDTO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsInventoryDTO.getCreateTime());
//                    logComponent.logOpertion(LogCode.LOG_INVENTORY, customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getSn(),
//                            CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc(),
//                            CustomsActionStatus.DEC_CANCEL.getDesc(), "操作清单取消");
                    CustomsOrderDTO customsOrderDO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
                    if (customsOrderDO != null) {
                        customsOrderService.updateStatusSection(customsOrderDO.getId(), CustomsActionStatus.DEC_CANCEL.getValue(), customsOrderDO.getCreateTime());
                    }
                }
            }
            if (orderDTO.getExceptionFlag()) {
                this.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
            }
//            this.updateStatusSection(orderDTO.getId(), OrderFlowBook.order.target(orderDTO.getStatus(), OrderAction.CANCEL.getOperation()), orderDTO.getCreateTime());

        } else {
            throw new ArgsErrorException("申报单状态不允许取消");
        }
    }

    @Override
    public List<OrderDTO> listOrder(Long ebpId, List<Integer> statusList, Integer page, Integer queryDays) {
        List<Long> ebpIdList = new ArrayList<>();
        ebpIdList.add(ebpId);
        return this.getOrderDTOS(ebpIdList, statusList, page, queryDays);
    }

    @Override
    public List<OrderDTO> listOrder(Long ebpId, Integer status, Integer page, Integer queryDays) {
        List<Long> ebpIdList = new ArrayList<>();
        ebpIdList.add(ebpId);
        return this.listOrder(ebpIdList, status, page, queryDays);
    }

    @Override
    public List<OrderDTO> listOrder(List<Long> ebpIdList, Integer status, Integer page, Integer queryDays) {
        List<Integer> statusList = new ArrayList<>();
        if (status != null) {
            statusList.add(status);
        }
        return getOrderDTOS(ebpIdList, statusList, page, queryDays);
    }

    @Override
    public List<OrderDTO> listOrder(List<Long> ebpIdList, List<Integer> statusList, Integer page, Integer queryDays) {

        return getOrderDTOS(ebpIdList, statusList, page, queryDays);
    }

    private List<OrderDTO> getOrderDTOS(List<Long> ebpIdList, List<Integer> statusList, Integer page, Integer queryDays) {
        RowBounds rowBounds = new RowBounds(0, page);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(queryDays).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(ebpIdList)) {
            criteria.andIn("ebpId", ebpIdList);
        }
        if (!CollectionUtils.isEmpty(statusList)) {
            criteria.andIn("status", statusList);
        }
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        example.setOrderByClause("create_time DESC");
        List<OrderDO> orderDOList = orderMapper.selectByExampleAndRowBounds(example, rowBounds);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }


    @Override
    public List<OrderDTO> listOrderExcludeEbpId(List<Long> ebpIdList, Integer status, Integer page, Integer queryDays) {
        RowBounds rowBounds = new RowBounds(0, page);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(queryDays).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        if (!CollectionUtils.isEmpty(ebpIdList)) {
            criteria.andNotIn("ebpId", ebpIdList);
        }
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<OrderDO> orderDOList = orderMapper.selectByExampleAndRowBounds(example, rowBounds);
        return orderDOList.stream().map(OrderBuilder::buildDTO).collect(Collectors.toList());
    }


    @Resource
    private CustomsStatusMappingService mappingService;

    @Resource
    private ExceptionService exceptionService;


    @Override
    public ListVO<OrderDTO> pagingES(OrderSearch search) {
        try {
            List<Integer> receiptIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(search.getCustomsStatus()) || !CollectionUtils.isEmpty(search.getExceptionTypeId())) {
                List<CustomsStatusMappingDTO> mappingDTO = mappingService.findByCustomsCodeAndException(search.getCustomsStatus(), search.getExceptionTypeId());
                if (!CollectionUtils.isEmpty(mappingDTO)) {
                    receiptIds = mappingDTO.stream().map(m -> m.getId().intValue()).distinct().collect(Collectors.toList());
                }
            }
            if (!CollectionUtils.isEmpty(search.getReceiptDescribe())) {
                List<Long> receiptDescribe = search.getReceiptDescribe();
                List<Integer> integerList = receiptDescribe.stream().map(m -> m.intValue()).distinct().collect(Collectors.toList());
                receiptIds.addAll(integerList);
            }
            if (!CollectionUtils.isEmpty(receiptIds)) {
                search.setExceptionTypes(receiptIds);
            }
            Page<OrderEsDO> page = orderEsDao.paging(search);
            ListVO<OrderDTO> orderDTOListVO = new ListVO<>();
            PageResult pageResult = new PageResult();
            pageResult.setTotalCount((int) page.getTotalElements());
            pageResult.setTotalPage(page.getTotalPages());
            pageResult.setCurrentPage(search.getCurrentPage());
            pageResult.setPageSize(search.getPageSize());
            orderDTOListVO.setPage(pageResult);
            List<OrderDTO> orderDTOList = new ArrayList<>();
            for (OrderEsDO orderEsDO : page.getContent()) {
                OrderDTO orderDTO = this.findByIdSection(Long.parseLong(orderEsDO.getId()), new DateTime(orderEsDO.getCreateTime()).toDate());
                if (orderDTO != null) {
                    CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOById(orderDTO.getExceptionType().longValue());
                    if (mappingDTO != null) {
                        if (Objects.nonNull(mappingDTO.getReceiptExplain())) {
                            orderDTO.setReceiptDescribe(mappingDTO.getReceiptExplain());
                        } else {
                            orderDTO.setReceiptDescribe(mappingDTO.getNote());
                        }
                        if (Objects.nonNull(mappingDTO.getNote())) {
                            orderDTO.setCustomsReceipt(mappingDTO.getNote());
                        }
                        // 这里通用特殊处理下，回执映射常规的异常描述
                        if (Objects.equals(RouteActionEnum.COMMON.getCode(), mappingDTO.getAction())) {
                            String exceptionDes = orderDTO.getExceptionDetail();
                            if (StringUtils.hasText(exceptionDes) && Objects.equals(true, orderDTO.getExceptionFlag())) {
                                orderDTO.setCustomsReceipt(orderDTO.getCustomsReceipt() + ";[" + exceptionDes + "]");
                            }
                        }
                        if (Objects.nonNull(mappingDTO.getExceptionId())) {
                            ExceptionDTO exceptionDTO = baseDataService.getExceptionDTOById(mappingDTO.getExceptionId());
                            ExceptionTypeDTO typeDTO = new ExceptionTypeDTO();
                            typeDTO.setExceptionName(exceptionDTO.getExceptionName());
                            typeDTO.setDescribe(exceptionDTO.getExceptionDescribe() + "_" + exceptionDTO.getHandlePropose());
                            orderDTO.setExceptionTypeStr(typeDTO);

                        }
                    }
                    orderDTO.setTenantOuterId(orderEsDO.getTenantOuterId());
                    orderDTOList.add(orderDTO);
                }
            }
            orderDTOListVO.setDataList(orderDTOList);
            return orderDTOListVO;
        } catch (Exception e) {
            log.error("[op:OrderServiceImpl-paging] exception={}", e.getMessage(), e);
            throw e;
        }
    }


    @Override
    public void orderMonitor(JdMonitorParamDto jdMonitorParamDto) {
        orderEsDao.queryForList(jdMonitorParamDto);
    }

    @Override
    public void dumpBySn(String sn) {
        try {
            OrderDTO orderDTO = this.findBySnSection(sn);
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
            orderEsDao.esSave(orderDTO, customsInventoryDTO, customsOrderDTO);
        } catch (Exception e) {
            log.info("[op:OrderServiceImpl-dumpById] exception,sn={},message={}", sn, e.getMessage(), e);
        }
    }

    @Override
    public void dumpBySn(List<String> snList) {

        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        try {
            // 申报单SN 列表
            List<OrderDTO> orderDTO = this.findBySnSection(snList);
            // 清单SN列表
            List<String> inventorySnList = orderDTO.stream().map(OrderDTO::getCustomsInventorySn).collect(Collectors.toList());
            List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.findBySnList(inventorySnList);
            // 海关订单SN列表
            List<String> customsOrderList = orderDTO.stream().map(OrderDTO::getCustomsOrderSn).collect(Collectors.toList());
            List<CustomsOrderDTO> customsOrderDTOList = customsOrderService.findBySnList(customsOrderList);
            // 包装申报单DTO
            List<DeclareOrderWrapDTO> orderWrapDTOList = getDeclareOrderWrap(orderDTO, inventoryDTOList, customsOrderDTOList);
            // ES批量保存
            orderEsDao.esSaveBatch(orderWrapDTOList);
        } catch (Exception e) {
            log.info("[op:OrderServiceImpl-dumpById] exception,sn={},message={}", snList, e.getMessage(), e);
        }
    }

    private List<DeclareOrderWrapDTO> getDeclareOrderWrap(List<OrderDTO> orderDTOList, List<CustomsInventoryDTO> inventoryDTOList, List<CustomsOrderDTO> customsOrderDTOList) {

        if (CollectionUtils.isEmpty(orderDTOList)) {
            return null;
        }
        Map<String, CustomsInventoryDTO> inventoryMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(inventoryDTOList)) {
            inventoryMap = inventoryDTOList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getSn, Function.identity(), (v1, v2) -> v1));
        }
        Map<String, CustomsOrderDTO> customsOrderDTOMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(customsOrderDTOList)) {
            customsOrderDTOMap = customsOrderDTOList.stream().collect(Collectors.toMap(CustomsOrderDTO::getSn, Function.identity(), (v1, v2) -> v1));
        }
        Map<String, CustomsInventoryDTO> finalInventoryMap = inventoryMap;
        Map<String, CustomsOrderDTO> finalCustomsOrderDTOMap = customsOrderDTOMap;
        return orderDTOList.stream().map(z -> {
            DeclareOrderWrapDTO orderWrapDTO = new DeclareOrderWrapDTO();
            orderWrapDTO.setOrderDTO(z);
            CustomsInventoryDTO inventoryDTO = finalInventoryMap.get(z.getCustomsInventorySn());
            if (Objects.nonNull(inventoryDTO)) {
                orderWrapDTO.setCustomsInventoryDTO(inventoryDTO);
            }
            CustomsOrderDTO customsOrderDTO = finalCustomsOrderDTOMap.get(z.getCustomsInventorySn());
            if (Objects.nonNull(customsOrderDTO)) {
                orderWrapDTO.setCustomsOrderDTO(customsOrderDTO);
            }
            return orderWrapDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void deliver(OrderDTO orderDTO, String logisticsNo, String depotOrderSn, BigDecimal weight, Long shipmentTime) {
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        customsInventoryService.updateLogisticsSection(customsInventoryDTO.getId(), logisticsNo, customsInventoryDTO.getCreateTime());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        DeliverActiveInfo activeInfo = new DeliverActiveInfo();
        CompanyDTO ebp = companyService.findUnifiedCrossInfoById(orderDTO.getEbpId());
        activeInfo.setEbpCode(ebp.getCode());
        activeInfo.setOutOrderNo(orderDTO.getOutOrderNo());
        activeInfo.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        activeInfo.setDepotOrderNo(depotOrderSn);
        activeInfo.setLogisticsNo(logisticsNo);
        activeInfo.setWeight(weight);
        activeInfo.setShipmentTime(shipmentTime);
        messageService.createMessage(MessageType.ORDER_DELIVER, Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel()), orderDTO.getDeclareOrderNo(), JSON.toJSONString(activeInfo), "");

    }

    @Override
    @GetMapping("/es/book/deal")
    public void esAccountBookIdDeal() {
        orderEsHandlerService.orderEsHandler();
    }

    @Resource
    private OrderOwnerMappingService orderOwnerMappingService;

    @Override
    public List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode, String entityWarehouseCode) {
        return getRefundOrderByOwnerCode(ownerCode, entityWarehouseCode, null, null);
    }

    @Override
    public List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd) {
        if (ownerCode == null) {
            throw new ArgsErrorException("参数异常:ownerCode未传");
        }
        if (entityWarehouseCode == null) {
            throw new ArgsErrorException("参数异常:entityWarehouseCode");
        }
        try {
            List<CustomsCancelOrderMappingDTO> list = orderOwnerMappingService.getRefundOrderByOwnerCode(ownerCode, entityWarehouseCode, timeBegin, timeEnd);
            log.info("getRefundOrderByOwnerCode list= {}", JSON.toJSONString(list));
            return list;
        } catch (Exception e) {
            log.error("getRefundOrderByOwnerCode error={}", e.getMessage());
        }
        return null;
    }

    @Override
    public List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode, String entityWarehouseCode) {
        return getCallBackOrderByOwnerCode(ownerCode, entityWarehouseCode, null, null);
    }

    @Override
    public List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd) {
        if (ownerCode == null) {
            throw new ArgsErrorException("参数异常:ownerCode未传");
        }
        if (entityWarehouseCode == null) {
            throw new ArgsErrorException("参数异常:entityWarehouseCode");
        }
        try {
            List<CustomsCancelOrderMappingDTO> list = orderOwnerMappingService.getCallBackOrderByOwnerCode(ownerCode, entityWarehouseCode, timeBegin, timeEnd);
            log.info("getCallBackOrderByOwnerCode list= {}", JSON.toJSONString(list));
            return list;
        } catch (Exception e) {
            log.error("getRefundOrderByOwnerCode error={}", e.getMessage());
        }
        return null;
    }

    @Override
    public String getOrderSubmit(Long id) throws ArgsErrorException {
        OrderDTO orderDTO = this.findByIdFull(id);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("getOrderSubmit orderDTO为空");
        }
        return orderDTO.getExtraJson();
    }

    @Override
    public List<String> getOrderSubmitOrginMsg(List<Long> idList) {

        return idList.stream().map(z -> {
            OrderDTO orderDTO = this.findByIdFull(z);
            if (Objects.isNull(orderDTO)) {
                throw new ArgsInvalidException("申报单ID:[" + z + "],getOrderSubmit orderDTO为空");
            }
            return orderDTO.getExtraJson();
        }).collect(Collectors.toList());
    }

    @Override
    public void dumpEsTaxPrice(OrderTotalTaxEsDTO totalTaxEsDTO) throws IOException {
        OrderDTO orderDTO = this.findBySnSection(totalTaxEsDTO.getOrderSn());
        if (Objects.isNull(orderDTO)) {
            return;
        }
        OrderEsDO orderEsDO = new OrderEsDO();
        orderEsDO.setId(orderDTO.getId().toString());
        orderEsDO.setTaxPrice(totalTaxEsDTO.getTaxPrice());
        log.info("dumpEsTaxPrice orderEsDO={}", JSON.toJSONString(orderEsDO));
        orderEsDao.updateTaxPrice(orderEsDO);
    }

    @Override
    @GetMapping("/order/updateLastDeclareTime")
    public void updateLastDeclareTime(String sn, String action) {
        if (StringUtils.isEmpty(sn)) {
            throw new ArgsErrorException("updateLastDeclareTime sn为空");
        }
        List<String> actions = Splitter.on(",").splitToList(action);
        for (String type : actions) {
            if (type.equals(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
                customsInventoryService.updateLastDeclareTime(sn);
            }
            if (type.equals(RouteActionEnum.DECLARE_ORDER.getCode())) {
                customsOrderService.updateLastDeclareTime(sn);
            }
            if (type.equals(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
                customsLogisticsService.updateLastDeclareTime(sn);
            }
        }
    }

    @Override
    @GetMapping("/order/syncEsData")
    public void syncEsData(String sn) throws ArgsErrorException {
        if (StringUtils.isEmpty(sn)) {
            throw new ArgsErrorException("sn 为空");
        }
        orderEsDumpProducer.send(sn);
    }

    @Override
    public void updateOrderInternalStatus(Long orderId, String internalStatus) {
        this.updateOrderInternalStatus(orderId, null, internalStatus);
    }

    @Override
    public void updateOrderInternalStatus(Long orderId, String orderSn, String internalStatus) {
        log.info("updateOrderInternalStatus orderId={},orderSn={},internalStatus={}", orderId, orderSn, internalStatus);
        if (Objects.isNull(orderId) && Objects.isNull(orderSn)) {
            return;
        }
        OrderDTO orderDTO = null;
        if (Objects.nonNull(orderSn) && Objects.nonNull(orderId)) {
            String dateStr = orderSn.substring(2, 12);
            DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
            Date date = dateTimeFormatter.parseDateTime(dateStr).toDate();
            String quarter = DateHelper.getQuarter(date);
            orderBaseService.updateOrderInternalStatusByQuarter(orderId, internalStatus, quarter);
            return;
        } else if (Objects.nonNull(orderSn)) {
            orderDTO = this.findBySnSection(orderSn);
        } else {
            orderDTO = this.findByIdFull(orderId);
        }
        if (Objects.isNull(orderDTO)) {
            return;
        }
        this.updateOrderInternalStatus(orderDTO, internalStatus);
    }

    @Override
    public void updateOrderInternalStatus(OrderDTO orderDTO, String internalStatus) {
        orderBaseService.updateOrderInternalStatus(orderDTO, internalStatus);
    }

    @Override
    public void updateOrderDeclareRecord(OrderDTO orderDTO) {
        orderBaseService.updateOrderDeclareRecord(orderDTO);
    }

    @Override
    public void updateOrderInternalStatusAndDeclareRecord(OrderDTO orderDTO, String internalStatus) {
        orderBaseService.updateOrderInternalStatusAndDeclareRecord(orderDTO, internalStatus);
    }

    @Override
    public void updateOrderInternalStatusBySnList(List<String> snList, String internalStatus) {
        orderBaseService.updateOrderInternalStatusBySnList(snList, internalStatus);
    }

    /**
     * pdd 数据解密
     *
     * @param orderSn pdd 外部单号
     * @param data    密文
     * @return 明文
     */
    @Override
    @PostMapping("/pddDecrypt")
    @TenantHttpMethod(type = TenantHttpType.HEADER)
    public Map<String, String> pddDecrypt(String orderSn, List<String> data, String pati) {
        log.info("origin: orderSn = {}, data = {}", orderSn, data);
        DecryptSubmit submit = new DecryptSubmit();
        submit.setOrderSn(orderSn);
        submit.setDataList(data);
        String PDD_HOST = orderCBaseConfig.getPDD_HOST();
        HttpRequest httpRequest = HttpRequest.post(PDD_HOST + "/pdd/controller/declare/decryptBatchData").header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr()).header("Content-Type", HttpRequest.CONTENT_TYPE_JSON).header("X-PDD-Pati", pati == null ? "" : pati).send(JSON.toJSONString(submit));
        if (httpRequest.ok()) {
            List<DecodeAndEncodeResVO> response = JSON.parseArray(httpRequest.body(), DecodeAndEncodeResVO.class);
            log.info("[op:pdd data decrypt] receive={}", response);
            Map<String, String> result = new HashMap<>();
            for (DecodeAndEncodeResVO resVO : response) {
                result.put(resVO.getOrigin(), resVO.getTarget());
            }
            return result;
        } else {
            throw new RuntimeException("网络请求失败");
        }
    }

    /**
     * pdd 数据加密
     *
     * @param orderSn pdd 外部单号
     * @param data    明文
     * @param type    敏感信息类型 id: 身份证号, phone: 手机号码, simple: 昵称, 地址等
     * @return 密文
     */
    @Override
    public Map<String, String> pddEncrypt(String orderSn, List<String> data, String type) {
        log.info("origin: orderSn = {}, data = {}, type = {}", orderSn, data, type);
        EncryptSubmit submit = new EncryptSubmit();
        submit.setOrderSn(orderSn);
        submit.setDataList(data);
        submit.setType(type);
        String PDD_HOST = orderCBaseConfig.getPDD_HOST();
        HttpRequest request = HttpRequest.post(PDD_HOST + "/pdd/controller/declare/encryptBatchData").header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr()).header("Content-Type", HttpRequest.CONTENT_TYPE_JSON).send(JSON.toJSONString(submit));
        if (request.ok()) {
            List<DecodeAndEncodeResVO> response = JSON.parseArray(request.body(), DecodeAndEncodeResVO.class);
            log.info("[op:pdd data encrypt] receive={}", response);
            Map<String, String> result = new HashMap<>();
            for (DecodeAndEncodeResVO resVO : response) {
                result.put(resVO.getOrigin(), resVO.getTarget());
            }
            return result;
        } else {
            throw new RuntimeException("网络请求失败");
        }
    }

    public ConsigneeInfoRes getConsigneeInfo(String orderSn, String pati) {
        OrderDTO orderDTO = this.findBySnSection(orderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("未找到匹配订单");
        }
        ConsigneeInfoRes result = new ConsigneeInfoRes();
        result.setId(orderDTO.getId());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        OrderSubmit submit = orderExtra.getSubmit();
        //从extraJson中取出收件人信息
        String consigneeName = submit.getConsigneeName();
        String consigneeAddress = submit.getConsigneeAddress();
        String consigneeTel = submit.getConsigneeTel();
        String buyerName = submit.getBuyerName();
        String buyerTelNumber = submit.getBuyerTelNumber();
        //如果是拼多多的单子需要解密
        Long pddEbpId = orderCBaseConfig.getPddEbpId();
        if (orderDTO.getEbpId().equals(pddEbpId)) {
            log.info("ppd 解密处理");
            List<String> strList = new ArrayList<>();
            strList.add(consigneeName);
//            strList.add(result.getBuyerName());
            strList.add(consigneeAddress);
            strList.add(consigneeTel);
            strList.add(buyerTelNumber);
            Map<String, String> decodeMap = orderService.pddDecrypt(orderDTO.getOutOrderNo(), strList, pati);
            consigneeName = decodeMap.get(consigneeName);
            consigneeAddress = decodeMap.get(consigneeAddress);
            consigneeTel = decodeMap.get(consigneeTel);
            buyerName = null;
            buyerTelNumber = decodeMap.get(buyerTelNumber);
            //目前先写死 解决pdd无法解密订购人信息问题
            result.setOrigin("pdd");
        }
        CompanyDTO ebpDTO = baseDataService.getCompanyDTOById(orderDTO.getEbpId());
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpDTO.getCode(), orderDTO.getExtraJson())) {
            OrderSensitivePlainText sensitivePlainText = Objects.isNull(submit.getSensitivePlainText()) ? new OrderSensitivePlainText() : submit.getSensitivePlainText();
            result.setEncryptName(consigneeName);
            result.setEncryptAddress(consigneeAddress);
            result.setEncryptTelephone(consigneeTel);
            result.setEncryptBuyerName(buyerName);
            result.setEncryptBuyerTelephone(buyerTelNumber);

            consigneeName = sensitivePlainText.getConsigneeName();
            consigneeTel = sensitivePlainText.getConsigneeTel();
            consigneeAddress = sensitivePlainText.getConsigneeAddress();
            buyerName = sensitivePlainText.getBuyerName();
            buyerTelNumber = sensitivePlainText.getBuyerTelNumber();
        }
        result.setName(consigneeName);
        result.setAddress(consigneeAddress);
        result.setTelephone(consigneeTel);
        result.setBuyerName(buyerName);
        result.setBuyerTelephone(buyerTelNumber);
        return result;
    }

    @Override
    public void updateConsigneeAndBuyerInfo(ConsigneeInfoParam param) {
        log.info("updateConsigneeAndBuyerInfo param={}", JSON.toJSONString(param));
        String orderSn = param.getOrderSn();
        OrderDTO orderDTO = this.findBySnSection(orderSn);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("未找到匹配订单");
        }
        String address = param.getAddress();
        String name = param.getName();
        String telephone = param.getTelephone();
        String buyerName = param.getBuyerName();
        String buyerTelephone = param.getBuyerTelephone();
        //如果是拼多多的单子需要加密
        Long pddEbpId = orderCBaseConfig.getPddEbpId();
        if (orderDTO.getEbpId().equals(pddEbpId)) {
            log.info("updateConsigneeAndBuyerInfo pdd加密处理 orderSn={}", orderSn);
            List<String> simpleDecode = new ArrayList<>();
            simpleDecode.add(name);
            simpleDecode.add(address);
            if (Objects.nonNull(buyerName) && StringUtils.hasText(buyerName)) {
                simpleDecode.add(buyerName);
            }
            Map<String, String> simpleEncodeMap = this.pddEncrypt(orderDTO.getOutOrderNo(), simpleDecode, "simple");
            if (Objects.isNull(simpleEncodeMap)) {
                throw new ArgsErrorException("获取拼多多加密信息失败");
            }
            name = simpleEncodeMap.get(name);
            address = simpleEncodeMap.get(address);
            if (Objects.nonNull(buyerName) && StringUtils.hasText(buyerName)) {
                buyerName = simpleEncodeMap.get(buyerName);
            }
            List<String> phoneList = new ArrayList<>();
            phoneList.add(telephone);
            phoneList.add(buyerTelephone);
            Map<String, String> phoneEncodeMap = this.pddEncrypt(orderDTO.getOutOrderNo(), phoneList, "phone");
            if (Objects.isNull(phoneEncodeMap)) {
                throw new ArgsErrorException("获取拼多多加密信息失败");
            }
            telephone = phoneEncodeMap.get(telephone);
            buyerTelephone = phoneEncodeMap.get(buyerTelephone);
        }
        //支付单、运单、清单 数据更新
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        OrderSubmit submit = orderExtra.getSubmit();
        CompanyDTO ebpDTO = baseDataService.getCompanyDTOById(orderDTO.getEbpId());
        if (byteDanceDeclareService.isDeclareWithinCloud(ebpDTO.getCode(), orderDTO.getExtraJson())) {
            OrderSensitivePlainText sensitivePlainText = Objects.isNull(submit.getSensitivePlainText()) ? new OrderSensitivePlainText() : submit.getSensitivePlainText();
            sensitivePlainText.setConsigneeName(name);
            sensitivePlainText.setConsigneeAddress(address);
            sensitivePlainText.setConsigneeTel(telephone);
            sensitivePlainText.setBuyerName(buyerName);
            sensitivePlainText.setBuyerTelNumber(buyerTelephone);
            submit.setSensitivePlainText(sensitivePlainText);
        } else {
            submit.setConsigneeName(name);
            submit.setConsigneeAddress(address);
            submit.setConsigneeTel(telephone);
            if (Objects.nonNull(buyerName) && StringUtils.hasText(buyerName)) {
                submit.setBuyerName(buyerName);
            }
            submit.setBuyerTelNumber(buyerTelephone);
        }
        OrderSubmitDto submitReqVo = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        this.submit(submitReqVo);
        log.info("reSubmit 重推报文 submit={}", JSON.toJSONString(submitReqVo));
    }

    private boolean isByteDanceCloud(Long ebpId, Boolean isEncrypt) {
        String BYTE_DANCE_EBP_CODES = orderCBaseConfig.getBYTE_DANCE_EBP_CODES();
        if (StringUtil.isEmpty(BYTE_DANCE_EBP_CODES)) {
            return false;
        }
        List<String> ebpCodeList = Splitter.on(",").splitToList(BYTE_DANCE_EBP_CODES);
        CompanyDTO ebpCompanyDTO = baseDataService.getCompanyDTOById(ebpId);
        if (Objects.isNull(ebpCompanyDTO)) {
            return false;
        }
        return ebpCodeList.contains(ebpCompanyDTO.getCode()) && Boolean.TRUE.equals(isEncrypt);
    }

//    @Override
//    public void logicDeleteBySn(String sn) {
//        if (StringUtils.isEmpty(sn)) {
//            log.error("orderService- deleteBySn sn为空");
//            return;
//        }
//        String dateStr = sn.substring(2, 12);
//        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
//        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
//        TimeRangeParam timeRangeParam = new TimeRangeParam();
//        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
//        //timeRangeParam.setEndDate(new DateTime(createTime).millisOfDay().withMaximumValue().toDate());
//        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
//        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
//        example.and(example.createCriteria().andEqualTo("sn", sn).andEqualTo("deleted", 0));
//        OrderDO orderDO = new OrderDO();
//        orderDO.setDeleted(true);
//        orderMapper.updateByExampleSelective(orderDO, example);
//        orderEsDumpProducer.send(sn);
//    }

    @Override
    public void updateBySn(OrderDTO orderDTO, String sn) {
        if (StringUtils.isEmpty(sn) || Objects.isNull(orderDTO)) {
            log.error("orderService - updateBySn 参数为空");
            return;
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn).andEqualTo("deleted", 0));
        OrderDO orderDO = new OrderDO();
        BeanUtils.copyProperties(orderDTO, orderDO);
        orderMapper.updateByExampleSelective(orderDO, example);
        orderEsDumpProducer.send(sn);
    }

    @Override
    public JdFopDetailVO getJdFopDetailByDeclareOrderNo(String declareOrderNo) {
        OrderSearch search = new OrderSearch();
        search.setQueryInfo(declareOrderNo);
        search.setQueryType("declareOrderNo");
        search.setPageSize(1);
        search.setCurrentPage(1);
        Page<OrderEsDO> paging = orderEsDao.paging(search);
        List<OrderEsDO> orderEsDOList = paging.getContent();
        if (CollUtil.isEmpty(orderEsDOList)) {
            throw new ArgsInvalidException("申报单不存在");
        }
        OrderEsDO orderEsDO = orderEsDOList.get(0);
        String customsInventorySn = orderEsDO.getCustomsInventorySn();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(customsInventorySn);
        if (Objects.isNull(customsInventoryDTO)) {
            throw new ArgsInvalidException("清单不存在");
        }
        CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findByLogisticsNo(orderEsDO.getLogisticsNo());
        OrderDTO orderDTO = this.findBySnSection(orderEsDO.getSn());
        JdFopDetailVO detailVO = new JdFopDetailVO();
        detailVO.setGlobalSystemSn(orderDTO.getSystemGlobalSn());
        detailVO.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        detailVO.setInventoryNo(customsInventoryDTO.getInventoryNo());
        detailVO.setPattern("beihuo");//跨境业务模式, 保税备货=beihuo
        detailVO.setDeclarePaymentList("2"); //是否申报支付单 1-是;2-否 默认否
        detailVO.setPostType("I");//电商平台的订单类型* I-进口商品订单；E-出口商品订单 默认进口商品订单
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsInventoryDTO.getAccountBookId());
        if (Objects.equals(customsBookDTO.getCustomsAreaCode(), "2924")) { // 金义口岸
            detailVO.setCustoms("hangzhou");
        }
        if (Objects.equals(customsBookDTO.getCustomsAreaCode(), "2925")) { // 义乌口岸
            detailVO.setCustoms("yiwu");
        }
        detailVO.setDeclareOrder(Objects.equals(customsInventoryDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue()) ? "1" : "2");
        detailVO.setDeclareWayBill(Objects.nonNull(logisticsDTO) && Objects.equals(logisticsDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue()) ? "1" : "2");

        CompanyDTO ebpDTO = baseDataService.getCompanyDTOById(customsInventoryDTO.getEbpId());
        if (Objects.nonNull(ebpDTO)) {
            detailVO.setEbpCode(ebpDTO.getCode());
            detailVO.setEbpName(ebpDTO.getName());
        }
        CompanyDTO ebcDTO = baseDataService.getCompanyDTOById(customsInventoryDTO.getEbcId());
        if (Objects.nonNull(ebcDTO)) {
            detailVO.setEbcCode(ebcDTO.getCode());
            detailVO.setEbcName(ebcDTO.getName());
        }
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        if (Objects.nonNull(orderExtra)) {
            String payChannel = orderExtra.getSubmit().getPayChannel();
            PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOByCode(payChannel);
            if (Objects.nonNull(payChannelDTO) && Objects.nonNull(payChannelDTO.getPayCompanyId())) {
                CompanyDTO payCompanyDTO = baseDataService.getCompanyDTOById(payChannelDTO.getPayCompanyId());
                if (Objects.nonNull(payCompanyDTO)) {
                    detailVO.setPayCode(payCompanyDTO.getCode());
                    detailVO.setPayName(payCompanyDTO.getName());
                }
            }
        }
        List<JdFopGoodsInfoVO> goodsList = new ArrayList<>();
        List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        for (CustomsInventoryItemDTO itemDTO : customsInventoryItemDTOS) {
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            JdFopGoodsInfoVO goodsInfoVO = new JdFopGoodsInfoVO();
            goodsInfoVO.setGnum(itemDTO.getSort());
            goodsInfoVO.setQuantity(itemDTO.getCount());
            goodsInfoVO.setPrice(itemDTO.getUnitPrice());
            goodsInfoVO.setPattern("beihuo");
            goodsInfoVO.setGoodsName(itemDTO.getItemName());
            if (Objects.nonNull(itemExtra)) {
                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(customsInventoryDTO.getAccountBookId(), itemExtra.getUnifiedProductId());
                if (Objects.nonNull(goodsRecordDTO)) {
                    goodsInfoVO.setGoodsCode(goodsRecordDTO.getGoodsCode());
                    goodsInfoVO.setSku(goodsRecordDTO.getSkuId());
                    goodsInfoVO.setLength(goodsRecordDTO.getLength());
                    goodsInfoVO.setWidth(goodsRecordDTO.getWidth());
                    goodsInfoVO.setHeight(goodsRecordDTO.getHeight());
                    goodsInfoVO.setSafeDays(goodsRecordDTO.getShelfLife());
                    goodsInfoVO.setQiRecord(goodsRecordDTO.getCountryRecordNo());
                }
                goodsInfoVO.setBarcodes(itemExtra.getBarCode());
                goodsInfoVO.setBrandName(itemExtra.getBrand());
                goodsInfoVO.setGrossWeight(itemExtra.getGrossWeight());
                goodsInfoVO.setNetWeight(itemExtra.getNetWeight());
                goodsInfoVO.setCustomRecord(itemExtra.getUnifiedProductId());
                goodsInfoVO.setSellerRecord(itemExtra.getUnifiedProductId());
                goodsInfoVO.setSpe(itemExtra.getGoodsModel());
                goodsInfoVO.setVatRate(itemExtra.getVatRate());
                goodsInfoVO.setTaxRate(itemExtra.getTaxRate());
                goodsInfoVO.setHsCode(itemExtra.getHsCode());
                CustomsDictionaryDTO countryDTO = dictionaryService.findByCodeAndType(itemExtra.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
                if (Objects.nonNull(countryDTO)) {
                    goodsInfoVO.setCountry(countryDTO.getName());
                    goodsInfoVO.setQiCountry(countryDTO.getName());
                }
                goodsInfoVO.setLegalUnit1(itemExtra.getFirstUnit());
                goodsInfoVO.setLegalAmount1(itemExtra.getFirstUnitAmount());
                goodsInfoVO.setLegalUnit2(itemExtra.getSecondUnit());
                goodsInfoVO.setLegalAmount2(itemExtra.getSecondUnitAmount());
                goodsInfoVO.setMeasurement(itemExtra.getGoodsUnit());
                goodsInfoVO.setQiMeasurement(itemExtra.getGoodsUnit());
                goodsInfoVO.setHgsbys(itemExtra.getHgsbys());
            }
            goodsInfoVO.setModelNumber(null);
            goodsInfoVO.setItemLink(null);
            goodsInfoVO.setBatch(null);
            goodsInfoVO.setTaxNumberPost(null);
            goodsInfoVO.setPostRate(null);
            goodsInfoVO.setGoodsNameEn(null);
            goodsList.add(goodsInfoVO);
        }
        detailVO.setGoodsList(goodsList);
        log.info("getJdFopDetailByDeclareOrderNo detailVO={}", JSON.toJSONString(detailVO));
        return detailVO;
    }

    @Data
    private static class DecodeAndEncodeResVO {
        private String origin;
        private String target;
    }

    /**
     * 获取商品备案映射方式
     *
     * @param declareOrderTypeList 申报单类型
     * @return
     */
    @Override
    public GoodsRecordMappingWayEnums getGoodsRecordMappingWayEnum(List<String> declareOrderTypeList) {
        if (CollectionUtils.isEmpty(declareOrderTypeList)) {
            return GoodsRecordMappingWayEnums.DEFAULT;
        }
        String goodsRecordMappingMapJson = orderCBaseConfig.getGoodsRecordMappingMapJson();
        Map goodsRecordMappingMap = StringUtils.isEmpty(goodsRecordMappingMapJson) ? new HashMap<>() : JSON.parseObject(goodsRecordMappingMapJson, Map.class);
        String mappingWay = "";
        for (String declareOrderType : declareOrderTypeList) {
            if (goodsRecordMappingMap.containsKey(declareOrderType)) {
                mappingWay = (String) goodsRecordMappingMap.get(declareOrderType);
                break;
            }
        }
        return GoodsRecordMappingWayEnums.getEnums(mappingWay);
    }

    @Override
    public Long findOrderIdByDeclareOrderNo(String declareOrderNo) {
        if (Objects.isNull(declareOrderNo)) {
            return null;
        }
        Long orderId = orderEsDao.findOrderIdByDeclareOrderNo(declareOrderNo);
        return orderId;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deletedById(Long id) throws ArgsErrorException {
        if (Objects.isNull(id)) {
            throw new ArgsErrorException("id不能为空");
        }
        log.info("申报单删除 申报单id={}", id);
        OrderDTO orderDTO = orderService.findByIdFull(id);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsErrorException("没有查询到申报单数据");
        }
        log.info("申报单删除 申报单号={}", orderDTO.getDeclareOrderNo());
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        if (Objects.nonNull(customsInventoryDTO)) {
            if (Objects.equals(customsInventoryDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue())) {
                throw new ArgsErrorException("清关状态等于申报完成不允许删除：" + orderDTO.getDeclareOrderNo());
            }
            //查询明细
            List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            //如果占用库存，则清除占用库存
            if (Objects.equals(customsInventoryDTO.getIsOccupiedStock(), Boolean.TRUE)) {
                //更新账册库存
                orderService.updateInventory(customsInventoryItemDTOList, id, customsInventoryDTO);
            }
            //删除清单
            orderService.deleteCustomsInventoryAndDetail(customsInventoryDTO, customsInventoryItemDTOList);
        }
        //删除订单
        orderService.deleteCustomsOrder(orderDTO);
        //删除运单
        orderService.deleteCustomsLogistics(orderDTO);
        //删除申报单
        orderService.deleteOrder(orderDTO);
        customsInventoryCalloffService.deleteByOrderId(orderDTO.getId());
//        customsInventoryCancelService.deleteByOrderId(orderDTO.getId());
        orderEsDao.delete(orderDTO.getId().toString());
        customsSingleInventoryEsDao.delete(customsInventoryDTO.getId().toString());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAndReSubmitById(Long id) {
        if (Objects.isNull(id)) {
            throw new ArgsInvalidException("id不能为空");
        }
        String declareOrderNo = null;
        String extraJson = this.getOrderSubmit(id);
        log.info("申报单删除 申报单id={}", id);
        OrderDTO orderDTO = orderService.findByIdFull(id);
        if (Objects.isNull(orderDTO)) {
            throw new ArgsInvalidException("没有查询到申报单数据");
        }
        if (Objects.equals(orderDTO.getStatus(), OrderStatus.CANCEL.getValue())) {
            throw new ArgsInvalidException("申报单状态不允许删除重推");
        }
        log.info("申报单删除 申报单号={}", orderDTO.getDeclareOrderNo());
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        this.deletedOrder(orderDTO, customsInventoryDTO);
        OrderExtra orderExtra = JSON.parseObject(extraJson, OrderExtra.class);
        OrderSubmit submit = orderExtra.getSubmit();
        OrderSubmitDto submitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        declareOrderNo = submitDto.getDeclareOrderNo();
        // 这三个参数有任一不为空则 不传routeCode 后面自动去匹配routeCode
        if (StrUtil.isNotBlank(submit.getFirstIdentify()) || StrUtil.isNotBlank(submit.getSecondIdentify()) || StrUtil.isNotBlank(submit.getThirdIdentify())) {
            log.info("删除并重推时清空routeCode并自动匹配, 单号: {} , oldRouteCode: {}", declareOrderNo, submit.getRouteCode());
            submitDto.setRouteCode(null);
        }
//        Long newOrderId =
        //记录历史申报记录
        submitDto.setDeclareWayRecord(orderDTO.getDeclareWayRecord());
        Long newOrderId = orderService.submit(submitDto);

//        OrderDTO newOrderDTO = orderService.findByIdSection(newOrderId, new Date());
//        customsInventoryCancelService.updateOrderInfoByOldInfo(orderDTO,newOrderDTO);
        orderEsDao.delete(orderDTO.getId().toString());
        try {
            if (customsInventoryDTO != null) {
                customsSingleInventoryEsDao.delete(customsInventoryDTO.getId().toString());
            }
        } catch (Exception e) {
            log.error("[ 删除并重推 ] 删除清单失败 extraJson = {}", extraJson, e);
        }
    }

    @Override
    public boolean isByteDanceDeclareWithinCloud(String ebpCode, String extraJson) {
        return byteDanceDeclareService.isDeclareWithinCloud(ebpCode, extraJson);
    }

    // Delete order by id
    public void deleteOrder(OrderDTO orderDTO) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(orderDTO.getCreateTime());
        timeRangeParam.setEndDate(orderDTO.getCreateTime());
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", orderDTO.getId()));
        orderMapper.deleteByExample(example);
    }

    // Delete customs inventory and detail by id
    @Transactional(rollbackFor = Exception.class)
    public void deleteCustomsInventoryAndDetail(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> customsInventoryItemDTOList) {
        //删除主表
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(customsInventoryDTO.getCreateTime());
        timeRangeParam.setEndDate(customsInventoryDTO.getCreateTime());
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("id", customsInventoryDTO.getId()));
        customsInventoryMapper.deleteByExample(example);
        //删除明细
        if (!CollectionUtils.isEmpty(customsInventoryItemDTOList)) {
            customsInventoryItemDTOList.forEach(customsInventoryItem -> {
                TimeRangeParam detailTimeRangeParam = new TimeRangeParam();
                detailTimeRangeParam.setBeginDate(customsInventoryItem.getCreateTime());
                detailTimeRangeParam.setEndDate(customsInventoryItem.getCreateTime());
                Example detailExample = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, detailTimeRangeParam);
                detailExample.and(detailExample.createCriteria().andEqualTo("id", customsInventoryItem.getId()));
                customsInventoryItemMapper.deleteByExample(detailExample);
            });
        }
    }

    // Update inventory by customs inventory status
    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.REDUCE_OCCUPATION, handler = StockInventoryDeleteHandler.class)
    public void updateInventory(List<CustomsInventoryItemDTO> customsInventoryItemDTOList, Long orderId, CustomsInventoryDTO customsInventoryDTO) {
        if (Objects.nonNull(customsInventoryItemDTOList)) {
            List<UpdateInventoryDTO> updateInventoryDTOlist = new ArrayList<>();
            customsInventoryItemDTOList.forEach(customsInventoryItem -> {
                UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
                updateInventoryDTO.setCustomsBookId(customsInventoryDTO.getAccountBookId());
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.DELETE_APPLY);
                updateInventoryDTO.setBusinessNo(customsInventoryDTO.getDeclareOrderNo());
                CustomsInventoryItemExtra itemExtra = JSON.parseObject(customsInventoryItem.getExtraJson(), CustomsInventoryItemExtra.class);
                if (Objects.nonNull(itemExtra)) {
                    updateInventoryDTO.setCustomsBookItemId(customsInventoryItem.getBookItemId()).setDeclareUnitQfy(customsInventoryItem.getCount()).setProductId(itemExtra.getProductId()).setGoodsSeqNo(itemExtra.getGoodsSeqNo());
                    updateInventoryDTOlist.add(updateInventoryDTO);
                }
            });
            if (Objects.nonNull(updateInventoryDTOlist) && updateInventoryDTOlist.size() > 0) {
                stockInventoryService.updateInventory(updateInventoryDTOlist);
            }
        }

    }


    // Delete customs order by order id and sn
    public void deleteCustomsOrder(OrderDTO orderDTO) {
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        if (Objects.nonNull(customsOrderDTO)) {
            log.warn("deleteCustomsOrder 申报单号={}", customsOrderDTO.getDeclareOrderNo());
            TimeRangeParam timeRangeParam = new TimeRangeParam();
            timeRangeParam.setBeginDate(customsOrderDTO.getCreateTime());
            timeRangeParam.setEndDate(customsOrderDTO.getCreateTime());
            Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
            example.and(example.createCriteria().andEqualTo("id", customsOrderDTO.getId()));
            customsOrderMapper.deleteByExample(example);
        }
    }

    // Delete customs logistics by order id and sn
    public void deleteCustomsLogistics(OrderDTO orderDTO) {
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
        if (Objects.nonNull(customsLogisticsDTO)) {
            log.warn("deleteCustomsLogistics 申报单号={} 运单号={}", customsLogisticsDTO.getDeclareOrderNo(), customsLogisticsDTO.getLogisticsNo());
            TimeRangeParam timeRangeParam = new TimeRangeParam();
            timeRangeParam.setBeginDate(customsLogisticsDTO.getCreateTime());
            timeRangeParam.setEndDate(customsLogisticsDTO.getCreateTime());
            Example example = ShardingBaseExampleBuilder.getExample(CustomsLogisticsDO.class, timeRangeParam);
            example.and(example.createCriteria().andEqualTo("id", customsLogisticsDTO.getId()));
            customsLogisticsMapper.deleteByExample(example);
        }
    }


    /**
     * 内部调用，调用方必须添加事物
     *
     * @param orderDTO
     * @throws ArgsErrorException
     */
    private void deletedOrder(OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO) throws ArgsErrorException {
        if (Objects.nonNull(customsInventoryDTO)) {
            if (Objects.equals(customsInventoryDTO.getStatus(), CustomsActionStatus.DEC_SUCCESS.getValue())) {
                throw new ArgsErrorException("清关状态等于申报完成不允许删除：" + orderDTO.getDeclareOrderNo());
            }
            //查询明细
            List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            //如果占用库存，则清除占用库存
            if (Objects.equals(customsInventoryDTO.getIsOccupiedStock(), Boolean.TRUE)) {
                //更新账册库存
                orderService.updateInventory(customsInventoryItemDTOList, orderDTO.getId(), customsInventoryDTO);
            }
            //删除清单
            orderService.deleteCustomsInventoryAndDetail(customsInventoryDTO, customsInventoryItemDTOList);
        }
        //删除订单
        orderService.deleteCustomsOrder(orderDTO);
        //删除运单
        orderService.deleteCustomsLogistics(orderDTO);
        //删除申报单
        orderService.deleteOrder(orderDTO);
    }

    private void callbackOmsInventoryPassAsync(CustomsInventoryDTO inventoryDTO, OrderDTO orderDTO, String ebpCode) {
        OrderCustomsInventoryMessage message = new OrderCustomsInventoryMessage();
        message.setInventoryNo(inventoryDTO.getInventoryNo());
        message.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        message.setDeclareOrderNo(inventoryDTO.getDeclareOrderNo());
        message.setOutOrderNo(orderDTO.getOutOrderNo());
        message.setEbpCode(ebpCode);
        message.setCustomsStatus(inventoryDTO.getCustomsStatus());
        message.setCustomsDetail(inventoryDTO.getCustomsDetail());
        message.setTime(new DateTime(inventoryDTO.getCustomsPassTime()).getMillis());
        message.setLogisticsNo(inventoryDTO.getLogisticsNo());
        message.setOrigMessage("");
        CompanyDTO declareCompanyDTO = baseDataService.getCompanyDTOById(inventoryDTO.getAgentCompanyId());
        message.setCompanyCode(declareCompanyDTO.getCode());
        message.setCompanyName(declareCompanyDTO.getName());
//        return JSON.parseObject(JSON.toJSONString(obj));
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        List<MessageSubscribeDTO> messageSubscribeDTOS = messageService.listSubscribeByTagAndType("CHANNEL-" + orderExtra.getSubmit().getChannel(), MessageType.ORDER_CUSTOMS_INVENTORY.getCode());
        if (!CollectionUtils.isEmpty(messageSubscribeDTOS)) {
            MessageSubscribeDTO messageSubscribeDTO = messageSubscribeDTOS.get(0);
            if (Objects.nonNull(messageSubscribeDTO)) {
                log.info("消息回调 回调地址: {} 消息:{}", messageSubscribeDTO.getNotifyUrl(), JSON.toJSONString(message));
                HttpRequest httpRequest = HttpRequestUtil.post(messageSubscribeDTO.getNotifyUrl(), JSON.toJSONString(message));
                String body = httpRequest.body();
                log.info("消息回调 返回数据，业务编码: {} ,返回数据信息: {}", inventoryDTO.getDeclareOrderNo(), body);
                if (httpRequest.ok()) {
                    log.info("消息回调成功");
                } else {
                    log.error("消息回调处理异常：{}", httpRequest.code());
                }
            }
        }

    }

}
