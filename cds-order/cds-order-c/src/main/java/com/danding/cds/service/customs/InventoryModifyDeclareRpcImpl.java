package com.danding.cds.service.customs;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.InventoryModifyDeclareService;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.ModifyDeclareStat;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.order.base.bean.dao.CustomsInventoryItemDO;
import com.danding.cds.order.base.bean.dao.InventoryModifyDeclareDO;
import com.danding.cds.bean.dto.InventoryModifyDeclareDto;
import com.danding.cds.handler.CustomsInventoryHandler;
import com.danding.cds.service.InventoryModifyDeclareBaseService;
import com.danding.cds.service.base.CustomsInventoryBaseService;
import com.danding.cds.service.base.CustomsInventoryItemBaseService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.vo.req.ModifyDeclareReqVo;
import com.danding.cds.v2.bean.vo.req.ModifyItemReqVo;
import com.danding.cds.v2.bean.vo.res.ModifyDeclareItemResVo;
import com.danding.cds.v2.bean.vo.res.ModifyDeclareResVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Create 2021/8/16  16:57
 * @Describe service - db 解耦
 **/
@Slf4j
@Service
public class InventoryModifyDeclareRpcImpl implements InventoryModifyDeclareService {

    /**
     * 时间格式
     */
    private static final String TIMESTAMP_FORMAT = "yyyy-MM-dd HH:mm:ss";

    @Autowired
    private InventoryModifyDeclareBaseService inventoryModifyDeclareBaseService;
    @Autowired
    private CustomsInventoryItemBaseService customsInventoryItemBaseService;
    @Autowired
    private CustomsInventoryBaseService customsInventoryBaseService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private SequenceService sequenceService;

    @Override
    public ListVO<ModifyDeclareResVo> paging(ModifyDeclareReqVo declareReqVo) {

        final ListVO<InventoryModifyDeclareDto> modifyDeclareDtoListVO = inventoryModifyDeclareBaseService.paging(declareReqVo);
        ListVO<ModifyDeclareResVo> declareResVoListVO = new ListVO<>();
        if (modifyDeclareDtoListVO == null) {
            return declareResVoListVO;
        }
        declareResVoListVO.setPage(modifyDeclareDtoListVO.getPage());

        // 数据获取
        List<InventoryModifyDeclareDto> declareDtoList = modifyDeclareDtoListVO.getDataList();
        List<Long> ids = declareDtoList.stream().map(InventoryModifyDeclareDto::getId).collect(Collectors.toList());
        final Map<Long, List<CustomsInventoryItemDTO>> customsInventoryItemMap = customsInventoryItemBaseService.batchSelectByInventoryIdsIn90Days(ids);
        List<Long> bookIds = declareDtoList.stream().map(InventoryModifyDeclareDto::getAccountBookId).collect(Collectors.toList());
        final Map<Long, CustomsBookDTO> customsBookMap = this.getCustomsBookDTO(bookIds);

        // 数据列表转换
        List<ModifyDeclareResVo> modifyDeclareResVos = declareDtoList.stream()
                .map(z -> {
                    ModifyDeclareResVo modifyDeclareResVo = new ModifyDeclareResVo();
                    modifyDeclareResVo.setId(z.getId().toString());
                    modifyDeclareResVo.setDeclareNo(z.getDeclareNo());
                    modifyDeclareResVo.setLogisticsNo(z.getLogisticsNo());
                    modifyDeclareResVo.setInventoryNo(z.getInventoryNo());
                    CustomsBookDTO bookDTO = customsBookMap.get(z.getAccountBookId());
                    modifyDeclareResVo.setAccountBookId(z.getAccountBookId());
                    modifyDeclareResVo.setAccountBookNo(bookDTO == null ? null : bookDTO.getBookNo());
                    modifyDeclareResVo.setStatus(z.getStatus());
                    modifyDeclareResVo.setStatusName(ModifyDeclareStat.getDesc(z.getStatus()));
                    modifyDeclareResVo.setCustomsDetail(z.getCustomsDetail());
                    modifyDeclareResVo.setCreateTime(DateUtil.format(z.getCreateTime(), TIMESTAMP_FORMAT));
                    modifyDeclareResVo.setLastCustomsTime(DateUtil.format(z.getLastCustomsTime(), TIMESTAMP_FORMAT));
                    modifyDeclareResVo.setLastDeclareTime(DateUtil.format(z.getLastDeclareTime(), TIMESTAMP_FORMAT));

                    final CompanyDTO company = companyService.findUnifiedCrossInfoById(z.getAgentCompanyId());
                    modifyDeclareResVo.setAgentCompany(company.getName());

                    List<CustomsInventoryItemDTO> inventoryItemDTOList = customsInventoryItemMap.get(z.getId());
                    if (!CollectionUtils.isEmpty(inventoryItemDTOList)) {
                        List<ModifyDeclareItemResVo> declareItemResVos = inventoryItemDTOList.stream()
                                .map(a -> {
                                    ModifyDeclareItemResVo declareItemResVo = new ModifyDeclareItemResVo();
                                    declareItemResVo.setId(a.getId().toString());
                                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(a.getExtraJson(), CustomsInventoryItemExtra.class);
                                    declareItemResVo.setProductId(itemExtra.getProductId());
                                    declareItemResVo.setGoodsSeqNo(itemExtra.getGoodsSeqNo());
                                    declareItemResVo.setHsCode(itemExtra.getHsCode());
                                    declareItemResVo.setFirstUnit(itemExtra.getFirstUnit());
                                    declareItemResVo.setFirstAmount(itemExtra.getFirstUnitAmount());
                                    declareItemResVo.setSecondUnit(itemExtra.getSecondUnit());
                                    declareItemResVo.setSecondAmount(itemExtra.getSecondUnitAmount());
                                    return declareItemResVo;
                                }).collect(Collectors.toList());

                        modifyDeclareResVo.setDeclareItemVos(declareItemResVos);
                    }
                    return modifyDeclareResVo;
                }).collect(Collectors.toList());

        declareResVoListVO.setDataList(modifyDeclareResVos);
        return declareResVoListVO;
    }

    /**
     * 获取账册信息
     *
     * @param bookIds 账册ID列表
     * @return
     */
    private Map<Long, CustomsBookDTO> getCustomsBookDTO(List<Long> bookIds) {
        final List<CustomsBookDTO> bookDTOList = customsBookService.listBooksByIds(bookIds);
        log.info("InventoryModifyDeclareRpcImpl getCustomsBookDTO bookDTOList={}", JSON.toJSONString(bookDTOList));
        if (CollectionUtils.isEmpty(bookDTOList)) {
            return Collections.emptyMap();
        }
        return bookDTOList.stream().collect(Collectors.toMap(CustomsBookDTO::getId, Function.identity()));
    }

    @Autowired
    private CustomsInventoryHandler customsInventoryHandler;

    @Override
    public void batchDeclare(List<Long> ids) throws ArgsErrorException {
        inventoryModifyDeclareBaseService.judgeStatus(ids, "DECLARE");
        customsInventoryHandler.modifyHandle(ids);
    }

    @Override
    public void batchDelete(List<Long> ids) throws ArgsErrorException {
        inventoryModifyDeclareBaseService.judgeStatus(ids, "DELETE");
        List<InventoryModifyDeclareDto> baseServiceListByIds = inventoryModifyDeclareBaseService.getListByIds(ids);
        if (!CollectionUtils.isEmpty(baseServiceListByIds)) {
            Date begin = baseServiceListByIds.stream().sorted().findFirst().get().getCreateTime();
            Date end = baseServiceListByIds.stream().sorted(Comparator.comparing(InventoryModifyDeclareDto::getCreateTime).reversed()).findFirst().get().getCreateTime();
            inventoryModifyDeclareBaseService.batchDelete(ids);
            customsInventoryItemBaseService.batchDeleteByInventoryId(ids, begin, end);
        }
    }

    @Override
    public void batchCancel(List<Long> ids) throws ArgsErrorException {
        inventoryModifyDeclareBaseService.judgeStatus(ids, "CANCEL");
        inventoryModifyDeclareBaseService.batchCancel(ids);
    }

    /**
     * 校验运单编号对应的清单能否导入
     *
     * @param logisticsNo 运单编号
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportResultResVo checkAndImport(String logisticsNo) {

        ImportResultResVo resultResVo = new ImportResultResVo();
        final CustomsInventoryDTO inventoryDTO = customsInventoryBaseService.findByLogisticsNo(logisticsNo);
        log.info("InventoryModifyDeclareRpcImpl checkAndImport inventoryDTO={}", JSON.toJSONString(inventoryDTO));
        if (!Objects.equals(inventoryDTO.getCustomsStatus(), CustomsStat.CUSTOMS_PASS.getValue())) {
            resultResVo.setFlag(false);
            resultResVo.setReason("海关状态非放行，不允许变更申报");
            return resultResVo;
        }

        final List<CustomsInventoryItemDTO> inventoryItemDTOList = customsInventoryItemBaseService.selectByInventoryIdIn90Days(inventoryDTO.getId());
        log.info("InventoryModifyDeclareRpcImpl checkAndImport inventoryItemDTOList={}", JSON.toJSONString(inventoryItemDTOList));
        if (CollectionUtils.isEmpty(inventoryItemDTOList)) {
            resultResVo.setFlag(false);
            resultResVo.setReason("此运单对应的清单没有表体");
            return resultResVo;
        }

        final InventoryModifyDeclareDto modifyDeclareDto = inventoryModifyDeclareBaseService.selectByLogisticsNo(logisticsNo);
        log.info("InventoryModifyDeclareRpcImpl checkAndImport modifyDeclareDto={}", JSON.toJSONString(modifyDeclareDto));
        if (modifyDeclareDto != null) {
            resultResVo.setFlag(false);
            resultResVo.setReason("此运单已导入");
            return resultResVo;
        }

        Long modifyId = sequenceService.generateId();
        InventoryModifyDeclareDO modifyDeclareDO = new InventoryModifyDeclareDO();
        modifyDeclareDO.setId(modifyId);
        modifyDeclareDO.setOrderId(inventoryDTO.getOrderId());
        modifyDeclareDO.setDeclareNo(inventoryDTO.getDeclareOrderNo());
        modifyDeclareDO.setLogisticsNo(inventoryDTO.getLogisticsNo());
        modifyDeclareDO.setInventoryNo(inventoryDTO.getInventoryNo());
        modifyDeclareDO.setAccountBookId(inventoryDTO.getAccountBookId());
        modifyDeclareDO.setAgentCompanyId(inventoryDTO.getAgentCompanyId());
        modifyDeclareDO.setStatus(ModifyDeclareStat.WAITING.getValue());
        modifyDeclareDO.setCreateTime(DateUtil.date());
        modifyDeclareDO.setUpdateTime(DateUtil.date());
        UserUtils.setCreateAndUpdateBy(modifyDeclareDO);
        log.info("InventoryModifyDeclareRpcImpl checkAndImport modifyDeclareDO={}", JSON.toJSONString(modifyDeclareDO));
        inventoryModifyDeclareBaseService.insertSelective(modifyDeclareDO);

        List<CustomsInventoryItemDO> inventoryItemDOList = inventoryItemDTOList.stream().map(z -> {
            CustomsInventoryItemDO inventoryItemDO = new CustomsInventoryItemDO();
            BeanUtils.copyProperties(z, inventoryItemDO);
            UserUtils.setCommonData(inventoryItemDO);
            inventoryItemDO.setCustomsInventoryId(modifyId);
            inventoryItemDO.setModifyDeclare(1);
            inventoryItemDO.setId(sequenceService.generateId());
            return inventoryItemDO;
        }).collect(Collectors.toList());

        customsInventoryItemBaseService.batchInsert(inventoryItemDOList);

        resultResVo.setFlag(true);
        resultResVo.setReason("导入成功");
        return resultResVo;
    }

    @Override
    public void modifyItem(List<ModifyItemReqVo> collect) {
        List<Long> idList = collect.stream().map(ModifyItemReqVo::getId).collect(Collectors.toList());
        log.info("InventoryModifyDeclareRpcImpl modifyItem idList={}", JSON.toJSONString(idList));
        Map<Long, CustomsInventoryItemDTO> idDTOMap = customsInventoryItemBaseService.batchGetItemByIdListIn90Days(idList);
        log.info("InventoryModifyDeclareRpcImpl modifyItem idDTOMap={}", JSON.toJSONString(idDTOMap));
        collect.forEach(c -> {
            if (!idDTOMap.containsKey(c.getId())) {
                log.info("InventoryModifyDeclareRpcImpl modifyItem 找不到表体 return");
                return;
            }
            CustomsInventoryItemDO itemDO = new CustomsInventoryItemDO();
            CustomsInventoryItemDTO itemDTOS = idDTOMap.get(c.getId());
            CustomsInventoryItemExtra extra = JSON.parseObject(itemDTOS.getExtraJson(), CustomsInventoryItemExtra.class);
            extra.setFirstUnit(c.getFirstUnit());
            extra.setFirstUnitAmount(new BigDecimal(c.getFirstAmount()));
            extra.setHsCode(c.getHsCode());
            if (Objects.nonNull(c.getSecondUnit())) {
                extra.setSecondUnit(c.getSecondUnit());
            } else {
                extra.setSecondUnit(null);
            }
            if (Objects.nonNull(c.getSecondAmount())) {
                extra.setSecondUnitAmount(new BigDecimal(c.getSecondAmount()));
            } else {
                extra.setSecondUnitAmount(null);
            }
            itemDO.setId(c.getId());
            itemDO.setExtraJson(JSON.toJSONString(extra));
            log.info("InventoryModifyDeclareRpcImpl modifyItem itemDO={}", JSON.toJSONString(itemDO));
            customsInventoryItemBaseService.updateByDOSelective(itemDO, itemDTOS.getCreateTime());
        });
    }
}
