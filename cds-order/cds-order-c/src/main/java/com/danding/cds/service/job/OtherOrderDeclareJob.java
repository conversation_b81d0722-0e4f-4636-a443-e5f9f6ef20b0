package com.danding.cds.service.job;


import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsPaymentService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderDeclareV2DTO;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.DeclareRepushService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
@RestController
public class OtherOrderDeclareJob extends IJobHandler {

    @Resource
    private CustomsOrderService customsOrderService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private OrderService orderService;

    @Resource
    private CustomsPaymentService customsPaymentService;

    @Autowired
    private DeclareRepushService declareRepushService;


    static final Integer CUSTOMS_INVENTORY_STATUS = -1;

    static final Integer DECLARING = 20;

    static final Integer CUSTOMS_SYSTEM_ERROR = 42;

    static final List<String> statusList = Arrays.asList(
            CustomsStat.CUSTOMS_PASS.getValue(),
            CustomsStat.CUSTOMS_REFUSE.getValue(),
            CustomsStat.CUSTOMS_PERSON.getValue());


    @Override
    @XxlJob(value = "OrderCOtherOrderDeclareJob", enableTenant = true)
    public ReturnT<String> execute(String param) throws Exception {
        try {
            XxlJobLogger.log("参数设置 :" + param);
            OrderDeclareV2DTO orderDeclareDTO = JSON.parseObject(param, OrderDeclareV2DTO.class);
            log.info("[[OtherOrderDeclareJob] 参数设置-{}]", orderDeclareDTO);
            List<OrderDTO> orderDTOListBD = getDTOListOtherDance(orderDeclareDTO);
            declareRepushService.reDeclarePushAndNotify(orderDTOListBD, orderDeclareDTO, "【其它电商平台】");
        } catch (Exception e) {
            XxlJobLogger.log("重推失败 :" + e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    @PostMapping("/orderDeclareJob/menual/execute")
    @TenantHttpMethod(type = TenantHttpType.HEADER)
    public ReturnT<String> execute1(@RequestBody Map<String, Object> param) throws Exception {

        OrderDTO orderDTO = orderService.findBySnSection((String) param.get("sn"));
        List<OrderDTO> orderDTOList = new ArrayList() {{
            add(orderDTO);
        }};
        OrderDeclareV2DTO orderDeclareDTO = JSON.parseObject(JSON.toJSONString(param), OrderDeclareV2DTO.class);
        declareRepushService.reDeclarePushAndNotify(orderDTOList, orderDeclareDTO, "[测试]");
        return ReturnT.SUCCESS;
    }


    private List<OrderDTO> getDTOListOtherDance(OrderDeclareV2DTO orderDeclareDTO) {
        List<OrderDTO> orderDTOList = orderService.listOrderExcludeEbpId(orderDeclareDTO.getExcludeEbpIdList(), OrderStatus.DEC_ING.getValue(), orderDeclareDTO.getPage(), orderDeclareDTO.getQueryDays());
        XxlJobLogger.log("[op:OtherOrderDeclareJob] 待放行订单数={} ", orderDTOList.size());
        return orderDTOList;
    }

    private void reDeclareBD(OrderDTO orderDTO) {
        try {
            // order处于申报中
            if (DECLARING.equals(orderDTO.getStatus())) {
                CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), null);
                if (Objects.nonNull(customsOrderDTO)) {
                    if (DECLARING.equals(customsOrderDTO.getStatus())) {
                        if (customsOrderDTO.getLastDeclareTime() != null && new DateTime(customsOrderDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                            orderService.resetDeclare(orderDTO.getId(), RouteActionEnum.DECLARE_ORDER);
                            XxlJobLogger.log("[op:OtherOrderDeclareJob] 待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_ORDER.getDesc(), orderDTO.getDeclareOrderNo());
                            return;
                        }
                    }
                }
                CustomsLogisticsDTO logisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), null);
                if (Objects.isNull(logisticsDTO)) {
                    return;
                }
                if (DECLARING.equals(logisticsDTO.getStatus())) {
                    if (logisticsDTO.getLastDeclareTime() != null && new DateTime(logisticsDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                        orderService.resetDeclare(orderDTO.getId(), RouteActionEnum.DECLARE_LOGISTICS);
                        XxlJobLogger.log("[op:OtherOrderDeclareJob] 待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_LOGISTICS.getDesc(), orderDTO.getDeclareOrderNo());
                        return;
                    }
                }
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), null);
                if (Objects.isNull(inventoryDTO)) {
                    return;
                }
                if (DECLARING.equals(inventoryDTO.getStatus())) {
                    if (statusList.contains(inventoryDTO.getCustomsStatus())) {
                        return;
                    }
                }
                if (CUSTOMS_INVENTORY_STATUS.equals(inventoryDTO.getStatus())) {
                    if (!CUSTOMS_SYSTEM_ERROR.equals(orderDTO.getExceptionType())) {
                        return;
                    }
                }
                if (inventoryDTO.getLastDeclareTime() != null && new DateTime(inventoryDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                    orderService.resetDeclare(orderDTO.getId(), RouteActionEnum.DECLARE_INVENTORY);
                    XxlJobLogger.log("[op:OtherOrderDeclareJob] 待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_INVENTORY.getDesc(), orderDTO.getDeclareOrderNo());
                }

                CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), null);
                if (Objects.isNull(customsPaymentDTO)) {
                    return;
                }
                if (customsPaymentDTO.getLastDeclareTime() != null && new DateTime(customsPaymentDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                    orderService.resetDeclare(orderDTO.getId(), RouteActionEnum.DECLARE_PAYMENT);
                    XxlJobLogger.log("[op:OtherOrderDeclareJob] 待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_PAYMENT.getDesc(), orderDTO.getDeclareOrderNo());
                }
            }
        } catch (ArgsErrorException errorException) {
            log.warn("[[op:ByteOrderDeclareJob] 待放行重推异常 Message={}]", errorException.getErrorMessage());
        } catch (Exception e) {
            log.error("[[op:OtherOrderDeclareJob] 待放行重推异常 Message={} 堆栈：{}]", e.getMessage(), ExceptionJoinUtil.exceptionStackTraceAsString(e));
        }

    }

}
