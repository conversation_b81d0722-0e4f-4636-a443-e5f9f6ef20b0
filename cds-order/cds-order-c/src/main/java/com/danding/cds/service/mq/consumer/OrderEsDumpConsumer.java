package com.danding.cds.service.mq.consumer;

import com.danding.cds.c.api.service.OrderService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Component
@RestController
@RocketMQMessageListener(
        consumerGroup = "ccs-order-dump-c-consumer",
        topic = "ccs-order-dump-c-topic"
)
public class OrderEsDumpConsumer extends MessageHandler {

    @Resource
    private OrderService orderService;

    @Override
    public void handle(Object o) throws RuntimeException {
        if (ObjectUtils.isEmpty(o)) {
            return;
        }
        String sn = o.toString();
        // 批量处理
        List<String> snList = Lists.newArrayList(sn.split(","));
        if (snList.size() > 1) {
            orderService.dumpBySn(snList);
        } else {
            orderService.dumpBySn(sn);
        }
    }

    @GetMapping("/OrderEsDumpConsumer/dumpBySn")
    public String test(String snList) {
        handle(snList);
        return "SUCCESS";
    }
}
