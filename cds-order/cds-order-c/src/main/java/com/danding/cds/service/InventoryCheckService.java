package com.danding.cds.service;

import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.google.common.base.Objects;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @program: cds-center
 * @description: 清单校验
 * @author: 潘本乐（Belep）
 * @create: 2022-02-26 12:42
 **/
@Service
@RefreshScope
public class InventoryCheckService {

    /**
     * 特定异常webhook
     */
    @Value("${specific.exception.robot.webhook:}")
    private String specificExceptionRobotWebhook;
    /**
     * 清单编号关区和账册不一致通知
     */
    @Value("${invtno.customs.area.different.notify.phones:}")
    private String[] invtnoCustomsAreaDifferentNotifyPhones;


    private static ExecutorService initTasksThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);


    /**
     * 校验下清单编号关区是否和账册一致
     *
     * @param declareNo      申报单号
     * @param invtNo         清单编号，如：29242022I168601022
     * @param customsBookDTO 账册，如：T2924W000229
     */
    public void checkInvtNoAndAccountAreaSame(String declareNo, String invtNo, CustomsBookDTO customsBookDTO) {

        if (StringUtils.isEmpty(invtNo)) {
            return;
        }
        String accountNo = Optional.ofNullable(customsBookDTO).map(CustomsBookDTO::getBookNo).orElse(null);
        if (StringUtils.isEmpty(accountNo)) {
            return;
        }
        if (Objects.equal(invtNo.substring(0, 4), accountNo.substring(1, 5))) {
            return;
        }
        List<String> phoneList = Arrays.asList(invtnoCustomsAreaDifferentNotifyPhones);
        String message = WechatNotifyUtils.mdWarnMessage("清单编号和申报关区不一致", declareNo, "清单编号：[" + invtNo + "]; 账册编号：[" + accountNo + "];关区不一致", null);
        initTasksThread.submit(() -> WechatNotifyUtils.wechatNotifyMd(specificExceptionRobotWebhook, phoneList, message));
    }

}
