package com.danding.cds.service.customs;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.QuarterUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderReceive;
import com.danding.cds.customs.order.api.dto.CustomsOrderSearch;
import com.danding.cds.handler.ReceiptTrackLogParametersHandler;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.monitor.MappingEmptyEvent;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryDO;
import com.danding.cds.order.base.bean.dao.CustomsOrderDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.service.CustomsOrderBaseService;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.cds.service.es.OrderEsDao;
import com.danding.cds.service.mq.producer.OrderDeclareMQProducer;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.danding.cds.utils.CustomsDeclareUtils;
import com.danding.cds.utils.CustomsOrderBuilder;
import com.danding.cds.utils.flow.OrderAction;
import com.danding.cds.utils.flow.OrderFlowBook;
import com.danding.common.utils.CopyUtil;
import com.danding.logistics.api.common.page.TimeRangeParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.cache.common.annotation.Lock;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import com.google.common.eventbus.EventBus;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.ibatis.session.RowBounds;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 订单处理
 */
@Slf4j
@Service
public class CustomsOrderServiceImpl implements CustomsOrderService {

//    @DubboReference
//    private CompanyService companyService;

    @Resource
    private OrderService orderService;

    @Resource
    private OrderDeclareMQProducer orderDeclareMQProducer;

    @DubboReference
    private MessageService messageService;

    @Autowired
    private EventBus eventBus;

    @Resource
    private CustomsOrderBaseService customsOrderBaseService;

    @Resource
    private BaseDataService baseDataService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @Resource
    private OrderEsDao orderEsDao;

    /**
     * 海关返回信息编码信息
     */
    private static final Pattern codePattern = Pattern.compile("Code:[0-9]+;");

    /**
     * 编码值
     */
    private static final Pattern codeValuePattern = Pattern.compile("[0-9]+");


    @Override
    public CustomsOrderDTO findById(Long id) {
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class);
        example.and(example.createCriteria().andEqualTo("id", id));
        CustomsOrderDO customsOrderDO = customsOrderBaseService.selectOneByExample(example);
        return CustomsOrderBuilder.buildDTO(customsOrderDO);
    }

    @Override
    public CustomsOrderDTO findByOrder(Long orderId, String sn) {
        if (StringUtils.isEmpty(sn)) {
            Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class);
            example.and(example.createCriteria().andEqualTo("orderId", orderId));
            CustomsOrderDO item = customsOrderBaseService.selectOneByExample(example);
            return CustomsOrderBuilder.buildDTO(item);
        } else {
            return this.findBySnSection(sn);
        }
    }

    @Override
    public List<CustomsOrderDTO> findBySnList(List<String> snList) {

        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        if (CollectionUtils.isEmpty(quarterMap)) {
            return null;
        }
        List<CustomsOrderDTO> customsOrderDTOList = new ArrayList<>();
        quarterMap.forEach((k, v) -> {
            Date beginOfQuarter = QuarterUtil.beginOfQuarter(k);
            Date endOfQuarter = QuarterUtil.endOfQuarter(beginOfQuarter);
            List<CustomsOrderDO> customsOrderDOList = customsOrderBaseService.selectBySnList(v, beginOfQuarter, endOfQuarter);
            List<CustomsOrderDTO> orderDTOList = CustomsOrderBuilder.buildDTO(customsOrderDOList);
            if (!CollectionUtils.isEmpty(orderDTOList)) {
                customsOrderDTOList.addAll(orderDTOList);
            }
        });
        return customsOrderDTOList;
    }


    @Override
    public CustomsOrderDTO findBySnSection(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(createTime).millisOfDay().withMaximumValue().toDate()));
        //timeRangeParam.setEndDate(new DateTime(createTime).millisOfDay().withMaximumValue().toDate());
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        // Step::返回值处理
        CustomsOrderDO result = customsOrderBaseService.selectOneByExample(example);
        return CustomsOrderBuilder.buildDTO(result);
    }

    @Override
    public CustomsOrderDTO findByDeclareNo(String declareNo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andEqualTo("declareOrderNo", declareNo);
        CustomsOrderDO result = customsOrderBaseService.selectOneByExample(example);
        CustomsOrderDTO customsOrderDTO = BeanUtil.copyProperties(result, CustomsOrderDTO.class);
        return customsOrderDTO;
    }

    /**
     * 修改最后一次申报时间
     *
     * @param orderSn
     */
    @Override
    public void updateByLastDeclareTime(String orderSn) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryDO.class, timeRangeParam);
        Example.Criteria criteria = example.getOredCriteria().get(0);
        criteria.andEqualTo("declareOrderNo", orderSn);
        CustomsOrderDO result = customsOrderBaseService.selectOneByExample(example);
        if (Objects.nonNull(result.getLastDeclareTime())) {
            CustomsOrderDO customsOrderDO = new CustomsOrderDO();
            customsOrderDO.setId(result.getId());
            Date nowBeforeFiveMinutes = new Date(result.getLastDeclareTime().getTime() - 600000);
            customsOrderDO.setLastDeclareTime(nowBeforeFiveMinutes);
            UserUtils.setUpdateBy(customsOrderDO);
            customsOrderDO.setUpdateTime(new Date());
            customsOrderBaseService.updateByPrimaryKeySelective(customsOrderDO);
        }
    }

    @Override
    public void updateLastDeclareTime(String sn) {
        if (StringUtils.isEmpty(sn)) {
            throw new ArgsErrorException("updateLastDeclareTime sn为空");
        }
        String dateStr = sn.substring(2, 12);
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
        Example example = getExample(createTime);
        example.and(example.createCriteria().andEqualTo("orderSn", sn));
        Date fifteenMinusAgo = new Date(System.currentTimeMillis() - 900000);
        CustomsOrderDO customsOrderDO = new CustomsOrderDO();
        customsOrderDO.setLastDeclareTime(fifteenMinusAgo);
        customsOrderBaseService.updateByExampleSelective(customsOrderDO, example);
    }

    @Override
    public void orderDeclareSend(OrderDTO orderDTO) {
        orderDeclareMQProducer.send(orderDTO);
    }

//    @Override
//    public void logicDeleteBySn(String sn) {
//        if (StringUtils.isEmpty(sn)) {
//            throw new ArgsErrorException("deleteBySn sn为空");
//        }
//        String dateStr = sn.substring(2, 12);
//        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("yyMMddHHmm");
//        Date createTime = dateTimeFormatter.parseDateTime(dateStr).toDate();
//        Example example = getExample(createTime);
//        example.and(example.createCriteria().andEqualTo("orderSn", sn).andEqualTo("deleted", 0));
//        CustomsOrderDO customsOrderDO = new CustomsOrderDO();
//        customsOrderDO.setDeleted(true);
//        customsOrderBaseService.updateByExampleSelective(customsOrderDO, example);
//    }

    private Example getExample(Date createTime) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        return example;
    }

    private Example getExample(Date createTimeFrom, Date createTimeTo) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTimeFrom));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTimeTo));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        return example;
    }

    @Override
    public List<CustomsOrderDTO> listByStatus(Integer status, Integer limit, Date sectionDate) {
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        RowBounds rowBounds = new RowBounds(0, limit);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", status);
        example.and(criteria);
        List<CustomsOrderDO> doList = customsOrderBaseService.selectByExampleAndRowBounds(example, rowBounds);
        return doList.stream().map(CustomsOrderBuilder::buildDTO).collect(Collectors.toList());
    }

    @Override
    public void updateBuyerInfo(Long id, String buyerIdNum, String buyerName, Date sectionDate) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setBuyerIdNumber(buyerIdNum);
        template.setBuyerName(buyerName);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateSnWithFix(String sn, String newSn, Date sectionDate) {
        if (StringUtils.isEmpty(sn)) {
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new DateTime(sectionDate).millisOfDay().withMaximumValue().toDate()));
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        example.and(example.createCriteria().andEqualTo("sn", sn));
        // Step::返回值处理
        CustomsOrderDO result = customsOrderBaseService.selectOneByExample(example);
        CustomsOrderDO template = new CustomsOrderDO();
        template.setSn(newSn);
        this.updateByIdSection(result.getId(), template, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setStatus(status);
        template.setLastDeclareTime(new Date());
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByPush(Long id, Integer status, Date sectionDate, Integer declareFrequency) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setStatus(status);
        template.setLastDeclareTime(new Date());
        template.setDeclareFrequency(declareFrequency);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateStatusSection(Long id, Integer status, Date sectionDate) {

        CustomsOrderDO template = new CustomsOrderDO();
        template.setStatus(status);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateStatusResetDeclareTime(String sn, CustomsActionStatus status) {

        String tableName = QuarterUtil.getCustomsOrderTableBySn(sn);
        if (StringUtils.isEmpty(tableName)) {
            return;
        }
        customsOrderBaseService.updateStatusResetDeclareTime(sn, tableName, status.getValue());
    }

    @Override
    public void updateByCustomsActive(Long id, String customsStatus, String customsDetail, Date lastCustomsTime, Date sectionDate) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setCustomsStatus(customsStatus);
        template.setCustomsDetail(customsDetail);
        template.setLastCustomsTime(lastCustomsTime);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByBaseInfo(Long id, String buyerName, String consigneeAddress, String buyerIdNumber, String itemJson, String extraJson, Date sectionDate) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setBuyerName(buyerName);
        template.setConsigneeAddress(consigneeAddress);
        template.setBuyerIdNumber(buyerIdNumber);
        template.setItemJson(itemJson);
        template.setExtraJson(extraJson);
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    public void updateByCustomsPass(Long id, Date sectionDate) {
        CustomsOrderDO template = new CustomsOrderDO();
        template.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
        this.updateByIdSection(id, template, sectionDate);
    }

    @Override
    @Lock(lockType = Lock.LockType.METHOD, keyPrefix = "receiveOrder", lockMethod = "getDeclareOrderNo")
    @TrackLog(infoIndex = 0,
            handler = ReceiptTrackLogParametersHandler.class,
            receiptType = TrackLogConstantMixAll.CUSTOMS_ORDER_RECEIPT)
    public String receive(CustomsOrderReceive receive) {
        log.info("[op:CustomsOrderServiceImpl-receive] receiveCustoms,info={}", JSON.toJSONString(receive));
        // Step::基础参数解析
        String customsStatus = receive.getCustomsStatus();
        String ebpCode = receive.getEbpCode();
        String ebcCode = receive.getEbcCode();
        String declareOrderNo = receive.getDeclareOrderNo();
        String customsDetail = receive.getCustomsDetail();
        Long customsTime = receive.getCustomsTime();
//        CompanyDTO ebp = baseDataService.getCompanyDTOByCode(ebpCode);
        OrderDTO orderDTO = null;
        if (CustomsStat.ZJ_PORT_EXCEPTION.getValue().equals(customsStatus)) {
            OrderSearch search = new OrderSearch();
            search.setCurrentPage(1);
            search.setPageSize(20);
            search.setQueryType("declareOrderNo");
            search.setQueryInfo(declareOrderNo);
            Page<OrderEsDO> paging = orderEsDao.paging(search);
            List<OrderEsDO> content = paging.getContent();
            if (CollectionUtils.isEmpty(content)) {
                return "订单不存在";
            }
            OrderEsDO next = content.iterator().next();
            orderDTO = orderService.findBySnSection(next.getSn());
        } else {
            CompanyDTO ebp = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(ebpCode);
            if (ebp == null) {
                return "电商平台不存在";
            }
            orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), declareOrderNo, 0L);
        }
        if (orderDTO == null) {
            return "订单不存在";
        }
        CustomsOrderDTO customsOrderDTO = this.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        // Step::打印订单轨迹日志 TODO:暂无
        log.info("[op:OrderServiceImpl-receiveInventory] order log finish, declareSn={}", receive.getDeclareOrderNo());
        // Step::更新清关状态及回执 根据更新时间来取舍 只保存最新的回执
        if (customsOrderDTO.getLastCustomsTime() != null && new DateTime(customsTime).isBefore(new DateTime(customsOrderDTO.getLastCustomsTime()))) {
            log.warn("当前回执时间迟于最后一次回执时间，略过不做清关状态更新，{}", JSON.toJSONString(receive));
        } else {
            // 更新清关状态
            this.updateByCustomsActive(
                    customsOrderDTO.getId()
                    , customsStatus
                    , customsDetail
                    , new DateTime(customsTime).toDate()
                    , customsOrderDTO.getCreateTime()
            );
        }
        log.info("[op:OrderServiceImpl-receiveInventory] inventory customs receive finish, declareSn={}", receive.getDeclareOrderNo());
        // Step::海关状态映射
        // 清单映射Code拼接规则 DECLARE_INVENTORY:[清关状态](:[详细状态码])    例：[Code:2600;Desc:放行]==>DECLARE_INVENTORY:800:2600
        // 支持以下几种格式:
        // 无详细状态码:清单新增成功
        // 4位审单码:[Code:2600;Desc:放行]
        // 5位审单码:[Code:13121;Desc:撤单申请退回清单]
        // 5位校验码:22001:申报企业需与企业备案时所填写名称一致
        String mappingCode = RouteActionEnum.DECLARE_ORDER.getCode() + ":" + receive.getCustomsStatus();
        boolean examineFlag = receive.getCustomsDetail().length() >= 5 && "Code".equals(receive.getCustomsDetail().substring(1, 5));
        if (examineFlag) {
            mappingCode += ":";
            if (receive.getCustomsDetail().length() >= 11 && ";".equals(receive.getCustomsDetail().substring(10, 11))) {
                mappingCode += receive.getCustomsDetail().substring(6, 10);
            } else {
                String code = this.getCustomsCode(receive.getCustomsDetail());
                mappingCode += code;
            }
        } else {
            examineFlag = receive.getCustomsDetail().length() >= 6 && ":".equals(receive.getCustomsDetail().substring(5, 6));
            if (examineFlag) {
                mappingCode += ":";
                mappingCode += receive.getCustomsDetail().substring(0, 5);
            }
        }
        CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode(mappingCode);
        // Step::mapping不存在报警
        if (mappingDTO == null) {
            eventBus.post(new MappingEmptyEvent(mappingCode, JSON.toJSONString(receive)));
            mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("UN_KNOW");
        } else {
            mappingDTO = customsStatusMappingService.getFinalMappingCode(mappingDTO, customsDetail);
        }
        log.info("[op:OrderServiceImpl-receiveInventory] mapping finish, declareSn={}", receive.getDeclareOrderNo());
        // Step::申报单及申报项状态处理
        /**
         * 规则：
         * 申报项状态：
         * 放行：可更改待审报；申报中；申报失败
         * 申报失败：可更改申报中
         */
        Integer resultStatus = mappingDTO.getStatus();
        Boolean exceptionFlag = mappingDTO.getExceptionFlag();

        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        messageService.createMessage(MessageType.ORDER_CUSTOMS_ORDER,
                Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel()),
                orderDTO.getDeclareOrderNo(), JSON.toJSONString(
                        new OrderActiveInfo(orderDTO.getId())
                                .buildCustomsInfo(customsStatus, customsDetail, new DateTime(customsTime).toDate())),
                "");
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(orderDTO.getId(), orderDTO.getSn(), orderDTO.getDeclareOrderNo());
        if (CustomsActionStatus.DEC_SUCCESS.getValue().equals(resultStatus)) {
            // 订单转完成
            if (CustomsActionStatus.DEC_SUCCESS.getValue() == customsOrderDTO.getStatus()) {
                return "订单已申报，忽略重复回传";
            } else {
                this.updateByCustomsPass(customsOrderDTO.getId(), customsOrderDTO.getCreateTime());
//                orderDeclareMQProducer.send(orderDTO.getSn(), orderExtra.getSubmit().getRouteCode());
                if (Objects.nonNull(ebcCode)) {
                    try {
                        log.info("ebcCode={} sender={} 确定最终申报方式", ebcCode, receive.getSender());
                        boolean updated = CustomsDeclareUtils.determineFinalDeclareRecord(orderDTO, ebcCode, receive.getSender(), DeclareEnum.CUSTOMS_ORDER);
                        if (updated) {
                            log.info("最终申报方式改变 更新数据库");
                            orderService.updateOrderDeclareRecord(orderDTO);
                        }
                    } catch (Exception e) {
                        log.error("ebcCode={} sender={} 确定最终申报方式异常", ebcCode, receive.getSender(), e);
                        return null;
                    }
                }
                orderDeclareMQProducer.send(orderDTO);
            }
        } else if (CustomsActionStatus.DEC_FAIL.getValue().equals(resultStatus)) {
            if (!customsOrderDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                return "订单状态非申报中，不接收异常或失败信息";
            }
            // 订单转失败
            if (exceptionFlag) {
                if (!customsOrderDTO.getStatus().equals(CustomsActionStatus.DEC_ING.getValue())) {
                    return "订单状态非申报中，不接收异常信息";
                }
                // 订单转异常处理
                if (!orderDTO.getStatus().equals(OrderStatus.DEC_ING.getValue())) {
                    return "申报单状态不允许清关异常变更";
                }
                this.updateStatusSection(customsOrderDTO.getId(), CustomsActionStatus.DEC_FAIL.getValue(), customsOrderDTO.getCreateTime());
                //更新回执时间
                CustomsOrderDO template = new CustomsOrderDO();
                template.setLastCustomsTime(new Date());
                this.updateByIdSection(customsOrderDTO.getId(), template, customsOrderDTO.getCreateTime());
                orderService.addExceptionSection(customsOrderDTO.getOrderId(), mappingDTO.getId(), customsDetail, orderDTO.getCreateTime());
            } else {
                if (OrderFlowBook.order.operationAllowed(orderDTO.getStatus(), OrderAction.FAIL.getOperation())) {
                    this.updateStatusSection(customsOrderDTO.getId(), CustomsActionStatus.DEC_FAIL.getValue(), customsOrderDTO.getCreateTime());
                    //更新回执时间
                    CustomsOrderDO template = new CustomsOrderDO();
                    template.setLastCustomsTime(new Date());
                    template.setStatus(CustomsActionStatus.DEC_FAIL.getValue());
                    this.updateByIdSection(customsOrderDTO.getId(), template, customsOrderDTO.getCreateTime());

                    orderService.updateStatusSection(customsOrderDTO.getOrderId(), OrderFlowBook.order.target(orderDTO.getStatus(), OrderAction.FAIL.getOperation()), orderDTO.getCreateTime());
                    orderService.clearExceptionSection(customsOrderDTO.getOrderId(), orderDTO.getCreateTime());
                } else {
                    // 例如已取消，已完成等状态，不处理清关不可逆失败
                    return "申报单状态不允许清关失败变更";
                }
            }
        } else if (CustomsActionStatus.DEC_ING.getValue().equals(resultStatus)) {
            return "中间状态，不做处理";
        } else {
            return "映射状态定义不合法";
        }
        log.info("[op:OrderServiceImpl-receiveInventory] full finish, declareSn={}", receive.getDeclareOrderNo());
        return "回传成功";
    }

    /**
     * 获取海关编码
     * [Code:130105;Desc:系统异常，请稍后重试]
     *
     * @param detail 海关回执信息
     * @return
     */
    private String getCustomsCode(String detail) {
        Matcher matcher = codePattern.matcher(detail);
        if (matcher.find()) {
            String codeValue = matcher.group();
            Matcher codeValueMattcher = codeValuePattern.matcher(codeValue);
            if (codeValueMattcher.find()) {
                return codeValueMattcher.group();
            }
        }
        return null;
    }

    @Override
    @PageSelect
    public ListVO<CustomsOrderDTO> paging(CustomsOrderSearch search) {
        log.info("[op:CustomsOrderServiceImpl-paging] search={}", search);
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(new Date());
        Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();

        if (search.getStatus() != null && search.getStatus() != 0) {
            criteria.andEqualTo("status", search.getStatus());
        }

        if (!StringUtils.isEmpty(search.getCustomsStatusDesc())) {
            criteria.andEqualTo("customsStatus", search.getCustomsStatusDesc());
        }

        if (!StringUtils.isEmpty(search.getQueryType()) && "declareOrderNo".equalsIgnoreCase(search.getQueryType())) {
            criteria.andIn("declareOrderNo", Splitter.on(",").splitToList(search.getQueryInfo()));
        } else if (!StringUtils.isEmpty(search.getQueryType()) && "outOrderNo".equalsIgnoreCase(search.getQueryType())) {
            List<OrderDTO> orderDTOList = orderService.listByOutOrderNo90Days(Splitter.on(",").splitToList(search.getQueryInfo()));
            if (CollUtil.isNotEmpty(orderDTOList)) {
                criteria.andIn("orderId", orderDTOList.stream().map(OrderDTO::getId).collect(Collectors.toList()));
            } else {
                // 没找到订单
                criteria.andIn("orderId", Collections.singletonList(0L));
            }
        }

//        Long declareFrom = LongUtil.getFrom(search.getCreateFrom(),search.getDeclareFrom());
//        Long declareTo = LongUtil.getEnd(search.getCreateFrom(),search.getDeclareTo());
//        if (!LongUtil.isNone(declareFrom) && !LongUtil.isNone(declareTo)){
//            criteria.andBetween("createTime",new DateTime(declareFrom).toDate(),new DateTime(declareTo).toDate());
//        }
//
//        Long createFrom = LongUtil.getFrom(search.getCreateFrom(),search.getCreateTo());
//        Long createTo = LongUtil.getEnd(search.getCreateFrom(),search.getCreateTo());
//        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)){
//            criteria.andBetween("lastDeclareTime",new DateTime(createFrom).toDate(),new DateTime(createTo).toDate());
//        }
        example.and(criteria);
        PageMethod.startPage(search.getCurrentPage(), search.getPageSize());
        List<CustomsOrderDO> list = customsOrderBaseService.selectByExample(example);
        ListVO<CustomsOrderDTO> result = new ListVO<>();
        result.setDataList(CopyUtil.copyList(list, CustomsOrderDTO.class));
        // 分页
        PageInfo<CustomsOrderDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public void rePush(String sn, Boolean sendNow) {
        log.info("[op:OrderServiceImpl-resetDeclare] reset with action sn={}, action={}", sn, RouteActionEnum.DECLARE_ORDER.getDesc());
        CustomsOrderDTO customsOrderDTO = this.findBySnSection(sn);
        if (customsOrderDTO == null) {
            throw new ArgsErrorException("对不起，非法操作重推订单不存在");
        }
        OrderDTO orderDTO = orderService.findBySnSection(customsOrderDTO.getOrderSn());
        if (orderDTO.getStatus().equals(OrderStatus.DEC_ING.getValue()) &&
                customsOrderDTO.getLastDeclareTime() != null &&
                new DateTime(customsOrderDTO.getLastDeclareTime()).isAfter(DateTime.now().minusMinutes(10))
        ) {
            log.info("[op:OrderServiceImpl-resetDeclare] not reset. last update time={}", customsOrderDTO.getUpdateTime());
            // 申报中，且更新时间小于十分钟，则不允许重推
            throw new ArgsErrorException("重推间隔需大于十分钟");
        }
        if (!customsOrderDTO.getStatus().equals(CustomsActionStatus.DEC_WAIT.getValue())) {
            this.updateStatusSection(customsOrderDTO.getId(), CustomsActionStatus.DEC_WAIT.getValue(), customsOrderDTO.getCreateTime());
        }
        //更新申报时间
//        CustomsOrderDO template = new CustomsOrderDO();
        // template.setLastDeclareTime( new Date());
//        this.updateByIdSection(customsOrderDTO.getId(), template, customsOrderDTO.getCreateTime());

        if (sendNow) {
            orderService.clearExceptionSection(orderDTO.getId(), orderDTO.getCreateTime());
            orderDeclareMQProducer.send(orderDTO);
        }
        log.info("[op:OrderServiceImpl-resetDeclare] reset success. last update time={}", customsOrderDTO.getUpdateTime());
    }

    @Override
    public List<CustomsOrderDTO> findByEbpIdAndDate(String ebpId, String begin, String end, String offset) {
        List<CustomsOrderDTO> customsOrderDTOS = null;
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            TimeRangeParam timeRangeParam = new TimeRangeParam();
            timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sdf.parse(begin)));
            timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sdf.parse(end)));
            Example example = ShardingBaseExampleBuilder.getExample(CustomsOrderDO.class, timeRangeParam);
            example.and(example.createCriteria().andEqualTo("ebpId", ebpId)
                    .andGreaterThanOrEqualTo("createTime", sdf.parse(begin))
                    .andLessThanOrEqualTo("createTime", sdf.parse(end)));
            List<CustomsOrderDO> customsOrderDOS = customsOrderBaseService.selectByExample(example);
            customsOrderDTOS = customsOrderDOS.stream().map(c -> {
                CustomsOrderDTO customsOrderDTO = buildDTO(c);
                return customsOrderDTO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("findByEbpIdAndDate");
        }
        return customsOrderDTOS;
    }

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    public void pddRepair(CustomsOrderDTO l) {
        pddDeclareService.pddRepair(l);
    }

    @Override
    public void pddRepairConsignee(CustomsOrderDTO l) {
        pddDeclareService.pddRepairConsignee(l);
    }

    private void updateByIdSection(Long id, CustomsOrderDO template, Date sectionDate) {
        if (LongUtil.isNone(id)) {
            throw new RuntimeException("ID不能为空");
        }
        // Step::初始化时间区间
        if (sectionDate == null) {
            throw new RuntimeException("分表区间时间不能为空");
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(sectionDate));
        //timeRangeParam.setEndDate(sectionDate);
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(sectionDate));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        example.and(criteria);
        customsOrderBaseService.updateByExampleSelective(template, example);
    }

    private CustomsOrderDTO buildDTO(CustomsOrderDO item) {
        if (item == null) {
            return null;
        } else {
            CustomsOrderDTO customsOrderDTO = new CustomsOrderDTO();
            BeanUtils.copyProperties(item, customsOrderDTO);
            return customsOrderDTO;
        }
    }
}