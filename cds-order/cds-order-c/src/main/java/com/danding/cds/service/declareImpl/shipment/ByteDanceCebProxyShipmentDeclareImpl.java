package com.danding.cds.service.declareImpl.shipment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.customs.declare.ByteDanceDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 字节CEB代理运单申报
 **/
@Service("BYTE_DANCE_CEB_PROXY_SHIPMENT_DECLARE")
@Slf4j
public class ByteDanceCebProxyShipmentDeclareImpl extends ShipmentDeclareAbstract {

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("字节CEB运单代理申报，测试环境MOCK");
        byteDanceDeclareService.cloudDeclareMock(info);
        return null;
    }

    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("字节CEB运单代理申报，info={}", JSON.toJSONString(info));
        byteDanceDeclareService.cloudLogisticsDeclare(info);
        return null;
    }
}
