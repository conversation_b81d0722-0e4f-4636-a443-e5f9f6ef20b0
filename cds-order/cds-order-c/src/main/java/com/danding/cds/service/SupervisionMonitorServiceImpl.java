package com.danding.cds.service;

import java.util.Arrays;
import java.util.Objects;

import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.SupervisionMonitorService;
import com.danding.monitor.packageout.api.PackageOutRpcClient;
import com.danding.monitor.packageout.dto.cmd.ChangeRemarkCmd;
import com.danding.monitor.packageout.dto.enums.SystemRemarkEnum;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description: 时效监管
 * @date 2023/3/8 22:14
 */
@Slf4j
@Service
public class SupervisionMonitorServiceImpl implements SupervisionMonitorService {
    @DubboReference
    private PackageOutRpcClient packageOutRpcClient;

    @Override
    public void changeCancelByGsNo(String gsNo) {
        if (Objects.isNull(gsNo)) {
            return;
        }
        ChangeRemarkCmd changeRemarkCmd = new ChangeRemarkCmd();
        changeRemarkCmd.setGlobalNoList(Arrays.asList(gsNo));
        changeRemarkCmd.setRemarkId(SystemRemarkEnum.CANCEL.getId());
        log.info("changeCancelByGsNo reqVo={}", JSON.toJSONString(changeRemarkCmd));
        packageOutRpcClient.changeRemark(changeRemarkCmd);
    }
}
