package com.danding.cds.service.declareImpl.cancel;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.cancel.InventoryCancelDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.service.customs.declare.InCloudDeclareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 京东CEB清单取消代理申报
 **/
@Service("JD_IN_CLOUD_CANCEL_DECLARE_COMMON")
@Slf4j
public class JdInCloudICancelDeclareCommonImpl extends InventoryCancelDeclareAbstract {
    /**
     * 测试用
     */
    @Autowired
    private InCloudDeclareService inCloudDeclareService;

    @Override
    protected InventoryCancelResult mockDeclareTest(WarpCancelOrderInfo info) {
        log.info("京东CEB清单取消代理申报 测试环境-MOCK info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }

    /**
     * @param info 数据
     * @return
     */
    @Override
    protected InventoryCancelResult declare(WarpCancelOrderInfo info) {
        log.info("京东CEB清单取消 代理申报 info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }
}
