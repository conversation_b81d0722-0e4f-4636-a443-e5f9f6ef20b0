package com.danding.cds.service.customs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.bean.dto.CalloffRefundGoodsInfoDTO;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.bean.vo.CalloffEditRefundGoodsInfoReqVo;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.Assert;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.customs.refund.api.enums.RefundOrderEnum;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.base.bean.dao.CustomsInventoryCalloffDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.mapper.CustomsInventoryCalloffMapper;
import com.danding.cds.order.base.service.CullOrderBaseService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.mq.producer.JDCalloffOrderProducer;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.dto.JdCustomsOrderMsgDataDTO;
import com.danding.cds.v2.bean.dto.LinkCustomsRefundOrderMessageNotifyDTO;
import com.danding.cds.v2.bean.vo.req.CancelOverTimeNotifyReqVo;
import com.danding.cds.v2.bean.vo.req.RefundWarehouseCancelReq;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.common.utils.DateUtils;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Author: Raymond
 * @Date: 2020/10/14 10:25
 * @Description:
 */

@Slf4j
@Service
@RefreshScope
public class CustomsInventoryCalloffServiceImpl implements CustomsInventoryCalloffService {

    @Resource
    private CustomsInventoryCalloffMapper customsInventoryCalloffMapper;

    @Autowired
    private RefundOrderService refundOrderService;

    @Autowired
    private CustomsInventoryCancelService customsInventoryCancelService;

    @Autowired
    private CustomsInventoryService customsInventoryService;

    @Autowired
    private OrderService orderService;

    @DubboReference
    private SequenceService sequenceService;

    @Resource
    private OrderOwnerMappingService orderOwnerMappingService;

    @Resource
    private CullOrderBaseService cullOrderBaseService;

    @Resource
    private RedisTemplate redisTemplate;

    private final static String JD_CANCEL_ORDER_KEY = "CCS:JdCancelOrder";
    private final static String JD_CANCEL_ORDER_KEY_DETATEILS = "CCS:JdCancelOrder:JD:details";

    @Resource
    private JDCalloffOrderProducer jdCalloffOrderProducer;

    @Autowired
    private BaseDataService baseDataService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CompanyService companyService;

    @Resource(name = "calloffCountThreadExecutor")
    private ThreadPoolTaskExecutor calloffCountThreadExecutor;

    @Override
    public CustomsInventoryCalloffDTO findById(Long id) {
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        criteria.andEqualTo("id", id);
        CustomsInventoryCalloffDTO _info = new CustomsInventoryCalloffDTO();
        CustomsInventoryCalloffDO customsInventoryCalloffDTO = customsInventoryCalloffMapper.selectOneByExample(example);
        if (customsInventoryCalloffDTO == null) {
            return null;
        }
        BeanUtils.copyProperties(customsInventoryCalloffDTO, _info);
        return _info;
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findById(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("id", idList);
        List<CustomsInventoryCalloffDO> customsInventoryCalloffDOS = customsInventoryCalloffMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsInventoryCalloffDOS, CustomsInventoryCalloffDTO.class);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findListByOrderId(Long orderId) {
        if (LongUtil.isNone(orderId)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        criteria.andEqualTo("orderId", orderId);

        List<CustomsInventoryCalloffDO> calloffDoList = customsInventoryCalloffMapper.selectByExample(example);
        List<CustomsInventoryCalloffDTO> calloffList = new ArrayList<CustomsInventoryCalloffDTO>();
        for (CustomsInventoryCalloffDO item : calloffDoList) {
            CustomsInventoryCalloffDTO _info = new CustomsInventoryCalloffDTO();
            BeanUtils.copyProperties(item, _info);
            calloffList.add(_info);
        }


        return calloffList;
    }

    @Override
    public CustomsInventoryCalloffDTO findByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return null;
        }
        CustomsInventoryCalloffDO customsInventoryCalloffDO = new CustomsInventoryCalloffDO();
        customsInventoryCalloffDO.setOrderId(orderId);
        List<CustomsInventoryCalloffDO> customsInventoryCalloffDOS = customsInventoryCalloffMapper.select(customsInventoryCalloffDO);
        if (!CollectionUtils.isEmpty(customsInventoryCalloffDOS)) {
            CustomsInventoryCalloffDO calloffDO = customsInventoryCalloffDOS.get(0);
            CustomsInventoryCalloffDTO customsInventoryCalloffDTO = ConvertUtil.beanConvert(calloffDO, CustomsInventoryCalloffDTO.class);
            return customsInventoryCalloffDTO;
        }
        return null;
    }

    @Override
    public List<CustomsInventoryCalloffDTO> findListByOrderIdList(List<Long> orderIdList) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            return new ArrayList<>();
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("deleted", 0)
                .andIn("orderId", orderIdList);
        List<CustomsInventoryCalloffDO> customsInventoryCalloffDOS = customsInventoryCalloffMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(customsInventoryCalloffDOS)) {
            return new ArrayList<>();
        }
        return ConvertUtil.listConvert(customsInventoryCalloffDOS, CustomsInventoryCalloffDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<CustomsInventoryCalloffDTOV2> paging(CustomsInventoryCalloffSearch search) {
        Example example = buildExample(search);
        if (Objects.nonNull(search.getOverTimeSearchType())) {
            example = buildSearchTypeExample(search, example);
        }
        example.orderBy("id").desc();
        List<CustomsInventoryCalloffDO> list = customsInventoryCalloffMapper.selectByExample(example);
        ListVO<CustomsInventoryCalloffDTOV2> result = new ListVO<>();
        List<CustomsInventoryCalloffDTOV2> customsInventoryCalloffDTOS = JSON.parseArray(JSON.toJSONString(list), CustomsInventoryCalloffDTOV2.class);
        // 分页
        if (!CollectionUtils.isEmpty(customsInventoryCalloffDTOS)) {
            customsInventoryCalloffDTOS = orderOwnerMappingService.getWareHouseAndOwnerInventoryCallOff(customsInventoryCalloffDTOS);
        }
        result.setDataList(customsInventoryCalloffDTOS);
        PageInfo<CustomsInventoryCalloffDTO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example buildSearchTypeExample(CustomsInventoryCalloffSearch search, Example example) {

        Example searchTypeExample = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = searchTypeExample.createCriteria();
        String overTimeSearchType = search.getOverTimeSearchType();
        Date hourEnd;
        Date hourBegin;
        Date dayEnd;
        Date dayBegin;
        switch (overTimeSearchType) {
//            case "cancelCalloffWaiting12hTo24h":
//                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
//                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                search.setCancelTimeOut(12);
//                hourEnd = DateUtils.addHour(new Date(), -24);
//                hourBegin = DateUtils.addHour(new Date(), -12);
//                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
//                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                criteria.andLessThanOrEqualTo("createTime", hourBegin);
//                criteria.andGreaterThanOrEqualTo("createTime", hourEnd);
//                break;
//            case "cancelCalloffWaiting24hAndMore":
//                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
//                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                search.setCancelTimeOut(24);
//                hourEnd = DateUtils.addHour(new Date(), -24);
//                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
//                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                criteria.andLessThanOrEqualTo("createTime", hourEnd);
//                break;
            case "cancelCalloffWaiting3dTo5d":
                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                search.setCancelTimeOut(3 * 24);
                hourEnd = DateUtils.addHour(new Date(), -5 * 24);
                hourBegin = DateUtils.addHour(new Date(), -3 * 24);
                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                criteria.andLessThanOrEqualTo("createTime", hourBegin);
                criteria.andGreaterThanOrEqualTo("createTime", hourEnd);
                break;
            case "cancelCalloffWaiting5dAndMore":
                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                search.setCancelTimeOut(5 * 24);
                hourEnd = DateUtils.addHour(new Date(), -5 * 24);
                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                criteria.andLessThanOrEqualTo("createTime", hourEnd);
                break;
            case "refundCalloffWaiting7dTo10d":
                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()));
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                search.setCancelTimeOut(7 * 24);
                dayEnd = DateUtils.addDay(new Date(), -10);
                dayBegin = DateUtils.addDay(new Date(), -7);
                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                criteria.andLessThanOrEqualTo("createTime", dayBegin);
                criteria.andGreaterThanOrEqualTo("createTime", dayEnd);
                break;
            case "refundCalloffWaiting10dAndMore":
                search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()));
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                search.setCancelTimeOut(10 * 24);
                dayEnd = DateUtils.addDay(new Date(), -10);
                criteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                criteria.andLessThanOrEqualTo("createTime", dayEnd);
                break;
            case "calloffing24hTo36h":
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                search.setCancelTimeOut(24);
                hourEnd = DateUtils.addHour(new Date(), -36);
                hourBegin = DateUtils.addHour(new Date(), -24);
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                criteria.andLessThanOrEqualTo("updateTime", hourBegin);
                criteria.andGreaterThanOrEqualTo("updateTime", hourEnd);
                break;
            case "calloffing36hAndMore":
                search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                search.setCancelTimeOut(36);
                hourEnd = DateUtils.addHour(new Date(), -36);
                criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                criteria.andLessThanOrEqualTo("updateTime", hourEnd);
                break;
            default:
                break;
        }
        example.and(criteria);
        return example;
    }

    @Override
    public InventoryCallOffOverTimeCountVO inventoryCalloffOverTimeCount(CustomsInventoryCalloffSearch search) {
        InventoryCallOffOverTimeCountVO countVO = new InventoryCallOffOverTimeCountVO();
        Example example = buildExample(search);
        boolean countCancelCalloffWaiting3dTo5d = true;
        boolean countCancelCalloffWaiting5dAndMore = true;
        boolean countRefundCalloffWaiting7dTo10d = true;
        boolean countRefundCalloffWaiting10dAndMore = true;
        boolean countCalloffing24hTo36h = true;
        boolean countCalloffing36hAndMore = true;
        String overTimeSearchType = search.getOverTimeSearchType();
        if (Objects.nonNull(overTimeSearchType)) {
            switch (overTimeSearchType) {
//                case "cancelCalloffWaiting12hTo24h":
//                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
//                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                    search.setCancelTimeOut(12);
//                    break;
//                case "cancelCalloffWaiting24hAndMore":
//                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
//                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//                    search.setCancelTimeOut(24);
//                    break;
                case "cancelCalloffWaiting3dTo5d":
                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                    search.setCancelTimeOut(3 * 24);
                    break;
                case "cancelCalloffWaiting5dAndMore":
                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()));
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                    search.setCancelTimeOut(5 * 24);
                    break;
                case "refundCalloffWaiting7dTo10d":
                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()));
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                    search.setCancelTimeOut(7 * 24);
                    break;
                case "refundCalloffWaiting10dAndMore":
                    search.setCalloffType(Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()));
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
                    search.setCancelTimeOut(10 * 24);
                    break;
                case "calloffing24hTo36h":
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                    search.setCancelTimeOut(24);
                    break;
                case "calloffing36hAndMore":
                    search.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
                    search.setCancelTimeOut(36);
                    break;
                default:
                    break;
            }
        }
        Integer calloffType = search.getCalloffType();
        Integer cancelTimeOut = search.getCancelTimeOut();

        if (Objects.nonNull(calloffType)) {
            // 1. 撤单
            if (Objects.equals(calloffType, Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode()))) {
                if (Objects.equals(search.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())) {
                    if (Objects.nonNull(cancelTimeOut)) {
                        if (cancelTimeOut == 3 * 24) {
                            countRefundCalloffWaiting7dTo10d = false;
                            countRefundCalloffWaiting10dAndMore = false;
                            countCalloffing24hTo36h = false;
                            countCalloffing36hAndMore = false;
                        } else if (cancelTimeOut >= 5 * 24) {
                            countRefundCalloffWaiting7dTo10d = false;
                            countRefundCalloffWaiting10dAndMore = false;
                            countCalloffing24hTo36h = false;
                            countCalloffing36hAndMore = false;
                        }
                    }
                }
            } else if (Objects.equals(calloffType, Integer.valueOf(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()))) {
                if (Objects.equals(search.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())) {
                    if (Objects.nonNull(cancelTimeOut)) {
                        if (cancelTimeOut == 7 * 24) {
                            countCancelCalloffWaiting5dAndMore = false;
                            countCancelCalloffWaiting3dTo5d = false;
                            countCalloffing24hTo36h = false;
                            countCalloffing36hAndMore = false;
                        } else if (cancelTimeOut >= 10 * 24) {
                            countCancelCalloffWaiting5dAndMore = false;
                            countCancelCalloffWaiting3dTo5d = false;
                            countCalloffing24hTo36h = false;
                            countCalloffing36hAndMore = false;
                        }
                    } else {
                        countCancelCalloffWaiting5dAndMore = false;
                        countCancelCalloffWaiting3dTo5d = false;
                        countCalloffing24hTo36h = false;
                        countCalloffing36hAndMore = false;
                    }
                }
            }
        }
        if (Objects.equals(search.getCalloffStatus(), InventoryCalloffStatusEnum.CALLOFF_ING.getCode())) {
            if (Objects.nonNull(cancelTimeOut)) {
                if (cancelTimeOut == 12 || cancelTimeOut == 24) {
                    countCancelCalloffWaiting3dTo5d = false;
                    countCancelCalloffWaiting5dAndMore = false;
                    countRefundCalloffWaiting7dTo10d = false;
                    countRefundCalloffWaiting10dAndMore = false;
                } else if (cancelTimeOut > 24) {
                    countCancelCalloffWaiting3dTo5d = false;
                    countCancelCalloffWaiting5dAndMore = false;
                    countRefundCalloffWaiting7dTo10d = false;
                    countRefundCalloffWaiting10dAndMore = false;
                }
            }
        }

        List<CompletableFuture<Void>> futureList = new ArrayList<>();

        if (countCancelCalloffWaiting3dTo5d) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countCalloffWaitingTimeOut(example, -3 * 24, -5 * 24);
                countVO.setCancelCalloffWaiting3dTo5d(count);
            }));
        }
        if (countCancelCalloffWaiting5dAndMore) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countCalloffWaitingTimeOut(example, -5 * 24);
                countVO.setCancelCalloffWaiting5dAndMore(count);
            }));
        }
        if (countRefundCalloffWaiting7dTo10d) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countRefundCalloffWaitingTimeOut(example, -7, -10);
                countVO.setRefundCalloffWaiting7dTo10d(count);
            }));
        }
        if (countRefundCalloffWaiting10dAndMore) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countRefundCalloffWaitingTimeOut(example, -10);
                countVO.setRefundCalloffWaiting10dAndMore(count);
            }));
        }
        if (countCalloffing24hTo36h) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countCalloffingTimeOut(example, -24, -36);
                countVO.setCalloffing24hTo36h(count);
            }));

        }
        if (countCalloffing36hAndMore) {
            futureList.add(CompletableFuture.runAsync(() -> {
                int count = countCalloffingTimeOut(example, -36);
                countVO.setCalloffing36hAndMore(count);
            }));

        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("获取取消单计数统计CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("获取取消单计数统计CompletableFuture结果异常" + e.getMessage(), e);
        }
        return countVO;
    }

    private Integer countCalloffWaitingTimeOut(Example example, Integer timeOffset) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date timeOutDate = DateUtils.addHour(new Date(), timeOffset);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
        subCriteria.andLessThanOrEqualTo("createTime", timeOutDate);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }

    private Integer countCalloffWaitingTimeOut(Example example, Integer begin, Integer end) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date hourEnd = DateUtils.addHour(new Date(), end);
        Date hourBegin = DateUtils.addHour(new Date(), begin);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
        subCriteria.andLessThanOrEqualTo("createTime", hourBegin);
        subCriteria.andGreaterThanOrEqualTo("createTime", hourEnd);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }

    private Integer countRefundCalloffWaitingTimeOut(Example example, Integer timeOffset) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date timeOutDate = DateUtils.addDay(new Date(), timeOffset);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
        subCriteria.andLessThanOrEqualTo("createTime", timeOutDate);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }

    private Integer countRefundCalloffWaitingTimeOut(Example example, Integer begin, Integer end) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date dayEnd = DateUtils.addDay(new Date(), end);
        Date dayBegin = DateUtils.addDay(new Date(), begin);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
        subCriteria.andLessThanOrEqualTo("createTime", dayBegin);
        subCriteria.andGreaterThanOrEqualTo("createTime", dayEnd);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }


    private Integer countCalloffingTimeOut(Example example, Integer timeOffset) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date timeOutDate = DateUtils.addHour(new Date(), timeOffset);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
        subCriteria.andLessThanOrEqualTo("updateTime", timeOutDate);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }


    private Integer countCalloffingTimeOut(Example example, Integer begin, Integer end) {
        Example timeExample = new Example(CustomsInventoryCalloffDO.class);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
        Date dayEnd = DateUtils.addHour(new Date(), end);
        Date dayBegin = DateUtils.addHour(new Date(), begin);
        Example.Criteria subCriteria = timeExample.createCriteria();
        subCriteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
        subCriteria.andLessThanOrEqualTo("updateTime", dayBegin);
        subCriteria.andGreaterThanOrEqualTo("updateTime", dayEnd);
        oredCriteria.forEach(timeExample::and);
        int count = customsInventoryCalloffMapper.selectCountByExample(timeExample);
        return count;
    }

    private Example buildExample(CustomsInventoryCalloffSearch search) {
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);
        // 账册Id列表
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("accountBookId", accountBookIdList);
        }
        //区内企业
        if (Objects.nonNull(search.getAreaCompanyId())) {
            criteria.andEqualTo("areaCompanyId", search.getAreaCompanyId());
        }
        /**
         * 取消类型
         */
        if (search.getCalloffType() != null) {
            criteria.andEqualTo("calloffType", search.getCalloffType());
        }
        /**
         *  取消状态
         */
        if (!StringUtils.isEmpty(search.getCalloffStatus())) {
            criteria.andEqualTo("calloffStatus", search.getCalloffStatus());
        }

        /**
         *  清单状态
         */
        if (!StringUtils.isEmpty(search.getCustomsStatus())) {
            criteria.andEqualTo("customsStatus", search.getCustomsStatus());
        }

        if (!StringUtils.isEmpty(search.getTenantId())) {
            criteria.andEqualTo("tenantId", search.getTenantId());
        }

        if (!StringUtils.isEmpty(search.getOper())) {
            criteria.andEqualTo("createBy", search.getOper());
        }

        /**
         *  出区状态
         */
        if (search.getExitRegionStatus() != null) {
            criteria.andEqualTo("exitRegionStatus", search.getExitRegionStatus());
        }

        if (!LongUtil.isNone(search.getEbcId())) {
            criteria.andEqualTo("ebcId", search.getEbcId());
        }
        if (!LongUtil.isNone(search.getAgentCompanyId())) {
            //清关企业
            criteria.andEqualTo("agentCompanyId", search.getAgentCompanyId());
        }

        if (search.getBeginCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCreateTime());
            criteria.andGreaterThanOrEqualTo("createTime", tempDate);
        }
        if (search.getEndCreateTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCreateTime());
            criteria.andLessThanOrEqualTo("createTime", tempDate);
        }

        if (search.getBeginCalloffTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getBeginCalloffTime());
            criteria.andGreaterThanOrEqualTo("calloffTime", tempDate);
        }
        if (search.getEndCalloffTime() != null) {
            Date tempDate = new Date();
            tempDate.setTime(search.getEndCalloffTime());
            criteria.andLessThanOrEqualTo("calloffTime", tempDate);
        }

        if (search.getLabel() != null) {
            String logisticsSn = cullOrderBaseService.findByStatus(CullStatusEnums.PENDING);
            if (Objects.nonNull(logisticsSn)) {
                if (Objects.nonNull(search.getNoType()) && search.getNoType() == 2) {
                    search.setNoStr(logisticsSn + "," + search.getNoStr());
                } else {
                    search.setNoType(2);
                    search.setNoStr(logisticsSn);
                }
            }

        }

        //0:申报单，1：清单编号, 2:运单编号
        if ((search.getNoType() != null) && (!StringUtils.isEmpty(search.getNoStr()))) {
            String[] nos = StringUtils.split(search.getNoStr(), ",");
            List<String> nosArray = Arrays.asList(nos);
            if (nosArray != null && nosArray.size() > 0) {
                if (search.getNoType() == 0) {
                    criteria.andIn("declareOrderNo", nosArray);
                } else if (search.getNoType() == 1) {
                    criteria.andIn("inventoryNo", nosArray);
                } else if (search.getNoType() == 2) {
                    criteria.andIn("logisticsNo", nosArray);
                }
            }
        }
        if (Objects.nonNull(search.getErpPhyWarehouseSn())) {
            List<EntityWarehouseDTO> warehouseDTOS = entityWarehouseService.findDTOByErpCode(search.getErpPhyWarehouseSn());
            List<Long> bookIdList = warehouseDTOS.stream().map(EntityWarehouseDTO::getCustomsBookId).distinct().collect(Collectors.toList());
            criteria.andIn("accountBookId", bookIdList);
        }

        // 特殊处理当选择取消类型为撤单时  默认只查询未出区数据，如果同时还选中已出区状态则将出区状态置成-1保证查询不到数据
//        if (search.getCalloffType() != null && search.getCalloffType() == 1) {
//            if (search.getExitRegionStatus() != null && search.getExitRegionStatus() == 1) {
//                criteria.andEqualTo("exitRegionStatus", -1);
//            } else {
//                criteria.andEqualTo("exitRegionStatus", 0);
//            }
//        }

//
//        if (Objects.nonNull(search.getCancelTimeOut())) {
//            // 当前时间 - 创建时间 && 取消单状态 {待取消、取消中}
//            Date timeOutDate = DateUtils.addDay(new Date(), search.getCancelTimeOut());
//            Example.Criteria subCriteria = example.createCriteria();
//            List<String> statusList = new ArrayList<>();
//            statusList.add(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//            statusList.add(InventoryCalloffStatusEnum.CALLOFF_ING.getCode());
//            subCriteria.andIn("calloffStatus", statusList);
//            subCriteria.andLessThanOrEqualTo("createTime", timeOutDate);
//            example.and(subCriteria);
//        }
        Example.Criteria orderTagCriteria = example.createCriteria();
        if (Objects.nonNull(search.getOrderTag())) {
            orderTagCriteria.andCondition("order_tag & " + search.getOrderTag() + " = ", search.getOrderTag());
        }
        example.and(orderTagCriteria);
        return example;
    }

    @Override
    public CustomsInventoryCalloffCountDTO selectCountByCondition(CustomsInventoryCalloffSearch search) {
        Example example = buildExample(search);
        List<CustomsInventoryCalloffDO> list = customsInventoryCalloffMapper.selectByExample(example);
        CustomsInventoryCalloffCountDTO dto = new CustomsInventoryCalloffCountDTO();
        for (CustomsInventoryCalloffDO item : list) {
            dto.totalCount++;
            if (InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode().equalsIgnoreCase(item.getCalloffStatus())) {
                dto.rejectCount++;
            }
            if (InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode().equalsIgnoreCase(item.getCalloffType())) {
                dto.returnCount++;
            } else if (InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode().equalsIgnoreCase(item.getCalloffType())) {
                dto.cacelCount++;
            }
        }
        return dto;
    }


    /**
     * 保税售后统计
     *
     * @return
     */
    @Override
    public CalloffCountDTO selectCalloffCount(String areaCompanyId) {
        CalloffCountDTO calloffCountDTO = new CalloffCountDTO();
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())
                .andEqualTo("deleted", false);
        if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
            criteria.andEqualTo("areaCompanyId", areaCompanyId);
        }
        calloffCountDTO.setCancelCount(customsInventoryCalloffMapper.selectCountByExample(example));
        calloffCountDTO.setCancelInfoCount(customsInventoryCancelService.selectCancelInfoCount(areaCompanyId));
        calloffCountDTO.setRefundOrder(refundOrderService.selectRefundOrderInfoCount(areaCompanyId));
        calloffCountDTO.setRefundInWarehouse(refundOrderService.selectRefundInWarehouseCount(areaCompanyId));
        return calloffCountDTO;
    }

    @Override
    public Map<String, CalloffAfterSalesCount> selectCalloffCountDetail(List<String> handlerIdList, Long
            beginTime, Long endTime) {
        ConcurrentHashMap<String, CalloffAfterSalesCount> result = new ConcurrentHashMap<>();
        //时间段的搜索条件
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria timeCriteria = example.createCriteria();
        timeCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        timeCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));

        List<CompletableFuture<Void>> futureList = new ArrayList<>();

        //遍历操作人统计数量
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, timeCriteria, result);
            futureList.add(CompletableFuture.runAsync(runnable, calloffCountThreadExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("售后统计-取消单组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("售后统计-取消单组合CompletableFuture结果异常" + e.getMessage(), e);
        }

        return result;
    }

    class Task extends TraceDataRunnable {
        private final String handlerId;

        private final Example.Criteria timeCriteria;

        private ConcurrentHashMap<String, CalloffAfterSalesCount> result;

        public Task(String handlerId, final Example.Criteria timeCriteria, final ConcurrentHashMap<String, CalloffAfterSalesCount> result) {
            super();
            this.handlerId = handlerId;
            this.timeCriteria = timeCriteria;
            this.result = result;
        }


        @Override
        public void proxy() {

            CalloffAfterSalesCount calloffAfterSalesCount = new CalloffAfterSalesCount();
            calloffAfterSalesCount.setUserId(handlerId);

            //需处理 - ALL+时间段
            Example example1 = new Example(CustomsInventoryCalloffDO.class);
            example1.createCriteria().andEqualTo("deleted", false);
            example1.and(timeCriteria);
            calloffAfterSalesCount.setPending(customsInventoryCalloffMapper.selectCountByExample(example1));

            //已处理 - 取消中+取消成功+取消失败+时间段
            List<String> processedStatus = Arrays.asList(InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode()
                    , InventoryCalloffStatusEnum.CALLOFF_ING.getCode()
                    , InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode());
            Example example2 = new Example(CustomsInventoryCalloffDO.class);
            example2.createCriteria().andEqualTo("deleted", false)
                    .andIn("calloffStatus", processedStatus)
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example2.and(timeCriteria);
            calloffAfterSalesCount.setProcessed(customsInventoryCalloffMapper.selectCountByExample(example2));

            //取消驳回 - 取消驳回+时间段
            Example example3 = new Example(CustomsInventoryCalloffDO.class);
            example3.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(timeCriteria);
            calloffAfterSalesCount.setReject(customsInventoryCalloffMapper.selectCountByExample(example3));

            //未处理 - 待取消+时间段
            Example example4 = new Example(CustomsInventoryCalloffDO.class);
            example4.createCriteria().andEqualTo("deleted", false)
                    .andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
            example4.and(timeCriteria);
            calloffAfterSalesCount.setUnprocessed(customsInventoryCalloffMapper.selectCountByExample(example4));

            result.put(handlerId, calloffAfterSalesCount);
        }
    }

    @Override
    public int create(CustomsInventoryCalloffDTO customsInventoryCalloffDTO) {
        CustomsInventoryCalloffDO customsInventoryCalloffDO = new CustomsInventoryCalloffDO();
        BeanUtils.copyProperties(customsInventoryCalloffDTO, customsInventoryCalloffDO);
        UserUtils.setCreateAndUpdateBy(customsInventoryCalloffDO);
        return customsInventoryCalloffMapper.insertSelective(customsInventoryCalloffDO);
    }

    @Override
    public int update(CustomsInventoryCalloffDTO customsInventoryCalloffDTO) {
        CustomsInventoryCalloffDO customsInventoryCalloffDO = new CustomsInventoryCalloffDO();
        BeanUtils.copyProperties(customsInventoryCalloffDTO, customsInventoryCalloffDO);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCalloffDO);
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", customsInventoryCalloffDTO.getId());
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCalloffDO);
        }
        customsInventoryCalloffDO.setUpdateTime(new Date());
        return customsInventoryCalloffMapper.updateByExampleSelective(customsInventoryCalloffDO, example);
    }

    @Override
    public int updateCustomsInventoryCalloffStatus(Long id, String calloffStatus, String calloffType, String
            calloffReason, String rejectReason) {
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        _info.setId(id);
        _info.setCalloffStatus(calloffStatus);
        _info.setCalloffType(calloffType);
        _info.setCalloffReason(calloffReason);
        _info.setRejectReason(rejectReason);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCalloffTime(new Date());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        return customsInventoryCalloffMapper.updateByPrimaryKeySelective(_info);
    }

    @Override
    public int updateCustomsInventoryCalloffStatus(List<Long> idList, String calloffStatus, String
            calloffType, String calloffReason, String rejectReason) {
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        _info.setCalloffStatus(calloffStatus);
        _info.setCalloffType(calloffType);
        _info.setCalloffReason(calloffReason);
        _info.setRejectReason(rejectReason);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCalloffTime(new Date());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andIn("id", idList);
        return customsInventoryCalloffMapper.updateByExampleSelective(_info, example);
    }

    @Override
    public int updateCancelStatusInfo(Long orderId, String cancelStatus) {
        if (Objects.isNull(orderId)) {
            return 0;
        }
        String calloffStatus = null;
        String calloffType = null;
        if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equalsIgnoreCase(cancelStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
        } else if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equalsIgnoreCase(cancelStatus)
                || InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue().equalsIgnoreCase(cancelStatus)
                || InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue().equalsIgnoreCase(cancelStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
        }
        return updateCustomsInventoryCalloffStatusByOrderId(orderId, null, null, calloffStatus, calloffType);
    }

    @Override
    public int updateCancelStatusInfo(Long orderId, String cancelStatus, String customsStatus, String
            customsStatusDetail) {
        if (Objects.isNull(orderId)) {
            return 0;
        }
        String calloffStatus = null;
        String calloffType = null;
        if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue().equalsIgnoreCase(cancelStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
        } else if (InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equalsIgnoreCase(cancelStatus)
                || InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue().equalsIgnoreCase(cancelStatus)
                || InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue().equalsIgnoreCase(cancelStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("orderId", orderId).andEqualTo("deleted", false);
        CustomsInventoryCalloffDO customsInventoryCalloffDO = new CustomsInventoryCalloffDO();
        customsInventoryCalloffDO.setCalloffStatus(calloffStatus);
        customsInventoryCalloffDO.setCalloffType(calloffType);
        customsInventoryCalloffDO.setCusAfterSalesCallback(customsStatus);
        customsInventoryCalloffDO.setCusAfterSalesCallbackDetail(customsStatusDetail);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCalloffDO);
        }
        customsInventoryCalloffDO.setUpdateTime(new Date());
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            customsInventoryCalloffDO.setCalloffTime(new Date());
        }
        return customsInventoryCalloffMapper.updateByExampleSelective(customsInventoryCalloffDO, example);
    }

    @Override
    public int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus) {
        String calloffStatus = null;
        String calloffType = null;
        if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue().equalsIgnoreCase(RefundCheckStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
        } else if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equalsIgnoreCase(RefundCheckStatus)
                || RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue().equals(refundStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
        }
        return updateCustomsInventoryCalloffStatusByOrderId(orderId, null, null, calloffStatus, calloffType);
    }

    @Override
    public int updateRefundStatusInfo(Long orderId, Integer refundStatus, String RefundCheckStatus, String
            refundCustomsStatus, String refundCustomsStatusDetail) {
        String calloffStatus = null;
        String calloffType = null;
        if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_PASS.getValue().equalsIgnoreCase(RefundCheckStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
        } else if (RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue().equalsIgnoreCase(RefundCheckStatus)
                || RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue().equals(refundStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode();
            calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("orderId", orderId);
        CustomsInventoryCalloffDO customsInventoryCalloffDO = new CustomsInventoryCalloffDO();
        customsInventoryCalloffDO.setOrderId(orderId);
        customsInventoryCalloffDO.setCalloffStatus(calloffStatus);
        customsInventoryCalloffDO.setCalloffType(calloffType);
        customsInventoryCalloffDO.setCusAfterSalesCallback(refundCustomsStatus);
        customsInventoryCalloffDO.setCusAfterSalesCallbackDetail(refundCustomsStatusDetail);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            customsInventoryCalloffDO.setCalloffTime(new Date());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCalloffDO);
        }
        customsInventoryCalloffDO.setUpdateTime(new Date());
        return customsInventoryCalloffMapper.updateByExampleSelective(customsInventoryCalloffDO, example);
    }

    @Override
    public int updateCustomsStatus(Long orderId, String customsStatus, Integer exitRegionStatus, Integer
            orderStatus) {
        String calloffStatus = null;
        String calloffType = null;
        String rejectReason = null;
        if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(customsStatus)) {
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode();
//            calloffType = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
        }
        if (Objects.equals(exitRegionStatus, 0) && Objects.equals(customsStatus, CustomsStat.CUSTOMS_PASS.getValue())) {
            // 如果清关状态为 放行，且未出区， 取消单： 拦截申报 --> 撤单
            calloffType = InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode();
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode();
        }
        if (!OrderStatus.CANCEL.getValue().equals(orderStatus) && CustomsStat.CUSTOMS_PASS.getValue().equals(customsStatus)) {
            //申报单状态不为【订单取消】且收到【放行】回执, 取消单状态变为【取消驳回】
            calloffStatus = InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode();
            rejectReason = "上游唤醒订单，海关放行";
        }
        return updateCustomsInventoryCalloffStatusByOrderId(orderId, customsStatus, exitRegionStatus, calloffStatus, calloffType, rejectReason);
    }

    @Override
    public int updateExitRegionStatus(Long orderId, Integer exitRegionStatus) {
        String type = null;

        // 添加当出区状态改成已出区时  判断取消单是否是撤单类型同时没有生成撤单记录时
        // 将取消单类型改成退货
        if (exitRegionStatus == 1) {
            CustomsInventoryCalloffDTO dto = null;
            List<CustomsInventoryCalloffDTO> list = this.findListByOrderId(orderId);
            log.info("i{}", JSON.toJSONString(list));
            if (CollUtil.isNotEmpty(list)) {
                dto = list.get(0);
            }
            if (dto != null && InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode().equals(dto.getCalloffType())) {
                CustomsInventoryCancelDTO cancel = customsInventoryCancelService.findByOrderId(dto.getOrderId());
                log.info("判断当前取消单的类型为撤单后去查询是否已经有撤单了产生了{}", cancel);
                if (cancel == null) {
                    type = InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode();
                }
            }
        }
        return updateCustomsInventoryCalloffStatusByOrderId(orderId, null, exitRegionStatus, null, type);
    }

    @Override
    public int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer
            exitRegionStatus, String calloffStatus, String calloffType) {
        return this.updateCustomsInventoryCalloffStatusByOrderId(orderId, customsStatus, exitRegionStatus, calloffStatus, calloffType, null);
    }

    @Override
    public int updateCustomsInventoryCalloffStatusByOrderId(Long orderId, String customsStatus, Integer
            exitRegionStatus, String calloffStatus, String calloffType, String rejectReason) {
        if (orderId == null) {
            throw new ArgsInvalidException(String.format("取消单状态更新,orderId不能为空"));
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
//        // Step::根据时间区间检索更新
        example.createCriteria().andEqualTo("orderId", orderId);
        CustomsInventoryCalloffDO customsInventoryCalloffDO = customsInventoryCalloffMapper.selectOneByExample(example);
        if (Objects.isNull(customsInventoryCalloffDO)) {
            log.info("updateCustomsInventoryCalloffStatusByOrderId 未查询到取消单");
            return 0;
        }
        if (Objects.nonNull(customsStatus)) {
            customsInventoryCalloffDO.setCustomsStatus(customsStatus);
        }
        if (Objects.nonNull(exitRegionStatus)) {
            customsInventoryCalloffDO.setExitRegionStatus(exitRegionStatus);
        }
        if (Objects.nonNull(calloffStatus)) {
            customsInventoryCalloffDO.setCalloffStatus(calloffStatus);
        }
        //任意退货类型变更 都清空完成时间 2024/1/8 沈费强说的
        if (!Objects.equals(customsInventoryCalloffDO.getCalloffType(), calloffType)) {
            customsInventoryCalloffDO.setCalloffTime(null);
        }
        if (Objects.nonNull(calloffType)) {
            customsInventoryCalloffDO.setCalloffType(calloffType);
        }
        if (InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode().equalsIgnoreCase(calloffStatus)) {
            customsInventoryCalloffDO.setRejectReason(rejectReason);
        }
        if (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equalsIgnoreCase(calloffStatus)) {
            customsInventoryCalloffDO.setCusAfterSalesCallback("");
            customsInventoryCalloffDO.setCusAfterSalesCallbackDetail("");
        }
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            customsInventoryCalloffDO.setCalloffTime(new Date());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(customsInventoryCalloffDO);
        }
        customsInventoryCalloffDO.setUpdateTime(new Date());
        log.info("updateCustomsInventoryCalloffStatusByOrderId do={}", JSON.toJSONString(customsInventoryCalloffDO));
        return customsInventoryCalloffMapper.updateByPrimaryKey(customsInventoryCalloffDO);
    }

    @Override
    public void updateCustomsInventoryCalloffStatusBatchByOrderId(List<Long> orderIdList, String customsStatus,
                                                                  Integer exitRegionStatus, String calloffStatus, String calloffType) {
        if (CollectionUtils.isEmpty(orderIdList)) {
            throw new ArgsInvalidException(String.format("取消单状态更新,orderId不能为空"));
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        // Step::根据时间区间检索更新
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("orderId", orderIdList);
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        _info.setCustomsStatus(customsStatus);
        _info.setExitRegionStatus(exitRegionStatus);
        _info.setCalloffStatus(calloffStatus);
        _info.setCalloffType(calloffType);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCalloffTime(new Date());
        }
        if (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCusAfterSalesCallback("");
            _info.setCusAfterSalesCallbackDetail("");
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        customsInventoryCalloffMapper.updateByExampleSelective(_info, example);
    }

    @Override
    public void updateCustomsInventoryCalloffStatusBatchByLogisticsNo(List<String> logisticsNoList, String
            customsStatus, Integer exitRegionStatus, String calloffStatus, String calloffType) {
        if (CollectionUtils.isEmpty(logisticsNoList)) {
            throw new ArgsInvalidException(String.format("取消单状态更新,orderId不能为空"));
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        // Step::根据时间区间检索更新
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("logisticsNo", logisticsNoList);
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        _info.setCustomsStatus(customsStatus);
        _info.setExitRegionStatus(exitRegionStatus);
        _info.setCalloffStatus(calloffStatus);
        _info.setCalloffType(calloffType);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCalloffTime(new Date());
        }
        if (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCusAfterSalesCallback("");
            _info.setCusAfterSalesCallbackDetail("");
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        customsInventoryCalloffMapper.updateByExampleSelective(_info, example);
    }

    @Override
    public void updateCalloffStatusByLogisticsNo(String logisticsNo, String customsStatus, Integer
            exitRegionStatus, String calloffStatus, String calloffType) {
        if (StringUtils.isEmpty(logisticsNo)) {
            throw new ArgsInvalidException(String.format("取消单状态更新,logisticsNo不能为空"));
        }
        Example example = new Example(CustomsInventoryCalloffDO.class);
        // Step::根据时间区间检索更新
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("logisticsNo", logisticsNo).andEqualTo("deleted", false);
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        _info.setCustomsStatus(customsStatus);
        _info.setExitRegionStatus(exitRegionStatus);
        _info.setCalloffStatus(calloffStatus);
        _info.setCalloffType(calloffType);
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffStatus)) {
            _info.setCalloffTime(new Date());
        }
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        customsInventoryCalloffMapper.updateByExampleSelective(_info, example);
    }

    @Override
    public List<Map<String, Object>> sumInventoryCalloff(Date beginTime, Date endTime) {
        return customsInventoryCalloffMapper.sumInventoryCalloff(beginTime, endTime);
    }

    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason) {
        OrderDTO orderDTO = orderService.findByIdFull(orderId);
        CustomsInventoryDTO customsInventoryDTO;
        if (StringUtil.isNotBlank(invSn)) {
            customsInventoryDTO = customsInventoryService.findBySnSection(invSn);
        } else {
            customsInventoryDTO = customsInventoryService.findByOrder(orderId, null);
        }
        return this.upset(orderDTO, customsInventoryDTO, calloffType, calloffStatus, reason);
    }


    @Override
    public Response<String> upset(OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO, String calloffType, String calloffStatus, String reason) {
        if (Objects.isNull(orderDTO)) {
            return new Response<>(-1, "订单单信息为空");
        }
        if (Objects.isNull(customsInventoryDTO)) {
            return new Response<>(-1, "关联清单不存在");
        }
        Long orderId = orderDTO.getId();
        List<CustomsInventoryCalloffDTO> calloffDtoList = findListByOrderId(orderId);
        CustomsInventoryCalloffDTO calloffDto = null;
        if (!calloffDtoList.isEmpty()) {
            calloffDto = calloffDtoList.get(0);
        }

        if (calloffDto != null && InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
            log.info("[op:CustomsInventoryCalloffService-upset] 取消单已经存在并且取消成功了，申报单号为：{}", orderId);
            return new Response<>(-1, "取消单已经存在并且取消成功了");
        }
        if (calloffDto != null && InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
            log.info("[op:CustomsInventoryCalloffService-upset] 取消单已经存在并且待处理或正在申报取消，申报单号为：{}", orderId);
            return new Response<>(-1, "取消单已经存在并且待处理或正在申报取消");
        }

//        CustomsInventoryDTO customsInventoryDTO = null;
//        if (StringUtil.isNotBlank(invSn)) {
//            customsInventoryDTO = customsInventoryService.findBySnSection(invSn);
//        } else {
//            customsInventoryDTO = customsInventoryService.findByOrder(orderId, null);
//        }
//        if (Objects.isNull(customsInventoryDTO)) {
//            return new Response<>(-1, "关联清单不存在");
//        }
//        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
        //生成取消单
        CustomsInventoryCalloffDTO customsInventoryCalloff = new CustomsInventoryCalloffDTO();
        customsInventoryCalloff.setOrderId(orderDTO.getId());
        customsInventoryCalloff.setOrderSn(orderDTO.getSn());
        customsInventoryCalloff.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        customsInventoryCalloff.setAccountBookId(customsInventoryDTO.getAccountBookId());
        customsInventoryCalloff.setSn(sequenceService.generateCalloffOrderSn());
        customsInventoryCalloff.setEbcId(customsInventoryDTO.getEbcId());
        customsInventoryCalloff.setAgentCompanyId(customsInventoryDTO.getAgentCompanyId());
        customsInventoryCalloff.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
        customsInventoryCalloff.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        customsInventoryCalloff.setInventoryNo(customsInventoryDTO.getInventoryNo());
        customsInventoryCalloff.setCustomsStatus(customsInventoryDTO.getCustomsStatus());
        customsInventoryCalloff.setExitRegionStatus(customsInventoryDTO.getExitRegionStatus());
        customsInventoryCalloff.setCustomsPassTime(customsInventoryDTO.getCustomsPassTime());
        if (orderDTO.getExtraJson() != null) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            if (orderExtra.getSubmit().getTenantOuterId() != null) {
                customsInventoryCalloff.setTenantId(orderExtra.getSubmit().getTenantOuterId());
            }
        }
        if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(customsInventoryDTO.getCustomsStatus())) {
            customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode());
            customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode());

        } else {
            customsInventoryCalloff.setCalloffType(calloffType);
            customsInventoryCalloff.setCalloffStatus(calloffStatus);
        }
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(customsInventoryCalloff.getCalloffStatus())) {
            customsInventoryCalloff.setCalloffTime(new Date());
        }
        customsInventoryCalloff.setCalloffReason(reason);
        customsInventoryCalloff.setUserName("");
        customsInventoryCalloff.setOper("");
        if (calloffDto == null) {
            create(customsInventoryCalloff);
        } else {
            customsInventoryCalloff.setId(calloffDto.getId());
            update(customsInventoryCalloff);
        }
        customsInventoryService.updateAfterStatus(customsInventoryDTO.getId(), InventoryAfterStatus.YES.getValue(), customsInventoryDTO.getCreateTime());
        return new Response<>(0, "取消单创建成功");
    }

    @Override
    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String
            picJson, String refundLogisticsNo) {
        return this.upset(orderId, invSn, calloffType, calloffStatus, reason, picJson, refundLogisticsNo, null, null, null, null);
    }

    @Override
    public Response<String> upset(Long orderId, String invSn, String calloffType, String calloffStatus, String reason, String
            picJson, String refundLogisticsNo, String entityWarehouseCode, String entityWarehouseName, String
                                          ownerCode, String ownerName) {
        List<CustomsInventoryCalloffDTO> calloffDtoList = findListByOrderId(orderId);
        CustomsInventoryCalloffDTO calloffDto = null;
        if (0 < calloffDtoList.size()) {
            calloffDto = calloffDtoList.get(0);
        }

        if (Objects.nonNull(calloffDto)) {
            if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
                log.info("[op:CustomsInventoryCalloffService-upset] 取消单已经存在 状态为:{}，申报单号为:{}", InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getDesc(), calloffDto.getDeclareOrderNo());
                return new Response<>(0, "取消单已经存在 状态为:" + InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getDesc());
            }
            if (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
                log.info("[op:CustomsInventoryCalloffService-upset] 取消单已经存在 状态为:{}，申报单号为:{}", InventoryCalloffStatusEnum.CALLOFF_ING.getDesc(), calloffDto.getDeclareOrderNo());
                return new Response<>(0, "取消单已经存在 状态为:" + InventoryCalloffStatusEnum.CALLOFF_ING.getDesc());
            }
            if (InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
                log.info("[op:CustomsInventoryCalloffService-upset] 取消单已经存在 状态为:{}，申报单号为:{}", InventoryCalloffStatusEnum.CALLOFF_WAITING.getDesc(), calloffDto.getDeclareOrderNo());
                return new Response<>(0, "取消单已经存在 状态为:" + InventoryCalloffStatusEnum.CALLOFF_WAITING.getDesc());
            }
        }

        CustomsInventoryDTO customsInventoryDTO = null;
        if (StringUtil.isNotBlank(invSn)) {
            customsInventoryDTO = customsInventoryService.findBySnSection(invSn);
        } else {
            customsInventoryDTO = customsInventoryService.findByOrder(orderId, null);
        }
        if (Objects.isNull(customsInventoryDTO)) {
            return new Response<>(-1, "关联清单不存在");
        }
        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
        //生成取消单
        CustomsInventoryCalloffDTO customsInventoryCalloff = new CustomsInventoryCalloffDTO();
        customsInventoryCalloff.setOrderId(orderDTO.getId());
        customsInventoryCalloff.setOrderSn(orderDTO.getSn());
        customsInventoryCalloff.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
        customsInventoryCalloff.setSn(sequenceService.generateCalloffOrderSn());
        customsInventoryCalloff.setEbcId(customsInventoryDTO.getEbcId());
        customsInventoryCalloff.setAgentCompanyId(customsInventoryDTO.getAgentCompanyId());
        customsInventoryCalloff.setAreaCompanyId(customsInventoryDTO.getAreaCompanyId());
        customsInventoryCalloff.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        customsInventoryCalloff.setInventoryNo(customsInventoryDTO.getInventoryNo());
        customsInventoryCalloff.setCustomsStatus(customsInventoryDTO.getCustomsStatus());
        customsInventoryCalloff.setExitRegionStatus(customsInventoryDTO.getExitRegionStatus());
        customsInventoryCalloff.setPicJson(StrUtil.isNotBlank(picJson) ? picJson : null);
        customsInventoryCalloff.setRefundLogisticsNo(refundLogisticsNo);
        customsInventoryCalloff.setAccountBookId(customsInventoryDTO.getAccountBookId());
        customsInventoryCalloff.setCustomsPassTime(customsInventoryDTO.getCustomsPassTime());
        //上游发起取消 消驳回原因清空，取消单状态改为待取消
        customsInventoryCalloff.setRejectReason("");
        if (orderDTO.getExtraJson() != null) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            if (orderExtra.getSubmit().getTenantOuterId() != null) {
                customsInventoryCalloff.setTenantId(orderExtra.getSubmit().getTenantOuterId());
            }
        }
        if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(customsInventoryDTO.getCustomsStatus())) {
            customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode());
            customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode());

        } else {
            customsInventoryCalloff.setCalloffType(calloffType);
            customsInventoryCalloff.setCalloffStatus(calloffStatus);
        }
        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(customsInventoryCalloff.getCalloffStatus())) {
            customsInventoryCalloff.setCalloffTime(new Date());
        }
        customsInventoryCalloff.setCalloffReason(reason);
        customsInventoryCalloff.setUserName("");
        customsInventoryCalloff.setOper("");
        if (calloffDto == null) {
            create(customsInventoryCalloff);
        } else {
            customsInventoryCalloff.setId(calloffDto.getId());
            update(customsInventoryCalloff);
        }
        //更新售后状态
        customsInventoryService.updateAfterStatus(customsInventoryDTO.getId(), InventoryAfterStatus.YES.getValue(), customsInventoryDTO.getCreateTime());
//        customsInventoryService.updateAfterStatus(customsInventoryDTO.getId(), InventoryAfterStatus.YES.getValue(), CustomsActionStatus.DEC_CANCEL.getValue(), customsInventoryDTO.getCreateTime());
        if (Stream.of(entityWarehouseCode, entityWarehouseName, ownerCode, ownerName).allMatch(s -> s != null)) {
            customsInventoryCalloff.setEntityWarehouseCode(entityWarehouseCode);
            customsInventoryCalloff.setEntityWarehouseName(entityWarehouseName);
            customsInventoryCalloff.setOwnerCode(ownerCode);
            customsInventoryCalloff.setOwnerName(ownerName);
            orderOwnerMappingService.saveOrderCalloffOwnerMapping(customsInventoryCalloff);
        }
        return new Response<>(0, "取消单创建成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void direct(Long id, String calloffReason) {
        // Step::业务检查
        CustomsInventoryCalloffDTO callOffDto = this.findById(id);
        if (!InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(callOffDto.getCalloffStatus())) {
            throw new ArgsErrorException("取消单状态不允许直接发起取消");
        }
        // Step::调用申报单关闭接口
        orderService.cancel(callOffDto.getOrderId());
        // Step::更新取消单状态为拦截申报
        updateCustomsInventoryCalloffStatus(id,
                InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(),
                InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode(),
                calloffReason,
                null);
    }

    @Override
    public int failCallof(Long orderId) {
        Example example = new Example(CustomsInventoryCalloffDO.class);
        // Step::根据时间区间检索更新
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("orderId", orderId);
        example.and(criteria);
        CustomsInventoryCalloffDO _info = new CustomsInventoryCalloffDO();
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(_info);
        }
        _info.setUpdateTime(new Date());
        //撤单、退货删除后， 取消单更新为 待取消
        _info.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
        return customsInventoryCalloffMapper.updateByExampleSelective(_info, example);
    }

    @Override
    public List<CustomsInventoryCalloffDTO> getCallOffByDeclareNo(String ownerCode, String
            entityWarehouseCode, List<String> cancelTypeList, Long timeBegin, Long timeEnd) {
        String begin = null;
        String end = null;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (timeBegin != null) {
            begin = sdf.format(timeBegin);
        }
        if (timeEnd != null) {
            end = sdf.format(timeEnd);
        }
        List<CustomsInventoryCalloffDTO> customsInventoryCalloffDTOS = null;
        try {
            log.info("CustomsInventoryCalloffServiceImpl getCallOffByDeclareNo 入参 ownerCode={} entityWarehouseCode={} cancelTypeList={} timeBegin={} timeEnd={}]", ownerCode, entityWarehouseCode, cancelTypeList, begin, end);
            List<CustomsInventoryCalloffDO> customsInventoryCalloffDOList = customsInventoryCalloffMapper.getCallOffByDeclareNo(ownerCode, entityWarehouseCode, cancelTypeList, begin, end);
            log.info("CustomsInventoryCalloffServiceImpl getCallOffByDeclareNo size={}", customsInventoryCalloffDOList.size());
            customsInventoryCalloffDTOS = customsInventoryCalloffDOList.stream().map(c -> {
                CustomsInventoryCalloffDTO calloffDTO = new CustomsInventoryCalloffDTO();
                BeanUtils.copyProperties(c, calloffDTO);
                return calloffDTO;
            }).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("CustomsInventoryCalloffServiceImpl getCallOffByDeclareNo error", e);
        }
        return customsInventoryCalloffDTOS;
    }

    @Override
    public void delJd() {
        redisTemplate.delete(JD_CANCEL_ORDER_KEY);
    }

    @Override
    public void delJdDetail() {
        redisTemplate.delete(JD_CANCEL_ORDER_KEY_DETATEILS);
    }

    /**
     * 京东取消单同步筛选
     *
     * @param submit
     * @return synJdCancelOrder
     */
    @Override
    public List<CustomsCancelOrderDTO> jdCancelOrderSynPreview(CustomsInventoryCalloffSubmit submit) throws
            ArgsErrorException {
        List<CustomsCancelOrderDTO> customsCancelOrderDTOS = new ArrayList<>();
        Map<String, Object> entriesMap = redisTemplate.opsForHash().entries(JD_CANCEL_ORDER_KEY);
        if (submit != null) {
            for (String key : entriesMap.keySet()) {
                JSONObject jsonObject = JSONObject.parseObject((String) entriesMap.get(key));
                CustomsCancelOrderDTO cancelOrderDTO = JSONObject.parseObject(jsonObject.toJSONString(), CustomsCancelOrderDTO.class);
                if (Objects.equals(cancelOrderDTO.getStatus(), 1)) {
                    throw new ArgsErrorException("存在处理中的任务，请等待！");
                }
            }
            CustomsCancelOrderDTO customsCancelOrderDTO = new CustomsCancelOrderDTO();
            String taskId = DateUtil.formatDateStr(new Date(), DateUtil.DATE_DEF_PATTERN);
            submit.setTaskId(taskId);
            customsCancelOrderDTO.setTaskId(taskId);
            customsCancelOrderDTO.setStaCreateTime(DateUtil.getDateForLong(DateUtil.DATE_DEF_PATTERN, submit.getBeginCalloffTime()));
            customsCancelOrderDTO.setEndCreateTime(DateUtil.getDateForLong(DateUtil.DATE_DEF_PATTERN, submit.getEndCalloffTime()));
            customsCancelOrderDTO.setStatusStr(CustomsCancelOrderStatus.PROCESSING.getDesc());
            customsCancelOrderDTO.setStatus(CustomsCancelOrderStatus.PROCESSING.getCode());
            if (Objects.nonNull(submit.getDeclareOrderNo())) {
                customsCancelOrderDTO.setQueryType(1);
            } else {
                customsCancelOrderDTO.setQueryType(2);
            }
            Map<String, Object> map = new HashMap<>();
            map.put(taskId, JSON.toJSONString(customsCancelOrderDTO));
            redisTemplate.opsForHash().putAll(JD_CANCEL_ORDER_KEY, map);
            entriesMap.putAll(map);
            // 发送MQ去处理明细数据
            jdCalloffOrderProducer.jdCalloffOrderSend(submit);
        }
        log.warn("[op:京东取消单列表] - {} ", entriesMap);
        for (String key : entriesMap.keySet()) {
            JSONObject jsonObject = JSONObject.parseObject((String) entriesMap.get(key));
            CustomsCancelOrderDTO cancelOrderDTO = JSONObject.parseObject(jsonObject.toJSONString(), CustomsCancelOrderDTO.class);
            customsCancelOrderDTOS.add(cancelOrderDTO);
        }
        return customsCancelOrderDTOS;
    }

    /**
     * 交接单明细
     *
     * @param submit
     * @return
     */
    @Override
    public List<CustomsCancelOrderDetailsDTO> jdCancelOrderDetails(CustomsInventoryCalloffSubmit submit) {
        Map<String, Object> entriesMap = redisTemplate.opsForHash().entries(JD_CANCEL_ORDER_KEY_DETATEILS);
        List<CustomsCancelOrderDetailsDTO> detailsDTOS = new ArrayList<>();
        if (!Objects.equals(entriesMap.size(), 0)) {
            String response = entriesMap.get(submit.getSubtasksId()).toString();
            if (Objects.equals(response, "[]")) {
                return detailsDTOS;
            }
            List<String> list = Arrays.asList(response.split(","));
            for (String cancelOrder : list) {
                CustomsCancelOrderDetailsDTO detailsDTO = new CustomsCancelOrderDetailsDTO();
                String[] array = cancelOrder.split("_");
                detailsDTO.setDeclareOrderNo(array[0].replace("[", ""));
                detailsDTO.setStatusStr(array[1].replace("]", ""));
                detailsDTOS.add(detailsDTO);
            }
        }
        return detailsDTOS;
    }

    @Override
    public CustomsInventoryCalloffCountDTO2 calloffOrderCount(CustomsInventoryCalloffSearch search) {
        Example example = buildExample(search);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        CustomsInventoryCalloffCountDTO2 result = new CustomsInventoryCalloffCountDTO2();

        //撤单单待取消
        Example example1 = new Example(CustomsInventoryCalloffDO.class);
        example1.createCriteria().andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())
                .andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
        oredCriteria.forEach(example1::and);
        result.setCancelOrderToCancel(customsInventoryCalloffMapper.selectCountByExample(example1));

        //撤单单取消中
        Example example2 = new Example(CustomsInventoryCalloffDO.class);
        example2.createCriteria().andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode())
                .andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
        oredCriteria.forEach(example2::and);
        result.setCancelOrderCanceling(customsInventoryCalloffMapper.selectCountByExample(example2));

        //退货待取消
        Example example3 = new Example(CustomsInventoryCalloffDO.class);
        example3.createCriteria().andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode())
                .andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
        oredCriteria.forEach(example3::and);
        result.setRefundToCancel(customsInventoryCalloffMapper.selectCountByExample(example3));

        //退货取消中
        Example example4 = new Example(CustomsInventoryCalloffDO.class);
        example4.createCriteria().andEqualTo("calloffStatus", InventoryCalloffStatusEnum.CALLOFF_ING.getCode())
                .andEqualTo("calloffType", InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
        oredCriteria.forEach(example4::and);
        result.setRefundCanceling(customsInventoryCalloffMapper.selectCountByExample(example4));
        return result;
    }


    @Override
    public void cancelByLinkCustoms(LinkCustomsRefundOrderMessageNotifyDTO linkCustomsRefundOrderMessageNotifyDTO) {
        LinkCustomsRefundOrderMessageNotifyDTO.InvtRefundHead invtRefundHead = linkCustomsRefundOrderMessageNotifyDTO.getInvtRefundHead();
        String invtNo = invtRefundHead.getInvtNo();
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByInventoryNo90Days(invtNo);
        if (Objects.isNull(customsInventoryDTO)) {
            log.error("退货单未查询到 或者超过90天");
            return;
        }
        orderService.cancel(customsInventoryDTO.getOrderId());
    }

    @Override
    public void updateLinkCustomsContent(CustomsInventoryCalloffDTO inventoryCalloffDTO) {
        CustomsInventoryCalloffDO customsInventoryCalloffDO = ConvertUtil.beanConvert(inventoryCalloffDTO, CustomsInventoryCalloffDO.class);
        customsInventoryCalloffMapper.updateByPrimaryKeySelective(customsInventoryCalloffDO);
    }

    @Override
    public void updateTypeAndStatusAndExit(Long id, String calloffType, String calloffStatus, int exitRegionStatus) {
        if (Objects.isNull(id)) {
            return;
        }
        CustomsInventoryCalloffDO calloffDO = new CustomsInventoryCalloffDO();
        calloffDO.setId(id);
        calloffDO.setCalloffType(calloffType);
        calloffDO.setCalloffStatus(calloffStatus);
        calloffDO.setExitRegionStatus(exitRegionStatus);
        customsInventoryCalloffMapper.updateByPrimaryKeySelective(calloffDO);
    }

    @Override
    public void updateRejectReasonAndStatusByOrderId(Long orderId, String rejectReason, String status) {
        if (LongUtil.isNone(orderId)) {
            return;
        }
        List<CustomsInventoryCalloffDTO> calloffDTOList = this.findListByOrderId(orderId);
        if (CollectionUtil.isEmpty(calloffDTOList)) {
            return;
        }
        CustomsInventoryCalloffDTO calloffDTO = calloffDTOList.get(0);
        calloffDTO.setCalloffStatus(status);
        calloffDTO.setRejectReason(rejectReason);
        this.update(calloffDTO);
    }

    @Override
    public void migrateByOrderId(Long oldOrderId, OrderDTO newOrderDTO) {
        CustomsInventoryCalloffDO calloffDO = new CustomsInventoryCalloffDO();
        calloffDO.setOrderId(newOrderDTO.getId());
        calloffDO.setOrderSn(newOrderDTO.getSn());
        calloffDO.setDeclareOrderNo(newOrderDTO.getDeclareOrderNo());

        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("orderId", oldOrderId);
        customsInventoryCalloffMapper.updateByExampleSelective(calloffDO, example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refundWarehouseCancel(RefundWarehouseCancelReq reqVO) {
        if (StringUtils.isEmpty(reqVO.getLogisticsNo()) || StringUtils.isEmpty(reqVO.getExpressCode())) {
            throw new ArgsInvalidException("运单号不能为空");
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(reqVO.getExpressCode());
        if (Objects.isNull(expressDTO)) {
            throw new ArgsInvalidException("快递编码不存在 - " + reqVO.getExpressCode());
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByLogisticsNo90Days(expressDTO.getId(), reqVO.getLogisticsNo());
        if (Objects.isNull(customsInventoryDTO)) {
            //若运单+快递编码没查到，则运单号查询 -- 兼容中通 申报时 转 中通国际，上游推下来是按照中通给会查不到
            List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.listByLogistics90Days(reqVO.getLogisticsNo());
            if (CollUtil.isNotEmpty(customsInventoryDTOS)) {
                customsInventoryDTO = customsInventoryDTOS.get(0);
            }
        }
        if (Objects.isNull(customsInventoryDTO)) {
            log.error("运单号未找到 - {}", reqVO.getLogisticsNo());
            throw new ArgsInvalidException("运单号未找到");
        }
        Long orderId = customsInventoryDTO.getOrderId();
        if (StringUtils.isNotEmpty(reqVO.getEntityWarehouseCode())) {
            // 转成erp的仓库编码
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(reqVO.getEntityWarehouseCode());
            if (CollUtil.isNotEmpty(entityWarehouseDTOList)) {
                reqVO.setEntityWarehouseCode(entityWarehouseDTOList.get(0).getErpWarehouseCode());
                reqVO.setEntityWarehouseName(entityWarehouseDTOList.get(0).getErpWarehouseName());
            }
        }
        CustomsInventoryCalloffDTO calloffDTO = this.findByOrderId(orderId);
        if (Objects.nonNull(calloffDTO)) {
            log.info("取消单已创建，更新状态取消单状态， 运单号={}", reqVO.getLogisticsNo());
            CustomsInventoryCalloffDO updateDO = new CustomsInventoryCalloffDO();
            updateDO.setId(calloffDTO.getId());
            if (StrUtil.isNotBlank(reqVO.getRefundLogisticsNo())) {
                updateDO.setRefundLogisticsNo(reqVO.getRefundLogisticsNo());
            }
            updateDO.setOrderTag(InventoryCalloffOrderTagEnums.add(calloffDTO.getOrderTag(),
                    InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE));
            if (InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode().equals(calloffDTO.getCalloffStatus())) {
                updateDO.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
            }
            customsInventoryCalloffMapper.updateByPrimaryKeySelective(updateDO);
        } else {
            log.info("取消单未创建，创建取消单， 运单号={}", reqVO.getLogisticsNo());
            OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
            orderService.cancelByRefundWarehouse(orderDTO, customsInventoryDTO, reqVO);
            CustomsInventoryCalloffDTO newCalloffDTO = this.findByOrderId(orderId);
            CustomsInventoryCalloffDO updateDO = new CustomsInventoryCalloffDO();
            updateDO.setId(newCalloffDTO.getId());
            updateDO.setOrderTag(InventoryCalloffOrderTagEnums.add(newCalloffDTO.getOrderTag(),
                    InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE));

            //取消单 退货信息维护
            if (CollUtil.isNotEmpty(reqVO.getRefundGoodsInfoList())) {
                log.info("退货仓下发取消单 运单号={} 维护退货商品信息={}", reqVO.getLogisticsNo(), JSON.toJSONString(reqVO.getRefundGoodsInfoList()));
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService
                        .listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                Map<String, Integer> barCodeItemCountMap = new HashMap<>();
                customsInventoryItemDTOS.forEach(c -> {
                    if (OrderItemTagEnum.containsFbGifts(c.getItemTag())) {
                        return;
                    }
                    CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(c.getExtraJson(), CustomsInventoryItemExtra.class);
                    if (Objects.nonNull(customsInventoryItemExtra)) {
                        barCodeItemCountMap.merge(customsInventoryItemExtra.getBarCode(), c.getCount(), Integer::sum);
                    }
                });
                List<CalloffRefundGoodsInfoDTO> refundGoodsInfoDTOS = new ArrayList<>();
                long idx = 0;
                for (RefundWarehouseCancelReq.RefundGoodsInfo refundGoodsInfo : reqVO.getRefundGoodsInfoList()) {
                    CalloffRefundGoodsInfoDTO refundGoodsInfoDTO = new CalloffRefundGoodsInfoDTO();
                    refundGoodsInfoDTO.setId(idx++);
                    refundGoodsInfoDTO.setGoodsName(refundGoodsInfo.getGoodsName());
                    refundGoodsInfoDTO.setSku(refundGoodsInfo.getSku());
                    refundGoodsInfoDTO.setRefundCount(refundGoodsInfo.getCount());
                    refundGoodsInfoDTO.setBarCode(refundGoodsInfo.getBarCode());
                    refundGoodsInfoDTO.setTradeType(refundGoodsInfo.getTradeType());
                    if (Objects.equals(refundGoodsInfo.getTradeType(), 1)) {
                        // 保税商品校验
                        if (barCodeItemCountMap.containsKey(refundGoodsInfo.getBarCode())) {
                            refundGoodsInfoDTO.setDeclareCount(barCodeItemCountMap.get(refundGoodsInfo.getBarCode()));
                            if (refundGoodsInfoDTO.getDeclareCount() < refundGoodsInfoDTO.getRefundCount()) {
                                refundGoodsInfoDTO.setErrorMsg("退货数量大于清单申报数量");
                            }
                        } else {
                            refundGoodsInfoDTO.setErrorMsg("未查询到清单表体条码");
                        }
                    }
                    refundGoodsInfoDTOS.add(refundGoodsInfoDTO);
                }
                // 判断是否是部分退
                boolean isPartRefund = false;
                Map<String, Integer> refundCountMap = refundGoodsInfoDTOS.stream()
                        .collect(Collectors.toMap(CalloffRefundGoodsInfoDTO::getBarCode, CalloffRefundGoodsInfoDTO::getRefundCount, Integer::sum));
                for (Map.Entry<String, Integer> entry : barCodeItemCountMap.entrySet()) {
                    if (!refundCountMap.containsKey(entry.getKey()) || refundCountMap.get(entry.getKey()) < entry.getValue()) {
                        log.info("退货仓下发取消单 退货数量小于清单申报数量，退货单为部分退货，退货商品条码={}", entry.getKey());
                        isPartRefund = true;
                        break;
                    }
                }
                updateDO.setPartRefundFlag(isPartRefund ? 1 : 0);
                updateDO.setRefundGoodsInfoJson(JSON.toJSONString(refundGoodsInfoDTOS));
            }
            customsInventoryCalloffMapper.updateByPrimaryKeySelective(updateDO);
        }
    }

    @Override
    public List<String> findMissOwnerWarehouseByBookId(List<Long> customsBookIdList, Date createTimeFrom, Date createTimeTo) {
        List<String> declareOrderNoList = customsInventoryCalloffMapper.findMissOwnerWarehouseByBookId(customsBookIdList, createTimeFrom, createTimeTo);
        return CollUtil.isEmpty(declareOrderNoList) ? new ArrayList<>() : declareOrderNoList;
    }

    @Value("${cancel.overtime.config:}")
    private String cancelOverTimeConfig;


    @Override
    public void cancelOvertimeAlert() {
        List<CancelOverTimeNotifyReqVo> cancelOverTimeNotifyReqVos = JSON.parseArray(cancelOverTimeConfig, CancelOverTimeNotifyReqVo.class);
        Map<String, CancelOverTimeNotifyReqVo> customsNotifyMap = cancelOverTimeNotifyReqVos.stream().collect(Collectors.toMap(CancelOverTimeNotifyReqVo::getCode, Function.identity(), (k, v) -> v));
        List<CustomsBookDTO> customsBookDTOS = customsBookService.findBookByDistrictCode(Arrays.asList("JINYI", "YIWU"));
        if (CollectionUtils.isEmpty(customsBookDTOS)) {
            return;
        }
        List<Long> jinyiCompanyIdList = customsBookDTOS.stream().filter(c -> Objects.equals(c.getCustomsDistrictCode(), "JINYI")).map(CustomsBookDTO::getAreaCompanyId).collect(Collectors.toList());
        List<Long> yiwuCompanyIdList = customsBookDTOS.stream().filter(c -> Objects.equals(c.getCustomsDistrictCode(), "YIWU")).map(CustomsBookDTO::getAreaCompanyId).collect(Collectors.toList());
        List<Long> allCompanyIdList = Stream.of(jinyiCompanyIdList, yiwuCompanyIdList).flatMap(Collection::stream).collect(Collectors.toList());
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date day3 = DateUtils.addDay(new Date(), -3);
        String overTimeDate = dateFormat.format(day3);
        //撤单待取消3d
        Map<Long, Integer> cancelWaitingOverTimeMap = getCancelOverTimeAlertData(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), overTimeDate, allCompanyIdList);
        Date day7 = DateUtils.addDay(new Date(), -7);
        overTimeDate = dateFormat.format(day7);
        Map<Long, Integer> refundWaitingOverTimeMap = getCancelOverTimeAlertData(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode(), InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), overTimeDate, allCompanyIdList);
        Date hour24 = DateUtils.addHour(new Date(), -24);
        overTimeDate = dateFormat.format(hour24);
        Map<Long, Integer> calloffingOverTimeMap = getCancelOverTimeAlertData(null, InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), overTimeDate, allCompanyIdList);

        // 合并KeySet
        Set<Long> mergedKeySet = new HashSet<>();
        mergedKeySet.addAll(cancelWaitingOverTimeMap.keySet());
        mergedKeySet.addAll(refundWaitingOverTimeMap.keySet());
        mergedKeySet.addAll(calloffingOverTimeMap.keySet());

        List<Long> companyIdList = new ArrayList<>(mergedKeySet);
        StringBuilder jinyiMessage = new StringBuilder();
        StringBuilder yiwuMessage = new StringBuilder();
        String format = dateFormat.format(new Date());
        jinyiMessage.append("【金义】取消单超时提醒\n");
        jinyiMessage.append("统计时间:").append(format).append("\n");
        yiwuMessage.append("【义乌】取消单超时提醒\n");
        yiwuMessage.append("统计时间:").append(format).append("\n");
        List<CompanyDTO> companyDTOList = companyService.findById(companyIdList);
        if (CollectionUtils.isEmpty(companyIdList)) {
            return;
        }
        Map<Long, CompanyDTO> idCompanyMap = companyDTOList.stream().collect(Collectors.toMap(CompanyDTO::getId, Function.identity(), (v1, v2) -> v1));
        for (Long companyId : companyIdList) {
            CompanyDTO companyDTO = idCompanyMap.get(companyId);
            if (Objects.isNull(companyDTO)) {
                continue;
            }
            Integer cancelWaitingCount = cancelWaitingOverTimeMap.get(companyId);
            Integer refundWaitingCount = refundWaitingOverTimeMap.get(companyId);
            Integer calloffingCount = calloffingOverTimeMap.get(companyId);
            if (Objects.isNull(calloffingCount) && Objects.isNull(refundWaitingCount) && Objects.isNull(cancelWaitingCount)) {
                continue;
            }
            StringBuilder countSb = new StringBuilder();
            countSb.append("<font color=\\\"warning\\\">区内企业：").append(companyDTO.getName()).append("</font>\n");
            if (Objects.nonNull(cancelWaitingCount)) {
                countSb.append("撤单待取消超时:").append(cancelWaitingCount).append("单\n");
            }
            if (Objects.nonNull(refundWaitingCount)) {
                countSb.append("退货待取消超时:").append(refundWaitingCount).append("单\n");
            }
            if (Objects.nonNull(calloffingCount)) {
                countSb.append("取消中超时:").append(calloffingCount).append("单\n");
            }
            if (jinyiCompanyIdList.contains(companyId)) {
                jinyiMessage.append(countSb);
            } else {
                yiwuMessage.append(countSb);
            }
        }
        CancelOverTimeNotifyReqVo jinyi = customsNotifyMap.get("JINYI");
        CancelOverTimeNotifyReqVo yiwu = customsNotifyMap.get("YIWU");
        WechatNotifyUtils.wechatNotifyMd(jinyi.getWebHook(), jinyi.getPhoneList(), jinyiMessage.toString());
        WechatNotifyUtils.wechatNotifyMd(yiwu.getWebHook(), yiwu.getPhoneList(), yiwuMessage.toString());
    }

    @Override
    public void deleteByOrderId(Long orderId) {
        if (Objects.isNull(orderId)) {
            return;
        }
        log.info("CustomsInventoryCalloffServiceImpl deleteByOrderId:{}", orderId);
        Example example = new Example(CustomsInventoryCalloffDO.class);
        example.createCriteria().andEqualTo("orderId", orderId).andEqualTo("deleted", false);
        customsInventoryCalloffMapper.deleteByExample(example);
    }

    @Override
    public void editRefundGoodsInfo(CalloffEditRefundGoodsInfoReqVo reqVO) {
        Assert.notNull(reqVO.getOrderId(), "申报单id不能为空");
        Assert.notNull(reqVO.getBarCode(), "条码不能为空");
        Assert.notNull(reqVO.getCount(), "数量不能为空");
        Assert.notNull(reqVO.getId(), "id不能为空");
        CustomsInventoryCalloffDTO calloffDTO = this.findByOrderId(reqVO.getOrderId());
        Assert.notNull(calloffDTO, "取消单不存在");
        Assert.isTrue(StringUtils.isNotEmpty(calloffDTO.getRefundGoodsInfoJson()), "退货商品明细不存在，无法编辑");
        Assert.isTrue(InventoryCalloffOrderTagEnums.contains(calloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE), "非退货仓订单无法修改退货数量");
        Assert.isTrue(Objects.equals(calloffDTO.getCalloffType(), InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode()), "非退货取消单无法修改退货数量");
        Assert.isTrue(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equals(calloffDTO.getCalloffStatus()), "已生成退货单后，退货数量无法修改");
        OrderDTO orderDTO = orderService.findBySnSection(calloffDTO.getOrderSn());
        Assert.notNull(orderDTO, "申报单不存在");
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(orderDTO.getCustomsInventorySn());
        List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        Map<String, Integer> declareBarCodeCountMap = new HashMap<>();
        customsInventoryItemDTOS.forEach(c -> {
            if (OrderItemTagEnum.containsFbGifts(c.getItemTag())) {
                return;
            }
            CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(c.getExtraJson(), CustomsInventoryItemExtra.class);
            if (Objects.nonNull(customsInventoryItemExtra)) {
                declareBarCodeCountMap.merge(customsInventoryItemExtra.getBarCode(), c.getCount(), Integer::sum);
            }
        });
        if (!declareBarCodeCountMap.containsKey(reqVO.getBarCode())) {
            throw new ArgsInvalidException("原清单无此条码，无法保存");
        }
        List<CalloffRefundGoodsInfoDTO> calloffRefundGoodsInfoDTOS = JSON.parseArray(calloffDTO.getRefundGoodsInfoJson(), CalloffRefundGoodsInfoDTO.class);
        CalloffRefundGoodsInfoDTO calloffRefundGoodsInfoDTO = calloffRefundGoodsInfoDTOS.stream()
                .filter(r -> Objects.equals(r.getBarCode(), reqVO.getBarCode())).findAny().orElse(null);
        if (calloffRefundGoodsInfoDTO != null && !Objects.equals(calloffRefundGoodsInfoDTO.getId(), reqVO.getId())) {
            throw new ArgsInvalidException("已存在条码，无法保存");
        }
        for (CalloffRefundGoodsInfoDTO c : calloffRefundGoodsInfoDTOS) {
            if (!Objects.equals(c.getId(), reqVO.getId())) {
                continue;
            }
            c.setBarCode(reqVO.getBarCode());
            c.setRefundCount(reqVO.getCount());
            Integer declareCount = declareBarCodeCountMap.get(c.getBarCode());
            if (declareCount < c.getRefundCount()) {
                throw new ArgsInvalidException("退货数量不允许大于正向申报数量");
            } else {
                c.setErrorMsg(null);
            }
            break;
        }
        // 判断取消单是否为 部分退
        Map<String, CalloffRefundGoodsInfoDTO> refundGoodsInfoDTOMap = calloffRefundGoodsInfoDTOS.stream()
                .collect(Collectors.toMap(CalloffRefundGoodsInfoDTO::getBarCode, Function.identity(), (k, v) -> v));
        boolean isPartRefund = false;
        for (Map.Entry<String, Integer> entry : declareBarCodeCountMap.entrySet()) {
            String barCode = entry.getKey();
            Integer count = entry.getValue();
            if (!refundGoodsInfoDTOMap.containsKey(barCode)) {
                isPartRefund = true;
                break;
            }
            CalloffRefundGoodsInfoDTO refundGoodsInfoDTO = refundGoodsInfoDTOMap.get(barCode);
            if (refundGoodsInfoDTO.getRefundCount() < count) {
                isPartRefund = true;
                break;
            }
        }
        // 更新数据库
        CustomsInventoryCalloffDO updateDO = new CustomsInventoryCalloffDO();
        updateDO.setId(calloffDTO.getId());
        updateDO.setRefundGoodsInfoJson(JSON.toJSONString(calloffRefundGoodsInfoDTOS));
        updateDO.setPartRefundFlag(isPartRefund ? 1 : 0);
        customsInventoryCalloffMapper.updateByPrimaryKeySelective(updateDO);
    }

    @Override
    public void jdRefundCancel(JdCustomsOrderMsgDataDTO reqVO) {
        log.info("jdRefundCancel reqVO={}", JSON.toJSONString(reqVO));
        if (StringUtils.isEmpty(reqVO.getWaybillNo())) {
            throw new ArgsInvalidException("运单号不能为空");
        }
        List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.listByLogistics90Days(reqVO.getWaybillNo());
        if (CollUtil.isEmpty(customsInventoryDTOS)) {
            log.error("运单号未找到 - {}", reqVO.getWaybillNo());
            throw new ArgsInvalidException("运单号未找到");
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOS.get(0);
        Long orderId = customsInventoryDTO.getOrderId();
        CustomsInventoryCalloffDTO calloffDTO = this.findByOrderId(orderId);
        if (Objects.nonNull(calloffDTO)) {
            log.info("取消单已创建，更新状态取消单状态， 运单号={}", reqVO.getWaybillNo());
            CustomsInventoryCalloffDO updateDO = new CustomsInventoryCalloffDO();
            updateDO.setId(calloffDTO.getId());
            updateDO.setOrderTag(InventoryCalloffOrderTagEnums.add(calloffDTO.getOrderTag(),
                    InventoryCalloffOrderTagEnums.JD_REFUND));
            if (InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode().equals(calloffDTO.getCalloffStatus())) {
                updateDO.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
            }
            customsInventoryCalloffMapper.updateByPrimaryKeySelective(updateDO);
        } else {
            log.info("取消单未创建，创建取消单， 运单号={}", reqVO.getWaybillNo());
            OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
            List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService
                    .listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            orderService.cancelByJdRefund(orderDTO, customsInventoryItemDTOS);
            CustomsInventoryCalloffDTO newCalloffDTO = this.findByOrderId(orderId);
            CustomsInventoryCalloffDO updateDO = new CustomsInventoryCalloffDO();
            updateDO.setId(newCalloffDTO.getId());
            updateDO.setOrderTag(InventoryCalloffOrderTagEnums.add(newCalloffDTO.getOrderTag(),
                    InventoryCalloffOrderTagEnums.JD_REFUND));

            //取消单 退货信息维护
            if (Objects.equals(reqVO.getType(), "return") && CollUtil.isNotEmpty(reqVO.getRejectionReturnGoodsDtos())) {
                log.info("退货仓下发取消单 运单号={} 维护退货商品信息={}", reqVO.getWaybillNo(), JSON.toJSONString(reqVO.getRejectionReturnGoodsDtos()));
                Map<String, Integer> productIdItemCountMap = new HashMap<>();
                customsInventoryItemDTOS.forEach(c -> {
                    if (OrderItemTagEnum.containsFbGifts(c.getItemTag())) {
                        return;
                    }
                    CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(c.getExtraJson(), CustomsInventoryItemExtra.class);
                    if (Objects.nonNull(customsInventoryItemExtra)) {
                        productIdItemCountMap.merge(customsInventoryItemExtra.getProductId(), c.getCount(), Integer::sum);
                    }
                });
                List<CalloffRefundGoodsInfoDTO> refundGoodsInfoDTOS = new ArrayList<>();
                long idx = 0;
                for (JdCustomsOrderMsgDataDTO.RejectionReturnGoodsDto refundGoodsDto : reqVO.getRejectionReturnGoodsDtos()) {
                    CalloffRefundGoodsInfoDTO refundGoodsInfoDTO = new CalloffRefundGoodsInfoDTO();
                    refundGoodsInfoDTO.setId(idx++);
                    refundGoodsInfoDTO.setGoodsName(refundGoodsDto.getGoodsName());
                    refundGoodsInfoDTO.setCustomsProductId(refundGoodsDto.getRecordNo());
                    refundGoodsInfoDTO.setSku(refundGoodsDto.getSkuId());
                    refundGoodsInfoDTO.setRefundCount(refundGoodsDto.getQty());
                    refundGoodsInfoDTO.setTradeType(1);
                    // 保税商品校验
                    if (productIdItemCountMap.containsKey(refundGoodsDto.getRecordNo())) {
                        refundGoodsInfoDTO.setDeclareCount(productIdItemCountMap.get(refundGoodsDto.getRecordNo()));
                        if (refundGoodsInfoDTO.getDeclareCount() < refundGoodsInfoDTO.getRefundCount()) {
                            refundGoodsInfoDTO.setErrorMsg("退货数量大于清单申报数量");
                        }
                    } else {
                        refundGoodsInfoDTO.setErrorMsg("货号：" + refundGoodsDto.getRecordNo() + "，未匹配到清单的海关备案料号");
                    }
                    refundGoodsInfoDTOS.add(refundGoodsInfoDTO);
                }
                // 判断是否是部分退
                boolean isPartRefund = false;
                Map<String, Integer> refundCountMap = refundGoodsInfoDTOS.stream()
                        .collect(Collectors.toMap(CalloffRefundGoodsInfoDTO::getCustomsProductId, CalloffRefundGoodsInfoDTO::getRefundCount, Integer::sum));
                for (Map.Entry<String, Integer> entry : productIdItemCountMap.entrySet()) {
                    if (!refundCountMap.containsKey(entry.getKey()) || refundCountMap.get(entry.getKey()) < entry.getValue()) {
                        log.info("京东退货下发取消单 退货数量小于清单申报数量，退货单为部分退货，退货商品条码={}", entry.getKey());
                        isPartRefund = true;
                        break;
                    }
                }
                updateDO.setPartRefundFlag(isPartRefund ? 1 : 0);
                updateDO.setRefundGoodsInfoJson(JSON.toJSONString(refundGoodsInfoDTOS));
            }
            customsInventoryCalloffMapper.updateByPrimaryKeySelective(updateDO);
        }
    }

    public static void main(String[] args) {
        StringBuilder jinyiMessage = new StringBuilder();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = dateFormat.format(new Date());
        jinyiMessage.append("【金义】取消单超时提醒\n");
        jinyiMessage.append("统计时间:").append(format).append("\n");
        StringBuilder countSb = new StringBuilder();
        countSb.append("<font color=\\\"warning\\\">区内企业：").append("cesssss").append("</font>\n");
        countSb.append("撤单待取消超时:").append(123).append("单\n");
        countSb.append("退货待取消超时:").append(4214).append("单\n");
        countSb.append("取消中超时:").append(41231).append("单\n");
        countSb.append("<font color=\\\"warning\\\">区内企业：").append("21321312312").append("</font>\n");
        countSb.append("撤单待取消超时:").append(643643).append("单\n");
        countSb.append("退货待取消超时:").append(643653).append("单\n");
        countSb.append("取消中超时:").append(756756).append("单\n");
        jinyiMessage.append(countSb);
        WechatNotifyUtils.wechatNotifyMd("https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=56b193a0-f556-43ff-9d54-3ed40e9c759c", Arrays.asList("15712612803"), jinyiMessage.toString());
    }

    private Map<Long, Integer> getCancelOverTimeAlertData(String calloffType, String calloffStatus, String overTimeDate, List<Long> companyIdList) {
        List<Map<String, Object>> cancelOverTimeAlertData = customsInventoryCalloffMapper.getCancelOverTimeAlertData(calloffType, calloffStatus, overTimeDate, companyIdList);
        Map<Long, Integer> result = new HashMap<>();
        for (Map<String, Object> item : cancelOverTimeAlertData) {
            Long companyId = (Long) item.get("companyId");
            Long count = (Long) item.get("count");
            result.put(companyId, count.intValue());
        }
        return result;
    }

}
