package com.danding.cds.service.declareImpl.inventory;

import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.declare.base.component.order.OrderDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.order.api.dto.OrderDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/10/10 17:39
 */
@Service("JUMP_ORDER_DECLARE")
@Slf4j
public class JumpOrderDeclareImpl extends OrderDeclareAbstract {

    @Resource
    private CustomsOrderService customsOrderService;
    @Resource
    private OrderService orderService;

    @Override
    protected OrderDeclareResult mockDeclareTest(WrapOrderDeclareInfo info) {
        log.info("申报单: {} ,跳过【订单申报】MOCK", info.getDeclareNos());
        return declare(info);
    }

    @Override
    protected OrderDeclareResult declare(WrapOrderDeclareInfo info) {
        log.info("订单开始申报，申报单: {} 订单选择跳过申报 系统直接申报成功", info.getDeclareNos());
        CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(null, info.getSn());
        OrderDTO orderDTO = orderService.findBySnSection(info.getMainOrderSn());
        customsOrderService.updateByCustomsPass(customsOrderDTO.getId(), customsOrderDTO.getCreateTime());
        customsOrderService.orderDeclareSend(orderDTO);
        log.info("处理完成，申报单: {} 系统直接申报成功", info.getDeclareNos());
        return null;
    }
}
