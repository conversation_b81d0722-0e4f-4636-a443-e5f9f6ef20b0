package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsPaymentService;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.order.api.dto.OrderDeclareV2DTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.service.DeclareRepushService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 支付单定时重推
 * @date 2022/4/25
 */
@Slf4j
@Service
public class PaymentRePushJob extends IJobHandler {

    @Resource
    private CustomsPaymentService customsPaymentService;

    @Autowired
    private DeclareRepushService declareRepushService;

    @Override
    @XxlJob(value = "OrderCPaymentRePushJob", enableTenant = true)
    public ReturnT<String> execute(String param) throws Exception {
        XxlJobLogger.log("支付单定时启动-");
        try {
            if (Objects.isNull(param)){
                XxlJobLogger.log("入参不能为空");
                return null;
            }
            XxlJobLogger.log("参数设置 :" + param);
            OrderDeclareV2DTO v2DTO = JSON.parseObject(param,OrderDeclareV2DTO.class);
            List<CustomsPaymentDTO> customsPaymentDTOList = getPaymentOrderList(v2DTO);
            List<String> rePushLimitedList = new ArrayList<>();
            customsPaymentDTOList.forEach(a ->{
                boolean result = declareRepushService.paymentReDeclarePush(a);
                if (result){
                    rePushLimitedList.add(a.getDeclareOrderNo());
                }
            });
            declareRepushService.orderReMsgNotice(v2DTO,"【支付单】",rePushLimitedList);
        }catch (ArgsErrorException e){
            XxlJobLogger.log("支付单重推失败 :" + e.getErrorMessage());
        }
        return SUCCESS;
    }

    private List<CustomsPaymentDTO> getPaymentOrderList(OrderDeclareV2DTO v2DTO) {
        List<Integer> statusList = Arrays.asList(CustomsActionStatus.DEC_WAIT.getValue(),CustomsActionStatus.DEC_ING.getValue(),CustomsActionStatus.DEC_FAIL.getValue());
        List<CustomsPaymentDTO> paymentDTOList =customsPaymentService.findByEbpIdAndStatusPaymentOrder(v2DTO.getEbpId(), statusList, v2DTO.getPage(), v2DTO.getQueryDays());
        XxlJobLogger.log("[op:PaymentRePushJob] 支付单未申报订单数={} ", paymentDTOList.size());
        return paymentDTOList;
    }



}
