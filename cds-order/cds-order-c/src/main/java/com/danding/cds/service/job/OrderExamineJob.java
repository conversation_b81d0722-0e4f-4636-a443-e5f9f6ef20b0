package com.danding.cds.service.job;

import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderSearch;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.service.mq.producer.OrderExamineMQProducer;
import com.danding.logistics.api.common.response.ListVO;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class OrderExamineJob extends IJobHandler {

    @Resource
    private OrderService orderService;

    @Autowired
    private OrderExamineMQProducer examineMQProducer;

    @Override
    @XxlJob(value = "OrderCExamineJob", enableTenant = true)
    public ReturnT<String> execute(String ids) throws Exception {
        List<OrderDTO> orderDTOList = new ArrayList<>();
        if (!StringUtils.isEmpty(ids)){
            // Module::指定订单检查模式
            List<String> snList = Lists.newArrayList(ids.split(","));
            for (String sn : snList) {
                OrderDTO orderDTO = orderService.findBySnSection(sn);
                if (orderDTO != null){
                    orderDTOList.add(orderDTO);
                }else {
                    XxlJobLogger.log("[op:OrderExamineJob] sn：{} 的订单不存在", sn);
                }
            }
        }else {
            // Module::批量自动检索检查模式
            // 审核中
            // 前5-10分钟有更新
            // 非异常状态
            // 最多检查1000单，超出上限报警
            OrderSearch search = new OrderSearch();
            search.setStatus(OrderStatus.DEC_WAIT.getValue());
            search.setExceptionFlag(-1);
            Long updateFrom = DateTime.now().minusDays(14).getMillis();
            Long updateTo = DateTime.now().getMillis();
            Integer maxDeal = 1000;
            search.setCurrentPage(1);
            search.setPageSize(maxDeal);
            search.setUpdateFrom(updateFrom);
            search.setUpdateTo(updateTo);
            ListVO lib = orderService.pagingES(search);
            orderDTOList = lib.getDataList();
            XxlJobLogger.log("[op:OrderExamineJob] 批量检索报告，更新时间段{}-{},数据总数={},本次检查数量={}",
                    new DateTime(updateFrom).toString("yyyy/MM/dd HH:mm:ss"),
                    new DateTime(updateTo).toString("yyyy/MM/dd HH:mm:ss"),lib.getPage().getTotalCount(),
                    maxDeal < lib.getPage().getTotalCount() ? maxDeal : lib.getPage().getTotalCount()
            );
            if (maxDeal < lib.getPage().getTotalCount()){
                XxlJobLogger.log("[op:OrderExamineJob] 待处理数量超出最大可处理数量");
                return FAIL;
            }
        }
        for (OrderDTO orderDTO : orderDTOList) {
            examineMQProducer.send(orderDTO.getSn());
            XxlJobLogger.log("[op:OrderExamineJob] 审核发起完成, declareSn={} ", orderDTO.getDeclareOrderNo());
        }
        XxlJobLogger.log("[op:OrderExamineJob] 审核检查发起完成");
        return SUCCESS;
    }
}
