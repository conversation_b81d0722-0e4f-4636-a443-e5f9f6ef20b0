package com.danding.cds.service.mq;


import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.constant.CustomsReportTopicConstants;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

/**
 * 处理税金状态回执
 */
@Component
@Slf4j
@RocketMQMessageListener(
        consumerGroup = CustomsReportTopicConstants.CCS_CUSTOMS_REPORT_C_TAX_STATUS_CONSUMER,
        topic = CustomsReportTopicConstants.CCS_CUSTOMS_REPORT_C_TAX_STATUS
)
public class CustomsReportTaxStatusReceiver extends MessageHandler {

    @Resource
    private OrderService orderService;

    @Override
    public void handle(Object message) throws RuntimeException {
        if (ObjectUtils.isEmpty(message)) return;
        String data = message.toString();
        orderService.acceptCustomsSupport(data);
    }
}
