package com.danding.cds.service.base;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.order.base.bean.dao.CustomsInventoryItemDO;
import com.danding.cds.order.base.mapper.CustomsInventoryItemMapper;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.logistics.api.common.page.TimeRangeParam;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/8/17  10:09
 * @Describe
 **/
@Service
public class CustomsInventoryItemBaseService {
    @Autowired
    private CustomsInventoryItemMapper customsInventoryItemMapper;


    /**
     * 批量插入
     *
     * @param inventoryItemDOList
     */
    public void batchInsert(List<CustomsInventoryItemDO> inventoryItemDOList) {
        customsInventoryItemMapper.insertList(inventoryItemDOList);
    }

    /**
     * 获取表体信息，通过清关单ID
     *
     * @param inventoryId
     * @return
     */
    public List<CustomsInventoryItemDTO> selectByInventoryIdIn90Days(Long inventoryId) {

        final Map<Long, List<CustomsInventoryItemDTO>> longListMap = this.batchSelectByInventoryIdsIn90Days(Collections.singletonList(inventoryId));

        if (CollectionUtils.isEmpty(longListMap)) {
            return Collections.emptyList();
        }
        return longListMap.get(inventoryId);
    }

    /**
     * 根据清单id批量查询数据,90天内的数据
     * collect
     *
     * @param inventoryIds 清关单ID
     */
    public Map<Long, List<CustomsInventoryItemDTO>> batchSelectByInventoryIdsIn90Days(List<Long> inventoryIds) {
        if (CollectionUtils.isEmpty(inventoryIds)) {
            return Collections.emptyMap();
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("customsInventoryId", inventoryIds));
        List<CustomsInventoryItemDO> inventoryItemDOList = customsInventoryItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(inventoryItemDOList)) {
            return Collections.emptyMap();
        }
        return inventoryItemDOList.stream()
                .map(z -> {
                    CustomsInventoryItemDTO inventoryItemDTO = new CustomsInventoryItemDTO();
                    BeanUtils.copyProperties(z, inventoryItemDTO);
                    return inventoryItemDTO;
                }).collect(Collectors.groupingBy(CustomsInventoryItemDTO::getCustomsInventoryId));
    }


    public void batchDeleteByInventoryId(List<Long> ids, Date begin, Date end) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(begin);
        timeRangeParam.setEndDate(end);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("customsInventoryId", ids));
        customsInventoryItemMapper.deleteByExample(example);
    }

    public List<CustomsInventoryItemDTO> findByInventoryId(List<Long> ids, Date begin, Date end) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(begin);
        timeRangeParam.setEndDate(end);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("customsInventoryId", ids));
        List<CustomsInventoryItemDO> customsInventoryItemDOS = customsInventoryItemMapper.selectByExample(example);
        return ConvertUtil.listConvert(customsInventoryItemDOS, CustomsInventoryItemDTO.class);
    }

//    public void batchLogicDeleteByInveOrderId(Long customsInventoryId, TimeRangeParam timeRangeParam) {
//        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
//        example.and(example.createCriteria().andEqualTo("customsInventoryId", customsInventoryId).andEqualTo("deleted", 0));
//        CustomsInventoryItemDO customsInventoryItemDO = new CustomsInventoryItemDO();
//        customsInventoryItemDO.setDeleted(true);
//        if (!Objects.equals(UserUtils.getUserId(), 0)) {
//            UserUtils.setUpdateBy(customsInventoryItemDO);
//        }
//        customsInventoryItemDO.setUpdateTime(new Date());
//        customsInventoryItemMapper.updateByExampleSelective(customsInventoryItemDO, example);
//    }

    /**
     * 获取90天内的清单表体数据
     *
     * @param ids
     * @return
     */
    public Map<Long, CustomsInventoryItemDTO> batchGetItemByIdListIn90Days(List<Long> ids) {

        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(DateTime.now().minusDays(90).toDate());
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(new Date()));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        example.and(example.createCriteria().andIn("id", ids));
        List<CustomsInventoryItemDO> customsInventoryItemDOS = customsInventoryItemMapper.selectByExample(example);
        List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryItemDOS.stream().map(c -> {
            CustomsInventoryItemDTO dto = new CustomsInventoryItemDTO();
            BeanUtils.copyProperties(c, dto);
            return dto;
        }).collect(Collectors.toList());
        return customsInventoryItemDTOS.stream().collect(Collectors.toMap(CustomsInventoryItemDTO::getId, Function.identity(), (v1, v2) -> v1));
    }

    public void updateByDOSelective(CustomsInventoryItemDO itemDO, Date createTime) {
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(createTime));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(createTime));
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", itemDO.getId());
        example.and(criteria);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(itemDO);
        }
        itemDO.setUpdateTime(new Date());
        customsInventoryItemMapper.updateByExampleSelective(itemDO, example);
    }

    public List<CustomsInventoryItemDO> selectByCustomsInventorySnList(List<Long> idList, Date beginOfQuarter, Date endOfQuarter) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(beginOfQuarter);
        timeRangeParam.setEndDate(endOfQuarter);
        Example example = ShardingBaseExampleBuilder.getExample(CustomsInventoryItemDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("customsInventoryId", idList);
        example.and(criteria);
        return customsInventoryItemMapper.selectByExample(example);

    }
}
