package com.danding.cds.service.mq.isolate;

import com.danding.cds.common.constant.DeclareTopicCons;
import com.danding.cds.service.mq.consumer.DeclareOrderConsumerService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RocketMQMessageListener(
        topic = DeclareTopicCons.CCS_DECLARE_IC_CARD_ONE_HZCENTER_C,
        consumerGroup = DeclareTopicCons.CCS_DECLARE_IC_CARD_ONE_HZCENTER_C
)
public class OrderDeclareIcCardOneHzCenterConsumer extends MessageHandler {

    @Autowired
    private DeclareOrderConsumerService orderConsumerService;

    @Override
    public void handle(Object o) throws RuntimeException {
        orderConsumerService.appointDeclareTypeConsumerhandle(o);
    }
}
