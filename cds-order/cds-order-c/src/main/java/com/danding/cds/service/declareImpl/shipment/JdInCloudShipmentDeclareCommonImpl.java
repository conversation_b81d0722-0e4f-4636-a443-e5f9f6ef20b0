package com.danding.cds.service.declareImpl.shipment;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.base.component.shipment.ShipmentDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.ShipmentDeclareResult;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.customs.declare.InCloudDeclareService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 京东CEB清单代理申报
 **/
@Service("JD_IN_CLOUD_SHIPMENT_DECLARE_COMMON")
@Slf4j
public class JdInCloudShipmentDeclareCommonImpl extends ShipmentDeclareAbstract {
    /**
     * 测试用
     */
    @Autowired
    private InCloudDeclareService inCloudDeclareService;

    @Override
    protected ShipmentDeclareResult mockDeclareTest(WrapShipmentInfo info) {
        log.info("京东CEB运单代理申报 测试环境-MOCK info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }

    /**
     * @param info 数据
     * @return
     */
    @Override
    protected ShipmentDeclareResult declare(WrapShipmentInfo info) {
        log.info("京东CEB运单代理申报 info={}", JSON.toJSONString(info));
        inCloudDeclareService.jdInCloudDeclareInvoke(info);
        return null;
    }
}
