package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.vo.InCloudDeclareBaseReqVo;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.bean.dto.CebSwxaDxpIdConfigDto;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.utils.HttpRequestUtil;
import com.danding.cds.declare.sdk.clear.zhejiang.ZJAgentToken;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.utils.TokenUtil;
import com.danding.cds.handler.JdInCloudDeclareTrackLogParametersHandler;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSensitivePlainText;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.utils.TrackLogUtils;
import com.github.kevinsawicki.http.HttpRequest;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Objects;

@Service
@Slf4j
@RefreshScope
public class InCloudDeclareService {

    @Value("${jd.cloud.declare.url:}")
    private String jdCloudDeclareUrl;

    @Value("${ceb.code.swxa.dxp.config:}")
    private String cebCodeSwxaDxpConfig;

    @Resource
    private OrderService orderService;

    /**
     * 京东入云申报
     *
     * @param info
     */
    @TrackLog(infoIndex = 0, handler = JdInCloudDeclareTrackLogParametersHandler.class)
    public void jdInCloudDeclareInvoke(WrapBeanInfo info) {
        InCloudDeclareBaseReqVo inCloudDeclareBaseReqVo = getInCloudDeclareReqVo(info);
        if (StringUtils.isEmpty(jdCloudDeclareUrl)) {
            throw new RuntimeException("京东入云申报地址为空");
        }
        inCloudDeclareInvokeAndLog(inCloudDeclareBaseReqVo, jdCloudDeclareUrl);
    }

    /**
     * 云内申报订单
     *
     * @param info
     */

    public void inCloudDeclareInvokeAndLog(InCloudDeclareBaseReqVo info, String inCloudUrl) {
        log.info("inCloudDeclareInvokeAndLog proxy, url={}, param={}", inCloudUrl, JSON.toJSONString(info));
        if (StringUtils.isEmpty(inCloudUrl)) {
            throw new RuntimeException("入云申报地址为空");
        }
        // 拼多多转发至云内应用申报
        HttpRequest httpRequest = HttpRequestUtil.post(inCloudUrl, JSON.toJSONString(info));
        String body = httpRequest.body();
        String message = StringUtils.isEmpty(body) ? "" : body.replace("\r\n", "");
        TrackLogBaseInfo logBaseInfo = new TrackLogBaseInfo();
        logBaseInfo.setStatusDesc(TrackLogConstantMixAll.FAIL);
        if (httpRequest.ok()) {
            JSONObject jsonObject = JSON.parseObject(body);
            if ("200".equalsIgnoreCase((String) jsonObject.get("code"))) {
                log.info("inCloudDeclareInvokeAndLog url={} success,  res={} , 原始信息：{}，", inCloudUrl, message, JSON.toJSONString(info));
                logBaseInfo.setStatusDesc(TrackLogConstantMixAll.SUCCESS);
            } else {
                log.info("inCloudDeclareInvokeAndLog url={} fail,exception body={} , 原始信息：{}，", inCloudUrl, message, JSON.toJSONString(info));
            }
        } else {
            log.info("inCloudDeclareInvokeAndLog url={} fail,exception body={} , 原始信息：{}，", inCloudUrl, message, JSON.toJSONString(info));
        }
        TrackLogUtils.setTrackLogBaseInfoThreadLocal(logBaseInfo);
    }

    private InCloudDeclareBaseReqVo getInCloudDeclareReqVo(WrapBeanInfo info) {

        InCloudDeclareBaseReqVo reqVo = new InCloudDeclareBaseReqVo();
        // 获取下申报企业的海关十位编码
        String encryptAlgorithm = getEncryptAlgorithmByCode(info.getDeclareCompanyCebCode());
        if (!StringUtils.isEmpty(encryptAlgorithm)) {
            reqVo.setEncryptAlgorithm(encryptAlgorithm);
        }

        OrderDTO orderDTO = orderService.findBySnSection(info.getMainOrderSn());
        if (Objects.nonNull(orderDTO)) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            reqVo.setVenderId(orderExtra.getSubmit().getTenantOuterId());
        }
        reqVo.setDeclareOrderSn(info.getDeclareNos());
        ZJAgentToken agentToken = TokenUtil.getZjPortToken(info.getDeclareCompanyCebCode());
        if (Objects.isNull(agentToken)) {
            throw new RuntimeException("ceb- " + info.getDeclareCompanyCebCode() + " 企业申报配置信息未配置");
        }
        InCloudDeclareBaseReqVo.CompanyConfig companyConfig = new InCloudDeclareBaseReqVo.CompanyConfig();
        if (!EnvironmentConfig.isOnline() && (StringUtil.isEmpty(agentToken.getAppId()) || StringUtil.isEmpty(agentToken.getAppKey()))) {
            companyConfig.setAppId("RYT00001");
            companyConfig.setAppKey("abcdefghij1234567890abcdefghij1234567890");
        } else {
            companyConfig.setAppId(agentToken.getAppId());
            companyConfig.setAppKey(agentToken.getAppKey());
        }
        companyConfig.setAesKey(agentToken.getZjPortKeyInfo().getAesKey());
        reqVo.setZjPortCompanyConfig(companyConfig);
        reqVo.setInfo(info);
        String clearanceMessageType = null;
        Class<?> infoClz = info.getClass();
        if (Objects.equals(infoClz, WrapOrderDeclareInfo.class)) {
            clearanceMessageType = InCloudDeclareBaseReqVo.ClearanceMessageType.CEB311.getKey();
        } else if (Objects.equals(infoClz, WrapShipmentInfo.class)) {
            clearanceMessageType = InCloudDeclareBaseReqVo.ClearanceMessageType.CEB511.getKey();
        } else if (Objects.equals(infoClz, WrapInventoryOrderInfo.class)) {
            clearanceMessageType = InCloudDeclareBaseReqVo.ClearanceMessageType.CEB621.getKey();
        } else if (Objects.equals(infoClz, WarpCancelOrderInfo.class)) {
            clearanceMessageType = InCloudDeclareBaseReqVo.ClearanceMessageType.CEB623.getKey();
        } else if (Objects.equals(infoClz, WarpRefundOrderInfo.class)) {
            clearanceMessageType = InCloudDeclareBaseReqVo.ClearanceMessageType.CEB625.getKey();
        }
        reqVo.setClearanceMessageType(clearanceMessageType);
        OrderSensitivePlainText plainText = info.getPlainText();
        if (Objects.nonNull(plainText)) {
            InCloudDeclareBaseReqVo.PlainTextReplaceInfo plainTextReplaceInfo = new InCloudDeclareBaseReqVo.PlainTextReplaceInfo();
            plainTextReplaceInfo.setConsigneeName(plainText.getConsigneeName());
            plainTextReplaceInfo.setConsigneeAddress(plainText.getConsigneeAddress());
            plainTextReplaceInfo.setConsigneeTel(plainText.getConsigneeTel());
            plainTextReplaceInfo.setBuyerName(plainText.getBuyerName());
            plainTextReplaceInfo.setBuyerTelNumber(plainText.getBuyerTelNumber());
            reqVo.setPlainTextReplaceInfo(plainTextReplaceInfo);
        }
        return reqVo;
    }

    private String getEncryptAlgorithmByCode(String cebCode) {
        if (StringUtils.isEmpty(cebCodeSwxaDxpConfig) || StringUtils.isEmpty(cebCode)) {
            return null;
        }
        return JSON.parseArray(cebCodeSwxaDxpConfig, CebSwxaDxpIdConfigDto
                        .class).stream()
                .filter(Z -> Objects.equals(Z.getCebCode(), cebCode))
                .map(CebSwxaDxpIdConfigDto::getEncryptAlgorithm)
                .findFirst().orElse(null);
    }

}
