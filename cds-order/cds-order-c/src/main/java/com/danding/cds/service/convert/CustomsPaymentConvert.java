package com.danding.cds.service.convert;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.OrderSubmitDto;
import com.danding.cds.bean.dto.OrderSubmitItemDto;
import com.danding.cds.bean.model.order.RichOrder;
import com.danding.cds.c.api.service.CustomsPaymentDeclareService;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ObjectMapperUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.payment.api.service.dto.PaymentSubmit;
import com.danding.cds.declare.sdk.enums.CustomsPaymentStatus;
import com.danding.cds.declare.sdk.enums.PayCustoms;
import com.danding.cds.declare.sdk.enums.PayCustomsChannel;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.base.bean.dao.CustomsPaymentDO;
import com.danding.cds.order.base.bean.dao.CustomsPaymentDeclareDO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.order.base.data.service.SequenceServiceBaseService;
import com.danding.cds.order.base.mapper.CustomsPaymentDeclareMapper;
import com.danding.cds.order.base.mapper.CustomsPaymentMapper;
import com.danding.cds.order.base.util.OrderConvertWorkSpace;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 海关支付单数据转换器
 * 含数据检查 + 数据组装
 */
@Component
public class CustomsPaymentConvert {

    @Resource
    private SequenceServiceBaseService sequenceServiceBaseService;

    @Resource
    private CustomsPaymentMapper customsPaymentMapper;

    @Resource
    private CustomsPaymentDeclareMapper customsPaymentDeclareMapper;

    @Resource
    private CustomsPaymentDeclareService customsPaymentDeclareService;

    @Autowired
    private BaseDataService baseDataService;

    @Deprecated
    public CustomsPaymentDO preCustomsPayment(Long userId, OrderSubmit submit, OrderConvertWorkSpace workSpace) {
        OrderSubmitDto orderSubmitDto = ObjectMapperUtil.convertValue(submit, OrderSubmitDto.class);
        return this.preCustomsPayment(userId, orderSubmitDto, workSpace);
    }

    public CustomsPaymentDO preCustomsPayment(Long userId, OrderSubmitDto submit, OrderConvertWorkSpace workSpace) {
        PaymentSubmit paymentSubmit = new PaymentSubmit();
        paymentSubmit.setMerchantCode(submit.getMerchantCode());
        paymentSubmit.setPayChannel(submit.getPayChannel());
        paymentSubmit.setOutTradeNo(submit.getOutTradeNo());
        paymentSubmit.setTradePayNo(submit.getTradePayNo());
        paymentSubmit.setOutOrderNo(submit.getOutOrderNo());
        paymentSubmit.setDeclareOrderNo(submit.getDeclareOrderNo());
        paymentSubmit.setBuyerIdNumber(submit.getBuyerIdNumber());
        paymentSubmit.setBuyerName(submit.getBuyerName());
        paymentSubmit.setTradeTime(submit.getTradeTime());
        paymentSubmit.setTaxAmount(submit.getTaxAmount());
        paymentSubmit.setDiscount(submit.getDiscount());
        paymentSubmit.setFeeAmount(submit.getFeeAmount());
        paymentSubmit.setRouteCode(submit.getRouteCode());
        paymentSubmit.setRelDeclareOrderNoList(submit.getPayNoReldeclareOrderNoList());
        paymentSubmit.setPayMerchantOutNo(submit.getPayMerchantOutNo());
        BigDecimal goodsAmount = BigDecimal.ZERO;
        for (OrderSubmitItemDto item : submit.getItemList()) {
            goodsAmount = goodsAmount.add(item.getUnitPrice().multiply(new BigDecimal(item.getCount())));
        }
        paymentSubmit.setGoodsSumAmount(goodsAmount);
        if ("tonglian".equalsIgnoreCase(submit.getPayChannel())) {
            paymentSubmit.setPayAmount(goodsAmount);
            paymentSubmit.setGoodsSumAmount(goodsAmount.add(paymentSubmit.getFeeAmount()).subtract(submit.getTaxAmount()));
        } else {
            paymentSubmit.setPayAmount(
                    paymentSubmit.getGoodsSumAmount().add(paymentSubmit.getFeeAmount()).add(paymentSubmit.getTaxAmount())
                            .subtract(paymentSubmit.getDiscount()));
        }
        paymentSubmit.setCustomsCode(submit.getCustomsCode());
        paymentSubmit.setTenantOuterId(submit.getTenantOuterId());
        if (userId == null || userId.equals(0L)) {
            userId = 1L;
        }
        return this.preCustomsPayment(userId, paymentSubmit, workSpace, submit.getPayRequestMessage());
    }

    public CustomsPaymentDO preCustomsPayment(Long userId, PaymentSubmit submit, OrderConvertWorkSpace workSpace, String payRequestMessage) {
        CustomsPaymentDO customsPaymentOldDO = new CustomsPaymentDO();
        if (StringUtils.isEmpty(submit.getPayChannel())) {
            throw new ArgsErrorException("付款渠道不能为空");
        }
//        PayChannelDTO payChannelDTO = workSpace.getPayChannelDTO(payChannelService, submit.getPayChannel());
        PayChannelDTO payChannelDTO = baseDataService.getPayChannelDTOByCode(submit.getPayChannel());
        if (payChannelDTO == null) {
            throw new ArgsErrorException("支付渠道未配置");
        }
        PayCustomsChannel payChannel = PayCustomsChannel.getEnum(payChannelDTO.getCode());
        if (PayCustomsChannel.NULL.getValue().equalsIgnoreCase(payChannel.getValue())) {
            throw new ArgsErrorException("该支付渠道不支持申报");
        }
//        CompanyDTO payCompany = workSpace.getCompanyDTO(companyService,payChannelDTO.getPayCompanyId());
        CompanyDTO payCompany = baseDataService.getUnifiedCrossCodeCompanyById(payChannelDTO.getPayCompanyId());
        if (payCompany == null || payCompany.getEnable() != 1) {
            throw new ArgsErrorException("支付渠道对应企业不存在或未启用");
        }
        if (CustomsDistrictEnum.NULL.equals(CustomsDistrictEnum.getEnum(submit.getCustomsCode()))) {
            throw new ArgsErrorException("关区编码未定义");
        }
        if (StringUtils.isEmpty(submit.getMerchantCode())) {
            throw new ArgsErrorException("商户编号|收款账号不能为空");
        }
//        PayMerchantAccountDTO merchantAccountDTO = payMerchantAccountService.findBySn(submit.getMerchantCode());
        PayMerchantAccountDTO merchantAccountDTO = baseDataService.getPayMerchantAccountDTOByCode(submit.getMerchantCode());
        if (merchantAccountDTO == null) {
            throw new ArgsErrorException("收款商户不存在");
        }
//        PayMerchantAccountChannelDTO merchantAccountChannelDTO = payMerchantAccountChannelService.findByMerchantAndChannel(merchantAccountDTO.getId(),submit.getPayChannel());
        PayMerchantAccountChannelDTO merchantAccountChannelDTO = baseDataService.getPayMerchantAccountChannelDTOByChannel(merchantAccountDTO.getId(), submit.getPayChannel());
        if (merchantAccountChannelDTO == null) {
            throw new ArgsErrorException("收款商户对应收款渠道未配置");
        }
//        RouteDTO routeDTO = workSpace.getRouteDTO(routeService, submit.getRouteCode());
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(submit.getRouteCode());
        if (routeDTO == null || routeDTO.getEnable() != 1) {
            throw new ArgsErrorException("申报路径不存在或未启用,路径标识：" + submit.getRouteCode());
        }
//        CompanyDTO ebp = workSpace.getCompanyDTO(companyService,routeDTO.getEbpId());
        CompanyDTO ebp = baseDataService.getUnifiedCrossCodeCompanyById(routeDTO.getEbpId());
        if (ebp == null || ebp.getEnable() != 1) {
            throw new ArgsErrorException("电商平台不存在或未启用");
        }
        if (StringUtils.isEmpty(submit.getOutTradeNo())) {
            throw new ArgsErrorException("上游交易流水号不能为空");
        }
        if (StringUtils.isEmpty(submit.getTradePayNo())) {
            throw new ArgsErrorException("支付平台交易流水号不能为空");
        }
        if (StringUtils.isEmpty(submit.getOutOrderNo())) {
            throw new ArgsErrorException("上游订单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getDeclareOrderNo())) {
            throw new ArgsErrorException("申报单号不能为空");
        }
        if (StringUtils.isEmpty(submit.getBuyerIdNumber())) {
            throw new ArgsErrorException("订购人证件号码不能为空");
        }
        if (StringUtils.isEmpty(submit.getBuyerName())) {
            throw new ArgsErrorException("订购人姓名不能为空");
        }
        if (LongUtil.isNone(submit.getTradeTime())) {
            throw new ArgsErrorException("交易时间不能为空");
        }
        if (submit.getGoodsSumAmount() == null || submit.getGoodsSumAmount().compareTo(new BigDecimal(5)) <= 0) {
            throw new ArgsErrorException("商品金额不能为空且需要大于5元");
        }
        if (submit.getPayAmount() == null || submit.getPayAmount().compareTo(new BigDecimal(5)) <= 0) {
            throw new ArgsErrorException("支付金额不能为空且需要大于5元");
        }
        if (submit.getPayAmount().subtract(
                submit.getGoodsSumAmount().add(submit.getTaxAmount()).add(submit.getFeeAmount()).subtract(submit.getDiscount())
        ).abs().compareTo(new BigDecimal("0.1")) > 0) {
            throw new ArgsErrorException("费用累加结果于支付金额误差不能超过0.1元");
        }
        if (userId == null || userId.equals(0L)) {
            throw new ArgsErrorException("用户不能为空");
        }

        customsPaymentOldDO.setUserId(userId);
        customsPaymentOldDO.setSn(sequenceServiceBaseService.generatePaymentDeclareSn());
        customsPaymentOldDO.setOutOrderNo(submit.getOutOrderNo());
        customsPaymentOldDO.setDeclareOrderNo(submit.getDeclareOrderNo());
        customsPaymentOldDO.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        customsPaymentOldDO.setChannel(submit.getPayChannel());
        customsPaymentOldDO.setPayChannelId(payChannelDTO.getId());
        customsPaymentOldDO.setPayCompanyId(payCompany.getId());
        customsPaymentOldDO.setEbpId(ebp.getId());
        customsPaymentOldDO.setMerchantCode(submit.getMerchantCode());
        customsPaymentOldDO.setPayTime(new DateTime(submit.getTradeTime()).toDate());
        customsPaymentOldDO.setOutTradeNo(submit.getOutTradeNo());
        customsPaymentOldDO.setBuyerName(submit.getBuyerName());
        customsPaymentOldDO.setBuyerIdNo(submit.getBuyerIdNumber());
        customsPaymentOldDO.setBuyerIdType("1"); // 默认身份证
        customsPaymentOldDO.setCustoms(submit.getCustomsCode());
        customsPaymentOldDO.setTradePayNo(submit.getTradePayNo());
        customsPaymentOldDO.setAmount(submit.getPayAmount());
        customsPaymentOldDO.setTaxFee(submit.getTaxAmount());
        customsPaymentOldDO.setDiscountFee(submit.getDiscount());
        customsPaymentOldDO.setTransportFee(submit.getFeeAmount());
        customsPaymentOldDO.setCommodityFee(submit.getGoodsSumAmount());
        customsPaymentOldDO.setPayMerchantOutNo(submit.getPayMerchantOutNo());
        if ("tonglian".equalsIgnoreCase(submit.getPayChannel())) {
            customsPaymentOldDO.setCommodityFee(submit.getGoodsSumAmount().subtract(submit.getTaxAmount()));
        }
        customsPaymentOldDO.setCurrencyCode("142"); // 默认人民币
        customsPaymentOldDO.setTagsJson("[]");
        customsPaymentOldDO.setTenantId(submit.getTenantOuterId());
        customsPaymentOldDO.setUmfJson(payRequestMessage);
        if (CollUtil.isNotEmpty(submit.getRelDeclareOrderNoList())) {
            customsPaymentOldDO.setRelDeclareOrderNoJson(JSON.toJSONString(submit.getRelDeclareOrderNoList()));
        }
        // 后定反写
        customsPaymentOldDO.setOrderSn("");
        customsPaymentOldDO.setOrderId(1L);
        // 默认空指
        customsPaymentOldDO.setVerDept(null);
        customsPaymentOldDO.setDeclarePayNo(null);
        customsPaymentOldDO.setPayTransactionId(null);
        customsPaymentOldDO.setLastCustomsTime(null);
        customsPaymentOldDO.setLastDeclareTime(null);
        customsPaymentOldDO.setFinishTime(null);
        return customsPaymentOldDO;
    }

    public void save(OrderDO orderDO, RichOrder richOrder) {

        CustomsPaymentDO customsPaymentDO = richOrder.getCustomsPaymentDO();
        if (customsPaymentDO != null) {
            if (richOrder.getDeclareIsNew()) {
                this.add(orderDO, customsPaymentDO);
            } else {
                // 这里通过路径判断，新的申报路径是否已经存在老的申报路径里，减少查库，存在则认为修改，否则新增
                Boolean inOldAction = richOrder.inOldAction(RouteActionEnum.DECLARE_PAYMENT.getCode());
                if (inOldAction) {
                    this.update(orderDO, customsPaymentDO);
                } else {
                    this.add(orderDO, customsPaymentDO);
                }
            }
        }
    }

    private void update(OrderDO orderDO, CustomsPaymentDO customsPaymentDO) {
        CustomsPaymentDO condition = new CustomsPaymentDO();
        condition.setOrderId(orderDO.getId());
        CustomsPaymentDO old = customsPaymentMapper.selectOne(condition);
        // 支付单本体更新
        CustomsPaymentDO template = new CustomsPaymentDO();
        template.setId(old.getId());
        if (!CustomsActionStatus.DEC_SUCCESS.getValue().equals(old.getStatus())) {
            template.setStatus(CustomsActionStatus.DEC_WAIT.getValue());
        }
        template.setBuyerIdNo(customsPaymentDO.getBuyerIdNo());
        template.setBuyerName(customsPaymentDO.getBuyerName());
        template.setAmount(customsPaymentDO.getAmount());
        template.setTaxFee(customsPaymentDO.getTaxFee());
        template.setTransportFee(customsPaymentDO.getTransportFee());
        template.setCommodityFee(customsPaymentDO.getCommodityFee());
        template.setDiscountFee(customsPaymentDO.getDiscountFee());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(template);
        }
        template.setUpdateTime(new Date());
        customsPaymentMapper.updateByPrimaryKeySelective(template);
        // 支付单申报项更新
        List<CustomsPaymentDeclareDTO> customsPaymentDeclareList = customsPaymentDeclareService.findByCustomsPaymentCode(old.getSn());
        for (CustomsPaymentDeclareDTO customsPaymentDeclareDTO : customsPaymentDeclareList) {
            CustomsPaymentStatus status = CustomsPaymentStatus.NULL;
            switch (CustomsPaymentStatus.getEnum(customsPaymentDeclareDTO.getStatus())) {
                case WAIT_RE_PUSH:
                case SUCCESS:
                case FAIL:
                    status = CustomsPaymentStatus.WAIT_RE_PUSH;
                    break;
                case CANCEL: // 需要考虑取消当时是否发生过申报
                case WAIT_DECLARE:
                    status = CustomsPaymentStatus.WAIT_DECLARE;
                    break;
            }
            if (CustomsPaymentStatus.NULL.equals(status)) {
                continue;
            }
            CustomsPaymentDeclareDO customsPaymentDeclareDO = new CustomsPaymentDeclareDO();
            customsPaymentDeclareDO.setId(customsPaymentDeclareDTO.getId());
            customsPaymentDeclareDO.setStatus(status.getValue());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(customsPaymentDeclareDO);
            }
            customsPaymentDeclareDO.setUpdateTime(new Date());
            customsPaymentDeclareMapper.updateByPrimaryKeySelective(customsPaymentDeclareDO);
        }
    }

    private void add(OrderDO orderDO, CustomsPaymentDO customsPaymentDO) {
        // Step::支付单创建
        customsPaymentDO.setOrderId(orderDO.getId());
        customsPaymentDO.setOrderSn(orderDO.getSn());
        customsPaymentDO.setCreateTime(new Date());
        UserUtils.setCreateAndUpdateBy(customsPaymentDO);
        customsPaymentMapper.insertSelective(customsPaymentDO);
        // Step::支付申报动作的创建
        String customsStr = getPayCustoms(CustomsDistrictEnum.getEnum(customsPaymentDO.getCustoms())).getCode();
        CustomsPaymentDeclareDO customsPaymentDeclareDO = new CustomsPaymentDeclareDO();
        customsPaymentDeclareDO.setCustoms(customsStr);
        customsPaymentDeclareDO.setPaymentId(customsPaymentDO.getId());
        customsPaymentDeclareDO.setPaymentSn(customsPaymentDO.getSn());
        customsPaymentDeclareDO.setSn(customsPaymentDO.getSn() + "1");
        customsPaymentDeclareDO.setOutRequestNo(customsPaymentDeclareDO.getSn());
        customsPaymentDeclareDO.setStatus(CustomsPaymentStatus.WAIT_DECLARE.getValue());
        customsPaymentDeclareDO.setCreateBy(UserUtils.getUserId());
        customsPaymentDeclareDO.setUpdateBy(UserUtils.getUserId());
        customsPaymentDeclareDO.setCreateTime(new Date());
        customsPaymentDeclareDO.setUpdateTime(new Date());
        customsPaymentDeclareMapper.insertSelective(customsPaymentDeclareDO);
        // 是否需要进行二次申报
        String secondCustoms = getSecondDeclareCustoms(customsPaymentDO.getChannel(), customsStr);
        if (secondCustoms != null) {
            customsPaymentDeclareDO.setId(null);
            customsPaymentDeclareDO.setCustoms(secondCustoms);
            customsPaymentDeclareDO.setSn(customsPaymentDO.getSn() + "2");
            UserUtils.setCreateAndUpdateBy(customsPaymentDeclareDO);
            customsPaymentDeclareMapper.insertSelective(customsPaymentDeclareDO);
        }
    }

    public static PayCustoms getPayCustoms(CustomsDistrictEnum customsDistrictEnum) {
        switch (customsDistrictEnum) {
            case JINYI:
            case YIWU:
            case SHAOXING:
            case DEQING:
                return PayCustoms.HANGZHOU;
            case CHONGQING:
                return PayCustoms.CHONGQING;
            case GUANGZHOU_NS:
                return PayCustoms.GUANGZHOU_NS_GJ;
            case GUANGZHOU_HP:
                return PayCustoms.GUANGZHOU_HP;
            case TIANJIN:
                return PayCustoms.TIANJIN;
            case SHANGHAI:
                return PayCustoms.SHANGHAI;
            default:
                return PayCustoms.NULL;
        }
    }

    public static CustomsDistrictEnum getCustomsDistrict(PayCustoms payCustoms) {
        switch (payCustoms) {
            case GUANGZHOU:
            case GUANGZHOU_NS_GJ:
                return CustomsDistrictEnum.GUANGZHOU_NS;
            case HANGZHOU:
                return CustomsDistrictEnum.JINYI;
            case TIANJIN:
                return CustomsDistrictEnum.TIANJIN;
            case CHONGQING:
                return CustomsDistrictEnum.CHONGQING;
            case SHANGHAI:
                return CustomsDistrictEnum.SHANGHAI;
            default:
                return CustomsDistrictEnum.NULL;
        }
    }

    private String getSecondDeclareCustoms(String channel, String customs) {
        String secondCustoms = null;
        if (channel.equalsIgnoreCase(PayCustomsChannel.WECHAT_PAY.getValue())) {
            if (customs.equalsIgnoreCase(PayCustoms.GUANGZHOU_NS_GJ.getCode())) {
                secondCustoms = PayCustoms.GUANGZHOU.getCode();
            }
        }
        if (channel.equalsIgnoreCase(PayCustomsChannel.ALIPAY.getValue())) {
            if (customs.equalsIgnoreCase(PayCustoms.TIANJIN.getCode())) {
                secondCustoms = PayCustoms.ZONGSHU.getCode();
            }
            if (customs.equalsIgnoreCase(PayCustoms.GUANGZHOU.getCode())) {
                secondCustoms = PayCustoms.ZONGSHU.getCode();
            }
        }
        return secondCustoms;
    }
}
