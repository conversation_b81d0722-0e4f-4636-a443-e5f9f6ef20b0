package com.danding.cds.service.mq;


import com.danding.cds.c.api.service.OrderService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Component
@Slf4j
@RocketMQMessageListener(
        consumerGroup = "ccs-customs-report-c-consumer",
        topic = "ccs-customs-report-c-topic"
)
public class CustomsReportReceiver extends MessageHandler {

    @Resource
    private OrderService orderService;

    @Override
    public void handle(Object message) throws RuntimeException {
        if(ObjectUtils.isEmpty(message))return;
        String data = message.toString();
        orderService.acceptCustomsSupport(data);
    }
}
