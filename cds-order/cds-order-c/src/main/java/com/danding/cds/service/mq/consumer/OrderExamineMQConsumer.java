package com.danding.cds.service.mq.consumer;

import com.danding.cds.c.api.service.OrderService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-order-examine-c-consumer",
        topic = "ccs-order-examine-c-topic"
)
public class OrderExamineMQConsumer extends MessageHandler {

    @Resource
    private OrderService orderService;

    @Override
    public void handle(Object o) throws RuntimeException {
        if(ObjectUtils.isEmpty(o)){
            return;
        }
        String sn = o.toString();
        orderService.examine(sn);
    }
}
