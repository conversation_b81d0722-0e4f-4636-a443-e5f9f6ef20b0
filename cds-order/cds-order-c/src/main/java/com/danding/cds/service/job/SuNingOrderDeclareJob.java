package com.danding.cds.service.job;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Resource;

import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.common.utils.ExceptionJoinUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderDeclareDTO;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;

import lombok.extern.slf4j.Slf4j;


@Slf4j
@Service
public class SuNingOrderDeclareJob extends IJobHandler {

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private OrderService orderService;

    static final Integer CUSTOMS_INVENTORY_STATUS = -1;

    static final Integer DECLARING = 20;

    static final Integer CUSTOMS_SYSTEM_ERROR = 42;

    static final List<String> statusList = Arrays.asList(
            CustomsStat.CUSTOMS_PASS.getValue(),
            CustomsStat.CUSTOMS_REFUSE.getValue(),
            CustomsStat.CUSTOMS_PERSON.getValue());


    @Override
    @XxlJob(value = "OrderCSuNingOrderDeclareJob", enableTenant = false)
    public ReturnT<String> execute(String param) throws Exception {
        try {
            XxlJobLogger.log("参数设置 :" + param);
            OrderDeclareDTO orderDeclareDTO = JSON.parseObject(param, OrderDeclareDTO.class);
            log.info("[[SuNingOrderDeclareJob] 苏宁参数设置-{}]",orderDeclareDTO);
            List<OrderDTO> orderDTOListBD = getDTOListSuNingDance(orderDeclareDTO);
            orderDTOListBD.forEach(o -> reDeclareBD(o));
        }catch (Exception e){
            XxlJobLogger.log("重推失败 :" + e.getMessage());
        }
        return ReturnT.SUCCESS;
    }

    private List<OrderDTO> getDTOListSuNingDance(OrderDeclareDTO orderDeclareDTO) {
        List<OrderDTO> orderDTOList = orderService.listOrder(119L, OrderStatus.DEC_ING.getValue(),orderDeclareDTO.getPage(),orderDeclareDTO.getQueryDays());
        XxlJobLogger.log("[op:SuNingOrderDeclareJob] 苏宁待放行订单数={} ", orderDTOList.size());
        return orderDTOList;
    }


    private void reDeclareBD(OrderDTO orderDTO) {
        try {
            CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), null);
            if (DECLARING.equals(inventoryDTO.getStatus())) {
                if (statusList.contains(inventoryDTO.getCustomsStatus())) {
                    return;
                }
            }
            if (CUSTOMS_INVENTORY_STATUS.equals(inventoryDTO.getStatus())) {
                if (!CUSTOMS_SYSTEM_ERROR.equals(orderDTO.getExceptionType())) {
                    return;
                }
            }
            if (inventoryDTO.getLastDeclareTime() != null && new DateTime(inventoryDTO.getLastDeclareTime()).isBefore(DateTime.now().minusMinutes(10))) {
                orderService.resetDeclare(orderDTO.getId(), RouteActionEnum.DECLARE_INVENTORY);
                XxlJobLogger.log("[op:SuNingOrderDeclareJob] 苏宁清单待放行重推{}, 申报单号={}", RouteActionEnum.DECLARE_INVENTORY.getDesc(), orderDTO.getDeclareOrderNo());
            }
        }catch (ArgsErrorException errorException) {
            log.warn("[[op:ByteOrderDeclareJob] 拼多多待放行重推异常 Message={}]", errorException.getErrorMessage());
        }catch (Exception e) {
            log.error("[[op:SuNingOrderDeclareJob] 苏宁待放行重推异常 Message={} 堆栈：{}]", e.getMessage(), ExceptionJoinUtil.exceptionStackTraceAsString(e));
        }
    }
}
