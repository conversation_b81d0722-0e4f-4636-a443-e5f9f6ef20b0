package com.danding.cds.service.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.danding.cds.customs.order.api.dto.CustomsOrderReceive;
import com.danding.cds.service.customs.declare.PddCustomsSendDataReceiptService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: pdd.customs.send.data.receipt海关数据上报和回执数据接收 ceb311回告
 * @date 2024/8/7 13:31
 */
@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(topic = "ccs-pdd-customs-send-data-receipt-topic",
        consumerGroup = "ccs-pdd-customs-send-data-receipt-CEB312-consumer", selectorExpression = "CEB312")
public class PddCustomsSendDataReceiptCeb312Consumer extends MessageHandler {
    @Autowired
    private PddCustomsSendDataReceiptService pddCustomsSendDataReceiptService;

    @Override
    public void handle(Object o) throws RuntimeException {
        if (Objects.isNull(o)) {
            return;
        }
        try {
            String beanInfo = o.toString();
            CustomsOrderReceive customsOrderReceive = JSON.parseObject(beanInfo, CustomsOrderReceive.class);
            pddCustomsSendDataReceiptService.sendCeb312CustomsReceipt(customsOrderReceive);
        } catch (Exception e) {
            log.error("PddCustomsSendDataReceiptCeb311Consumer handle error", e);
        }
    }
}
