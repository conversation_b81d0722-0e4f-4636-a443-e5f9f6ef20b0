package com.danding.cds.service.es;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.func.VoidFunc1;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.service.CustomsInventoryCancelService;
import com.danding.cds.c.api.service.RefundOrderService;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.MoneyUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.config.AutoOrderConfig;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryAfterStatus;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.es.search.CustomsInventoryEsBuilder;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.invenorder.api.dto.InventoryTaxStatisticsDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderStepEnum;
import com.danding.cds.inventory.api.dto.StockOccupiedCountResDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.order.base.bean.dao.es.CustomsInventoryItemEsDO;
import com.danding.cds.order.base.bean.dao.es.CustomsSingleInventoryEsDO;
import com.danding.cds.order.base.bean.dao.es.OrderEsDO;
import com.danding.cds.order.base.data.service.BaseDataService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.common.es.annotations.EsDao;
import com.danding.common.es.dao.AbstractEsDao;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.http.HttpHost;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.CredentialsProvider;
import org.apache.http.impl.client.BasicCredentialsProvider;
import org.apache.lucene.search.join.ScoreMode;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentBuilder;
import org.elasticsearch.common.xcontent.XContentFactory;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryRequest;
import org.elasticsearch.script.Script;
import org.elasticsearch.script.ScriptType;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.bucket.nested.Nested;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.bucket.terms.TermsAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.sum.ParsedSum;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.sort.SortBuilders;
import org.elasticsearch.search.sort.SortOrder;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.query.*;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/13 15:14
 * @Description:
 */
@EsDao
@Slf4j
@Repository
@RefreshScope
public class CustomsSingleInventoryEsDao extends AbstractEsDao<CustomsSingleInventoryEsDO> {

    @Autowired
    private BaseDataService baseDataService;

    @Resource
    private CustomsInventoryCancelService customsInventoryCancelService;

    @Resource
    private RefundOrderService refundOrderService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Resource(name = "restHighLevelClient")
    private RestHighLevelClient restHighLevelClient;

    /**
     * 是否按照清单创建时间锁定索引
     */
    @Value("${es.index.inventory.createTime:false}")
    Boolean createTimeFlag;

    /**
     * 是否处理撤单退货的canal同步消息
     */
    @Value("${es.index.cancelRefundSyncFlag:false}")
    Boolean cancelRefundSyncFlag;

    /**
     * 批量更新或新增
     * Bulk update all objects. Will do update
     *
     * @param orderEsDOList 索引数据
     */
    public void bulkIndex(List<CustomsSingleInventoryEsDO> orderEsDOList) {

        if (CollectionUtils.isEmpty(orderEsDOList)) {
            return;
        }
        List<IndexQuery> indexQueryList = orderEsDOList.stream()
                .map(z -> new IndexQueryBuilder().withObject(z).build())
                .collect(Collectors.toList());
        elasticsearchTemplate.bulkIndex(indexQueryList);
    }

    /**
     * 滚动查询
     *
     * @param scrollTimeInMillis 时间
     * @param query              查询语句
     * @return
     */
    public ScrolledPage<OrderEsDO> startScroll(long scrollTimeInMillis, SearchQuery query) {
        return elasticsearchTemplate.startScroll(scrollTimeInMillis, query, OrderEsDO.class);
    }

    /**
     * 继续滚动获取
     *
     * @param scrollId           滚动ID
     * @param scrollTimeInMillis 时间
     * @return
     */
    public ScrolledPage<OrderEsDO> continueScroll(String scrollId, long scrollTimeInMillis) {
        return elasticsearchTemplate.continueScroll(scrollId, scrollTimeInMillis, OrderEsDO.class);
    }

    /**
     * es scroll获取数据，并处理
     *
     * @param scrollTimeInMillis 滚动开始
     * @param pageSize           页尺寸
     * @param dealFunc           功能函数，真正处理逻辑的
     * @return
     */
    public void scrollApiDealAll(long scrollTimeInMillis, Integer pageSize, VoidFunc1<List<OrderEsDO>> dealFunc) throws Exception {

        NativeSearchQuery nativeSearchQuery = new NativeSearchQueryBuilder()
                .withPageable(PageRequest.of(0, pageSize))
                .build();
        ScrolledPage<OrderEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, nativeSearchQuery, OrderEsDO.class);

        while (scrolledPage.hasContent()) {
            final List<OrderEsDO> content = scrolledPage.getContent();
            // 执行处理
            dealFunc.call(content);
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, OrderEsDO.class);
        }
    }

    /**
     * yyMM eg.2207
     * 2-9 xx
     * 0-12 xx
     */
    private static final String MONTH_REGEX = "^([2-9]\\d{1})(([0]{0,1}[1-9])|([1][0-2]))$";

    /**
     * ES保存
     *
     * @param customsInventoryDTO 清单实体
     * @param itemList
     */
    public void esSave(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> itemList) {
        CustomsSingleInventoryEsDO inventoryVO = buildCustomsSingleInventoryEsDO(customsInventoryDTO);
        List<CustomsInventoryItemEsDO> itemExtraDTOList = new ArrayList<>();
        BigDecimal totalFee = new BigDecimal(0);
        for (CustomsInventoryItemDTO dto : itemList) {
            CustomsInventoryItemExtra extra = JSON.parseObject(dto.getExtraJson(), CustomsInventoryItemExtra.class);
            CustomsInventoryItemEsDO itemExtra = new CustomsInventoryItemEsDO();
            itemExtra.setId(dto.getId());
            itemExtra.setProductId(extra.getProductId());
            itemExtra.setGoodsSeqNo(extra.getGoodsSeqNo());
            itemExtra.setGoodsName(extra.getGoodsName());
            if (StrUtil.isBlank(itemExtra.getGoodsName())) {
                itemExtra.setGoodsName(dto.getItemName());
            }
            itemExtra.setCount(dto.getCount());
            itemExtra.setUnitPrice(dto.getUnitPrice().doubleValue());
            itemExtra.setUnifiedProductId(extra.getUnifiedProductId());
            itemExtra.setItemTag(dto.getItemTag());
            itemExtra.setHsCode(extra.getHsCode());
            itemExtraDTOList.add(itemExtra);
            totalFee = totalFee.add(dto.getUnitPrice().multiply(BigDecimal.valueOf(dto.getCount())));
        }
        inventoryVO.setTotalFee(MoneyUtil.yuan2fen(totalFee));
        inventoryVO.setItemExtras(itemExtraDTOList);
        if (createTimeFlag) {
            IndexQueryBuilder builder = new IndexQueryBuilder();
            IndexQuery build = builder.withObject(inventoryVO).build();
            String orderSn = customsInventoryDTO.getSn();
            String indexName = getIndexName(orderSn);
            build.setIndexName(indexName);
            log.info("save indexName:{} build={}", indexName, JSON.toJSONString(build));
            this.elasticsearchTemplate.index(build);
        } else {
            log.info("save inventoryVO={}", JSON.toJSONString(inventoryVO));
            this.save(inventoryVO);
        }
    }

    public void esSave(List<CustomsInventoryDTO> list) {
        List<CustomsSingleInventoryEsDO> data = list.stream().map(dto -> buildCustomsSingleInventoryEsDO(dto)).collect(Collectors.toList());
        this.bulkIndex(data);
    }

    public CustomsSingleInventoryEsDO buildCustomsSingleInventoryEsDO(CustomsInventoryDTO customsInventoryDTO) {
        CustomsSingleInventoryEsDO inventoryVO = new CustomsSingleInventoryEsDO();
        BeanUtils.copyProperties(customsInventoryDTO, inventoryVO);
        inventoryVO.setId(customsInventoryDTO.getId().toString());
        inventoryVO.setSn(customsInventoryDTO.getSn());
        inventoryVO.setInventoryNo(customsInventoryDTO.getInventoryNo());
        inventoryVO.setOrderId(customsInventoryDTO.getOrderId().toString());
        inventoryVO.setOrderSn(customsInventoryDTO.getOrderSn());
        inventoryVO.setStatus(customsInventoryDTO.getStatus());
        inventoryVO.setStatusDesc(CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc());
        inventoryVO.setExitRegionStatusDesc(Objects.equals(customsInventoryDTO.getExitRegionStatus(), 1) ? "已出区" : "未出区");
        // 用户
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        inventoryVO.setTenantName(inventoryExtra.getTenantName());
        inventoryVO.setTenantOuterId(inventoryExtra.getTenantOuterId());
        inventoryVO.setAfterSalesStatus(customsInventoryDTO.getAfterSalesStatus());
        inventoryVO.setReviewStatus(customsInventoryDTO.getReviewStatus());
        inventoryVO.setCustomsStatus(customsInventoryDTO.getCustomsStatus());
        if (Objects.nonNull(customsInventoryDTO.getTotalTax())) {
            inventoryVO.setTotalTax(customsInventoryDTO.getTotalTax().doubleValue());
        }
        // 存储下上游计算的税金
        if (inventoryExtra.getTaxFee() != null) {
            inventoryVO.setCalculateTaxFee(MoneyUtil.yuan2fen(inventoryExtra.getTaxFee()));
        }
        if (!org.springframework.util.StringUtils.isEmpty(customsInventoryDTO.getCustomsStatus()) && !org.springframework.util.StringUtils.isEmpty(customsInventoryDTO.getCustomsDetail())) {
            try {
                inventoryVO.setCustomsStatusDesc(CustomsStat.getEnum(customsInventoryDTO.getCustomsStatus()).getDesc());
                inventoryVO.setCustomsDetail(customsInventoryDTO.getCustomsDetail());
            } catch (Exception e) {
                inventoryVO.setCustomsStatusDesc(CustomsStat.NULL.getDesc());
                inventoryVO.setCustomsDetail("暂无");
            }
        } else {
            inventoryVO.setCustomsDetail("暂无");
        }
        inventoryVO.setCreateTime(customsInventoryDTO.getCreateTime().getTime());
        inventoryVO.setUpdateTime(customsInventoryDTO.getUpdateTime().getTime());
//        inventoryVO.setDeleted(Boolean.TRUE.equals(customsInventoryDTO.getDeleted()) ? 1 : 0);
        if (customsInventoryDTO.getCustomsPassTime() != null) {
            inventoryVO.setCustomsPassTime(customsInventoryDTO.getCustomsPassTime().getTime());
        }

        if (StrUtil.isNotEmpty(customsInventoryDTO.getCustomsStatus()) && !org.springframework.util.StringUtils.isEmpty(customsInventoryDTO.getLastCustomsTime())) {
            inventoryVO.setReceiveTime(customsInventoryDTO.getLastCustomsTime().getTime());
        }
        if (customsInventoryDTO.getLastDeclareTime() != null) {
            inventoryVO.setDeclareTime(customsInventoryDTO.getLastDeclareTime().getTime());
        }
        ExpressDTO expressDTO = baseDataService.getExpressDTOById(customsInventoryDTO.getExpressId());
        if (Objects.nonNull(expressDTO)) {
            inventoryVO.setExpressName(expressDTO.getName());
        }

        CompanyDTO ebcCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbcId());
        if (Objects.isNull(ebcCompany)) {
            inventoryVO.setEbcName("");
        } else {
            inventoryVO.setEbcName(ebcCompany.getName());
        }
        CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbpId());
        if (Objects.isNull(ebpCompany)) {
            inventoryVO.setEbpName("");
        } else {
            inventoryVO.setEbpName(ebpCompany.getName());
        }
        CompanyDTO agentCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAgentCompanyId());
        if (Objects.isNull(agentCompany)) {
            inventoryVO.setAgentCompanyName("");
        } else {
            inventoryVO.setAgentCompanyName(agentCompany.getName());
        }
        CompanyDTO areaCompany = baseDataService.getSpecialClientCodeCompanyById(customsInventoryDTO.getAreaCompanyId());
        if (Objects.isNull(areaCompany)) {
            inventoryVO.setAreaCompanyName("");
        } else {
            inventoryVO.setAreaCompanyName(areaCompany.getName());
        }
        CompanyDTO assureCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAssureCompanyId());
        if (Objects.isNull(assureCompany)) {
            inventoryVO.setAssureCompanyName("");
        } else {
            inventoryVO.setAssureCompanyName(assureCompany.getName());
        }
        CompanyDTO logisticsCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getLogisticsCompanyId());
        if (Objects.isNull(logisticsCompany)) {
            inventoryVO.setLogisticsCompanyName("");
        } else {
            inventoryVO.setLogisticsCompanyName(logisticsCompany.getName());
        }
        if (Objects.nonNull(customsInventoryDTO.getTenantryId())) {
            inventoryVO.setTenantryId(customsInventoryDTO.getTenantryId());
        } else {
            inventoryVO.setTenantryId(1001L);
        }
        CustomsInventoryCancelDTO cancelDTO = customsInventoryCancelService.findByInventoryId(customsInventoryDTO.getId());
        if (Objects.nonNull(cancelDTO)) {
            inventoryVO.setCancelStatus(cancelDTO.getStatus());
            inventoryVO.setCancelCreateTime(cancelDTO.getCreateTime().getTime());
        }
        RefundOrderInfoDto refundOrderInfoDto = refundOrderService.findByDeclareNo(customsInventoryDTO.getDeclareOrderNo());
        if (Objects.nonNull(refundOrderInfoDto)) {
            inventoryVO.setRefundStatus(refundOrderInfoDto.getRefundCheckStatus());
            inventoryVO.setRefundCreateTime(refundOrderInfoDto.getCreateTime().getTime());
        }
        if (Objects.nonNull(customsInventoryDTO.getCheckOutStatus())) {
            inventoryVO.setCheckOutStatus(customsInventoryDTO.getCheckOutStatus());
        }
        if (Objects.nonNull(customsInventoryDTO.getCheckOutTime())) {
            inventoryVO.setCheckOutTime(customsInventoryDTO.getCheckOutTime().getTime());
        }
        inventoryVO.setNote(customsInventoryDTO.getNote());
        return inventoryVO;
    }

    /**
     * 功能描述:  分页查询清单申报列表
     * 创建时间:  2021/8/26 9:22 上午
     *
     * @param search:
     * @return org.springframework.data.domain.Page<com.danding.cds.customs.inventory.impl.es.CustomsSingleInventoryEsDO>
     * <AUTHOR>
     */
    public Page<CustomsSingleInventoryEsDO> paging(SingleInvtOrderSearch search) {
        BoolQueryBuilder boolQueryBuilder = CustomsInventoryEsBuilder.getBoolQueryBuilder(search);
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(search.getCurrentPage() - 1, search.getPageSize()))
                .withCollapseField("id.keyword").build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        Page<CustomsSingleInventoryEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, CustomsSingleInventoryEsDO.class);
        return page;
    }

    public List<String> getUnExistQueryNos(SingleInvtOrderSearch search) {
        if (StringUtils.isEmpty(search.getQueryInfo())) {
            return new ArrayList<>();
        }
        List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
        if (CollectionUtils.isEmpty(noList)) {
            return new ArrayList<>();
        }
        if (noList.size() > 1000) {
            throw new ArgsInvalidException("不允许超过1000单");
        }
        BoolQueryBuilder boolQueryBuilder = CustomsInventoryEsBuilder.getBoolQueryBuilder(search);
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(0, 1000))
                .withCollapseField("id.keyword").build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        Page<CustomsSingleInventoryEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, CustomsSingleInventoryEsDO.class);
        List<CustomsSingleInventoryEsDO> content = page.getContent();
        List<String> existNoList = new ArrayList<>();
        if ("declareOrderNo".equals(search.getQueryType())) {
            existNoList = content.stream().map(CustomsSingleInventoryEsDO::getDeclareOrderNo).distinct().collect(Collectors.toList());
        } else if ("inventoryNo".equals(search.getQueryType())) {
            existNoList = content.stream().map(CustomsSingleInventoryEsDO::getInventoryNo).distinct().collect(Collectors.toList());
        } else if ("logisticsNo".equals(search.getQueryType())) {
            existNoList = content.stream().map(CustomsSingleInventoryEsDO::getLogisticsNo).distinct().collect(Collectors.toList());
        }
        if (!CollectionUtils.isEmpty(existNoList)) {
            noList.removeAll(existNoList);
        }
        return noList;
    }

    public InventoryTaxStatisticsDTO taxsStatistics(SingleInvtOrderSearch search) {
        if (StrUtil.isNotEmpty(search.getErpPhyWarehouseSn())) {
            List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(search.getErpPhyWarehouseSn());
            if (CollectionUtil.isEmpty(dtoByErpCode)) {
                search.setAccountBookIdList(Lists.newArrayList(-1L));
            } else {
                List<Long> list = dtoByErpCode.stream().map(EntityWarehouseDTO::getCustomsBookId).collect(Collectors.toList());
                search.setAccountBookIdList(list);
            }
        }
        String sum_totalTax = "totalTax";
        String sum_calculateTaxFee = "calculateTaxFee";
        String sum_totalFee = "totalFee";
        BoolQueryBuilder boolQueryBuilder = CustomsInventoryEsBuilder.getBoolQueryBuilder(search);
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(AggregationBuilders.sum(sum_totalTax).field("totalTax"))
                .addAggregation(AggregationBuilders.sum(sum_calculateTaxFee).field("calculateTaxFee"))
                .addAggregation(AggregationBuilders.sum(sum_totalFee).field("totalFee"))
                .build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        return elasticsearchTemplate.query(searchQuery, reponse -> {
            InventoryTaxStatisticsDTO taxStatisticsDTO = new InventoryTaxStatisticsDTO();
            List<Aggregation> aggregationList = reponse.getAggregations().asList();
            for (Aggregation aggre : aggregationList) {
                ParsedSum parsedSum = (ParsedSum) aggre;
                String name = parsedSum.getName();
                Double value = parsedSum.getValue();
                if (Objects.equals(sum_totalTax, name)) {
                    // ES这里单位是分
                    taxStatisticsDTO.setTotalTax(MoneyUtil.fen2Yuan(value.longValue()));
                } else if (Objects.equals(sum_calculateTaxFee, name)) {
                    // ES单位是分
                    taxStatisticsDTO.setCalculateTaxFee(MoneyUtil.fen2Yuan(value.longValue()));
                } else if (Objects.equals(sum_totalFee, name)) {
                    // ES单位是分
                    taxStatisticsDTO.setTotalFee(MoneyUtil.fen2Yuan(value.longValue()));
                }
            }
            return taxStatisticsDTO;
        });
    }

    private BoolQueryBuilder getBoolQueryBuilder(SingleInvtOrderSearch search) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        Long tenantryId = SimpleTenantHelper.getTenantId();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        if (Objects.nonNull(tenantryId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("tenantryId", tenantryId));
        }
        // 账册列表
        List<Long> accountBookIds = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIds)) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountBookId", accountBookIds));
        }
        //账册编号
        if (!LongUtil.isNone(search.getAccountBookId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("accountBookId", search.getAccountBookId()));
        }
        //备案名称
        if (Objects.nonNull(search.getNote())) {
            boolQueryBuilder.filter(QueryBuilders.matchQuery("note", search.getNote()));
        }
        //创建时间
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createFrom).to(createTo));
        }
        //申报成功时间
        Long customsPassFrom = LongUtil.getFrom(search.getCustomsPassFrom(), search.getCustomsPassTo());
        Long customsPassTo = LongUtil.getEnd(search.getCustomsPassFrom(), search.getCustomsPassTo());
        if (!LongUtil.isNone(customsPassFrom) && !LongUtil.isNone(customsPassTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("customsPassTime").from(customsPassFrom).to(customsPassTo));
        }
        //申报时间
        Long lastDeclareFrom = LongUtil.getFrom(search.getLastDeclareFrom(), search.getLastDeclareTo());
        Long lastDeclareTo = LongUtil.getEnd(search.getLastDeclareFrom(), search.getLastDeclareTo());
        if (!LongUtil.isNone(lastDeclareFrom) && !LongUtil.isNone(lastDeclareTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastDeclareTime").from(lastDeclareFrom).to(lastDeclareTo));
        }
        //回执时间
        Long lastReceiveFrom = LongUtil.getFrom(search.getLastReceiveFrom(), search.getLastReceiveTo());
        Long lastReceiveTo = LongUtil.getEnd(search.getLastReceiveFrom(), search.getLastReceiveTo());
        if (!LongUtil.isNone(lastReceiveFrom) && !LongUtil.isNone(lastReceiveTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("lastCustomsTime").from(lastReceiveFrom).to(lastReceiveTo));
        }
        if (!LongUtil.isNone(search.getEbcId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebcId", search.getEbcId()));
        }
        if (!LongUtil.isNone(search.getEbpId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("ebpId", search.getEbpId()));
        }
        if (!LongUtil.isNone(search.getAgentCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("agentCompanyId", search.getAgentCompanyId()));
        }
        if (!LongUtil.isNone(search.getAreaCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("areaCompanyId", search.getAreaCompanyId()));
        }
        if (search.getStatus() != null && search.getStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("status", search.getStatus()));
        }
        if (!StringUtils.isEmpty(search.getCustomsStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", search.getCustomsStatus()));
        }
        if (search.getExitRegionStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", search.getExitRegionStatus()));
        }
        if (search.getHandoverStatus() != null) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("handoverStatus", search.getHandoverStatus()));
        }
        if (!CollectionUtils.isEmpty(search.getUser())) {
            BoolQueryBuilder tenantOuterIdBuilder = new BoolQueryBuilder();
            tenantOuterIdBuilder.should(QueryBuilders.termsQuery("tenantOuterId", search.getUser()));
            tenantOuterIdBuilder.should(QueryBuilders.termsQuery("tenantOuterId.keyword", search.getUser()));
            boolQueryBuilder.filter(tenantOuterIdBuilder);
        }
        if (search.getAfterStatus() != null && search.getAfterStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("afterSalesStatus", search.getAfterStatus()));
        }
        if (search.getReviwStatus() != null && search.getReviwStatus() != 0) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("reviewStatus", search.getReviwStatus()));
        }
        if (!org.apache.commons.lang3.StringUtils.isEmpty(search.getInveOrderStep())) {
            if (InventoryOrderStepEnum.STEP_CANCLE.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可撤清单：已放行，未出区
                boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", CustomsStat.CUSTOMS_PASS));
                boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 0));
            } else if (InventoryOrderStepEnum.STEP_RETURN.getCode().equalsIgnoreCase(search.getInveOrderStep())) {//可退清单：已放行，已出区
                boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", CustomsStat.CUSTOMS_PASS));
                boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 1));
            }
        }
        if (Objects.nonNull(search.getCancelStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("cancelStatus", search.getCancelStatus()));
        }
        if (Objects.nonNull(search.getRefundStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("refundStatus", search.getRefundStatus()));
        }
        if (!LongUtil.isNone(search.getCancelCreateTimeFrom()) && !LongUtil.isNone(search.getCancelCreateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("cancelCreateTime").from(search.getCancelCreateTimeFrom()).to(search.getCancelCreateTimeTo()));
        }
        if (!LongUtil.isNone(search.getRefundCreateTimeFrom()) && !LongUtil.isNone(search.getRefundCreateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("refundCreateTime").from(search.getRefundCreateTimeFrom()).to(search.getRefundCreateTimeTo()));
        }
        if (CollUtil.isNotEmpty(search.getCustomsInventorySnList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("sn.keyword", search.getCustomsInventorySnList()));
        }

        if (Objects.nonNull(search.getTaxBillStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("taxBillStatus", search.getTaxBillStatus()));
        }
        if (Objects.nonNull(search.getAssureCompanyId())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("assureCompanyId", search.getAssureCompanyId()));
        }
        if (Objects.nonNull(search.getCheckOutStatus())) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("checkOutStatus", search.getCheckOutStatus()));
        }
        if (!LongUtil.isNone(search.getCheckOutTimeFrom()) && !LongUtil.isNone(search.getCheckOutTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("checkOutTime").from(search.getCheckOutTimeFrom()).to(search.getCheckOutTimeTo()));
        }
        if (CollUtil.isNotEmpty(search.getAccountBookIdList())) {
            boolQueryBuilder.filter(QueryBuilders.termsQuery("accountBookId", search.getAccountBookIdList()));
        }
        if (!StringUtils.isEmpty(search.getQueryInfo())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if (!CollectionUtils.isEmpty(noList)) {
                if ("declareOrderNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("declareOrderNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("inventoryNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("inventoryNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("logisticsNo".equals(search.getQueryType())) {
                    BoolQueryBuilder outOrderNoBuilder = new BoolQueryBuilder();
                    outOrderNoBuilder.should(QueryBuilders.termsQuery("logisticsNo.keyword", noList));
                    boolQueryBuilder.filter(outOrderNoBuilder);
                } else if ("productId".equals(search.getQueryType())) {
                    QueryBuilder orderQuery = QueryBuilders.nestedQuery("itemExtras",
                            QueryBuilders.boolQuery()
                                    .must(QueryBuilders.termsQuery("itemExtras.productId.keyword", noList)),
                            ScoreMode.None);
                    boolQueryBuilder.must(orderQuery);
                }
            }
        }
        if (Objects.nonNull(search.getContainFbGifts())) {
            Map<String, Object> params = new HashMap(1) {{
                put("itemTag", OrderItemTagEnum.FB_GIFTS.getCode());
            }};
            String script = "return ((doc['itemExtras.itemTag'].value & params.itemTag) != 0);";
            Script orderTagsScript = new Script(ScriptType.INLINE, Script.DEFAULT_SCRIPT_LANG, script, params);
            QueryBuilder orderTagQuery = QueryBuilders.nestedQuery("itemExtras",
                    QueryBuilders.boolQuery()
                            .must(QueryBuilders.existsQuery("itemExtras.itemTag"))
                            .must(QueryBuilders.scriptQuery(orderTagsScript)),
                    ScoreMode.None);
            if (Objects.equals(search.getContainFbGifts(), true)) {
                boolQueryBuilder.must(orderTagQuery);
            } else {
                boolQueryBuilder.mustNot(orderTagQuery);
            }
        }
        return boolQueryBuilder;
    }

    /**
     * 获取库存占用中订单信息
     *
     * @param search
     * @param customsBookItemDTOList
     * @return
     */
    public Page<CustomsSingleInventoryEsDO> getStockOccupiedInventoryOrder(StockOccupiedDetailSearch search, List<CustomsBookItemDTO> customsBookItemDTOList) {
        if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
            throw new ArgsInvalidException("获取占用明细 账册库存信息为空");
        }
        Long customsBookId = customsBookItemDTOList.get(0).getCustomsBookId();
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        List<Integer> statusList = Arrays.asList(
                CustomsActionStatus.DEC_ING.getValue(),
                CustomsActionStatus.DEC_SUCCESS.getValue());
        List<Integer> afterStatusList = Arrays.asList(InventoryAfterStatus.NO.getValue(), InventoryAfterStatus.YES.getValue());
        BoolQueryBuilder statusQueryBuilder1 = new BoolQueryBuilder();
        BoolQueryBuilder statusQueryBuilder2 = new BoolQueryBuilder();
        statusQueryBuilder1.must(QueryBuilders.termsQuery("status", statusList))
                .must(QueryBuilders.termsQuery("afterSalesStatus", afterStatusList));
        statusQueryBuilder2.must(QueryBuilders.termQuery("status", CustomsActionStatus.DEC_FAIL.getValue()))
                .must(QueryBuilders.termQuery("afterSalesStatus", InventoryAfterStatus.NO.getValue()));
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery().should(statusQueryBuilder1)
                .should(statusQueryBuilder2);
        BoolQueryBuilder discardQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("logisticsNo.keyword"))
                .mustNot(QueryBuilders.wildcardQuery("logisticsNo.keyword", "*_*"));
        boolQueryBuilder.must(statusQueryBuilder)
                .must(QueryBuilders.termQuery("accountBookId", customsBookId))
                .must(QueryBuilders.termQuery("exitRegionStatus", 0))
                .must(discardQueryBuilder);
        BoolQueryBuilder itemExtrasBoolQuery = QueryBuilders.boolQuery();
        customsBookItemDTOList.forEach(c -> {
            String productId = c.getProductId();
            String goodsSeqNo = c.getGoodsSeqNo();
            BoolQueryBuilder must = QueryBuilders.boolQuery()
                    .must(QueryBuilders.termsQuery("itemExtras.productId.keyword", productId))
                    .must(QueryBuilders.termsQuery("itemExtras.goodsSeqNo.keyword", goodsSeqNo));
            itemExtrasBoolQuery.should(must);
        });
        QueryBuilder nestedQuery = QueryBuilders.nestedQuery("itemExtras",
                itemExtrasBoolQuery,
                ScoreMode.None);
        boolQueryBuilder.must(nestedQuery);
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withPageable(PageRequest.of(search.getCurrentPage() - 1, search.getPageSize()))
                .withCollapseField("id.keyword").build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        log.info("getStockOccupiedInventoryOrder - searchQuery={}", JSONUtils.toJSONString(searchQuery));
        Page<CustomsSingleInventoryEsDO> page = elasticsearchTemplate.queryForPage(searchQuery, CustomsSingleInventoryEsDO.class);
        return page;
    }

    public List<StockOccupiedCountResDTO> getStockOccupiedCountList(List<CustomsBookItemDTO> customsBookItemDTOList, List<Long> bookIdList) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        List<Integer> statusList = Arrays.asList(
                CustomsActionStatus.DEC_ING.getValue(),
                CustomsActionStatus.DEC_SUCCESS.getValue());
        List<Integer> afterStatusList = Arrays.asList(InventoryAfterStatus.NO.getValue(), InventoryAfterStatus.YES.getValue());
        BoolQueryBuilder statusQueryBuilder1 = new BoolQueryBuilder();
        BoolQueryBuilder statusQueryBuilder2 = new BoolQueryBuilder();
        statusQueryBuilder1.must(QueryBuilders.termsQuery("status", statusList))
                .must(QueryBuilders.termsQuery("afterSalesStatus", afterStatusList));
        statusQueryBuilder2.must(QueryBuilders.termQuery("status", CustomsActionStatus.DEC_FAIL.getValue()))
                .must(QueryBuilders.termQuery("afterSalesStatus", InventoryAfterStatus.NO.getValue()));
        BoolQueryBuilder statusQueryBuilder = QueryBuilders.boolQuery().should(statusQueryBuilder1)
                .should(statusQueryBuilder2);
        BoolQueryBuilder discardQueryBuilder = QueryBuilders.boolQuery().must(QueryBuilders.existsQuery("logisticsNo.keyword"))
                .mustNot(QueryBuilders.wildcardQuery("logisticsNo.keyword", "*_*"));
        boolQueryBuilder.must(statusQueryBuilder)
                .must(QueryBuilders.termQuery("exitRegionStatus", 0))
                .must(discardQueryBuilder);
        if (CollectionUtil.isNotEmpty(bookIdList)) {
            boolQueryBuilder.must(QueryBuilders.termsQuery("accountBookId", bookIdList));
        }
        BoolQueryBuilder itemExtrasBoolQuery = QueryBuilders.boolQuery();
        if (CollectionUtil.isEmpty(bookIdList) && CollectionUtil.isNotEmpty(customsBookItemDTOList)) {
            customsBookItemDTOList.forEach(c -> {
                String productId = c.getProductId();
                String goodsSeqNo = c.getGoodsSeqNo();
                BoolQueryBuilder must = QueryBuilders.boolQuery()
                        .must(QueryBuilders.termsQuery("itemExtras.productId.keyword", productId))
                        .must(QueryBuilders.termsQuery("itemExtras.goodsSeqNo.keyword", goodsSeqNo));
                itemExtrasBoolQuery.should(must);
            });
        }
        boolQueryBuilder.must(itemExtrasBoolQuery);

        TermsAggregationBuilder aggregationBuilder = AggregationBuilders.terms("bookIdAgg").field("accountBookId").size(Integer.MAX_VALUE)
                .subAggregation(AggregationBuilders.nested("itemExtras", "itemExtras")
                        .subAggregation(AggregationBuilders.terms("productIdAgg").field("itemExtras.productId.keyword").size(Integer.MAX_VALUE)
                                .subAggregation(AggregationBuilders.terms("goodsSeqNoAgg").field("itemExtras.goodsSeqNo.keyword").size(Integer.MAX_VALUE)
                                        .subAggregation(AggregationBuilders.sum("countSum").field("itemExtras.count")))));
        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        log.info("getStockOccupiedInventoryOrder - searchQuery={}", JSONUtils.toJSONString(searchQuery));

        Map<String, StockOccupiedCountResDTO> stockOccupiedCountResDTOMap = elasticsearchTemplate.query(searchQuery, response -> {
            Map<String, StockOccupiedCountResDTO> stockOccupiedCountResDTOS = new HashMap<>();
            Terms bookIdAgg = response.getAggregations().get("bookIdAgg");
            List<? extends Terms.Bucket> bookIdAggBuckets = bookIdAgg.getBuckets();
            for (Terms.Bucket bookIdBucket : bookIdAggBuckets) {
                Long bookId = (Long) bookIdBucket.getKey();
                Nested itemExtrasAgg = bookIdBucket.getAggregations().get("itemExtras");
                Terms productIdAgg = itemExtrasAgg.getAggregations().get("productIdAgg");
                List<? extends Terms.Bucket> productIdBuckets = productIdAgg.getBuckets();
                for (Terms.Bucket productIdBucket : productIdBuckets) {
                    String productId = (String) productIdBucket.getKey();
                    Terms goodsSeqNoAgg = productIdBucket.getAggregations().get("goodsSeqNoAgg");
                    List<? extends Terms.Bucket> goodsSeqNoBuckets = goodsSeqNoAgg.getBuckets();
                    for (Terms.Bucket goodsSeqNoBucket : goodsSeqNoBuckets) {
                        String goodsSeqNo = (String) goodsSeqNoBucket.getKey();
                        Sum countSum = goodsSeqNoBucket.getAggregations().get("countSum");
                        Double stockOccupiedNum = countSum.getValue();
                        StockOccupiedCountResDTO stockOccupiedCountResDTO = new StockOccupiedCountResDTO(bookId, productId, goodsSeqNo, stockOccupiedNum);
                        stockOccupiedCountResDTOS.put(bookId + "-" + goodsSeqNo, stockOccupiedCountResDTO);
                    }
                }
            }
            return stockOccupiedCountResDTOS;
        });
        for (CustomsBookItemDTO customsBookItemDTO : customsBookItemDTOList) {
            String mapKey = customsBookItemDTO.getCustomsBookId() + "-" + customsBookItemDTO.getGoodsSeqNo();
            if (!stockOccupiedCountResDTOMap.containsKey(mapKey)) {
                StockOccupiedCountResDTO stockOccupiedCountResDTO = new StockOccupiedCountResDTO(
                        customsBookItemDTO.getCustomsBookId(),
                        customsBookItemDTO.getProductId(),
                        customsBookItemDTO.getGoodsSeqNo(),
                        (double) 0
                );
                stockOccupiedCountResDTOMap.put(mapKey, stockOccupiedCountResDTO);
            }
        }
        List<StockOccupiedCountResDTO> result = new ArrayList<>(stockOccupiedCountResDTOMap.values());
        log.info("getStockOccupiedInventoryOrder - result={}", JSONUtils.toJSONString(result));
        if (CollectionUtils.isEmpty(result)) {
            return new ArrayList<>();
        }
        return result;
    }

    /**
     * 根据清单id查询
     *
     * @param customsInventoryId
     * @return
     */
    public CustomsSingleInventoryEsDO getByInventoryId(Long customsInventoryId) {
        return getById(String.valueOf(customsInventoryId));
    }

    /**
     * 删除索引及数据
     *
     * @param month
     * @return
     */
    public Object delIndexByMonth(Integer month) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        DeleteQuery deleteQuery = new DeleteQuery();
        deleteQuery.setQuery(boolQueryBuilder);
        Date date = set(new Date(), Calendar.MONTH, -month);
//        collectBoolQueryBuilder.must(termQuery("trackingId", trackingId));
        String index = AutoOrderConfig.customsInventoryIndex + "_*";
        deleteQuery.setIndex(index);
        deleteQuery.setType("customsSingleInventory");
        deleteQuery.setPageSize(Integer.MAX_VALUE);
        elasticsearchTemplate.delete(deleteQuery);
        return "";
    }

    private static Date set(Date date, int calendarField, int amount) {
        Assert.notNull(date, "The date must not be null");
        Calendar c = Calendar.getInstance();
        c.setLenient(false);
        c.setTime(date);
        c.add(calendarField, amount);
        return c.getTime();
    }

    /**
     * 根据时间范围获取 海关状态与清单状态不匹配的 清单EsDTO
     *
     * @param createTimeFrom
     * @param createTimeTo
     * @return
     */
    public List<CustomsSingleInventoryEsDTO> getDiffInventoryEsDTOByCreateTime(Long createTimeFrom, Long createTimeTo) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        Long createFrom = LongUtil.getFrom(createTimeFrom, createTimeTo);
        Long createTo = LongUtil.getEnd(createTimeFrom, createTimeTo);
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createTimeFrom).to(createTimeTo));
        }
        boolQueryBuilder.mustNot(QueryBuilders.termQuery("status", CustomsActionStatus.DEC_SUCCESS.getValue()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("customsStatus", CustomsStat.CUSTOMS_PASS.getValue()));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder).build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        long scrollTimeInMillis = 10 * 1000;
        ScrolledPage<CustomsSingleInventoryEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, CustomsSingleInventoryEsDO.class);
        List<CustomsSingleInventoryEsDTO> result = new ArrayList<>();
        while (scrolledPage.hasContent()) {
            List<CustomsSingleInventoryEsDO> content = scrolledPage.getContent();
            result.addAll(ConvertUtil.listConvert(content, CustomsSingleInventoryEsDTO.class));
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, CustomsSingleInventoryEsDO.class);
        }
        return result;
    }

    public List<CustomsSingleInventoryEsDTO> getCusPersonInvOrderEsDTOList(ExportCusOfficerDataReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return new ArrayList<>();
        }
        long scrollTimeInMillis = 10 * 1000;
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
//        boolQueryBuilder.filter(QueryBuilders.termQuery("deleted", 0));
        if (!LongUtil.isNone(reqVO.getCreateTimeFrom()) && !LongUtil.isNone(reqVO.getCreateTimeTo())) {
            boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(reqVO.getCreateTimeFrom()).to(reqVO.getCreateTimeTo()));
        }
        boolQueryBuilder.filter(QueryBuilders.termQuery("accountBookId", reqVO.getBookId()));
        boolQueryBuilder.filter(QueryBuilders.wildcardQuery("customsDetail.keyword", "*人工审核*"));
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withIndices("ccs_customs_inventory_index_*")
                .build();
        ScrolledPage<CustomsSingleInventoryEsDO> scrolledPage = elasticsearchTemplate.startScroll(scrollTimeInMillis, searchQuery, CustomsSingleInventoryEsDO.class);
        List<CustomsSingleInventoryEsDTO> result = new ArrayList<>();
        while (scrolledPage.hasContent()) {
            List<CustomsSingleInventoryEsDO> content = scrolledPage.getContent();
            result.addAll(ConvertUtil.listConvert(content, CustomsSingleInventoryEsDTO.class));
            // 滚动获取
            scrolledPage = elasticsearchTemplate.continueScroll(scrolledPage.getScrollId(), scrollTimeInMillis, CustomsSingleInventoryEsDO.class);
        }
        return result;
    }

    public void updateCancelInfo(CustomsInventoryCancelDTO customsInventoryCancelDTO) {
        String id = customsInventoryCancelDTO.getRefInvoId().toString();
        String customsInventoryOrderSn = customsInventoryCancelDTO.getRefInvoSn();
        String cancelStatus = customsInventoryCancelDTO.getStatus();
        Date cancelCreateTime = customsInventoryCancelDTO.getCreateTime();
        log.info("updateCancelInfo customsInventoryOrderSn={} cancelStatus={} cancelCreateTime={} id={}", customsInventoryOrderSn, cancelStatus, JSON.toJSONString(cancelCreateTime), id);
        if (!cancelRefundSyncFlag) {
            log.info("updateCancelInfo 未开启同步");
            return;
        }
        String indexName = getIndexName(customsInventoryOrderSn);
        try {
            UpdateRequest request = new UpdateRequest(indexName, "customsSingleInventory", id);
            request.retryOnConflict(5);
            // 设置要更新的字段值
            XContentBuilder contentBuilder = XContentFactory.jsonBuilder().startObject()
                    .field("cancelStatus", cancelStatus).field("cancelCreateTime", cancelCreateTime.getTime())
                    .endObject();
            request.doc(contentBuilder);
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("updateCancelInfo error={}", e.getMessage(), e);
        }
    }

    /**
     * 不需要确定index
     *
     * @throws IOException
     */
    @Test
    public void testt() throws IOException {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "Qw$12345678"));
        RestClientBuilder builder = RestClient.builder(HttpHost.create("http://es716.yang800.com.cn"))
                .setRequestConfigCallback(request -> request.setConnectTimeout(15 * 1000).setSocketTimeout(15 * 1000))
                .setHttpClientConfigCallback(client -> client.setDefaultCredentialsProvider(credentialsProvider));
        RestHighLevelClient restHighLevelClient1 = new RestHighLevelClient(builder);
        for (int i = 0; i < 300; i++) {
            UpdateByQueryRequest request = new UpdateByQueryRequest("ccs_customs_inventory_index_*");
            request.setQuery(new TermQueryBuilder("sn.keyword", "CI2306161349081313"));
            String script = "ctx._source['cancelStatus']= params.status ;ctx._source['cancelCreateTime']= params.time";
            Map<String, Object> params = new HashMap<>();
            params.put("status", "AUDIT_PASS" + i);
            params.put("time", new Date().getTime() + i);
            request.setScript(new Script(ScriptType.INLINE, "painless", script, params));
            request.setRefresh(true);
            BulkByScrollResponse response = restHighLevelClient1.updateByQuery(request, RequestOptions.DEFAULT);
            long total = response.getTotal();
            log.info("updateCancelInfo sn={} total={}", i, total);
        }
    }

    /**
     * 不推荐使用 会造成编译script多次 达到上限造成熔断
     *
     * @throws IOException
     */
    @Test
    public void testt1111() throws IOException {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "Qw$12345678"));
        RestClientBuilder builder = RestClient.builder(HttpHost.create("http://es716.yang800.com.cn"))
                .setRequestConfigCallback(request -> request.setConnectTimeout(15 * 1000).setSocketTimeout(15 * 1000))
                .setHttpClientConfigCallback(client -> client.setDefaultCredentialsProvider(credentialsProvider));
        RestHighLevelClient restHighLevelClient1 = new RestHighLevelClient(builder);
        for (int i = 0; i < 300; i++) {
            UpdateByQueryRequest request = new UpdateByQueryRequest("ccs_customs_inventory_index_202306");
            request.setQuery(new TermQueryBuilder("sn.keyword", "CI2306161349081313"));
            String script = "ctx._source['cancelStatus']='AUDIT_PASS" + i + "' ;ctx._source['cancelCreateTime']='" + new Date().getTime() + i + "'";
            System.out.println(script);
            request.setScript(new Script(script));
            request.setRefresh(true);
            BulkByScrollResponse response = restHighLevelClient1.updateByQuery(request, RequestOptions.DEFAULT);
            long total = response.getTotal();
            log.info("updateCancelInfo sn={} total={}", i, total);
        }
    }

    /**
     * updateRequest测试
     *
     * @throws IOException
     */
    @Test
    public void testt2222() throws IOException {
        final CredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(AuthScope.ANY, new UsernamePasswordCredentials("elastic", "Qw$12345678"));
        RestClientBuilder builder = RestClient.builder(HttpHost.create("http://es716.yang800.com.cn"))
                .setRequestConfigCallback(request -> request.setConnectTimeout(15 * 1000).setSocketTimeout(15 * 1000))
                .setHttpClientConfigCallback(client -> client.setDefaultCredentialsProvider(credentialsProvider));
        RestHighLevelClient restHighLevelClient1 = new RestHighLevelClient(builder);
        UpdateRequest request = new UpdateRequest("ccs_customs_inventory_index_202306", "customsSingleInventory", "776697233109304273");
        request.retryOnConflict(5);
        // 设置要更新的字段值
        XContentBuilder contentBuilder = XContentFactory.jsonBuilder().startObject()
//                    .nullField("cancelStatus").nullField("cancelCreateTime")
                .field("cancelStatus", "AUDIT_PASS").field("cancelCreateTime", "1687330724000")
                .endObject();
        request.doc(contentBuilder);
        UpdateResponse response = restHighLevelClient1.update(request);
    }


    public void clearCancelInfo(CustomsInventoryCancelDTO customsInventoryCancelDTO) {
        String customsInventoryOrderSn = customsInventoryCancelDTO.getRefInvoSn();
        String id = customsInventoryCancelDTO.getRefInvoId().toString();
        log.info("clearCancelInfo customsInventoryOrderSn={} id={}", customsInventoryOrderSn, id);
        if (!cancelRefundSyncFlag) {
            log.info("clearCancelInfo 未开启同步");
            return;
        }
        String indexName = getIndexName(customsInventoryOrderSn);
        try {
            UpdateRequest request = new UpdateRequest(indexName, "customsSingleInventory", id);
            request.retryOnConflict(5);
            // 设置要更新的字段值
            XContentBuilder contentBuilder = XContentFactory.jsonBuilder().startObject()
                    .nullField("cancelStatus").nullField("cancelCreateTime")
                    .endObject();
            request.doc(contentBuilder);
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("clearCancelInfo error={}", e.getMessage(), e);
        }
    }

    public void updateRefundInfo(RefundOrderInfoDto refundOrderInfoDto) {
        String customsInventoryOrderSn = refundOrderInfoDto.getRefListBillSn();
        String refundStatus = refundOrderInfoDto.getRefundCheckStatus();
        Date refundCreateTime = refundOrderInfoDto.getCreateTime();
        String id = refundOrderInfoDto.getRefListBillId().toString();
        log.info("updateRefundInfo customsInventoryOrderSn={} refundStatus={} refundCreateTime={} invtId={}", customsInventoryOrderSn, refundStatus, JSON.toJSONString(refundCreateTime), id);
        if (!cancelRefundSyncFlag) {
            log.info("updateRefundInfo 未开启同步");
            return;
        }
        String indexName = getIndexName(customsInventoryOrderSn);
        try {
            UpdateRequest request = new UpdateRequest(indexName, "customsSingleInventory", id);
            request.retryOnConflict(5);
            // 设置要更新的字段值
            XContentBuilder contentBuilder = XContentFactory.jsonBuilder().startObject()
                    .field("refundStatus", refundStatus).field("refundCreateTime", refundCreateTime.getTime())
                    .endObject();
            request.doc(contentBuilder);
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("updateRefundInfo error={}", e.getMessage(), e);
        }
    }

    public void clearRefundInfo(RefundOrderInfoDto refundOrderInfoDto) {
        String customsInventoryOrderSn = refundOrderInfoDto.getRefListBillSn();
        String id = refundOrderInfoDto.getRefListBillId().toString();
        log.info("clearRefundInfo customsInventoryOrderSn={}", customsInventoryOrderSn);
        if (!cancelRefundSyncFlag) {
            log.info("clearRefundInfo 未开启同步");
            return;
        }
        String indexName = getIndexName(customsInventoryOrderSn);
        try {
            UpdateRequest request = new UpdateRequest(indexName, "customsSingleInventory", id);
            request.retryOnConflict(5);
            // 设置要更新的字段值
            XContentBuilder contentBuilder = XContentFactory.jsonBuilder().startObject()
                    .nullField("refundStatus").nullField("refundCreateTime")
                    .endObject();
            request.doc(contentBuilder);
            restHighLevelClient.update(request, RequestOptions.DEFAULT);
        } catch (IOException e) {
            log.error("clearRefundInfo error={}", e.getMessage(), e);
        }
    }

    /**
     * 查询商品是否在使用中
     *
     * @param unifiedProductIdList 统一料号
     * @param createTimeFrom       起始创建时间
     * @param createTimeTo         结束创建时间
     * @return 是否存在 true-存在， false-不存在
     */
    public boolean isExistProductIdInUse(List<String> unifiedProductIdList, Date createTimeFrom, Date createTimeTo) {
        if (CollUtil.isEmpty(unifiedProductIdList)) {
            return false;
        }
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("status", CustomsActionStatus.DEC_SUCCESS.getValue()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("afterSalesStatus", InventoryAfterStatus.NO.getValue()));
        boolQueryBuilder.filter(QueryBuilders.termQuery("exitRegionStatus", 0));
        boolQueryBuilder.filter(QueryBuilders.termsQuery("itemExtras.unifiedProductId.keyword", unifiedProductIdList));
        boolQueryBuilder.filter(QueryBuilders.rangeQuery("createTime").from(createTimeFrom.getTime()).to(createTimeTo.getTime()));

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withQuery(boolQueryBuilder)
                .withIndices(AutoOrderConfig.customsInventoryIndex + "_*")
                .build();
        log.info("findByUnifiedProductId - searchQuery={}", JSONUtils.toJSONString(searchQuery));
        long count = elasticsearchTemplate.count(searchQuery);
        return count > 0;
    }

    private static String getIndexName(String customsInventoryOrderSn) {
        String dateStr = customsInventoryOrderSn.substring(2, 6);
        Pattern pattern = Pattern.compile(MONTH_REGEX);
        Matcher matcher = pattern.matcher(dateStr);
        if (!matcher.find()) {
            throw new ArgsInvalidException("根据sn未获取到对应索引名");
        }
        return AutoOrderConfig.customsInventoryIndex + "_20" + dateStr;
    }

    public List<CustomsSingleInventoryEsDTO> findByLogistics(String logisticsNo, Long expressId) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("logisticsNo.keyword", logisticsNo));
        if (Objects.nonNull(expressId)) {
            boolQueryBuilder.filter(QueryBuilders.termQuery("expressId", expressId));
        }
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withCollapseField("id.keyword").build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        List<CustomsSingleInventoryEsDO> esDOList = elasticsearchTemplate
                .queryForList(searchQuery, CustomsSingleInventoryEsDO.class);
        return ConvertUtil.listConvert(esDOList, CustomsSingleInventoryEsDTO.class);
    }

    public List<CustomsSingleInventoryEsDTO> findByInventoryNo(String inventoryNo) {
        BoolQueryBuilder boolQueryBuilder = new BoolQueryBuilder();
        boolQueryBuilder.filter(QueryBuilders.termQuery("inventoryNo.keyword", inventoryNo));
        // 创建时间倒序排
        SearchQuery searchQuery = new NativeSearchQueryBuilder().withQuery(boolQueryBuilder)
                .withSort(SortBuilders.fieldSort("createTime").order(SortOrder.DESC))
                .withCollapseField("id.keyword").build();
        searchQuery.addIndices(AutoOrderConfig.customsInventoryIndex + "_*");
        List<CustomsSingleInventoryEsDO> esDOList = elasticsearchTemplate
                .queryForList(searchQuery, CustomsSingleInventoryEsDO.class);
        return ConvertUtil.listConvert(esDOList, CustomsSingleInventoryEsDTO.class);
    }
}
