package com.danding.cds.controller;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.ReconciliationOrderItemService;
import com.danding.cds.c.api.service.ReconciliationOrderService;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.v2.bean.vo.req.ReconciliationBalanceAccountReqVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationOrderImportVO;
import com.danding.cds.v2.bean.vo.req.ReconciliationOuterImportReqVO;
import com.danding.cds.v2.enums.ReconciliationImportTemplateEnums;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Date;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * menu 对账订单
 */
@RestController
@Slf4j
@RequestMapping("/reconciliation")
public class ReconciliationController {

    @Autowired
    private ReconciliationOrderItemService reconciliationOrderItemService;

    @Autowired
    private ReconciliationOrderService reconciliationOrderService;


    @RequestMapping("/item/outer/balanceAccount")
    public RpcResult<Boolean> balanceAccount(@RequestBody ReconciliationBalanceAccountReqVO reqVO) {
        try {
            reconciliationOrderItemService.balanceAccount(reqVO);
            return RpcResult.success();
        } catch (Exception e) {
            log.error("账单比对失败 - {}", e.getMessage(), e);
            return RpcResult.error("账单比对失败 - " + e.getMessage());
        }
    }

    @PostMapping("/order/outer/jd/import/file")
    public RpcResult<Boolean> jdImportFile(@RequestBody ReconciliationOuterImportReqVO reqVO) {
        log.info("ReconciliationController jdImportFile req={}", JSON.toJSONString(reqVO));
        try {
            ReconciliationOrderImportVO importVO = new ReconciliationOrderImportVO();
            importVO.setImportUrl(reqVO.getImportUrl());
            importVO.setTemplateType(ReconciliationImportTemplateEnums.JD_OUTBOUND_TEMPLATE.getCode());
            Date reconciliationDate = getReconciliationDate(reqVO.getImportUrl());
            importVO.setReconciliationTime(reconciliationDate.getTime());
            reconciliationOrderService.excelImport(importVO);
            return RpcResult.success();
        } catch (Exception e) {
            log.error("账单比对失败 - {}", e.getMessage(), e);
            return RpcResult.error("账单比对失败 - " + e.getMessage());
        }
    }

    private Date getReconciliationDate(String importUrl) {
        if (StrUtil.isEmpty(importUrl)) {
            return new Date();
        }
        String datePattern = "-(\\d{8})-";
        String fileName = importUrl.substring(importUrl.lastIndexOf("/") + 1, importUrl.lastIndexOf("."));
        Pattern pattern = Pattern.compile(datePattern);
        Matcher matcher = pattern.matcher(fileName);
        if (matcher.find()) {
            String reconciliationDateStr = matcher.group(1);
            log.info("jdImportFile 提取的日期: {}", reconciliationDateStr);
            return DateUtil.parse(reconciliationDateStr, "yyyyMMdd");
        } else {
            log.error("jdImportFile 未找到匹配的日期");
        }
        return new Date();
    }

    @Test
    public void test() {
        String fileName = "https://bgd-local.oss-cn-hangzhou.aliyuncs.com/jd-wms-export/客户订单复核结果查询-20241211-202412121506.csv";
        //截取文件名中的对账时间20241211
        Date reconciliationDate = getReconciliationDate(fileName);
        System.out.println(reconciliationDate);
    }
}
