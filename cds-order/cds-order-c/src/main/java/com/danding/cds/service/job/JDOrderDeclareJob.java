package com.danding.cds.service.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderDeclareDTO;
import com.danding.cds.order.api.dto.OrderDeclareV2DTO;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.service.DeclareRepushService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

@Slf4j
@Service
public class JDOrderDeclareJob extends IJobHandler {

    @Resource
    private OrderService orderService;
    @Autowired
    private DeclareRepushService declareRepushService;

    static final Integer CUSTOMS_INVENTORY_STATUS = -1;

    static final Integer DECLARING = 20;

    static final Integer CUSTOMS_SYSTEM_ERROR = 42;

    static final List<String> statusList = Arrays.asList(
            CustomsStat.CUSTOMS_PASS.getValue(),
            CustomsStat.CUSTOMS_REFUSE.getValue(),
            CustomsStat.CUSTOMS_PERSON.getValue());

    @Override
    @XxlJob(value = "OrderCJDOrderDeclareJob", enableTenant = true)
    public ReturnT<String> execute(String param) throws Exception {
        try {
            XxlJobLogger.log("参数设置 :" + param);
            OrderDeclareV2DTO orderDeclareDTO = JSON.parseObject(param, OrderDeclareV2DTO.class);
            log.info("[[JDOrderDeclareJob] 参数设置-{}]", orderDeclareDTO);
            List<OrderDTO> orderDTOListJD = getDTOListJD(orderDeclareDTO);
            declareRepushService.reDeclarePushAndNotify(orderDTOListJD, orderDeclareDTO, "【京东】");
        } catch (Exception e) {
            XxlJobLogger.log("京东重推失败 :" + e.getMessage());
        }
        return ReturnT.SUCCESS;
    }


    private List<OrderDTO> getDTOListJD(OrderDeclareDTO orderDeclareDTO) {
        List<Integer> stateList = new ArrayList() {{
            add(OrderStatus.DEC_WAIT.getValue());
            add(OrderStatus.DEC_ING.getValue());
        }};
        List<OrderDTO> orderDTOList = orderService.listOrder(65L, stateList, orderDeclareDTO.getPage(), orderDeclareDTO.getQueryDays());
        XxlJobLogger.log("[op:JDOrderDeclareJob] 京东清单待放行订单数={} ", orderDTOList.size());
        return orderDTOList;
    }

}
