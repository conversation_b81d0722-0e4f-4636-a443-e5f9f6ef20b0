package com.danding.cds.service.mq.consumer;

import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.PddWrapInventoryDto;
import com.danding.cds.bean.dto.TransferDto;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * @program: cds-center
 * @description: 消费者
 * @author: 潘本乐（Belep）
 * @create: 2021-10-15 17:27
 **/
@Slf4j
@Component
@AllArgsConstructor
@RocketMQMessageListener(topic = "ccs-pdd-order-c-declareTopic",
        consumerGroup = "ccs-cloud-order-c-declareConsumer")
public class PddDeclareConsumer extends MessageHandler {

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    public void handle(Object o) throws RuntimeException {
        try {
            // todo 根据类型区分反序列化，不同对象，调用不同的pdd业务处理
            String sn = o.toString();
            TransferDto dto =JSONUtil.toBean(sn, TransferDto.class);
            log.info("ccs-cloud-order-declareTopic 数据接收 -{}",dto);
            //处理清单
            if (Objects.equals(dto.getType(),"inventory")) {
                String data = dto.getData().toString();
                PddWrapInventoryDto info = JSON.parseObject(data, PddWrapInventoryDto.class);
                pddDeclareService.cloudInventoryDeclareAndLog(info.getWrapInventoryOrderInfo());
            }
            //处理订单
            if (Objects.equals(dto.getType(),"order")) {
                String data = dto.getData().toString();
                WrapOrderDeclareInfo info = JSONUtil.toBean(data, WrapOrderDeclareInfo.class);
                    pddDeclareService.cloudOrderDeclare(info);
                }
            //处理运单
            if (Objects.equals(dto.getType(),"logisticsDeclare")) {
                String data = dto.getData().toString();
                WrapShipmentInfo info = JSONUtil.toBean(data,WrapShipmentInfo.class);
                pddDeclareService.cloudLogisticsDeclare(info);
            }
            log.info("处理完成 -{}",dto);
        } catch (Exception e) {
            log.error("拼多多申报消费端-数据处理异常 -{}", e.getMessage(), e);
        }

    }






}
