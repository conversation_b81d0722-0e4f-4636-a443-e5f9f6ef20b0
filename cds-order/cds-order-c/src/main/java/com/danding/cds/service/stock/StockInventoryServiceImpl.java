package com.danding.cds.service.stock;

import java.util.List;

import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.StockInventoryService;
import com.danding.cds.inventory.api.dto.ItemLogsIdDTO;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.stock.StockContextUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Create 2021/7/20  10:07
 * @Describe
 **/
@Slf4j
@Service
public class StockInventoryServiceImpl implements StockInventoryService {
    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @Override
    public void updateInventory(UpdateInventoryDTO updateInventoryDTO) {
        log.info("StockInventoryServiceImpl-updateInventory updateInventoryDTO={}", JSON.toJSONString(updateInventoryDTO));
        StockContextUtil.setRpcContextOpsRec(updateInventoryDTO.getCustomsBookId(),updateInventoryDTO.getProductId(), updateInventoryDTO.getGoodsSeqNo());
        customsBookItemService.updateInventory(updateInventoryDTO);
    }

    @Override
    public List<ItemLogsIdDTO> updateInventory(List<UpdateInventoryDTO> list) {
        log.info("StockInventoryServiceImpl-updateInventory updateInventoryDTO={}", JSON.toJSONString(list));
        list.forEach(l -> StockContextUtil.setRpcContextOpsRec(l.getCustomsBookId(),l.getProductId(), l.getGoodsSeqNo()));
        return customsBookItemService.updateInventory(list);
    }
}
