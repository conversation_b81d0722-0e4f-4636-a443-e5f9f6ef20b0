package com.danding.cds.service.customs.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.service.*;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTOV2;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.order.api.dto.CalloffOrderItemDTO;
import com.danding.cds.order.api.dto.CustomsCancelOrderMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.bean.dao.OrderOwnerMappingDO;
import com.danding.cds.order.base.mapper.OrderOwnerMapper;
import com.danding.cds.ownerMapping.api.dto.OrderOwnerMappingTypeEnum;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Create 2021/7/5  10:30
 * @Describe
 **/
@Slf4j
@Service
public class OrderOwnerMappingServiceImpl implements OrderOwnerMappingService {
    @Autowired
    private OrderOwnerMapper orderOwnerMapper;
    @Resource
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @Resource
    private RefundOrderService refundOrderService;
    @Resource
    private OrderService orderService;

    @Override
    public void saveInventoryOwnerMapping(InventoryOrderInfoDTO infoDTO) {
        log.info("[op:InventoryOwnerMappingServiceImpl-saveInventoryOwnerMapping infoDTO={}]", JSONUtils.toJSONString(infoDTO));
        OrderOwnerMappingDO iomDO = new OrderOwnerMappingDO();
        iomDO.setOrderSn(infoDTO.getInveCustomsSn());
        iomDO.setOrderType(OrderOwnerMappingTypeEnum.INVENTORY_ORDER.getCode());
        iomDO.setEntityWarehouseCode(infoDTO.getEntityWarehouseCode());
        iomDO.setEntityWarehouseName(infoDTO.getEntityWarehouseName());
        iomDO.setOwnerCode(infoDTO.getOwnerCode());
        iomDO.setOwnerName(infoDTO.getOwnerName());
        UserUtils.setCreateAndUpdateBy(iomDO);
        orderOwnerMapper.insertSelective(iomDO);
        log.info("[op:InventoryOwnerMappingServiceImpl-saveInventoryOwnerMapping InventoryOwnerMappingDO={}]", JSONUtils.toJSONString(iomDO));
    }

    @Override
    public void saveOrderCalloffOwnerMapping(CustomsInventoryCalloffDTO infoDTO) {
        log.info("[op:InventoryOwnerMappingServiceImpl-saveOrderCalloffOwnerMapping infoDTO={}]", JSONUtils.toJSONString(infoDTO));
        OrderOwnerMappingDO iomDO = new OrderOwnerMappingDO();
        iomDO.setOrderSn(infoDTO.getDeclareOrderNo());
        iomDO.setOrderType(OrderOwnerMappingTypeEnum.INVENTORY_CALLOFF.getCode());
        iomDO.setEntityWarehouseCode(infoDTO.getEntityWarehouseCode());
        iomDO.setEntityWarehouseName(infoDTO.getEntityWarehouseName());
        iomDO.setOwnerCode(infoDTO.getOwnerCode());
        iomDO.setOwnerName(infoDTO.getOwnerName());
        if (infoDTO.getId() != null) {
            Example example = new Example(OrderOwnerMappingDO.class);
            example.createCriteria().andEqualTo("orderSn", infoDTO.getDeclareOrderNo()).andEqualTo("orderType", OrderOwnerMappingTypeEnum.INVENTORY_CALLOFF.getCode());
            UserUtils.setUpdateBy(iomDO);
            iomDO.setUpdateTime(new Date());
            orderOwnerMapper.updateByExampleSelective(iomDO, example);
        } else {
            UserUtils.setCreateAndUpdateBy(iomDO);
            orderOwnerMapper.insertSelective(iomDO);
        }
        log.info("[op:InventoryOwnerMappingServiceImpl-saveOrderCalloffOwnerMapping InventoryOwnerMappingDO={}]", JSONUtils.toJSONString(iomDO));
    }


    @Override
    public List<InventoryOrderInfoDTO> getWareHouseAndOwnerInventoryOrderInfo(List<InventoryOrderInfoDTO> orderInfoDTOS) {
        if (CollectionUtils.isEmpty(orderInfoDTOS)) {
            return orderInfoDTOS;
        }
        List<String> snList = orderInfoDTOS.stream().map(InventoryOrderInfoDTO::getInveCustomsSn).collect(Collectors.toList());
        log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwnerInventoryOrderInfo InveCustomsSnList={}]", JSONUtils.toJSONString(snList));
        Example example = new Example(OrderOwnerMappingDO.class);
        example.createCriteria().andIn("orderSn", snList).andEqualTo("orderType", OrderOwnerMappingTypeEnum.INVENTORY_ORDER.getCode());
        List<OrderOwnerMappingDO> doList = orderOwnerMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(doList)) {
            Map<String, OrderOwnerMappingDO> snOwnerMap = doList.stream().collect(Collectors.toMap(OrderOwnerMappingDO::getOrderSn, i -> i, (v1, v2) -> v1));
            orderInfoDTOS.forEach(o -> {
                if (snOwnerMap.containsKey(o.getInveCustomsSn())) {
                    OrderOwnerMappingDO mappingDO = snOwnerMap.get(o.getInveCustomsSn());
                    o.setEntityWarehouseName(mappingDO.getEntityWarehouseName());
                    o.setOwnerName(mappingDO.getOwnerName());
                    log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwnerInventoryOrderInfo InventoryOwnerMappingDO={}]", JSONUtils.toJSONString(o));
                }
            });
        }
        return orderInfoDTOS;
    }

    @Override
    public List<CustomsInventoryCalloffDTOV2> getWareHouseAndOwnerInventoryCallOff(List<CustomsInventoryCalloffDTOV2> orderInfoDTOS) {
        if (CollectionUtils.isEmpty(orderInfoDTOS)) {
            return orderInfoDTOS;
        }
        List<String> snList = orderInfoDTOS.stream().map(CustomsInventoryCalloffDTOV2::getDeclareOrderNo).collect(Collectors.toList());
        log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwnerInventoryCallOff InveCustomsSnList={}]", JSONUtils.toJSONString(snList));
        Example example = new Example(OrderOwnerMappingDO.class);
        example.createCriteria().andIn("orderSn", snList).andEqualTo("orderType", OrderOwnerMappingTypeEnum.INVENTORY_CALLOFF.getCode());
        List<OrderOwnerMappingDO> doList = orderOwnerMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(doList)) {
            Map<String, OrderOwnerMappingDO> snOwnerMap = doList.stream().collect(Collectors.toMap(OrderOwnerMappingDO::getOrderSn, i -> i, (v1, v2) -> v1));
            orderInfoDTOS.forEach(o -> {
                if (snOwnerMap.containsKey(o.getDeclareOrderNo())) {
                    OrderOwnerMappingDO mappingDO = snOwnerMap.get(o.getDeclareOrderNo());
                    o.setEntityWarehouseName(mappingDO.getEntityWarehouseName());
                    o.setOwnerName(mappingDO.getOwnerName());
                    log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwnerInventoryCallOff  InventoryOwnerMappingDO={}]", JSONUtils.toJSONString(o));
                }
            });
        }
        return orderInfoDTOS;
    }

    @Override
    public InventoryOrderInfoDTO getWareHouseAndOwner(InventoryOrderInfoDTO orderInfoDTO) {
        log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwner InventoryOrderInfoDTO={}]", JSONUtils.toJSONString(orderInfoDTO));
        Example example = new Example(OrderOwnerMappingDO.class);
        example.createCriteria().andEqualTo("orderSn", orderInfoDTO.getInveCustomsSn()).andEqualTo("orderType", OrderOwnerMappingTypeEnum.INVENTORY_ORDER.getCode());
        List<OrderOwnerMappingDO> doList = orderOwnerMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(doList)) {
            OrderOwnerMappingDO ownerMappingDO = doList.get(0);
            orderInfoDTO.setEntityWarehouseCode(ownerMappingDO.getEntityWarehouseCode());
            orderInfoDTO.setEntityWarehouseName(ownerMappingDO.getEntityWarehouseName());
            orderInfoDTO.setOwnerCode(ownerMappingDO.getOwnerCode());
            orderInfoDTO.setOwnerName(ownerMappingDO.getOwnerName());
        }
        log.info("[op:InventoryOwnerMappingServiceImpl-getWareHouseAndOwner InventoryOrderInfoDTO={}]", JSONUtils.toJSONString(orderInfoDTO));
        return orderInfoDTO;
    }

    /**
     * 根据实体仓编码货主查询取消单
     *
     * @param ownerCode
     * @param entityWarehouseCode
     * @return
     */
    private List<OrderOwnerMappingDO> getCalloffByOwnerCodeAndEntityWarehouseCode(String ownerCode, String entityWarehouseCode) {
        log.info("[op:InventoryOwnerMappingServiceImpl-getCalloff 货主编码={} , 实体仓编码={}]", ownerCode, entityWarehouseCode);
        Example example = new Example(OrderOwnerMappingDO.class);
        example.createCriteria().andEqualTo("ownerCode", ownerCode).andEqualTo("entityWarehouseCode", entityWarehouseCode);
        return orderOwnerMapper.selectByExample(example);
    }

    /**
     * 获取退货 ccs_refund_order_info
     *
     * @param ownerCode
     * @param timeBegin
     * @param timeEnd
     * @return
     */
    @Override
    public List<CustomsCancelOrderMappingDTO> getRefundOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd) throws Exception {
        log.info("[op:InventoryOwnerMappingServiceImpl-getRefundOrderByOwnerCode ownerCode={} entityWarehouseCode={} timeBegin={} timeEnd={}]", ownerCode, entityWarehouseCode, timeBegin, timeEnd);
        List<String> cancelTypeList = new ArrayList<>();
        cancelTypeList.add(InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
        List<CustomsInventoryCalloffDTO> calloffDTOS = customsInventoryCalloffService.getCallOffByDeclareNo(ownerCode, entityWarehouseCode, cancelTypeList, timeBegin, timeEnd);
        if (CollectionUtils.isEmpty(calloffDTOS)) {
            log.info("InventoryOwnerMappingServiceImpl getRefundOrderByOwnerCode calloffDTOS为空");
            return null;
        } else {
            log.info("InventoryOwnerMappingServiceImpl getRefundOrderByOwnerCode calloffDTOS={}", JSON.toJSONString(calloffDTOS));
        }
        List<OrderOwnerMappingDO> orderOwnerMappingDO = getCalloffByOwnerCodeAndEntityWarehouseCode(ownerCode, entityWarehouseCode);
        if (CollectionUtils.isEmpty(orderOwnerMappingDO)) {
            log.info("InventoryOwnerMappingServiceImpl getRefundOrderByOwnerCode 货主不存在 - {}", ownerCode);
            throw new ArgsInvalidException("货主不存在 ownerCode=" + ownerCode + " entityWarehouseCode=" + entityWarehouseCode);
        }

        List<CustomsCancelOrderMappingDTO> collect = buildCalloff(calloffDTOS, orderOwnerMappingDO);
        log.info("[op:InventoryOwnerMappingServiceImpl-getRefundOrderByOwnerCode collect={}]", JSON.toJSONString(collect));
        return collect;
    }


    @Autowired
    private CustomsInventoryService customsInventoryService;

    /**
     * 撤单表为 ccs_customs_inventory_cancel_info
     *
     * @param ownerCode
     * @param timeBegin
     * @param timeEnd
     * @return
     */
    @Override
    public List<CustomsCancelOrderMappingDTO> getCallBackOrderByOwnerCode(String ownerCode, String entityWarehouseCode, Long timeBegin, Long timeEnd) throws Exception {
        log.info("[op:InventoryOwnerMappingServiceImpl-getCallBackOrderByOwnerCode ownerCode={} entityWarehouseCode={} timeBegin={} timeEnd={}]", ownerCode, entityWarehouseCode, timeBegin, timeEnd);
        List<String> cancelTypeList = new ArrayList<>();
//        cancelTypeList.add(InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode());
        cancelTypeList.add(InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode());
//        cancelTypeList.add(InventoryCalloffTypeEnum.CALLOFF_PRE.getCode());
        List<CustomsInventoryCalloffDTO> calloffDTOS = customsInventoryCalloffService.getCallOffByDeclareNo(ownerCode, entityWarehouseCode, cancelTypeList, timeBegin, timeEnd);
        if (CollectionUtils.isEmpty(calloffDTOS)) {
            log.info("InventoryOwnerMappingServiceImpl getCallBackOrderByOwnerCode calloffDTOS为空");
            return null;
        } else {
            log.info("InventoryOwnerMappingServiceImpl getCallBackOrderByOwnerCode calloffDTOS={}", JSON.toJSONString(calloffDTOS));
        }
        List<OrderOwnerMappingDO> orderOwnerMappingDO = getCalloffByOwnerCodeAndEntityWarehouseCode(ownerCode, entityWarehouseCode);
        if (CollectionUtils.isEmpty(orderOwnerMappingDO)) {
            log.info("InventoryOwnerMappingServiceImpl getCallBackOrderByOwnerCode 货主不存在 - {}", ownerCode);
            throw new ArgsInvalidException("货主不存在 ownerCode=" + ownerCode + " entityWarehouseCode=" + entityWarehouseCode);
        }
        //获取取消单数据
        List<CustomsCancelOrderMappingDTO> collect = buildCalloff(calloffDTOS, orderOwnerMappingDO);
        log.info("[op:InventoryOwnerMappingServiceImpl-getCallBackOrderByOwnerCode collect={}]", JSON.toJSONString(collect));
        return collect;
    }


    private List<CustomsCancelOrderMappingDTO> buildCalloff(List<CustomsInventoryCalloffDTO> calloffDTOS, List<OrderOwnerMappingDO> orderOwnerMappingDO) throws ArgsInvalidException {
        List<CustomsCancelOrderMappingDTO> collect = new ArrayList<>();
        for (CustomsInventoryCalloffDTO calloffDTO : calloffDTOS) {
            CustomsCancelOrderMappingDTO dto = new CustomsCancelOrderMappingDTO();
            dto.setType(calloffDTO.getCalloffType());
            dto.setDeclareOrderNo(calloffDTO.getDeclareOrderNo());
            dto.setLogisticsNo(calloffDTO.getLogisticsNo());
            dto.setCreateTime(calloffDTO.getCreateTime());
            dto.setCalloffTime(calloffDTO.getCalloffTime());
            if (InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode().equalsIgnoreCase(calloffDTO.getCalloffType())) {
                RefundOrderInfoDto refundOrderInfoDto = refundOrderService.findByMailNo(calloffDTO.getRefundLogisticsNo());
                if (Objects.nonNull(refundOrderInfoDto)) {
                    dto.setCalloffTime(refundOrderInfoDto.getDeclareSuccessTime());
                }
            }
            dto.setEntityWarehouseCode(orderOwnerMappingDO.get(0).getEntityWarehouseCode());
            dto.setEntityWarehouseName(orderOwnerMappingDO.get(0).getEntityWarehouseName());
            dto.setOwnerCode(orderOwnerMappingDO.get(0).getOwnerCode());
            dto.setOwnerName(orderOwnerMappingDO.get(0).getOwnerName());
            OrderDTO orderDTO = orderService.findBySnSection(calloffDTO.getOrderSn());
            if (Objects.isNull(orderDTO)) {
                throw new ArgsInvalidException("申报单号:" + calloffDTO.getDeclareOrderNo() + "对应申报单为空;");
            }
//            OrderDTO orderDTO = orderService.findByDeclareOrderNoFull(r.getDeclareOrderNo());
            dto.setOutOrderNo(orderDTO.getSystemGlobalSn());
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(orderDTO.getCustomsInventorySn());
            if (Objects.isNull(customsInventoryDTO)) {
                throw new ArgsInvalidException("申报单号:" + calloffDTO.getDeclareOrderNo() + "对应清单为空");
            }
//            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByDeclareNo90Days(r.getDeclareOrderNo());
            List<CalloffOrderItemDTO> CalloffOrderItemDTO = new ArrayList<>();
            if (Objects.nonNull(customsInventoryDTO)) {
                dto.setInventoryNo(customsInventoryDTO.getInventoryNo());
                List<CustomsInventoryItemDTO> itemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                if (!CollectionUtils.isEmpty(itemDTOS)) {
                    CalloffOrderItemDTO = itemDTOS.stream().map(i -> {
                        CalloffOrderItemDTO res = new CalloffOrderItemDTO();
                        res.setQty(i.getCount());
                        res.setGoodName(i.getItemName());
                        res.setSku(i.getItemNo());
                        return res;
                    }).collect(Collectors.toList());
                }
            }
            dto.setCalloffOrderItemDTOList(CalloffOrderItemDTO);
            collect.add(dto);
        }
        return collect;
    }
}
