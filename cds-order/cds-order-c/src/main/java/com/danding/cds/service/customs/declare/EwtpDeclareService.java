package com.danding.cds.service.customs.declare;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.bean.dto.JieztechRequestDto;
import com.danding.cds.bean.vo.JieztechReqVo;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.declare.base.component.service.DeclareAndLogService;
import com.danding.cds.declare.inner.component.utils.JieztechDeclareService;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.clear.base.result.InventoryCancelResult;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.clear.base.result.InventoryRefundResult;
import com.danding.cds.declare.sdk.clear.base.result.OrderDeclareResult;
import com.danding.cds.declare.sdk.enums.BusinessTypeEnums;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.github.kevinsawicki.http.HttpRequest;
import com.gvt.apollo.ApolloSdk;
import com.gvt.apollo.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Objects;

/**
 * @program: cds-center
 * @description: EWTP海关订单申报实现
 * @author: 潘本乐（Belep）
 * @create: 2022-04-19 14:44
 **/
@Service
@Slf4j
@RefreshScope
public class EwtpDeclareService {


    @Value("${jieztech.ewtp.order.declare.adress:}")
    private String jieztechEwtpOrderAdress;
    @Value("${jieztech.ewtp.inventory.declare.adress:}")
    private String jieztechEwtpInventoryAdress;

    @Value("${jieztech.ewtp.inventory.cancel.declare.adress:}")
    private String jieztechEwtpInventoryCancelAddress;
    //清单退货url
    @Value("${jieztech.ewtp.inventory.refund.declare.adress:}")
    private String jieztechEwtpInventoryRefundAddress;

    @Autowired
    private JieztechDeclareService jieztechDeclareService;
    @Resource
    private CustomsOrderService customsOrderService;
    @Resource
    private CustomsInventoryService inventoryService;
    @Autowired
    private DeclareAndLogService declareAndLogService;

    @Value("${jieztech.ewtp.host:}")
    private String jieztechHost;

    @Value("${jieztech.ewtp.appId:}")
    private String jieztechAppId;

    @Value("${jieztech.ewtp.privateKey:}")
    private String jieztechPrivateKey;

    @Value("${jieztech.ewtp.subMerchantId:}")
    private String jieztechSubMerchantId;

    @Value("${jieztech.ewtp.subMerchantName:}")
    private String jieztechSubMerchantName;

    /**
     * EWTP清单申报
     *
     * @param info  申报信息
     * @param dxpId dxpIc
     * @return
     */
    public InventoryDeclareResult ewtpInventoryDeclare(WrapInventoryOrderInfo info, String dxpId) {

        log.info("开始处理，清单申报，EWTP申报单: {} ，发往麦德龙DXP报文封装", info.getDeclareNos());
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(info.getDeclareNos());
        result.setSn(info.getDeclareNos());
        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(info, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            ewtpDto.setUrl(jieztechEwtpInventoryAdress);
            String response = wrapSignAndRequest(ewtpDto, info);
            if (response != null) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    result.setStatus(Integer.valueOf(CustomsStat.CUSTOMS_PASS.getValue()));
                    log.info("申报单： {} 芥州EWTP清单申报推送成功 = {}", info.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-清单申报调用返回异常: 申报单号: {} , Rsponse:{}", info, response);
            result.setStatus(Integer.valueOf(CustomsStat.DEAL_EXCEPTION.getValue()));
            String message = "EWTP-清单申报调用返回异常：" + response;
            inventoryService.updateByCustomsActive(info.getCustomsInventoryDto().getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), info.getCustomsInventoryDto().getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-清单申报调用异常: 申报单号: {} ,message={}", info.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }

    /**
     * EWTP取消清单申报
     *
     * @param info  申报信息
     * @param dxpId dxpIc
     * @return
     */
    public InventoryCancelResult ewtpInventoryCancelDeclare(WarpCancelOrderInfo info, String dxpId) {

        log.info("开始处理，取消清单申报，EWTP申报单: {} ", info.getDeclareNos());
        InventoryCancelResult result = new InventoryCancelResult();
        result.setOrderNo(info.getDeclareNos());
        result.setSn(info.getDeclareNos());
        info.setBusinessType(BusinessTypeEnums.JIE_ZHOU.getCode());
        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(info, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            //撤销申报地址
            ewtpDto.setUrl(jieztechEwtpInventoryCancelAddress);
            String response = wrapSignAndRequest(ewtpDto, info);
            if (!StringUtils.isEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    log.info("取消清单申报单： {} 芥州EWTP取消清单申报推送成功 = {}", info.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-取消清单申报调用返回异常: 申报单号: {} , Rsponse:{}", info, response);
            String message = "EWTP-取消清单申报调用返回异常：" + response;
            inventoryService.updateByCustomsActive(info.getCustomsInventoryDto().getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), info.getCustomsInventoryDto().getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-取消清单申报调用异常: 申报单号: {} ,message={}", info.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }



    /**
     * EWTP清单退货申报
     *
     * @param info  清单退货信息
     * @param dxpId dxpIc
     * @return
     */
    public InventoryRefundResult ewtpInventoryRefundDeclare(WarpRefundOrderInfo info, String dxpId) {

        log.info("开始处理，清单退货申报，EWTP申报单: {} ", info.getDeclareNos());
        InventoryRefundResult result = new InventoryRefundResult();
        result.setOrderNo(info.getDeclareNos());
        result.setSn(info.getDeclareNos());
        info.setBusinessType(BusinessTypeEnums.JIE_ZHOU.getCode());
        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(info, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            //清单退货地址
            ewtpDto.setUrl(jieztechEwtpInventoryRefundAddress);
            String response = wrapSignAndRequest(ewtpDto, info);
            if (!StringUtils.isEmpty(response)) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    log.info("清单退货申报单： {} 芥州EWTP清单退货申报推送成功 = {}", info.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-清单退货申报调用返回异常: 申报单号: {} , Rsponse:{}", info, response);
            String message = "EWTP-清单退货申报调用返回异常：" + response;
            inventoryService.updateByCustomsActive(info.getCustomsInventoryDto().getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), info.getCustomsInventoryDto().getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-清单退货申报调用异常: 申报单号: {} ,message={}", info.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }

    public InventoryDeclareResult ewtpInventoryDeclareTest(WrapInventoryOrderInfo info, String dxpId) {

        log.info("开始处理，清单申报，EWTP申报单: {} ，发往麦德龙DXP报文封装", info.getDeclareNos());
        InventoryDeclareResult result = new InventoryDeclareResult();
        result.setOrderNo(info.getDeclareNos());
        result.setSn(info.getDeclareNos());
        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(info, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            ewtpDto.setUrl("");
            String response = wrapSignAndRequestTest(ewtpDto, info);
            if (response != null) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    result.setStatus(Integer.valueOf(CustomsStat.CUSTOMS_PASS.getValue()));
                    log.info("申报单： {} 芥州EWTP清单申报推送成功 = {}", info.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-清单申报调用返回异常: 申报单号: {} , Rsponse:{}", info, response);
            result.setStatus(Integer.valueOf(CustomsStat.DEAL_EXCEPTION.getValue()));
            String message = "EWTP-清单申报调用返回异常：" + response;
            inventoryService.updateByCustomsActive(info.getCustomsInventoryDto().getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), info.getCustomsInventoryDto().getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-清单申报调用异常: 申报单号: {} ,message={}", info.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }

    /**
     * EWTP订单申报
     *
     * @param declareInfo 申报信息
     * @param dxpId       dxpIc
     * @return
     */
    public OrderDeclareResult ewtpOrderDeclare(WrapOrderDeclareInfo declareInfo, String dxpId) {

        log.info("开始处理，订单申报，芥州EWTP申报单：{}，发往麦德龙DXP报文封装", declareInfo.getDeclareOrderNo());
        OrderDeclareResult result = new OrderDeclareResult();
        result.setOrderNo(declareInfo.getDeclareOrderNo());
        result.setSn(declareInfo.getSn());
        result.setStatus(declareInfo.getStatus());

        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(declareInfo, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            ewtpDto.setUrl(jieztechEwtpOrderAdress);
            String response = wrapSignAndRequest(ewtpDto, declareInfo);
            if (response != null) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    result.setStatus(Integer.valueOf(CustomsStat.CUSTOMS_PASS.getValue()));
                    log.info("申报单： {} 芥州EWTP订单申报推送成功 = {}", declareInfo.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-订单申报调用返回异常: 申报单号: {} , Rsponse:{}", declareInfo, response);
            result.setStatus(Integer.valueOf(CustomsStat.DEAL_EXCEPTION.getValue()));
            String message = "EWTP-订单申报调用返回异常：" + response;
            CustomsOrderDTO orderDTO = customsOrderService.findByDeclareNo(declareInfo.getDeclareNos());
            customsOrderService.updateByCustomsActive(orderDTO.getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), orderDTO.getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-订单申报调用异常: 申报单号: {} ,message={}", declareInfo.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }

    public OrderDeclareResult ewtpOrderDeclareTest(WrapOrderDeclareInfo declareInfo, String dxpId) {

        log.info("开始处理，订单申报，芥州EWTP申报单：{}，发往麦德龙DXP报文封装", declareInfo.getDeclareOrderNo());
        OrderDeclareResult result = new OrderDeclareResult();
        result.setOrderNo(declareInfo.getDeclareOrderNo());
        result.setSn(declareInfo.getSn());
        result.setStatus(declareInfo.getStatus());

        //封装DXP报文
        CustomsMsgDto customsMsgDto = CebMessageUtil.buildEwtpCustomsMessage(declareInfo, dxpId);
        try {
            JieztechRequestDto ewtpDto = buildMessage(customsMsgDto);
            ewtpDto.setUrl(jieztechEwtpOrderAdress);
            String response = wrapSignAndRequest(ewtpDto, declareInfo);
            if (response != null) {
                JSONObject jsonObject = JSON.parseObject(response);
                if (Objects.equals("true", jsonObject.getString("success"))) {
                    result.setStatus(Integer.valueOf(CustomsStat.CUSTOMS_PASS.getValue()));
                    log.info("申报单： {} 芥州EWTP订单申报推送成功 = {}", declareInfo.getDeclareNos(), response);
                    return result;
                }
            }
            log.error("EWTP-订单申报调用返回异常: 申报单号: {} , Rsponse:{}", declareInfo, response);
            result.setStatus(Integer.valueOf(CustomsStat.DEAL_EXCEPTION.getValue()));
            String message = "EWTP-订单申报调用返回异常：" + response;
            CustomsOrderDTO orderDTO = customsOrderService.findByDeclareNo(declareInfo.getDeclareNos());
            customsOrderService.updateByCustomsActive(orderDTO.getId(), CustomsStat.DEAL_EXCEPTION.getValue(), message, new Date(), orderDTO.getCreateTime());
            return result;
        } catch (Exception e) {
            log.error("EWTP-订单申报调用异常: 申报单号: {} ,message={}", declareInfo.getDeclareNos(), e.getMessage(), e);
        }
        return result;
    }

    private JieztechRequestDto buildMessage(CustomsMsgDto customsMsgDto) {
        JieztechRequestDto ewtpDto = new JieztechRequestDto();
        ewtpDto.setContent(customsMsgDto.getDxpMsg());
        ewtpDto.setSubMerchantId(jieztechSubMerchantId);
        ewtpDto.setSubMerchantName(jieztechSubMerchantName);
        ewtpDto.setAppid(jieztechAppId);
        ewtpDto.setPrivateKey(jieztechPrivateKey);
        ewtpDto.setHost(jieztechHost);
        return ewtpDto;
    }

    public String wrapSignAndRequest(JieztechRequestDto ewtpDto, WrapBeanInfo beanInfo) throws Exception {
        log.info("JieztechRequestUtil warpSignAndRequest req={}", JSON.toJSONString(ewtpDto));
        JieztechReqVo jieztechReqVo = new JieztechReqVo();
        jieztechReqVo.setAppid(ewtpDto.getAppid());
        jieztechReqVo.setSubMerchantId(ewtpDto.getSubMerchantId());
        jieztechReqVo.setSubMerchantName(ewtpDto.getSubMerchantName());
        jieztechReqVo.setEntryType(ewtpDto.getEntryType());
        jieztechReqVo.setPort(ewtpDto.getPort());
        jieztechReqVo.setContent(ewtpDto.getContent());
        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(ewtpDto.getPrivateKey()), JSON.toJSONString(jieztechReqVo));
        log.info("JieztechRequestUtil warpSignAndRequest requestJson={} url={}", requestJson, ewtpDto.getHost() + ewtpDto.getUrl());
        HttpRequest httpRequest = declareAndLogService.sendEwtpRequestAndLog(beanInfo, requestJson, ewtpDto.getHost() + ewtpDto.getUrl());
        String body = httpRequest.body();
        log.info("JieztechRequestUtil warpSignAndRequest body={}", body);
        if (httpRequest.ok()) {
            return body;
        } else {
            log.error("JieztechRequestUtil warpSignAndRequest 请求失败，返回信息:{}", body);
            return null;
        }
    }

    public String wrapSignAndRequestTest(JieztechRequestDto ewtpDto, WrapBeanInfo beanInfo) throws Exception {
        log.info("JieztechRequestUtil warpSignAndRequest req={}", JSON.toJSONString(ewtpDto));
        JieztechReqVo jieztechReqVo = new JieztechReqVo();
        jieztechReqVo.setAppid(ewtpDto.getAppid());
        jieztechReqVo.setSubMerchantId(ewtpDto.getSubMerchantId());
        jieztechReqVo.setSubMerchantName(ewtpDto.getSubMerchantName());
        jieztechReqVo.setEntryType(ewtpDto.getEntryType());
        jieztechReqVo.setPort(ewtpDto.getPort());
        jieztechReqVo.setContent(ewtpDto.getContent());
        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(ewtpDto.getPrivateKey()), JSON.toJSONString(jieztechReqVo));
        log.info("JieztechRequestUtil warpSignAndRequest requestJson={} url={}", requestJson, ewtpDto.getHost() + ewtpDto.getUrl());
        declareAndLogService.sendEwtpRequestAndLogTest(beanInfo, requestJson, "https://test.com");
        return null;
    }

}
