package com.danding.cds.service.declareImpl.inventory;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.danding.cds.bean.vo.PddOfficialDeclareReqVo;
import com.danding.cds.declare.base.component.inventory.InventoryDeclareAbstract;
import com.danding.cds.declare.sdk.clear.base.result.InventoryDeclareResult;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.service.customs.declare.PddDeclareService;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 拼多多CEB代理清单申报
 * @author: 潘本乐（Belep）
 * @create: 2022-02-16 10:33
 **/
@Service("PDD_CEB_PROXY_INVENTORY_DECLARE")
@Slf4j
public class PddCebProxyInventoryDeclareImpl extends InventoryDeclareAbstract {

    @Autowired
    private PddDeclareService pddDeclareService;

    @Override
    protected InventoryDeclareResult mockDeclareTest(WrapInventoryOrderInfo info) {
        log.info("拼多多CEB清单代理申报，测试环境MOCK");
        pddDeclareService.cloudOfficialDeclareTest(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB621);
        return null;
    }

    @Override
    protected InventoryDeclareResult declare(WrapInventoryOrderInfo info) {

        pddDeclareService.cloudOfficialDeclare(info, PddOfficialDeclareReqVo.ClearanceMessageType.CEB621);
        return null;
    }
}
