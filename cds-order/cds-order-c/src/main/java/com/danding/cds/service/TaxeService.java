package com.danding.cds.service;

import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryExtra;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountSubmit;
import com.danding.cds.taxes.api.enums.EnableStatus;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;

/**
 * @program: cds-center
 * @description: 税金service
 * @author: 潘本乐（Belep）
 * @create: 2021-10-19 21:12
 **/
@Service
@Slf4j
public class TaxeService {

    @Resource(name = "orderLogThreadExecutor")
    private ThreadPoolTaskExecutor orderLogThreadExecutor;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    public void asyncTaxesTenantSave(CustomsInventoryExtra extra, Long assureCompanyId) {
        TaxesTenantAccountSubmit submit = new TaxesTenantAccountSubmit();
        submit.setEnable(EnableStatus.CAN.getValue());
        submit.setName(extra.getTenantName());
        submit.setTenantId(extra.getTenantOuterId());
        submit.setConsumedAmount(new BigDecimal("0"));
        submit.setRechargeTotal(new BigDecimal("0"));
        submit.setAvailable(new BigDecimal("0"));
        submit.setUserId(1L);
        submit.setCompanyId(assureCompanyId);
        orderLogThreadExecutor.submit(new TaxeTenaTask(submit));
    }

    class TaxeTenaTask extends TraceDataRunnable {

        private TaxesTenantAccountSubmit submit;

        public TaxeTenaTask(TaxesTenantAccountSubmit submit) {
            super();
            this.submit = submit;
        }

        @Override
        public void proxy() {
            try {
                taxesTenantAccountService.save(submit);
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                ;
            }
        }
    }
}
