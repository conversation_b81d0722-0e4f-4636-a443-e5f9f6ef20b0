package com.danding.cds.service.mq.producer;

import cn.hutool.json.JSONUtil;
import com.danding.cds.bean.dto.PddWrapInventoryDto;
import com.danding.cds.bean.dto.TransferDto;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.logistics.mq.common.handler.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @program: cds-center
 * @description: 拼多多申报生产
 * @author: 潘本乐（Belep）
 * @create: 2021-10-15 17:09
 **/
@Service
public class PddDeclareProducer {

    @Autowired
    private MessageSender messageSender;


    /**
     * 发送清单信息
     *
     * @param info
     * @param logDTO
     */
    public void inventoryOrderSend(WrapInventoryOrderInfo info) {
        PddWrapInventoryDto pddWrapInventoryDto = new PddWrapInventoryDto();
        pddWrapInventoryDto.setWrapInventoryOrderInfo(info);
        //封装对象
        TransferDto dto = new TransferDto();
        dto.setType("inventory");
        dto.setData(JSONUtil.toJsonStr(pddWrapInventoryDto));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), "ccs-pdd-order-c-declareTopic");

    }


    /**
     * 发送订单信息
     * @param wrapOrderDto
     */
    public void orderSend(WrapOrderDeclareInfo wrapOrderDto) {
        TransferDto dto = new TransferDto();
        dto.setType("order");
        dto.setData(JSONUtil.toJsonStr(wrapOrderDto));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), "ccs-pdd-order-c-declareTopic");
    }

    /**
     * 发送运单信息
     * @param wrapShipmentInfo
     */
    public void logisticsDeclareOrderSend(WrapShipmentInfo wrapShipmentInfo) {
        TransferDto dto = new TransferDto();
        dto.setType("logisticsDeclare");
        dto.setData(JSONUtil.toJsonStr(wrapShipmentInfo));
        messageSender.sendMsg(JSONUtil.toJsonStr(dto), "ccs-pdd-order-c-declareTopic");
    }
}
