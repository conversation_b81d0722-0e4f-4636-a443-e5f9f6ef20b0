package com.danding.cds.handler;

import com.danding.cds.bean.vo.ByteDanceDeclareReqVo;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.declare.sdk.bean.dto.CustomsMsgDto;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.order.WrapOrderDeclareInfo;
import com.danding.cds.declare.sdk.model.refund.WarpRefundOrderInfo;
import com.danding.cds.declare.sdk.model.shipment.WrapShipmentInfo;
import com.danding.cds.declare.sdk.utils.CebMessageUtil;
import com.danding.cds.track.log.annotations.TrackLog;
import com.danding.cds.track.log.bean.TrackLogBaseInfo;
import com.danding.cds.track.log.bean.TrackLogInfoDto;
import com.danding.cds.track.log.interfaces.TrackLogParametersHandler;
import com.danding.cds.track.log.utils.TrackLogUtils;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: pdd 官方代报轨迹日志记录 handler
 * @date 2022/6/24 14:02
 */
@Component
public class ByteDanceDeclareTrackLogParametersHandler implements TrackLogParametersHandler {

    @Override
    public TrackLogInfoDto handle(TrackLog trackLog, Object[] args) throws Exception {
        TrackLogInfoDto trackLogInfoDto = new TrackLogInfoDto();
        int infoIndex = trackLog.infoIndex();
        if (Objects.equals(infoIndex, -1)) {
            throw new Exception("未获取到申报信息下标");
        }
        Object info = args[infoIndex];
        ByteDanceDeclareReqVo declareReqVo = (ByteDanceDeclareReqVo) info;
        WrapBeanInfo beanInfo = (WrapBeanInfo) declareReqVo.getInfo();
        if (Objects.isNull(beanInfo)) {
            throw new Exception("beanInfo 为空");
        }
        trackLogInfoDto.setOrderId(beanInfo.getMainOrderId());
        trackLogInfoDto.setOrderSn(beanInfo.getMainOrderSn());
        trackLogInfoDto.setDeclareOrderNo(beanInfo.getDeclareNos());
        trackLogInfoDto.setInternalStatus(OrderInternalEnum.DECLARING.getCode());
        trackLogInfoDto.setReceiver(TrackLogConstantMixAll.BYTE_DANCE_CLOUD);
        trackLogInfoDto.setSender(TrackLogConstantMixAll.DT_CCS);

        CustomsMsgDto customsMsgDto = CebMessageUtil.buildCustomsMsgNoSign(beanInfo, beanInfo.getDxpId());
        if (customsMsgDto != null) {
            trackLogInfoDto.setRequestMessage(customsMsgDto.getCebMsg());
        }

        Class<?> infoClz = beanInfo.getClass();
        if (Objects.equals(infoClz, WrapOrderDeclareInfo.class)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.BYTE_DANCE_CLOUD_ORDER_DECLARE);
        } else if (Objects.equals(infoClz, WrapShipmentInfo.class)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.BYTE_DANCE_CLOUD_LOGISTICS_DECLARE);
        } else if (Objects.equals(infoClz, WrapInventoryOrderInfo.class)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.BYTE_DANCE_CLOUD_INVENTORY_DECLARE);
        } else if (Objects.equals(infoClz, WarpCancelOrderInfo.class)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.BYTE_DANCE_CLOUD_CANCEL_DECLARE);
        } else if (Objects.equals(infoClz, WarpRefundOrderInfo.class)) {
            trackLogInfoDto.setEventDesc(TrackLogConstantMixAll.BYTE_DANCE_CLOUD_REFUND_DECLARE);
        }
        trackLogInfoDto.setEventTime(new Date());
        trackLogInfoDto.setOperator(UserUtils.getUserRealName());

        return trackLogInfoDto;
    }

    @Override
    public TrackLogInfoDto afterProcessHandle(TrackLog trackLog, TrackLogInfoDto trackLogInfoDto, Object proceed) throws Exception {
        TrackLogBaseInfo trackLogBaseInfo = TrackLogUtils.getBaseInfoAndRemove();
        String statusDesc = trackLogBaseInfo.getStatusDesc();
        trackLogInfoDto.setResult(statusDesc);
        return trackLogInfoDto;
    }
}
