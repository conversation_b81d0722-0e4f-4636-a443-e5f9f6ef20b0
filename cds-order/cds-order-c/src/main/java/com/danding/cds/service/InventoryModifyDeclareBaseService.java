package com.danding.cds.service;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.inventory.api.enums.ModifyDeclareStat;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.base.bean.dao.InventoryModifyDeclareDO;
import com.danding.cds.bean.dto.InventoryModifyDeclareDto;
import com.danding.cds.order.base.mapper.InventoryModifyDeclareMapper;
import com.danding.cds.v2.bean.vo.req.ModifyDeclareReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;

import lombok.extern.slf4j.Slf4j;
import tk.mybatis.mapper.entity.Example;

/**
 * @program: cds-center
 * @description: InventoryModifyDeclare 基础服务
 * @author: 潘本乐（Belep）
 * @create: 2021-08-16 13:45
 **/
@Slf4j
@Service
public class InventoryModifyDeclareBaseService {

    @Autowired
    private InventoryModifyDeclareMapper inventoryModifyDeclareMapper;

    /**
     * 插入数据
     *
     * @param record
     */
    public void insertSelective(InventoryModifyDeclareDO record) {
        UserUtils.setCreateAndUpdateBy(record);
        inventoryModifyDeclareMapper.insertSelective(record);
    }

    /**
     * 通过运单号获取实体
     *
     * @param logisticsNo 运单编号
     * @return
     */
    public InventoryModifyDeclareDto selectByLogisticsNo(String logisticsNo) {
        Example example = new Example(InventoryModifyDeclareDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("logisticsNo", logisticsNo);
        criteria.andEqualTo("deleted", 0);
        InventoryModifyDeclareDO modifyDeclareDO = inventoryModifyDeclareMapper.selectOneByExample(example);
        if (modifyDeclareDO == null) {
            return null;
        }
        InventoryModifyDeclareDto modifyDeclareDto = new InventoryModifyDeclareDto();
        BeanUtils.copyProperties(modifyDeclareDO, modifyDeclareDto);
        return modifyDeclareDto;
    }

    public void batchDelete(List<Long> ids) {
        Example example = new Example(InventoryModifyDeclareDO.class);
        example.createCriteria().andIn("id", ids);
        inventoryModifyDeclareMapper.deleteByExample(example);
    }

    /**
     * 变更申报查询
     *
     * @param declareReqVo
     * @return
     */
    @PageSelect
    public ListVO<InventoryModifyDeclareDto> paging(ModifyDeclareReqVo declareReqVo) {

        Example example = new Example(InventoryModifyDeclareDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(declareReqVo.getStatus())) {
            criteria.andEqualTo("status", declareReqVo.getStatus());
        }
        if (Objects.nonNull(declareReqVo.getAgentCompanyId())) {
            criteria.andEqualTo("agentCompanyId", declareReqVo.getAgentCompanyId());
        }
        if (Objects.nonNull(declareReqVo.getCreateStartTime())) {
            criteria.andGreaterThanOrEqualTo("createTime", new Date(declareReqVo.getCreateStartTime()));
        }
        if (Objects.nonNull(declareReqVo.getCreateEndTime())) {
            criteria.andLessThanOrEqualTo("createTime", new Date(declareReqVo.getCreateEndTime()));
        }
        if (Objects.nonNull(declareReqVo.getDeclareStartTime())) {
            criteria.andGreaterThanOrEqualTo("lastDeclareTime", new Date(declareReqVo.getDeclareStartTime()));
        }
        if (Objects.nonNull(declareReqVo.getDeclareEndTime())) {
            criteria.andLessThanOrEqualTo("lastDeclareTime", new Date(declareReqVo.getDeclareEndTime()));
        }
        if (Objects.nonNull(declareReqVo.getCustomsStartTime())) {
            criteria.andGreaterThanOrEqualTo("lastCustomsTime", new Date(declareReqVo.getCustomsStartTime()));
        }
        if (Objects.nonNull(declareReqVo.getCustomsEndTime())) {
            criteria.andLessThanOrEqualTo("lastCustomsTime", new Date(declareReqVo.getCustomsEndTime()));
        }

        if (Objects.equals(declareReqVo.getType(), "inventoryNo")) {
            criteria.andIn("inventoryNo", Splitter.on(",").splitToList(declareReqVo.getSn()));
        }
        if (Objects.equals(declareReqVo.getType(), "logisticsNo")) {
            criteria.andIn("logisticsNo", Splitter.on(",").splitToList(declareReqVo.getSn()));
        }
        if (Objects.equals(declareReqVo.getType(), "declareNo")) {
            criteria.andIn("declareNo", Splitter.on(",").splitToList(declareReqVo.getSn()));
        }
        List<InventoryModifyDeclareDO> list = inventoryModifyDeclareMapper.selectByExample(example);
        ListVO<InventoryModifyDeclareDto> result = new ListVO<>();
        List<InventoryModifyDeclareDto> dataList = list.stream()
                .map(z -> {
                    InventoryModifyDeclareDto modifyDeclareDto = new InventoryModifyDeclareDto();
                    BeanUtils.copyProperties(z, modifyDeclareDto);
                    return modifyDeclareDto;
                }).collect(Collectors.toList());
        result.setDataList(dataList);
        // 分页
        PageInfo<InventoryModifyDeclareDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    public void batchCancel(List<Long> ids) {
        Example example = new Example(InventoryModifyDeclareDO.class);
        example.createCriteria().andIn("id", ids);
        // TODO: 2021/8/17 枚举类还没写
        InventoryModifyDeclareDO inventoryModifyDeclareDO = new InventoryModifyDeclareDO();
        inventoryModifyDeclareDO.setStatus(ModifyDeclareStat.CANCEL.getValue());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(inventoryModifyDeclareDO);
        }
        inventoryModifyDeclareDO.setUpdateTime(new Date());
        inventoryModifyDeclareMapper.updateByExampleSelective(inventoryModifyDeclareDO, example);
    }

    public List<InventoryModifyDeclareDto> getListByIds(List<Long> ids) {
        Example example = new Example(InventoryModifyDeclareDO.class);
        example.createCriteria().andIn("id", ids);
        List<InventoryModifyDeclareDO> inventoryModifyDeclareDOList = inventoryModifyDeclareMapper.selectByExample(example);
        List<InventoryModifyDeclareDto> inventoryModifyDeclareDtos = inventoryModifyDeclareDOList.stream().map(i -> {
            InventoryModifyDeclareDto dto = new InventoryModifyDeclareDto();
            BeanUtils.copyProperties(i, dto);
            return dto;
        }).collect(Collectors.toList());
        return inventoryModifyDeclareDtos;

    }

    public InventoryModifyDeclareDto getOrderByInvtNo(String invtNo) {
        if (StringUtils.isEmpty(invtNo)) {
            return null;
        }
        Example example = new Example(InventoryModifyDeclareDO.class);
        example.createCriteria().andEqualTo("inventoryNo", invtNo);
        InventoryModifyDeclareDO inventoryModifyDeclareDO = inventoryModifyDeclareMapper.selectOneByExample(example);
        InventoryModifyDeclareDto dto = new InventoryModifyDeclareDto();
        if (inventoryModifyDeclareDO != null) {
            BeanUtils.copyProperties(inventoryModifyDeclareDO, dto);
            return dto;
        }
        return null;
    }

    /**
     * 更新海关回执状态
     *
     * @param id
     * @param receive
     */
    public void updateOrderCustomsStatus(Long id, CustomsReceive receive) {
        InventoryModifyDeclareDO inventoryModifyDeclareDO = new InventoryModifyDeclareDO();
        inventoryModifyDeclareDO.setId(id);
        inventoryModifyDeclareDO.setCustomsStatus(receive.getCustomsStatus());
        inventoryModifyDeclareDO.setCustomsDetail(receive.getCustomsDetail());
        inventoryModifyDeclareDO.setLastCustomsTime(new Date(receive.getCustomsTime()));
        inventoryModifyDeclareDO.setUpdateTime(new Date());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(inventoryModifyDeclareDO);
        }
        log.info("InventoryModifyDeclareBaseService updateOrderCustomsStatus={}", JSON.toJSONString(inventoryModifyDeclareDO));
        inventoryModifyDeclareMapper.updateByPrimaryKeySelective(inventoryModifyDeclareDO);
    }

    public void updateOrderStatus(Long id, Integer value) {
        this.updateOrderStatus(id, value, null);
    }

    public void updateOrderStatus(Long id, Integer value, Date lastDeclareTime) {
        InventoryModifyDeclareDO inventoryModifyDeclareDO = new InventoryModifyDeclareDO();
        inventoryModifyDeclareDO.setId(id);
        inventoryModifyDeclareDO.setStatus(value);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            inventoryModifyDeclareDO.setUpdateBy(UserUtils.getUserId());
        }
        inventoryModifyDeclareDO.setUpdateTime(new Date());
        inventoryModifyDeclareDO.setLastDeclareTime(lastDeclareTime);
        log.info("InventoryModifyDeclareBaseService updateOrderStatus={}", JSON.toJSONString(inventoryModifyDeclareDO));
        inventoryModifyDeclareMapper.updateByPrimaryKeySelective(inventoryModifyDeclareDO);
    }

    private static final List<Integer> DECLARE_LIST = Arrays.asList(ModifyDeclareStat.WAITING, ModifyDeclareStat.PENDING, ModifyDeclareStat.ZS_REJECT, ModifyDeclareStat.FAIL).stream().map(ModifyDeclareStat::getValue).collect(Collectors.toList());
    private static final List<Integer> DELETE_LIST = Arrays.asList(ModifyDeclareStat.WAITING, ModifyDeclareStat.ZS_REJECT, ModifyDeclareStat.FAIL, ModifyDeclareStat.CANCEL).stream().map(ModifyDeclareStat::getValue).collect(Collectors.toList());
    private static final List<Integer> CANCEL_LIST = Arrays.asList(ModifyDeclareStat.WAITING, ModifyDeclareStat.ZS_REJECT, ModifyDeclareStat.FAIL).stream().map(ModifyDeclareStat::getValue).collect(Collectors.toList());

    public void judgeStatus(List<Long> ids, String status) throws ArgsErrorException {
        Example example = new Example(InventoryModifyDeclareDO.class);
        example.createCriteria().andIn("id", ids);
        List<InventoryModifyDeclareDO> inventoryModifyDeclareDOList = inventoryModifyDeclareMapper.selectByExample(example);
        switch (status) {
            case "DECLARE":
                if (inventoryModifyDeclareDOList.stream().anyMatch(i -> !DECLARE_LIST.contains(i.getStatus()))) {
                    throw new ArgsErrorException("变更申报状态，非待申报、总署驳回、申报失败状态不允许申报");
                }
                break;
            case "DELETE":
                if (inventoryModifyDeclareDOList.stream().anyMatch(i -> !DELETE_LIST.contains(i.getStatus()))) {
                    throw new ArgsErrorException("变更申报状态，非待申报、总署驳回、申报失败、取消变更状态不允许删除");
                }
                break;
            case "CANCEL":
                if (inventoryModifyDeclareDOList.stream().anyMatch(i -> !CANCEL_LIST.contains(i.getStatus()))) {
                    throw new ArgsErrorException("变更申报状态，非待申报、总署驳回、申报失败状态不允许取消");
                }
                break;
            default:
                break;
        }
    }
}
