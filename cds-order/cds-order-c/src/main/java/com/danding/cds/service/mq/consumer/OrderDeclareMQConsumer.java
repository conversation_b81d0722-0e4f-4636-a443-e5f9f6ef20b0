package com.danding.cds.service.mq.consumer;

import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-order-declare-scan-c-consumer",
        topic = "ccs-order-declare-scan-c-topic",
        consumeThreadMax = 128
)
@RestController
public class OrderDeclareMQConsumer extends MessageHandler {

    @Autowired
    private DeclareOrderConsumerService orderConsumerService;

    @Override
    public void handle(Object o) throws RuntimeException {
        orderConsumerService.declareConsumerhandle(o);
    }

    @GetMapping("/mq/OrderDeclareMQConsumer")
    public String consumer(String orderSn) {
        this.handle(orderSn);
        return "SUCCESS";
    }
}
