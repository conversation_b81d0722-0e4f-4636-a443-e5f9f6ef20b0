package com.danding.cds.service.customs.declare;

import javax.annotation.Resource;

import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.c.api.service.CustomsLogisticsService;
import com.danding.cds.c.api.service.CustomsOrderService;
import com.danding.cds.c.api.service.CustomsPaymentService;
import com.danding.cds.c.api.service.OrderService;
import com.danding.cds.callback.api.service.CallbackRecordService;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.util.RouteActionUtil;
import com.danding.cds.handler.CustomsMainHandler;
import com.danding.cds.utils.flow.OrderAction;
import com.danding.cds.utils.flow.OrderFlowBook;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 申报单单据申报命令
 */
@Component
public class CustomsDeclareOrderCommand implements CustomsDeclareCommand<OrderDTO> {

    @Autowired
    private CustomsMainHandler customsMainHandler;

    @Resource
    private OrderService orderService;

    @Resource
    private CustomsInventoryService customsInventoryService;

    @Resource
    private CustomsPaymentService customsPaymentService;

    @Resource
    private CustomsOrderService customsOrderService;

    @DubboReference
    private CallbackRecordService callbackRecordService;

    @Resource
    private CustomsLogisticsService customsLogisticsService;

    @Override
    public void execute(OrderDTO input, DeclareEnum declareEnum) {

        Boolean handel = customsMainHandler.handleRequest(input, declareEnum);
        if (!handel) {
            extracted(input);
        }
    }


    @Override
    public void execute(OrderDTO input) {
        // Step::执行申报
        Boolean handel = customsMainHandler.handleRequest(input);
        if (!handel) {
            extracted(input);
        }
    }

    public DeclareEnum getDeclareEnumPresent(OrderDTO request) {
        return customsMainHandler.getDeclareEnumPresent(request);
    }

    /**
     * 抽取一下公共代码
     *
     * @param input
     */
    public void extracted(OrderDTO input) {
        String actionJson = input.getActionJson();
        // 正在申报中||申报完成 因为申报完成要所有申报项都完成才完成，申报失败是任何一单据失败就失败，所以成功在这里做解耦，失败不做，跟着单据更新
        if (RouteActionUtil.containPayment(actionJson)) {
            CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(input.getId(), input.getCustomsPaymentSn());
            if (customsPaymentDTO != null && !customsPaymentDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                return;
            }
        }
        if (RouteActionUtil.containCustomsOrder(actionJson)) {
            CustomsOrderDTO customsOrderDTO = customsOrderService.findByOrder(input.getId(), input.getCustomsOrderSn());
            if (customsOrderDTO != null && !customsOrderDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                return;
            }
        }
        if (RouteActionUtil.containShipment(actionJson)) {
            CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByOrder(input.getId(), input.getCustomsLogisticsSn());
            if (customsLogisticsDTO != null && !customsLogisticsDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                return;
            }
        }
        if (RouteActionUtil.containInventory(actionJson)) {
            CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(input.getId(), input.getCustomsInventorySn());
            if (inventoryDTO != null && !inventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                return;
            }
        }
        if (OrderFlowBook.order.operationAllowed(input.getStatus(), OrderAction.PASS.getOperation())) {
//                orderService.clearExceptionSection(input.getId(), input.getCreateTime());
            orderService.updateByFinishClearException(input.getId(), OrderFlowBook.order.target(input.getStatus(), OrderAction.PASS.getOperation()), input.getCreateTime());
        }
    }
}
