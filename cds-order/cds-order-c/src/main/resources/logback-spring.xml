<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="false" scanPeriod="60 seconds">
    <property name="log.level" value="info"/>
    <springProperty scope="context" name="app.name" source="spring.application.name"/>
    <springProperty scope="context" name="active" source="spring.profiles.active"/>
    <springProperty scope="context" name="pod.name" source="POD_NAME"/>
    <springProperty scope="context" name="tcp.enable" source="logback.tcp.enable" defaultValue="false"/>
    <springProperty scope="context" name="logstash.url" source="logstash.url" defaultValue=""/>

    <!-- 彩色日志依赖的渲染类 -->
    <conversionRule conversionWord="clr" converterClass="org.springframework.boot.logging.logback.ColorConverter"/>
    <conversionRule conversionWord="wex"
                    converterClass="org.springframework.boot.logging.logback.WhitespaceThrowableProxyConverter"/>
    <conversionRule conversionWord="wEx"
                    converterClass="org.springframework.boot.logging.logback.ExtendedWhitespaceThrowableProxyConverter"/>
    <!-- 彩色日志格式 -->
    <property name="CONSOLE_LOG_PATTERN"
              value="${CONSOLE_LOG_PATTERN:-%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
            <charset>utf8</charset>
        </encoder>
    </appender>

    <if condition='property("active").contains("test") || property("active").contains("pre") || property("active").contains("prod")'>
        <then>
            <!-- 按照每天生成日志文件 -->
            <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <!--                prudent：如果是 true，日志会被安全的写入文件，即使其他的FileAppender也在向此文件做写入操作，效率低，默认是 false。
                prudent：当为true时，不支持FixedWindowRollingPolicy。支持TimeBasedRollingPolicy，但是有两个限制，1 不支持也不允许文件压缩，2 不能设置file属性，必须留空。
                                    <FileNamePattern>/nfs/${app.name}/${app.name}-%d{yyyy-MM-dd}.log</FileNamePattern>
                -->
                <!--                <prudent>true</prudent>-->
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <!--日志文件输出的文件名-->
                    <FileNamePattern>/nfs/${app.name}/%d{yyyy-MM-dd}/${pod.name}.log.gz</FileNamePattern>
                    <!--日志文件保留天数-->
                    <MaxHistory>30</MaxHistory>
                </rollingPolicy>
                <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
                    <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
                    <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
                    <charset>UTF-8</charset> <!-- 此处设置字符集 -->
                </encoder>
                <!--日志文件最大的大小-->
                <!--                <triggeringPolicy class="ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy">-->
                <!--                    <MaxFileSize>100MB</MaxFileSize>-->
                <!--                </triggeringPolicy>-->
            </appender>
        </then>
    </if>


    <if condition='property("tcp.enable").contains("true")'>
        <then>
            <appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
                <destination>${logstash.url}</destination>
                <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder"/>
            </appender>
        </then>
    </if>

    <logger name="com.alibaba.nacos" level="error"/>
    <logger name="org.apache.dubbo" level="error"/>
    <logger name="org.redisson" level="error"/>
    <logger name="com.danding" level="debug"/>
    <logger name="io.netty" level="error"/>
    <logger name="com.ctrip.framework" level="error"/>
    <logger name="com.xxl.job" level="warn"/>
    <logger name="com.danding.park.client" level="error"/>
    <logger name="org.apache.rocketmq.spring" level="warn"/>


    <!-- Root Logger -->
    <root level="${log.level}">
        <appender-ref ref="console"/>
        <if condition='property("tcp.enable").contains("true")'>
            <then>
                <appender-ref ref="logstash"/>
            </then>
        </if>
        <if condition='property("active").contains("test") || property("active").contains("pre") || property("active").contains("prod")'>
            <then>
                <appender-ref ref="FILE"/>
            </then>
        </if>
    </root>

</configuration>
