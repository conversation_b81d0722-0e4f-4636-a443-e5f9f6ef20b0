<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-order</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cds-order-c</artifactId>


    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-base-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-base-data-service</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-order-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-c-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-taxes-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-canal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-es-component</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>tms-rpc-waybill</artifactId>
            <version>2.1.5-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>oms-order-rpc-client</artifactId>
        </dependency>

        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>sharding-core-common</artifactId>
        </dependency>

        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-rpc-client</artifactId>
            <version>3.2.6-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo-spring-boot-starter</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba.cloud</groupId>
                    <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-xxl-saas-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>mybatis-tenant-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>danding-sharding-core-route</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>dubbo-seata-component</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-rocketmq-business</artifactId>
        </dependency>
        <dependency>
            <artifactId>saas-tenant-support</artifactId>
            <groupId>com.danding</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.9.0</version>
        </dependency>

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>


        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>5.9</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.commons/commons-lang3 -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.13.0</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-trade-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-jcq-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>

    </dependencies>
    <build>
        <finalName>ccs-order-c</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>
                                repackage
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>