package com.danding.cds.order.base.bean.dao.exportorder;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "`ccs_export_item`")
@Getter
@Setter
public class ExportItemDO extends BaseDO {
    /**
     * 出区单ID
     */
    @Column(name = "export_order_id")
    private Long exportOrderId;

    /**
     * 核注清单Id
     */
    @Column(name = "endorsement_order_id")
    private Long endorsementOrderId;

    /**
     * 放行清单编号
     */
    @Column(name = "customs_inventory_sn")
    private String customsInventorySn;

    /**
     * 快递方式ID
     */
    @Column(name = "express_id")
    private Long expressId;

    /**
     * 快递编号
     */
    @Column(name = "mail_no")
    private String mailNo;

    /**
     * 托盘号
     */
    @Column(name = "tray_no")
    private String trayNo;

    /**
     * 操作员工号
     */
    @Column(name = "operator_no")
    private String operatorNo;

    /**
     * 工作台编号
     */
    @Column(name = "station_no")
    private String stationNo;

    /**
     * 包裹号
     */
    @Column(name = "biz_id")
    private String bizId;

    /**
     * SKU种类信息
     */
    @Column(name = "sku_json")
    private String skuJson;

    /**
     * 毛重（公斤）
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 净重（公斤）
     */
    @Column(name = "net_weight")
    private BigDecimal netWeight;

    /**
     * 账册id
     */
    private Long accountBookId;
}