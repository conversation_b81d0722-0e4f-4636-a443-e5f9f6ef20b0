package com.danding.cds.order.base.service;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.QuarterUtil;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.base.bean.dao.OrderDO;
import com.danding.cds.order.base.mapper.OrderMapper;
import com.danding.cds.order.base.mq.OrderEsDumpProducer;
import com.danding.cds.order.base.util.ShardingBaseExampleBuilder;
import com.danding.logistics.api.common.page.TimeRangeParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/7/6 20:12
 */
@Slf4j
@Service
public class OrderBaseService {
    @Autowired
    private OrderMapper orderMapper;
    @Autowired
    private OrderEsDumpProducer orderEsDumpProducer;
    private static final String TABLE_NAME = "ccs_order";

    public void updateOrderInternalStatusBySnList(List<String> snList, String internalStatus) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        // 同一季度放在一起
        Map<String, List<String>> quarterMap = QuarterUtil.splitByQuarter(snList);
        quarterMap.forEach((k, v) -> {
            // 按量切割
            try {
                // 限制下批量数据
                int index = 500;
                int totalSize = v.size();
                log.info("OrderBaseService 更新order内部流转状态，限制批量处理数据步长：{}，总数量：{}", index, totalSize);
                if (index >= totalSize) {
                    this.updateOrderInternalStatusBySnList(v, internalStatus, k);
                } else {
                    int times = totalSize % index > 0 ? totalSize / index + 1 : totalSize / index;
                    for (int i = 1; i <= times; i++) {
                        int fromIndex = (i - 1) * index;
                        int toIndex = index * i;
                        if (i == times) {
                            toIndex = totalSize;
                        }
                        List<String> logisticsNoList = v.subList(fromIndex, toIndex);
                        this.updateOrderInternalStatusBySnList(logisticsNoList, internalStatus, k);
                    }
                }
            } catch (Exception e) {
                log.error("OrderBaseService 更新order内部流转状态 error={}", e.getMessage(), e);
            }
        });
        orderEsDumpProducer.send(snList);
    }

    public void updateOrderInternalStatusBySnList(List<String> snList, String internalStatus, String quarter) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        orderMapper.updateInternalStatusBySnList(snList, internalStatus, TABLE_NAME + "_" + quarter);
        orderEsDumpProducer.send(snList);
    }

    public void updateOrderInternalStatusByQuarter(Long orderId, String internalStatus, String quarter) {
        orderMapper.updateInternalStatusById(orderId, internalStatus, TABLE_NAME + "_" + quarter);
    }

    public void updateOrderInternalStatus(OrderDTO orderDTO, String internalStatus) {
        if (Objects.isNull(orderDTO) || Objects.isNull(internalStatus)) {
            return;
        }
        if (Objects.isNull(orderDTO.getId())) {
            log.warn("updateOrderInternalStatus orderId为空");
            return;
        }
        if (Objects.equals(orderDTO.getInternalStatus(), internalStatus)) {
            log.warn("updateOrderInternalStatus 状态一致 不更新 orderId={} internalStatus={}", orderDTO.getId(), internalStatus);
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(orderDTO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(orderDTO.getCreateTime()));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", orderDTO.getId());
        example.and(criteria);
        OrderDO updateDO = new OrderDO();
        updateDO.setInternalStatus(internalStatus);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(updateDO);
        }
        updateDO.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(updateDO, example);
        orderEsDumpProducer.send(orderDTO.getSn());
    }


    public void updateOrderDeclareRecord(OrderDTO orderDTO) {
        if (Objects.isNull(orderDTO)) {
            return;
        }
        if (Objects.isNull(orderDTO.getId())) {
            log.warn("updateOrderInternalStatus orderId为空");
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(orderDTO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(orderDTO.getCreateTime()));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", orderDTO.getId());
        example.and(criteria);
        OrderDO updateDO = new OrderDO();
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(updateDO);
        }
        if (Objects.nonNull(orderDTO.getDeclareWayRecord())) {
            updateDO.setDeclareWayRecord(orderDTO.getDeclareWayRecord());
        }
        updateDO.setUpdateTime(new Date());
        orderMapper.updateByExampleSelective(updateDO, example);
    }

    public void updateOrderInternalStatusAndDeclareRecord(OrderDTO orderDTO, String internalStatus) {
        if (Objects.isNull(orderDTO) || Objects.isNull(internalStatus)) {
            return;
        }
        if (Objects.isNull(orderDTO.getId())) {
            log.warn("updateOrderInternalStatus orderId为空");
            return;
        }
        if (Objects.equals(orderDTO.getInternalStatus(), internalStatus) && Objects.isNull(orderDTO.getDeclareWayRecord())) {
            log.warn("updateOrderInternalStatus 状态一致且申报记录不变 不更新 orderId={} internalStatus={}", orderDTO.getId(), internalStatus);
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(orderDTO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(orderDTO.getCreateTime()));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", orderDTO.getId());
        example.and(criteria);
        OrderDO updateDO = new OrderDO();
        updateDO.setInternalStatus(internalStatus);
        if (Objects.nonNull(orderDTO.getDeclareWayRecord())) {
            updateDO.setDeclareWayRecord(orderDTO.getDeclareWayRecord());
        }
        orderMapper.updateByExampleSelective(updateDO, example);
        orderEsDumpProducer.send(orderDTO.getSn());
    }

    public void updateGlobalSnAndStatus(OrderDO orderDO, String newGsNo, Integer status) {
        if (Objects.isNull(orderDO) || Objects.isNull(newGsNo)) {
            return;
        }
        if (Objects.isNull(orderDO.getId())) {
            log.warn("updateGlobalSn orderId为空");
            return;
        }
        TimeRangeParam timeRangeParam = new TimeRangeParam();
        timeRangeParam.setBeginDate(ShardingBaseExampleBuilder.quarterStart(orderDO.getCreateTime()));
        timeRangeParam.setEndDate(ShardingBaseExampleBuilder.quarterEnd(orderDO.getCreateTime()));
        // Step::根据时间区间检索更新
        Example example = ShardingBaseExampleBuilder.getExample(OrderDO.class, timeRangeParam);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", orderDO.getId());
        example.and(criteria);
        OrderDO updateDO = new OrderDO();
        updateDO.setStatus(status);
        updateDO.setSystemGlobalSn(newGsNo);
        orderMapper.updateByExampleSelective(updateDO, example);
        orderEsDumpProducer.send(orderDO.getSn());
    }
}
