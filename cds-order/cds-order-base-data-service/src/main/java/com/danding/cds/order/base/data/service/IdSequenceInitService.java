package com.danding.cds.order.base.data.service;

import com.danding.cds.common.constants.OrderCons;
import com.danding.cds.order.base.data.util.IdSnCacheUtil;
import com.danding.cds.sequence.api.service.SequenceService;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * @program: cds-center
 * @description: id, sn初始化
 * @author: 潘本乐（Belep）
 * @create: 2021-10-20 14:41
 **/
@Service
@Slf4j
public class IdSequenceInitService {

    private static ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque(), new ThreadFactoryBuilder().setNameFormat("idSequenceInitCache%d").build());

    @DubboReference
    private SequenceService sequenceService;

    // 初始化数量
    private Long initNum = 20000L;


    public void init() {
        initOrderSn();
        initExternalOrderSn();
        initCustomsOrderSn();
        initCustomsLogisticsSn();
        initCustomsInventorySn();
        initCustomsPaymentSn();
    }

    /**
     * 异步执行缓存序列号
     *
     * @param type
     */
    public void asyncCacheSn(String type) {
        log.info("异步缓存序列号sn任务-开始-type:{}", type);
        taskExecutor.submit(new IdSequenceInitTask(type));
    }


    /**
     * 初始化-申报单SN
     *
     * @return sn
     */
    public synchronized void initOrderSn() {

        if (IdSnCacheUtil.orderSnOk()) {
            log.info("申报单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("申报单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepOrder(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setOrderSnIndex(++index);
        }
        log.info("申报单SN - 缓存数据 - 结束,最小: {}, 最大: {}", min, index);
    }

    public synchronized void initExternalOrderSn() {

        if (IdSnCacheUtil.externalOrderSnOk()) {
            log.info("外部申报单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("外部申报单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepExternalOrder(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setExternalOrderSnIndex(++index);
        }
        log.info("外部申报单SN - 缓存数据 - 结束,最小: {}, 最大: {}", min, index);
    }

    /**
     * 初始化-海关订单SN
     *
     * @return sn
     */
    public synchronized void initCustomsOrderSn() {

        if (IdSnCacheUtil.customsOrderSnOk()) {
            log.info("海关订单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("海关订单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepCustomsOrder(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setCustomsOrderSnIndex(++index);
        }
        log.info("海关订单SN - 缓存数据 - 结束,最小: {}, 最大: {}", min, index);
    }

    /**
     * 初始化-海关运单SN
     *
     * @return sn
     */
    public synchronized void initCustomsLogisticsSn() {
        if (IdSnCacheUtil.customsLogisticSnOk()) {
            log.info("海关运单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("海关运单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepCustomsLogistics(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setCustomsLogisticsSnIndex(++index);
        }
        log.info("海关运单SN - 缓存数据 - 结束,最小: {}, 最大: {}", min, index);
    }

    /**
     * 初始化-海关清单SN
     *
     * @return sn
     */

    public synchronized void initCustomsInventorySn() {

        if (IdSnCacheUtil.customsInventorySnOk()) {
            log.info("海关清单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("海关清单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepCustomsInventory(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setCustomsInventorySnIndex(++index);
        }
        log.info("海关清单SN - 缓存数据 - 结束,最小: {}, 最大: {}", min, index);
    }

    /**
     * 初始化-海关支付单SN
     *
     * @return sn
     */
    public synchronized void initCustomsPaymentSn() {
        if (IdSnCacheUtil.customsPaymentSnOk()) {
            log.info("海关支付单SN - 缓存数据充足，暂时不用缓存");
            return;
        }
        log.info("海关支付单SN - 缓存数据 - 开始");
        Long min = sequenceService.batchStepCustomsPayment(initNum);
        Long index = min;
        for (int i = 1; i < initNum; i++) {
            IdSnCacheUtil.setCustomsPaymentSnIndex(++index);
        }
        log.info("海关支付单SN - 缓存数据 - 结束，最小：{}，最大：{}", min, index);
    }

    class IdSequenceInitTask implements Runnable {

        private String type;

        public IdSequenceInitTask(String type) {
            this.type = type;
        }

        @Override
        public void run() {

            switch (type) {
                case OrderCons.ORDER:
                    initOrderSn();
                case OrderCons.CUSTOMS_ORDER:
                    initCustomsOrderSn();
                case OrderCons.CUSTOMS_LOGICTICS:
                    initCustomsLogisticsSn();
                case OrderCons.CUSTOMS_INVENTORY:
                    initCustomsInventorySn();
                case OrderCons.CUSTOMS_PAYMENT:
                    initCustomsPaymentSn();
            }
        }
    }
}
