package com.danding.cds.order.base.data.util;

import com.alibaba.fastjson.JSON;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.vo.ExceptionResVO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.vo.CustomsStatusMappingResVO;
import com.danding.cds.order.base.data.bean.BaseDataMapDTO;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: cds-center
 * @description: 基础数据
 * @author: 潘本乐（Belep）
 * @create: 2021-10-16 19:36
 **/
@Slf4j
public class BaseDataCacheUtil {

    private static final Map<Long, BaseDataMapDTO> tenantIdDataMap = new ConcurrentHashMap<>();

    public static Boolean companyCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getCompanyIdCache().isEmpty() && !v.getCompanyCodeCache().isEmpty());
    }

    public static Boolean statusMappingOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getCustomsStatusMappingDTOCache().isEmpty());
    }

    public static Boolean exceptionTypeOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getExceptionDTOCache().isEmpty());
    }

    public static Boolean payChannelCodeCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getPayChannelCodeCache().isEmpty() && !v.getPayChannelIdCache().isEmpty());
    }

    public static Boolean customsBookIdCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getCustomsBookIdCache().isEmpty());
    }

    public static Boolean expressCodeCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getExpressCodeCache().isEmpty());
    }

    public static Boolean expressIdCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getExpressIdCache().isEmpty());
    }

    public static Boolean routeCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getRouteIdCache().isEmpty() && !v.getRouteCodeCache().isEmpty());
    }


    public static Boolean payMerchantAccountChannelCodeCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getPayMerchantAccountChannelCodeCache().isEmpty());
    }

    public static Boolean payMerchantAccountCodeCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getPayMerchantAccountCodeCache().isEmpty());
    }


    public static Boolean pathCacheOk() {
        return tenantIdDataMap.values().stream().allMatch(v -> !v.getPathListCache().isEmpty());
    }

    public static BaseDataMapDTO getBaseData() {
        Long tenantId = SimpleTenantHelper.getTenantId();
        if (Objects.nonNull(tenantId) && tenantIdDataMap.containsKey(tenantId)) {
            BaseDataMapDTO baseDataMapDTO = tenantIdDataMap.get(tenantId);
            return baseDataMapDTO;
        }
        return new BaseDataMapDTO();
    }

    // FIXME: 2022/12/12 这里会有并发问题
    public static void setBaseData(BaseDataMapDTO baseData) {
        Long tenantId = SimpleTenantHelper.getTenantId();
        tenantIdDataMap.put(tenantId, baseData);
    }


    /**
     * 获取商户渠道
     *
     * @param merchantId 商户ID
     * @param channel    渠道
     * @return
     */
    public static PayMerchantAccountChannelDTO getPayMerchantAccountChannelDTOByChannel(Long merchantId, String channel) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        if (Objects.nonNull(payMerchantAccountChannelCodeCache)) {
            return payMerchantAccountChannelCodeCache.get(merchantId + "#" + channel);
        }
        return null;
    }

    /**
     * 通过code,获取支付商户
     *
     * @param code 编码
     * @return
     */
    public static PayMerchantAccountDTO getPayMerchantAccountDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        if (Objects.nonNull(payMerchantAccountCodeCache)) {
            return payMerchantAccountCodeCache.get(code);
        }
        return null;
    }

    /**
     * 通过code,获取支付方式
     *
     * @param code 编码
     * @return
     */
    public static PayChannelDTO getPayChannelDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        if (Objects.nonNull(payChannelCodeCache)) {
            return payChannelCodeCache.get(code);
        }
        return null;
    }

    public static PayChannelDTO getPayChannelDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, PayChannelDTO> payChannelIdCache = baseData.getPayChannelIdCache();
        if (Objects.nonNull(payChannelIdCache)) {
            return payChannelIdCache.get(id);
        }
        return null;
    }


    /**
     * 通过id获取回执映射
     *
     * @param id
     * @return
     */
    public static CustomsStatusMappingDTO getCustomsStatusMappingDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsStatusMappingDTO> payChannelIdCache = baseData.getCustomsStatusMappingDTOCache();
        if (Objects.nonNull(payChannelIdCache)) {
            return payChannelIdCache.get(id);
        }
        return null;
    }

    public static CustomsStatusMappingDTO getCustomsStatusMappingDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CustomsStatusMappingDTO> customsStatusMappingDTOCodeCache = baseData.getCustomsStatusMappingDTOCodeCache();
        if (Objects.nonNull(customsStatusMappingDTOCodeCache)) {
            return customsStatusMappingDTOCodeCache.get(code);
        }
        return null;
    }

    /**
     * 通过y获取异常分类
     *
     * @param useTag
     * @return
     */
    public static List<ExceptionDTO> getExceptionDTOByUseTag(Integer useTag) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        List<ExceptionDTO> exceptionDTOList = new ArrayList<>();
        exceptionDTOCache.forEach((k, v) -> {
            // 判断下useTag是否包含有数据
            Integer useTags = v.getUseTag();
            if (useTags == null) {
                return;
            }
            if ((useTags & useTag) == useTag) {
                exceptionDTOList.add(v);
            }
        });
        return exceptionDTOList.stream().sorted(Comparator.comparing(ExceptionDTO::getUpdateTime)).collect(Collectors.toList());
    }

    /**
     * 通过id获取异常分类
     *
     * @param id
     * @return
     */
    public static ExceptionDTO getExceptionDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        if (Objects.nonNull(exceptionDTOCache)) {
            return exceptionDTOCache.get(id);
        }
        return null;
    }

    /**
     * 通过code,获取快递
     *
     * @param code 编码
     * @return
     */
    public static ExpressDTO getExpressDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.nonNull(baseData)) {
            Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
            if (Objects.nonNull(expressCodeCache)) {
                return expressCodeCache.get(code);
            }
        }
        return null;
    }

    public static ExpressDTO getExpressDTOById(Long expressId) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExpressDTO> expressIdCache = baseData.getExpressIdCache();
        if (Objects.nonNull(expressIdCache)) {
            return expressIdCache.get(expressId);
        }
        return null;
    }

    /**
     * 通过id,获取账册
     *
     * @param id 主键ID
     * @return
     */
    public static CustomsBookDTO getCustomsBookDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        if (Objects.nonNull(customsBookIdCache)) {
            return customsBookIdCache.get(id);
        }
        return null;
    }

    public static CustomsBookDTO getCustomsBookDTOByNo(String customsBookNo) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CustomsBookDTO> customsBookNoCache = baseData.getCustomsBookNoCache();
        if (Objects.nonNull(customsBookNoCache)) {
            return customsBookNoCache.get(customsBookNo);
        }
        return null;
    }

    /**
     * 通过企业id,获取企业
     *
     * @param id 主键ID
     * @return
     */
    public static CompanyDTO getCompanyDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        if (Objects.nonNull(companyIdCache)) {
            return companyIdCache.get(id);
        }
        return null;
    }

    public static CompanyDTO getCompanyDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        if (Objects.nonNull(companyCodeCache)) {
            return companyCodeCache.get(code);
        }
        return null;
    }

    /**
     * 跨境进口统一编码
     *
     * @param code 海关十位编码
     * @return companyDTO
     */
    public static CompanyDTO getCompanyDTOByUnifiedCrossBroderCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        return companyCodeCache.values().stream().filter(c -> Objects.equals(c.getUnifiedCrossBroderCode(), code)).findAny().orElse(null);
    }

    /**
     * 保税物流管理系统编码
     *
     * @param code 海关十位编码
     * @return companyDTO
     */
    public static CompanyDTO getCompanyDTOBySpecialClientCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        return companyCodeCache.values().stream().filter(c -> Objects.equals(c.getSpecialClientCode(), code)).findAny().orElse(null);
    }

    /**
     * 分类监管编码
     *
     * @param code 海关十位编码
     * @return companyDTO
     */
    public static CompanyDTO getCompanyDTOByFbCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        return companyCodeCache.values().stream().filter(c -> Objects.equals(c.getFbCode(), code)).findAny().orElse(null);
    }

    /**
     * 获取路径
     *
     * @param identify1
     * @param identify2
     * @param identify3
     * @return
     */
    public static PathDTO getPathDTOByIdentify(String identify1, String identify2, String identify3) {

        if (!StringUtils.isEmpty(identify3)) {
            if (StringUtils.isEmpty(identify1) || StringUtils.isEmpty(identify2)) {
                throw new ArgsErrorException("标识3不为空时，标识1或标识2不能为空");
            }
        }
        if (!StringUtils.isEmpty(identify2)) {
            if (StringUtils.isEmpty(identify1)) {
                throw new ArgsErrorException("标识2不为空时，标识1不能为空");
            }
        }
        if (StringUtils.isEmpty(identify1)) {
            throw new ArgsErrorException("标识1不能为空");
        }
        //数据库之前默认的都是"" 需要在这里做一个兼容
        if (identify2 == null) {
            identify2 = "";
        }
        if (identify3 == null) {
            identify3 = "";
        }
        PathDTO path = getPathDTO(identify1, identify2, identify3);

        if (path == null) {
            path = getPathDTO(identify1, identify2, null);
        }

        if (path == null) {
            path = getPathDTO(identify1, null, null);
        }
        return path;
    }

    private static PathDTO getPathDTO(String identify1, String identify2, String identify3) {
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        List<PathDTO> pathDTO = pathListCache.stream()
                .filter(z -> {
                    return equalsTo(z.getThirdIdentify(), identify3)
                            && equalsTo(z.getSecondIdentify(), identify2)
                            && equalsTo(z.getFirstIdentify(), identify1);
                })
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(pathDTO) || pathDTO.size() > 1) {
            log.info("根据路由拓展字段：{}，{}，{}，未找到或找到多条数据", identify1, identify2, identify3);
            return null;
        }
        return pathDTO.get(0);
    }

    public static boolean equalsTo(Object a, Object b) {
        return Objects.equals(a, b) || (StringUtils.isEmpty(a) && StringUtils.isEmpty(b));
    }

    /**
     * 获取路径，通过code
     *
     * @param id 路径ID
     * @return
     */
    public static RouteDTO getRouteDTOById(Long id) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        if (Objects.nonNull(routeIdCache)) {
            return routeIdCache.get(id);
        }
        return null;
    }

    /**
     * 获取路径，通过code
     *
     * @param code 路径编码
     * @return
     */
    public static RouteDTO getRouteDTOByCode(String code) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        if (Objects.nonNull(routeCodeCache)) {
            return routeCodeCache.get(code);
        }
        return null;
    }


    /**
     * 初始化商户
     *
     * @param list
     */
    public static void initPayMerchantAccount(List<PayMerchantAccountDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, PayMerchantAccountDTO> tempPayAccountCodeCache = list.stream()
                .collect(Collectors.toMap(PayMerchantAccountDTO::getSn, Function.identity(), (v1, v2) -> v1));
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        payMerchantAccountCodeCache.putAll(tempPayAccountCodeCache);
        baseData.setPayMerchantAccountCodeCache(payMerchantAccountCodeCache);
    }

    /**
     * 初始化商户
     *
     * @param list
     */
    public static void initinitPayMerchantAccountChannel(List<PayMerchantAccountChannelDTO> list) {

        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Map<String, PayMerchantAccountChannelDTO> tempPayAccountCodeCache = list.stream()
                .collect(Collectors.toMap(k -> k.getMerchantId() + "#" + k.getChannel(), Function.identity(), (v1, v2) -> v1));
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        payMerchantAccountChannelCodeCache.putAll(tempPayAccountCodeCache);
        baseData.setPayMerchantAccountChannelCodeCache(payMerchantAccountChannelCodeCache);
    }

    /**
     * 初始化路由
     *
     * @param pathList
     */
    public static void initPath(List<PathDTO> pathList) {

        if (CollectionUtils.isEmpty(pathList)) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        pathListCache.addAll(pathList);
        baseData.setPathListCache(pathListCache);
    }

    /**
     * 初始化路径
     *
     * @param expressDTOList
     */
    public static void initRoute(List<RouteDTO> expressDTOList) {

        if (CollectionUtils.isEmpty(expressDTOList)) {
            return;
        }
        Map<String, RouteDTO> tempRouteCodeCache = expressDTOList.stream()
                .collect(Collectors.toMap(RouteDTO::getCode, Function.identity(), (v1, v2) -> v1));

        Map<Long, RouteDTO> tempRouteIdCache = expressDTOList.stream()
                .collect(Collectors.toMap(RouteDTO::getId, Function.identity()));

        BaseDataMapDTO baseData = getBaseData();
        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        routeCodeCache.putAll(tempRouteCodeCache);
        baseData.setRouteCodeCache(routeCodeCache);
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        routeIdCache.putAll(tempRouteIdCache);
        baseData.setRouteIdCache(routeIdCache);
    }


    /**
     * 初始化快递
     *
     * @param expressDTOList
     */
    public static void initExpress(List<ExpressDTO> expressDTOList) {
        if (CollectionUtils.isEmpty(expressDTOList)) {
            return;
        }
        Map<String, ExpressDTO> tempExpressCodeCache = expressDTOList.stream()
                .collect(Collectors.toMap(ExpressDTO::getCode, Function.identity(), (v1, v2) -> v1));
        Map<Long, ExpressDTO> tempExpressIdCache = expressDTOList.stream()
                .collect(Collectors.toMap(ExpressDTO::getId, Function.identity(), (v1, v2) -> v1));

        BaseDataMapDTO baseData = getBaseData();
        Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
        expressCodeCache.putAll(tempExpressCodeCache);
        baseData.setExpressCodeCache(expressCodeCache);
        Map<Long, ExpressDTO> expressIdCache = baseData.getExpressIdCache();
        expressIdCache.putAll(tempExpressIdCache);
        baseData.setExpressIdCache(expressIdCache);
    }

    /**
     * 初始化账册
     *
     * @param customsBookList
     */
    public static void initCustomsBook(List<CustomsBookDTO> customsBookList) {

        if (CollectionUtils.isEmpty(customsBookList)) {
            return;
        }
        Map<Long, CustomsBookDTO> tempCustomsBookIdCache = customsBookList.stream()
                .collect(Collectors.toMap(CustomsBookDTO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, CustomsBookDTO> tempCustomsBookNoCache = customsBookList.stream()
                .collect(Collectors.toMap(CustomsBookDTO::getBookNo, Function.identity(), (v1, v2) -> v1));
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        Map<String, CustomsBookDTO> customsBookNoCache = baseData.getCustomsBookNoCache();
        customsBookIdCache.putAll(tempCustomsBookIdCache);
        customsBookNoCache.putAll(tempCustomsBookNoCache);
        baseData.setCustomsBookIdCache(customsBookIdCache);
        baseData.setCustomsBookNoCache(customsBookNoCache);
    }

    /**
     * 初始化company
     *
     * @param companyList
     */
    public static void initCompanyCache(List<CompanyDTO> companyList) {

        if (CollectionUtils.isEmpty(companyList)) {
            log.info("BaseDataCacheUtil initCompanyCache companyList无数据 不进行初始化");
            return;
        }

        Map<Long, CompanyDTO> tempCompanyIdCache = companyList.stream()
                .collect(Collectors.toMap(CompanyDTO::getId, Function.identity()));

        Map<String, CompanyDTO> tempCompanyCodeCache = companyList.stream()
                .collect(Collectors.toMap(CompanyDTO::getCode, Function.identity(), (v1, v2) -> v1));

        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        companyIdCache.putAll(tempCompanyIdCache);
        baseData.setCompanyIdCache(companyIdCache);
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        companyCodeCache.putAll(tempCompanyCodeCache);
        baseData.setCompanyCodeCache(companyCodeCache);
    }

    /**
     * 初始化CustomsStatusMapping
     *
     * @param statusMappingResVOS
     */
    public static void initStatusMappingCache(List<CustomsStatusMappingResVO> statusMappingResVOS) {

        if (CollectionUtils.isEmpty(statusMappingResVOS)) {
            log.info("BaseDataCacheUtil initStatusMappingCache statusMappingResVOS 不进行初始化");
            return;
        }
        List<CustomsStatusMappingDTO> mappingDTOS = JSON.parseArray(JSON.toJSONString(statusMappingResVOS), CustomsStatusMappingDTO.class);
        Map<Long, CustomsStatusMappingDTO> statusMappingDTOMap = mappingDTOS.stream()
                .collect(Collectors.toMap(CustomsStatusMappingDTO::getId, Function.identity()));
//        customsStatusMappingDTOCache.putAll(statusMappingDTOMap);
        Map<String, CustomsStatusMappingDTO> statusMappingDTOCodeMap = mappingDTOS.stream()
                .collect(Collectors.toMap(CustomsStatusMappingDTO::getCode, Function.identity()));
//        customsStatusMappingDTOCodeCache.putAll(statusMappingDTOCodeMap);

        BaseDataMapDTO baseData = getBaseData();
        Map<String, CustomsStatusMappingDTO> customsStatusMappingDTOCodeCache = baseData.getCustomsStatusMappingDTOCodeCache();
        customsStatusMappingDTOCodeCache.putAll(statusMappingDTOCodeMap);
        baseData.setCustomsStatusMappingDTOCodeCache(customsStatusMappingDTOCodeCache);
        Map<Long, CustomsStatusMappingDTO> customsStatusMappingDTOCache = baseData.getCustomsStatusMappingDTOCache();
        customsStatusMappingDTOCache.putAll(statusMappingDTOMap);
        baseData.setCustomsStatusMappingDTOCache(customsStatusMappingDTOCache);
    }

    /**
     * 初始化ExceptionType
     *
     * @param exceptionResVOS
     */
    public static void initExceptionCache(List<ExceptionResVO> exceptionResVOS) {

        if (CollectionUtils.isEmpty(exceptionResVOS)) {
            log.info("BaseDataCacheUtil initExceptionCache exceptionResVOS 不进行初始化");
            return;
        }
        List<ExceptionDTO> exceptionDTOList = JSON.parseArray(JSON.toJSONString(exceptionResVOS), ExceptionDTO.class);
        Map<Long, ExceptionDTO> exceptionDTOMap = exceptionDTOList.stream()
                .collect(Collectors.toMap(ExceptionDTO::getId, Function.identity()));

        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        exceptionDTOCache.putAll(exceptionDTOMap);
        baseData.setExceptionDTOCache(exceptionDTOCache);
    }

    /**
     * 初始化company
     *
     * @param payChannelList
     */
    public static void initPayChannelCache(List<PayChannelDTO> payChannelList) {

        if (CollectionUtils.isEmpty(payChannelList)) {
            return;
        }
        Map<String, PayChannelDTO> payChannelCodeCacheMap = payChannelList.stream()
                .collect(Collectors.toMap(PayChannelDTO::getCode, Function.identity(), (v1, v2) -> v1));
        Map<Long, PayChannelDTO> payChannelIdCacheMap = payChannelList.stream()
                .collect(Collectors.toMap(PayChannelDTO::getId, Function.identity(), (v1, v2) -> v1));

        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        payChannelCodeCache.putAll(payChannelCodeCacheMap);
        baseData.setPayChannelCodeCache(payChannelCodeCache);
        Map<Long, PayChannelDTO> payChannelIdCache = baseData.getPayChannelIdCache();
        payChannelIdCache.putAll(payChannelIdCacheMap);
        baseData.setPayChannelIdCache(payChannelIdCache);
    }

    public static String getCompanyIdCache() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return null;
        }
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        return JSON.toJSONString(companyIdCache);
    }

    public static String getCompanyCodeCache() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return null;
        }
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        return JSON.toJSONString(companyCodeCache);
    }

    public static String getStatusMapping() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return null;
        }
        Map<Long, CustomsStatusMappingDTO> customsStatusMappingDTOCache = baseData.getCustomsStatusMappingDTOCache();
        return JSON.toJSONString(customsStatusMappingDTOCache);
    }

    public static String getException() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return null;
        }
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        return JSON.toJSONString(exceptionDTOCache);
    }

    public static String getPayChannelCache() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return null;
        }
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        return JSON.toJSONString(payChannelCodeCache);
    }


    public static synchronized void insertCompany(CompanyDTO companyDTO) {
        if (companyDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        companyCodeCache.put(companyDTO.getCode(), companyDTO);
        companyIdCache.put(companyDTO.getId(), companyDTO);
        baseData.setCompanyCodeCache(companyCodeCache);
        baseData.setCompanyIdCache(companyIdCache);
        log.info("缓存数据：{}", getCompanyCodeCache());
    }

    public static synchronized void insertStatusMapping(CustomsStatusMappingDTO mappingDTO) {
        if (mappingDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsStatusMappingDTO> customsStatusMappingDTOCache = baseData.getCustomsStatusMappingDTOCache();
        Map<String, CustomsStatusMappingDTO> customsStatusMappingDTOCodeCache = baseData.getCustomsStatusMappingDTOCodeCache();
        customsStatusMappingDTOCache.put(mappingDTO.getId(), mappingDTO);
        customsStatusMappingDTOCodeCache.put(mappingDTO.getCode(), mappingDTO);
        baseData.setCustomsStatusMappingDTOCache(customsStatusMappingDTOCache);
        baseData.setCustomsStatusMappingDTOCodeCache(customsStatusMappingDTOCodeCache);
        log.info("缓存数据：{}", getStatusMapping());
    }

    public static synchronized void insertException(ExceptionDTO exceptionDTO) {
        if (exceptionDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        exceptionDTOCache.put(exceptionDTO.getId(), exceptionDTO);
        baseData.setExceptionDTOCache(exceptionDTOCache);
        log.info("缓存数据：{}", getException());
    }

    public static void deleteCompany(CompanyDTO companyDTO) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        companyCodeCache.remove(companyDTO.getCode());
        companyIdCache.remove(companyDTO.getId());
        baseData.setCompanyCodeCache(companyCodeCache);
        baseData.setCompanyIdCache(companyIdCache);
    }

    public static void removeCompany() {
        BaseDataMapDTO baseData = getBaseData();
        if (Objects.isNull(baseData)) {
            return;
        }
        Map<String, CompanyDTO> companyCodeCache = baseData.getCompanyCodeCache();
        Map<Long, CompanyDTO> companyIdCache = baseData.getCompanyIdCache();
        companyIdCache.clear();
        companyCodeCache.clear();
        baseData.setCompanyCodeCache(companyCodeCache);
        baseData.setCompanyIdCache(companyIdCache);
    }

    public static void removeStatusMapping() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsStatusMappingDTO> customsStatusMappingDTOCache = baseData.getCustomsStatusMappingDTOCache();
        Map<String, CustomsStatusMappingDTO> customsStatusMappingDTOCodeCache = baseData.getCustomsStatusMappingDTOCodeCache();
        customsStatusMappingDTOCache.clear();
        customsStatusMappingDTOCodeCache.clear();
        baseData.setCustomsStatusMappingDTOCache(customsStatusMappingDTOCache);
        baseData.setCustomsStatusMappingDTOCodeCache(customsStatusMappingDTOCodeCache);
    }

    public static void removeException() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, ExceptionDTO> exceptionDTOCache = baseData.getExceptionDTOCache();
        exceptionDTOCache.clear();
        baseData.setExceptionDTOCache(exceptionDTOCache);
    }

    public static synchronized void insertPayChannel(PayChannelDTO payChannelDTO) {
        if (payChannelDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        Map<Long, PayChannelDTO> payChannelIdCache = baseData.getPayChannelIdCache();
        payChannelCodeCache.put(payChannelDTO.getCode(), payChannelDTO);
        payChannelIdCache.put(payChannelDTO.getId(), payChannelDTO);
        baseData.setPayChannelCodeCache(payChannelCodeCache);
        baseData.setPayChannelIdCache(payChannelIdCache);
    }

    public static void deletePayChannel(PayChannelDTO payChannelDTO) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        Map<Long, PayChannelDTO> payChannelIdCache = baseData.getPayChannelIdCache();
        payChannelCodeCache.remove(payChannelDTO.getCode());
        payChannelIdCache.remove(payChannelDTO.getId());
        baseData.setPayChannelCodeCache(payChannelCodeCache);
        baseData.setPayChannelIdCache(payChannelIdCache);
    }

    public static void removePayChannel() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayChannelDTO> payChannelCodeCache = baseData.getPayChannelCodeCache();
        Map<Long, PayChannelDTO> payChannelIdCache = baseData.getPayChannelIdCache();
        payChannelCodeCache.clear();
        payChannelIdCache.clear();
        baseData.setPayChannelCodeCache(payChannelCodeCache);
        baseData.setPayChannelIdCache(payChannelIdCache);
    }

    public static String getCustomsBookCache() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        return JSON.toJSONString(customsBookIdCache);
    }

    public static Map<Long, CustomsBookDTO> getCustomsBookCacheMap() {
        BaseDataMapDTO baseData = getBaseData();
        return baseData.getCustomsBookIdCache();
    }

    public static synchronized void insertCustomsBook(CustomsBookDTO customsBookDTO) {
        if (customsBookDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        Map<String, CustomsBookDTO> customsBookNoCache = baseData.getCustomsBookNoCache();
        customsBookIdCache.put(customsBookDTO.getId(), customsBookDTO);
        customsBookNoCache.put(customsBookDTO.getBookNo(), customsBookDTO);
        baseData.setCustomsBookIdCache(customsBookIdCache);
    }

    public static void deleteCustomsBook(CustomsBookDTO customsBookDTO) {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        Map<String, CustomsBookDTO> customsBookNoCache = baseData.getCustomsBookNoCache();
        customsBookIdCache.remove(customsBookDTO.getId());
        customsBookNoCache.remove(customsBookDTO.getBookNo());
        baseData.setCustomsBookIdCache(customsBookIdCache);
    }

    public static void removeCustomsBook() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, CustomsBookDTO> customsBookIdCache = baseData.getCustomsBookIdCache();
        Map<String, CustomsBookDTO> customsBookNoCache = baseData.getCustomsBookNoCache();
        customsBookIdCache.clear();
        customsBookNoCache.clear();
        baseData.setCustomsBookIdCache(customsBookIdCache);
        baseData.setCustomsBookNoCache(customsBookNoCache);
    }

    public static String getExpressCache() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
        return JSON.toJSONString(expressCodeCache);
    }

    public static synchronized void insertExpress(ExpressDTO expressDTO) {
        if (expressDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
        Map<Long, ExpressDTO> expressIdCache = baseData.getExpressIdCache();
        expressCodeCache.put(expressDTO.getCode(), expressDTO);
        expressIdCache.put(expressDTO.getId(), expressDTO);
        baseData.setExpressCodeCache(expressCodeCache);
        baseData.setExpressIdCache(expressIdCache);
    }

    public static void deleteExpress(ExpressDTO expressDTO) {
        if (expressDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
        Map<Long, ExpressDTO> expressIdCache = baseData.getExpressIdCache();
        expressCodeCache.remove(expressDTO.getCode());
        expressCodeCache.remove(expressDTO.getId());
        baseData.setExpressCodeCache(expressCodeCache);
        baseData.setExpressIdCache(expressIdCache);
    }

    public static void removeExpress() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, ExpressDTO> expressCodeCache = baseData.getExpressCodeCache();
        Map<Long, ExpressDTO> expressIdCache = baseData.getExpressIdCache();
        expressCodeCache.clear();
        expressIdCache.clear();
        baseData.setExpressCodeCache(expressCodeCache);
        baseData.setExpressIdCache(expressIdCache);
    }

    public static String getRouteIdCache() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        return JSON.toJSONString(routeIdCache);
    }

    public static String getRouteCodeCache() {
        BaseDataMapDTO baseData = getBaseData();

        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        return JSON.toJSONString(routeCodeCache);
    }

    public static synchronized void insertRoute(RouteDTO routeDTO) {
        if (routeDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        routeIdCache.put(routeDTO.getId(), routeDTO);
        routeCodeCache.put(routeDTO.getCode(), routeDTO);
        baseData.setRouteIdCache(routeIdCache);
        baseData.setRouteCodeCache(routeCodeCache);
    }

    public static void deleteRoute(RouteDTO routeDTO) {
        if (routeDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        routeIdCache.remove(routeDTO.getId());
        routeCodeCache.remove(routeDTO.getCode());
        baseData.setRouteIdCache(routeIdCache);
        baseData.setRouteCodeCache(routeCodeCache);
    }

    public static void removeRoute() {
        BaseDataMapDTO baseData = getBaseData();
        Map<Long, RouteDTO> routeIdCache = baseData.getRouteIdCache();
        Map<String, RouteDTO> routeCodeCache = baseData.getRouteCodeCache();
        routeIdCache.clear();
        routeCodeCache.clear();
        baseData.setRouteIdCache(routeIdCache);
        baseData.setRouteCodeCache(routeCodeCache);
    }

    public static String getPathCache() {
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        return JSON.toJSONString(pathListCache);
    }

    public static synchronized void insertPath(PathDTO pathDTO) {
        if (pathDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        Iterator<PathDTO> pathDTOIterator = pathListCache.iterator();
        if (pathDTOIterator.hasNext()) {
            PathDTO path = pathDTOIterator.next();
            if (Objects.equals(path.getId(), pathDTO.getId())) {
                pathDTOIterator.remove();
            }
        }
        pathListCache.add(pathDTO);
        baseData.setPathListCache(pathListCache);
    }

    public static void updatePath(PathDTO before, PathDTO after) {
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        Iterator<PathDTO> iterator = pathListCache.iterator();
        while (iterator.hasNext()) {
            PathDTO p = iterator.next();
            if (Objects.equals(p.getId(), before.getId())) {
                iterator.remove();
            }
        }
        pathListCache.add(after);
        baseData.setPathListCache(pathListCache);
    }

    public static void deletePath(PathDTO pathDTO) {
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        Iterator<PathDTO> iterator = pathListCache.iterator();
        while (iterator.hasNext()) {
            PathDTO p = iterator.next();
            if (Objects.equals(p.getId(), pathDTO.getId())) {
                iterator.remove();
            }
        }
        baseData.setPathListCache(pathListCache);
    }

    public static void removePath() {
        BaseDataMapDTO baseData = getBaseData();
        List<PathDTO> pathListCache = baseData.getPathListCache();
        pathListCache.clear();
        baseData.setPathListCache(pathListCache);
    }

    public static String getPayMerchantAccountCache() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        return JSON.toJSONString(payMerchantAccountCodeCache);
    }

    public static synchronized void insertPayMerchantAccount(PayMerchantAccountDTO payMerchantAccountDTO) {
        if (payMerchantAccountDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        payMerchantAccountCodeCache.put(payMerchantAccountDTO.getSn(), payMerchantAccountDTO);
        baseData.setPayMerchantAccountCodeCache(payMerchantAccountCodeCache);
    }

    public static void deletePayMerchantAccount(PayMerchantAccountDTO payMerchantAccountDTO) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        payMerchantAccountCodeCache.remove(payMerchantAccountDTO.getSn());
        baseData.setPayMerchantAccountCodeCache(payMerchantAccountCodeCache);
    }

    public static void removePayMerchantAccount() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = baseData.getPayMerchantAccountCodeCache();
        payMerchantAccountCodeCache.clear();
        baseData.setPayMerchantAccountCodeCache(payMerchantAccountCodeCache);
    }

    public static String getPayMerchantAccountChannelCache() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        return JSON.toJSONString(payMerchantAccountChannelCodeCache);
    }

    public static synchronized void insertPayMerchantAccountChannel(PayMerchantAccountChannelDTO payMerchantAccountChannelDTO) {
        if (payMerchantAccountChannelDTO == null) {
            return;
        }
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        payMerchantAccountChannelCodeCache.put(payMerchantAccountChannelDTO.getMerchantId() + "#" + payMerchantAccountChannelDTO.getChannel(), payMerchantAccountChannelDTO);
        baseData.setPayMerchantAccountChannelCodeCache(payMerchantAccountChannelCodeCache);
    }

    public static void deletePayMerchantAccountChannel(PayMerchantAccountChannelDTO payMerchantAccountChannelDTO) {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        payMerchantAccountChannelCodeCache.remove(payMerchantAccountChannelDTO.getMerchantId() + "#" + payMerchantAccountChannelDTO.getChannel());
        baseData.setPayMerchantAccountChannelCodeCache(payMerchantAccountChannelCodeCache);
    }

    public static void removePayMerchantAccountChannel() {
        BaseDataMapDTO baseData = getBaseData();
        Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = baseData.getPayMerchantAccountChannelCodeCache();
        payMerchantAccountChannelCodeCache.clear();
        baseData.setPayMerchantAccountChannelCodeCache(payMerchantAccountChannelCodeCache);
    }

}
