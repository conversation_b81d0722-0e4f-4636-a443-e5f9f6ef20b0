package com.danding.cds.order.base.data.util;

import com.danding.cds.common.utils.SequenceJoinUtil;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * @program: cds-center
 * @description: id和sn缓存，有读取和写入，其实就是读写锁互斥的问题，直接用非阻塞-FIFO队列的方式解决此问题。
 * @author: 潘本乐（Belep）
 * @create: 2021-10-20 13:42
 **/
public class IdSnCacheUtil {

    /**
     * ID-列表
     */
    private volatile static Queue<Long> ID_QUEUE = new ConcurrentLinkedQueue<Long>();
    /**
     * 申报单SN
     */
    private static Queue ORDER_SN_QUEUE = new ConcurrentLinkedQueue<Long>();

    private static Queue EXTERNAL_ORDER_SN_QUEUE = new ConcurrentLinkedQueue<Long>();

    /**
     * 订单Sn
     */
    private static Queue CUSTOMS_ORDER_SN_QUEUE = new ConcurrentLinkedQueue<Long>();
    /**
     * 运单Sn
     */
    private static Queue CUSTOMS_LOGICITICS_SN_QUEUE = new ConcurrentLinkedQueue<Long>();

    /**
     * 清单SN
     */
    private static Queue CUSTOMS_INVENTORY_SN_QUEUE = new ConcurrentLinkedQueue<Long>();
    /**
     * 支付单SN
     */
    private static Queue CUSTOMS_PAYMENT_SN_QUEUE = new ConcurrentLinkedQueue<Long>();


    public static boolean uniqueIdOk() {
        return ID_QUEUE.size() > 0;
    }

    public static boolean orderSnOk() {
        return !ORDER_SN_QUEUE.isEmpty();
    }

    public static boolean externalOrderSnOk() {
        return !EXTERNAL_ORDER_SN_QUEUE.isEmpty();
    }

    public static boolean customsOrderSnOk() {
        return !CUSTOMS_ORDER_SN_QUEUE.isEmpty();
    }

    public static boolean customsLogisticSnOk() {
        return !CUSTOMS_LOGICITICS_SN_QUEUE.isEmpty();
    }

    public static boolean customsInventorySnOk() {
        return !CUSTOMS_INVENTORY_SN_QUEUE.isEmpty();
    }

    public static boolean customsPaymentSnOk() {
        return !CUSTOMS_PAYMENT_SN_QUEUE.isEmpty();
    }

    /**
     * 设置-申报单SN-末尾编号
     *
     * @param index
     */
    public static void setOrderSnIndex(Long index) {
        ORDER_SN_QUEUE.offer(index);
    }

    public static void setExternalOrderSnIndex(Long index) {
        EXTERNAL_ORDER_SN_QUEUE.offer(index);
    }

    /**
     * 设置-订单SN-末尾编号
     *
     * @param index
     */
    public static void setCustomsOrderSnIndex(Long index) {
        CUSTOMS_ORDER_SN_QUEUE.offer(index);
    }

    /**
     * 设置-运单SN-末尾编号
     *
     * @param index
     */
    public static void setCustomsLogisticsSnIndex(Long index) {
        CUSTOMS_LOGICITICS_SN_QUEUE.offer(index);
    }

    /**
     * 设置-清单SN-末尾编号
     *
     * @param index
     */
    public static void setCustomsInventorySnIndex(Long index) {
        CUSTOMS_INVENTORY_SN_QUEUE.offer(index);
    }

    /**
     * 设置-支付单SN-末尾编号
     *
     * @param index
     */
    public static void setCustomsPaymentSnIndex(Long index) {
        CUSTOMS_PAYMENT_SN_QUEUE.offer(index);
    }


    /**
     * 获取唯一ID
     *
     * @return
     */
    public static Long getUniqueId() {
        return ID_QUEUE.poll();
    }

    /**
     * 获取-申报单SN
     *
     * @return sn
     */
    public static String getOrderSn() {
        Long num = (Long) ORDER_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generateOrderSn(num);
    }

    public static String getExternalOrderSn() {
        Long num = (Long) EXTERNAL_ORDER_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generateExternalOrderSn(num);
    }

    /**
     * 获取-海关订单SN
     *
     * @return sn
     */
    public static String getCustomsOrderSn() {

        Long num = (Long) CUSTOMS_ORDER_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generateCustomsOrderSn(num);
    }

    /**
     * 获取-海关运单SN
     *
     * @return sn
     */
    public static String getCustomsLogisticsSn() {

        Long num = (Long) CUSTOMS_LOGICITICS_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generateCustomsLogisticsSn(num);
    }

    /**
     * 获取-海关清单SN
     *
     * @return sn
     */
    public static String getCustomsInventorySn() {

        Long num = (Long) CUSTOMS_INVENTORY_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generateCustomsInventorySn(num);
    }

    /**
     * 获取-海关支付单SN
     *
     * @return sn
     */
    public static String getCustomsPaymentSn() {

        Long num = (Long) CUSTOMS_PAYMENT_SN_QUEUE.poll();
        if (num == null) {
            return null;
        }
        return SequenceJoinUtil.generatePaymentDeclareSn(num);
    }

}
