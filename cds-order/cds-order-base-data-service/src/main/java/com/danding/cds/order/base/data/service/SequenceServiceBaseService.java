package com.danding.cds.order.base.data.service;

import com.danding.cds.common.constants.OrderCons;
import com.danding.cds.order.base.data.util.IdSnCacheUtil;
import com.danding.cds.sequence.api.service.SequenceService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;

/**
 * @program: cds-center
 * @description: id和序列号
 * @author: 潘本乐（Belep）
 * @create: 2021-10-20 14:08
 **/
@Service
@Slf4j
public class SequenceServiceBaseService {

    @DubboReference
    private SequenceService sequenceService;

    @Resource
    private IdSequenceInitService idSequenceInitService;


    /**
     * 获取唯一ID
     *
     * @return
     */
    public Long getUniqueId() {
        return sequenceService.generateId();
    }

    /**
     * 获取-申报单SN
     *
     * @return sn
     */
    public String generateOrderSn() {

        String sn = IdSnCacheUtil.getOrderSn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generateOrderSn();
            log.info("缓存的序列号 - 申报单号SN - 已用完 - 准备异步缓存数据");
            idSequenceInitService.asyncCacheSn(OrderCons.ORDER);
        }
        return sn;
    }

    public String generateExternalOrderSn() {
        String sn = IdSnCacheUtil.getExternalOrderSn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generateExternalOrderSn();
        }
        return sn;
    }

    /**
     * 获取-海关订单SN
     *
     * @return sn
     */
    public String generateCustomsOrderSn() {
        String sn = IdSnCacheUtil.getCustomsOrderSn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generateCustomsOrderSn();
            log.info("缓存的序列号 - 海关订单SN - 已用完 - 放入线程池，异步缓存");
            idSequenceInitService.asyncCacheSn(OrderCons.CUSTOMS_ORDER);
        }
        return sn;
    }

    /**
     * 获取-海关运单SN
     *
     * @return sn
     */
    public String generateCustomsLogisticsSn() {
        String sn = IdSnCacheUtil.getCustomsLogisticsSn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generateCustomsLogisticsSn();
            log.info("缓存的序列号 - 海关运单SN - 已用完 - 放入线程池，异步缓存");
            idSequenceInitService.asyncCacheSn(OrderCons.CUSTOMS_LOGICTICS);
        }
        return sn;
    }

    /**
     * 获取-海关清单SN
     *
     * @return sn
     */
    public String generateCustomsInventorySn() {
        String sn = IdSnCacheUtil.getCustomsInventorySn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generateCustomsInventorySn();
            log.info("缓存的序列号 - 海关清单SN - 已用完 - 放入线程池，异步缓存");
            idSequenceInitService.asyncCacheSn(OrderCons.CUSTOMS_INVENTORY);
        }
        return sn;
    }

    /**
     * 获取-海关支付单SN
     *
     * @return sn
     */
    public String generatePaymentDeclareSn() {
        String sn = IdSnCacheUtil.getCustomsPaymentSn();
        if (StringUtils.isEmpty(sn)) {
            sn = sequenceService.generatePaymentDeclareSn();
            log.info("缓存的序列号 - 海关支付单SN - 已用完 - 放入线程池，异步缓存");
            idSequenceInitService.asyncCacheSn(OrderCons.CUSTOMS_PAYMENT);
        }
        return sn;
    }

}
