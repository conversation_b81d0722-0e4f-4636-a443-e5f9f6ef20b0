package com.danding.cds.order.base.data.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncDTO;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.ExceptionService;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.dto.CompanySearch;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.exception.api.vo.ExceptionReqVO;
import com.danding.cds.exception.api.vo.ExceptionResVO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookSearchCondition;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.vo.CustomsStatusMappingReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingResVO;
import com.danding.cds.order.base.data.bean.BaseDataMapDTO;
import com.danding.cds.order.base.data.util.BaseDataCacheUtil;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.path.api.dto.PathSearch;
import com.danding.cds.path.api.service.PathService;
import com.danding.cds.payChannel.api.dto.*;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.dto.RouteSearch;
import com.danding.cds.route.api.service.RouteService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @program: cds-center
 * @description: 基础数据初始化
 * @author: 潘本乐（Belep）
 * @create: 2021-10-16 19:26
 **/
@Slf4j
@Service
@RefreshScope
public class BaseDataInitService {

    @DubboReference
    private PayChannelService payChannelService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private RouteService routeService;
    @DubboReference
    private PathService pathService;
    @DubboReference
    private PayMerchantAccountService payMerchantAccountService;
    @DubboReference
    private PayMerchantAccountChannelService payMerchantAccountChannelService;

    @Resource
    private CustomsStatusMappingService customsStatusMappingService;

    @Resource
    private ExceptionService exceptionService;

    @Autowired
    private MessageSender messageSender;

    @Value("${baseData.init.tenantList:1001}")
    private Long[] baseDataInitTenantList;

    private static ExecutorService initTasksThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    private static ExecutorService refreshTasksThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    private static ExecutorService initTenantThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    class initBase implements Runnable {

        /**
         * When an object implementing interface <code>Runnable</code> is used
         * to create a thread, starting the thread causes the object's
         * <code>run</code> method to be called in that separately executing
         * thread.
         * <p>
         * The general contract of the method <code>run</code> is that it may
         * take any action whatsoever.
         *
         * @see Thread#run()
         */
        @Override
        public void run() {

        }
    }

    @Test
    public void test() {
        List<Long> tenantIdList = Arrays.asList(baseDataInitTenantList);
        tenantIdList.forEach(t ->
                initTenantThread.submit(() -> {
                    log.info("初始化");
                    SimpleTenantHelper.setTenantId(t);
                    long startTime = System.currentTimeMillis();
                    initCompany();
                    initPayChannel();
                    initCustomsBook();
                    initExpress();
                    initRoute();
                    initPath();
                    initPayMerchantAccount();
                    initPayMerchantAccountChannel();
                    initStatusMapping();
                    initExceptionType();
                    long endTime = System.currentTimeMillis();
                    System.out.println("BaseData初始化消耗:" + (endTime - startTime) + "ms");
                })
        );
    }

    /**
     * 初始化所有参数的缓存
     */
    public void init() {
        List<Long> tenantIdList = Arrays.asList(baseDataInitTenantList);
        tenantIdList.forEach(t ->
                initTenantThread.submit(() -> {
                    log.info("初始化数据 当前租户:{}", t);
                    SimpleTenantHelper.setTenantId(t);
                    BaseDataMapDTO baseData = new BaseDataMapDTO();
                    BaseDataCacheUtil.setBaseData(baseData);
                    long startTime = System.currentTimeMillis();
                    initCompany();
                    initPayChannel();
                    initCustomsBook();
                    initExpress();
                    initRoute();
                    initPath();
                    initPayMerchantAccount();
                    initPayMerchantAccountChannel();
                    initStatusMapping();
                    initExceptionType();
                    long endTime = System.currentTimeMillis();
                    System.out.println("BaseData初始化消耗:" + (endTime - startTime) + "ms");
                })
        );
    }

    public void createInitTasksThread(String taskType, Object data) {
        final Long tenantId = SimpleTenantHelper.getTenantId();
        switch (taskType) {
            case BaseDataSyncTypeEnums.COMPANY:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertCompany((CompanyDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.CUSTOMS_BOOK:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertCustomsBook((CustomsBookDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.EXPRESS:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertExpress((ExpressDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.PATH:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertPath((PathDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.PAY_CHANNEL:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertPayChannel((PayChannelDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertPayMerchantAccount((PayMerchantAccountDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertPayMerchantAccountChannel((PayMerchantAccountChannelDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.ROUTE:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertRoute((RouteDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertStatusMapping((CustomsStatusMappingDTO) data);
                });
                break;
            case BaseDataSyncTypeEnums.EXCEPTIONTYPE:
                initTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(tenantId);
                    BaseDataCacheUtil.insertException((ExceptionDTO) data);
                });
                break;
            default:
                break;
        }
    }

    /**
     * 转发mq广播 所有节点同步
     *
     * @return
     */
    public void refreshBaseDataProxy(String dataType) {
        List<Long> tenantIdList = Arrays.asList(baseDataInitTenantList);
        BaseDataSyncDTO baseDataSyncDTO = new BaseDataSyncDTO();
        baseDataSyncDTO.setChangeType(BaseDataSyncTypeEnums.REFRESH).setDataType(dataType);
        tenantIdList.forEach(t ->
                refreshTasksThread.submit(() -> {
                    SimpleTenantHelper.setTenantId(t);
                    messageSender.sendMsg(baseDataSyncDTO, BaseDataSyncTypeEnums.SYNC_TOPIC);
                    log.info("BaseDataInitService refreshBaseData 刷新数据 类型:{} MQ发送成功 ", dataType);
                })
        );
    }

    /**
     * 刷新全部基本参数的缓存
     *
     * @param dataType
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public List<String> refreshBaseData(String dataType) {
        List<Long> tenantIdList = Arrays.asList(baseDataInitTenantList);
        log.info("BaseDataInitService refreshBaseData 开始同步 类型:{}", dataType);
        List<String> result = new ArrayList<>();
        tenantIdList.forEach(t ->
                refreshTasksThread.submit(() -> {
                    // FIXME: 2023/6/30 这边最后记得移除
                    SimpleTenantHelper.setTenantId(t);
                    if (dataType.equals(BaseDataSyncTypeEnums.ALL_DATA)) {
                        refreshBaseDataAll();
                    }
                    switch (dataType) {
                        case BaseDataSyncTypeEnums.COMPANY:
                            result.add(this.refreshCompany());
                            break;
                        case BaseDataSyncTypeEnums.CUSTOMS_BOOK:
                            result.add(this.refreshCustomsBook());
                            break;
                        case BaseDataSyncTypeEnums.EXPRESS:
                            result.add(this.refreshExpress());
                            break;
                        case BaseDataSyncTypeEnums.PATH:
                            result.add(this.refreshPath());
                            break;
                        case BaseDataSyncTypeEnums.PAY_CHANNEL:
                            result.add(this.refreshPayChannel());
                            break;
                        case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT:
                            result.add(this.refreshPayMerchantAccount());
                            break;
                        case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL:
                            result.add(this.refreshPayMerchantAccountChannel());
                            break;
                        case BaseDataSyncTypeEnums.ROUTE:
                            result.add(this.refreshRoute());
                            break;
                        case BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING:
                            result.add(this.refreshStatusMapping());
                            break;
                        case BaseDataSyncTypeEnums.EXCEPTIONTYPE:
                            result.add(this.refreshExceptionType());
                            break;
                        default:
                            break;
                    }
                })
        );
        return result;
    }

    private List<String> refreshBaseDataAll() {
        List<String> result = new ArrayList<>();
        result.add(this.refreshCompany());
        result.add(this.refreshCustomsBook());
        result.add(this.refreshExpress());
        result.add(this.refreshPath());
        result.add(this.refreshPayChannel());
        result.add(this.refreshPayMerchantAccount());
        result.add(this.refreshPayMerchantAccountChannel());
        result.add(this.refreshRoute());
        result.add(this.refreshStatusMapping());
        result.add(this.refreshExceptionType());
        return result;
    }


    public synchronized void initCompany() {
        if (BaseDataCacheUtil.companyCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initCompany，开始", tenantId);
        CompanySearch search = new CompanySearch();
        search.setPageSize(1000);
        search.setCurrentPage(1);
        ListVO<CompanyDTO> companyDTOListVO = companyService.paging(search);
        log.info("当前租户={} 初始化-initCompany,数据：{}", tenantId, JSON.toJSONString(companyDTOListVO.getDataList()));
        BaseDataCacheUtil.initCompanyCache(companyDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initCompany，结束", tenantId);
    }

    public synchronized void initStatusMapping() {
        if (BaseDataCacheUtil.statusMappingOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initStatusMapping，开始 当前租户={}", tenantId, SimpleTenantHelper.getTenantId());
        CustomsStatusMappingReqVO reqVO = new CustomsStatusMappingReqVO();
        reqVO.setPageSize(1000);
        reqVO.setCurrentPage(1);
        ListVO<CustomsStatusMappingResVO> paging = customsStatusMappingService.paging(reqVO);
        log.info("当前租户={} 初始化-initStatusMapping,数据：{}", tenantId, JSON.toJSONString(paging.getDataList()));
        BaseDataCacheUtil.initStatusMappingCache(paging.getDataList());
        log.info("当前租户={} 初始化-initStatusMapping，结束", tenantId);
    }

    public synchronized void initExceptionType() {
        if (BaseDataCacheUtil.exceptionTypeOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initExceptionType，开始 当前租户={}", tenantId, SimpleTenantHelper.getTenantId());
        ExceptionReqVO reqVO = new ExceptionReqVO();
        reqVO.setPageSize(1000);
        reqVO.setCurrentPage(1);
        ListVO<ExceptionResVO> paging = exceptionService.paging(reqVO);
        log.info("当前租户={} 初始化-initExceptionType,数据：{}", tenantId, JSON.toJSONString(paging.getDataList()));
        BaseDataCacheUtil.initExceptionCache(paging.getDataList());
        log.info("当前租户={} 初始化-initExceptionType，结束", tenantId);
    }


    @Transactional(rollbackFor = Exception.class)
    public String refreshCompany() {
        BaseDataCacheUtil.removeCompany();
        this.initCompany();
        return "刷新缓存 initCompany 成功";
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshStatusMapping() {
        BaseDataCacheUtil.removeStatusMapping();
        this.initStatusMapping();
        return "刷新缓存 refreshStatusMapping 成功";
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshExceptionType() {
        BaseDataCacheUtil.removeException();
        this.initExceptionType();
        return "刷新缓存 refreshException 成功";
    }

    public synchronized void initPayChannel() {
        if (BaseDataCacheUtil.payChannelCodeCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initPayChannel，开始 当前租户={}", tenantId, SimpleTenantHelper.getTenantId());
        PayChannelSearch search = new PayChannelSearch();
        search.setPageSize(100);
        search.setCurrentPage(1);
        ListVO<PayChannelDTO> payChannelDTOListVO = payChannelService.paging(search);
        log.info("当前租户={} 初始化-initPayChannel,数据：{}", tenantId, JSON.toJSONString(payChannelDTOListVO.getDataList()));
        BaseDataCacheUtil.initPayChannelCache(payChannelDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initPayChannel，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshPayChannel() {
        BaseDataCacheUtil.removePayChannel();
        this.initPayChannel();
        return "刷新缓存 initPayChannel成功";
    }

    public synchronized void initCustomsBook() {
        if (BaseDataCacheUtil.customsBookIdCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initCustomsBook，开始", tenantId);
        CustomsBookSearchCondition search = new CustomsBookSearchCondition();
        search.setPageSize(500);
        search.setCurrentPage(1);
        ListVO<CustomsBookDTO> customsBookDTOListVO = customsBookService.paging(search);
        log.info("当前租户={} 初始化-initCustomsBook,数据：{}", tenantId, JSON.toJSONString(customsBookDTOListVO.getDataList()));
        BaseDataCacheUtil.initCustomsBook(customsBookDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initCustomsBook，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshCustomsBook() {
        BaseDataCacheUtil.removeCustomsBook();
        this.initCustomsBook();
        return "刷新缓存 initCustomsBook成功";
    }

    public synchronized void initExpress() {
        if (BaseDataCacheUtil.expressCodeCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initExpress，开始", tenantId);
        ExpressSearch search = new ExpressSearch();
        search.setPageSize(100);
        search.setCurrentPage(1);
        ListVO<ExpressDTO> expressDTOListVO = expressService.paging(search);
        log.info("当前租户={} 初始化-initExpress,数据：{}", tenantId, JSON.toJSONString(expressDTOListVO.getDataList()));
        BaseDataCacheUtil.initExpress(expressDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initExpress，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshExpress() {
        BaseDataCacheUtil.removeExpress();
        this.initExpress();
        return "刷新缓存 initExpress成功";
    }

    public synchronized void initRoute() {
        if (BaseDataCacheUtil.routeCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initRoute，开始", tenantId);
        RouteSearch search = new RouteSearch();
        search.setPageSize(1000);
        search.setCurrentPage(1);
        ListVO<RouteDTO> routeDTOListVO = routeService.paging(search);
        log.info("当前租户={} 初始化-initRoute,数据：{}", tenantId, JSON.toJSONString(routeDTOListVO.getDataList()));
        BaseDataCacheUtil.initRoute(routeDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initRoute，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshRoute() {
        BaseDataCacheUtil.removeRoute();
        this.initRoute();
        return "刷新缓存 initRoute成功";
    }

    public synchronized void initPath() {
        if (BaseDataCacheUtil.pathCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initPath，开始", tenantId);
        PathSearch search = new PathSearch();
        search.setPageSize(1000);
        search.setCurrentPage(1);
        ListVO<PathDTO> pathDTOListVO = pathService.paging(search);
        log.info("当前租户={} 初始化-initPath,数据：{}", tenantId, JSON.toJSONString(pathDTOListVO.getDataList()));
        BaseDataCacheUtil.initPath(pathDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initPath，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshPath() {
        BaseDataCacheUtil.removePath();
        this.initPath();
        return "刷新缓存 initPath成功";
    }

    public synchronized void initPayMerchantAccount() {
        if (BaseDataCacheUtil.payMerchantAccountCodeCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initPayMerchantAccount，开始", tenantId);
        PayMerchantAccountSearch search = new PayMerchantAccountSearch();
        search.setPageSize(500);
        search.setCurrentPage(1);
        ListVO<PayMerchantAccountDTO> payMerchantAccountDTOListVO = payMerchantAccountService.paging(search);
        log.info("当前租户={} 初始化-initPayMerchantAccount,数据：{}", tenantId, JSON.toJSONString(payMerchantAccountDTOListVO.getDataList()));
        BaseDataCacheUtil.initPayMerchantAccount(payMerchantAccountDTOListVO.getDataList());
        log.info("当前租户={} 初始化-initPayMerchantAccount，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshPayMerchantAccount() {
        BaseDataCacheUtil.removePayMerchantAccount();
        this.initPayMerchantAccount();
        return "刷新缓存 initPayMerchantAccount成功";
    }

    public synchronized void initPayMerchantAccountChannel() {
        if (BaseDataCacheUtil.payMerchantAccountChannelCodeCacheOk()) {
            return;
        }
        Long tenantId = SimpleTenantHelper.getTenantId();
        log.info("当前租户={} 初始化-initPayMerchantAccountChannel，开始", tenantId);
        PayMerchantAccountChannelSearch search = new PayMerchantAccountChannelSearch();
        search.setPageSize(500);
        search.setCurrentPage(1);
        ListVO<PayMerchantAccountChannelDTO> PayMerchantAccountChannel = payMerchantAccountChannelService.paging(search);
        log.info("当前租户={} 初始化-initPayMerchantAccountChannel,数据：{}", tenantId, JSON.toJSONString(PayMerchantAccountChannel.getDataList()));
        BaseDataCacheUtil.initinitPayMerchantAccountChannel(PayMerchantAccountChannel.getDataList());
        log.info("当前租户={} 初始化-initPayMerchantAccountChannel，结束", tenantId);
    }

    @Transactional(rollbackFor = Exception.class)
    public String refreshPayMerchantAccountChannel() {
        BaseDataCacheUtil.removePayMerchantAccountChannel();
        this.initPayMerchantAccountChannel();
        return "刷新缓存 initPayMerchantAccountChannel成功";
    }

    /**
     * 提供查看参数的方法
     */
    public List<String> getData() {
        String companyIdCache = BaseDataCacheUtil.getCompanyIdCache();
        String companyCodeCache = BaseDataCacheUtil.getCompanyCodeCache();
        String payChannelCache = BaseDataCacheUtil.getPayChannelCache();
        String customsBookCache = BaseDataCacheUtil.getCustomsBookCache();
        String expressCache = BaseDataCacheUtil.getExpressCache();
        String routeIdCache = BaseDataCacheUtil.getRouteIdCache();
        String routeCodeCache = BaseDataCacheUtil.getRouteCodeCache();
        String pathCache = BaseDataCacheUtil.getPathCache();
        String payMerchantAccountCache = BaseDataCacheUtil.getPayMerchantAccountCache();
        String payMerchantAccountChannelCache = BaseDataCacheUtil.getPayMerchantAccountChannelCache();
        ArrayList<String> list = new ArrayList<>();
        list.add(companyIdCache);
        list.add(companyCodeCache);
        list.add(payChannelCache);
        list.add(customsBookCache);
        list.add(expressCache);
        list.add(routeIdCache);
        list.add(routeCodeCache);
        list.add(pathCache);
        list.add(payMerchantAccountCache);
        list.add(payMerchantAccountChannelCache);
        return list;
    }

    public String getCompanyIdCache() {
        String companyIdCache = BaseDataCacheUtil.getCompanyIdCache();
        return companyIdCache;
    }

    public String getCompanyCodeCache() {
        String companyCodeCache = BaseDataCacheUtil.getCompanyCodeCache();
        return companyCodeCache;
    }

    public String getPayChannel() {
        String payChannelCache = BaseDataCacheUtil.getPayChannelCache();
        return payChannelCache;
    }

    public String getCustomsBookCache() {
        String customsBookCache = BaseDataCacheUtil.getCustomsBookCache();
        return customsBookCache;
    }

    public String getExpressCache() {
        String expressCache = BaseDataCacheUtil.getExpressCache();
        return expressCache;
    }

    public String getStatusMapping() {
        String statusMapping = BaseDataCacheUtil.getStatusMapping();
        return statusMapping;
    }


    public String getRouteIdCache() {
        String routeIdCache = BaseDataCacheUtil.getRouteIdCache();
        return routeIdCache;
    }

    public String getRouteCodeCache() {
        String routeCodeCache = BaseDataCacheUtil.getRouteCodeCache();
        return routeCodeCache;
    }

    public String getPathCache() {
        String pathCache = BaseDataCacheUtil.getPathCache();
        return pathCache;
    }

    public String getPayMerchantAccountCache() {
        String payMerchantAccountCache = BaseDataCacheUtil.getPayMerchantAccountCache();
        return payMerchantAccountCache;
    }

    public String getPayMerchantAccountChannelCache() {
        String payMerchantAccountChannelCache = BaseDataCacheUtil.getPayMerchantAccountChannelCache();
        return payMerchantAccountChannelCache;
    }

    /**
     * canal监听mq 新增消息
     *
     * @param baseDataSyncDTO
     */
    public void insertData(BaseDataSyncDTO baseDataSyncDTO) {
        if (Objects.isNull(baseDataSyncDTO)) {
            log.info("BaseDataInitService insertData object为空");
            return;
        }
        log.info("BaseDataInitService insertData type={} object={}", baseDataSyncDTO.getDataType(), JSON.toJSONString(baseDataSyncDTO));
        insertDataCore(baseDataSyncDTO.getDataType(), baseDataSyncDTO.getData());
    }

    private void insertDataCore(String dataType, Object object) {
        String json = JSON.toJSONString(object);
        switch (dataType) {
            case BaseDataSyncTypeEnums.COMPANY:
                CompanyDTO companyDTO = JSON.parseObject(json, CompanyDTO.class);
                BaseDataCacheUtil.insertCompany(companyDTO);
                break;
            case BaseDataSyncTypeEnums.CUSTOMS_BOOK:
                CustomsBookDTO customsBookDTO = JSON.parseObject(json, CustomsBookDTO.class);
                BaseDataCacheUtil.insertCustomsBook(customsBookDTO);
                break;
            case BaseDataSyncTypeEnums.EXPRESS:
                ExpressDTO expressDTO = JSON.parseObject(json, ExpressDTO.class);
                BaseDataCacheUtil.insertExpress(expressDTO);
                break;
            case BaseDataSyncTypeEnums.PATH:
                PathDTO pathDTO = JSON.parseObject(json, PathDTO.class);
                BaseDataCacheUtil.insertPath(pathDTO);
                break;
            case BaseDataSyncTypeEnums.PAY_CHANNEL:
                PayChannelDTO payChannelDTO = JSON.parseObject(json, PayChannelDTO.class);
                BaseDataCacheUtil.insertPayChannel(payChannelDTO);
                break;
            case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT:
                PayMerchantAccountDTO payMerchantAccountDTO = JSON.parseObject(json, PayMerchantAccountDTO.class);
                BaseDataCacheUtil.insertPayMerchantAccount(payMerchantAccountDTO);
                break;
            case BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL:
                PayMerchantAccountChannelDTO payMerchantAccountChannelDTO = JSON.parseObject(json, PayMerchantAccountChannelDTO.class);
                BaseDataCacheUtil.insertPayMerchantAccountChannel(payMerchantAccountChannelDTO);
                break;
            case BaseDataSyncTypeEnums.ROUTE:
                RouteDTO routeDTO = JSON.parseObject(json, RouteDTO.class);
                BaseDataCacheUtil.insertRoute(routeDTO);
                break;
            case BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING:
                CustomsStatusMappingDTO mappingDTO = JSON.parseObject(json, CustomsStatusMappingDTO.class);
                BaseDataCacheUtil.insertStatusMapping(mappingDTO);
                break;
            case BaseDataSyncTypeEnums.EXCEPTIONTYPE:
                ExceptionDTO exceptionDTO = JSON.parseObject(json, ExceptionDTO.class);
                BaseDataCacheUtil.insertException(exceptionDTO);
                break;
            default:
                break;
        }
    }

    /**
     * 更新 广播
     *
     * @param dataType
     * @param object
     */
    public void updateDataProxy(String dataType, Object object) {
        BaseDataSyncDTO baseDataSyncDTO = new BaseDataSyncDTO();
        baseDataSyncDTO.setChangeType(BaseDataSyncTypeEnums.UPDATE).setDataType(dataType).setData(object);
        messageSender.sendMsg(baseDataSyncDTO, BaseDataSyncTypeEnums.SYNC_TOPIC);
        log.info("BaseDataInitService refreshBaseData 更新数据 类型:{} MQ发送成功 ", dataType);
    }

    public void updateData(BaseDataSyncDTO baseDataSyncDTO) {
        if (Objects.isNull(baseDataSyncDTO)) {
            log.info("BaseDataInitService updateData object为空");
            return;
        }
        log.info("BaseDataInitService updateData type={} object={}", baseDataSyncDTO.getDataType(), JSON.toJSONString(baseDataSyncDTO));
        if (Objects.equals(baseDataSyncDTO.getDataType(), BaseDataSyncTypeEnums.PATH)) {
            String json = JSON.toJSONString(baseDataSyncDTO.getData());
            PathDTO pathDTO = JSON.parseObject(json, PathDTO.class);
            BaseDataCacheUtil.updatePath(pathDTO, pathDTO);
        } else {
            log.info("BaseDataInitService updateData 可以直接走insert逻辑");
            this.insertDataCore(baseDataSyncDTO.getDataType(), baseDataSyncDTO.getData());
        }
    }

    public void updateData(String dataType, Object object) {
        if (Objects.isNull(object)) {
            log.info("BaseDataInitService updateData object为空");
            return;
        }
        log.info("BaseDataInitService updateData type={} object={}", object.getClass().getName(), JSON.toJSONString(object));
        if (Objects.equals(dataType, BaseDataSyncTypeEnums.PATH)) {
            String json = JSON.toJSONString(object);
            PathDTO pathDTO = JSON.parseObject(json, PathDTO.class);
            BaseDataCacheUtil.updatePath(pathDTO, pathDTO);
        } else {
            this.insertDataCore(dataType, object);
        }
    }

    /**
     * 删除 mq转发广播
     *
     * @param dataType
     * @param object
     * @Descibe 好像用不到dataType 懒得改了
     */
    public void deleteDataProxy(String dataType, Object object) {
        BaseDataSyncDTO baseDataSyncDTO = new BaseDataSyncDTO();
        baseDataSyncDTO.setChangeType(BaseDataSyncTypeEnums.DELETE).setDataType(dataType).setData(object);
        messageSender.sendMsg(baseDataSyncDTO, BaseDataSyncTypeEnums.SYNC_TOPIC);
        log.info("BaseDataInitService refreshBaseData 删除数据 类型:{} MQ发送成功 ", dataType);
    }

    /**
     * 删除消息
     *
     * @param object
     */
    public void deleteData(Object object) {
        if (Objects.isNull(object)) {
            log.info("BaseDataInitService deleteData object为空");
            return;
        }
        log.info("BaseDataInitService type={} object={}", object.getClass().getName(), JSON.toJSONString(object));
        if (object instanceof CompanyDTO) {
            BaseDataCacheUtil.deleteCompany((CompanyDTO) object);
        } else if (object instanceof PayChannelDTO) {
            BaseDataCacheUtil.deletePayChannel((PayChannelDTO) object);
        } else if (object instanceof CustomsBookDTO) {
            BaseDataCacheUtil.deleteCustomsBook((CustomsBookDTO) object);
        } else if (object instanceof ExpressDTO) {
            BaseDataCacheUtil.deleteExpress((ExpressDTO) object);
        } else if (object instanceof RouteDTO) {
            BaseDataCacheUtil.deleteRoute((RouteDTO) object);
        } else if (object instanceof PathDTO) {
            BaseDataCacheUtil.deletePath((PathDTO) object);
        } else if (object instanceof PayMerchantAccountDTO) {
            BaseDataCacheUtil.deletePayMerchantAccount((PayMerchantAccountDTO) object);
        } else if (object instanceof PayMerchantAccountChannelDTO) {
            BaseDataCacheUtil.deletePayMerchantAccountChannel((PayMerchantAccountChannelDTO) object);
        }
    }


}
