package com.danding.cds.order.base.data.mq;

import java.util.Objects;

import org.apache.rocketmq.spring.annotation.MessageModel;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.danding.cds.bean.dto.BaseDataSyncDTO;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.order.base.data.service.BaseDataInitService;
import com.danding.logistics.mq.common.handler.MessageHandler;

import lombok.extern.slf4j.Slf4j;

/**
 * @program: cds-center
 * @description: 基础数据更新，广播的形式接收
 * @author: 潘本乐（Belep）
 * @create: 2021-10-16 21:42
 **/
@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-base-data-sync-topic-consumer",
        topic = "ccs-base-data-sync-topic",
        messageModel = MessageModel.BROADCASTING
)
public class BaseDataConsumer extends MessageHandler {

    @Autowired
    private BaseDataInitService baseDataInitService;

    @Override
    public void handle(Object o) throws RuntimeException {
        BaseDataSyncDTO syncDTO = (BaseDataSyncDTO) o;
        if (Objects.equals(syncDTO.getChangeType(), BaseDataSyncTypeEnums.INSERT)) {
            baseDataInitService.insertData(syncDTO);
        } else if (Objects.equals(syncDTO.getChangeType(), BaseDataSyncTypeEnums.UPDATE)) {
            baseDataInitService.updateData(syncDTO);
        } else if (Objects.equals(syncDTO.getChangeType(),BaseDataSyncTypeEnums.REFRESH)){
            baseDataInitService.refreshBaseData(syncDTO.getDataType());
        } else if (Objects.equals(syncDTO.getChangeType(),BaseDataSyncTypeEnums.DELETE)){
            baseDataInitService.deleteData(syncDTO.getData());
        }
    }

}