package com.danding.cds.order.base.data.bean;

import java.util.List;
import java.util.Map;
import java.util.Vector;
import java.util.concurrent.ConcurrentHashMap;

import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.route.api.dto.RouteDTO;

import lombok.Data;

/**
 * <AUTHOR>
 * @description: 基础数据map集合对象
 * @date 2022/12/12 16:49
 */
@Data
public class BaseDataMapDTO {

    /**
     * company缓存，key:主键ID;value:数据
     */
    private Map<Long, CompanyDTO> companyIdCache = new ConcurrentHashMap<>();

    /**
     * CustomsStatusMapping缓存，key：主键id;value:数据
     */
    private Map<Long, CustomsStatusMappingDTO> customsStatusMappingDTOCache = new ConcurrentHashMap<>();

    private Map<String, CustomsStatusMappingDTO> customsStatusMappingDTOCodeCache = new ConcurrentHashMap<>();

    /**
     * ExceptionType缓存，key:主键；value:数据
     */
    private Map<Long, ExceptionDTO> exceptionDTOCache = new ConcurrentHashMap<>();
    /**
     * company缓存，key:code;value:数据
     */
    private Map<String, CompanyDTO> companyCodeCache = new ConcurrentHashMap<>();

    /**
     * paychannel缓存，key:code;value:数据
     */
    private Map<String, PayChannelDTO> payChannelCodeCache = new ConcurrentHashMap<>();

    private Map<Long, PayChannelDTO> payChannelIdCache = new ConcurrentHashMap<>();
    /**
     * 账册缓存，key:主键ID;value:数据
     */
    private Map<Long, CustomsBookDTO> customsBookIdCache = new ConcurrentHashMap<>();
    private Map<String, CustomsBookDTO> customsBookNoCache = new ConcurrentHashMap<>();

    /**
     * 账册快递，key:code;value:数据
     */
    private Map<String, ExpressDTO> expressCodeCache = new ConcurrentHashMap<>();
    /**
     * key:id;value:数据
     */
    private Map<Long, ExpressDTO> expressIdCache = new ConcurrentHashMap<>();

    /**
     * 路径，key:code;value:数据
     */
    private Map<String, RouteDTO> routeCodeCache = new ConcurrentHashMap<>();
    /**
     * 路径，key:id;value:数据
     */
    private Map<Long, RouteDTO> routeIdCache = new ConcurrentHashMap<>();

    /**
     * 路由，
     */
    private List<PathDTO> pathListCache = new Vector<>();

    /**
     * 初始化，支付商户,key :商户编码，value:数据
     */
    private Map<String, PayMerchantAccountDTO> payMerchantAccountCodeCache = new ConcurrentHashMap<>();
    /**
     * 初始化，支付商户渠道,key :商户id+"#"+channel，value:数据
     */
    private Map<String, PayMerchantAccountChannelDTO> payMerchantAccountChannelCodeCache = new ConcurrentHashMap<>();

}
