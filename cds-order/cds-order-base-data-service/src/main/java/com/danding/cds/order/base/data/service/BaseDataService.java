package com.danding.cds.order.base.data.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.bean.dto.BaseDataSyncTypeEnums;
import com.danding.cds.c.api.service.CustomsStatusMappingService;
import com.danding.cds.c.api.service.ExceptionService;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.exception.api.dto.ExceptionDTO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.base.data.util.BaseDataCacheUtil;
import com.danding.cds.path.api.dto.PathDTO;
import com.danding.cds.path.api.service.PathService;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Create 2021/10/23  15:10
 * @Describe 用于数据的取用 获取缓存的数据或者从数据库中获取
 **/
@Slf4j
@Service
public class BaseDataService {
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private RouteService routeService;
    @DubboReference
    private PathService pathService;
    @DubboReference
    private PayChannelService payChannelService;
    @DubboReference
    private PayMerchantAccountService payMerchantAccountService;
    @DubboReference
    private PayMerchantAccountChannelService payMerchantAccountChannelService;
    @DubboReference
    private CustomsBookService customsBookService;
    @Autowired
    private BaseDataInitService baseDataInitService;
    @Resource
    private ExceptionService exceptionService;

    @Resource
    private CustomsStatusMappingService mappingService;

    public CustomsStatusMappingDTO getCustomsStatusMappingDTOById(Long id) {
        if (id == null) {
            return null;
        }
        CustomsStatusMappingDTO mappingDTO = BaseDataCacheUtil.getCustomsStatusMappingDTOById(id);
        if (Objects.isNull(mappingDTO)) {
            mappingDTO = mappingService.findById(id);
            if (mappingDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING, mappingDTO);
                log.info("BaseDataService getCustomsStatusMappingDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(mappingDTO));

            }
        }
        return mappingDTO;
    }

    public CustomsStatusMappingDTO getCustomsStatusMappingDTOByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        CustomsStatusMappingDTO mappingDTO = BaseDataCacheUtil.getCustomsStatusMappingDTOByCode(code);
        if (Objects.isNull(mappingDTO)) {
            mappingDTO = mappingService.findByCode(code);
            if (mappingDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.CUSTOMS_STATUS_MAPPING, mappingDTO);
                log.info("BaseDataService getCustomsStatusMappingDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(mappingDTO));

            }
        }
        return mappingDTO;
    }

    public List<ExceptionDTO> getExceptionDTOByUseTag(Integer useTag) {
        if (useTag == null) {
            return null;
        }
        List<ExceptionDTO> exceptionDTOList = BaseDataCacheUtil.getExceptionDTOByUseTag(useTag);
        if (CollectionUtils.isEmpty(exceptionDTOList)) {
            exceptionDTOList = exceptionService.findByUseTag(useTag);
            if (exceptionDTOList != null) {
                // todo
                log.info("BaseDataService getExceptionDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(exceptionDTOList));
            }
        }
        return exceptionDTOList;

    }

    public ExceptionDTO getExceptionDTOById(Long id) {
        if (id == null) {
            return null;
        }
        ExceptionDTO exceptionDTO = BaseDataCacheUtil.getExceptionDTOById(id);
        if (Objects.isNull(exceptionDTO)) {
            exceptionDTO = exceptionService.findById(id);
            if (exceptionDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.EXCEPTIONTYPE, exceptionDTO);
                log.info("BaseDataService getExceptionDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(exceptionDTO));

            }
        }
        return exceptionDTO;
    }

    public CompanyDTO getCompanyDTOById(Long ebpId) {
        if (ebpId == null) {
            return null;
        }
        CompanyDTO companyDTO = BaseDataCacheUtil.getCompanyDTOById(ebpId);
        if (Objects.isNull(companyDTO)) {
            companyDTO = companyService.findById(ebpId);
            if (companyDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.COMPANY, companyDTO);
                log.info("BaseDataService getCompanyDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(companyDTO));
            }
        }
        return companyDTO;
    }

    /**
     * 获取跨境进口统一编码的企业信息
     *
     * @param companyId
     * @return
     */
    public CompanyDTO getUnifiedCrossCodeCompanyById(Long companyId) {
        if (companyId == null) {
            return null;
        }
        CompanyDTO companyDTO = getCompanyDTOById(companyId);
        //将跨境进口统一编码code覆盖原始code
        if (Objects.nonNull(companyDTO)) {
            if (Objects.nonNull(companyDTO.getUnifiedCrossBroderCode())) {
                CompanyDTO newCompany = ConvertUtil.beanConvert(companyDTO, CompanyDTO.class);
                newCompany.setCode(companyDTO.getUnifiedCrossBroderCode());
                return newCompany;
            }
        }
        return companyDTO;
    }

    /**
     * 获取跨境进口统一编码的企业信息
     *
     * @param companyId
     * @return
     */
    public CompanyDTO getSpecialClientCodeCompanyById(Long companyId) {
        if (companyId == null) {
            return null;
        }
        CompanyDTO companyDTO = getCompanyDTOById(companyId);
        //使用specialClientCode覆盖原始code
        if (Objects.nonNull(companyDTO)) {
            if (Objects.nonNull(companyDTO.getSpecialClientCode())) {
                CompanyDTO newCompany = ConvertUtil.beanConvert(companyDTO, CompanyDTO.class);
                newCompany.setCode(companyDTO.getSpecialClientCode());
                return newCompany;
            }
        }
        return companyDTO;
    }

    public CompanyDTO getFbCompanyDTOById(Long ebpId) {
        CompanyDTO companyDTO = this.getCompanyDTOById(ebpId);
        if (Objects.nonNull(companyDTO)) {
            if ("3307680004".equals(companyDTO.getCode())) {
                CompanyDTO newCompany = ConvertUtil.beanConvert(companyDTO, CompanyDTO.class);
                newCompany.setCode("3307660066");
                return newCompany;
            }
            if (Objects.nonNull(companyDTO.getFbCode())) {
                CompanyDTO newCompany = ConvertUtil.beanConvert(companyDTO, CompanyDTO.class);
                newCompany.setCode(companyDTO.getFbCode());
                return newCompany;
            }
        }
        return companyDTO;
    }

    @Deprecated
    public CompanyDTO getCompanyDTOByCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        CompanyDTO companyDTO = BaseDataCacheUtil.getCompanyDTOByCode(code);
        if (Objects.isNull(companyDTO)) {
            companyDTO = companyService.findByCode(code);
            if (companyDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.COMPANY, companyDTO);
                log.info("BaseDataService getCompanyDTOByCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(companyDTO));
            }
        }
        return companyDTO;
    }

    public CompanyDTO getCompanyDTOByUnifiedCrossBroderCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        CompanyDTO companyDTO = BaseDataCacheUtil.getCompanyDTOByUnifiedCrossBroderCode(code);
        if (Objects.isNull(companyDTO)) {
            companyDTO = companyService.findByUnifiedCrossBroderCode(code);
            if (companyDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.COMPANY, companyDTO);
                log.info("BaseDataService getCompanyDTOByUnifiedCrossBroderCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(companyDTO));
            }
        }
        return companyDTO;
    }

    public CompanyDTO getCompanyDTOBySpecialClientCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        CompanyDTO companyDTO = BaseDataCacheUtil.getCompanyDTOBySpecialClientCode(code);
        if (Objects.isNull(companyDTO)) {
            companyDTO = companyService.findByCode(code);
            if (companyDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.COMPANY, companyDTO);
                log.info("BaseDataService getCompanyDTOByUnifiedCrossBroderCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(companyDTO));
            }
        }
        return companyDTO;
    }

    public CompanyDTO getCompanyDTOByFbCode(String code) {
        if (Objects.isNull(code)) {
            return null;
        }
        CompanyDTO companyDTO = BaseDataCacheUtil.getCompanyDTOByFbCode(code);
        if (Objects.isNull(companyDTO)) {
            companyDTO = companyService.findByCode(code);
            if (companyDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.COMPANY, companyDTO);
                log.info("BaseDataService getCompanyDTOByUnifiedCrossBroderCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(companyDTO));
            }
        }
        return companyDTO;
    }

    public ExpressDTO getExpressDTOByCode(String expressCode) {
        if (expressCode == null) {
            return null;
        }
        ExpressDTO expressDTO = BaseDataCacheUtil.getExpressDTOByCode(expressCode);
        if (Objects.isNull(expressDTO)) {
            expressDTO = expressService.findByCode(expressCode);
            if (expressDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.EXPRESS, expressDTO);
                log.info("BaseDataService getExpressDTOByCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(expressDTO));
            }
        }
        return expressDTO;
    }

    public ExpressDTO getExpressDTOById(Long expressId) {
        if (Objects.isNull(expressId)) {
            return null;
        }
        ExpressDTO expressDTO = BaseDataCacheUtil.getExpressDTOById(expressId);
        if (Objects.isNull(expressDTO)) {
            expressDTO = expressService.findById(expressId);
            if (expressDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.EXPRESS, expressDTO);
                log.info("BaseDataService getExpressDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(expressDTO));
            }
        }
        return expressDTO;
    }

    public RouteDTO getRouteDTOByCode(String routeCode) {
        if (routeCode == null) {
            return null;
        }

        RouteDTO routeDTO = BaseDataCacheUtil.getRouteDTOByCode(routeCode);
        if (Objects.isNull(routeDTO)) {
            routeDTO = routeService.findByCode(routeCode);
            if (routeDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.ROUTE, routeDTO);
                log.info("BaseDataService getRouteDTOByCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(routeDTO));
            }
        }
        return routeDTO;
    }

    public RouteDTO getRouteDTOById(Long routeId) {
        if (routeId == null) {
            return null;
        }

        RouteDTO routeDTO = BaseDataCacheUtil.getRouteDTOById(routeId);
        if (Objects.isNull(routeDTO)) {
            routeDTO = routeService.findById(routeId);
            if (routeDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.ROUTE, routeDTO);
                log.info("BaseDataService getRouteDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(routeDTO));
            }
        }
        return routeDTO;
    }

    public PathDTO getPathDTOByIdentify(String firstIdentify, String secondIdentify, String thirdIdentify) {

        PathDTO pathDTO = BaseDataCacheUtil.getPathDTOByIdentify(firstIdentify, secondIdentify, thirdIdentify);
        if (Objects.isNull(pathDTO)) {
            pathDTO = pathService.findByIdentify(firstIdentify, secondIdentify, thirdIdentify);
            if (pathDTO == null) {
                pathDTO = pathService.findByIdentify(firstIdentify, secondIdentify, null);
            }
            if (pathDTO == null) {
                pathDTO = pathService.findByIdentify(firstIdentify, null, null);
            }
            if (pathDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.PATH, pathDTO);
                log.info("BaseDataService getPathDTOByIdentify 缓存为空 添加初始化,数据：{}", JSON.toJSONString(pathDTO));
            }
        }
        return pathDTO;
    }

    public PayChannelDTO getPayChannelDTOByCode(String payChannel) {
        if (StringUtils.isEmpty(payChannel)) {
            return null;
        }

        PayChannelDTO payChannelDTOByCode = BaseDataCacheUtil.getPayChannelDTOByCode(payChannel);
        if (Objects.isNull(payChannelDTOByCode)) {
            payChannelDTOByCode = payChannelService.findByCode(payChannel);
            if (payChannelDTOByCode != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.PAY_CHANNEL, payChannelDTOByCode);
                log.info("BaseDataService getPayChannelDTOByCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(payChannelDTOByCode));
            }
        }
        return payChannelDTOByCode;
    }

    public PayChannelDTO getPayChannelDTOById(Long payChannelId) {
        if (StringUtils.isEmpty(payChannelId)) {
            return null;
        }

        PayChannelDTO payChannelDTOById = BaseDataCacheUtil.getPayChannelDTOById(payChannelId);
        if (Objects.isNull(payChannelDTOById)) {
            payChannelDTOById = payChannelService.findById(payChannelId);
            if (payChannelDTOById != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.PAY_CHANNEL, payChannelDTOById);
                log.info("BaseDataService getPayChannelDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(payChannelDTOById));
            }
        }
        return payChannelDTOById;
    }

    public PayMerchantAccountDTO getPayMerchantAccountDTOByCode(String merchantCode) {
        if (StringUtils.isEmpty(merchantCode)) {
            return null;
        }
        PayMerchantAccountDTO payMerchantAccountDTO = BaseDataCacheUtil.getPayMerchantAccountDTOByCode(merchantCode);
        if (Objects.isNull(payMerchantAccountDTO)) {
            payMerchantAccountDTO = payMerchantAccountService.findBySn(merchantCode);
            if (payMerchantAccountDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT, payMerchantAccountDTO);
                log.info("BaseDataService getPayMerchantAccountDTOByCode 缓存为空 添加初始化,数据：{}", JSON.toJSONString(payMerchantAccountDTO));
            }
        }
        return payMerchantAccountDTO;
    }

    public PayMerchantAccountChannelDTO getPayMerchantAccountChannelDTOByChannel(Long id, String payChannel) {
        PayMerchantAccountChannelDTO payMerchantAccountChannelDTO = BaseDataCacheUtil.getPayMerchantAccountChannelDTOByChannel(id, payChannel);
        if (Objects.isNull(payMerchantAccountChannelDTO)) {
            payMerchantAccountChannelDTO = payMerchantAccountChannelService.findByMerchantAndChannel(id, payChannel);
            if (payMerchantAccountChannelDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.PAY_MERCHANT_ACCOUNT_CHANNEL, payMerchantAccountChannelDTO);
                log.info("BaseDataService getPayMerchantAccountChannelDTOByChannel 缓存为空 添加初始化,数据：{}", JSON.toJSONString(payMerchantAccountChannelDTO));
            }
        }
        return payMerchantAccountChannelDTO;
    }


    public CustomsBookDTO getCustomsBookDTOById(Long customsBookId) {
        if (customsBookId == null) {
            return null;
        }

        CustomsBookDTO customsBookDTO = BaseDataCacheUtil.getCustomsBookDTOById(customsBookId);
        if (Objects.isNull(customsBookDTO)) {
            customsBookDTO = customsBookService.findById(customsBookId);
            if (customsBookDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.CUSTOMS_BOOK, customsBookDTO);
                log.info("BaseDataService getCustomsBookDTOById 缓存为空 添加初始化,数据：{}", JSON.toJSONString(customsBookDTO));
            }
        }
        return customsBookDTO;
    }

    public CustomsBookDTO getCustomsBookDTOByNo(String customsBookNo) {
        if (customsBookNo == null) {
            return null;
        }
        CustomsBookDTO customsBookDTO = BaseDataCacheUtil.getCustomsBookDTOByNo(customsBookNo);
        if (Objects.isNull(customsBookDTO)) {
            customsBookDTO = customsBookService.findByCode(customsBookNo);
            if (customsBookDTO != null) {
                baseDataInitService.createInitTasksThread(BaseDataSyncTypeEnums.CUSTOMS_BOOK, customsBookDTO);
                log.info("BaseDataService getCustomsBookDTOByNo 缓存为空 添加初始化,数据：{}", JSON.toJSONString(customsBookDTO));
            }
        }
        return customsBookDTO;
    }
}
