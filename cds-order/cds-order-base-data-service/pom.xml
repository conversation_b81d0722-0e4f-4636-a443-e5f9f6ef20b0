<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-order</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>cds-order-base-data-service</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-c-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-es-search-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-declare-inner-component</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
            <exclusions>
                <exclusion>
                    <groupId>com.danding</groupId>
                    <artifactId>cds-order-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

</project>