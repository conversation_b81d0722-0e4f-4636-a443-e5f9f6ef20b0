-- 模板管理表
CREATE TABLE `ccs_template_manage`
(
    `id`            bigint(20)   NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `template_name` varchar(64)  NOT NULL COMMENT '模板名称',
    `purpose`       varchar(32)  NOT NULL COMMENT '用途',
    `ports`         varchar(500) NOT NULL COMMENT '口岸（多个以逗号分隔）',
    `remark`        varchar(64)           DEFAULT NULL COMMENT '备注',
    `file_url`      varchar(500) NOT NULL COMMENT '文件URL（OSS路径）',
    `file_name`     varchar(255) NOT NULL COMMENT '文件名',
    `upload_time`   datetime     NOT NULL COMMENT '上传时间',
    `create_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`   datetime     NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `create_by`     int(11)               DEFAULT NULL COMMENT '创建人',
    `update_by`     int(11)               DEFAULT NULL COMMENT '更新人',
    `deleted`       tinyint(1)   NOT NULL DEFAULT '0' COMMENT '是否删除：0-否，1-是',
    PRIMARY KEY (`id`),
    KEY `idx_template_name` (`template_name`),
    KEY `idx_purpose` (`purpose`),
    KEY `idx_upload_time` (`upload_time`),
    KEY `idx_deleted` (`deleted`),
    KEY `idx_create_time` (`create_time`),
    KEY `idx_update_time` (`update_time`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='模板管理表';

-- 插入模板用途数据字典示例数据
INSERT INTO `ccs_customs_dictionary` (`type`, `code`, `name`, `enable`, `create_time`, `update_time`, `deleted`)
VALUES ('templatePurpose', 'IMPORT_TEMPLATE', '进口模板', 1, NOW(), NOW(), 0),
       ('templatePurpose', 'EXPORT_TEMPLATE', '出口模板', 1, NOW(), NOW(), 0),
       ('templatePurpose', 'DECLARE_TEMPLATE', '申报模板', 1, NOW(), NOW(), 0),
       ('templatePurpose', 'INVENTORY_TEMPLATE', '清单模板', 1, NOW(), NOW(), 0),
       ('templatePurpose', 'OTHER_TEMPLATE', '其他模板', 1, NOW(), NOW(), 0);
