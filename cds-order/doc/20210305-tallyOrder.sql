CREATE TABLE `ccs_inventory_order_tally_report` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `inve_order_id` bigint(20) NOT NULL COMMENT '清关单id',
    `inve_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单号',
    `tally_order_no` varchar(100) NOT NULL DEFAULT '' COMMENT '理货编号',
    `out_bound_no` varchar(100) NOT NULL DEFAULT '' COMMENT '出库单号',
    `tally_json` text NOT NULL COMMENT '理货明细json',
    `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='清关单对应理货报告';