# 2020/07/01 2020年第3季度
CREATE TABLE `ccs_customs_inventory_2020q3` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '清单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs_status` varchar(255) DEFAULT '' COMMENT '清关状态',
  `exit_region_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '出区状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流运单编号',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `pre_no` char(18) DEFAULT NULL COMMENT '预录入编号',
  `assure_company_id` bigint(20) NOT NULL COMMENT '担保企业',
  `account_book_id` bigint(20) NOT NULL COMMENT '账册ID',
  `inventory_no` varchar(30) DEFAULT NULL COMMENT '清单编号 申请单编号',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `buyer_tel_number` varchar(30) NOT NULL DEFAULT '' COMMENT '购买人电话',
  `buyer_id_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订购人证件类型',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `agent_company_id` bigint(20) NOT NULL COMMENT '申报企业',
  `area_company_id` bigint(20) NOT NULL COMMENT '区内企业',
  `fee_amount` decimal(14,4) NOT NULL COMMENT '运费',
  `insure_amount` decimal(14,4) NOT NULL COMMENT '保费',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `net_weight` decimal(14,4) NOT NULL COMMENT '净重（公斤）',
  `customs_pass_time` timestamp NULL DEFAULT NULL COMMENT '海关放行时间',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_sn` (`sn`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_inventoryno` (`inventory_no`),
  KEY `idx_eb_declareNo` (`ebc_id`,`declare_order_no`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单表';

CREATE TABLE `ccs_customs_inventory_item_2020q3` (
  `id` bigint(20) unsigned NOT NULL,
  `customs_inventory_id` bigint(20) NOT NULL COMMENT '海关清单表ID',
  `book_item_id` bigint(20) NOT NULL COMMENT '账册商品ID',
  `item_no` varchar(30) NOT NULL DEFAULT '' COMMENT '企业商品货号',
  `item_name` varchar(255) NOT NULL DEFAULT '' COMMENT '企业商品品名',
  `count` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(14,4) NOT NULL COMMENT '单价',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_inventory` (`customs_inventory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单商品表';

CREATE TABLE `ccs_customs_order_2020q3` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '订单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `pay_channel_id` bigint(20) NOT NULL COMMENT '支付方式ID',
  `pay_company_id` bigint(20) NOT NULL COMMENT '支付企业',
  `agent_company_id` bigint(20) NOT NULL COMMENT '报文传输企业',
  `declare_pay_no` varchar(64) DEFAULT NULL COMMENT '支付申报流水号',
  `trade_pay_no` varchar(64) DEFAULT NULL COMMENT '支付交易流水号',
  `freight` decimal(14,4) NOT NULL COMMENT '运费',
  `tax` decimal(14,4) NOT NULL COMMENT '税费',
  `discount` decimal(14,4) NOT NULL COMMENT '折扣 正数',
  `trade_time` timestamp(3) NULL DEFAULT NULL COMMENT '交易时间',
  `sender_name` varchar(60) NOT NULL DEFAULT '' COMMENT '发件人名称',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_name` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `consignee_tel` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人电话',
  `consignee_email` varchar(64) NOT NULL DEFAULT '' COMMENT '收件人邮箱',
  `customs_status` varchar(255) DEFAULT '' COMMENT '海关状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `item_json` varchar(4096) NOT NULL COMMENT 'json储存的商品信息',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关订单表';

CREATE TABLE `ccs_order_2020q3` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `system_global_sn` varchar(50) DEFAULT NULL COMMENT '系统全局单号',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `out_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '上游单号',
  `declare_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '申报单号',
  `status` tinyint(3) NOT NULL COMMENT '申报订单状态',
  `exception_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 清关异常标识',
  `exception_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '清关异常类型',
  `exception_detail` varchar(516) NOT NULL DEFAULT '' COMMENT '清关异常描述',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '记录版本 当前生效版本0',
  `action_json` varchar(128) NOT NULL DEFAULT '[]' COMMENT '申报项',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `finish_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ebp_order_version` (`ebp_id`,`declare_order_no`,`version`),
  KEY `idx_sn` (`sn`),
  KEY `idx_system_global_sn` (`system_global_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申报主订单';

# 2020/10/01 2020年第4季度

CREATE TABLE `ccs_customs_inventory_2020q4` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '清单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs_status` varchar(255) DEFAULT '' COMMENT '清关状态',
  `exit_region_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '出区状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流运单编号',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `pre_no` char(18) DEFAULT NULL COMMENT '预录入编号',
  `assure_company_id` bigint(20) NOT NULL COMMENT '担保企业',
  `account_book_id` bigint(20) NOT NULL COMMENT '账册ID',
  `inventory_no` varchar(30) DEFAULT NULL COMMENT '清单编号 申请单编号',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `buyer_tel_number` varchar(30) NOT NULL DEFAULT '' COMMENT '购买人电话',
  `buyer_id_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订购人证件类型',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `agent_company_id` bigint(20) NOT NULL COMMENT '申报企业',
  `area_company_id` bigint(20) NOT NULL COMMENT '区内企业',
  `fee_amount` decimal(14,4) NOT NULL COMMENT '运费',
  `insure_amount` decimal(14,4) NOT NULL COMMENT '保费',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `net_weight` decimal(14,4) NOT NULL COMMENT '净重（公斤）',
  `customs_pass_time` timestamp NULL DEFAULT NULL COMMENT '海关放行时间',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_sn` (`sn`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_inventoryno` (`inventory_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单表';

CREATE TABLE `ccs_customs_inventory_item_2020q4` (
  `id` bigint(20) unsigned NOT NULL,
  `customs_inventory_id` bigint(20) NOT NULL COMMENT '海关清单表ID',
  `book_item_id` bigint(20) NOT NULL COMMENT '账册商品ID',
  `item_no` varchar(30) NOT NULL DEFAULT '' COMMENT '企业商品货号',
  `item_name` varchar(255) NOT NULL DEFAULT '' COMMENT '企业商品品名',
  `count` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(14,4) NOT NULL COMMENT '单价',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_inventory` (`customs_inventory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单商品表';

CREATE TABLE `ccs_customs_order_2020q4` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '订单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `pay_channel_id` bigint(20) NOT NULL COMMENT '支付方式ID',
  `pay_company_id` bigint(20) NOT NULL COMMENT '支付企业',
  `agent_company_id` bigint(20) NOT NULL COMMENT '报文传输企业',
  `declare_pay_no` varchar(64) DEFAULT NULL COMMENT '支付申报流水号',
  `trade_pay_no` varchar(64) DEFAULT NULL COMMENT '支付交易流水号',
  `freight` decimal(14,4) NOT NULL COMMENT '运费',
  `tax` decimal(14,4) NOT NULL COMMENT '税费',
  `discount` decimal(14,4) NOT NULL COMMENT '折扣 正数',
  `trade_time` timestamp(3) NULL DEFAULT NULL COMMENT '交易时间',
  `sender_name` varchar(60) NOT NULL DEFAULT '' COMMENT '发件人名称',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_name` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `consignee_tel` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人电话',
  `consignee_email` varchar(64) NOT NULL DEFAULT '' COMMENT '收件人邮箱',
  `customs_status` varchar(255) DEFAULT '' COMMENT '海关状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `item_json` varchar(4096) NOT NULL COMMENT 'json储存的商品信息',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关订单表';

CREATE TABLE `ccs_order_2020q4` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `system_global_sn` varchar(50) DEFAULT NULL COMMENT '系统全局单号',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `out_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '上游单号',
  `declare_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '申报单号',
  `status` tinyint(3) NOT NULL COMMENT '申报订单状态',
  `exception_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 清关异常标识',
  `exception_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '清关异常类型',
  `exception_detail` varchar(516) NOT NULL DEFAULT '' COMMENT '清关异常描述',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '记录版本 当前生效版本0',
  `action_json` varchar(128) NOT NULL DEFAULT '[]' COMMENT '申报项',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `finish_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ebp_order_version` (`ebp_id`,`declare_order_no`,`version`),
  KEY `idx_sn` (`sn`),
  KEY `idx_system_global_sn` (`system_global_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申报主订单';

# 2020/10/13 申报单冗余子单据编号用于分表查询
ALTER TABLE ccs_order_2020q3 ADD COLUMN `customs_payment_sn` varchar(32) DEFAULT NULL COMMENT '支付单系统编号' after `sn`;
ALTER TABLE ccs_order_2020q3 ADD COLUMN `customs_order_sn` varchar(32) DEFAULT NULL COMMENT '订单系统编号' after `customs_payment_sn`;
ALTER TABLE ccs_order_2020q3 ADD COLUMN `customs_logistics_sn` varchar(32) DEFAULT NULL COMMENT '运单系统编号' after `customs_order_sn`;
ALTER TABLE ccs_order_2020q3 ADD COLUMN `customs_inventory_sn` varchar(32) DEFAULT NULL COMMENT '清单系统编号' after `customs_logistics_sn`;
ALTER TABLE ccs_order_2020q4 ADD COLUMN `customs_payment_sn` varchar(32) DEFAULT NULL COMMENT '支付单系统编号' after `sn`;
ALTER TABLE ccs_order_2020q4 ADD COLUMN `customs_order_sn` varchar(32) DEFAULT NULL COMMENT '订单系统编号' after `customs_payment_sn`;
ALTER TABLE ccs_order_2020q4 ADD COLUMN `customs_logistics_sn` varchar(32) DEFAULT NULL COMMENT '运单系统编号' after `customs_order_sn`;
ALTER TABLE ccs_order_2020q4 ADD COLUMN `customs_inventory_sn` varchar(32) DEFAULT NULL COMMENT '清单系统编号' after `customs_logistics_sn`;


# 2020/10/28 申报订单项添加索引
ALTER TABLE ccs_customs_inventory_2020q3 ADD INDEX idx_declareorderno(declare_order_no);
ALTER TABLE ccs_customs_inventory_2020q4 ADD INDEX idx_declareorderno(declare_order_no);
alter table ccs_customs_order_2020q3 add index idx_sn(sn);
alter table ccs_customs_order_2020q4 add index idx_sn(sn);


