CREATE TABLE `ccs_channel_load_info` (
                                         `id` bigint(20) unsigned NOT NULL,
                                         `channel` varchar(255) NOT NULL DEFAULT '' COMMENT '渠道',
                                         `serial_no` varchar(255) NOT NULL DEFAULT '' COMMENT '流水号',
                                         `load_order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '装载单号',
                                         `total_count` int(11) NOT NULL DEFAULT '0' COMMENT '总件数',
                                         `total_weight` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '总重量',
                                         `total_tory_num` int(11) NOT NULL DEFAULT '0' COMMENT '总托数',
                                         `license_plate` varchar(255) NOT NULL DEFAULT '' COMMENT '车牌号',
                                         `audit_status` tinyint(3) NOT NULL DEFAULT '10' COMMENT '审核状态',
                                         `audit_time` timestamp NULL DEFAULT NULL COMMENT '审核时间',
                                         `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
                                         `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
                                         `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                         `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                         `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
                                         PRIMARY KEY (`id`) USING BTREE,
                                         UNIQUE KEY `idx_loadOrderNo` (`load_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='装载信息表';

CREATE TABLE `ccs_channel_load_info_item` (
                                              `id` bigint(20) unsigned NOT NULL,
                                              `load_order_id` bigint(20) NOT NULL COMMENT '装载单id',
                                              `declare_order_no` varchar(255) NOT NULL DEFAULT '' COMMENT '申报单号',
                                              `status` tinyint(3) NOT NULL DEFAULT '10' COMMENT '核注状态',
                                              `finish_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
                                              `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
                                              `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
                                              `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
                                              `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
                                              `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
                                              PRIMARY KEY (`id`) USING BTREE,
                                              UNIQUE KEY `idx_declareOrderNo` (`declare_order_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='装载信息订单表';