CREATE TABLE `undo_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `branch_id` bigint(20) NOT NULL COMMENT '分支事务id',
  `xid` varchar(100) NOT NULL COMMENT '全局事务id',
  `context` varchar(128) NOT NULL COMMENT '撤消日志上下文，例如序列化',
  `rollback_info` longblob NOT NULL COMMENT '回滚信息',
  `log_status` int(11) NOT NULL COMMENT '0:正常状态，1:防御状态',
  `log_created` datetime NOT NULL COMMENT '创建时间',
  `log_modified` datetime NOT NULL COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ux_undo_log` (`xid`,`branch_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分布式事务模式撤消表';

CREATE TABLE `ccs_checklist` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sn` varchar(64) NOT NULL COMMENT '企业内部核放单号',
  `pre_order_no` varchar(64) DEFAULT NULL COMMENT '预录入核放单号',
  `real_order_no` varchar(64) DEFAULT NULL COMMENT '真实核放单号',
  `declare_company_id` bigint(20) NOT NULL COMMENT '清关企业ID',
  `license_plate` varchar(64) NOT NULL COMMENT '车牌号',
  `license_frame` varchar(64) NOT NULL DEFAULT '' COMMENT '车架号',
  `car_weight` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '车辆自重',
  `frame_weight` decimal(10,4) NOT NULL DEFAULT '0.0000' COMMENT '车架重',
  `account_book_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账册ID',
  `status` varchar(64) NOT NULL COMMENT '核放单状态',
  `customs_status` varchar(64) NOT NULL DEFAULT '' COMMENT '海关回执状态',
  `information_desc` varchar(510) NOT NULL DEFAULT '' COMMENT '海关回执描述',
  `type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '核放单类型',
  `ie_flag` tinyint(3) NOT NULL DEFAULT '0' COMMENT '出入区标志',
  `extra_json` text COMMENT '额外其他属性',
  `applicant` varchar(50) NOT NULL DEFAULT '' COMMENT '申请人',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_sn` (`sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核放单';

CREATE TABLE `ccs_customs_inventory_2020q2` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '清单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs_status` varchar(255) DEFAULT '' COMMENT '清关状态',
  `exit_region_status` tinyint(3) NOT NULL DEFAULT '0' COMMENT '出区状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流运单编号',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `pre_no` char(18) DEFAULT NULL COMMENT '预录入编号',
  `assure_company_id` bigint(20) NOT NULL COMMENT '担保企业',
  `account_book_id` bigint(20) NOT NULL COMMENT '账册ID',
  `inventory_no` varchar(30) DEFAULT NULL COMMENT '清单编号 申请单编号',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `buyer_tel_number` varchar(30) NOT NULL DEFAULT '' COMMENT '购买人电话',
  `buyer_id_type` varchar(20) NOT NULL DEFAULT '' COMMENT '订购人证件类型',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `agent_company_id` bigint(20) NOT NULL COMMENT '申报企业',
  `area_company_id` bigint(20) NOT NULL COMMENT '区内企业',
  `fee_amount` decimal(14,4) NOT NULL COMMENT '运费',
  `insure_amount` decimal(14,4) NOT NULL COMMENT '保费',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `net_weight` decimal(14,4) NOT NULL COMMENT '净重（公斤）',
  `customs_pass_time` timestamp NULL DEFAULT NULL COMMENT '海关放行时间',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_sn` (`sn`),
  KEY `idx_logistics_no` (`logistics_no`),
  KEY `idx_inventoryno` (`inventory_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单表';

CREATE TABLE `ccs_customs_inventory_item_2020q2` (
  `id` bigint(20) unsigned NOT NULL,
  `customs_inventory_id` bigint(20) NOT NULL COMMENT '海关清单表ID',
  `book_item_id` bigint(20) NOT NULL COMMENT '账册商品ID',
  `item_no` varchar(30) NOT NULL DEFAULT '' COMMENT '企业商品货号',
  `item_name` varchar(255) NOT NULL DEFAULT '' COMMENT '企业商品品名',
  `count` int(11) NOT NULL COMMENT '数量',
  `unit_price` decimal(14,4) NOT NULL COMMENT '单价',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_inventory` (`customs_inventory_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关清单商品表';


CREATE TABLE `ccs_customs_inventory_cancel_info` (
  `id` bigint(20) NOT NULL,
  `order_cancel_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '撤单SN',
  `lessee_no` varchar(100) NOT NULL DEFAULT '' COMMENT '租户',
  `ref_invo_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '清单相关ID',
  `ref_invo_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清单相关编号',
  `ref_order_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '相关申报单ID',
  `ref_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '相关申报单编号',
  `channel_no` varchar(50) NOT NULL DEFAULT '' COMMENT '渠道号',
  `invo_no` varchar(50) NOT NULL DEFAULT '' COMMENT '清单号',
  `mail_no` varchar(20) DEFAULT NULL COMMENT '运单号',
  `express_id` bigint(20) DEFAULT NULL COMMENT '快递公司ID',
  `customs_book_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账册ID',
  `area_company_id` bigint(20) DEFAULT NULL COMMENT '区内企业',
  `status` varchar(20) DEFAULT NULL COMMENT '撤单状态',
  `status_time` timestamp(3) NULL DEFAULT NULL COMMENT '撤单状态发生时间',
  `complete_status` varchar(10) DEFAULT NULL COMMENT '最终状态[备用]',
  `complete_time` timestamp(3) NULL DEFAULT NULL COMMENT '撤单完成时间',
  `customs_status` varchar(20) DEFAULT NULL COMMENT '海关回执状态',
  `customs_check_detail` varchar(255) DEFAULT NULL COMMENT '海关回执明细',
  `customs_last_time` timestamp(3) NULL DEFAULT NULL COMMENT '海关回执时间',
  `cancel_reason` varchar(100) DEFAULT NULL COMMENT '撤单原因',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_ref_order_id` (`ref_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='清单撤单表';

CREATE TABLE `ccs_customs_logistics` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '所属用户id',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `sn` varchar(50) NOT NULL COMMENT '系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `out_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '上游单号',
  `declare_order_no` varchar(50) NOT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `logistics_status` tinyint(3) DEFAULT NULL COMMENT '运单状态',
  `express_code` varchar(40) NOT NULL DEFAULT '' COMMENT '快递标识',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `customs_status` varchar(255) DEFAULT '' COMMENT '海关状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `finish_time` timestamp(3) NULL DEFAULT NULL COMMENT '完成时间',
  `consignee_name` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人姓名',
  `consignee_province` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人省',
  `consignee_city` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人市',
  `consignee_district` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人区',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `consignee_tel` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人电话',
  `logistics_no` varchar(50) NOT NULL DEFAULT '' COMMENT '物流运单编号',
  `fee_amount` decimal(14,4) NOT NULL COMMENT '运费',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `item_json` varchar(4096) NOT NULL COMMENT 'json储存的商品信息',
  `tags_json` varchar(1024) DEFAULT '' COMMENT '订单标签',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关运单';

CREATE TABLE `ccs_customs_order_2020q2` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '订单系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `declare_order_no` varchar(50) DEFAULT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `customs` varchar(40) NOT NULL COMMENT '关区口岸',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
  `pay_channel_id` bigint(20) NOT NULL COMMENT '支付方式ID',
  `pay_company_id` bigint(20) NOT NULL COMMENT '支付企业',
  `agent_company_id` bigint(20) NOT NULL COMMENT '报文传输企业',
  `declare_pay_no` varchar(64) DEFAULT NULL COMMENT '支付申报流水号',
  `trade_pay_no` varchar(64) DEFAULT NULL COMMENT '支付交易流水号',
  `freight` decimal(14,4) NOT NULL COMMENT '运费',
  `tax` decimal(14,4) NOT NULL COMMENT '税费',
  `discount` decimal(14,4) NOT NULL COMMENT '折扣 正数',
  `trade_time` timestamp(3) NULL DEFAULT NULL COMMENT '交易时间',
  `sender_name` varchar(60) NOT NULL DEFAULT '' COMMENT '发件人名称',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `logistics_company_id` bigint(20) NOT NULL COMMENT '物流企业',
  `buyer_id_number` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人证件号码',
  `buyer_name` varchar(60) NOT NULL DEFAULT '' COMMENT '订购人姓名',
  `consignee_name` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人姓名',
  `consignee_address` varchar(255) NOT NULL DEFAULT '' COMMENT '收件人地址',
  `consignee_tel` varchar(30) NOT NULL DEFAULT '' COMMENT '收件人电话',
  `consignee_email` varchar(64) NOT NULL DEFAULT '' COMMENT '收件人邮箱',
  `customs_status` varchar(255) DEFAULT '' COMMENT '海关状态',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次清关回执时间',
  `last_declare_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `customs_detail` varchar(255) DEFAULT NULL COMMENT '清关回执描述',
  `item_json` varchar(4096) NOT NULL COMMENT 'json储存的商品信息',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关订单表';

CREATE TABLE `ccs_customs_payment` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `user_id` bigint(20) unsigned NOT NULL COMMENT '所属用户id',
  `sn` varchar(50) NOT NULL COMMENT '支付申报系统编号',
  `order_sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `order_id` bigint(20) NOT NULL COMMENT '订单ID',
  `out_order_no` varchar(50) NOT NULL COMMENT '外部订单号',
  `declare_order_no` varchar(50) NOT NULL COMMENT '订单编号 申报单号',
  `status` tinyint(3) NOT NULL COMMENT '状态',
  `channel` varchar(50) NOT NULL COMMENT '支付渠道编码',
  `pay_channel_id` bigint(20) NOT NULL COMMENT '支付渠道ID',
  `pay_company_id` bigint(20) NOT NULL COMMENT '支付企业ID',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台ID',
  `merchant_code` varchar(60) DEFAULT NULL COMMENT '商户编码|收款账号',
  `pay_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `out_trade_no` varchar(50) NOT NULL COMMENT '外部交易流水号',
  `ver_dept` varchar(60) DEFAULT NULL COMMENT '验核机构名称',
  `buyer_name` varchar(80) NOT NULL COMMENT '订购人姓名',
  `buyer_id_type` varchar(80) NOT NULL COMMENT '订购人证件类型',
  `buyer_id_no` varchar(80) NOT NULL COMMENT '订购人身份证号',
  `customs` varchar(50) NOT NULL COMMENT '关区口岸',
  `trade_pay_no` varchar(50) NOT NULL COMMENT '第三方支付流水号',
  `declare_pay_no` varchar(64) DEFAULT NULL COMMENT '支付申报流水号',
  `pay_transaction_id` varchar(60) DEFAULT '' COMMENT '验核机构交易流水号',
  `amount` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '支付总价格',
  `tax_fee` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '物流费',
  `discount_fee` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '物流费',
  `transport_fee` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '物流费',
  `commodity_fee` decimal(14,2) NOT NULL DEFAULT '0.00' COMMENT '商品费用',
  `currency_code` varchar(20) DEFAULT NULL COMMENT '币制编码',
  `last_customs_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次回执时间',
  `last_declare_time` timestamp NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `finish_time` timestamp(3) NULL DEFAULT NULL COMMENT '完成时间',
  `tags_json` varchar(1024) DEFAULT '' COMMENT '订单标签',
  `tenant_id` varchar(32) DEFAULT NULL COMMENT '租户ID',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关支付单';

CREATE TABLE `ccs_customs_payment_declare` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `sn` varchar(50) NOT NULL COMMENT '系统编号',
  `payment_id` bigint(20) NOT NULL COMMENT '支付申报主表id',
  `payment_sn` varchar(50) NOT NULL COMMENT '支付申报主表系统编号',
  `out_request_no` varchar(50) NOT NULL COMMENT '报关流水号',
  `customs` varchar(20) NOT NULL COMMENT '海关',
  `return_msg` varchar(500) DEFAULT NULL COMMENT '申报返回信息',
  `return_time` timestamp NULL DEFAULT NULL COMMENT '回执时间',
  `extra` text COMMENT '返回的报文',
  `post_msg` text COMMENT '提交的报文',
  `status` int(2) NOT NULL COMMENT '状态',
  `create_by` bigint(20) NOT NULL COMMENT '创建人',
  `update_by` bigint(20) NOT NULL COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(2) NOT NULL COMMENT '1逻辑删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_payment_sn` (`payment_sn`) USING BTREE,
  KEY `index_payment_id` (`payment_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `ccs_customs_status_mapping` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `action` varchar(128) NOT NULL DEFAULT '' COMMENT '申报项，用于区分模块',
  `code` varchar(128) NOT NULL DEFAULT '' COMMENT '标识，各类场景根据特定规则拼凑成的唯一标识码',
  `detail` varchar(255) NOT NULL DEFAULT '' COMMENT '描述，会作为异常下拉列表的名称',
  `status` tinyint(3) NOT NULL COMMENT '申报状态',
  `exception_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否属异常',
  `note` varchar(512) NOT NULL DEFAULT '' COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `ccs_code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关申报状态映射表';

CREATE TABLE `ccs_docker_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `system_global_sn` varchar(50) DEFAULT NULL COMMENT '全局订单号',
  `out_order_no` varchar(50) NOT NULL COMMENT '外部单号',
  `out_trade_no` varchar(50) NOT NULL COMMENT '外部交易流水号',
  `declare_order_no` varchar(50) NOT NULL COMMENT '申报单号',
  `route_code` varchar(30) DEFAULT NULL COMMENT '申报路径编码',
  `logistics_no` varchar(50) DEFAULT NULL COMMENT '运单号',
  `fee_amount` decimal(14,2) DEFAULT NULL COMMENT '运费 ',
  `insure_amount` decimal(14,2) DEFAULT NULL COMMENT '保费',
  `tax_amount` decimal(14,2) DEFAULT NULL COMMENT '税费',
  `discount` decimal(14,2) DEFAULT NULL COMMENT '折扣',
  `goods_sum_amount` decimal(14,2) DEFAULT NULL COMMENT '商品总价',
  `buyer_tel_number` varchar(30) DEFAULT NULL COMMENT '购买人电话',
  `buyer_id_number` varchar(30) DEFAULT NULL COMMENT '购买人证件号码',
  `buyer_name` varchar(30) DEFAULT NULL COMMENT '订购人姓名',
  `pay_channel` varchar(30) DEFAULT NULL COMMENT '付款方式',
  `declare_pay_no` varchar(50) DEFAULT NULL COMMENT '支付申报流水号',
  `trade_time` timestamp NULL DEFAULT NULL COMMENT '交易时间',
  `sender_name` varchar(50) DEFAULT NULL COMMENT '发件人姓名',
  `pay_info_data_check_flag` int(2) DEFAULT NULL COMMENT '是否开启海关179备查(0:不开启；1:开启)',
  `trade_pay_no` varchar(50) DEFAULT NULL COMMENT '支付交易流水号',
  `pay_transaction_id` varchar(50) DEFAULT NULL COMMENT '验核机构交易流水号',
  `ver_dept` varchar(50) DEFAULT NULL COMMENT '验核机构名称',
  `pay_way` varchar(50) DEFAULT NULL COMMENT '海关订单支付方式',
  `pay_transaction_amount` decimal(14,2) DEFAULT NULL COMMENT '支付单总金额',
  `recp_code` varchar(50) DEFAULT NULL COMMENT '收款企业社会信用代码',
  `recp_name` varchar(50) DEFAULT NULL COMMENT '收款企业工商备案名称',
  `recp_account` varchar(50) DEFAULT NULL COMMENT '收款渠道下的账号',
  `pay_request_message` text COMMENT '支付请求原始数据',
  `pay_response_message` text COMMENT '支付返回原始数据',
  `orig_goods_info_json` text COMMENT '原始商品信息 一个原始商品可能对应多个发货SKU',
  `merchant_code` varchar(50) DEFAULT NULL COMMENT '支付商户编码',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC;

CREATE TABLE `ccs_endorsement` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `export_order_id` bigint(20) DEFAULT NULL COMMENT '出区单ID',
  `inventory_order_id` bigint(20) DEFAULT NULL COMMENT '清关单ID',
  `ie_flag` tinyint(3) NOT NULL DEFAULT '0' COMMENT '出入区标志',
  `bussiness_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `checklists_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '一票多车标志',
  `sn` varchar(64) NOT NULL COMMENT '企业内部核注清单编号',
  `pre_order_no` varchar(64) DEFAULT NULL COMMENT '预录入核注单号',
  `real_order_no` varchar(64) NOT NULL DEFAULT '' COMMENT '真实核注清单编号',
  `account_book_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账册ID',
  `declare_company_id` bigint(20) NOT NULL COMMENT '清关企业ID',
  `status` varchar(64) NOT NULL COMMENT '核注单状态',
  `customs_status` varchar(64) NOT NULL DEFAULT '' COMMENT '海关回执状态',
  `information_desc` varchar(510) NOT NULL DEFAULT '' COMMENT '海关回执描述',
  `extra_json` text COMMENT '额外其他属性',
  `finish_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核注清单';

CREATE TABLE `ccs_endorsement_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `checklist_id` bigint(20) DEFAULT NULL COMMENT '核放单ID',
  `endorsement_id` bigint(20) NOT NULL COMMENT '核注清单ID',
  `product_id` varchar(50) NOT NULL COMMENT '商品料号',
  `goods_seq_no` varchar(50) DEFAULT NULL COMMENT '账册项号',
  `goods_name` varchar(100) DEFAULT NULL COMMENT '料号名称',
  `record_product_name` varchar(100) DEFAULT NULL COMMENT '备案名称',
  `hs_code` varchar(50) DEFAULT NULL COMMENT 'HS代码',
  `declare_unit_qfy` decimal(10,2) DEFAULT NULL COMMENT '申报单位数量',
  `extra_json` text COMMENT '额外其他属性',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `net_weight` decimal(14,4) NOT NULL COMMENT '净重（公斤）',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='核注清单表体信息';

CREATE TABLE `ccs_export_order` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `sn` varchar(64) NOT NULL COMMENT '出区单号',
  `status` tinyint(3) NOT NULL COMMENT '出区单状态',
  `express_list` varchar(64) NOT NULL COMMENT '快递公司Id列表',
  `account_book_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账册ID',
  `declare_company_id` bigint(20) NOT NULL COMMENT '清关企业ID',
  `finish_time` timestamp NULL DEFAULT NULL COMMENT '完成时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申请出库单';

CREATE TABLE `ccs_export_item` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `export_order_id` bigint(20) NOT NULL COMMENT '出区单ID',
  `endorsement_order_id` bigint(20) DEFAULT NULL COMMENT '核注清单Id',
  `customs_inventory_sn` varchar(50) NOT NULL COMMENT '放行清单编号',
  `express_id` bigint(20) NOT NULL COMMENT '快递方式ID',
  `mail_no` varchar(64) NOT NULL COMMENT '快递编号',
  `tray_no` varchar(64) NOT NULL DEFAULT '' COMMENT '托盘号',
  `operator_no` varchar(64) NOT NULL DEFAULT '' COMMENT '操作员工号',
  `station_no` varchar(32) NOT NULL DEFAULT '' COMMENT '工作台编号',
  `biz_id` varchar(64) NOT NULL COMMENT '包裹号',
  `sku_json` varchar(4096) NOT NULL COMMENT 'SKU种类信息',
  `gross_weight` decimal(14,4) NOT NULL COMMENT '毛重（公斤）',
  `net_weight` decimal(14,4) NOT NULL COMMENT '净重（公斤）',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_customs_inventory_sn` (`customs_inventory_sn`),
  KEY `idx_export_order_id` (`export_order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='出区订单项';

CREATE TABLE `ccs_export_express` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `export_order_id` bigint(20) NOT NULL COMMENT '出区单ID',
  `express_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '快递ID',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申请出库-快递关联表';

CREATE TABLE `ccs_inventory_order_attach` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ref_inve_order_id` bigint(20) NOT NULL COMMENT '清关单ID',
  `ref_inve_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单SN',
  `attach_name` varchar(100) NOT NULL DEFAULT '' COMMENT '附件名称',
  `store_name` varchar(100) NOT NULL COMMENT '存储文件名',
  `content_type` varchar(100) NOT NULL DEFAULT '' COMMENT '文档类型',
  `attach_path` varchar(200) NOT NULL COMMENT '附件存储路径',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_inventory_order_info` (
  `id` bigint(20) NOT NULL,
  `inve_customs_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单号',
  `inve_company_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '清关企业',
  `inve_business_type` varchar(50) DEFAULT NULL COMMENT '业务类型',
  `in_account_book` varchar(50) DEFAULT NULL COMMENT '区间转出，区内转出的业务类型时[关联转入账册字段]',
  `out_account_book` varchar(50) DEFAULT NULL COMMENT '区间转入，区内转入的业务类型时[关联转出账册字段]',
  `ref_check_order_no` varchar(20) DEFAULT NULL COMMENT '核放单编号',
  `ref_hz_inve_no` varchar(20) DEFAULT NULL COMMENT '核注清单编号',
  `pick_up_no` varchar(50) DEFAULT '' COMMENT '提取号',
  `rent_person` varchar(100) NOT NULL DEFAULT '' COMMENT '租户',
  `book_id` bigint(20) NOT NULL DEFAULT '0' COMMENT '账册ID',
  `apply_person` varchar(50) DEFAULT NULL COMMENT '申请人',
  `remark` varchar(100) DEFAULT NULL COMMENT '备注',
  `status` varchar(10) NOT NULL COMMENT '清关单状态',
  `status_time` timestamp(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3) COMMENT '清关单状态对应时间',
  `enable` tinyint(3) NOT NULL DEFAULT '1' COMMENT '状态:0.停用;1.启用(默认)',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='清关单';

CREATE TABLE `ccs_inventory_order_item` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ref_inve_order_id` bigint(20) NOT NULL COMMENT '清关单ID',
  `ref_inve_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单SN',
  `is_new` varchar(5) NOT NULL DEFAULT '0' COMMENT '是否是新的(0:旧,1:新)',
  `product_id` varchar(50) NOT NULL COMMENT '商品料号',
  `goods_seq_no` varchar(50) DEFAULT NULL COMMENT '账册项号',
  `goods_name` varchar(100) DEFAULT NULL COMMENT '料号名称',
  `record_product_name` varchar(100) DEFAULT NULL COMMENT '备案名称',
  `hs_code` varchar(50) DEFAULT NULL COMMENT 'HS代码',
  `declare_unit_qfy` decimal(10,2) DEFAULT NULL COMMENT '申报单位数量',
  `declare_price` decimal(10,4) DEFAULT '0.0000' COMMENT '申报价格',
  `extra_json` text COMMENT '额外其他属性',
  `ref_endorsement_id` bigint(20) DEFAULT NULL COMMENT '关联核注清单ID',
  `ref_endorsement_sn` varchar(50) DEFAULT NULL COMMENT '关联核注清单SN',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_inventory_order_log` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ref_inve_order_id` bigint(20) NOT NULL COMMENT '清关单ID',
  `ref_inve_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单SN',
  `log_name` varchar(100) NOT NULL DEFAULT '' COMMENT '日志名称',
  `log_detail` varchar(200) NOT NULL COMMENT '日志明细',
  `log_content` text COMMENT '备注说明',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_inventory_order_relation` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `ref_inve_order_id` bigint(20) NOT NULL COMMENT '清关单ID',
  `ref_inve_order_sn` varchar(50) NOT NULL DEFAULT '' COMMENT '清关单SN',
  `rel_type` varchar(20) NOT NULL COMMENT '单据类型',
  `rel_no` varchar(50) NOT NULL COMMENT '单据号',
  `oper` varchar(100) DEFAULT NULL COMMENT '操作人',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='清关单关联单证';

CREATE TABLE `ccs_log_customs_inventory` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `declare_no` varchar(50) NOT NULL COMMENT '申报单号',
  `sn` varchar(50) NOT NULL COMMENT '相关单据SN',
  `old_status` varchar(30) DEFAULT NULL COMMENT '修改前值',
  `new_status` varchar(30) DEFAULT NULL COMMENT '修改后值',
  `oper_detail` varchar(100) DEFAULT NULL COMMENT '操作情况',
  `content` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `has_xml_message` tinyint(1) DEFAULT '0' COMMENT '是否有报文,默认没有',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_log_customs_inventory_cancel` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `declare_no` varchar(50) NOT NULL COMMENT '申报单号',
  `sn` varchar(50) NOT NULL COMMENT '相关单据SN',
  `old_status` varchar(30) DEFAULT NULL COMMENT '修改前值',
  `new_status` varchar(30) DEFAULT NULL COMMENT '修改后值',
  `oper_detail` varchar(100) DEFAULT NULL COMMENT '操作情况',
  `content` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `has_xml_message` tinyint(1) DEFAULT '0' COMMENT '是否有报文,默认没有',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_log_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `declare_no` varchar(50) NOT NULL COMMENT '申报单号',
  `sn` varchar(50) NOT NULL COMMENT '相关单据SN',
  `old_status` varchar(30) DEFAULT NULL COMMENT '修改前值',
  `new_status` varchar(30) DEFAULT NULL COMMENT '修改后值',
  `oper_detail` varchar(100) DEFAULT NULL COMMENT '操作情况',
  `content` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `has_xml_message` tinyint(1) DEFAULT '0' COMMENT '是否有报文,默认没有',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_log_payment` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `declare_no` varchar(50) NOT NULL COMMENT '申报单号',
  `sn` varchar(50) NOT NULL COMMENT '相关单据SN',
  `old_status` varchar(30) DEFAULT NULL COMMENT '修改前值',
  `new_status` varchar(30) DEFAULT NULL COMMENT '修改后值',
  `oper_detail` varchar(100) DEFAULT NULL COMMENT '操作情况',
  `content` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `has_xml_message` tinyint(1) DEFAULT '0' COMMENT '是否有报文,默认没有',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_log_refund_order` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT,
  `declare_no` varchar(50) NOT NULL COMMENT '申报单号',
  `sn` varchar(50) NOT NULL COMMENT '相关单据SN',
  `old_status` varchar(30) DEFAULT NULL COMMENT '修改前值',
  `new_status` varchar(30) DEFAULT NULL COMMENT '修改后值',
  `oper_detail` varchar(100) DEFAULT NULL COMMENT '操作情况',
  `content` varchar(255) DEFAULT NULL COMMENT '备注说明',
  `has_xml_message` tinyint(1) DEFAULT '0' COMMENT '是否有报文,默认没有',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

CREATE TABLE `ccs_order_2020q2` (
  `id` bigint(20) unsigned NOT NULL,
  `sn` varchar(50) NOT NULL COMMENT '申报单系统编号',
  `system_global_sn` varchar(50) DEFAULT NULL COMMENT '系统全局单号',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `out_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '上游单号',
  `declare_order_no` varchar(60) NOT NULL DEFAULT '' COMMENT '申报单号',
  `status` tinyint(3) NOT NULL COMMENT '申报订单状态',
  `exception_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1 清关异常标识',
  `exception_type` tinyint(3) NOT NULL DEFAULT '0' COMMENT '清关异常类型',
  `exception_detail` varchar(516) NOT NULL DEFAULT '' COMMENT '清关异常描述',
  `version` bigint(20) NOT NULL DEFAULT '0' COMMENT '记录版本 当前生效版本0',
  `action_json` varchar(128) NOT NULL DEFAULT '[]' COMMENT '申报项',
  `extra_json` text NOT NULL COMMENT 'json储存的其他属性键值对',
  `finish_time` timestamp(3) NULL DEFAULT NULL COMMENT '最后一次申报时间',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ebp_order_version` (`ebp_id`,`declare_order_no`,`version`),
  KEY `idx_sn` (`sn`),
  KEY `idx_system_global_sn` (`system_global_sn`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申报主订单';

CREATE TABLE `ccs_refund_order_info` (
  `id` bigint(20) NOT NULL COMMENT '主键',
  `refund_no` varchar(50) DEFAULT NULL COMMENT '公司退货单号',
  `ref_list_bill_id` bigint(20) NOT NULL COMMENT '相关清单据ID(清单号)',
  `ref_list_bill_sn` varchar(32) NOT NULL COMMENT '公司退货单号',
  `ref_declare_id` bigint(20) NOT NULL COMMENT '申报单号ID',
  `ref_declare_no` varchar(50) DEFAULT NULL COMMENT '公司退货单号',
  `refund_express_code` varchar(50) DEFAULT NULL COMMENT '物流公司代码',
  `refund_express_name` varchar(50) DEFAULT NULL COMMENT '物流公司名称',
  `mail_no` varchar(50) DEFAULT NULL COMMENT '物流运单号',
  `channel_order_no` varchar(50) DEFAULT NULL COMMENT '物流运单号',
  `assure_no` varchar(20) DEFAULT NULL COMMENT '担保企业代码',
  `assure_name` varchar(50) DEFAULT NULL COMMENT '担保企业代码',
  `refund_check_status` varchar(20) NOT NULL COMMENT '退回审核状态',
  `refund_check_time` datetime DEFAULT NULL COMMENT '退货审核时间',
  `refund_custom_check_detail` varchar(50) DEFAULT NULL COMMENT '海关返回明细',
  `refund_custom_status` varchar(20) DEFAULT NULL COMMENT '海关返回状态码',
  `refund_custom_time` datetime DEFAULT NULL COMMENT '海关回执时间',
  `refund_status` tinyint(2) NOT NULL COMMENT '退货订单完成状态[初始化，待退货,退货完成,退货关闭]',
  `refund_status_time` datetime DEFAULT NULL COMMENT '退货订单完成时间',
  `refund_mail_no` varchar(50) DEFAULT NULL COMMENT '退货运单号',
  `customs_code` varchar(20) DEFAULT NULL COMMENT '申报海关代码',
  `order_no` varchar(50) DEFAULT NULL COMMENT '订单编号',
  `ebp_code` varchar(10) DEFAULT NULL COMMENT '电商平台代码',
  `ebp_name` varchar(50) DEFAULT NULL COMMENT '电商平台名称',
  `ebc_code` varchar(10) DEFAULT NULL COMMENT '电商企业代码',
  `ebc_name` varchar(50) DEFAULT NULL COMMENT '电商企业名称',
  `logistics_no` varchar(50) DEFAULT NULL COMMENT '物流运单编号',
  `logistics_code` varchar(10) DEFAULT NULL COMMENT '物流企业代码',
  `logistics_name` varchar(50) DEFAULT NULL COMMENT '物流企业名称',
  `cop_no` varchar(50) DEFAULT NULL COMMENT '企业内部编号',
  `pre_no` varchar(50) DEFAULT NULL COMMENT '预录入编号',
  `invt_no` varchar(50) DEFAULT NULL COMMENT '清单编号',
  `buyer_id_type` varchar(10) DEFAULT NULL COMMENT '订购人证件类型',
  `buyer_id_number` varchar(20) DEFAULT NULL COMMENT '订购人证件号码',
  `buyer_name` varchar(20) DEFAULT NULL COMMENT '订购人姓名',
  `buyer_telephone` varchar(20) DEFAULT NULL COMMENT '订购人电话',
  `agent_code` varchar(10) DEFAULT NULL COMMENT '申报企业代码',
  `agent_name` varchar(50) DEFAULT NULL COMMENT '申报企业名称',
  `reason` text COMMENT '退货原因',
  `note` text COMMENT '备注',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_ref_declare_id` (`ref_declare_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='申请退货表';

CREATE TABLE `cds_extra_pay_info` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
  `declare_order_sn` varchar(60) DEFAULT '' COMMENT '申报单号',
  `trade_pay_no` varchar(64) DEFAULT '' COMMENT '支付单流水号',
  `trade_time` timestamp(3) NULL DEFAULT NULL COMMENT '交易时间',
  `ebp_id` bigint(20) NOT NULL COMMENT '电商平台',
  `pay_company_id` bigint(20) NOT NULL COMMENT '支付企业',
  `pay_transaction_id` varchar(60) DEFAULT '' COMMENT '验核机构交易流水号',
  `ver_dept` varchar(60) DEFAULT NULL COMMENT '验核机构名称',
  `pay_channel` varchar(24) DEFAULT '' COMMENT '海关订单支付渠道',
  `pay_way` varchar(24) DEFAULT '' COMMENT '海关订单支付方式-对应海关代码',
  `pay_transaction_amount` decimal(10,2) unsigned DEFAULT NULL COMMENT '支付单总金额',
  `item_json` varchar(2048) DEFAULT NULL COMMENT '商品信息',
  `recp_code` varchar(255) DEFAULT '' COMMENT '收款企业社会信用代码',
  `recp_name` varchar(255) DEFAULT '' COMMENT '收款企业工商备案名称',
  `recp_account` varchar(255) DEFAULT '' COMMENT '收款渠道下的账号',
  `pay_request_message` text COMMENT '支付请求原始数据',
  `pay_response_message` text COMMENT '支付返回原始数据',
  `create_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '创建人',
  `update_by` bigint(20) NOT NULL DEFAULT '0' COMMENT '更新人',
  `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_order_ebp` (`declare_order_sn`,`ebp_id`),
  KEY `idx_trade_pay_no` (`trade_pay_no`,`pay_company_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='海关额外支付信息';












