# 2020/10/13 申报单冗余子单据编号用于分表查询
ALTER TABLE ccs_order_2020q2 ADD COLUMN `customs_payment_sn` varchar(32) DEFAULT NULL COMMENT '支付单系统编号' after `sn`;
ALTER TABLE ccs_order_2020q2 ADD COLUMN `customs_order_sn` varchar(32) DEFAULT NULL COMMENT '订单系统编号' after `customs_payment_sn`;
ALTER TABLE ccs_order_2020q2 ADD COLUMN `customs_logistics_sn` varchar(32) DEFAULT NULL COMMENT '运单系统编号' after `customs_order_sn`;
ALTER TABLE ccs_order_2020q2 ADD COLUMN `customs_inventory_sn` varchar(32) DEFAULT NULL COMMENT '清单系统编号' after `customs_logistics_sn`;

# 2020/10/28 申报订单项添加索引
ALTER TABLE ccs_customs_inventory_2020q2 ADD INDEX idx_declareorderno(declare_order_no);
alter table ccs_customs_order_2020q2 add index idx_sn(sn);

# 2020/10/28 清关单添加字段
ALTER TABLE ccs_inventory_order_info ADD COLUMN `entry_exit_customs` varchar(32) DEFAULT NULL COMMENT '进出境关别' after `pick_up_no`;
ALTER TABLE ccs_inventory_order_info ADD COLUMN `transport_mode` varchar(32) DEFAULT NULL COMMENT '运输方式' after `entry_exit_customs`;
ALTER TABLE ccs_inventory_order_info ADD COLUMN `shipment_country` varchar(32) DEFAULT NULL COMMENT '启运国' after `transport_mode`;

# 2020/10/28 退货单添加字段
ALTER TABLE ccs_refund_order_info ADD COLUMN `endorsement_status` varchar(20) DEFAULT 'UNENDORSE' COMMENT '核注状态' after `refund_mail_no`;

# 2020/10/28 运单添加字段
ALTER TABLE ccs_customs_logistics ADD COLUMN `agent_company_id` bigint(20) NOT NULL COMMENT '报文传输企业'after `logistics_company_id`;

# 2020/10/28 运单添加字段
ALTER TABLE ccs_customs_logistics ADD COLUMN `goods_total_amount` decimal(14,4) DEFAULT 0 COMMENT '货值' after `logistics_no`;

# 2020/10/28 运单添加字段
ALTER TABLE ccs_customs_logistics ADD COLUMN `customs_detail` varchar(255) DEFAULT NULL COMMENT '海关回执描述' after `customs_status`;

# 2021/01/15 清关单添加字段
ALTER TABLE ccs_inventory_order_info ADD COLUMN `channel_business_type` varchar(32) DEFAULT NULL COMMENT '上游单据类型' after `channel`;
ALTER TABLE ccs_inventory_order_info ADD COLUMN `channel_business_sn` varchar(32) DEFAULT NULL COMMENT '上游单据编号' after `channel_business_type`;

# 2021/01/27 取消单添加字段
ALTER TABLE ccs_customs_inventory_calloff ADD COLUMN `refund_logistics_no` varchar(50)  NOT NULL DEFAULT '' COMMENT '退货运单编号'after `logistics_no`;

# 2021/03/04 清关单添加字段
ALTER TABLE ccs_inventory_order_info ADD COLUMN `customs_invt_type` varchar(32) DEFAULT NULL COMMENT '清单类型（指区港联动等）' after `inve_business_type`;
ALTER TABLE ccs_inventory_order_info ADD COLUMN `customs_entry_no` varchar(32) DEFAULT NULL COMMENT '报关单号' after `customs_invt_type`;

# 2021/03/04 清关单表体添加字段
ALTER TABLE ccs_inventory_order_item ADD COLUMN `sku_id` varchar(50) DEFAULT NULL COMMENT 'SKU' after `product_id`;
ALTER TABLE ccs_inventory_order_item ADD COLUMN `plan_declare_qty` decimal(10,2) DEFAULT NULL COMMENT '计划申报数量' after `hs_code`;
ALTER TABLE ccs_inventory_order_item ADD COLUMN `out_bound_no` varchar(500) DEFAULT NULL COMMENT '出库单号' after `plan_declare_qty`;
ALTER TABLE ccs_inventory_order_item ADD COLUMN `actual_tally_qty` decimal(10,2) DEFAULT NULL COMMENT '实际理货数量' after `out_bound_no`;

# 2021/04/20 清关单添加字段
ALTER TABLE ccs_inventory_order_info ADD COLUMN `customs_entry_company` bigint(20) DEFAULT NULL COMMENT '报关企业主键' after `customs_entry_no`;
ALTER TABLE ccs_inventory_order_info ADD COLUMN `customs_entry_type` int(2) DEFAULT NULL COMMENT '报关单类型' after `customs_entry_company`;


#2021/04/22 取消单添加租户ID字段
ALTER TABLE ccs_customs_inventory_calloff ADD COLUMN tenant_id varchar(60) DEFAULT NULL COMMENT '租户id' after logistics_no;

#2021/04/27 核放单添加报关单号字段
ALTER TABLE ccs_checklist ADD COLUMN declare_order_no varchar(64) DEFAULT NULL COMMENT '报关单号' after declare_company_id;

#2021/05/14 添加索引
alter table `ccs_order`.`ccs_endorsement_item` add index idx_endorsement_id(`endorsement_id`)
alter table `ccs_order`.`ccs_endorsement_item` add index idx_checklist_id(`checklist_id`)

#2021/05/19 添加索引
alter table `ccs_order`.`ccs_customs_inventory_2020q2` add index idx_agent_company_id(`agent_company_id`);
alter table `ccs_order`.`ccs_customs_inventory_2020q3` add index idx_agent_company_id(`agent_company_id`);
alter table `ccs_order`.`ccs_customs_inventory_2020q4` add index idx_agent_company_id(`agent_company_id`);
alter table `ccs_order`.`ccs_customs_inventory_2021q1` add index idx_agent_company_id(`agent_company_id`);
alter table `ccs_order`.`ccs_customs_inventory_2021q2` add index idx_agent_company_id(`agent_company_id`);

#2021/05/27 添加索引
alter table ccs_order.ccs_customs_inventory_2020q2 add index idx_create_time(create_time);
alter table ccs_order.ccs_customs_inventory_2020q3 add index idx_create_time(create_time);
alter table ccs_order.ccs_customs_inventory_2020q4 add index idx_create_time(create_time);
alter table ccs_order.ccs_customs_inventory_2021q1 add index idx_create_time(create_time);
alter table ccs_order.ccs_customs_inventory_2021q2 add index idx_create_time(create_time);

#2021/06/03 添加取消凭证
ALTER TABLE `ccs_order`.`ccs_customs_inventory_calloff`
ADD COLUMN `pic_json` varchar(1025) NULL COMMENT '取消凭证图片json串' AFTER `calloff_time`;

#2021/06/08 轨迹日志
CREATE TABLE `ccs_customs_inventory_track_log`
(
    `id`               bigint(20)   NOT NULL AUTO_INCREMENT,
    `declare_order_no` varchar(50)  NOT NULL COMMENT '申报单号',
    `old_status`       varchar(255) NOT NULL COMMENT '修改前状态',
    `new_status`       varchar(255) NOT NULL COMMENT '修改后状态',
    `operate_des`      varchar(512)          DEFAULT NULL COMMENT '操作描述',
    `log_des`          varchar(512)          DEFAULT NULL COMMENT '日志描述',
    `content`          text COMMENT '报文',
    `has_xml_message`  tinyint(1)            DEFAULT '0' COMMENT '是否有报文,默认没有',
    `create_by`        bigint(20)   NOT NULL DEFAULT '0' COMMENT '创建人',
    `update_by`        bigint(20)   NOT NULL DEFAULT '0' COMMENT '更新人',
    `create_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间，日志发生时间',
    `update_time`      timestamp    NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted`          tinyint(1)   NOT NULL DEFAULT '0' COMMENT '1逻辑删除',
    PRIMARY KEY (`id`),
    KEY `declare_no_index` (`declare_order_no`),
    KEY `create_time_index` (`create_time`) USING BTREE
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4


#2021/06/15 清关单添加字段
ALTER TABLE `ccs_inventory_order_info`
ADD COLUMN `master_order_sn` varchar(512) DEFAULT NULL COMMENT '主单号' after `inve_customs_sn`,
ADD COLUMN `sub_order_sn` varchar(512) DEFAULT NULL COMMENT '主单号' after `master_order_sn`;

#2021/06/22 清关单备注字段大小修改
ALTER TABLE `ccs_order`.`ccs_inventory_order_info`
    MODIFY COLUMN `remark` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注' AFTER `apply_person`;

#2021/06/28 字段修改
ALTER TABLE `ccs_order`.`ccs_customs_inventory_track_log`
    MODIFY COLUMN `old_status` int(4) NOT NULL COMMENT '修改前状态' AFTER `declare_order_no`,
    MODIFY COLUMN `new_status` int(4) NOT NULL COMMENT '修改后状态' AFTER `old_status`,
    MODIFY COLUMN `operate_des` int(4) NOT NULL COMMENT '操作描述' AFTER `new_status`;

#2021/07/05 清关单增加清关单与仓库/货主映射表
CREATE TABLE `ccs_order`.`ccs_inventory_order_onwer_mapping`
(
    `id`                  bigint(20)   NOT NULL  AUTO_INCREMENT COMMENT '主键',
    `inve_customs_sn`     varchar(50)  NOT NULL COMMENT '关联清关单sn',
    `entityWarehouseCode` varchar(50)  NOT NULL COMMENT '实体仓编码',
    `entityWarehouseName` varchar(50)  NOT NULL COMMENT '实体仓名称',
    `ownerCode`           varchar(50)  NOT NULL COMMENT '货主编码',
    `ownerName`           varchar(50)  NOT NULL COMMENT '货主名称',
    `create_by`           bigint(20)   NOT NULL DEFAULT 0 COMMENT '创建人',
    `update_by`           bigint(20)   NOT NULL DEFAULT 0 COMMENT '更新人',
    `create_time`         timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
    `update_time`         timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
    `deleted`             tinyint(1)   NOT NULL DEFAULT 0 COMMENT '1逻辑删除',
    PRIMARY KEY (`id`),
    INDEX `index_inventory_sn` (`inve_customs_sn`) USING BTREE COMMENT '清关单单号'
);

#2021/07/06 表结构修改 上master的时候改sql
ALTER TABLE `ccs_order`.`ccs_inventory_order_owner_mapping`
    CHANGE COLUMN `inve_customs_sn` `order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '单号' AFTER `id`,
    ADD COLUMN `order_type`                    varchar(40)                                                  NOT NULL COMMENT '订单类型' AFTER `order_sn`;

#2021/09/29 核放单增加IC卡
ALTER TABLE `ccs_order`.`ccs_checklist`
    ADD COLUMN `vehicle_ic_no` varchar(64) NULL COMMENT 'ic卡号' AFTER `declare_order_no`;