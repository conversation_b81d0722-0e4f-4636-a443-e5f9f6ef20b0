<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-order</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cds-order-service</artifactId>
    <properties>
        <wms-out-rpc-clientr-version>3.1.1-RELEASE</wms-out-rpc-clientr-version>
    </properties>
    <dependencies>

        <!-- https://mvnrepository.com/artifact/org.springframework.cloud/spring-cloud-context -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>2.2.5.RELEASE</version>
        </dependency>


        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-tools</artifactId>
            <version>4.7.0</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-rocketmq-business</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>mq-component</artifactId>-->
        <!--        </dependency>-->

        <!-- 一方库 -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-es-component</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-declare-inner-component</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-stock-helper</artifactId>
            <version>1.0-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>boost-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-item-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-api</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>cds-declare-sdk</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-taxes-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-jcq-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-xxl-saas-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!-- 二方库 -->

        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-out-rpc-client</artifactId>
            <version>${wms-out-rpc-clientr-version}</version>
        </dependency>
        <dependency>
            <groupId>com.danding.wcms</groupId>
            <artifactId>danding-wcms-rpc-api</artifactId>
            <version>1.0.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.danding.monitor</groupId>
            <artifactId>order-monitor-rpc</artifactId>
            <version>1.1.1-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>tms-rpc-waybill</artifactId>
            <version>2.1.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>wms-platform-rpc-client</artifactId>
            <version>3.1.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.dubbo</groupId>
                    <artifactId>dubbo-spring-boot-starter</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-encryption-standalone</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>common-utils</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cache-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>mybatis-component</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.shardingsphere</groupId>
                    <artifactId>sharding-core-route</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>sharding-core-common</artifactId>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>dubbo-seata-component</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>boost-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dt-component-rocketmq-business</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>jieztechSignUtil</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>uc-user-rpc-client</artifactId>-->
        <!--            <version>1.1-RELEASE</version>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-dubbo</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-core</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>oms-order-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>mybatis-tenant-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding.component</groupId>
            <artifactId>danding-sharding-core-route</artifactId>
        </dependency>
        <!-- 三方库 -->
        <!-- 浙江电子口岸SSLCLIENT -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>zjportGatewayClient</artifactId>
            <version>1.0.0</version>
        </dependency>
        <!-- 京东sdk -->
        <dependency>
            <groupId>com.jd</groupId>
            <artifactId>open-api-sdk</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
            <version>1.9.2</version>
        </dependency>
        <dependency>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
            <version>1.9.2</version>
        </dependency>
        <dependency>
            <artifactId>saas-tenant-support</artifactId>
            <groupId>com.danding</groupId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>5.3.1</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-order-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-trade-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-config-rpc-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-configuration-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dt</groupId>
            <artifactId>dt-component-canal</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>org.tukaani</groupId>
            <artifactId>xz</artifactId>
            <version>1.9</version>
        </dependency>

        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.6</version>
        </dependency>
    </dependencies>
    <build>
        <finalName>ccs-order-service</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>
                                repackage
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
