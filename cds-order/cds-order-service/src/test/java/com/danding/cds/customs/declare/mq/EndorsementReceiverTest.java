package com.danding.cds.customs.declare.mq;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.model.checkList.EndorsementResult;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.endorsement.api.service.EndorsementTrackLogService;
import com.danding.cds.service.OrderSnTenantService;
import com.danding.core.tenant.SimpleTenantHelper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class EndorsementReceiverTest {

    @Mock
    private EndorsementService endorsementService;

    @Mock
    private EndorsementTrackLogService endorsementTrackLogService;

    @Mock
    private OrderSnTenantService orderSnTenantService;

    @InjectMocks
    private EndorsementReceiver endorsementReceiver;

    private EndorsementDTO mockEndorsementDTO;

    @BeforeEach
    void setUp() {
        SimpleTenantHelper.setTenantId(1001L);

        mockEndorsementDTO = new EndorsementDTO();
        mockEndorsementDTO.setId(1L);
        mockEndorsementDTO.setSn("TEST123456789012345678");
        mockEndorsementDTO.setStatus(EndorsementOrderStatus.EXAMINE.getCode());
    }

    @Test
    void testSpecialCase_SkipStatusUpdate() throws Exception {
        // 准备测试数据：跳过状态更新的特殊情况
        EndorsementResult specialResult = new EndorsementResult(
                "TEST123456789012345678",
                "TEST_PRE_NO",
                EndorsementResult.STATUS_EXAMINED,
                "TEST_REAL_NO",
                "INV201_1",
                Arrays.asList("核注清单审核回执：通过（已核扣），另实际核扣标记：未核扣"),
                "test message",
                true // 跳过状态更新
        );

        when(endorsementService.findBySn("TEST123456789012345678")).thenReturn(mockEndorsementDTO);

        // 执行测试
        String message = JSON.toJSONString(specialResult);
        endorsementReceiver.handle(message);

        // 验证：应该只记录日志，不更新状态
        verify(endorsementTrackLogService).buildFullLog(
                eq(1L),
                eq(EndorsementOrderStatus.EXAMINE),
                eq("核注清单审核回执：通过（已核扣），另实际核扣标记：未核扣"),
                eq("test message")
        );

        // 验证：不应该调用状态更新方法
        verify(endorsementService, never()).fillItemById(anyLong(), anyString());
        verify(endorsementService, never()).examinedPostProcess(any(), any(), anyBoolean());
    }

    @Test
    void testNormalCase_ExaminedStatus() throws Exception {
        // 准备测试数据：正常的审核状态处理
        EndorsementResult normalResult = new EndorsementResult(
                "TEST123456789012345678",
                "TEST_PRE_NO",
                EndorsementResult.STATUS_EXAMINED,
                "TEST_REAL_NO",
                "INV201_1",
                Arrays.asList("通过（已核扣）"),
                "test message"
        );

        when(endorsementService.findBySn("TEST123456789012345678")).thenReturn(mockEndorsementDTO);

        // 执行测试
        String message = JSON.toJSONString(normalResult);
        endorsementReceiver.handle(message);

        // 验证：应该调用正常的处理方法
        verify(endorsementService).fillItemById(1L, "TEST_REAL_NO");
        verify(endorsementService).examinedPostProcess(eq(mockEndorsementDTO), eq(Arrays.asList("通过（已核扣）")), eq(false));
    }

    @Test
    void testNormalCase_StoragingStatus() throws Exception {
        // 准备测试数据：正常的暂存状态处理
        mockEndorsementDTO.setStatus(EndorsementOrderStatus.STORAGING.getCode());

        EndorsementResult normalResult = new EndorsementResult(
                "TEST123456789012345678",
                "TEST_PRE_NO",
                EndorsementResult.STATUS_INIT,
                "TEST_REAL_NO",
                "INV201_Y",
                Arrays.asList("入库成功"),
                "test message"
        );

        when(endorsementService.findBySn("TEST123456789012345678")).thenReturn(mockEndorsementDTO);

        // 执行测试
        String message = JSON.toJSONString(normalResult);
        endorsementReceiver.handle(message);

        // 验证：应该更新状态和记录日志
        verify(endorsementService).updateEndorsementAndInvOrderStatus(1L, EndorsementOrderStatus.STORAGED.getCode());
        verify(endorsementTrackLogService).buildFullLog(
                eq(1L),
                eq(EndorsementOrderStatus.STORAGED),
                eq("暂存成功,预录入编号为TEST_PRE_NO"),
                eq("test message")
        );
    }
}
