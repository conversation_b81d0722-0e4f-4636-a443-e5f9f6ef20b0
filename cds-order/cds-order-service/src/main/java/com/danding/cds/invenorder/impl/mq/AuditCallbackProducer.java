package com.danding.cds.invenorder.impl.mq;

import com.alibaba.fastjson.JSON;
import com.danding.cds.invenorder.api.dto.InvenorderAuditDTO;
import com.danding.cds.item.api.dto.GoodsRecordAuditDTO;
import com.danding.logistics.mq.common.handler.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: Raymond
 * @Date: 2020/11/19 18:52
 * @Description:
 */
@Component
public class AuditCallbackProducer {
    @Autowired
    private MessageSender messageSender;

    public Boolean send(InvenorderAuditDTO bizResultInfo){

        String bizResult = JSON.toJSONString(bizResultInfo);
        messageSender.sendMsg(bizResult,"ccs-invenorder-audit-callback-topic:" + bizResultInfo.getChannel().getValue());
        return true;
    }
}
