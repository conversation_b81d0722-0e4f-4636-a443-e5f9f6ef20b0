package com.danding.cds.promotion.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description: 
 */
/**
 * 促销订单
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "ccs_promotion_order")
public class PromotionOrderDO extends BaseDO {


    /**
    * 货主编码
    */
    @Column(name = "owner_code")
    private String ownerCode;


    /**
     * 淘天货主编码
     */
    @Column(name = "tt_owner_code")
    private String ttOwnerCode;

    /**
    * 仓库编码
    */
    @Column(name = "warehouse_code")
    private String warehouseCode;

    /**
    * 场景类型
    */
    @Column(name = "scenario_type")
    private String scenarioType;

    /**
    * 报备单号
    */
    @Column(name = "order_code")
    private String orderCode;

    /**
    * 单据来源
    */
    @Column(name = "order_source")
    private String orderSource;

    /**
    * 商家的卖家昵称
    */
    @Column(name = "seller_nick")
    private String sellerNick;

    /**
    * 活动名称
    */
    @Column(name = "activity_name")
    private String activityName;

    /**
    * 促销类型
    */
    @Column(name = "marketing_type")
    private String marketingType;

    /**
     * 主营行业
     */
    @Column(name = "category_name")
    private String categoryName;

    /**
    * 活动类型
    */
    @Column(name = "activity_type")
    private String activityType;

    /**
    * 活动开始时间
    */
    @Column(name = "activity_start_date")
    private Date activityStartDate;

    /**
    * 活动结束时间
    */
    @Column(name = "activity_end_date")
    private Date activityEndDate;

    /**
     * 活动创建时间
     */
    @Column(name = "activity_create_date")
    private Date activityCreateDate;

    /**
     * 活动报备截止时间
     */
    @Column(name = "activity_report_end_date")
    private Date activityReportEndDate;

    /**
    * 仓库名称
    */
    @Column(name = "warehouse_name")
    private String warehouseName;

    /**
    * 预估订单金额（单元：元）
    */
    @Column(name = "predict_amount")
    private String predictAmount;

    /**
    * 商家联系人名称
    */
    @Column(name = "seller_contact_name")
    private String sellerContactName;

    /**
    * 商家联系人电话
    */
    @Column(name = "seller_contact_phone")
    private String sellerContactPhone;

    /**
    * 状态
    */
    @Column(name = "status")
    private String status;

    /**
    * 备注
    */
    @Column(name = "remark")
    private String remark;

    /**
    * 扩展属性
    */
    @Column(name = "extend_props")
    private String extendProps;

    /**
     * 店铺id （上游透传）
     */
    @Column(name = "shop_id")
    private Long shopId;

}