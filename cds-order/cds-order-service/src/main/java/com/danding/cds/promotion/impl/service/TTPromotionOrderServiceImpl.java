package com.danding.cds.promotion.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.config.facade.OwnerRpcFacade;
import com.danding.business.client.rpc.config.result.OwnerRpcResult;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.promotion.api.dto.TTPromotionExportDTO;
import com.danding.cds.promotion.api.enums.TTPromotionItemTypeEnum;
import com.danding.cds.promotion.api.enums.TTPromotionOperateTypeEnum;
import com.danding.cds.promotion.api.enums.TTPromotionOrderStatusEnum;
import com.danding.cds.promotion.api.service.TTPromotionOrderService;
import com.danding.cds.promotion.api.vo.TTPromotionDetailVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderPageVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderSearch;
import com.danding.cds.promotion.impl.entity.PromotionItemDO;
import com.danding.cds.promotion.impl.entity.PromotionOrderDO;
import com.danding.cds.promotion.impl.entity.PromotionTrackLog;
import com.danding.cds.promotion.impl.mapper.PromotionItemMapper;
import com.danding.cds.promotion.impl.mapper.PromotionOrderMapper;
import com.danding.cds.promotion.impl.mapper.PromotionTrackLogMapper;
import com.danding.cds.v2.api.TaotianOrderService;
import com.danding.cds.v2.bean.dto.TTActiveRegistrationCreateDTO;
import com.danding.cds.v2.bean.dto.TTActiveRegistrationReportDTO;
import com.danding.cds.v2.bean.vo.req.TTPromotionAuditReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
@Slf4j
@Service
public class TTPromotionOrderServiceImpl implements TTPromotionOrderService {
    @Resource
    private PromotionOrderMapper promotionOrderMapper;
    @Resource
    private PromotionItemMapper promotionItemMapper;
    @DubboReference
    private OwnerRpcFacade ownerRpcFacade;
    @Resource
    private TaotianOrderService taotianOrderService;
    @Resource
    private PromotionTrackLogMapper promotionTrackLogMapper;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createByTaotian(TTActiveRegistrationCreateDTO createDTO) {
        if (Objects.isNull(createDTO) || Objects.isNull(createDTO.getActivityRecordOrder())
                || Objects.isNull(createDTO.getActivityRecordOrder().getOrderCode())) {
            throw new ArgsInvalidException("入参错误");
        }
        PromotionOrderDO promotionOrderDO = new PromotionOrderDO();
        promotionOrderDO.setOwnerCode(createDTO.getErpOwnerCode());
        promotionOrderDO.setTtOwnerCode(createDTO.getOwnerCode());
        promotionOrderDO.setWarehouseCode(createDTO.getWmsWarehouseCode());
        promotionOrderDO.setScenarioType(createDTO.getScenarioType());
        promotionOrderDO.setShopId(createDTO.getShopId());

        TTActiveRegistrationCreateDTO.ActivityRecordOrder activityRecordOrder = createDTO.getActivityRecordOrder();
        promotionOrderDO.setOrderCode(activityRecordOrder.getOrderCode());
        promotionOrderDO.setOrderSource(activityRecordOrder.getOrderSource());
        promotionOrderDO.setSellerNick(activityRecordOrder.getSellerNick());
        promotionOrderDO.setActivityName(activityRecordOrder.getActivityName());
        promotionOrderDO.setMarketingType(activityRecordOrder.getMarketingType());
        promotionOrderDO.setCategoryName(activityRecordOrder.getCategoryName());
        promotionOrderDO.setActivityType(activityRecordOrder.getActivityType());
        if (StringUtils.isNotEmpty(activityRecordOrder.getActivityStartDate())) {
            promotionOrderDO.setActivityStartDate(DateUtil.parse(activityRecordOrder.getActivityStartDate(), "yyyy-MM-dd"));
            promotionOrderDO.setActivityReportEndDate(DateUtil.parse(activityRecordOrder.getActivityStartDate(), "yyyy-MM-dd"));
        }
        if (StringUtils.isNotEmpty(activityRecordOrder.getActivityEndDate())) {
            promotionOrderDO.setActivityEndDate(DateUtil.parse(activityRecordOrder.getActivityEndDate(), "yyyy-MM-dd"));
        }
        if (StringUtils.isNotEmpty(activityRecordOrder.getCreateTime())) {
            promotionOrderDO.setActivityCreateDate(DateUtil.parse(activityRecordOrder.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
        }
        promotionOrderDO.setWarehouseName(activityRecordOrder.getWarehouseName());
        promotionOrderDO.setPredictAmount(activityRecordOrder.getPredictAmount());
        if (Objects.nonNull(activityRecordOrder.getSellerContactInfo())) {
            TTActiveRegistrationCreateDTO.ActivityRecordOrder.SellerContactInfo sellerContactInfo = activityRecordOrder.getSellerContactInfo();
            promotionOrderDO.setSellerContactName(sellerContactInfo.getName());
            promotionOrderDO.setSellerContactPhone(sellerContactInfo.getPhone());
        }
        promotionOrderDO.setStatus(TTPromotionOrderStatusEnum.PENDING.getCode());
        promotionOrderDO.setRemark(activityRecordOrder.getRemark());
        if (Objects.nonNull(activityRecordOrder.getExtendProps())) {
            promotionOrderDO.setExtendProps(JSON.toJSONString(activityRecordOrder.getExtendProps()));
        }
        UserUtils.setCommonData(promotionOrderDO);
        Example example = new Example(PromotionOrderDO.class);
        example.createCriteria().andEqualTo("orderCode", activityRecordOrder.getOrderCode());
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);

        if (CollUtil.isNotEmpty(promotionOrderDOS)) {
            PromotionOrderDO old = promotionOrderDOS.get(0);
            promotionOrderDO.setId(old.getId());
            promotionOrderMapper.updateByPrimaryKeySelective(promotionOrderDO);
            buildTrackLog(promotionOrderDO.getId(), TTPromotionOperateTypeEnum.UPDATED.getCode(), "提交备案任务", JSON.toJSONString(createDTO), UserUtils.getUserRealName());
            Example itemDeleteExample = new Example(PromotionItemDO.class);
            itemDeleteExample.createCriteria().andEqualTo("refOrderId", old.getId());
            promotionItemMapper.deleteByExample(itemDeleteExample);
        } else {
            promotionOrderMapper.insertSelective(promotionOrderDO);
            buildTrackLog(promotionOrderDO.getId(), TTPromotionOperateTypeEnum.CREATED.getCode(), "提交备案任务", JSON.toJSONString(createDTO), UserUtils.getUserRealName());
        }

        if (Objects.nonNull(createDTO.getMainItem())) {
            for (TTActiveRegistrationCreateDTO.Item item : createDTO.getMainItem()) {
                PromotionItemDO promotionItemDO = new PromotionItemDO();
                promotionItemDO.setRefOrderId(promotionOrderDO.getId());
                promotionItemDO.setType(TTPromotionItemTypeEnum.MAIN_ITEM.getCode());
                buildAndInsertPromotionItem(promotionOrderDO, item, promotionItemDO);
            }
        }
        if (Objects.nonNull(createDTO.getPresentItem())) {
            for (TTActiveRegistrationCreateDTO.Item item : createDTO.getPresentItem()) {
                PromotionItemDO promotionItemDO = new PromotionItemDO();
                promotionItemDO.setRefOrderId(promotionOrderDO.getId());
                promotionItemDO.setType(TTPromotionItemTypeEnum.PRESENT_ITEM.getCode());
                buildAndInsertPromotionItem(promotionOrderDO, item, promotionItemDO);
            }
        }
    }


    private void buildAndInsertPromotionItem(PromotionOrderDO promotionOrderDO, TTActiveRegistrationCreateDTO.Item item, PromotionItemDO promotionItemDO) {
        promotionItemDO.setItemCode(item.getItemCode());
        promotionItemDO.setItemName(item.getItemName());
        promotionItemDO.setItemUrl(item.getItemUrl());
        promotionItemDO.setOriginalPrice(item.getOriginalPrice());
        promotionItemDO.setRegisterPrice(item.getRegisterPrice());
        promotionItemDO.setActivityPrice(item.getActivityPrice());
        promotionItemDO.setActivityQuantity(item.getActivityQuatity());
        promotionItemDO.setRecordNumber(item.getRecordNumber());
        promotionItemDO.setBarCode(item.getBarCode());
        if (Objects.nonNull(promotionItemDO.getExtendProps())) {
            promotionItemDO.setExtendProps(JSON.toJSONString(item.getExtendProps()));
        }
        UserUtils.setCommonData(promotionOrderDO);
        promotionItemMapper.insertSelective(promotionItemDO);
    }

    @Override
    @PageSelect
    public ListVO<TTPromotionOrderPageVO> paging(TTPromotionOrderSearch search) {
        ListVO<TTPromotionOrderPageVO> result = new ListVO<>();
        Example example = this.getExample(search);
        List<PromotionOrderDO> list = promotionOrderMapper.selectByExample(example);
        List<TTPromotionOrderPageVO> ttPromotionOrderPageVOS = ConvertUtil.listConvert(list, TTPromotionOrderPageVO.class);
        ttPromotionOrderPageVOS.forEach(ttPromotionOrderPageVO -> {
            if (StrUtil.isNotEmpty(ttPromotionOrderPageVO.getOwnerCode())) {
                OwnerRpcResult owner = ownerRpcFacade.getOneByOwnerCode(ttPromotionOrderPageVO.getOwnerCode());
                if (Objects.nonNull(owner)) {
                    ttPromotionOrderPageVO.setOwnerName(owner.getOwnerName());
                }
            }
            TTPromotionOrderStatusEnum statusEnum = TTPromotionOrderStatusEnum.getEnum(ttPromotionOrderPageVO.getStatus());
            if (Objects.nonNull(statusEnum)) {
                ttPromotionOrderPageVO.setStatus(statusEnum.getDesc());
            }
        });
        result.setDataList(ttPromotionOrderPageVOS);
        // 分页
        PageInfo<PromotionOrderDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example getExample(TTPromotionOrderSearch search) {
        Example example = new Example(PromotionOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (CollUtil.isNotEmpty(search.getOrderCode())) {
            criteria.andIn("orderCode", search.getOrderCode());
        }
        if (StrUtil.isNotEmpty(search.getWarehouseName())) {
            criteria.andLike("warehouseName", "%" + search.getWarehouseName() + "%");
        }
        if (StrUtil.isNotEmpty(search.getActivityType())) {
            criteria.andEqualTo("activityType", search.getActivityType());
        }
        if (StrUtil.isNotEmpty(search.getMarketingType())) {
            criteria.andEqualTo("marketingType", search.getMarketingType());
        }
        if (StrUtil.isNotEmpty(search.getActivityName())) {
            criteria.andLike("activityName", "%" + search.getActivityName() + "%");
        }
        if (StrUtil.isNotEmpty(search.getSellerNick())) {
            criteria.andEqualTo("sellerNick", search.getSellerNick());
        }
        if (StrUtil.isNotEmpty(search.getOrderSource())) {
            criteria.andEqualTo("orderSource", search.getOrderSource());
        }
        if (Objects.nonNull(search.getActivityCreateDateFrom())) {
            criteria.andGreaterThanOrEqualTo("activityCreateDate", search.getActivityCreateDateFrom());
        }
        if (Objects.nonNull(search.getActivityCreateDateTo())) {
            criteria.andLessThanOrEqualTo("activityCreateDate", search.getActivityCreateDateTo());
        }
        if (Objects.nonNull(search.getActivityStartDateFrom())) {
            criteria.andGreaterThanOrEqualTo("activityStartDate", search.getActivityStartDateFrom());
        }
        if (Objects.nonNull(search.getActivityStartDateTo())) {
            criteria.andLessThanOrEqualTo("activityStartDate", search.getActivityStartDateTo());
        }
        if (Objects.nonNull(search.getActivityEndDateFrom())) {
            criteria.andGreaterThanOrEqualTo("activityEndDate", search.getActivityEndDateFrom());
        }
        if (Objects.nonNull(search.getActivityEndDateTo())) {
            criteria.andLessThanOrEqualTo("activityEndDate", search.getActivityEndDateTo());
        }
        if (Objects.nonNull(search.getActivityReportEndDateFrom())) {
            criteria.andGreaterThanOrEqualTo("activityReportEndDate", search.getActivityReportEndDateFrom());
        }
        if (Objects.nonNull(search.getActivityReportEndDateTo())) {
            criteria.andLessThanOrEqualTo("activityReportEndDate", search.getActivityReportEndDateTo());
        }
        if (CollUtil.isNotEmpty(search.getOwnerCode())) {
            criteria.andIn("ownerCode", search.getOwnerCode());
        }
        if (StrUtil.isNotEmpty(search.getStatus())) {
            if (TTPromotionOrderStatusEnum.LESS_THAN_2DAYS.getCode().equals(search.getStatus())) {
                DateTime dateTime = DateUtil.offsetDay(new Date(), 2);
                criteria.andLessThanOrEqualTo("activityReportEndDate", dateTime);
                criteria.andEqualTo("status", TTPromotionOrderStatusEnum.PENDING.getCode());
            } else {
                criteria.andEqualTo("status", search.getStatus());
            }
        }
        example.orderBy("id").desc();
        return example;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void audit(TTPromotionAuditReqVo reqVo) {
        String ids = reqVo.getIds();
        String[] split = ids.split(",");
        List<String> list = Arrays.asList(split);
        if (CollUtil.isEmpty(list)) {
            throw new ArgsInvalidException("请选择报备单");
        }
        Example example = new Example(PromotionOrderDO.class);
        example.createCriteria().andIn("id", list).andEqualTo("deleted", false);
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);
        if (CollUtil.isEmpty(promotionOrderDOS)) {
            throw new ArgsInvalidException("未查询到有效的报备单");
        }
        long count = promotionOrderDOS.stream().map(PromotionOrderDO::getOwnerCode).distinct().count();
        if (count > 1) {
            throw new ArgsInvalidException("货主不一致，不允许批量审核");
        }
        PromotionOrderDO notPendingOrder = promotionOrderDOS.stream().filter(p -> !Objects.equals(p.getStatus(), TTPromotionOrderStatusEnum.PENDING.getCode())).findFirst().orElse(null);
        if (Objects.nonNull(notPendingOrder)) {
            throw new ArgsInvalidException("仅限待报备的任务回传审核结果");
        }
        boolean pass;
        if (Objects.equals(reqVo.getOpinion(), "1")) {
            pass = true;
        } else if (Objects.equals(reqVo.getOpinion(), "0")) {
            pass = false;
        } else {
            throw new ArgsInvalidException("审核状态不正确");
        }
        PromotionOrderDO old = promotionOrderDOS.get(0);
        String ownerCode = old.getTtOwnerCode();
        String warehouseCode = old.getWarehouseCode();
        TTActiveRegistrationReportDTO reportDTO = new TTActiveRegistrationReportDTO();
        reportDTO.setWarehouseCode(warehouseCode);
        reportDTO.setOwnerCode(ownerCode);
        reportDTO.setBusinessValue(old.getShopId());
        List<TTActiveRegistrationReportDTO.Order> collect = promotionOrderDOS.stream().map(promotionOrderDO -> {
            TTActiveRegistrationReportDTO.Order order = new TTActiveRegistrationReportDTO.Order();
            order.setOrderCode(promotionOrderDO.getOrderCode());
            if (pass) {
                order.setResult("海关审核通过");
            } else {
                order.setResult("海关审核不通过");
            }
            order.setRemark(reqVo.getReason());
            return order;
        }).collect(Collectors.toList());
        reportDTO.setOrders(collect);
        log.info("audit reportDTO={}", reportDTO);
        example.clear();
        example.createCriteria().andIn("id", list);
        PromotionOrderDO promotionOrderDO = new PromotionOrderDO();
        if (pass) {
            promotionOrderDO.setStatus(TTPromotionOrderStatusEnum.PASS.getCode());
        } else {
            promotionOrderDO.setStatus(TTPromotionOrderStatusEnum.REJECT.getCode());
        }
        promotionOrderDO.setUpdateBy(UserUtils.getUserId());
        promotionOrderDO.setUpdateTime(new Date());
        promotionOrderMapper.updateByExampleSelective(promotionOrderDO, example);
        try {
            taotianOrderService.activeRegistrationResultReport(reportDTO);
            log.info("audit activeRegistrationResultReport success");
        } catch (ArgsInvalidException ex) {
            log.error("audit activeRegistrationResultReport error={}", ex.getMessage(), ex);
            throw new ArgsInvalidException(ex.getMessage());
        }
        for (String id : list) {
            buildTrackLog(Long.valueOf(id), pass ? TTPromotionOperateTypeEnum.AUDITED_PASS.getCode() : TTPromotionOperateTypeEnum.AUDITED_REJECT.getCode(),
                    "", "", UserUtils.getUserRealName());
        }
    }

    @Override
    public TTPromotionDetailVO detail(Long id) {
        if (Objects.isNull(id)) {
            throw new ArgsInvalidException("参数为空");
        }
        PromotionOrderDO promotionOrderDO = promotionOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(promotionOrderDO)) {
            throw new ArgsInvalidException("促销报备单不存在");
        }
        List<PromotionItemDO> itemDOList = findItemByRefOrderId(id);
        List<PromotionTrackLog> trackLogDOList = findTrackLogByPromotionId(id);

        TTPromotionDetailVO detailVO = new TTPromotionDetailVO();
        if (StringUtils.isNotEmpty(promotionOrderDO.getOwnerCode())) {
            OwnerRpcResult ownerRpcResult = ownerRpcFacade.getOneByOwnerCode(promotionOrderDO.getOwnerCode());
            if (Objects.nonNull(ownerRpcResult)) {
                detailVO.setOwnerName(ownerRpcResult.getOwnerName());
            }
        }
        detailVO.setScenarioType(promotionOrderDO.getScenarioType());
        detailVO.setOrderCode(promotionOrderDO.getOrderCode());
        detailVO.setOrderSource(promotionOrderDO.getOrderSource());
        detailVO.setSellerNick(promotionOrderDO.getSellerNick());
        detailVO.setActivityName(promotionOrderDO.getActivityName());
        detailVO.setMarketingType(promotionOrderDO.getMarketingType());
        detailVO.setCategoryName(promotionOrderDO.getCategoryName());
        detailVO.setActivityType(promotionOrderDO.getActivityType());
        detailVO.setActivityStartDate(promotionOrderDO.getActivityStartDate());
        detailVO.setActivityEndDate(promotionOrderDO.getActivityEndDate());
        detailVO.setActivityCreateDate(promotionOrderDO.getActivityCreateDate());
        detailVO.setWarehouseName(promotionOrderDO.getWarehouseName());
        detailVO.setPredictAmount(promotionOrderDO.getPredictAmount());
        detailVO.setSellerContactName(promotionOrderDO.getSellerContactName());
        detailVO.setSellerContactPhone(promotionOrderDO.getSellerContactPhone());
        detailVO.setStatus(promotionOrderDO.getStatus());
        TTPromotionOrderStatusEnum statusEnum = TTPromotionOrderStatusEnum.getEnum(promotionOrderDO.getStatus());
        if (Objects.nonNull(statusEnum)) {
            detailVO.setStatusDesc(statusEnum.getDesc());
        }
        detailVO.setRemark(promotionOrderDO.getRemark());
        detailVO.setCreateTime(promotionOrderDO.getCreateTime());

        List<TTPromotionDetailVO.TTPromotionItemVO> mainItemVOList = new ArrayList<>();
        List<TTPromotionDetailVO.TTPromotionItemVO> presentItemVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(itemDOList)) {
            for (PromotionItemDO promotionItemDO : itemDOList) {
                TTPromotionDetailVO.TTPromotionItemVO itemVO = new TTPromotionDetailVO.TTPromotionItemVO();
                itemVO.setId(promotionItemDO.getId());
                itemVO.setRefOrderId(promotionItemDO.getRefOrderId());
                itemVO.setItemCode(promotionItemDO.getItemCode());
                itemVO.setItemName(promotionItemDO.getItemName());
                itemVO.setItemUrl(promotionItemDO.getItemUrl());
                itemVO.setOriginalPrice(promotionItemDO.getOriginalPrice());
                itemVO.setRegisterPrice(promotionItemDO.getRegisterPrice());
                itemVO.setActivityPrice(promotionItemDO.getActivityPrice());
                itemVO.setActivityQuantity(promotionItemDO.getActivityQuantity());
                itemVO.setRecordNumber(promotionItemDO.getRecordNumber());
                itemVO.setBarCode(promotionItemDO.getBarCode());
                if (Objects.equals(promotionItemDO.getType(), TTPromotionItemTypeEnum.MAIN_ITEM.getCode())) {
                    mainItemVOList.add(itemVO);
                } else if (Objects.equals(promotionItemDO.getType(), TTPromotionItemTypeEnum.PRESENT_ITEM.getCode())) {
                    presentItemVOList.add(itemVO);
                }
            }
        }
        List<TTPromotionDetailVO.TTPromotionTrackLogVO> trackLogVOList = new ArrayList<>();
        if (CollUtil.isNotEmpty(trackLogDOList)) {
            trackLogVOList = trackLogDOList.stream().map(i -> {
                TTPromotionDetailVO.TTPromotionTrackLogVO trackLogVO = new TTPromotionDetailVO.TTPromotionTrackLogVO();
                trackLogVO.setId(i.getId());
                trackLogVO.setPromotionId(i.getPromotionId());
                TTPromotionOperateTypeEnum operateTypeEnum = TTPromotionOperateTypeEnum.getEnum(i.getOperateType());
                if (Objects.nonNull(operateTypeEnum)) {
                    trackLogVO.setOperateType(operateTypeEnum.getDesc());
                }
                trackLogVO.setLogInfo(i.getLogInfo());
                trackLogVO.setCallbackDetail(i.getCallbackDetail());
                trackLogVO.setOperator(i.getOperator());
                trackLogVO.setCreatTime(i.getCreateTime());
                return trackLogVO;
            }).collect(Collectors.toList());
        }

        detailVO.setMainItemVOList(mainItemVOList);
        detailVO.setPresentItemVOList(presentItemVOList);
        detailVO.setTrackLogVOList(trackLogVOList);
        return detailVO;
    }


    public List<PromotionItemDO> findItemByRefOrderId(Long refOrderId) {
        if (Objects.isNull(refOrderId)) {
            return new ArrayList<>();
        }
        Example example = new Example(PromotionItemDO.class);
        example.createCriteria().andEqualTo("refOrderId", refOrderId);
        return promotionItemMapper.selectByExample(example);
    }

    public List<PromotionTrackLog> findTrackLogByPromotionId(Long promotionId) {
        if (Objects.isNull(promotionId)) {
            return new ArrayList<>();
        }
        Example example = new Example(PromotionTrackLog.class);
        example.createCriteria().andEqualTo("promotionId", promotionId);
        example.orderBy("id").desc();
        return promotionTrackLogMapper.selectByExample(example);
    }

    /**
     * 构建日志
     *
     * @param promotionId 报备单id
     * @param operateType 操作类型
     * @param logInfo     日志描述
     * @param operator    操作人
     */
    private void buildTrackLog(Long promotionId, String operateType, String logInfo, String callbackDetail, String operator) {
        PromotionTrackLog promotionTrackLog = new PromotionTrackLog();
        promotionTrackLog.setPromotionId(promotionId);
        promotionTrackLog.setOperateType(operateType);
        promotionTrackLog.setLogInfo(logInfo);
        promotionTrackLog.setOperator(operator);
        promotionTrackLog.setCallbackDetail(callbackDetail);
        promotionTrackLogMapper.insertSelective(promotionTrackLog);
    }

    @Override
    public List<TTPromotionExportDTO> exportItemExcel(TTPromotionOrderSearch search) {
        Example example = getExample(search);
        List<PromotionOrderDO> list = promotionOrderMapper.selectByExample(example);
        List<TTPromotionExportDTO> orders = ConvertUtil.listConvert(list, TTPromotionExportDTO.class);

        for (TTPromotionExportDTO order : orders) {
            if (StrUtil.isNotEmpty(order.getOwnerCode())) {
                OwnerRpcResult owner = ownerRpcFacade.getOneByOwnerCode(order.getOwnerCode());
                if (Objects.nonNull(owner)) {
                    order.setOwnerName(owner.getOwnerName());
                }
            }
            TTPromotionOrderStatusEnum anEnum = TTPromotionOrderStatusEnum.getEnum(order.getStatus());
            if (anEnum != null) {
                order.setStatus(anEnum.getDesc());
            }
        }

        List<Long> ids = list.stream().map(PromotionOrderDO::getId).collect(Collectors.toList());

        Example example1 = new Example(PromotionItemDO.class);
        Example.Criteria criteria = example1.createCriteria();
        criteria.andIn("refOrderId", ids);
        List<PromotionItemDO> promotionItemDOS = promotionItemMapper.selectByExample(example1);

        Map<Long, List<PromotionItemDO>> itemMap = promotionItemDOS.stream().collect(Collectors.groupingBy(PromotionItemDO::getRefOrderId));
        for (TTPromotionExportDTO exportDTO : orders) {
            List<PromotionItemDO> itemDOS = itemMap.get(exportDTO.getId());
            if (CollUtil.isEmpty(itemDOS)) {
                continue;
            }
            List<TTPromotionExportDTO.TTPromotionItem> ttPromotionItems = ConvertUtil.listConvert(itemDOS, TTPromotionExportDTO.TTPromotionItem.class);
            exportDTO.setItems(ttPromotionItems);
        }
        return orders;
    }

    @Override
    public List<String> activityTypeList() {
        Example example = new Example(PromotionOrderDO.class);
        example.selectProperties("activityType");
        example.setDistinct(true);
        example.createCriteria()
                .andEqualTo("deleted", false);
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);
        return promotionOrderDOS.stream().map(PromotionOrderDO::getActivityType).collect(Collectors.toList());
    }

    @Override
    public List<String> marketingTypeList() {
        Example example = new Example(PromotionOrderDO.class);
        example.selectProperties("marketingType");
        example.setDistinct(true);
        example.createCriteria()
                .andEqualTo("deleted", false);
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);
        return promotionOrderDOS.stream().map(PromotionOrderDO::getMarketingType).collect(Collectors.toList());
    }

    @Override
    public List<String> sellerNickList() {
        Example example = new Example(PromotionOrderDO.class);
        example.selectProperties("sellerNick");
        example.setDistinct(true);
        example.createCriteria()
                .andEqualTo("deleted", false);
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);
        return promotionOrderDOS.stream().map(PromotionOrderDO::getSellerNick).collect(Collectors.toList());
    }

    @Override
    public List<String> orderSourceList() {
        Example example = new Example(PromotionOrderDO.class);
        example.selectProperties("orderSource");
        example.setDistinct(true);
        example.createCriteria()
                .andEqualTo("deleted", false);
        List<PromotionOrderDO> promotionOrderDOS = promotionOrderMapper.selectByExample(example);
        return promotionOrderDOS.stream().map(PromotionOrderDO::getOrderSource).collect(Collectors.toList());
    }
}
