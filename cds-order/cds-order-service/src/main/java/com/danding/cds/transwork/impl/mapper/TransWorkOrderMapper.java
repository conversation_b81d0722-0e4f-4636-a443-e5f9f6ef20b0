package com.danding.cds.transwork.impl.mapper;

import com.danding.cds.transwork.impl.entity.TransWorkOrderDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;

import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
public interface TransWorkOrderMapper extends Mapper<TransWorkOrderDO>, InsertListMapper<TransWorkOrderDO>, BatchUpdateMapper<TransWorkOrderDO>, AggregationPlusMapper<TransWorkOrderDO> {
}
