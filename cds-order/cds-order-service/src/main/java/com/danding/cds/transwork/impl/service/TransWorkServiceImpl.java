package com.danding.cds.transwork.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.rpc.config.facade.OwnerRpcFacade;
import com.danding.business.client.rpc.config.result.OwnerRpcResult;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.Assert;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.transwork.api.dto.*;
import com.danding.cds.transwork.api.enums.*;
import com.danding.cds.transwork.api.service.TransWorkService;
import com.danding.cds.transwork.api.vo.*;
import com.danding.cds.transwork.impl.entity.TransWorkOrderDO;
import com.danding.cds.transwork.impl.entity.TransWorkOrderItemsDO;
import com.danding.cds.transwork.impl.entity.TransWorkOrderPackagingItemsDO;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.vehicleResource.dto.VehicleResourceDTO;
import com.danding.cds.vehicleResource.enums.LoadableContainerTypeEnums;
import com.danding.cds.vehicleResource.service.VehicleResourceService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */

@Service
@Slf4j
@RefreshScope
public class TransWorkServiceImpl implements TransWorkService {

    private static final String ORDER_PREFIX = "YS";

    @Resource
    private TransWorkBaseService transWorkBaseService;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;
    @DubboReference
    private OwnerRpcFacade ownerRpcFacade;
    @DubboReference
    private VehicleResourceService vehicleResourceService;

    // todo order 配置要加
    @Value("${mercury.open.api.url:}")
    private String mercuryOpenApiUrl = "http://localhost:8092";

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createPortdeparture(TTPortdepartureTransportorderCreateDTO request) throws ArgsErrorException {
        // 校验
        validatePortDep(request);

        TransWorkOrderDO transWorkOrderDO = new TransWorkOrderDO();

        TransWorkOrderDO order = transWorkBaseService.selectOrderByOutOrderNoAndType(request.getOrderCode(), request.getOrderType());
        if (order != null) {
            return order.getTransNo();
        }

        transWorkOrderDO.setCreateTime(new Date());
        transWorkOrderDO.setUpdateTime(new Date());

        transWorkOrderDO.setTransNo(getTransNo());
        transWorkOrderDO.setOrderType(request.getOrderType());
        transWorkOrderDO.setStatus(TransOrderStatusEnum.CREATE.getStatus());
        transWorkOrderDO.setOutOrderNo(request.getOrderCode());
        if (CollUtil.isNotEmpty(request.getRelatedOrders())) {
            String orderCode = request.getRelatedOrders().stream()
                    .map(TTPortdepartureTransportorderCreateDTO.RelatedOrder::getOrderCode)
                    .collect(Collectors.joining(","));
            transWorkOrderDO.setRemark(orderCode);
        }
        transWorkOrderDO.setTtOwnerCode(request.getOwnerCode());
        transWorkOrderDO.setOwnerCode(request.getErpOwnerCode());
        transWorkOrderDO.setShopId(request.getShopId());
        List<EntityWarehouseDTO> dtoByWmsCode = entityWarehouseService.findDTOByWmsCode(request.getWmsWarehouseCode());
        if (CollUtil.isNotEmpty(dtoByWmsCode)) {
            transWorkOrderDO.setWarehouseCode(dtoByWmsCode.get(0).getErpWarehouseCode());
        }
        transWorkOrderDO.setOrderOrigin(TransOrderOriginEnum.TT.getCode());
        if (CollUtil.isNotEmpty(request.getAttachments())) {
            transWorkOrderDO.setAttachments(JSON.toJSONString(request.getAttachments()));
        }
        TTPortdepartureTransportorderCreateDTO.BasicInfo basicInfo = request.getBasicInfo();
        TransWorkOrderBaseInfoDTO baseInfo = new TransWorkOrderBaseInfoDTO();

        baseInfo.setResourceCode(request.getResourceCode());
        if (Objects.nonNull(basicInfo)) {
            baseInfo.setFromRegion(basicInfo.getFromRegion());
            baseInfo.setFromLocation(basicInfo.getFromLocation());
            baseInfo.setFromWarehouse(basicInfo.getFromWarehouse());
            baseInfo.setToRegion(basicInfo.getToRegion());
            baseInfo.setToLocation(basicInfo.getToLocation());
            baseInfo.setToWarehouse(basicInfo.getFromWarehouse());
        }
        TTPortdepartureTransportorderCreateDTO.TransportInfo transportInfo = request.getTransportInfo();
        if (Objects.nonNull(transportInfo)) {
            baseInfo.setLoadingTypePortToWh(transportInfo.getLoadingTypePortToWH());
            baseInfo.setEntryPort(transportInfo.getEntryPort());
        }
        transWorkOrderDO.setBaseInfo(JSON.toJSONString(baseInfo));

        // 装箱
        TTPortdepartureTransportorderCreateDTO.CarrierInfo carrierInfo = request.getCarrierInfo();
        if (Objects.nonNull(carrierInfo)) {
            transWorkOrderDO.setCarrierInfo(JSON.toJSONString(carrierInfo));
        }
        // 发货人
        TTPortdepartureTransportorderCreateDTO.ShipperInfo shipperInfo = request.getShipperInfo();
        if (Objects.nonNull(shipperInfo)) {
            transWorkOrderDO.setShipperInfo(JSON.toJSONString(shipperInfo));
        }

        // 收货人
        TTPortdepartureTransportorderCreateDTO.ConsigneeInfo consigneeInfo = request.getConsigneeInfo();
        if (Objects.nonNull(consigneeInfo)) {
            transWorkOrderDO.setConsigneeInfo(JSON.toJSONString(consigneeInfo));
        }
        // 通知人
        TTPortdepartureTransportorderCreateDTO.NotifyPersonInfo notifyPersonInfo = request.getNotifyPersonInfo();
        if (Objects.nonNull(notifyPersonInfo)) {
            transWorkOrderDO.setNotifyPersonInfo(JSON.toJSONString(notifyPersonInfo));
        }
        // 包裹信息
        TTPortdepartureTransportorderCreateDTO.PackageInfo packageInfo = request.getPackageInfo();
        if (Objects.nonNull(packageInfo)) {
            transWorkOrderDO.setPackageInfo(JSON.toJSONString(packageInfo));
        }

        // 装箱信息
        TTPortdepartureTransportorderCreateDTO.LoadInfo loadInfo = request.getLoadInfo();
        TransWorkOrderLoadInfoDTO loadInfoDTO = new TransWorkOrderLoadInfoDTO();
        if (Objects.nonNull(loadInfo)) {
            loadInfoDTO.setProductType(loadInfo.getProductType());
            loadInfoDTO.setLoadingType(loadInfo.getLoadingType());
            loadInfoDTO.setTemperature(loadInfo.getTemperature());
            List<TTPortdepartureTransportorderCreateDTO.ContainerType> containerTypes = loadInfo.getContainerTypes();
            if (CollUtil.isNotEmpty(containerTypes)) {
                loadInfoDTO.setContainerTypes(containerTypes.stream()
                        .filter(c -> Objects.nonNull(c.getContainerType()) && Objects.nonNull(c.getQty()))
                        .map(c -> c.getContainerType() + "-" + c.getQty())
                        .collect(Collectors.joining(",")));
            }
            transWorkOrderDO.setLoadInfo(JSON.toJSONString(loadInfoDTO));
        }

        // 供应商
        TTPortdepartureTransportorderCreateDTO.SupplierInfo supplierInfo = request.getSupplierInfo();
        if (Objects.nonNull(supplierInfo)) {
            transWorkOrderDO.setSupplierInfo(JSON.toJSONString(supplierInfo));
        }
        // 商家联系人
        TTPortdepartureTransportorderCreateDTO.BookingPartyInfo bookingPartyInfo = request.getBookingPartyInfo();
        if (Objects.nonNull(bookingPartyInfo)) {
            TransWorkOrderContactsDTO transWorkOrderContactsDTO = new TransWorkOrderContactsDTO();
            transWorkOrderContactsDTO.setName(bookingPartyInfo.getName());
            transWorkOrderContactsDTO.setPhone(bookingPartyInfo.getPhone());
            transWorkOrderContactsDTO.setEmail(StrUtil.join(",", bookingPartyInfo.getEmails()));
            transWorkOrderDO.setBookingPartyInfo(JSON.toJSONString(transWorkOrderContactsDTO));
        }

        transWorkBaseService.insert(transWorkOrderDO);
        //
        List<TTPortdepartureTransportorderCreateDTO.Item> items = request.getItems();
        List<TransWorkOrderItemsDO> itemsDOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(items)) {
            for (TTPortdepartureTransportorderCreateDTO.Item item : items) {
                TransWorkOrderItemsDO itemsDO = new TransWorkOrderItemsDO();
                itemsDO.setTransId(transWorkOrderDO.getId());
                itemsDO.setItemCode(item.getItemCode());
                itemsDO.setItemName(item.getItemName());
                itemsDO.setQty(item.getQty());
                itemsDO.setActSignQty(item.getQty());
                itemsDO.setActDeliveryQty(item.getQty());
                itemsDO.setOriginRegion(item.getOriginRegion());
                itemsDO.setPurchasePrice(item.getPurchasePrice());
                itemsDO.setCurrency(item.getCurrency());
                itemsDO.setTotalPrice(item.getTotalPrice());
                itemsDO.setVolume(item.getVolume());
                itemsDO.setWeight(item.getWeight());
                itemsDO.setProduceCode(item.getProduceCode());
                itemsDO.setProductDate(item.getProductDate());
                itemsDO.setExpiryDate(item.getExpiryDate());
                itemsDO.setCreateTime(new Date());
                itemsDO.setUpdateTime(new Date());
                itemsDOS.add(itemsDO);
            }
            transWorkBaseService.insertItems(itemsDOS);
        }
        Map<String, String> itemMap = new HashMap<>();
        if (CollUtil.isNotEmpty(items)) {
            itemMap = items.stream().collect(Collectors.toMap(TTPortdepartureTransportorderCreateDTO.Item::getItemCode, TTPortdepartureTransportorderCreateDTO.Item::getItemName, (k1, k2) -> k1));
        }
        // 装箱信息
        List<TransWorkOrderPackagingItemsDO> packagingItemsDOS = new ArrayList<>();
        List<TTPortdepartureTransportorderCreateDTO.PackagingItem> packagingItems = request.getPackagingItems();
        if (CollUtil.isNotEmpty(packagingItems)) {
            for (TTPortdepartureTransportorderCreateDTO.PackagingItem packagingItem : packagingItems) {
                TransWorkOrderPackagingItemsDO packagingItemsDO = new TransWorkOrderPackagingItemsDO();
                packagingItemsDO.setTransId(transWorkOrderDO.getId());
                packagingItemsDO.setItemCode(packagingItem.getItemCode());
                packagingItemsDO.setItemName(itemMap.get(packagingItem.getItemCode()));
                packagingItemsDO.setItemQuantity(packagingItem.getItemQuantity());
                packagingItemsDO.setActSignQty(packagingItem.getItemQuantity());
                packagingItemsDO.setActDeliveryQty(packagingItem.getItemQuantity());
                packagingItemsDO.setPalletNo(packagingItem.getPalletNo());
                packagingItemsDO.setBoxNo(packagingItem.getBoxNo());
                packagingItemsDO.setPackagingItemLineNo(packagingItem.getPackagingItemLineNo());
                packagingItemsDO.setCreateTime(new Date());
                packagingItemsDO.setUpdateTime(new Date());
                packagingItemsDOS.add(packagingItemsDO);
            }
            transWorkBaseService.insertPackagingItems(packagingItemsDOS);
        }

        return transWorkOrderDO.getTransNo();
    }

    private void validatePortDep(TTPortdepartureTransportorderCreateDTO request) {
        if (StrUtil.isEmpty(request.getOwnerCode())) {
            throw new ArgsErrorException("ownerCode不能为空");
        }
        if (StrUtil.isEmpty(request.getResourceCode())) {
            throw new ArgsErrorException("resourceCode不能为空");
        }
        if (StrUtil.isEmpty(request.getOrderCode())) {
            throw new ArgsErrorException("orderCode不能为空");
        }
        if (StrUtil.isEmpty(request.getOrderType())) {
            throw new ArgsErrorException("orderType不能为空");
        }
//        TTPortdepartureTransportorderCreateDTO.BasicInfo basicInfo = request.getBasicInfo();
//        if (Objects.isNull(basicInfo)) {
//            throw new ArgsErrorException("basicInfo不能为空");
//        }
//        if (StrUtil.isEmpty(basicInfo.getFromRegion())) {
//            throw new ArgsErrorException("fromRegion不能为空");
//        }
//        if (StrUtil.isEmpty(basicInfo.getToRegion())) {
//            throw new ArgsErrorException("toRegion不能为空");
//        }
//        if (StrUtil.isEmpty(basicInfo.getFromLocation())) {
//            throw new ArgsErrorException("fromLocation不能为空");
//        }
//        if (StrUtil.isEmpty(basicInfo.getToLocation())) {
//            throw new ArgsErrorException("toLocation不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.ShipperInfo shipperInfo = request.getShipperInfo();
//        if (Objects.isNull(shipperInfo)) {
//            throw new ArgsErrorException("shipperInfo不能为空");
//        }
//        if (StrUtil.isEmpty(shipperInfo.getName())) {
//            throw new ArgsErrorException("shipperInfo.name不能为空");
//        }
//        if (StrUtil.isEmpty(shipperInfo.getPhone())) {
//            throw new ArgsErrorException("shipperInfo.phone不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.ConsigneeInfo consigneeInfo = request.getConsigneeInfo();
//        if (Objects.isNull(consigneeInfo)) {
//            throw new ArgsErrorException("consigneeInfo不能为空");
//        }
//        if (StrUtil.isEmpty(consigneeInfo.getName())) {
//            throw new ArgsErrorException("consigneeInfo.name不能为空");
//        }
//        if (StrUtil.isEmpty(consigneeInfo.getPhone())) {
//            throw new ArgsErrorException("consigneeInfo.phone不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.NotifyPersonInfo notifyPersonInfo = request.getNotifyPersonInfo();
//        if (Objects.isNull(notifyPersonInfo)) {
//            throw new ArgsErrorException("notifyPersonInfo不能为空");
//        }
//        if (StrUtil.isEmpty(notifyPersonInfo.getName())) {
//            throw new ArgsErrorException("notifyPersonInfo.name不能为空");
//        }
//        if (StrUtil.isEmpty(notifyPersonInfo.getPhone())) {
//            throw new ArgsErrorException("notifyPersonInfo.phone不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.BookingPartyInfo bookingPartyInfo = request.getBookingPartyInfo();
//        if (Objects.isNull(bookingPartyInfo)) {
//            throw new ArgsErrorException("bookingPartyInfo不能为空");
//        }
//        if (StrUtil.isEmpty(bookingPartyInfo.getName())) {
//            throw new ArgsErrorException("bookingPartyInfo.name不能为空");
//        }
//        if (StrUtil.isEmpty(bookingPartyInfo.getPhone())) {
//            throw new ArgsErrorException("bookingPartyInfo.phone不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.PackageInfo packageInfo = request.getPackageInfo();
//        if (Objects.isNull(packageInfo)) {
//            throw new ArgsErrorException("packageInfo不能为空");
//        }
//        if (StrUtil.isEmpty(packageInfo.getTotalVolume())) {
//            throw new ArgsErrorException("packageInfo.totalVolume不能为空");
//        }
//        if (StrUtil.isEmpty(packageInfo.getTotalWeight())) {
//            throw new ArgsErrorException("packageInfo.totalWeight不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.LoadInfo loadInfo = request.getLoadInfo();
//        if (Objects.isNull(loadInfo)) {
//            throw new ArgsErrorException("loadInfo不能为空");
//        }
//        if (StrUtil.isEmpty(loadInfo.getProductType())) {
//            throw new ArgsErrorException("loadInfo.productType不能为空");
//        }
//        if (StrUtil.isEmpty(loadInfo.getProductDesc())) {
//            throw new ArgsErrorException("loadInfo.productDesc不能为空");
//        }
//        TTPortdepartureTransportorderCreateDTO.CarrierInfo carrierInfo = request.getCarrierInfo();
//        if (Objects.isNull(carrierInfo)) {
//            throw new ArgsErrorException("carrierInfo不能为空");
//        }
//        if (StrUtil.isEmpty(carrierInfo.getTransportMode())) {
//            throw new ArgsErrorException("carrierInfo.transportMode不能为空");
//        }
//        if (StrUtil.isEmpty(carrierInfo.getMblNo())) {
//            throw new ArgsErrorException("carrierInfo.mblNo不能为空");
//        }
//        if (StrUtil.isEmpty(carrierInfo.getHblNo())) {
//            throw new ArgsErrorException("carrierInfo.hblNo不能为空");
//        }
//        if (StrUtil.isEmpty(carrierInfo.getCarrierName())) {
//            throw new ArgsErrorException("carrierInfo.carrierName不能为空");
//        }
//        if (StrUtil.isEmpty(carrierInfo.getExpectArrivePortDate())) {
//            throw new ArgsErrorException("carrierInfo.expectArrivePortDate不能为空");
//        }
//        List<TTPortdepartureTransportorderCreateDTO.Item> items = request.getItems();
//        if (CollUtil.isEmpty(items)) {
//            throw new ArgsErrorException("items不能为空");
//        }
//        items.forEach(item -> {
//            if (StrUtil.isEmpty(item.getItemCode())) {
//                throw new ArgsErrorException("items.itemCode不能为空");
//            }
//            if (StrUtil.isEmpty(item.getItemName())) {
//                throw new ArgsErrorException("items.itemName不能为空");
//            }
//            if (StrUtil.isEmpty(item.getOriginRegion())) {
//                throw new ArgsErrorException("items.originRegion不能为空");
//            }
//            if (StrUtil.isEmpty(item.getWeight())) {
//                throw new ArgsErrorException("items.weight不能为空");
//            }
//            if (Objects.isNull(item.getQty())) {
//                throw new ArgsErrorException("items.qty不能为空");
//            }
//            if (StrUtil.isEmpty(item.getCurrency())) {
//                throw new ArgsErrorException("items.currency不能为空");
//            }
//            if (StrUtil.isEmpty(item.getTotalPrice())) {
//                throw new ArgsErrorException("items.totalPrice不能为空");
//            }
//            if (StrUtil.isEmpty(item.getPurchasePrice())) {
//                throw new ArgsErrorException("items.purchasePrice不能为空");
//            }
//            if (StrUtil.isEmpty(item.getVolume())) {
//                throw new ArgsErrorException("items.volume不能为空");
//            }
//        });

    }

    private static String getTransNo() {
        return ORDER_PREFIX + DateUtil.format(new DateTime(), "yyMMddHHmmss") + RandomUtil.randomNumbers(5);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String createMainlineTransportorder(TTMainlineTransportorderCreateDTO request) throws ArgsErrorException {
        //
        validateMainline(request);

        TransWorkOrderDO order = transWorkBaseService.selectOrderByOutOrderNoAndType(request.getOrderCode(), request.getOrderType());
        if (order != null) {
            return order.getTransNo();
        }

        TransWorkOrderDO transWorkOrderDO = new TransWorkOrderDO();
        transWorkOrderDO.setTransNo(getTransNo());
        transWorkOrderDO.setOrderType(request.getOrderType());
        transWorkOrderDO.setStatus(TransOrderStatusEnum.CREATE.getStatus());
        transWorkOrderDO.setOutOrderNo(request.getOrderCode());
        List<TTMainlineTransportorderCreateDTO.RelatedOrder> relatedOrders = request.getRelatedOrders();
        if (CollUtil.isNotEmpty(relatedOrders)) {
            String orderCode = relatedOrders.stream()
                    .map(TTMainlineTransportorderCreateDTO.RelatedOrder::getOrderCode)
                    .collect(Collectors.joining(","));
            transWorkOrderDO.setRemark(orderCode);
        }
        transWorkOrderDO.setOwnerCode(request.getErpOwnerCode());
        transWorkOrderDO.setTtOwnerCode(request.getOwnerCode());
        transWorkOrderDO.setShopId(request.getShopId());
        List<EntityWarehouseDTO> dtoByWmsCode = entityWarehouseService.findDTOByWmsCode(request.getWmsWarehouseCode());
        if (CollUtil.isNotEmpty(dtoByWmsCode)) {
            transWorkOrderDO.setWarehouseCode(dtoByWmsCode.get(0).getErpWarehouseCode());
        }

        transWorkOrderDO.setOrderOrigin(TransOrderOriginEnum.TT.getCode());
        TransWorkOrderBaseInfoDTO baseInfo = new TransWorkOrderBaseInfoDTO();
        TTMainlineTransportorderCreateDTO.FromLocation fromLocation = request.getFromLocation();
        baseInfo.setMainLineResourceCode(request.getResourceCode());
        if (Objects.nonNull(fromLocation)) {
            baseInfo.setFromWarehouse(fromLocation.getWarehouseCode());
            List<String> area = Lists.newArrayList(fromLocation.getAddress(), fromLocation.getArea(), fromLocation.getCity(), fromLocation.getProvince());
            String str = area.stream().filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
            baseInfo.setMainLineFromLocation(str);
        }
        TTMainlineTransportorderCreateDTO.ToLocation toLocation = request.getToLocation();
        if (Objects.nonNull(toLocation)) {
            baseInfo.setToWarehouse(toLocation.getWarehouseCode());
            List<String> area = Lists.newArrayList(toLocation.getAddress(), toLocation.getArea(), toLocation.getCity(), toLocation.getProvince());
            String str = area.stream().filter(StrUtil::isNotBlank).collect(Collectors.joining(","));
            baseInfo.setMainLineToLocation(str);
        }
        transWorkOrderDO.setBaseInfo(JSON.toJSONString(baseInfo));
        // 发货人
        TTMainlineTransportorderCreateDTO.SenderInfo senderInfo = request.getSenderInfo();
        if (Objects.nonNull(senderInfo)) {
            transWorkOrderDO.setShipperInfo(JSON.toJSONString(senderInfo));
        }
        // 收货人
        TTMainlineTransportorderCreateDTO.ReceiverInfo receiverInfo = request.getReceiverInfo();
        if (Objects.nonNull(receiverInfo)) {
            transWorkOrderDO.setConsigneeInfo(JSON.toJSONString(receiverInfo));
        }
        transWorkOrderDO.setCreateTime(new Date());
        transWorkOrderDO.setUpdateTime(new Date());

        transWorkBaseService.insert(transWorkOrderDO);

        List<TTMainlineTransportorderCreateDTO.OrderLine> orderLines = request.getOrderLines();
        List<TransWorkOrderItemsDO> itemsDOS = new ArrayList<>();
        if (CollUtil.isNotEmpty(orderLines)) {
            for (TTMainlineTransportorderCreateDTO.OrderLine item : orderLines) {
                TransWorkOrderItemsDO itemsDO = new TransWorkOrderItemsDO();
                itemsDO.setTransId(transWorkOrderDO.getId());
                itemsDO.setItemCode(item.getItemCode());
                itemsDO.setItemName(item.getItemName());
                itemsDO.setQty(Double.valueOf(item.getPlanQty()).longValue());
                itemsDO.setActSignQty(Double.valueOf(item.getPlanQty()).longValue());
                itemsDO.setActDeliveryQty(Double.valueOf(item.getPlanQty()).longValue());
                itemsDO.setOrderLineNo(item.getOrderLineNo());
                itemsDO.setCreateTime(new Date());
                itemsDO.setUpdateTime(new Date());
                itemsDOS.add(itemsDO);
            }
            transWorkBaseService.insertItems(itemsDOS);
        }
        return transWorkOrderDO.getTransNo();
    }

    private void validateMainline(TTMainlineTransportorderCreateDTO request) {
        if (StrUtil.isEmpty(request.getOwnerCode())) {
            throw new ArgsErrorException("ownerCode不能为空");
        }
        if (StrUtil.isEmpty(request.getOrderCode())) {
            throw new ArgsErrorException("orderCode不能为空");
        }
        if (StrUtil.isEmpty(request.getOrderType())) {
            throw new ArgsErrorException("orderType不能为空");
        }
        if (StrUtil.isEmpty(request.getResourceCode())) {
            throw new ArgsErrorException("resourceCode不能为空");
        }
//        if (CollUtil.isEmpty(request.getOrderLines())) {
//            throw new ArgsErrorException("orderLines不能为空");
//        }
//        request.getOrderLines().forEach(item -> {
//            if (StrUtil.isEmpty(item.getItemCode())) {
//                throw new ArgsErrorException("itemCode不能为空");
//            }
//            if (StrUtil.isEmpty(item.getItemName())) {
//                throw new ArgsErrorException("itemName不能为空");
//            }
//            if (StrUtil.isEmpty(item.getPlanQty())) {
//                throw new ArgsErrorException("planQty不能为空");
//            }
//            if (StrUtil.isEmpty(item.getOrderLineNo())) {
//                throw new ArgsErrorException("orderLineNo不能为空");
//            }
//        });

    }

    @Override
    @PageSelect
    public ListVO<TransWorkPageVO> paging(TransWorkSearch search) {
        Example example = getExample(search);

        List<TransWorkOrderDO> list = transWorkBaseService.selectOrderByExample(example);
        ListVO<TransWorkPageVO> result = new ListVO<>();
        List<TransWorkPageVO> dataList = JSON.parseArray(JSON.toJSONString(list), TransWorkPageVO.class);

        List<String> code = dataList.stream().map(TransWorkPageVO::getWarehouseCode).collect(Collectors.toList());
        List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(code);
        Map<String, String> map = dtoByErpCode.stream().collect(Collectors.toMap(EntityWarehouseDTO::getErpWarehouseCode, EntityWarehouseDTO::getErpWarehouseName, (k1, k2) -> k1));
        for (TransWorkPageVO transWorkPageVO : dataList) {
            // 仓库名称
            transWorkPageVO.setWarehouseName(map.get(transWorkPageVO.getWarehouseCode()));
            TransOrderStatusEnum anEnum = TransOrderStatusEnum.getEnum(transWorkPageVO.getStatus());
            // 状态
            if (Objects.nonNull(anEnum)) {
                transWorkPageVO.setStatusDesc(anEnum.getDesc());
            }
            TransOrderTypeEnum type = TransOrderTypeEnum.getEnum(transWorkPageVO.getOrderType());
            if (Objects.nonNull(type)) {
                transWorkPageVO.setOrderTypeDesc(type.getDesc());
            }
            transWorkPageVO.setOrderOriginDesc(TransOrderOriginEnum.TT.getDesc());
            // 货主名称
            if (StrUtil.isNotEmpty(transWorkPageVO.getOwnerCode())) {
                OwnerRpcResult owner = ownerRpcFacade.getOneByOwnerCode(transWorkPageVO.getOwnerCode());
                if (Objects.nonNull(owner)) {
                    transWorkPageVO.setOwnerName(owner.getOwnerName());
                }
            }
        }

        result.setDataList(dataList);
        // 分页
        PageInfo<TransWorkOrderDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private static Example getExample(TransWorkSearch search) {
        Example example = new Example(TransWorkOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (CollUtil.isNotEmpty(search.getTransNos())) {
            criteria.andIn("transNo", search.getTransNos());
        }
        if (StrUtil.isNotEmpty(search.getTransType())) {
            criteria.andEqualTo("orderType", search.getTransType());
        }
        if (Objects.nonNull(search.getCreateTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("createTime", search.getCreateTimeFrom());
        }
        if (Objects.nonNull(search.getCreateTimeTo())) {
            criteria.andLessThanOrEqualTo("createTime", search.getCreateTimeTo());
        }
        if (Objects.nonNull(search.getFinishTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("finishTime", search.getFinishTimeFrom());
        }
        if (Objects.nonNull(search.getFinishTimeTo())) {
            criteria.andLessThanOrEqualTo("finishTime", search.getFinishTimeTo());
        }
        if (Objects.nonNull(search.getActDepTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("actDepTime", search.getActDepTimeFrom());
        }
        if (Objects.nonNull(search.getActDepTimeTo())) {
            criteria.andLessThanOrEqualTo("actDepTime", search.getActDepTimeTo());
        }
        if (Objects.nonNull(search.getActSignTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("actSignTime", search.getActSignTimeFrom());
        }
        if (Objects.nonNull(search.getActSignTimeTo())) {
            criteria.andLessThanOrEqualTo("actSignTime", search.getActSignTimeTo());
        }
        if (Objects.nonNull(search.getActArrTimeFrom())) {
            criteria.andGreaterThanOrEqualTo("actArrTime", search.getActArrTimeFrom());
        }
        if (Objects.nonNull(search.getActArrTimeTo())) {
            criteria.andLessThanOrEqualTo("actArrTime", search.getActArrTimeTo());
        }
        if (CollUtil.isNotEmpty(search.getOutOrderNos())) {
            criteria.andIn("outOrderNo", search.getOutOrderNos());
        }
        if (Objects.nonNull(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (CollUtil.isNotEmpty(search.getWarehouseCode())) {
            criteria.andIn("warehouseCode", search.getWarehouseCode());
        }
        if (StrUtil.isNotEmpty(search.getTruckResourceNo())) {
            criteria.andLike("truckResourceNo", "%" + search.getTruckResourceNo() + "%");
        }
        if (CollUtil.isNotEmpty(search.getTruckNos())) {
            criteria.andIn("truckNo", search.getTruckNos());
        }
        if (CollUtil.isNotEmpty(search.getOwnerCode())) {
            criteria.andIn("ownerCode", search.getOwnerCode());
        }
        if (Objects.nonNull(search.getShipmentNo())) {
            criteria.andEqualTo("shipmentNo", search.getShipmentNo());
        }
        if (StrUtil.isNotEmpty(search.getRemark())) {
            criteria.andLike("remark", "%" + search.getRemark() + "%");
        }
        //排序
        example.setOrderByClause("create_time DESC");
        return example;
    }

    @Override
    public TransWorkDetailVO detail(Long id) {
        TransWorkDetailVO res = new TransWorkDetailVO();
        TransWorkOrderDO transWorkOrderDO = transWorkBaseService.selectOrderById(id);
        if (transWorkOrderDO == null) {
            throw new ArgsErrorException("作业单不存在");
        }
        BeanUtils.copyProperties(transWorkOrderDO, res);
        res.setVehiclePlate(transWorkOrderDO.getTruckNo());
        res.setVehicleTypeName(transWorkOrderDO.getTruckType());
        res.setVehicleResourceCode(transWorkOrderDO.getTruckResourceNo());

        TransOrderTypeEnum type = TransOrderTypeEnum.getEnum(transWorkOrderDO.getOrderType());
        if (Objects.nonNull(type)) {
            res.setOrderTypeDesc(type.getDesc());
        }
        TransOrderStatusEnum status = TransOrderStatusEnum.getEnum(transWorkOrderDO.getStatus());
        if (Objects.nonNull(status)) {
            res.setStatusDesc(status.getDesc());
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getOwnerCode())) {
            OwnerRpcResult oneByOwnerCode = ownerRpcFacade.getOneByOwnerCode(transWorkOrderDO.getOwnerCode());
            if (Objects.nonNull(oneByOwnerCode)) {
                res.setOwnerName(oneByOwnerCode.getOwnerName());
            }
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getWarehouseCode())) {
            List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(transWorkOrderDO.getWarehouseCode());
            if (CollUtil.isNotEmpty(dtoByErpCode)) {
                res.setWarehouseName(dtoByErpCode.get(0).getErpWarehouseName());
            }
        }
        res.setOrderOriginDesc(TransOrderOriginEnum.TT.getDesc());
        res.setContainerTypeDesc(LoadableContainerTypeEnums.getEnum(res.getContainerType()).getDesc());

        if (StrUtil.isNotEmpty(transWorkOrderDO.getBaseInfo())) {
            TransWorkOrderBaseInfoDTO transWorkOrderBaseInfoDTO = JSON.parseObject(transWorkOrderDO.getBaseInfo(), TransWorkOrderBaseInfoDTO.class);
            res.setBaseInfo(transWorkOrderBaseInfoDTO);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getShipperInfo())) {
            TransWorkOrderContactsDTO shipperInfo = JSON.parseObject(transWorkOrderDO.getShipperInfo(), TransWorkOrderContactsDTO.class);
            res.setShipperInfo(shipperInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getConsigneeInfo())) {
            TransWorkOrderContactsDTO consigneeInfo = JSON.parseObject(transWorkOrderDO.getConsigneeInfo(), TransWorkOrderContactsDTO.class);
            res.setConsigneeInfo(consigneeInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getNotifyPersonInfo())) {
            TransWorkOrderContactsDTO notifyPersonInfo = JSON.parseObject(transWorkOrderDO.getNotifyPersonInfo(), TransWorkOrderContactsDTO.class);
            res.setNotifyPersonInfo(notifyPersonInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getSupplierInfo())) {
            TransWorkOrderContactsDTO supplierInfo = JSON.parseObject(transWorkOrderDO.getSupplierInfo(), TransWorkOrderContactsDTO.class);
            res.setSupplierInfo(supplierInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getBookingPartyInfo())) {
            TransWorkOrderContactsDTO bookingPartyInfo = JSON.parseObject(transWorkOrderDO.getBookingPartyInfo(), TransWorkOrderContactsDTO.class);
            res.setBookingPartyInfo(bookingPartyInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getPackageInfo())) {
            TransWorkOrderPackageInfoDTO packageInfo = JSON.parseObject(transWorkOrderDO.getPackageInfo(), TransWorkOrderPackageInfoDTO.class);

            List<TransWorkOrderPackageInfoDTO.PackageInfoItem> packageInfoItems = packageInfo.getPackageInfoItems();
            if (CollUtil.isNotEmpty(packageInfoItems)) {
                packageInfoItems.forEach(item -> {
                    item.setPackageTypeDesc(TransPackageTypeEnum.getEnum(item.getPackageType()).getDesc());
                    item.setOverlayTrayDesc(TransOverlayTrayEnum.getEnum(item.getOverlayTray()).getDesc());
                    item.setTrayTypeDesc(TransTrayTypeEnum.getEnum(item.getTrayType()).getDesc());
                });
            }
            res.setPackageInfo(packageInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getLoadInfo())) {
            TransWorkOrderLoadInfoDTO loadInfo = JSON.parseObject(transWorkOrderDO.getLoadInfo(), TransWorkOrderLoadInfoDTO.class);
            loadInfo.setLoadingTypeDesc(TransLodingTypeEnum.getEnum(loadInfo.getLoadingType()).getDesc());
            loadInfo.setProductTypeDesc(TransProductTypeEnum.getEnum(loadInfo.getProductType()).getDesc());
            res.setLoadInfo(loadInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getCarrierInfo())) {
            TransWorkOrderCarrierInfoDTO carrierInfo = JSON.parseObject(transWorkOrderDO.getCarrierInfo(), TransWorkOrderCarrierInfoDTO.class);
            carrierInfo.setTransportModeDesc(TransTransportModeEnum.getEnum(carrierInfo.getTransportMode()).getDesc());
            res.setCarrierInfo(carrierInfo);
        }
        if (StrUtil.isNotEmpty(transWorkOrderDO.getAttachments())) {
            List<TransWorkOrderAttachmentsDTO> attachments = JSON.parseArray(transWorkOrderDO.getAttachments(), TransWorkOrderAttachmentsDTO.class);
            attachments.forEach(item -> {
                item.setAttachmentTypeDesc(TransAttachmentTypeEnum.getEnum(item.getAttachmentType()).getDesc());
            });
            res.setAttachments(attachments);
        }

        return res;
    }

    @Override
    @PageSelect
    public ListVO<TransWorkItemVO> itemPaging(Long id) {
        if (null == id) {
            throw new ArgsErrorException("id不能为空");
        }
        ListVO<TransWorkItemVO> result = new ListVO<>();
        List<TransWorkOrderItemsDO> itemsDOS = transWorkBaseService.selectItemsByTransId(id);

        List<TransWorkItemVO> transWorkItemVOS = ConvertUtil.listConvert(itemsDOS, TransWorkItemVO.class);
        result.setDataList(transWorkItemVOS);
        // 分页
        PageInfo<TransWorkOrderItemsDO> pageInfo = new PageInfo<>(itemsDOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void itemEdit(TransWorkItemEditVO param) throws ArgsErrorException {
        List<TransWorkItemEditVO.Detail> items = param.getItems();
        if (CollUtil.isEmpty(items)) {
            throw new ArgsErrorException("明细不能为空");
        }
        TransWorkOrderItemsDO itemsDOS = transWorkBaseService.selectItemsById(items.get(0).getId());
        TransWorkOrderDO transWorkOrderDO = transWorkBaseService.selectOrderById(itemsDOS.getTransId());
        if (!TransOrderStatusEnum.CREATE.getStatus().equals(transWorkOrderDO.getStatus())) {
            throw new ArgsErrorException("该作业单状态无法修改");
        }
        for (TransWorkItemEditVO.Detail item : param.getItems()) {
            if (Objects.isNull(item.getId())) {
                throw new ArgsErrorException("明细id不能为空");
            }
            if (Objects.isNull(item.getActDeliveryQty())) {
                throw new ArgsErrorException("实际发货数量不能为空");
            }
            if (Objects.isNull(item.getActSignQty())) {
                throw new ArgsErrorException("实际签收数量不能为空");
            }
            if (item.getActDeliveryQty() < 0) {
                throw new ArgsErrorException("实际发货数量不能小于0");
            }
            if (item.getActSignQty() < 0) {
                throw new ArgsErrorException("实际签收数量不能小于0");
            }
            Example example = new Example(TransWorkOrderItemsDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("id", item.getId());
            TransWorkOrderItemsDO itemsDO = new TransWorkOrderItemsDO();
            itemsDO.setActDeliveryQty(item.getActDeliveryQty());
            itemsDO.setActSignQty(item.getActSignQty());
            itemsDO.setUpdateBy(UserUtils.getUserId());
            itemsDO.setUpdateTime(new Date());
            transWorkBaseService.updateItemByExample(itemsDO, example);
        }
    }

    @Override
    public void editTimeInfo(TransWorkTimeEditVO param) throws ArgsErrorException {
        if (null == param.getId()) {
            throw new ArgsErrorException("id不能为空");
        }
        TransWorkOrderDO orderDO = transWorkBaseService.selectOrderById(Long.valueOf(param.getId()));
        if (!TransOrderStatusEnum.CREATE.getStatus().equals(orderDO.getStatus())) {
            throw new ArgsErrorException("该作业单状态无法修改");
        }

        if (DateUtil.compare(param.getActDepTime(), param.getActArrTime()) > 0) {
            throw new ArgsErrorException("实际发车时间不能大于实际到达时间");
        }
        if (DateUtil.compare(param.getActDepTime(), param.getActSignTime()) > 0) {
            throw new ArgsErrorException("实际发车时间不能大于实际签收时间");
        }
        if (DateUtil.compare(param.getActArrTime(), param.getActSignTime()) > 0) {
            throw new ArgsErrorException("实际到达时间不能大于实际签收时间");
        }

        TransWorkOrderDO transWorkOrderDO = new TransWorkOrderDO();
        transWorkOrderDO.setActArrTime(param.getActArrTime());
        transWorkOrderDO.setActDepTime(param.getActDepTime());
        transWorkOrderDO.setActSignTime(param.getActSignTime());
        transWorkOrderDO.setUpdateBy(UserUtils.getUserId());
        transWorkOrderDO.setUpdateTime(new Date());

        Example example = new Example(TransWorkOrderDO.class);
        example.createCriteria().
                andEqualTo("id", param.getId());
        transWorkBaseService.updateOrderByExample(transWorkOrderDO, example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTruckInfo(TransTruckInfoVO param) throws ArgsErrorException {
        String lockKey = null;
        try {
            VehicleResourceDTO vehicle = vehicleResourceService.findById(param.getVehicleId());
            if (!Objects.equals(vehicle.getEnable(), 1)) {
                throw new ArgsErrorException("车辆信息未启用");
            }
            String key = "ccs:transwork:truckinfo:" + vehicle.getVehicleResourceCode();
            if (!RedissLockUtil.tryLock(key, TimeUnit.SECONDS, 3, 5)) {
                throw new ArgsErrorException("添加车辆信息失败, 请稍后重试");
            }

            lockKey = key;
            // 可能存在车牌号不存在的情况
            if (StrUtil.isNotBlank(vehicle.getVehiclePlate())) {
                Example example = new Example(TransWorkOrderDO.class);
                Example.Criteria criteria = example.createCriteria();
                criteria.andEqualTo("truckNo", vehicle.getVehiclePlate());
                criteria.andEqualTo("status", TransOrderStatusEnum.CREATE.getStatus());
                List<TransWorkOrderDO> transWorkOrderDOS = transWorkBaseService.selectOrderByExample(example);
                if (CollUtil.isNotEmpty(transWorkOrderDOS)) {
                    String transNo = transWorkOrderDOS.get(0).getTransNo();
                    log.error("添加车辆信息失败, 已存在未完成的运输作业单：{}", transNo);
                    throw new ArgsErrorException("存在已关联的运输作业单未完成：" + transNo);
                }
            }
            // 校验作业单状态
            TransWorkOrderDO orderDO = transWorkBaseService.selectOrderById(param.getId());
            if (!TransOrderStatusEnum.CREATE.getStatus().equals(orderDO.getStatus())) {
                throw new ArgsErrorException("该作业单状态无法修改");
            }

            TransWorkOrderDO transWorkOrderDO = new TransWorkOrderDO();
            transWorkOrderDO.setId(param.getId());
            transWorkOrderDO.setTruckResourceNo(vehicle.getVehicleResourceCode());
            transWorkOrderDO.setTruckNo(vehicle.getVehiclePlate());
            transWorkOrderDO.setTruckType(vehicle.getVehicleTypeName());
            transWorkOrderDO.setContainerType(vehicle.getLoadableContainerType());
            transWorkOrderDO.setUpdateBy(UserUtils.getUserId());
            transWorkOrderDO.setUpdateTime(new Date());
            // 计算运输次数
            if (StrUtil.isNotBlank(vehicle.getVehiclePlate())) {
                Example countExp = new Example(TransWorkOrderDO.class);
                Example.Criteria criteria1 = countExp.createCriteria();
                criteria1.andEqualTo("truckNo", vehicle.getVehiclePlate());
                int historyShipmentsNum = transWorkBaseService.countByExample(countExp);
                transWorkOrderDO.setHistoryShipmentsNum(historyShipmentsNum);
                transWorkOrderDO.setShipmentNo(vehicle.getVehiclePlate() + (historyShipmentsNum + 1));
            }
            transWorkBaseService.updateOrderSelectiveById(transWorkOrderDO);
        } finally {
            if (lockKey != null) {
                RedissLockUtil.unlock(lockKey);
            }
        }
    }

    @Override
    public List<TransWorkOrderDTO> findByTruckResourceCode(String truckResourceCode) {
        if (StringUtil.isEmpty(truckResourceCode)) {
            return new ArrayList<>();
        }
        Example example = new Example(TransWorkOrderDO.class);
        example.createCriteria().andEqualTo("truckResourceNo", truckResourceCode);
        List<TransWorkOrderDO> transWorkOrderDOS = transWorkBaseService.selectOrderByExample(example);
        return ConvertUtil.listConvert(transWorkOrderDOS, TransWorkOrderDTO.class);
    }

    @Override
    public List<TransWorkOrderDTO> findByTruckResourceCodeAndPlate(String truckResourceCode, String truckNo) {
        if (StringUtil.isEmpty(truckResourceCode) && StringUtil.isEmpty(truckNo)) {
            return new ArrayList<>();
        }
        Example example = new Example(TransWorkOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (StringUtils.isNotBlank(truckNo)) {
            criteria.andEqualTo("truckNo", truckNo);
        }
        if (StringUtils.isNotBlank(truckResourceCode)) {
            criteria.andEqualTo("truckResourceNo", truckResourceCode);
        }
        List<TransWorkOrderDO> transWorkOrderDOS = transWorkBaseService.selectOrderByExample(example);
        return ConvertUtil.listConvert(transWorkOrderDOS, TransWorkOrderDTO.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finishTransWork(Long id) throws ArgsErrorException {
        if (Objects.isNull(id)) {
            throw new ArgsErrorException("id不能为空");
        }
        TTTransportOrderResultReport report = getTtTransportOrderResultReport(id);

        // 调用回告
        Map<String, Object> request = new HashMap<>();
        request.put("method", "taobao.logistics.tms.transportorder.result.report");
        request.put("bizData", JSON.toJSONString(report));
        log.info("callMercury request= {}", JSON.toJSONString(request));
        HttpRequest httpRequest = HttpRequest.post(mercuryOpenApiUrl + "/open/callbackv3")
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .form(JSON.parseObject(JSON.toJSONString(request)));
        if (!httpRequest.ok()) {
            throw new ArgsErrorException("调用Mercury接口失败");
        }
        String body = httpRequest.body();
        log.info("callMercury body= {}", body);

        JSONObject jsonObject = JSON.parseObject(body);
        Boolean success = jsonObject.getBoolean("success");
        if (!Boolean.TRUE.equals(success)) {
            String error = jsonObject.getString("error");
            String errorMessage = jsonObject.getString("errorMessage");
            log.error("callMercury error = {}, errorMsg = {}", error, errorMessage);
            throw new ArgsErrorException("回告失败：" + error + ":" + errorMessage);
        }
        // 成功更新状态

        TransWorkOrderDO updateDo = new TransWorkOrderDO();
        updateDo.setId(id);
        updateDo.setStatus(TransOrderStatusEnum.FINISH.getStatus());
        updateDo.setUpdateBy(UserUtils.getUserId());
        updateDo.setUpdateTime(new Date());
        updateDo.setFinishTime(new Date());
        transWorkBaseService.updateOrderSelectiveById(updateDo);
    }

    private TTTransportOrderResultReport getTtTransportOrderResultReport(Long id) {
        TransWorkOrderDO orderDO = transWorkBaseService.selectOrderById(id);
        List<TransWorkOrderItemsDO> itemsDOS = transWorkBaseService.selectItemsByTransId(id);
        List<TransWorkOrderPackagingItemsDO> packagingItemsDOS = transWorkBaseService.selectPackagingItemsByTransId(id);
        validateOrder(orderDO);
        String baseInfo = orderDO.getBaseInfo();
        TransWorkOrderBaseInfoDTO baseInfoDTO = JSON.parseObject(baseInfo, TransWorkOrderBaseInfoDTO.class);
        // 参数组装
        TTTransportOrderResultReport report = new TTTransportOrderResultReport();
        if (TransOrderTypeEnum.TOB_TRANSPORT.getCode().equals(orderDO.getOrderType())) {
            report.setResourceCode(baseInfoDTO.getMainLineResourceCode());
        } else if (TransOrderTypeEnum.TOB_PORT_TO_WAREHOUSE.getCode().equals(orderDO.getOrderType())) {
            report.setResourceCode(baseInfoDTO.getResourceCode());
        }
        report.setOwnerCode(orderDO.getTtOwnerCode());
        report.setOrderType(orderDO.getOrderType());
        report.setOuterOrderCode(orderDO.getTransNo());
        report.setOutBizCode(UUID.randomUUID().toString().replace("-", ""));
        String dateFormat = "yyyy-MM-dd HH:mm:ss";
        report.setOperateTime(DateUtil.format(new DateTime(), dateFormat));
        report.setStatus("20100");
//        report.setRemark();
        List<TTTransportOrderResultReport.TransportVehicle> vehicleList = new ArrayList<>();
        TTTransportOrderResultReport.TransportVehicle transportVehicle = new TTTransportOrderResultReport.TransportVehicle();
        vehicleList.add(transportVehicle);
        transportVehicle.setTransportMode("TRUCK");
        transportVehicle.setVehicleResourceCode(orderDO.getTruckResourceNo());
        transportVehicle.setContainerType(orderDO.getContainerType());
        transportVehicle.setVehicleNo(orderDO.getShipmentNo());
        transportVehicle.setActualArriveTime(DateUtil.format(orderDO.getActArrTime(), dateFormat));
        transportVehicle.setActualDepartTime(DateUtil.format(orderDO.getActDepTime(), dateFormat));
        transportVehicle.setActualSignTime(DateUtil.format(orderDO.getActSignTime(), dateFormat));
        ArrayList<TTTransportOrderResultReport.TransportVehicleItem> transportVehicleItems = new ArrayList<>();
        transportVehicle.setTransportVehicleItems(transportVehicleItems);

        if (TransOrderTypeEnum.TOB_TRANSPORT.getCode().equals(orderDO.getOrderType())) {
            for (TransWorkOrderItemsDO itemsDO : itemsDOS) {
                TTTransportOrderResultReport.TransportVehicleItem vehicleItem = new TTTransportOrderResultReport.TransportVehicleItem();
                vehicleItem.setOrderCode(orderDO.getOutOrderNo());
                vehicleItem.setOrderLineId(itemsDO.getOrderLineNo());
                vehicleItem.setOwnerCode(orderDO.getTtOwnerCode());
                vehicleItem.setItemCode(itemsDO.getItemCode());
                vehicleItem.setItemName(itemsDO.getItemName());
                vehicleItem.setPlanQuantity(itemsDO.getQty());
                vehicleItem.setActualSendQuantity(itemsDO.getActDeliveryQty());
                vehicleItem.setActualSignQuantity(itemsDO.getActSignQty());
                transportVehicleItems.add(vehicleItem);
            }
        } else {
            if (CollUtil.isNotEmpty(packagingItemsDOS)) {
                for (TransWorkOrderPackagingItemsDO packagingItemDTO : packagingItemsDOS) {
                    TTTransportOrderResultReport.TransportVehicleItem vehicleItem = new TTTransportOrderResultReport.TransportVehicleItem();
                    vehicleItem.setOrderCode(orderDO.getOutOrderNo());
                    vehicleItem.setOrderLineId(packagingItemDTO.getPackagingItemLineNo());
                    vehicleItem.setOwnerCode(orderDO.getTtOwnerCode());
                    vehicleItem.setItemCode(packagingItemDTO.getItemCode());
                    vehicleItem.setItemName(packagingItemDTO.getItemName());
                    vehicleItem.setPlanQuantity(packagingItemDTO.getItemQuantity());
                    vehicleItem.setActualSendQuantity(packagingItemDTO.getActDeliveryQty());
                    vehicleItem.setActualSignQuantity(packagingItemDTO.getActSignQty());
                    transportVehicleItems.add(vehicleItem);
                }
            }
        }

        report.setVehicleList(vehicleList);
        report.setBusinessValue(orderDO.getShopId());
        return report;
    }

    private static void validateOrder(TransWorkOrderDO transWorkOrderDO) {
        if (Objects.isNull(transWorkOrderDO)) {
            throw new ArgsErrorException("不存在的运输作业单");
        }
        if (!TransOrderStatusEnum.CREATE.getStatus().equals(transWorkOrderDO.getStatus())) {
            throw new ArgsErrorException("该作业单状态无法修改");
        }
        if (StrUtil.isEmpty(transWorkOrderDO.getTruckNo())) {
            throw new ArgsErrorException("车牌号不能为空");
        }
        if (StrUtil.isEmpty(transWorkOrderDO.getTruckResourceNo())) {
            throw new ArgsErrorException("车辆资源号不能为空");
        }
        if (StrUtil.isEmpty(transWorkOrderDO.getShipmentNo())) {
            throw new ArgsErrorException("运次编号不能为空");
        }
        if (Objects.isNull(transWorkOrderDO.getActArrTime())) {
            throw new ArgsErrorException("实际到达时间不能为空");
        }
        if (Objects.isNull(transWorkOrderDO.getActDepTime())) {
            throw new ArgsErrorException("实际发车时间不能为空");
        }
        if (Objects.isNull(transWorkOrderDO.getActSignTime())) {
            throw new ArgsErrorException("实际签收时间不能为空");
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelTransportOrder(TTGeneralCancelDTO request) throws ArgsErrorException {
        String orderType = request.getOrderType();
        String orderNo = request.getOrderNo();
        TransWorkOrderDO transWorkOrderDO = transWorkBaseService.selectOrderByOutOrderNoAndType(orderNo, orderType);

        if (transWorkOrderDO == null) {
            throw new ArgsErrorException("不存在的运输作业单");
        }
        if (TransOrderStatusEnum.FINISH.getStatus().equals(transWorkOrderDO.getStatus())) {
            throw new ArgsErrorException("作业单已完成，无法取消");
        }
        if (TransOrderStatusEnum.CANCEL.getStatus().equals(transWorkOrderDO.getStatus())) {
            return;
        }
        transWorkOrderDO.setId(transWorkOrderDO.getId());
        transWorkOrderDO.setStatus(TransOrderStatusEnum.CANCEL.getStatus());
        transWorkOrderDO.setUpdateTime(new Date());
        transWorkOrderDO.setTruckResourceNo(null);
        transWorkOrderDO.setShipmentNo(null);
        transWorkOrderDO.setTruckNo(null);
        transWorkOrderDO.setTruckType(null);
        transWorkOrderDO.setContainerType(null);
        transWorkOrderDO.setHistoryShipmentsNum(null);
        transWorkBaseService.updateOrderById(transWorkOrderDO);

    }

    @Override
    public List<TransWorkOrderPackagingItemsVO> getPackagingItems(Long id) {
        List<TransWorkOrderPackagingItemsDO> packagingItemsDOS = transWorkBaseService.selectPackagingItemsByTransId(id);
        return ConvertUtil.listConvert(packagingItemsDOS, TransWorkOrderPackagingItemsVO.class);
    }

    @Override
    public void packagingItemEdit(TransWorkItemEditVO param) throws ArgsErrorException {
        List<TransWorkItemEditVO.Detail> items = param.getItems();
        if (CollUtil.isEmpty(items)) {
            throw new ArgsErrorException("明细不能为空");
        }
        TransWorkOrderItemsDO itemsDOS = transWorkBaseService.selectItemsById(items.get(0).getId());
        TransWorkOrderDO transWorkOrderDO = transWorkBaseService.selectOrderById(itemsDOS.getTransId());
        if (!TransOrderStatusEnum.CREATE.getStatus().equals(transWorkOrderDO.getStatus())) {
            throw new ArgsErrorException("该作业单状态无法修改");
        }
        for (TransWorkItemEditVO.Detail item : param.getItems()) {
            if (Objects.isNull(item.getId())) {
                throw new ArgsErrorException("明细id不能为空");
            }
            if (Objects.isNull(item.getActDeliveryQty())) {
                throw new ArgsErrorException("实际发货数量不能为空");
            }
            if (Objects.isNull(item.getActSignQty())) {
                throw new ArgsErrorException("实际签收数量不能为空");
            }
            if (item.getActDeliveryQty() < 0) {
                throw new ArgsErrorException("实际发货数量不能小于0");
            }
            if (item.getActSignQty() < 0) {
                throw new ArgsErrorException("实际签收数量不能小于0");
            }
            Example example = new Example(TransWorkOrderItemsDO.class);
            Example.Criteria criteria = example.createCriteria();
            criteria.andEqualTo("id", item.getId());
            TransWorkOrderPackagingItemsDO itemsDO = new TransWorkOrderPackagingItemsDO();
            itemsDO.setActDeliveryQty(item.getActDeliveryQty());
            itemsDO.setActSignQty(item.getActSignQty());
            itemsDO.setUpdateBy(UserUtils.getUserId());
            itemsDO.setUpdateTime(new Date());
            transWorkBaseService.updatePackagingItemsByExampleSelective(itemsDO, example);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addTruckNo(TransAddTruckNoVO param) throws ArgsErrorException {

        Assert.notNull(param.getId(), "作业单id不能为空");
        Assert.notEmpty(param.getVehiclePlate(), "车牌号不能为空");
        Assert.notEmpty(param.getTruckResourceCode(), "车辆资源号不能为空");

        TransWorkOrderDO transWorkOrderDO = transWorkBaseService.selectOrderById(param.getId());
        Assert.notNull(transWorkOrderDO, "不存在的运输作业单");

        if (StrUtil.isBlank(transWorkOrderDO.getTransNo())) {
            // 无车牌号
            List<VehicleResourceDTO> vehicleResourceDTOS = vehicleResourceService.findEnableByCode(param.getTruckResourceCode());

            List<VehicleResourceDTO> collect = vehicleResourceDTOS.stream().filter(vehicleResourceDTO -> StrUtil.isEmpty(vehicleResourceDTO.getVehiclePlate())).collect(Collectors.toList());
            if (CollUtil.isEmpty(collect)) {
                throw new ArgsErrorException("车辆信息已被更新，请重新添加车辆信息");
            }
            VehicleResourceDTO vehicleResourceDTO = collect.get(0);
            vehicleResourceService.updateVehiclePlateById(vehicleResourceDTO.getId(), param.getVehiclePlate());

            // 更新车牌号
            updateOrderTruckNo(param);
        } else {
            // 有车牌号
            if (!Objects.equals(transWorkOrderDO.getTruckNo(), param.getVehiclePlate())) {
                // 编辑的车牌号和当前作业单车牌号不一致
                VehicleResourceDTO vehicle = vehicleResourceService.findByEnableVehicleNo(param.getVehiclePlate());
                if (Objects.nonNull(vehicle)) {
                    // 与当前作业单的车辆资源code不一致
                    if (!Objects.equals(vehicle.getVehicleResourceCode(), param.getTruckResourceCode())) {
                        // 资源号不一致
                        throw new ArgsErrorException("车牌号与当前的车辆资源code不匹配，请检查或重新绑定车辆资源code");
                    }
                    // 更新车牌号
                    updateOrderTruckNo(param);
                } else {
                    // 新增一条
                    List<VehicleResourceDTO> vehicleResourceDTOS = vehicleResourceService.findByCode(Lists.newArrayList(param.getTruckResourceCode()));
                    if (CollUtil.isEmpty(vehicleResourceDTOS)) {
                        throw new ArgsErrorException("车辆资源code不存在，请检查或重新绑定车辆资源code");
                    }
                    VehicleResourceDTO dbTruck = vehicleResourceDTOS.get(0);
                    dbTruck.setVehiclePlate(param.getVehiclePlate());
                    vehicleResourceService.insert(dbTruck);
                    updateOrderTruckNo(param);
                }
            }
        }
    }

    private void updateOrderTruckNo(TransAddTruckNoVO param) {
        TransWorkOrderDO update = new TransWorkOrderDO();
        update.setId(param.getId());
        update.setTruckNo(param.getVehiclePlate());
        update.setUpdateBy(UserUtils.getUserId());
        update.setUpdateTime(new Date());

        Example countExp = new Example(TransWorkOrderDO.class);
        Example.Criteria criteria1 = countExp.createCriteria();
        criteria1.andEqualTo("truckNo", param.getVehiclePlate());
        criteria1.andNotEqualTo("id", param.getId());
        int historyShipmentsNum = transWorkBaseService.countByExample(countExp);
        update.setHistoryShipmentsNum(historyShipmentsNum);
        update.setShipmentNo(param.getVehiclePlate() + (historyShipmentsNum + 1));
        transWorkBaseService.updateOrderSelectiveById(update);
    }
}
