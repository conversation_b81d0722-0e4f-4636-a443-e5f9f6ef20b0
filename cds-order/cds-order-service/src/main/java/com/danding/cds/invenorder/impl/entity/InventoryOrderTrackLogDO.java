package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 清关单日志
 * @date 2024/8/6 09:59
 */
@Data
@Table(name = "ccs_inventory_order_track_log")
public class InventoryOrderTrackLogDO extends BaseDO {

    /**
     * 清关单id
     */
    @Column(name = "inventory_order_id")
    private Long inventoryOrderId;

    /**
     * 清关单sn
     */
    @Column(name = "inventory_order_sn")
    private String inventoryOrderSn;

    /**
     * 清关单状态
     * {@link InventoryOrderEnum}
     */
    @Column(name = "inventory_status")
    private String inventoryStatus;

    /**
     * 日志描述
     */
    @Column(name = "log_desc")
    private String logDesc;

    /**
     * 操作时间
     */
    @Column(name = "operate_time")
    private Date operateTime;

    /**
     * 操作人
     */
    @Column(name = "operate")
    private String operator;

    /**
     * 请求报文
     */
    @Column(name = "request_message")
    private String requestMessage;

    /**
     * 返回报文
     */
    @Column(name = "return_message")
    private String returnMessage;
}
