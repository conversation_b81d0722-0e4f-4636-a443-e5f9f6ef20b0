package com.danding.cds.exportorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "`ccs_export_order`")
@Getter
@Setter
public class ExportOrderDO extends BaseDO {
    /**
     * 出区单号
     */
    private String sn;

    /**
     * 出区单状态
     */
    private Integer status;

    /**
     * 快递公司Id列表
     */
    @Column(name = "express_list")
    private String expressList;

    /**
     * 账册ID
     */
    @Column(name = "account_book_id")
    private Long accountBookId;

    /**
     * 清关企业ID
     */
    @Column(name = "declare_company_id")
    private Long declareCompanyId;
    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private Date finishTime;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 账册idListJson
     */
    private String bookIdListJson;

}