package com.danding.cds.collaborateorder.impl.mapper;

import com.danding.cds.collaborateorder.impl.entity.CollaborateOrderDO;
import com.danding.cds.collaborateorder.impl.entity.CollaborateOrderDetailsDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface CollaborateOrderDetailsMapper extends Mapper<CollaborateOrderDetailsDO>, InsertListMapper<CollaborateOrderDetailsDO>, BatchUpdateMapper<CollaborateOrderDetailsDO>, AggregationPlusMapper<CollaborateOrderDetailsDO> {



}
