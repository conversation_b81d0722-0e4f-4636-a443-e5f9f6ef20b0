package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "ccs_inventory_order_relation")
@Getter
@Setter
public class InventoryOrderRelationDO extends BaseDO {
    /**
     * 清关单ID
     */
    @Column(name = "ref_inve_order_id")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @Column(name = "ref_inve_order_sn")
    private String  refInveOrderSn;
    /**
     * 单据类型
     */
    @Column(name = "rel_type")
    private String relType;
    /**
     * 相关单号
     */
    @Column(name = "rel_no")
    private String relNo;

    /**
     * 操作人
     */
    @Column(name = "oper")
    private String oper;

}
