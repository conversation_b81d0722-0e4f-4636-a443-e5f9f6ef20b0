package com.danding.cds.collaborateorder.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.collaborateorder.api.dto.*;
import com.danding.cds.collaborateorder.api.enums.CollaborateDetailsLabEnum;
import com.danding.cds.collaborateorder.api.enums.CollaborateStatus;
import com.danding.cds.collaborateorder.api.enums.DiffType;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderDetailsService;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderDetailsResVO;
import com.danding.cds.collaborateorder.impl.entity.CollaborateOrderDetailsDO;
import com.danding.cds.collaborateorder.impl.mapper.CollaborateOrderDetailsMapper;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemExtra;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.impl.entity.InventoryOrderItemDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderItemMapper;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.dto.InventoryFlowStateDTO;
import com.danding.cds.v2.util.CalculationUtil;
import com.danding.cds.v2.util.CollaborateOrderFlowStateBuilder;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 协同单子单
 * @date 2022/3/31
 */
@Slf4j
@DubboService
public class CollaborateOrderDetailsServiceImpl implements CollaborateOrderDetailsService {

    @Autowired
    private CollaborateOrderDetailsMapper detailsMapper;

    @DubboReference
    private InventoryOrderInfoService infoService;

    @Autowired
    private InventoryOrderItemMapper inventoryOrderItemMapper;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private CollaborateOrderService orderService;


    /**
     * 根据协同单ID查询协同子单
     *
     * @param collaborateOrderId
     * @return
     */
    @Override
    public List<CollaborateOrderDetailsResVO> selectByCollaborateOrderId(Long collaborateOrderId) {
        Example example = new Example(CollaborateOrderDetailsDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("collaborateOrderId", collaborateOrderId).andEqualTo("deleted", false);
        List<CollaborateOrderDetailsDO> detailsDOList = detailsMapper.selectByExample(example);
        List<CollaborateOrderDetailsResVO> detailsResVOS = JSON.parseArray(JSON.toJSONString(detailsDOList), CollaborateOrderDetailsResVO.class);
        return detailsResVOS;
    }

    /**
     * 状态流
     *
     * @param infoDTO
     * @return
     */
    @Override
    public InventoryFlowStateDTO getFlowState(InventoryOrderInfoDTO infoDTO, String collaborateOrderId) {
        CollaborateOrderDTO collaborateOrderResVO = orderService.selectByInveCustomsId(collaborateOrderId);
        List<InventoryFlowStateDTO.FlowState> flowStateList = CollaborateOrderFlowStateBuilder.getFlowStateList(infoDTO);
        InventoryFlowStateDTO stateDTO = new InventoryFlowStateDTO();
        // 当前状态
        String collaborateStatus = collaborateOrderResVO.getCollaborateStatus();
        boolean currentNode = false;
        for (InventoryFlowStateDTO.FlowState i : flowStateList) {

            if (Objects.equals(collaborateStatus, i.getBusinessStatus())) {
                i.setStatus("finish");
                currentNode = true;
                stateDTO.setCurrNode(CollaborateStatus.getEnum(collaborateOrderResVO.getCollaborateStatus()).getOrdinaryCode());
            } else {
                if (!currentNode) {
                    i.setStatus("finish");
                } else {
                    i.setStatus("wait");
                }
            }
        }
        stateDTO.setFlowStateList(flowStateList);
        return stateDTO;
    }


    /**
     * 生成协同单详情
     *
     * @param inveCustomsSn
     * @param collaborateId
     */
    @Override
    public void saveCollaborateOrderDetails(String inveCustomsSn, Long collaborateId) {
        //清单号查询
        InventoryOrderInfoDTO infoDTO = infoService.findBySn(inveCustomsSn);
        List<InventoryOrderItemDTO> inventoryOrderItemDTOS = infoService.findListByInvenOrderId(infoDTO.getId());
        for (InventoryOrderItemDTO itemDTO : inventoryOrderItemDTOS) {
            CollaborateOrderDetailsDO orderDetailsDO = this.selectByCollaborateOrder(collaborateId, itemDTO.getOriginProductId(), itemDTO.getSkuId(), null);
            log.info("根据清关表体，sku={},productId={},查找到协同单详情={}", itemDTO.getSkuId(), itemDTO.getOriginProductId(), JSON.toJSONString(orderDetailsDO));
            if (orderDetailsDO != null) {
                continue;
            }
            CollaborateOrderDetailsDO detailsDO = new CollaborateOrderDetailsDO();
            detailsDO.setId(sequenceService.generateId());
            detailsDO.setInveItemId(itemDTO.getId());
            detailsDO.setGoodsId(itemDTO.getGoodsCode());
            detailsDO.setGoodsName(itemDTO.getGoodsName());
            detailsDO.setCollaborateOrderId(collaborateId);
            detailsDO.setBarCode(itemDTO.getGoodsBar());
            detailsDO.setSku(itemDTO.getSkuId());
            detailsDO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
            detailsDO.setProductId(itemDTO.getOriginProductId());
            ExtraJsonDTO extraJsonDTO = JSON.parseObject(itemDTO.getExtraJson(), ExtraJsonDTO.class);
            if (Objects.nonNull(extraJsonDTO.getNetweight())) {
                detailsDO.setNetWeight(extraJsonDTO.getNetweight());
            }
            if (Objects.nonNull(extraJsonDTO.getGrossWeight())) {
                detailsDO.setGrossWeight(extraJsonDTO.getGrossWeight());
            }

            detailsDO.setCreateBy(UserUtils.getUserId());
            detailsDO.setUpdateBy(UserUtils.getUserId());
            detailsMapper.insert(detailsDO);
        }

    }

    /**
     * 根据理货报告修改详情(一步申报)
     *
     * @param reportDTO
     * @param collaborateOrderId
     */
    @Override
    public int updateDetailsOneStepDeclare(TallyReportDTO reportDTO, Long collaborateOrderId, InventoryOrderInfoDTO infoDTO) {
        if (LongUtil.isNone(collaborateOrderId)) {
            log.info("接受理货报告 - 一步申报 - 未生成协同单，只更新清关单, 出入库单号：{}", reportDTO.getInOutOrderNo());
        }
        if (Objects.isNull(infoDTO)) {
            log.error("接受理货报告 - 一步申报 - 清关单不存在, 出入库单号={}", reportDTO.getInOutOrderNo());
            return 0;
        }
        List<InventoryOrderItemDTO> itemDTOList = infoService.findListByInvenOrderId(infoDTO.getId());
        //统一料号 和 sku 作为清关单表体唯一key
        Map<String, InventoryOrderItemDTO> itemDTOMap = itemDTOList.stream()
                .collect(Collectors.toMap(i -> i.getOriginProductId() + '-' + i.getSkuId(), Function.identity(), (v1, v2) -> v1));
        //差异数量
        int diffQty = 0;
        for (TallyReportDetailDTO detailReqVo : reportDTO.getTallyDetailList()) {
            //清关单表体获取理货报告数量
            InventoryOrderItemDTO inventoryOrderItemDTO = itemDTOMap.get(detailReqVo.getProductId() + '-' + detailReqVo.getSku());
            //多品的itemId
            InventoryOrderItemDTO newItemDTO = null;
            if (Objects.nonNull(inventoryOrderItemDTO)) {
                //一步申报/先报后理 只更新理货数量
                infoService.updateInventoryOrderActualTallyQtyById(inventoryOrderItemDTO.getId(), detailReqVo.getTallyNum(), reportDTO.getInOutOrderNo());
            } else {//多品：统一料号和sku 未找到指定清关单表体
                newItemDTO = infoService.multiProductCreateItem(infoDTO, detailReqVo);
            }
            //协同单表体获取理货报告数量
            if (!LongUtil.isNone(collaborateOrderId)) {// 已生成协同单
                CollaborateOrderDetailsDO detailsDO = selectByCollaborateOrder(collaborateOrderId, detailReqVo.getProductId(), detailReqVo.getSku(), null);
                if (detailsDO != null) {
                    itemDTOList.removeIf(z -> Objects.equals(z.getId(), detailsDO.getInveItemId()));
                    log.info("理货报告详情 - {}", detailReqVo);
                    Tuple<DiffType, Integer> tallyReportTuple = CalculationUtil.tallyReportDiff(detailReqVo.getTallyNum(), detailsDO.getDeclareQty());
                    if (tallyReportTuple != null) {
                        DiffType diffType = tallyReportTuple.getF();
                        detailsDO.setDiffType(diffType.getCode());
                        Integer tallyDiffQty = tallyReportTuple.getS();
                        if (tallyDiffQty != null) {
                            detailsDO.setDiffQty(tallyDiffQty);
                        }
                        if (detailsDO.getDiffQty() != null) {
                            diffQty += detailsDO.getDiffQty();
                        }
                    }
                    detailsDO.setTallyQty(detailReqVo.getTallyNum());
                    detailsDO.setId(detailsDO.getId());
                    detailsDO.setGoodsId(detailReqVo.getGoodsCode());
                    detailsMapper.updateByPrimaryKeySelective(detailsDO);
                } else {
                    diffQty += detailReqVo.getTallyNum();
                    CollaborateOrderDetailsDO orderDetailsDO = new CollaborateOrderDetailsDO();
                    orderDetailsDO.setDiffType(DiffType.MULTI_PRODUCT.getCode());
                    orderDetailsDO.setDeclareQty(0);
                    orderDetailsDO.setDiffQty(detailReqVo.getTallyNum());
                    if (Objects.nonNull(newItemDTO)) {
                        orderDetailsDO.setInveItemId(newItemDTO.getId());
                        CustomsInventoryItemExtra extra = JSON.parseObject(newItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                        if (Objects.nonNull(extra)) {
                            orderDetailsDO.setNetWeight(extra.getNetWeight());
                            orderDetailsDO.setGrossWeight(extra.getGrossWeight());
                        }
                    }
                    multiProductCreate(collaborateOrderId, detailReqVo, orderDetailsDO, false);
                }
            }
        }
        //查出没有匹配到的数据--少品
        diffQty = updateFewProduct(itemDTOList, DiffType.FEW_PRODUCT, false, diffQty);
        return diffQty;
    }

    /**
     * 修改两步申报协同单详情（两步申报）
     *
     * @param reportDTO
     * @param collaborateOrderId
     */
    @Override
    public void updateDetailsTwoStepDeclare(TallyReportDTO reportDTO, Long collaborateOrderId, InventoryOrderInfoDTO infoDTO) {
        if (LongUtil.isNone(collaborateOrderId)) {
            log.info("接受理货报告 - 两步申报 - 先理后报 - 未生成协同单，只更新清关单, 出入库单号：{}", reportDTO.getInOutOrderNo());
        }
        if (Objects.isNull(infoDTO)) {
            log.error("接受理货报告 - 两步申报 - 先理后报 - 清关单不存在, 出入库单号={}", reportDTO.getInOutOrderNo());
            return;
        }
        List<InventoryOrderItemDTO> itemDTOList = infoService.findListByInvenOrderId(infoDTO.getId());
        //统一料号 和 sku 作为清关单表体唯一key
        Map<String, InventoryOrderItemDTO> itemDTOMap = itemDTOList.stream()
                .collect(Collectors.toMap(i -> i.getOriginProductId() + '-' + i.getSkuId(), Function.identity(), (v1, v2) -> v1));
        for (TallyReportDetailDTO detailReqVo : reportDTO.getTallyDetailList()) {
            //清关单表体获取理货报告数量
            InventoryOrderItemDTO inventoryOrderItemDTO = itemDTOMap.get(detailReqVo.getProductId() + '-' + detailReqVo.getSku());
            InventoryOrderItemDTO newItemDTO = null;
            if (Objects.nonNull(inventoryOrderItemDTO)) {
                itemDTOList.removeIf(z -> Objects.equals(z.getId(), inventoryOrderItemDTO.getId()));
                //更新理货数量，最终申报数量
                infoService.updateInventoryOrderItemTallyQtyById(inventoryOrderItemDTO.getId(), detailReqVo.getTallyNum(), reportDTO.getInOutOrderNo());
            } else {
                //多品：统一料号和sku 未找到指定清关单表体
                newItemDTO = infoService.multiProductCreateItem(infoDTO, detailReqVo);
            }
            if (!LongUtil.isNone(collaborateOrderId)) {//已生成协同单
                CollaborateOrderDetailsDO detailsDO = selectByCollaborateOrder(collaborateOrderId, detailReqVo.getProductId(), detailReqVo.getSku(), null);
                if (detailsDO != null) {
                    detailsDO.setTallyQty(detailReqVo.getTallyNum());
                    detailsDO.setId(detailsDO.getId());
                    detailsDO.setGoodsId(detailReqVo.getGoodsCode());
                    detailsMapper.updateByPrimaryKeySelective(detailsDO);
                } else {
                    CollaborateOrderDetailsDO collaborateOrderDetailsDO = new CollaborateOrderDetailsDO();
                    if (Objects.nonNull(newItemDTO)) {
                        collaborateOrderDetailsDO.setInveItemId(newItemDTO.getId());
                        CustomsInventoryItemExtra extra = JSON.parseObject(newItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                        if (Objects.nonNull(extra)) {
                            collaborateOrderDetailsDO.setNetWeight(extra.getNetWeight());
                            collaborateOrderDetailsDO.setGrossWeight(extra.getGrossWeight());
                        }
                        collaborateOrderDetailsDO.setGoodsSeqNo(newItemDTO.getGoodsSeqNo());
                    }
                    multiProductCreate(collaborateOrderId, detailReqVo, collaborateOrderDetailsDO, true);
                }
            }
        }
        //查出没有匹配到的数据--少品
        updateFewProduct(itemDTOList, null, true, null);
    }

    private Integer updateFewProduct(List<InventoryOrderItemDTO> list, DiffType diffType, boolean isTallyThanDeclare, Integer diffQty) {
        //查出没有匹配到的数据--少品
        if (!CollectionUtils.isEmpty(list)) {
            for (InventoryOrderItemDTO itemDTO : list) {
                //更新协同单表体
                CollaborateOrderDetailsDO collaborateOrderDetailsDO = new CollaborateOrderDetailsDO();
                collaborateOrderDetailsDO.setInveItemId(itemDTO.getId());
                collaborateOrderDetailsDO.setTallyQty(0);
                if (!isTallyThanDeclare) { // 先报后理
                    collaborateOrderDetailsDO.setDeclareQty(itemDTO.getDeclareUnitQfy().intValue());
                    collaborateOrderDetailsDO.setDiffQty(itemDTO.getDeclareUnitQfy().intValue());
                    collaborateOrderDetailsDO.setDiffType(DiffType.FEW_PRODUCT.getCode());
                    diffQty += collaborateOrderDetailsDO.getDiffQty();
                }
                Example example = new Example(CollaborateOrderDetailsDO.class);
                example.createCriteria().andEqualTo("deleted", false).andEqualTo("inveItemId", itemDTO.getId());
                detailsMapper.updateByExampleSelective(collaborateOrderDetailsDO, example);

                //更新清关单表体
                InventoryOrderItemDO inventoryOrderItemDO = new InventoryOrderItemDO();
                inventoryOrderItemDO.setId(itemDTO.getId());
                inventoryOrderItemDO.setActualTallyQty(new BigDecimal(0));
                if (isTallyThanDeclare) {
                    inventoryOrderItemDO.setDeclareUnitQfy(new BigDecimal(0));
                }
                inventoryOrderItemMapper.updateByPrimaryKeySelective(inventoryOrderItemDO);

            }
        }
        return diffQty;
    }

    private void multiProductCreate(Long collaborateOrderId, TallyReportDetailDTO detailReqVo, CollaborateOrderDetailsDO orderDetailsDO, Boolean isTallyThenDeclare) {
        //多品
        Integer userId = UserUtils.getUserId();
        orderDetailsDO.setId(sequenceService.generateId());
        orderDetailsDO.setGoodsId(detailReqVo.getGoodsCode());
        orderDetailsDO.setGoodsName(detailReqVo.getGoodsName());
        orderDetailsDO.setBarCode(detailReqVo.getBarcode());
        orderDetailsDO.setProductId(detailReqVo.getProductId());
        orderDetailsDO.setSku(detailReqVo.getSku());
        orderDetailsDO.setTallyQty(detailReqVo.getTallyNum());
        orderDetailsDO.setCollaborateOrderId(collaborateOrderId);
        orderDetailsDO.setLabel(CollaborateDetailsLabEnum.ADD.getCode());
        if (!isTallyThenDeclare) {
            orderDetailsDO.setDiffType(DiffType.MULTI_PRODUCT.getCode());
            orderDetailsDO.setDiffQty(detailReqVo.getTallyNum());
        }
        orderDetailsDO.setCreateBy(userId);
        orderDetailsDO.setUpdateBy(userId);
        detailsMapper.insertSelective(orderDetailsDO);
    }

    private CollaborateOrderDetailsDO selectByCollaborateOrder(Long collaborateOrderId, String productId, String sku, String goodsSeqNo) {
        Example example = new Example(CollaborateOrderDetailsDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(collaborateOrderId)) {
            criteria.andEqualTo("collaborateOrderId", collaborateOrderId);
        }
        criteria.andEqualTo("sku", sku);
        criteria.andEqualTo("productId", productId);
        if (Objects.nonNull(goodsSeqNo) && !Objects.equals(goodsSeqNo, "")) {
            criteria.andEqualTo("goodsSeqNo", goodsSeqNo);
        }
        return detailsMapper.selectOneByExample(example);
    }

    @Override
    public void updateByGrossWeightAndGoodsSeqNo(String inveSn, List<InventoryOrderItemDTO> orginOrderItemList, List<InventoryOrderItemDTO> currentItemList) {

        if (CollectionUtils.isEmpty(currentItemList)) {
            return;
        }
        List<InventoryOrderItemDTO> deleteOrderItemList = getOrignExtraToTargetItem(orginOrderItemList, currentItemList);
        List<InventoryOrderItemDTO> addOrderItemList = getOrignExtraToTargetItem(currentItemList, orginOrderItemList);
        updateByGrossWeightAndGoodsSeqNo(inveSn, currentItemList, deleteOrderItemList, addOrderItemList);
    }

//    public static void main(String[] args) {
//
//        List<InventoryOrderItemDTO> orginOrderItemList = new ArrayList() {{
//
//            InventoryOrderItemDTO orderItemDTO1 = new InventoryOrderItemDTO();
//            orderItemDTO1.setId(1L);
//            orderItemDTO1.setSkuId("1L");
//            orderItemDTO1.setProductId("1L");
//            add(orderItemDTO1);
//
//            InventoryOrderItemDTO orderItemDTO2 = new InventoryOrderItemDTO();
//            orderItemDTO2.setId(2L);
//            orderItemDTO2.setSkuId("2L");
//            orderItemDTO2.setProductId("2L");
//            add(orderItemDTO2);
//
//            InventoryOrderItemDTO orderItemDTO3 = new InventoryOrderItemDTO();
//            orderItemDTO3.setId(3L);
//            orderItemDTO3.setSkuId("3L");
//            orderItemDTO3.setProductId("3L");
//            add(orderItemDTO3);
//        }};
//
//
//        List<InventoryOrderItemDTO> currentItemList = new ArrayList() {{
//
//            InventoryOrderItemDTO orderItemDTO1 = new InventoryOrderItemDTO();
//            orderItemDTO1.setId(1L);
//            orderItemDTO1.setSkuId("1L");
//            orderItemDTO1.setProductId("1L");
//            add(orderItemDTO1);
//
//            InventoryOrderItemDTO orderItemDTO2 = new InventoryOrderItemDTO();
//            orderItemDTO2.setId(2L);
//            orderItemDTO2.setSkuId("2L");
//            orderItemDTO2.setProductId("2L");
//            add(orderItemDTO2);
//            InventoryOrderItemDTO orderItemDTO3 = new InventoryOrderItemDTO();
//            orderItemDTO3.setId(3L);
//            orderItemDTO3.setSkuId("3L");
//            orderItemDTO3.setProductId("3L");
//            add(orderItemDTO3);
//
////            InventoryOrderItemDTO orderItemDTO4 = new InventoryOrderItemDTO();
////            orderItemDTO4.setId(4L);
////            orderItemDTO4.setSkuId("4L");
////            orderItemDTO4.setProductId("4L");
////            add(orderItemDTO4);
//        }};
//
//
//        List<InventoryOrderItemDTO> deleteOrderItemList = getOrignExtraToTargetItem(orginOrderItemList, currentItemList);
//        List<InventoryOrderItemDTO> addOrderItemList = getOrignExtraToTargetItem(currentItemList, orginOrderItemList);
//
//        System.out.println(addOrderItemList);
//    }


    private static List<InventoryOrderItemDTO> getOrignExtraToTargetItem(List<InventoryOrderItemDTO> orginList, List<InventoryOrderItemDTO> targetList) {

        if (CollectionUtils.isEmpty(orginList)) {
            return null;
        }
        if (CollectionUtils.isEmpty(targetList)) {
            return orginList;
        }
        return orginList.stream()
                .filter(z -> {
                    return targetList.stream().noneMatch(target -> Objects.equals(target.getSkuId(), z.getSkuId()) && Objects.equals(target.getProductId(), z.getProductId()));
                }).collect(Collectors.toList());
    }


    @Override
    public void updateByGrossWeightAndGoodsSeqNo(String inveSn, List<InventoryOrderItemDTO> currentOrderItemList, List<InventoryOrderItemDTO> deleteOrderItemList, List<InventoryOrderItemDTO> addOrderItemList) {

        if (Objects.isNull(inveSn)) {
            log.info("【清关单编辑】 清关单号为空 updateByGrossWeightAndGoodsSeqNo 清关单号 - {}", inveSn);
            return;
        }
        CollaborateOrderDTO orderDTO = orderService.selectByInveCustomsSn(inveSn);
        if (orderDTO == null) {
            log.info("【清关单编辑】 未找到协同单 updateByGrossWeightAndGoodsSeqNo 清关单号 - {}", inveSn);
            return;
        }
        List<CollaborateOrderDetailsDTO> collaborateOrderDetailsDTOS = this.findByCollaborateOrderDetailsList(orderDTO.getId());
        if (CollectionUtils.isEmpty(collaborateOrderDetailsDTOS)) {
            return;
        }
        // 当前提交清关单表体
        if (!CollectionUtils.isEmpty(currentOrderItemList)) {
            currentOrderItemList.forEach(z -> {
                CollaborateOrderDetailsDTO collaborateDetails = getCollaborateDeatailByInventroyOrderItem(collaborateOrderDetailsDTOS, z);
                log.info("根据【当前】清关单表体：sku={},productId={} ,获取协同单数据为：{}", z.getSkuId(), z.getProductId(), JSON.toJSONString(collaborateDetails));
                if (collaborateDetails != null) {
                    CollaborateOrderDetailsDO detailsDO = new CollaborateOrderDetailsDO();
                    detailsDO.setId(collaborateDetails.getId());
                    if (Objects.equals(collaborateDetails.getLabel(), CollaborateDetailsLabEnum.DEL.getCode())) {
                        detailsDO.setLabel(CollaborateDetailsLabEnum.ADD.getCode());
                    }
                    detailsDO.setInveItemId(z.getId());
                    ExtraJsonDTO extraJsonDTO = JSON.parseObject(z.getExtraJson(), ExtraJsonDTO.class);
                    if (Objects.nonNull(extraJsonDTO.getNetweight())) {
                        detailsDO.setNetWeight(extraJsonDTO.getNetweight());
                    }
                    if (Objects.nonNull(extraJsonDTO.getGrossWeight())) {
                        detailsDO.setGrossWeight(extraJsonDTO.getGrossWeight());
                    }
                    if (Objects.nonNull(z.getGoodsSeqNo())) {
                        detailsDO.setGoodsSeqNo(z.getGoodsSeqNo());
                    }
                    detailsMapper.updateByPrimaryKeySelective(detailsDO);
                }
            });
        }


        // 删除提交清关单表体
        if (!CollectionUtils.isEmpty(deleteOrderItemList)) {
            log.info("【删除】清关单表体：{}", JSON.toJSONString(deleteOrderItemList));
            deleteOrderItemList.forEach(z -> {
                CollaborateOrderDetailsDTO collaborateOrderDetailsDTO = getCollaborateDeatailByInventroyOrderItem(collaborateOrderDetailsDTOS, z);
                if (collaborateOrderDetailsDTO != null) {
                    CollaborateOrderDetailsDO detailsDO = new CollaborateOrderDetailsDO();
                    detailsDO.setLabel(CollaborateDetailsLabEnum.DEL.getCode());
                    detailsDO.setId(collaborateOrderDetailsDTO.getId());
                    detailsMapper.updateByPrimaryKeySelective(detailsDO);
                }
            });
        }

        // 新增提交清关单表体
        if (!CollectionUtils.isEmpty(addOrderItemList)) {
            log.info("【新增】清关单表体：{}", JSON.toJSONString(addOrderItemList));
            addOrderItemList.forEach(itemDTO -> {
                CollaborateOrderDetailsDO detailsDO = new CollaborateOrderDetailsDO();
                detailsDO.setId(sequenceService.generateId());
                detailsDO.setInveItemId(itemDTO.getId());
                detailsDO.setGoodsId(itemDTO.getGoodsCode());
                detailsDO.setGoodsName(itemDTO.getGoodsName());
                detailsDO.setCollaborateOrderId(orderDTO.getId());
                detailsDO.setBarCode(itemDTO.getGoodsBar());
                detailsDO.setSku(itemDTO.getSkuId());
                detailsDO.setProductId(itemDTO.getProductId());
                InventoryOrderItemExtra extra = JSON.parseObject(itemDTO.getExtraJson(), InventoryOrderItemExtra.class);
                detailsDO.setNetWeight(extra.getNetweight());
                detailsDO.setGrossWeight(extra.getGrossWeight());
                detailsDO.setBarCode(extra.getGoodsBar());
                detailsDO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                detailsDO.setLabel(CollaborateDetailsLabEnum.ADD.getCode());
                detailsDO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                UserUtils.setCreateAndUpdateBy(detailsDO);
                detailsMapper.insert(detailsDO);
            });
        }
    }
//
//    @Test
//    public void test(){
//
//        List<CollaborateOrderDetailsDTO> collaborateOrderDetailsDTOS = new ArrayList() {{
//            CollaborateOrderDetailsDTO orderDetail1 = new CollaborateOrderDetailsDTO();
//            orderDetail1.setSku("1L");
//            orderDetail1.setProductId("1L");
//            add(orderDetail1);
//
//            CollaborateOrderDetailsDTO orderDetail2 = new CollaborateOrderDetailsDTO();
//            orderDetail2.setSku("2L");
//            orderDetail2.setProductId("2L");
//            add(orderDetail2);
//        }};
//
//        InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
//        inventoryOrderItemDTO.setSkuId("2L");
//        inventoryOrderItemDTO.setProductId("1L");
//        CollaborateOrderDetailsDTO collaborateOrderDetailsDTO =  getCollaborateDeatailByInventroyOrderItem(collaborateOrderDetailsDTOS, inventoryOrderItemDTO);
//
//        System.out.println(collaborateOrderDetailsDTO);
//
//    }

    /**
     * 获取协同单详情数据
     *
     * @param collaborateOrderDetailsDTOS 协同单详情列表
     * @param inventoryOrderItemDTO       清单表体
     * @return
     */
    private static CollaborateOrderDetailsDTO getCollaborateDeatailByInventroyOrderItem(List<CollaborateOrderDetailsDTO> collaborateOrderDetailsDTOS, InventoryOrderItemDTO inventoryOrderItemDTO) {

        if (CollectionUtils.isEmpty(collaborateOrderDetailsDTOS)) {
            return null;
        }
        if (inventoryOrderItemDTO == null) {
            return null;
        }
        return collaborateOrderDetailsDTOS.stream()
                .filter(z -> Objects.equals(z.getSku(), inventoryOrderItemDTO.getSkuId())
                        && Objects.equals(z.getProductId(), inventoryOrderItemDTO.getProductId()))
                .findFirst().orElse(null);
    }


    /**
     * 根据清关单id修改申报数量
     *
     * @param inveId
     */
    @Override
    public void updateDeclareQtyByInveId(Long inveId) {
        InventoryOrderInfoDTO infoDTO = infoService.findById(inveId);
        CollaborateOrderDTO orderDTO = orderService.selectByInveCustomsSn(infoDTO.getInveCustomsSn());
        if (orderDTO == null) {
            log.info("【服务完成】 未找到协同单 updateDeclareQtyByInveId 清关单号 - {}", infoDTO.getInveCustomsSn());
            return;
        }
        List<InventoryOrderItemDTO> inventoryOrderItemDTOList = infoService.findListByInvenOrderId(inveId);
        List<CollaborateOrderDetailsResVO> detailsList = this.selectByCollaborateOrderId(orderDTO.getId());
        Map<String, CollaborateOrderDetailsDO> detailsMap = detailsList.stream()
                .map(i -> BeanUtils.copyProperties(i, CollaborateOrderDetailsDO.class))
                .collect(Collectors.toMap(i -> (i.getProductId() + i.getSku()), Function.identity(), (v1, v2) -> v1));
        int diffQty = 0;
        for (InventoryOrderItemDTO itemDTO : inventoryOrderItemDTOList) {
            CollaborateOrderDetailsDO detailsDO = detailsMap.get(itemDTO.getOriginProductId() + itemDTO.getSkuId());
            if (Objects.nonNull(detailsDO)) {
                detailsDO.setDeclareQty(itemDTO.getDeclareUnitQfy().intValue());
                Tuple<DiffType, Integer> tallyReportTuple = CalculationUtil.tallyReportDiff(detailsDO.getTallyQty(), detailsDO.getDeclareQty());
                if (tallyReportTuple != null) {
                    DiffType diffType = tallyReportTuple.getF();
                    detailsDO.setDiffType(diffType.getCode());
                    Integer tallyDiffQty = tallyReportTuple.getS();
                    if (tallyDiffQty != null) {
                        detailsDO.setDiffQty(tallyDiffQty);
                        diffQty += detailsDO.getDiffQty();
                    }
                }
                UserUtils.setUpdateBy(detailsDO);
                detailsDO.setUpdateTime(new Date());
                detailsMapper.updateByPrimaryKeySelective(detailsDO);
            }
        }
        if (diffQty != 0) {
            orderService.updateDiffQty(orderDTO.getId(), diffQty);
        }
    }

    /**
     * 修改差异数量
     *
     * @param inveSn
     * @return
     */
    @Override
    public int updateDeclareQtyByInveSn(String inveSn) {
        //差异数量
        int diffQty = 0;
        CollaborateOrderDTO orderDTO = orderService.selectByInveCustomsSn(inveSn);
        if (orderDTO == null) {
            log.info("[op updateDeclareQtyByInveSn] 未找到协同单 - {}", inveSn);
            return diffQty;
        }
        List<CollaborateOrderDetailsDTO> detailsDTOList = this.findByCollaborateOrderDetailsList(orderDTO.getId());
        for (CollaborateOrderDetailsDTO detailsDTO : detailsDTOList) {
            CollaborateOrderDetailsDO orderDetailsDO = new CollaborateOrderDetailsDO();
            InventoryOrderItemDTO itemDTO = infoService.findSkuAndProductIdGoodsSeqNo(detailsDTO.getSku(), detailsDTO.getProductId(), detailsDTO.getGoodsSeqNo(), inveSn);
            if (itemDTO == null) {
                if (Objects.equals(detailsDTO.getTallyQty(), 0)) {
                    orderDetailsDO.setDeclareQty(0);
                    orderDetailsDO.setDiffType(DiffType.NORMAL.getCode());
                    orderDetailsDO.setDiffQty(0);
                } else {    // 清关单表体不存在且理货数量不为0 -- 多品
                    if (orderDetailsDO.getDiffQty() != null) {
                        orderDetailsDO.setDeclareQty(0);
                        orderDetailsDO.setDiffType(DiffType.MULTI_PRODUCT.getCode());
                        orderDetailsDO.setDiffQty(orderDetailsDO.getDiffQty());
                        diffQty += orderDetailsDO.getDiffQty();
                    }
                }
            } else {
                //最终申报数量 == 理货数量 =正常
                Integer tallyQty = detailsDTO.getTallyQty();
                Integer declareQty = itemDTO.getDeclareUnitQfy().intValue();
                orderDetailsDO.setDeclareQty(declareQty);
                Tuple<DiffType, Integer> tallyReportDiffTuple = CalculationUtil.tallyReportDiff(tallyQty, declareQty);
                if (tallyReportDiffTuple == null) {
                    continue;
                }
                DiffType diffType = tallyReportDiffTuple.getF();
                orderDetailsDO.setDiffType(diffType.getCode());
                Integer tallyDiffQty = tallyReportDiffTuple.getS();
                orderDetailsDO.setDiffQty(tallyDiffQty);
                diffQty += tallyDiffQty;
            }
            orderDetailsDO.setId(detailsDTO.getId());
            detailsMapper.updateByPrimaryKeySelective(orderDetailsDO);
        }
        orderService.updateDiffQty(orderDTO.getId(), diffQty);
        return diffQty;
    }


    public List<CollaborateOrderDetailsDTO> findByCollaborateOrderDetailsList(Long id) {
        Example example = new Example(CollaborateOrderDetailsDO.class);
        example.createCriteria().andEqualTo("collaborateOrderId", id);
        List<CollaborateOrderDetailsDO> detailsDOList = detailsMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(detailsDOList)) {
            return null;
        }
        List<CollaborateOrderDetailsDTO> list = JSON.parseArray(JSON.toJSONString(detailsDOList), CollaborateOrderDetailsDTO.class);
        return list;
    }

}
