package com.danding.cds.endorsement.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Table;

@Table(name = "`ccs_endorsement_relation`")
@Getter
@Setter
public class EndorsementRelationDO extends BaseDO {

    /**
     * 关联核注id
     */
    private Long endorsementId;

    /**
     * 申报单id
     */
    private Long orderId;

    /**
     * 申报单sn
     */
    private String orderSn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 清单sn
     */
    private String customsInventorySn;

    /**
     * 清单编号
     */
    private String inventoryNo;

    /**
     * 运单号
     */
    private String logisticsNo;

    /**
     * 租户id
     */
    private Long tenantryId;
}