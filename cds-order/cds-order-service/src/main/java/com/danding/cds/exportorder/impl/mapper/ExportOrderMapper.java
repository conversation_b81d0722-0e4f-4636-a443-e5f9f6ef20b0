package com.danding.cds.exportorder.impl.mapper;

import com.danding.cds.exportorder.impl.entity.ExportOrderDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface ExportOrderMapper extends Mapper<ExportOrderDO>, InsertListMapper<ExportOrderDO>, BatchUpdateMapper<ExportOrderDO>, AggregationPlusMapper<ExportOrderDO> {
}