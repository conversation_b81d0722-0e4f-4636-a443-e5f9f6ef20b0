package com.danding.cds;

import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.customs.declare.CustomsSupportListener;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.config.SpecialConfig;
import com.danding.logistics.mq.common.handler.MessageSender;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Component("autoOrderConfig")
@SpringBootConfiguration
public class AutoOrderConfig {

    public static final String customsInventoryIndex = "ccs_customs_inventory_index";

    /**
     * eg.202205
     */
    private String orderTrackLogIndex = "ccs_order_track_log_index";

    @Bean
    public String getCustomsInventoryIndex() {
        return customsInventoryIndex + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    public String getOrderTrackLogIndex() {
        return orderTrackLogIndex + "_" + new SimpleDateFormat("yyyyMM").format(new Date());
    }

    @Bean
    public CustomsSupport customsSupport(CustomsSupportListener listener, SpecialConfig specialConfig, MessageSender messageSender) {
        if (EnvironmentConfig.isOnline()) {
            CustomsSupport customsSupport = new CustomsSupport(listener, CustomsSupport.ENV_ONLINE, specialConfig, messageSender);
            return customsSupport;
        } else if (EnvironmentConfig.isUat()) {
            // todo 这里需要删除掉，这里这样做主要是因为国运测单在预发布，核注申报里面的代理又要走线上，所以先这样简单处理
            CustomsSupport customsSupport = new CustomsSupport(listener, CustomsSupport.ENV_ONLINE, specialConfig, messageSender);
            return customsSupport;
        } else {
            CustomsSupport customsSupport = new CustomsSupport(listener, CustomsSupport.ENV_MOCK, specialConfig, messageSender);
            return customsSupport;
        }
    }

    @Bean(name = "orderLogThreadExecutor")
    public ThreadPoolTaskExecutor orderLogThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("orderLogThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }


    @Bean(name = "trackLogEsThreadExecutor")
    public ThreadPoolTaskExecutor trackLogEsThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("trackLogEsThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "calloffCountThreadExecutor")
    public ThreadPoolTaskExecutor calloffCountThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("calloffCountThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "refundCountThreadExecutor")
    public ThreadPoolTaskExecutor refundCountThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("refundCountThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "cacnelCountThreadExecutor")
    public ThreadPoolTaskExecutor cacnelCountThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("cacnelCountThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "endorsementCountTaskExecutor")
    public ThreadPoolTaskExecutor endorsementCountTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("endorsementCountTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "inventoryOrderCountTaskExecutor")
    public ThreadPoolTaskExecutor inventoryOrderCountTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("inventoryOrderCountTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "checklistCountTaskExecutor")
    public ThreadPoolTaskExecutor checklistCountTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("checklistCountTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "inveDelayDeclareTaskExecutor")
    public ThreadPoolTaskExecutor inveDelayDeclareTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("inveDelayDeclareTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "exportOrderWritingItemTaskExecutor")
    public ThreadPoolTaskExecutor exportOrderWritingItemTaskExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(10);
        executor.setMaxPoolSize(20);
        executor.setQueueCapacity(30000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("exportOrderWritingItemTaskExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }

    @Bean(name = "inventoryOrderAlarmThreadExecutor")
    public ThreadPoolTaskExecutor inventoryOrderAlarmThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(5);
        executor.setMaxPoolSize(5);
        executor.setQueueCapacity(1000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("inventoryOrderAlarmThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
