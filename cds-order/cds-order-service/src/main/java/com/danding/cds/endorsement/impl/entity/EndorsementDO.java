package com.danding.cds.endorsement.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "`ccs_endorsement`")
@Getter
@Setter
public class EndorsementDO extends BaseDO {
    /**
     * 出区单ID
     */
    @Column(name = "export_order_id")
    private Long exportOrderId;

    /**
     * 清关单ID
     */
    @Column(name = "inventory_order_id")
    private Long inventoryOrderId;

    /**
     * 出入区标志
     */
    @Column(name = "ie_flag")
    private Integer ieFlag;

    /**
     * 一票多车标识
     */
    @Column(name = "checklists_flag")
    private Boolean checklistsFlag;

    /**
     * 业务类型
     */
    @Column(name = "bussiness_type")
    private String bussinessType;
    /**
     * 企业内部核注清单编号
     */
    private String sn;

    /**
     * 预录入核注单号
     */
    @Column(name = "pre_order_no")
    private String preOrderNo;

    /**
     * 真实核注清单编号
     */
    @Column(name = "real_order_no")
    private String realOrderNo;

    /**
     * 账册ID
     */
    @Column(name = "account_book_id")
    private Long accountBookId;

    /**
     * 清关企业ID
     */
    @Column(name = "declare_company_id")
    private Long declareCompanyId;

    /**
     * 核注单状态
     */
    private String status;
    /**
     * 海关回执状态
     */
    @Column(name = "customs_status")
    private String customsStatus;
    /**
     * 描述
     */
    @Column(name = "information_desc")
    private String informationDesc;
    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private Date finishTime;

    /**
     * 额外其他属性
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 是否核扣库存
     */
    private Boolean stockChangeEnable;

    /**
     * 出库单
     */
    @Column(name = "out_bond")
    private String outBond;

    /**
     * 推送状态  0：待推送；1：已推送；2：推送失败；3：海关已入库；4：海关入库异常；
     */
    @Column ( name = "push_status" )
    private Integer pushStatus;

    /**
     * 关企回执信息
     */
    @Column ( name = "push_msg" )
    private String pushMsg;

    /**
     * 关企推送id
     */
    @Column(name = "push_msg_id")
    private String pushMsgId;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 监管方式
     */
    @Column(name = "supv_mode")
    private String supvMode;

    /**
     * 录入员IC卡号
     */
    @Column(name = "ic_card_no")
    private String icCardNo;

    /**
     * 清单类型
     */
    @Column(name = "invt_type")
    private String invtType;

    /**
     * 报关单统一编号
     */
    @Column(name = "customs_entry_seq_no")
    private String customsEntrySeqNo;

    /**
     * 报关单生成状态
     */
    @Column(name = "generate_declare_status")
    private String generateDeclareStatus;

    /**
     * 报关单生成失败原因
     */
    @Column(name = "generate_declare_reason")
    private String generateDeclareReason;

    /**
     * 单据类型
     */
    private String orderType;

    /**
     * 运输方式
     */
    private String transportMode;
}