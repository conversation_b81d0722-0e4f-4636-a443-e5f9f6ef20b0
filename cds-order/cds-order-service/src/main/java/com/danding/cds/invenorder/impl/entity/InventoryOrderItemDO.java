package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

@Table(name = "ccs_inventory_order_item")
@Getter
@Setter
public class InventoryOrderItemDO extends BaseDO {
    /**
     * 清关单ID
     */
    @Column(name = "ref_inve_order_id")
    private Long refInveOrderId;
    /**
     * 清关单SN
     */
    @Column(name = "ref_inve_order_sn")
    private String refInveOrderSn;
    /**
     * 是否是新的(0:旧,1:新)
     */
    @Column(name = "is_new")
    private String isNew;

    /**
     * 原始料号
     */
    private String originProductId;


    /**
     * 海关备案料号
     * 由上游指定
     */
    private String customsRecordProductId;

    /**
     * 货品id
     */
    private String goodsCode;

    /**
     * 商品料号
     */
    @Column(name = "product_id")
    private String productId;
    /**
     * SKU
     */
    @Column(name = "sku_id")
    private String skuId;
    /**
     * 商品序号
     */
    @Column(name = "goods_seq_no")
    private String goodsSeqNo;
    /**
     * 记账金二序号
     */
    @Column(name = "customs_callback_seq_no")
    private String customsCallBackSeqNo;
    /**
     * 料号名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 来源标识
     */
    @Column(name = "goods_source")
    private String goodsSource;

    /**
     * 备案名称
     */
    @Column(name = "record_product_name")
    private String recordProductName;
    /**
     * HS代码
     */
    @Column(name = "hs_code")
    private String hsCode;
    /**
     * 计划申报数量
     */
    @Column(name = "plan_declare_qty")
    private BigDecimal planDeclareQty;
    /**
     * 出库单号
     */
    @Column(name = "out_bound_no")
    private String outBoundNo;
    /**
     * 实际理货数量
     */
    @Column(name = "actual_tally_qty")
    private BigDecimal actualTallyQty;
    /**
     * 申报单位数量
     */
    @Column(name = "declare_unit_qfy")
    private BigDecimal declareUnitQfy;
    /**
     * 申报单价
     */
    @Column(name = "declare_price")
    private BigDecimal declarePrice;

    /**
     * 额外其他属性
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 核注清单ID
     */
    @Column(name = "ref_endorsement_id")
    private Long refEndorsementId;

    /**
     * 核注清单SN
     */
    @Column(name = "ref_endorsement_sn")
    private String refEndorsementSn;

    /**
     * 通关校验结果
     * 存json串 {@link com.danding.cds.invenorder.api.dto.InventoryVerifyResult}
     */
    private String verifyResult;

    /**
     * 是否涉濒危
     */
    private Boolean endangered;

    /**
     * 物种证明附件名称
     */
    private String speciesCertificateAttachmentName;

    /**
     * 物种证明附件URL
     */
    private String speciesCertificateAttachmentUrl;


    /**
     * 是否生化品
     */
    @Column(name = "dangerous_flag")
    private String dangerousFlag;

    /**
     * 行号
     */
    private String idx;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;


    // ---------------------------------- 溯源信息----------------------------------
    /**
     * 关联一线入境报关单号
     */
    @Column(name = "customs_entry_no")
    private String customsEntryNo;

    /**
     * 报关日期
     */
    @Column(name = "customs_entry_time")
    private Date customsEntryTime;

    /**
     * 启运国
     */
    @Column(name = "shipment_country")
    private String shipmentCountry;

    /**
     * 启运港
     */
    @Column(name = "from_location")
    private String fromLocation;

    /**
     * 运输方式
     */
    @Column(name = "transport_mode")
    private String transportMode;

    /**
     * 进境口岸
     */
    @Column(name = "entry_port")
    private String entryPort;

    // ---------------------------------- 溯源信息----------------------------------

}
