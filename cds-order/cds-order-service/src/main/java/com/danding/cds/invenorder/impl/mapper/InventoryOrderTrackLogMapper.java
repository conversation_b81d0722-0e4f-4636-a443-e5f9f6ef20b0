package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderTrackLogDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/8/6 11:25
 */
public interface InventoryOrderTrackLogMapper extends Mapper<InventoryOrderTrackLogDO>, InsertListMapper<InventoryOrderTrackLogDO>, BatchUpdateMapper<InventoryOrderTrackLogDO>, AggregationPlusMapper<InventoryOrderTrackLogDO> {
}
