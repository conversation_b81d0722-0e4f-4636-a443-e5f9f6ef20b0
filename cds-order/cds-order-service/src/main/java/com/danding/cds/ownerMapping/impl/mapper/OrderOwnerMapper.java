package com.danding.cds.ownerMapping.impl.mapper;

import com.danding.cds.ownerMapping.impl.entity.OrderOwnerMappingDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @Create 2021/7/5  10:26
 * @Describe
 **/
public interface OrderOwnerMapper extends Mapper<OrderOwnerMappingDO>, InsertListMapper<OrderOwnerMappingDO>, BatchUpdateMapper<OrderOwnerMappingDO>, AggregationPlusMapper<OrderOwnerMappingDO> {
}
