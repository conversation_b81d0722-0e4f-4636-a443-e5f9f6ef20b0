package com.danding.cds.invenorder.impl.mq;

import com.alibaba.fastjson.JSON;
import com.danding.cds.invenorder.api.dto.InvenorderDeclareDTO;
import com.danding.cds.invenorder.api.dto.InvenorderStatusDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.logistics.mq.common.handler.MessageSender;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: Raymond
 * @Date: 2020/11/30 9:17
 * @Description:
 */
@Component
public class InventorderStatusCallbackProducer {
    @Autowired
    private MessageSender messageSender;

    public Boolean send(InvenorderStatusDTO bizResultInfo){

        String bizResult = JSON.toJSONString(bizResultInfo);
        messageSender.sendMsg(bizResult,"ccs-invenorder-status-callback-topic:" + InventoryOrderChannel.LOGISTICS.getValue());
        return true;
    }
}
