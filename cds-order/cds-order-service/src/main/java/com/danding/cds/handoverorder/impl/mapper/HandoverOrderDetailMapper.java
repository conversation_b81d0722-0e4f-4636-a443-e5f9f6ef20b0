package com.danding.cds.handoverorder.impl.mapper;

import com.danding.cds.handoverorder.impl.entity.HandoverOrderDetailDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface HandoverOrderDetailMapper extends Mapper<HandoverOrderDetailDO>, InsertListMapper<HandoverOrderDetailDO>, BatchUpdateMapper<HandoverOrderDetailDO>, AggregationPlusMapper<HandoverOrderDetailDO> {
    @Update(value = "<script>" +
            "update ccs_customs_handover_order_detail " +
            "<set>" +
            "    outbound_order = NULL ,associate_outbound_status = 0 " +
            "</set>" +
            " where customs_inventory_sn in " +
            "   <foreach collection='snList' item='item' index ='index' open='(' separator=',' close=')' > " +
            "       #{item} " +
            "   </foreach>" +
            "</script>")
    void cleanExportOrderInfo(@Param("snList") List<String> snList);

    @Update(value = "<script>" +
            "update ccs_customs_handover_order_detail " +
            "<set>" +
            "    outbound_order = NULL,associate_outbound_status = 0 " +
            "</set>" +
            " where outbound_order = #{exportOrderNo} " +
            "</script>")
    void disassociateDetailByOutboundOrder(@Param("exportOrderNo") String exportOrderNo);

    @Select(value = "<script>" +
            "select distinct handover_sn from ccs_customs_handover_order_detail " +
            " where outbound_order in " +
            "   <foreach collection='outboundOrder' item='item' index ='index' open='(' separator=',' close=')' > " +
            "       #{item} " +
            "   </foreach>" +
            "</script>")
    List<String> getHandoverSnByOutBoundOrder(@Param("outboundOrder") List<String> outboundOrder);
}