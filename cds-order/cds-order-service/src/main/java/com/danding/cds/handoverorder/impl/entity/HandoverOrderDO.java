package com.danding.cds.handoverorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 交接单实体对象
 * @date 2021/12/14
 */
@Table(name = "`ccs_customs_handover_order`")
@Data
public class HandoverOrderDO extends BaseDO {

    /**
     * 交接单号
     */
    @Column(name = "handover_sn")
    private String handoverSn;

    /**
     * 包裹数
     */
    @Column(name = "package_num")
    private Integer packageNum;

    /**
     * 总重量(Kg)
     */
    @Column(name = "total_weight")
    private BigDecimal totalWeight;

    /**
     *总托数
     */
    @Column(name = "total_pallets_num")
    private Integer totalPalletsNum;

    /**
     * 快递公司名称(wms系统数据)
     */
    @Column(name = "wms_express_name")
    private String wmsExpressName;

    /**
     * 快递公司ID(wms系统数据)
     */
    @Column(name = "wms_express_code")
    private String wmsExpressCode;

    /**
     * 仓库名称
     */
    @Column(name = "store_house_name")
    private String storeHouseName;

    /**
     * 仓库编码
     */
    @Column(name = "store_house_sn")
    private String storeHouseSn;

    /**
     * 完成状态
     */
    @Column(name = "finish_status")
    private Integer finishStatus;

    /**
     * 出库状态0-待生成出库单 1-已生成出库单 2-部分出库
     * {@link com.danding.cds.handoverOrder.api.enums.HandoverOrderStatus}
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 出库单号
     */
    @Column(name = "outbound_order")
    private String outboundOrder;

    /**
     * 车辆信息
     */
    @Column(name = "vehicle_info")
    private String vehicleInfo;
}
