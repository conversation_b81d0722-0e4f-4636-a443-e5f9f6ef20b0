package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "ccs_inventory_order_info")
@Data
public class InventoryOrderInfoDO extends BaseDO {

    /**
     * 清关单号
     */
    @Column(name = "inve_customs_sn")
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    @Column(name = "inve_company_id")
    private Long inveCompanyId;
    /*
        单据类型
        @Column(name = "inve_order_type")
        private String inveOrderType;
    */
    @Column(name = "inve_business_type")
    private String inveBusinessType;


    //("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    @Column(name = "in_account_book")
    private String inAccountBook;

    //("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    @Column(name = "out_account_book")
    private String outAccountBook;
    /**
     * 核放单编号
     */
    @Column(name = "ref_check_order_no")
    private String refCheckOrderNo;
    /**
     * 核注清单编号
     */
    @Column(name = "ref_hz_inve_no")
    private String refHzInveNo;
    /**
     * 提取号 后用作关联核注清单号
     */
    @Column(name = "pick_up_no")
    private String pickUpNo;
    /**
     * 进出境关别
     */
    @Column(name = "entry_exit_customs")
    private String entryExitCustoms;
    /**
     * 运输方式
     */
    @Column(name = "transport_mode")
    private String transportMode;
    /**
     * 启运国
     */
    @Column(name = "shipment_country")
    private String shipmentCountry;
    /**
     * 租户
     */
    @Column(name = "rent_person")
    private String rentPerson;

    /**
     * 账册ID
     */
    @Column(name = "book_id")
    private Long bookId;
    /**
     * 申请人
     */
    @Column(name = "apply_person")
    private String applyPerson;
    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;
    /**
     * 清关单状态
     */
    @Column(name = "status")
    private String status;

    @Column(name = "audit_status")
    private String auditStatus;

    /**
     * 清关单状态对应时间
     */
    @Column(name = "status_time")
    private Date statusTime;

    @Column(name = "expected_out_area_time")
    private Date expectedOutAreaTime;

    /**
     * 预计到港
     */
    @Column(name = "expected_to_port_time")
    private Date expectedToPortTime;
    /**
     * 状态:0.停用;1.启用(默认)
     */
    @Column(name = "enable")
    private Integer enable;

    @Column(name = "channel")
    private Integer channel;

    @Column(name = "channel_business_type")
    private String channelBusinessType;

    @Column(name = "channel_business_sn")
    private String channelBusinessSn;

    @Column(name = "reason")
    private String reason;

    /**
     * 清单类型
     */
    @Column(name = "customs_invt_type")
    private String customsInvtType;

    @Column(name = "customs_entry_no")
    private String customsEntryNo;

    @Column(name = "customs_entry_company")
    private Long customsEntryCompany;

    @Column(name = "customs_entry_type")
    private String customsEntryType;

    @Column(name = "master_order_sn")
    private String masterOrderSn;

    @Column(name = "sub_order_sn")
    private String subOrderSn;

//    /**
//     * 起点仓货主Code
//     */
//    private String outsetOwnerCode;
//
//    /**
//     * 起点仓货主
//     */
//    private String outsetOwnerName;
//
//    /**
//     * 目的仓货主Code
//     */
//    private String destinationOwnerCode;
//
//    /**
//     * 目的地货主
//     */
//    private String destinationOwnerName;

    /**
     * 货主是否自备车辆
     */
    private Boolean selfOwnedVehicle;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;

    /**
     * 托数
     */
    private Integer palletsNums;

    /**
     * 核注企业单号
     */
    private String endorsementSn;

    /**
     * 报关标志(一线就默认都是报关，非一线就是非报关)(1:是 2:否)
     */
    private Integer customsFlag;

    /**
     * 是否两步申报(1:是 2:否)（指区港联动等）
     */
    private Integer twoStepFlag;

    /**
     * 是否生成报关单(1:是 2:否)
     */
    private Integer declarationFlag;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 实际到港日期
     */
    @Column(name = "actual_arrival_date")
    private Date actualArrivalDate;

    /**
     * 是否是关仓协同
     */
    @Column(name = "collaborate_flag")
    private Boolean collaborateFlag;

    /**
     * 质押货主标志
     */
    @Column(name = "pledge_owner_flag")
    private Boolean pledgeOwnerFlag;

    /**
     * 邮箱地址
     */
    @Column(name = "mail_addr_list")
    private String mailAddrList;

    /**
     * 邮件状态
     */
    @Column(name = "mail_status")
    private Integer mailStatus;

    /**
     * 邮件驳回原因
     */
    @Column(name = "mail_reject_reason")
    private String mailRejectReason;

    /**
     * 调用erp驳回订单接口错误信息
     */
    @Column(name = "callback_erp_Reject_order_err_msg")
    private String callbackErpRejectOrderErrMsg;

    /**
     * 清关方式
     */
    private Integer declareWay;

    /**
     * 外部单号
     */
    @Column(name = "upstream_no")
    private String upstreamNo;

    /**
     * 出入库单号
     */
    @Column(name = "in_out_order_no")
    private String inOutOrderNo;

    /**
     * 关联核注清单编号
     */
    @Column(name = "associated_endorsement_no")
    private String associatedEndorsementNo;

    /**
     * 状态流 JSON格式保存
     * ['CREATED','CONFIRMING','ENDORSEMENT']
     */
    private String stateFlow;

    /**
     * 状态流详情，json对象数组
     */
    @Column(name = "state_flow_detail")
    private String stateFlowDetail;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * WMS仓编码
     */
    private String wmsWarehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 上游是否取消
     */
    private Boolean upstreamCancel;

    /**
     * 清关单标记 用位运算去搞
     */
    private Integer orderTag;

    /**
     * 清关单待办标记(位运算)
     */
    private Integer orderTodoTag;

    /**
     * 核注清单编号
     */
    private String endorsementRealOrderNo;

    /**
     * 关联草单附件名称
     */
    private String draftListAttachmentName;

    /**
     * 关联草单附件链接
     */
    private String draftListAttachmentUrl;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;

    /**
     * 是否开启中转 0:否 1:是
     * (只有中转主单才变动)
     */
    private Integer transitFlag;
    /**
     * 清关企业(终点)
     */
    private Long finalInveCompanyId;
    /**
     * 账册ID(终点)
     */
    private Long finalBookId;
    /**
     * 实体仓编码(终点)
     */
    private String finalEntityWarehouseCode;
    /**
     * 实体仓名称(终点)
     */
    private String finalEntityWarehouseName;
    /**
     * 货主编码(终点)
     */
    private String finalOwnerCode;
    /**
     * 货主名称(终点)
     */
    private String finalOwnerName;
    /**
     * 关联中转清关单号
     */
    private String associatedTransitOrderSn;
    /**
     * 关联调入清关单号
     */
    private String associatedInOrderSn;
    /**
     * 关联调出清关单号
     */
    private String associatedOutOrderSn;

    /**
     * 用户id
     * ERP下发(上游商家用户中心id)
     */
    private Long userId;

    /**
     * 用户名称
     * ERP下发(上游商家用户中心name)
     */
    private String userName;

    /**
     * 推送状态  0：待推送；1：已推送；2：推送失败；3：海关已入库；4：海关入库异常；
     */
    @Column(name = "push_status")
    private Integer pushStatus;

    /**
     * 关企回执信息
     */
    @Column(name = "push_msg")
    private String pushMsg;

    /**
     * 关企推送id
     */
    @Column(name = "push_msg_id")
    private String pushMsgId;

    /**
     * 报关单附件链接
     */
    @Column(name = "customs_entry_attach_url")
    private String customsEntryAttachUrl;

    /**
     * 报关日期
     */
    @Column(name = "customs_entry_date")
    private Date customsEntryDate;

    /**
     * 是否理货完成
     * 0-否 / 1-是
     */
    @Column(name = "tally_complete")
    private Integer tallyComplete;

    /**
     * 清关回传状态
     */
    @Column(name = "callback_status")
    private Integer callbackStatus;

    /**
     * 清关回执错误日志
     */
    @Column(name = "callback_error_msg")
    private String callbackErrorMsg;

    /**
     * 分区结转单
     */
    private String carryOverNo;


    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    @Column(name = "customs_type")
    private Integer customsType;

    /**
     * 对应报关单申报单位
     */
    @Column(name = "corr_cus_declare_company_id")
    private Long corrCusDeclareCompanyId;

    /**
     * 关联报关单境内收发货人
     */
    @Column(name = "rlt_cus_inner_sfhr_company_id")
    private Long rltCusInnerSFHRCompanyId;

    /**
     * 关联报关单消费使用单位
     */
    @Column(name = "rlt_cus_xfdw_company_id")
    private Long rltCusXFDYCompanyId;

    /**
     * 关联报关单申报单位
     */
    @Column(name = "rlt_cus_declare_company_id")
    private Long rltCusDeclareCompanyId;

    /**
     * 进出区标志
     * {@link com.danding.cds.invenorder.api.enums.InventoryInOutEnum}
     */
    @Column(name = "in_out_flag")
    private String inOutFlag;

    /**
     * 约车单号
     */
    @Column(name = "yc_no")
    private String ycNo;

    /**
     * 非保标记 (1:是非保清关单, 0:不是非保清关单)
     */
    @Column(name = "fb_flag")
    private Integer fbFlag;

    /**
     * 非保核放单流水号
     */
    @Column(name = "fb_checklist_sn")
    private String fbChecklistSn;

    /**
     * 进境口岸
     */
    @Column(name = "entry_port")
    private String entryPort;

    /**
     * 起运港（始发机场）
     */
    @Column(name = "from_location")
    private String fromLocation;

    /**
     * 上游原始报文
     */
    @Column(name = "upstream_orig_msg")
    private String upstreamOrigMsg;

    /**
     * （调入/调出）关联清关单号
     */
    @Column(name = "associated_inve_customs_sn")
    private String associatedInveCustomsSn;

    /**
     * 核注单备注
     */
    @Column(name = "endorsement_remark")
    private String endorsementRemark;

    /**
     * 操作人
     */
    @Column(name = "operator_name")
    private String operatorName;

    /**
     * 清关完成时间
     */
    @Column(name = "inventory_complete_time")
    private Date inventoryCompleteTime;

    /**
     * 服务完成时间
     */
    @Column(name = "inventory_finish_time")
    private Date inventoryFinishTime;

    /**
     * 草单比对类型
     */
    @Column(name = "draft_compare_type")
    private Integer draftCompareType;
    /**
     * 草单比对类型
     */
    @Column(name = "urgent_sort")
    private Integer urgentSort;

    /**
     * 申报表编号
     */
    @Column(name = "declare_form_no")
    private String declareFormNo;
    /**
     * 是否锁定库存
     * 0：未锁定 1：锁定
     */
    @Column(name = "lock_stock_flag")
    private Integer lockStockFlag;
}
