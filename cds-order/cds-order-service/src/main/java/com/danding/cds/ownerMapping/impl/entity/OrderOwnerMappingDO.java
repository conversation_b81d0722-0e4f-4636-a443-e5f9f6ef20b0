package com.danding.cds.ownerMapping.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;

/**
 * <AUTHOR>
 * @Create 2021/7/5  9:37
 * @Describe
 **/
@Table(name = "ccs_order_owner_mapping")
@Data
public class OrderOwnerMappingDO extends BaseDO {
    @Column(name = "order_sn")
    private String orderSn;
    @Column(name = "order_type")
    private String orderType;
    @Column(name = "entityWarehouseCode")
    private String entityWarehouseCode;
    @Column(name = "entityWarehouseName")
    private String entityWarehouseName;
    @Column(name = "ownerCode")
    private String ownerCode;
    @Column(name = "ownerName")
    private String ownerName;
}
