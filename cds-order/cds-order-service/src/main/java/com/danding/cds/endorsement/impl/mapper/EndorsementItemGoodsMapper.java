package com.danding.cds.endorsement.impl.mapper;

import com.danding.cds.endorsement.impl.entity.EndorsementItemGoodsDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface EndorsementItemGoodsMapper extends Mapper<EndorsementItemGoodsDO>,
        InsertListMapper<EndorsementItemGoodsDO>, BatchUpdateMapper<EndorsementItemGoodsDO>, AggregationPlusMapper<EndorsementItemGoodsDO> {

}