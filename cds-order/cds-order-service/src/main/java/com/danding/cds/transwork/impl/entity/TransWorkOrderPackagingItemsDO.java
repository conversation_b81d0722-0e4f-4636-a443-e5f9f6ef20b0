package com.danding.cds.transwork.impl.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

import com.danding.cds.common.model.BaseDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Data
@Table(name = "ccs_trans_work_order_packaging_items")
public class TransWorkOrderPackagingItemsDO extends BaseDO {

    /**
     * 运输作业id
     */
    @Column(name = "trans_id")
    private Long transId;

    /**
     * 商品编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商品名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 数量
     */
    @Column(name = "item_quantity")
    private Long itemQuantity;

    /**
     * 实际发货数量
     */
    @Column(name = "act_delivery_qty")
    private Long actDeliveryQty;


    /**
     * 实际签收数量
     */
    @Column(name = "act_sign_qty")
    private Long actSignQty;


    /**
     * 托盘号
     */
    @Column(name = "pallet_no")
    private String palletNo;

    /**
     * 箱号
     */
    @Column(name = "box_no")
    private String boxNo;

    /**
     * 装箱明细行号
     */
    @Column(name = "packaging_item_line_no")
    private String packagingItemLineNo;

}