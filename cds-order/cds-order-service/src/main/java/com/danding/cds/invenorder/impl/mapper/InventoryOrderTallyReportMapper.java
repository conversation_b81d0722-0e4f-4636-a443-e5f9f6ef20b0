package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderTallyReportDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface InventoryOrderTallyReportMapper extends Mapper<InventoryOrderTallyReportDO>, InsertListMapper<InventoryOrderTallyReportDO>, BatchUpdateMapper<InventoryOrderTallyReportDO>, AggregationPlusMapper<InventoryOrderTallyReportDO> {
}