package com.danding.cds;

import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.config.XxlJobConfig;
import com.danding.cds.v2.service.BaseDataInitService;
import com.danding.cds.v2.service.IdSequenceInitService;
import com.danding.component.mybatis.common.interceptor.plus.MybatisPlusTenantLineInterceptorConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

@EnableDubbo
@EnableScheduling
@SpringBootApplication(
        exclude = {DataSourceAutoConfiguration.class, FlywayAutoConfiguration.class}
        , scanBasePackages = {
        "com.danding.cds"
        , "com.danding.logistics"
})
@MapperScan(basePackages = {"com.danding.cds.**.impl.mapper", "com.danding.cds.**.mapper"})
//@Import(XxlJobConfig.class)
@EnableTransactionManagement(proxyTargetClass = true)
@ServletComponentScan(basePackages = "com.danding.cds.http.saas.filter")
@Import({MybatisPlusTenantLineInterceptorConfiguration.class, XxlJobConfig.class})
public class AppOrderServer {
    public static void main(String[] args) {
        if (!StringUtils.isEmpty(System.getProperty("env")) && "DEV".equals(System.getProperty("env").toUpperCase())) {
            if (StringUtils.isEmpty(System.getProperty("local"))) {
                System.setProperty("local", "true");
            }
        }

        System.setProperty("es.set.netty.runtime.available.processors", "false");  // 解决SpringBoot netty与ES netty 相关jar冲突
        System.setProperty("rocketmq.client.logLevel","ERROR");                    // 提升RocketMQ日志级别 防止磁盘压力过大
        SpringApplication.run(AppOrderServer.class, args);
    }


    /**
     * 启动后加载下基础数据信息
     */
    @Bean
    public CommandLineRunner commandLineRunner() {
        return new CommandLineRunner() {

            @Autowired
            private BaseDataInitService baseDataInitService;
            @Autowired
            private IdSequenceInitService idSequenceInitService;

            @Override
            public void run(String... args) {

                if (!EnvironmentConfig.isOnline()) {
                    if (EnvironmentConfig.isDev()) {
                        baseDataInitService.init();
                    } else if (EnvironmentConfig.isDev()) {
                        baseDataInitService.init();
                    }
                    return;
                }
                baseDataInitService.init();
                idSequenceInitService.init();
            }
        };
    }
}