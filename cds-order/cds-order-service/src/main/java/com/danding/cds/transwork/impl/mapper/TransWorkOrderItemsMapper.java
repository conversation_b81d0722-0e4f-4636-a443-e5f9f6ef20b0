package com.danding.cds.transwork.impl.mapper;

import com.danding.cds.transwork.impl.entity.TransWorkOrderItemsDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.common.Mapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
public interface TransWorkOrderItemsMapper extends Mapper<TransWorkOrderItemsDO>, InsertListMapper<TransWorkOrderItemsDO>, BatchUpdateMapper<TransWorkOrderItemsDO>, AggregationPlusMapper<TransWorkOrderItemsDO> {
}
