package com.danding.cds.handoverorder.impl.mapper;

import com.danding.cds.handoverorder.impl.entity.HandoverOrderDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface HandoverOrderMapper extends Mapper<HandoverOrderDO>, InsertListMapper<HandoverOrderDO>, BatchUpdateMapper<HandoverOrderDO>, AggregationPlusMapper<HandoverOrderDO> {
}