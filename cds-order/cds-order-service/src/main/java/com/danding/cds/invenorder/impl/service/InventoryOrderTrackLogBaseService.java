package com.danding.cds.invenorder.impl.service;

import com.danding.cds.invenorder.impl.entity.InventoryOrderTrackLogDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderTrackLogMapper;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 清关单日志基础服务
 * @date 2024/8/6 11:25
 */
@Service
public class InventoryOrderTrackLogBaseService {
    @Resource
    private InventoryOrderTrackLogMapper inventoryOrderTrackLogMapper;

    public List<InventoryOrderTrackLogDO> selectTrackLogByInventoryOrderId(Long inventoryOrderId) {
        Example example = new Example(InventoryOrderTrackLogDO.class);
        example.createCriteria().andEqualTo("inventoryOrderId", inventoryOrderId).andEqualTo("deleted", false);
        example.orderBy("operateTime").desc();
        return inventoryOrderTrackLogMapper.selectByExample(example);
    }

    public void save(InventoryOrderTrackLogDO inventoryOrderTrackLogDO) {
        inventoryOrderTrackLogMapper.insertSelective(inventoryOrderTrackLogDO);
    }

}
