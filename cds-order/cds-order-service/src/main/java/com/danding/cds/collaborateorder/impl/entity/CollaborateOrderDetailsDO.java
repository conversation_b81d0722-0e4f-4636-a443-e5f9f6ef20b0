package com.danding.cds.collaborateorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description 协同单子单
 * @date 2022/3/31
 */
@Table(name = "ccs_collaborate_order_item")
@Data
public class CollaborateOrderDetailsDO extends BaseDO {

    /**
     * 清关单表体id
     */
    @Column(name = "inve_item_id")
    private Long inveItemId;

    /**
     * 协同单id
     */
    @Column(name = "collaborate_order_id")
    private Long collaborateOrderId;

    /**
     * 货品ID
     */
    @Column(name = "goods_id")
    private String goodsId;

    /**
     * 货品名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * sku
     */
    @Column(name = "sku")
    private String sku;

    /**
     * 条形码
     */
    @Column(name = "bar_code")
    private String barCode;

    /**
     * 申报料号
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 净重
     */
    @Column(name = "net_weight")
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 申报数量
     */
    @Column(name = "declare_qty")
    private Integer declareQty;

    /**
     * 理货数量
     */
    @Column(name = "tally_qty")
    private Integer tallyQty;

    /**
     * 差异类型,1:多品；2:少品；3:多件；4:少件
     */
    @Column(name = "diff_type")
    private Integer diffType;

    /**
     * 差异数量
     */
    @Column(name = "diff_qty")
    private Integer diffQty;

    /**
     * 商品序号
     */
    @Column(name = "goods_seq_no")
    private String goodsSeqNo;

    /**
     * 标识
     */
    @Column(name = "label")
    private String label;


}
