package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderAttachDO;
import com.danding.cds.invenorder.impl.entity.InventoryOrderInfoDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface InventoryOrderAttachMapper extends Mapper<InventoryOrderAttachDO>, InsertListMapper<InventoryOrderAttachDO>, BatchUpdateMapper<InventoryOrderAttachDO>, AggregationPlusMapper<InventoryOrderAttachDO> {
}