package com.danding.cds.transwork.impl.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

import com.danding.cds.common.model.BaseDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Table(name = "ccs_trans_work_order")
@Data
public class TransWorkOrderDO extends BaseDO {
    /**
     * 运输作业单号
     */
    @Column(name = "trans_no")
    private String transNo;

    /**
     * 作业单类型
     */
    @Column(name = "order_type")
    private String orderType;

    /**
     * 作业单状态
     */
    @Column(name = "status")
    private Integer status;

    /**
     * 淘天运输作业单号
     */
    @Column(name = "out_order_no")
    private String outOrderNo;

    /**
     * 车辆资源编号
     */
    @Column(name = "truck_resource_no")
    private String truckResourceNo;

    /**
     * 车牌号
     */
    @Column(name = "truck_no")
    private String truckNo;

    /**
     * 车型名称
     */
    @Column(name = "truck_type")
    private String truckType;

    /**
     * 店铺id
     */
    @Column(name = "shop_id")
    private Long shopId;

    /**
     * 集装箱类型
     */
    @Column(name = "container_type")
    private String containerType;

    /**
     * 历史运输次数
     */
    @Column(name = "history_shipments_num")
    private Integer historyShipmentsNum;

    /**
     * 运次编号
     */
    @Column(name = "shipment_no")
    private String shipmentNo;

    /**
     * 备注
     */
    @Column(name = "remark")
    private String remark;

    /**
     * 货主编码
     */
    @Column(name = "owner_code")
    private String ownerCode;

    /**
     * 淘天货主编码
     */
    @Column(name = "tt_owner_code")
    private String ttOwnerCode;

    /**
     * 仓库编码
     */
    @Column(name = "warehouse_code")
    private String warehouseCode;

    /**
     * 单据来源
     */
    @Column(name = "order_origin")
    private String orderOrigin;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    private Date finishTime;

    /**
     * 实际到达时间
     */
    @Column(name = "act_arr_time")
    private Date actArrTime;

    /**
     * 实际发车时间
     */
    @Column(name = "act_dep_time")
    private Date actDepTime;

    /**
     * 实际签收时间
     */
    @Column(name = "act_sign_time")
    private Date actSignTime;

    /**
     * 附件
     */
    @Column(name = "attachments")
    private String attachments;

    /**
     * 基础信息
     */
    @Column(name = "base_info")
    private String baseInfo;

    /**
     * 头程承运信息
     */
    @Column(name = "carrier_info")
    private String carrierInfo;

    /**
     * 发货人
     */
    @Column(name = "shipper_info")
    private String shipperInfo;

    /**
     * 收货人
     */
    @Column(name = "consignee_info")
    private String consigneeInfo;

    /**
     * 通知人
     */
    @Column(name = "notify_person_info")
    private String notifyPersonInfo;

    /**
     * 商家联系人信息
     */
    @Column(name = "booking_party_info")
    private String bookingPartyInfo;

    /**
     * 供应商信息
     */
    @Column(name = "supplier_info")
    private String supplierInfo;

    /**
     * 装箱信息
     */
    @Column(name = "load_info")
    private String loadInfo;

    /**
     * 包裹信息
     */
    @Column(name = "package_info")
    private String packageInfo;
}