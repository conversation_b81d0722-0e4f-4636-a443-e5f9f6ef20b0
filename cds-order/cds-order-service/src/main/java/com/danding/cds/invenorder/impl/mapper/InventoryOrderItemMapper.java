package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderItemDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface InventoryOrderItemMapper extends Mapper<InventoryOrderItemDO>, InsertListMapper<InventoryOrderItemDO>, BatchUpdateMapper<InventoryOrderItemDO>, AggregationPlusMapper<InventoryOrderItemDO> {
    @Update("UPDATE ccs_inventory_order_item SET ref_endorsement_id = NULL,ref_endorsement_sn= NULL WHERE ref_inve_order_id = #{0}")
    void disconEndorsement(Long refInventoryId);
}