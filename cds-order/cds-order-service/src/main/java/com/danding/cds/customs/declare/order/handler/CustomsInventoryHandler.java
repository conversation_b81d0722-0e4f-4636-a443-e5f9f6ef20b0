package com.danding.cds.customs.declare.order.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.aspects.handler.StockInventoryHandler;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.enums.DeclareEnum;
import com.danding.cds.common.enums.InventoryCalculationTypeEnums;
import com.danding.cds.common.enums.InventoryChangeTypeEnums;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.dto.CompanyDistrictDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.company.api.vo.CompanyResVo;
import com.danding.cds.customs.declare.base.CustomsDeclareOrderHandler;
import com.danding.cds.customs.declare.base.CustomsDeclareUtils;
import com.danding.cds.customs.declare.mq.CustomsDeclareMQProducer;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.ModifyDeclareStat;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.service.CustomsLogisticsService;
import com.danding.cds.declare.base.component.util.DeclareUtils;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.model.WrapBeanInfo;
import com.danding.cds.declare.sdk.model.account.AccountBookDto;
import com.danding.cds.declare.sdk.model.company.CompanyDeclareConfigDto;
import com.danding.cds.declare.sdk.model.company.CompanyInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryInfo;
import com.danding.cds.declare.sdk.model.inventory.CustomsInventoryItemInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.sdk.model.route.RouteDeclareConfig;
import com.danding.cds.declare.sdk.model.route.RouteInfo;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.jieztech.*;
import com.danding.cds.log.api.service.TrackLogService;
import com.danding.cds.order.api.dto.*;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.api.service.CustomsStatusMappingService;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.pdd.suppprt.PddEncodeUtil;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.dto.RouteExtra;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.route.api.vo.RouteInfoResVo;
import com.danding.cds.route.api.vo.RouteResVo;
import com.danding.cds.stock.StockContextUtil;
import com.danding.cds.stock.annotations.StockVerify;
import com.danding.cds.stock.api.service.StockInventoryService;
import com.danding.cds.stock.bean.OrderInfoDto;
import com.danding.cds.v2.bean.dto.InventoryModifyDeclareDto;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.bean.vo.req.BarCodeDeclareReqVo;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.mq.producer.ByteDanceDeclareProducer;
import com.danding.cds.v2.mq.producer.PddDeclareProducer;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.ByteDanceDeclareService;
import com.danding.cds.v2.service.JdSecurityLogService;
import com.danding.cds.v2.service.PddDeclareService;
import com.danding.cds.v2.service.base.CustomsInventoryBaseService;
import com.danding.cds.v2.service.base.InventoryModifyDeclareBaseService;
import com.danding.cds.v2.util.BuildInfoUtil;
import com.danding.cds.v2.util.RouteDxpUtils;
import com.danding.cds.v2.util.SortUtil;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.exception.BusinessException;
import com.github.kevinsawicki.http.HttpRequest;
import com.gvt.apollo.ApolloSdk;
import com.gvt.apollo.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
@RefreshScope
public class CustomsInventoryHandler extends CustomsDeclareOrderHandler {

    @Autowired
    private CustomsInventoryHandler customsInventoryHandler;

    @Autowired
    private CustomsDeclareMQProducer producer;

    @Autowired
    private InventoryModifyDeclareBaseService inventoryModifyDeclareBaseService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @Autowired
    private CustomsInventoryBaseService customsInventoryBaseService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private OrderService orderService;

    @DubboReference
    private CustomsStatusMappingService customsStatusMappingService;

    @DubboReference
    private RouteService routeService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CustomsLogisticsService customsLogisticsService;

    @DubboReference
    private TrackLogService trackLogService;

    @DubboReference
    private StockInventoryService stockInventoryService;
    @Autowired
    private PddDeclareService pddDeclareService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Autowired
    private JdSecurityLogService jdSecurityLogService;

    @Autowired
    private CustomsSupport support;

    @Value("${pdd.host:}")
    private String PDD_HOST;

    @Value("${jieztech.host:}")
    private String jieztechHost;

    @Value("${jieztech.appId:}")
    private String jieztechAppId;

    @Value("${jieztech.privateKey:}")
    private String jieztechPrivateKey;

    @Value("${jieztech.subMerchantId:}")
    private String jieztechSubMerchantId;

    @Value("${jieztech.subMerchantName:}")
    private String jieztechSubMerchantName;

    @Value("${jieztech.merchantId:}")
    private String jieztechMerchantId;

    @Value("${jieztech.merchantName:}")
    private String jieztechMerchantName;

    @Value("${jieztech.declare.appId:}")
    private String jieztechAppDeclareId;

    @Value("${jieztech.declare.privateKey:}")
    private String jieztechDeclarePrivateKey;

    @Value("${filter.special.char.config:}")
    private String filterSpecialCharConfig;

    @Autowired
    private PddDeclareProducer pddDeclareProducer;

    @Autowired
    private ByteDanceDeclareProducer byteDanceDeclareProducer;

    @Autowired
    private ByteDanceDeclareService byteDanceDeclareService;

    @Autowired
    private BaseDataService baseDataService;

    /**
     * 要注入要Autowire
     */
    @Autowired
    public CustomsInventoryHandler(CustomsDeclareMQProducer producer) {
        super(null);
        this.producer = producer;
    }


    @Override
    protected DeclareEnum declareEnum() {
        return DeclareEnum.INVENTORY;
    }

    /**
     * 检查是否由该模块负责处理
     *
     * @param request
     * @return
     */
    @Override
    protected Boolean checkHandle(OrderDTO request) {
        log.info("[op:CustomsInventoryHandler-check] request declareSn={}, status={}", request.getDeclareOrderNo(), OrderStatus.getEnum(request.getStatus()).getDesc());
        String actionJson = request.getActionJson();
        if (!actionJson.contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            log.info("[op:CustomsInventoryHandler-check] declareNo={} actionJson 不包含 DECLARE_INVENTORY", request.getDeclareOrderNo());
            return false;
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(request.getId(), request.getCustomsInventorySn());
//        if (customsInventoryDTO == null && JSON.parseArray(request.getActionJson(),String.class).contains(RouteActionEnum.DECLARE_INVENTORY.getCode())){
//            try {
//                Thread.sleep(500); // 避免前面的事务来不及提交
//            } catch (InterruptedException e) {
//                log.warn("处理异常：{}", e.getMessage(), e);
//            }
//            customsInventoryDTO = customsInventoryService.findByOrderFull(request.getId());
//        }
        if (customsInventoryDTO == null) {
            log.info("[op:CustomsInventoryHandler-check] inventory is null, declareSn={}", request.getDeclareOrderNo());
            return false;
        }
        log.info("[op:CustomsInventoryHandler-check] inventory declareSn={}, status={}", request.getDeclareOrderNo(), CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc());

        // 因为重推时订单状态可能是申报中+非异常 所以如果需要重推，都需要重置状态
        return CustomsActionStatus.DEC_WAIT.getValue().equals(customsInventoryDTO.getStatus())
                || (CustomsActionStatus.DEC_ING.getValue().equals(customsInventoryDTO.getStatus()) && !request.getExceptionFlag());
    }

    /**
     * 处理业务逻辑
     *
     * @param request
     */
    @Override
    protected void handle(OrderDTO request) {
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(request.getId(), request.getCustomsInventorySn());
        try {
            List<CustomsInventoryItemDeclareDTO> itemDeclareList = this.getCustomsInventoryItemDeclareList(customsInventoryDTO);
            //清单申报中 检查可用库存
            try {
                if (customsInventoryDTO.getLastDeclareTime() != null) {
                    customsInventoryHandler.customsInventorySend(customsInventoryDTO, itemDeclareList, request, customsInventoryDTO.getIsOccupiedStock(), true);
                } else {
                    customsInventoryHandler.customsInventorySend(customsInventoryDTO, itemDeclareList, request, customsInventoryDTO.getIsOccupiedStock(), false);
                }
            } catch (ArgsInvalidException ex) {
                log.info("申报单: {} ,清单申报Handler处理异常：{}", request.getDeclareOrderNo(), ex.getMessage(), ex);
            } catch (ArgsErrorException e) {
                log.error("申报单: {} ,清单申报Handler处理异常：{}", request.getDeclareOrderNo(), e.getMessage(), e);
            }
        } catch (Exception ex) {
            String message = ExceptionJoinUtil.getExceptionMessage(ex);
            log.error("申报单: {} ,清单申报Handler处理异常：{}", request.getDeclareOrderNo(), message, ex);
            // 处理异常，这里更改下申报状态为带申报，并维护下
            customsInventoryService.updateStatusResetDeclareTime(customsInventoryDTO.getSn(), CustomsActionStatus.DEC_WAIT);
            // Step::添加配置异常
            CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("DECLARE_PROCESSING_ERROR");
            if (Objects.nonNull(mappingDTO)) {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(message) && message.length() > 1024) {
                    message = message.substring(0, 1024);
                }
                orderService.addExceptionSection(request.getId(), request.getSn(), mappingDTO.getId(), "清单申报Handler处理异常:" + message, request.getCreateTime());
            }
        }
    }

    public List<CustomsInventoryItemDeclareDTO> getCustomsInventoryItemDeclareList(CustomsInventoryDTO customsInventoryDTO) {
        //获取清单表体
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsInventoryDTO.getAccountBookId());
        List<String> productIdList = customsInventoryItemDTOList.stream().map(c -> {
            String extraJson = c.getExtraJson();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
            if (Objects.nonNull(itemExtra.getUnifiedProductId())) {
                return itemExtra.getUnifiedProductId();
            }
            return itemExtra.getProductId();
        }).filter(Objects::nonNull).collect(Collectors.toList());
        log.info("getCustomsInventoryItemDeclareList productIdList={}", JSON.toJSONString(productIdList));
        Map<String, List<CustomsBookItemDTO>> productItemDTOList = goodsRecordService.getCustomsDeclareProductIdList(customsBookDTO.getId(), productIdList);
        //构造申报参数
        List<CustomsInventoryItemDeclareDTO> itemDeclareList = customsInventoryItemDTOList.stream().map(item -> {
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(item.getExtraJson(), CustomsInventoryItemExtra.class);
            String productId;
            if (Objects.nonNull(itemExtra.getUnifiedProductId())) {
                productId = itemExtra.getUnifiedProductId();
            } else {
                productId = itemExtra.getProductId();
            }
            String assignOriginCountry = itemExtra.getAssignOriginCountry();
            CustomsInventoryItemDeclareDTO itemDeclareDTO = new CustomsInventoryItemDeclareDTO();
            itemDeclareDTO.setCustomsInventoryItemDTO(item);
            Integer bookTag = customsBookDTO.getBookTag();
            List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(bookTag);
            if (bookTagList.contains(CustomsBookTagEnums.NEGATIVE_STOCK_CHECK_ENABLE.getCode())) {
                itemDeclareDTO.setNegativeStockCheckEnable(true);
            } else {
                itemDeclareDTO.setNegativeStockCheckEnable(false);
            }
            if (bookTagList.contains(CustomsBookTagEnums.STOCK_AUTO_SWITCH_ENABLE.getCode())) {
                itemDeclareDTO.setStockAutoSwitchEnable(true);
            } else {
                itemDeclareDTO.setStockAutoSwitchEnable(false);
            }
            if (productItemDTOList.containsKey(productId)) {
                List<CustomsBookItemDTO> itemSwitchUpList = productItemDTOList.get(productId);
                //如果上游指定了原产国 则过滤备用账册列表只保留指定的原产国多序号
                if (StringUtils.isNotEmpty(assignOriginCountry)) {
                    itemSwitchUpList = itemSwitchUpList.stream().filter(c -> Objects.equals(c.getOriginCountry(), assignOriginCountry)).collect(Collectors.toList());
                }
                itemSwitchUpList = itemSwitchUpList.stream()
                        .filter(c -> Objects.equals(c.getEnable(), 1)
                                && !Objects.equals(c.getId(), item.getBookItemId())
                                && Objects.nonNull(c.getOccupiedNum()) && Objects.nonNull(c.getAvailableNum()))
                        .distinct()
                        .collect(Collectors.toList());
                itemDeclareDTO.setItemSwitchBackUpList(itemSwitchUpList);
            }
            return itemDeclareDTO;
        }).collect(Collectors.toList());
        return itemDeclareList;
    }

    @Transactional(rollbackFor = Exception.class)
    public void modifyHandle(List<Long> ids) throws ArgsErrorException {
        List<InventoryModifyDeclareDto> modifyList = inventoryModifyDeclareBaseService.getListByIds(ids);
        modifyList.forEach(m -> {
            // 更新下状态，变更申报状态
            inventoryModifyDeclareBaseService.updateOrderStatus(m.getId(), ModifyDeclareStat.PENDING.getValue(), new Date());
            log.info("CustomsInventoryHandler modifyHandle 变更清单ID={} updateOrderStatus完成", m.getId());
            // 申报
            OrderDTO request = orderService.findByIdFull(m.getOrderId());
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(m.getOrderId(), request.getCustomsInventorySn());
            log.info("CustomsInventoryHandler modifyHandle 变更清单ID={} customsInventoryDTO={}", m.getId(), JSON.toJSONString(customsInventoryDTO));
//            customsInventoryDTO.setId(m.getId());
            customsInventoryDTO.setType("EDIT");
            //清单申报中 检查可用库存
            List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            log.info("CustomsInventoryHandler modifyHandle customsInventoryItemDTOList={}", JSON.toJSONString(customsInventoryItemDTOList));
            List<CustomsInventoryItemDeclareDTO> inventoryItemDeclareList = getCustomsInventoryItemDeclareList(customsInventoryDTO);
            customsInventoryHandler.customsInventorySend(customsInventoryDTO, inventoryItemDeclareList, request, customsInventoryDTO.getIsOccupiedStock(), true);
        });
    }

    @Value("${jieztech.haikouRoute:HAIKOU-JZXLS}")
    private String haikouRoute;
    @Value("${jieztech.haikouCode:6409}")
    private String haikouCode;
    @Value("${jieztech.kunmingRoute:KUNMING-JZXLS}")
    private String kunmingRoute;
    @Value("${jieztech.kunmingCode:8634}")
    private String kunmingCode;
    @Value("${jieztech.taoBaoGlobalFx:}")
    private String[] taoBaoGlobalFx;
    @Value("${jieztech.weatherForwardJieztech:false}")
    private Boolean weatherForwardJieztech;

    /**
     * 清单申报
     *
     * @param customsInventoryDTO
     * @param customsInventoryItemDeclareDTOList
     * @param orderDTO
     * @param isOccupiedStock                    是否占用库存，true 占用, false 未占用
     */
    @StockVerify(methodParameters = {1, 0, 3, 2, 4}, changeType = InventoryCalculationTypeEnums.ADD_OCCUPATION, handler = StockInventoryHandler.class, blockFlag = true, stockVerifyEnable = true)
    public void customsInventorySend(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDeclareDTO> customsInventoryItemDeclareDTOList, OrderDTO orderDTO, Boolean isOccupiedStock, Boolean rePush) {
        log.info("[CustomsInventoryHandler-customsInventorySend customsInventoryDTO={} customsInventoryItemDeclareDTOList={} request={}]", JSON.toJSONString(customsInventoryDTO), JSON.toJSONString(customsInventoryItemDeclareDTOList), JSON.toJSONString(orderDTO));
        if (customsInventoryDTO.getLastDeclareTime() != null && (DateTime.now().minusMinutes(10)).isBefore(new DateTime(customsInventoryDTO.getLastDeclareTime()))) {
            throw new ArgsInvalidException("申报时间小于十分钟，不进行申报");
        }
        if (CustomsActionStatus.DEC_ING.getValue().equals(customsInventoryDTO.getStatus())) {
            throw new ArgsInvalidException("清单状态为申报中，不进行申报");
        }
        List<Integer> orderTag = DeclareOrderTagEnums.getOrderTag(orderDTO.getOrderTags());
        if (orderTag.contains(DeclareOrderTagEnums.DISCARD.getCode())) {
            throw new ArgsInvalidException("申报单已作废，不进行申报");
        }
        // Step::各类配置有效性校验
        String configError = "";
        CompanyDTO assureCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getAssureCompanyId());
        if (Objects.isNull(assureCompany)) {
            configError += "担保企业不存在";
        } else if (assureCompany.getEnable() != 1) {
            configError += "担保企业未启用;";
        }
        CompanyDTO areaCompany = baseDataService.getSpecialClientCodeCompanyById(customsInventoryDTO.getAreaCompanyId());
        if (Objects.isNull(areaCompany)) {
            configError += "区内企业不存在";
        } else if (areaCompany.getEnable() != 1) {
            configError += "区内企业未启用;";
        }
        // 补丁： 拼多多 且 中通 转中通国际
        if (12 == orderDTO.getEbpId() && 4 == customsInventoryDTO.getLogisticsCompanyId()) {
            ExpressDTO expressDTO = baseDataService.getExpressDTOByCode("ZTOINTER");
            customsInventoryDTO.setLogisticsCompanyId(expressDTO.getExpressCompanyId());
        }
        CompanyDTO logisCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getLogisticsCompanyId());
        if (Objects.isNull(logisCompany)) {
            configError += "物流企业不存在";
        } else if (logisCompany.getEnable() != 1) {
            configError += "物流企业未启用;";
        }
        CompanyDTO ebpCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbpId());
        if (Objects.isNull(ebpCompany)) {
            configError += "电商平台不存在";
        } else if (ebpCompany.getEnable() != 1) {
            configError += "电商平台未启用;";
        }
        CompanyDTO ebcCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsInventoryDTO.getEbcId());
        if (Objects.isNull(ebcCompany)) {
            configError += "电商企业不存在";
        } else if (ebcCompany.getEnable() != 1) {
            configError += "电商企业未启用;";
        }
//        CompanyDTO agentCompany = companyService.findById(customsInventoryDTO.getAgentCompanyId());
        CompanyResVo agentCompany = companyService.findUnifiedCrossInfoByIdV2(customsInventoryDTO.getAgentCompanyId());
        if (Objects.isNull(agentCompany)) {
            configError += "申报企业不存在";
        } else if (agentCompany.getEnable() != 1) {
            configError += "申报企业未启用;";
        }

        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
        if (customsBookResVo.getEnable() != 1) {
            configError += "账册企业未启用;";
        }
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        RouteDTO routeDTO = baseDataService.getRouteDTOByCode(inventoryExtra.getRouteCode());
        if (Objects.isNull(routeDTO)) {
            configError += "申报路径不存在,路径标识：" + inventoryExtra.getRouteCode();
        } else if (routeDTO.getEnable() != 1) {
            configError += "申报路径未启用,路径标识：" + inventoryExtra.getRouteCode();
        }
        //判断是否申报运单
        CompanyDTO logisticsDeclareCompany = new CompanyDTO();
        CustomsLogisticsDTO customsLogisticsDTO = customsLogisticsService.findByLogisticsNo(customsInventoryDTO.getLogisticsNo());
        boolean isDeclareLogisticsInSystem = false;
        if (routeDTO.getActionList().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())
                && customsLogisticsDTO != null) {
            logisticsDeclareCompany = baseDataService.getUnifiedCrossCodeCompanyById(customsLogisticsDTO.getAgentCompanyId());
            isDeclareLogisticsInSystem = true;
        }

        String payCompanyCode = inventoryExtra.getPayCompanyCode();
        CompanyDTO payCompany = null;
        if (!StringUtils.isEmpty(payCompanyCode)) {
//            payCompany = baseDataService.getCompanyDTOByCode(payCompanyCode);
            payCompany = baseDataService.getCompanyDTOByUnifiedCrossBroderCode(payCompanyCode);
            if (payCompany != null) {
                if (payCompany.getEnable() != 1) {
                    configError += "支付企业未启用;";
                }
            } else {
                configError += "支付企业不存在;";
            }
        }
        if (!StringUtils.isEmpty(configError)) {
            // Step::添加配置异常
            CustomsStatusMappingDTO mappingDTO = baseDataService.getCustomsStatusMappingDTOByCode("CONFIG_ERROR");
            orderService.addExceptionSection(orderDTO.getId(), orderDTO.getSn(), mappingDTO.getId(), configError, orderDTO.getCreateTime());
            throw new ArgsErrorException(configError + "，不进行申报");
        }
        //根据切面校验后的数据 找到最终申报的表体信息
        List<CustomsInventoryItemDTO> customsInventoryItemDTOList = updateCustomsInventoryItem(customsInventoryDTO, customsInventoryItemDeclareDTOList);
        this.itemWeightPreCheck(customsInventoryDTO, customsInventoryItemDTOList);
        RouteInfoResVo routeResVo = routeService.findRouteByCode(inventoryExtra.getRouteCode());
        //过滤掉emoji表情符
        if (!judgeIsLegalAndReplace(customsInventoryDTO, orderDTO, ebpCompany)) return;
        // Step::申报数据模型构建
        WrapInventoryOrderInfo model = this.buildModel(orderDTO, customsInventoryDTO, customsInventoryItemDTOList, assureCompany,
                areaCompany, logisCompany, ebpCompany, ebcCompany, agentCompany, payCompany,
                logisticsDeclareCompany, isDeclareLogisticsInSystem, customsBookResVo, routeResVo);
        log.info("[op:CustomsInventoryHandler-handle-buildModel]  is finished, declareSn= {} , WrapInventoryOrderInfo ={}", orderDTO.getDeclareOrderNo(), JSON.toJSONString(model));
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        OrderSensitivePlainText orderSensitivePlainText = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getSensitivePlainText).orElse(null);
        model.setPlainText(orderSensitivePlainText);
        //更新清单申报状态并增加申报次数统计
        Integer declareFrequency = customsInventoryDTO.getDeclareFrequency();
        declareFrequency = declareFrequency == null ? 1 : declareFrequency + 1;
        customsInventoryService.updateByPush(customsInventoryDTO.getId(), CustomsActionStatus.DEC_ING.getValue(), customsInventoryDTO.getCreateTime(), declareFrequency);
        //更新内部流转状态和记录申报记录
        RouteDeclareConfig inventoryConfig = this.updateOrderInternalStatusAndDeclareRecord(model, orderDTO);
        String finalImpl = Objects.isNull(inventoryConfig) ? null : inventoryConfig.getFinalImpl();
        //凡尔纳88账册的清单数据转发一份给芥舟
        if (this.checkWeatherForwardJieztech(inventoryExtra, routeDTO)) {
            this.declareForward2Jieztech(customsInventoryDTO, assureCompany, areaCompany, logisCompany, ebpCompany, ebcCompany, agentCompany, logisticsDeclareCompany, isDeclareLogisticsInSystem, payCompany);
        }
        /**
         * 这里首先判断下是否能够使用动态申报的形式
         */
        if (model.dynamicDeclareEnable()) {
            DeclareUtils.inventoryDeclare(model, finalImpl);
        } else if (PddEncodeUtil.isPddOrder(ebpCompany.getUnifiedCrossBroderCode(), customsInventoryDTO.getDeclareOrderNo())) {
            //发送拼多多云内申报MQ
            log.info("申报单号：{}，开始发送拼多多云内申报Mq", customsInventoryDTO.getDeclareOrderNo());
            pddDeclareProducer.inventoryOrderSend(model);
        }
        // 根据电商平台进行区分，向芥舟数字清关平台传输清单报文，ccs不进行申报
        else if (this.checkWeatherDeclareByJieztech(routeDTO)) {
            this.declareByJieztech(customsInventoryDTO, assureCompany, areaCompany, logisCompany, agentCompany, routeDTO, logisticsDeclareCompany, isDeclareLogisticsInSystem);
        } else {
            support.clearInventoryDeclare(model);
        }
        // logService.logNormal(LogCode.LOG_INVENTORY, LogOperation.DECLARE,
        //         customsInventoryDTO.getSn(),customsInventoryDTO.getDeclareOrderNo(),"操作清单申报");
        //清单申报中 库存变动
        if (Boolean.FALSE.equals(customsInventoryDTO.getIsOccupiedStock())
                || (Objects.isNull(customsInventoryDTO.getIsOccupiedStock()) && Objects.isNull(customsInventoryDTO.getLastDeclareTime()))) {
            List<UpdateInventoryDTO> list = new ArrayList<>();
//            List<CustomsInventoryItemDTO> customsInventoryItemDTOList = customsInventoryItemDeclareDTOList.stream().map(CustomsInventoryItemDeclareDTO::getCustomsInventoryItemDTO).collect(Collectors.toList());
            customsInventoryItemDTOList.forEach(c -> {
                UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
                updateInventoryDTO.setBusinessNo(customsInventoryDTO.getDeclareOrderNo()).setCustomsBookId(customsInventoryDTO.getAccountBookId()).setChangeType(InventoryChangeTypeEnums.LIST_ORDER_APPLYING);
                CustomsInventoryItemExtra itemExtra = JSON.parseObject(c.getExtraJson(), CustomsInventoryItemExtra.class);
                updateInventoryDTO.setCustomsBookItemId(c.getBookItemId()).setProductId(itemExtra.getProductId())
                        .setDeclareUnitQfy(c.getCount()).setGoodsSeqNo(itemExtra.getGoodsSeqNo());
                list.add(updateInventoryDTO);
            });
            stockInventoryService.updateInventory(list);
            customsInventoryService.updateIsOccupiedStockSection(customsInventoryDTO.getId(), Boolean.TRUE, customsInventoryDTO.getCreateTime());
        }

        // 给下京东安全追踪日志
        jdSecurityLogService.jdSecurityLogSend(orderDTO.getEbpId(), orderDTO.getSystemGlobalSn(), DeclareEnum.INVENTORY);
    }


    private void itemWeightPreCheck(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> customsInventoryItemDTOList) {
        BigDecimal grossWeight = customsInventoryDTO.getGrossWeight();
        BigDecimal totalWeight = BigDecimal.ZERO;
        for (CustomsInventoryItemDTO item : customsInventoryItemDTOList) {
            String extraJson = item.getExtraJson();
            CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
            String firstUnit = customsInventoryItemExtra.getFirstUnit();
            String secondUnit = customsInventoryItemExtra.getSecondUnit();
            if (Objects.equals(firstUnit, "035") && Objects.nonNull(customsInventoryItemExtra.getFirstUnitAmount())) {
                totalWeight = totalWeight.add(customsInventoryItemExtra.getFirstUnitAmount().multiply(BigDecimal.valueOf(item.getCount())));
            }
            if (Objects.equals(secondUnit, "035") && Objects.nonNull(customsInventoryItemExtra.getSecondUnitAmount())) {
                totalWeight = totalWeight.add(customsInventoryItemExtra.getSecondUnitAmount().multiply(BigDecimal.valueOf(item.getCount())));
            }
        }
        if (grossWeight.compareTo(totalWeight) < 0) {
            log.error("清单申报 申报单号:{} 总毛重:{} 表体总重量:{}", customsInventoryDTO.getDeclareOrderNo(), grossWeight, totalWeight);
            throw new RuntimeException("毛重小于商品项重量之和，总毛重:" + grossWeight + ",表体总重量:" + totalWeight);
        }
    }

    /**
     * 向芥舟数字清关平台传输清单报文，ccs不进行申报
     *
     * @param customsInventoryDTO
     * @param assureCompany
     * @param areaCompany
     * @param logisCompany
     * @param agentCompany
     * @param routeDTO
     * @param logisticsDeclareCompany
     * @param isDeclareLogisticsInSystem
     */
    private void declareByJieztech(CustomsInventoryDTO customsInventoryDTO, CompanyDTO assureCompany, CompanyDTO areaCompany, CompanyDTO logisCompany, CompanyResVo agentCompany, RouteDTO routeDTO, CompanyDTO logisticsDeclareCompany, boolean isDeclareLogisticsInSystem) {
        String appId;
        String privateKey;
        if (EnvironmentConfig.isOnline()) {
            appId = jieztechAppDeclareId;
            privateKey = jieztechDeclarePrivateKey;
        } else {
            appId = jieztechAppId;
            privateKey = jieztechPrivateKey;
        }
        JieztechDeclareInventoryReq req = buildJiezTechDeclareInventoryReq(customsInventoryDTO, assureCompany, areaCompany,
                logisCompany, agentCompany, logisticsDeclareCompany, isDeclareLogisticsInSystem);
        req.setAppid(appId);
        if (haikouRoute.equals(routeDTO.getCode())) {
            req.setPortCode(haikouCode);
        }
        if (kunmingRoute.equals(routeDTO.getCode())) {
            req.setPortCode(kunmingCode);
        }
        if ("YWZB-TbGlobalBuy".equals(routeDTO.getCode())) {
            req.setPortCode("2925");
        }
        log.info("[op:jieztech-buildJiezTechDeclareReq]  req={}", JSON.toJSONString(req));
        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = null;
        try {
            requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(privateKey), JSON.toJSONString(req));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException |
                NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException |
                UnsupportedEncodingException e) {
            log.error("处理异常：{}", e.getMessage(), e);
        }
        HttpRequest httpRequest = HttpRequestUtil.post(jieztechHost + "/api/aoa/logistics/logisticsOrderorderCallBack", requestJson);
        if (httpRequest.ok()) {
            log.info("[op:jieztech-inventoryDeclare]  succ, res={}", httpRequest.body());
        } else {
            log.info("[op:jieztech-inventoryDeclare]  fail, res={}", httpRequest.body());
        }
    }

    /**
     * 根据电商平台进行区分，向芥舟数字清关平台传输清单报文，ccs不进行申报
     *
     * @param routeDTO
     * @return
     */
    private boolean checkWeatherDeclareByJieztech(RouteDTO routeDTO) {
        return "TbGlobalBuy".equals(routeDTO.getCode())
                || haikouRoute.equals(routeDTO.getCode())
                || kunmingRoute.equals(routeDTO.getCode())
                || (!ArrayUtils.isEmpty(taoBaoGlobalFx) && ArrayUtils.contains(taoBaoGlobalFx, routeDTO.getCode()));
    }

    private boolean checkWeatherForwardJieztech(CustomsInventoryExtra inventoryExtra, RouteDTO routeDTO) {
        return weatherForwardJieztech && EnvironmentConfig.isOnline() && "T2924W000088".equals(inventoryExtra.getCustomsBookNo()) && !"TbGlobalBuy".equals(routeDTO.getCode());
    }

    /**
     * 转发jiez申报
     *
     * @param customsInventoryDTO
     * @param assureCompany
     * @param areaCompany
     * @param logisCompany
     * @param ebpCompany
     * @param ebcCompany
     * @param agentCompany
     * @param logisticsDeclareCompany
     * @param isDeclareLogisticsInSystem
     * @param payCompany
     */
    private void declareForward2Jieztech(CustomsInventoryDTO customsInventoryDTO, CompanyDTO assureCompany, CompanyDTO areaCompany, CompanyDTO logisCompany, CompanyDTO ebpCompany, CompanyDTO ebcCompany, CompanyResVo agentCompany, CompanyDTO logisticsDeclareCompany, boolean isDeclareLogisticsInSystem, CompanyDTO payCompany) {
        JieztechInventoryReq req = buildJiezTechReq(customsInventoryDTO, assureCompany,
                areaCompany, logisCompany, ebpCompany, ebcCompany, agentCompany, payCompany, logisticsDeclareCompany, isDeclareLogisticsInSystem);
        req.setAppid(jieztechAppId);
        req.setSubMerchantId(jieztechSubMerchantId);
        req.setSubMerchantName(jieztechSubMerchantName);
        req.setMerchantId(jieztechMerchantId);
        req.setMerchantName(jieztechMerchantName);
        log.info("[op:jieztech-buildJiezTechReq]  req={}", JSON.toJSONString(req));
        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = null;
        try {
            requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(jieztechPrivateKey), JSON.toJSONString(req));
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException | SignatureException |
                NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException |
                UnsupportedEncodingException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        HttpRequest httpRequest = HttpRequestUtil.post(jieztechHost + "/api/aoa/publicServicePlatformFacade/inventory", requestJson);
        if (httpRequest.ok()) {
            log.info("[op:jieztech-inventory]  succ, res={}", httpRequest.body());
        } else {
            log.info("[op:jieztech-inventory]  fail, res={}", httpRequest.body());
        }
    }

    /**
     * 更新order表内部流转状态与申报记录
     *
     * @param info
     * @param orderDTO
     */
    public RouteDeclareConfig updateOrderInternalStatusAndDeclareRecord(WrapBeanInfo info, OrderDTO orderDTO) {
//        RouteDeclareConfig inventoryConfig = CustomsDeclareUtils.buildDeclareRecord(info, orderDTO, DeclareEnum.INVENTORY);
//        CustomsDeclareUtils.buildDeclareRecord(info, orderDTO, DeclareEnum.INVENTORY_CANCEL);
//        CustomsDeclareUtils.buildDeclareRecord(info, orderDTO, DeclareEnum.INVENTORY_REFUND);
        RouteDeclareConfig inventoryConfig = CustomsDeclareUtils.updateInventoryDeclareRecord(info, orderDTO, info.getDeclareCompanyCebCode());
        orderService.updateOrderInternalStatusAndDeclareRecord(orderDTO, OrderInternalEnum.DECLARING.getCode());
        return inventoryConfig;
    }

    private List<CustomsInventoryItemDTO> updateCustomsInventoryItem(CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDeclareDTO> customsInventoryItemDeclareDTOList) {
        List<OrderInfoDto> orderGoodsInfoList = StockContextUtil.getAllOrderGoodsInfo();
        if (CollectionUtils.isEmpty(orderGoodsInfoList)) {
            // FIXME: 2022/7/26 重推不会有数据 以第一次的为准
            log.info("清单申报 申报单号:{} 未获取到申报商品信息", customsInventoryDTO.getDeclareOrderNo());
            return customsInventoryItemDeclareDTOList.stream().map(CustomsInventoryItemDeclareDTO::getCustomsInventoryItemDTO).collect(Collectors.toList());
        }
        Map<Long, Long> itemIdMap = orderGoodsInfoList.stream().collect(Collectors.toMap(OrderInfoDto::getInitCustomsBookItemId, OrderInfoDto::getDeclareCustomsBookItemId));
        List<CustomsInventoryItemDTO> declareList = new ArrayList<>();
        customsInventoryItemDeclareDTOList.forEach(c -> {
            CustomsInventoryItemDTO item = c.getCustomsInventoryItemDTO();
            Long bookItemId = item.getBookItemId();
            if (itemIdMap.containsKey(bookItemId)) {
                Long declareItemId = itemIdMap.get(bookItemId);
                //如果最初申报的和经过对比的品相同直接返回
                if (Objects.equals(bookItemId, declareItemId)) {
                    declareList.add(item);
                    return;
                }
                List<CustomsBookItemDTO> itemSwitchBackUpList = c.getItemSwitchBackUpList();
                CustomsBookItemDTO customsBookItemDTO = itemSwitchBackUpList.stream().filter(i -> Objects.equals(i.getId(), declareItemId)).findFirst().orElse(null);
                if (Objects.nonNull(customsBookItemDTO)) {
                    String extraJson = item.getExtraJson();
                    item.setBookItemId(declareItemId);
                    item.setItemName(customsBookItemDTO.getGoodsName());
                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
                    BeanUtils.copyProperties(customsBookItemDTO, itemExtra);
                    itemExtra.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                    itemExtra.setProductId(customsBookItemDTO.getProductId());
                    itemExtra.setHsCode(customsBookItemDTO.getHsCode());
                    itemExtra.setGoodsName(customsBookItemDTO.getGoodsName());
                    itemExtra.setCurrCode(customsBookItemDTO.getCurrCode());
                    itemExtra.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                    itemExtra.setGoodsModel(customsBookItemDTO.getGoodsModel());
                    itemExtra.setOriginCountry(customsBookItemDTO.getOriginCountry());
                    itemExtra.setGoodsUnit(customsBookItemDTO.getGoodsUnit());
                    itemExtra.setFirstUnit(customsBookItemDTO.getFirstUnit());
                    itemExtra.setSecondUnit(customsBookItemDTO.getSecondUnit());
                    itemExtra.setFirstUnitAmount(customsBookItemDTO.getFirstUnitAmount());
                    itemExtra.setSecondUnitAmount(customsBookItemDTO.getSecondUnitAmount());
                    item.setExtraJson(JSON.toJSONString(itemExtra));
                    declareList.add(item);
                }
            }
        });
        return declareList;
    }

    /**
     * 设置路由信息
     *
     * @param inventoryOrderInfo
     * @param routeDTO
     */
    private void setRoute(WrapInventoryOrderInfo inventoryOrderInfo, RouteResVo routeDTO) {
        if (routeDTO == null || inventoryOrderInfo == null) {
            return;
        }
        RouteInfo routeInfo = new RouteInfo();
        routeInfo.setCode(routeDTO.getCode());
        routeInfo.setName(routeDTO.getName());
        routeInfo.setDeclareWay(routeDTO.getDeclareWay());
        inventoryOrderInfo.setRouteInfo(routeInfo);
    }

    /**
     * TODO:未完成
     * 构建申报模块数据对象
     *
     * @param orderDTO
     * @param customsInventoryDTO
     * @return
     */
    private WrapInventoryOrderInfo buildModel(OrderDTO orderDTO, CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> itemDTOList,
                                              CompanyDTO assureCompany, CompanyDTO areaCompany,
                                              CompanyDTO logisCompany, CompanyDTO ebpCompany,
                                              CompanyDTO ebcCompany, CompanyDTO agentCompany,
                                              CompanyDTO payCompany, CompanyDTO logisticsDeclareCompany, boolean isDeclareLogisticsInSystem,
                                              CustomsBookResVo customsBookResVo, RouteInfoResVo routeResVo) {
//        List<CustomsInventoryItemDTO> itemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        // 这里做下排序，由于有历史数据，所以子项中所有的sort都有值的时候才排序
        itemDTOList = SortUtil.inventoryItemSortByAsc(itemDTOList);
        for (CustomsInventoryItemDTO itemDTO : itemDTOList) {
            customsInventoryService.updateByItemBaseInfo(itemDTO.getId(), itemDTO.getBookItemId(), itemDTO.getItemName(), itemDTO.getExtraJson(), itemDTO.getCreateTime());
        }
        // Step::清单信息
        CustomsInventoryInfo customsInventoryInfo = new CustomsInventoryInfo();
        if (StringUtils.isNotEmpty(customsInventoryDTO.getInventoryNo())) {
            customsInventoryInfo.setInventoryNo(customsInventoryDTO.getInventoryNo());
        }
        if (StringUtils.isNotEmpty(customsInventoryDTO.getPreNo())) {
            customsInventoryInfo.setPreNo(customsInventoryDTO.getPreNo());
        }
        customsInventoryInfo.setNote(customsInventoryDTO.getNote());
        customsInventoryInfo.setStatus(customsInventoryDTO.getStatus());
        customsInventoryInfo.setNetWeight(customsInventoryDTO.getNetWeight() == null ? new BigDecimal(0) : customsInventoryDTO.getNetWeight());
        customsInventoryInfo.setGrossWeight(customsInventoryDTO.getGrossWeight() == null ? new BigDecimal(0) : customsInventoryDTO.getGrossWeight());
        customsInventoryInfo.setInsureAmount(customsInventoryDTO.getInsureAmount() == null ? new BigDecimal(0) : customsInventoryDTO.getInsureAmount());
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        customsInventoryInfo.setBookNo(inventoryExtra.getCustomsBookNo());
        customsInventoryInfo.setBookId(customsInventoryDTO.getAccountBookId());
        customsInventoryInfo.setId(customsInventoryDTO.getId());
        customsInventoryInfo.setSn(customsInventoryDTO.getSn());
        customsInventoryInfo.setOrderNo(customsInventoryDTO.getDeclareOrderNo());
        customsInventoryInfo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        customsInventoryInfo.setClientCustoms(CustomsDistrictEnum.getEnum(customsInventoryDTO.getCustoms()).getCustoms());
        customsInventoryInfo.setCustoms(customsInventoryDTO.getCustoms());
        customsInventoryInfo.setCreateTime(customsInventoryDTO.getCreateTime());
        customsInventoryInfo.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
        customsInventoryInfo.setBuyerName(customsInventoryDTO.getBuyerName());
        customsInventoryInfo.setBuyerIdType(customsInventoryDTO.getBuyerIdType());
        customsInventoryInfo.setBuyerTelNumber(customsInventoryDTO.getBuyerTelNumber());
        customsInventoryInfo.setPayTransactionId(inventoryExtra.getPayTransactionId());
        String consigneeStreet = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(inventoryExtra.getConsigneeStreet()) && !PddEncodeUtil.isPddOrder(ebpCompany.getCode(), orderDTO.getDeclareOrderNo())) {
            consigneeStreet = inventoryExtra.getConsigneeStreet() + "_";
        }
        customsInventoryInfo.setConsigneeAddress(
                inventoryExtra.getConsigneeProvince() + "_"
                        + inventoryExtra.getConsigneeCity() + "_"
                        + inventoryExtra.getConsigneeDistrict() + "_"
                        + consigneeStreet
                        + customsInventoryDTO.getConsigneeAddress());
        customsInventoryInfo.setCustomsField(CustomsDistrictEnum.getEnum(customsInventoryDTO.getCustoms()).getLoctNo());
        customsInventoryInfo.setFreight(customsInventoryDTO.getFeeAmount());
        customsInventoryInfo.setMainGName(itemDTOList.get(0).getItemName());
        customsInventoryInfo.setEbpCode(ebpCompany.getCode());
        customsInventoryInfo.setTaxFee(inventoryExtra.getTaxFee());
        customsInventoryInfo.setDiscountFee(inventoryExtra.getDiscountFee());
        // Step::商品信息
        List<CustomsInventoryItemInfo> listCustomsInventoryItemInfo = new ArrayList<>();
        for (CustomsInventoryItemDTO itemDTO : itemDTOList) {
            CustomsInventoryItemInfo itemInfo = new CustomsInventoryItemInfo();
            itemInfo.setId(itemDTO.getId());
            itemInfo.setCount(itemDTO.getCount());
            itemInfo.setUnitPrice(itemDTO.getUnitPrice());
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            if (barCodeGrayTest(routeResVo, customsInventoryDTO.getDeclareOrderNo())) {
                // 增加判断，商品备案有条形码且条形码与海关备案料号不一致时，清单填报条形码字段
                CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                if (Objects.nonNull(customsInventoryItemExtra) && !Objects.equals(itemExtra.getBarCode(), customsInventoryItemExtra.getProductId())) {
                    List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(customsInventoryItemExtra.getUnifiedProductId());
                    if (Objects.nonNull(goodsRecordDTOList) && goodsRecordDTOList.size() > 1) {
                        log.info("清单申报上游下发的统一料号{} 存在多条记录，BARCODE赋值无", customsInventoryItemExtra.getUnifiedProductId());
                        itemInfo.setBarCode("无");
                    } else {
                        List<Integer> goodsRecordTagList = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordDTOList.get(0).getGoodsRecordTag());
                        if (goodsRecordTagList.contains(GoodsRecordTagEnums.GTIN_NO_RULE.getCode())) {
                            itemInfo.setBarCode("无");
                        } else {
                            itemInfo.setBarCode(itemExtra.getBarCode());
                        }
                    }
                } else {
                    log.info("清单申报 条形码与料号一致 申报单号={} 条码={}", customsInventoryDTO.getDeclareOrderNo(), itemExtra.getBarCode());
                }
            }
            itemInfo.setItemNo(itemExtra.getProductId());
            itemInfo.setItemName(itemExtra.getGoodsName());
            itemInfo.setItemRecordNo(itemExtra.getGoodsSeqNo());
            itemInfo.setGmodle(itemExtra.getGoodsModel());
            itemInfo.setHsCode(itemExtra.getHsCode());
            itemInfo.setCountry(itemExtra.getOriginCountry());
            itemInfo.setUnit(itemExtra.getGoodsUnit());
            itemInfo.setUnit1(itemExtra.getFirstUnit());
            itemInfo.setFirstCount(itemExtra.getFirstUnitAmount().toString());
            if (!StringUtils.isEmpty(itemExtra.getSecondUnit()) && itemExtra.getSecondUnitAmount() != null) {
                itemInfo.setUnit2(itemExtra.getSecondUnit());
                itemInfo.setSecondCount(itemExtra.getSecondUnitAmount().toString());
            }
            Float weight = Optional.ofNullable(itemExtra.getGrossWeight()).map(BigDecimal::floatValue).orElse(null);
            itemInfo.setWeight(weight);
            //天津
            if (!StringUtils.isEmpty(itemExtra.getGoodsRegNo())) {
                itemInfo.setGoodsRegNo(itemExtra.getGoodsRegNo());
            }
            if (!StringUtils.isEmpty(itemExtra.getDeclIINo())) {
                itemInfo.setDeclIINo(itemExtra.getDeclIINo());
            }
            if (!StringUtils.isEmpty(itemExtra.getIoGoodsSerialNo())) {
                itemInfo.setIoGoodsSerialNo(itemExtra.getIoGoodsSerialNo());
            }
            if (!StringUtils.isEmpty(itemExtra.getOriginCiqCountry())) {
                itemInfo.setOriginCountryCode(itemExtra.getOriginCiqCountry());
            }
            listCustomsInventoryItemInfo.add(itemInfo);
        }

        // 账册信息
        AccountBookDto accountBookDto = new AccountBookDto();
        BeanUtils.copyProperties(customsBookResVo, accountBookDto);
        // 路由信息
        RouteInfo routeInfo = BuildInfoUtil.buildRouteInfo(routeResVo);
        //手动替换一下areaCompanyDTO进行特殊处理
        CompanyDTO newAreaCompany = ConvertUtil.beanConvert(areaCompany, CompanyDTO.class);
        if (Objects.equals(newAreaCompany.getCode(), "330668900A")) {
            newAreaCompany.setCode("330666900Z");
        }
        WrapInventoryOrderInfo model = new WrapInventoryOrderInfo(
                this.buildDeclareInventoryCompanyInfo(assureCompany),
                customsInventoryInfo,
                listCustomsInventoryItemInfo,
                this.buildDeclareInventoryCompanyInfo(areaCompany),
                this.buildDeclareInventoryCompanyInfo(logisCompany),
                this.buildDeclareInventoryCompanyInfo(logisticsDeclareCompany),
                isDeclareLogisticsInSystem,
                this.buildDeclareInventoryCompanyInfo(ebpCompany),
                this.buildDeclareInventoryCompanyInfo(ebcCompany),
                this.buildDeclareInventoryCompanyInfo(agentCompany),
                this.buildDeclareInventoryCompanyInfo(payCompany),
                accountBookDto, routeInfo);
        // 字节订购人、收件人修改信息明文替换
        if (Objects.nonNull(orderDTO)) {
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            if (Objects.nonNull(orderExtra.getSubmit().getSensitivePlainText())) {
                OrderSensitivePlainText sensitivePlainText = orderExtra.getSubmit().getSensitivePlainText();
                model.setPlainText(sensitivePlainText);
            }
        }

        if (customsInventoryDTO.getType() != null) {
            model.setType(customsInventoryDTO.getType());
        }
        // 设置下申报单主表ID/SN
        model.setMainOrderId(orderDTO.getId());
        model.setMainOrderSn(orderDTO.getSn());
        OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
        Integer channel = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getChannel).orElse(null);
        model.setMainChannel(channel);
        String dxpId = RouteDxpUtils.getConfigDxpIdByType(model);
        model.setDxpId(dxpId);
        return model;
    }

    @Autowired
    private RedisTemplate redisTemplate;

    private static final String INVENTORY_BARCODE_CONTROL = "ccs:inventory:barCode_control";

    /**
     * {@link com.danding.cds.v2.bean.vo.req.BarCodeDeclareReqVo}
     */
    @Value("${inventory.gray.barCode.config:}")
    private String barCodeConfig;

    /**
     * 条码的灰度判断 通过路径去判断
     *
     * @param routeResVo
     * @return
     */
    private boolean barCodeGrayTest(RouteInfoResVo routeResVo, String declareNo) {
        log.info("barCodeGrayTest route={} declareNo={} 控制参数={}", routeResVo.getCode(), declareNo, JSON.toJSONString(barCodeConfig));
        if (Objects.isNull(routeResVo)) {
            return false;
        }
        List<BarCodeDeclareReqVo> declareReqVos = JSON.parseArray(barCodeConfig, BarCodeDeclareReqVo.class);
        RouteExtra routeExtra = JSON.parseObject(routeResVo.getExtraJson(), RouteExtra.class);
        if (!CollectionUtils.isEmpty(declareReqVos) && Objects.nonNull(routeExtra)) {
            BarCodeDeclareReqVo barCodeDeclareReqVo = declareReqVos.stream().filter(s -> Objects.equals(s.getCustomsBookId(), routeExtra.getCustomsBookId())).findFirst().orElse(null);
            if (Objects.isNull(barCodeDeclareReqVo)) {
                return false;
            }
            if (Objects.equals("FULL", barCodeDeclareReqVo.getDeclareMode())) {
                log.info("barCodeGrayTest declareNo={} 控制参数={} 全量走条形码模式 ", declareNo, JSON.toJSONString(barCodeDeclareReqVo));
                return true;
            } else if (Objects.equals("COUNT", barCodeDeclareReqVo.getDeclareMode())) {
                List values = redisTemplate.opsForHash().values(INVENTORY_BARCODE_CONTROL + "_" + routeExtra.getCustomsBookId());
                if (values.size() <= barCodeDeclareReqVo.getCount()) {
                    redisTemplate.opsForHash().put(INVENTORY_BARCODE_CONTROL + "_" + routeExtra.getCustomsBookId(), declareNo, declareNo);
                    log.info("barCodeGrayTest declareNo={} 控制参数={} 走条形码灰度模式 存入redis", declareNo, JSON.toJSONString(barCodeDeclareReqVo));
                    return true;
                } else if (redisTemplate.opsForHash().hasKey(INVENTORY_BARCODE_CONTROL + "_" + routeExtra.getCustomsBookId(), declareNo)) {
                    log.info("barCodeGrayTest declareNo={} 控制参数={} 走条形码灰度模式 已在redis中", declareNo, JSON.toJSONString(barCodeDeclareReqVo));
                    return true;
                } else {
                    log.info("barCodeGrayTest declareNo={} 控制参数={} 不走条形码灰度模式次数已满={}", declareNo, JSON.toJSONString(barCodeDeclareReqVo), barCodeDeclareReqVo.getCount());
                }
            } else {
                log.info("barCodeGrayTest declareNo={} route={} customsBookId={} 不走条形码灰度模式", declareNo, routeResVo.getCode(), routeExtra.getCustomsBookId());
            }
        }
        return false;
    }

    /**
     * 构建用于c单申报的企业信息
     *
     * @param companyDTO
     * @return companyInfo
     */
    private CompanyInfo buildDeclareInventoryCompanyInfo(CompanyDTO companyDTO) {
        if (companyDTO == null) {
            return null;
        }
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getCode());
        ebpInfo.setCode(companyDTO.getCode());
        ebpInfo.setCebName(companyDTO.getName());
        ebpInfo.setName(companyDTO.getName());
        if (StringUtils.isNotEmpty(companyDTO.getExtraJson())) {
            JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
            String dxpId = (String) agentExtra.getOrDefault("dxpId", "");
            if (!StringUtils.isEmpty(dxpId)) {
                ebpInfo.setDxpId(dxpId);
            }
        }

        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }
        return ebpInfo;
    }

    private CompanyInfo buildCompany(CompanyDTO companyDTO, String customs) {
        if (companyDTO == null) {
            return null;
        }
        CompanyInfo ebpInfo = new CompanyInfo();
        ebpInfo.setCebCode(companyDTO.getCode());
        ebpInfo.setCebName(companyDTO.getName());
        CompanyDistrictDTO ebpDis = companyDTO.getDistrict(CustomsDistrictEnum.getEnum(customs));
        if (ebpDis == null) {
            ebpInfo.setCode(companyDTO.getCode());
            ebpInfo.setName(companyDTO.getName());
        } else {
            ebpInfo.setCode(ebpDis.getCode());
            ebpInfo.setName(ebpDis.getName());
        }
        if (StringUtils.isNotEmpty(companyDTO.getExtraJson())) {
            JSONObject agentExtra = JSON.parseObject(companyDTO.getExtraJson());
            String dxpId = (String) agentExtra.getOrDefault("dxpId", "");
            if (!StringUtils.isEmpty(dxpId)) {
                ebpInfo.setDxpId(dxpId);
            }
        }

        // 这里判断下企业申报配置拓展字段
        if (companyDTO instanceof CompanyResVo) {
            CompanyResVo companyResVo = (CompanyResVo) companyDTO;
            List<CompanyDeclareConfigDto> declareConfigDtoList = BuildInfoUtil.getCompanyDeclareConfigDto(companyResVo.getDeclareConfigResList());
            if (!CollectionUtils.isEmpty(declareConfigDtoList)) {
                ebpInfo.setDeclareConfigList(declareConfigDtoList);
            }
        }

        return ebpInfo;
    }

    private JieztechInventoryReq buildJiezTechReq(CustomsInventoryDTO customsInventoryDTO,
                                                  CompanyDTO assureCompany, CompanyDTO areaCompany,
                                                  CompanyDTO logisCompany, CompanyDTO ebpCompany,
                                                  CompanyDTO ebcCompany, CompanyDTO agentCompany,
                                                  CompanyDTO payCompany, CompanyDTO logisticsDeclareCompany,
                                                  boolean isDeclareLogisticsInSystem) {
        List<CustomsInventoryItemDTO> itemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        JieztechInventoryReq req = new JieztechInventoryReq();
        List<JieztechInventoryHead> inventoryHeadList = new ArrayList<>();
        JieztechInventoryHead inventoryHead = new JieztechInventoryHead();
        inventoryHead.setCommitTime(DateUtil.dateToString(new Date(System.currentTimeMillis()), DateUtil.DATE_DEF_PATTERN));
        inventoryHead.setOrderNo(customsInventoryDTO.getDeclareOrderNo());
        inventoryHead.setEbpCode(ebpCompany.getCode());
        inventoryHead.setEbpName(ebpCompany.getName());
        inventoryHead.setEbcCode(ebcCompany.getCode());
        inventoryHead.setEbcName(ebcCompany.getName());
        inventoryHead.setPayCode(payCompany.getCode());
        inventoryHead.setPayName(payCompany.getName());
        inventoryHead.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        if (isDeclareLogisticsInSystem) {
            inventoryHead.setLogisticsCode(logisticsDeclareCompany.getCode());
            inventoryHead.setLogisticsName(logisticsDeclareCompany.getName());
        } else {
            inventoryHead.setLogisticsCode(logisCompany.getCode());
            inventoryHead.setLogisticsName(logisCompany.getName());
        }
        inventoryHead.setCopNo(customsInventoryDTO.getSn());
        inventoryHead.setAssureCode(assureCompany.getCode());
        inventoryHead.setEmsNo("T2924W0000088");
        inventoryHead.setDeclTime(DateUtil.getCDateString("yyyyMMdd"));
        inventoryHead.setCustomsCode("2924");
        inventoryHead.setPortCode("2900");
        inventoryHead.setIeDate(DateUtil.getTCurrentDate("yyyyMMdd"));
        inventoryHead.setBuyerIdNumber(customsInventoryDTO.getBuyerIdNumber());
        inventoryHead.setBuyerName(customsInventoryDTO.getBuyerName());
        inventoryHead.setBuyerTelephone(customsInventoryDTO.getBuyerTelNumber());
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        inventoryHead.setConsignee(customsInventoryDTO.getBuyerName());
        inventoryHead.setConsigneeTelephone(customsInventoryDTO.getBuyerTelNumber());
        inventoryHead.setConsigneePrvince(inventoryExtra.getConsigneeProvince());
        inventoryHead.setConsigneeCity(inventoryExtra.getConsigneeCity());
        inventoryHead.setConsigneeCounty(inventoryExtra.getConsigneeDistrict());
        String consigneeStreet = "";
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(inventoryExtra.getConsigneeStreet())
                && !PddEncodeUtil.isPddOrder(ebpCompany.getCode(), customsInventoryDTO.getDeclareOrderNo())) {
            consigneeStreet = inventoryExtra.getConsigneeStreet();
        }
        inventoryHead.setConsigneeAddress(inventoryExtra.getConsigneeProvince() + "_"
                + inventoryExtra.getConsigneeCity() + "_"
                + inventoryExtra.getConsigneeDistrict() + "_"
                + consigneeStreet
                + customsInventoryDTO.getConsigneeAddress());
        inventoryHead.setAgentCode(agentCompany.getCode());
        inventoryHead.setAgentName(agentCompany.getName());
        inventoryHead.setAreaCode(areaCompany.getCode());
        inventoryHead.setAreaName(areaCompany.getName());
        inventoryHead.setLoctNo("292401");
        inventoryHead.setDiscount(inventoryExtra.getDiscountFee());
        inventoryHead.setTaxTotal(inventoryExtra.getTaxFee());
        inventoryHead.setFreight(customsInventoryDTO.getFeeAmount());
        inventoryHead.setInsuredFee(new BigDecimal("0"));
        inventoryHead.setPayTransactionId(inventoryExtra.getPayTransactionId());
        List<JieztechInventoryItem> inventoryList = new ArrayList<>();
        int num = 1;
        BigDecimal grossWeight = new BigDecimal("0");
        BigDecimal netWeight = new BigDecimal("0");
        BigDecimal actualPaid = new BigDecimal("0");
        BigDecimal goodsValue = new BigDecimal("0");
        for (CustomsInventoryItemDTO itemDTO : itemDTOList) {
            JieztechInventoryItem inventoryItem = new JieztechInventoryItem();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            inventoryItem.setGnum(num + "");
            inventoryItem.setItemRecordNo(itemExtra.getGoodsSeqNo());
            inventoryItem.setItemNo(itemDTO.getItemNo());
            inventoryItem.setItemName(itemDTO.getItemName());
            inventoryItem.setGcode(itemExtra.getHsCode());
            inventoryItem.setGname(itemDTO.getItemName());
            inventoryItem.setGmodel(itemExtra.getGoodsModel());
            inventoryItem.setCountry(itemExtra.getOriginCountry());
            inventoryItem.setCurrency("142");
            inventoryItem.setQty(itemDTO.getCount().toString());
            inventoryItem.setQty1(itemExtra.getFirstUnitAmount().toString());
            inventoryItem.setUnit(itemExtra.getGoodsUnit());
            inventoryItem.setUnit1(itemExtra.getFirstUnit());
            if (StringUtils.isNotEmpty(itemExtra.getSecondUnit()) && itemExtra.getSecondUnitAmount() != null) {
                inventoryItem.setQty2(itemExtra.getSecondUnitAmount().toString());
                inventoryItem.setUnit2(itemExtra.getSecondUnit());
            }
            BigDecimal totalPrice = new BigDecimal(itemDTO.getCount()).multiply(itemDTO.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryItem.setPrice(itemDTO.getUnitPrice().toString());
            inventoryItem.setTotalPrice(totalPrice.toString());
            num++;
            inventoryList.add(inventoryItem);
            if (null != itemExtra.getNetWeight()) {
                netWeight = netWeight.add(new BigDecimal(itemDTO.getCount()).multiply(itemExtra.getNetWeight()));
            }
            if (null != itemExtra.getGrossWeight()) {
                grossWeight = grossWeight.add(new BigDecimal(itemDTO.getCount()).multiply(itemExtra.getGrossWeight()));
            }
            goodsValue = goodsValue.add(totalPrice);
        }
        inventoryHead.setNetWeight(netWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        inventoryHead.setGrossWeight(grossWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        actualPaid = actualPaid.add(goodsValue).add(inventoryExtra.getTaxFee()).add(customsInventoryDTO.getFeeAmount()).subtract(inventoryExtra.getDiscountFee());
        inventoryHead.setActuralPaid(actualPaid);
        inventoryHead.setGoodsValue(goodsValue);
        inventoryHead.setList(inventoryList);
        inventoryHeadList.add(inventoryHead);
        req.setInventoryHeadList(inventoryHeadList);
        return req;
    }

    private JieztechDeclareInventoryReq buildJiezTechDeclareInventoryReq(CustomsInventoryDTO customsInventoryDTO,
                                                                         CompanyDTO assureCompany, CompanyDTO areaCompany,
                                                                         CompanyDTO logisCompany, CompanyDTO agentCompany,
                                                                         CompanyDTO logisticsDeclareCompany, boolean isDeclareLogisticsInSystem) {
        List<CustomsInventoryItemDTO> itemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        CustomsInventoryExtra inventoryExtra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
        JieztechDeclareInventoryReq req = new JieztechDeclareInventoryReq();
        req.setEmsNo(inventoryExtra.getCustomsBookNo());
        req.setOrderNo(customsInventoryDTO.getDeclareOrderNo());
        req.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        if (isDeclareLogisticsInSystem) {
            req.setLogisticsCode(logisticsDeclareCompany.getCode());
            req.setLogisticsName(logisticsDeclareCompany.getName());
        } else {
            req.setLogisticsCode(logisCompany.getCode());
            req.setLogisticsName(logisCompany.getName());
        }
        req.setPortCode("2924");
        req.setCopNo(customsInventoryDTO.getSn());
        req.setAssureCode(assureCompany.getCode());
        req.setAgentCode(agentCompany.getCode());
        req.setAgentName(agentCompany.getName());
        req.setAreaCode(areaCompany.getCode());
        req.setAreaName(areaCompany.getName());
        req.setTrafMode("7");
        req.setInsuredFee("0");
        int num = 1;
        BigDecimal grossWeight = new BigDecimal("0");
        BigDecimal netWeight = new BigDecimal("0");
        List<JieztechDeclareInventoryItem> inventoryList = new ArrayList<>();
        for (CustomsInventoryItemDTO itemDTO : itemDTOList) {
            JieztechDeclareInventoryItem inventoryItem = new JieztechDeclareInventoryItem();
            CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
            inventoryItem.setGnum(num + "");
            /**
             * 芥舟的itemSku取的商品备案的sku
             */
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(customsInventoryDTO.getAccountBookId(), itemExtra.getProductId());
            if (goodsRecordDTO == null) {
                throw new BusinessException(-1, "根据料号：" + itemExtra.getProductId() + "，未找到备案数据");
            }
            inventoryItem.setItemSku(goodsRecordDTO.getSkuId());
            inventoryItem.setItemRecordNo(itemExtra.getGoodsSeqNo());
            inventoryItem.setItemNo(itemExtra.getProductId());
            inventoryItem.setGcode(itemExtra.getHsCode());
            inventoryItem.setGname(itemDTO.getItemName());
            inventoryItem.setGmodel(itemExtra.getGoodsModel());
            inventoryItem.setBarCode("无");
            inventoryItem.setCountry(itemExtra.getOriginCountry());
            inventoryItem.setQty(itemDTO.getCount().toString());
            inventoryItem.setQty1(itemExtra.getFirstUnitAmount().multiply(new BigDecimal(itemDTO.getCount())).toString());
            inventoryItem.setUnit(itemExtra.getGoodsUnit());
            inventoryItem.setUnit1(itemExtra.getFirstUnit());
            if (StringUtils.isNotEmpty(itemExtra.getSecondUnit()) && itemExtra.getSecondUnitAmount() != null) {
                inventoryItem.setQty2(itemExtra.getSecondUnitAmount().multiply(new BigDecimal(itemDTO.getCount())).toString());
                inventoryItem.setUnit2(itemExtra.getSecondUnit());
            }
            BigDecimal totalPrice = new BigDecimal(itemDTO.getCount()).multiply(itemDTO.getUnitPrice()).setScale(2, BigDecimal.ROUND_HALF_UP);
            inventoryItem.setPrice(itemDTO.getUnitPrice().toString());
            inventoryItem.setTotalPrice(totalPrice.toString());
            inventoryItem.setHsCode(itemExtra.getHsCode());
            num++;
            inventoryList.add(inventoryItem);
            if (null != itemExtra.getNetWeight()) {
                netWeight = netWeight.add(new BigDecimal(itemDTO.getCount()).multiply(itemExtra.getNetWeight()));
            }
            if (null != itemExtra.getGrossWeight()) {
                grossWeight = grossWeight.add(new BigDecimal(itemDTO.getCount()).multiply(itemExtra.getGrossWeight()));
            }
        }
        req.setInventoryList(inventoryList);
        req.setNetWeight(netWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        req.setGrossWeight(grossWeight.setScale(4, BigDecimal.ROUND_HALF_UP).toString());
        return req;
    }


    public boolean judgeIsLegalAndReplace(CustomsInventoryDTO customsInventoryDTO, OrderDTO orderDTO, CompanyDTO ebpCompany) {
        if (StringUtils.isEmpty(filterSpecialCharConfig)) {
            log.info("judgeIsLegalAndReplace 没有配置参数");
            return true;
        }
        FilterSpecialConfigDTO orderDeclareDTO = JSON.parseObject(filterSpecialCharConfig, FilterSpecialConfigDTO.class);
        if (PddEncodeUtil.isPddOrder(ebpCompany.getCode(), orderDTO.getDeclareOrderNo())) {
            if (orderDeclareDTO.getNotFilterPlatformCodeList().contains("PDD")) {
                log.info("judgeIsLegalAndReplace设置PDD不参与过滤");
                return true;
            }
        }
        log.debug("CustomsInventoryHandler judgeIsLegalAndReplace过滤收件人地址前{}", customsInventoryDTO.getConsigneeAddress());
        String consigneeAddress = FiltrerSpecialAndEmojUtil.filterEmojis(customsInventoryDTO.getConsigneeAddress());
        log.debug("CustomsInventoryHandler judgeIsLegalAndReplace过滤收件人地址后{}", consigneeAddress);
        if (StringUtils.isEmpty(consigneeAddress)) {
            StringBuilder builder = new StringBuilder();
            builder.append("<font color=\\\"warning\\\">**" + "【订单异常】收件人或详细地址是纯特殊字符，无法过滤" + "**</font>\\n\n");
            builder.append(String.format("申报单号：%s", orderDTO.getDeclareOrderNo()) + "\r\n");
            String content = builder.toString();
            WechatNotifyUtils.wechatNotifyMd(orderDeclareDTO.getWebHook(), orderDeclareDTO.getPhoneList(), content);
            return false;
        }
        customsInventoryDTO.setConsigneeAddress(consigneeAddress);
        return true;
    }
}
