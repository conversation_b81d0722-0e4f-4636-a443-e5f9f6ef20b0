package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderInfoDO;
import com.danding.cds.invenorder.impl.entity.InventoryOrderLogDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface InventoryOrderLogMapper extends Mapper<InventoryOrderLogDO>, InsertListMapper<InventoryOrderLogDO>, BatchUpdateMapper<InventoryOrderLogDO>, AggregationPlusMapper<InventoryOrderLogDO> {
}