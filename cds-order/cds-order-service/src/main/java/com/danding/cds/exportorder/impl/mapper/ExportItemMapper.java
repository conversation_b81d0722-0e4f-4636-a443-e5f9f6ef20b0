package com.danding.cds.exportorder.impl.mapper;

import com.danding.cds.exportorder.impl.entity.ExportItemDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchInsertMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

public interface ExportItemMapper extends Mapper<ExportItemDO>, BatchInsertMapper<ExportItemDO>, InsertListMapper<ExportItemDO>, BatchUpdateMapper<ExportItemDO>, AggregationPlusMapper<ExportItemDO> {
}