package com.danding.cds.endorsement.impl.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 核注单表体Extra对象
 * @date 2024/4/28 09:25
 */
@Data
public class EndorsementExtraDO {
    //申报单位
    private String declareUnit;
    //单价
    private BigDecimal declarePrice;
    // 总价
    private BigDecimal declareTotalPrice;
    // 币制
    private String currency;
    // 法一单位
    private String firstUnit;
    // 法一单位数量
    private BigDecimal firstUnitQfy;
    // 法二单位
    private String secondUnit;
    // 法二单位数量
    private BigDecimal secondUnitQfy;
    // 规格型号
    private String goodsModel;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 最终目的国（地区）
     */
    private String destinationCountry;
    /**
     * 危化品标志
     */
    private String dangerousFlag;
    /**
     * 征免方式
     */
    private String avoidTaxMethod;
    /**
     * 单号版本号
     */
    private String version;
    /**
     * 备注
     */
    private String remark;
}
