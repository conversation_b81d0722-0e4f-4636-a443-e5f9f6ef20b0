package com.danding.cds.endorsement.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IDistributionOrderRpcFacade;
import com.danding.business.client.rpc.goods.facade.IReadyOrderRpcFacade;
import com.danding.cds.aspects.handler.StockEndorsementDeleteHandle;
import com.danding.cds.aspects.handler.StockEndorsementHandler;
import com.danding.cds.aspects.handler.StockEndorsementInventoryHandler;
import com.danding.cds.aspects.handler.StockExportHandler;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.collaborateorder.api.enums.CollaborateStatus;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderDetailsService;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.enums.*;
import com.danding.cds.common.exception.ArgsErrorRpcException;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.config.OrderBaseConfig;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.cull.api.service.CullOrderService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.config.CustomsSpecialToken;
import com.danding.cds.declare.sdk.executor.CustomsIeExecutor;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.InvtHeadType;
import com.danding.cds.endorsement.api.dto.*;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementCustomsStatus;
import com.danding.cds.endorsement.api.enums.EndorsementModfMarkEnums;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.endorsement.api.service.EndorsementTrackLogService;
import com.danding.cds.endorsement.impl.builder.Inv101MessageRequestBuilder;
import com.danding.cds.endorsement.impl.entity.*;
import com.danding.cds.endorsement.impl.mapper.EndorsementItemMapper;
import com.danding.cds.endorsement.impl.mapper.EndorsementMapper;
import com.danding.cds.endorsement.impl.mapper.EndorsementRelationMapper;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.enums.ExportOrderStatus;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.exportorder.impl.entity.ExportItemDO;
import com.danding.cds.exportorder.impl.entity.ExportOrderDO;
import com.danding.cds.exportorder.impl.mapper.ExportItemMapper;
import com.danding.cds.exportorder.impl.mapper.ExportOrderMapper;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.*;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.api.service.InventoryOrderTallyReportService;
import com.danding.cds.invenorder.impl.entity.InventoryOrderInfoDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderInfoMapper;
import com.danding.cds.invenorder.impl.mq.InventorderStatusCallbackProducer;
import com.danding.cds.inventory.api.dto.UpdateInventoryDTO;
import com.danding.cds.inventory.api.service.InventoryChangeService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.ItemRegionLogSubmit;
import com.danding.cds.item.api.service.*;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.enums.AbnormalTypeEnum;
import com.danding.cds.itemstocklist.api.enums.DataTypeNum;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.log.api.service.TrackLogEsService;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.service.OrderSnTenantService;
import com.danding.cds.stock.annotations.StockVerify;
import com.danding.cds.stock.api.service.StockInventoryService;
import com.danding.cds.v2.api.InventoryOrderOverdueService;
import com.danding.cds.v2.api.TaotianOrderService;
import com.danding.cds.v2.bean.config.ByteDanceFbNoteConfig;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.vo.req.AutoCarryOverEndorsementNotifyReqVo;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvAddParam;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvDeleteParam;
import com.danding.cds.v2.bean.vo.res.EndorsementEbInvEditResVO;
import com.danding.cds.v2.bean.vo.wms.WmsCwCallbackParam;
import com.danding.cds.v2.config.constans.TaotianClearanceOrderStatusConstants;
import com.danding.cds.v2.enums.EndorsementOrderTypeEnums;
import com.danding.cds.v2.enums.InventoryCheckOutStatusEnum;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.cds.v2.enums.InventoryOrderTodoTagEnums;
import com.danding.cds.v2.mq.producer.CeEndorsementProducer;
import com.danding.cds.v2.mq.producer.ItemStockListBatchInsertProducer;
import com.danding.cds.v2.service.*;
import com.danding.cds.v2.service.base.*;
import com.danding.cds.v2.util.InventoryOrderUtils;
import com.danding.cds.v2.util.RedissonLockUtilV2;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.cache.common.config.RedisLockUtils;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.rpc.client.BookCarryoverClient;
import com.dt.platform.wms.rpc.client.shipment.IShipmentRpcClient;
import com.dt.platform.wms.rpc.dto.TaoTianCustomsClearanceRpcDTO;
import com.dt.platform.wms.rpc.enums.ShipmentCustomsClearanceStatusEnum;
import com.dt.platform.wms.rpc.param.BookCarryoverCallbackDetail;
import com.dt.platform.wms.rpc.param.BookCarryoverCallbackParam;
import com.github.kevinsawicki.http.HttpRequest;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import com.jd.open.api.sdk.response.customsglobalAPI.CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginResponse;
import com.sansec.util.Strings;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.ibatis.session.RowBounds;
import org.assertj.core.util.Lists;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@DubboService
@RestController
//@RefreshScope
public class EndorsementServiceImpl implements EndorsementService {
    @Autowired
    private MessageSender messageSender;
    @Autowired
    private EndorsementServiceImpl endorsementService;

    @Autowired
    private EndorsementMapper endorsementMapper;

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private ExportOrderMapper exportOrderMapper;

    @Autowired
    private ExportItemMapper exportItemMapper;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsBookService customsBookService;

    @Autowired
    private EndorsementItemMapper endorsementItemMapper;

    @Resource
    private EndorsementRelationMapper endorsementRelationMapper;

    @DubboReference
    private ItemStockListService itemStockListService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;
    @Autowired
    private InventoryOrderInfoTrackLogService inventoryOrderInfoTrackLogService;
    @Autowired
    private ErpIReadyOrderRpcFacadeService erpIReadyOrderRpcFacadeService;
    @Autowired
    private ErpIDistributionOrderRpcFacadeService erpIDistributionOrderRpcFacadeService;
    @Autowired
    private InventoryOrderInfoMapper inventoryOrderInfoMapper;
    @DubboReference
    private CustomsInventoryRpc customsInventoryService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private ItemRegionLogService itemRegionLogService;

    @Autowired
    private CustomsSupport customsSupport;

    @Autowired
    private Inv101MessageRequestBuilder inv101MessageRequestBuilder;

    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @Autowired
    private InventorderStatusCallbackProducer inventorderStatusCallbackProducer;

    @DubboReference
    private InventoryOrderOverdueService inventoryOrderOverdueService;

    @DubboReference
    private RefundOrderService refundOrderService;

    @DubboReference
    private InventoryOrderTallyReportService inventoryOrderTallyReportService;

    @DubboReference
    private InventoryChangeService inventoryChangeService;

    @DubboReference
    private StockInventoryService stockInventoryService;

    @Autowired
    private EndorsementBaseService endorsementBaseService;

    @Autowired
    private EndorsementItemBaseService endorsementItemBaseService;

    @Autowired
    private EndorsementChecklistMappingBaseService endorsementChecklistMappingBaseService;

    @DubboReference
    private CollaborateOrderService collaborateOrderService;

    @DubboReference
    private CollaborateOrderDetailsService detailsService;

    @DubboReference
    private OrderService orderService;

    @DubboReference
    private CarryforwardCustomsBookService carryforwardCustomsBookService;

    @DubboReference
    private JdServProviderService jdServProviderService;

    @Autowired
    private BaseDataService baseDataService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @Autowired
    private WmsCwCallbackClientService wmsCwCallbackClientService;

    @DubboReference
    private EndorsementTrackLogService endorsementTrackLogService;

    @Autowired
    private CustomsSupport support;

    @DubboReference
    private OrderSnTenantService orderSnTenantService;

    @DubboReference
    private MessageService messageService;

    @DubboReference
    private TrackLogEsService trackLogEsService;

    @Autowired
    private InventoryOrderInfoBaseService inventoryOrderInfoBaseService;

    @DubboReference
    private TaotianOrderService taotianOrderService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @DubboReference
    private IShipmentRpcClient iShipmentRpcClient;

    @Resource
    private InventoryOrderAlarmService inventoryOrderAlarmService;

    @Resource(name = "endorsementCountTaskExecutor")
    private ThreadPoolTaskExecutor endorsementCountTaskExecutor;

    @Autowired
    private UserServiceUtil userServiceUtil;

    @DubboReference
    private CullOrderService cullOrderService;

    @Resource
    private EndorsementItemGoodsBaseService endorsementItemGoodsBaseService;

    @Autowired
    private OrderBaseConfig orderBaseConfig;

    @DubboReference
    private IDistributionOrderRpcFacade iDistributionOrderRpcFacade;
    @DubboReference
    private IReadyOrderRpcFacade iReadyOrderRpcFacade;

    /**
     * 核注单统计
     *
     * @return
     */
    @Override
    public InvBusinessCountDTO selectEndorsementBussinessCount(String areaCompanyId) {

        List<String> businessStatus = Stream.of(InventoryOrderEnum.STATUS_ENDORSEMENT.getEndorsementStatus().getCode(),
                InventoryOrderEnum.STATUS_START_STORAGING.getEndorsementStatus().getCode(),
                InventoryOrderEnum.STATUS_START_STORAGED.getEndorsementStatus().getCode(),
                InventoryOrderEnum.STATUS_SERVERING.getEndorsementStatus().getCode(),
                InventoryOrderEnum.STATUS_FAILURE.getEndorsementStatus().getCode(),
                InventoryOrderEnum.STATUS_COMPLETE.getEndorsementStatus().getCode()).distinct().collect(Collectors.toList());

        InvBusinessCountDTO countDTO = new InvBusinessCountDTO();
        //一线入境统计
        countDTO.setOnelineInCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN, businessStatus, areaCompanyId));
        //区间流转入
        countDTO.setSectionInCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN, businessStatus, areaCompanyId));
        //区内流转入
        countDTO.setSectionInnerInCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN, businessStatus, areaCompanyId));
        //区间流转出
        countDTO.setSectionOutCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT, businessStatus, areaCompanyId));
        //区内流转出
        countDTO.setSectionInnerOutCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT, businessStatus, areaCompanyId));
        //退货入区
        countDTO.setRefundInAreaCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA, businessStatus, areaCompanyId));
        //销毁
        countDTO.setDestroyCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_DESTORY, businessStatus, areaCompanyId));
        //一线退运
        countDTO.setOneLineRefund(countInvBusinessNum(InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND, businessStatus, areaCompanyId));
        //保税物流转大贸
        countDTO.setBondedToTrade(countInvBusinessNum(InventoryOrderBusinessEnum.BUSINESS_BONDED_TO_TRADE, businessStatus, areaCompanyId));
        //后续补税
        countDTO.setSubsequentTax(countInvBusinessNum(InventoryOrderBusinessEnum.BUSINESS_SUBSEQUENT_TAX, businessStatus, areaCompanyId));
        return countDTO;
    }

    private Integer countInvBusinessNum(InventoryOrderBusinessEnum bussinessOnelineIn, List<String> statusList, String areaCompanyId) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bussinessType", bussinessOnelineIn.getCode())
                .andIn("status", statusList)
                .andEqualTo("deleted", false);
        if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
            criteria.andEqualTo("declareCompanyId", areaCompanyId);
        }
        return endorsementMapper.selectCountByExample(example);
    }


    @Override
    public Map<String, CustomsCompleteCountDTO> selectEndorsementBussinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        ConcurrentMap<String, CustomsCompleteCountDTO> result = new ConcurrentHashMap<>();
        Example example = new Example(EndorsementDO.class);
        Example.Criteria unionCriteria = example.createCriteria();
        unionCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andEqualTo("status", EndorsementOrderStatus.FINISH.getCode()).andEqualTo("deleted", false);

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, unionCriteria, result);
            futureList.add(CompletableFuture.runAsync(runnable, endorsementCountTaskExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("清关完成-核注单统计组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("清关完成-核注单统计组合CompletableFuture结果异常" + e.getMessage(), e);
        }
        return result;
    }

    class Task extends TraceDataRunnable {

        private String handlerId;

        private Example.Criteria unionCriteria;

        private ConcurrentMap<String, CustomsCompleteCountDTO> result;

        public Task(String handlerId, Example.Criteria unionCriteria, ConcurrentMap<String, CustomsCompleteCountDTO> result) {
            super();
            this.handlerId = handlerId;
            this.unionCriteria = unionCriteria;
            this.result = result;
        }

        @Override
        public void proxy() {
            CustomsCompleteCountDTO countDTO = new CustomsCompleteCountDTO();
            //一线入境统计
            Example example0 = new Example(EndorsementDO.class);
            example0.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example0.and(unionCriteria);
            countDTO.setOneLineIn(endorsementMapper.selectCountByExample(example0));
            //区间流转入
            Example example1 = new Example(EndorsementDO.class);
            example1.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example1.and(unionCriteria);
            countDTO.setSectionIn(endorsementMapper.selectCountByExample(example1));
            //区内流转入
            Example example2 = new Example(EndorsementDO.class);
            example2.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example2.and(unionCriteria);
            countDTO.setSectionInnerIn(endorsementMapper.selectCountByExample(example2));

            //区间流转出
            Example example3 = new Example(EndorsementDO.class);
            example3.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(unionCriteria);
            countDTO.setSectionOut(endorsementMapper.selectCountByExample(example3));

            //区内流转出
            Example example4 = new Example(EndorsementDO.class);
            example4.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example4.and(unionCriteria);
            countDTO.setSectionInnerOut(endorsementMapper.selectCountByExample(example4));

            //退货入区
            Example example5 = new Example(EndorsementDO.class);
            example5.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example5.and(unionCriteria);
            countDTO.setRefundInArea(endorsementMapper.selectCountByExample(example5));

            //销毁
            Example example6 = new Example(EndorsementDO.class);
            example6.createCriteria().andEqualTo("bussinessType", InventoryOrderBusinessEnum.BUSSINESS_DESTORY.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example6.and(unionCriteria);
            countDTO.setDestroy(endorsementMapper.selectCountByExample(example6));

            result.put(handlerId, countDTO);
        }
    }

    @Override
    @Transactional
    public List<String> generate(EndorsementSubmit submit, Long userId) throws ArgsErrorException {
        String lockKey = "ccs:endorsement:generate:";
        if (Objects.nonNull(submit.getInventoryOrderId())) {
            lockKey = lockKey + "inventoryOrderId:" + submit.getInventoryOrderId();
        } else {
            lockKey = lockKey + "exportOrderId:" + submit.getExportOrderId();
        }
        String finalLockKey = lockKey;
        if (RedissonLockUtilV2.tryLock(finalLockKey, 3, 5)) {
            log.info("generate lockKey={}", finalLockKey);
            List<String> snList = generateCore(submit, userId);
            return snList;
        } else {
            throw new ArgsErrorRpcException("核注单生成中，请稍后重试");
        }
    }

    @Transactional
    public List<String> generateCore(EndorsementSubmit submit, Long userId) throws ArgsErrorException {
        log.info("endorsement generate submit={}", JSON.toJSONString(submit));
        List<String> endorsementSnList = new ArrayList<>();
        List<Long> endorsementIdList = new ArrayList<>();
        //非二线出区
        if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(submit.getBussinessType())) {
            InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findById(submit.getInventoryOrderId());
            log.info("核注单生成 订单信息 orderInfoDTO={}", JSON.toJSONString(orderInfoDTO));
            List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderId(submit.getInventoryOrderId());
            Integer orderTodoTag = orderInfoDTO.getOrderTodoTag();
            this.generateNotSecondOutCheck(orderInfoDTO, itemDTOS, submit.getStockChangeEnable());
            Map<Integer, List<InventoryOrderItemDTO>> endorsementItemGroup = new HashMap<>();
            int i = 0;
            //一线入境表体不能大于50个 其余区间、区内、退货入区可以

            EndorsementBussiness oneLineInBusinessType = EndorsementBussiness.BUSSINESS_ONELINE_IN;
            int index = 0;

            if (oneLineInBusinessType.getCode().equalsIgnoreCase(submit.getBussinessType())) {
                for (InventoryOrderItemDTO itemDTO : itemDTOS) {
                    // 2024.08.20 清关单与核注单一对多场景下可能会有问题，先不进行表体拆分了
//                    index = i / 50; // 表体数量限制
//                    i++;
                    List<InventoryOrderItemDTO> endorsementItemList = endorsementItemGroup.computeIfAbsent(index, k -> new ArrayList<>());
                    endorsementItemList.add(itemDTO);
                }
            } else {
                for (InventoryOrderItemDTO itemDTO : itemDTOS) {
                    List<InventoryOrderItemDTO> endorsementItemList = endorsementItemGroup.computeIfAbsent(index, k -> new ArrayList<>());
                    endorsementItemList.add(itemDTO);
                }
            }
            boolean createEndorsementOverdueFlag = false;
            for (Map.Entry<Integer, List<InventoryOrderItemDTO>> entry : endorsementItemGroup.entrySet()) {
                EndorsementDO endorsementDO = new EndorsementDO();
                endorsementDO.setExportOrderId(submit.getExportOrderId());
                endorsementDO.setIeFlag(submit.getIeFlag());
                endorsementDO.setChecklistsFlag(submit.getChecklistsFlag());
                endorsementDO.setSn(sequenceService.generateEndorsementSn());
                endorsementDO.setAccountBookId(orderInfoDTO.getBookId());
                endorsementDO.setDeclareCompanyId(orderInfoDTO.getInveCompanyId());
                endorsementDO.setStatus(EndorsementOrderStatus.INIT.getCode());
                endorsementDO.setBussinessType(submit.getBussinessType());
                if (Objects.nonNull(orderInfoDTO.getEndorsementRemark())) {
                    endorsementDO.setRemark(orderInfoDTO.getEndorsementRemark());
                }
                // 初始化清单类型和监管方式
                EndorsementBussiness businessType = EndorsementBussiness.getEnum(submit.getBussinessType());
                switch (businessType) {
                    case BUSINESS_BONDED_PROCESSING_ONELINE_IN:
                    case BUSINESS_BONDED_PROCESSING_ONELINE_OUT:
                        endorsementDO.setInvtType("0"); //清单类型：普通清单
                        endorsementDO.setSupvMode("5014"); //监管方式：区内来料加工
                        break;
                    case BUSSINESS_SECTIONINNER_IN:
                    case BUSSINESS_SECTIONINNER_OUT:
                        endorsementDO.setInvtType("6"); //清单类型：区内流转
                        endorsementDO.setSupvMode("1200"); //监管方式：保税间货物
                        break;
                    case BUSSINESS_REFUND_INAREA:
                        endorsementDO.setInvtType("8"); //清单类型：保税电商
                        endorsementDO.setSupvMode("1210"); //监管方式：保税电商
                        break;
                    case BUSSINESS_ONELINE_IN:
                        if (Objects.equals(orderInfoDTO.getTwoStepFlag(), InventoryTwoStepEnum.TWO_STEP.getCode())) {
                            endorsementDO.setInvtType("7"); //清单类型：区港联动(7)普通清单(0)
                        } else {
                            endorsementDO.setInvtType("0"); //清单类型：区港联动(7)普通清单(0)
                        }
                        endorsementDO.setSupvMode("1210"); //监管方式：保税电商
                        break;
                    case BUSSINESS_SECTION_IN:
                    case BUSSINESS_SECTION_OUT:
                        endorsementDO.setInvtType("0"); //清单类型：普通清单
                        endorsementDO.setSupvMode("1200");//监管方式：保税间货物
                        break;
                    case BUSSINESS_DESTORY:
                        endorsementDO.setInvtType("0");//清单类型：普通清单
                        endorsementDO.setSupvMode("0200");//监管方式：料件销毁
                        break;
                    case BUSINESS_INVENTORY_PROFIT:
                    case BUSINESS_RANDOM_INSPECTION_DECLARATION:
                        endorsementDO.setInvtType("0");//清单类型：普通清单
                        endorsementDO.setSupvMode("AAAA");//监管方式：库存调整
                        break;
                    case BUSINESS_ONELINE_REFUND:
                        endorsementDO.setInvtType("0");//清单类型：普通清单
                        endorsementDO.setSupvMode("4561");//监管方式：区内物流货物
                        break;
                    case BUSINESS_BONDED_ONELINE_IN:
                        endorsementDO.setInvtType("0");//清单类型：普通清单
                        endorsementDO.setSupvMode("5034");//监管方式：区内物流货物
                        break;
                    case BUSINESS_BONDED_TO_TRADE:
                        endorsementDO.setInvtType("0");//清单类型：普通清单
                        endorsementDO.setSupvMode("5000");//监管方式：料件进出区
                        break;
                    case BUSINESS_SUBSEQUENT_TAX:
                        endorsementDO.setInvtType("0"); //清单类型：普通清单
                        endorsementDO.setSupvMode("9700"); //监管方式：后续补税
                        break;
                    case BUSINESS_SIMPLE_PROCESSING:
                        endorsementDO.setInvtType("4"); // 4-简单加工
                        endorsementDO.setSupvMode("1200");
                        break;
                    default:
                        endorsementDO.setInvtType("0"); // 普通清单
                }

                if (Objects.nonNull(submit.getStockChangeEnable())) {
                    endorsementDO.setStockChangeEnable(submit.getStockChangeEnable() == 1);
                } else {
                    endorsementDO.setStockChangeEnable(true);
                }
                if (!endorsementDO.getStockChangeEnable()) {
                    //不核扣账册
                    endorsementDO.setStatus(EndorsementOrderStatus.FINISH.getCode());
                }
                endorsementDO.setInventoryOrderId(submit.getInventoryOrderId());
                endorsementDO.setOrderType(EndorsementOrderTypeEnums.INVENTORY_ORDER.getCode());
                endorsementDO.setCreateBy(userId.intValue());
                UserUtils.setCreateAndUpdateBy(endorsementDO);
                endorsementMapper.insertSelective(endorsementDO);
                endorsementSnList.add(endorsementDO.getSn());
                endorsementIdList.add(endorsementDO.getId());
//                if (bizList.contains(submit.getBussinessType())) {
//                    Map<String, BigDecimal> bookItemIdAndQty = entry.getValue().stream().collect(Collectors.toMap(InventoryOrderItemDTO::getProductId, InventoryOrderItemDTO::getDeclareUnitQfy));
//                    String result = customsBookItemService.checkAvailableNum(inventoryOrderInfoDTO.getBookId(), bookItemIdAndQty);
//                    if (!StringUtils.isEmpty(result)) {
//                        throw new ArgsErrorException(result);
//                    } else {
//                        //库存都可用 则执行更新
//                        inventoryChangeService.updateInventory(entry.getValue().stream().map(e -> buildUidto(e, inventoryOrderInfoDTO.getBookId(), endorsementDO.getSn())).collect(Collectors.toList()), customsBookItemService.createRedisToken());
//                    }生成核注单失败
//                }
                for (InventoryOrderItemDTO orderItemDTO : entry.getValue()) {
                    // 将核注清单关联更新到清关单表体里
                    orderItemDTO.setRefEndorsementId(endorsementDO.getId());
                    orderItemDTO.setRefEndorsementSn(endorsementDO.getSn());
                }
                createEndorsementOverdueFlag = inventoryOrderOverdueService.calculateEndorsementCreateTime(endorsementDO.getId(), endorsementDO.getInventoryOrderId());
                //记录核注单日志
                endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.INIT, "新建核注清单");
                if (Objects.equals(submit.getStockChangeEnable(), 0)) {
                    endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.FINISH, "核注单清关完成，是否核扣账册【否】");
                }
            }

            /**
             * 关联清关单，并修改状态
             */
            Collection<List<InventoryOrderItemDTO>> refChangedInventoryOrderItems = endorsementItemGroup.values();
            List<InventoryOrderItemDTO> _refChangedInventoryOrderItems = refChangedInventoryOrderItems.stream().map(s -> s.toArray(new InventoryOrderItemDTO[]{})).flatMap(Arrays::stream).collect(Collectors.toList());
            InventoryOrderInfoDTO updateInventoryOrderInfoDTO = new InventoryOrderInfoDTO();
            BeanUtils.copyProperties(orderInfoDTO, updateInventoryOrderInfoDTO);
            updateInventoryOrderInfoDTO.setListItems(_refChangedInventoryOrderItems);
            updateInventoryOrderInfoDTO.setEndorsementSn(endorsementSnList.get(0));
            if (createEndorsementOverdueFlag) {
                orderTodoTag = InventoryOrderTodoTagEnums.add(orderTodoTag, InventoryOrderTodoTagEnums.CREATE_ENDORSEMENT_OVERDUE);
                updateInventoryOrderInfoDTO.setOrderTodoTag(orderTodoTag);
            }
            inventoryOrderInfoService.updateInventoryOrderInfoDTO(updateInventoryOrderInfoDTO, false);
            if (Objects.equals(submit.getStockChangeEnable(), 0)) {
                //核注单不核扣库存 -- 执行核注单服务完成后续操作
                List<EndorsementDTO> newEndorsementDTOList = this.findByIdList(endorsementIdList);
                newEndorsementDTOList.forEach(endorsementDTO -> {
                    // 补一下已审核回告节点
                    if (Objects.equals(InventoryOrderChannel.TAO_TIAN.getValue(), orderInfoDTO.getChannel())) {
                        examinedTaotianReportProcess(endorsementDTO, orderInfoDTO);
                    }
                    endorsementFinishNotSecondOut(endorsementDTO);
                    // 退货入区 生成申报单轨迹日志
                    if (Objects.equals(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode(), endorsementDTO.getBussinessType())) {
                        messageSender.sendMsg(endorsementDTO.getId(), "ccs-trackLog-es-endorsement-topic");
                    }
                });
            } else {
                inventoryOrderInfoService.correlation(updateInventoryOrderInfoDTO, InventoryOrderEnum.STATUS_ENDORSEMENT.getCode());
                //生成核注去修改协同单状态
                collaborateOrderService.updateCollaborateOrderStatus(orderInfoDTO.getInveCustomsSn(), CollaborateStatus.GENERATE_ENDORSEMENT);
                if (Objects.nonNull(orderInfoDTO.getSubOrderSn())) {
                    List<String> subSnList = Arrays.asList(orderInfoDTO.getSubOrderSn().split(","));
                    subSnList.forEach(s -> {
                        collaborateOrderService.updateCollaborateOrderStatus(s, CollaborateStatus.GENERATE_ENDORSEMENT);
                    });
                }
                if (Objects.equals(orderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode())) {
                    refundOrderService.updateCustomsStatusByInveSn(orderInfoDTO.getInveCustomsSn(), InventoryOrderEnum.STATUS_ENDORSEMENT.getCode());
                }
            }
            if (InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode().equals(orderInfoDTO.getInveBusinessType()) ||
                    InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode().equals(orderInfoDTO.getInveBusinessType())) {
                inventoryOrderAlarmService.deleteAlarm(orderInfoDTO.getInveCustomsSn());
            }
            if (InventoryOrderTagEnums.contains(orderInfoDTO.getOrderTag(), InventoryOrderTagEnums.AUTO_TRANSFER)
                    && InventoryInOutEnum.IN.getCode().equalsIgnoreCase(orderInfoDTO.getInOutFlag())) {
                log.info("全自动调拨 创建核注单 发送异步消息 invOrderId={}", orderInfoDTO.getId());
                messageSender.sendMsg(orderInfoDTO.getId(), "ccs-inventory-auto-transfer-topic");
            }
        } else {
            ExportOrderDTO exportOrderDTO = exportOrderService.findById(submit.getExportOrderId());
            if (Objects.isNull(exportOrderDTO)) {
                log.error("未查询到出库单详情");
                throw new ArgsInvalidException("未查询到出库单详情");
            }
            log.info("generate exportOrderDTO={}", JSON.toJSONString(exportOrderDTO));
            if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
                log.error("关联出库单状态不允许生成核注单");
                throw new ArgsInvalidException("关联出库单状态不允许生成核注单");
            }
            String lockName = "CCS:EXPORT:IMPORT:" + exportOrderDTO.getSn();

            boolean locked = RedissLockUtil.tryLock(lockName, TimeUnit.SECONDS, 5, -1);
            if (!locked) {
                log.error("出库单录入单号中，无法创建核注单 单号:{}", exportOrderDTO.getSn());
                throw new ArgsInvalidException("出库单录入单号中，无法创建核注单");
            }
            try {
                // 出区（二线出）
                Map<String, List<ExportItemDTO>> endorsementItemGroup = exportOrderService.distributionForEndorsement(submit.getExportOrderId());

                ExportOrderDO example = new ExportOrderDO();
                example.setId(exportOrderDTO.getId());
                example.setStatus(ExportOrderStatus.DECLARING.getValue());
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(example);
                }
                example.setUpdateTime(new Date());
                exportOrderMapper.updateByPrimaryKeySelective(example);
                List<String> inventorySnList = new ArrayList<>();
                for (Map.Entry<String, List<ExportItemDTO>> entry : endorsementItemGroup.entrySet()) {
                    Long bookId = Long.valueOf(Strings.split(entry.getKey(), '_')[0]);
                    List<ExportItemDTO> exportItemDTOList = entry.getValue();
                    EndorsementDO endorsementDO = new EndorsementDO();
                    endorsementDO.setExportOrderId(submit.getExportOrderId());
                    endorsementDO.setIeFlag(submit.getIeFlag());
                    endorsementDO.setChecklistsFlag(submit.getChecklistsFlag());
                    endorsementDO.setSn(sequenceService.generateEndorsementSn());
                    endorsementDO.setAccountBookId(bookId);
                    CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(bookId);
                    endorsementDO.setDeclareCompanyId(customsBookDTO.getAreaCompanyId());
                    endorsementDO.setStatus(EndorsementOrderStatus.INIT.getCode());
                    endorsementDO.setInvtType("8");
                    endorsementDO.setSupvMode("5000");
                    if (Objects.equals(submit.getStockChangeEnable(), 0)) {
                        endorsementDO.setStatus(EndorsementOrderStatus.FINISH.getCode());
                    }
                    endorsementDO.setBussinessType(submit.getBussinessType());
                    endorsementDO.setOrderType(EndorsementOrderTypeEnums.EXPORT_ORDER.getCode());
                    endorsementDO.setStockChangeEnable(true);
                    UserUtils.setCreateAndUpdateBy(endorsementDO);
                    endorsementDO.setUpdateTime(new Date());

                    String remark = this.getRemark(exportItemDTOList, 4000);
                    if (Objects.nonNull(remark) && !StringUtils.isEmpty(remark)) {
                        {
                            endorsementDO.setRemark(remark);
                        }
                    }

                    endorsementMapper.insertSelective(endorsementDO);
                    endorsementSnList.add(endorsementDO.getSn());

                    List<Long> exportItemIdList = exportItemDTOList.stream().map(dto -> {
                        inventorySnList.add(dto.getCustomsInventorySn());
                        return dto.getId();
                    }).collect(Collectors.toList());
                    ExportItemDO exportItemDO = new ExportItemDO();
                    exportItemDO.setEndorsementOrderId(endorsementDO.getId());
                    if (!Objects.equals(UserUtils.getUserId(), 0)) {
                        UserUtils.setUpdateBy(exportItemDO);
                    }
                    exportItemDO.setUpdateTime(new Date());
                    Example example1 = new Example(ExportItemDO.class);
                    example1.createCriteria().andIn("id", exportItemIdList).andEqualTo("deleted", 0);
                    exportItemMapper.updateByExampleSelective(exportItemDO, example1);
                    try {
                        List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.findBySnList(inventorySnList);
                        //构建字节二线出区核注备注
                        endorsementService.buildEndorsementFbNote(customsInventoryDTOS, endorsementDO.getId(), endorsementDO.getDeclareCompanyId());
                        List<String> orderSnList = customsInventoryDTOS.stream().map(CustomsInventoryDTO::getOrderSn).collect(Collectors.toList());
                        orderService.updateOrderInternalStatusBySnList(orderSnList, OrderInternalEnum.INVENTORY_DECLARING.getCode());
                        messageSender.sendMsg(endorsementDO.getId(), "ccs-trackLog-es-endorsement-topic");
                    } catch (Exception e) {
                        log.error("核注单轨迹日志生成异常 error={}", e.getMessage(), e);
                    }
                    endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.INIT, "新建核注清单");
                    if (Objects.equals(submit.getStockChangeEnable(), 0)) {
                        endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.FINISH, "核注单清关完成，是否核扣账册【否】");
                    }
                }
                //去关联清关单申报核注状态
                customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, InventoryReviewStatus.LINKED_REVIEW.getValue());
            } catch (Exception e) {
                log.error("创建核注单异常 error={}", e.getMessage(), e);
                throw e;
            } finally {
                try {
                    RedissLockUtil.unlock(lockName);
                    log.info("出库单生成核注单 解锁成功 lockName={}", lockName);
                } catch (Exception ex) {
                    log.error("解锁异常 lockName={} error={}", lockName, ex.getMessage(), ex);
                }
            }
        }
        return endorsementSnList;
    }

    private void generateNotSecondOutCheck(InventoryOrderInfoDTO orderInfoDTO, List<InventoryOrderItemDTO> itemDTOS, Integer stockChangeEnable) {
        if (StrUtil.isNotBlank(orderInfoDTO.getEndorsementSn())) {
            throw new ArgsInvalidException("清关单已生成核注单，请勿重复生成");
        }
        if (InventoryOrderChannel.LOGISTICS.getValue().equals(orderInfoDTO.getChannel())
                && InventoryDeclareWayEnum.TALLY_THEN_DECLARE.getCode().equals(orderInfoDTO.getDeclareWay())
                && Objects.equals(orderInfoDTO.getTallyComplete(), 0)
        ) {
            throw new ArgsInvalidException("先理后报下未收到理货报告无法生成核注单");
        }
        Integer orderTodoTag = orderInfoDTO.getOrderTodoTag();
        List<Integer> orderTodoTags = InventoryOrderTodoTagEnums.getOrderTodoTags(orderTodoTag);
        if (orderTodoTags.contains(InventoryOrderTodoTagEnums.WAIT_CARRYOVER_DETAIL.getCode())) {
            throw new ArgsInvalidException("存在结转明细待确认 无法生成核注单");
        }
        if (orderTodoTags.contains(InventoryOrderTodoTagEnums.UPDATE_ITEM_LIST_FAIL.getCode())) {
            throw new ArgsInvalidException("存在表体更新失败异常 无法生成核注单");
        }
        if (Objects.equals(1, orderInfoDTO.getFbFlag())) {
            // 非保清关单 不生成核注
            throw new ArgsInvalidException("非保清关单 无法生成核注单");
        }
        List<InventoryOrderBusinessEnum> checkLockStatusList = InventoryOrderBusinessEnum.listLockBizTypeByChannel(orderInfoDTO.getChannel());
        if (checkLockStatusList.contains(InventoryOrderBusinessEnum.getEnum(orderInfoDTO.getInveBusinessType()))
                && !InventoryOrderTagEnums.contains(orderInfoDTO.getOrderTag(), InventoryOrderTagEnums.MASTER_ORDER)
                && !Objects.equals(orderInfoDTO.getLockStockFlag(), 1)) {

            if (InventoryOrderChannel.LOGISTICS.getValue().equals(orderInfoDTO.getChannel())
                    && Objects.equals(stockChangeEnable, 0)) {
                log.info("关仓单 不核扣账册 不校验库存是否锁定");
            } else {
                throw new ArgsInvalidException("表体库存未锁定，请重新保存表体");
            }
        }
        Integer orderTag = orderInfoDTO.getOrderTag();
        List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(orderTag);
        //检查关联分区结转
        if (orderTags.contains(InventoryOrderTagEnums.RELATE.getCode())) {
            log.info("checkCarryOverRelateOrder carryOverNo={} ", orderInfoDTO.getCarryOverNo());
            String result = inventoryOrderInfoService.checkCarryOverRelateOrder(orderInfoDTO.getCarryOverNo());
            log.info("checkCarryOverRelateOrder carryOverNo={} result={}", orderInfoDTO.getCarryOverNo(), result);
            if (StringUtils.isNotEmpty(result)) {
                throw new ArgsInvalidException(result);
            }
        }
        if (InventoryOrderBusinessEnum.BUSINESS_SIMPLE_PROCESSING.getCode().equalsIgnoreCase(orderInfoDTO.getInveBusinessType())) {
            List<InventoryOrderItemGoodsDTO> inventoryOrderItemGoodsDTOS = inventoryOrderInfoService.listItemGoodsByInventoryId(orderInfoDTO.getId());
            if (CollUtil.isEmpty(inventoryOrderItemGoodsDTOS)) {
                throw new ArgsInvalidException("清关单料件表体不能为空");
            }
        }
        if (InventoryOrderTagEnums.contains(orderInfoDTO.getOrderTag(), InventoryOrderTagEnums.ASSOCIATE_TRANSFER)) {
            List<DraftItemCompareDTO> draftItemCompareDTOS = inventoryOrderInfoService.compareDraftItemList(orderInfoDTO.getId(), orderInfoDTO.getDraftCompareType());
            if (CollUtil.isNotEmpty(draftItemCompareDTOS) && draftItemCompareDTOS.stream().anyMatch(d -> d.getExitsFail() || d.getExitsDiff())) {
                throw new ArgsInvalidException("未操作/未通过草单比对，无法生成核注单");
            }
        }
        InventoryOrderItemDTO inventoryOrderItemDTO = itemDTOS.stream()
                .filter(i -> Objects.isNull(i.getDeclareUnitQfy()) || i.getDeclareUnitQfy().compareTo(BigDecimal.ZERO) == 0)
                .findAny().orElse(null);
        if (Objects.nonNull(inventoryOrderItemDTO)) {
            throw new ArgsInvalidException("存在表体申报数量为0");
        }
    }


    public void buildEndorsementFbNote(List<CustomsInventoryDTO> customsInventoryDTOS, Long endorsementId, Long declareCompanyId) throws ArgsInvalidException {
        ByteDanceFbNoteConfig fbNoteConfig = JSON.parseObject(orderBaseConfig.getFbNoteConfig(), ByteDanceFbNoteConfig.class);
        if (Objects.isNull(fbNoteConfig)) {
            log.info("ByteDanceFbNoteConfig is null");
            return;
        }
        if (!fbNoteConfig.getEnable()) {
            log.info("ByteDanceFbNoteConfig is not enable");
            return;
        }
        if (!isDeclareCompanyIdValid(fbNoteConfig, declareCompanyId)) {
            return;
        }

        // 编译正则表达式
        Pattern pattern = compileRegex(fbNoteConfig);
        if (pattern == null) {
            return;
        }

        // 提取符合正则表达式的备注信息
        List<String> fbNoteList = extractFbNotes(customsInventoryDTOS, pattern, fbNoteConfig);
        if (CollectionUtils.isEmpty(fbNoteList)) {
            log.info("ByteDanceFbNoteConfig fbNoteList is null");
            return;
        }
        // 处理提取的备注信息
        String note = processExtractedNotes(fbNoteList, fbNoteConfig);
        log.info("ByteDanceFbNoteConfig note={}", note);

        // 更新EndorsementDO对象并执行数据库更新
        endorsementService.updateEndorsementDO(endorsementId, note);
    }

    private boolean isDeclareCompanyIdValid(ByteDanceFbNoteConfig fbNoteConfig, Long declareCompanyId) {
        List<Long> areaCompanyIdList = fbNoteConfig.getAreaCompanyIdList();
        if (CollectionUtils.isEmpty(areaCompanyIdList)) {
            log.info("ByteDanceFbNoteConfig areaCompanyIdList is null");
            return false;
        }
        if (!areaCompanyIdList.contains(declareCompanyId)) {
            log.info("ByteDanceFbNoteConfig is not contains declareCompanyId={}", declareCompanyId);
            return false;
        }
        return true;
    }

    public Pattern compileRegex(ByteDanceFbNoteConfig fbNoteConfig) {
        String regex = fbNoteConfig.getRegex();
        log.info("ByteDanceFbNoteConfig regex={}", regex);

        try {
            return Pattern.compile(regex);
        } catch (Exception e) {
            log.error("Failed to compile regex: {}", e.getMessage());
            return null;
        }
    }

    public List<String> extractFbNotes(List<CustomsInventoryDTO> customsInventoryDTOS, Pattern pattern, ByteDanceFbNoteConfig fbNoteConfig) {
        List<String> fbNoteList = new ArrayList<>();
        int sum = 0;

        for (CustomsInventoryDTO customsInventoryDTO : customsInventoryDTOS) {
            String note = customsInventoryDTO.getNote();
            if (StringUtils.isNotEmpty(note)) {
                Matcher matcher = pattern.matcher(note);
                if (matcher.find()) {
                    int groupIndex = fbNoteConfig.getGroupIndex() != null ? fbNoteConfig.getGroupIndex() : 1;
                    String group = matcher.group(groupIndex);
                    log.info("ByteDanceFbNoteConfig regex match group={}", group);
                    fbNoteList.add(group);
                    sum++;
                }
            }
            if (sum == fbNoteConfig.getMaxMatches()) {
                break;
            }
        }

        return fbNoteList;
    }

    private String getRemark(List<ExportItemDTO> exportItemDTOList, Integer limit) {
        List<String> remarkList = new ArrayList<>();
        Map<String, Integer> countMap = new HashMap<>();
        Map<String, ExportSkuInfo> productIdMap = new HashMap<>();
        exportItemDTOList.forEach(exp -> {
            List<ExportSkuInfo> skuInfoList = JSON.parseArray(exp.getSkuJson(), ExportSkuInfo.class);
            if (Objects.nonNull(skuInfoList)) {
                skuInfoList.forEach(sku -> {
                    List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(sku.getItemTag());
                    if (orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode())) {
                        productIdMap.put(sku.getProductId(), sku);
                        Integer count = countMap.getOrDefault(sku.getProductId(), 0);
                        count = sku.getCount() + count;
                        countMap.put(sku.getProductId(), count);
                    }
                });
            }
        });
        for (Map.Entry<String, ExportSkuInfo> entry : productIdMap.entrySet()) {
            Integer i = countMap.get(entry.getKey());
            remarkList.add(entry.getValue().getGoodsName() + "," + i + "件");
        }
        if (!CollectionUtils.isEmpty(remarkList)) {
            String remark = String.join(";", remarkList);
            if (Objects.nonNull(limit)) {
                if (remark.length() > limit) {
                    remark = remark.substring(0, limit);
                }
            }
            return remark;
        } else {
            return null;
        }
    }


    @Override
    public List<EndorsementItemDTO> preItemView(Long id) {
        EndorsementDTO old = this.findById(id);
        Map<Long, EndorsementItemDO> itemDOMap = this.preItems(old);
        Map<String, EndorsementItemDO> fbItemDOMap = this.preItemsByFb(old);
        List<EndorsementItemDTO> items = itemDOMap.values().stream().map(this::buildItemDTO).collect(Collectors.toList());
        List<EndorsementItemDTO> fbItems = fbItemDOMap.values().stream().map(this::buildItemDTO).collect(Collectors.toList());
        items.addAll(fbItems);
        return items;
    }

    @Override
    public Map<Long, EndorsementItemDTO> preItemDTOs(EndorsementDTO endorsementDTO, List<ExportItemDTO> itemList) {
        Map<Long, EndorsementItemDO> itemDOMap = this.preItems(endorsementDTO, itemList);
        Map<Long, EndorsementItemDTO> result = new HashMap<>();
        itemDOMap.forEach((k, v) -> {
            EndorsementItemDTO dto = new EndorsementItemDTO();
            result.put(k, buildItemDTO(v));
        });
        return result;
    }

    private Map<Long, EndorsementItemDO> preItems(EndorsementDTO endorsementDTO) {
        return this.preItems(endorsementDTO, null);
    }

    private Map<Long, EndorsementItemDO> preItems(EndorsementDTO endorsementDTO, List<ExportItemDTO> itemList) {
        //外层能传 这里不需要再查一遍了
        if (CollectionUtils.isEmpty(itemList)) {
            itemList = exportOrderService.listItemByEndorsementId(endorsementDTO.getId());
        }
//        Map<Long, GoodsRecordDTO> goodsRecordDTOMap = new HashMap<>();
        Map<Long, EndorsementItemDO> endorsementItemDOMap = new HashMap<>();
        Map<Long, CustomsBookItemDTO> customsBookItemDTOMap = new HashMap<>();
        List<Long> customsBookItemIdList = itemList.stream()
                .flatMap(i -> {
                    List<ExportSkuInfo> skuInfoList = JSON.parseArray(i.getSkuJson(), ExportSkuInfo.class);
                    return skuInfoList.stream().map(ExportSkuInfo::getBookItemId);
                }).collect(Collectors.toList());
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findById(customsBookItemIdList);
        if (CollUtil.isNotEmpty(customsBookItemDTOList)) {
            customsBookItemDTOMap = customsBookItemDTOList.stream()
                    .collect(Collectors.toMap(CustomsBookItemDTO::getId, Function.identity(), (v1, v2) -> v1));
        }
        for (ExportItemDTO exportItemDTO : itemList) {
            List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
            for (ExportSkuInfo skuInfo : skuInfoList) {
                EndorsementItemDO endorsementItemDO = endorsementItemDOMap.get(skuInfo.getBookItemId());
                if (endorsementItemDO == null) {
                    // 初始化
                    endorsementItemDO = new EndorsementItemDO();
                    CustomsBookItemDTO customsBookItemDTO = customsBookItemDTOMap.get(skuInfo.getBookItemId());
                    List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(skuInfo.getItemTag());
                    // 若是非保赠品，则不去查备案
                    if (orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode())) {
                        continue;
                    }
                    if (Objects.isNull(customsBookItemDTO)) {
                        customsBookItemDTO = customsBookItemService.findById(skuInfo.getBookItemId());
                        if (Objects.isNull(customsBookItemDTO)) {
                            log.error("EndorsementService preItems 账册库存id=" + skuInfo.getBookItemId() + "未找到");
                            throw new ArgsErrorException("账册库存id=" + skuInfo.getBookItemId() + "未找到");
                        }
                        customsBookItemDTOMap.put(skuInfo.getBookItemId(), customsBookItemDTO);
                    }
                    endorsementItemDO.setProductId(customsBookItemDTO.getProductId());
                    endorsementItemDO.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                    endorsementItemDO.setGoodsName(customsBookItemDTO.getGoodsName());
                    endorsementItemDO.setRecordProductName(customsBookItemDTO.getGoodsName());
                    endorsementItemDO.setHsCode(customsBookItemDTO.getHsCode());
                    // itemTag
                    endorsementItemDO.setItemTag(skuInfo.getItemTag());
                    endorsementItemDO.setEndorsementId(endorsementDTO.getId());
                    endorsementItemDO.setOriginProductId(skuInfo.getUnifiedProductId());
                    endorsementItemDO.setDeclareUnitQfy(BigDecimal.ZERO);
                    endorsementItemDO.setRemainDeclareUnitQfy(BigDecimal.ZERO);
                    endorsementItemDO.setGrossWeight(skuInfo.getGrossWeight() == null ? BigDecimal.ZERO : skuInfo.getGrossWeight());
                    endorsementItemDO.setNetWeight(skuInfo.getNetWeight() == null ? BigDecimal.ZERO : skuInfo.getNetWeight());
                    endorsementItemDO.setModfMark(EndorsementModfMarkEnums.NOT_MODIFY.getCode());
                    //保存extra信息
                    EndorsementExtraDO endorsementExtraDO = new EndorsementExtraDO();
                    endorsementExtraDO.setDeclareUnit(customsBookItemDTO.getGoodsUnit());
                    endorsementExtraDO.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                    endorsementExtraDO.setDeclareTotalPrice(customsBookItemDTO.getDeclarePrice().multiply(BigDecimal.valueOf(skuInfo.getCount())));
                    endorsementExtraDO.setCurrency(customsBookItemDTO.getCurrCode());
                    endorsementExtraDO.setFirstUnit(customsBookItemDTO.getFirstUnit());
                    endorsementExtraDO.setFirstUnitQfy(customsBookItemDTO.getFirstUnitAmount());
                    endorsementExtraDO.setSecondUnit(customsBookItemDTO.getSecondUnit());
                    endorsementExtraDO.setSecondUnitQfy(customsBookItemDTO.getSecondUnitAmount());
                    endorsementItemDO.setGoodsSource(customsBookItemDTO.getGoodsSource());
                    endorsementItemDO.setExtraJson(JSON.toJSONString(endorsementExtraDO));
                } else {
                    BigDecimal grossWeight = skuInfo.getGrossWeight() == null ? BigDecimal.ZERO : skuInfo.getGrossWeight();
                    BigDecimal netWeight = skuInfo.getNetWeight() == null ? BigDecimal.ZERO : skuInfo.getNetWeight();
                    endorsementItemDO.setGrossWeight(endorsementItemDO.getGrossWeight().add(grossWeight));
                    endorsementItemDO.setNetWeight(endorsementItemDO.getNetWeight().add(netWeight));
                }
                // 对商品备案没有处理，就不去查了
//                GoodsRecordDTO goodsRecordDTO = goodsRecordDTOMap.get(skuInfo.getBookItemId());
//                if (goodsRecordDTO == null) {
//                    String productId = endorsementItemDO.getProductId();
//                    if (Objects.nonNull(endorsementItemDO.getOriginProductId())) {
//                        productId = endorsementItemDO.getOriginProductId();
//                    }
//                    goodsRecordDTO = goodsRecordService.findByBookIdAndProId(endorsementDTO.getAccountBookId(), productId);
//                    goodsRecordDTOMap.put(skuInfo.getBookItemId(), goodsRecordDTO);
//                }
                // 值累加
//                log.info("[EndorsementBuild.preItems goodsRecordDTO = {},skuInfo={}]", JSON.toJSONString(goodsRecordDTO), JSON.toJSONString(skuInfo));
                //BigDecimal grossWeight = goodsRecordDTO.getGrossWeight().multiply(new BigDecimal(skuInfo.getCount()));
                //BigDecimal netWeight = goodsRecordDTO.getNetWeight().multiply(new BigDecimal(skuInfo.getCount()));
                endorsementItemDO.setDeclareUnitQfy(endorsementItemDO.getDeclareUnitQfy().add(new BigDecimal(skuInfo.getCount())));
                endorsementItemDO.setRemainDeclareUnitQfy(endorsementItemDO.getDeclareUnitQfy());
                endorsementItemDOMap.put(skuInfo.getBookItemId(), endorsementItemDO);
            }
        }
        return endorsementItemDOMap;
    }

    /**
     * 非保赠品
     *
     * @param endorsementDTO
     * @return
     */
    private Map<String, EndorsementItemDO> preItemsByFb(EndorsementDTO endorsementDTO) {
        List<ExportItemDTO> itemList = exportOrderService.listItemByEndorsementId(endorsementDTO.getId());
        Map<String, EndorsementItemDO> endorsementItemDOMap = new HashMap<>();
        // 虚拟的itemId
        for (ExportItemDTO exportItemDTO : itemList) {
            List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
            for (ExportSkuInfo skuInfo : skuInfoList) {
                EndorsementItemDO endorsementItemDO = endorsementItemDOMap.get(skuInfo.getProductId());
                if (endorsementItemDO == null) {
                    // 初始化
                    endorsementItemDO = new EndorsementItemDO();
                    List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(skuInfo.getItemTag());
                    // 若是非保赠品，则不去查备案
                    if (!orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode())) {
                        continue;
                    }
                    endorsementItemDO.setProductId(skuInfo.getProductId());
                    endorsementItemDO.setGoodsName(skuInfo.getGoodsName());
                    endorsementItemDO.setRecordProductName(skuInfo.getGoodsName());
                    // itemTag
                    endorsementItemDO.setItemTag(skuInfo.getItemTag());
                    endorsementItemDO.setEndorsementId(endorsementDTO.getId());
                    endorsementItemDO.setOriginProductId(skuInfo.getUnifiedProductId());
                    endorsementItemDO.setDeclareUnitQfy(BigDecimal.ZERO);
                    endorsementItemDO.setRemainDeclareUnitQfy(BigDecimal.ZERO);
                    endorsementItemDO.setModfMark(EndorsementModfMarkEnums.NOT_MODIFY.getCode());
                }
                endorsementItemDO.setDeclareUnitQfy(endorsementItemDO.getDeclareUnitQfy().add(new BigDecimal(skuInfo.getCount())));
                endorsementItemDO.setRemainDeclareUnitQfy(endorsementItemDO.getDeclareUnitQfy());
                endorsementItemDO.setGrossWeight(BigDecimal.ZERO);
                endorsementItemDO.setNetWeight(BigDecimal.ZERO);

                endorsementItemDOMap.put(skuInfo.getProductId(), endorsementItemDO);
            }
        }
        return endorsementItemDOMap;
    }

    @Override
    public EndorsementDTO findById(Long id) {
        if (Objects.isNull(id)) {
            return null;
        }
        return this.buildDTO(endorsementMapper.selectByPrimaryKey(id));
    }

    @Override
    public EndorsementDTO findByRealOrderNo(String realOrderNo) {
        EndorsementDO endorsementDO = new EndorsementDO();
        endorsementDO.setRealOrderNo(realOrderNo);
        EndorsementDO result = endorsementMapper.selectOne(endorsementDO);
        return ConvertUtil.beanConvert(result, EndorsementDTO.class);
    }

    @Override
    public List<EndorsementDTO> findByRealOrderNo(List<String> realOrderNoList) {
        if (CollUtil.isEmpty(realOrderNoList)) {
            return new ArrayList<>();
        }
        Example example = new Example(EndorsementDO.class);
        example.createCriteria().andEqualTo("deleted", false).andIn("realOrderNo", realOrderNoList);
        List<EndorsementDO> endorsementDOS = endorsementMapper.selectByExample(example);
        return ConvertUtil.listConvert(endorsementDOS, EndorsementDTO.class);
    }

    /**
     * 通过预录入编号获取数据
     *
     * @param preOrderNo 预录入编号
     * @return
     */
    @Override
    public EndorsementDTO findByPreOrderNo(String preOrderNo) {

        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("preOrderNo", preOrderNo);
        return this.buildDTO(endorsementMapper.selectOneByExample(example));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void push(Long id) {
        EndorsementDTO old = this.findById(id);
        if (!old.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGE_EXCEPTION.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGED.getCode())
        ) {
            throw new ArgsErrorException("仅【已创建】【核注异常】【暂存异常】【已暂存】【申报中】状态核注单可被推送");
        }
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(old.getAccountBookId());
        CompanyDTO declareCompanyDTO = baseDataService.getCompanyDTOById(old.getDeclareCompanyId());
        if (Objects.nonNull(customsBookDTO) && Objects.nonNull(declareCompanyDTO)
                && !Objects.equals(old.getDeclareCompanyId(), customsBookDTO.getAreaCompanyId())) {
            throw new ArgsErrorException("区内企业:" + declareCompanyDTO.getName() + "，不存在账册" + customsBookDTO.getBookNo());
        }
        if (Objects.equals(old.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            log.info("导入订单无需校验");
        } else if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(old.getBussinessType())) {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findByEndorsementSn(old.getSn());
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())
                    && InventoryDeclareWayEnum.TALLY_THEN_DECLARE.getCode().equals(inventoryOrderInfoDTO.getDeclareWay())
                    && Objects.equals(inventoryOrderInfoDTO.getTallyComplete(), 0)
            ) {
                throw new ArgsInvalidException("清关单先理后报，未接收到理货报告!");
            }
            if (!Objects.equals(inventoryOrderInfoDTO.getInveCompanyId(), old.getDeclareCompanyId())) {
                throw new ArgsErrorException("核注单与清关单的清关企业不一致，请检查!");
            }
            if (Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.TAO_TIAN.getValue())
                    && Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
            ) {
                if (!inventoryOrderInfoService.checkTaotian2050Exist(inventoryOrderInfoDTO)) {
                    throw new ArgsErrorException("请先回传货物到港");
                }
            }
            if (Objects.equals(InventoryOrderChannel.LOGISTICS.getValue(), inventoryOrderInfoDTO.getChannel())
                    && Objects.equals(inventoryOrderInfoDTO.getChannelBusinessType(), "Distribution")) {
                List<InventoryOrderItemDTO> itemDTOList = inventoryOrderInfoService.findListByInvenOrderId(inventoryOrderInfoDTO.getId());
                if (itemDTOList.stream().anyMatch(i -> Objects.isNull(i.getGoodsSeqNo()))) {
                    throw new ArgsErrorException("存在新品，无法推送核注单");
                }
            }
            if (StringUtil.isNotBlank(inventoryOrderInfoDTO.getSubOrderSn())) {
                //淘天合单校验
                List<InventoryOrderInfoDTO> subInvOrderDTOList = inventoryOrderInfoService.findBySn(
                        Splitter.on(",").splitToList(inventoryOrderInfoDTO.getSubOrderSn()));
                for (InventoryOrderInfoDTO subInvOrderDTO : subInvOrderDTOList) {
                    if (Objects.equals(subInvOrderDTO.getChannel(), InventoryOrderChannel.TAO_TIAN.getValue())
                            && Objects.equals(subInvOrderDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
                            && !inventoryOrderInfoService.checkTaotian2050Exist(subInvOrderDTO)
                    ) {
                        throw new ArgsErrorException("子单：" + subInvOrderDTO.getInveCustomsSn() + "未回告货物到港");
                    }
                }
            }
        }
        if (Objects.equals(old.getStatus(), EndorsementOrderStatus.DECALRING.getCode())
                && (EndorsementCustomsStatus.INV201_WEIHEKOU.getCode().equals(old.getCustomsStatus())
                || EndorsementCustomsStatus.INV201_YIHEKOU.getCode().equals(old.getCustomsStatus()))
                || EndorsementCustomsStatus.INV201_YUKEKOU.getCode().equals(old.getCustomsStatus())) {
            throw new ArgsErrorException("无法推送，核注单已收到海关回执【通过（未核扣）】或【通过（已核扣）】");
        }
        // 限制重推时间
        Long endorsementPushInterval = orderBaseConfig.getEndorsementPushInterval();
        if (!redisLockUtils.lock("ccs:endorsement:push:limit:" + id, System.currentTimeMillis() + endorsementPushInterval * 60 * 1000L)) {
            throw new ArgsErrorException("核注单推送异常，重推间隔需大于" + endorsementPushInterval + "分钟");
        }
        // Step::更新状态
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setStatus(EndorsementOrderStatus.DECALRING.getCode());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);


//        if (old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode())
//                || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode())
//                || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_DESTORY.getCode())) {
//            List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderIdAndEndorsementId(old.getInventoryOrderId(), old.getId());
//            endorsementService.getInv101MessageRequestOutAO(itemDTOS, itemList, old, goodsRecordDTOMap, endorsementItemDOMap, request);
//        } else {
        this.getInv101MessageRequest(old, 1, 1);
//        }
        String message = EndorsementLogUtils.getLogDetailAndRemove();
        endorsementTrackLogService.buildFullLog(old.getId(), EndorsementOrderStatus.DECALRING, "核注清单请求报文提交", message);
        if (Objects.nonNull(old.getInventoryOrderId())) {
            inventoryOrderInfoTrackLogService.saveInventoryOrderLog(old.getInventoryOrderId(), "清关服务中");
        }
    }

    @Override
    @Transactional
    public void push(String sn) throws ArgsErrorException {
        EndorsementDTO endorsementDTO = this.findBySn(sn);
        if (Objects.isNull(endorsementDTO)) {
            throw new ArgsErrorException("未查询到核注单信息");
        }
        this.push(endorsementDTO.getId());
    }

    @Override
    public void autoCarryOverOrderPush(String sn) throws ArgsErrorException {
        boolean autoCarryOverOrderPush = orderBaseConfig.getAutoCarryOverOrderPush();
        log.info("autoCarryOverOrderPush = {}", autoCarryOverOrderPush);
        if (autoCarryOverOrderPush) {
            endorsementService.push(sn);
        }
    }

    //结转出核注推送
//    public void getInv101MessageRequestOutAO(List<InventoryOrderItemDTO> itemDTOS, List<ExportItemDTO> itemList, EndorsementDTO old, Map<Long, GoodsRecordDTO> goodsRecordDTOMap, Map<Long, EndorsementItemDO> endorsementItemDOMap, Inv101MessageRequest request) {
//        this.getInv101MessageRequest(itemList, old, goodsRecordDTOMap, endorsementItemDOMap, request);
//        if (EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
//            return;
//        }
//        endorsementService.getInv101MessageRequestOutAOCore(itemDTOS, old);
//    }

    @StockVerify(methodParameters = {0, 2}, changeType = InventoryCalculationTypeEnums.ADD_OCCUPATION, handler = StockEndorsementInventoryHandler.class)
    public void getInv101MessageRequestOutAOCore(List<InventoryOrderItemDTO> itemDTOS, EndorsementDTO old) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        itemDTOS.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn());
            if (EndorsementBussiness.BUSSINESS_DESTORY.getCode().equals(old.getBussinessType())) {
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.DESTORY_APPLY);
            } else {
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SECTION_OUT_PUSH);
            }
            updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue());
            list.add(updateInventoryDTO);
        });
        stockInventoryService.updateInventory(list);
    }

    /**
     * 构建推送报文
     *
     * @param old
     * @param declareFlag 0-暂存 1-申报
     * @param dclTypecd   1-备案申请 2-变更申请 3-删单申请; （删除申请选3，默认选1）
     * @return
     * @throws ArgsErrorException
     */
    private void getInv101MessageRequest(EndorsementDTO old, Integer declareFlag, Integer dclTypecd) {
        log.info("[EndorsementBuild.start declareFlag = {}, dclTypecd = {} old = {} ]", declareFlag, dclTypecd, JSON.toJSONString(old));
        // Step::推送
        String declareFlagDesc = declareFlag == 0 ? "暂存" : "申报";
        String declareTypeDesc;
        switch (dclTypecd) {
            case 1:
                declareTypeDesc = "备案申请";
                break;
            case 2:
                declareTypeDesc = "变更申请";
                break;
            case 3:
                declareTypeDesc = "删单申请";
                break;
            default:
                log.error("getInv101MessageRequest dclTypecd = {} 不合法", dclTypecd);
                throw new ArgsErrorException("dclTypecd = " + dclTypecd + " 不合法");
        }
        Inv101MessageRequest request;
        if (Objects.equals(old.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            request = inv101MessageRequestBuilder.buildRequestByExcelImport(old, declareFlag, dclTypecd);
        } else if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(old.getBussinessType())) {
            request = inv101MessageRequestBuilder.buildRequestByExportOrder(old, declareFlag, dclTypecd);
        } else {//其他
            request = inv101MessageRequestBuilder.buildRequestByInvtOrder(old, declareFlag, dclTypecd);
        }
        request.setEndorsementOrderNo(old.getSn());
        request.setPreEndorsementOrderNo(StringUtils.isNotBlank(old.getPreOrderNo()) ? old.getPreOrderNo() : "");
        request.setRealEndorsementOrderNo(StringUtils.isNotBlank(old.getRealOrderNo()) ? old.getRealOrderNo() : "");
        request.setBussinessType(old.getBussinessType());
        InvtHeadType invtHeadType = request.getInvtHeadType() == null ? new InvtHeadType() : request.getInvtHeadType();
        if (Objects.nonNull(old.getRemark())) {
            String remark = old.getRemark();
            invtHeadType.setRemark(CustomsCharLengthUtils.subStringByCharLength(remark, 255));
        }
        request.setInvtHeadType(invtHeadType);
        log.info("[EndorsementBuild.end request = {}]", JSON.toJSONString(request));

        String lockKey = "ccs:endorsement:push:" + old.getId() + "_" + declareFlag + "_" + dclTypecd;
        if (!RedissLockUtil.tryLock(lockKey, TimeUnit.SECONDS, 3, 5)) {
            throw new ArgsErrorRpcException("操作过于频繁，请稍后重试");
        }
        try {
            log.info("endorsement push key:{} 加锁成功", lockKey);
            String[] skipHzPushOrderList = orderBaseConfig.getSkipHzPushOrderList();
            ThreadContextUtil.setSkipHzPushNoList(Arrays.asList(skipHzPushOrderList));
            CustomsResult customsResult = customsSupport.ieEndorsementHZ(request);
            if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
                throw new ArgsErrorRpcException("核注" + declareFlagDesc + declareTypeDesc + "推送异常:" + customsResult.getErrorMessage());
            }
        } finally {
            try {
                ThreadContextUtil.removeSkipHzPushNoList();
            } catch (Exception ex) {
                log.error("移除，跳过核注推送单号ThreadLoal异常：{}", ex.getMessage(), ex);
            }
            try {
                RedissLockUtil.unlock(lockKey);
                log.info("endorsement push key:{} 释放锁成功", lockKey);
            } catch (Exception ex) {
                log.warn("解锁异常，可能是根本就没有加到锁就解锁了。异常信息：{}", ex.getMessage(), ex);
            }
        }
        if (declareFlag == 1 && Objects.equals(old.getOrderType(), EndorsementOrderTypeEnums.INVENTORY_ORDER.getCode())) {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
            //核注清单类型是以下五种，推送核注后改变清关单状态为【清关服务中】
            if (Objects.nonNull(inventoryOrderInfoDTO)) {
                if (old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode())
                        || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode())
                        || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode())
                        || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode())
                        || old.getBussinessType().equals(EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode())
                        || old.getBussinessType().equals(EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode())) {
                    inventoryOrderInfoService.correlation(inventoryOrderInfoDTO, InventoryOrderEnum.STATUS_SERVERING.getCode());
                    if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
                        InvenorderStatusDTO dto = new InvenorderStatusDTO();
                        dto.setStatus(InventoryOrderEnum.STATUS_SERVERING.getCode());
                        dto.setInveCustomsSn(inventoryOrderInfoDTO.getInveCustomsSn());
                        dto.setInveBusinessType(inventoryOrderInfoDTO.getInveBusinessType());
                        inventorderStatusCallbackProducer.send(dto);
                    }
                }
                if (InventoryOrderChannel.TAO_TIAN.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
                    this.taotianPushEndorsementReportMessage(old, inventoryOrderInfoDTO);
                }
                if (StringUtils.isNotEmpty(inventoryOrderInfoDTO.getSubOrderSn())) {
                    // 淘天合单-子单淘天回告
                    List<InventoryOrderInfoDTO> subInvOrderList = inventoryOrderInfoService.findBySn(
                            Splitter.on(",").splitToList(inventoryOrderInfoDTO.getSubOrderSn()));
                    subInvOrderList.forEach(i -> {
                        if (InventoryOrderChannel.TAO_TIAN.getValue().equals(i.getChannel())) {
                            this.taotianPushEndorsementReportMessage(old, i);
                        }
                    });
                }
            }
        }
    }

    private void taotianPushEndorsementReportMessage(EndorsementDTO endorsementDTO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        String bussinessType = endorsementDTO.getBussinessType();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(bussinessType);

        switch (endorsementBussiness) {
            case BUSSINESS_ONELINE_IN:
                taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.CLEARANCE_STARTS);
                break;
            case BUSSINESS_SECTION_IN:
            case BUSSINESS_SECTIONINNER_IN:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_START);
                }
                break;
            case BUSINESS_ONELINE_REFUND:
            case BUSSINESS_SECTION_OUT:
            case BUSSINESS_SECTIONINNER_OUT:
            case BUSSINESS_DESTORY:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    long time = new Date().getTime();
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_START, time);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_START, time + 1000L, 3);
                }
                break;
        }
        inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "淘天：入区申报开始回传");

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void temporaryStorage(Long id) throws ArgsErrorException {
        EndorsementDTO old = this.findById(id);
        List<String> allowTemporaryBusiType = Arrays.asList(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode(),
                EndorsementBussiness.BUSSINESS_SECTION_IN.getCode(),
                EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode(),
                EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode(),
                EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode(),
                EndorsementBussiness.BUSSINESS_DESTORY.getCode(),
                EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode(),
                EndorsementBussiness.BUSINESS_BONDED_TO_TRADE.getCode(),
                EndorsementBussiness.BUSINESS_ONELINE_REFUND.getCode(),
                EndorsementBussiness.BUSINESS_SUBSEQUENT_TAX.getCode(),
                EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN.getCode(),
                EndorsementBussiness.BUSINESS_INVENTORY_PROFIT.getCode(),
                EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode(),
                EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode(),
                EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode(),
                EndorsementBussiness.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode());
        if (!allowTemporaryBusiType.contains(old.getBussinessType())) {
            throw new ArgsErrorException("核注清单业务类型不可被暂存");
        }
        if (!old.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGED.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGING.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGE_EXCEPTION.getCode())
        ) {
            throw new ArgsErrorException("仅【已创建】、【申报中】、【异常】、【暂存异常】、【已暂存】、【暂存中】状态核注单可被暂存");
        }
        if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(old.getBussinessType())) {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findByEndorsementSn(old.getSn());
            if (!Objects.equals(inventoryOrderInfoDTO.getInveCompanyId(), old.getDeclareCompanyId())) {
                throw new ArgsErrorException("核注单与清关单的清关企业不一致，请检查!");
            }
            if (InventoryOrderUtils.checkTwoStepAndDeclareWay(inventoryOrderInfoDTO)
                    && !Objects.equals(inventoryOrderInfoDTO.getTallyComplete(), 1)) {
                throw new ArgsErrorException("清关单先理后报，未接收到理货报告!");
            }
        }
        // 限制重推时间
        Long lastTime = (Long) redisTemplate.boundHashOps("endorsement:rePush:temporaryStorage:limit").get(id);
        if (Objects.nonNull(lastTime) && DateUtil.offsetMinute(new Date(), -1).getTime() < lastTime) {
            throw new ArgsErrorException("重推间隔需大于1分钟");
        }
        redisTemplate.boundHashOps("endorsement:rePush:temporaryStorage:limit").put(id, (new Date()).getTime());
        // Step::更新状态
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setStatus(EndorsementOrderStatus.STORAGING.getCode());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
        // Step::推送
        Inv101MessageRequest request = new Inv101MessageRequest();
        request.setEndorsementOrderNo(old.getSn());
        request.setPreEndorsementOrderNo(StringUtils.isNotBlank(old.getPreOrderNo()) ? old.getPreOrderNo() : "");
        request.setRealEndorsementOrderNo(StringUtils.isNotBlank(old.getRealOrderNo()) ? old.getRealOrderNo() : "");
        request.setBussinessType(old.getBussinessType());
        //暂存
        this.getInv101MessageRequest(old, 0, 1);
//        request = inv101MessageRequestBuilder.buildRequestByInvtOrder(old, 0, 1);
//        CustomsResult customsResult = customsSupport.ieEndorsementHZ(request);
//        if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
//            throw new RuntimeException("核注暂存异常:" + customsResult.getErrorMessage());
//        }

        // 更新下清关单状态
        EndorsementDO endorsementDO = endorsementMapper.selectByPrimaryKey(id);
        Long orderInfoId = endorsementDO.getInventoryOrderId();
        if (orderInfoId != null) {
            inventoryOrderInfoService.updateStatusByEndorsementStatus(orderInfoId, EndorsementOrderStatus.STORAGING.getCode());
        }
        String requestXml = EndorsementLogUtils.getLogDetailAndRemove();
        //构建核注暂存日志
        endorsementTrackLogService.buildFullLog(endorsementDO.getId(), EndorsementOrderStatus.STORAGING, "暂存请求报文提交", requestXml);
        //构造清关单开始日志
        inventoryOrderInfoTrackLogService.saveInventoryOrderLog(orderInfoId, "清关开始");
    }


    @Override
    public void saveRemark(Long id, String remark) throws ArgsErrorException {
        // Step::更新备注
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setRemark(remark);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }


    @Override
//    @PageSelect
    public ListVO<EndorsementDTOV2> paging(EndorsementSearch search) {
        boolean skipPaging = this.buildPagingSearch(search);
        if (skipPaging) {
            return ListVOBuilder.buildEmptyListVO(EndorsementDTOV2.class);
        }
        Example example = getPagingExample(search);
        PageHelper.startPage(search.getCurrentPage(), search.getPageSize());
        List<EndorsementDO> list = endorsementMapper.selectByExample(example);
        ListVO<EndorsementDTOV2> result = new ListVO<>();
        result.setDataList(list.stream().map(this::buildDTOV2).collect(Collectors.toList()));
        // 分页
        PageInfo<EndorsementDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(search.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private boolean buildPagingSearch(EndorsementSearch search) {
        if (!org.springframework.util.StringUtils.isEmpty(search.getMailNo())) {
            List<ExportItemDTO> checklistOrderDTOList = exportOrderService.listItemByMailNos(new HashSet<>(com.google.common.collect.Lists.newArrayList(search.getMailNo())));
            Set<Long> idSet = checklistOrderDTOList.stream().map(ExportItemDTO::getEndorsementOrderId).collect(Collectors.toSet());
            List<Long> inventoryOrderIds = inventoryOrderInfoService.findListInventoryOrderIds(search.getMailNo());
            if (CollectionUtils.isEmpty(idSet) && CollectionUtils.isEmpty(inventoryOrderIds)) {
                return true;
            } else {
                search.setInventoryOrderIds(inventoryOrderIds);
                search.setIdSet(idSet);
            }
        }
        //清关单号
        List<Long> inventoryOrderIds = search.getInventoryOrderIds();
        if (!org.springframework.util.StringUtils.isEmpty(search.getInvOrderSn())) {
            List<String> invOrderSns = Splitter.on(",").splitToList(search.getInvOrderSn());
            List<InventoryOrderInfoDTO> inventoryOrderInfoDTOS = inventoryOrderInfoService.findBySn(invOrderSns);
            if (!CollectionUtils.isEmpty(inventoryOrderInfoDTOS)) {
                List<Long> list = inventoryOrderInfoDTOS.stream().map(InventoryOrderInfoDTO::getId).collect(Collectors.toList());
                if (CollUtil.isEmpty(inventoryOrderIds)) {
                    search.setInventoryOrderIds(list);
                } else {
                    // 取id交集
                    List<Long> intersectionList = new ArrayList<>(CollectionUtil.intersection(inventoryOrderIds, list));
                    if (CollUtil.isEmpty(intersectionList)) {
                        return true;
                    }
                    search.setInventoryOrderIds(intersectionList);
                }
            } else {
                return true;
            }
        }
        //出库单号
        if (!org.springframework.util.StringUtils.isEmpty(search.getExportOrderSn())) {
            List<String> exportOrderSns = Splitter.on(",").splitToList(search.getExportOrderSn());
            List<ExportOrderDTO> exportOrderDTOS = exportOrderService.findBySn(exportOrderSns);
            if (!CollectionUtils.isEmpty(exportOrderDTOS)) {
                search.setExportOrderIds(exportOrderDTOS.stream().map(ExportOrderDTO::getId).collect(Collectors.toList()));
            } else {
                return true;
            }
        }
        // 关联账册备案号 & 关联核注清单编号
        if (StringUtil.isNotEmpty(search.getRltEndorsementNo()) || StringUtil.isNotEmpty(search.getRltCustomsBookNo())) {
            Example example = new Example(InventoryOrderInfoDO.class);
            Example.Criteria criteria = example.createCriteria();
            // 关联核注清单编号
            if (StringUtil.isNotEmpty(search.getRltEndorsementNo())) {
                List<String> rltENdorsementNoList = new ArrayList<>(Arrays.asList(search.getRltEndorsementNo().split(",")));
                criteria.andIn("associatedEndorsementNo", rltENdorsementNoList);
            }
            // 关联账册备案号
            if (StringUtil.isNotEmpty(search.getRltCustomsBookNo())) {
                criteria.andCondition(" `out_account_book` = \"" + search.getRltCustomsBookNo() + "\" OR `in_account_book` = \"" + search.getRltCustomsBookNo() + "\"");
            }
            List<InventoryOrderInfoDO> inventoryOrderInfoDOS = inventoryOrderInfoBaseService.selectByExample(example);
            if (!CollectionUtils.isEmpty(inventoryOrderInfoDOS)) {
                List<Long> list = inventoryOrderInfoDOS.stream().map(InventoryOrderInfoDO::getId).collect(Collectors.toList());
                if (CollUtil.isEmpty(inventoryOrderIds)) {
                    search.setInventoryOrderIds(list);
                } else {
                    // 取id交集
                    List<Long> intersectionList = new ArrayList<>(CollectionUtil.intersection(inventoryOrderIds, list));
                    if (CollUtil.isEmpty(intersectionList)) {
                        return true;
                    }
                    search.setInventoryOrderIds(intersectionList);
                }
            } else {
                return true;
            }
        }
        // 操作人
        if (StringUtil.isNotEmpty(search.getOperator())) {
            Long operatorId = userServiceUtil.getUserIdByUserName(search.getOperator());
            if (operatorId == null) return true;
            search.setOperatorId(operatorId);
        }
        return false;
    }


    private Example getPagingExample(EndorsementSearch search) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        //出库单号
        if (!CollectionUtils.isEmpty(search.getExportOrderIds())) {
            criteria.andIn("exportOrderId", search.getExportOrderIds());
        }
        //清关单号
        if (!CollectionUtils.isEmpty(search.getInventoryOrderIds())) {
            criteria.andIn("inventoryOrderId", search.getInventoryOrderIds());
        }
        // 账册Id列表
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("accountBookId", accountBookIdList);
        }
        if (!LongUtil.isNone(search.getAgentCompanyId())) {
            criteria.andEqualTo("declareCompanyId", search.getAgentCompanyId());
        }
//        if (!StringUtils.isEmpty(search.getQueryInfo())) {
//            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
//            if ("realOrderNo".equals(search.getQueryType())) {
//                criteria.andIn("realOrderNo", noList);
//            } else if ("sn".equals(search.getQueryType())) {
//                criteria.andIn("sn", noList);
//            }
//        }
        //  企业内部编号
        if (!StringUtils.isEmpty(search.getSnNos())) {
            List<String> snList = Lists.newArrayList(search.getSnNos().split(","));
            criteria.andIn("sn", snList);
        }
        //  核注清单编号
        if (!StringUtils.isEmpty(search.getRealOrderNos())) {
            List<String> realOrderNoList = Lists.newArrayList(search.getRealOrderNos().split(","));
            criteria.andIn("realOrderNo", realOrderNoList);
        }
        //  预备核注清单编号
        if (!StringUtils.isEmpty(search.getPreOrderNos())) {
            List<String> preOrderNoList = Lists.newArrayList(search.getPreOrderNos().split(","));
            criteria.andIn("preOrderNo", preOrderNoList);
        }

        if (!LongUtil.isNone(search.getCustomsBookId())) {
            criteria.andEqualTo("accountBookId", search.getCustomsBookId());
        }
        if (!StringUtils.isEmpty(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (!CollectionUtils.isEmpty(search.getIdSet())) {
            criteria.andIn("id", search.getIdSet());
        }
        if (!StringUtils.isEmpty(search.getBussinessType())) {
            criteria.andEqualTo("bussinessType", search.getBussinessType());
        }
        if (!CollectionUtils.isEmpty(search.getInventoryOrderIds())) {
            criteria.andIn("inventoryOrderId", search.getInventoryOrderIds());
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            criteria.andBetween("createTime", new Date(createFrom), new Date(createTo));
        }
        Long finishFrom = LongUtil.getFrom(search.getFinishFrom(), search.getFinishTo());
        Long finishTo = LongUtil.getEnd(search.getFinishFrom(), search.getFinishTo());
        if (!LongUtil.isNone(finishFrom) && !LongUtil.isNone(finishTo)) {
            criteria.andBetween("finishTime", new Date(finishFrom), new Date(finishTo));
        }
        if (Objects.nonNull(search.getStockChangeEnable())) {
            criteria.andEqualTo("stockChangeEnable", search.getStockChangeEnable());
        }
        if (Objects.nonNull(search.getOperatorId())) {
            criteria.andEqualTo("createBy", search.getOperatorId());
        }
        if (StrUtil.isNotBlank(search.getGenerateDeclareStatus())) {
            criteria.andEqualTo("generateDeclareStatus", search.getGenerateDeclareStatus());
        }
        if (StrUtil.isNotBlank(search.getOrderType())) {
            criteria.andEqualTo("orderType", search.getOrderType());
        }
        example.setOrderByClause("create_time DESC");
        return example;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void finish(Long id, String realNo, List<String> information, Boolean isManual) throws ArgsErrorException {
        EndorsementDTO old = this.findById(id);
        if (StringUtils.isEmpty(realNo)) {
            throw new ArgsErrorException("核注单号不能为空");
        }
        List<EndorsementItemDTO> endorsementItemDTOList = this.listItemById(id);
        if (CollectionUtils.isEmpty(endorsementItemDTOList)) {
            saveEndorsementItem(id);
            endorsementItemDTOList = this.listItemById(id);
        }
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setRealOrderNo(realNo);
        example.setStatus(EndorsementOrderStatus.FINISH.getCode());
        example.setFinishTime(DateTime.now().toDate());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
        //记录核注单完成时间，便于更新库存流水的发生时间
        old.setFinishTime(example.getFinishTime());
        // Step::出库单|清关单处理
        //if (old.getIeFlag().equals(IEType.IMPORT.getValue())){
        CustomsBookDTO customsBookDTO = customsBookService.findById(old.getAccountBookId());
        if (EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode().equals(old.getOrderType())) {
            EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(old.getBussinessType());
            switch (endorsementBussiness) {
                case BUSSINESS_SECONDE_OUT:
                    endorsementService.endorsementFinishAndDeleteRACore(endorsementItemDTOList, old, realNo);
                    break;
                case BUSSINESS_REFUND_INAREA:
                    endorsementService.endorsementFinishNotSecondOutAACore(endorsementItemDTOList, old, false, realNo);
                    break;
                default:
                    break;
            }
        } else if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(old.getBussinessType())) {
            EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(old.getBussinessType());
            switch (endorsementBussiness) {
                case BUSSINESS_ONELINE_IN:
                case BUSSINESS_SECTION_IN:
                case BUSSINESS_SECTIONINNER_IN:
                case BUSINESS_BONDED_ONELINE_IN: // 保税物流一线入境
                case BUSINESS_INVENTORY_PROFIT:  //盘盈
                    endorsementService.endorsementFinishNotSecondOutAA(endorsementItemDTOList, old, realNo);
                    break;
                case BUSSINESS_SECTION_OUT:
                case BUSSINESS_SECTIONINNER_OUT:
                case BUSSINESS_DESTORY:
                    endorsementService.endorsementFinishNotSecondOutATU(endorsementItemDTOList, old, realNo);
                    break;
                case BUSSINESS_REFUND_INAREA:
                    endorsementService.endorsementFinishNotSecondOutUTA(endorsementItemDTOList, old, realNo);
                    break;
                case BUSINESS_BONDED_TO_TRADE:
                case BUSINESS_ONELINE_REFUND:
                case BUSINESS_SUBSEQUENT_TAX:
                case BUSINESS_RANDOM_INSPECTION_DECLARATION: // 抽检申报
                    endorsementService.endorsementFinishNotSecondOutRA(endorsementItemDTOList, old, realNo);
                    break;
                case BUSINESS_SIMPLE_PROCESSING:
                    List<EndorsementItemGoodsDTO> endorsementItemGoodsDTOS = listItemGoodsById(id);
                    List<EndorsementItemDTO> itemList = ConvertUtil.listConvert(endorsementItemGoodsDTOS, EndorsementItemDTO.class);
                    // 账册库存变动根据料件表体变动
                    endorsementService.endorsementFinishNotSecondOutATU(itemList, old, realNo);
                    break;
                default:
                    break;
            }
        } else {
            List<String> mailNos = new ArrayList<>();
            List<String> inventorySnList = new ArrayList<>();
            List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementId(old.getId());
            List<ExportSkuInfo> skuInfoList = new ArrayList<>();
            List<ExportSkuInfo> sumSkuInfoList = new ArrayList<>();
            exportItemDTOList.forEach(e -> {
                mailNos.add(e.getMailNo());
                inventorySnList.add(e.getCustomsInventorySn());
                // 在这里过滤 非保 sku
                List<ExportSkuInfo> exportSkuInfoList = JSON.parseArray(e.getSkuJson(), ExportSkuInfo.class);
                exportSkuInfoList = exportSkuInfoList.stream().filter(sku -> {
                    List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(sku.getItemTag());
                    return !orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode());
                }).collect(Collectors.toList());
                skuInfoList.addAll(exportSkuInfoList);
            });
            skuInfoList.stream().collect(Collectors.groupingBy(ExportSkuInfo::getBookItemId)).forEach((i, c) -> {
                ExportSkuItemInfo skuInfo = new ExportSkuItemInfo();
                skuInfo.setBookItemId(i);
                skuInfo.setCount(c.stream().mapToInt(ExportSkuInfo::getCount).sum());
                sumSkuInfoList.add(skuInfo);
            });
            //二线出区 取消单状态更新 -- 已出区 待取消 退货
            List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.listByLogistics90Days(mailNos);
            Map<Boolean, List<String>> mailNoMap = customsInventoryDTOS.stream()
                    .collect(Collectors.groupingBy(i -> CustomsStat.CUSTOMS_PASS.getValue().equals(i.getCustomsStatus()), Collectors.mapping(CustomsInventoryDTO::getLogisticsNo, Collectors.toList())));
            if (mailNoMap.containsKey(Boolean.TRUE)) {
                List<String> mailNoList = mailNoMap.get(Boolean.TRUE);
                customsInventoryCalloffService.updateCustomsInventoryCalloffStatusBatchByLogisticsNo(mailNoList, null, 1,
                        null, InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
            }
            if (mailNoMap.containsKey(Boolean.FALSE)) {
                List<String> mailNoList = mailNoMap.get(Boolean.FALSE);
                customsInventoryCalloffService.updateCustomsInventoryCalloffStatusBatchByLogisticsNo(mailNoList, null, 1,
                        InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(), InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
            }
            endorsementService.endorsementFinishSecondOutOTU(sumSkuInfoList, old, id, realNo, exportItemDTOList);
            //去关联清关单申报核注状态
            customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, InventoryReviewStatus.VERIFICATION_COMPLETED.getValue());
            customsInventoryService.updateIsOccupiedStockBySnList(inventorySnList, Boolean.FALSE);
            cullOrderService.updStatus(mailNos, CullStatusEnums.PROCESSED);
            String ceCompanyCode = orderBaseConfig.getCeCompanyCode();
            if (ceCompanyCode != null && ceCompanyCode.length() != 0) {
//                CompanyDTO companyDTO = baseDataService.getCompanyDTOByCode(code);
                CompanyDTO companyDTO = baseDataService.getCompanyDTOBySpecialClientCode(ceCompanyCode);
                //根据提供的企业编码获取企业
                if (Objects.nonNull(companyDTO)) {
                    //义乌楠欧供应链管理有限公司 发送至关企
                    if (Objects.equals(old.getDeclareCompanyId(), companyDTO.getId()) && Objects.equals(EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode(), old.getBussinessType())) {
                        log.warn("发送关企消费 核注单号 - {}", old.getSn());
                        producer.send(old.getSn());
                    }
                }
            }
        }
        try {
            String info = String.join("/", information);
            String message = EndorsementLogUtils.getLogDetailAndRemove();
            String logInfo;
            //改不动了 不想动finish方法 先这样兼容下吧
            if (Objects.equals(info, "核注清单手动核扣")) {
                logInfo = info;
            } else {
                logInfo = "核注清单审核回执:" + info;
            }
            endorsementTrackLogService.buildFullLog(old.getId(), EndorsementOrderStatus.FINISH, logInfo, message);
        } catch (Exception e) {
            log.error("buildFullLog error={}", e.getMessage(), e);
        }
        try {
            //更新核注单明细的金二序号
            Boolean isExcute = true;
            if (Objects.equals(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode(), old.getBussinessType()) ||
                    Objects.equals(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode(), old.getBussinessType()) ||
                    Objects.equals(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode(), old.getBussinessType())) {
                if (!endorsementService.updateEndorsementItemSeqNo(endorsementItemDTOList, realNo)) {
                    isExcute = false;
                }
            }
            //批量更新到数据库
            //判断是否含有金二序号为空的情况
            Boolean goodsNoExistEmpty = endorsementItemDTOList.stream().anyMatch(d -> StringUtils.isEmpty(d.getGoodsSeqNo()));
            log.info("核注单号{}是否存在金二序号为空的情况{}", old.getSn(), goodsNoExistEmpty);
            //判断账册的配置属性是否允许生成库存流水
            Boolean isGenStockList = false;
            if (Objects.nonNull(customsBookDTO) && Objects.equals(customsBookDTO.getStockListGenerateEnable(), 1)) {
                isGenStockList = true;
            }
            if (isExcute && !goodsNoExistEmpty && isGenStockList) {
                // 使用RocketMQ异步处理库存流水批量插入，避免阻塞主流程
                log.info("发送库存流水批量插入消息, endorsementId={}, endorsementSn={}, realNo={}",
                        old.getId(), old.getSn(), realNo);
                itemStockListBatchInsertProducer.sendItemStockListBatchInsertMessage(endorsementItemDTOList, old, realNo);
            }
        } catch (Exception e) {
            log.error("更新核注单明细的金二序号 error={}", e.getMessage(), e);
        }

        if (Boolean.TRUE.equals(old.getStockChangeEnable())) {
            List<EndorsementItemDTO> itemDTOList = listItemById(id);
            if (itemDTOList.stream()
                    .allMatch(i -> StringUtils.isNotBlank(i.getGoodsSeqNo()) || StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo()))) {
                //全为老品直接记录[账册库存处理成功]日志，否则要等待记账回执
                endorsementTrackLogService.buildStatusAndInfoLog(old.getId(), EndorsementOrderStatus.FINISH, "账册库存处理成功");
            }
        } else {
            endorsementTrackLogService.buildStatusAndInfoLog(old.getId(), EndorsementOrderStatus.FINISH, "核注单清关完成，是否核扣账册【否】");
        }
    }
    @Autowired
    private ItemStockListBatchInsertProducer itemStockListBatchInsertProducer;

    private void manualTaotianFinishReportProcess(EndorsementDTO endorsementDTO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        String businessType = endorsementDTO.getBussinessType();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(businessType);
        switch (endorsementBussiness) {
            case BUSSINESS_ONELINE_IN:
                taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.SERVICE_COMPLETED);
                break;
            case BUSSINESS_SECTION_IN:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    //区间入 + 服务完成  触发 入区申报结束与服务完成
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED, new Date().getTime() + 3000L, 3);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED);
                }
                break;
            case BUSSINESS_SECTIONINNER_IN:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_CLEARANCE_COMPLETED);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END, new Date().getTime() + 3000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED, new Date().getTime() + 6000L, 4);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED);
                }
                break;
            case BUSSINESS_SECTION_OUT:
            case BUSINESS_ONELINE_REFUND:
                taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED);
                break;
            case BUSSINESS_SECTIONINNER_OUT:
            case BUSSINESS_DESTORY:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_END);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_COMPLETED, new Date().getTime() + 3000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED, new Date().getTime() + 6000L, 4);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED);
                }
                break;
        }
        // 回告wms清关完成
        if (Objects.equals(endorsementBussiness, EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT)
                || Objects.equals(endorsementBussiness, EndorsementBussiness.BUSSINESS_SECTION_OUT)
                || Objects.equals(endorsementBussiness, EndorsementBussiness.BUSINESS_ONELINE_REFUND)
                || Objects.equals(endorsementBussiness, EndorsementBussiness.BUSSINESS_DESTORY)
        ) {
            TaoTianCustomsClearanceRpcDTO customsClearanceRpcDTO = new TaoTianCustomsClearanceRpcDTO();
            String upstreamOrigMsg = inventoryOrderInfoDTO.getUpstreamOrigMsg();
            TTClearanceEntryorderCreateDTO createDTO = JSON.parseObject(upstreamOrigMsg, TTClearanceEntryorderCreateDTO.class);
            List<TTClearanceEntryorderCreateDTO.RelatedOrder> relatedOrders = createDTO.getRelatedOrders();
            if (!CollectionUtils.isEmpty(relatedOrders)) {
                String orderCodes = relatedOrders.stream().map(TTClearanceEntryorderCreateDTO.RelatedOrder::getOrderCode).collect(Collectors.joining(","));
                customsClearanceRpcDTO.setWarehouseCode(createDTO.getWmsWarehouseCode());
                customsClearanceRpcDTO.setOutOrderNo(orderCodes);
                customsClearanceRpcDTO.setStatus(ShipmentCustomsClearanceStatusEnum.CLEARANCE_DONE);
                log.info("taotianFinishReportProcess outCallBackWmsToTaoTian req={}", JSON.toJSONString(customsClearanceRpcDTO));
                try {
                    Result<String> result = iShipmentRpcClient.outCallBackWmsToTaoTian(customsClearanceRpcDTO);
                    log.info("taotianFinishReportProcess outCallBackWmsToTaoTian stringResult={}", JSON.toJSONString(result));
                } catch (Exception e) {
                    log.error("taotianFinishReportProcess outCallBackWmsToTaoTian error={}", e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 同 manualTaotianFinishReportProcess ，考虑注释掉一个
     *
     * @param inventoryOrderInfoDTO
     */
    private void taotianFinishReportProcess(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        InventoryOrderBusinessEnum inventoryOrderBusinessEnum = InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType());
        switch (inventoryOrderBusinessEnum) {
            case BUSSINESS_ONELINE_IN:
                taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.SERVICE_COMPLETED);
                break;
            case BUSSINESS_SECTION_IN:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    //区间入 + 服务完成  触发 入区申报结束与服务完成
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED, new Date().getTime() + 3000L, 3);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED);
                }
                break;
            case BUSSINESS_SECTIONINNER_IN:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_CLEARANCE_COMPLETED);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END, new Date().getTime() + 3000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED, new Date().getTime() + 6000L, 4);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.INTER_ZONE_SERVICE_COMPLETED);
                }
                break;
            case BUSSINESS_SECTION_OUT:
            case BUSINESS_ONELINE_REFUND:
                taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED);
                break;
            case BUSSINESS_SECTIONINNER_OUT:
            case BUSSINESS_DESTORY:
                if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_END);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_COMPLETED, new Date().getTime() + 3000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED, new Date().getTime() + 6000L, 4);
                } else {
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_SERVICE_COMPLETED);
                }
                break;
        }
        inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "淘天：申报完成开始回传");
        taotianCallBackWms(inventoryOrderInfoDTO, inventoryOrderBusinessEnum);
    }

    @Override
    public void taotianCallBackWms(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        InventoryOrderBusinessEnum inventoryOrderBusinessEnum = InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType());
        this.taotianCallBackWms(inventoryOrderInfoDTO, inventoryOrderBusinessEnum);
    }

    @Override
    public List<EndorsementDTO> findByExportOrderId(Long exportOrderId) {
        if (Objects.isNull(exportOrderId)) {
            return Collections.emptyList();
        }
        Example example = new Example(EndorsementDO.class);
        example.createCriteria()
                .andEqualTo("exportOrderId", exportOrderId)
                .andEqualTo("deleted", false);
        List<EndorsementDO> endorsementDOS = endorsementMapper.selectByExample(example);
        return ConvertUtil.listConvert(endorsementDOS, EndorsementDTO.class);
    }

    @Override
    @PageSelect
    public ListVO<EndorsementRelationDTO> pagingRelation(EndorsementEbInvSearch search) {
        if (StrUtil.isNotBlank(search.getQueryInfo()) && !Objects.equals(search.getQueryType(), "inventoryNo")) {
            return ListVO.emptyListVO();
        }
        Example example = new Example(EndorsementRelationDO.class);
        Example.Criteria criteria = example.createCriteria()
                .andEqualTo("endorsementId", search.getId())
                .andEqualTo("deleted", false);
        if (Objects.equals(search.getQueryType(), "inventoryNo") && StrUtil.isNotBlank(search.getQueryInfo())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            criteria.andIn("inventoryNo", noList);
        }
        List<EndorsementRelationDO> list = endorsementRelationMapper.selectByExample(example);
        ListVO<EndorsementRelationDTO> result = new ListVO<>();
        result.setDataList(ConvertUtil.listConvert(list, EndorsementRelationDTO.class));
        PageInfo<EndorsementRelationDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<EndorsementRelationDTO> listRelationById(Long endorsementId) {
        if (Objects.isNull(endorsementId)) {
            return new ArrayList<>();
        }
        Example example = new Example(EndorsementRelationDO.class);
        example.createCriteria()
                .andEqualTo("endorsementId", endorsementId)
                .andEqualTo("deleted", false);
        List<EndorsementRelationDO> endorsementRelationDOS = endorsementRelationMapper.selectByExample(example);
        return ConvertUtil.listConvert(endorsementRelationDOS, EndorsementRelationDTO.class);
    }

    @Override
    public List<EndorsementRelationDTO> listRelationByInventoryNos(List<String> inventoryNos) {
        if (CollUtil.isEmpty(inventoryNos)) {
            return new ArrayList<>();
        }
        Example example = new Example(EndorsementRelationDO.class);
        example.createCriteria()
                .andIn("inventoryNo", inventoryNos)
                .andEqualTo("deleted", false);
        List<EndorsementRelationDO> endorsementRelationDOS = endorsementRelationMapper.selectByExample(example);
        return ConvertUtil.listConvert(endorsementRelationDOS, EndorsementRelationDTO.class);
    }

    public void taotianCallBackWms(InventoryOrderInfoDTO inventoryOrderInfoDTO, InventoryOrderBusinessEnum inventoryOrderBusinessEnum) {
        // 回告wms清关完成
        if (Objects.equals(inventoryOrderBusinessEnum, InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT)
                || Objects.equals(inventoryOrderBusinessEnum, InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT)
                || Objects.equals(inventoryOrderBusinessEnum, InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND)
                || Objects.equals(inventoryOrderBusinessEnum, InventoryOrderBusinessEnum.BUSSINESS_DESTORY)
                || Objects.equals(inventoryOrderBusinessEnum, InventoryOrderBusinessEnum.BUSINESS_FB_OUT)
        ) {
            TaoTianCustomsClearanceRpcDTO customsClearanceRpcDTO = new TaoTianCustomsClearanceRpcDTO();
            String upstreamOrigMsg = inventoryOrderInfoDTO.getUpstreamOrigMsg();
            TTClearanceEntryorderCreateDTO createDTO = JSON.parseObject(upstreamOrigMsg, TTClearanceEntryorderCreateDTO.class);
            List<TTClearanceEntryorderCreateDTO.RelatedOrder> relatedOrders = createDTO.getRelatedOrders();
            if (!CollectionUtils.isEmpty(relatedOrders)) {
                String orderCodes = relatedOrders.stream().map(TTClearanceEntryorderCreateDTO.RelatedOrder::getOrderCode).collect(Collectors.joining(","));
                customsClearanceRpcDTO.setWarehouseCode(createDTO.getWmsWarehouseCode());
                customsClearanceRpcDTO.setOutOrderNo(orderCodes);
                customsClearanceRpcDTO.setStatus(ShipmentCustomsClearanceStatusEnum.CLEARANCE_DONE);
                log.info("taotianFinishReportProcess outCallBackWmsToTaoTian req={}", JSON.toJSONString(customsClearanceRpcDTO));
                try {
                    Result<String> result = iShipmentRpcClient.outCallBackWmsToTaoTian(customsClearanceRpcDTO);
                    log.info("taotianFinishReportProcess outCallBackWmsToTaoTian stringResult={}", JSON.toJSONString(result));
                    ThreadContextUtil.setRequestInfoAndResponse(JSON.toJSONString(customsClearanceRpcDTO), JSON.toJSONString(result));
                } catch (Exception e) {
                    log.error("taotianFinishReportProcess outCallBackWmsToTaoTian error={}", e.getMessage(), e);
                }
                inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "WMS：淘天申报完成回传");
            }
        }
    }

    //批量插入到库存流水
    public void itemStockListBatchInsert(List<EndorsementItemDTO> endorsementItemDTOList, EndorsementDTO old, String realOrderNo) {
        InventoryOrderInfoDTO inventoryOrderInfoTemp = inventoryOrderInfoService.findById(old.getInventoryOrderId());
        List<ItemStockListDTO> itemStockLists = new ArrayList<>();
        endorsementItemDTOList.forEach(endorsementItem -> {
            ItemStockListDTO itemStockListDTO = new ItemStockListDTO();
            itemStockListDTO.setCustomsRecordProductId(endorsementItem.getProductId());
            itemStockListDTO.setGoodsName(endorsementItem.getGoodsName());
            itemStockListDTO.setGoodsSeqNo(endorsementItem.getGoodsSeqNo());
            if (StringUtils.isEmpty(itemStockListDTO.getGoodsSeqNo())) {
                itemStockListDTO.setGoodsSeqNo(endorsementItem.getCustomsCallBackSeqNo());
            }
            itemStockListDTO.setSn(old.getSn());
            itemStockListDTO.setBookId(old.getAccountBookId());
            itemStockListDTO.setRealOrderNo(realOrderNo);
            Integer listsCount = itemStockListService.findCountByRealOrderNoAndDataType(realOrderNo, DataTypeNum.HG.getCode());
            if (listsCount == 0) {
                itemStockListDTO.setAbnormalType(AbnormalTypeEnum.ABNORMAL_HG_NOT_EXISTS.getCode());
            }
            if (!Objects.equals(old.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
                InventoryOrderBusinessEnum businessEnum = InventoryOrderBusinessEnum.getEnum(old.getBussinessType());
                if (Objects.equals(businessEnum.getType(), InventoryInOutEnum.IN)) {
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.IN.getCode());
                } else if (Objects.equals(businessEnum.getType(), InventoryInOutEnum.OUT)) {
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.OUT.getCode());
                }
            } else {
                itemStockListDTO.setInOutFlag(InventoryInOutEnum.OUT.getCode());
            }
            if (Objects.nonNull(inventoryOrderInfoTemp)) {
                if (!StringUtils.isEmpty(inventoryOrderInfoTemp.getInAccountBook())) {
                    itemStockListDTO.setInAccountBook(inventoryOrderInfoTemp.getInAccountBook());
                }
                if (!StringUtils.isEmpty(inventoryOrderInfoTemp.getOutAccountBook())) {
                    itemStockListDTO.setOutAccountBook(inventoryOrderInfoTemp.getOutAccountBook());
                }
                if (!StringUtils.isEmpty(inventoryOrderInfoTemp.getAssociatedEndorsementNo())) {
                    if (Objects.equals(itemStockListDTO.getInOutFlag(), InventoryInOutEnum.IN.getCode())) {
                        itemStockListDTO.setAssociatedOutEndorsementNo(inventoryOrderInfoTemp.getAssociatedEndorsementNo());
                    } else if (Objects.equals(itemStockListDTO.getInOutFlag(), InventoryInOutEnum.OUT.getCode())) {
                        itemStockListDTO.setAssociatedInEndorsementNo(inventoryOrderInfoTemp.getAssociatedEndorsementNo());
                    }
                }
            }
            itemStockListDTO.setInveBusinessType(old.getBussinessType());
            itemStockListDTO.setEventQty(endorsementItem.getDeclareUnitQfy().intValue());
            itemStockListDTO.setEventTime(old.getFinishTime());
            itemStockListDTO.setDataType(DataTypeNum.CCS.getCode());
            itemStockLists.add(itemStockListDTO);
        });
        if (!CollectionUtils.isEmpty(itemStockLists) && itemStockLists.size() > 0) {
            log.info("ItemStockListBatchInsert生成料号库存流水JSON：{}", JSON.toJSONString(itemStockLists));
            itemStockListService.batchInsertOrUpdate(itemStockLists);
        }
    }

    //根据核注清单编号更新核注单明细的金二序号
    public boolean updateEndorsementItemSeqNo(List<EndorsementItemDTO> endorsementItemDTOList, String realOrderNo) {
        log.info("updateEndorsementItemSeqNo 核注单号：{}", realOrderNo);
        endorsementItemDTOList = endorsementItemDTOList.stream().filter(d -> StringUtils.isEmpty(d.getGoodsSeqNo())).collect(Collectors.toList());
        //如果都含有金二序号的话返回true,继续执行
        boolean result = true;
        if (Objects.isNull(endorsementItemDTOList) || endorsementItemDTOList.size() == 0) return true;
        for (EndorsementItemDTO endorsementItem : endorsementItemDTOList) {
            if (StringUtils.isNotEmpty(endorsementItem.getCustomsCallBackSeqNo())) {
                log.info("updateEndorsementItemSeqNo 记账金二序号已存在不用更新");
                continue;
            }
            CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByinvtNoAndProductId(realOrderNo, endorsementItem.getProductId());
            if (Objects.isNull(customsBookItemDTO) || StringUtils.isEmpty(customsBookItemDTO.getGoodsSeqNo())) {
                log.info("updateEndorsementItemSeqNo 核注单号：{} 海关备案料号{} 在账册库存中未找到数据,略过处理", realOrderNo, endorsementItem.getProductId());
                result = false;
                continue;
            }
            endorsementItem.setCustomsCallBackSeqNo(customsBookItemDTO.getGoodsSeqNo());
            EndorsementItemDO updateInfo = new EndorsementItemDO();
            updateInfo.setId(endorsementItem.getId());
            updateInfo.setCustomsCallBackSeqNo(endorsementItem.getCustomsCallBackSeqNo());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(updateInfo);
            }
            updateInfo.setUpdateTime(new Date());
            endorsementItemMapper.updateByPrimaryKeySelective(updateInfo);
        }
        return result;
    }


    //批量插入到库存流水
//    public void updateCustomsBookItemTotalInOutQty(List<EndorsementItemDTO> endorsementItemDTOList, EndorsementDTO old) {
//
//        endorsementItemDTOList.forEach(endorsementItem -> {
//            CustomsBookItemDTO customsBookItemDTO = new CustomsBookItemDTO();
//            customsBookItemDTO.setProductId(endorsementItem.getProductId());
//            customsBookItemDTO.setGoodsSeqNo(endorsementItem.getGoodsSeqNo());
//            customsBookItemDTO.setCustomsBookId(old.getAccountBookId());
//
//            log.info("updateCustomsBookItemTotalInOutQty JSON：{}", JSON.toJSONString(customsBookItemDTO));
//            String inOutFlag = "";
//            if (!Objects.equals(old.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
//                InventoryOrderBusinessEnum businessEnum = InventoryOrderBusinessEnum.getEnum(old.getBussinessType());
//                if (Objects.equals(businessEnum.getType(), InventoryInOutEnum.IN)) {
//                    inOutFlag = InventoryInOutEnum.IN.getCode();
//                } else if (Objects.equals(businessEnum.getType(), InventoryInOutEnum.OUT)) {
//                    inOutFlag =  InventoryInOutEnum.OUT.getCode();
//                }
//            } else {
//                inOutFlag =  InventoryInOutEnum.OUT.getCode();
//            }
//            customsBookItemService.updateTotalInOutQty(customsBookItemDTO,inOutFlag,endorsementItem.getDeclareUnitQfy().intValue());
//        });
//
//    }


    public void endorsementFinishNotSecondOutAA(List<EndorsementItemDTO> sumSkuInfoList, EndorsementDTO old, String realNo) {
//        List<EndorsementDTO> endorsementDOList = this.listByInventory(old.getInventoryOrderId());
//        if (endorsementDOList.stream().anyMatch(e -> !e.getStatus().equals(EndorsementOrderStatus.FINISH.getCode()))) {
//            return;
//        }
        this.endorsementFinishNotSecondOut(old, realNo);

        if (EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
            log.info("endorsementFinishNotSecondOutAA 核注单{}已经FINISH 略过不处理", old.getSn());
            return;
        }
        endorsementService.endorsementFinishNotSecondOutAACore(sumSkuInfoList, old, false, realNo);
    }

    /**
     * 核注删除 账册库存加可用
     *
     * @param sumSkuInfoList
     * @param old
     */
    public void endorsementDeleteAA(List<EndorsementItemDTO> sumSkuInfoList, EndorsementDTO old) {
        if (!EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
            log.info("endorsementDeleteAA 核注单{}非服务完成，无法删除 略过不处理", old.getSn());
            return;
        }
        endorsementService.endorsementFinishNotSecondOutAACore(sumSkuInfoList, old, true, old.getRealOrderNo());
    }

    @StockVerify(methodParameters = {0, 1, 2}, changeType = InventoryCalculationTypeEnums.ADD_AVAILABLE, handler = StockEndorsementHandler.class)
    public void endorsementFinishNotSecondOutAACore(List<EndorsementItemDTO> sumSkuInfoList, EndorsementDTO old, Boolean isDelete, String realNo) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        sumSkuInfoList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn()).setCustomsNo(realNo);
            EndorsementBussiness bussinessEnum = EndorsementBussiness.getEnum(old.getBussinessType());
            updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue());
//            删除反核扣 需要通过金二记账序号反差账册库存，已核扣新品通过记账回执记录不重复核扣库存
            if (StringUtils.isEmpty(i.getGoodsSeqNo()) && StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo()) && isDelete) {
                updateInventoryDTO.setGoodsSeqNo(i.getCustomsCallBackSeqNo());
            }
            populateFinishUidto(bussinessEnum, updateInventoryDTO, isDelete);
            if (EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode().equals(old.getOrderType())) {
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.REFUND_INAREA_EXCEL_IMPORT);
            }
            if (updateInventoryDTO.getChangeType() != null) {
                list.add(updateInventoryDTO);
            }
        });
        stockInventoryService.updateInventory(list);
    }

    public void endorsementFinishNotSecondOutUTA(List<EndorsementItemDTO> sumSkuInfoList, EndorsementDTO old, String realNo) {
//        List<EndorsementDTO> endorsementDOList = this.listByInventory(old.getInventoryOrderId());
//        if (endorsementDOList.stream().anyMatch(e -> !e.getStatus().equals(EndorsementOrderStatus.FINISH.getCode()))) {
//            return;
//        }
        this.endorsementFinishNotSecondOut(old, realNo);

        if (EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
            log.info("endorsementFinishNotSecondOutAA 核注单{}已经FINISH 略过不处理", old.getSn());
            return;
        }

        endorsementService.endorsementFinishNotSecondOutUTACore(sumSkuInfoList, old, realNo);
        if (Objects.equals(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode(), old.getBussinessType())) {
            messageSender.sendMsg(old.getId(), "ccs-trackLog-es-endorsement-topic");
        }
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.USED_TO_AVAILABLE, handler = StockEndorsementHandler.class)
    public void endorsementFinishNotSecondOutUTACore(List<EndorsementItemDTO> sumSkuInfoList, EndorsementDTO old, String realNo) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        sumSkuInfoList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn()).setCustomsNo(realNo);
            EndorsementBussiness bussinessEnum = EndorsementBussiness.getEnum(old.getBussinessType());
            updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue());
//            删除反核扣 需要通过金二记账序号反差账册库存，已核扣新品通过记账回执记录不重复核扣库存, 已用转可用都为核扣操作
//            if (StringUtils.isEmpty(i.getGoodsSeqNo()) && StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo()) ) {
//                updateInventoryDTO.setGoodsSeqNo(i.getCustomsCallBackSeqNo());
//            }
            populateFinishUidto(bussinessEnum, updateInventoryDTO, false);
            if (updateInventoryDTO.getChangeType() != null) {
                list.add(updateInventoryDTO);
            }
        });
        stockInventoryService.updateInventory(list);
    }

    public void endorsementFinishNotSecondOutATU(List<EndorsementItemDTO> endorsementItemDTOList, EndorsementDTO old, String realNo) {
//        List<EndorsementDTO> endorsementDOList = this.listByInventory(old.getInventoryOrderId());
//        if (endorsementDOList.stream().anyMatch(e -> !e.getStatus().equals(EndorsementOrderStatus.FINISH.getCode()))) {
//            return;
//        }
        this.endorsementFinishNotSecondOut(old, realNo);
        if (EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
            log.info("endorsementFinishNotSecondOutOTU 核注单{}已经FINISH 略过不处理", old.getSn());
            return;
        }
        endorsementService.endorsementFinishNotSecondOutATUCore(endorsementItemDTOList, old, realNo);
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.AVAILABLE_TO_USED, handler = StockEndorsementHandler.class)
    public void endorsementFinishNotSecondOutATUCore(List<EndorsementItemDTO> endorsementItemDTOList, EndorsementDTO old, String realNo) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        endorsementItemDTOList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn()).setCustomsNo(realNo);
            EndorsementBussiness bussinessEnum = EndorsementBussiness.getEnum(old.getBussinessType());
            updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue());
//            删除反核扣 需要通过金二记账序号反差账册库存，已核扣新品通过记账回执记录不重复核扣库存
//            if (StringUtils.isEmpty(i.getGoodsSeqNo()) && StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo())) {
//                updateInventoryDTO.setGoodsSeqNo(i.getCustomsCallBackSeqNo());
//            }
            populateFinishUidto(bussinessEnum, updateInventoryDTO, false);
            if (updateInventoryDTO.getChangeType() != null) {
                list.add(updateInventoryDTO);
            }
        });
        stockInventoryService.updateInventory(list);
    }

    public void endorsementFinishSecondOutOTU(List<ExportSkuInfo> sumSkuInfoList, EndorsementDTO old, Long id, String realNo, List<ExportItemDTO> exportItemDTOList) {

        // 更新下清单状态
        this.endorsementFinishSecondOut(old, id, exportItemDTOList);

        if (EndorsementOrderStatus.FINISH.getCode().equals(old.getStatus())) {
            log.info("endorsementFinishSecondOutOTU 核注单{}已经FINISH 略过不处理", old.getSn());
            return;
        }
        endorsementService.endorsementFinishSecondOutOTUCore(sumSkuInfoList, old, realNo);
//        /**
//         * 批量
//         */
//        messageSender.sendMsg(old.getId(),"ccs-endorsement-internal-status-topic");
        /**
         * 批量修改核注单内的清单内部流转状态并记录日志
         */
        messageSender.sendMsg(old.getId(), "ccs-trackLog-es-endorsement-topic");
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.OCCUPIED_TO_USED, handler = StockExportHandler.class)
    public void endorsementFinishSecondOutOTUCore(List<ExportSkuInfo> sumSkuInfoList, EndorsementDTO old, String realNo) {
        /**
         * 扣减库存
         */
        List<UpdateInventoryDTO> list = new ArrayList<>();
        Map<Long, CustomsBookItemDTO> bookItemDTOMap = customsBookItemService.findByIdList(sumSkuInfoList.stream().map(ExportSkuInfo::getBookItemId).collect(Collectors.toList()));
        sumSkuInfoList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            // TODO: 2022/8/30 businessNo换成申报单号
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn()).setCustomsNo(realNo);
            EndorsementBussiness bussinessEnum = EndorsementBussiness.getEnum(old.getBussinessType());
            CustomsBookItemDTO customsBookItemDTO = bookItemDTOMap.get(i.getBookItemId());
            if (customsBookItemDTO == null) {
                return;
            }
            updateInventoryDTO.setCustomsBookItemId(i.getBookItemId()).setDeclareUnitQfy(i.getCount())
                    .setProductId(customsBookItemDTO.getProductId()).setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
            populateFinishUidto(bussinessEnum, updateInventoryDTO, false);
            if (updateInventoryDTO.getChangeType() != null) {
                list.add(updateInventoryDTO);
            }
        });
        stockInventoryService.updateInventory(list);
    }

    @Autowired
    private CeEndorsementProducer producer;

    @DubboReference
    private BookCarryoverClient bookCarryoverClient;

    @Override
    public void endorsementFinishNotSecondOut(EndorsementDTO old) {
        this.endorsementFinishNotSecondOut(old, null);
    }

    @Override
    public void endorsementFinishNotSecondOut(EndorsementDTO old, String realNo) {
        List<Long> needUpdateFinishTimeIds = new ArrayList<>();
        List<Long> needUpdateCompleteTimeIds = new ArrayList<>();
        needUpdateFinishTimeIds.add(old.getInventoryOrderId());
        if (StrUtil.isNotBlank(realNo)) {
            InventoryOrderInfoDTO updateInveOrderDTO = new InventoryOrderInfoDTO();
            updateInveOrderDTO.setEndorsementRealOrderNo(realNo);
            updateInveOrderDTO.setId(old.getInventoryOrderId());
            inventoryOrderInfoService.updateInventoryOrderInfoDTO(updateInveOrderDTO);
        }
        inventoryOrderInfoService.updateInventoryOrderInfoStatus(old.getInventoryOrderId(), InventoryOrderEnum.STATUS_FINISH.getCode());
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
        if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode())
                || Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode())) {
            needUpdateCompleteTimeIds.add(inventoryOrderInfoDTO.getId());
        }
        if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())) {
            if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode())) {
                messageSender.sendMsg(inventoryOrderInfoDTO.getId(), "ccs-inventory-transit-finish-topic");
            }
        }
        if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_IN.getCode())) {
            inventoryOrderInfoService.updateTransitOutOrderEndorsementSn(old, inventoryOrderInfoDTO);
        }
        if (Objects.equals(inventoryOrderInfoDTO.getLockStockFlag(), 1)) {
            try {
                inventoryOrderInfoService.releaseLockStockById(inventoryOrderInfoDTO.getId());
            } catch (Exception e) {
                log.error("释放锁库存失败 inventoryOrderId={}", inventoryOrderInfoDTO.getId(), e);
            }
        }
        //分区结转相关处理
        this.carryOverOrderPostProcess(old, inventoryOrderInfoDTO);
        //退货入区更改退货单状态为退货完成
        if (inventoryOrderInfoDTO.getInveBusinessType().equals(InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode())) {
            refundOrderService.updateCustomsStatusByInveSn(inventoryOrderInfoDTO.getInveCustomsSn(), inventoryOrderInfoDTO.getStatus());
            List<InventoryOrderRelationDTO> relationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(old.getInventoryOrderId());
            for (InventoryOrderRelationDTO relationDTO : relationDTOList) {
                List<RefundOrderInfoDto> refundOrderInfoDtoList = refundOrderService.findRefundOrderByMailNo(relationDTO.getRelNo());
                if (!CollectionUtils.isEmpty(refundOrderInfoDtoList)) {
                    RefundOrderInfoDto refundOrderInfoDto = refundOrderInfoDtoList.get(0);
                    orderService.updateOrderInternalStatus(refundOrderInfoDto.getRefDeclareId(), OrderInternalEnum.REFUND_INTO_WAREHOUSE.getCode());
//                    customsInventoryCalloffService.updateCalloffStatusByLogisticsNo(relationDTO.getRelNo(), null, 1,
//                            InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode(), InventoryCalloffTypeEnum.CALLOFF_RETURN.getCode());
                }
            }
        }
        //去除加急排序
        endorsementService.removeInventoryOrderUrgentSort(inventoryOrderInfoDTO);
        // 淘天回告
        this.callbackTaotianFinish(inventoryOrderInfoDTO);
        // 子单处理
        if (Objects.isNull(inventoryOrderInfoDTO.getSubOrderSn())) {
            collaborateOrderFinish(inventoryOrderInfoDTO);
        } else {
            List<String> subSnList = Arrays.asList(inventoryOrderInfoDTO.getSubOrderSn().split(","));
            List<InventoryOrderInfoDTO> subInventoryOrderDTOList = inventoryOrderInfoService.findBySn(subSnList);
            for (InventoryOrderInfoDTO subOrderInfo : subInventoryOrderDTOList) {
                //去除加急标识
                endorsementService.removeInventoryOrderUrgentSort(subOrderInfo);
                //关仓逻辑
                collaborateOrderFinish(subOrderInfo);
                if (InventoryOrderTagEnums.contains(subOrderInfo.getOrderTag(), InventoryOrderTagEnums.CW_INVENTORY)) {
                    // 主单完成，子单回传wms清关完成
                    try {
                        this.callbackWmsCwFinish(subOrderInfo);
                    } catch (Exception e) {
                        log.error("回传wms清关服务完成失败 error={}", e.getMessage(), e);
                    }
                }
                this.callbackTaotianFinish(subOrderInfo);
                //记录需要更新清关完成时间的单子
                if (Objects.equals(subOrderInfo.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode())
                        || Objects.equals(subOrderInfo.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode())) {
                    needUpdateCompleteTimeIds.add(subOrderInfo.getId());
                }
                if (Objects.equals(subOrderInfo.getLockStockFlag(), 1)) {
                    try {
                        inventoryOrderInfoService.releaseLockStockById(subOrderInfo.getId());
                    } catch (Exception e) {
                        log.error("释放锁库存失败 inventoryOrderId={}", subOrderInfo.getId(), e);
                    }
                }
                needUpdateFinishTimeIds.add(subOrderInfo.getId());
            }
        }
        if (!CollectionUtils.isEmpty(needUpdateCompleteTimeIds)) {
            inventoryOrderInfoService.updateCompleteTime(needUpdateCompleteTimeIds, new Date());
        }
        Integer orderTodoTag = inventoryOrderInfoDTO.getOrderTodoTag();
        List<Integer> orderTodoTags = InventoryOrderTodoTagEnums.getOrderTodoTags(orderTodoTag);
        if (orderTodoTags.contains(InventoryOrderTodoTagEnums.CREATE_ENDORSEMENT_OVERDUE.getCode())) {
            orderTodoTag = InventoryOrderTodoTagEnums.remove(orderTodoTag, InventoryOrderTodoTagEnums.CREATE_ENDORSEMENT_OVERDUE);
            inventoryOrderInfoDTO.setOrderTodoTag(orderTodoTag);
            inventoryOrderInfoService.updateOrderTodoTag(inventoryOrderInfoDTO.getId(), orderTodoTag);
        }

        inventoryOrderInfoService.updateFinishTime(needUpdateFinishTimeIds, new Date());
        messageSender.sendMsg(inventoryOrderInfoDTO.getId(), "ccs-inventory-finish-overdue-topic");
        if (Boolean.TRUE.equals(InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.CW_INVENTORY))) {
            try {
                this.callbackWmsCwFinish(inventoryOrderInfoDTO);
            } catch (Exception e) {
                log.error("回传wms清关服务完成失败 error={}", e.getMessage(), e);
            }
        } else {
            //ERP下发状态同步ERP
            endorsementFinishCallBackErp(inventoryOrderInfoDTO);
        }
        inventoryOrderInfoTrackLogService.saveInventoryOrderLog(inventoryOrderInfoDTO.getId(), "服务完成");

        if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.AUTO_TRANSFER)) {
            Long sendId = 0L;
            if (Objects.equals(InventoryInOutEnum.IN.getCode(), inventoryOrderInfoDTO.getInOutFlag())) {
                sendId = inventoryOrderInfoDTO.getId();
            } else if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.SAME_BOOK_NO_TRANSFER)) {
                // 同账册调拨 转出清关单（清关单I）服务完成 发送任务使用关联调入的清关单Id
                InventoryOrderInfoDTO transferInOrderDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedInOrderSn());
                sendId = transferInOrderDTO.getId();
            } else {
                return;
            }
            log.info("全自动调拨 接受已核扣回执 发送异步消息 invOrderId={} sendId={}", inventoryOrderInfoDTO.getId(), sendId);
            messageSender.sendMsg(sendId, "ccs-inventory-auto-transfer-topic");
        }
    }

    public void removeInventoryOrderUrgentSort(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        Integer orderTag = inventoryOrderInfoDTO.getOrderTag();
        List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(orderTag);
        log.debug("removeInventoryOrderUrgentSort orderTags={}", JSON.toJSONString(orderTags));
        if (orderTags.contains(InventoryOrderTagEnums.URGENT_PROCESS.getCode())) {
            inventoryOrderInfoDTO.setUrgentSort(null);
            inventoryOrderInfoService.removeUrgentSort(inventoryOrderInfoDTO.getId());
//            inventoryOrderInfoService.batchChangeUrgentProcessWithoutCheck(Arrays.asList(inventoryOrderInfoDTO.getId()), false);
        }
    }

    private void callbackTaotianFinish(InventoryOrderInfoDTO orderInfoDTO) {
        if (Objects.isNull(orderInfoDTO)) {
            return;
        }
        if (Objects.equals(InventoryOrderChannel.TAO_TIAN.getValue(), orderInfoDTO.getChannel())) {
            // 已经剔除淘天清关单申报业务 不校验了
//            if (Objects.equals(orderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())) {
//                if (!inventoryOrderInfoService.checkTaotian2050Exist(orderInfoDTO)) {
//                    throw new ArgsErrorException("请先回传货物到港");
//                }
//            }
            //淘天回告
            try {
                this.taotianFinishReportProcess(orderInfoDTO);
                inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(orderInfoDTO, "淘天：清关完成（放行）");
            } catch (Exception e) {
                log.error("taotianFinishReportProcess error={}", e.getMessage(), e);
            }
        }
    }

    //分区结转相关处理
    public void carryOverOrderPostProcess(EndorsementDTO old, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        Integer orderTag = inventoryOrderInfoDTO.getOrderTag();
        List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(orderTag);
        if (!CollectionUtils.isEmpty(orderTags) &&
                (orderTags.contains(InventoryOrderTagEnums.AUTO_PART_CARRYOVER.getCode())
                        || orderTags.contains(InventoryOrderTagEnums.TO_B_PART_CARRYOVER.getCode()))) {
            this.callBackWmsFinish(inventoryOrderInfoDTO, old);
            inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "WMS：服务完成回传请求");
            if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode())) {
                log.info("updateCarryOverEndorsementSn carryOverNo={} type={} endorsement={}", inventoryOrderInfoDTO.getUpstreamNo(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode(), old.getRealOrderNo());
                updateCarryOverRealOrderNo(inventoryOrderInfoDTO.getUpstreamNo(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode(), old.getRealOrderNo());
                messageSender.sendMsg(inventoryOrderInfoDTO.getUpstreamNo(), "ccs-carryover-endorsement-topic");
            } else if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode())) {
                log.info("updateCarryOverEndorsementSn carryOverNo={} type={} endorsement={}", inventoryOrderInfoDTO.getUpstreamNo(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode(), old.getRealOrderNo());
                updateCarryOverRealOrderNo(inventoryOrderInfoDTO.getUpstreamNo(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode(), old.getRealOrderNo());
            }
        }
    }

    /**
     * 更新清关单 核注清单编号
     *
     * @param upstreamNo
     * @param businessType
     * @param realOrderNo
     */
    public void updateCarryOverRealOrderNo(String upstreamNo, String businessType, String realOrderNo) {
        Example example = new Example(InventoryOrderInfoDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("upstreamNo", upstreamNo).andEqualTo("inveBusinessType", businessType);
        InventoryOrderInfoDO inventoryOrderInfoDO = new InventoryOrderInfoDO();
        inventoryOrderInfoDO.setAssociatedEndorsementNo(realOrderNo);
        inventoryOrderInfoBaseService.updateByExampleSelective(inventoryOrderInfoDO, example);
    }


    public void callBackWmsFinish(InventoryOrderInfoDTO orderInfoDTO, EndorsementDTO endorsementDTO) {
        BookCarryoverCallbackParam callbackParam = new BookCarryoverCallbackParam();
        callbackParam.setCarryoverCode(orderInfoDTO.getUpstreamNo());
        BookCarryoverCallbackDetail detail = new BookCarryoverCallbackDetail();
        detail.setInventoryOrderSn(orderInfoDTO.getInveCustomsSn());
        detail.setEndorsementSn(endorsementDTO.getSn());
        String businessType = endorsementDTO.getBussinessType();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(businessType);
        if (Objects.nonNull(endorsementBussiness)) {
            detail.setEndorsementType(endorsementBussiness.getDesc());
        }
        detail.setEndorsementStatus(20);
        List<BookCarryoverCallbackDetail> list = Arrays.asList(detail);
        callbackParam.setBookCarryoverCallbackDetailList(list);
        callbackParam.setWarehouseCode(orderInfoDTO.getWmsWarehouseCode());
        bookCarryoverClient.callback(callbackParam);
        ThreadContextUtil.setRequestInfo(JSON.toJSONString(callbackParam));
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @GetMapping("/endorsement/manualDeletedApply")
    public void manualDeletedApply(Long id) {
        if (LongUtil.isNone(id)) {
            log.warn("manualDeletedApply, 参数为空");
            return;
        }
        EndorsementDTO endorsementDTO = this.findById(id);
        if (Objects.isNull(endorsementDTO)) {
            log.error("manualDeletedApply endorsementDTO为空 id={}", id);
            return;
        }
        if (Objects.equals(EndorsementOrderStatus.DELETED.getCode(), endorsementDTO.getStatus())) {
            log.info("manualDeletedApply 核注单已删除");
            return;
        }
        List<String> notAllowList = Arrays.asList(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode(),
                EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode());
        if (notAllowList.contains(endorsementDTO.getBussinessType())) {
            log.error("manualDeletedApply 此业务类型不支持发起手动删除申请 id={} type={}", id, endorsementDTO.getBussinessType());
            throw new ArgsInvalidException("此业务类型不支持发起手动删除申请");
        }
        doDeleteApplyCore(endorsementDTO);

        endorsementTrackLogService.buildStatusAndInfoLog(endorsementDTO.getId(), EndorsementOrderStatus.DELETED, "核注单手动删除申请：已删除");
    }


    @Override
    public void examinedPostProcess(EndorsementDTO endorsementDTO, List<String> information, Boolean isManual) {
        Long inventoryOrderId = endorsementDTO.getInventoryOrderId();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        if (Objects.nonNull(inventoryOrderInfoDTO)) {
            List<Long> needUpdateCompleteTimeIds = new ArrayList<>();
            needUpdateCompleteTimeIds.add(inventoryOrderId);
            inventoryOrderInfoTrackLogService.saveInventoryOrderLog(inventoryOrderInfoDTO, "清关完成(放行)");
            if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_IN.getCode())) {
                inventoryOrderInfoService.updateTransitOutOrderEndorsementSn(endorsementDTO, inventoryOrderInfoDTO);
            }
            //淘天回传
            if (InventoryOrderChannel.TAO_TIAN.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
                if (isManual) {
                    manualExaminedTaotianReportProcess(endorsementDTO, inventoryOrderInfoDTO);
                } else {
                    examinedTaotianReportProcess(endorsementDTO, inventoryOrderInfoDTO);
                }
            }
            if (StringUtil.isNotBlank(inventoryOrderInfoDTO.getSubOrderSn())) {
                List<String> subOrderSnList = Splitter.on(",").splitToList(inventoryOrderInfoDTO.getSubOrderSn());
                List<InventoryOrderInfoDTO> subInvOrderDTOList = inventoryOrderInfoService.findBySn(subOrderSnList);
                subInvOrderDTOList.forEach(i -> {
                    if (InventoryOrderChannel.TAO_TIAN.getValue().equals(i.getChannel())) {
                        if (isManual) {
                            manualExaminedTaotianReportProcess(endorsementDTO, i);
                        } else {
                            examinedTaotianReportProcess(endorsementDTO, i);
                        }
                    }
                    needUpdateCompleteTimeIds.add(i.getId());
                });
            }
            //更新服务完成时间
            inventoryOrderInfoService.updateCompleteTime(needUpdateCompleteTimeIds, new Date());
            if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.AUTO_TRANSFER)) {
                Long sendId = 0L;
                if (Objects.equals(InventoryInOutEnum.IN.getCode(), inventoryOrderInfoDTO.getInOutFlag())) {
                    sendId = inventoryOrderInfoDTO.getId();
                } else if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.SAME_BOOK_NO_TRANSFER)) {
                    // 同账册调拨 转出清关单（清关单I）服务完成 发送任务使用关联调入的清关单Id
                    InventoryOrderInfoDTO transferInOrderDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedInOrderSn());
                    sendId = transferInOrderDTO.getId();
                }
                log.info("全自动调拨 接受未核扣回执 发送异步消息 invOrderId={} sendId={}", inventoryOrderInfoDTO.getId(), sendId);
                messageSender.sendMsg(sendId, "ccs-inventory-auto-transfer-topic");
            }
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
                this.syncERPStatus(inventoryOrderInfoDTO, InventoryOrderEnum.STATUS_COMPLETE.getCode());
            }
        }

        // 维护清单核注状态  未核扣
        this.syncInventoryReviewStatus(endorsementDTO.getId(), InventoryReviewStatus.PASS_NOT_COMPLETED.getValue());
        String message = EndorsementLogUtils.getLogDetailAndRemove();
        String info = String.join("/", information);
        String logInfoPrefix = "";
        if (Boolean.FALSE.equals(isManual)) {
            logInfoPrefix = "核注清单审核回执:";
        }
        endorsementTrackLogService.buildFullLog(endorsementDTO.getId(), EndorsementOrderStatus.EXAMINE, logInfoPrefix + info, message);
    }

    private void manualExaminedTaotianReportProcess(EndorsementDTO endorsementDTO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        String bussinessType = endorsementDTO.getBussinessType();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(bussinessType);
        try {
            switch (endorsementBussiness) {
                case BUSSINESS_ONELINE_IN:
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.CLEARANCE_STARTS);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION, new Date().getTime() + 3000L, 2);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION_START, new Date().getTime() + 6000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION_END, new Date().getTime() + 9000L, 4);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_RELEASE, new Date().getTime() + 12000L, 5);
//                    Thread.sleep(1000L);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.CLEARANCE_COMPLETED, new Date().getTime() + 15000L, 6);
                    break;
                case BUSSINESS_SECTION_IN:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_START);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_CLEARANCE_COMPLETED, new Date().getTime() + 3000L, 2);
                    }
//                    Thread.sleep(1000L);
//                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END);
                    break;
                case BUSSINESS_SECTIONINNER_IN:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_START);
                    }
//                    Thread.sleep(1000L);
//                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END);
                    break;
                case BUSSINESS_SECTION_OUT:
                case BUSINESS_ONELINE_REFUND:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_START);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_START, new Date().getTime() + 3000L, 2);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_END, new Date().getTime() + 6000L, 3);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_COMPLETED, new Date().getTime() + 9000L, 4);
                    }
                    break;
                case BUSSINESS_SECTIONINNER_OUT:
                case BUSSINESS_DESTORY:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_START);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_START, new Date().getTime() + 3000L, 2);
                    }
                    break;
            }
            inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "淘天：手动审核回传");
        } catch (Exception e) {
            log.error("回传淘天失败 error={}", e.getMessage(), e);
        }
    }

    private void examinedTaotianReportProcess(EndorsementDTO endorsementDTO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        String bussinessType = endorsementDTO.getBussinessType();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(bussinessType);
        try {
            switch (endorsementBussiness) {
                case BUSSINESS_ONELINE_IN:
                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION_START, new Date().getTime() + 3000L, 2);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_INSPECTION_END, new Date().getTime() + 6000L, 3);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.PORT_RELEASE, new Date().getTime() + 9000L, 4);
                    Thread.sleep(1000L);
                    taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.CLEARANCE_COMPLETED, new Date().getTime() + 12000L, 4);
                    break;
                case BUSSINESS_SECTION_IN:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_CLEARANCE_COMPLETED);
                    }
//                    Thread.sleep(1000L);
//                    taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.ZONE_ENTRY_DECLARATION_END);
                    break;
                case BUSSINESS_SECTION_OUT:
                case BUSINESS_ONELINE_REFUND:
                    if (!taotianOrderService.checkIsKHZ(inventoryOrderInfoDTO)) {
                        taotianOrderService.buildReportMessage(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.EXIT_DECLARATION_END);
                        taotianOrderService.buildReportMessageWithSpecificTime(inventoryOrderInfoDTO, TaotianClearanceOrderStatusConstants.COMMON_CLEARANCE_COMPLETED, new Date().getTime() + 3000L, 2);
                    }
                    break;
            }
            inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO, "淘天：已审核回传");
        } catch (Exception e) {
            log.error("回传淘天失败 error={}", e.getMessage(), e);
        }
    }


    /**
     * 关仓出库 - 删除申请 - 调整库存
     *
     * @param itemDTOList
     * @param endorsementDTO
     */
    public void endorsementDeleteUTA(List<EndorsementItemDTO> itemDTOList, EndorsementDTO endorsementDTO) {

        log.info("endorsementFinishAndDeleteUTA, itemDTOList={}, EndorsementDTO={}", JSONUtils.toJSONString(itemDTOList), JSONUtils.toJSONString(endorsementDTO));
        if (!EndorsementOrderStatus.FINISH.getCode().equals(endorsementDTO.getStatus())) {
            log.info("endorsementFinishAndDeleteUTA: 仅在核注已完成状态下进行调整库存, status={}", endorsementDTO.getStatus());
            return;
        }
        endorsementService.endorsementFinishAndDeleteUTACore(itemDTOList, endorsementDTO);
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.USED_TO_AVAILABLE, handler = StockEndorsementDeleteHandle.class)
    public void endorsementFinishAndDeleteUTACore(List<EndorsementItemDTO> itemDTOList, EndorsementDTO endorsementDTO) {
        /**
         * 库存退还
         */
        List<UpdateInventoryDTO> list = new ArrayList<>();
        itemDTOList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(endorsementDTO.getAccountBookId()).setBusinessNo(endorsementDTO.getSn())
                    .setCustomsNo(endorsementDTO.getRealOrderNo());
            updateInventoryDTO.setProductId(i.getProductId()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue()).setGoodsSeqNo(i.getGoodsSeqNo());
            EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(endorsementDTO.getBussinessType());
            List<EndorsementBussiness> deleteBusiness = Arrays.asList(EndorsementBussiness.BUSSINESS_SECTION_OUT,
                    EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT, EndorsementBussiness.BUSSINESS_DESTORY, EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING);
            // 删除反核扣业务类型新品序号没有取金二记账回执，核扣业务类型新品不核扣
            if (deleteBusiness.contains(endorsementBussiness)
                    && StringUtils.isEmpty(updateInventoryDTO.getGoodsSeqNo())
                    && StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo())) {
                updateInventoryDTO.setGoodsSeqNo(i.getCustomsCallBackSeqNo());
            }
            switch (endorsementBussiness) {
                case BUSSINESS_SECTION_OUT:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_OUT_DELETE);
                    break;
                case BUSSINESS_SECTIONINNER_OUT:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_INNER_OUT_DELETE);
                    break;
                case BUSSINESS_DESTORY:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.DESTORY_DELETE);
                    break;
                case BUSINESS_SIMPLE_PROCESSING:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SIMPLE_PROCESSING_DELETE);
                    break;
            }
            list.add(updateInventoryDTO);
        });

        stockInventoryService.updateInventory(list);
    }

    /**
     * 关仓入库 - 删除申请 - 调整库存
     *
     * @param itemDTOList
     * @param endorsementDTO
     */
    public void endorsementDeleteRA(List<EndorsementItemDTO> itemDTOList, EndorsementDTO endorsementDTO) {
        log.info("endorsementFinishAndDeleteRA, itemDTOList={}, EndorsementDTO={}", JSONUtils.toJSONString(itemDTOList), JSONUtils.toJSONString(endorsementDTO));
        if (!EndorsementOrderStatus.FINISH.getCode().equals(endorsementDTO.getStatus())) {
            log.info("endorsementFinishAndDeleteUTA: 仅在核注已完成状态下进行调整库存, status={}", endorsementDTO.getStatus());
            return;
        }
        endorsementService.endorsementFinishAndDeleteRACore(itemDTOList, endorsementDTO, endorsementDTO.getRealOrderNo());
    }

    /**
     * 核注完成 可用核减
     *
     * @param itemDTOList
     * @param endorsementDTO
     */
    public void endorsementFinishNotSecondOutRA(List<EndorsementItemDTO> itemDTOList, EndorsementDTO endorsementDTO, String realNo) {
        log.info("endorsementFinishRA, itemDTOList={}, EndorsementDTO={}", JSONUtils.toJSONString(itemDTOList), JSONUtils.toJSONString(endorsementDTO));
        if (EndorsementOrderStatus.FINISH.getCode().equals(endorsementDTO.getStatus())) {
            log.info("endorsementFinishRA: 核注单状态已为服务完成，不做处理");
            return;
        }
        this.endorsementFinishNotSecondOut(endorsementDTO, realNo);

        endorsementService.endorsementFinishAndDeleteRACore(itemDTOList, endorsementDTO, realNo);
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.REDUCE_AVAILABLE, handler = StockEndorsementDeleteHandle.class)
    public void endorsementFinishAndDeleteRACore(List<EndorsementItemDTO> itemDTOList, EndorsementDTO endorsementDTO, String realNo) {
        /**
         * 可用退还
         */
        List<UpdateInventoryDTO> list = new ArrayList<>();
        itemDTOList.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(endorsementDTO.getAccountBookId()).setBusinessNo(endorsementDTO.getSn()).setCustomsNo(realNo);
            updateInventoryDTO.setProductId(i.getProductId()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue()).setGoodsSeqNo(i.getGoodsSeqNo());
            EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(endorsementDTO.getBussinessType());
            // 删除反核扣业务类型新品序号没有取金二记账回执，核扣业务类型新品不核扣
            List<EndorsementBussiness> deleteBusiness = Arrays.asList(EndorsementBussiness.BUSSINESS_SECTION_IN,
                    EndorsementBussiness.BUSSINESS_SECTIONINNER_IN, EndorsementBussiness.BUSSINESS_ONELINE_IN,
                    EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN, EndorsementBussiness.BUSINESS_INVENTORY_PROFIT);
            if (deleteBusiness.contains(endorsementBussiness)
                    && StringUtils.isEmpty(updateInventoryDTO.getGoodsSeqNo())
                    && StringUtils.isNotEmpty(i.getCustomsCallBackSeqNo())) {
                updateInventoryDTO.setGoodsSeqNo(i.getCustomsCallBackSeqNo());
            }
            switch (endorsementBussiness) {
                case BUSSINESS_SECTION_IN:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_IN_DELETE);
                    break;
                case BUSSINESS_SECTIONINNER_IN:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_INNER_IN_DELETE);
                    break;
                case BUSSINESS_ONELINE_IN:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.ONELINE_IN_ENDORSEMENT_DELETE);
                    break;
                case BUSINESS_BONDED_TO_TRADE:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.BONDED_TO_TRADE);
                    break;
                case BUSINESS_ONELINE_REFUND:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.ONELINE_REFUND);
                    break;
                case BUSINESS_SUBSEQUENT_TAX:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SUBSEQUENCE_TAX);
                    break;
                case BUSINESS_BONDED_ONELINE_IN:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.BONDED_ONELINE_IN_DELETE);
                    break;
                case BUSINESS_INVENTORY_PROFIT:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.INVENTORY_PROFIT_DELETE);
                    break;
                case BUSINESS_RANDOM_INSPECTION_DECLARATION:
                    updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.RANDOM_INSPECTION_DECLARATION);
                    break;
                case BUSSINESS_SECONDE_OUT:
                    if (Objects.equals(endorsementDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
                        updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SECONDE_OUT_EXCEL_IMPORT);
                    }
                    break;
            }
            list.add(updateInventoryDTO);
        });

        stockInventoryService.updateInventory(list);
    }

    @Override
    public List<OneLineStatisticsResVO> statisticsOneLineReport(Long startTime, Long endTime, List<Long> declareCompanyIdList) {
        List<OneLineStatisticsResVO> result = new ArrayList<>();
        if (LongUtil.isNone(startTime) || LongUtil.isNone(endTime)) {
            log.warn("oneLineStaticReport - 参数为空");
            return result;
        }
        //海关回执 -- 通过已核扣 & 通过未核扣
        List<String> customsStatus = Arrays.asList(EndorsementCustomsStatus.INV201_YUKEKOU.getCode(), EndorsementCustomsStatus.INV201_YIHEKOU.getCode());
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", EndorsementOrderStatus.FINISH.getCode());
        criteria.andEqualTo("deleted", false);
        criteria.andEqualTo("bussinessType", EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode());
        criteria.andIn("customsStatus", customsStatus);
        criteria.andGreaterThanOrEqualTo("finishTime", new Date(startTime));
        criteria.andLessThanOrEqualTo("finishTime", new Date(endTime));
        if (!CollectionUtils.isEmpty(declareCompanyIdList)) {
            criteria.andIn("declareCompanyId", declareCompanyIdList);
        }
        List<EndorsementDO> endorsementDOS = endorsementMapper.selectByExample(example);
        List<Long> inveOrderIdList = endorsementDOS.stream().map(EndorsementDO::getInventoryOrderId).collect(Collectors.toList());
        // （清关单id 核注单DO） map
        Map<Long, EndorsementDO> map = endorsementDOS.stream().collect(Collectors.toMap(EndorsementDO::getInventoryOrderId, Function.identity()));
        List<InventoryOrderInfoDTO> inventoryOrderInfoDTOList = inventoryOrderInfoService.findById(inveOrderIdList);
        // 拼接清关单表体商品信息
        inventoryOrderInfoDTOList.forEach(dto -> {
            OneLineStatisticsResVO resVO = new OneLineStatisticsResVO();
            EndorsementDO endorsementDO = map.get(dto.getId());
            BeanUtils.copyProperties(endorsementDO, resVO);
            resVO.setCustomsEntryNo(dto.getCustomsEntryNo());
            resVO.setDeclareCompanyName(baseDataService.getCompanyDTOById(resVO.getDeclareCompanyId()).getName());
            List<InventoryOrderItemDTO> listItems = inventoryOrderInfoService.findListByInvenOrderId(dto.getId());
            List<OneLineStatisticsResVO.OneLineItemDetail> detailList = new ArrayList<>();
            if (!CollectionUtils.isEmpty(listItems)) {
                listItems.forEach(item -> {
                    OneLineStatisticsResVO.OneLineItemDetail detail = new OneLineStatisticsResVO.OneLineItemDetail();
                    BeanUtils.copyProperties(item, detail);
                    detailList.add(detail);
                });
            }
            resVO.setItemDetails(detailList);
            result.add(resVO);
        });
        return result;
    }

    @Override
    public List<OrderEndorsementCheckResult.OrderEndorsementCheckDTO> getOrderEndorsementCheckList(Date startTime, Date endTime) {
        List<OrderEndorsementCheckResult.OrderEndorsementCheckDTO> result = new ArrayList<>();
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andGreaterThanOrEqualTo("finishTime", startTime);
        criteria.andLessThanOrEqualTo("finishTime", endTime);
        criteria.andEqualTo("status", "FINISH");
        List<EndorsementDO> endorsementDOList = endorsementMapper.selectByExample(example);
        endorsementDOList.forEach(endorsementDO -> {
            OrderEndorsementCheckResult.OrderEndorsementCheckDTO orderEndorsementCheckDTO = new OrderEndorsementCheckResult.OrderEndorsementCheckDTO();
            orderEndorsementCheckDTO.setSn(endorsementDO.getSn());
            orderEndorsementCheckDTO.setRealOrderNo(endorsementDO.getRealOrderNo());
            orderEndorsementCheckDTO.setCustomsStatus(endorsementDO.getCustomsStatus());
            orderEndorsementCheckDTO.setStatus(endorsementDO.getStatus());
            orderEndorsementCheckDTO.setBussinessType(endorsementDO.getBussinessType());
            CompanyDTO companyDTO = companyService.findById(endorsementDO.getDeclareCompanyId());
            orderEndorsementCheckDTO.setCompanyName(companyDTO.getName());
            orderEndorsementCheckDTO.setCompanyCode(companyDTO.getCode());
            result.add(orderEndorsementCheckDTO);
        });
        return result;
    }

    private void collaborateOrderFinish(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        //修改协同单状态 --服务完成
        if (InventoryOrderUtils.checkTwoStepAndDeclareWay(inventoryOrderInfoDTO)) {
            //先理后报
            int diffQty = detailsService.updateDeclareQtyByInveSn(inventoryOrderInfoDTO.getInveCustomsSn());
            //根据差异数量判断
            if (Objects.equals(diffQty, 0)) {
                collaborateOrderService.updateCollaborateOrderStatus(inventoryOrderInfoDTO.getInveCustomsSn(), CollaborateStatus.END);
            } else {
                collaborateOrderService.updateCollaborateOrderStatus(inventoryOrderInfoDTO.getInveCustomsSn(), CollaborateStatus.END_WAIT_VERIFY);
            }
        } else {
            //修改协同单状态，详情最终申报数量
            collaborateOrderService.updateCollaborateOrderStatus(inventoryOrderInfoDTO.getInveCustomsSn(), CollaborateStatus.SERVE_FINISH);
            detailsService.updateDeclareQtyByInveId(inventoryOrderInfoDTO.getId());
        }
    }

    //ERP下发状态同步ERP
    private void endorsementFinishCallBackErp(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
            log.info("清关单回传ERP sn={}", inventoryOrderInfoDTO.getInveCustomsSn());
            if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode())) {
                //中转主单服务完成时 不回传erp
                log.info("中转主清关单服务完成不回传erp, 清关单号={}", inventoryOrderInfoDTO.getInveCustomsSn());
                return;
            }
            syncERPStatus(inventoryOrderInfoDTO, InventoryOrderEnum.STATUS_FINISH.getCode());
            inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO.getId(), "ERP:服务完成回传请求");
        } else if (inventoryOrderInfoDTO.getSubOrderSn() != null) {
            //清关单子单回传erp
            String subOrderSn = inventoryOrderInfoDTO.getSubOrderSn();
            List<String> subSnList = Arrays.asList(subOrderSn.split(","));
            if (!CollectionUtils.isEmpty(subSnList)) {
                subSnList.forEach(s -> {
                    InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findBySn(s);
                    if (InventoryOrderChannel.LOGISTICS.getValue().equals(infoDTO.getChannel())) {
                        log.info("清关单子单回传ERP sn={}", infoDTO.getInveCustomsSn());
                        syncERPStatus(infoDTO, InventoryOrderEnum.STATUS_FINISH.getCode());
                        inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO.getId(), "ERP:服务完成回传请求");
                    }
                });
            }
        } else if (Objects.nonNull(inventoryOrderInfoDTO.getAssociatedTransitOrderSn())) {
            //中转调出单子服务完成 主单回传erp
            if (!Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_OUT.getCode())) {
                log.info("中转调入清关单服务完成不回传erp, 清关单号={}", inventoryOrderInfoDTO.getInveCustomsSn());
                return;
            }
            //当前订单为中转调出的单子
            log.info("中转调出清关单服务完成回传erp, 清关单号={}", inventoryOrderInfoDTO.getInveCustomsSn());
            InventoryOrderInfoDTO transitMaster = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedTransitOrderSn());
            syncERPStatus(transitMaster, InventoryOrderEnum.STATUS_FINISH.getCode());
            inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderInfoDTO.getId(), "ERP:服务完成回传请求");
        }
    }

    /**
     * 入库单直接调这个
     *
     * @param inventoryOrderInfoDTO
     */
    private void syncERPStatus(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        this.syncERPStatus(inventoryOrderInfoDTO, null);
    }

    /**
     * 手动补偿回传ERP状态
     *
     * @param sn
     * @param action
     */
    public void retryCallBackERPStatus(String sn, String action) {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findBySn(sn);
        log.info("retryCallBackERPStatus sn={} infoDTO={} action={}", sn, JSON.toJSONString(inventoryOrderInfoDTO), action);
        if (StringUtils.isEmpty(action)) {
            action = InventoryOrderEnum.STATUS_FINISH.getCode();
        }
        this.syncERPStatus(inventoryOrderInfoDTO, action);
    }

    @Override
    public void autoCarryOverEndorsementNotify(Integer timeOut) {
        LocalDateTime dateTime = LocalDateTime.now().minusHours(timeOut);
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String formattedDateTime = dateTime.format(formatter);
        log.info("autoCarryOverEndorsementNotify timeout个小时前的日期时间为: " + formattedDateTime);
        List<Long> timeOutEndorsement = endorsementMapper.findAutoCarryOverTimeOutEndorsement(formattedDateTime, InventoryOrderTagEnums.AUTO_PART_CARRYOVER.getCode());
        if (CollectionUtils.isEmpty(timeOutEndorsement)) {
            return;
        }
        List<EndorsementDTO> endorsementDTOList = this.findByIdList(timeOutEndorsement);
        if (CollectionUtils.isEmpty(endorsementDTOList)) {
            return;
        }
        List<Long> inventoryOrderInfoIdList = endorsementDTOList.stream().map(EndorsementDTO::getInventoryOrderId).distinct().collect(Collectors.toList());
        List<InventoryOrderInfoDTO> inventoryOrderInfoDTOS = inventoryOrderInfoService.findById(inventoryOrderInfoIdList);
        if (CollectionUtils.isEmpty(inventoryOrderInfoDTOS)) {
            return;
        }
        Map<Long, InventoryOrderInfoDTO> inventoryOrderInfoDTOMap = inventoryOrderInfoDTOS.stream().collect(Collectors.toMap(InventoryOrderInfoDTO::getId, Function.identity(), (v1, v2) -> v1));
        Map<EndorsementDTO, InventoryOrderInfoDTO> endorsementDTOInventoryOrderInfoDTOMap = endorsementDTOList.stream()
                .filter(endorsementDTO -> inventoryOrderInfoDTOMap.containsKey(endorsementDTO.getInventoryOrderId()))
                .collect(Collectors.toMap(Function.identity(), endorsementDTO -> inventoryOrderInfoDTOMap.get(endorsementDTO.getInventoryOrderId())));
        log.debug("endorsementDTOInventoryOrderInfoDTOMap = {}", JSON.toJSONString(endorsementDTOInventoryOrderInfoDTOMap));
        for (Map.Entry<EndorsementDTO, InventoryOrderInfoDTO> next : endorsementDTOInventoryOrderInfoDTOMap.entrySet()) {
            EndorsementDTO endorsementDTO = next.getKey();
            InventoryOrderInfoDTO inventoryOrderInfoDTO = next.getValue();
            notifyAutoCarryOverException(endorsementDTO, inventoryOrderInfoDTO, 1);
        }
    }

    @Override
    public Map<String, Integer> countPagingStatus(EndorsementSearch search) {
        Map<String, Integer> map = new LinkedHashMap<>();
        for (EndorsementOrderStatus status : EndorsementOrderStatus.values()) {
            if (status.equals(EndorsementOrderStatus.NULL)) {
                continue;
            }
            search.setStatus(status.getCode());
            Example example = this.getPagingExample(search);
            int count = endorsementMapper.selectCountByExample(example);
            map.put(status.getCode(), count);
        }
        return map;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletedApply(List<Long> idList) {
        if (CollUtil.isEmpty(idList)) {
            throw new ArgsInvalidException("id不不能为空");
        }
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(idList);
        for (EndorsementDTO old : endorsementDTOList) {
            // 删除申请校验
            if (!EndorsementOrderStatus.FINISH.getCode().equalsIgnoreCase(old.getStatus())
                    && !EndorsementOrderStatus.EXAMINE.getCode().equalsIgnoreCase(old.getStatus())) {
                throw new ArgsInvalidException("核注单[" + old.getSn() + "]状态无法操作删除申请");
            }
            List<String> notAllowList = Arrays.asList(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode(),
                    EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode());
            if (notAllowList.contains(old.getBussinessType())) {
                throw new ArgsInvalidException("此业务类型不支持发起手动删除申请");
            }
            if (StringUtils.isBlank(old.getPreOrderNo())) {
                throw new ArgsInvalidException("预录入核注编号不能为空");
            }
            if (StringUtils.isBlank(old.getRealOrderNo())) {
                throw new ArgsInvalidException("核注清单编号不能为空");
            }
            // 限制等5分钟推送删除申请
            String lockKey = "ccs:endorsement:deleteApply:" + old.getId();
            log.info("核注单删除申请，锁key:{}", lockKey);
            try {
                if (!RedissLockUtil.tryLock(lockKey, TimeUnit.MINUTES, 5, 6)) {
                    log.warn("核注单{}已发起删除申请，请5分钟后再次提交", old.getSn());
                    throw new ArgsInvalidException("核注单" + old.getSn() + "已发起删除申请，请5分钟后再次提交");
                }
                // Step::推送
                Inv101MessageRequest request = new Inv101MessageRequest();
                request.setEndorsementOrderNo(old.getSn());
                request.setPreEndorsementOrderNo(StringUtils.isNotBlank(old.getPreOrderNo()) ? old.getPreOrderNo() : "");
                request.setRealEndorsementOrderNo(StringUtils.isNotBlank(old.getRealOrderNo()) ? old.getRealOrderNo() : "");
                request.setBussinessType(old.getBussinessType());
                this.getInv101MessageRequest(old, 1, 3);
//                request = inv101MessageRequestBuilder.buildRequestByInvtOrder(old, 1, 3);
//                customsSupport.ieEndorsementHZ(request);
            } finally {
                try {
                    RedissLockUtil.unlock(lockKey);
                } catch (Exception ex) {
                    log.warn("解锁异常，可能是根本就没有加到锁就解锁了");
                }
            }
            String message = EndorsementLogUtils.getLogDetailAndRemove();
            endorsementTrackLogService.buildFullLog(old.getId(), EndorsementOrderStatus.getEnum(old.getStatus()), "核注清单删除请求报文提交", message);
        }


    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletedApplyReceive(Long id) {
        EndorsementDTO endorsementDTO = findById(id);
        if (Objects.equals(endorsementDTO.getStatus(), EndorsementOrderStatus.DELETED.getCode())) {
            log.info("核注单{}已删除，回执不处理", endorsementDTO.getSn());
            return;
        }
        doDeleteApplyCore(endorsementDTO);
        try {
            String message = EndorsementLogUtils.getLogDetailAndRemove();
            endorsementTrackLogService.buildFullLog(endorsementDTO.getId(), EndorsementOrderStatus.DELETED, "核注单删除申请成功", message);
        } catch (Exception e) {
            log.error("buildFullLog error={}", e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public void doDeleteApplyCore(EndorsementDTO endorsementDTO) {
        List<EndorsementItemDTO> endorsementItemDTOS = this.listItemById(endorsementDTO.getId());
        if (Boolean.TRUE.equals(endorsementDTO.getStockChangeEnable())) {
            if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode())
                    || Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode())
                    || Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode())
                    || Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_DESTORY.getCode())
            ) {
                log.info("关仓出库单取消：核注单号={}，业务类型={}", endorsementDTO.getSn(), endorsementDTO.getBussinessType());
                if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode())) {
                    List<EndorsementItemGoodsDTO> endorsementItemGoodsDTOS = this.listItemGoodsById(endorsementDTO.getId());
                    endorsementService.endorsementDeleteUTA(ConvertUtil.listConvert(endorsementItemGoodsDTOS, EndorsementItemDTO.class), endorsementDTO);
                } else {
                    endorsementService.endorsementDeleteUTA(endorsementItemDTOS, endorsementDTO);
                }
            }
            if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECTION_IN.getCode())
                    || Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode())
                    || Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode())
            ) {
                log.info("关仓入库单取消：核注单号={}，业务类型={}", endorsementDTO.getSn(), endorsementDTO.getBussinessType());
                endorsementService.endorsementDeleteRA(endorsementItemDTOS, endorsementDTO);
            }
            if (EndorsementBussiness.BUSINESS_ONELINE_REFUND.getCode().equals(endorsementDTO.getBussinessType())
                    || EndorsementBussiness.BUSINESS_BONDED_TO_TRADE.getCode().equals(endorsementDTO.getBussinessType())
                    || EndorsementBussiness.BUSINESS_SUBSEQUENT_TAX.getCode().equals(endorsementDTO.getBussinessType())) {
                log.info("出库取消：核注单号={}，业务类型={}", endorsementDTO.getSn(), endorsementDTO.getBussinessType());
                endorsementService.endorsementDeleteAA(endorsementItemDTOS, endorsementDTO);
            }
            //重置"核扣库存" （不需要重置核扣库存，删除库存回滚前直接根据状态校验住）
//            EndorsementDO endorsementDO = new EndorsementDO();
//            endorsementDO.setStockChangeEnable(false);
//            endorsementDO.setId(endorsementDTO.getId());
//            endorsementMapper.updateByPrimaryKeySelective(endorsementDO);
        }
        // 关联清关单后续处理
        inventoryOrderInfoService.doEndorsementDeletedPostProcess(endorsementDTO.getInventoryOrderId());

        //更新核注单数据
        this.updateStatus(endorsementDTO.getId(), EndorsementOrderStatus.DELETED.getCode());
        //同步清单 核注状态
        this.syncInventoryReviewStatus(endorsementDTO.getId(), InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
    }

    @Override
    public void updatePreNoAndSyncEs(Long id, String preOrderNo) {
        EndorsementDTO byPreNo = findByPreNo(preOrderNo);
        if (Objects.nonNull(byPreNo)) {
            throw new ArgsInvalidException("预录入核注编号已存在");
        }
        this.updatePreNo(id, preOrderNo);
        EndorsementDTO endorsementDTO = this.findById(id);
        try {
            orderSnTenantService.savePreNo(endorsementDTO.getSn(), preOrderNo);
        } catch (Exception e) {
            log.error("updatePreNoAndSyncEs sync error={}", e.getMessage(), e);
        }
    }

    @Override
    public void updateItemByPrimaryKeySelective(EndorsementItemDTO template) {
        if (Objects.isNull(template) || Objects.isNull(template.getId())) {
            return;
        }
        EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
        BeanUtils.copyProperties(template, endorsementItemDO);
        endorsementItemMapper.updateByPrimaryKeySelective(endorsementItemDO);
    }


    /**
     * 出库单使用这个
     *
     * @param inventoryOrderInfoDTO
     * @param action
     */
    private void syncERPStatus(InventoryOrderInfoDTO inventoryOrderInfoDTO, String action) {

        // 是否关仓系统发送
        boolean isCollaborateAndReadyOrder = erpIReadyOrderRpcFacadeService.isCollaborateAndReadyOrder(inventoryOrderInfoDTO.getCollaborateFlag(), inventoryOrderInfoDTO.getChannelBusinessType());
        boolean isCollaborateAndDistribution = erpIDistributionOrderRpcFacadeService.isCollaborateAndDistribution(inventoryOrderInfoDTO.getCollaborateFlag(), inventoryOrderInfoDTO.getChannelBusinessType());
        if (isCollaborateAndReadyOrder || isCollaborateAndDistribution) {
            log.info("清关单号={}，关仓协同回传清关完成状态给ERP", inventoryOrderInfoDTO.getInveCustomsSn());
            try {
                if (isCollaborateAndReadyOrder) {
                    //入库单
                    erpIReadyOrderRpcFacadeService.inventoryOrderCompleteCallbackErp(inventoryOrderInfoDTO);
                }
                if (isCollaborateAndDistribution) {
                    //出库单
                    erpIDistributionOrderRpcFacadeService.inventoryOrderCompleteCallbackErp(inventoryOrderInfoDTO, action);
                }
            } catch (Exception ex) {
                log.error("清关单号={}，关仓协同回传清关完成状态ERP异常={}", inventoryOrderInfoDTO.getInveCustomsSn(), ex.getMessage(), ex);
            }
        } else {
            InvenorderStatusDTO dto = new InvenorderStatusDTO();
            dto.setStatus(InventoryOrderEnum.STATUS_COMPLETE.getCode());
            dto.setInveCustomsSn(inventoryOrderInfoDTO.getInveCustomsSn());
            dto.setInveBusinessType(inventoryOrderInfoDTO.getInveBusinessType());
            if (StringUtils.isNotEmpty(inventoryOrderInfoDTO.getChannelBusinessType()) && "Distribution".equals(inventoryOrderInfoDTO.getChannelBusinessType())) {
                List<InventoryOrderTallyReportDTO> tallyReportDTOList = inventoryOrderTallyReportService.findListByInveOrderId(inventoryOrderInfoDTO.getId());
                if (!CollectionUtils.isEmpty(tallyReportDTOList)) {
                    dto.setOutBoundList(tallyReportDTOList.stream().map(InventoryOrderTallyReportDTO::getOutBoundNo).collect(Collectors.toList()));
                }
            }
            Long finishTime = null;
            InventoryOrderInfoDTO updatedOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderInfoDTO.getId());
            if (Objects.equals(updatedOrderInfoDTO.getStatus(), InventoryOrderEnum.STATUS_FINISH.getCode())) {
                finishTime = Objects.nonNull(updatedOrderInfoDTO.getInventoryFinishTime()) ? updatedOrderInfoDTO.getInventoryFinishTime().getTime() : System.currentTimeMillis();
            } else if (Objects.equals(updatedOrderInfoDTO.getStatus(), InventoryOrderEnum.STATUS_COMPLETE.getCode())) {
                finishTime = Objects.nonNull(updatedOrderInfoDTO.getInventoryCompleteTime()) ? updatedOrderInfoDTO.getInventoryCompleteTime().getTime() : System.currentTimeMillis();
            }
            dto.setFinishTime(finishTime);
            log.info("清关单号={} dto={}，同步清关完成状态给ERP", inventoryOrderInfoDTO.getInveCustomsSn(), JSON.toJSONString(dto));
            inventorderStatusCallbackProducer.send(dto);
        }
    }


    private void endorsementFinishSecondOut(EndorsementDTO old, Long id, List<ExportItemDTO> exportItemDTOList) {

        Date finishTime = new Date();
        List<EndorsementDTO> endorsementDOList = this.listByExport(old.getExportOrderId());
        if (endorsementDOList.stream().allMatch(e -> e.getStatus().equals(EndorsementOrderStatus.FINISH.getCode()))) {
            ExportOrderDO exportOrderDO = new ExportOrderDO();
            exportOrderDO.setId(old.getExportOrderId());
            exportOrderDO.setStatus(ExportOrderStatus.FINISH.getValue());
            exportOrderDO.setFinishTime(finishTime);
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setUpdateBy(exportOrderDO);
            }
            exportOrderDO.setUpdateTime(finishTime);
            exportOrderMapper.updateByPrimaryKeySelective(exportOrderDO);
        }

        //更新清单出区状态为出区
        List<String> customsInventorySnList = exportItemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).distinct().collect(Collectors.toList());
        List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.findBySnList(customsInventorySnList);
        customsInventoryService.updateExitRegionStatusBySn(customsInventoryDTOList, 1, finishTime);
        //同步更新取消单的出区状态
//        customsInventoryCalloffService.updateExitRegionStatus(customsInventoryDTO.getOrderId(), 1);
        customsInventoryCalloffService.endorsementFinishUpdateExitRegionStatus(customsInventoryDTOList);
//        Map<String, CustomsInventoryDTO> snInventoryMap = customsInventoryDTOList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getSn, Function.identity(), (v1, v2) -> v1));
        List<String> orderSnList = customsInventoryDTOList.stream().map(CustomsInventoryDTO::getOrderSn).distinct().collect(Collectors.toList());
        //更新内部流转状态
        if (!CollectionUtils.isEmpty(orderSnList)) {
            orderService.updateOrderInternalStatusBySnList(orderSnList, OrderInternalEnum.INVENTORY_DECLARE_SUCCESS.getCode());
        }
    }


    private void populateFinishUidto(EndorsementBussiness bussinessEnum, UpdateInventoryDTO updateInventoryDTO, Boolean isDelete) {
        switch (bussinessEnum) {
            case BUSSINESS_ONELINE_IN:
                //一线入境核注完成 增加账册库存 减少在途库存 增加可用库存
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.ONELINE_IN_ENDORSEMENT_FINISH);
                break;
            case BUSSINESS_SECTION_IN:
                // 区间/区内流转入 结转入核注完成 增加账册库存 减少在途库存 增加可用库存
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_IN_FINISH);
                break;
            case BUSSINESS_SECTIONINNER_IN:
                // 区间/区内流转入 结转入核注完成 增加账册库存 减少在途库存 增加可用库存
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_INNER_IN_FINISH);
                break;
            case BUSSINESS_SECTION_OUT:
                // 区间/区内流转出 结转出核注完成
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_OUT_FINISH);
                break;
            case BUSSINESS_SECTIONINNER_OUT:
                // 区间/区内流转出 结转出核注完成
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.NEW_SECTION_INNER_OUT_FINISH);
                break;
            case BUSSINESS_REFUND_INAREA:
                //退货入区完成 减少已用库存 增加可用库
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.REFUND_INAREA);
                break;
            case BUSSINESS_SECONDE_OUT:
                // 二线出区
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SECOND_OUT_FINISH);
                break;
            case BUSSINESS_DESTORY:
                // 销毁
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.DESTORY_FINISH);
                break;
            case BUSINESS_BONDED_TO_TRADE:
                //保税物流转大贸
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.BONDED_TO_TRADE_DELETE : InventoryChangeTypeEnums.BONDED_TO_TRADE);
                break;
            case BUSINESS_ONELINE_REFUND:
                //一线退运
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.ONELINE_REFUND_DELETE : InventoryChangeTypeEnums.ONELINE_REFUND);
                break;
            case BUSINESS_SUBSEQUENT_TAX:
                //后续补税
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.SUBSEQUENCE_TAX_DELETE : InventoryChangeTypeEnums.SUBSEQUENCE_TAX);
                break;
            case BUSINESS_BONDED_ONELINE_IN:
                //保税物流一线入境
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.BONDED_ONELINE_IN_DELETE : InventoryChangeTypeEnums.BONDED_ONELINE_IN);
                break;
            case BUSINESS_INVENTORY_PROFIT:
                //盘盈
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.INVENTORY_PROFIT_DELETE : InventoryChangeTypeEnums.INVENTORY_PROFIT);
                break;
            case BUSINESS_RANDOM_INSPECTION_DECLARATION:
                //抽检申报
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.RANDOM_INSPECTION_DECLARATION_DELETE : InventoryChangeTypeEnums.RANDOM_INSPECTION_DECLARATION);
                break;
            case BUSINESS_SIMPLE_PROCESSING:
                //抽检申报
                updateInventoryDTO.setChangeType((Boolean.TRUE.equals(isDelete)) ?
                        InventoryChangeTypeEnums.SIMPLE_PROCESSING_DELETE : InventoryChangeTypeEnums.SIMPLE_PROCESSING);
                break;
            default:
                break;
        }
    }

    private ItemRegionLogSubmit buildItemRegionLogSubmit(CustomsInventoryItemDTO inventoryItemDTO, EndorsementDTO endorsementDTO, CustomsInventoryDTO customsInventoryDTO) {
        ItemRegionLogSubmit submit = new ItemRegionLogSubmit();
        submit.setId(sequenceService.generateId());
        submit.setCustomsInventoryItemId(inventoryItemDTO.getId());
        submit.setCustomsInventoryId(inventoryItemDTO.getCustomsInventoryId());
        submit.setInventoryNo(customsInventoryDTO.getInventoryNo());
        submit.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
        submit.setEndorsementId(endorsementDTO.getId());
        submit.setEndorsementRealNo(endorsementDTO.getRealOrderNo());
        submit.setEndorsementFinishTime(endorsementDTO.getFinishTime());
        submit.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
        submit.setInventoryCreateTime(customsInventoryDTO.getCreateTime());
        submit.setCustomsBookId(customsInventoryDTO.getAccountBookId());
        submit.setBookItemId(inventoryItemDTO.getBookItemId());
        submit.setItemNo(inventoryItemDTO.getItemNo());
        submit.setItemName(inventoryItemDTO.getItemName());
        submit.setCount(inventoryItemDTO.getCount());
        submit.setUnitPrice(inventoryItemDTO.getUnitPrice());
        return submit;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fillItemByIdStockVerify(List<InventoryOrderItemDTO> itemDTOS, EndorsementDTO old, Long id, String realNo) {
        fillItemById(id, realNo);
        endorsementService.fillItemByIdStockVerifyCore(itemDTOS, old);
    }

    @StockVerify(methodParameters = {0, 1}, changeType = InventoryCalculationTypeEnums.ADD_OCCUPATION, handler = StockEndorsementInventoryHandler.class)
    public void fillItemByIdStockVerifyCore(List<InventoryOrderItemDTO> itemDTOS, EndorsementDTO old) {
        List<UpdateInventoryDTO> list = new ArrayList<>();
        itemDTOS.forEach(i -> {
            UpdateInventoryDTO updateInventoryDTO = new UpdateInventoryDTO();
            updateInventoryDTO.setCustomsBookId(old.getAccountBookId()).setBusinessNo(old.getSn());
            if (EndorsementBussiness.BUSSINESS_DESTORY.getCode().equals(old.getBussinessType())) {
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.DESTORY_APPLY);
            } else {
                updateInventoryDTO.setChangeType(InventoryChangeTypeEnums.SECTION_OUT_PUSH);
            }
            updateInventoryDTO.setProductId(i.getProductId()).setGoodsSeqNo(i.getGoodsSeqNo()).setDeclareUnitQfy(i.getDeclareUnitQfy().intValue());
            list.add(updateInventoryDTO);
        });
        stockInventoryService.updateInventory(list);
    }

    @Override
    @Transactional
    public void fillItemById(Long id, String realNo) {
        EndorsementDTO old = this.findById(id);
        if (!old.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())
                && !old.getStatus().equals(EndorsementOrderStatus.STORAGE_EXCEPTION.getCode())
        ) {
            return; // 仅申报中转换至审核通过的过程，允许固化表体信息
        }
        if (!Objects.equals(old.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
            endorsementItemDO.setEndorsementId(id);
            endorsementItemMapper.delete(endorsementItemDO);
            // 填充子项
            this.saveEndorsementItem(old);
        }

        EndorsementOrderStatus status = EndorsementOrderStatus.EXAMINE;
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setRealOrderNo(realNo);
        example.setStatus(status.getCode());
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
        // 维护下清关单状态为，清关完成即核注已审核，(入区才修改)
        Long inventoryOrderId = old.getInventoryOrderId();
        if (inventoryOrderId != null) {
            inventoryOrderInfoService.updateStatusByEndorsementStatus(inventoryOrderId, status.getCode());
            // 维护清关单 核注清单编号
            InventoryOrderInfoDTO inventoryOrderInfoDTO = new InventoryOrderInfoDTO();
            inventoryOrderInfoDTO.setId(inventoryOrderId);
            inventoryOrderInfoDTO.setEndorsementRealOrderNo(realNo);
            inventoryOrderInfoService.updateInventoryOrderInfoDTO(inventoryOrderInfoDTO);
            //修改协同单
            InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
            if (Objects.isNull(infoDTO.getSubOrderSn())) {
                collaborateOrderService.updateCollaborateOrderStatus(infoDTO.getInveCustomsSn(), CollaborateStatus.CUSTOMS_FINISH);
            } else {
                List<String> subSnList = Arrays.asList(infoDTO.getSubOrderSn().split(","));
                subSnList.forEach(s -> collaborateOrderService.updateCollaborateOrderStatus(s, CollaborateStatus.CUSTOMS_FINISH));
            }
            // 回传下ERP
            try {
                if (erpIReadyOrderRpcFacadeService.isCollaborateAndReadyOrder(infoDTO.getCollaborateFlag(), infoDTO.getInveBusinessType())) {
                    //入库单
                    erpIReadyOrderRpcFacadeService.inventoryOrderCompleteCallbackErp(inventoryOrderId);
                    inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderId, "清关完成回传erp");
                }
                if (erpIDistributionOrderRpcFacadeService.isCollaborateAndDistribution(infoDTO.getCollaborateFlag(), infoDTO.getChannelBusinessType())
                        || Objects.equals(infoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode())) {
                    //出库单
                    erpIDistributionOrderRpcFacadeService.inventoryOrderCompleteCallbackErp(inventoryOrderId, InventoryOrderEnum.STATUS_COMPLETE.getCode());
                    inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(inventoryOrderId, "清关完成回传erp");

                }
            } catch (ArgsInvalidException e) {
                log.error(e.getErrorMessage());
            }
        }
    }

    /**
     * 添加核注单表体数据
     *
     * @param id
     */
    private void saveEndorsementItem(Long id) {
        EndorsementDTO old = this.findById(id);
        this.saveEndorsementItem(old);
    }

    /**
     * 添加核注单表体数据
     *
     * @param old
     */
    private void saveEndorsementItem(EndorsementDTO old) {
        Long id = old.getId();
        //if(old.getIeFlag().equals(IEType.IMPORT.getValue()))
        int serNo = 1;
        if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(old.getBussinessType())) {
//            Map<Integer, List<InventoryOrderItemDTO>> endorsementItemGroup = new HashMap<>();
            List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderId(old.getInventoryOrderId());
            itemDTOS = itemDTOS.stream().filter((InventoryOrderItemDTO s) -> id.equals(s.getRefEndorsementId())).collect(Collectors.toList());

            for (InventoryOrderItemDTO entry : itemDTOS) {
                EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
                endorsementItemDO.setDeclareFormItemSeqNo(entry.getDeclareFormItemSeqNo());
                //CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(entry.getBookItemId());
                endorsementItemDO.setEndorsementId(id);
                endorsementItemDO.setSerialNumber(serNo++);
                endorsementItemDO.setProductId(entry.getProductId());
                endorsementItemDO.setGoodsSeqNo(entry.getGoodsSeqNo());
                endorsementItemDO.setGoodsName(entry.getGoodsName());
                endorsementItemDO.setRecordProductName(entry.getGoodsName());
                endorsementItemDO.setHsCode(entry.getHsCode());
                endorsementItemDO.setDeclareUnitQfy(entry.getDeclareUnitQfy() == null ? BigDecimal.ZERO : entry.getDeclareUnitQfy());
                endorsementItemDO.setRemainDeclareUnitQfy(endorsementItemDO.getDeclareUnitQfy());
                if (Objects.nonNull(entry.getTotalGrossWeight())) {
                    endorsementItemDO.setGrossWeight(entry.getTotalGrossWeight());
                } else {
                    endorsementItemDO.setGrossWeight(entry.getGrossWeight() == null ? BigDecimal.ZERO : entry.getGrossWeight().multiply(entry.getDeclareUnitQfy()));
                }
                if (Objects.nonNull(entry.getTotalNetWeight())) {
                    endorsementItemDO.setNetWeight(entry.getTotalNetWeight());
                } else {
                    endorsementItemDO.setNetWeight(entry.getNetweight() == null ? BigDecimal.ZERO : entry.getNetweight().multiply(entry.getDeclareUnitQfy()));
                }
                //保存extra信息
                EndorsementExtraDO endorsementExtraDO = new EndorsementExtraDO();
                endorsementExtraDO.setDeclareUnit(entry.getUnit());
                endorsementExtraDO.setDeclarePrice(entry.getDeclarePrice());
                endorsementExtraDO.setDeclareTotalPrice(entry.getDeclareTotalPrice());
                endorsementExtraDO.setCurrency(entry.getCurrency());
                endorsementExtraDO.setFirstUnit(entry.getFirstUnit());
                endorsementExtraDO.setFirstUnitQfy(entry.getFirstUnitQfy());
                endorsementExtraDO.setSecondUnit(entry.getSecondUnit());
                endorsementExtraDO.setSecondUnitQfy(entry.getSecondUnitQfy());
                endorsementItemDO.setExtraJson(JSON.toJSONString(endorsementExtraDO));
//                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(old.getAccountBookId(), entry.getProductId());
//                if (Objects.nonNull(goodsRecordDTO)) {
//                    endorsementItemDO.setGrossWeight(goodsRecordDTO.getGrossWeight() == null ? BigDecimal.ZERO : goodsRecordDTO.getGrossWeight().multiply(entry.getDeclareUnitQfy()));
//                }
                UserUtils.setCreateAndUpdateBy(endorsementItemDO);
                endorsementItemMapper.insertSelective(endorsementItemDO);
            }

            // 料件表体保存
            if (EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode().equals(old.getBussinessType())) {
                List<InventoryOrderItemGoodsDTO> itemGoodsDTOList = inventoryOrderInfoService.listItemGoodsByInventoryId(old.getInventoryOrderId());
                for (InventoryOrderItemGoodsDTO entry : itemGoodsDTOList) {
                    EndorsementItemGoodsDO endorsementItemGoodsDO = new EndorsementItemGoodsDO();
                    endorsementItemGoodsDO.setDeclareFormItemSeqNo(entry.getDeclareFormItemSeqNo());
                    endorsementItemGoodsDO.setEndorsementId(id);
                    endorsementItemGoodsDO.setSerialNumber(serNo++);
                    endorsementItemGoodsDO.setProductId(entry.getProductId());
                    endorsementItemGoodsDO.setGoodsSeqNo(entry.getGoodsSeqNo());
                    endorsementItemGoodsDO.setGoodsName(entry.getGoodsName());
                    endorsementItemGoodsDO.setRecordProductName(entry.getGoodsName());
                    endorsementItemGoodsDO.setHsCode(entry.getHsCode());
                    endorsementItemGoodsDO.setDeclareUnitQfy(entry.getDeclareUnitQfy() == null ? BigDecimal.ZERO : entry.getDeclareUnitQfy());
                    endorsementItemGoodsDO.setRemainDeclareUnitQfy(endorsementItemGoodsDO.getDeclareUnitQfy());
                    endorsementItemGoodsDO.setGrossWeight(entry.getGrossWeight() == null ? BigDecimal.ZERO : entry.getGrossWeight().multiply(entry.getDeclareUnitQfy()));
                    endorsementItemGoodsDO.setNetWeight(entry.getNetweight() == null ? BigDecimal.ZERO : entry.getNetweight().multiply(entry.getDeclareUnitQfy()));
                    if (Objects.nonNull(entry.getTotalGrossWeight())) {
                        endorsementItemGoodsDO.setGrossWeight(entry.getTotalGrossWeight());
                    } else {
                        endorsementItemGoodsDO.setGrossWeight(entry.getGrossWeight() == null ? BigDecimal.ZERO : entry.getGrossWeight().multiply(entry.getDeclareUnitQfy()));
                    }
                    if (Objects.nonNull(entry.getTotalNetWeight())) {
                        endorsementItemGoodsDO.setNetWeight(entry.getTotalNetWeight());
                    } else {
                        endorsementItemGoodsDO.setNetWeight(entry.getNetweight() == null ? BigDecimal.ZERO : entry.getNetweight().multiply(entry.getDeclareUnitQfy()));
                    }
                    //保存extra信息
                    EndorsementExtraDO endorsementExtraDO = new EndorsementExtraDO();
                    endorsementExtraDO.setDeclareUnit(entry.getUnit());
                    endorsementExtraDO.setDeclarePrice(entry.getDeclarePrice());
                    endorsementExtraDO.setDeclareTotalPrice(entry.getDeclareTotalPrice());
                    endorsementExtraDO.setCurrency(entry.getCurrency());
                    endorsementExtraDO.setFirstUnit(entry.getFirstUnit());
                    endorsementExtraDO.setFirstUnitQfy(entry.getFirstUnitQfy());
                    endorsementExtraDO.setSecondUnit(entry.getSecondUnit());
                    endorsementExtraDO.setSecondUnitQfy(entry.getSecondUnitQfy());
                    endorsementItemGoodsDO.setExtraJson(JSON.toJSONString(endorsementExtraDO));
                    endorsementItemGoodsBaseService.insertSelective(endorsementItemGoodsDO);
                }
            }

        } else {
            // TODO:若收到了回执，可直接从回执解析表体
            Map<Long, EndorsementItemDO> endorsementItemDOMap = this.preItems(old);
            for (EndorsementItemDO value : endorsementItemDOMap.values()) {
                value.setSerialNumber(serNo++);
                UserUtils.setCreateAndUpdateBy(value);
                endorsementItemMapper.insertSelective(value);
            }
            Map<String, EndorsementItemDO> stringEndorsementItemDOMap = this.preItemsByFb(old);
            for (EndorsementItemDO value : stringEndorsementItemDOMap.values()) {
                value.setSerialNumber(serNo++);
                UserUtils.setCreateAndUpdateBy(value);
                endorsementItemMapper.insertSelective(value);
            }
        }
    }

    public EndorsementDTO findByRealNo(String realNo) {
        EndorsementDO template = new EndorsementDO();
        template.setRealOrderNo(realNo);
        return this.buildDTO(endorsementMapper.selectOne(template));
    }

    @Override
    public EndorsementDTO findBySn(String businessNo) {
        if (StrUtil.isEmpty(businessNo)) {
            return null;
        }
        Example example = new Example(EndorsementDO.class);
        example.and(example.createCriteria().andEqualTo("sn", businessNo));
        // Step::返回值处理
        EndorsementDO result = endorsementMapper.selectOneByExample(example);
        return this.buildDTO(result);
    }

    /**
     * 查询C单推送失败
     *
     * @param businessNo
     * @return
     */
    @Override
    public EndorsementDTO findByBusinessNo(String businessNo) {
//        CompanyDTO companyDTO = baseDataService.getCompanyDTOByCode(code);
        String ceCompanyCode = orderBaseConfig.getCeCompanyCode();
        CompanyDTO companyDTO = baseDataService.getCompanyDTOBySpecialClientCode(ceCompanyCode);
        if (Objects.isNull(companyDTO)) {
            return null;
        }
        List<Integer> status = new ArrayList<>(Arrays.asList(0, 2, 4));
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(businessNo)) {
            criteria.andEqualTo("sn", businessNo);
        }
        criteria.andEqualTo("declareCompanyId", companyDTO.getId());
        criteria.andIn("pushStatus", status);
        criteria.andEqualTo("bussinessType", "SECONDE_OUT");
        // Step::返回值处理
        EndorsementDO result = endorsementMapper.selectOneByExample(example);
        return this.buildDTO(result);
    }

    /**
     * 查询B单核注
     */
    @Override
    public List<EndorsementDTO> findByBtoBEndorsement(String id, Integer offset, Integer limit) {
        List<String> type = new ArrayList<>();
        type.add("SECTIONINNER_OUT");
        type.add("SECTION_OUT");
        String ceCompanyCode = orderBaseConfig.getCeCompanyCode();
        if (ceCompanyCode != null && ceCompanyCode.length() != 0) {
//            CompanyDTO companyDTO = baseDataService.getCompanyDTOByCode(code);
            CompanyDTO companyDTO = baseDataService.getCompanyDTOBySpecialClientCode(ceCompanyCode);
            List<Integer> status = new ArrayList<>(Arrays.asList(0, 2, 4));
            // Step::返回值处理
            Example example = new Example(EndorsementDO.class);
            example.orderBy("createTime").desc();
            Example.Criteria criteria = example.createCriteria();
            criteria.andIn("pushStatus", status);
            if (Objects.nonNull(companyDTO)) {
                criteria.andEqualTo("declareCompanyId", companyDTO.getId());
            }
            if (Objects.nonNull(id)) {
                criteria.andGreaterThan("id", id);
            }
            criteria.andIn("bussinessType", type);
            example.and(criteria);
            RowBounds rowBounds = new RowBounds(offset, limit);
//            List<EndorsementDTO> result = endorsementMapper.findByBtoBEndorsement(id,type,companyDTO.getId(),offset,limit);
            List<EndorsementDO> result = endorsementMapper.selectByExampleAndRowBounds(example, rowBounds);
            log.warn("findByBtoBEndorsement 核注单 - {},企业编码 - {}", JSON.toJSONString(result), ceCompanyCode);
            if (!CollectionUtils.isEmpty(result)) {
                return JSON.parseArray(JSON.toJSONString(result), EndorsementDTO.class);
            }
        }
        return new ArrayList<>();
    }

    @Override
    public EndorsementDTO findByPreNo(String preNo) {
        Example example = new Example(EndorsementDO.class);
        example.and(example.createCriteria().andEqualTo("preOrderNo", preNo));
        // Step::返回值处理
        EndorsementDO result = endorsementMapper.selectOneByExample(example);
        return this.buildDTO(result);
    }


    private EndorsementDTO buildDTO(EndorsementDO endorsementDO) {
        if (endorsementDO == null) {
            return null;
        }
        if (StringUtils.isBlank(endorsementDO.getIcCardNo())) {
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(endorsementDO.getAccountBookId());
            CompanyDTO customsCompanyDTO = baseDataService.getCompanyDTOById(endorsementDO.getDeclareCompanyId());
            CustomsSpecialToken specialToken = support.getSpecialConfig().getToken(customsCompanyDTO.getCode(), customsBookDTO.getBookNo());
            log.info("buildDTO::customsBookDTO={},customsCompanyDTO={},specialToken={}", JSON.toJSONString(customsBookDTO), JSON.toJSONString(customsCompanyDTO), JSON.toJSONString(specialToken));
            // todo fixme 这个位置纯碎是为了兼容一个企业两本账册情况，不优雅，要改，要改，要改
            if (Objects.nonNull(customsBookDTO) && Objects.nonNull(specialToken)) {
                if ("T2924W000137".equals(customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getCustomsSpecialTokenByCustomsBookCode();
                } else if (Objects.equals("T2925W000135", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getYwNoOneCustomsSpecialTokenByCustomsBookCode();
                } else if (Objects.equals("T2924W000093", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getFenToken_T2924W000093();
                } else if (Objects.equals("T2964W000112", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getShaoXingZongBao("T2964W000112");
                } else if (Objects.equals("T2964W000113", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getShaoXingZongBao("T2964W000113");
                } else if (Objects.equals("T2925W000338", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getYWJIAYIN_T2925W000338();
                } else if (Objects.equals("T2925W000349", customsBookDTO.getBookNo())) {
                    specialToken = CustomsIeExecutor.getYWSHUOYUN_T2925W000349();
                }
                endorsementDO.setIcCardNo(specialToken.getIcCard());
            }
        }
        EndorsementDTO endorsementDTO = new EndorsementDTO();
        BeanUtils.copyProperties(endorsementDO, endorsementDTO);
        return endorsementDTO;
    }

    private EndorsementDTOV2 buildDTOV2(EndorsementDO endorsementDO) {
        if (endorsementDO == null) {
            return null;
        }
        EndorsementDTOV2 endorsementDTO = new EndorsementDTOV2();
        BeanUtils.copyProperties(endorsementDO, endorsementDTO);
        return endorsementDTO;
    }


    @Override
    public void updateRealNo(Long id, String realNo) throws ArgsErrorException {
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setRealOrderNo(realNo);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }

    @Override
    public void updatePushStatus(Long id, Integer pushStatus, String pushMsgId, String pushMsg) {
        EndorsementDO endorsementDO = new EndorsementDO();
        if (Objects.nonNull(id)) {
            endorsementDO.setId(id);
        } else {
            Example example = new Example(EndorsementDO.class);
            example.createCriteria().andEqualTo("pushMsgId", pushMsgId);
            EndorsementDO data = endorsementMapper.selectOneByExample(example);
            endorsementDO.setId(data.getId());
        }
        endorsementDO.setPushStatus(pushStatus);
        endorsementDO.setPushMsg(pushMsg);
        endorsementDO.setPushMsgId(pushMsgId);
        endorsementDO.setPushStatus(pushStatus);
        endorsementMapper.updateByPrimaryKeySelective(endorsementDO);
    }

    @Override
    public void updatePreNo(Long id, String preNo) throws ArgsErrorException {
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setPreOrderNo(preNo);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }

    @Override
    public void updateStatus(Long id, String status) throws ArgsErrorException {
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setStatus(status);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateEndorsementAndInvOrderStatus(Long id, String statusCode) {

        EndorsementDO endorsementDO = endorsementMapper.selectByPrimaryKey(id);
        if (endorsementDO == null) {
            return;
        }
        updateStatus(id, statusCode);
        // 清关单状态
        Long inventoryOrderId = endorsementDO.getInventoryOrderId();
        if (inventoryOrderId == null) {
            return;
        }
        inventoryOrderInfoService.updateStatusByEndorsementStatus(inventoryOrderId, statusCode);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void exceptionAction(Long id) throws ArgsErrorException {
        EndorsementDTO old = this.findById(id);
        if (Objects.isNull(old)) {
            throw new ArgsErrorException("未查询到核注单");
        }
        if (Objects.equals(old.getStatus(), EndorsementOrderStatus.STORAGING.getCode())) {
            updateStatus(id, EndorsementOrderStatus.STORAGE_EXCEPTION.getCode());
        } else {
            updateStatus(id, EndorsementOrderStatus.EXCEPTION.getCode());
        }
        if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(old.getBussinessType())) {
            inventoryOrderInfoService.updateInventoryOrderInfoStatus(old.getInventoryOrderId(), InventoryOrderEnum.STATUS_FAILURE.getCode());
            InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
            if (Objects.equals(InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode(), infoDTO.getInveBusinessType())) {
                //退货单更新状态
                refundOrderService.updateCustomsStatusByInveSn(infoDTO.getInveCustomsSn(), infoDTO.getStatus());
            }
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())) {
                InvenorderStatusDTO dto = new InvenorderStatusDTO();
                dto.setStatus(InventoryOrderEnum.STATUS_FAILURE.getCode());
                dto.setInveCustomsSn(inventoryOrderInfoDTO.getInveCustomsSn());
                dto.setInveBusinessType(inventoryOrderInfoDTO.getInveBusinessType());
                inventorderStatusCallbackProducer.send(dto);
            }
            Integer orderTag = infoDTO.getOrderTag();
            List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(orderTag);
            if (orderTags.contains(InventoryOrderTagEnums.AUTO_PART_CARRYOVER.getCode())) {
                this.notifyAutoCarryOverException(old, infoDTO);
            }
            inventoryOrderInfoTrackLogService.saveInventoryOrderLog(inventoryOrderInfoDTO, "清关失败");
        }
        String message = EndorsementLogUtils.getLogDetailAndRemove();
        if (Objects.equals(old.getStatus(), EndorsementOrderStatus.STORAGING.getCode())) {
            endorsementTrackLogService.buildFullLog(old.getId(), EndorsementOrderStatus.STORAGE_EXCEPTION, "海关返回清单暂存失败", message);
        } else {
            endorsementTrackLogService.buildFullLog(old.getId(), EndorsementOrderStatus.EXCEPTION, "海关返回核注清单申报失败", message);
        }
    }

    public void notifyAutoCarryOverException(EndorsementDTO old, InventoryOrderInfoDTO infoDTO) {
        notifyAutoCarryOverException(old, infoDTO, null);
    }

    /**
     * @param old
     * @param infoDTO
     * @param notifyType null 核注异常, 1 核注超时
     */
    public void notifyAutoCarryOverException(EndorsementDTO old, InventoryOrderInfoDTO infoDTO, Integer notifyType) {
        List<AutoCarryOverEndorsementNotifyReqVo> notifyReqVoList = new ArrayList<>();
        String autoCarryOverNotifyConfig = orderBaseConfig.getAutoCarryOverNotifyConfig();
        if (StringUtils.isNotEmpty(autoCarryOverNotifyConfig)) {
            notifyReqVoList = JSON.parseArray(autoCarryOverNotifyConfig, AutoCarryOverEndorsementNotifyReqVo.class);
        }
        if (CollectionUtils.isEmpty(notifyReqVoList)) {
            return;
        }
        Map<String, AutoCarryOverEndorsementNotifyReqVo> areaCodeMap = notifyReqVoList.stream().collect(Collectors.toMap(AutoCarryOverEndorsementNotifyReqVo::getCode, Function.identity(), (v1, v2) -> v1));
        Long bookId = infoDTO.getBookId();
        Long inveCompanyId = infoDTO.getInveCompanyId();
        CompanyDTO inveCompany = baseDataService.getCompanyDTOById(inveCompanyId);
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(bookId);
        String customsAreaCode = customsBookDTO.getCustomsAreaCode();
        AutoCarryOverEndorsementNotifyReqVo reqVo = areaCodeMap.get(customsAreaCode);
        if (Objects.isNull(reqVo)) {
            log.info("未获取到企业微信提醒配置信息");
            return;
        }
        CustomsDictionaryDTO customsDictionaryDTO = customsDictionaryService.findByCodeAndType(customsAreaCode, DataDictionaryTypeEnums.PORT.getValue());
        if (Objects.isNull(customsDictionaryDTO)) {
            log.error(customsAreaCode + " 对应关区为空");
            return;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder builder = new StringBuilder();
        builder.append("【分区自动结转】异常预警 ");
//        builder.append("<font color=\\\"warning\\\">**" + "【分区自动结转】异常预警" + "**</font>\\n\n");
        if (Objects.nonNull(customsDictionaryDTO)) {
            builder.append(String.format(" 关区：%s", customsDictionaryDTO.getName()));
        }
        if (Objects.nonNull(inveCompany)) {
            builder.append(String.format(" 清关企业：%s", inveCompany.getName()));
        }
        if (Objects.nonNull(customsBookDTO)) {
            builder.append(String.format(" 账册：%s", customsBookDTO.getBookNo()));
        }
        builder.append(String.format(" 清关单号：%s", infoDTO.getInveCustomsSn()));
        builder.append(String.format(" 创建时间：%s", sdf.format(infoDTO.getCreateTime())));
        if (Objects.isNull(notifyType)) {
            builder.append(" 异常类型：核注异常");
        } else if (Objects.equals(1, notifyType)) {
            builder.append(" 异常类型：核注超时");
        }
        String informationDesc = old.getInformationDesc();
        if (StringUtils.isNotEmpty(informationDesc)) {
            informationDesc = informationDesc.trim().replaceAll("^\"|\"$", "")
                    .replace("\\", "")
                    .replace("\"", "");
        } else {
            informationDesc = "空";
        }
        builder.append(String.format("海关回执：%s", informationDesc));
        String content = builder.toString();
        log.info("content: " + content);
        WechatNotifyUtils.wechatNoteSend(reqVo.getWebHook(), reqVo.getPhoneList(), content);
    }

    @Override
    public void updateCustomsStatus(Long id, String status) throws ArgsErrorException {
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setCustomsStatus(status);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }

    @Override
    public void updateInformationDesc(Long id, String informationDesc) throws ArgsErrorException {
        EndorsementDO example = new EndorsementDO();
        example.setId(id);
        example.setInformationDesc(informationDesc);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(example);
    }

    @Override
    public List<EndorsementDTO> listBySns(List<String> sns) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("sn", sns);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementDO> itemDOList = endorsementMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementDTO> listByExport(Long exportId) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("exportOrderId", exportId);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementDO> itemDOList = endorsementMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementDTO> listByInventory(Long inventoryId) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryOrderId", inventoryId);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementDO> itemDOList = endorsementMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementDTO> listByStatus(String status) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementDO> itemDOList = endorsementMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList) {
        return this.listByStatusAndBookIdList(status, accountBookIdList, null);
    }

    @Override
    public List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList, String businessType) {
        return this.listByStatusAndBookIdList(status, accountBookIdList, businessType, null);
    }

    /**
     * 根据账册和状态筛选
     *
     * @param status
     * @param businessType
     * @return
     */
    @Override
    public List<EndorsementDTO> listByStatusAndBookIdList(String status, List<Long> accountBookIdList, String businessType, Long companyId) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("status", status);
        criteria.andEqualTo("deleted", false);
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("accountBookId", accountBookIdList);
        }
        if (!Objects.isNull(businessType)) {
            criteria.andEqualTo("bussinessType", businessType);
        }
        if (!Objects.isNull(companyId)) {
            criteria.andEqualTo("declareCompanyId", companyId);
        }
        example.and(criteria);
        List<EndorsementDO> itemDOList = endorsementMapper.selectByExample(example);
        if (CollUtil.isEmpty(itemDOList)) {
            return new ArrayList<>();
        }
        return itemDOList.stream().map(this::buildDTO).collect(Collectors.toList());
    }


    @Override
    public List<EndorsementItemDTO> listItemById(Long id) {
        EndorsementItemDO template = new EndorsementItemDO();
        template.setEndorsementId(id);
        List<EndorsementItemDO> exportItemDOS = endorsementItemMapper.select(template);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementItemDTO> listItemByChecklist(Long checklistId) {
        EndorsementItemDO template = new EndorsementItemDO();
        template.setChecklistId(checklistId);
        List<EndorsementItemDO> exportItemDOS = endorsementItemMapper.select(template);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementItemGoodsDTO> listItemGoodsById(Long id) {
        if (id == null) {
            return new ArrayList<>();
        }
        List<EndorsementItemGoodsDO> itemGoodsDOList = endorsementItemGoodsBaseService.findByOrderId(id);
        return ConvertUtil.listConvert(itemGoodsDOList, EndorsementItemGoodsDTO.class);
    }


    @Override
    public boolean checkRealNoUnqiue(String realNo, Long id) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andNotEqualTo("id", id);
        criteria.andEqualTo("realOrderNo", realNo);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementDO> list = endorsementMapper.selectByExample(example);
        if (list == null) list = new ArrayList<>();
        return list.size() <= 0;
    }

    @Override
    public List<EndorsementItemDTO> listItemByItemIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Example example = new Example(EndorsementItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", ids);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementItemDO> itemDOList = endorsementItemMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementItemDTO> listItemByEndorsementIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }
        Example example = new Example(EndorsementItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("endorsementId", ids);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<EndorsementItemDO> itemDOList = endorsementItemMapper.selectByExample(example);
        if (CollUtil.isEmpty(itemDOList)) {
            return new ArrayList<>();
        }
        return itemDOList.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<EndorsementItemDTO> listItemByEndorsementSns(List<String> snList) {
        if (CollectionUtils.isEmpty(snList)) {
            return new ArrayList<>();
        }
        List<EndorsementDTO> endorsementDTOS = this.listBySns(snList);
        List<Long> idList = endorsementDTOS.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        return this.listItemByEndorsementIds(idList);
    }

    @Override
    public List<EndorsementItemDTO> findByIdAndProductId(Long id, String productId) {
        EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
        endorsementItemDO.setEndorsementId(id);
        endorsementItemDO.setProductId(productId);
        endorsementItemDO.setDeleted(false);
        List<EndorsementItemDO> endorsementItemDOList = endorsementItemMapper.select(endorsementItemDO);
        List<EndorsementItemDTO> endorsementItemDTOList = endorsementItemDOList.stream().map(e -> {
            EndorsementItemDTO dto = new EndorsementItemDTO();
            BeanUtils.copyProperties(e, dto);
            return dto;
        }).collect(Collectors.toList());
        return endorsementItemDTOList;
    }

    @Override
    public void updateItemChecklistById(Long itemId, Long checklistId) {
        try {
            endorsementItemMapper.updateChecklist(itemId, checklistId);
        } catch (Exception e) {
            log.error("核注子表处理异常：核注子表ID:{}，异常信息：{}，", itemId, e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void discard(Long id) {
        EndorsementDTO endorsementDTO = this.findById(id);
        if (!endorsementDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode()) &&
                !endorsementDTO.getStatus().equals(EndorsementOrderStatus.STORAGING.getCode()) &&
                !endorsementDTO.getStatus().equals(EndorsementOrderStatus.STORAGED.getCode()) &&
                !endorsementDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode()) &&
                !endorsementDTO.getStatus().equals(EndorsementOrderStatus.STORAGE_EXCEPTION.getCode())) {
            throw new ArgsErrorException("仅已创建、暂存、暂存异常或异常状态的核注清单支持作废");
        }
        Long[] insufficientLedgerBookIdList = orderBaseConfig.getInsufficientLedgerBookIdList();
        List<Long> bookNoList = Arrays.asList(insufficientLedgerBookIdList);
        if (CollectionUtil.isNotEmpty(bookNoList) && bookNoList.contains(endorsementDTO.getAccountBookId())
                && StringUtils.isNotEmpty(endorsementDTO.getInformationDesc())
                && (endorsementDTO.getInformationDesc().contains("[8987]") && endorsementDTO.getInformationDesc().contains("超过底账允许数量"))
                && Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(endorsementDTO.getAccountBookId());
            throw new ArgsErrorException("底账不足核注无法作废和编辑, 账册编号【" + customsBookDTO.getBookNo() + "】");
        }
        EndorsementDO example = endorsementMapper.selectByPrimaryKey(id);
        example.setStatus(EndorsementOrderStatus.DISCARD.getCode());
        example.setInventoryOrderId(null);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            UserUtils.setUpdateBy(example);
        }
        example.setUpdateTime(new Date());
        if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementDTO.getBussinessType())) {
            example.setExportOrderId(null);
        }
        endorsementMapper.updateByPrimaryKey(example);
        //if (endorsementDTO.getIeFlag().equals(IEType.EXPORT.getValue())){
        if (EndorsementOrderTypeEnums.EXPORT_ORDER.getCode().equals(endorsementDTO.getOrderType())) {
            // 出区类型要清空有关的运单记录
            exportOrderService.deleteExportItemByEndorsement(endorsementDTO.getId(), endorsementDTO.getExportOrderId());
        } else if (EndorsementOrderTypeEnums.INVENTORY_ORDER.getCode().equals(endorsementDTO.getOrderType())) {
            //清关单后置处理
            inventoryOrderInfoService.endorsementDiscardPostProcess(endorsementDTO);
        }
        endorsementTrackLogService.buildStatusAndInfoLog(endorsementDTO.getId(), EndorsementOrderStatus.DISCARD, "核注清单已作废");
//        else {
//            this.inventoryOrderInfoService.discard(endorsementDTO.getInventoryOrderId(), endorsementDTO);
            /*
                inventoryOrderInfoDO.setStatus(InventoryOrderEnum.STATUS_DISCARE.getCode());
                inventoryOrderInfoMapper.updateByPrimaryKeySelective(inventoryOrderInfoDO);
                List<InventoryOrderItemDTO> list  = inventoryOrderInfoService.findListByInvenOrderId(endorsementDTO.getInventoryOrderId());
                inventoryOrderItemMapper.disconEndorsement(endorsementDTO.getInventoryOrderId());
             */
//        }
    }

    @Override
    public void discardByInventoryOrderId(Long inventoryOrderId) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inventoryOrderId", inventoryOrderId);
        List<EndorsementDO> endorsementDTOList = endorsementMapper.selectByExample(example);
        if (!CollectionUtils.isEmpty(endorsementDTOList)) {
//            List<EndorsementDO> endorsementConditionList = new ArrayList<>();
            for (EndorsementDO endorsementDO : endorsementDTOList) {
                endorsementDO.setStatus(EndorsementOrderStatus.DISCARD.getCode());
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(endorsementDO);
                }
                endorsementDO.setInventoryOrderId(null); // 解除清关单关联关系
                endorsementDO.setUpdateTime(new Date());
//                endorsementConditionList.add(endorsementDO);
                endorsementMapper.updateByPrimaryKey(endorsementDO);
            }
        }
    }

    @Override
    public List<EndorsementDTO> findByIdList(List<Long> endorsementIdList) {
        List<EndorsementDTO> endorsementDTOList = new ArrayList<>();
        if (CollectionUtils.isEmpty(endorsementIdList)) {
            return endorsementDTOList;
        }
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("id", endorsementIdList).andEqualTo("deleted", false);
        List<EndorsementDO> endorsementDOS = endorsementMapper.selectByExample(example);
        endorsementDTOList = endorsementDOS.stream().map(e -> {
            EndorsementDTO dto = new EndorsementDTO();
            BeanUtils.copyProperties(e, dto);
            return dto;
        }).collect(Collectors.toList());
        return endorsementDTOList;
    }

    /**
     * checklist_id 有索引 直接通过表体查询
     *
     * @param id
     * @return
     */
    @Override
    public List<String> snListByChecklistId(Long id) {
        List<String> snList = endorsementItemMapper.snListByChecklistId(id);
        return snList;
    }

    @Override
    public EndorsementItemDTO getEndorsementItem(Long endorsementId, Integer serialNumber) throws ArgsErrorException {
        EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
        endorsementItemDO.setEndorsementId(endorsementId);
        endorsementItemDO.setSerialNumber(serialNumber);
        endorsementItemDO.setDeleted(false);
        List<EndorsementItemDO> endorsementItemDOS = endorsementItemMapper.select(endorsementItemDO);
        if (CollectionUtils.isEmpty(endorsementItemDOS)) {
            throw new ArgsErrorException("新增核放单表体失败，输入的关联商品序号不存在");
        } else if (endorsementItemDOS.size() > 1) {
            throw new ArgsErrorException("关联核注单表体过多");
        }
        EndorsementItemDTO endorsementItemDTO = new EndorsementItemDTO();
        BeanUtils.copyProperties(endorsementItemDOS.get(0), endorsementItemDTO);
        return endorsementItemDTO;
    }

    @Override
    public List<EndorsementDTO> findByInventoryId(Long inventoryId) {
        return endorsementBaseService.findByInventoryOrderId(inventoryId);
    }

    @Override
    public EndorsementItemDTO findItemById(Long id) {
        return endorsementItemBaseService.findById(id);
    }


    private EndorsementItemDTO buildItemDTO(EndorsementItemDO exportItemDO) {
        if (exportItemDO == null) {
            return null;
        }
        EndorsementItemDTO dto = new EndorsementItemDTO();
        BeanUtils.copyProperties(exportItemDO, dto);
        if (StringUtils.isNotEmpty(exportItemDO.getExtraJson())) {
            EndorsementExtraDO extraDO = JSON.parseObject(exportItemDO.getExtraJson(), EndorsementExtraDO.class);
            BeanUtils.copyProperties(extraDO, dto);
        }
        return dto;
    }

    private static ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(5, 10, 10, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque(), new ThreadFactoryBuilder().setNameFormat("customsInventoryUpd%d").build());

    private AtomicLong atomicLong;

    @Override
    public void synchronizeInventory(String type, String status, String createTime, String endTime) {
        Example example = new Example(EndorsementDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("bussinessType", type).andEqualTo("status", status);
        criteria.andBetween("createTime", createTime, endTime);
        List<EndorsementDO> endorsementDOList = endorsementMapper.selectByExample(example);
        Set<Long> ids = endorsementDOList.stream().map(EndorsementDO::getId).collect(Collectors.toSet());
        List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementIds(ids);
        log.warn("selectEndorsement 数据预处理，共计{}", exportItemDTOList.size());
        List<ExportItemDTO> param = new ArrayList<>();
        int count = 0;
        Integer exportItemWritingGroupSize = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
        if (exportItemDTOList.size() < exportItemWritingGroupSize) {
            taskExecutor.execute(new EndorsementHandle(exportItemDTOList, customsInventoryService, atomicLong));
        } else {
            for (int i = 0; i < exportItemDTOList.size(); i += exportItemWritingGroupSize) {
                if (i + exportItemWritingGroupSize < exportItemDTOList.size()) {
                    for (int j = i; j < i + exportItemWritingGroupSize; j++) {
                        param.add(exportItemDTOList.get(j));
                    }
                } else {
                    for (int j = i; j < exportItemDTOList.size(); j++) {
                        param.add(exportItemDTOList.get(j));
                    }
                }
                taskExecutor.execute(new EndorsementHandle(param, customsInventoryService, atomicLong));
                count += param.size();
                //置空
                param = new ArrayList<>();
            }
            log.warn("selectEndorsement 数据处理完成，共处理{}", count);
        }

    }

    /**
     * 手动修改核注单状态
     *
     * @param endorsementDTO
     */
    @Override
    public void manualUpdStatus(EndorsementDTO endorsementDTO) {
        EndorsementDTO old = this.findById(endorsementDTO.getId());
        Integer userId = UserUtils.getUserId();
        EndorsementDO endorsementDO = new EndorsementDO();
        BeanUtils.copyProperties(endorsementDTO, endorsementDO);
        endorsementDO.setUpdateBy(userId);
        endorsementDO.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(endorsementDO);

        EndorsementOrderStatus statusEnum = EndorsementOrderStatus.getEnum(endorsementDO.getStatus());
        Integer inventoryReviewStatus = null;
        switch (statusEnum) {
            case INIT:
                inventoryReviewStatus = InventoryReviewStatus.LINKED_REVIEW.getValue();
                break;
            case EXCEPTION:
            case STORAGED:
                inventoryReviewStatus = InventoryReviewStatus.DECLARING.getValue();
                break;
            case EXAMINE:
                inventoryReviewStatus = InventoryReviewStatus.PASS_NOT_COMPLETED.getValue();
                break;
        }
        endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.getEnum(old.getStatus()),
                "核注单手动操作修改成功：" + statusEnum.getDesc());
        this.syncInventoryReviewStatus(endorsementDO.getId(), inventoryReviewStatus);
    }

    @Override
    public List<EndorsementTrackLogDTO> listTrackLogById(Long id) {
        return endorsementTrackLogService.listTrackLogById(id);
    }

    @Override
    public EndorsementEbInvEditResVO addEbInvBatch(EndorsementEbInvAddParam addParam, Boolean save) {
        EndorsementDTO endorsementDTO = this.findById(addParam.getId());
        if (Objects.equals(endorsementDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            throw new ArgsInvalidException("Excel导入的核注单不支持编辑跨境电商订单");
        }
        List<String> orderNolist = Splitter.on(",").splitToList(addParam.getQueryInfo()).stream().distinct().collect(Collectors.toList());
        EndorsementEbInvEditResVO resVO = new EndorsementEbInvEditResVO();
        List<EndorsementEbInvEditResVO.ResItem> failList = new ArrayList<>();
        List<ExportItemRecord> addList = new ArrayList<>();
        Map<String, String> mailNoMap = new HashMap<>();

        for (String orderNo : orderNolist) {
            EndorsementEbInvEditResVO.ResItem resItem = new EndorsementEbInvEditResVO.ResItem();
            resItem.setOrderNo(orderNo);
            CustomsInventoryDTO inventoryDTO = null;
            if (Objects.equals(addParam.getQueryType(), "declareOrderNo")) {
                inventoryDTO = customsInventoryService.findByDeclareNo90Days(orderNo);
            } else if (Objects.equals(addParam.getQueryType(), "inventoryNo")) {
                inventoryDTO = customsInventoryService.findByInventoryNo90Days(orderNo);
            } else if (Objects.equals(addParam.getQueryType(), "logisticsNo")) {
                inventoryDTO = customsInventoryService.findByLogisticsNo(orderNo);
            }
            if (Objects.isNull(inventoryDTO)) {
                resItem.setFailReason("未找到对应清单");
                resItem.setSeqNo(failList.size() + 1);
                failList.add(resItem);
                continue;
            }
            mailNoMap.put(inventoryDTO.getLogisticsNo(), orderNo);
            ExportItemRecord exportItemRecord = new ExportItemRecord();
            exportItemRecord.setMailNo(inventoryDTO.getLogisticsNo());
            exportItemRecord.setExpressId(inventoryDTO.getExpressId());
            exportItemRecord.setCustomsInventorySn(inventoryDTO.getSn());
            addList.add(exportItemRecord);
        }
        // 复用核注单编辑方法
        ExportItemEditReport exportItemEditReport = exportOrderService.editByEndorsement(addParam.getId(), addList, new ArrayList<>(), save);
        // 更新remark 二线出区
        EndorsementDO endorsementDO = endorsementMapper.selectByPrimaryKey(addParam.getId());
        if (save && EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(endorsementDO.getBussinessType())) {
            List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(addParam.getId());
            String remark = getRemark(itemDTOList, 4000);
            if (Objects.nonNull(remark) && !StringUtils.isEmpty(remark)) {
                EndorsementDO record = new EndorsementDO();
                record.setRemark(remark);

                Example example = new Example(EndorsementDO.class);
                example.createCriteria().andEqualTo("id", addParam.getId());
                endorsementMapper.updateByExampleSelective(record, example);
            }
        }
        for (ExportItemRecord exportItemRecord : exportItemEditReport.getAddFailList()) {
            EndorsementEbInvEditResVO.ResItem resItem = new EndorsementEbInvEditResVO.ResItem();
            resItem.setSeqNo(failList.size() + 1);
            resItem.setOrderNo(mailNoMap.get(exportItemRecord.getMailNo()));
            resItem.setFailReason(exportItemRecord.getErrorMsg());
            failList.add(resItem);
        }
        List<EndorsementEbInvEditResVO.ResItem> successList = exportItemEditReport.getAddSuccessList().stream().map(i -> {
            EndorsementEbInvEditResVO.ResItem resItem = new EndorsementEbInvEditResVO.ResItem();
            resItem.setSeqNo(exportItemEditReport.getAddSuccessList().indexOf(i));
            resItem.setOrderNo(mailNoMap.get(i.getMailNo()));
            return resItem;
        }).collect(Collectors.toList());
        resVO.setAddFailRes(failList);
        resVO.setAddFailCount(failList.size());
        resVO.setAddSuccessRes(successList);
        resVO.setAddSuccessCount(successList.size());
        resVO.setTotalCount(orderNolist.size());
        return resVO;
    }

    @Override
    public EndorsementEbInvEditResVO deleteEbInvBatch(EndorsementEbInvDeleteParam deleteParam, Boolean save) {
        EndorsementDTO endorsementDTO = this.findById(deleteParam.getId());
        if (Objects.equals(endorsementDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            throw new ArgsInvalidException("Excel导入的核注单不支持删除跨境电商订单");
        }
        List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.findBySnList(deleteParam.getCustomsInventorySnList());
        List<ExportItemRecord> delRecords = inventoryDTOList.stream().map(i -> {
            ExportItemRecord exportItemRecord = new ExportItemRecord();
            exportItemRecord.setCustomsInventorySn(i.getSn());
            exportItemRecord.setMailNo(i.getLogisticsNo());
            return exportItemRecord;
        }).collect(Collectors.toList());
        // 复用核注单编辑方法
        ExportItemEditReport exportItemEditReport = exportOrderService.editByEndorsement(deleteParam.getId(), new ArrayList<>(), delRecords, save);
        // 更新remark 二线出区
        EndorsementDO endorsementDO = endorsementMapper.selectByPrimaryKey(deleteParam.getId());
        if (save && EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(endorsementDO.getBussinessType())) {
            List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(deleteParam.getId());
            String remark = getRemark(itemDTOList, 4000);
            if (Objects.nonNull(remark) && !StringUtils.isEmpty(remark)) {
                EndorsementDO record = new EndorsementDO();
                record.setRemark(remark);

                Example example = new Example(EndorsementDO.class);
                example.createCriteria().andEqualTo("id", deleteParam.getId());
                endorsementMapper.updateByExampleSelective(record, example);
            }
        }
        List<EndorsementEbInvEditResVO.ResItem> failList = exportItemEditReport.getDeleteFailList()
                .stream().map(i -> {
                    EndorsementEbInvEditResVO.ResItem resItem = new EndorsementEbInvEditResVO.ResItem();
                    resItem.setSeqNo(exportItemEditReport.getDeleteFailList().indexOf(i));
                    resItem.setOrderNo(i.getMailNo());
                    resItem.setFailReason(i.getErrorMsg());
                    return resItem;
                }).collect(Collectors.toList());
        List<EndorsementEbInvEditResVO.ResItem> successList = exportItemEditReport.getDeleteSuccessList()
                .stream().map(i -> {
                    EndorsementEbInvEditResVO.ResItem resItem = new EndorsementEbInvEditResVO.ResItem();
                    resItem.setSeqNo(exportItemEditReport.getDeleteSuccessList().indexOf(i));
                    resItem.setOrderNo(i.getMailNo());
                    return resItem;
                }).collect(Collectors.toList());
        EndorsementEbInvEditResVO resVO = new EndorsementEbInvEditResVO();
        resVO.setDelFailRes(failList);
        resVO.setDelFailCount(failList.size());
        resVO.setDelSuccessRes(successList);
        resVO.setDelSuccessCount(successList.size());
        resVO.setTotalCount(deleteParam.getCustomsInventorySnList().size());
        return resVO;
    }

    @Override
    public void editDetail(EndorsementEditDetailReqVO reqVO) {
        List<String> allowStatusList = Arrays.asList(EndorsementOrderStatus.INIT.getCode(), EndorsementOrderStatus.STORAGED.getCode(),
                EndorsementOrderStatus.EXCEPTION.getCode(), EndorsementOrderStatus.STORAGE_EXCEPTION.getCode());
        EndorsementDTO endorsementDTO = endorsementService.findById(reqVO.getId());
        if (!allowStatusList.contains(endorsementDTO.getStatus())) {
            throw new ArgsInvalidException("核注单状态不允许编辑");
        }
        String lockKey = "ccs:endorsement:edit_push:" + reqVO.getId();
        if (!RedissLockUtil.tryLock(lockKey, TimeUnit.SECONDS, 3, 5)) {
            throw new RuntimeException("操作过于频繁，请稍后重试");
        }
        try {
            log.info("核注单编辑 id:{} 加锁成功", reqVO.getId());
            EndorsementDO endorsementDO = new EndorsementDO();
            endorsementDO.setId(reqVO.getId());
            endorsementDO.setIcCardNo(reqVO.getInputerIcCard());
            endorsementDO.setRemark(reqVO.getRemark());
            endorsementDO.setSupvMode(reqVO.getSupvMode());
            endorsementDO.setInvtType(reqVO.getInvtType());
            endorsementMapper.updateByPrimaryKeySelective(endorsementDO);
        } finally {
            try {
                RedissLockUtil.unlock(lockKey);
                log.info("endorsement editDetail key:{} 释放锁成功", lockKey);
            } catch (Exception ex) {
                log.warn("解锁异常，可能是根本就没有加到锁就解锁了");
            }
        }
        if (Objects.nonNull(reqVO.getRemark())) {
            InventoryOrderInfoDO updateDO = new InventoryOrderInfoDO();
            updateDO.setId(endorsementDTO.getInventoryOrderId());
            updateDO.setEndorsementRemark(reqVO.getRemark());
            inventoryOrderInfoBaseService.updateByPrimaryKeySelective(updateDO);
        }
    }

    /**
     * 同步清单 核注状态
     *
     * @param endorsementId
     * @param reviewStatus
     */
    private void syncInventoryReviewStatus(Long endorsementId, Integer reviewStatus) {
        List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementId(endorsementId);
        if (CollectionUtils.isEmpty(exportItemDTOList)) {
            return;
        }
        List<String> inventorySnList = exportItemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).distinct().collect(Collectors.toList());
        //去关联清关单申报核注状态
        customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, reviewStatus);
    }


    /**
     * 根据核注清单编号更新核注单表体的记账金二序号
     *
     * @param productId
     * @param goodsSeqNo
     * @param realOrderNo
     * @param invtGNo
     */
    @Override
    public void updateEndorsementItemSeqCallback(String productId, String goodsSeqNo, String realOrderNo, String invtGNo) {
        log.info("updateEndorsementItemSeqCallback productId={} goodsSeqNo={} realOrderNo={}", productId, goodsSeqNo, realOrderNo);
        EndorsementDTO endorsementDTO = this.findByRealOrderNo(realOrderNo);
        if (Objects.isNull(endorsementDTO)) {
            log.info("updateEndorsementItemSeqCallback 根据核注清单编号未找到关联核注单 realOrderNo={}", realOrderNo);
            throw new ArgsInvalidException("根据核注清单编号" + realOrderNo + "未找到关联核注单");
        }
        Example example = new Example(EndorsementItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("productId", productId)
                .andEqualTo("endorsementId", endorsementDTO.getId())
                .andEqualTo("deleted", false);
        if (StrUtil.isNotBlank(invtGNo)) {
            criteria.andEqualTo("serialNumber", invtGNo);
        }
        List<EndorsementItemDO> endorsementItemDOList = endorsementItemMapper.selectByExample(example);
        if (Objects.isNull(endorsementItemDOList)) {
            log.info("updateEndorsementItemSeqCallback 根据核注清单ID,商品料号未找到核注单明细 ID={} productId={}", endorsementDTO.getId(), productId);
            throw new ArgsInvalidException("根据核注单ID:" + endorsementDTO.getId() + "商品料号:" + productId + "未找到核注单明细");
        }
        endorsementItemDOList = endorsementItemDOList.stream().filter(d -> StringUtils.isEmpty(d.getGoodsSeqNo())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(endorsementItemDOList)) {
            for (EndorsementItemDO itemDTO : endorsementItemDOList) {
                EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
                endorsementItemDO.setCustomsCallBackSeqNo(goodsSeqNo);
                if (!Objects.equals(UserUtils.getUserId(), 0)) {
                    UserUtils.setUpdateBy(endorsementItemDO);
                }
                endorsementItemDO.setUpdateTime(new Date());
                endorsementItemDO.setId(itemDTO.getId());
                endorsementItemMapper.updateByPrimaryKeySelective(endorsementItemDO);
            }
        }
        // 核注表体记账金二序号都反填完成 则账册库存处理成功
        List<EndorsementItemDTO> itemDTOList = this.listItemById(endorsementDTO.getId());
        List<EndorsementItemDTO> seqNoNonExistItemList = itemDTOList.stream()
                .filter(d -> StringUtils.isEmpty(d.getGoodsSeqNo()) && StringUtils.isEmpty(d.getCustomsCallBackSeqNo()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(itemDTOList) && CollUtil.isEmpty(seqNoNonExistItemList)) {
            endorsementTrackLogService.buildStatusAndInfoLog(endorsementDTO.getId(), EndorsementOrderStatus.FINISH, "账册库存处理成功");
        }
        this.insertItemStockListCallback(endorsementDTO, realOrderNo);
    }

    public void insertItemStockListCallback(EndorsementDTO endorsementDTO, String realOrderNo) {
        log.info("InsertItemStockListCallback realOrderNo={}", realOrderNo);
        List<EndorsementItemDTO> endorsementItemDTOList = this.listItemById(endorsementDTO.getId());
        if (CollectionUtils.isEmpty(endorsementItemDTOList)) {
            log.info("InsertItemStockListCallback 根据核注清单ID未找到核注单明细ID={}", endorsementDTO.getId());
            throw new ArgsInvalidException("根据核注清单ID未找到核注单明细ID:" + endorsementDTO.getId());
        }
        //判断是否含有金二序号为空的情况
        boolean goodsNoExistEmpty = endorsementItemDTOList.stream()
                .anyMatch(d -> StringUtils.isEmpty(d.getGoodsSeqNo())
                        && StringUtils.isEmpty(d.getCustomsCallBackSeqNo()));
        log.info("核注清单单号{}是否存在金二序号为空的情况{}", realOrderNo, goodsNoExistEmpty);
        //是否生成流水是否开启
        boolean isGenStockList = false;
        CustomsBookDTO customsBookDTO = customsBookService.findById(endorsementDTO.getAccountBookId());
        if (Objects.nonNull(customsBookDTO) && Objects.equals(customsBookDTO.getStockListGenerateEnable(), 1)) {
            isGenStockList = true;
        }
        if (!goodsNoExistEmpty && isGenStockList) {
            // 使用RocketMQ异步处理库存流水批量插入，避免阻塞主流程
            log.info("发送库存流水批量插入消息, endorsementId={}, endorsementSn={}, realOrderNo={}",
                    endorsementDTO.getId(), endorsementDTO.getSn(), realOrderNo);
            itemStockListBatchInsertProducer.sendItemStockListBatchInsertMessage(endorsementItemDTOList, endorsementDTO, realOrderNo);
        }
    }

    /**
     * 根据ID插入库存流水   用于定时任务补流水库存
     *
     * @param id
     */
    @Override
    public void insertItemStockListById(Long id) {
        log.info("insertItemStockListById id={}", id);
        EndorsementDTO endorsementDTO = this.findById(id);
        if (Objects.isNull(endorsementDTO)) {
            log.info("insertItemStockListById 根据核注ID未找到关联核注单 id={}", id);
            return;
        }
        List<EndorsementItemDTO> endorsementItemDTOList = this.listItemById(id);
        if (CollectionUtils.isEmpty(endorsementItemDTOList)) {
            log.info("insertItemStockListById 根据核注清单ID未找到核注单明细ID={}", id);
            return;
        }
        //判断是否含有金二序号为空的情况
        boolean goodsNoExistEmpty = endorsementItemDTOList.stream().anyMatch(d -> StringUtils.isEmpty(d.getGoodsSeqNo()));
        log.info("insertItemStockListById核注清单ID{}是否存在金二序号为空的情况{}", id, goodsNoExistEmpty);
        if (!goodsNoExistEmpty) {
            // 使用RocketMQ异步处理库存流水批量插入，避免阻塞主流程
            log.info("发送库存流水批量插入消息, endorsementId={}, endorsementSn={}, realOrderNo={}",
                    endorsementDTO.getId(), endorsementDTO.getSn(), endorsementDTO.getRealOrderNo());
            itemStockListBatchInsertProducer.sendItemStockListBatchInsertMessage(endorsementItemDTOList, endorsementDTO, endorsementDTO.getRealOrderNo());
        }
    }

    /**
     * 查找 金二序号不为空的核注单表体
     *
     * @param endorsementId 核注单id
     * @return
     */
    @Override
    public List<EndorsementItemDTO> getItemListSeqNoNonExistById(Long endorsementId) {
        Example example = new Example(EndorsementItemDO.class);
        example.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("endorsementId", endorsementId);
        Example.Criteria goodsSeqNoCriteria = example.createCriteria();
        goodsSeqNoCriteria.andIsNull("goodsSeqNo").orEqualTo("goodsSeqNo", "");
        example.and(goodsSeqNoCriteria);
        List<EndorsementItemDO> endorsementItemDOS = endorsementItemMapper.selectByExample(example);
        return ConvertUtil.listConvert(endorsementItemDOS, EndorsementItemDTO.class);
    }


    /**
     * 回告wms cw清关服务完成
     *
     * @param infoDTO
     */
    @Override
    public void callbackWmsCwFinish(InventoryOrderInfoDTO infoDTO) {
        //校验
        if (Boolean.FALSE.equals(InventoryOrderTagEnums.contains(infoDTO.getOrderTag(), InventoryOrderTagEnums.CW_INVENTORY))) {
            throw new ArgsInvalidException("清关单不为CW清关单，不回传WMS");
        }
        if (!InventoryOrderEnum.STATUS_FINISH.getCode().equals(infoDTO.getStatus())) {
            throw new ArgsInvalidException("清关单未服务完成，不回传WMS");
        }
        //拼装&推送
        WmsCwCallbackParam param = new WmsCwCallbackParam();
        param.setWarehouseCode(infoDTO.getWmsWarehouseCode());
        param.setInOutOrderNo(infoDTO.getInOutOrderNo());
        param.setInOutFlag(InventoryOrderBusinessEnum.getEnum(infoDTO.getInveBusinessType()).getType().getCode());
        Boolean callback = wmsCwCallbackClientService.callback(param);
        inventoryOrderInfoTrackLogService.saveThirdPartySystemReqAndResLog(infoDTO, "WMS：服务完成CW回传");
        //维护清关单回执状态
        if (Boolean.TRUE.equals(callback)) {
            infoDTO.setCallbackStatus(InventoryOrderCallbackStatusEnum.SUCCESS.getCode());
        } else {
            infoDTO.setCallbackStatus(InventoryOrderCallbackStatusEnum.FAILED.getCode());
        }
        inventoryOrderInfoService.updateInventoryOrderInfoDTO(infoDTO);
    }

    //MOCK测试专用接口
    @Override
    public void createMockPushReceiveOutRegionMsg(EndorsementDTO endorsementDTO, Integer checkOutStatus) {
        JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(endorsementDTO.getAccountBookId());
        if (Objects.isNull(jdServProviderDTO) || StringUtils.isEmpty(jdServProviderDTO.getBondedAreaCode())) {
            throw new ArgsInvalidException("找不到对应的保税区编码,核注清单编号:" + endorsementDTO.getRealOrderNo());
        }
        String mockJdUrl = orderBaseConfig.getMockJdUrl();
        if (StringUtils.isEmpty(mockJdUrl)) {
            throw new ArgsInvalidException("请在appllo设置mock url");
        }
        List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementId(endorsementDTO.getId());
        for (ExportItemDTO exportItemDTO : exportItemDTOList) {
            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(exportItemDTO.getCustomsInventorySn());
            if (Objects.isNull(customsInventoryDTO)) {
                log.error("createMockPushReceiveOutRegionMsg找不到对应的清单单记录,sn：{}", exportItemDTO.getCustomsInventorySn());
                continue;
            }
            OrderDTO orderDTO = orderService.findByDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
            if (Objects.isNull(orderDTO)) {
                log.error("createMockPushReceiveOutRegionMsg找不到对应的申报单记录,申报单号：{}", customsInventoryDTO.getDeclareOrderNo());
                continue;
            }
            JdReceiveOutReginPushMsgDTO request = new JdReceiveOutReginPushMsgDTO();
            request.setCustomsId(jdServProviderDTO.getBondedAreaCode());
            request.setYunJiaoYi(false);
            request.setOrderId(customsInventoryDTO.getDeclareOrderNo());
            CompanyDTO ebpCompany = companyService.findById(customsInventoryDTO.getEbpId());
            if (Objects.nonNull(ebpCompany) && Objects.equals(ebpCompany.getCode(), "31149679BZ")) {
                request.setPlatformId("1");
            } else {
                request.setPlatformId("");
            }
            request.setServiceId(jdServProviderDTO.getCode());
            request.setOperationTime(endorsementDTO.getFinishTime());
            request.setCreateTime(customsInventoryDTO.getCreateTime());
            request.setAccountBookId(customsInventoryDTO.getAccountBookId());
            log.info("createPushReceiveOutReginMsg  request={}", JSON.toJSONString(request));
            try {
                HttpRequest httpRequest = HttpRequestUtil.post(mockJdUrl, JSON.toJSONString(request));
                if (httpRequest.ok()) {
                    String body = httpRequest.body();
                    log.info("createMockPushReceiveOutRegionMsg调用mock返回body={}", body);
                    CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginResponse response = JSON.parseObject(body, CustomsCenterServiceSoaStorageStorageJsfServiceReceiveOutReginResponse.class);
                    TrackLogEsDTO trackLogEsDO = new TrackLogEsDTO();
                    if (Objects.nonNull(response) && Objects.equals(response.getCode(), "0") && Objects.equals(response.getReturnType().getResultCode(), 1)) {
                        customsInventoryService.updateCheckOutTimeAndStatusSection(customsInventoryDTO.getId(), InventoryCheckOutStatusEnum.REGIN_SUCCESS.getCode(),
                                endorsementDTO.getFinishTime(), customsInventoryDTO.getCreateTime());
                        trackLogEsDO.setResult(TrackLogConstantMixAll.SUCCESS);
                    } else {
                        customsInventoryService.updateCheckOutTimeAndStatusSection(customsInventoryDTO.getId(), InventoryCheckOutStatusEnum.REGIN_FAIL.getCode(),
                                endorsementDTO.getFinishTime(), customsInventoryDTO.getCreateTime());
                        trackLogEsDO.setResult(TrackLogConstantMixAll.FAIL);
                    }
                    trackLogEsDO.setOrderId(customsInventoryDTO.getOrderId());
                    trackLogEsDO.setOrderSn(customsInventoryDTO.getOrderSn());
                    trackLogEsDO.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                    trackLogEsDO.setRequestMessage(JSON.toJSONString(request));
                    trackLogEsDO.setSender(TrackLogConstantMixAll.DT_CCS);
                    trackLogEsDO.setReceiver(TrackLogConstantMixAll.JD);
                    trackLogEsDO.setEventDesc(TrackLogConstantMixAll.RECEIVE_OUT_REGIN);
                    trackLogEsDO.setEventTime(endorsementDTO.getFinishTime());
                    trackLogEsDO.setOperator(UserUtils.getUserRealName());
                    trackLogEsDO.setInternalStatus(OrderInternalEnum.INVENTORY_DECLARE_SUCCESS.getCode());
                    trackLogEsDO.setCustomsReceipt(JSON.toJSONString(response));
                    trackLogEsService.submit(trackLogEsDO);

                } else {
                    String body = httpRequest.body();
                    log.error("调用mock接口失败 body={}", body);
                    break;
                }

            } catch (Exception ex) {
                log.error("createMockPushReceiveOutRegionMsg异常：{}", ex.getMessage(), ex);
                break;
            }
        }
    }

    @Override
    public void receiveGenerateDeclareCallback(EndorsementGenerateDeclareCallbackDTO callbackDTO) {
        String businessId = callbackDTO.getBusinessId();
        EndorsementDTO endorsementDTO = endorsementService.findByRealOrderNo(businessId);
        if (Objects.isNull(endorsementDTO)) {
            log.warn("receiveGenerateDeclareCallback 核注清单编号不存在 {}", businessId);
            throw new ArgsInvalidException("核注清单编号不存在 " + businessId);
        }
        if (Objects.equals(endorsementDTO.getGenerateDeclareStatus(), "1")) {
            log.info("receiveGenerateDeclareCallback 核注清单 {} 生成报关单成功 略过不处理", businessId);
            return;
        }
        String logInfo = "";
        EndorsementDO updateDO = new EndorsementDO();
        updateDO.setGenerateDeclareStatus(callbackDTO.getManageResult());
        updateDO.setCustomsEntrySeqNo(callbackDTO.getEntrySeqNo());
        if (Objects.equals(callbackDTO.getManageResult(), "1")) {
            updateDO.setGenerateDeclareReason("");
            logInfo = "核注清单生成报关单成功";
        } else {
            updateDO.setGenerateDeclareReason(callbackDTO.getReason());
            logInfo = "核注清单生成报关单失败：" + callbackDTO.getReason();
        }
        updateDO.setId(endorsementDTO.getId());
        endorsementMapper.updateByPrimaryKeySelective(updateDO);
        endorsementTrackLogService.buildFullLog(endorsementDTO.getId(), EndorsementOrderStatus.getEnum(endorsementDTO.getStatus()),
                logInfo, JSON.toJSONString(callbackDTO));
    }

    @Override
    public void updateRemarkBySn(String remark, String sn) {
        if (StrUtil.isBlank(sn)) {
            log.warn("endorsementService updateRemarkBySn sn为空");
            return;
        }
        EndorsementDO updateDO = new EndorsementDO();
        updateDO.setRemark(remark);
        Example example = new Example(EndorsementDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("sn", sn);
        endorsementMapper.updateByExampleSelective(updateDO, example);
    }

    @Override
    public JdReceiveOutReginPushMsgDTO getReceiveOutReginPushMsg(OrderDTO orderDTO) {

        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(orderDTO.getCustomsInventorySn());
        if (Objects.isNull(customsInventoryDTO)) {
            throw new ArgsInvalidException("找不到对应的清单数据,申报单号:" + orderDTO.getDeclareOrderNo());
        }
        ExportItemDTO exportItemDTO = exportOrderService.findItemByCustomInventory(orderDTO.getCustomsInventorySn());
        if (Objects.isNull(exportItemDTO)) {
            throw new ArgsInvalidException("找不到对应的出库单数据,业务编号:" + orderDTO.getDeclareOrderNo());
        }
        EndorsementDTO endorsementDTO = endorsementService.findById(exportItemDTO.getEndorsementOrderId());
        if (Objects.isNull(endorsementDTO)) {
            throw new ArgsInvalidException("找不到对应的核注单数据,业务编号:" + orderDTO.getDeclareOrderNo());
        }

        if (!Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
            throw new ArgsInvalidException("核注清单业务类型不是二线出区,核注清单编号：" + endorsementDTO.getRealOrderNo());
        }

        if (!Objects.equals(endorsementDTO.getStatus(), EndorsementOrderStatus.FINISH.getCode())) {
            throw new ArgsInvalidException("核注清单状态不是清关完成,核注清单编号：" + endorsementDTO.getRealOrderNo());
        }
        CustomsBookDTO customsBookDTO = customsBookService.findById(endorsementDTO.getAccountBookId());
        if (Objects.isNull(customsBookDTO)) {
            throw new ArgsInvalidException("找不到对应的账册数据，业务编号：" + orderDTO.getDeclareOrderNo());
        }
        if (Objects.isNull(customsBookDTO.getBookTag())) {
            throw new ArgsInvalidException("请设置京东出区回传,账册编号：" + customsBookDTO.getBookNo());
        }
        Integer bookTag = customsBookDTO.getBookTag();
        List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(bookTag);
        if (!bookTagList.contains(CustomsBookTagEnums.JD_RECEIVE_OUT_REGIN.getCode())) {
            throw new ArgsInvalidException("请设置京东出区回传,账册编号：" + customsBookDTO.getBookNo());
        }
        JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(customsInventoryDTO.getAccountBookId());
        if (Objects.isNull(jdServProviderDTO) || StringUtils.isEmpty(jdServProviderDTO.getBondedAreaCode())) {
            throw new ArgsInvalidException("找不到对应的保税区编码,账册编号:" + customsBookDTO.getBookNo());
        }

        //先更新待回传
        JdReceiveOutReginPushMsgDTO request = new JdReceiveOutReginPushMsgDTO();
        request.setCustomsId(jdServProviderDTO.getBondedAreaCode());
        request.setYunJiaoYi(false);
        request.setOrderId(customsInventoryDTO.getDeclareOrderNo());
        CompanyDTO ebpCompany = companyService.findById(customsInventoryDTO.getEbpId());
        if (Objects.nonNull(ebpCompany) && Objects.equals(ebpCompany.getCode(), "31149679BZ")) {
            request.setPlatformId("1");
        } else {
            request.setPlatformId("");
        }
        request.setServiceId(jdServProviderDTO.getCode());
        request.setOperationTime(endorsementDTO.getFinishTime());
        request.setCreateTime(customsInventoryDTO.getCreateTime());
        request.setAccountBookId(customsInventoryDTO.getAccountBookId());
        return request;
    }


    public String processExtractedNotes(List<String> fbNoteList, ByteDanceFbNoteConfig fbNoteConfig) {
        String collect = String.join(";", fbNoteList);
        String suffix = StringUtils.defaultIfEmpty(fbNoteConfig.getSuffix(), "");
        int length = fbNoteConfig.getLength();
        return CustomsCharLengthUtils.subStringByCharLength(collect, suffix, length);
    }

    public void updateEndorsementDO(Long endorsementId, String note) {
        EndorsementDO updateDO = new EndorsementDO();
        updateDO.setId(endorsementId);
        updateDO.setRemark(note);
        updateDO.setUpdateTime(new Date());
        endorsementMapper.updateByPrimaryKeySelective(updateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void importExcel(String businessType, Long declareCompanyId, Long accountBookId,
                            List<EndorsementItemDTO> itemList, List<EndorsementRelationDTO> ebInvList) {
        if (!RedissonLockUtilV2.tryLock("ccs:endorsement:import:excel:" + UserUtils.getUserId(), 3, 5)) {
            throw new ArgsErrorRpcException("核注单生成中，请稍后重试");
        }
        log.info("开始Excel导入核注单，业务类型：{}，清关企业ID：{}，账册ID：{}",
                businessType, declareCompanyId, accountBookId);

        // 1. 创建核注单主表记录
        EndorsementDO endorsementDO = new EndorsementDO();
        endorsementDO.setSn(sequenceService.generateEndorsementSn());
        endorsementDO.setAccountBookId(accountBookId);
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(accountBookId);
        endorsementDO.setDeclareCompanyId(customsBookDTO.getAreaCompanyId());
        endorsementDO.setStatus(EndorsementOrderStatus.INIT.getCode());
        endorsementDO.setBussinessType(businessType);
        switch (EndorsementBussiness.getEnum(businessType)) {
            case BUSSINESS_SECONDE_OUT:
                endorsementDO.setIeFlag(1);
                endorsementDO.setInvtType("8");
                endorsementDO.setSupvMode("5000");
                break;
            case BUSSINESS_REFUND_INAREA:
                endorsementDO.setIeFlag(2);
                endorsementDO.setInvtType("8"); //清单类型：保税电商
                endorsementDO.setSupvMode("1210"); //监管方式：保税电商
                break;
        }
        endorsementDO.setTransportMode("9");
        endorsementDO.setStockChangeEnable(true);
        endorsementDO.setOrderType(EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode());
        UserUtils.setCommonData(endorsementDO);
        endorsementMapper.insertSelective(endorsementDO);
        // 2. 处理表体(成品)数据
        if (CollUtil.isNotEmpty(itemList)) {
            List<EndorsementItemDO> itemDOList = itemList.stream().map(i -> {
                EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
                BeanUtils.copyProperties(i, endorsementItemDO, "deleted");
                endorsementItemDO.setEndorsementId(endorsementDO.getId());
                EndorsementExtraDO extraDO = new EndorsementExtraDO();
                BeanUtils.copyProperties(i, extraDO);
                endorsementItemDO.setExtraJson(JSON.toJSONString(extraDO));
                UserUtils.setCommonData(endorsementItemDO);
                return endorsementItemDO;
            }).collect(Collectors.toList());
            endorsementItemMapper.insertList(itemDOList);
        }
        // 3. 处理保税电商清单数据
        if (CollUtil.isNotEmpty(ebInvList)) {
            List<EndorsementRelationDO> relationDOList = ebInvList.stream().map(i -> {
                EndorsementRelationDO endorsementRelationDO = new EndorsementRelationDO();
                endorsementRelationDO.setEndorsementId(endorsementDO.getId());
                endorsementRelationDO.setInventoryNo(i.getInventoryNo());
                UserUtils.setCommonData(endorsementRelationDO);
                return endorsementRelationDO;
            }).collect(Collectors.toList());
            endorsementRelationMapper.insertList(relationDOList);
        }
        // 4. 保存相关数据到数据库
        log.info("Excel导入核注单完成，表体数量：{}，保税电商清单数量：{}",
                itemList != null ? itemList.size() : 0,
                ebInvList != null ? ebInvList.size() : 0);
        endorsementTrackLogService.buildStatusAndInfoLog(endorsementDO.getId(), EndorsementOrderStatus.INIT, "新建核注清单");
    }

}

@Slf4j
class EndorsementHandle extends TraceDataRunnable {
    private List<ExportItemDTO> exportItemDTO;

    private CustomsInventoryRpc customsInventoryService;

    private AtomicLong atomicLong;

    public EndorsementHandle(List<ExportItemDTO> exportItemDTO, CustomsInventoryRpc customsInventoryService, AtomicLong atomicLong) {
        super();
        this.exportItemDTO = exportItemDTO;
        this.customsInventoryService = customsInventoryService;
        this.atomicLong = atomicLong;
    }

    @Override
    public void proxy() {
        if (CollectionUtils.isEmpty(exportItemDTO)) {
            log.info("Endorsement，任务为空");
            return;
        }
        try {
            for (ExportItemDTO itemDTO : exportItemDTO) {
                if (Objects.isNull(itemDTO.getCustomsInventorySn())) {
                    continue;
                }
                CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(itemDTO.getCustomsInventorySn());
                if (Objects.nonNull(customsInventoryDTO) && Objects.equals(customsInventoryDTO.getExitRegionStatus(), 0)) {
                    customsInventoryService.updateExitRegionStatus(customsInventoryDTO.getId(), 1, new Date(), customsInventoryDTO.getCreateTime());
                }
            }
            log.warn("selectEndorsement 数据处理批次，第{}批", atomicLong.getAndIncrement() + "处理完成");
        } catch (Exception e) {
            log.warn("selectEndorsement 数据处理第{}批次,处理失败,异常{}", atomicLong.getAndIncrement(), e.getMessage());
        }

    }
}
