package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_inventory_order_tally_report")
@Getter
@Setter
public class InventoryOrderTallyReportDO extends BaseDO {
    /**
     * 清关单id
     */
    @Column(name = "inve_order_id")
    private Long inveOrderId;

    /**
     * 清关单号
     */
    @Column(name = "inve_order_sn")
    private String inveOrderSn;

    /**
     * 理货编号
     */
    @Column(name = "tally_order_no")
    private String tallyOrderNo;

    /**
     * 出库单号
     */
    @Column(name = "out_bound_no")
    private String outBoundNo;

    /**
     * 理货明细json
     */
    @Column(name = "tally_json")
    private String tallyJson;
}