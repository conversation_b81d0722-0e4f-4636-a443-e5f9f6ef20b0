package com.danding.cds.endorsement.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;

@Table(name = "`ccs_endorsement_item_goods`")
@Getter
@Setter
public class EndorsementItemGoodsDO extends BaseDO {
    /**
     * 核放单ID
     */
    @Column(name = "checklist_id")
    private Long checklistId;

    /**
     * 核注清单ID
     */
    @Column(name = "endorsement_id")
    private Long endorsementId;

    /**
     * 商品表体序号
     */
    @Column(name = "serial_number")
    private Integer serialNumber;

    /**
     * 统一料号
     */
    private String originProductId;

    /**
     * 商品料号
     */
    @Column(name = "product_id")
    private String productId;

    /**
     * 金二序号
     */
    @Column(name = "goods_seq_no")
    private String goodsSeqNo;

    /**
     * 料号名称
     */
    @Column(name = "goods_name")
    private String goodsName;

    /**
     * 备案名称
     */
    @Column(name = "record_product_name")
    private String recordProductName;

    /**
     * HS代码
     */
    @Column(name = "hs_code")
    private String hsCode;

    /**
     * 申报单位数量
     */
    @Column(name = "declare_unit_qfy")
    private BigDecimal declareUnitQfy;

    /**
     * 剩余申报单位
     */
    @Column(name = "remain_declare_unit_qfy")
    private BigDecimal remainDeclareUnitQfy;

    /**
     * 毛重（公斤）
     */
    @Column(name = "gross_weight")
    private BigDecimal grossWeight;

    /**
     * 净重（公斤）
     */
    @Column(name = "net_weight")
    private BigDecimal netWeight;

    /**
     * 额外其他属性
     */
    @Column(name = "extra_json")
    private String extraJson;

    /**
     * 修改标志
     */
    @Column(name = "modf_mark")
    private String modfMark;

    /**
     * 记账金二序号
     */
    @Column(name = "customs_callback_seq_no")
    private String customsCallBackSeqNo;

    /**
     * 表体标签
     * {@link com.danding.cds.c.api.bean.enums.OrderItemTagEnum}
     */
    @Column(name = "item_tag")
    private Integer itemTag;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;

    /**
     * 来源标识
     */
    private String goodsSource;
}