package com.danding.cds.endorsement.impl.mapper;

import com.danding.cds.endorsement.impl.entity.EndorsementDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface EndorsementMapper extends Mapper<EndorsementDO>, InsertListMapper<EndorsementDO>, BatchUpdateMapper<EndorsementDO>, AggregationPlusMapper<EndorsementDO> {


//    @Results(id = "EndorsementMap", value = {
//            @Result(property = "sn", column = "sn"),
//            @Result(property = "outBond", column = "outBond"),
//            @Result(property = "id", column = "id"),
//            @Result(property = "inventoryOrderId", column = "inventoryOrderId"),
//    })


    /**
     * 根据预录入核注编号查询相关清关单ID
     *
     * @param preOrderNo
     * @return
     */
    @Select("SELECT DISTINCT inventory_order_id FROM ccs_endorsement WHERE  ie_flag = 2 AND pre_order_no = '${preOrderNo}' AND  inventory_order_id IS NOT NULL ")
    List<Long> findIdsByPreOrderNo(@Param(value = "preOrderNo") String preOrderNo);

    /**
     * 根据核注单号查询清关单ID
     *
     * @param endorsementSnList
     * @return
     */
    @Select(value =
            "<script>" +
                    "select distinct inventory_order_id from ccs_endorsement where deleted = 0 and sn in " +
                    "<foreach collection='snList' item='item' index ='index' open='(' separator=',' close=')' >" +
                    "#{item}" +
                    "</foreach>" +
                    "</script>")
    List<Long> findInventoryOrderIdByEndorsementSn(@Param("snList") List<String> endorsementSnList);

    /**
     * 根据核注清单编号查询清关单ID
     *
     * @param realOrderNoList
     * @return
     */
    @Select(value =
            "<script>" +
                    "select distinct inventory_order_id from ccs_endorsement where deleted = 0 and real_order_no in " +
                    "<foreach collection='realOrderNoList' item='item' index ='index' open='(' separator=',' close=')' >" +
                    "#{item}" +
                    "</foreach>" +
                    "</script>")
    List<Long> findInventoryOrderIdByRealOrderNoList(@Param("realOrderNoList") List<String> realOrderNoList);

    @Select(value =
            "<script>" +
                    "select ce.id as endorsementId from `ccs_endorsement` ce left join `ccs_inventory_order_info` cioi on ce.`inventory_order_id` = cioi.id " +
                    "where ce.`status` in  ('DECALRING','INIT','EXCEPTION') and ce.`create_time` &lt;= #{formattedDateTime} and cioi.`order_tag` &amp; #{code} = #{code} " +
                    "</script>"
    )
    List<Long> findAutoCarryOverTimeOutEndorsement(@Param("formattedDateTime") String formattedDateTime, @Param("code") Integer code);
}