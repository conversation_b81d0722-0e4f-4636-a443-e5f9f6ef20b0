package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderRelationDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface InventoryOrderRelationMapper extends Mapper<InventoryOrderRelationDO>, InsertListMapper<InventoryOrderRelationDO>, BatchUpdateMapper<InventoryOrderRelationDO>, AggregationPlusMapper<InventoryOrderRelationDO> {

    @Results(value = {
            @Result(property = "id", column = "id"),
            @Result(property = "refInveOrderId", column = "ref_inve_order_id"),
            @Result(property = "refInveOrderSn", column = "ref_inve_order_sn"),
            @Result(property = "relType", column = "rel_type"),
            @Result(property = "relNo", column = "rel_no"),
            @Result(property = "oper", column = "oper"),
            @Result(property = "createTime", column = "create_time"),
            @Result(property = "updateTime", column = "update_time"),
            @Result(property = "createBy", column = "create_by"),
            @Result(property = "updateBy", column = "update_by"),
            @Result(property = "deleted", column = "deleted"),
    })
    @Select(value = "<script>" +
            "select " +
            "  * " +
            "from " +
            "  `ccs_inventory_order_relation` ior " +
            "  inner join `ccs_inventory_order_info` ioi on ior.`ref_inve_order_id` = ioi.id " +
            "where " +
            " ior.deleted = false " +
            " and ioi.deleted = false " +
            " and ioi.`status` != 'DISCARD' " +
            " and ior.`rel_type` = 'YUNDAN' " +
            " and ior.`rel_no` in " +
            "<foreach collection='mailNoList' item='item' index ='index' open='(' separator=',' close=')' > " +
            " #{item} " +
            "</foreach> " +
            "</script>")
    List<InventoryOrderRelationDO> findByMailNo(@Param("mailNoList") List<String> mailNoList);

}