package com.danding.cds.endorsement.impl.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.common.enums.InventoryCustomsTypeEnums;
import com.danding.cds.common.enums.InventoryDeclarationEnum;
import com.danding.cds.common.enums.InventoryTwoStepEnum;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.CustomsCharLengthUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.declare.sdk.model.checkList.Inv101MessageRequest;
import com.danding.cds.declare.sdk.model.checkList.InvtHeadType;
import com.danding.cds.declare.sdk.model.checkList.InvtItemBody;
import com.danding.cds.declare.sdk.model.checkList.InvtItemGoodsBody;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.dto.EndorsementItemDTO;
import com.danding.cds.endorsement.api.dto.EndorsementRelationDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import com.danding.cds.invenorder.api.enums.Inv101TrspModecd;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dto.InventoryOrderItemGoodsDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.enums.InventoryItemVerifyStrategy;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.cds.v2.util.InventoryOrderUtils;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Component
public class Inv101MessageRequestBuilder {

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private ProcessTradeBookService processTradeBookService;

    @Autowired
    private BaseDataService baseDataService;

    @DubboReference
    private CustomsDictionaryService dictionaryService;
    @DubboReference
    private EndorsementService endorsementService;
    @DubboReference
    private ExportOrderService exportOrderService;

    /**
     * 出库单生产表体
     *
     * @param old
     * @param delcareFlag 0-暂存 1-申报
     * @param dclTypecd   1-备案申请 2-变更申请 3-注销申请; （删除申请选3，默认选1）
     * @return
     * @throws ArgsErrorException
     */
    public Inv101MessageRequest buildRequestByExportOrder(EndorsementDTO old, Integer delcareFlag, Integer dclTypecd) {
        List<ExportItemDTO> itemList = exportOrderService.listItemByEndorsementId(old.getId());
        // 维护清单核注状态  核注终审中
        List<String> inventorySnList = itemList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
        customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, InventoryReviewStatus.DECLARING.getValue());
        Map<Long, EndorsementItemDTO> endorsementItemDOMap = endorsementService.preItemDTOs(old, itemList);

        Inv101MessageRequest request = new Inv101MessageRequest();
        //核注清单随附单据
        List<String> customsInventorySnList = itemList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
        List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.findBySnList(customsInventorySnList);
        List<String> invtNos = customsInventoryDTOList.stream().map(CustomsInventoryDTO::getInventoryNo).collect(Collectors.toList());
        request.setInvtNos(invtNos);
        //核注清单表体
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findById(new ArrayList<>(endorsementItemDOMap.keySet()));
        Map<Long, CustomsBookItemDTO> customsBookItemDTOMap = new HashMap<>();
        if (CollUtil.isNotEmpty(customsBookItemDTOList)) {
            customsBookItemDTOMap = customsBookItemDTOList.stream().collect(Collectors.toMap(CustomsBookItemDTO::getId, Function.identity(), (k1, k2) -> k1));
        }
        List<InvtItemBody> invtItemBodyList = new ArrayList<>();
        CustomsBookDTO bookResVo = baseDataService.getCustomsBookDTOById(old.getAccountBookId());
        Map<Long, GoodsRecordDTO> goodsRecordDTOMap = new HashMap<>();
        for (Map.Entry<Long, EndorsementItemDTO> itemDOEntry : endorsementItemDOMap.entrySet()) {
            EndorsementItemDTO endorsementItemDTO = itemDOEntry.getValue();
            // endorsementItemDTO 过滤掉非保类型的
            List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(endorsementItemDTO.getItemTag());
            if (orderItemTags.contains(OrderItemTagEnum.FB_GIFTS.getCode())) {
                continue;
            }
            CustomsBookItemDTO itemDTO = null;
            if (customsBookItemDTOMap.containsKey(itemDOEntry.getKey())) {
                itemDTO = customsBookItemDTOMap.get(itemDOEntry.getKey());
            } else {
                itemDTO = customsBookItemService.findById(itemDOEntry.getKey());
            }
            GoodsRecordDTO recordDTO = goodsRecordDTOMap.get(itemDOEntry.getKey());
            if (recordDTO == null) {
                String productId = endorsementItemDTO.getProductId();
                if (Objects.nonNull(endorsementItemDTO.getOriginProductId())) {
                    productId = endorsementItemDTO.getOriginProductId();
                }
                recordDTO = goodsRecordService.findByBookIdAndProId(old.getAccountBookId(), productId);
                if (Objects.isNull(recordDTO)) {
                    log.error("EndorsementServiceImpl 核注单推送 #push #getInv101MessageRequest 未查询到商品备案信息 customsBookId={} productId={} ", old.getAccountBookId(), endorsementItemDTO.getProductId());
                    throw new ArgsInvalidException("账册号:" + bookResVo.getBookNo() + " 下未找到备案 料号:" + endorsementItemDTO.getProductId());
                }
                goodsRecordDTOMap.put(itemDOEntry.getKey(), recordDTO);
            }
            InvtItemBody invtItemBody = new InvtItemBody();
            invtItemBody.setProductId(endorsementItemDTO.getProductId());
            invtItemBody.setItemRecordNo(endorsementItemDTO.getGoodsSeqNo());
            invtItemBody.setGcode(endorsementItemDTO.getHsCode());
            invtItemBody.setGname(endorsementItemDTO.getGoodsName());
            invtItemBody.setGmodel(itemDTO.getGoodsModel());
            invtItemBody.setCurrency(itemDTO.getCurrCode());
            //申报单位
            invtItemBody.setUnit(itemDTO.getGoodsUnit());
            invtItemBody.setQty(endorsementItemDTO.getDeclareUnitQfy().toString());
            //法一
            invtItemBody.setUnit1(itemDTO.getFirstUnit());
            invtItemBody.setTotalQty1(recordDTO.getFirstUnitAmount().multiply(endorsementItemDTO.getDeclareUnitQfy()).toString());
            //法二
            if (StringUtils.isNotEmpty(itemDTO.getSecondUnit())) {
                invtItemBody.setUnit2(itemDTO.getSecondUnit());
                if (recordDTO.getSecondUnitAmount() == null) {
                    recordDTO.setSecondUnitAmount(new BigDecimal(0));
                }
                if (endorsementItemDTO.getDeclareUnitQfy() == null) {
                    endorsementItemDTO.setDeclareUnitQfy(new BigDecimal(0));
                    endorsementItemDTO.setRemainDeclareUnitQfy(new BigDecimal(0));
                }
                invtItemBody.setTotalQty2(recordDTO.getSecondUnitAmount().multiply(endorsementItemDTO.getDeclareUnitQfy()).toString());
            }
            //价格
            invtItemBody.setPrice(itemDTO.getDeclarePrice().toString());
            invtItemBody.setTotalPrice(itemDTO.getDeclarePrice().multiply(endorsementItemDTO.getDeclareUnitQfy()).toString());
            invtItemBody.setCountry(itemDTO.getOriginCountry());
            invtItemBody.setDestCountry("142");//TODO:先默认中国
            //净重 毛重
            invtItemBody.setTotalGrossWeight(endorsementItemDTO.getGrossWeight().toString());
            invtItemBody.setTotalNetWeight(endorsementItemDTO.getNetWeight().toString());
            invtItemBody.setClyMarkcd("0");
            if (CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode().equals(bookResVo.getBookType())
                    && Objects.nonNull(itemDTO.getGoodsSource())) {
                invtItemBody.setParam1(itemDTO.getGoodsSource());
            }
            invtItemBodyList.add(invtItemBody);
        }
        request.setInvtItemBodyList(invtItemBodyList);
        CompanyDTO companyDTO = baseDataService.getCompanyDTOById(old.getDeclareCompanyId());
        CustomsBookDTO customsBookResVo = baseDataService.getCustomsBookDTOById(old.getAccountBookId());
        if (customsBookResVo != null) {
            request.setCustomsBookCode(customsBookResVo.getBookNo());
            request.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
        }
        request.setCustomsCompanyCode(companyDTO.getSpecialClientCode());
        return request;
    }

    /**
     * @param old
     * @param delcareFlag 0-暂存 1-申报
     * @param dclTypecd   1-备案申请 2-变更申请 3-注销申请; （删除申请选3，默认选1）
     * @return
     * @throws ArgsErrorException
     */
    public Inv101MessageRequest buildRequestByInvtOrder(EndorsementDTO old, Integer delcareFlag, Integer dclTypecd) throws ArgsErrorException {
        if (Objects.equals(delcareFlag, 1) && Objects.equals(dclTypecd, 1)) {
            // 申报推送才校验
            this.paramCheckFromInvOrder(old);
        }
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
        List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderIdAndEndorsementId(old.getInventoryOrderId(), old.getId());
        List<InventoryOrderRelationDTO> itemDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(old.getInventoryOrderId());
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(old.getAccountBookId());
//        Map<String, String> decTypeMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.CUSTOMS_DEC_TYPE.getValue());

        Inv101MessageRequest request = new Inv101MessageRequest();
        request.setEndorsementOrderNo(old.getSn());
        request.setPreEndorsementOrderNo(StringUtils.isNotBlank(old.getPreOrderNo()) ? old.getPreOrderNo() : "");
        request.setRealEndorsementOrderNo(StringUtils.isNotBlank(old.getRealOrderNo()) ? old.getRealOrderNo() : "");
        request.setBussinessType(old.getBussinessType());
        request.setDelcareFlag(delcareFlag);
        InvtHeadType headType = new InvtHeadType();
        headType.setDclTypecd(Objects.isNull(dclTypecd) ? "1" : dclTypecd.toString());
        //区内流转
        if (EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode().equals(old.getBussinessType())
                || EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode().equals(old.getBussinessType())) {
            //表头
            headType.setImpexpMarkCd(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode().equals(old.getBussinessType()) ? "I" : "E");//进出口标志：进口
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "6");//清单类型：区内流转
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1200");//监管方式：保税间货物
            headType.setTrspModecd(StringUtils.isEmpty(inventoryOrderInfoDTO.getTransportMode())
                    ? "9" : inventoryOrderInfoDTO.getTransportMode());//运输方式：其他运输
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setRltPutrecNo(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode().equals(old.getBussinessType()) ? inventoryOrderInfoDTO.getOutAccountBook() : inventoryOrderInfoDTO.getInAccountBook());//关联账册编号
            //区内流转 转出 才需要关联清单编号
            if (EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode().equals(old.getBussinessType())) {
                headType.setRltInvtNo(inventoryOrderInfoDTO.getAssociatedEndorsementNo());
            }
        }
        //退货入区
        else if (EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode().equals(old.getBussinessType())) {
            //表头
            headType.setImpexpMarkCd("I");//进出口标志：进口
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "8");//清单类型：保税电商
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1210");//监管方式：保税电商
            headType.setTrspModecd(StringUtils.isEmpty(inventoryOrderInfoDTO.getTransportMode())
                    ? "4" : inventoryOrderInfoDTO.getTransportMode());//运输方式：公路运输
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            //退货直接申报
            //request.setDelcareFlag(1);
        }
        //一体化进出区
        else if (EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("I");//进出口标志：进口
            if (Objects.equals(inventoryOrderInfoDTO.getTwoStepFlag(), InventoryTwoStepEnum.TWO_STEP.getCode())) {
                headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "7");//清单类型：区港联动(7)普通清单(0)
                //区港联动有对应报关单号
                headType.setEntryNo(inventoryOrderInfoDTO.getCustomsEntryNo());
            } else {
                headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：区港联动(7)普通清单(0)
            }
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1210");//监管方式：保税电商
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式 TODO:前端界面选择
            headType.setDclcusFlag("1");//是否报关标志:报关
            headType.setGenDecFlag("1");//是否生成报关单：生成
            headType.setStshipTrsarvNatcd(inventoryOrderInfoDTO.getShipmentCountry());//启运国 TODO:前端界面选择
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进境关别 TODO:前端界面选择
            headType.setDclcusTypeCd("2");//报关类型代码:对应报关
            if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())) { //报关类型代码:取清关单
                headType.setDclcusTypeCd(inventoryOrderInfoDTO.getCustomsType().toString());
            }

            // 这里取下清关单配置数据(是否生成报关单：生成)
            Integer declarationFlag = inventoryOrderInfoDTO.getDeclarationFlag();
            if (Objects.equals(InventoryDeclarationEnum.NOT_GENERATE.getCode(), declarationFlag)) {
                headType.setGenDecFlag("2");
            }

            //对应报关单申报单位信息
            CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            if (Objects.isNull(corrCusDeclareCompany)) {
                corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCustomsEntryCompany());
            }
            if (Objects.isNull(corrCusDeclareCompany)) {
                throw new ArgsErrorException("对应报关单申报单位信息不能为空");
            }
            headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
            headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
            headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());

        }
        //区间流转
        else if (EndorsementBussiness.BUSSINESS_SECTION_IN.getCode().equals(old.getBussinessType())
                || EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode().equals(old.getBussinessType()) ? "I" : "E");//进出口标志
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1200");//监管方式：保税间货物
            headType.setTrspModecd(StringUtils.isEmpty(inventoryOrderInfoDTO.getTransportMode())
                    ? "9" : inventoryOrderInfoDTO.getTransportMode());//运输方式：其他运输
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            //区间流转 转出 才需要关联清单编号
            if (EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode().equals(old.getBussinessType())) {
                headType.setRltPutrecNo(inventoryOrderInfoDTO.getInAccountBook());
                headType.setRltInvtNo(inventoryOrderInfoDTO.getAssociatedEndorsementNo());
            } else if (EndorsementBussiness.BUSSINESS_SECTION_IN.getCode().equals(old.getBussinessType())) {
                headType.setRltPutrecNo(inventoryOrderInfoDTO.getOutAccountBook());
                if (Objects.equals(Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey(), inventoryOrderInfoDTO.getTransportMode())) {
                    // 区间流转入 + 特殊综合保税区
                    CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
                    if (Objects.isNull(corrCusDeclareCompany)) {
                        throw new ArgsErrorException("对应报关单申报单位信息不能为空");
                    }
                    headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
                    headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
                    headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());
                    headType.setDclcusFlag(Objects.nonNull(inventoryOrderInfoDTO.getCustomsFlag())
                            ? (inventoryOrderInfoDTO.getCustomsFlag()).toString() : "1");//是否报关标志:默认报关
                    headType.setGenDecFlag(Objects.nonNull(inventoryOrderInfoDTO.getDeclarationFlag())
                            ? (inventoryOrderInfoDTO.getDeclarationFlag()).toString() : "1");//是否生成报关单：默认生成
                    headType.setDclcusTypeCd(Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())
                            ? (inventoryOrderInfoDTO.getCustomsType()).toString() : "2");//报关类型代码:对应报关
                    headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
                }
            }
        }
        //销毁
        else if (EndorsementBussiness.BUSSINESS_DESTORY.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("I");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "0200");//监管方式：保税间货物
            headType.setTrspModecd(StringUtils.isEmpty(inventoryOrderInfoDTO.getTransportMode())
                    ? "9" : inventoryOrderInfoDTO.getTransportMode());//运输方式：其他运输
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
        }
        //一线退运
        else if (EndorsementBussiness.BUSINESS_ONELINE_REFUND.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("E");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "4561");//监管方式：区内物流货物
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("1");//是否报关标志:报关
            headType.setGenDecFlag(Objects.equals(1, inventoryOrderInfoDTO.getDeclarationFlag()) ? "1" : "2");//是否生成报关单：取清关单
            headType.setStshipTrsarvNatcd(inventoryOrderInfoDTO.getShipmentCountry());//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setDclcusTypeCd(InventoryCustomsTypeEnums.CORRESPOND_CUSTOMS.getCode().toString());//报关类型：对应报关
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            if (Objects.isNull(corrCusDeclareCompany)) {
                throw new ArgsErrorException("对应报关单申报单位信息不能为空");
            }
            //对应报关申报单位
            headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
            headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
            headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
        }
        //保税物流转大贸
        else if (EndorsementBussiness.BUSINESS_BONDED_TO_TRADE.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("E");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "5000");//监管方式：料件进出区
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("1");//是否报关标志:报关
            headType.setGenDecFlag(Objects.equals(1, inventoryOrderInfoDTO.getDeclarationFlag()) ? "1" : "2");//是否生成报关单：取清关单
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setDclcusTypeCd(InventoryCustomsTypeEnums.RELATE_CUSTOMS.getCode().toString());//报关类型：关联报关
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            CompanyDTO rltCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
            if (Objects.isNull(rltCusDeclareCompany)) {
                throw new ArgsErrorException("关联报关申报单位信息不能为空");
            }
            headType.setRltEntryDclEtpsNm(rltCusDeclareCompany.getName());
            headType.setRltEntryDclEtpsNo(rltCusDeclareCompany.getCode());
            headType.setRltEntryDclEtpsSccd(rltCusDeclareCompany.getUniformSocialCreditCode());
            CompanyDTO rltCusInnerSFHRCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
            if (Objects.isNull(rltCusInnerSFHRCompany)) {
                throw new ArgsErrorException("关联境内收发货人信息不能为空");
            }
            headType.setRltEntryBizopEtpsNm(rltCusInnerSFHRCompany.getName());
            headType.setRltEntryBizopEtpsNo(rltCusInnerSFHRCompany.getCode());
            headType.setRltEntryBizopEtpsSccd(rltCusInnerSFHRCompany.getUniformSocialCreditCode());
            CompanyDTO rltCusXFDYCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getRltCusXFDYCompanyId());
            if (Objects.isNull(rltCusXFDYCompany)) {
                throw new ArgsErrorException("关联报关单消费使用单位信息不能为空");
            }
            headType.setRltEntryRvsngdEtpsNm(rltCusXFDYCompany.getName());
            headType.setRltEntryRvsngdEtpsNo(rltCusXFDYCompany.getCode());
            headType.setRltEntryRvsngdEtpsSccd(rltCusXFDYCompany.getUniformSocialCreditCode());
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
        }
        //后续补税
        else if (EndorsementBussiness.BUSINESS_SUBSEQUENT_TAX.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("I");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "9700");//监管方式：后续补税
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("1");//是否报关标志:报关
            headType.setGenDecFlag(Objects.nonNull(inventoryOrderInfoDTO.getDeclarationFlag())
                    ? inventoryOrderInfoDTO.getDeclarationFlag().toString() : "1");//是否生成报关单：生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
            //报关类型：对应报关
            headType.setDclcusTypeCd(Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())
                    ? inventoryOrderInfoDTO.getCustomsType().toString() : InventoryCustomsTypeEnums.CORRESPOND_CUSTOMS.getCode().toString());
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            if (Objects.isNull(corrCusDeclareCompany)) {
                throw new ArgsErrorException("对应报关单申报单位信息不能为空");
            }
            headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
            headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
            headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());
        }
        // 保税物流一线入境
        else if (EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("I");//进出口标志：进口
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "5034");//监管方式：区内物流货物
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("1");//是否报关标志:报关
            headType.setGenDecFlag(Objects.equals(1, inventoryOrderInfoDTO.getDeclarationFlag()) ? "1" : "2");//是否生成报关单：取清关单
            headType.setStshipTrsarvNatcd(inventoryOrderInfoDTO.getShipmentCountry());//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
            headType.setDclcusTypeCd(Objects.nonNull(inventoryOrderInfoDTO.getCustomsType()) ? inventoryOrderInfoDTO.getCustomsType().toString()
                    : InventoryCustomsTypeEnums.CORRESPOND_CUSTOMS.getCode().toString());//报关类型：对应报关
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            if (Objects.isNull(corrCusDeclareCompany)) {
                throw new ArgsErrorException("对应报关单申报单位信息不能为空");
            }
            headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
            headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
            headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());
        }
        // 盘盈
        else if (EndorsementBussiness.BUSINESS_INVENTORY_PROFIT.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("I");//进出口标志：进口
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "AAAA");//监管方式：库存调整
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
        }
        // 抽检申报
        else if (EndorsementBussiness.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("E");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "AAAA");//监管方式：库存调整
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
        } else if (EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd("E");
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "4");//清单类型：普通清单
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1200");//监管方式：库存调整
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式：取清关单
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进出境关别
            headType.setMtpckEndprdMarkcd("I");//料件成品标志: I：料件，E：成品
            headType.setApplyNo(inventoryOrderInfoDTO.getDeclareFormNo());
            headType.setRltPutrecNo(inventoryOrderInfoDTO.getInAccountBook());
            headType.setRltInvtNo(inventoryOrderInfoDTO.getAssociatedEndorsementNo());

            // 料件表体

            AtomicInteger idx = new AtomicInteger(1);
            List<InvtItemGoodsBody> invtItemGoodsBodyList = itemDTOS.stream()
                    .map(item -> {
                        String seqNo = String.valueOf(idx.getAndIncrement());
                        InvtItemGoodsBody invtItemGoodsBody = new InvtItemGoodsBody();
                        invtItemGoodsBody.setSeqNo(old.getPreOrderNo());
                        if (Objects.isNull(item.getDeclareFormItemSeqNo())) {
                            throw new ArgsInvalidException("简单加工-申报表序号不能为空");
                        }
                        invtItemGoodsBody.setApplyTbSeqno(item.getDeclareFormItemSeqNo().toString());
                        invtItemGoodsBody.setGdsSeqNo(seqNo);
                        invtItemGoodsBody.setPutrecSeqNo(item.getGoodsSeqNo());
                        invtItemGoodsBody.setGdsMtno(item.getProductId());
                        invtItemGoodsBody.setGdecd(item.getHsCode());
                        invtItemGoodsBody.setGdsNm(item.getGoodsName());
                        invtItemGoodsBody.setGdsSpcfModelDesc(item.getGoodsModel());
                        invtItemGoodsBody.setDclUnitcd(item.getUnit());
                        invtItemGoodsBody.setLawfUnitcd(item.getFirstUnit());
                        invtItemGoodsBody.setSecdLawfUnitcd(item.getSecondUnit());
                        invtItemGoodsBody.setNatcd(item.getOriginCountry());
                        invtItemGoodsBody.setDclUprcAmt(item.getDeclarePrice().setScale(4, RoundingMode.HALF_UP).toString());
                        BigDecimal declareTotalPrice = Objects.nonNull(item.getDeclareTotalPrice()) ?
                                item.getDeclareTotalPrice() : item.getDeclarePrice().multiply(item.getDeclareUnitQfy());
                        invtItemGoodsBody.setDclTotalAmt(declareTotalPrice.setScale(2, RoundingMode.HALF_UP).toString());
                        invtItemGoodsBody.setDclCurrcd(item.getCurrency());
                        invtItemGoodsBody.setLawfQty(item.getFirstUnitQfy().multiply(item.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP).toString());
                        if (item.getSecondUnitQfy() != null) {
                            invtItemGoodsBody.setSecdLawfQty(item.getSecondUnitQfy().multiply(item.getDeclareUnitQfy()).setScale(4, RoundingMode.HALF_UP).toString());
                        }
                        invtItemGoodsBody.setDclQty(item.getDeclareUnitQfy().setScale(5, RoundingMode.HALF_UP).toString());
                        BigDecimal totalGrossWeight = item.getTotalGrossWeight() != null ?
                                item.getTotalGrossWeight() : item.getGrossWeight().multiply(item.getDeclareUnitQfy());
                        BigDecimal totalNetWeight = item.getTotalNetWeight() != null ?
                                item.getTotalNetWeight() : item.getNetweight().multiply(item.getDeclareUnitQfy());
                        invtItemGoodsBody.setGrossWt(totalGrossWeight.setScale(5, RoundingMode.HALF_UP).toString());
                        invtItemGoodsBody.setNetWt(totalNetWeight.setScale(5, RoundingMode.HALF_UP).toString());
                        invtItemGoodsBody.setLvyrlfModecd(item.getAvoidTaxMethod());
                        invtItemGoodsBody.setUcnsVerno(item.getOrderVersion());
                        invtItemGoodsBody.setEntryGdsSeqno(seqNo);
                        invtItemGoodsBody.setDestinationNatcd(item.getDestinationCountry());
                        invtItemGoodsBody.setModfMarkcd("0");
                        if (CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode().equals(customsBookDTO.getBookType())
                                && Objects.nonNull(item.getGoodsSource())) {
                            invtItemGoodsBody.setParam1(item.getGoodsSource());
                        }
                        return invtItemGoodsBody;
                    }).collect(Collectors.toList());
            request.setInvtItemGoodsBodyList(invtItemGoodsBodyList);
        } else if (EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode().equals(old.getBussinessType())
                || EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode().equals(old.getBussinessType())) {
            headType.setImpexpMarkCd(EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode().equals(old.getBussinessType()) ? "I" : "E");//进出口标志：进口
            headType.setMtpckEndprdMarkcd(EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode().equals(old.getBussinessType()) ? "I" : "E");//料件成品标志: I：料件，E：成品
            headType.setInvtType(StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "0");//清单类型：普通清单(0)
            headType.setSupvModecd(StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "5014");//监管方式：区内来料加工
            headType.setTrspModecd(inventoryOrderInfoDTO.getTransportMode());//运输方式
            headType.setDclcusFlag(Objects.equals(inventoryOrderInfoDTO.getCustomsFlag(), 1) ? "1" : "2");//是否报关标志:报关
            headType.setGenDecFlag(Objects.equals(inventoryOrderInfoDTO.getDeclarationFlag(), 1) ? "1" : "2");//是否生成报关单：生成
            headType.setStshipTrsarvNatcd(inventoryOrderInfoDTO.getShipmentCountry());//启运国
            headType.setImpexpPortcd(inventoryOrderInfoDTO.getEntryExitCustoms());//进境关别
            headType.setDecType(inventoryOrderInfoDTO.getCustomsEntryType());
            headType.setDclcusTypeCd("2");//报关类型代码:对应报关
            if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())) { //报关类型代码:取清关单
                headType.setDclcusTypeCd(inventoryOrderInfoDTO.getCustomsType().toString());
            }
            //对应报关单申报单位信息
            CompanyDTO corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            if (Objects.isNull(corrCusDeclareCompany)) {
                corrCusDeclareCompany = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getCustomsEntryCompany());
            }
            if (Objects.isNull(corrCusDeclareCompany)) {
                throw new ArgsErrorException("对应报关单申报单位信息不能为空");
            }
            headType.setCorrEntryDclEtpsNm(corrCusDeclareCompany.getName());
            headType.setCorrEntryDclEtpsNo(corrCusDeclareCompany.getCode());
            headType.setCorrEntryDclEtpsSccd(corrCusDeclareCompany.getUniformSocialCreditCode());
        }


        //核注清单随附电商单
        List<String> invtNos = new ArrayList<>();
        if (!CollectionUtils.isEmpty(itemDTOList)) {
            List<String> logisticsNoList = itemDTOList.stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.toList());
            List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.listByLogistics90Days(logisticsNoList);
            invtNos = customsInventoryDTOS.stream().map(CustomsInventoryDTO::getInventoryNo).collect(Collectors.toList());
        }
        //核注清单表体
        List<InvtItemBody> invtItemBodyList = new ArrayList<>();
        int idx = 1;
        List<InventoryOrderItemDTO> invtItemBodyDTOList = itemDTOS;
        if (EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode().equals(old.getBussinessType())) {
            List<InventoryOrderItemGoodsDTO> inventoryOrderItemGoodsDTOS = inventoryOrderInfoService.listItemGoodsByInventoryId(inventoryOrderInfoDTO.getId());
            invtItemBodyDTOList = ConvertUtil.listConvert(inventoryOrderItemGoodsDTOS, InventoryOrderItemDTO.class);
        }
        for (InventoryOrderItemDTO param : invtItemBodyDTOList) {
            InvtItemBody invtItemBody = new InvtItemBody();
            invtItemBody.setProductId(param.getProductId());
            invtItemBody.setItemRecordNo(param.getGoodsSeqNo());
            invtItemBody.setGcode(param.getHsCode());
            invtItemBody.setGname(param.getGoodsName());
            invtItemBody.setGmodel(param.getGoodsModel());
            invtItemBody.setCurrency(StringUtils.isNotBlank(param.getCurrency()) ? param.getCurrency() : "142");//退货默认人民币
            //申报单位
            invtItemBody.setUnit(param.getUnit());
            invtItemBody.setQty(param.getDeclareUnitQfy().toString());
            //法一
            invtItemBody.setUnit1(param.getFirstUnit());
            invtItemBody.setTotalQty1(param.getFirstUnitQfy().multiply(param.getDeclareUnitQfy()).toString());
            //法二
            if (StringUtils.isNotEmpty(param.getSecondUnit())) {
                invtItemBody.setUnit2(param.getSecondUnit());
                invtItemBody.setTotalQty2(param.getSecondUnitQfy().multiply(param.getDeclareUnitQfy()).toString());
            }
            //价格
            invtItemBody.setPrice(param.getDeclarePrice().toString());
            invtItemBody.setTotalPrice(param.getDeclarePrice().multiply(param.getDeclareUnitQfy()).toString());
            invtItemBody.setCountry(param.getOriginCountry());
            invtItemBody.setDestCountry(param.getDestinationCountry() != null ? param.getDestinationCountry() : "142");//TODO:先默认中国
            if (EndorsementBussiness.BUSINESS_ONELINE_REFUND.getCode().equals(old.getBussinessType())) {
                invtItemBody.setDestCountry(param.getDestinationCountry()); /* 一线退运 最终目的国取清关单表体 */
            }
//            if (!Objects.equals(EndorsementBussiness.BUSSINESS_DESTORY.getCode(), old.getBussinessType())
//                    && !Objects.equals(EndorsementBussiness.BUSSINESS_EMPTY.getCode(), old.getBussinessType())) {
//            }
            invtItemBody.setClyMarkcd(param.getDangerousFlag());
            if (!InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.AUTO_TRANSFER)) {
                if (CollUtil.isNotEmpty(param.getVerifyResultList()) && param.getVerifyResultList().stream()
                        .anyMatch(v -> v.getCode().equals(InventoryItemVerifyStrategy.DIFF_DECLARE_UNIT.getCode()))) {
                    throw new ArgsErrorException("海关备案料号:" + param.getProductId() + "，申报单位不符，请检查后重新添加或重新生成表体");
                }
            }
            if (Objects.equals(headType.getGenDecFlag(), "1")) {
                invtItemBody.setEntryGdsSeqno(String.valueOf(idx++));
            }
            if (Objects.equals(old.getBussinessType(), EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode())) {
                if (param.getDeclareFormItemSeqNo() == null) {
                    throw new ArgsErrorException("简单加工-申报表序号不能为空");
                }
                invtItemBody.setApplyTbSeqno(param.getDeclareFormItemSeqNo().toString());
            }
            if (CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode().equals(customsBookDTO.getBookType())
                    && Objects.nonNull(param.getGoodsSource())) {
                invtItemBody.setParam1(param.getGoodsSource());
            }
            // 保税加工 单耗版本号
            if (old.getBussinessType().equals(EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode())) {
                List<ProcessTradeBookConsumptionDTO> consumptionDTOList = processTradeBookService.findConsumptionByProductIdAndBookId(2, param.getProductId(), old.getAccountBookId());
                log.info("保税交工一线出境 维护单耗版本号 inventoryOrderItemDTO={}, consumptionDTOList={}", JSON.toJSONString(param), JSON.toJSONString(consumptionDTOList));
                if (Objects.nonNull(consumptionDTOList)) {
                    consumptionDTOList.forEach(consumptionDTO -> {
                        InvtItemBody duplicateInvtItemBody = new InvtItemBody();
                        BeanUtils.copyProperties(invtItemBody, duplicateInvtItemBody);
                        duplicateInvtItemBody.setUcnsVerno(consumptionDTO.getConsumptionVersionNo());
                        invtItemBodyList.add(duplicateInvtItemBody);
                    });
                } else {
                    invtItemBodyList.add(invtItemBody);
                }
            } else {
                invtItemBodyList.add(invtItemBody);
            }
        }
        if (StrUtil.isNotBlank(old.getRemark())) {
            headType.setRemark(CustomsCharLengthUtils.subStringByCharLength(old.getRemark(), 255));
        }
        request.setInvtHeadType(headType);
        request.setInvtNos(invtNos);
        request.setInvtItemBodyList(invtItemBodyList);
        CompanyDTO companyDTO = baseDataService.getCompanyDTOById(old.getDeclareCompanyId());
        request.setCustomsCompanyCode(companyDTO.getSpecialClientCode());
        if (StringUtil.isNotEmpty(old.getInvtType())) {
            headType.setInvtType(old.getInvtType());
        }
        if (StringUtil.isNotEmpty(old.getIcCardNo())) {
            headType.setIcCardNo(old.getIcCardNo());
        }
        if (StringUtil.isNotEmpty(old.getSupvMode())) {
            headType.setSupvModecd(old.getSupvMode());
        }
        if (customsBookDTO != null) {
            request.setCustomsBookCode(customsBookDTO.getBookNo());
            request.setCustomsAreaCode(customsBookDTO.getCustomsAreaCode());
        }
        return request;
    }

    private void paramCheckFromInvOrder(EndorsementDTO old) {
        if (!EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode().equals(old.getBussinessType())) {
            StringBuffer errorMessage = new StringBuffer();
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
            String inveCustomsSn = inventoryOrderInfoDTO.getInveCustomsSn();
            String inveBusinessType = inventoryOrderInfoDTO.getInveBusinessType();
            //区内入
            if (InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode().equals(inveBusinessType)) {
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getOutAccountBook())) {
                    errorMessage.append("关联转出账册不能为空！;");
                }
            }
            //区内出
            else if (InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode().equals(inveBusinessType)) {
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getInAccountBook())) {
                    errorMessage.append("关联转入账册不能为空！;");
                }
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getAssociatedEndorsementNo())) {
                    errorMessage.append("关联核注清单编号不能为空！;");
                }
            }
            //区间入
            else if (InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode().equals(inveBusinessType)) {
                if (!Objects.equals(inventoryOrderInfoDTO.getTransportMode(), Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey())
                        && StringUtils.isEmpty(inventoryOrderInfoDTO.getOutAccountBook())) {
                    errorMessage.append("关联转出账册不能为空！;");
                }
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getEntryExitCustoms())) {
                    errorMessage.append("进出境关别不能为空！;");
                }
                if (Objects.equals(inventoryOrderInfoDTO.getTransportMode(), Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey())) {
                    if (Objects.isNull(inventoryOrderInfoDTO.getCustomsFlag())) {
                        errorMessage.append("报关标志不能为空！;");
                    }
                    if (Objects.isNull(inventoryOrderInfoDTO.getDeclarationFlag())) {
                        errorMessage.append("是否生成报关单不能为空！;");
                    }
                    if (Objects.isNull(inventoryOrderInfoDTO.getCustomsEntryType())) {
                        errorMessage.append("报关单类型不能为空！;");
                    }
                    if (Objects.isNull(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId())) {
                        errorMessage.append("对应报关单申报单位不能为空！;");
                    }
                }
            }
            //区间出
            else if (InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode().equals(inveBusinessType)) {
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getInAccountBook())) {
                    errorMessage.append("关联转入账册不能为空！;");
                }
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getAssociatedEndorsementNo())) {
                    errorMessage.append("关联核注清单编号不能为空！;");
                }
                if (StringUtils.isEmpty(inventoryOrderInfoDTO.getEntryExitCustoms())) {
                    errorMessage.append("进出境关别不能为空！;");
                }
            }
            //一线
            else if (InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode().equals(inveBusinessType)) {
                if (Objects.equals(inventoryOrderInfoDTO.getTwoStepFlag(), InventoryTwoStepEnum.TWO_STEP.getCode())
                        && StringUtils.isEmpty(inventoryOrderInfoDTO.getCustomsEntryNo())) {
                    errorMessage.append("一线入境（区港联动）报关单号不能为空！;");
                }
                if (LongUtil.isNone(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId()) || null == inventoryOrderInfoDTO.getCustomsEntryType()) {
                    errorMessage.append("对应报关企业|对应报关单类型不能为空！;");
                }
            }
            if (InventoryOrderUtils.checkTwoStepAndDeclareWay(inventoryOrderInfoDTO)
                    && !Objects.equals(inventoryOrderInfoDTO.getTallyComplete(), 1)) {
                // 先理后报
                errorMessage.append("清关单先理后报，未接收到理货报告！;");
            }
            if (errorMessage.length() > 0) {
                errorMessage.insert(0, "清关单号:" + inveCustomsSn);
                log.error("推送核注异常:" + errorMessage);
                throw new ArgsInvalidException(errorMessage.toString());
            }
        }
    }

    /**
     * 导出生产表体
     *
     * @param old
     * @param delcareFlag 0-暂存 1-申报
     * @param dclTypecd   1-备案申请 2-变更申请 3-注销申请; （删除申请选3，默认选1）
     * @return
     * @throws ArgsErrorException
     */
    public Inv101MessageRequest buildRequestByExcelImport(EndorsementDTO old, Integer delcareFlag, Integer dclTypecd) {
        Inv101MessageRequest request = new Inv101MessageRequest();
        request.setDelcareFlag(delcareFlag);
        InvtHeadType headType = new InvtHeadType();
        if (EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode().equals(old.getBussinessType())) {
            //表头
            headType.setImpexpMarkCd("I");//进出口标志：进口
            headType.setInvtType(com.github.pagehelper.util.StringUtil.isNotEmpty(old.getInvtType()) ? old.getInvtType() : "8");//清单类型：保税电商
            headType.setSupvModecd(com.github.pagehelper.util.StringUtil.isNotEmpty(old.getSupvMode()) ? old.getSupvMode() : "1210");//监管方式：保税电商
            headType.setTrspModecd(StringUtils.isEmpty(old.getTransportMode()) ? "4" : old.getTransportMode());//运输方式：公路运输
            headType.setDclcusFlag("2");//是否报关标志:非报关
            headType.setGenDecFlag("2");//是否生成报关单：不生成
            headType.setStshipTrsarvNatcd("142");//启运国
            headType.setDclTypecd(String.valueOf(dclTypecd));
            //退货直接申报
            request.setInvtHeadType(headType);
        }
        List<EndorsementRelationDTO> endorsementRelationDTOS = endorsementService.listRelationById(old.getId());
        List<String> invtNos = endorsementRelationDTOS.stream().map(EndorsementRelationDTO::getInventoryNo).collect(Collectors.toList());
        request.setInvtNos(invtNos);
        CustomsBookDTO bookResVo = baseDataService.getCustomsBookDTOById(old.getAccountBookId());
        List<InvtItemBody> invtItemBodyList = new ArrayList<>();
        List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemById(old.getId());
        for (EndorsementItemDTO itemDTO : endorsementItemDTOS) {
            InvtItemBody invtItemBody = new InvtItemBody();
            invtItemBody.setProductId(itemDTO.getProductId());
            invtItemBody.setItemRecordNo(itemDTO.getGoodsSeqNo());
            invtItemBody.setGcode(itemDTO.getHsCode());
            invtItemBody.setGname(itemDTO.getGoodsName());
            invtItemBody.setGmodel(itemDTO.getGoodsModel());
            invtItemBody.setCurrency(itemDTO.getCurrency());
            //申报单位
            invtItemBody.setUnit(itemDTO.getDeclareUnit());
            invtItemBody.setQty(itemDTO.getDeclareUnitQfy().toString());
            //法一
            invtItemBody.setUnit1(itemDTO.getFirstUnit());
            invtItemBody.setTotalQty1(itemDTO.getFirstUnitQfy().toString());
            //法二
            if (StringUtils.isNotEmpty(itemDTO.getSecondUnit())) {
                invtItemBody.setUnit2(itemDTO.getSecondUnit());
                BigDecimal secondUnitQfy = itemDTO.getSecondUnitQfy() == null ? BigDecimal.ZERO : itemDTO.getSecondUnitQfy();
                invtItemBody.setTotalQty2(secondUnitQfy.toString());
            }
            //价格
            invtItemBody.setPrice(itemDTO.getDeclarePrice().toString());
            invtItemBody.setTotalPrice(itemDTO.getDeclarePrice().multiply(itemDTO.getDeclareUnitQfy()).toString());
            invtItemBody.setCountry(itemDTO.getOriginCountry());
            invtItemBody.setDestCountry(itemDTO.getDestinationCountry() == null ? "142" : itemDTO.getDestinationCountry());
            //净重 毛重
            invtItemBody.setTotalGrossWeight(itemDTO.getGrossWeight().toString());
            invtItemBody.setTotalNetWeight(itemDTO.getNetWeight().toString());
            invtItemBody.setClyMarkcd("0");
            if (CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode().equals(bookResVo.getBookType())
                    && Objects.nonNull(itemDTO.getGoodsSource())) {
                invtItemBody.setParam1(itemDTO.getGoodsSource());
            }
            invtItemBodyList.add(invtItemBody);
        }
        request.setInvtItemBodyList(invtItemBodyList);

        if (StrUtil.isNotBlank(old.getRemark())) {
            headType.setRemark(CustomsCharLengthUtils.subStringByCharLength(old.getRemark(), 255));
        }
        if (StringUtil.isNotEmpty(old.getInvtType())) {
            headType.setInvtType(old.getInvtType());
        }
        if (StringUtil.isNotEmpty(old.getIcCardNo())) {
            headType.setIcCardNo(old.getIcCardNo());
        }
        if (StringUtil.isNotEmpty(old.getSupvMode())) {
            headType.setSupvModecd(old.getSupvMode());
        }
        request.setInvtHeadType(headType);
        request.setInvtItemBodyList(invtItemBodyList);
        CompanyDTO companyDTO = baseDataService.getCompanyDTOById(old.getDeclareCompanyId());
        if (bookResVo != null) {
            request.setCustomsBookCode(bookResVo.getBookNo());
            request.setCustomsAreaCode(bookResVo.getCustomsAreaCode());
        }
        request.setCustomsCompanyCode(companyDTO.getSpecialClientCode());
        return request;
    }
}
