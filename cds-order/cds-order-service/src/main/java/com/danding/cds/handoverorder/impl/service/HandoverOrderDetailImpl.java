package com.danding.cds.handoverorder.impl.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailParam;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailVO;
import com.danding.cds.handoverOrder.api.enums.HandoverDetailOutBoundStatus;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.handoverorder.impl.entity.HandoverOrderDetailDO;
import com.danding.cds.handoverorder.impl.mapper.HandoverOrderDetailMapper;
import com.danding.common.utils.CopyUtil;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 交接单明细
 * @date 2021/12/14
 */
@Slf4j
@DubboService
public class HandoverOrderDetailImpl implements HandoverOrderDetailService {
    @DubboReference
    private HandoverOrderService handoverOrderService;
    @Autowired
    private HandoverOrderDetailMapper handoverOrderDetailMapper;

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @PageSelect
    @Override
    public ListVO<HandoverOrderDetailVO> paging(HandoverOrderDetailParam param) {
        Example example = new Example(HandoverOrderDetailDO.class);
        example.orderBy("outHouseTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (Objects.isNull(param.getHandoverSn())) {
            throw new ArgsErrorException("交接单号不能为空！");
        }
        criteria.andEqualTo("handoverSn", param.getHandoverSn());
        if (Objects.nonNull(param.getQueryInfo())) {
            List<String> Info = Arrays.asList(param.getQueryInfo().split(","));
            if (Objects.equals(param.getQueryType(), "mailNo")) {
                criteria.andIn("wayBillSn", Info);
            } else if (Objects.equals(param.getQueryType(), "sn")) {
                criteria.andIn("declareOrderNo", Info);
            }
        }
        if (Objects.nonNull(param.getAssociateOutboundStatus())) {
            criteria.andEqualTo("associateOutboundStatus", param.getAssociateOutboundStatus());
        }
        if (Objects.nonNull(param.getUnusualStatus())) {
            criteria.andEqualTo("unusualStatus", param.getUnusualStatus());
        }
        Long createFrom = LongUtil.getFrom(param.getCreateFrom(), param.getCreateTo());
        Long createTo = LongUtil.getEnd(param.getCreateFrom(), param.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            criteria.andBetween("createTime",
                    new DateTime(createFrom).toString("yyyy-MM-dd HH:mm:ss"),
                    new DateTime(createTo).toString("yyyy-MM-dd HH:mm:ss"));
        }
        example.and(criteria);
        List<HandoverOrderDetailDO> handoverOrderDetailDOList = handoverOrderDetailMapper.selectByExample(example);
        List<HandoverOrderDetailDO> orderDetailDOList = new ArrayList<>();
        for (HandoverOrderDetailDO detailDO : handoverOrderDetailDOList) {
            HandoverOrderDetailDO orderDetailDO = BeanUtil.copyProperties(detailDO, HandoverOrderDetailDO.class);
            if (Objects.isNull(detailDO.getDeclareOrderNo())) {
                orderDetailDO.setDeclareOrderNo(detailDO.getUnusualMsg());
            }
            orderDetailDOList.add(orderDetailDO);
        }
        ListVO<HandoverOrderDetailVO> result = new ListVO<>();
        result.setDataList(CopyUtil.copyList(orderDetailDOList, HandoverOrderDetailVO.class));
        // 分页
        PageInfo<HandoverOrderDetailDO> pageInfo = new PageInfo(handoverOrderDetailDOList);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(param.getPageSize());
        result.setPage(pageResult);
        return result;
    }


    /**
     * 批量新增交接单明细
     *
     * @param detailDTOList
     */
    @Override
    public void addListDetail(List<HandoverOrderDetailDTO> detailDTOList) {
        List<HandoverOrderDetailDO> orderDetailDTOList = detailDTOList.stream().map(dto -> {
            HandoverOrderDetailDO handoverOrderDetailDO = new HandoverOrderDetailDO();
            BeanUtils.copyProperties(dto, handoverOrderDetailDO);
            UserUtils.setCreateAndUpdateBy(handoverOrderDetailDO);
            return handoverOrderDetailDO;
        }).collect(Collectors.toList());
        handoverOrderDetailMapper.insertList(orderDetailDTOList);
    }

    @Override
    public void updateListDetail(List<HandoverOrderDetailDTO> orderDetailDTOList) {
        orderDetailDTOList.stream().filter(o -> Objects.nonNull(o.getId())).forEach(
                o -> {
                    HandoverOrderDetailDO handoverOrderDetailDO = ConvertUtil.beanConvert(o, HandoverOrderDetailDO.class);
                    UserUtils.setUpdateBy(handoverOrderDetailDO);
                    handoverOrderDetailDO.setUpdateTime(new Date());
                    handoverOrderDetailMapper.updateByPrimaryKey(handoverOrderDetailDO);
                }
        );
    }

    /**
     * 根据运单或者出库单查询
     *
     * @param waybillSn
     * @param
     * @return
     */
    @Override
    public List<HandoverOrderDetailDTO> getHandoverOrderDetailByMailNo(String waybillSn) {
        Example example = new Example(HandoverOrderDetailDO.class);
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(waybillSn)) {
            List<String> waybillSns = Arrays.asList(waybillSn.split(","));
            criteria.andIn("wayBillSn", waybillSns);
        }
        example.and(criteria);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }

    @Override
    public List<HandoverOrderDetailDTO> getHandoverOrderDetailByMailNo(List<String> waybillSn) {
        if (CollectionUtils.isEmpty(waybillSn)) {
            return new ArrayList<>();
        }
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("wayBillSn", waybillSn).andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }

    /**
     * 修改交接单明细出库单号
     *
     * @param status
     */
    @Override
    public void updHandoverSn(String mailNo, HandoverDetailOutBoundStatus status, String exportOrderSn) {
        log.warn("[updHandoverDetail] 交接单号 - {}, 状态 - {}, 出库单号 - {}", mailNo, status.getDesc(), exportOrderSn);
        HandoverOrderDetailDO orderDetailDO = getByWayBillSn(mailNo, status);
        orderDetailDO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.YES.getValue());
        orderDetailDO.setOutboundOrder(exportOrderSn);
        orderDetailDO.setUpdateBy(UserUtils.getUserId());
        orderDetailDO.setUpdateTime(new Date());
        handoverOrderDetailMapper.updateByPrimaryKey(orderDetailDO);
        log.warn("HandoverOrderDetailDO 修改交接单明细- {}", orderDetailDO);
    }

    /**
     * 根据运单号查询
     *
     * @param mailNo
     * @return
     */
    public HandoverOrderDetailDO getByWayBillSn(String mailNo, HandoverDetailOutBoundStatus status) {
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andEqualTo("wayBillSn", mailNo).andEqualTo("associateOutboundStatus", status.getValue());
        HandoverOrderDetailDO handoverOrderDetailDOS = handoverOrderDetailMapper.selectOneByExample(example);
        return handoverOrderDetailDOS;
    }

    /**
     * 根据交接单号查询交接单表体信息
     *
     * @param handoverSn
     * @return
     */
    public List<HandoverOrderDetailDTO> getDetailBySnStatus(String handoverSn, HandoverDetailOutBoundStatus status) {
        if (StringUtils.isEmpty(handoverSn)) {
            return null;
        }
        List<String> handoverSns = Arrays.asList(handoverSn.split(","));
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("handoverSn", handoverSns)
                .andEqualTo("associateOutboundStatus", status.getValue())
                .andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }

    /**
     * 根据交接单号查询
     *
     * @param handoverSn
     * @return
     */
    @Override
    public List<HandoverOrderDetailDTO> getDetailByHandoverOrder(String handoverSn) {
        if (StringUtils.isEmpty(handoverSn)) {
            return null;
        }
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andEqualTo("handoverSn", handoverSn).andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }

    @Override
    public List<HandoverOrderDetailDTO> getDetailByOutboundOrder(String exportOrderSn) {
        if (StringUtils.isEmpty(exportOrderSn)) {
            return new ArrayList<>();
        }
        HandoverOrderDetailDO handoverOrderDetailDO = new HandoverOrderDetailDO();
        handoverOrderDetailDO.setOutboundOrder(exportOrderSn);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.select(handoverOrderDetailDO);
        List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
        return handoverOrderDetailDTOS;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void disassociateDetailByOutboundOrder(String exportOrderSn) {
        if (StringUtils.isEmpty(exportOrderSn)) {
            return;
        }
        log.info("disassociateDetailByOutboundOrder exportOrderSn={}", exportOrderSn);
        List<HandoverOrderDetailDTO> detailByOutboundOrder = this.getDetailByOutboundOrder(exportOrderSn);
        log.info("disassociateDetailByOutboundOrder detailByOutboundOrder={}", JSON.toJSONString(detailByOutboundOrder));
        List<String> handoverSn = detailByOutboundOrder.stream().map(HandoverOrderDetailDTO::getHandoverSn).distinct().collect(Collectors.toList());
        handoverOrderDetailMapper.disassociateDetailByOutboundOrder(exportOrderSn);
        log.info("disassociateDetailByOutboundOrder 解除表体关联 exportOrderSn={}", exportOrderSn);
        handoverSn.forEach(h -> handoverOrderService.updateHandoverOrderStatus(h));
    }

    /**
     * 根据交接单号查询
     *
     * @param handoverSnList
     * @return
     */
    @Override
    public List<HandoverOrderDetailDTO> getDetailByHandoverOrder(List<String> handoverSnList) {
        if (CollectionUtils.isEmpty(handoverSnList)) {
            return new ArrayList<>();
        }
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("handoverSn", handoverSnList).andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }

    @Override
    public void updateDetailExportOrder(List<String> mailNoList, String exportOrderSn) {
        if (CollectionUtils.isEmpty(mailNoList)) {
            return;
        }
        HandoverOrderDetailDO detailDO = new HandoverOrderDetailDO();
        detailDO.setOutboundOrder(exportOrderSn);
        detailDO.setOutHouseTime(new Date());
        detailDO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.YES.getValue());
        detailDO.setUpdateTime(new Date());
        UserUtils.setUpdateBy(detailDO);
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("wayBillSn", mailNoList).andEqualTo("deleted", false);
        handoverOrderDetailMapper.updateByExampleSelective(detailDO, example);
    }

    @Override
    public void cleanExportOrderInfo(List<String> snList) {
        if (CollectionUtils.isEmpty(snList)) {
            return;
        }
        //获取所有单号对应的交接单表体
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("customsInventorySn", snList).andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        handoverOrderDetailMapper.cleanExportOrderInfo(snList);
        //更新对应交接单的状态
        List<String> handoverSnList = handoverOrderDetailDOS.stream().map(HandoverOrderDetailDO::getHandoverSn).distinct().collect(Collectors.toList());
        handoverSnList.forEach(h -> handoverOrderService.updateHandoverOrderStatus(h));
    }

    @Override
    public void addExportOrderInfo(List<String> addInventorySnList, String sn) {
        HandoverOrderDetailDO handoverOrderDetailDO = new HandoverOrderDetailDO();
        handoverOrderDetailDO.setOutboundOrder(sn);
        handoverOrderDetailDO.setOutHouseTime(new Date());
        handoverOrderDetailDO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.YES.getValue());
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andIn("customsInventorySn", addInventorySnList).andEqualTo("deleted", false);
        handoverOrderDetailMapper.updateByExampleSelective(handoverOrderDetailDO, example);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        List<String> handoverSnList = handoverOrderDetailDOS.stream().map(HandoverOrderDetailDO::getHandoverSn).distinct().collect(Collectors.toList());
        handoverSnList.forEach(h -> handoverOrderService.updateHandoverOrderStatus(h));
    }

    /**
     * 根据出库单获取交接单号
     *
     * @param outboundOrder
     * @return
     */
    @Override
    public List<String> getHandoverSnByOutBoundOrder(List<String> outboundOrder) {
        List<String> handoverSnList = handoverOrderDetailMapper.getHandoverSnByOutBoundOrder(outboundOrder);
        return handoverSnList;
    }

    @Override
    public List<HandoverOrderDetailDTO> getDetailByHandoverSnListAndStatus(List<String> handoverSnList, HandoverDetailOutBoundStatus status) {
        if (CollectionUtils.isEmpty(handoverSnList) || Objects.isNull(status)) {
            return new ArrayList<>();
        }
        Example example = new Example(HandoverOrderDetailDO.class);
        example.createCriteria().andEqualTo("associateOutboundStatus", status.getValue())
                .andIn("handoverSn", handoverSnList)
                .andEqualTo("deleted", false);
        List<HandoverOrderDetailDO> handoverOrderDetailDOS = handoverOrderDetailMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDetailDOS, HandoverOrderDetailDTO.class);
    }
}
