package com.danding.cds.invenorder.impl.service;

import com.danding.cds.invenorder.api.dto.InventoryOrderLogDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderLogService;
import com.danding.cds.invenorder.impl.entity.InventoryOrderLogDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderLogMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.ArrayList;
import java.util.List;
@DubboService
@Slf4j
public class InventoryOrderLogServiceImpl implements InventoryOrderLogService {

    @Autowired
    private InventoryOrderLogMapper inventoryOrderLogMapper;
    @Override
    public List<InventoryOrderLogDTO> findList(Long refInveOrderId) {
        Example example = new Example(InventoryOrderLogDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refInveOrderId",refInveOrderId);
        criteria.andEqualTo("deleted",false);
        List<InventoryOrderLogDO> list= inventoryOrderLogMapper.selectByExample(example);
        if(list == null)list  = new java.util.ArrayList<InventoryOrderLogDO>();
        List<InventoryOrderLogDTO> oretList = new ArrayList<InventoryOrderLogDTO>();
        for(InventoryOrderLogDO inventoryOrderLogDO:list)
        {
            InventoryOrderLogDTO inventoryOrderLogDTO = new InventoryOrderLogDTO();
            BeanUtils.copyProperties(inventoryOrderLogDO,inventoryOrderLogDTO);
            oretList.add(inventoryOrderLogDTO);
        }
        return oretList;
    }

}
