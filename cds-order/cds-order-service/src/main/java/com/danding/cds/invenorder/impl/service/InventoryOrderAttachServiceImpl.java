package com.danding.cds.invenorder.impl.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.exception.ArgsErrorRpcException;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.common.utils.FileOpsUtil;
import com.danding.cds.common.utils.JSONUtils;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.dto.CompanyExtraDTO;
import com.danding.cds.declare.sdk.utils.DateUtils;
import com.danding.cds.invenorder.api.dto.InventoryOrderAttachDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.enums.InventoryInOutEnum;
import com.danding.cds.invenorder.api.enums.InventoryOrderAttachTypeEnums;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.cds.invenorder.api.service.InventoryOrderAttachService;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.impl.entity.CompanyReceiveInfo;
import com.danding.cds.invenorder.impl.entity.InventoryOrderAttachDO;
import com.danding.cds.invenorder.impl.entity.InventoryOrderLogDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderAttachMapper;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderLogMapper;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.InventoryOrderInfoTrackLogService;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@DubboService
@Slf4j
public class InventoryOrderAttachServiceImpl implements InventoryOrderAttachService {
    @Autowired
    private InventoryOrderAttachMapper inventoryOrderAttachMapper;
    @Autowired
    private InventoryOrderLogMapper inventoryOrderLogMapper;
    @Resource
    private InventoryOrderInfoService inventoryOrderInfoService;
    @Autowired
    private BaseDataService baseDataService;

    private static final String OSS_PATH = "https://daita-oss.oss-cn-hangzhou.aliyuncs.com/";

    @Autowired
    private InventoryOrderInfoTrackLogService inventoryOrderInfoTrackLogService;

    @Autowired
    private Validator validator;

    @Override
    public InventoryOrderAttachDTO findById(Long id) {
        InventoryOrderAttachDO inventoryOrderAttachDO = inventoryOrderAttachMapper.selectByPrimaryKey(id);
        if (inventoryOrderAttachDO == null) return null;
        InventoryOrderAttachDTO inventoryOrderAttachDTO = new InventoryOrderAttachDTO();
        BeanUtils.copyProperties(inventoryOrderAttachDO, inventoryOrderAttachDTO);
        return inventoryOrderAttachDTO;
    }

    @Override
    public List<InventoryOrderAttachDTO> findList(Long inveOrderId) {
        Example example = new Example(InventoryOrderAttachDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("refInveOrderId", inveOrderId);
        criteria.andEqualTo("deleted", false);
        List<InventoryOrderAttachDO> list = inventoryOrderAttachMapper.selectByExample(example);
        if (list == null) list = new java.util.ArrayList<InventoryOrderAttachDO>();
        List<InventoryOrderAttachDTO> oretList = new ArrayList<InventoryOrderAttachDTO>();
        for (InventoryOrderAttachDO inventoryOrderAttachDO : list) {
            InventoryOrderAttachDTO inventoryOrderAttachDTO = new InventoryOrderAttachDTO();
            BeanUtils.copyProperties(inventoryOrderAttachDO, inventoryOrderAttachDTO);
            oretList.add(inventoryOrderAttachDTO);
        }
        return oretList;
    }

    @Override
    @Transactional
    public void createInventoryOrderAttachDTO(InventoryOrderAttachDTO inventoryOrderAttachDTO) {
        log.info("[op:InventoryOrderAttachServiceImpl-createInventoryOrderAttachDTO inventoryOrderAttachDTO={}]", JSONUtils.toJSONString(inventoryOrderAttachDTO));
        InventoryOrderAttachDO inventoryOrderAttachDO = new InventoryOrderAttachDO();
        BeanUtils.copyProperties(inventoryOrderAttachDTO, inventoryOrderAttachDO);
        InventoryOrderLogDO logInfo = new InventoryOrderLogDO();
        logInfo.setRefInveOrderId(inventoryOrderAttachDTO.getRefInveOrderId());
        logInfo.setRefInveOrderSn(inventoryOrderAttachDTO.getRefInveOrderSn());
        logInfo.setLogName("上传附件");
        logInfo.setLogDetail("操作上传附件：[" + inventoryOrderAttachDO.getAttachName() + "]");
        logInfo.setUpdateBy(UserUtils.getUserId());
        logInfo.setCreateBy(UserUtils.getUserId());
        logInfo.setCreateTime(new Date());
        logInfo.setUpdateTime(new Date());
        inventoryOrderAttachMapper.insertSelective(inventoryOrderAttachDO);
        inventoryOrderLogMapper.insertSelective(logInfo);
    }

    @Override
    public void traceLogDownload(Long id) {
        InventoryOrderAttachDO inventoryOrderAttachDO = inventoryOrderAttachMapper.selectByPrimaryKey(id);
        if (inventoryOrderAttachDO != null) {
            InventoryOrderLogDO logInfo = new InventoryOrderLogDO();
            logInfo.setRefInveOrderId(inventoryOrderAttachDO.getRefInveOrderId());
            logInfo.setRefInveOrderSn(inventoryOrderAttachDO.getRefInveOrderSn());
            logInfo.setLogName("下载附件");
            logInfo.setLogDetail("操作下载附件：[" + inventoryOrderAttachDO.getAttachName() + "]");
            logInfo.setUpdateBy(UserUtils.getUserId());
            logInfo.setCreateBy(UserUtils.getUserId());
            logInfo.setCreateTime(new Date());
            logInfo.setUpdateTime(new Date());
            inventoryOrderLogMapper.insertSelective(logInfo);
        }
    }

    @Override
    public void saveAttachUrl(InventoryOrderInfoDTO dto, String url, String fileName, String attachType) {
        InventoryOrderAttachDO attachDO = new InventoryOrderAttachDO();
        attachDO.setRefInveOrderId(dto.getId());
        attachDO.setRefInveOrderSn(dto.getInveCustomsSn());
        attachDO.setAttachName(fileName);
        attachDO.setStoreName(fileName);
        attachDO.setAttachPath(url);
        attachDO.setContentType("");
        attachDO.setAttachType(attachType);
        attachDO.setSource(InventoryOrderChannel.CCS_SELF.getValue());
        attachDO.setCreateBy(UserUtils.getUserId());
        attachDO.setUpdateBy(UserUtils.getUserId());
        attachDO.setCreateTime(new Date());
        attachDO.setUpdateTime(new Date());
        inventoryOrderAttachMapper.insertSelective(attachDO);

        inventoryOrderInfoTrackLogService.saveInventoryOrderLog(dto.getId(), "上传附件成功【" + fileName + "】");
    }

    @Override
    public String download(Long id, Integer isUpdateCompanyInfo) {
        InventoryOrderAttachDTO attachDTO = this.findById(id);
        if (attachDTO == null) {
            throw new ArgsErrorRpcException("附件id不存在");
        }
        if (Objects.equals(attachDTO.getAttachType(), InventoryOrderAttachTypeEnums.BOX_INVOICE_CONTRACT.getValue())
                && Objects.equals(isUpdateCompanyInfo, 1)) {
            return editAttachReUpload(attachDTO);
        }
        return attachDTO.getAttachPath();
    }

    private String editAttachReUpload(InventoryOrderAttachDTO attachDTO) {
        log.info("editAttachReUpload attachDTO={}", JSONUtils.toJSONString(attachDTO));
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(attachDTO.getRefInveOrderId());
        String fileName = attachDTO.getAttachName();
        String url = attachDTO.getAttachPath();
        if (inventoryOrderInfoDTO == null) {
            throw new ArgsErrorRpcException("清关单id不存在");
        }
        CompanyDTO companyDTO = baseDataService.getCompanyDTOById(inventoryOrderInfoDTO.getInveCompanyId());
        if (companyDTO == null) {
            throw new ArgsErrorRpcException("企业id不存在");
        }
        CompanyExtraDTO companyExtraDTO = JSON.parseObject(companyDTO.getExtraJson(), CompanyExtraDTO.class);
        if (companyExtraDTO == null) {
            throw new ArgsErrorRpcException("企业extra信息不存在");
        }
        CompanyReceiveInfo receiveInfo = new CompanyReceiveInfo();
        BeanUtils.copyProperties(companyExtraDTO, receiveInfo);
        receiveInfo.setUniformSocialCreditCode(companyDTO.getUniformSocialCreditCode());
        String content = getCompanyReceiveInfoContent(receiveInfo);

        try {
            Workbook workbook = new XSSFWorkbook(DownLoadUtil.downloadNet(url));
            // 2. 获取工作表（可根据名称获取）
            Sheet sheet0 = workbook.getSheet("PL装箱单");
            if (sheet0 == null) {
                throw new ArgsErrorRpcException("PL装箱单Sheet不存在");
            }
            Sheet sheet1 = workbook.getSheet("IN发票");
            if (sheet1 == null) {
                throw new ArgsErrorRpcException("IN发票Sheet不存在");
            }
            Sheet sheet2 = workbook.getSheet("合同");
            if (sheet2 == null) {
                throw new ArgsErrorRpcException("合同Sheet不存在");
            }

            // 3. 填充指定单元格（示例：第3行第2列，即B4单元格）
            if (Objects.equals(inventoryOrderInfoDTO.getInOutFlag(), InventoryInOutEnum.OUT.getCode())) {
                fillCell(sheet0, 5, 0, content);
                fillCell(sheet1, 3, 0, content);
                fillCell(sheet2, 3, 3, content);
            } else {
                fillCell(sheet0, 5, 4, content);
                fillCell(sheet1, 3, 4, content);
                fillCell(sheet2, 3, 0, content);
            }

            // 4. 导出新文件
            byte[] byteArr = null;
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                workbook.write(bos);
                byteArr = bos.toByteArray();
            } catch (Exception e) {
                throw new RuntimeException("转换Excel到字节数组失败", e);
            }
            fileName = fileName.replace(".xlsx", String.format("_%s.xlsx", DateUtils.secondsStr(new Date().getTime())));
            FileOpsUtil.uploadFile2OssDownload(fileName, byteArr, 1);
            String fileNameNew = OSS_PATH + fileName;
            log.info("editAttachReUpload fileNameNew={}", fileNameNew);
            return fileNameNew;
        } catch (IOException e) {
            throw new ArgsErrorRpcException("文件编辑异常，请检查文件格式是否正确");
        }

    }

    private String getCompanyReceiveInfoContent(CompanyReceiveInfo receiveInfo) {
        String errorMsg = ValidatorUtils.doValidator(validator, receiveInfo);
        if (StrUtil.isNotBlank(errorMsg)) {
            throw new ArgsInvalidException("清关企业未配置地址电话邮箱等，请先完善");
        }
        return String.format("%s\n%s\nTel：%s \nE-mail：%s\nUSCI：%s",
                receiveInfo.getEnglishName(), receiveInfo.getEnglishAddress(), receiveInfo.getPhone(),
                receiveInfo.getEmail(), receiveInfo.getUniformSocialCreditCode());
    }

    @Test
    public void test() {
        String fileName = "清关资料_生成.xlsx";
        String url = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/77889699284.xlsx";
        try {
            Workbook workbook = new XSSFWorkbook(DownLoadUtil.downloadNet(url));
            // 2. 获取第一个工作表（可根据名称获取）
            Sheet sheet0 = workbook.getSheetAt(1);
            Sheet sheet1 = workbook.getSheetAt(2);
            Sheet sheet2 = workbook.getSheetAt(3);

            // 3. 填充指定单元格（示例：第3行第2列，即B4单元格）
            fillCell(sheet0, 5, 0, "填充内容");
            fillCell(sheet1, 3, 0, "填充内容");
            fillCell(sheet2, 3, 3, "填充内容");

//            fillCell(sheet0, 6, 4, "填充内容");
//            fillCell(sheet1, 4, 4, "填充内容");
//            fillCell(sheet2, 4, 0, "填充内容");

            // 4. 导出新文件
            byte[] byteArr = null;
            try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
                workbook.write(bos);
                byteArr = bos.toByteArray();
            } catch (Exception e) {
                throw new RuntimeException("转换Excel到字节数组失败", e);
            }
            fileName = fileName.replace(".xlsx", String.format("_%s.xlsx", DateUtils.secondsStr(new Date().getTime())));
            FileOpsUtil.uploadFile2OssDownload(fileName, byteArr);
            String filePath = OSS_PATH + fileName;
            System.out.printf(filePath);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void fillCell(Sheet sheet, int rowNum, int colNum, String value) {
        // 获取行（如果不存在则创建）
        Row row = sheet.getRow(rowNum);
        if (row == null) {
            row = sheet.createRow(rowNum);
        }

        // 获取单元格（如果不存在则创建）
        Cell cell = row.getCell(colNum);
        if (cell == null) {
            cell = row.createCell(colNum);
        }

        // 设置单元格值
        cell.setCellValue(value);
    }

    @Override
    @Transactional
    public void deleteById(Long id) {
        InventoryOrderAttachDO inventoryOrderAttachDO = inventoryOrderAttachMapper.selectByPrimaryKey(id);
        if (inventoryOrderAttachDO != null) {
            InventoryOrderLogDO logInfo = new InventoryOrderLogDO();
            logInfo.setRefInveOrderId(inventoryOrderAttachDO.getRefInveOrderId());
            logInfo.setRefInveOrderSn(inventoryOrderAttachDO.getRefInveOrderSn());
            logInfo.setLogName("删除附件");
            logInfo.setLogDetail("操作删除附件：[" + inventoryOrderAttachDO.getAttachName() + "]");
            logInfo.setUpdateBy(UserUtils.getUserId());
            logInfo.setCreateBy(UserUtils.getUserId());
            logInfo.setCreateTime(new Date());
            logInfo.setUpdateTime(new Date());
            inventoryOrderLogMapper.insertSelective(logInfo);
            inventoryOrderInfoTrackLogService.saveInventoryOrderLog(inventoryOrderAttachDO.getRefInveOrderId(), "删除附件成功【" + inventoryOrderAttachDO.getAttachName() + "】");
        }
        inventoryOrderAttachMapper.deleteByPrimaryKey(id);

    }
}
