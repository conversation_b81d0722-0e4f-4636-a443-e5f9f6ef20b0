package com.danding.cds.collaborateorder.impl.service;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderCountDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderDTO;
import com.danding.cds.collaborateorder.api.dto.TallyReportDTO;
import com.danding.cds.collaborateorder.api.enums.CollaborateStatus;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderDetailsService;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderReqVO;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderResVO;
import com.danding.cds.collaborateorder.impl.entity.CollaborateOrderDO;
import com.danding.cds.collaborateorder.impl.mapper.CollaborateOrderMapper;
import com.danding.cds.common.enums.InventoryDeclareWayEnum;
import com.danding.cds.common.enums.InventoryTwoStepEnum;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.declare.sdk.utils.DateUtils;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.InventoryOrderAlarmService;
import com.danding.cds.v2.service.InventoryOrderInfoTrackLogService;
import com.danding.cds.v2.util.InventoryOrderUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.soul.client.common.exception.BusinessException;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 协同单业务实现
 * @date 2022/3/29
 */
@Slf4j
@DubboService
public class CollaborateOrderServiceImpl implements CollaborateOrderService {

    @Autowired
    private CollaborateOrderMapper collaborateOrderMapper;

    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private CollaborateOrderDetailsService detailsService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CompanyService companyService;

    @Autowired
    private BaseDataService baseDataService;

    @Resource
    private InventoryOrderAlarmService inventoryOrderAlarmService;

    @Autowired
    private InventoryOrderInfoTrackLogService inventoryOrderInfoTrackLogService;

    /**
     * 协同单分页查询
     *
     * @param reqVO
     * @return
     */
    @Override
    @PageSelect
    public ListVO<CollaborateOrderResVO> paging(CollaborateOrderReqVO reqVO) throws ArgsErrorException {
        // FIXME: 2022/8/13 不想在mapper里改返回字段了 如果需要优化可以在这里一次性查出来
        if (StringUtils.isNotEmpty(reqVO.getInveCustomsSn())) {
            reqVO.setInveCustomsSnList(Splitter.on(",").splitToList(reqVO.getInveCustomsSn()));
        }
        List<CollaborateOrderDTO> collaborateOrderDTOS = collaborateOrderMapper.selectByPaging(reqVO);
        List<CollaborateOrderResVO> resVOList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(collaborateOrderDTOS)) {
            for (CollaborateOrderDTO orderDTO : collaborateOrderDTOS) {
                CollaborateOrderResVO resVO = BeanUtil.copyProperties(orderDTO, CollaborateOrderResVO.class);
                resVO.setId(orderDTO.getId().toString());
                resVO.setCollaborateStatus(CollaborateStatus.getEnum(orderDTO.getCollaborateStatus()).getDesc());
                resVO.setInveBusinessType(resVO.getInveBusinessType());
                resVO.setInveBusinessTypeDesc(InventoryOrderBusinessEnum.getEnum(resVO.getInveBusinessType()).getDesc());
                resVO.setCreateTime(DateUtil.defFormatDateStr(orderDTO.getCreateTime()));
                if (Objects.nonNull(orderDTO.getInveCompanyId())) {
                    CompanyDTO companyDTO = baseDataService.getCompanyDTOById(orderDTO.getInveCompanyId());
                    if (Objects.nonNull(companyDTO)) {
                        resVO.setCompanyName(companyDTO.getName());
                    }
                }
                if (Objects.nonNull(orderDTO.getTallyFinishTime())) {
                    resVO.setTallyFinishTime(DateUtil.defFormatDateStr(orderDTO.getTallyFinishTime()));
                }
                if (Objects.nonNull(orderDTO.getInveFinishTime())) {
                    resVO.setCustomsFinishTime(DateUtil.defFormatDateStr(orderDTO.getInveFinishTime()));
                }
                if (Objects.nonNull(orderDTO.getTallyReportSn())) {
                    resVO.setTallyReportReceive("是");
                } else {
                    resVO.setTallyReportReceive("否");
                }
                resVO.setPlanDeclareQty(inventoryOrderInfoService.sumPlanDeclareQty(orderDTO.getInveId()));
                resVO.setActuralDeclareQty(inventoryOrderInfoService.sumActualDeclareQty(orderDTO.getInveId()));
                resVO.setDiffQty(orderDTO.getDiffQty());
                // 清关单信息
                InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(Long.valueOf(orderDTO.getInveId()));
                if (inventoryOrderInfoDTO != null) {
                    resVO.setCustomsEntryNo(inventoryOrderInfoDTO.getCustomsEntryNo());
                    resVO.setPickUpNo(inventoryOrderInfoDTO.getPickUpNo());
                    CustomsDictionaryDTO shipmentCountry = customsDictionaryService.findByCodeAndType(inventoryOrderInfoDTO.getShipmentCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
                    if (Objects.nonNull(shipmentCountry)) {
                        resVO.setShipmentCountry(shipmentCountry.getName());
                    }
                    resVO.setRefHzInveNo(inventoryOrderInfoDTO.getEndorsementRealOrderNo());
                    resVO.setForwardingCompany(inventoryOrderInfoDTO.getForwardingCompany());
                    resVO.setConNo(inventoryOrderInfoDTO.getConNo());
                    resVO.setProductName(inventoryOrderInfoDTO.getProductName());
                    resVO.setCategory(inventoryOrderInfoDTO.getCategory());
                    resVO.setArrivalPort(inventoryOrderInfoDTO.getArrivalPort());
                    resVO.setExpectedToPortTime(inventoryOrderInfoDTO.getExpectedToPortTime());
                    resVO.setActualArrivalDate(inventoryOrderInfoDTO.getActualArrivalDate());
                    resVO.setVehicleCostRemark(inventoryOrderInfoDTO.getVehicleCostRemark());
                }
                resVOList.add(resVO);
            }
        }
        ListVO<CollaborateOrderResVO> result = new ListVO<>();
        result.setDataList(JSON.parseArray(JSON.toJSONString(resVOList), CollaborateOrderResVO.class));
        // 分页
        PageInfo<CollaborateOrderDTO> pageInfo = new PageInfo(collaborateOrderDTOS);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(result.getPage().getTotalCount());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<CollaborateOrderCountDTO> selectCountByPaging(CollaborateOrderReqVO reqVO) {
        List<CollaborateOrderCountDTO> collaborateOrderCountDTOS = collaborateOrderMapper.selectCountByPaging(reqVO);
        if (CollectionUtils.isEmpty(collaborateOrderCountDTOS)) {
            return new ArrayList<>();
        }
        return collaborateOrderCountDTOS;
    }

    /**
     * 生成协同单
     *
     * @param inveCustomsSn
     */
    @Override
    public void saveCollaborateOrder(String inveCustomsSn) {
        log.info("saveCollaborateOrder 清关单号 - {},", inveCustomsSn);
        InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findBySn(inveCustomsSn);
        if (orderInfoDTO == null) {
            return;
        }
        if (Objects.nonNull(orderInfoDTO.getBookId())) {
            CustomsBookResVo bookResVo = customsBookService.findByIdV2(orderInfoDTO.getBookId());
            if (Objects.nonNull(bookResVo) && !bookResVo.getBookTagList().contains(CustomsBookTagEnums.COLLABORATE_ENABLE.getCode())) {
                log.info("saveCollaborateOrder 账册编号：{} 未配置生成协同单 不生成协同单", bookResVo.getBookNo());
                return;
            }
        }
        log.info("saveCollaborateOrder 清关单:{} ", JSON.toJSONString(orderInfoDTO));
        CollaborateOrderDO collaborateOrderDO = new CollaborateOrderDO();
        collaborateOrderDO.setInveCustomsSn(inveCustomsSn);
        collaborateOrderDO.setId(sequenceService.generateId());
        collaborateOrderDO.setCollaborateSn(sequenceService.generateCollaborateOrderSn());
        collaborateOrderDO.setCollaborateStatus(CollaborateStatus.WAIT_CUSTOMS_VERIFY.getCode());
        if (Objects.equals(orderInfoDTO.getChannelBusinessType(), "ReadyOrder")) {
            collaborateOrderDO.setCurrentWareCode(orderInfoDTO.getEntityWarehouseCode());
            collaborateOrderDO.setCurrentOwnerCode(orderInfoDTO.getOwnerCode());
        }
        if (Objects.equals(orderInfoDTO.getChannelBusinessType(), "Distribution")) {
            collaborateOrderDO.setCurrentWareCode(orderInfoDTO.getFinalEntityWarehouseCode());
            collaborateOrderDO.setCurrentOwnerCode(orderInfoDTO.getFinalOwnerCode());
        }
        List<String> stateflow = new ArrayList() {{
            add(CollaborateStatus.WAIT_CUSTOMS_VERIFY.getCode());
        }};
        collaborateOrderDO.setStateFlow(JSON.toJSONString(stateflow));
        UserUtils.setCreateAndUpdateBy(collaborateOrderDO);

        if (Objects.nonNull(orderInfoDTO.getEntityWarehouseName())) {
            InventoryOrderBusinessEnum type = InventoryOrderBusinessEnum.getEnum(orderInfoDTO.getInveBusinessType());
            switch (type) {
                case BUSSINESS_SECTION_IN:
                case BUSSINESS_SECTIONINNER_IN:
                case BUSSINESS_ONELINE_IN:
                    collaborateOrderDO.setEntityInWarehouseName(orderInfoDTO.getEntityWarehouseName());
                    break;
                case BUSSINESS_SECTION_OUT:
                case BUSSINESS_SECTIONINNER_OUT:
                    collaborateOrderDO.setEntityOutWarehouseName(orderInfoDTO.getEntityWarehouseName());
                    break;
                default:
                    break;
            }
        }
        UserUtils.setCreateAndUpdateBy(collaborateOrderDO);
        collaborateOrderMapper.insertSelective(collaborateOrderDO);
        log.info("saveCollaborateOrderDetails 清关单号:{} 协同单id:{}", inveCustomsSn, collaborateOrderDO.getId());
        //生成协同单详情
        detailsService.saveCollaborateOrderDetails(inveCustomsSn, collaborateOrderDO.getId());
    }


    @Override
    public void updateCollaborateOrderStatus(Long inventoryOrderId, CollaborateStatus status) {
        InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        if (Objects.isNull(orderInfoDTO)) {
            return;
        }
        this.updateCollaborateOrderStatus(orderInfoDTO.getInveCustomsSn(), status);
    }

    /**
     * 协同单状态修改
     *
     * @param inveCustomsSn
     * @param status
     */
    @Override
    public void updateCollaborateOrderStatus(String inveCustomsSn, CollaborateStatus status) {
        CollaborateOrderDTO collaborateOrderDTO = selectByInveCustomsSn(inveCustomsSn);
        if (collaborateOrderDTO != null) {
            CollaborateOrderDO orderDO = new CollaborateOrderDO();
            orderDO.setId(collaborateOrderDTO.getId());
            orderDO.setCollaborateStatus(status.getCode());
            List<String> stateFlow = JSON.parseArray(collaborateOrderDTO.getStateFlow(), String.class);
            stateFlow.add(status.getCode());
            orderDO.setStateFlow(JSON.toJSONString(stateFlow));
            // 如果是服务完成，更新下清关完成时间
            if (Objects.equals(status.getCode(), CollaborateStatus.SERVE_FINISH.getCode())) {
                orderDO.setInventoryFinishTime(new Date());
            }
            UserUtils.setUpdateBy(orderDO);
            orderDO.setUpdateTime(new Date());
            collaborateOrderMapper.updateByPrimaryKeySelective(orderDO);
        }
    }


    @Override
    public CollaborateOrderDTO selectByInveCustomsSn(String inveCustomsSn) {
        CollaborateOrderDO collaborateOrderDO = new CollaborateOrderDO();
        collaborateOrderDO.setInveCustomsSn(inveCustomsSn);
        CollaborateOrderDO orderDO = collaborateOrderMapper.selectOne(collaborateOrderDO);
        if (Objects.isNull(orderDO)) {
            return null;
        }
        return BeanUtil.copyProperties(orderDO, CollaborateOrderDTO.class);
    }

    /**
     * 根据id查询
     *
     * @param id
     * @return
     */
    @Override
    public CollaborateOrderDTO selectByInveCustomsId(String id) {

        Example example = new Example(CollaborateOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("id", id);
        CollaborateOrderDO collaborateOrderDO = collaborateOrderMapper.selectOneByExample(example);
        return BeanUtil.copyProperties(collaborateOrderDO, CollaborateOrderDTO.class);
    }

    @Override
    public void updateAssociatedInventorySn(String inveCustomsSn, String associatedInventorySn) {
        Example example = new Example(CollaborateOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inveCustomsSn", inveCustomsSn);
        CollaborateOrderDO orderDO = new CollaborateOrderDO();
        orderDO.setCollaborateStatus(CollaborateStatus.SERVE_FINISH.getCode());
        orderDO.setAssociatedInveCustomsSn(associatedInventorySn);
        UserUtils.setUpdateBy(orderDO);
        orderDO.setUpdateTime(new Date());
        collaborateOrderMapper.updateByExampleSelective(orderDO, example);
    }

    /**
     * 修改运输费，托数
     *
     * @param fightQty
     * @param shippingFee
     * @param InveCustomsSn
     */
    @Override
    public void updateFightQtyAndShippingFeeByInveCustomsSn(String entityInWarehouseName, String entityOutWarehouseName, Integer fightQty, BigDecimal shippingFee, String InveCustomsSn) {
        CollaborateOrderDTO orderDTO = selectByInveCustomsSn(InveCustomsSn);
        if (Objects.isNull(orderDTO)) {
            return;
        }
        CollaborateOrderDO orderDO = new CollaborateOrderDO();
        orderDO.setShippingFee(shippingFee);
        orderDO.setFightQty(fightQty);
        orderDO.setId(orderDTO.getId());
        if (Objects.nonNull(entityOutWarehouseName)) {
            orderDO.setEntityOutWarehouseName(entityOutWarehouseName);
        }
        if (Objects.nonNull(entityInWarehouseName)) {
            orderDO.setEntityInWarehouseName(entityInWarehouseName);
        }
        UserUtils.setUpdateBy(orderDO);
        orderDO.setUpdateTime(new Date());
        collaborateOrderMapper.updateByPrimaryKeySelective(orderDO);
    }

    /**
     * 接收理货报告回传
     *
     * @param tallyReportDTO
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveTallyReport(TallyReportDTO tallyReportDTO) throws ArgsErrorException {
        log.info("receiveTallyReport report={}", JSON.toJSONString(tallyReportDTO));
        //出入库单号查询清关单
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findByInOutOrderNo(tallyReportDTO.getInOutOrderNo());
        if (infoDTO == null) {
            log.error("出入库单号{}不存在!", tallyReportDTO.getInOutOrderNo());
            throw new BusinessException("出入库单号" + tallyReportDTO.getInOutOrderNo() + "不存在!");
        }
        Integer orderTag = infoDTO.getOrderTag();
        List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(orderTag);
        if (orderTags.contains(InventoryOrderTagEnums.RELATE.getCode())) {
            log.error("出入库单{} 存在结转明细无法更新理货报告", tallyReportDTO.getInOutOrderNo());
//            throw new BusinessException("出库单存在结转明细无法更新理货报告");
            return;
        }
        if (Objects.equals("1", tallyReportDTO.getType())) {
            //单据类型( 1 : 入库单, 2:出库单)
            if (Objects.isNull(tallyReportDTO.getWarehouseTime())) {
                log.error("到库时间不能为空");
                throw new BusinessException("到库时间不能为空");
            }
        }
        log.info("receiveTallyReport 理货报告号:{} 出入库单号:{} 理货回传状态:{}", tallyReportDTO.getTallyOrderNo(), tallyReportDTO.getInOutOrderNo(), tallyReportDTO.getStatus());
        if (Objects.equals(tallyReportDTO.getStatus(), 3)) {
            //出库单包含草单附件
            inventoryOrderInfoService.updateInveOrderDraftById(infoDTO.getId(), tallyReportDTO.getAttachmentName(), tallyReportDTO.getAttachmentUrl());
        }
        //现在status = 2 由WMS回传 ERP下发的不处理
        else if (Objects.equals(tallyReportDTO.getStatus(), 2)) {
            //理货报告已接收不处理
            if (Objects.equals(infoDTO.getTallyComplete(), 1)) {
                log.info("receiveTallyReport 重复提交理货报告 出入库单号:{}", tallyReportDTO.getInOutOrderNo());
                return;
            }
            log.info("receiveTallyReport 理货报告号:{} 出入库单号:{} 理货回传状态:{}", tallyReportDTO.getTallyOrderNo(), tallyReportDTO.getInOutOrderNo(), tallyReportDTO.getStatus());
            //检查是否存在协同单
            CollaborateOrderDTO collaborateOrderDTO = this.selectByInveCustomsSn(infoDTO.getInveCustomsSn());
            //修改协同单 - 默认生成协同单，若没生成协同单不做更新
            CollaborateOrderDO collaborateOrder = new CollaborateOrderDO();
            log.info("receiveTallyReport 清关单申报类型:{}", InventoryTwoStepEnum.getEnum(infoDTO.getTwoStepFlag()).getDesc());
            //判断是否两步申报
            Long collaborateOrderId = Objects.nonNull(collaborateOrderDTO) ? collaborateOrderDTO.getId() : null;
            //两步申报 & 先理后报
            if (InventoryOrderUtils.checkTwoStepAndDeclareWay(infoDTO)) {
                detailsService.updateDetailsTwoStepDeclare(tallyReportDTO, collaborateOrderId, infoDTO);
                collaborateOrder.setCollaborateStatus(CollaborateStatus.TALL_RETURNED.getCode());
            } else {
                //两步申报、先报后理 修改协同单、清关单详情
                Integer diffQty = detailsService.updateDetailsOneStepDeclare(tallyReportDTO, collaborateOrderId, infoDTO);
                collaborateOrder.setDiffQty(diffQty);
                if (Objects.equals(diffQty, 0)) {
                    collaborateOrder.setCollaborateStatus(CollaborateStatus.END.getCode());
                } else {
                    collaborateOrder.setCollaborateStatus(CollaborateStatus.END_WAIT_VERIFY.getCode());
                }
            }

            // 更新协同单 - (存在协同单)
            if (!LongUtil.isNone(collaborateOrderId)) {
                collaborateOrder.setId(collaborateOrderId);
                List<String> stateFlow = JSON.parseArray(infoDTO.getStateFlow(), String.class);
                stateFlow.add(collaborateOrder.getCollaborateStatus());
                collaborateOrder.setStateFlow(JSON.toJSONString(stateFlow));
                collaborateOrder.setTallyFinishTime(DateUtils.timeMillisToDate(tallyReportDTO.getTallyFinishTime()));
                collaborateOrder.setTallyQty(tallyReportDTO.getTallyTotalQty());
                collaborateOrder.setTallyReportSn(tallyReportDTO.getTallyOrderNo());
                if (Objects.nonNull(tallyReportDTO.getWarehouseTime())) {
                    collaborateOrder.setArrivalTime(DateUtils.timeMillisToDate(tallyReportDTO.getWarehouseTime()));
                }
                collaborateOrder.setUpdateTime(new Date());
                if (Objects.nonNull(tallyReportDTO.getPalletsNums())) {
                    collaborateOrder.setFightQty(tallyReportDTO.getPalletsNums());
                }
                UserUtils.setUpdateBy(collaborateOrder);
                collaborateOrder.setUpdateTime(new Date());
                collaborateOrderMapper.updateByPrimaryKeySelective(collaborateOrder);
            }
            // 更新清关单
            infoDTO.setPalletsNums(tallyReportDTO.getPalletsNums());
            infoDTO.setDraftListAttachmentName(tallyReportDTO.getAttachmentName());
            infoDTO.setDraftListAttachmentUrl(tallyReportDTO.getAttachmentUrl());
            infoDTO.setTallyComplete(1); // 理货已完成
            inventoryOrderInfoService.updateInventoryOrderInfoDTO(infoDTO);
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(infoDTO.getChannel())
                    && Objects.equals("Distribution", infoDTO.getChannelBusinessType())
                    && Objects.equals(infoDTO.getDeclareWay(), InventoryDeclareWayEnum.DECLARE_THEN_TALLY.getCode())
            ) {
                log.info("关仓出库单 先报后理，不修改核出企业");
            } else {
                try {
                    // 修改核出企业 变更表体
                    String actualOutCompanyName = tallyReportDTO.getActualOutCompanyName();
                    String carryOverNo = tallyReportDTO.getCarryOverNo();
                    String inOutOrderNo = tallyReportDTO.getInOutOrderNo();
                    inventoryOrderInfoService.changeCompanyByInOutOrderNo(inOutOrderNo, carryOverNo, actualOutCompanyName);
                } catch (Exception e) {
                    log.error("修改核出企业失败 error={}", e.getMessage(), e);
                    throw new ArgsErrorException("修改核注企业失败" + e.getMessage());
                }
            }
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(infoDTO.getChannel()) &&
                    Objects.equals("Distribution", infoDTO.getChannelBusinessType()) &&
                    !InventoryOrderBusinessEnum.BUSINESS_FB_OUT.getCode().equals(infoDTO.getInveBusinessType())) {
                inventoryOrderAlarmService.setAlarm(infoDTO.getInveCustomsSn(), infoDTO.getInveCompanyId(), infoDTO.getBookId(), infoDTO.getCreateTime());
            }
            String logDesc = "仓库理货完成";
            if (StringUtils.isNotEmpty(tallyReportDTO.getActualOutCompanyName())) {
                logDesc = logDesc + "，清关企业：" + tallyReportDTO.getActualOutCompanyName();
            } else {
                Long inveCompanyId = infoDTO.getInveCompanyId();
                CompanyDTO companyDTO = companyService.findById(inveCompanyId);
                if (Objects.nonNull(companyDTO)) {
                    logDesc = logDesc + "，清关企业：" + companyDTO.getName();
                }
            }
            inventoryOrderInfoTrackLogService.saveInventoryOrderLog(infoDTO, logDesc);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void receiveTallyReportWms(TallyReportDTO tallyReportDTO) {
        log.info("receiveTallyReportWms report:{}", JSON.toJSONString(tallyReportDTO));
        //出入库单号查询清关单
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findByInOutOrderNo(tallyReportDTO.getInOutOrderNo());
        if (infoDTO == null) {
            log.error("出入库单号{}不存在！", tallyReportDTO.getInOutOrderNo());
            throw new BusinessException("出入库单号" + tallyReportDTO.getInOutOrderNo() + "不存在!");
        }

    }

    public List<String> hasReceiveTallyReport(String inveCustomsSn) {
        List<String> snList = new ArrayList<>();
        snList.add(inveCustomsSn);
        return this.hasAllReceiveTallyReport(snList);
    }

    public List<String> hasAllReceiveTallyReport(List<String> inveCustomsSnList) {
        List<String> result = new ArrayList<>();
        Example example = new Example(CollaborateOrderDO.class);
        example.createCriteria().andIn("inveCustomsSn", inveCustomsSnList).andEqualTo("deleted", 0);
        List<CollaborateOrderDO> collaborateOrderDOS = collaborateOrderMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(collaborateOrderDOS)) {
            log.warn("协同单不存在");
        }
        for (CollaborateOrderDO collaborateOrderDO : collaborateOrderDOS) {
            if (Objects.isNull(collaborateOrderDO.getTallyReportSn())) {
                log.info("协同单id={}，清单编号={}，未收到理货报告", collaborateOrderDO.getId(), collaborateOrderDO.getInveCustomsSn());
                result.add(collaborateOrderDO.getInveCustomsSn());
            }
        }
        return result;
    }

    public void updateDiffQty(Long collaborateId, int diffQty) {
        CollaborateOrderDO collaborateOrderDO = new CollaborateOrderDO();
        collaborateOrderDO.setId(collaborateId);
        collaborateOrderDO.setDiffQty(diffQty);
        collaborateOrderMapper.updateByPrimaryKeySelective(collaborateOrderDO);
    }
}
