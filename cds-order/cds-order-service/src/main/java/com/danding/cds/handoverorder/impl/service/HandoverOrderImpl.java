package com.danding.cds.handoverorder.impl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import com.danding.cds.exportorder.api.dto.ExportOrderDTO;
import com.danding.cds.exportorder.api.dto.ExportOrderSubmit;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handoverOrder.api.dto.*;
import com.danding.cds.handoverOrder.api.enums.*;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.handoverorder.impl.entity.HandoverOrderDO;
import com.danding.cds.handoverorder.impl.mapper.HandoverOrderMapper;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.common.utils.CopyUtil;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.dt.component.common.result.Result;
import com.dt.platform.wms.rpc.client.handover.IHandoverBizRpcClient;
import com.dt.platform.wms.rpc.dto.HandoverDetailRpcDTO;
import com.dt.platform.wms.rpc.dto.HandoverRpcDTO;
import com.dt.platform.wms.rpc.dto.WarehouseRpcDTO;
import com.dt.platform.wms.rpc.param.HandoverRpcParam;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * {@code @description} 交接单业务处理
 * @date 2021/12/14
 */
@Slf4j
@DubboService
@RefreshScope
public class HandoverOrderImpl implements HandoverOrderService {
    @Autowired
    private HandoverOrderMapper handoverOrderMapper;
    @DubboReference
    private HandoverOrderDetailService handoverOrderDetailService;
    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private IHandoverBizRpcClient iHandoverBizRpcClient;
    @DubboReference
    private ExportOrderService exportOrderService;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private OrderService orderService;
    @Autowired
    private MessageSender messageSender;

    @Autowired
    private BaseDataService baseDataService;

    /**
     * 多租户查询
     */
    private static ExecutorService handleTasksThread = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    /**
     * 批量新增交接单
     *
     * @param handoverOrderDTOList
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveHandoverOrder(List<HandoverOrderDTO> handoverOrderDTOList) {
        log.warn("【po:HandoverOrder】 交接单数据 - {} ", JSON.toJSON(handoverOrderDTOList));
        List<HandoverOrderDO> handoverOrderDOS = new ArrayList<>();
        //获取所有交接单sn
        Map<Long, List<HandoverOrderDTO>> tenantDTOMap = new HashMap<>();
        for (HandoverOrderDTO handoverOrderDTO : handoverOrderDTOList) {
            List<HandoverOrderDetailDTO> allTenantDetailList = handoverOrderDTO.getHandoverOrderDetailDTO();
            //获取所有的租户id
            List<Long> tenantIdList = allTenantDetailList.stream().map(HandoverOrderDetailDTO::getTenantId).distinct().collect(Collectors.toList());
            tenantIdList.forEach(tenantId -> {
                List<HandoverOrderDetailDTO> detailDTOList = handoverOrderDTO.getHandoverOrderDetailDTO();
                List<HandoverOrderDetailDTO> result = detailDTOList.stream().filter(d ->
                        Objects.equals(tenantId, d.getTenantId())
                ).collect(Collectors.toList());
                //影分身复制一个
                HandoverOrderDTO tenantOrderDto = BeanUtil.copyProperties(handoverOrderDTO, HandoverOrderDTO.class);
                tenantOrderDto.setHandoverOrderDetailDTO(result);
                if (tenantDTOMap.containsKey(tenantId)) {
                    List<HandoverOrderDTO> handoverOrderDTOS = tenantDTOMap.get(tenantId);
                    handoverOrderDTOS.add(tenantOrderDto);
                } else {
                    List<HandoverOrderDTO> handoverOrderDTOS = new ArrayList<>();
                    handoverOrderDTOS.add(tenantOrderDto);
                    tenantDTOMap.put(tenantId, handoverOrderDTOS);
                }
            });
        }

        Iterator<Map.Entry<Long, List<HandoverOrderDTO>>> iterator = tenantDTOMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, List<HandoverOrderDTO>> next = iterator.next();
            Long tenantId = next.getKey();
            List<HandoverOrderDTO> handoverOrderDTOS = next.getValue();
            handleTasksThread.submit(() -> {
                SimpleTenantHelper.setTenantId(tenantId);
                this.handleHandoverOrderCore(handoverOrderDTOS);
            });
        }
//
    }

    public void handleHandoverOrderCore(List<HandoverOrderDTO> handoverOrderDTOList) {
        for (HandoverOrderDTO handoverOrderDTO : handoverOrderDTOList) {
            String handoverSn = handoverOrderDTO.getHandoverSn();
            HandoverOrderDO handoverOrderDO = this.findByHandoverSn(handoverSn);
            if (Objects.nonNull(handoverOrderDO)) {
                continue;
            }
            //set交接单数据
            handoverOrderDO = BeanUtil.copyProperties(handoverOrderDTO, HandoverOrderDO.class);
            handoverOrderDO.setStatus(OutboundOrderStatus.TREAT_OUTBOUND.getValue());
            handoverOrderDO.setFinishStatus(HandoverOrderFinishStatus.TREAT_OUTBOUND.getValue());
//            handoverOrderDO.setTotalWeight(new BigDecimal(handoverOrderDTO.getTotalWeight()));
            handoverOrderDO.setCreateTime(new Date());
            UserUtils.setCreateAndUpdateBy(handoverOrderDO);
            List<HandoverOrderDetailDTO> handoverOrderDetailDTO = handoverOrderDTO.getHandoverOrderDetailDTO();
            log.info("【po:HandoverOrderDetailDTO】 交接单明细 - {} ", JSON.toJSON(handoverOrderDetailDTO));
            //获取包裹总重量
            double weightSum = handoverOrderDetailDTO.stream().mapToDouble(h -> Double.valueOf(h.getPackageWeight())).sum();
            handoverOrderDO.setTotalWeight(BigDecimal.valueOf(weightSum));
            handoverOrderDO.setPackageNum(handoverOrderDetailDTO.size());

            List<String> wayBillList = handoverOrderDetailDTO.stream().map(HandoverOrderDetailDTO::getWayBillSn).distinct().collect(Collectors.toList());
            List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.listByLogistics90Days(wayBillList);
            Map<String, CustomsInventoryDTO> logisticsNoMap = customsInventoryDTOList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getLogisticsNo, Function.identity(), (v1, v2) -> v1));
            final Map<String, String> mailNoExportOrderDTOMap = exportOrderService.listExportOrderByMailNo(wayBillList);
            Integer associateWaybillCount = 0;
            List<HandoverOrderDetailDTO> orderDetailDTOList = new ArrayList<>();
            for (HandoverOrderDetailDTO orderDetailDTO : handoverOrderDetailDTO) {
                String wayBillSn = orderDetailDTO.getWayBillSn();
                HandoverOrderDetailDTO detailDTO = BeanUtil.copyProperties(orderDetailDTO, HandoverOrderDetailDTO.class);
                detailDTO.setHandoverSn(handoverOrderDTO.getHandoverSn());
                detailDTO.setExpressCode(handoverOrderDTO.getWmsExpressCode());
                detailDTO.setExpressName(handoverOrderDTO.getWmsExpressName());
                detailDTO.setStoreHouseName(handoverOrderDTO.getStoreHouseName());
                detailDTO.setStoreHouseSn(handoverOrderDTO.getStoreHouseSn());
                detailDTO.setCreateBy(UserUtils.getUserId());
                detailDTO.setUpdateBy(UserUtils.getUserId());
                //根据WMS运单编号获取申报单
                CustomsInventoryDTO inventoryDTO = null;
                if (logisticsNoMap.containsKey(orderDetailDTO.getWayBillSn())) {
                    inventoryDTO = logisticsNoMap.get(orderDetailDTO.getWayBillSn());
                }
                log.info("CustomsLogisticsDTO 运单{} 是否存在={}, 交接单号={}", orderDetailDTO.getWayBillSn(), Objects.nonNull(inventoryDTO), handoverOrderDTO.getHandoverSn());
                if (inventoryDTO != null) {
                    detailDTO.setUnusualStatus(HandoverDetailStatus.NO.getValue());
                    detailDTO.setCustomsInventorySn(inventoryDTO.getSn());
                    detailDTO.setDeclareOrderNo(inventoryDTO.getDeclareOrderNo());
                } else {
                    detailDTO.setUnusualStatus(HandoverDetailStatus.YES.getValue());
                    detailDTO.setUnusualMsg("获取申报单号异常!");
                }
                //判断是否已绑定出库单
                if (mailNoExportOrderDTOMap.containsKey(wayBillSn)) {
                    String exportOrderSn = mailNoExportOrderDTOMap.get(wayBillSn);
                    detailDTO.setOutboundOrder(exportOrderSn);
                    detailDTO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.YES.getValue());
                    associateWaybillCount++;
                } else {
                    detailDTO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.NO.getValue());
                }
                orderDetailDTOList.add(detailDTO);
            }
            //根据表体已关联单号的数量判断交接单状态
            if (associateWaybillCount == 0) {
                handoverOrderDO.setStatus(HandoverOrderStatus.WAIT_EXPORT.getValue());
            } else if (associateWaybillCount == orderDetailDTOList.size()) {
                handoverOrderDO.setStatus(HandoverOrderStatus.ALL_EXPORTED.getValue());
            } else {
                handoverOrderDO.setStatus(HandoverOrderStatus.PART_EXPORT.getValue());
            }
            log.info("【po:HandoverOrderDetail】 添加明细 - {}", JSON.toJSON(orderDetailDTOList));
            handoverOrderDetailService.addListDetail(orderDetailDTOList);
            log.info("【po:HandoverOrderDetail】 添加交接单 - {}", JSON.toJSON(handoverOrderDO));
            handoverOrderMapper.insert(handoverOrderDO);
            //发送mq更新清单状态(是否关联交接单)
            messageSender.sendMsg(handoverOrderDO.getHandoverSn(), "ccs-handover-inventory-associate-topic");
        }
    }

    public List<HandoverOrderDO> findByHandoverSn(List<String> handoverSnList) {
        if (CollectionUtil.isEmpty(handoverSnList)) {
            return new ArrayList<>();
        }
        Example example = new Example(HandoverOrderDO.class);
        example.createCriteria().andIn("handoverSn", handoverSnList).andEqualTo("deleted", false);
        List<HandoverOrderDO> handoverOrderDOList = handoverOrderMapper.selectByExample(example);
        return handoverOrderDOList;
    }

    public HandoverOrderDO findByHandoverSn(String handoverSn) {
        HandoverOrderDO handoverOrderDO = new HandoverOrderDO();
        handoverOrderDO.setHandoverSn(handoverSn);
        HandoverOrderDO orderDO = handoverOrderMapper.selectOne(handoverOrderDO);
        return orderDO;
    }

    /**
     * 获取WMS下保税仓列表
     *
     * @return
     */
    @Override
    public List<WarehouseDTO> getWarehouseInfo() {
        Result<List<WarehouseRpcDTO>> result = iHandoverBizRpcClient.getWarehouseInfo();
        log.info("【WarehouseRpc】 WMS仓库列表 - {}", result.getData());
        if (Objects.equals(result.getCode(), 200)) {
            return CopyUtil.copyList(result.getData(), WarehouseDTO.class);
        } else {
            throw new ArgsErrorException("仓库列表获取异常！");
        }

    }

    /**
     * 交接单同步
     *
     * @param orderDTO
     * @return
     */
    public List<HandoverOrderDTO> handoverOrderSyn(HandoverOrderDTO orderDTO) throws ArgsErrorException {
        List<HandoverOrderDTO> handoverOrderDTOList = new ArrayList<>();
        log.info("【po:HandoverOrder】 交接单号 - {} , 仓库编码 - {}, 开始时间 - {}, 结束时间 - {} ", orderDTO.getHandoverSn(), orderDTO.getStoreHouseSn(), orderDTO.getStaTime(), orderDTO.getEndTime());
        //设置同步入参
        HandoverRpcParam param = new HandoverRpcParam();
        if (Objects.nonNull(orderDTO.getHandoverSn())) {
            param.setHandoverSn(orderDTO.getHandoverSn());
        }
        param.setStoreHouseSn(orderDTO.getStoreHouseSn());
        param.setTimeStart(orderDTO.getStaTime());
        param.setTimeEnd(orderDTO.getEndTime());
        //先获取交接单号
        Result<List<String>> result = iHandoverBizRpcClient.getHandoverList(param);
        log.info("HandoverRpc data - {}, code- {},msg - {}", JSON.toJSONString(result.getData()), result.getCode(), result.getMessage());
        if (!Objects.equals(result.getCode(), 200)) {
            throw new ArgsErrorException("交接单获取失败！");
        }
        if (Objects.equals(result.getData().size(), 0)) {
            throw new ArgsErrorException("暂未获取到更多的交接单数据！");
        }
        //去同步交接单
        param.setHandoverSnList(result.getData());
        Result<List<HandoverRpcDTO>> handoverWithDetailList = iHandoverBizRpcClient.getHandoverWithDetailList(param);
        log.info("HandoverRpcSyn data - {}, code- {},msg - {}", JSON.toJSONString(handoverWithDetailList.getData()), handoverWithDetailList.getCode(), handoverWithDetailList.getMessage());
        if (Objects.equals(handoverWithDetailList.getCode(), 400)) {
            log.error("HandoverRpcSyn - {}", handoverWithDetailList.getMessage());
            throw new ArgsErrorException("交接单同步失败！");
        }
        for (HandoverRpcDTO rpcDTO : handoverWithDetailList.getData()) {
            //根据交接单剔除重复数据
            HandoverOrderDO handoverOrderDO = findByHandoverSn(rpcDTO.getHandoverSn());
            if (handoverOrderDO != null) {
                continue;
            }
            HandoverOrderDTO handoverOrderDTO = BeanUtil.copyProperties(rpcDTO, HandoverOrderDTO.class);
            handoverOrderDTO.setWmsExpressCode(rpcDTO.getWmsExpressId());
            handoverOrderDTO.setTotalWeight(rpcDTO.getTotalWeight().toString());
            List<HandoverOrderDetailDTO> handoverOrderDetailDTOList = new ArrayList<>();
            for (HandoverDetailRpcDTO detailRpcDTO : rpcDTO.getDetailList()) {
                HandoverOrderDetailDTO detailDTO = BeanUtil.copyProperties(detailRpcDTO, HandoverOrderDetailDTO.class);
                detailDTO.setPackageWeight(detailRpcDTO.getPackageWeight().toString());
                handoverOrderDetailDTOList.add(detailDTO);
            }
            handoverOrderDTO.setHandoverOrderDetailDTO(handoverOrderDetailDTOList);
            handoverOrderDTO.setWmsExpressName(rpcDTO.getWmsExpressName());
            handoverOrderDTOList.add(handoverOrderDTO);
        }
        log.info("[saveHandoverOrder] 发送交接单信息 - {}", JSONObject.toJSONString(handoverOrderDTOList));
        //批量新增交接单
        saveHandoverOrder(handoverOrderDTOList);
        return handoverOrderDTOList;
    }


    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    @PageSelect
    public ListVO<HandoverOrderVO> paging(HandoverOrderParam param) {
        ListVO<HandoverOrderVO> result = new ListVO<>();
        Example example = this.buildExample(param);
//        example.and(criteria);
        List<HandoverOrderDO> list = handoverOrderMapper.selectByExample(example);
        List<String> snList = list.stream().map(HandoverOrderDO::getHandoverSn).distinct().collect(Collectors.toList());
        List<HandoverOrderDetailDTO> orderDetailDTOList = handoverOrderDetailService.getDetailByHandoverOrder(snList);
        Map<String, List<HandoverOrderDetailDTO>> snDetailMap = orderDetailDTOList.stream().collect(Collectors.groupingBy(HandoverOrderDetailDTO::getHandoverSn));
        List<HandoverOrderVO> orderVOList = list.stream().map(i -> {
            HandoverOrderVO handoverOrderVO = new HandoverOrderVO();
            BeanUtils.copyProperties(i, handoverOrderVO);
            handoverOrderVO.setFinishStatus(HandoverOrderFinishStatus.getEnum(i.getFinishStatus()).getDesc());
            if (snDetailMap.containsKey(i.getHandoverSn())) {
                List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = snDetailMap.get(i.getHandoverSn());
                int outBoundCount = (int) handoverOrderDetailDTOS.stream().filter(h -> Objects.equals(h.getAssociateOutboundStatus(), HandoverDetailOutBoundStatus.YES.getValue())).count();
                int notOutCount = handoverOrderDetailDTOS.size() - outBoundCount;
                handoverOrderVO.setNotAssociateCount(notOutCount);
                handoverOrderVO.setAssociateCount(outBoundCount);
                String exportOrder = handoverOrderDetailDTOS.stream().map(HandoverOrderDetailDTO::getOutboundOrder).filter(Objects::nonNull).distinct().collect(Collectors.joining(","));
                handoverOrderVO.setOutboundOrder(exportOrder);
            }
            return handoverOrderVO;
        }).collect(Collectors.toList());

        result.setDataList(orderVOList);
        // 分页
        PageInfo<HandoverOrderDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(param.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public List<HandoverOrderDTO> pagingListAll(HandoverOrderParam param) {
        if (Objects.isNull(param)) {
            return new ArrayList<>();
        }
        Example example = this.buildExample(param);
        List<HandoverOrderDO> handoverOrderDOS = handoverOrderMapper.selectByExample(example);
        return ConvertUtil.listConvert(handoverOrderDOS, HandoverOrderDTO.class);
    }

    private Example buildExample(HandoverOrderParam param) {
        Example example = new Example(HandoverOrderDO.class);
        example.orderBy("createTime").desc();
        Example.Criteria criteria = example.createCriteria();
        if (Objects.nonNull(param.getHandoverSn())) {
            List<String> handoverOrders = Arrays.asList(param.getHandoverSn().split(","));
            criteria.andIn("handoverSn", handoverOrders);
        }
        if (Objects.nonNull(param.getExpressCode())) {
            criteria.andEqualTo("wmsExpressCode", param.getExpressCode());
        }
        if (Objects.nonNull(param.getHouse())) {
            criteria.andEqualTo("storeHouseSn", param.getHouse());
        }
        if (Objects.nonNull(param.getVehicleInfo())) {
            criteria.andEqualTo("vehicleInfo", param.getVehicleInfo());
        }
        if (Objects.nonNull(param.getCreateFrom()) && Objects.nonNull(param.getCreateTo())) {
            criteria.andBetween("createTime", new Date(param.getCreateFrom()), new Date(param.getCreateTo()));
        }
        if (Objects.nonNull(param.getWaybillSn())) {
            List<HandoverOrderDetailDTO> orderDetailDTOList = handoverOrderDetailService.getHandoverOrderDetailByMailNo(param.getWaybillSn());
            List<String> handoverOrders = new ArrayList<>();
            for (HandoverOrderDetailDTO detailDTO : orderDetailDTOList) {
                handoverOrders.add(detailDTO.getHandoverSn());
            }
            if (!CollectionUtils.isEmpty(handoverOrders) && handoverOrders.size() > 0) {
                criteria.andIn("handoverSn", handoverOrders);
            } else {
                criteria.andEqualTo("handoverSn", "-1");
                // 分页
//                PageResult pageResult = new PageResult();
//                pageResult.setCurrentPage(1);
//                pageResult.setPageSize(20);
//                result.setPage(pageResult);
//                return result;
            }
        }
        if (Objects.nonNull(param.getOutboundOrder())) {
            List<String> outboundOrder = Arrays.asList(param.getOutboundOrder().split(","));
            List<String> handoverSnList = handoverOrderDetailService.getHandoverSnByOutBoundOrder(outboundOrder);
            if (!CollectionUtils.isEmpty(handoverSnList)) {
                criteria.andIn("handoverSn", handoverSnList);
            }
        }
        criteria.andEqualTo("status", param.getStatus());
        return example;
    }

    @Override
    public HandoverOrderDTO getById(Long id) {
        HandoverOrderDO handoverOrderDO = new HandoverOrderDO();
        handoverOrderDO.setId(id);
        HandoverOrderDTO handoverOrderDTO = BeanUtil.copyProperties(handoverOrderMapper.selectOne(handoverOrderDO), HandoverOrderDTO.class);
        return handoverOrderDTO;
    }

    @Override
    public void updHandoverOrder(ExportOrderDTO exportOrderDTO, String handoverSn, String mailNo) {
        log.warn("[ExportOrderSubmit] 出库单 - {} ,交接单 - {}", exportOrderDTO, handoverSn);
        //修改明细出库单关联状态
        handoverOrderDetailService.updHandoverSn(mailNo, HandoverDetailOutBoundStatus.NO, exportOrderDTO.getSn());
        HandoverOrderDO handoverOrderDO = this.findByHandoverSn(handoverSn);
        handoverOrderDO.setOutboundOrder(exportOrderDTO.getSn());
        //判断运单是否全部绑定出库单
        List<HandoverOrderDetailDTO> orderDetailDTOList = handoverOrderDetailService.getDetailByHandoverOrder(handoverSn);
        HandoverOrderStatus handoverOrderStatus = this.getHandoverOrderStatus(orderDetailDTOList);
        handoverOrderDO.setStatus(handoverOrderStatus.getValue());
        //修改交接单
        UserUtils.setUpdateBy(handoverOrderDO);
        handoverOrderDO.setUpdateTime(new Date());
        handoverOrderMapper.updateByPrimaryKey(handoverOrderDO);
        log.warn("[handoverOrderDO] 交接单 - {} ", handoverOrderDO);
    }

    public HandoverOrderStatus getHandoverOrderStatus(String sn) {
        List<HandoverOrderDetailDTO> detailByHandoverOrder = handoverOrderDetailService.getDetailByHandoverOrder(sn);
        return this.getHandoverOrderStatus(detailByHandoverOrder);
    }

    /**
     * 获取交接单表体判断交接单状态
     *
     * @param orderDetailDTOList
     * @return
     */
    public HandoverOrderStatus getHandoverOrderStatus(List<HandoverOrderDetailDTO> orderDetailDTOList) {
        if (CollectionUtils.isEmpty(orderDetailDTOList)) {
            return HandoverOrderStatus.WAIT_EXPORT;
        }
        if (orderDetailDTOList.stream().allMatch(v -> Objects.nonNull(v.getOutboundOrder()))) {
            return HandoverOrderStatus.ALL_EXPORTED;
        } else if (orderDetailDTOList.stream().allMatch(v -> Objects.isNull(v.getOutboundOrder()))) {
            return HandoverOrderStatus.WAIT_EXPORT;
        } else {
            return HandoverOrderStatus.PART_EXPORT;
        }
    }

    /**
     * 更新交接单状态
     *
     * @param sn
     */
    @Override
    public void updateHandoverOrderStatus(String sn) {
        HandoverOrderStatus handoverOrderStatus = this.getHandoverOrderStatus(sn);
        log.info("updateHandoverOrderStatus sn={} 状态为={}", sn, handoverOrderStatus.getDesc());
        HandoverOrderDO handoverOrderDO = new HandoverOrderDO();
        handoverOrderDO.setStatus(handoverOrderStatus.getValue());
        Example example = new Example(HandoverOrderDO.class);
        example.createCriteria().andEqualTo("deleted", false).andEqualTo("handoverSn", sn);
        handoverOrderMapper.updateByExampleSelective(handoverOrderDO, example);
    }

    /**
     * 创建出库单
     *
     * @return
     */
    @Override
    public void saveExportOrder(HandoverOrderParam param) {
        List<ExportItemRecord> successRecordList = param.getExportItemWritingReport().getSuccessRecordList();
        if (CollectionUtil.isEmpty(successRecordList)) {
            return;
        }
        List<String> handoverSnList = Arrays.asList(param.getHandoverSn().split(","));
        List<HandoverOrderDO> handoverOrderDOList = this.findByHandoverSn(handoverSnList);
        //1.生成出库单
        ExportOrderSubmit exportOrderSubmit = new ExportOrderSubmit();
//        exportOrderSubmit.setAccountBookId(param.getAccountBookId());
//        exportOrderSubmit.setDeclareCompanyId(param.getDeclareCompanyId());
        exportOrderSubmit.setEntityWarehouseCode(param.getEntityWarehouseCode());
        ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(param.getExpressCode());
        if (Objects.nonNull(expressDTO)) {
            List<Long> expressIds = new ArrayList<>();
            expressIds.add(expressDTO.getId());
            exportOrderSubmit.setExpressIdList(expressIds);
        }
        Long exportId = exportOrderService.create(exportOrderSubmit);
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(exportId);
        //2.将运单绑定到出库单
        exportOrderService.bindWaybill(exportId, param.getExportItemWritingReport());
        List<String> mailNoList = successRecordList.stream().map(ExportItemRecord::getMailNo).distinct().collect(Collectors.toList());
        //3.更新表体关联出库单
        handoverOrderDetailService.updateDetailExportOrder(mailNoList, exportOrderDTO.getSn());
        //4.修改交接单状态
        this.refreshHandoverStatus(handoverOrderDOList.stream().map(HandoverOrderDO::getHandoverSn).distinct().collect(Collectors.toList()));
    }

    /**
     * 更新交接单状态
     *
     * @param handoverOrderDOList
     */
    public void refreshHandoverStatus(List<String> handoverOrderDOList) {
        List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = handoverOrderDetailService.getDetailByHandoverOrder(handoverOrderDOList);
        Map<String, List<HandoverOrderDetailDTO>> snDetailMap = handoverOrderDetailDTOS.stream().collect(Collectors.groupingBy(HandoverOrderDetailDTO::getHandoverSn));
        Iterator<Map.Entry<String, List<HandoverOrderDetailDTO>>> iterator = snDetailMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<String, List<HandoverOrderDetailDTO>> next = iterator.next();
            String handoverSn = next.getKey();
            List<HandoverOrderDetailDTO> value = next.getValue();
            if (value.stream().allMatch(v -> Objects.nonNull(v.getOutboundOrder()))) {
                this.updateHandoverStatus(handoverSn, HandoverOrderStatus.ALL_EXPORTED);
            } else if (value.stream().allMatch(v -> Objects.isNull(v.getOutboundOrder()))) {
                this.updateHandoverStatus(handoverSn, HandoverOrderStatus.WAIT_EXPORT);
            } else {
                this.updateHandoverStatus(handoverSn, HandoverOrderStatus.PART_EXPORT);
            }
        }
    }

    @Override
    public void updateHandoverStatus(String handoverSn, HandoverOrderStatus handoverOrderStatus) {
        HandoverOrderDO handoverOrderDO = new HandoverOrderDO();
        handoverOrderDO.setStatus(handoverOrderStatus.getValue());
        Example example = new Example(HandoverOrderDO.class);
        example.createCriteria().andEqualTo("handoverSn", handoverSn);
        handoverOrderMapper.updateByExampleSelective(handoverOrderDO, example);
    }

    /**
     * apollo配置可以跳过
     */
    @Value("${uncheckHandover.customsBookId:[]}")
    private Long[] uncheckHandoverCustomsBookId;

    /**
     * 在允许跳过校验的账册中直接返回ture
     *
     * @param customsBookId
     * @return
     */
    @Override
    public Boolean skipCheckHandoverByBookId(Long customsBookId) {
        List<Long> skipCheckBookIdList = Arrays.asList(uncheckHandoverCustomsBookId);
        CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(customsBookId);
        List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(customsBookDTO.getBookTag());
        Boolean skipCheck = false;
        if (skipCheckBookIdList.contains(customsBookId)
                || !bookTagList.contains(CustomsBookTagEnums.HANDOVER_ENABLE.getCode())) {
            skipCheck = true;
        }
        return skipCheck;
    }

    @Override
    public HandoverStatusCountResult getHandoverStatusCount(HandoverOrderParam param) {
        Example example = this.buildExample(param);
        List<Example.Criteria> oredCriteria = example.getOredCriteria();
        Example partExample = new Example(HandoverOrderDO.class);
        partExample.createCriteria().andEqualTo("status", HandoverOrderStatus.PART_EXPORT.getValue());
        oredCriteria.forEach(partExample::and);
        int partCount = handoverOrderMapper.selectCountByExample(partExample);

        Example waitExample = new Example(HandoverOrderDO.class);
        waitExample.createCriteria().andEqualTo("status", HandoverOrderStatus.WAIT_EXPORT.getValue());
        oredCriteria.forEach(waitExample::and);
        int waitCount = handoverOrderMapper.selectCountByExample(waitExample);

        HandoverStatusCountResult handoverStatusCountResult = new HandoverStatusCountResult();
        handoverStatusCountResult.setWaitExportCount(waitCount);
        handoverStatusCountResult.setPartExportCount(partCount);
        return handoverStatusCountResult;
    }

    /**
     * 刷新出库单状态
     * 1.刷新每个运单关联的出库单
     * 2.刷新交接单状态
     *
     * @param id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void refresh(Long id) {
        if (Objects.isNull(id)) {
            throw new ArgsInvalidException("id不允许为空");
        }
        HandoverOrderDO handoverOrderDO = handoverOrderMapper.selectByPrimaryKey(id);
        if (Objects.isNull(handoverOrderDO)) {
            throw new ArgsInvalidException("未查询到关联交接单");
        }
        //获取交接单详情
        List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = handoverOrderDetailService.getDetailByHandoverOrder(handoverOrderDO.getHandoverSn());
        //获取运单
        List<String> logisticsList = handoverOrderDetailDTOS.stream().map(HandoverOrderDetailDTO::getWayBillSn).distinct().collect(Collectors.toList());
        final Map<String, String> mailNoExportOrderDTOMap = exportOrderService.listExportOrderByMailNo(logisticsList);
        log.debug("refresh mailNoExportOrderDTOMap={}", JSON.toJSONString(mailNoExportOrderDTOMap));
        int associateWaybillCount = 0;
        List<HandoverOrderDetailDTO> orderDetailDTOList = new ArrayList<>();
        for (HandoverOrderDetailDTO orderDetailDTO : handoverOrderDetailDTOS) {
            HandoverOrderDetailDTO updateOrderDetailDTO = ConvertUtil.beanConvert(orderDetailDTO, HandoverOrderDetailDTO.class);
            //判断是否已绑定出库单
            String wayBillSn = orderDetailDTO.getWayBillSn();
            if (mailNoExportOrderDTOMap.containsKey(wayBillSn)) {
                String exportOrderSn = mailNoExportOrderDTOMap.get(wayBillSn);
                associateWaybillCount++;
                //如果和原来的出库单号一致不需要再次更新
                if (Objects.equals(exportOrderSn, orderDetailDTO.getOutboundOrder())) {
                    continue;
                } else {
                    updateOrderDetailDTO.setOutboundOrder(exportOrderSn);
                    updateOrderDetailDTO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.YES.getValue());
                }
            } else {
                updateOrderDetailDTO.setOutboundOrder(null);
                updateOrderDetailDTO.setAssociateOutboundStatus(HandoverDetailOutBoundStatus.NO.getValue());
            }
            orderDetailDTOList.add(updateOrderDetailDTO);
        }
        HandoverOrderDO updateOrderDO = new HandoverOrderDO();
        updateOrderDO.setId(handoverOrderDO.getId());
        //根据表体已关联单号的数量判断交接单状态
        if (associateWaybillCount == 0) {
            updateOrderDO.setStatus(HandoverOrderStatus.WAIT_EXPORT.getValue());
        } else if (associateWaybillCount == handoverOrderDetailDTOS.size()) {
            updateOrderDO.setStatus(HandoverOrderStatus.ALL_EXPORTED.getValue());
        } else {
            updateOrderDO.setStatus(HandoverOrderStatus.PART_EXPORT.getValue());
        }
        log.debug("refresh orderDetailDTOList={}", JSON.toJSONString(orderDetailDTOList));
        handoverOrderDetailService.updateListDetail(orderDetailDTOList);
        UserUtils.setUpdateBy(updateOrderDO);
        updateOrderDO.setUpdateTime(new Date());
        log.debug("refresh updateOrderDO={}", JSON.toJSONString(updateOrderDO));
        handoverOrderMapper.updateByPrimaryKeySelective(updateOrderDO);
        //发送mq更新清单状态(是否关联交接单)
        messageSender.sendMsg(handoverOrderDO.getHandoverSn(), "ccs-handover-inventory-associate-topic");
    }

    /**
     * 出库单预览
     *
     * @param report
     * @return
     */
    @Override
    public HandoverExportItemRecord outboundOrderPreview(HandoverWritingReport report) throws ArgsErrorException {
        HandoverExportItemRecord itemRecord = new HandoverExportItemRecord();
        List<String> snList = Arrays.asList(report.getHandoverSn().split(","));
        List<HandoverOrderDO> handoverOrderDOList = this.findByHandoverSn(snList);
        HandoverOrderDO allExportedOrder = handoverOrderDOList.stream().filter(h -> Objects.equals(h.getStatus(), HandoverOrderStatus.ALL_EXPORTED.getValue())).findAny().orElse(null);
        if (Objects.nonNull(allExportedOrder)) {
            throw new ArgsErrorException("交接单:" + allExportedOrder.getHandoverSn() + "已全部出库");
        }
        if (CollectionUtil.isEmpty(handoverOrderDOList)) {
            throw new ArgsErrorException("未找到交接单信息");
        }
        long warehouseSn = handoverOrderDOList.stream().map(HandoverOrderDO::getStoreHouseSn).distinct().count();
        boolean warehouseSame = warehouseSn == 1;
        if (!warehouseSame) {
            throw new ArgsErrorException("交接单仓库编码不一致!");
        }
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(report.getEntityWarehouseCode());
        if (CollectionUtil.isEmpty(entityWarehouseDTOList)
                || entityWarehouseDTOList.stream()
                    .noneMatch(ew ->
                        EntityWarehouseTagEnums.containsAny(ew.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))) {
            throw new ArgsErrorException("实体仓未配置普通账册或专用账册，请联系添加");
        }
        long expressCode = handoverOrderDOList.stream().map(HandoverOrderDO::getWmsExpressCode).distinct().count();
        boolean expressSame = expressCode == 1;
        if (!expressSame) {
            throw new ArgsErrorException("交接单快递编码不一致!");
        }
        //先根据交接单号获取到运单号
        List<HandoverOrderDetailDTO> detailDTOList = handoverOrderDetailService.getDetailBySnStatus(report.getHandoverSn(), HandoverDetailOutBoundStatus.NO);
        if (CollectionUtil.isEmpty(detailDTOList)) {
            throw new ArgsErrorException("未找到交接单明细,请检查交接单明细是否异常");
        }
        List<Long> expressList = new ArrayList<>();
        List<ExportItemRecord> exportItemRecords = new ArrayList<>();
        Integer idx = 0;
        for (HandoverOrderDetailDTO detailDTO : detailDTOList) {
            idx += 1;
            ExpressDTO expressDTO = baseDataService.getExpressDTOByCode(detailDTO.getExpressCode());
            if (Objects.isNull(expressDTO)) {
                continue;
            }
            //为了规避从申报出库单中完成出库申报同步已完成的出库单
            ExportItemRecord exportItemRecord = new ExportItemRecord();
            expressList.add(expressDTO.getId());
            exportItemRecord.setIdx(idx);
            exportItemRecord.setHandoverSn(detailDTO.getHandoverSn());
            exportItemRecord.setExpressId(expressDTO.getId());
            exportItemRecord.setExpressName(expressDTO.getName());
            exportItemRecord.setMailNo(detailDTO.getWayBillSn());
            exportItemRecord.setBizId(detailDTO.getPackageSn());
            exportItemRecord.setCustomsInventorySn(detailDTO.getCustomsInventorySn());
            exportItemRecords.add(exportItemRecord);
        }
        itemRecord.setExportItemRecords(exportItemRecords);
        itemRecord.setExpressList(expressList);
        return itemRecord;
    }


}
