package com.danding.cds.endorsement.impl.service;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.GzipUtil;
import com.danding.cds.common.utils.SevenZipUtil;
import com.danding.cds.endorsement.api.dto.EndorsementTrackLogDTO;
import com.danding.cds.endorsement.api.dto.EndorsementTrackLogSaveDTO;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementTrackLogService;
import com.danding.cds.endorsement.impl.entity.EndorsementTrackLogDO;
import com.danding.cds.endorsement.impl.mapper.EndorsementTrackLogMapper;
import com.danding.component.uc.helper.SimpleUserHelper;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import tk.mybatis.mapper.entity.Example;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 核注单日志
 * @date 2023/6/25 16:54
 */
@Slf4j
@DubboService
@RefreshScope
public class EndorsementTrackLogServiceImpl implements EndorsementTrackLogService {
    @Autowired
    private EndorsementTrackLogService endorsementTrackLogService;
    @Autowired
    private EndorsementTrackLogMapper endorsementTrackLogMapper;

    @Value("${endorsement.callback.sub.length:}")
    private Integer endorsementCallbackSubLength;

    @Override
    public void save(EndorsementTrackLogSaveDTO endorsementTrackLogSaveDTO) {
        EndorsementTrackLogDO endorsementTrackLogDO = ConvertUtil.beanConvert(endorsementTrackLogSaveDTO, EndorsementTrackLogDO.class);
        endorsementTrackLogMapper.insertSelective(endorsementTrackLogDO);
    }

    @Override
    public List<EndorsementTrackLogDTO> listTrackLogById(Long id) {
        Example example = new Example(EndorsementTrackLogDO.class);
        example.createCriteria().andEqualTo("endorsementId", id).andEqualTo("deleted", false);
        example.orderBy("createTime").desc();
        List<EndorsementTrackLogDO> endorsementTrackLogDOList = endorsementTrackLogMapper.selectByExample(example);
        if (CollUtil.isEmpty(endorsementTrackLogDOList)) {
            return new ArrayList<>();
        }
        return endorsementTrackLogDOList.stream().map(i -> {
            EndorsementTrackLogDTO trackLogDTO = new EndorsementTrackLogDTO();
            BeanUtils.copyProperties(i, trackLogDTO);
            if (StringUtil.isNotBlank(i.getCallbackDetail())) {
                String decompress;
                String compressType = StringUtil.isEmpty(i.getCompressType()) ? "" : i.getCompressType();
                switch (compressType) {
                    case "7zip":
                        decompress = SevenZipUtil.decompress(i.getCallbackDetail());
                        trackLogDTO.setCallbackDetail(StringUtil.isBlank(decompress) ? i.getCallbackDetail() : decompress);
                        break;
                    case "gzip":
                    default:
                        decompress = GzipUtil.decompress(i.getCallbackDetail());
                        trackLogDTO.setCallbackDetail(StringUtil.isBlank(decompress) ? i.getCallbackDetail() : decompress);
                        break;
                }
            }
            return trackLogDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public void buildStatusAndInfoLog(Long endorsementId, EndorsementOrderStatus status, String logInfo) {
        //记录核注单日志
        EndorsementTrackLogSaveDTO saveDTO = new EndorsementTrackLogSaveDTO();
        saveDTO.setEndorsementId(endorsementId);
        saveDTO.setStatus(status.getCode());
        saveDTO.setLogInfo(logInfo);
        saveDTO.setOperator(SimpleUserHelper.getRealUserName());
        saveDTO.setCreateTime(new Date());
        endorsementTrackLogService.save(saveDTO);
    }

    @Test
    public void test() {
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        try {
            File file = new File("D:\\xml.txt");
            FileInputStream inputStream = new FileInputStream(file);
            int len = 0;
            byte[] buffer = new byte[1024];
            while ((len = inputStream.read(buffer)) > 0) {
                outputStream.write(buffer, 0, len);
            }
            inputStream.close();
            String compressString = SevenZipUtil.compress(outputStream.toString());
            System.out.println(compressString + "\n" + compressString.length());
            String decompressString = SevenZipUtil.decompress(compressString);
            System.out.println(decompressString + "\n" + decompressString.length());
        } catch (Exception e) {

        }
    }

    @Override
    public void buildFullLog(Long endorsementId, EndorsementOrderStatus status, String logInfo, String detail) {
        try {
            //记录核注单日志
            EndorsementTrackLogSaveDTO saveDTO = new EndorsementTrackLogSaveDTO();
            saveDTO.setEndorsementId(endorsementId);
            saveDTO.setStatus(status.getCode());
            saveDTO.setLogInfo(logInfo);
            if (StringUtil.isNotBlank(detail)) {
                if (Objects.nonNull(endorsementCallbackSubLength) && detail.length() > endorsementCallbackSubLength) {
                    detail = detail.substring(0, endorsementCallbackSubLength);
                }
                saveDTO.setCallbackDetail(SevenZipUtil.compress(detail));
                saveDTO.setCompressType("7zip");
            }
            saveDTO.setOperator(SimpleUserHelper.getRealUserName());
            saveDTO.setCreateTime(new Date());
            endorsementTrackLogService.save(saveDTO);
        } catch (Exception e) {
            log.error("核注单日志保存失败", e);
        }
    }
}
