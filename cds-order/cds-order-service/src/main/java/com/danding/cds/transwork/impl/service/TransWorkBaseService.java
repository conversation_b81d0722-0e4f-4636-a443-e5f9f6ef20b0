package com.danding.cds.transwork.impl.service;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.danding.cds.transwork.impl.entity.TransWorkOrderDO;
import com.danding.cds.transwork.impl.entity.TransWorkOrderItemsDO;
import com.danding.cds.transwork.impl.entity.TransWorkOrderPackagingItemsDO;
import com.danding.cds.transwork.impl.mapper.TransWorkOrderItemsMapper;
import com.danding.cds.transwork.impl.mapper.TransWorkOrderMapper;
import com.danding.cds.transwork.impl.mapper.TransWorkPackagingItemsMapper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;

import cn.hutool.core.collection.CollUtil;
import tk.mybatis.mapper.entity.Example;

/**
 * @Author: yousx
 * @Date: 2024/03/11
 * @Description:
 */
@Service
public class TransWorkBaseService {

    @Resource
    private TransWorkOrderItemsMapper transWorkOrderItemsMapper;

    @Resource
    private TransWorkOrderMapper transWorkOrderMapper;

    @Resource
    private TransWorkPackagingItemsMapper transWorkPackagingItemsMapper;


    @Transactional(rollbackFor = Exception.class)
    public void insert(TransWorkOrderDO transWorkOrderDO) {
        if (transWorkOrderDO == null) {
            throw new ArgsErrorException("DO not null");
        }
        transWorkOrderMapper.insert(transWorkOrderDO);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertItems(List<TransWorkOrderItemsDO> itemsDOS) {
        if (CollUtil.isEmpty(itemsDOS)) {
            throw new ArgsErrorException("itemDOS not empty");
        }
        transWorkOrderItemsMapper.insertList(itemsDOS);
    }

    public List<TransWorkOrderDO> selectOrderByExample(Example example) {
        return transWorkOrderMapper.selectByExample(example);
    }

    public TransWorkOrderDO selectOrderById(Long id) {
        return transWorkOrderMapper.selectByPrimaryKey(id);
    }

    public List<TransWorkOrderItemsDO> selectItemsByTransId(Long id) {
        Example example = new Example(TransWorkOrderItemsDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("transId", id);
        return transWorkOrderItemsMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateItemByExample(TransWorkOrderItemsDO itemsDO, Example example) {
        transWorkOrderItemsMapper.updateByExampleSelective(itemsDO, example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderByExample(TransWorkOrderDO transWorkOrderDO, Example example) {
        transWorkOrderMapper.updateByExampleSelective(transWorkOrderDO, example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderSelectiveById(TransWorkOrderDO transWorkOrderDO) {
        transWorkOrderMapper.updateByPrimaryKeySelective(transWorkOrderDO);
    }

    public TransWorkOrderDO selectOrderByOutOrderNoAndType(String orderCode, String orderType) {
        Example example = new Example(TransWorkOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("outOrderNo", orderCode);
        criteria.andEqualTo("orderType", orderType);
        return transWorkOrderMapper.selectOneByExample(example);
    }

    public int countByExample(Example countExp) {
        return transWorkOrderMapper.selectCountByExample(countExp);
    }

    @Transactional(rollbackFor = Exception.class)
    public void insertPackagingItems(List<TransWorkOrderPackagingItemsDO> packagingItemsDOS) {
        transWorkPackagingItemsMapper.insertList(packagingItemsDOS);
    }

    public List<TransWorkOrderPackagingItemsDO> selectPackagingItemsByTransId(Long id) {
        Example example = new Example(TransWorkOrderPackagingItemsDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("transId", id);
        return transWorkPackagingItemsMapper.selectByExample(example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updatePackagingItemsByExampleSelective(TransWorkOrderPackagingItemsDO itemsDO, Example example) {
        transWorkPackagingItemsMapper.updateByExampleSelective(itemsDO, example);
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderById(TransWorkOrderDO transWorkOrderDO) {
        transWorkOrderMapper.updateByPrimaryKey(transWorkOrderDO);
    }

    public TransWorkOrderItemsDO selectItemsById(Long id) {
        return transWorkOrderItemsMapper.selectByPrimaryKey(id);
    }
}
