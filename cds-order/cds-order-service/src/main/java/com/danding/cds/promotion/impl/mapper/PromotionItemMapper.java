package com.danding.cds.promotion.impl.mapper;

import com.danding.cds.promotion.impl.entity.PromotionItemDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;

import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
public interface PromotionItemMapper extends Mapper<PromotionItemDO>, InsertListMapper<PromotionItemDO>, BatchUpdateMapper<PromotionItemDO>, AggregationPlusMapper<PromotionItemDO> {
}
