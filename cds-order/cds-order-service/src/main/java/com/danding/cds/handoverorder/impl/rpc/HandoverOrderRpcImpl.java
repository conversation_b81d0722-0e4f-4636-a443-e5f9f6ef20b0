package com.danding.cds.handoverorder.impl.rpc;


import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDTO;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.out.api.HandoverOrderRpc;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.HandoverOrderReqVo;
import com.danding.common.utils.CopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 交接单业务处理
 * @date 2021/12/14
 */
@Slf4j
@DubboService

public class HandoverOrderRpcImpl implements HandoverOrderRpc {


    @DubboReference
    private HandoverOrderService handoverOrderService;


    /**
     * 接收WMS交接单
     * @param submits
     * @return
     */
    @Override
    public RpcResult<String> acceptWMSHandoverOrder(List<HandoverOrderReqVo> submits){
        log.warn("【HandoverOrder accept】 WMS推送交接单数据 - {}", JSON.toJSON(submits));
        if (Objects.isNull(submits)) {
            return RpcResult.error("提交了为空的对象");
        }
        try {
            List<HandoverOrderDTO> handoverOrderDTOList = new ArrayList<>();
            for (HandoverOrderReqVo resVo : submits){
                HandoverOrderDTO handoverOrderDTO = BeanUtil.copyProperties(resVo,HandoverOrderDTO.class);
                List<HandoverOrderDetailDTO> detailDTOList = CopyUtil.copyList(resVo.getHandoverOrderDetailReqVo(),HandoverOrderDetailDTO.class);
                handoverOrderDTO.setHandoverOrderDetailDTO(detailDTOList);
                handoverOrderDTOList.add(handoverOrderDTO);
            }
            log.warn("【HandoverOrder accept】交接单数据转换 - {}", JSON.toJSON(handoverOrderDTOList));
            handoverOrderService.saveHandoverOrder(handoverOrderDTOList);
        }catch (Exception e){
            log.error("批量接收异常 - {}", e.getMessage(), e);
            return RpcResult.error("操作失败");
        }
        return RpcResult.success("操作成功");

    }



}
