package com.danding.cds.transwork.impl.mapper;

import com.danding.cds.transwork.impl.entity.TransWorkOrderPackagingItemsDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;

import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

/**
 * @Author: yousx
 * @Date: 2024/03/13
 * @Description:
 */
public interface TransWorkPackagingItemsMapper extends Mapper<TransWorkOrderPackagingItemsDO>, InsertListMapper<TransWorkOrderPackagingItemsDO>, BatchUpdateMapper<TransWorkOrderPackagingItemsDO>, AggregationPlusMapper<TransWorkOrderPackagingItemsDO> {
}
