package com.danding.cds.invenorder.impl.mapper;

import com.danding.cds.invenorder.impl.entity.InventoryOrderInfoDO;
import com.danding.cds.v2.bean.dto.InventoryOrderItemLockStockDTO;
import com.danding.cds.v2.bean.dto.SelectCountRes;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.*;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;
import java.util.List;

public interface InventoryOrderInfoMapper extends Mapper<InventoryOrderInfoDO>, InsertListMapper<InventoryOrderInfoDO>, BatchUpdateMapper<InventoryOrderInfoDO>, AggregationPlusMapper<InventoryOrderInfoDO> {
//    @Results(id = "InventoryOrderInfoMap", value = {
//            @Result(property = "id", column = "id"),
//            @Result(property = "inveCustomsSn", column = "inve_customs_sn"),
//            @Result(property = "inveCompanyId", column = "inve_company_id"),
//            @Result(property = "inveBusinessType", column = "inve_business_type"),
//            @Result(property = "inAccountBook", column = "in_account_book"),
//            @Result(property = "outAccountBook", column = "out_account_book"),
//            @Result(property = "refCheckOrderNo", column = "ref_check_order_no"),
//            @Result(property = "refHzInveNo", column = "ref_hz_inve_no"),
//            @Result(property = "pickUpNo", column = "pick_up_no"),
//            @Result(property = "entryExitCustoms", column = "entry_exit_customs"),
//            @Result(property = "transportMode", column = "transport_mode"),
//            @Result(property = "shipmentCountry", column = "shipment_country"),
//            @Result(property = "rentPerson", column = "rent_person"),
//            @Result(property = "bookId", column = "book_id"),
//            @Result(property = "applyPerson", column = "apply_person"),
//            @Result(property = "remark", column = "remark"),
//            @Result(property = "status", column = "status"),
//            @Result(property = "auditStatus", column = "audit_status"),
//            @Result(property = "statusTime", column = "status_time"),
//            @Result(property = "expectedOutAreaTime", column = "expected_out_area_time"),
//            @Result(property = "expectedToPortTime", column = "expected_to_port_time"),
//            @Result(property = "enable", column = "enable"),
//            @Result(property = "channel", column = "channel"),
//            @Result(property = "channelBusinessType", column = "channel_business_type"),
//            @Result(property = "channelBusinessSn", column = "channel_business_sn"),
//            @Result(property = "reason", column = "reason"),
//            @Result(property = "customsInvtType", column = "customs_invt_type"),
//            @Result(property = "customsEntryNo", column = "customs_entry_no"),
//            @Result(property = "customsEntryCompany", column = "customs_entry_company"),
//            @Result(property = "customsEntryType", column = "customs_entry_type"),
//            @Result(property = "masterOrderSn", column = "master_order_sn"),
//            @Result(property = "subOrderSn", column = "sub_order_sn"),
//            @Result(property = "selfOwnedVehicle", column = "self_owned_vehicle"),
//            @Result(property = "licensePlate", column = "license_plate"),
//            @Result(property = "vehicleCostRemark", column = "vehicle_cost_remark"),
//            @Result(property = "palletsNums", column = "pallets_nums"),
//            @Result(property = "endorsementSn", column = "endorsement_sn"),
//            @Result(property = "customsFlag", column = "customs_flag"),
//            @Result(property = "twoStepFlag", column = "twoStep_flag"),
//            @Result(property = "declarationFlag", column = "declaration_flag"),
//            @Result(property = "forwardingCompany", column = "forwarding_company"),
//            @Result(property = "conNo", column = "con_no"),
//            @Result(property = "arrivalPort", column = "arrival_port"),
//            @Result(property = "productName", column = "product_name"),
//            @Result(property = "category", column = "category"),
//            @Result(property = "actualArrivalDate", column = "actual_arrival_date"),
//            @Result(property = "collaborateFlag", column = "collaborate_flag"),
//            @Result(property = "pledgeOwnerFlag", column = "pledgeOwner_flag"),
//            @Result(property = "mailAddrList", column = "mailAddr_list"),
//            @Result(property = "mailStatus", column = "mail_status"),
//            @Result(property = "mailRejectReason", column = "mail_reject_reason"),
//            @Result(property = "callbackErpRejectOrderErrMsg", column = "callback_erp_Reject_order_err_msg"),
//            @Result(property = "declareWay", column = "declare_way"),
//            @Result(property = "upstreamNo", column = "upstream_no"),
//            @Result(property = "inOutOrderNo", column = "in_out_order_no"),
//            @Result(property = "associatedEndorsementNo", column = "associated_endorsement_no"),
//            @Result(property = "stateFlow", column = "state_flow"),
//            @Result(property = "entityWarehouseCode", column = "entity_warehouse_code"),
//            @Result(property = "entityWarehouseName", column = "entity_warehouse_name"),
//            @Result(property = "wmsWarehouseCode", column = "wms_warehouse_code"),
//            @Result(property = "ownerCode", column = "owner_code"),
//            @Result(property = "ownerName", column = "owner_name"),
//            @Result(property = "upstreamCancel", column = "upstream_cancel"),
//            @Result(property = "orderTag", column = "order_tag"),
//            @Result(property = "orderTodoTag", column = "order_todo_tag"),
//            @Result(property = "endorsementRealOrderNo", column = "endorsement_real_order_no"),
//            @Result(property = "draftListAttachmentName", column = "draft_list_attachment_name"),
//            @Result(property = "draftListAttachmentUrl", column = "draft_list_attachment_url"),
//            @Result(property = "transferor", column = "transferor"),
//            @Result(property = "transferee", column = "transferee"),
//            @Result(property = "transitFlag", column = "transit_flag"),
//            @Result(property = "finalInveCompanyId", column = "final_inve_company_id"),
//            @Result(property = "finalBookId", column = "final_book_id"),
//            @Result(property = "finalEntityWarehouseCode", column = "final_entity_warehouse_code"),
//            @Result(property = "finalEntityWarehouseName", column = "final_entity_warehouse_name"),
//            @Result(property = "finalOwnerCode", column = "final_owner_code"),
//            @Result(property = "finalOwnerName", column = "final_owner_name"),
//            @Result(property = "associatedTransitOrderSn", column = "associated_transit_order_sn"),
//            @Result(property = "associatedInOrderSn", column = "associated_in_order_sn"),
//            @Result(property = "associatedOutOrderSn", column = "associated_out_order_sn"),
//            @Result(property = "userId", column = "user_id"),
//            @Result(property = "userName", column = "user_name"),
//            @Result(property = "pushStatus", column = "push_status"),
//            @Result(property = "pushMsg", column = "push_msg"),
//            @Result(property = "pushMsgId", column = "push_msg_id"),
//            @Result(property = "customsEntryAttachUrl", column = "customs_entry_attach_url"),
//            @Result(property = "customsEntryDate", column = "customs_entry_date"),
//            @Result(property = "tallyComplete", column = "tally_complete"),
//            @Result(property = "callbackStatus", column = "callback_status"),
//            @Result(property = "callbackErrorMsg", column = "callback_error_msg"),
//            @Result(property = "carryOverNo", column = "carry_over_no"),
//            @Result(property = "enable", column = "enable"),
//            @Result(property = "createBy", column = "create_by"),
//            @Result(property = "updateBy", column = "update_by"),
//            @Result(property = "createTime", column = "create_time"),
//            @Result(property = "updateTime", column = "update_time"),
//            @Result(property = "deleted", column = "deleted")
//    })

    /**
     * 获取状态流
     *
     * @param id
     * @return
     */
    @Select("select state_flow from ccs_inventory_order_info where id = #{id}")
    String getStateFlow(@Param("id") Long id);


    /**
     * 获取状态流
     *
     * @param id
     * @return
     */
    @Select("select state_flow_detail from ccs_inventory_order_info where id = #{id}")
    String getStateFlowDetail(@Param("id") Long id);

    /**
     * 统计30天内是否存在包含该料号的清关单
     *
     * @param statusList
     * @param productId
     * @param createTimeFrom
     * @return
     */
    @Results(value = {
            @Result(property = "count", column = "count")
    })
    @Select(value = "<script>" +
            "SELECT COUNT(*) as count FROM `ccs_inventory_order_info` info " +
            "INNER JOIN `ccs_inventory_order_item` item ON info.`id` = item.`ref_inve_order_id` " +
            "where item.`origin_product_id` = #{productId} " +
            "and info.`status` in " +
            "   <foreach collection='statusList' item='item' index ='index' open='(' separator=',' close=')' > " +
            "       #{item} " +
            "   </foreach>" +
            "and info.`create_time` between #{createTimeFrom} and #{createTimeTo}" +
            "</script>")
    SelectCountRes countExist30DaysInByProductId(@Param("statusList") List<String> statusList,
                                                 @Param("productId") String productId,
                                                 @Param("createTimeFrom") Date createTimeFrom,
                                                 @Param("createTimeTo") Date createTimeTo);

    @Update(value = "<script>" +
            " UPDATE `ccs_inventory_order_info` " +
            " <set> " +
            " endorsement_sn = null, endorsement_real_order_no = null" +
            " </set>" +
            " WHERE id = #{id} " +
            "</script>")
    void cleanEndorsementInfoById(@Param("id") Long id);

    @Results(id = "inventoryOrderInfoResultMap", value = {
            @Result(property = "inveCustomsSn", column = "inve_customs_sn"),
            @Result(property = "inveCompanyId", column = "inve_company_id"),
            @Result(property = "inveBusinessType", column = "inve_business_type"),
            @Result(property = "inAccountBook", column = "in_account_book"),
            @Result(property = "outAccountBook", column = "out_account_book"),
            @Result(property = "refCheckOrderNo", column = "ref_check_order_no"),
            @Result(property = "refHzInveNo", column = "ref_hz_inve_no"),
            @Result(property = "pickUpNo", column = "pick_up_no"),
            @Result(property = "entryExitCustoms", column = "entry_exit_customs"),
            @Result(property = "transportMode", column = "transport_mode"),
            @Result(property = "shipmentCountry", column = "shipment_country"),
            @Result(property = "rentPerson", column = "rent_person"),
            @Result(property = "bookId", column = "book_id"),
            @Result(property = "applyPerson", column = "apply_person"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "status", column = "status"),
            @Result(property = "auditStatus", column = "audit_status"),
            @Result(property = "statusTime", column = "status_time"),
            @Result(property = "expectedOutAreaTime", column = "expected_out_area_time"),
            @Result(property = "expectedToPortTime", column = "expected_to_port_time"),
            @Result(property = "enable", column = "enable"),
            @Result(property = "channel", column = "channel"),
            @Result(property = "channelBusinessType", column = "channel_business_type"),
            @Result(property = "channelBusinessSn", column = "channel_business_sn"),
            @Result(property = "reason", column = "reason"),
            @Result(property = "customsInvtType", column = "customs_invt_type"),
            @Result(property = "customsEntryNo", column = "customs_entry_no"),
            @Result(property = "customsEntryCompany", column = "customs_entry_company"),
            @Result(property = "customsEntryType", column = "customs_entry_type"),
            @Result(property = "masterOrderSn", column = "master_order_sn"),
            @Result(property = "subOrderSn", column = "sub_order_sn"),
            @Result(property = "selfOwnedVehicle", column = "self_owned_vehicle"),
            @Result(property = "licensePlate", column = "license_plate"),
            @Result(property = "vehicleCostRemark", column = "vehicle_cost_remark"),
            @Result(property = "palletsNums", column = "pallets_nums"),
            @Result(property = "endorsementSn", column = "endorsement_sn"),
            @Result(property = "customsFlag", column = "customs_flag"),
            @Result(property = "twoStepFlag", column = "two_step_flag"),
            @Result(property = "declarationFlag", column = "declaration_flag"),
            @Result(property = "forwardingCompany", column = "forwarding_company"),
            @Result(property = "conNo", column = "con_no"),
            @Result(property = "arrivalPort", column = "arrival_port"),
            @Result(property = "productName", column = "product_name"),
            @Result(property = "category", column = "category"),
            @Result(property = "actualArrivalDate", column = "actual_arrival_date"),
            @Result(property = "collaborateFlag", column = "collaborate_flag"),
            @Result(property = "pledgeOwnerFlag", column = "pledge_owner_flag"),
            @Result(property = "mailAddrList", column = "mail_addr_list"),
            @Result(property = "mailStatus", column = "mail_status"),
            @Result(property = "mailRejectReason", column = "mail_reject_reason"),
            @Result(property = "callbackErpRejectOrderErrMsg", column = "callback_erp_Reject_order_err_msg"),
            @Result(property = "declareWay", column = "declare_way"),
            @Result(property = "upstreamNo", column = "upstream_no"),
            @Result(property = "inOutOrderNo", column = "in_out_order_no"),
            @Result(property = "associatedEndorsementNo", column = "associated_endorsement_no"),
            @Result(property = "stateFlow", column = "state_flow"),
            @Result(property = "entityWarehouseCode", column = "entity_warehouse_code"),
            @Result(property = "entityWarehouseName", column = "entity_warehouse_name"),
            @Result(property = "wmsWarehouseCode", column = "wms_warehouse_code"),
            @Result(property = "ownerCode", column = "owner_code"),
            @Result(property = "ownerName", column = "owner_name"),
            @Result(property = "upstreamCancel", column = "upstream_cancel"),
            @Result(property = "orderTag", column = "order_tag"),
            @Result(property = "orderTodoTag", column = "order_todo_tag"),
            @Result(property = "endorsementRealOrderNo", column = "endorsement_real_order_no"),
            @Result(property = "draftListAttachmentName", column = "draft_list_attachment_name"),
            @Result(property = "draftListAttachmentUrl", column = "draft_list_attachment_url"),
            @Result(property = "transferor", column = "transferor"),
            @Result(property = "transferee", column = "transferee"),
            @Result(property = "transitFlag", column = "transit_flag"),
            @Result(property = "finalInveCompanyId", column = "final_inve_company_id"),
            @Result(property = "finalBookId", column = "final_book_id"),
            @Result(property = "finalEntityWarehouseCode", column = "final_entity_warehouse_code"),
            @Result(property = "finalEntityWarehouseName", column = "final_entity_warehouse_name"),
            @Result(property = "finalOwnerCode", column = "final_owner_code"),
            @Result(property = "finalOwnerName", column = "final_owner_name"),
            @Result(property = "associatedTransitOrderSn", column = "associated_transit_order_sn"),
            @Result(property = "associatedInOrderSn", column = "associated_in_order_sn"),
            @Result(property = "associatedOutOrderSn", column = "associated_out_order_sn"),
            @Result(property = "userId", column = "user_id"),
            @Result(property = "userName", column = "user_name"),
            @Result(property = "pushStatus", column = "push_status"),
            @Result(property = "pushMsg", column = "push_msg"),
            @Result(property = "pushMsgId", column = "push_msg_id"),
            @Result(property = "customsEntryAttachUrl", column = "customs_entry_attach_url"),
            @Result(property = "customsEntryDate", column = "customs_entry_date"),
            @Result(property = "tallyComplete", column = "tally_complete"),
            @Result(property = "callbackStatus", column = "callback_status"),
            @Result(property = "callbackErrorMsg", column = "callback_error_msg"),
            @Result(property = "carryOverNo", column = "carry_over_no"),
            @Result(property = "customsType", column = "customs_type"),
            @Result(property = "corrCusDeclareCompanyId", column = "corr_cus_declare_company_id"),
            @Result(property = "rltCusInnerSFHRCompanyId", column = "rlt_cus_inner_sfhr_company_id"),
            @Result(property = "rltCusXFDYCompanyId", column = "rlt_cus_xfdw_company_id"),
            @Result(property = "rltCusDeclareCompanyId", column = "rlt_cus_declare_company_id"),
            @Result(property = "inOutFlag", column = "in_out_flag"),
            @Result(property = "ycNo", column = "yc_no"),
            @Result(property = "fbFlag", column = "fb_flag"),
            @Result(property = "fbChecklistSn", column = "fb_checklist_sn"),
            @Result(property = "entryPort", column = "entry_port"),
            @Result(property = "fromLocation", column = "from_location"),
            @Result(property = "upstreamOrigMsg", column = "upstream_orig_msg")
    })
    @Select(value = "<script>" +
            "SELECT" +
            " o.*  " +
            "FROM " +
            " ccs_inventory_order_info o " +
            " INNER JOIN ccs_inventory_order_item oi ON o.id = oi.ref_inve_order_id  " +
            "WHERE " +
            " product_id = #{productId} " +
            " AND channel = '4' " +
            " AND o.`status` = 'FINISH' " +
            " AND oi.is_new = 'new' " +
            " AND inve_business_type = 'ONELINE_IN' " +
            " order by status_time ASC " +
            " limit 1 " +
            "</script>")
    InventoryOrderInfoDO selectTaotianInfoByProductId(@Param("productId") String productId);

    @Update(value = "<script> " +
            "UPDATE ccs_inventory_order_info " +
            "<set>" +
            "urgent_sort = #{sort} ," +
            "order_tag = #{orderTag} " +
            "</set>" +
            "WHERE id  = #{id}" +
            "</script>")
    void updateUrgentProcess(@Param("id") Long id, @Param("isUrgent") Boolean isUrgent, @Param("orderTag") Integer orderTag, @Param("sort") Integer sort);

    @Update(value = "<script> " +
            "UPDATE ccs_inventory_order_info " +
            "<set>" +
            "urgent_sort = #{sort} ," +
            "</set>" +
            "WHERE id  = #{id}" +
            "</script>")
    void updateUrgentSort(@Param("id") Long id, @Param("sort") Integer sort);


    @Results(value = {
            @Result(property = "inventoryOrderSn", column = "inve_customs_sn"),
            @Result(property = "lockStockNum", column = "declare_unit_qfy"),
            @Result(property = "inventoryOrderStatus", column = "status"),
            @Result(property = "inventoryOrderBizType", column = "inve_business_type"),
            @Result(property = "inventoryOrderChannel", column = "channel"),
            @Result(property = "createTime", column = "create_time"),
    })
    @Select(value = "<script>" +
            "SELECT info.inve_customs_sn, item.declare_unit_qfy, info.status, info.inve_business_type, info.channel, info.`create_time` " +
            "FROM `ccs_inventory_order_info` info " +
            "INNER JOIN `ccs_inventory_order_item` item ON info.`id` = item.`ref_inve_order_id` " +
            "where info.lock_stock_flag = 1 " +
            "and info.`book_id` = #{customsBookId} " +
            "and item.`product_id` = #{productId} " +
            "and item.`goods_seq_no` = #{goodsSeqNo} " +
            "order by info.`create_time` desc " +
            "</script>")
    List<InventoryOrderItemLockStockDTO> getLockStockDetail(@Param("customsBookId") Long customsBookId,
                                                            @Param("productId") String productId,
                                                            @Param("goodsSeqNo") String goodsSeqNo);

//    @ResultMap(value = "InventoryOrderInfoMap")
//    @Select(value = "<script>" +
//            "SELECT * FROM ccs_inventory_order_info WHERE in_out_order_no = #{inOutOrderNo} " +
//            "</script>")
//    InventoryOrderInfoDO findByInOutOrderNo(@Param("inOutOrderNo") String inOutOrderNo);
//
//    @ResultMap(value = "InventoryOrderInfoMap")
//    @Select(value = "<script>" +
//            "SELECT * FROM ccs_inventory_order_info WHERE in_out_order_no in " +
//            "<foreach collection='inOutOrderNoList' item='item' index ='index' open='(' separator=',' close=')' > " +
//            " #{item} " +
//            "</foreach> " +
//            "</script>")
//    List<InventoryOrderInfoDO> findByInOutOrderNoList(@Param("inOutOrderNoList") List<String> inOutOrderNo);
}