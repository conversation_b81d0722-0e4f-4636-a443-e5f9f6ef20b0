package com.danding.cds.exportorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import javax.persistence.*;
import lombok.Getter;
import lombok.Setter;

@Table(name = "`ccs_export_express`")
@Getter
@Setter
public class ExportExpressDO extends BaseDO {
    /**
     * 出区单ID
     */
    @Column(name = "export_order_id")
    private Long exportOrderId;

    /**
     * 快递ID
     */
    @Column(name = "express_id")
    private Long expressId;
}