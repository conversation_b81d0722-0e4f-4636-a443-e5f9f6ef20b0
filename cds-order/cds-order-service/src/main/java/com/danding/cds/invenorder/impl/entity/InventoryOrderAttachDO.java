package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_inventory_order_attach")
@Getter
@Setter
public class InventoryOrderAttachDO extends BaseDO {
    /**
     * 清关单ID
     */
    @Column(name = "ref_inve_order_id")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @Column(name = "ref_inve_order_sn")
    private String  refInveOrderSn;
    /**
     * 附件名称
     */
    @Column(name = "attach_name")
    private String attachName;

    /**
     * 存储文件名
     */
    @Column(name = "store_name")
    private String storeName;
    /**
     * 存储路径
     */
    @Column(name = "attach_path")
    private String attachPath;

    /**
     * 文档类型
     */
    @Column(name = "content_type")
    private String contentType;

    /**
     * 附件类型 0-其他 1-箱单/发票/合同
     */
    private String attachType;

    /**
     * 附件来源
     */
    private Integer source;

}
