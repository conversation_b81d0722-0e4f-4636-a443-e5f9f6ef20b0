package com.danding.cds.exportorder.impl.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.TrackLogConstantMixAll;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.config.OrderBaseConfig;
import com.danding.cds.cull.api.service.CullOrderService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.enums.ExportOrderStatus;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.exportorder.impl.entity.ExportExpressDO;
import com.danding.cds.exportorder.impl.entity.ExportItemDO;
import com.danding.cds.exportorder.impl.entity.ExportOrderDO;
import com.danding.cds.exportorder.impl.mapper.ExportExpressMapper;
import com.danding.cds.exportorder.impl.mapper.ExportItemMapper;
import com.danding.cds.exportorder.impl.mapper.ExportOrderMapper;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.log.impl.TrackLogEsBuilder;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.api.SupervisionMonitorService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.assertj.core.util.Lists;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@DubboService
@RefreshScope
public class ExportOrderServiceImpl implements ExportOrderService {

    @Autowired
    private ExportOrderMapper exportOrderMapper;

    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private ExportItemMapper exportItemMapper;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @Autowired
    private ExportExpressMapper exportExpressMapper;
    @DubboReference
    private RouteService routeService;

    @DubboReference
    private CullOrderService cullOrderService;

    @DubboReference
    private HandoverOrderService handoverOrderService;

    @DubboReference
    private HandoverOrderDetailService handoverOrderDetailService;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private MessageSender messageSender;

    @DubboReference
    private SupervisionMonitorService supervisionMonitorService;

    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Autowired
    private ExportOrderService exportOrderService;

    @Resource(name = "exportOrderWritingItemTaskExecutor")
    private ThreadPoolTaskExecutor exportOrderWritingItemTaskExecutor;

    @Value("${insufficient_leger_book_id_List:[]}")
    private Long[] insufficientLedgerBookIdList;

    @Autowired
    private OrderBaseConfig orderBaseConfig;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Override
    public Set<Long> listIdsByExpress(Long expressId) {
        ExportExpressDO example = new ExportExpressDO();
        example.setExpressId(expressId);
        List<ExportExpressDO> expressDOList = exportExpressMapper.select(example);
        return expressDOList.stream().map(ExportExpressDO::getExportOrderId).collect(Collectors.toSet());
    }

    @Override
    @Transactional
    public Long create(ExportOrderSubmit submit) {
        if (CollectionUtils.isEmpty(submit.getExpressIdList())) {
            throw new ArgsErrorException("快递公司不能为空");
        }
        // todo 专用账册 创建出库单
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(submit.getEntityWarehouseCode());
        List<Long> customsBookIdList = entityWarehouseDTOList.stream()
                .filter(dto ->
                        EntityWarehouseTagEnums.containsAny(dto.getWarehouseTag(),
                                EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                .map(EntityWarehouseDTO::getCustomsBookId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(customsBookIdList)) {
            EntityWarehouseDTO entityWarehouseDTO = entityWarehouseDTOList.stream()
                    .filter(i -> Objects.equals(1, i.getEnable())).findFirst().orElse(null);
            if (entityWarehouseDTO != null) {
                customsBookIdList.add(entityWarehouseDTO.getCustomsBookId());
            }
        }

        ExportOrderDO exportOrderDO = new ExportOrderDO();
        exportOrderDO.setSn(sequenceService.generateExportOrderSn());
        exportOrderDO.setStatus(ExportOrderStatus.WRITING.getValue());
        exportOrderDO.setExpressList(JSON.toJSONString(submit.getExpressIdList()));
        exportOrderDO.setAccountBookId(submit.getAccountBookId());
        exportOrderDO.setDeclareCompanyId(submit.getDeclareCompanyId());
        exportOrderDO.setEntityWarehouseCode(submit.getEntityWarehouseCode());
        UserUtils.setCreateAndUpdateBy(exportOrderDO);
        exportOrderMapper.insertSelective(exportOrderDO);
        for (Long id : submit.getExpressIdList()) {
            ExportExpressDO exportExpressDO = new ExportExpressDO();
            exportExpressDO.setExportOrderId(exportOrderDO.getId());
            exportExpressDO.setExpressId(id);
            UserUtils.setCreateAndUpdateBy(exportOrderDO);
            exportExpressMapper.insertSelective(exportExpressDO);
        }
        return exportOrderDO.getId();
    }

    @PageSelect
    @Override
    public ListVO<ExportOrderDTOV2> paging(ExportOrderSearch search) {
        Example example = new Example(ExportOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        // 账册Id列表
//        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
//        if (!CollectionUtils.isEmpty(accountBookIdList)) {
//            criteria.andIn("accountBookId", accountBookIdList);
//        }
        criteria.andEqualTo("deleted", false);
        if (search.getStatus() != null && search.getStatus() != 0) {
            criteria.andEqualTo("status", search.getStatus());
        }

        if (!StringUtils.isEmpty(search.getQueryInfo())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            if ("sn".equals(search.getQueryType())) {
                criteria.andIn("sn", noList);
            }
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            criteria.andBetween("createTime", new Date(createFrom), new Date(createTo));
        }
        Long finishFrom = LongUtil.getFrom(search.getFinishFrom(), search.getFinishTo());
        Long finishTo = LongUtil.getEnd(search.getFinishFrom(), search.getFinishTo());
        if (!LongUtil.isNone(finishFrom) && !LongUtil.isNone(finishTo)) {
            criteria.andBetween("finishTime", new Date(finishFrom), new Date(finishTo));
        }
        if (!CollectionUtils.isEmpty(search.getIdSet())) {
            criteria.andIn("id", search.getIdSet());
        }
        if (!StringUtils.isEmpty(search.getEntityWarehouseCode())) {
            criteria.andEqualTo("entityWarehouseCode", search.getEntityWarehouseCode());
        }
        example.setOrderByClause("create_time DESC");
        List<ExportOrderDO> list = exportOrderMapper.selectByExample(example);
        ListVO<ExportOrderDTOV2> result = new ListVO<>();
        result.setDataList(list.stream().map(this::buildDTOV2).collect(Collectors.toList()));
        // 分页
        PageInfo<ExportOrderDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    @Override
    public ExportOrderDTO findById(Long id) {
        return this.buildDTO(exportOrderMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<ExportOrderDTO> findById(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        Example example = new Example(ExportOrderDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", false);
        List<ExportOrderDO> exportOrderDOS = exportOrderMapper.selectByExample(example);
        return ConvertUtil.listConvert(exportOrderDOS, ExportOrderDTO.class);
    }

    @Override
    public ExportOrderDTO findBySn(String sn) {
        if (StringUtils.isEmpty(sn)) {
            return null;
        }
        Example example = new Example(ExportOrderDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sn", sn);
        return this.buildDTO(exportOrderMapper.selectOneByExample(example));
    }

    @Override
    public List<ExportOrderDTO> findBySn(List<String> sn) {
        Example example = new Example(ExportOrderDO.class);
        example.createCriteria().andIn("sn", sn).andEqualTo("deleted", 0);
        List<ExportOrderDO> exportOrderDOS = exportOrderMapper.selectByExample(example);
        if (exportOrderDOS == null) {
            return new ArrayList<>();
        }
        List<ExportOrderDTO> exportOrderDTOS = ConvertUtil.listConvert(exportOrderDOS, ExportOrderDTO.class);
        return exportOrderDTOS;
    }

    @Override
    public List<ExportOrderDTO> listByStatus(Integer status) {
        ExportOrderDO example = new ExportOrderDO();
        example.setStatus(status);
        return exportOrderMapper.select(example).stream().map(this::buildDTO).collect(Collectors.toList());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteById(Long id) {
        ExportOrderDTO exportOrderDTO = this.findById(id);
        if (!exportOrderDTO.getStatus().equals(ExportOrderStatus.WRITING.getValue()) && !exportOrderDTO.getStatus().equals(ExportOrderStatus.DISCARD.getValue())) {
            throw new ArgsErrorException("仅已创建或已作废的申报出库单支持删除");
        }
        //查出来再删 没啥必要
//        List<ExportItemDTO> exportItemDTOList = this.listItemById(id);
//        for (ExportItemDTO exportItemDTO : exportItemDTOList) {
//            exportItemMapper.deleteByPrimaryKey(exportItemDTO.getId());
//        }
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andEqualTo("exportOrderId", exportOrderDTO.getId()).andEqualTo("deleted", false);
        //同步清单 核注状态
        this.syncInventoryReviewStatus(id);
        exportItemMapper.deleteByExample(example);
        exportOrderMapper.deleteByPrimaryKey(id);
        handoverOrderDetailService.disassociateDetailByOutboundOrder(exportOrderDTO.getSn());
    }

    @Override
    @Transactional
    public void discardById(Long id) {
        ExportOrderDTO exportOrderDTO = this.findById(id);
        if (!exportOrderDTO.getStatus().equals(ExportOrderStatus.WRITING.getValue())) {
            throw new ArgsErrorException("仅已创建的申报出库单支持作废");
        }
        ExportOrderDO exportOrderDO = new ExportOrderDO();
        exportOrderDO.setId(id);
        exportOrderDO.setStatus(ExportOrderStatus.DISCARD.getValue());
        UserUtils.setUpdateBy(exportOrderDO);
        exportOrderDO.setUpdateTime(new Date());
        exportOrderMapper.updateByPrimaryKeySelective(exportOrderDO);
        //  2022/10/10 发送消息去除交接单关联单号
        messageSender.sendMsg(exportOrderDTO.getSn(), "ccs-handover-export-discard-topic", exportOrderDTO.getSn());
        //同步清单 核注状态
        this.syncInventoryReviewStatus(id);
    }

    private void syncInventoryReviewStatus(Long exportOrderId) {
        List<ExportItemDTO> itemDTOS = this.listItemById(exportOrderId);
        if (CollectionUtil.isNotEmpty(itemDTOS)) {
            List<String> inventoryOrderSn = itemDTOS.stream().map(ExportItemDTO::getCustomsInventorySn).distinct().collect(Collectors.toList());
            customsInventoryService.updateEndorsementsStatusBySn(inventoryOrderSn, InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
        }
    }

    @Override
    @Transactional
    public ExportItemWritingReport writing(Long id, List<ExportItemRecord> importList, Boolean save) throws ArgsErrorException {
        // Step::分析已有数据
        ExportOrderDTO exportOrderDTO = this.findById(id);
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在");
        }
        List<Long> customsBookIdList = new ArrayList<>();
        entityWarehouseDTOList = entityWarehouseDTOList.stream()
                .filter(entityWarehouseDTO -> entityWarehouseDTO.getEnable() == 1
                        && EntityWarehouseTagEnums.containsAny(entityWarehouseDTO.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在可用清关账册");
        }
        customsBookIdList = entityWarehouseDTOList.stream()
                .map(EntityWarehouseDTO::getCustomsBookId)
                .distinct()
                .collect(Collectors.toList());
        List<Long> expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(), Long.class);
        // Step::补全出区订单信息
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = this.fillInfo(importList);
        // Step::数据校验及预录入
        // Step::预录入新增列表
        List<ExportItemRecord> addSuccessList = new ArrayList<>();
        List<ExportItemRecord> addFailList = new ArrayList<>();
        List<String> newAddList = new ArrayList<>();
        for (ExportItemRecord record : importList) {
            String errorMsg = "";
            if (StringUtils.isEmpty(record.getMailNo())) {
                errorMsg += "运单号为空;";
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn()) && !StringUtils.isEmpty(record.getMailNo())) {
                if (StringUtils.isEmpty(record.getExpressName()))
                    errorMsg += "运单号系统不存在;";
                else
                    errorMsg += "运单号与快递名称无法有效匹配到清单;";
            } else if (StringUtils.isEmpty(record.getExpressName())) {
                errorMsg += "快递公司为空;";
            } else {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOMap.get(record.getCustomsInventorySn());
                if (!record.getExpressName().equals(record.getTempExpressName())) {
                    errorMsg += "快递公司系统不存在;";
                }
                        /*else if(!exportOrderDTO.getDeclareCompanyId().equals(customsInventoryDTO.getDeclareCompanyId()))
                        {
                            errorMsg += "运单和申报出库单不属于同一个清关企业";
                        }*/
                else if (LongUtil.isNone(record.getExpressId())) {
                    errorMsg += "快递公司填写不正确;";
                } else if (!expressIdList.contains(record.getExpressId())) {
                    errorMsg += "当前申报出库单不支持该快递公司;";
                } else if (StringUtils.isEmpty(record.getCustomsInventorySn()) || customsInventoryDTO == null) {
                    errorMsg += "清单未找到;";
                } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                    errorMsg += "清单未放行;";
                } else if (newAddList.contains(record.getCustomsInventorySn())) {
                    errorMsg += "重复的运单号;"; // 重复的运单才会找到重复的清单
                } else if (this.findItemByCustomInventory(customsInventoryDTO.getSn()) != null) {
                    errorMsg += "该运单申报记录已存在;";
                } else if (!customsBookIdList.contains(customsInventoryDTO.getAccountBookId())) {
                    errorMsg += "对应放行清单的账册与申报出库单的账册不一致;";
                }
            }
            if (StringUtils.isEmpty(errorMsg)) {
                newAddList.add(record.getCustomsInventorySn());
                addSuccessList.add(record);
            } else {
                record.setErrorMsg(errorMsg);
                addFailList.add(record);
            }

            if (StringUtils.isEmpty(errorMsg)) {
                newAddList.add(record.getCustomsInventorySn());
                addSuccessList.add(record);
            } else {
                record.setErrorMsg(errorMsg);
                addFailList.add(record);
            }
        }
        if (save) {
            if (!CollectionUtils.isEmpty(addFailList)) {
                throw new ArgsErrorException("提交的列表还存在错误，请重新预览导入");
            }
            List<String> inventoryOrderSnList = new ArrayList<>();
            try {
                // 持久化
                Integer exportItemWritingGroupSize = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
                int i = 1;
                for (ExportItemRecord record : addSuccessList) {
                    ExportItemDO exportItemDO = new ExportItemDO();
                    exportItemDO.setExportOrderId(id);
                    exportItemDO.setCustomsInventorySn(record.getCustomsInventorySn());
                    exportItemDO.setExpressId(record.getExpressId());
                    exportItemDO.setMailNo(record.getMailNo());
                    exportItemDO.setTrayNo(((i / exportItemWritingGroupSize) + 1) + ""); // 默认托盘，后续从record导入
                    exportItemDO.setOperatorNo("暂无"); // 默认操作员工
                    exportItemDO.setStationNo("暂无"); // 默认工作台
                    exportItemDO.setBizId(record.getBizId());
                    exportItemDO.setSkuJson(JSON.toJSONString(record.getSkuInfoList()));
                    exportItemDO.setGrossWeight(record.getGrossWeight());
                    exportItemDO.setNetWeight(record.getNetWeight());
                    UserUtils.setCreateAndUpdateBy(exportItemDO);
                    exportItemMapper.insertSelective(exportItemDO);
                    i++;
                    inventoryOrderSnList.add(record.getCustomsInventorySn());
                }
                if (CollectionUtil.isNotEmpty(inventoryOrderSnList)) {
                    inventoryOrderSnList = inventoryOrderSnList.stream().distinct().collect(Collectors.toList());
                    customsInventoryService.updateEndorsementsStatusBySn(inventoryOrderSnList, InventoryReviewStatus.LINKED_EXPORT_ORDER.getValue());
                }

            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                throw new ArgsErrorException("导入出现异常");
            }
        }
        ExportItemWritingReport report = new ExportItemWritingReport();
        report.setTotalCount(importList.size());
        report.setSuccessCount(addSuccessList.size());
        report.setFailCount(addFailList.size());
        report.setSuccessRecordList(addSuccessList);
        report.setFailRecordList(addFailList);
        return report;
    }

    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRES_NEW)
    public void writingByItem(Long id, ExportItemWritingReport report) throws ArgsErrorException {
        log.info("[op:ExportOrderServiceImpl-writingByItem] id={}, report={}", id, JSON.toJSONString(report));
        if (!CollectionUtils.isEmpty(report.getFailRecordList())) {
            throw new ArgsErrorException("提交的列表还存在错误，请重新预览导入");
        }
        try {
            // 使用 Set 去重，避免后续 Stream 操作
            Set<String> inventoryOrderSnSet = new HashSet<>();
            // 收集所有待插入的对象
            List<ExportItemDO> exportItemDOList = new ArrayList<>();
            // 持久化
            Integer exportItemWritingGroupSize = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
            int i = 1;
            // 循环处理记录
            for (ExportItemRecord record : report.getSuccessRecordList()) {
                ExportItemDO exportItemDO = new ExportItemDO();
                exportItemDO.setExportOrderId(id);
                exportItemDO.setCustomsInventorySn(record.getCustomsInventorySn());
                exportItemDO.setExpressId(record.getExpressId());
                exportItemDO.setMailNo(record.getMailNo());
                exportItemDO.setTrayNo(((i / exportItemWritingGroupSize) + 1) + "");
                exportItemDO.setOperatorNo("暂无");
                exportItemDO.setStationNo("暂无");
                exportItemDO.setBizId(record.getBizId());
                exportItemDO.setSkuJson(JSON.toJSONString(record.getSkuInfoList()));
                exportItemDO.setGrossWeight(record.getGrossWeight());
                exportItemDO.setNetWeight(record.getNetWeight());
                exportItemDO.setAccountBookId(record.getCustomsInventoryDTO().getAccountBookId());
                UserUtils.setCreateAndUpdateBy(exportItemDO);

                exportItemDOList.add(exportItemDO);
                inventoryOrderSnSet.add(record.getCustomsInventorySn());
                i++;
            }
            // 批量插入数据库
            if (!exportItemDOList.isEmpty()) {
                exportItemMapper.insertList(exportItemDOList);
            }
            // 更新库存状态
            if (!inventoryOrderSnSet.isEmpty()) {
                customsInventoryService.updateEndorsementsStatusBySn(
                        new ArrayList<>(inventoryOrderSnSet),
                        InventoryReviewStatus.LINKED_EXPORT_ORDER.getValue()
                );
            }

            // 获取订单信息并更新交接单
            ExportOrderDTO exportOrderDTO = this.findById(id);
            List<String> customsInventorySnList = report.getSuccessRecordList()
                    .stream()
                    .map(ExportItemRecord::getCustomsInventorySn)
                    .distinct()
                    .collect(Collectors.toList());
            handoverOrderDetailService.addExportOrderInfo(customsInventorySnList, exportOrderDTO.getSn());
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            throw new ArgsErrorException("导入出现异常");
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    public void writingBlockByItem(Long id, ExportItemWritingReport report) {
        if (Objects.isNull(id) || Objects.isNull(report) || CollectionUtils.isEmpty(report.getSuccessRecordList())) {
            return;
        }
        List<ExportItemRecord> successRecordList = report.getSuccessRecordList();
        Integer groupSize = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
        log.info("ExportOrderService writingBlockByItem group={}", groupSize);
        Map<Integer, List<ExportItemRecord>> recordGroup = successRecordList.stream()
                .collect(Collectors.groupingBy(i -> successRecordList.indexOf(i) / groupSize));
        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (Map.Entry<Integer, List<ExportItemRecord>> entry : recordGroup.entrySet()) {
            List<ExportItemRecord> subSuccessRecordList = entry.getValue();
            List<String> mailNoList = subSuccessRecordList.stream().map(ExportItemRecord::getMailNo).collect(Collectors.toList());
            log.info("出库单表体分块写入 group{}, 运单号={}", entry.getKey(), mailNoList);
            ExportItemWritingReport partReport = new ExportItemWritingReport();
            partReport.setSuccessCount(subSuccessRecordList.size());
            partReport.setSuccessRecordList(subSuccessRecordList);
            Long tenantId = SimpleTenantHelper.getTenantId();
            Runnable runnable = () -> {
                SimpleTenantHelper.setTenantId(tenantId);
                exportOrderService.writingByItem(id, partReport);
            };
            futureList.add(CompletableFuture.runAsync(runnable, exportOrderWritingItemTaskExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("出库单表体分块写入CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("出库单表体分块写入CompletableFuture结果异常" + e.getMessage(), e);
        }
        setBookList(id);
        log.info("出库单表体分块写入完成 出库单id={}, recordList={}", id, JSON.toJSON(report));
    }

    private void setBookList(Long id) {
        ExportOrderDTO exportOrderDTO = this.findById(id);
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在");
        }
        entityWarehouseDTOList = entityWarehouseDTOList.stream()
                .filter(entityWarehouseDTO -> entityWarehouseDTO.getEnable() == 1
                        && EntityWarehouseTagEnums.containsAny(entityWarehouseDTO.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在可用清关账册");
        }
        List<Long> customsBookIdList = entityWarehouseDTOList.stream()
                .map(EntityWarehouseDTO::getCustomsBookId)
                .distinct()
                .collect(Collectors.toList());
        ExportOrderDO updateDO = new ExportOrderDO();
        updateDO.setId(id);
        updateDO.setBookIdListJson(JSON.toJSONString(customsBookIdList));
        exportOrderMapper.updateByPrimaryKeySelective(updateDO);
    }

    @Override
    @Transactional
    public void bindWaybill(Long id, ExportItemWritingReport report) throws ArgsErrorException {
        log.info("[op:ExportOrderServiceImpl-writingByItem] id={}, report={}", id, JSON.toJSONString(report));
        try {
            List<String> inventorySnList = new ArrayList<>();
            // 持久化
            int i = 1;
            Integer exportItemWritingGroupSize = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
            for (ExportItemRecord record : report.getSuccessRecordList()) {
                ExportItemDO exportItemDO = new ExportItemDO();
                exportItemDO.setExportOrderId(id);
                exportItemDO.setCustomsInventorySn(record.getCustomsInventorySn());
                exportItemDO.setExpressId(record.getExpressId());
                exportItemDO.setMailNo(record.getMailNo());
                exportItemDO.setTrayNo(((i / exportItemWritingGroupSize) + 1) + ""); // 默认托盘，后续从record导入
                exportItemDO.setOperatorNo("暂无"); // 默认操作员工
                exportItemDO.setStationNo("暂无"); // 默认工作台
                exportItemDO.setBizId(record.getBizId());
                if (Objects.nonNull(record.getCustomsInventoryDTO())) {
                    exportItemDO.setAccountBookId(record.getCustomsInventoryDTO().getAccountBookId());
                }
                exportItemDO.setSkuJson(JSON.toJSONString(record.getSkuInfoList()));
                exportItemDO.setGrossWeight(record.getGrossWeight());
                exportItemDO.setNetWeight(record.getNetWeight());
                UserUtils.setCreateAndUpdateBy(exportItemDO);
                exportItemMapper.insertSelective(exportItemDO);
                inventorySnList.add(record.getCustomsInventorySn());
                i++;
            }
            // 清单核注状态 已汇总
            customsInventoryService.updateEndorsementsStatusBySn(CollectionUtil.distinct(inventorySnList),
                    InventoryReviewStatus.LINKED_EXPORT_ORDER.getValue());
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            throw new ArgsErrorException("导入出现异常");
        }
    }


    @Override
    @Transactional
    public ExportItemEditReport editByEndorsement(Long endorsementId, List<ExportItemRecord> addList, List<ExportItemRecord> delList, Boolean save) throws ArgsErrorException {
        List<ExportItemDTO> itemDTOList = this.listItemByEndorsementId(endorsementId);
        EndorsementDTO endorsementDTO = endorsementService.findById(endorsementId);
        if (!endorsementDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !endorsementDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
            throw new ArgsErrorException("仅未申报或异常的核注清单允许编辑运单");
        }
        List<Long> bookNoList = Arrays.asList(insufficientLedgerBookIdList);
        if (CollectionUtil.isNotEmpty(bookNoList) && bookNoList.contains(endorsementDTO.getAccountBookId())
                && StringUtils.isNotEmpty(endorsementDTO.getInformationDesc())
                && (endorsementDTO.getInformationDesc().contains("[8987]") && endorsementDTO.getInformationDesc().contains("超过底账允许数量"))
                && Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(endorsementDTO.getAccountBookId());
            throw new ArgsErrorException("底账不足核注无法作废和编辑, 账册编号【" + customsBookDTO.getBookNo() + "】");
        }
        ExportOrderDTO exportOrderDTO = this.findById(endorsementDTO.getExportOrderId());
        List<Long> expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(), Long.class);
        List<Long> bookIdList = JSON.parseArray(exportOrderDTO.getBookIdListJson(), Long.class);
        // PS::两个限制 1.SKU不能超200 2.运单至少存在一条
        Set<Long> skuIdSet = new HashSet<>();
        for (ExportItemDTO itemDTO : itemDTOList) {
            List<ExportSkuInfo> skuInfoList = JSON.parseArray(itemDTO.getSkuJson(), ExportSkuInfo.class);
            for (ExportSkuInfo dto : skuInfoList) {
                skuIdSet.add(dto.getBookItemId());
            }
        }
        ExportItemEditReport report = new ExportItemEditReport();
        // Step::提交总数
        report.setTotalCount(addList.size() + delList.size());
        // Step::补全出区订单信息
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = this.fillInfo(addList);
        List<Long> orderIdList = customsInventoryDTOMap.values().stream().map(CustomsInventoryDTO::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        List<String> itemTempList = itemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
        // Step::新增列表
        List<ExportItemRecord> addSuccessList = new ArrayList<>();
        List<ExportItemRecord> addFailList = new ArrayList<>();

        List<String> mailNoList = addList.stream().map(ExportItemRecord::getMailNo).collect(Collectors.toList());
        List<CustomsInventoryCalloffDTO> calloffDTOList = customsInventoryCalloffService.findListByOrderIdList(orderIdList);
        Map<Long, CustomsInventoryCalloffDTO> orderIdCalloffMap = calloffDTOList.stream().collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderId, Function.identity(), (v1, v2) -> v1));

        List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = handoverOrderDetailService.getHandoverOrderDetailByMailNo(mailNoList);
        Map<String, HandoverOrderDetailDTO> mailNoMap = handoverOrderDetailDTOS.stream().collect(Collectors.toMap(HandoverOrderDetailDTO::getWayBillSn, Function.identity(), (v1, v2) -> v1));
        Boolean skipCheck = handoverOrderService.skipCheckHandoverByBookId(endorsementDTO.getAccountBookId());
        for (ExportItemRecord record : addList) {
            // Step::检查
            String errorMsg = "";
            CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOMap.get(record.getCustomsInventorySn());
            if (StringUtils.isEmpty(record.getCustomsInventorySn()) || customsInventoryDTO == null) {
                errorMsg += "运单号不存在;";
            } else if (LongUtil.isNone(record.getExpressId())) {
                errorMsg += "快递公司为空;";
            } else if (!expressIdList.contains(record.getExpressId())) {
                errorMsg += "当前核放单不支持该快递公司;";
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn()) || customsInventoryDTO == null) {
                errorMsg += "清单未找到;";
            } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                errorMsg += "清单未放行;";
            } else if (this.findItemByCustomInventory(customsInventoryDTO.getSn()) != null) {
                errorMsg += "该运单已关联出库单;";
            } else if (!bookIdList.contains(customsInventoryDTO.getAccountBookId())) {
                errorMsg += "对应放行清单的账册与申请出库单的账册不一致;";
            } else if (addSuccessList.contains(record)) {
                errorMsg += "该运单申报记录已存在;";
            }
            Long orderId = customsInventoryDTO.getOrderId();
            if (orderIdCalloffMap.containsKey(orderId)) {
                CustomsInventoryCalloffDTO customsInventoryCalloffDTO = orderIdCalloffMap.get(orderId);
                if (Objects.nonNull(customsInventoryCalloffDTO)) {
                    if (Objects.equals(customsInventoryCalloffDTO.getCalloffType(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode())
                            || Objects.equals(customsInventoryCalloffDTO.getCalloffType(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode())) {
                        String calloffStatus = customsInventoryCalloffDTO.getCalloffStatus();
                        if (Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode()) ||
                                Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_ING.getCode()) ||
                                Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode()) ||
                                Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode())) {
                            errorMsg += "存在撤单，无法汇单;";
                        }
                    }
                }
            }
            // 京东账册不需要校验(不走WMS的不用校验)
            if (!skipCheck) {
                if (mailNoMap.containsKey(record.getMailNo())) {
                    HandoverOrderDetailDTO handoverOrderDetailDTO = mailNoMap.get(record.getMailNo());
                    if (Objects.nonNull(handoverOrderDetailDTO.getOutboundOrder())) {
                        errorMsg += "该运单已关联出库单;";
                    }
                } else {
                    errorMsg += "该运单未关联交接单;";
                }
            }

            Set<Long> tempSku = new HashSet<>(skuIdSet);
            List<ExportSkuInfo> skuInfoList = record.getSkuInfoList();
            if (skuInfoList == null) skuInfoList = new ArrayList<>();
            tempSku.addAll(skuInfoList.stream().map(ExportSkuInfo::getBookItemId).collect(Collectors.toList()));
            Integer groupSizeSku = orderBaseConfig.getExportItemWritingGroupSizeSku();
            if (tempSku.size() > groupSizeSku) {
                errorMsg += "超出清单SKU种类上限" + groupSizeSku + ";";
            }
            if (StringUtils.isEmpty(errorMsg)) {
                skuIdSet = tempSku;
                itemTempList.add(record.getCustomsInventorySn());
                addSuccessList.add(record);
            } else {
                record.setErrorMsg(errorMsg);
                addFailList.add(record);
            }
        }
        report.setAddSuccessList(addSuccessList);
        report.setAddFailList(addFailList);

        // Step::删除列表
        List<String> delMailNo = new ArrayList<>();
        List<String> delInventorySnList = new ArrayList<>();
        List<ExportItemRecord> deleteSuccessList = new ArrayList<>();
        List<ExportItemRecord> deleteFailList = new ArrayList<>();
        for (ExportItemRecord record : delList) {
            if (itemTempList.size() == 1) {
                record.setErrorMsg("当前运单只剩一条，不允许删除");
                deleteFailList.add(record);
            } else {
                for (ExportItemDTO exportItemDTO : itemDTOList) {
                    if (exportItemDTO.getMailNo().equals(record.getMailNo())) {
                        // 匹配已存在的清单
                        record.setCustomsInventorySn(exportItemDTO.getCustomsInventorySn());
                        itemTempList.remove(exportItemDTO.getCustomsInventorySn());
                        delMailNo.add(record.getMailNo());
                        delInventorySnList.add(exportItemDTO.getCustomsInventorySn());
                        break;
                    }
                }
                deleteSuccessList.add(record);
            }
        }
        report.setDeleteSuccessList(deleteSuccessList);
        report.setDeleteFailList(deleteFailList);

        if (save) {
            // Step::持久化
            if (!CollectionUtils.isEmpty(addFailList) || !CollectionUtils.isEmpty(deleteFailList)) {
                throw new ArgsErrorException("预览结果已失效，请重新编辑提交");
            }
            try {
                List<String> addMailNo = new ArrayList<>();
                List<String> addInventorySnList = new ArrayList<>();
                // 持久化
                for (ExportItemRecord record : addSuccessList) {
                    ExportItemDO exportItemDO = new ExportItemDO();
                    exportItemDO.setExportOrderId(exportOrderDTO.getId());
                    exportItemDO.setEndorsementOrderId(endorsementId);
                    exportItemDO.setCustomsInventorySn(record.getCustomsInventorySn());
                    exportItemDO.setExpressId(record.getExpressId());
                    exportItemDO.setMailNo(record.getMailNo());
                    exportItemDO.setTrayNo("EDIT"); // 默认托盘，后续从record导入
                    exportItemDO.setOperatorNo("暂无"); // 默认操作员工
                    exportItemDO.setStationNo("暂无"); // 默认工作台
                    exportItemDO.setBizId(record.getBizId());
                    exportItemDO.setSkuJson(JSON.toJSONString(record.getSkuInfoList()));
                    exportItemDO.setGrossWeight(record.getGrossWeight());
                    exportItemDO.setNetWeight(record.getNetWeight());
                    UserUtils.setCreateAndUpdateBy(exportItemDO);
                    exportItemMapper.insertSelective(exportItemDO);
                    addMailNo.add(record.getMailNo());
                    addInventorySnList.add(record.getCustomsInventorySn());
                }
                //如果是新增的运单号修改清单核注状态
                if (!CollectionUtils.isEmpty(addMailNo)) {
                    customsInventoryService.updateEndorsementsStatusBySn(addInventorySnList, InventoryReviewStatus.LINKED_REVIEW.getValue());
                    handoverOrderDetailService.addExportOrderInfo(addInventorySnList, exportOrderDTO.getSn());
                }
                //如果是删除的运单号新增剔除单并修改清单核注状态
                if (!CollectionUtils.isEmpty(delMailNo)) {
                    cullOrderService.create(delMailNo);
                    customsInventoryService.updateEndorsementsStatusBySn(delInventorySnList, InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
                    try {
                        supervisionMonitorService.changeCullRemarkByInventorySn(delInventorySnList);
                    } catch (Exception e) {
                        log.error("回传时效监管系统剔除节点异常 error={}", e.getMessage(), e);
                    }
                }
                if (!CollectionUtils.isEmpty(deleteSuccessList)) {
                    List<String> snList = deleteSuccessList.stream().map(ExportItemRecord::getCustomsInventorySn).distinct().collect(Collectors.toList());
                    //出库单删除
                    log.info("editByEndorsement 出库单删除 snList={}", JSON.toJSONString(snList));
                    this.deleteByCustomsInventorySn(snList);
                    //清除交接单关联
                    log.info("editByEndorsement 清除交接单关联 snList={}", JSON.toJSONString(snList));
                    handoverOrderDetailService.cleanExportOrderInfo(snList);
                }
                if (!CollectionUtils.isEmpty(addSuccessList) || !CollectionUtils.isEmpty(deleteSuccessList)) {
                    List<String> inventorySnList = itemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).distinct().collect(Collectors.toList());
                    if (!CollectionUtils.isEmpty(addSuccessList)) {
                        List<String> addSuccessInventorySnList = addSuccessList.stream().map(ExportItemRecord::getCustomsInventorySn).distinct().collect(Collectors.toList());
                        inventorySnList.addAll(addSuccessInventorySnList);
                    }
                    if (!CollectionUtils.isEmpty(deleteSuccessList)) {
                        List<String> deleteSuccessInventorySnList = deleteSuccessList.stream().map(ExportItemRecord::getCustomsInventorySn).distinct().collect(Collectors.toList());
                        inventorySnList.removeAll(deleteSuccessInventorySnList);
                    }
                    List<CustomsInventoryDTO> inventoryDTOS = customsInventoryService.findBySnList(inventorySnList);
                    endorsementService.buildEndorsementFbNote(inventoryDTOS, endorsementId, endorsementDTO.getDeclareCompanyId());
                }
            } catch (Exception e) {
                log.warn("处理异常：{}", e.getMessage(), e);
                throw new ArgsErrorException("导入出现异常");
            }
            List<String> snList = addSuccessList.stream().map(ExportItemRecord::getCustomsInventorySn).collect(Collectors.toList());
            // 目前只记录新增的单号
            String buildType = TrackLogConstantMixAll.ENDORSEMENT_CREATE;
            trackLogEsBuilder.buildEndorsementTrackLogAsyncSnList(snList, endorsementDTO.getSn(), buildType);
        }
        return report;
    }


    @Autowired
    private TrackLogEsBuilder trackLogEsBuilder;

    private Map<String, CustomsInventoryDTO> fillInfo(List<ExportItemRecord> origList) {
        Map<Long, ExpressDTO> expressDTOMap = new HashMap<>();
        Map<String, RouteDTO> declareDTOMap = new HashMap<>();
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = new HashMap<>();
        for (ExportItemRecord record : origList) {
            if (StringUtils.isEmpty(record.getMailNo())) {
                continue;
            }
            ExpressDTO expressDTO = null;
            if (!StringUtils.isEmpty(record.getExpressName())) {
                expressDTO = expressDTOMap.get(record.getExpressName());
                if (expressDTO == null) {
                    expressDTO = expressService.findOneByName(record.getExpressName());
                }
            }
            CustomsInventoryDTO customsInventoryDTO = null;
            if (expressDTO != null && StringUtils.isEmpty(record.getCustomsInventorySn())) {
                // TODO:这里有优化空间，可以改成批量查询
                customsInventoryDTO = customsInventoryService.findByLogistics90Days(expressDTO.getId(), record.getMailNo());
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn())) {
                List<CustomsInventoryDTO> list = customsInventoryService.listByLogistics90Days(record.getMailNo());
                if (!CollectionUtils.isEmpty(list)) {
                    customsInventoryDTO = customsInventoryService.listByLogistics90Days(record.getMailNo()).get(0);
                    expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
                }
            } else {
                customsInventoryDTO = customsInventoryService.findBySnSection(record.getCustomsInventorySn());
            }
            if (expressDTO != null) {
                expressDTOMap.put(expressDTO.getId(), expressDTO);
                record.setTempExpressName(expressDTO.getName());
                record.setExpressId(expressDTO.getId());
            }
            if (customsInventoryDTO != null) {
                try {
                    if (!StringUtils.isEmpty(customsInventoryDTO.getExtraJson())) {
                        CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
                        if (!StringUtils.isEmpty(extra.getRouteCode())) {
                            RouteDTO routeDTO = declareDTOMap.get(extra.getRouteCode());
                            if (routeDTO == null) {
                                routeDTO = routeService.findByCode(extra.getRouteCode());
                                if (routeDTO != null) {
                                    declareDTOMap.put(extra.getRouteCode(), routeDTO);
                                }
                            }
                            if (routeDTO != null) {
                                customsInventoryDTO.setDeclareCompanyId(routeDTO.getListDeclareCompanyId());
                            }
                        }
                    }
                } catch (Exception ex) {

                }
                //declareDTOMap.put()
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                List<ExportSkuInfo> skuInfoList = new ArrayList<>();
//                BigDecimal grossWeight = new BigDecimal(0),netWeight =   new BigDecimal(0);
                for (CustomsInventoryItemDTO customsInventoryItemDTO : customsInventoryItemDTOS) {
                    ExportSkuInfo skuInfo = new ExportSkuInfo();
                    skuInfo.setBookItemId(customsInventoryItemDTO.getBookItemId());
                    skuInfo.setCount(customsInventoryItemDTO.getCount());
                    String extraJson = customsInventoryItemDTO.getExtraJson();
                    if (StringUtils.isNotEmpty(extraJson)) {
                        CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
                        String productId = customsInventoryItemExtra.getProductId();
                        String unifiedProductId = customsInventoryItemExtra.getUnifiedProductId();
                        skuInfo.setProductId(productId);
                        skuInfo.setUnifiedProductId(unifiedProductId);
                    }
                    skuInfo.setItemTag(customsInventoryItemDTO.getItemTag());
                    skuInfo.setGoodsName(customsInventoryItemDTO.getItemName());
                    skuInfoList.add(skuInfo);
//                    CustomsInventoryItemExtra extra = JSON.parseObject(customsInventoryItemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
//                    BigDecimal _grossWeight =  (extra.getGrossWeight() == null ? BigDecimal.ZERO : extra.getGrossWeight())
//                            .multiply(new BigDecimal(customsInventoryItemDTO.getCount()));
//                    if(_grossWeight==null)_grossWeight = BigDecimal.ZERO;
//                    grossWeight = grossWeight.add(_grossWeight);
//                    BigDecimal _netWeight =   (extra.getNetWeight() == null ? BigDecimal.ZERO : extra.getNetWeight()).multiply(new BigDecimal(customsInventoryItemDTO.getCount()));
//                    if(_netWeight==null)_netWeight = BigDecimal.ZERO;
//                    netWeight = netWeight.add(_netWeight);
                }
                List<String> unifiedProductIds = skuInfoList.stream().map(ExportSkuInfo::getUnifiedProductId).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(unifiedProductIds)) {
                    List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordService.findByProId(unifiedProductIds);
                    Map<String, GoodsRecordDTO> goodsMap = goodsRecordDTOS.stream().collect(Collectors.toMap(GoodsRecordDTO::getProductId, Function.identity(), (k1, k2) -> k1));
                    for (ExportSkuInfo skuInfo : skuInfoList) {
                        GoodsRecordDTO goodsRecordDTO = goodsMap.get(skuInfo.getUnifiedProductId());
                        if (goodsRecordDTO != null) {
                            if (goodsRecordDTO.getGrossWeight() != null && skuInfo.getCount() != null) {
                                skuInfo.setGrossWeight(goodsRecordDTO.getGrossWeight().multiply(new BigDecimal(skuInfo.getCount().toString())));
                            }
                            if (goodsRecordDTO.getNetWeight() != null && skuInfo.getCount() != null) {
                                skuInfo.setNetWeight(goodsRecordDTO.getNetWeight().multiply(new BigDecimal(skuInfo.getCount().toString())));
                            }
                        }
                    }
                }
                record.setSkuInfoList(skuInfoList);

                record.setGrossWeight(customsInventoryDTO.getGrossWeight() == null ? BigDecimal.ZERO : customsInventoryDTO.getGrossWeight());
                record.setNetWeight(customsInventoryDTO.getNetWeight());
//                record.setGrossWeight(grossWeight.setScale(4, BigDecimal.ROUND_HALF_UP));
//                record.setNetWeight(netWeight.setScale(4, BigDecimal.ROUND_HALF_UP));
                record.setCustomsInventorySn(customsInventoryDTO.getSn());
                record.setBizId(customsInventoryDTO.getDeclareOrderNo());
                customsInventoryDTOMap.put(customsInventoryDTO.getSn(), customsInventoryDTO);
            }
        }
        return customsInventoryDTOMap;
    }

    public static void main(String[] args) {
        BigDecimal A = BigDecimal.ONE;
        A.add(BigDecimal.ONE);
        System.out.println(A);
    }

    @Override
    public List<ExportItemDTO> listItemByMailNos(Set<String> mainNoSet) {
        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("mailNo", mainNoSet);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<ExportItemDO> itemDOList = exportItemMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    /**
     * 根据运单号查询单个
     *
     * @param mainNo
     * @return
     */
    @Override
    public ExportOrderDTO itemByMailNos(String mainNo) {

        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("mailNo", mainNo);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        ExportItemDO itemDOList = exportItemMapper.selectOneByExample(example);
        log.warn("[ExportItemDO] 运单号查询结果 = {}", itemDOList);
        ExportOrderDTO exportOrderDTO = null;
        if (itemDOList != null) {
            exportOrderDTO = findById(itemDOList.getExportOrderId());
            log.warn("[ExportOrderDTO ] 出库单号查询结果 = {}", exportOrderDTO);
        }
        return exportOrderDTO;
    }


    /**
     * 根据运单号查询单个（字表查询）
     *
     * @param mainNo
     * @return
     */
    @Override
    public ExportItemDTO findByItemMailNo(String mainNo) {
        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("mailNo", mainNo);
        criteria.andEqualTo("deleted", 0);
        ExportItemDO exportItemDO = exportItemMapper.selectOneByExample(example);
        log.warn("[ExportItemDO] 运单号查询结果 = {}", exportItemDO);
        if (exportItemDO != null) {
            ExportItemDTO exportItemDTO = ConvertUtil.beanConvert(exportItemDO, ExportItemDTO.class);
            log.warn("[exportItemDTO ] 出库单号查询结果 = {}", exportItemDTO);
            return exportItemDTO;
        }
        return null;
    }

    /**
     * 根据运单号获取对应出库单
     *
     * @param mailNoList
     * @return 运单号-出库单号
     */
    @Override
    public Map<String, String> listExportOrderByMailNo(List<String> mailNoList) {
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andIn("mailNo", mailNoList).andEqualTo("deleted", false);
        example.selectProperties("mailNo", "exportOrderId");
        List<ExportItemDO> exportItemDOS = exportItemMapper.selectByExample(example);
        List<Long> exportOrderIdList = exportItemDOS.stream().map(ExportItemDO::getExportOrderId).distinct().collect(Collectors.toList());
        List<ExportOrderDTO> exportOrderDTOS = this.findById(exportOrderIdList);
        Map<Long, ExportOrderDTO> idDtoMap = exportOrderDTOS.stream().collect(Collectors.toMap(ExportOrderDTO::getId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> result = new HashMap<>();
        exportItemDOS.forEach(e -> {
            String mailNo = e.getMailNo();
            Long exportOrderId = e.getExportOrderId();
            if (idDtoMap.containsKey(exportOrderId)) {
                ExportOrderDTO exportOrderDTO = idDtoMap.get(exportOrderId);
                if (Objects.nonNull(exportOrderDTO)) {
                    result.put(mailNo, exportOrderDTO.getSn());
                }
            }
        });
        return result;
    }

    @Override
    public List<ExportItemDTO> listItemById(Long id) {
        ExportItemDO template = new ExportItemDO();
        template.setExportOrderId(id);
        List<ExportItemDO> exportItemDOS = exportItemMapper.select(template);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<ExportItemDTO> listItemByIdAndBookId(Long id, Long accountBookId) {
        ExportItemDO template = new ExportItemDO();
        template.setExportOrderId(id);
        template.setAccountBookId(accountBookId);
        List<ExportItemDO> exportItemDOS = exportItemMapper.select(template);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<ExportItemDTO> listItemByEndorsementId(Long endorsementId) {
        ExportItemDO template = new ExportItemDO();
        template.setEndorsementOrderId(endorsementId);
        List<ExportItemDO> exportItemDOS = exportItemMapper.select(template);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public List<ExportItemDTO> listItemByEndorsementIds(Set<Long> endorsementIds) {
        if (CollectionUtils.isEmpty(endorsementIds)) {
            return new ArrayList<>();
        }
        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("endorsementOrderId", endorsementIds);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<ExportItemDO> itemDOList = exportItemMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }

    @Override
    public ExportItemDTO findItemByCustomInventory(String customsInventorySn) {
        ExportItemDO template = new ExportItemDO();
        template.setCustomsInventorySn(customsInventorySn);
        ExportItemDO exportItemDO = exportItemMapper.selectOne(template);
        return this.buildItemDTO(exportItemDO);
    }

    @Override
    public Map<String, ExportItemDTO> getInvetSnItemMapByCustomInventoryList(List<String> customsInventorySnList) {
        if (CollectionUtils.isEmpty(customsInventorySnList)) {
            return new HashMap<>();
        }
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andIn("customsInventorySn", customsInventorySnList).andEqualTo("deleted", false);
        List<ExportItemDO> exportItemDOS = exportItemMapper.selectByExample(example);
        return exportItemDOS.stream().map(this::buildItemDTO).collect(Collectors.toMap(ExportItemDTO::getCustomsInventorySn, Function.identity(), (v1, v2) -> v1));
    }

    @Override
    public List<ExportItemDTO> listItemByCustomInventorySAndEndorsementId(List<String> customsInventorySns, Long endorsementOrderId) {
        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("customsInventorySn", customsInventorySns);
        criteria.andEqualTo("endorsementOrderId", endorsementOrderId);
        criteria.andEqualTo("deleted", false);
        example.and(criteria);
        List<ExportItemDO> itemDOList = exportItemMapper.selectByExample(example);
        return itemDOList.stream().map(this::buildItemDTO).collect(Collectors.toList());
    }
    /*
        修复：sku不能超过200个BUG ,这个暂时我写了个版本，
        后陆续再出现BUG ,再用异常数据测试.<AUTHOR>
     */
//    public Map<Integer, List<ExportItemDTO>> distributionForEndorsement(Long id) {

    /// /        Map<Integer, List<ExportItemDTO>> endorsementGroup = new HashMap<>();
    /// /        Map<Integer, Set<Long>> skuGroup = new HashMap<>();
//        List<ExportItemDTO> itemDTOList = this.listItemById(id);
//        if (CollectionUtils.isEmpty(itemDTOList)){
//            throw new ArgsErrorException("该出库单还未添加运单，请添加后再试");
//        }
//        java.util.Map<Integer,LinkedList<Long>> groupSukInfo = new java.util.HashMap<Integer,LinkedList<Long>>();
//        java.util.Map<Integer,List<ExportItemDTO>> groupInfo = new java.util.HashMap<Integer,List<ExportItemDTO>>();
//        int groupIndex=1;
//        for (ExportItemDTO exportItemDTO : itemDTOList) {
//            List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(),ExportSkuInfo.class);
//            LinkedList linkedList  =  groupSukInfo.get(groupIndex);
//            List<ExportItemDTO> listItem = groupInfo.get(groupIndex);
//            if(linkedList==null)
//            {
//                linkedList = new java.util.LinkedList<Long>();
//                groupSukInfo.put(groupIndex,linkedList);
//                listItem = new java.util.LinkedList<ExportItemDTO>();
//                groupInfo.put(groupIndex,listItem);
//            }
//            Set<Long> itemBookIds = skuInfoList.stream().map(s->s.getBookItemId()).collect(Collectors.toSet());
//            /**
//             * 获取交集
//             */
//            Collection<Long>  intersection = org.apache.commons.collections4.CollectionUtils.intersection(linkedList,itemBookIds);
//            /**
//             * 获取子集合
//             */
//            Collection<Long>  subtract =   org.apache.commons.collections4.CollectionUtils.subtract(itemBookIds,intersection);
//            if(subtract.size()>200)
//                throw   new ArgsErrorException("数据异常");
//            if(subtract.size()+linkedList.size()>200)
//            {
//                groupIndex++;
//                linkedList = new java.util.LinkedList<Long>();
//                listItem = new java.util.LinkedList<ExportItemDTO>();
//                groupSukInfo.put(groupIndex,linkedList);
//                groupInfo.put(groupIndex,listItem);
//            }
//            linkedList.addAll(subtract);
//            listItem.add(exportItemDTO);
//        }
//        return groupInfo;
//    }
    @Override
    public Map<String, List<ExportItemDTO>> distributionForEndorsement(Long id) throws ArgsErrorException {
        Map<String, List<ExportItemDTO>> result = new HashMap<>();
        List<ExportItemDTO> itemDTOList = this.listItemById(id);
        if (CollectionUtils.isEmpty(itemDTOList)) {
            throw new ArgsInvalidException("该出库单还未添加运单，请添加后再试");
        }
        itemDTOList.stream().collect(Collectors.groupingBy(ExportItemDTO::getAccountBookId))
                .forEach((accountBookId, exportItemDTOS) -> {
                    Map<Integer, List<ExportItemDTO>> endorsementGroup = new HashMap<>();
                    Map<Integer, Set<Long>> skuGroup = new HashMap<>();
                    for (ExportItemDTO exportItemDTO : exportItemDTOS) {
                        List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
                        Integer index = this.dispatchGroup(skuGroup, skuInfoList, endorsementGroup);
                        List<ExportItemDTO> exportItemDTOList = endorsementGroup.get(index);
                        if (exportItemDTOList == null) {
                            exportItemDTOList = new ArrayList<>();
                        }
                        exportItemDTOList.add(exportItemDTO);
                        endorsementGroup.put(index, exportItemDTOList);
                        Set<Long> proIdSet = skuGroup.get(index);
                        if (proIdSet == null) {
                            proIdSet = new HashSet<>();
                        }
                        for (ExportSkuInfo skuInfo : skuInfoList) {
                            proIdSet.add(skuInfo.getBookItemId());
                        }
                        skuGroup.put(index, proIdSet);
                    }
                    endorsementGroup.forEach((index, list) -> {
                        result.put(accountBookId + "_" + index, list);
                    });
                });
        return result;
    }

    @Override
    public void deleteByCustomsInventorySn(List<String> customsInventoryList) {
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andIn("customsInventorySn", customsInventoryList).andEqualTo("deleted", false);
        exportItemMapper.deleteByExample(example);
    }

    @Override
    public Map<String, EndorsementDTO> getEndorsementByCustomsInventorySnList(List<String> customsInventorySnList) {
        Map<String, EndorsementDTO> resultMap = new HashMap<>();
        if (CollectionUtils.isEmpty(customsInventorySnList)) {
            return resultMap;
        }
        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("customsInventorySn", customsInventorySnList);
        criteria.andEqualTo("deleted", false);
        List<ExportItemDO> itemDOList = exportItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(itemDOList)) {
            return resultMap;
        }
        List<Long> endorsementIdList = itemDOList.stream().map(ExportItemDO::getEndorsementOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(endorsementIdList);
        Map<Long, EndorsementDTO> endorsementIdMap = endorsementDTOList.stream().collect(Collectors.toMap(EndorsementDTO::getId, Function.identity()));
        itemDOList.forEach(i -> {
            Long endorsementOrderId = i.getEndorsementOrderId();
            if (Objects.nonNull(endorsementOrderId)) {
                if (endorsementIdMap.containsKey(endorsementOrderId)) {
                    EndorsementDTO endorsementDTO = endorsementIdMap.get(endorsementOrderId);
                    resultMap.put(i.getCustomsInventorySn(), endorsementDTO);
                }
            }
        });
        return resultMap;
    }

    @Override
    public Map<String, Long> findEndorsementIdListByLogisticsNo(List<String> logisticsNo) {
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andIn("mailNo", logisticsNo).andEqualTo("deleted", false);
        example.selectProperties("endorsementOrderId", "mailNo");
        List<ExportItemDO> exportItemDOS = exportItemMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(exportItemDOS)) {
            return new HashMap<>();
        }
        Map<String, Long> logisticsNoEndorsementIdMap = exportItemDOS.stream().filter(e -> Objects.nonNull(e.getMailNo()) && Objects.nonNull(e.getEndorsementOrderId())).collect(Collectors.toMap(ExportItemDO::getMailNo, ExportItemDO::getEndorsementOrderId));
        return logisticsNoEndorsementIdMap;
    }

    /**
     * 核注单作废删除出库单表体
     *
     * @param endorsementId 核注单id
     * @param exportOrderId 出库单id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteExportItemByEndorsement(Long endorsementId, Long exportOrderId) {
        List<ExportItemDTO> exportItemDTOList = this.listItemByEndorsementId(endorsementId);
        List<String> inventorySnList = new ArrayList<>();
        List<Long> delIdList = new ArrayList<>();
        for (ExportItemDTO exportItemDTO : exportItemDTOList) {
            inventorySnList.add(exportItemDTO.getCustomsInventorySn());
            delIdList.add(exportItemDTO.getId());
        }
        Example example = new Example(ExportItemDO.class);
        example.createCriteria().andIn("id", delIdList);
        exportItemMapper.deleteByExample(example);
        // 所有表体都删除后，出库单状态变为已创建
        Example itemExample = new Example(ExportItemDO.class);
        itemExample.createCriteria().andEqualTo("deleted", false)
                .andEqualTo("exportOrderId", exportOrderId);
        int count = exportItemMapper.selectCountByExample(itemExample);
        if (count == 0) {
            ExportOrderDO exportOrderDO = new ExportOrderDO();
            exportOrderDO.setId(exportOrderId);
            exportOrderDO.setStatus(ExportOrderStatus.WRITING.getValue());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                exportOrderDO.setUpdateBy(UserUtils.getUserId());
            }
            exportOrderDO.setUpdateTime(new Date());
            exportOrderMapper.updateByPrimaryKeySelective(exportOrderDO);
        }
        // 解除交接单绑定关系
//        ExportOrderDTO exportOrderDTO = exportOrderService.findById(exportOrderId);
//        handoverOrderDetailService.updateDetailExportOrder(mailNoList, exportOrderDTO.getSn(), HandoverDetailOutBoundStatus.NO);

        //去关联清单申报核注状态
        if (CollUtil.isNotEmpty(inventorySnList)) {
            //新增剔除单并修改清单核注状态 剔除单改造还未上线
//            cullOrderService.createByInventorySn(inventorySnList, InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
            customsInventoryService.updateEndorsementsStatusBySn(inventorySnList, InventoryReviewStatus.UNLINKED_ENDORSEMENT.getValue());
            try {
                supervisionMonitorService.changeCullRemarkByInventorySn(inventorySnList);
            } catch (Exception e) {
                log.error("回传时效监管系统剔除节点异常 error={}", e.getMessage(), e);
            }
            //清除交接单关联
            log.info("deleteExportItemByEndorsement 解除交接单绑定关系 snList={}", JSON.toJSONString(inventorySnList));
            handoverOrderDetailService.cleanExportOrderInfo(inventorySnList);
        }
    }

    @Override
    public ListVO<ExportItemDTO> itemPaging(ExportOrderItemSearch search) {
        if (Objects.isNull(search.getExportOrderId())) {
            return ListVO.emptyListVO();
        }
        if (CollUtil.isEmpty(search.getRoleAccountBookIdList())) {
            return ListVO.emptyListVO();
        }
        if (StrUtil.isNotBlank(search.getAccountBookNo())) {
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOByNo(search.getAccountBookNo());
            if (customsBookDTO == null || !search.getRoleAccountBookIdList().contains(customsBookDTO.getId())) {
                return ListVO.emptyListVO();
            }
            search.setRoleAccountBookIdList(CollUtil.newArrayList(customsBookDTO.getId()));
        }

        Example example = new Example(ExportItemDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andIn("accountBookId", search.getRoleAccountBookIdList())
                .andEqualTo("exportOrderId", search.getExportOrderId())
                .andEqualTo("deleted", false);
        PageHelper.startPage(search.getCurrentPage(), search.getPageSize());
        List<ExportItemDO> list = exportItemMapper.selectByExample(example);
        ListVO<ExportItemDTO> listVO = new ListVO<>();

        PageInfo<ExportItemDO> pageInfo = new PageInfo<>(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        listVO.setPage(pageResult);
        listVO.setDataList(ConvertUtil.listConvert(list, ExportItemDTO.class));
        return listVO;
    }

    /**
     * 返回下标
     *
     * @param skuGroup
     * @param itemDTOS
     * @return
     */
    private Integer dispatchGroup(Map<Integer, Set<Long>> skuGroup, List<ExportSkuInfo> itemDTOS, Map<Integer, List<ExportItemDTO>> endorsementGroup) {
        int batterIndex = skuGroup.size(); // 默认新增分组
        int batterMatch = 0;
        Integer groupSizeSku = orderBaseConfig.getExportItemWritingGroupSizeSku();
        Integer groupSizeLogistic = orderBaseConfig.getExportItemWritingGroupSizeLogistic();
        for (Map.Entry<Integer, Set<Long>> entry : skuGroup.entrySet()) {
            // 提前校验是否运单数大于3000
            List<ExportItemDTO> exportItemDTOList = endorsementGroup.get(entry.getKey());
            if (!CollectionUtils.isEmpty(exportItemDTOList) && exportItemDTOList.size() >= groupSizeLogistic) {
                continue; // 清单数最多3000
            }
            int match = 0;
            int not = 0;
            for (ExportSkuInfo inventoryItemDTO : itemDTOS) {
                if (entry.getValue().contains(inventoryItemDTO.getBookItemId())) {
                    match += 1;
                } else {
                    not += 1;
                }
            }
            // Step::匹配是否有匹配的清单组
            if (match > 0 && not == 0) {
                return entry.getKey();
            }
            // Step::过滤无效分组|新增度要满足 入区SKU限制 999
            if ((groupSizeSku - entry.getValue().size()) < not) {
                continue;
            }
            //Step::记录匹配度最高的组|相似度按高->低排序
            log.info("[op:Endorsement-dispatchGroup] match={}, batterMatch={}, batterIndex={}, skuGroup={}", match, batterMatch, batterIndex, skuGroup.size());
            if (match > batterMatch || (batterIndex == skuGroup.size())) {
                log.info("[op:Endorsement-dispatchGroup] chose index={}", entry.getKey());
                batterMatch = match;
                batterIndex = entry.getKey();
            }
        }
        return batterIndex;
    }

    private ExportOrderDTO buildDTO(ExportOrderDO exportOrderDO) {
        if (exportOrderDO == null) {
            return null;
        }
        ExportOrderDTO dto = new ExportOrderDTO();
        BeanUtils.copyProperties(exportOrderDO, dto);
        return dto;
    }

    private ExportOrderDTOV2 buildDTOV2(ExportOrderDO exportOrderDO) {
        if (exportOrderDO == null) {
            return null;
        }
        ExportOrderDTOV2 dto = new ExportOrderDTOV2();
        BeanUtils.copyProperties(exportOrderDO, dto);
        return dto;
    }

    private ExportItemDTO buildItemDTO(ExportItemDO exportItemDO) {
        if (exportItemDO == null) {
            return null;
        }
        ExportItemDTO dto = new ExportItemDTO();
        BeanUtils.copyProperties(exportItemDO, dto);
        return dto;
    }
}
