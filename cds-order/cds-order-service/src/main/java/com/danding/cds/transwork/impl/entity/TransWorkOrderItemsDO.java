package com.danding.cds.transwork.impl.entity;

import java.util.Date;

import javax.persistence.Column;
import javax.persistence.Table;

import com.danding.cds.common.model.BaseDO;

import lombok.Data;
import lombok.EqualsAndHashCode;

@EqualsAndHashCode(callSuper = true)
@Table(name = "ccs_trans_work_order_items")
@Data
public class TransWorkOrderItemsDO extends BaseDO {

    /**
     * 运输作业id
     */
    @Column(name = "trans_id")
    private Long transId;

    /**
     * 明细行号
     */
    @Column(name = "order_line_no")
    private String orderLineNo;

    /**
     * 商品编码
     */
    @Column(name = "item_code")
    private String itemCode;

    /**
     * 商品名称
     */
    @Column(name = "item_name")
    private String itemName;

    /**
     * 数量
     */
    @Column(name = "qty")
    private Long qty;

    /**
     * 实际发货数量
     */
    @Column(name = "act_delivery_qty")
    private Long actDeliveryQty;


    /**
     * 实际签收数量
     */
    @Column(name = "act_sign_qty")
    private Long actSignQty;


    /**
     * 原产国
     */
    @Column(name = "origin_region")
    private String originRegion;

    /**
     * 采购单价
     */
    @Column(name = "purchase_price")
    private String purchasePrice;

    /**
     * 币种
     */
    @Column(name = "currency")
    private String currency;

    /**
     * 总价
     */
    @Column(name = "total_price")
    private String totalPrice;

    /**
     * 合计体积
     */
    @Column(name = "volume")
    private String volume;

    /**
     * 合计重量
     */
    @Column(name = "weight")
    private String weight;

    /**
     * 生产批号
     */
    @Column(name = "produce_code")
    private String produceCode;

    /**
     * 生产日期
     */
    @Column(name = "product_date")
    private String productDate;

    /**
     * 过期日期
     */
    @Column(name = "expiry_date")
    private String expiryDate;
}