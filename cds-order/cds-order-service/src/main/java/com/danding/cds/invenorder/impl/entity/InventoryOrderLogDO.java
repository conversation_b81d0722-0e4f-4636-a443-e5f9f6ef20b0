package com.danding.cds.invenorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Getter;
import lombok.Setter;

import javax.persistence.Column;
import javax.persistence.Table;

@Table(name = "ccs_inventory_order_log")
@Getter
@Setter
public class InventoryOrderLogDO extends BaseDO {
    /**
     * 清关单ID
     */
    @Column(name = "ref_inve_order_id")
    private Long refInveOrderId;
    /**
     *清关单SN
     */
    @Column(name = "ref_inve_order_sn")
    private String  refInveOrderSn;
    /**
     * 日志名称
     */
    @Column(name = "log_name")
    private String logName;
    /**
     * 日志明细
     */
    @Column(name = "log_detail")
    private String logDetail;
    /**
     * 备注说明
     */
    @Column(name = "log_content")
    private String logContent;

}
