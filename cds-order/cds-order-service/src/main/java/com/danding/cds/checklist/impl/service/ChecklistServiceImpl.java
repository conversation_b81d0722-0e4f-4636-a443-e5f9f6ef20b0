package com.danding.cds.checklist.impl.service;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.checklist.api.dto.*;
import com.danding.cds.checklist.api.enums.ChecklistBindType;
import com.danding.cds.checklist.api.enums.ChecklistStatusEnum;
import com.danding.cds.checklist.api.enums.ChecklistType;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.checklist.impl.entity.ChecklistDO;
import com.danding.cds.checklist.impl.entity.ChecklistTrackLogDO;
import com.danding.cds.checklist.impl.mapper.ChecklistMapper;
import com.danding.cds.common.bean.dto.TraceDataRunnable;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ChecklistLogUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.declare.sdk.CustomsSupport;
import com.danding.cds.declare.sdk.clear.base.CustomsResult;
import com.danding.cds.declare.sdk.enums.CheckListCustomsStatusEnum;
import com.danding.cds.declare.sdk.model.checkList.*;
import com.danding.cds.declare.sdk.utils.Tuple;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.dto.EndorsementItemDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementCustomsStatus;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.enums.IEType;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.endorsement.impl.entity.EndorsementExtraDO;
import com.danding.cds.endorsement.impl.entity.EndorsementItemDO;
import com.danding.cds.endorsement.impl.mapper.EndorsementItemMapper;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.invenorder.api.dto.CustomsCompleteCountDTO;
import com.danding.cds.invenorder.api.dto.InvBusinessCountDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.enums.Inv101TrspModecd;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.api.ChecklistAuthService;
import com.danding.cds.v2.bean.dao.ChecklistItemDO;
import com.danding.cds.v2.bean.dto.ChecklistAuthDTO;
import com.danding.cds.v2.bean.dto.ChecklistItemDTO;
import com.danding.cds.v2.bean.dto.EndorsementChecklistMappingDTO;
import com.danding.cds.v2.bean.dto.RecordWarehouseProductIdDTO;
import com.danding.cds.v2.bean.vo.ChecklistVO;
import com.danding.cds.v2.bean.vo.EndorsementVO;
import com.danding.cds.v2.bean.vo.req.ChecklistAssociateItemReqVo;
import com.danding.cds.v2.bean.vo.req.ChecklistItemSaveReqVo;
import com.danding.cds.v2.bean.vo.res.CheckListViewResVo;
import com.danding.cds.v2.bean.vo.res.ChecklistAssociateItemResVo;
import com.danding.cds.v2.enums.ChecklistAuthCancelStatusEnum;
import com.danding.cds.v2.mapper.ChecklistItemMapper;
import com.danding.cds.v2.service.BaseDataService;
import com.danding.cds.v2.service.base.ChecklistBaseService;
import com.danding.cds.v2.service.base.EndorsementChecklistMappingBaseService;
import com.danding.cds.v2.service.base.EndorsementItemBaseService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.cache.common.config.RedisLockUtils;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.assertj.core.util.Lists;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@DubboService
@Slf4j
@RefreshScope
public class ChecklistServiceImpl implements ChecklistService {

    @Autowired
    private ChecklistMapper checklistMapper;

    @Autowired
    private ChecklistItemMapper checklistItemMapper;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private SequenceService sequenceService;

    @Autowired
    private CustomsSupport customsSupport;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Autowired
    private EndorsementItemMapper endorsementItemMapper;

    @Autowired
    private ChecklistBaseService checklistBaseService;

    @Autowired
    private EndorsementChecklistMappingBaseService endorsementChecklistMappingBaseService;

    @Autowired
    private BaseDataService baseDataService;

    @Autowired
    private Validator validator;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @Autowired
    private EndorsementItemBaseService endorsementItemBaseService;

    @Autowired
    private ChecklistService checklistService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private ChecklistAuthService checklistAuthService;

    @Autowired
    private ChecklistTrackLogService checklistTrackLogService;

    @Autowired
    private RedisLockUtils redisLockUtils;

    @Resource(name = "checklistCountTaskExecutor")
    private ThreadPoolTaskExecutor checklistCountTaskExecutor;

    @Value("${checklist.push.interval:1}")
    private Long checklistPushInterval;


    /**
     * 核放单统计
     *
     * @return
     */
    @Override
    public InvBusinessCountDTO selectChecklistBussinessCount(String areaCompanyId) {

        List<String> businessStatus = Arrays.asList(ChecklistStatusEnum.CREATED.getCode(),
                ChecklistStatusEnum.DECALRING.getCode(),
                ChecklistStatusEnum.EXCEPTION.getCode(),
                ChecklistStatusEnum.DELETING.getCode());

        InvBusinessCountDTO countDTO = new InvBusinessCountDTO();
        //一线入境统计
        countDTO.setOnelineInCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN, businessStatus, areaCompanyId));
        //区间流转入
        countDTO.setSectionInCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN, businessStatus, areaCompanyId));
        //区间流转出
        countDTO.setSectionOutCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT, businessStatus, areaCompanyId));
        //销毁
        countDTO.setDestroyCount(countInvBusinessNum(InventoryOrderBusinessEnum.BUSSINESS_DESTORY, businessStatus, areaCompanyId));
        //一线退运
        countDTO.setOneLineRefund(countInvBusinessNum(InventoryOrderBusinessEnum.BUSINESS_ONELINE_REFUND, businessStatus, areaCompanyId));
        //保税物流转大贸
        countDTO.setBondedToTrade(countInvBusinessNum(InventoryOrderBusinessEnum.BUSINESS_BONDED_TO_TRADE, businessStatus, areaCompanyId));
        return countDTO;
    }

    private Integer countInvBusinessNum(InventoryOrderBusinessEnum businessEnum, List<String> statusList, String areaCompanyId) {
        Example example = new Example(ChecklistDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("endorsementType", businessEnum.getCode())
                .andIn("status", statusList)
                .andEqualTo("deleted", false);
        if (StringUtils.isNotBlank(areaCompanyId) && !"all".equalsIgnoreCase(areaCompanyId)) {
            criteria.andEqualTo("declareCompanyId", areaCompanyId);
        }
        return checklistMapper.selectCountByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void push(Long id) {
        ChecklistDO checklistOrderDO = checklistMapper.selectByPrimaryKey(id);
        if ((!checklistOrderDO.getStatus().equals(ChecklistStatusEnum.CREATED.getCode())
                && !checklistOrderDO.getStatus().equals(ChecklistStatusEnum.EXCEPTION.getCode())
                && !checklistOrderDO.getStatus().equals(ChecklistStatusEnum.DECALRING.getCode())
                && !checklistOrderDO.getStatus().equals(ChecklistStatusEnum.STORED.getCode())
        ) && !checklistOrderDO.getType().equals(ChecklistType.EMPTY.getCode())) {
            throw new ArgsErrorException("仅已创建、申报中、已暂存或失败状态支持推送海关");
        }
        // 核放单状态为申报中，回执状态为 通过 或 过卡 - 修正核放单状态 完结订单
        if (Objects.equals(checklistOrderDO.getStatus(), ChecklistStatusEnum.DECALRING.getCode())) {
            String updateStatus = null;
            if (CheckListCustomsStatusEnum.SAS221_PASS.getCode().equals(checklistOrderDO.getCustomsStatus())) {
                updateStatus = ChecklistStatusEnum.AUDITING.getCode();
            } else if (CheckListCustomsStatusEnum.SAS223_GUOKA.getCode().equals(checklistOrderDO.getCustomsStatus())) {
                updateStatus = ChecklistStatusEnum.FINISH.getCode();
            }
            if (StringUtils.isNotBlank(updateStatus)) {
                this.updateStatus(id, updateStatus);
                return;
            }
        }
        Map<String, List<ChecklistAuthDTO>> checklistAuthMap = new HashMap<>();
        if (!Objects.equals(checklistOrderDO.getBindType(), ChecklistBindType.NULL.getCode())) {
            List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(checklistOrderDO.getId());
            if (CollectionUtils.isEmpty(endorsementDTOList)) {
                throw new ArgsErrorException("绑定类型不为\"无\"时,绑定核注单号不能为空");
            }
            checkAuthFinish(endorsementDTOList, checklistOrderDO, checklistAuthMap);
        }
        if (Objects.equals(checklistOrderDO.getBindType(), ChecklistBindType.ONE_TICKET_MULTI_CAR.getCode())) {
            List<ChecklistItemDTO> checklistItemDTOS = this.listChecklistItemById(checklistOrderDO.getId());
            if (CollectionUtils.isEmpty(checklistItemDTOS)) {
                throw new ArgsErrorException("绑定类型为\"一票多车\"时,未绑定核放单表体无法推送核放");
            }
            List<Long> endorsementIdList = checklistItemDTOS.stream().map(ChecklistItemDTO::getEndorsementId).collect(Collectors.toList());
            List<EndorsementDTO> endorsementDTOS = endorsementService.findByIdList(endorsementIdList);
            checkAuthFinish(endorsementDTOS, checklistOrderDO, checklistAuthMap);
        }
        if (Objects.equals(checklistOrderDO.getType(), ChecklistType.EMPTY.getCode())) {
            if (Objects.isNull(checklistOrderDO.getEmptyCarPicUrl())) {
                throw new ArgsErrorException("空车照片/磅单未上传");
            }
        }
        if (!redisLockUtils.lock("ccs:checklist:push:limit:" + id, System.currentTimeMillis() + checklistPushInterval * 60 * 1000L)) {
            throw new ArgsErrorException("核放单推送异常，重推间隔需大于" + checklistPushInterval + "分钟!");
        }
        CustomsChecklistOrderInfo customsChecklistOrderInfo = new CustomsChecklistOrderInfo();
        //系统内核放单号
        customsChecklistOrderInfo.setChecklistOrderNo(checklistOrderDO.getSn());
        //承运车车牌号
        customsChecklistOrderInfo.setLicensePlate(checklistOrderDO.getLicensePlate());
        //联系人
        customsChecklistOrderInfo.setApplicant(checklistOrderDO.getApplicant());
        //IC卡号
        customsChecklistOrderInfo.setVehicleIcNo(checklistOrderDO.getVehicleIcNo());
        customsChecklistOrderInfo.setRemark(checklistOrderDO.getRemark());
        CompanyDTO companyDTO = baseDataService.getCompanyDTOById(checklistOrderDO.getDeclareCompanyId());
        //两步申报
        if (ChecklistType.TWO_STEP.getCode().equals(checklistOrderDO.getType())) {
            Icp101MessageRequest twoStepRequest = new Icp101MessageRequest();
            //twoStepRequest.setCustomsBookCode(customsBookDTO.getBookNo());
            CustomsBookResVo customsBookResVo = this.getCustomsBookByDeclareCompanyId(checklistOrderDO.getDeclareCompanyId());
            if (Objects.nonNull(customsBookResVo)) {
                twoStepRequest.setCustomsBookCode(customsBookResVo.getBookNo());
                twoStepRequest.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
            }
            customsChecklistOrderInfo.setVehicleWeight(String.valueOf(checklistOrderDO.getCarWeight()));
            customsChecklistOrderInfo.setVehicleFrameNo(checklistOrderDO.getLicenseFrame());
            customsChecklistOrderInfo.setVehicleFrameWeight(String.valueOf(checklistOrderDO.getFrameWeight()));
            BigDecimal totalGrossWeight = checklistOrderDO.getTotalGrossWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getTotalGrossWeight();
            BigDecimal carWeight = checklistOrderDO.getCarWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getCarWeight();
            BigDecimal frameWeight = checklistOrderDO.getFrameWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getFrameWeight();
            BigDecimal totalWeight = totalGrossWeight.add(carWeight).add(frameWeight);
            customsChecklistOrderInfo.setTotalWt(totalWeight);
            customsChecklistOrderInfo.setTotalGrossWt(String.valueOf(totalGrossWeight));
            customsChecklistOrderInfo.setContainerType(checklistOrderDO.getContainerType());
            customsChecklistOrderInfo.setContainerWt(checklistOrderDO.getContainerWt());

            customsChecklistOrderInfo.setDeclareOrderNo(checklistOrderDO.getDeclareOrderNo());
            twoStepRequest.setCustomsChecklistOrderInfo(customsChecklistOrderInfo).setCustomsCompanyCode(companyDTO.getSpecialClientCode());
            log.info("[ChecklistServiceImpl.push twoStep preNo = {}]", checklistOrderDO.getPreOrderNo());
            CustomsResult customsResult = customsSupport.ieChecklistTwoStep(twoStepRequest);
            if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
                throw new RuntimeException("核放两步申报推送异常:" + customsResult.getErrorMessage());
            }
        } else {
            Sas121MessageRequest request = new Sas121MessageRequest();
            customsChecklistOrderInfo.setVehicleWeight(checklistOrderDO.getCarWeight().toString());
            customsChecklistOrderInfo.setVehicleFrameNo(checklistOrderDO.getLicenseFrame());
            customsChecklistOrderInfo.setVehicleFrameWeight(checklistOrderDO.getFrameWeight().toString());
            customsChecklistOrderInfo.setPreChecklistOrderNo(StringUtils.isNotEmpty(checklistOrderDO.getPreOrderNo()) ? checklistOrderDO.getPreOrderNo() : "");
            customsChecklistOrderInfo.setRealChecklistOrderNo(StringUtils.isNotEmpty(checklistOrderDO.getRealOrderNo()) ? checklistOrderDO.getRealOrderNo() : "");
            // 出入区标识
            if (checklistOrderDO.getIeFlag().equals(IEType.IMPORT.getValue())) {
                customsChecklistOrderInfo.setChecklistType("SECOND_IN");
            } else {
                customsChecklistOrderInfo.setChecklistType("SECOND_OUT");
            }
            if (checklistOrderDO.getType().equals(ChecklistType.EMPTY.getCode())
                    || checklistOrderDO.getType().equals(ChecklistType.EMPTY_OUT.getCode())) {//空车进出区区
                customsChecklistOrderInfo.setPassPortTypeCd("6");
            } else if (checklistOrderDO.getType().equals(ChecklistType.SECOND.getCode())//二线进出区
                    || checklistOrderDO.getType().equals(ChecklistType.SECOND_OUT.getCode())
                    || checklistOrderDO.getType().equals(ChecklistType.FIRST.getCode()) //一线一体化进出区
                    || checklistOrderDO.getType().equals(ChecklistType.ONE_OUT.getCode())) {
                customsChecklistOrderInfo.setPassPortTypeCd(checklistOrderDO.getType().equals(ChecklistType.SECOND.getCode())
                        || checklistOrderDO.getType().equals(ChecklistType.SECOND_OUT.getCode()) ? "3" : "2");
                List<EndorsementDTO> endorsementDTOS = this.getEndorsementListByChecklistId(id);
                List<Long> endorsementIdList = endorsementDTOS.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
                List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByEndorsementIds(endorsementIdList);
                BigDecimal totalGrossWt = BigDecimal.ZERO;
                BigDecimal totalNetWt = BigDecimal.ZERO;
                if (Objects.isNull(checklistOrderDO.getTotalGrossWeight()) || checklistOrderDO.getTotalGrossWeight().equals(BigDecimal.ZERO)) {
                    for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                        totalGrossWt = totalGrossWt.add(endorsementItemDTO.getGrossWeight());
                    }
                } else {
                    totalGrossWt = checklistOrderDO.getTotalGrossWeight();
                }
                if (Objects.isNull(checklistOrderDO.getTotalNetWeight()) || checklistOrderDO.getTotalNetWeight().equals(BigDecimal.ZERO)) {
                    for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                        totalNetWt = totalNetWt.add(endorsementItemDTO.getNetWeight());
                    }
                } else {
                    totalNetWt = checklistOrderDO.getTotalNetWeight();
                }
                //关联核注清单
                List<CustomsEndorsementOrderInfo> endorsementOrderInfoList = new ArrayList<>();
                for (EndorsementDTO endorsementDTO : endorsementDTOS) {
                    CustomsEndorsementOrderInfo endorsementOrderInfo = new CustomsEndorsementOrderInfo();
                    endorsementOrderInfo.setEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getSn()) ? endorsementDTO.getSn() : "");
                    endorsementOrderInfo.setPreEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getPreOrderNo()) ? endorsementDTO.getPreOrderNo() : "");
                    endorsementOrderInfo.setRealEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getRealOrderNo()) ? endorsementDTO.getRealOrderNo() : "");
                    endorsementOrderInfoList.add(endorsementOrderInfo);
                }
                if (Objects.equals(checklistOrderDO.getBindType(), ChecklistBindType.MULTI_TICKET_ONE_CAR.getCode())) {
                    customsChecklistOrderInfo.setBindTypeCd("1");
                    //需核放单关联单证表体
                    request.setCustomsEndorsementOrderInfo(endorsementOrderInfoList);
                } else if (Objects.equals(checklistOrderDO.getBindType(), ChecklistBindType.ONE_TICKET_ONE_CAR.getCode())) {
                    customsChecklistOrderInfo.setBindTypeCd("2");
                } else if (Objects.equals(checklistOrderDO.getBindType(), ChecklistBindType.ONE_TICKET_MULTI_CAR.getCode())) {
                    customsChecklistOrderInfo.setBindTypeCd("3");
                    List<CheckListPassPort> checkListPassPortList = new ArrayList<>();
                    List<ChecklistItemDTO> checklistItemDTOList = checklistBaseService.listItemByChecklistId(checklistOrderDO.getId());
                    totalGrossWt = checklistOrderDO.getTotalGrossWeight();
                    totalNetWt = checklistOrderDO.getTotalNetWeight();
                    for (ChecklistItemDTO item : checklistItemDTOList) {
                        CheckListPassPort checkListPassPort = new CheckListPassPort();
                        checkListPassPort.setGoodsNo(item.getProductId());
                        checkListPassPort.setHsCode(item.getHsCode());
                        checkListPassPort.setGoodsName(item.getGoodsName());
                        checkListPassPort.setDeclareUnit(item.getDeclareUnit());
                        checkListPassPort.setRelateGoodsSeqNo(item.getEndorsementSerialNumber().toString());
                        checkListPassPort.setDeclareQty(item.getDeclareUnitQfy().toString());
                        checkListPassPortList.add(checkListPassPort);
                    }
                    request.setCheckListPassPortList(checkListPassPortList);
                }
                customsChecklistOrderInfo.setTotalNetWt(totalNetWt.toString());
                customsChecklistOrderInfo.setTotalGrossWt(totalGrossWt.toString());
                customsChecklistOrderInfo.setRltTbTypeCd("1");
                request.setCustomsEndorsementOrderInfo(endorsementOrderInfoList);
            }
            request.setCustomsChecklistOrderInfo(customsChecklistOrderInfo);
            CustomsBookResVo customsBookResVo = this.getCustomsBookByDeclareCompanyId(checklistOrderDO.getDeclareCompanyId());
            if (Objects.nonNull(customsBookResVo)) {
                request.setCustomsBookCode(customsBookResVo.getBookNo());
                request.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
            }
            request.setCustomsCompanyCode(companyDTO.getSpecialClientCode());
            log.info("[ChecklistServiceImpl.push preNo = {}]", checklistOrderDO.getPreOrderNo());
            CustomsResult customsResult = customsSupport.ieChecklistHZ(request);
            if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
                throw new RuntimeException("核放推送异常:" + customsResult.getErrorMessage());
            }
        }
        checklistTrackLogService.saveLog(checklistOrderDO.getId(), ChecklistStatusEnum.DECALRING.getCode(),
                "核放单请求报文提交", ChecklistLogUtils.getLogDetailAndRemove());
        this.updateStatus(id, ChecklistStatusEnum.DECALRING.getCode());
    }

    /**
     * 检查核放单授权任务 是否全部完成
     *
     * @param endorsementDTOS
     * @param checklistOrderDO
     * @param checklistAuthMap
     */
    private void checkAuthFinish(List<EndorsementDTO> endorsementDTOS, ChecklistDO checklistOrderDO, Map<String, List<ChecklistAuthDTO>> checklistAuthMap) {
        for (EndorsementDTO endorsementDTO : endorsementDTOS) {
            if (!Objects.equals(checklistOrderDO.getDeclareCompanyId(), endorsementDTO.getDeclareCompanyId())) {
                if (!checklistAuthMap.containsKey(endorsementDTO.getBussinessType())) {
                    List<ChecklistAuthDTO> checklistAuthDTOS =
                            checklistAuthService.findByBeAuthCompanyId(checklistOrderDO.getDeclareCompanyId(), endorsementDTO.getBussinessType());
                    checklistAuthMap.put(endorsementDTO.getBussinessType(), checklistAuthDTOS);
                }
                List<ChecklistAuthDTO> checklistAuthDTOList = checklistAuthMap.get(endorsementDTO.getBussinessType());
                if (checklistAuthDTOList.stream().noneMatch(i -> i.getEndorsementRealOrderNoList().contains(endorsementDTO.getRealOrderNo()))) {
                    throw new ArgsErrorException("核注单:" + endorsementDTO.getRealOrderNo() + "，不属于授权完成状态");
                }
            }
        }
    }

    /**
     * 通过清关企业匹配账册的区内企业
     *
     * @param id
     * @return
     */
    private CustomsBookResVo getCustomsBookByDeclareCompanyId(Long id) {
        List<CustomsBookResVo> customsBookResVos = customsBookService.findByAreaCompanyId(id);
        if (!CollectionUtils.isEmpty(customsBookResVos)) {
            return customsBookResVos.stream().filter(i -> Objects.equals(i.getEnable(), 1)).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void pushTemporaryStorage(Long id) {
        ChecklistDO checklistOrderDO = checklistMapper.selectByPrimaryKey(id);
        if (!ChecklistType.TWO_STEP.getCode().equals(checklistOrderDO.getType())) {
            throw new ArgsErrorException("仅两步申报支持暂存推送");
        }
        if (
                (!checklistOrderDO.getStatus().equals(ChecklistStatusEnum.CREATED.getCode())
                        && !checklistOrderDO.getStatus().equals(ChecklistStatusEnum.EXCEPTION.getCode()))
                        && !checklistOrderDO.getStatus().equals(ChecklistStatusEnum.STORED.getCode())
                        && !checklistOrderDO.getType().equals(ChecklistType.EMPTY.getCode())
        ) {
            throw new ArgsErrorException("仅已创建或失败状态支持推送海关");
        }
        //申报企业编码和申报企业名称必填；
        if (Objects.isNull(checklistOrderDO.getDeclareCompanyId())) {
            throw new ArgsErrorException("清关企业信息未填写");
        }
        CustomsChecklistOrderInfo customsChecklistOrderInfo = new CustomsChecklistOrderInfo();
        //系统内核放单号
        customsChecklistOrderInfo.setChecklistOrderNo(checklistOrderDO.getSn());
        //承运车车牌号
        customsChecklistOrderInfo.setLicensePlate(checklistOrderDO.getLicensePlate());
        customsChecklistOrderInfo.setRemark(checklistOrderDO.getRemark());
        //联系人
        customsChecklistOrderInfo.setApplicant(checklistOrderDO.getApplicant());
        customsChecklistOrderInfo.setVehicleWeight(String.valueOf(checklistOrderDO.getCarWeight()));
        customsChecklistOrderInfo.setVehicleFrameNo(checklistOrderDO.getLicenseFrame());
        customsChecklistOrderInfo.setVehicleFrameWeight(String.valueOf(checklistOrderDO.getFrameWeight()));
        BigDecimal totalGrossWeight = checklistOrderDO.getTotalGrossWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getTotalGrossWeight();
        BigDecimal carWeight = checklistOrderDO.getCarWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getCarWeight();
        BigDecimal frameWeight = checklistOrderDO.getFrameWeight() == null ? BigDecimal.ZERO : checklistOrderDO.getFrameWeight();
        BigDecimal totalWeight = totalGrossWeight.add(carWeight).add(frameWeight);
        customsChecklistOrderInfo.setTotalWt(totalWeight);
        customsChecklistOrderInfo.setTotalGrossWt(String.valueOf(totalGrossWeight));
        customsChecklistOrderInfo.setContainerType(checklistOrderDO.getContainerType());
        customsChecklistOrderInfo.setContainerWt(checklistOrderDO.getContainerWt());

        CompanyDTO companyDTO = companyService.findById(checklistOrderDO.getDeclareCompanyId());
        //两步申报
        Icp101MessageRequest twoStepRequest = new Icp101MessageRequest();
        customsChecklistOrderInfo.setDeclareOrderNo(checklistOrderDO.getDeclareOrderNo());
        twoStepRequest.setDelcareFlag(0);
        twoStepRequest.setCustomsChecklistOrderInfo(customsChecklistOrderInfo).setCustomsCompanyCode(companyDTO.getSpecialClientCode());
        CustomsBookResVo customsBookResVo = this.getCustomsBookByDeclareCompanyId(checklistOrderDO.getDeclareCompanyId());
        if (Objects.nonNull(customsBookResVo)) {
            twoStepRequest.setCustomsBookCode(customsBookResVo.getBookNo());
            twoStepRequest.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
        }
        //CustomsBookDTO customsBookDTO = customsBookService.findById(checklistOrderDO.getAccountBookId());
        //twoStepRequest.setCustomsBookCode(customsBookDTO.getBookNo());
        log.info("[ChecklistServiceImpl.push twoStep pushTemporaryStorage preNo = {}]", checklistOrderDO.getPreOrderNo());
        CustomsResult customsResult = customsSupport.ieChecklistTwoStep(twoStepRequest);
        if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
            throw new RuntimeException("核放暂存推送异常:" + customsResult.getErrorMessage());
        }
        checklistTrackLogService.saveLog(checklistOrderDO.getId(), ChecklistStatusEnum.STORING.getCode(),
                "暂存请求报文提交", ChecklistLogUtils.getLogDetailAndRemove());
        this.updateStatus(id, ChecklistStatusEnum.STORING.getCode());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ChecklistSubmit submit, Long userId) {
        if (StringUtils.isEmpty(submit.getLicensePlate())) {
            throw new ArgsErrorException("车辆信息不能为空");
        }
//        BigDecimal carWeight = submit.getCarWeight();
//        BigDecimal frameWeight = submit.getFrameWeight();
//        if (Objects.nonNull(carWeight)) {
//            if (carWeight.precision() > 16) {
//                throw new ArgsErrorException("车辆自重不能超过16位");
//            }
//            if (carWeight.scale() > 3) {
//                throw new ArgsErrorException("车辆自重小数点仅支持3位数");
//            }
//        }
//        if (Objects.nonNull(frameWeight)) {
//            if (frameWeight.precision() > 16) {
//                throw new ArgsErrorException("车架重不能超过16位");
//            }
//            if (frameWeight.scale() > 3) {
//                throw new ArgsErrorException("车架重小数点仅支持3位数");
//            }
//        }
        ChecklistDO checklistOrderDO = new ChecklistDO();
        checklistOrderDO.setLicensePlate(submit.getLicensePlate());
        checklistOrderDO.setDeclareCompanyId(submit.getDeclareCompanyID());
        checklistOrderDO.setApplicant(submit.getApplicant());
        checklistOrderDO.setSn(sequenceService.generateChecklistSn());
        checklistOrderDO.setStatus(ChecklistStatusEnum.CREATED.getCode());
        checklistOrderDO.setType(submit.getType());
        checklistOrderDO.setCreateBy(userId.intValue());
        checklistOrderDO.setVehicleIcNo(submit.getVehicleIcNo());
        checklistOrderDO.setBindType(submit.getBindType());
        checklistOrderDO.setCarWeight(submit.getCarWeight().setScale(3, RoundingMode.HALF_UP));
        checklistOrderDO.setLicenseFrame(submit.getLicenseFrame());
        checklistOrderDO.setFrameWeight(submit.getFrameWeight().setScale(3, RoundingMode.HALF_UP));
        checklistOrderDO.setIeFlag(submit.getIeFlag());
        //默认置0 ，统一需要用户手动输入
        checklistOrderDO.setTotalGrossWeight(BigDecimal.ZERO);
        checklistOrderDO.setTotalNetWeight(BigDecimal.ZERO);
        checklistOrderDO.setTotalWeight(checklistOrderDO.getCarWeight().add(checklistOrderDO.getFrameWeight()));
        //如果是两步申报 直接保存
        if (ChecklistType.TWO_STEP.getCode().equals(submit.getType())) {
            checklistOrderDO.setDeclareOrderNo(submit.getDeclareOrderNo());
            UserUtils.setCreateAndUpdateBy(checklistOrderDO);
            checklistMapper.insertSelective(checklistOrderDO);
//            this.refreshTotalWeight(checklistOrderDO.getId(), submit.getTotalGrossWeight(), submit.getTotalNetWeight());

            checklistTrackLogService.saveLog(checklistOrderDO.getId(), ChecklistStatusEnum.CREATED.getCode(), "新建核放单");
            return checklistOrderDO.getId();
        }
        List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByEndorsementIds(submit.getEndorsementIdList());
        log.info("ChecklistServiceImpl create endorsementItemIdList={}]", JSON.toJSONString(submit.getEndorsementIdList()));
//        if ((!submit.getType().equals(ChecklistType.EMPTY.getCode())) &&
//                (!submit.getType().equals(ChecklistType.EMPTY_OUT.getCode())) &&
//                (!submit.getType().equals(ChecklistType.TWO_STEP.getCode()))) {
        // TODO:这里未校验是否一票多车，表体是否勾选完整

//            if (CollectionUtils.isEmpty(submit.getEndorsementItemIdList())) {
//                throw new ArgsErrorException("未选择表体信息");
//            }
//            Map<Long, EndorsementDTO> endorsementDTOMap = new HashMap<>();
//            Boolean moreCar = false;
//            for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
//                EndorsementDTO endorsementDTO = endorsementDTOMap.get(endorsementItemDTO.getEndorsementId());
//                if (endorsementDTO == null) {
//                    endorsementDTO = endorsementService.findById(endorsementItemDTO.getEndorsementId());
//                    endorsementDTOMap.put(endorsementDTO.getId(), endorsementDTO);
//                }
//                moreCar = moreCar || endorsementDTO.getChecklistsFlag();
//            }
//            if (moreCar && endorsementDTOMap.values().size() > 1) {
//                // 多车时不允许多票
//                throw new ArgsErrorException("不允许多车多票");
//            }
//            List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(submit.getEndorsementIdList());
//            Map<Integer, EndorsementBussiness[]> checkListTypeMap = ChecklistType.getCheckListTypeMapEndorsementBussiness();
//            EndorsementBussiness[] endorsementBussiness = checkListTypeMap.get(submit.getType());
//            for (EndorsementDTO endorsementDTO : endorsementDTOList) {
//                boolean _isFind = Arrays.stream(endorsementBussiness).filter(s -> s.getCode().equalsIgnoreCase(endorsementDTO.getBussinessType())).findAny().isPresent();
//                if (!_isFind) {
//                    throw new ArgsErrorException("核放单映射类型与核准清单不匹配");
//                }
//                if (checklistOrderDO.getAccountBookId() == null) {
//                    checklistOrderDO.setAccountBookId(endorsementDTO.getAccountBookId());
//                }
//                if (!checklistOrderDO.getAccountBookId().equals(endorsementDTO.getAccountBookId())) {
//                    throw new ArgsErrorException("核注清单的账册不一致");
//                }
//                if (!endorsementDTO.getDeclareCompanyId().equals(submit.getDeclareCompanyID())) {
//                    throw new ArgsErrorException("核注清单与核放单申报企业不一致");
//                }
//                if (!endorsementDTO.getIeFlag().equals(submit.getIeFlag())) {
//                    throw new ArgsErrorException("核注清单与核放单出入区类型不一致");
//                }
//            }
//        } else {
//            checklistOrderDO.setAccountBookId(0L);
//        }
//        List<Long> endorsementIdList = endorsementItemDTOList.stream().map(EndorsementItemDTO::getEndorsementId).distinct().collect(Collectors.toList());
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(submit.getEndorsementIdList());
        Map<Long, EndorsementDTO> endorsementDTOMap = endorsementDTOList.stream().collect(Collectors.toMap(EndorsementDTO::getId, Function.identity(), (v1, v2) -> v1));
        for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
            if (endorsementItemDTO.getChecklistId() != null) {
                if (endorsementDTOMap.containsKey(endorsementItemDTO.getEndorsementId())) {
                    EndorsementDTO endorsementDTO = endorsementDTOMap.get(endorsementItemDTO.getEndorsementId());
                    throw new ArgsErrorException("核注单号:" + endorsementDTO.getSn() + ":" + endorsementItemDTO.getGoodsName() + "表体已被关联过核放单");
                }
            }
        }
        UserUtils.setCreateAndUpdateBy(checklistOrderDO);
        checklistMapper.insertSelective(checklistOrderDO);
        for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
            endorsementService.updateItemChecklistById(endorsementItemDTO.getId(), checklistOrderDO.getId());
        }
        checklistTrackLogService.saveLog(checklistOrderDO.getId(), ChecklistStatusEnum.CREATED.getCode(), "新建核放单");
        endorsementChecklistMappingBaseService.saveNewEndorsementChecklistMapping(checklistOrderDO.getId(), submit.getEndorsementIdList());
//        this.refreshTotalWeight(checklistOrderDO.getId(), submit.getTotalGrossWeight(), submit.getTotalNetWeight());
        return checklistOrderDO.getId();
    }

    @Override
    public void finish(Long id, String realNo) {
        ChecklistDO old = checklistMapper.selectByPrimaryKey(id);
        if (!old.getStatus().equals(ChecklistStatusEnum.CREATED.getCode()) && !old.getStatus().equals(ChecklistStatusEnum.EXCEPTION.getCode())) {
            throw new ArgsErrorException("仅支持已创建,申报失败状态的核放单手动完成核放");
        } else {
            ChecklistDO example = new ChecklistDO();
            example.setRealOrderNo(realNo);
            old = checklistMapper.selectOne(example);
            if (old != null) {
                throw new ArgsErrorException("该核放单号已存在");
            }
            // Step::更改后联动状态
            example.setId(id);
            example.setStatus(ChecklistStatusEnum.AUDITING.getCode());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                example.setUpdateBy(UserUtils.getUserId());
            }
            example.setUpdateTime(new Date());
            checklistMapper.updateByPrimaryKeySelective(example);

            checklistTrackLogService.saveLog(id, ChecklistStatusEnum.AUDITING.getCode(), "核放单手动审核：核放单号：" + realNo);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long update(ChecklistSubmit submit) {
        ChecklistDO old = checklistMapper.selectByPrimaryKey(submit.getId());
        int userId = UserUtils.getUserId();
        ChecklistDO checklistDO = new ChecklistDO();
        checklistDO.setId(submit.getId());
        checklistDO.setDeleted(false);
        checklistDO.setRemark(submit.getRemark());
        // ic卡号
        if (!StringUtils.isEmpty(submit.getVehicleIcNo())) {
            checklistDO.setVehicleIcNo(submit.getVehicleIcNo());
        }
        // 车牌号编辑
        if (!StringUtils.isEmpty(submit.getLicensePlate()) && !submit.getLicensePlate().equals(old.getLicensePlate())) {
            checklistDO.setLicensePlate(submit.getLicensePlate());
        }
        // 车架号
        if (!StringUtils.isEmpty(submit.getLicenseFrame()) && !submit.getLicenseFrame().equals(old.getLicenseFrame())) {
            checklistDO.setLicenseFrame(submit.getLicenseFrame());
        }
        //车辆自重
        if (!Objects.isNull(submit.getCarWeight())) {
            checklistDO.setCarWeight(submit.getCarWeight().setScale(5, RoundingMode.HALF_UP));
        }
        //车架重
        if (!Objects.isNull(submit.getFrameWeight())) {
            checklistDO.setFrameWeight(submit.getFrameWeight().setScale(5, RoundingMode.HALF_UP));
        }
        // 申请人
        if (!StringUtils.isEmpty(submit.getApplicant()) && !submit.getApplicant().equals(old.getApplicant())) {
            checklistDO.setApplicant(submit.getApplicant());
        }
        if (Objects.nonNull(submit.getTotalGrossWeight())) {
            checklistDO.setTotalGrossWeight(submit.getTotalGrossWeight().setScale(5, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(submit.getTotalNetWeight())) {
            checklistDO.setTotalNetWeight(submit.getTotalNetWeight().setScale(5, RoundingMode.HALF_UP));
        }
        if (Objects.nonNull(submit.getEmptyCarPicUrl())) {
            checklistDO.setEmptyCarPicUrl(submit.getEmptyCarPicUrl());
        }
        if (Objects.nonNull(submit.getEmptyCarPicName())) {
            checklistDO.setEmptyCarPicName(submit.getEmptyCarPicName());
        }
        checklistDO.setContainerType(submit.getContainerType());
        checklistDO.setContainerWt(submit.getContainerWt());
        BigDecimal totalGrossWeight = checklistDO.getTotalGrossWeight() == null ? BigDecimal.ZERO : checklistDO.getTotalGrossWeight();
        //避免出现old和submit都为空的情况
        BigDecimal carWeight = checklistDO.getCarWeight() == null ? (old.getCarWeight() == null ? BigDecimal.ZERO : old.getCarWeight()) : checklistDO.getCarWeight();
        BigDecimal frameWeight = checklistDO.getFrameWeight() == null ? (old.getFrameWeight() == null ? BigDecimal.ZERO : old.getFrameWeight()) : checklistDO.getFrameWeight();
//        BigDecimal totalNetWeight = checklistDO.getTotalNetWeight() == null ? BigDecimal.ZERO : checklistDO.getTotalNetWeight();
        BigDecimal totalWeight = totalGrossWeight.add(carWeight).add(frameWeight);
        checklistDO.setTotalWeight(totalWeight.setScale(5, RoundingMode.HALF_UP));
        checklistDO.setEndorsementType(submit.getEndorsementType());
        checklistDO.setDeclareCompanyId(submit.getDeclareCompanyID());
        checklistDO.setUpdateBy(userId);
        checklistDO.setBindType(submit.getBindType());
        // 两步申报直接更新返回
        if (ChecklistType.TWO_STEP.getCode().equals(submit.getType())) {
            checklistDO.setDeclareOrderNo(submit.getDeclareOrderNo());
            UserUtils.setUpdateBy(checklistDO);
            checklistDO.setUpdateTime(new Date());
            checklistMapper.updateByPrimaryKeySelective(checklistDO);
            return checklistDO.getId();
        }
        // 核放编号编辑
        if (!StringUtils.isEmpty(submit.getRealOrderNo()) && !submit.getRealOrderNo().equals(old.getRealOrderNo())) {
            checklistDO.setRealOrderNo(submit.getRealOrderNo());
        }
        // 预录入编号编辑
        if (!StringUtils.isEmpty(submit.getOrderNo()) && !submit.getOrderNo().equals(old.getPreOrderNo())) {
            checklistDO.setPreOrderNo(submit.getOrderNo());
        }
        if (Objects.equals(ChecklistBindType.NULL.getCode(), submit.getBindType())) {
            endorsementChecklistMappingBaseService.clearEndorsementChecklistMapping(old.getId());
            checklistBaseService.clearChecklistItem(old.getId());
        }
        UserUtils.setUpdateBy(checklistDO);
        checklistDO.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(checklistDO);
//        this.refreshTotalWeight(checklistDO.getId(), submit.getTotalGrossWeight(), submit.getTotalNetWeight());
        return checklistDO.getId();
    }

    private void preCheckBindType(ChecklistSubmit submit) {
        if (Objects.nonNull(submit.getEndorsementIdList()) && Objects.isNull(submit.getBindType())) {
            throw new ArgsErrorException("请先选择绑定类型再关联核注清单");
        }
        ChecklistBindType bindType = ChecklistBindType.getEnum(submit.getBindType());
        if (submit.getType().equals(ChecklistType.EMPTY.getCode()) ||
                submit.getType().equals(ChecklistType.EMPTY_OUT.getCode()) ||
                submit.getType().equals(ChecklistType.TWO_STEP.getCode())) {
            if (!Objects.equals(bindType, ChecklistBindType.NULL)) {
                throw new ArgsErrorException("空车出入区、两步申报 绑定类型只允许选择无");
            }
        }

        switch (bindType) {
            case NULL:
                if (!CollectionUtils.isEmpty(submit.getEndorsementIdList())) {
                    throw new ArgsErrorException("绑定类型:无 不支持绑定核注单");
                }
                break;
            case ONE_TICKET_MULTI_CAR:
            case ONE_TICKET_ONE_CAR:
                if (!CollectionUtils.isEmpty(submit.getEndorsementIdList()) && submit.getEndorsementIdList().size() > 1) {
                    throw new ArgsErrorException("绑定类型:一票一车/多车 不支持绑定多个单据");
                }
                if (submit.getId() != null) {
                    List<EndorsementDTO> endorsementDTOList = checklistService.getEndorsementListByChecklistId(submit.getId());
                    if (endorsementDTOList.size() > 1) {
                        throw new ArgsErrorException("核放单已绑定多个单据 不允许选择一票一车/多车");
                    }
                }
                break;
            default:
                break;
        }
    }

    @Override
    public void bindEndorsement(ChecklistSubmit submit) {
        IEType ieType = ChecklistType.getIEType(submit.getType());
        if (ieType == null) {
            throw new ArgsErrorException("核放单类型参数错误");
        }
        submit.setIeFlag(ieType.getValue());
        this.preCheckBindType(submit);
        ChecklistDO checklistDO = checklistMapper.selectByPrimaryKey(submit.getId());
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(submit.getEndorsementIdList());
        this.checkBindEndorsementList(submit, endorsementDTOList, checklistDO);
        // 一票多车业务不需要更新核注单表体
        if (!checklistDO.getType().equals(ChecklistType.EMPTY.getCode())
                && !Objects.equals(submit.getBindType(), ChecklistBindType.ONE_TICKET_MULTI_CAR.getCode())) {
            checkAndUpdateEndorsementItem(submit, checklistDO);
        }
        if (!Objects.equals(ChecklistBindType.NULL.getCode(), submit.getBindType())) {
            saveEndorsementChecklistMapping(submit, checklistDO);
        }
        UserUtils.setUpdateBy(checklistDO);
        checklistDO.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(checklistDO);

        String endorsementSns = endorsementDTOList.stream().map(EndorsementDTO::getSn).collect(Collectors.joining("、"));
        checklistTrackLogService.saveLog(checklistDO.getId(), checklistDO.getStatus(), "关联核注清单：" + endorsementSns);

    }

    private void checkBindEndorsementList(ChecklistSubmit submit, List<EndorsementDTO> endorsementDTOList, ChecklistDO checklistDO) {
//        if (endorsementDTOList.size() > 1 && endorsementDTOList.stream().map(EndorsementDTO::getAccountBookId).distinct().count() != 1) {
//            throw new ArgsErrorException("绑定的核注单账册不一致");
//        }
        Set<Long> endorsementBookIdSet = new HashSet<>();
        Map<String, List<ChecklistAuthDTO>> checklistAuthMap = new HashMap<>();
        for (EndorsementDTO endorsementDTO : endorsementDTOList) {
            boolean isAuth = false;
            boolean isFind = true;
            Map<Integer, EndorsementBussiness[]> checkListTypeMapEndorsementBussiness = ChecklistType.getCheckListTypeMapEndorsementBussiness();
            if (!checkListTypeMapEndorsementBussiness.containsKey(submit.getType())) {
                isFind = false;
            } else {
                isFind = Stream.of(checkListTypeMapEndorsementBussiness.get(submit.getType()))
                        .anyMatch(enums -> Objects.equals(enums.getCode(), endorsementDTO.getBussinessType()));
            }
//            if (Objects.equals(ChecklistType.ONE_OUT.getCode(), submit.getType())) {
//                if (!Objects.equals(EndorsementBussiness.BUSINESS_ONELINE_REFUND.getCode(), endorsementDTO.getBussinessType())) {
//                    isFind = false;
//                }
//            }
//            if (Objects.equals(ChecklistType.FIRST.getCode(), submit.getType())) {
//                if (!Objects.equals(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode(), endorsementDTO.getBussinessType())
//                        && !Objects.equals(EndorsementBussiness.BUSINESS_BONDED_ONELINE_IN.getCode(), endorsementDTO.getBussinessType())
//                        && !Objects.equals(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode(), endorsementDTO.getBussinessType())
//                ) {
//                    isFind = false;
//                }
//            }
//            if (Objects.equals(ChecklistType.SECOND.getCode(), submit.getType())) {
//                if (!Objects.equals(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode(), endorsementDTO.getBussinessType())) {
//                    isFind = false;
//                }
//            }
//            if (Objects.equals(ChecklistType.SECOND_OUT.getCode(), submit.getType())) {
//                List<String> codeList = Arrays.asList(EndorsementBussiness.BUSINESS_BONDED_TO_TRADE.getCode(),
//                        EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode(),
//                        EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode(),
//                        EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode());
//                if (!codeList.contains(endorsementDTO.getBussinessType())) {
//                    isFind = false;
//                }
//            }
            if (!isFind) {
                throw new ArgsErrorException("核放单类型与核注单业务类型不匹配，生成核放单失败");
            }
            if (!endorsementDTO.getDeclareCompanyId().equals(checklistDO.getDeclareCompanyId())) {
                //数据进行二次校验，判定核注单是否都符合授权完成+未发起取消
                String realOrderNo = endorsementDTO.getRealOrderNo();
                if (!checklistAuthMap.containsKey(endorsementDTO.getBussinessType())) {
                    List<ChecklistAuthDTO> checklistAuthDTOList = checklistAuthService.findByBeAuthCompanyId(
                            checklistDO.getDeclareCompanyId(), endorsementDTO.getBussinessType());
                    checklistAuthMap.put(endorsementDTO.getBussinessType(), checklistAuthDTOList);
                }
                List<ChecklistAuthDTO> checklistAuthDTOList = checklistAuthMap.get(endorsementDTO.getBussinessType());
                if (CollUtil.isNotEmpty(checklistAuthDTOList)) {
                    List<String> authRealOrderNoList = checklistAuthDTOList.stream()
                            .flatMap(i -> i.getEndorsementRealOrderNoList().stream()).collect(Collectors.toList());
                    List<String> cancelAuthRealOrderNoList = checklistAuthDTOList.stream()
                            .filter(i -> Objects.equals(i.getCancelStatus(), ChecklistAuthCancelStatusEnum.SENT.getCode()))
                            .flatMap(i -> i.getEndorsementRealOrderNoList().stream()).collect(Collectors.toList());
                    if (!authRealOrderNoList.contains(realOrderNo)) {
                        throw new ArgsInvalidException("核注清单编号【" + realOrderNo + "】，核放单未授权完成");
                    }
                    if (cancelAuthRealOrderNoList.contains(realOrderNo)) {
                        throw new ArgsErrorException("核注清单编号【" + realOrderNo + "】，核放单授权已发起取消");
                    }
                    isAuth = true;
                } else {
                    throw new ArgsErrorException("核注清单与核放单申报企业不一致");
                }
            }
            if (!isAuth) {
                endorsementBookIdSet.add(endorsementDTO.getAccountBookId());
            }
//            if (endorsementBookIdSet.size() > 1) {
//                throw new ArgsErrorException("绑定的核注单账册不一致");
//            }
            if (!endorsementDTO.getIeFlag().equals(checklistDO.getIeFlag())) {
                throw new ArgsErrorException("核注清单与核放单出入区类型不一致");
            }
        }
        checklistDO.setAccountBookId(endorsementDTOList.get(0).getAccountBookId());
    }


    private void checkAndUpdateEndorsementItem(ChecklistSubmit submit, ChecklistDO old) {
        List<Long> endorsementIdList = submit.getEndorsementIdList();
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(endorsementIdList);
        List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByEndorsementIds(endorsementIdList);
//            if (CollectionUtils.isEmpty(submit.getEndorsementItemIdList())) {
//                throw new ArgsErrorException("未选择表体信息");
//            }
//            Map<Long, EndorsementDTO> endorsementDTOMap = new HashMap<>();
//            Boolean moreCar = false;
//            for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
//                EndorsementDTO endorsementDTO = endorsementDTOMap.get(endorsementItemDTO.getEndorsementId());
//                if (endorsementDTO == null) {
//                    endorsementDTO = endorsementService.findById(endorsementItemDTO.getEndorsementId());
//                    endorsementDTOMap.put(endorsementDTO.getId(), endorsementDTO);
//                }
//                moreCar = moreCar || endorsementDTO.getChecklistsFlag();
//            }
//            if (moreCar && endorsementDTOMap.values().size() > 1) {
//                // 多车时不允许多票
//                throw new ArgsErrorException("不允许多车多票");
//            }
        for (EndorsementDTO endorsementDTO : endorsementDTOList) {
            if (!endorsementDTO.getIeFlag().equals(old.getIeFlag())) {
                throw new ArgsErrorException("核注清单与核放单出入区类型不一致");
            }
        }
        // 非空车时才有清单表体编辑先清空所有关联，再重新添加
        List<EndorsementItemDTO> oldItemList = endorsementService.listItemByChecklist(old.getId());
        for (EndorsementItemDTO endorsementItemDTO : oldItemList) {
            endorsementService.updateItemChecklistById(endorsementItemDTO.getId(), null);
        }
        for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
            if (endorsementItemDTO.getChecklistId() != null && !endorsementItemDTO.getChecklistId().equals(submit.getId())) {
                EndorsementDTO endorsementDTO = endorsementService.findById(endorsementItemDTO.getEndorsementId());
                throw new ArgsErrorException("核注单号:" + endorsementDTO.getSn() + ":" + endorsementItemDTO.getGoodsName() + "表体已被关联过核放单");
            }
        }
        for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
            endorsementService.updateItemChecklistById(endorsementItemDTO.getId(), old.getId());
        }
    }

    /**
     * 保存Endorsement checklist的关联关系
     * 1.查找关联的核注单
     * 2.比较这次提交的核注单和之前的是否一致
     * 3.如果是一票多车业务那就清除核放单表体
     *
     * @param submit
     * @param old
     */
    private void saveEndorsementChecklistMapping(ChecklistSubmit submit, ChecklistDO old) {
        //查已经关联的核注单号
        List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(old.getId());
        List<Long> endorsementIdList = endorsementDTOList.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        log.info("saveEndorsementChecklistMapping endorsementIdList={}", JSON.toJSONString(endorsementIdList));
        log.info("saveEndorsementChecklistMapping submit-endorsementIdList={}", JSON.toJSONString(submit.getEndorsementIdList()));
        //比较两个endorsementId 一致的话直接返回
        if (Objects.equals(endorsementIdList, submit.getEndorsementIdList())) {
            log.info("saveEndorsementChecklistMapping 两个list没有差别 不进行更新直接返回");
            return;
        }
        if (!CollectionUtils.isEmpty(endorsementIdList)) {
            log.info("saveEndorsementChecklistMapping 清除老的关联关系");
            endorsementChecklistMappingBaseService.clearEndorsementChecklistMapping(old.getId(), endorsementIdList);
        }
        log.info("saveEndorsementChecklistMapping 保存新的关联关系");
        endorsementChecklistMappingBaseService.saveNewEndorsementChecklistMapping(old.getId(), submit.getEndorsementIdList());
        //一票多车业务更换绑定核注单需要清空核放单表体
        if (Objects.equals(old.getBindType(), ChecklistBindType.ONE_TICKET_MULTI_CAR.getCode())) {
            log.info("saveEndorsementChecklistMapping 一票多车业务更换绑定核注单需要清空核放单表体");
            checklistBaseService.clearChecklistItem(old.getId());
        }
    }

    @Override
    public Long updateStatus(Long id, String status) {
        ChecklistDO example = new ChecklistDO();
        example.setId(id);
        example.setStatus(status);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            example.setUpdateBy(UserUtils.getUserId());
        }
        example.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(example);
        return example.getId();
    }

    @Override
    public Long updateCustomsStatus(Long id, String status) {
        ChecklistDO example = new ChecklistDO();
        example.setId(id);
        example.setCustomsStatus(status);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            example.setUpdateBy(UserUtils.getUserId());
        }
        example.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(example);
        return example.getId();
    }

    @Override
    public Long updateInformationDesc(Long id, String informationDesc) throws ArgsErrorException {
        ChecklistDO example = new ChecklistDO();
        example.setId(id);
        example.setInformationDesc(informationDesc);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            example.setUpdateBy(UserUtils.getUserId());
        }
        example.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(example);
        return example.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long discard(Long id) {
        ChecklistDO old = checklistMapper.selectByPrimaryKey(id);
        ChecklistDO example = new ChecklistDO();
        example.setId(id);

        if (old.getStatus().equals(ChecklistStatusEnum.CREATED.getCode())
                || old.getStatus().equals(ChecklistStatusEnum.EXCEPTION.getCode())
                || old.getStatus().equals(ChecklistStatusEnum.STORED.getCode())) {
            // Step::更改后联动状态
            example.setStatus(ChecklistStatusEnum.DELETE.getCode());
            //不是空车核放 找出核放单关联核注清单
            if (!old.getType().equals(ChecklistType.EMPTY.getCode()) && !old.getType().equals(ChecklistType.TWO_STEP.getCode())) {
//                List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByChecklist(old.getId());
//                Map<Long, EndorsementDTO> endorsementDTOMap = new HashMap<>();
//                for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
//                    //取消关联核注清单表体
//                    endorsementService.updateItemChecklistById(endorsementItemDTO.getId(), null);
//
//                    log.info("置空ChecklistId解除关联关系[{}]", endorsementItemDTO.getId());
//                    EndorsementDTO endorsementDTO = endorsementDTOMap.get(endorsementItemDTO.getEndorsementId());
//                    if (endorsementDTO == null) {
//                        endorsementDTO = endorsementService.findById(endorsementItemDTO.getEndorsementId());
//                        endorsementDTOMap.put(endorsementDTO.getId(), endorsementDTO);
//                    }
//                }
//                for (EndorsementDTO endorsementDTO : endorsementDTOMap.values()) {
//                    //更改核注清单状态为已审核
//                    endorsementService.updateStatus(endorsementDTO.getId(), EndorsementOrderStatus.EXAMINE.getCode());
//                }
                postDiscard(id);
            }
            UserUtils.setUpdateBy(example);
            example.setUpdateTime(new Date());
            checklistMapper.updateByPrimaryKeySelective(example);
            checklistTrackLogService.saveLog(id, ChecklistStatusEnum.DELETE.getCode(), "核放单已作废");
        } else if (old.getStatus().equals(ChecklistStatusEnum.AUDITING.getCode())) {
            // TODO:这里需根据进行作废申报的操作
            Sas121MessageRequest request = new Sas121MessageRequest();
            request.setDeclareType("CLOSE");
            CustomsChecklistOrderInfo checklistOrderInfo = new CustomsChecklistOrderInfo();
            checklistOrderInfo.setPreChecklistOrderNo(old.getPreOrderNo());
            checklistOrderInfo.setChecklistOrderNo(old.getSn());
            checklistOrderInfo.setRealChecklistOrderNo(StringUtils.isNotEmpty(old.getRealOrderNo()) ? old.getRealOrderNo() : "");
            checklistOrderInfo.setLicensePlate(old.getLicensePlate());
            checklistOrderInfo.setVehicleWeight(old.getCarWeight().toString());
            checklistOrderInfo.setVehicleFrameNo(old.getLicenseFrame());
            checklistOrderInfo.setVehicleFrameWeight(old.getFrameWeight().toString());
            checklistOrderInfo.setApplicant(old.getApplicant());
            //CustomsBookDTO customsBookDTO = customsBookService.findById(old.getAccountBookId());
            // 出入区标识
            if (old.getIeFlag().equals(IEType.IMPORT.getValue())) {
                checklistOrderInfo.setChecklistType("SECOND_IN");
            } else {
                checklistOrderInfo.setChecklistType("SECOND_OUT");
            }
            if (old.getType().equals(ChecklistType.EMPTY.getCode()) || old.getType().equals(ChecklistType.EMPTY_OUT.getCode())) {//空车进出区区
                checklistOrderInfo.setPassPortTypeCd("6");
            } else if (old.getType().equals(ChecklistType.SECOND.getCode())//二线进出区
                    || old.getType().equals(ChecklistType.SECOND_OUT.getCode())//二线进出区
                    || old.getType().equals(ChecklistType.ONE_OUT.getCode())//一线出区
                    || old.getType().equals(ChecklistType.FIRST.getCode())) {//一线一体化进出区
                checklistOrderInfo.setPassPortTypeCd(old.getType().equals(ChecklistType.SECOND.getCode()) || old.getType().equals(ChecklistType.SECOND_OUT.getCode()) ? "3" : "2");
                List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByChecklist(id);
                Map<Long, EndorsementDTO> endorsementDTOMap = new HashMap<>();
                BigDecimal totalGrossWt = BigDecimal.ZERO;
                BigDecimal totalNetWt = BigDecimal.ZERO;
                for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                    EndorsementDTO endorsementDTO = endorsementDTOMap.get(endorsementItemDTO.getEndorsementId());
                    if (endorsementDTO == null) {
                        endorsementDTO = endorsementService.findById(endorsementItemDTO.getEndorsementId());
                        endorsementDTOMap.put(endorsementDTO.getId(), endorsementDTO);
                    }
                    totalGrossWt = totalGrossWt.add(endorsementItemDTO.getGrossWeight());
                    totalNetWt = totalNetWt.add(endorsementItemDTO.getNetWeight());
                }
                //关联核注清单
                List<CustomsEndorsementOrderInfo> endorsementOrderInfoList = new ArrayList<>();
                for (EndorsementDTO endorsementDTO : endorsementDTOMap.values()) {
                    CustomsEndorsementOrderInfo endorsementOrderInfo = new CustomsEndorsementOrderInfo();
                    endorsementOrderInfo.setEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getSn()) ? endorsementDTO.getSn() : "");
                    endorsementOrderInfo.setPreEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getPreOrderNo()) ? endorsementDTO.getPreOrderNo() : "");
                    endorsementOrderInfo.setRealEndorsementOrderNo(StringUtils.isNotEmpty(endorsementDTO.getRealOrderNo()) ? endorsementDTO.getRealOrderNo() : "");
                    endorsementOrderInfoList.add(endorsementOrderInfo);
                }
                if (endorsementDTOMap.size() > 1) {//多票一车
                    checklistOrderInfo.setBindTypeCd("1");
                    //需核放单关联单证表体
                    request.setCustomsEndorsementOrderInfo(endorsementOrderInfoList);
                } else if (endorsementDTOMap.size() == 1) {//一票
                    checklistOrderInfo.setBindTypeCd("2");
                    EndorsementDTO endorsementDTO = new ArrayList<>(endorsementDTOMap.values()).get(0);
                    if (endorsementDTO.getChecklistsFlag()) {//多车 （需关联核放单表体）
                        checklistOrderInfo.setBindTypeCd("3");
                    }
                }
                checklistOrderInfo.setTotalNetWt(totalNetWt.toString());
                checklistOrderInfo.setTotalGrossWt(totalGrossWt.toString());
                checklistOrderInfo.setRltTbTypeCd("1");
                request.setCustomsEndorsementOrderInfo(endorsementOrderInfoList);
            }
            request.setCustomsChecklistOrderInfo(checklistOrderInfo);
            CompanyDTO companyDTO = companyService.findById(old.getDeclareCompanyId());
            request.setCustomsCompanyCode(companyDTO.getSpecialClientCode());
            CustomsBookResVo customsBookResVo = this.getCustomsBookByDeclareCompanyId(old.getDeclareCompanyId());
            if (customsBookResVo != null) {
                request.setCustomsBookCode(customsBookResVo.getBookNo());
                request.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
            }
            CustomsResult customsResult = customsSupport.ieChecklistHZ(request);
            if (Objects.isNull(customsResult) || !Boolean.TRUE.equals(customsResult.getSuccess())) {
                throw new RuntimeException("核放作废推送异常:" + customsResult.getErrorMessage());
            }
            checklistTrackLogService.saveLog(id, ChecklistStatusEnum.DELETING.getCode(),
                    "核放单作废请求报文提交", ChecklistLogUtils.getLogDetailAndRemove());
            //更新状态
            example.setStatus(ChecklistStatusEnum.DELETING.getCode());
            UserUtils.setUpdateBy(example);
            example.setUpdateTime(new Date());
            checklistMapper.updateByPrimaryKeySelective(example);
        } else {
            throw new ArgsErrorException("当前状态不支持作废操作");
        }
//        // 清除关联关系
//        this.postDiscard(id);
        return example.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void postDiscard(Long id) {
        endorsementChecklistMappingBaseService.clearEndorsementChecklistMapping(id);
        discardChecklistItemByChecklistId(id);
    }

    @Transactional(rollbackFor = Exception.class)
    public void discardChecklistItemByChecklistId(Long id) {
        List<ChecklistItemDTO> checklistItemDTOList = checklistBaseService.listItemByChecklistId(id);
        List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByChecklist(id);
//        if (CollectionUtils.isEmpty(checklistItemDTOList)) {
//            return;
//        }
//        List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemByItemIds(checklistItemDTOList.stream().map(ChecklistItemDTO::getEndorsementItemId).collect(Collectors.toList()));
//        if (CollectionUtils.isEmpty(checklistItemDTOList)) {
//            return;
//        }
        // 一票多车
        if (CollUtil.isNotEmpty(checklistItemDTOList)) {
            List<Long> endorsementIdList = checklistItemDTOList.stream().map(ChecklistItemDTO::getEndorsementItemId).collect(Collectors.toList());
            List<EndorsementItemDTO> endorsementItemDTO2List = endorsementService.listItemByItemIds(endorsementIdList);
            endorsementItemDTOList.addAll(endorsementItemDTO2List);
            Map<Long, EndorsementItemDTO> filterMap = endorsementItemDTO2List.stream().collect(Collectors.toMap(EndorsementItemDTO::getId, e -> e));
            endorsementItemDTOList = new ArrayList<>(filterMap.values());
        }

        // 维护核注单剩余申报数量 & 解除核放单关联关系
        Map<Long, ChecklistItemDTO> idItemMap = checklistItemDTOList.stream().collect(Collectors.toMap(ChecklistItemDTO::getEndorsementItemId, e -> e));
        endorsementItemDTOList.forEach(e -> {
            EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
            BeanUtils.copyProperties(e, endorsementItemDO);
            // 剩余申报数量回退
            ChecklistItemDTO checklistItemDTO = idItemMap.get(e.getId());
            if (Objects.nonNull(checklistItemDTO)) {
                BigDecimal remain = e.getRemainDeclareUnitQfy().add(new BigDecimal(checklistItemDTO.getDeclareUnitQfy()));
                endorsementItemDO.setRemainDeclareUnitQfy(remain);
            }
            endorsementItemDO.setChecklistId(null); //解除核放单关联关系
            Example example = new Example(EndorsementItemDO.class);
            example.createCriteria().andEqualTo("id", e.getId()).andEqualTo("remainDeclareUnitQfy", e.getRemainDeclareUnitQfy());
            UserUtils.setUpdateBy(endorsementItemDO);
            endorsementItemDO.setUpdateTime(new Date());
            int i = endorsementItemMapper.updateByExample(endorsementItemDO, example);
            if (i != 1) {
                log.warn("更新失败");
                throw new ArgsErrorException("更新失败");
            }
        });
        checklistItemDTOList.forEach(c -> checklistBaseService.deleteChecklistItem(c.getId()));
    }

    @Override
    public ChecklistDTO findByBusinessNo(String businessNo) {
        Example example = new Example(ChecklistDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("sn", businessNo);
        // Step::返回值处理
        ChecklistDO result = checklistMapper.selectOneByExample(example);
        return this.buildDTO(result);
    }

    @Override
    public ChecklistDTO findByPreNo(String preNo) {
        Example example = new Example(ChecklistDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("preOrderNo", preNo);
        // Step::返回值处理
        ChecklistDO result = checklistMapper.selectOneByExample(example);
        return this.buildDTO(result);
    }

    @Override
    public ChecklistDTO findById(Long id) {
        return this.buildDTO(checklistMapper.selectByPrimaryKey(id));
    }

    @Override
    public List<ChecklistDTO> findByIdList(List<Long> idList) {
        Example example = new Example(ChecklistDO.class);
        example.createCriteria().andIn("id", idList).andEqualTo("deleted", false);
        List<ChecklistDO> list = checklistMapper.selectByExample(example);
        return list.stream().map(this::buildDTOV2).collect(Collectors.toList());
    }

    @Override
    public ListVO<ChecklistDTOV2> paging(ChecklistSearch search) {
        Example example = this.buildExample(search);
        PageHelper.startPage(search.getCurrentPage(), search.getPageSize());
        List<ChecklistDO> list = checklistMapper.selectByExample(example);
        ListVO<ChecklistDTOV2> result = new ListVO<>();
        result.setDataList(list.stream().map(this::buildDTOV2).collect(Collectors.toList()));
        // 分页
        PageInfo<ChecklistDO> pageInfo = new PageInfo(list);
        PageResult pageResult = new PageResult();
        pageResult.setTotalCount(pageInfo.getTotal());
        pageResult.setTotalPage(pageInfo.getPages());
        pageResult.setCurrentPage(pageInfo.getPageNum());
        pageResult.setPageSize(pageInfo.getPageSize());
        result.setPage(pageResult);
        return result;
    }

    private Example buildExample(ChecklistSearch search) {
        Example example = new Example(ChecklistDO.class);
        Example.Criteria criteria = example.createCriteria();
        // 账册Id列表
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            criteria.andIn("accountBookId", accountBookIdList);
        }
        if (StringUtils.isNotEmpty(search.getEndorsementSns())) {
            List<String> endorsementSnList = Splitter.on(",").splitToList(search.getEndorsementSns());
            List<Long> idList = this.getIdListByEndorsementSns(endorsementSnList);
            if (!CollectionUtils.isEmpty(idList)) {
                criteria.andIn("id", idList);
            } else {
                //未匹配到核注单号，查询结果为空
                criteria.andEqualTo("id", -1);
            }
        }
        criteria.andEqualTo("deleted", false);
        if (!StringUtils.isEmpty(search.getStatus())) {
            criteria.andEqualTo("status", search.getStatus());
        }
        if (search.getType() != null && search.getType() != 0) {
            criteria.andEqualTo("type", search.getType());
        }
        if (Objects.nonNull(search.getBindType())) {
            criteria.andEqualTo("bindType", search.getBindType());
        }
        Long createFrom = LongUtil.getFrom(search.getCreateFrom(), search.getCreateTo());
        Long createTo = LongUtil.getEnd(search.getCreateFrom(), search.getCreateTo());
        if (!LongUtil.isNone(createFrom) && !LongUtil.isNone(createTo)) {
            criteria.andBetween("createTime", new Date(createFrom), new Date(createTo));
        }
        Long lastCustomsFrom = LongUtil.getFrom(search.getLastCustomsFrom(), search.getLastCustomsTo());
        Long lastCustomsTo = LongUtil.getEnd(search.getLastCustomsFrom(), search.getLastCustomsTo());
        if (!LongUtil.isNone(lastCustomsFrom) && !LongUtil.isNone(lastCustomsTo)) {
            criteria.andBetween("lastCustomsTime", new Date(createFrom), new Date(createTo));
        }
//        if (!StringUtils.isEmpty(search.getQueryInfo())) {
//            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
//            if ("realOrderNo".equals(search.getQueryType())) {
//                criteria.andIn("realOrderNo", noList);
//            } else if ("orderNo".equals(search.getQueryType())) {
//                criteria.andIn("preOrderNo", noList);
//            } else if ("sn".equals(search.getQueryType())) {
//                criteria.andIn("sn", noList);
//            } else if ("licensePlate".equals(search.getQueryType())) {
//                criteria.andIn("licensePlate", noList);
//            }
//        }
        if (StringUtils.isNotBlank(search.getRealOrderNo())) {
            List<String> realOrderNoList = Lists.newArrayList(search.getRealOrderNo().split(","));
            criteria.andIn("realOrderNo", realOrderNoList);
        }
        if (StringUtils.isNotBlank(search.getOrderNo())) {
            List<String> orderNoList = Lists.newArrayList(search.getOrderNo().split(","));
            criteria.andIn("preOrderNo", orderNoList);
        }
        if (StringUtils.isNotBlank(search.getSn())) {
            List<String> snList = Lists.newArrayList(search.getSn().split(","));
            criteria.andIn("sn", snList);
        }
        if (StringUtils.isNotBlank(search.getLicensePlate())) {
            List<String> lincensePlateList = Lists.newArrayList(search.getLicensePlate().split(","));
            criteria.andIn("licensePlate", lincensePlateList);
        }
        if (Objects.nonNull(search.getDeclareCompanyId())) {
            criteria.andEqualTo("declareCompanyId", search.getDeclareCompanyId());
        }
        example.setOrderByClause("create_time DESC");
        return example;
    }


    private List<Long> getIdListByEndorsementSns(List<String> snList) {
        List<Long> checklistId;
        //核注单号 转为 核放单id
        List<EndorsementChecklistMappingDTO> mappingDTOList = endorsementChecklistMappingBaseService.findByEndorsementSnList(snList);
        checklistId = mappingDTOList.stream().map(EndorsementChecklistMappingDTO::getChecklistId).collect(Collectors.toList());
        if (!Objects.equals(snList.size(), checklistId.size())) {
            // 从核注单补充
            List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemByEndorsementSns(snList);
            checklistId.addAll(endorsementItemDTOS.stream().map(EndorsementItemDTO::getChecklistId).collect(Collectors.toList()));
        }
        return checklistId.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 获取已绑定过的核注单id
     *
     * @param endorsemnetIdList 核注单id
     * @return 获取已绑定过的核注单id
     */
    @Override
    public List<Long> findBoundEndorsementIdByMapping(List<Long> endorsemnetIdList) {
        if (CollUtil.isEmpty(endorsemnetIdList)) {
            return new ArrayList<>();
        }
        List<EndorsementChecklistMappingDTO> mappingDTOList = endorsementChecklistMappingBaseService.findByEndorsementIdList(endorsemnetIdList);
        return mappingDTOList.stream().map(EndorsementChecklistMappingDTO::getEndorsementId)
                .filter(endorsemnetIdList::contains).collect(Collectors.toList());
    }

    /**
     * ("出入区标志 2入 1出")
     *
     * @param id
     * @return
     */
    @Override
    public CheckListViewResVo viewCheckList(Long id) {
        CheckListViewResVo checkListViewInfo = new CheckListViewResVo();
        ChecklistVO checklistVO = getChecklistVO(id);
        // 计算核注重量
        try {
            Tuple<BigDecimal, BigDecimal> decimalBigDecimalTuple = this.calculateTotalNetAndGrossWeight(id);
            checklistVO.setCalculateTotalNetWeight(decimalBigDecimalTuple.getF().toString());
            checklistVO.setCalculateTotalGrossWeight(decimalBigDecimalTuple.getS().toString());

        } catch (Exception e) {
            log.error("viewCheckList calculateTotalNetAndGrossWeight error={}", e.getMessage(), e);
        }
        List<EndorsementVO> endorsementVOList = getEndorsementVOList(id, checklistVO.getDeclareCompanyId());
        List<ChecklistItemDTO> checklistItemDTOList = checklistBaseService.listItemByChecklistId(id);
        checkListViewInfo.setChecklist(checklistVO);
        checkListViewInfo.setEndorsementVOList(endorsementVOList);
        checklistItemDTOList.forEach(c -> {
            if (Objects.nonNull(c.getDeclareUnit())) {
                CustomsUomDTO customsUomDTO = customsUomService.findByCode(c.getDeclareUnit());
                if (Objects.nonNull(customsUomDTO)) {
                    String declareUnit = c.getDeclareUnit();
                    c.setDeclareUnit(declareUnit + ":" + customsUomDTO.getName());
                }
            }
        });
        checkListViewInfo.setChecklistItemList(checklistItemDTOList);
        return checkListViewInfo;
    }

    private List<EndorsementVO> getEndorsementVOList(Long id, Long declareCompanyId) {
        List<EndorsementDTO> endorsementDTOList = new ArrayList<>();
        List<EndorsementChecklistMappingDTO> list = endorsementChecklistMappingBaseService.findByChecklistId(id);
        if (!CollectionUtils.isEmpty(list)) {
            List<Long> endorsementIdList = list.stream().map(EndorsementChecklistMappingDTO::getEndorsementId).collect(Collectors.toList());
            endorsementDTOList = endorsementService.findByIdList(endorsementIdList);
        }
        if (CollectionUtils.isEmpty(endorsementDTOList)) {
            endorsementDTOList = this.getEndorsementListByChecklistId(id);
        }
        if (CollectionUtils.isEmpty(endorsementDTOList)) {
            log.info("getEndorsementVOList 为空");
            return null;
        }
        List<EndorsementVO> endorsementVOList = new ArrayList<>();
        List<ChecklistAuthDTO> checklistAuthDTOList = checklistAuthService.findByBeAuthCompanyId(declareCompanyId, endorsementDTOList.get(0).getBussinessType());
        List<String> authRealOrderNos = checklistAuthDTOList.stream().flatMap(i -> i.getEndorsementRealOrderNoList().stream()).collect(Collectors.toList());
        for (EndorsementDTO dto : endorsementDTOList) {
            EndorsementVO vo = new EndorsementVO();
            BeanUtils.copyProperties(dto, vo);
            CustomsBookDTO customsBookDTO = baseDataService.getCustomsBookDTOById(dto.getAccountBookId());
            if (Objects.nonNull(customsBookDTO)) {
                vo.setCustomsBookNo(customsBookDTO.getBookNo());
            }
            vo.setCustomsStatusDesc(EndorsementCustomsStatus.getEnum(dto.getCustomsStatus()).getDesc());
            vo.setStatusDesc(EndorsementOrderStatus.getEnum(dto.getStatus()).getDesc());
            vo.setBussinessTypeDesc(EndorsementBussiness.getEnum(dto.getBussinessType()).getDesc());
            vo.setRealEndorsementOrderNo(dto.getRealOrderNo());
            if (authRealOrderNos.contains(dto.getRealOrderNo())) {
                vo.setSn(vo.getSn() + "【授权】");
            }
            endorsementVOList.add(vo);
        }
        return endorsementVOList;
    }

    private ChecklistVO getChecklistVO(Long id) {
        ChecklistDO checklistDO = checklistBaseService.findById(id);
        ChecklistVO checklistVO = new ChecklistVO();
        BeanUtils.copyProperties(checklistDO, checklistVO);
        checklistVO.setTypeDesc(ChecklistType.getEnum(checklistDO.getType()).getDesc());
        if (Objects.nonNull(checklistDO.getTotalGrossWeight())) {
            checklistVO.setTotalGrossWeight(checklistDO.getTotalGrossWeight().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.nonNull(checklistDO.getTotalNetWeight())) {
            checklistVO.setTotalNetWeight(checklistDO.getTotalNetWeight().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.nonNull(checklistDO.getCarWeight())) {
            checklistVO.setCarWeight(checklistDO.getCarWeight().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.nonNull(checklistDO.getFrameWeight())) {
            checklistVO.setFrameWeight(checklistDO.getFrameWeight().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.nonNull(checklistDO.getTotalWeight())) {
            checklistVO.setTotalWeight(checklistDO.getTotalWeight().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.nonNull(checklistDO.getContainerWt())) {
            checklistVO.setContainerWt(checklistDO.getContainerWt().setScale(5, RoundingMode.HALF_UP).toString());
        }
        if (Objects.equals(1, checklistDO.getIeFlag())) {
            checklistVO.setIeFlag("出区");
        }
        if (Objects.equals(2, checklistDO.getIeFlag())) {
            checklistVO.setIeFlag("入区");
        }
        checklistVO.setStatus(ChecklistStatusEnum.getEnum(checklistDO.getStatus()).getDesc());
        checklistVO.setDeclareCompany(baseDataService.getCompanyDTOById(checklistDO.getDeclareCompanyId()).getName());
        return checklistVO;
    }

    @Override
    public List<ChecklistItemDTO> listChecklistItemById(Long id) {
        List<ChecklistItemDTO> checklistItemDTOList = checklistBaseService.listItemByChecklistId(id);
        return checklistItemDTOList;
    }

    /**
     * 1。先查关联核注单的数量
     * 2。再查这个序号对应的品
     * 3。去核注单表体里面取出这个品
     *
     * @param reqVo
     * @return
     * @throws ArgsErrorException
     */
    @Override
    public ChecklistAssociateItemResVo associateItemBySerialNumber(ChecklistAssociateItemReqVo reqVo) throws ArgsErrorException {
        String validateResult = ValidatorUtils.doValidator(validator, reqVo);
        if (!StringUtils.isEmpty(validateResult)) {
            throw new ArgsErrorException(validateResult);
        }
        Long checklistId = reqVo.getChecklistId();
        Long endorsementId = reqVo.getEndorsementId();
        Integer serialNumber = reqVo.getSerialNumber();
        ChecklistDO checklistDO = checklistBaseService.findById(checklistId);
        Long accountBookId = checklistDO.getAccountBookId();
        if (accountBookId == null) {
            throw new ArgsErrorException("核放单关联的账册号为空");
        }
        this.checkBindEndorsementCount(checklistId, checklistDO);
        ChecklistAssociateItemResVo checklistAssociateItemResVo = this.getChecklistAssociateItemResVo(checklistId, endorsementId, serialNumber, accountBookId);
        return checklistAssociateItemResVo;
    }

    /**
     * 检查关联核注单数量
     *
     * @param checklistId
     * @param checklistDO
     */
    private void checkBindEndorsementCount(Long checklistId, ChecklistDO checklistDO) {
        List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(checklistId);
        if (endorsementDTOList.isEmpty()) {
            throw new ArgsErrorException("新增核放单表体失败，未绑定核注清单");
        }
        if (endorsementDTOList.size() > 1) {
            StringBuffer sb = new StringBuffer();
            sb.append("核注单" + checklistDO.getSn() + "关联核注单号过多:");
            endorsementDTOList.forEach(e -> sb.append(e.getSn() + ","));
            log.warn(sb.toString());
            throw new ArgsErrorException(sb.toString());
        }
    }

    private ChecklistAssociateItemResVo getChecklistAssociateItemResVo(Long checklistId, Long endorsementId, Integer serialNumber, Long accountBookId) throws ArgsErrorException {
        //通过核注单id和序号获取表体
        EndorsementItemDTO endorsementItem = endorsementService.getEndorsementItem(endorsementId, serialNumber);
        if (Objects.isNull(endorsementItem)) {
            throw new ArgsErrorException("新增核放单表体失败，输入的关联商品序号不存在");
        }
        List<ChecklistItemDO> checklistItemDOList = checklistBaseService.findByEndorsementItemId(checklistId, endorsementItem.getId());
        if (checklistItemDOList.size() > 0) {
            throw new ArgsErrorException("新增核放单表体失败，关联商品序号" + serialNumber + "已存在");
        }
//        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(accountBookId, endorsementItem.getProductId());
//        if (Objects.isNull(goodsRecordDTO)) {
//            InventoryOrderItemDTO inventoryItemDTO = inventoryOrderInfoService.findByEndorsementIdAndProductId(endorsementId, endorsementItem.getProductId());
//            if (Objects.nonNull(inventoryItemDTO) && Objects.nonNull(inventoryItemDTO.getOriginProductId())) {
//                goodsRecordDTO = goodsRecordService.findByBookIdAndProId(accountBookId, inventoryItemDTO.getOriginProductId());
//            }
//        }
        ChecklistAssociateItemResVo checklistAssociateItemResVo = new ChecklistAssociateItemResVo();
        checklistAssociateItemResVo.setSerialNumber(endorsementItem.getSerialNumber())
                .setEndorsementItemId(endorsementItem.getId())
                .setProductId(endorsementItem.getProductId())
                .setGoodsName(endorsementItem.getGoodsName())
                .setAllDeclareUnitQfy(endorsementItem.getRemainDeclareUnitQfy())
                .setNetWeight(endorsementItem.getNetWeight().divide(endorsementItem.getDeclareUnitQfy(), 3, RoundingMode.HALF_UP))
//                .setGoodsRecordId(goodsRecordDTO.getId())
        ;
        // 商品序号为空的是一线入境的 没有序号
        if (!Objects.isNull(endorsementItem.getGoodsSeqNo())) {
            CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(accountBookId, endorsementItem.getGoodsSeqNo(), endorsementItem.getProductId());
            if (!Objects.isNull(customsBookItemDTO)) {
                checklistAssociateItemResVo.setCustomsBookItemId(customsBookItemDTO.getId());
            }
        }
        return checklistAssociateItemResVo;
    }

    /**
     * 通过endorsement-checklist-mapping 获取核放单关联的核注单
     *
     * @param checklistId
     * @return
     */
    @Override
    public List<EndorsementDTO> getEndorsementListByChecklistId(Long checklistId) {
        List<EndorsementDTO> endorsementDTOList = endorsementChecklistMappingBaseService.getEndorsementByChecklistId(checklistId);
        if (CollectionUtils.isEmpty(endorsementDTOList)) {
            return this.endorsementListByChecklistId(checklistId);
        }
        return endorsementDTOList;
    }


    @Override
    public List<ChecklistDTO> checklistByEndorsementId(Long id) {
        List<ChecklistDTO> list = new ArrayList<>();
        List<EndorsementChecklistMappingDTO> dtoList = endorsementChecklistMappingBaseService.findByEndorsementId(id);
        if (CollectionUtils.isEmpty(dtoList)) {
            return list;
        }
        List<Long> idList = dtoList.stream().map(EndorsementChecklistMappingDTO::getChecklistId).collect(Collectors.toList());
        return this.findByIdList(idList);
    }

    /**
     * 核放单手动修改状态
     * todo 判断修改状态类型及原因
     *
     * @param checklistDTO 修改状态及回执信息的核放单DTO
     */
    @Override
    public void manualUpdStatus(ChecklistDTO checklistDTO) {
        Integer userId = UserUtils.getUserId();
        ChecklistDO checklistDO = new ChecklistDO();
        BeanUtils.copyProperties(checklistDTO, checklistDO);
        checklistDO.setUpdateBy(userId);
        checklistDO.setUpdateTime(new Date());
        checklistMapper.updateByPrimaryKeySelective(checklistDO);

        checklistTrackLogService.saveLog(checklistDTO.getId(), checklistDO.getStatus(),
                "核放单手动操作修改成功: " + ChecklistStatusEnum.getEnum(checklistDO.getStatus()).getDesc());
    }


    @Override
    public Map<Long, ChecklistDTO> getChecklistByEndorsementListSecondOut(List<Long> endorsementIdList) {
        Map<Long, Long> endorsementIdChecklistIdMap = endorsementItemBaseService.getChecklistIdByEndorsementSecondOut(endorsementIdList);
        Map<Long, ChecklistDTO> result = new HashMap<>();
        if (CollectionUtils.isEmpty(endorsementIdChecklistIdMap)) {
            return result;
        }
        List<Long> checklistIdList = endorsementIdChecklistIdMap.values().stream().collect(Collectors.toList());
        List<ChecklistDTO> checklistDTOS = this.findByIdList(checklistIdList);
        Map<Long, ChecklistDTO> checklistIdMap = checklistDTOS.stream().distinct().collect(Collectors.toMap(ChecklistDTO::getId, Function.identity()));
        Iterator<Map.Entry<Long, Long>> iterator = endorsementIdChecklistIdMap.entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Long, Long> next = iterator.next();
            Long endorsementId = next.getKey();
            Long checklistId = next.getValue();
            if (checklistIdMap.containsKey(checklistId)) {
                ChecklistDTO checklistDTO = checklistIdMap.get(checklistId);
                result.put(endorsementId, checklistDTO);
            }
        }
        return result;
    }

    public Map<Long, List<ChecklistDTO>> getChecklistByEndorsementList(List<Long> endorsementIdList) {
        Map<Long, List<ChecklistDTO>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(endorsementIdList)) {
            log.warn("getChecklistByEndorsementList - endorsementIdList is null");
            return result;
        }
        List<EndorsementItemDO> endorsementItemDOList = endorsementItemBaseService.getChecklistIdByEndorsement(endorsementIdList);
        //获取所有核放单id
        Set<Long> checklistIdList = endorsementItemDOList.stream().map(EndorsementItemDO::getChecklistId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(checklistIdList)) {
            log.warn("getChecklistByEndorsementList - 核放单不存在");
            return result;
        }
        // 查询映射表关联关系
        List<EndorsementChecklistMappingDTO> mappingDTOList = endorsementChecklistMappingBaseService.findByEndorsementIdList(endorsementIdList);
        Map<Long, Set<Long>> endorsementIdMapping = new HashMap<>();
        if (CollUtil.isNotEmpty(mappingDTOList)) {
            endorsementIdMapping.putAll(mappingDTOList.stream()
                    .collect(Collectors.groupingBy(EndorsementChecklistMappingDTO::getEndorsementId,
                            Collectors.mapping(EndorsementChecklistMappingDTO::getChecklistId, Collectors.toSet()))));
            checklistIdList.addAll(mappingDTOList.stream().map(EndorsementChecklistMappingDTO::getChecklistId).collect(Collectors.toSet()));
        }
        List<ChecklistDTO> checklistDTOList = this.findByIdList(new ArrayList<>(checklistIdList));
        // 核放id-核放单dto
        Map<Long, ChecklistDTO> idChecklistMap = checklistDTOList.stream().collect(Collectors.toMap(ChecklistDTO::getId, Function.identity()));
        // 核注id-核放单id
        Map<Long, Set<Long>> collect = endorsementItemDOList.stream()
                .collect(Collectors.groupingBy(EndorsementItemDO::getEndorsementId,
                        Collectors.mapping(EndorsementItemDO::getChecklistId, Collectors.toSet())));
        collect.forEach((endorsementId, checklistIds) -> {
            List<ChecklistDTO> list = new ArrayList<>();
            // 映射表补充
            if (endorsementIdMapping.containsKey(endorsementId)) {
                checklistIds.addAll(endorsementIdMapping.get(endorsementId));
            }
            checklistIds.forEach(id -> {
                if (idChecklistMap.containsKey(id)) {
                    ChecklistDTO checklistDTO = idChecklistMap.get(id);
                    list.add(checklistDTO);
                }
            });
            result.put(endorsementId, list);
        });

//        endorsementItemDOList.forEach(e -> {
//            Long endorsementId = e.getEndorsementId();
//            List<ChecklistDTO> list = new ArrayList<>();
//            if (collect.containsKey(endorsementId)) {
//                Set<Long> cl = collect.get(endorsementId);
//                cl.forEach(c -> {
//                    if (idChecklistMap.containsKey(c)) {
//                        ChecklistDTO checklistDTO = idChecklistMap.get(c);
//                        list.add(checklistDTO);
//                    }
//                });
//            }
//            result.put(endorsementId, list);
//        });
        return result;
    }

    @Override
    public Map<String, CustomsCompleteCountDTO> selectChecklistBussinessCountDetail(List<String> handlerIdList, Long beginTime, Long endTime) {
        ConcurrentMap<String, CustomsCompleteCountDTO> result = new ConcurrentHashMap<>();
        Example example = new Example(ChecklistDO.class);
        Example.Criteria unionCriteria = example.createCriteria();
        unionCriteria.andLessThanOrEqualTo("updateTime", new DateTime(endTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andGreaterThanOrEqualTo("updateTime", new DateTime(beginTime).toString("yyyy-MM-dd HH:mm:ss"));
        unionCriteria.andEqualTo("status", ChecklistStatusEnum.FINISH.getCode()).andEqualTo("deleted", false);

        List<CompletableFuture<Void>> futureList = new ArrayList<>();
        for (String handlerId : handlerIdList) {
            Runnable runnable = new Task(handlerId, unionCriteria, result);
            futureList.add(CompletableFuture.runAsync(runnable, checklistCountTaskExecutor));
        }
        try {
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
        } catch (Exception e) {
            log.error("清关完成-核放单统计组合CompletableFuture结果异常：{}", e.getMessage(), e);
            throw new RuntimeException("清关完成-核放单统计组合CompletableFuture结果异常" + e.getMessage(), e);
        }
        return result;
    }

    @Override
    public Map<String, Integer> countPagingStatus(ChecklistSearch search) {
        Map<String, Integer> result = new LinkedHashMap<>();
        for (ChecklistStatusEnum statusEnum : ChecklistStatusEnum.values()) {
            if (statusEnum.equals(ChecklistStatusEnum.NULL)) {
                continue;
            }
            search.setStatus(statusEnum.getCode());
            Example example = this.buildExample(search);
            int count = checklistMapper.selectCountByExample(example);
            result.put(statusEnum.getCode(), count);
        }
        return result;
    }

    /**
     * 核注单创建核放单
     *
     * @param submit
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createChecklist(ChecklistSubmit submit) {
        Long userId = UserUtils.getUserId().longValue();
        List<Long> endorsementIdList = submit.getEndorsementIdList();
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(endorsementIdList);
        List<Integer> ieFlag = endorsementDTOList.stream().map(EndorsementDTO::getIeFlag).distinct().collect(Collectors.toList());
        if (ieFlag.size() > 1) {
            throw new ArgsErrorException("核注单出入区标志存在多个");
        }
        if (CollectionUtils.isEmpty(ieFlag)) {
            throw new ArgsErrorException("出区标志不存在");
        }
        if (Objects.equals(submit.getBindType(), ChecklistBindType.NULL.getCode())) {
            throw new ArgsErrorException("绑定类型不能为无");
        }
        submit.setIeFlag(ieFlag.get(0));
        Long checklistId = checklistService.create(submit, userId);
        submit.setId(checklistId);
        checklistService.bindEndorsement(submit);
    }

    @Override
    public List<SelectOptionVO<Integer>> listTypeByEndorsement(String endorsementType) {
        Map<Integer, EndorsementBussiness[]> checklistTypeMap = ChecklistType.getCheckListTypeMapEndorsementBussiness();
        EndorsementBussiness endorsementBussiness = EndorsementBussiness.getEnum(endorsementType);
        List<SelectOptionVO<Integer>> result = new ArrayList<>();
        for (Map.Entry<Integer, EndorsementBussiness[]> entry : checklistTypeMap.entrySet()) {
            EndorsementBussiness[] value = entry.getValue();
            if (Arrays.asList(value).contains(endorsementBussiness)) {
                ChecklistType checklistType = ChecklistType.getEnum(entry.getKey());
                SelectOptionVO<Integer> selectOptionVO = new SelectOptionVO<>();
                selectOptionVO.setId(checklistType.getCode());
                selectOptionVO.setName(checklistType.getDesc());
                result.add(selectOptionVO);
            }
        }
        return result;
    }

    @Override
    public List<ChecklistTrackLogDTO> listChecklistTrackLog(Long checklistId) {
        List<ChecklistTrackLogDO> checklistTrackLogDOS = checklistTrackLogService.listByChecklistId(checklistId);
        return checklistTrackLogDOS.stream().map(i -> {
            ChecklistTrackLogDTO checklistTrackLogDTO = new ChecklistTrackLogDTO();
            BeanUtils.copyProperties(i, checklistTrackLogDTO);
            checklistTrackLogDTO.setStatusDesc(ChecklistStatusEnum.getEnum(i.getStatus()).getDesc());
            return checklistTrackLogDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SelectOptionVO<Integer>> listTypeByEndorsement(String endorsementType, Long endorsementId) {
        if (EndorsementBussiness.BUSSINESS_SECTION_IN.getCode().equals(endorsementType)) {
            // 区间流转入 + 特殊综保区 ==> 一线入区
            EndorsementDTO endorsementDTO = endorsementService.findById(endorsementId);
            if (Objects.nonNull(endorsementDTO)) {
                InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(endorsementDTO.getInventoryOrderId());
                if (Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey().equalsIgnoreCase(inventoryOrderInfoDTO.getTransportMode())) {
                    SelectOptionVO<Integer> selectOptionVO = new SelectOptionVO<>();
                    selectOptionVO.setId(ChecklistType.FIRST.getCode());
                    selectOptionVO.setName(ChecklistType.FIRST.getDesc());
                    return Collections.singletonList(selectOptionVO);
                }
            }
        }
        List<SelectOptionVO<Integer>> list = this.listTypeByEndorsement(endorsementType);
        if (EndorsementBussiness.BUSSINESS_SECTION_IN.getCode().equals(endorsementType)) {
            // 区间流转入 非 特殊综保区 过滤 一线入区
            list.removeIf(i -> Objects.equals(i.getId(), ChecklistType.FIRST.getCode()));
        }
        return list;
    }

    @Override
    @Transactional
    public void handlerCheck(List<Long> idList) {
        for (Long id : idList) {
            this.updateStatus(id, ChecklistStatusEnum.FINISH.getCode());
            this.updateCustomsStatus(id, CheckListCustomsStatusEnum.SAS223_GUOKA.getCode());
            checklistTrackLogService.saveLog(id, ChecklistStatusEnum.FINISH.getCode(), "核放单手动过卡");
        }
    }

    class Task extends TraceDataRunnable {

        private String handlerId;

        private Example.Criteria unionCriteria;

        private ConcurrentMap<String, CustomsCompleteCountDTO> result;

        public Task(String handlerId, Example.Criteria unionCriteria, ConcurrentMap<String, CustomsCompleteCountDTO> result) {
            super();
            this.handlerId = handlerId;
            this.unionCriteria = unionCriteria;
            this.result = result;
        }

        @Override
        public void proxy() {
            CustomsCompleteCountDTO countDTO = new CustomsCompleteCountDTO();
            //一线入境统计
            Example example0 = new Example(ChecklistDO.class);
            example0.createCriteria().andEqualTo("endorsementType", InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example0.and(unionCriteria);
            countDTO.setOneLineIn(checklistMapper.selectCountByExample(example0));
            //区间流转入
            Example example1 = new Example(ChecklistDO.class);
            example1.createCriteria().andEqualTo("endorsementType", InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example1.and(unionCriteria);
            countDTO.setSectionIn(checklistMapper.selectCountByExample(example1));

            //区间流转出
            Example example3 = new Example(ChecklistDO.class);
            example3.createCriteria().andEqualTo("endorsementType", InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example3.and(unionCriteria);
            countDTO.setSectionOut(checklistMapper.selectCountByExample(example3));

            //销毁
            Example example6 = new Example(ChecklistDO.class);
            example6.createCriteria().andEqualTo("endorsementType", InventoryOrderBusinessEnum.BUSSINESS_DESTORY.getCode())
                    .andEqualTo("updateBy", Integer.valueOf(handlerId));
            example6.and(unionCriteria);
            countDTO.setDestroy(checklistMapper.selectCountByExample(example6));

            result.put(handlerId, countDTO);
        }
    }

    /**
     * 从核注单表体查
     *
     * @param id
     * @return
     */
    private List<EndorsementDTO> endorsementListByChecklistId(Long id) {
        List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemByChecklist(id);
        List<Long> idList = endorsementItemDTOS.stream().map(EndorsementItemDTO::getEndorsementId).distinct().collect(Collectors.toList());
        List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(idList);
        return endorsementDTOList;
    }


    @Override
    public ChecklistAssociateItemResVo viewChecklistItem(Long checklistItemId) throws ArgsErrorException {
        ChecklistItemDTO dto = checklistBaseService.checklistItemById(checklistItemId);
        if (Objects.isNull(dto)) {
            throw new ArgsErrorException("未找到关联核放单表体信息");
        }
        ChecklistDO checklistDO = checklistBaseService.findById(dto.getChecklistId());
        if (Objects.isNull(checklistDO)) {
            throw new ArgsErrorException("未找到关联核放单信息");
        }
        EndorsementItemDTO endorsementItemDTO = endorsementService.findItemById(dto.getEndorsementItemId());
        if (Objects.isNull(endorsementItemDTO)) {
            throw new ArgsErrorException("未找到关联核注单表体信息");
        }
//        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(checklistDO.getAccountBookId(), dto.getProductId());
//        if (Objects.isNull(goodsRecordDTO)) {
//            throw new ArgsErrorException("未找到关联商品备案信息");
//        }
        ChecklistAssociateItemResVo resVo = new ChecklistAssociateItemResVo();
        resVo.setChecklistItemId(checklistItemId)
                .setSerialNumber(dto.getEndorsementSerialNumber())
                .setEndorsementItemId(dto.getEndorsementItemId())
                .setCustomsBookItemId(dto.getCustomsBookItemId())
                .setSerialNumber(dto.getEndorsementSerialNumber())
                .setProductId(dto.getProductId())
                .setTotalNetWeight(dto.getNetWeight())
                .setTotalGrossWeight(dto.getGrossWeight())
                .setNetWeight(endorsementItemDTO.getNetWeight().divide(endorsementItemDTO.getDeclareUnitQfy(), 3, RoundingMode.HALF_UP))
                .setGoodsName(dto.getGoodsName())
                .setDeclareUnitQfy(dto.getDeclareUnitQfy())
                .setAllDeclareUnitQfy(endorsementItemDTO.getRemainDeclareUnitQfy());
//                .setGoodsRecordId(goodsRecordDTO.getId());
        // 商品序号为空的是一线入境的 没有序号
        if (!Objects.isNull(dto.getGoodsSeqNo())) {
            CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(checklistDO.getAccountBookId(), dto.getGoodsSeqNo(), dto.getProductId());
            if (!Objects.isNull(customsBookItemDTO)) {
                resVo.setCustomsBookItemId(customsBookItemDTO.getId());
            }
        }
        return resVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteChecklistItem(Long id) throws ArgsErrorException {
        ChecklistItemDTO checklistItemDTO = checklistBaseService.checklistItemById(id);
        if (Objects.isNull(checklistItemDTO)) {
            throw new ArgsErrorException("未找到核放单表体");
        }
        EndorsementItemDTO endorsementItemDTO = endorsementService.findItemById(checklistItemDTO.getEndorsementItemId());
        if (Objects.isNull(endorsementItemDTO)) {
            throw new ArgsErrorException("未找到核注单表体");
        }
        BigDecimal remain = endorsementItemDTO.getRemainDeclareUnitQfy().add(new BigDecimal(checklistItemDTO.getDeclareUnitQfy()));
        this.updateEndorsementItem(remain, endorsementItemDTO);

        checklistBaseService.deleteChecklistItem(id);
        log.info("删除核放单表体成功 刷新重量 checklistId={}", checklistItemDTO.getChecklistId());
        ChecklistDTO checklistDTO = this.findById(checklistItemDTO.getChecklistId());
        checklistTrackLogService.saveLog(checklistDTO.getId(), checklistDTO.getStatus(), "删除核放单表体", JSON.toJSONString(checklistItemDTO));
//        this.refreshTotalWeight(checklistItemDTO.getChecklistId(), BigDecimal.ZERO, BigDecimal.ZERO);
    }

    /**
     * @param reqVo
     * @throws ArgsErrorException
     * @description 新增/编辑 核放单表体
     * <p>
     * 1.校验核注表体剩余库存
     * 2.保存核放单表体
     * 3.计算货物总毛重、总净值
     */
    @Override
    @Transactional(rollbackFor = ArgsErrorException.class)
    public synchronized void saveChecklistItem(ChecklistItemSaveReqVo reqVo) throws ArgsErrorException {
        this.saveItemProxy(reqVo);
//        this.refreshTotalWeight(reqVo.getChecklistId());
    }

    private void refreshTotalWeight(Long id) {
        refreshTotalWeight(id, null, null);
    }

    private Tuple<BigDecimal, BigDecimal> calculateTotalNetAndGrossWeight(Long checklistId) {
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        ChecklistDTO checklistDTO = checklistService.findById(checklistId);
        ChecklistBindType checklistBindType = ChecklistBindType.getEnum(checklistDTO.getBindType());
        if (Objects.equals(checklistBindType, ChecklistBindType.ONE_TICKET_MULTI_CAR)) {
            List<ChecklistItemDO> checklistItemDTOList = checklistBaseService.listByChecklistId(checklistDTO.getId());
            if (!CollectionUtils.isEmpty(checklistItemDTOList)) {
                for (ChecklistItemDO c : checklistItemDTOList) {
                    totalNetWeight = totalNetWeight.add(c.getNetWeight());
                    totalGrossWeight = totalGrossWeight.add(c.getGrossWeight());
                }
            }
        } else if (Objects.equals(checklistBindType, ChecklistBindType.MULTI_TICKET_ONE_CAR) ||
                Objects.equals(checklistBindType, ChecklistBindType.ONE_TICKET_ONE_CAR)) {
            List<EndorsementItemDTO> endorsementItemDTOList = new ArrayList<>();
            List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(checklistDTO.getId());
            if (!CollectionUtils.isEmpty(endorsementDTOList)) {
                endorsementDTOList.forEach(e -> endorsementItemDTOList.addAll(endorsementService.listItemById(e.getId())));
            }
            if (!CollectionUtils.isEmpty(endorsementItemDTOList)) {
                for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                    totalNetWeight = totalNetWeight.add(endorsementItemDTO.getNetWeight());
                    totalGrossWeight = totalGrossWeight.add(endorsementItemDTO.getGrossWeight());
                }
            }
        }
        //统一格式化为5位
        totalNetWeight = totalNetWeight.setScale(5, RoundingMode.HALF_UP);
        totalGrossWeight = totalGrossWeight.setScale(5, RoundingMode.HALF_UP);
        log.info("calculateTotalNetAndGrossWeight checklistId={} totalNetWeight={} totalGrossWeight={}", checklistId, totalNetWeight, totalGrossWeight);
        return new Tuple<>(totalNetWeight, totalGrossWeight);
    }

    /**
     * 刷新核放单总重量
     *
     * @param checklistId
     * @param enterTotalGrossWeight 界面输入的总毛重
     * @param enterTotalNetWeight   界面输入的总净重
     */
    private void refreshTotalWeight(Long checklistId, BigDecimal enterTotalGrossWeight, BigDecimal enterTotalNetWeight) {
        ChecklistDTO checklistDTO = checklistService.findById(checklistId);
        Boolean isGrossAutofill = Boolean.FALSE;
        Boolean isNetAutofill = Boolean.FALSE;
        if (Objects.isNull(enterTotalGrossWeight)) {
            isGrossAutofill = true;
        }
        if (Objects.isNull(enterTotalNetWeight)) {
            isNetAutofill = true;
        }
        //有1个符合条件就去数据库查询填充重量
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        if (isGrossAutofill || isNetAutofill) {
            ChecklistBindType checklistBindType = ChecklistBindType.getEnum(checklistDTO.getBindType());
            if (Objects.equals(checklistBindType, ChecklistBindType.ONE_TICKET_MULTI_CAR)) {
                List<ChecklistItemDO> checklistItemDTOList = checklistBaseService.listByChecklistId(checklistDTO.getId());
                if (!CollectionUtils.isEmpty(checklistItemDTOList)) {
                    for (ChecklistItemDO c : checklistItemDTOList) {
                        totalNetWeight = totalNetWeight.add(c.getNetWeight().multiply(BigDecimal.valueOf(c.getDeclareUnitQfy())));
                    }
                }
            } else if (Objects.equals(checklistBindType, ChecklistBindType.MULTI_TICKET_ONE_CAR) ||
                    Objects.equals(checklistBindType, ChecklistBindType.ONE_TICKET_ONE_CAR)) {
                List<EndorsementItemDTO> endorsementItemDTOList = new ArrayList<>();
                List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(checklistDTO.getId());
                if (!CollectionUtils.isEmpty(endorsementDTOList)) {
                    endorsementDTOList.forEach(e -> endorsementItemDTOList.addAll(endorsementService.listItemById(e.getId())));
                }
                if (!CollectionUtils.isEmpty(endorsementItemDTOList)) {
                    for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                        totalNetWeight = totalNetWeight.add(endorsementItemDTO.getNetWeight().multiply(endorsementItemDTO.getDeclareUnitQfy()));
                    }
                }
            }
        }


        ChecklistDO checklistDO = new ChecklistDO();
        checklistDO.setId(checklistDTO.getId());
        if (!isGrossAutofill) {
            checklistDO.setTotalGrossWeight(totalGrossWeight.setScale(3, RoundingMode.HALF_UP));
        }
        if (!isNetAutofill) {
            checklistDO.setTotalNetWeight(totalNetWeight.setScale(3, RoundingMode.HALF_UP));
        }
        checklistDO.setTotalWeight(totalGrossWeight.add(checklistDTO.getCarWeight()).add(checklistDTO.getFrameWeight()));
        UserUtils.setUpdateBy(checklistDO);
        checklistDO.setUpdateTime(new Date());
        log.info("refreshTotalWeight checklistDO={}", JSON.toJSONString(checklistDO));
        checklistMapper.updateByPrimaryKeySelective(checklistDO);
    }


    private void saveItemProxy(ChecklistItemSaveReqVo reqVo) throws ArgsErrorException {
        List<EndorsementDTO> endorsementDTOList = this.getEndorsementListByChecklistId(reqVo.getChecklistId());
        if (CollectionUtils.isEmpty(endorsementDTOList)) {
            throw new ArgsErrorException("核放单当前未关联核注单");
        }
        if (endorsementDTOList.size() > 1) {
            throw new ArgsErrorException("核放单关联核注单过多");
        }
        EndorsementItemDTO endorsementItemDTO = endorsementService.findItemById(reqVo.getEndorsementItemId());
//        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(reqVo.getGoodsRecordId());
//        goodsRecordDTO.setProductId(endorsementItemDTO.getProductId());
//        List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.findByIdAndProductId(reqVo.getEndorsementId(), goodsRecordDTO.getProductId());
//        if (endorsementItemDTOS.size() > 1) {
//            log.warn("核注单表体已存在相同料号");
//            throw new ArgsErrorException("核注单表体中已存在相同料号");
//        }
        if (Objects.isNull(reqVo.getChecklistItemId())) {
            List<ChecklistItemDO> checklistItemDOList = checklistBaseService.findByEndorsementItemId(reqVo.getChecklistId(), reqVo.getEndorsementItemId());
            if (checklistItemDOList.size() > 0) {
                throw new ArgsErrorException("新增核放单表体失败，关联商品序号" + reqVo.getSerialNumber() + "已存在");
            }
            insertChecklistItem(reqVo, endorsementItemDTO, null);
        } else {
            updateChecklistItem(reqVo, endorsementItemDTO);
        }
        ChecklistDTO checklistDTO = this.findById(reqVo.getChecklistId());
        checklistTrackLogService.saveLog(checklistDTO.getId(), checklistDTO.getStatus(), "保存核放单表体", JSON.toJSONString(reqVo));
    }

    private void updateChecklistItem(ChecklistItemSaveReqVo reqVo, EndorsementItemDTO endorsementItemDTO) throws ArgsErrorException {
        ChecklistItemDO checklistItemDO = checklistItemMapper.selectByPrimaryKey(reqVo.getChecklistItemId());
        int remain = endorsementItemDTO.getRemainDeclareUnitQfy().intValue();
        remain = remain + checklistItemDO.getDeclareUnitQfy() - reqVo.getDeclareUnitQfy();
        if (remain < 0) {
            throw new ArgsErrorException("序号:" + reqVo.getSerialNumber() + "商品的申报数量超过剩余数量");
        }
        checklistItemDO.setDeclareUnitQfy(reqVo.getDeclareUnitQfy());
        checklistItemDO.setGrossWeight(reqVo.getTotalGrossWeight());
        checklistItemDO.setNetWeight(reqVo.getTotalNetWeight());
        UserUtils.setUpdateBy(checklistItemDO);
        checklistItemDO.setUpdateTime(new Date());
        checklistItemMapper.updateByPrimaryKey(checklistItemDO);
        updateEndorsementItem(BigDecimal.valueOf(remain), endorsementItemDTO);
    }


    /**
     * 先计算剩余数量是否小于0 在更新的时候做乐观锁控制
     *
     * @param reqVo
     * @param endorsementItemDTO
     * @param goodsRecordDTO
     * @throws ArgsErrorException
     */
    private void insertChecklistItem(ChecklistItemSaveReqVo reqVo, EndorsementItemDTO endorsementItemDTO, GoodsRecordDTO goodsRecordDTO) throws ArgsErrorException {
        log.info("insertChecklistItem => ChecklistItemSaveReqVo:{},", JSON.toJSONString(reqVo));
        // 如果剩余数量减去占用数量小于0 就抛异常
        String validateResult = ValidatorUtils.doValidator(validator, reqVo);
        if (!StringUtils.isEmpty(validateResult)) {
            throw new ArgsErrorException(validateResult);
        }
        if (Objects.isNull(endorsementItemDTO.getRemainDeclareUnitQfy())) {
            throw new ArgsErrorException("核注表体剩余数量为空");
        }
        if (Objects.equals(endorsementItemDTO.getRemainDeclareUnitQfy(), BigDecimal.ZERO)) {
            throw new ArgsErrorException("核注表体剩余数量不足");
        }
        BigDecimal remain = endorsementItemDTO.getRemainDeclareUnitQfy().subtract(BigDecimal.valueOf(reqVo.getDeclareUnitQfy()));
        if (remain.intValue() < 0) {
            throw new ArgsErrorException("核注表体剩余数量不足");
        }
        insertItem(reqVo, endorsementItemDTO);
        updateEndorsementItem(remain, endorsementItemDTO);
    }


    /**
     * 插入表体表
     *
     * @param reqVo
     * @param goodsRecordDTO
     */
    private void insertItem(ChecklistItemSaveReqVo reqVo, EndorsementItemDTO endorsementItemDO) {
        ChecklistItemDO itemDO = new ChecklistItemDO();
        itemDO.setChecklistId(reqVo.getChecklistId());
        itemDO.setEndorsementId(reqVo.getEndorsementId());
        itemDO.setEndorsementItemId(reqVo.getEndorsementItemId());
        itemDO.setEndorsementSerialNumber(reqVo.getSerialNumber());
        itemDO.setProductId(endorsementItemDO.getProductId());
        itemDO.setGoodsName(endorsementItemDO.getGoodsName());
        itemDO.setHsCode(endorsementItemDO.getHsCode());
        if (Objects.nonNull(endorsementItemDO.getExtraJson()) && StringUtils.isNotEmpty(endorsementItemDO.getExtraJson())) {
            EndorsementExtraDO endorsementExtraDO = JSON.parseObject(endorsementItemDO.getExtraJson(), EndorsementExtraDO.class);
            if (Objects.nonNull(endorsementExtraDO)) {
                itemDO.setDeclareUnit(endorsementExtraDO.getDeclareUnit());
            } else {
                throw new ArgsErrorException("核注单表体中未获取到申报单价");
            }
        } else {
            EndorsementDTO endorsementDTO = endorsementService.findById(endorsementItemDO.getEndorsementId());
            if (Objects.nonNull(endorsementDTO)) {
                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(endorsementDTO.getAccountBookId(), endorsementItemDO.getProductId());
                if (Objects.isNull(goodsRecordDTO)) {
                    List<RecordWarehouseProductIdDTO> recordWarehouseProductIdDTOS = goodsRecordService.findByCustomsDeclareProductIdAndCustomsBook(endorsementItemDO.getProductId(), endorsementDTO.getAccountBookId());
                    if (!CollectionUtils.isEmpty(recordWarehouseProductIdDTOS)) {
                        RecordWarehouseProductIdDTO recordWarehouseProductIdDTO = recordWarehouseProductIdDTOS.get(recordWarehouseProductIdDTOS.size() - 1);
                        Long recordId = recordWarehouseProductIdDTO.getRecordId();
                        goodsRecordDTO = goodsRecordService.findById(recordId);
                        if (Objects.nonNull(goodsRecordDTO)) {
                            itemDO.setDeclareUnit(goodsRecordDTO.getDeclareUnit());
                        }
                    }
                } else {
                    itemDO.setDeclareUnit(goodsRecordDTO.getDeclareUnit());
                }
            }
            if (Objects.isNull(itemDO.getDeclareUnit())) {
                throw new ArgsErrorException("核注单表体中通过料号未查询到备案");
            }
        }
        if (!Objects.isNull(reqVo.getCustomsBookItemId())) {
            CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(reqVo.getCustomsBookItemId());
            if (!Objects.isNull(customsBookItemDTO)) {
                itemDO.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                itemDO.setGoodsName(customsBookItemDTO.getGoodsName());
                itemDO.setHsCode(customsBookItemDTO.getHsCode());
                itemDO.setDeclareUnit(customsBookItemDTO.getGoodsUnit());
            }
        }
        itemDO.setDeclareUnitQfy(reqVo.getDeclareUnitQfy());
        itemDO.setGrossWeight(reqVo.getTotalGrossWeight());
        itemDO.setNetWeight(reqVo.getTotalNetWeight());
        itemDO.setRemark(reqVo.getRemark());
        UserUtils.setCreateAndUpdateBy(itemDO);
        checklistBaseService.insertChecklistItem(itemDO);
    }

    /**
     * 更新的时候做一个对剩余库存的乐观锁的判断
     *
     * @param remain
     * @param endorsementItemDTO
     * @throws ArgsErrorException
     */
    private void updateEndorsementItem(BigDecimal remain, EndorsementItemDTO endorsementItemDTO) throws ArgsErrorException {
        EndorsementItemDO endorsementItemDO = new EndorsementItemDO();
        endorsementItemDO.setRemainDeclareUnitQfy(remain);
        Example example = new Example(EndorsementItemDO.class);
        example.createCriteria().andEqualTo("id", endorsementItemDTO.getId()).andEqualTo("remainDeclareUnitQfy", endorsementItemDTO.getRemainDeclareUnitQfy());
        UserUtils.setUpdateBy(endorsementItemDO);
        endorsementItemDO.setUpdateTime(new Date());
        int i = endorsementItemMapper.updateByExampleSelective(endorsementItemDO, example);
        if (i != 1) {
            log.warn("更新失败");
            throw new ArgsErrorException("更新失败");
        }
    }

    private ChecklistDTO buildDTO(ChecklistDO model) {
        if (model == null) {
            return null;
        }
        ChecklistDTO dto = new ChecklistDTO();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }

    /**
     * 用于扩展操作人字段
     *
     * @param model
     * @return
     */
    private ChecklistDTOV2 buildDTOV2(ChecklistDO model) {
        if (model == null) {
            return null;
        }
        ChecklistDTOV2 dto = new ChecklistDTOV2();
        BeanUtils.copyProperties(model, dto);
        return dto;
    }
}
