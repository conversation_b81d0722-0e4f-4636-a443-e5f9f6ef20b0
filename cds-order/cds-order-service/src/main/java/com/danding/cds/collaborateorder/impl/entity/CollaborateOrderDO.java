package com.danding.cds.collaborateorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 协同单实体
 * @date 2022/3/29
 */
@Table(name = "ccs_collaborate_order")
@Data
public class CollaborateOrderDO extends BaseDO {
    /**
     * 协同单号
     */
    @Column(name = "collaborate_sn")
    private String collaborateSn;

    /**
     * 协同单状态
     * {@link com.danding.cds.collaborateorder.api.enums.CollaborateStatus}
     */
    @Column(name = "collaborate_status")
    private String collaborateStatus;

    /**
     * 清关单号
     */
    @Column(name = "inve_customs_sn")
    private String inveCustomsSn;

    /**
     * 关联清关单号
     */
    @Column(name = "associated_inve_customs_sn")
    private String associatedInveCustomsSn;

    /**
     * 理货数量
     */
    @Column(name = "tally_qty")
    private Integer tallyQty;

    /**
     * 差异数量(总)
     */
    @Column(name = "diff_qty")
    private Integer diffQty;

    /**
     * 理货完成时间
     */
    @Column(name = "tally_finish_time")
    private Date tallyFinishTime;

    /**
     * 运输费
     */
    @Column(name = "shipping_fee")
    private BigDecimal shippingFee;

    /**
     * 托数
     */
    @Column(name = "fight_qty")
    private Integer fightQty;

    /**
     * 理货报告号
     */
    @Column(name = "tally_report_sn")
    private String tallyReportSn;

    /**
     * 到库完成时间
     */
    @Column(name = "arrival_time")
    private Date arrivalTime;

    /**
     * 状态流 JSON格式保存
     */
    @Column(name = "state_flow")
    private String stateFlow;

    /**
     * 清关完成时间
     */
    @Column(name = "inventory_finish_time")
    private Date inventoryFinishTime;

    /**
     * 转入实体仓
     */
    @Column(name = "entity_in_warehouse_name")
    private String entityInWarehouseName;

    /**
     * 转出实体仓
     */
    @Column(name = "entity_out_warehouse_name")
    private String entityOutWarehouseName;
}
