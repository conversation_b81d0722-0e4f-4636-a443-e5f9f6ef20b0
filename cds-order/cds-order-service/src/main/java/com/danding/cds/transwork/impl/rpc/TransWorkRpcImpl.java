package com.danding.cds.transwork.impl.rpc;

import com.danding.cds.transwork.api.rpc.TransWorkRpc;
import com.danding.cds.transwork.api.service.TransWorkService;
import com.danding.cds.transwork.api.vo.*;
import com.danding.cds.v2.bean.dto.TransWorkOrderDTO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import org.apache.dubbo.config.annotation.DubboService;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@DubboService
public class TransWorkRpcImpl implements TransWorkRpc {

    @Resource
    public TransWorkService transWorkService;

    @Override
    public ListVO<TransWorkPageVO> paging(TransWorkSearch search) throws ArgsErrorException {
        return transWorkService.paging(search);
    }

    @Override
    public TransWorkDetailVO detail(Long id) {
        return transWorkService.detail(id);
    }

    @Override
    public ListVO<TransWorkItemVO> itemPaging(Long id) {
        return transWorkService.itemPaging(id);
    }

    @Override
    public void itemEdit(TransWorkItemEditVO param) throws ArgsErrorException {
        transWorkService.itemEdit(param);
    }

    @Override
    public void editTimeInfo(TransWorkTimeEditVO param) throws ArgsErrorException {
        transWorkService.editTimeInfo(param);
    }

    @Override
    public void addTruckInfo(TransTruckInfoVO param) throws ArgsErrorException {
        transWorkService.addTruckInfo(param);
    }

    @Override
    public void finishTransWork(Long id) throws ArgsErrorException {
        transWorkService.finishTransWork(id);
    }

    @Override
    public List<TransWorkOrderDTO> findByTruckResourceCode(String vehicleCode) {
        return transWorkService.findByTruckResourceCode(vehicleCode);
    }

    @Override
    public List<TransWorkOrderDTO> findByTruckResourceCode(String vehicleCode, String truckNo) {
        return transWorkService.findByTruckResourceCodeAndPlate(vehicleCode, truckNo);
    }

    @Override
    public List<TransWorkOrderPackagingItemsVO> getPackagingItems(Long id) {
        return transWorkService.getPackagingItems(id);
    }

    @Override
    public void packagingItemEdit(TransWorkItemEditVO param) throws ArgsErrorException {
        transWorkService.packagingItemEdit(param);
    }

    @Override
    public void addTruckNo(TransAddTruckNoVO param) throws ArgsErrorException {
        transWorkService.addTruckNo(param);
    }
}
