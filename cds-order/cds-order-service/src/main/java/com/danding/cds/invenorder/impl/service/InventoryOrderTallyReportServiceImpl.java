package com.danding.cds.invenorder.impl.service;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.invenorder.api.dto.InventoryOrderTallyReportSubmit;
import com.danding.cds.invenorder.api.dto.InventoryOrderTallyReportDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderTallyReportService;
import com.danding.cds.invenorder.impl.entity.InventoryOrderTallyReportDO;
import com.danding.cds.invenorder.impl.mapper.InventoryOrderTallyReportMapper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import tk.mybatis.mapper.entity.Example;

import java.util.List;
import java.util.Objects;

@DubboService
@Slf4j
public class InventoryOrderTallyReportServiceImpl implements InventoryOrderTallyReportService {

    @Autowired
    private InventoryOrderTallyReportMapper inventoryOrderTallyReportMapper;

    @Override
    public List<InventoryOrderTallyReportDTO> findListByInveOrderId(Long inveOrderId) {
        if (LongUtil.isNone(inveOrderId)) {
            return null;
        }
        Example example = new Example(InventoryOrderTallyReportDO.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("inveOrderId", inveOrderId);
        List<InventoryOrderTallyReportDO> list = inventoryOrderTallyReportMapper.selectByExample(example);
        if (list.size() == 0) {
            return null;
        } else {
            return JSON.parseArray(JSON.toJSONString(list), InventoryOrderTallyReportDTO.class);
        }
    }

    @Override
    public InventoryOrderTallyReportDTO findById(Long id) {
        if (LongUtil.isNone(id)) {
            return null;
        }
        InventoryOrderTallyReportDO inventoryOrderTallyReportDO = inventoryOrderTallyReportMapper.selectByPrimaryKey(id);
        if (inventoryOrderTallyReportDO == null) {
            return null;
        }
        InventoryOrderTallyReportDTO inventoryOrderTallyReportDTO = new InventoryOrderTallyReportDTO();
        BeanUtils.copyProperties(inventoryOrderTallyReportDO, inventoryOrderTallyReportDTO);
        return inventoryOrderTallyReportDTO;
    }

    @Override
    public Long upset(InventoryOrderTallyReportSubmit submit) throws ArgsErrorException {
        InventoryOrderTallyReportDO inventoryOrderTallyReportDO = new InventoryOrderTallyReportDO();
        BeanUtils.copyProperties(submit, inventoryOrderTallyReportDO);
        UserUtils.setCreateAndUpdateBy(inventoryOrderTallyReportDO);
        inventoryOrderTallyReportMapper.insertSelective(inventoryOrderTallyReportDO);
        return inventoryOrderTallyReportDO.getId();
    }
}
