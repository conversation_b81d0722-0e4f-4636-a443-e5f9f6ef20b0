package com.danding.cds.collaborateorder.impl.mapper;

import com.danding.cds.collaborateorder.api.dto.CollaborateOrderCountDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderDTO;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderReqVO;
import com.danding.cds.collaborateorder.impl.entity.CollaborateOrderDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface CollaborateOrderMapper extends Mapper<CollaborateOrderDO>, InsertListMapper<CollaborateOrderDO>, BatchUpdateMapper<CollaborateOrderDO>, AggregationPlusMapper<CollaborateOrderDO> {

    @Results(id = "CollaborateOrderMapper", value = {
            @Result(property = "id", column = "id"),
            @Result(property = "collaborateSn", column = "collaborateSn"),
            @Result(property = "collaborateStatus", column = "collaborateStatus"),
            @Result(property = "tallyQty", column = "tallyQty"),
            @Result(property = "createTime", column = "createTime"),
            @Result(property = "tallyFinishTime", column = "tallyFinishTime"),
            @Result(property = "updateTime", column = "updateTime"),
            @Result(property = "inveBusinessType", column = "inveBusinessType"),
            @Result(property = "inveCustomsSn", column = "inveCustomsSn"),
            @Result(property = "inOutOrderNo", column = "inOutOrderNo"),
            @Result(property = "upstreamNo", column = "upstreamNo"),
            @Result(property = "customsEntryNo", column = "customsEntryNo"),
            @Result(property = "refHzInveNo", column = "refHzInveNo"),
            @Result(property = "inveCompanyId", column = "inveCompanyId"),
            @Result(property = "warehouseName", column = "warehouseName"),
            @Result(property = "remark", column = "remark"),
            @Result(property = "inveFinishTime", column = "inveFinishTime"),
            @Result(property = "planDeclareQty", column = "planDeclareQty"),
            @Result(property = "inveId", column = "inveId"),
            @Result(property = "tallyReportSn", column = "tallyReportSn"),
            @Result(property = "diffQty", column = "diffQty"),
    })
    @Select("<script>" +
            " SELECT eo.id AS id,eo.collaborate_sn AS collaborateSn,eo.collaborate_status AS collaborateStatus," +
            " eo.tally_qty AS tallyQty,eo.create_time AS createTime,eo.tally_finish_time AS tallyFinishTime," +
            " eo.update_time AS updateTime,oi.inve_business_type AS inveBusinessType,oi.inve_customs_sn AS inveCustomsSn," +
            " oi.in_out_order_no AS inOutOrderNo,oi.upstream_no AS upstreamNo,oi.customs_entry_no AS customsEntryNo," +
            " oi.ref_hz_inve_no AS refHzInveNo,oi.inve_company_id AS companyId,oi.entity_warehouse_name AS warehouseName,oi.remark AS remark,oi.inve_company_id AS inveCompanyId," +
            " eo.inventory_finish_time AS inveFinishTime,oi.id AS inveId, eo.tally_report_sn as tallyReportSn, eo.diff_qty as diffQty " +
            " FROM ccs_inventory_order_info oi " +
            " INNER JOIN ccs_collaborate_order eo ON eo.inve_customs_sn = oi.inve_customs_sn " +
            " WHERE oi.deleted = 0 and eo.deleted = 0  " +
            " <if test = 'reqVO.searchType != null'>" +
            " <if test = 'reqVO.searchType == 2'>" +
            " AND oi.inve_business_type = 'ONELINE_IN'" +
            " </if>" +
            " <if test = 'reqVO.searchType == 3'>" +
            " AND oi.inve_business_type != 'ONELINE_IN'" +
            " </if>" +
            " </if>" +
            //协同单号查询
            " <if test = 'reqVO.collaborateSns != null'>" +
            "   AND eo.collaborate_sn in " +
            "       <foreach collection='reqVO.collaborateSns' item='item' open='(' separator=',' close=')'>" +
            "           #{item}" +
            "       </foreach>" +
            "</if>" +
            //协同类型
            " <if test = 'reqVO.collaborateStatus != null'> AND eo.collaborate_status = #{reqVO.collaborateStatus} </if>" +
            //创建时间查询
            " <if test = 'reqVO.createStaTime != null'> AND eo.create_time BETWEEN #{reqVO.createStaTime} AND #{reqVO.createEndTime} </if>" +
            //创建时间查询
            " <if test = 'reqVO.finishStaTime != null'> AND eo.update_time BETWEEN #{reqVO.finishStaTime} AND #{reqVO.finishEndTime} </if>" +
            //理货完成时间
            " <if test = 'reqVO.tallyStaTime != null'> AND eo.tally_finish_time BETWEEN #{reqVO.tallyStaTime} AND #{reqVO.tallyEndTime} </if>" +
            //
            " <if test = 'reqVO.collaborateType != null'> AND oi.inve_business_type = #{reqVO.collaborateType} </if>" +
            //清关单号
            " <if test = 'reqVO.inveCustomsSnList != null'> " +
            "   AND oi.inve_customs_sn in " +
            "       <foreach collection='reqVO.inveCustomsSnList' item='item' open='(' separator=',' close=')'> " +
            "           #{item}" +
            "       </foreach> " +
            " </if>" +
            //出入库单号
            " <if test = 'reqVO.inOutOrderNos != null'> " +
            "   AND oi.in_out_order_no in " +
            "       <foreach collection='reqVO.inOutOrderNos' item='item' open='(' separator=',' close=')'> " +
            "           #{item}" +
            "       </foreach> " +
            " </if>" +
            //核注清单编号
            " <if test = 'reqVO.endorsementSn != null'> AND oi.ref_hz_inve_no = #{reqVO.endorsementSn} </if>" +
            //实体仓
            " <if test = 'reqVO.warehouseName != null'> AND oi.entity_warehouse_code = #{reqVO.warehouseName} </if>" +
            //申报企业
            " <if test = 'reqVO.agentCompanyId != null'> AND oi.inve_company_id = #{reqVO.agentCompanyId} </if>" +
            //清关完成时间
            " <if test = 'reqVO.invStaFinishTime != null'> AND eo.inventory_finish_time BETWEEN #{reqVO.invStaFinishTime} AND #{reqVO.invEndFinishTime} </if>" +
            " ORDER BY eo.create_time DESC " +
            " </script>"
    )
    List<CollaborateOrderDTO> selectByPaging(@Param("reqVO") CollaborateOrderReqVO reqVO);


    @Results(value = {
            @Result(property = "num", column = "num"),
            @Result(property = "inveBusinessType", column = "inveBusinessType"),
    })
    @Select("<script>" +
            " SELECT COUNT(*) as num,  oi.inve_business_type as inveBusinessType " +
            " FROM ccs_inventory_order_info oi " +
            " INNER JOIN ccs_collaborate_order eo ON eo.inve_customs_sn = oi.inve_customs_sn " +
//            " INNER JOIN ccs_company.ccs_company cp ON cp.id = oi.inve_company_id " +
            " WHERE oi.deleted = 0 and eo.deleted = 0  " +
            " <if test = 'reqVO.searchType != null'>" +
            " <if test = 'reqVO.searchType == 2'>" +
            " AND oi.inve_business_type = 'ONELINE_IN'" +
            " </if>" +
            " <if test = 'reqVO.searchType == 3'>" +
            " AND oi.inve_business_type != 'ONELINE_IN'" +
            " </if>" +
            " </if>" +
            //协同单号查询
            " <if test = 'reqVO.collaborateSns != null'>" +
            "   AND eo.collaborate_sn in " +
            "       <foreach collection='reqVO.collaborateSns' item='item' open='(' separator=',' close=')'>" +
            "           #{item}" +
            "       </foreach>" +
            "</if>" +
            //协同类型
            " <if test = 'reqVO.collaborateStatus != null'> AND eo.collaborate_status = #{reqVO.collaborateStatus} </if>" +
            //创建时间查询
            " <if test = 'reqVO.createStaTime != null'> AND eo.create_time BETWEEN #{reqVO.createStaTime} AND #{reqVO.createEndTime} </if>" +
            //完成时间查询
            " <if test = 'reqVO.finishStaTime != null'> AND eo.update_time BETWEEN #{reqVO.finishStaTime} AND #{reqVO.finishEndTime} </if>" +
            //理货完成时间
            " <if test = 'reqVO.tallyStaTime != null'> AND eo.tally_finish_time BETWEEN #{reqVO.tallyStaTime} AND #{reqVO.tallyEndTime} </if>" +
            //
            " <if test = 'reqVO.collaborateType != null'> AND oi.inve_business_type = #{reqVO.collaborateType} </if>" +
            //清关单号
            " <if test = 'reqVO.inveCustomsSnList != null'> " +
            "   AND oi.inve_customs_sn in " +
            "       <foreach collection='reqVO.inveCustomsSnList' item='item' open='(' separator=',' close=')'> " +
            "           #{item}" +
            "       </foreach> " +
            " </if>" +
            //出入库单号
            " <if test = 'reqVO.inOutOrderNos != null'> " +
            "   AND oi.in_out_order_no in " +
            "       <foreach collection='reqVO.inOutOrderNos' item='item' open='(' separator=',' close=')'> " +
            "           #{item}" +
            "       </foreach> " +
            " </if>" +
            //核注清单编号
            " <if test = 'reqVO.endorsementSn != null'> AND oi.ref_hz_inve_no = #{reqVO.endorsementSn} </if>" +
            //实体仓
            " <if test = 'reqVO.warehouseName != null'> AND oi.entity_warehouse_code = #{reqVO.warehouseName} </if>" +
            //申报企业
            " <if test = 'reqVO.agentCompanyId != null'> AND oi.inve_company_id = #{reqVO.agentCompanyId} </if>" +
            //清关完成时间
            " <if test = 'reqVO.invStaFinishTime != null'> AND eo.inventory_finish_time BETWEEN #{reqVO.invStaFinishTime} AND #{reqVO.invEndFinishTime} </if>" +
            " GROUP BY oi.inve_business_type " +
            " </script>"
    )
    List<CollaborateOrderCountDTO> selectCountByPaging(@Param("reqVO") CollaborateOrderReqVO reqVO);

}
