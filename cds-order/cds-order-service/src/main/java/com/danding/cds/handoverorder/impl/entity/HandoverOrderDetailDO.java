package com.danding.cds.handoverorder.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 交接单明细实体对象
 * @date 2021/12/14
 */
@Table(name = "`ccs_customs_handover_order_detail`")
@Data
public class HandoverOrderDetailDO extends BaseDO {

    /**
     * 包裹号
     */
    @Column(name = "package_sn")
    private String packageSn;

    /**
     * 包裹重量（kg）
     */
    @Column(name = "package_weight")
    private String packageWeight;

    /**
     * 运单编号
     */
    @Column(name = "way_bill_sn")
    private String wayBillSn;

    /**
     * 交接单
     */
    @Column(name = "handover_sn")
    private String handoverSn;

    /**
     * 清单sn
     */
    @Column(name = "customs_inventory_sn")
    private String customsInventorySn;

    /**
     * 申报单号
     */
    @Column(name = "declare_order_no")
    private String declareOrderNo;

    /**
     * 快递编码
     */
    @Column(name = "express_code")
    private String expressCode;

    /**
     * 快递公司
     */
    @Column(name = "express_name")
    private String expressName;

    /**
     * 出库时间
     */
    @Column(name = "out_house_time")
    private Date outHouseTime;

    /**
     * 出库单号
     */
    @Column(name = "outbound_order")
    private String outboundOrder;

    /**
     * 是否联系出库0-未关联  1-已关联
     */
    @Column(name = "associate_outbound_status")
    private Integer associateOutboundStatus;

    /**
     * 是否有异常 0 -否 -1 是
     */
    @Column(name = "unusual_status")
    private Integer unusualStatus;

    /**
     * unusualMsg
     * 异常原因
     */
    @Column(name = "unusual_msg")
    private String unusualMsg;

    /**
     * 仓库名称
     */
    @Column(name = "store_house_name")
    private String storeHouseName;

    /**
     * 仓库编码
     */
    @Column(name = "store_house_sn")
    private String storeHouseSn;
}
