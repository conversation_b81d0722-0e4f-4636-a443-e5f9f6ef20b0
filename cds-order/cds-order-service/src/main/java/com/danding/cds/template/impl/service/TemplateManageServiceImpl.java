package com.danding.cds.template.impl.service;

import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.UserServiceUtil;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.template.api.dto.TemplateManageDTO;
import com.danding.cds.template.api.dto.TemplateManageSearch;
import com.danding.cds.template.api.dto.TemplateManageSubmit;
import com.danding.cds.template.api.service.TemplateManageService;
import com.danding.cds.template.entity.TemplateManageDO;
import com.danding.cds.template.mapper.TemplateManageMapper;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.component.boost.annotation.PageSelect;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 模板管理服务实现类
 * @date 2025/7/31
 */
@Slf4j
@DubboService
public class TemplateManageServiceImpl implements TemplateManageService {

    @Resource
    private TemplateManageMapper templateManageMapper;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CustomsDistrictService customsDistrictService;

    @Resource
    private UserServiceUtil userServiceUtil;

    @Override
    @PageSelect
    public ListVO<TemplateManageDTO> paging(TemplateManageSearch search) {
        log.info("开始分页查询模板，查询参数：{}", search);

        try {
            Example example = buildPagingExample(search);
            List<TemplateManageDO> list = templateManageMapper.selectByExample(example);
            log.info("数据库查询完成，查询到{}条记录", list.size());

            ListVO<TemplateManageDTO> result = new ListVO<>();
            List<TemplateManageDTO> dataList = convertToDTOList(list);
            result.setDataList(dataList);

            // 设置分页信息
            PageInfo<TemplateManageDO> pageInfo = new PageInfo<>(list);
            PageResult pageResult = new PageResult();
            pageResult.setTotalCount(pageInfo.getTotal());
            pageResult.setTotalPage(pageInfo.getPages());
            pageResult.setCurrentPage(search.getCurrentPage());
            pageResult.setPageSize(search.getPageSize());
            result.setPage(pageResult);

            log.info("分页查询模板完成，总记录数：{}，当前页：{}，页大小：{}",
                    pageInfo.getTotal(), search.getCurrentPage(), search.getPageSize());
            return result;
        } catch (Exception e) {
            log.error("分页查询模板异常，查询参数：{}", search, e);
            throw e;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long upload(TemplateManageSubmit submit) {
        log.info("开始上传模板，提交参数：{}", submit);

        try {
            TemplateManageDO templateDO = ConvertUtil.beanConvert(submit, TemplateManageDO.class);
            log.debug("参数转换完成，转换后的DO：{}", templateDO);

            Long userId = SimpleUserHelper.getUserId();
            String realUserName = SimpleUserHelper.getRealUserName();
            templateDO.setUploadTime(new Date());
            log.info("设置创建者信息，用户ID：{}/{}，上传时间：{}", userId, realUserName, templateDO.getUploadTime());

            // 设置通用字段
            templateDO.setId(null);
            UserUtils.setCreateAndUpdateBy(templateDO);

            templateManageMapper.insertSelective(templateDO);
            Long templateId = templateDO.getId();
            log.info("模板上传成功，模板ID：{}，模板名称：{}", templateId, submit.getTemplateName());

            return templateId;
        } catch (Exception e) {
            log.error("上传模板失败，提交参数：{}", submit, e);
            throw new ArgsErrorException("上传模板失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long edit(TemplateManageSubmit submit) {
        try {
            if (submit.getId() == null) {
                throw new ArgsErrorException("编辑时模板ID不能为空");
            }

            TemplateManageDO existingTemplate = templateManageMapper.selectByPrimaryKey(submit.getId());
            if (existingTemplate == null) {
                throw new ArgsErrorException("模板不存在");
            }

            // 更新基本信息
            existingTemplate.setTemplateName(submit.getTemplateName());
            existingTemplate.setPurpose(submit.getPurpose());
            existingTemplate.setPorts(submit.getPorts());
            existingTemplate.setRemark(submit.getRemark());

            // 更新文件信息（如果有新的文件URL和文件名）
            if (StringUtils.hasText(submit.getFileUrl())) {
                existingTemplate.setFileUrl(submit.getFileUrl());
                existingTemplate.setUploadTime(new Date());
            }
            if (StringUtils.hasText(submit.getFileName())) {
                existingTemplate.setFileName(submit.getFileName());
            }

            // 设置更新信息
            UserUtils.setUpdateBy(existingTemplate);

            templateManageMapper.updateByPrimaryKeySelective(existingTemplate);
            return existingTemplate.getId();
        } catch (Exception e) {
            log.error("编辑模板失败", e);
            throw new ArgsErrorException("编辑模板失败: " + e.getMessage());
        }
    }

    @Override
    public TemplateManageDTO getById(Long id) {
        if (id == null) {
            return null;
        }

        TemplateManageDO templateDO = templateManageMapper.selectByPrimaryKey(id);
        if (templateDO == null) {
            return null;
        }

        return convertToDTO(templateDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean delete(Long id) {
        log.info("开始删除模板，模板ID：{}", id);

        try {
            if (id == null) {
                throw new ArgsErrorException("模板ID不能为空");
            }

            TemplateManageDO existingTemplate = templateManageMapper.selectByPrimaryKey(id);
            if (existingTemplate == null) {
                log.warn("要删除的模板不存在，模板ID：{}", id);
                throw new ArgsErrorException("模板不存在");
            }

            // 逻辑删除
            existingTemplate.setDeleted(true);
            UserUtils.setUpdateBy(existingTemplate);

            int updateCount = templateManageMapper.updateByPrimaryKeySelective(existingTemplate);
            boolean success = updateCount > 0;

            log.info("删除模板完成，模板ID：{}，模板名称：{}，删除结果：{}",
                    id, existingTemplate.getTemplateName(), success);

            return success;
        } catch (Exception e) {
            log.error("删除模板失败，模板ID：{}", id, e);
            throw new ArgsErrorException("删除模板失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchDelete(List<Long> ids) {
        log.info("开始批量删除模板，模板ID列表：{}", ids);

        try {
            if (CollectionUtils.isEmpty(ids)) {
                throw new ArgsErrorException("模板ID列表不能为空");
            }

            int successCount = 0;
            List<String> failedTemplateNames = new ArrayList<>();

            for (Long id : ids) {
                try {
                    TemplateManageDO existingTemplate = templateManageMapper.selectByPrimaryKey(id);
                    if (existingTemplate == null) {
                        log.warn("要删除的模板不存在，模板ID：{}", id);
                        failedTemplateNames.add("ID:" + id + "(不存在)");
                        continue;
                    }

                    // 逻辑删除
                    existingTemplate.setDeleted(true);
                    UserUtils.setUpdateBy(existingTemplate);

                    int updateCount = templateManageMapper.updateByPrimaryKeySelective(existingTemplate);
                    if (updateCount > 0) {
                        successCount++;
                        log.info("删除模板成功，模板ID：{}，模板名称：{}", id, existingTemplate.getTemplateName());
                    } else {
                        failedTemplateNames.add(existingTemplate.getTemplateName());
                        log.warn("删除模板失败，模板ID：{}，模板名称：{}", id, existingTemplate.getTemplateName());
                    }
                } catch (Exception e) {
                    log.error("删除单个模板异常，模板ID：{}", id, e);
                    failedTemplateNames.add("ID:" + id + "(异常)");
                }
            }

            log.info("批量删除模板完成，总数：{}，成功：{}，失败：{}，失败列表：{}",
                    ids.size(), successCount, failedTemplateNames.size(), failedTemplateNames);

            if (!failedTemplateNames.isEmpty()) {
                throw new ArgsErrorException("部分模板删除失败：" + String.join(",", failedTemplateNames));
            }

            return successCount;
        } catch (Exception e) {
            log.error("批量删除模板失败，模板ID列表：{}", ids, e);
            throw new ArgsErrorException("批量删除模板失败: " + e.getMessage());
        }
    }

    /**
     * 构建分页查询条件
     */
    private Example buildPagingExample(TemplateManageSearch search) {
        Example example = new Example(TemplateManageDO.class);
        example.orderBy("uploadTime").desc();

        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("deleted", false);

        // 模板名称模糊查询
        if (StringUtils.hasText(search.getTemplateName())) {
            criteria.andLike("templateName", "%" + search.getTemplateName() + "%");
        }

        // 用途查询
        if (StringUtils.hasText(search.getPurpose())) {
            criteria.andEqualTo("purpose", search.getPurpose());
        }

        // 口岸查询
        if (StringUtils.hasText(search.getPort())) {
            criteria.andLike("ports", "%" + search.getPort() + "%");
        }

        // 上传时间范围查询
        if (search.getUploadTimeStart() != null) {
            criteria.andGreaterThanOrEqualTo("uploadTime", search.getUploadTimeStart());
        }
        if (search.getUploadTimeEnd() != null) {
            criteria.andLessThanOrEqualTo("uploadTime", search.getUploadTimeEnd());
        }

        // 备注模糊查询
        if (StringUtils.hasText(search.getRemark())) {
            criteria.andLike("remark", "%" + search.getRemark() + "%");
        }

        return example;
    }

    /**
     * 转换DO列表为DTO列表
     */
    private List<TemplateManageDTO> convertToDTOList(List<TemplateManageDO> doList) {
        if (CollectionUtils.isEmpty(doList)) {
            return new ArrayList<>();
        }

        // 获取口岸信息
        Map<String, String> portNameMap = getPortNameMap();

        // 获取用途信息
        Map<String, String> purposeNameMap = getPurposeNameMap();

        return doList.stream().map(templateDO -> {
            TemplateManageDTO dto = convertToDTO(templateDO);

            // 设置口岸名称
            if (StringUtils.hasText(templateDO.getPorts())) {
                String[] portCodes = templateDO.getPorts().split(",");
                String portNames = Arrays.stream(portCodes)
                        .map(code -> portNameMap.getOrDefault(code.trim(), code.trim()))
                        .collect(Collectors.joining(","));
                dto.setPortNames(portNames);
            }

            // 设置用途名称
            if (StringUtils.hasText(templateDO.getPurpose())) {
                dto.setPurposeName(purposeNameMap.getOrDefault(templateDO.getPurpose(), templateDO.getPurpose()));
            }

            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 转换DO为DTO
     */
    private TemplateManageDTO convertToDTO(TemplateManageDO templateDO) {
        return ConvertUtil.beanConvert(templateDO, TemplateManageDTO.class);
    }

    /**
     * 获取口岸名称映射
     */
    private Map<String, String> getPortNameMap() {
        try {
            List<CustomsDistrictDTO> ports = customsDistrictService.listAll();
            return ports.stream()
                    .collect(Collectors.toMap(
                            CustomsDistrictDTO::getCode,
                            CustomsDistrictDTO::getName,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("获取口岸信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取用途名称映射
     */
    private Map<String, String> getPurposeNameMap() {
        try {
            // 这里需要根据实际的数据字典实现来获取用途名称映射
            // 暂时返回空映射，实际项目中需要实现
            return new HashMap<>();
        } catch (Exception e) {
            log.error("获取用途信息失败", e);
            return new HashMap<>();
        }
    }


}
