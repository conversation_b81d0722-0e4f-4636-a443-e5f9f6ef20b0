package com.danding.cds.invenorder.impl.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class CompanyReceiveInfo {

    @NotBlank(message = "企业英文名称不能为空")
    private String englishName;
    @NotBlank(message = "企业英文地址不能为空")
    private String englishAddress;
    @NotBlank(message = "企业电话不能为空")
    private String phone;
    @NotBlank(message = "企业邮箱不能为空")
    private String email;
    @NotBlank(message = "企业统一社会信用代码不能为空")
    private String uniformSocialCreditCode;
}
