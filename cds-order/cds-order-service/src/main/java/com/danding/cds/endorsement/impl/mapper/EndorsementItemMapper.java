package com.danding.cds.endorsement.impl.mapper;

import com.danding.cds.endorsement.impl.entity.EndorsementItemDO;
import com.danding.logistics.mybatis.common.mybatis.Aggregate.AggregationPlusMapper;
import com.danding.logistics.mybatis.common.mybatis.BatchUpdateMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import tk.mybatis.mapper.additional.insert.InsertListMapper;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface EndorsementItemMapper extends Mapper<EndorsementItemDO>, InsertListMapper<EndorsementItemDO>, BatchUpdateMapper<EndorsementItemDO>, AggregationPlusMapper<EndorsementItemDO> {

    @Update(value = "update `ccs_endorsement_item` set checklist_id = #{checklistId} where id = #{id}")
    Integer updateChecklist(@Param("id") Long id, @Param("checklistId") Long checklistId);

    @Select(value = "select endorsement.sn as sn" +
            "from ccs_endorsement_item item " +
            "left join ccs_endorsement endorsement on item.endorsement_id = endorsement.id" +
            "where item.checklist_id = #{id} and deleted = 0")
    List<String> snListByChecklistId(@Param("id") Long id);
}