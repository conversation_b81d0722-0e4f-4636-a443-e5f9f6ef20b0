package com.danding.cds.endorsement.impl.entity;

import com.danding.cds.common.model.BaseDO;
import lombok.Data;

import javax.persistence.Table;

@Data
@Table(name = "ccs_endorsement_track_log")
public class EndorsementTrackLogDO extends BaseDO {

    /**
     * 核注单id
     */
    private Long endorsementId;

    /**
     * 核注单状态
     */
    private String status;

    /**
     * 日志描述
     */
    private String logInfo;

    /**
     * 回执详情
     */
    private String callbackDetail;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 租户
     */
    private Long tenantryId;

    /**
     * 压缩方式
     */
    private String compressType;
}
