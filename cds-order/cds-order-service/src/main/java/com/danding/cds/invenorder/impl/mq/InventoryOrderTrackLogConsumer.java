package com.danding.cds.invenorder.impl.mq;

import com.alibaba.fastjson.JSON;
import com.danding.cds.invenorder.api.dto.InventoryOrderTrackLogDTO;
import com.danding.cds.v2.service.InventoryOrderInfoTrackLogService;
import com.danding.logistics.mq.common.handler.MessageHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * 异步保存清关单变动日志
 * <AUTHOR>
 */
@Slf4j
@Component
@RocketMQMessageListener(
        consumerGroup = "ccs-inventory-order-track-log-consumer",
        topic = "ccs-inventory-track-log-topic"
)
public class InventoryOrderTrackLogConsumer extends MessageHandler {
    @Autowired
    private InventoryOrderInfoTrackLogService inventoryOrderTrackLogService;

    @Override
    public void handle(Object message) throws RuntimeException {
        if (ObjectUtils.isEmpty(message)) {
            return;
        }
        InventoryOrderTrackLogDTO inventoryOrderTrackLogDTO = (InventoryOrderTrackLogDTO) message;
        log.info("InventoryOrderTrackLogConsumer inventoryOrderTrackLogDTO={}", JSON.toJSONString(inventoryOrderTrackLogDTO));
        inventoryOrderTrackLogService.saveTrackLog(inventoryOrderTrackLogDTO);
    }
}
