ALTER TABLE ccs_inventory_order_info ADD COLUMN channel tinyint(3) NULL DEFAULT '' COMMENT '渠道' AFTER remark;
ALTER TABLE ccs_inventory_order_info ADD COLUMN reason varchar(50) NULL DEFAULT ''  COMMENT '驳回原因' AFTER status;
ALTER TABLE ccs_inventory_order_info ADD COLUMN audit_status varchar(10) NULL DEFAULT '' COMMENT '审核状态' AFTER channel;
ALTER TABLE ccs_inventory_order_info ADD COLUMN expected_out_area_time timestamp NULL  COMMENT '预计出区时间' AFTER status_time;
ALTER TABLE ccs_inventory_order_info ADD COLUMN expected_to_port_time timestamp NULL  COMMENT '预计到港时间' AFTER expected_out_area_time;