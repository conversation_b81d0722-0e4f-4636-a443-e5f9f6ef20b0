CREATE TABLE `ccs_customs_inventory_calloff`  (
      `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT,
      `sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '清单系统编号',
      `order_sn` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '申报单系统编号',
      `order_id` bigint(20) NOT NULL COMMENT '订单ID',
      `declare_order_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '申报单号',
      `inventory_no` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '清单编号',
      `customs_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '清关状态',
      `exit_region_status` tinyint(3) NOT NULL DEFAULT 0 COMMENT '出区状态',
      `calloff_status` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '取消状态',
      `calloff_type` varchar(50) NULL DEFAULT '' COMMENT '取消类型',
      `calloff_reason` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '取消原因',
      `reject_reason` varchar(2000) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT '' COMMENT '驳回原因',
      `logistics_no` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流运单编号',
      `ebc_id` bigint(20) NOT NULL COMMENT '电商企业',
      `agent_company_id` bigint(20) NOT NULL COMMENT '申报企业',
      `user_name` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '用户名称',
      `calloff_time` timestamp(0) NULL  DEFAULT NULL COMMENT '取消时间',
      `create_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '创建人',
      `update_by` bigint(20) NOT NULL DEFAULT 0 COMMENT '更新人',
      `create_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) COMMENT '创建时间',
      `update_time` timestamp(0) NOT NULL DEFAULT CURRENT_TIMESTAMP(0) ON UPDATE CURRENT_TIMESTAMP(0) COMMENT '更新时间',
      `deleted` tinyint(1) NOT NULL DEFAULT 0 COMMENT '1逻辑删除',
      PRIMARY KEY (`id`) USING BTREE,
      INDEX `idx_order_id`(`order_id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT = '取消单表';