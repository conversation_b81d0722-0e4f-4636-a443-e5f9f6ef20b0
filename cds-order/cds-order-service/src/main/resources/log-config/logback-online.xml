<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="true" scan="true" scanPeriod="60 seconds">
	<property name="log.level" value="info"/>
	<property name="log.thredhold" value="info"/>
	<property name="log.path" value="/nfs"/>

	<springProperty scope="context" name="app.name" source="dubbo.application.name"/>
	<springProperty scope="context" name="logstashUrl" source="logstash.url"/>

	<!-- 导入spring的默认配置：含颜色解析器，异常解析器等-->
	<include resource="org/springframework/boot/logging/logback/defaults.xml"/>

	<!-- 控制台彩色日志格式 -->
	<property name="CONSOLE_LOG"
			  value="${CONSOLE_LOG:-%clr(%X{trace.id}){blue} %clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(%X{TENANT_ID}){yellow} %clr(%X{CHAIN_TYPE}){magenta} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

	<!-- 简化输出过程：不使用颜色解析器 -->
	<property name="FILE_LOG"
			  value="${FILE_LOG:-%X{trace.id} %d{yyyy-MM-dd HH:mm:ss.SSS} ${LOG_LEVEL_PATTERN:-%5p} %X{TENANT_ID} %X{CHAIN_TYPE} [%15.15t] %-40.40logger{39} : %m%n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

	<appender name="console" class="ch.qos.logback.core.ConsoleAppender">
		<filter class="ch.qos.logback.classic.filter.ThresholdFilter">
			<level>${log.thredhold}</level>
		</filter>
		<encoder>
			<pattern>${FILE_LOG}</pattern>
		</encoder>
	</appender>

	<appender name="logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
		<destination>${logstashUrl}</destination>
		<encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder"/>
	</appender>

	<!-- 输出到文件 -->
	<appender name="logVolume" class="ch.qos.logback.core.rolling.RollingFileAppender">
		<!--日志输出编码格式化-->
		<encoder>
			<charset>UTF-8</charset>
			<pattern>${FILE_LOG}</pattern>
		</encoder>
		<rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
			<fileNamePattern>${log.path}/ccs-order/%d{yyyy-MM-dd}.log</fileNamePattern>
			<maxHistory>100</maxHistory>
		</rollingPolicy>
	</appender>

	<logger name="com.alibaba.nacos" level="warn"/>
	<logger name="com.alibaba.nacos.client.identify" level="error"/>
	<logger name="com.xxl.job" level="warn"/>
	<logger name="org.redisson" level="error"/>
	<logger name="org.apache.shardingsphere.sharding.route.engine.type.tenant.TenantShardingRouteEngine" level="error"/>
	<!-- Root Logger -->
	<root level="${log.level}">
		<appender-ref ref="console" />
		<appender-ref ref="logstash"/>
		<appender-ref ref="logVolume"/>
	</root>
</configuration>