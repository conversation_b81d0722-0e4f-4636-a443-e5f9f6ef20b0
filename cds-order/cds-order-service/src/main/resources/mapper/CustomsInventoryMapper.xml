<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.inventory.impl.mapper.CustomsInventoryMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.inventory.impl.entity.CustomsInventoryDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="ebp_id" jdbcType="BIGINT" property="ebpId" />
    <result column="ebc_id" jdbcType="BIGINT" property="ebcId" />
    <result column="express_id" jdbcType="BIGINT" property="expressId" />
    <result column="logistics_no" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="logistics_company_id" jdbcType="BIGINT" property="logisticsCompanyId" />
    <result column="pre_no" jdbcType="CHAR" property="preNo" />
    <result column="assure_company_id" jdbcType="BIGINT" property="assureCompanyId" />
    <result column="account_book_id" jdbcType="BIGINT" property="accountBookId" />
    <result column="inventory_no" jdbcType="VARCHAR" property="inventoryNo" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="buyer_tel_number" jdbcType="VARCHAR" property="buyerTelNumber" />
    <result column="buyer_id_type" jdbcType="VARCHAR" property="buyerIdType" />
    <result column="buyer_id_number" jdbcType="VARCHAR" property="buyerIdNumber" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="agent_company_id" jdbcType="BIGINT" property="agentCompanyId" />
    <result column="area_company_id" jdbcType="BIGINT" property="areaCompanyId" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
    <result column="insure_amount" jdbcType="DECIMAL" property="insureAmount" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />
    <result column="net_weight" jdbcType="DECIMAL" property="netWeight" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="customs_pass_time" jdbcType="TIMESTAMP" property="customsPassTime" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="exit_region_status" jdbcType="TINYINT" property="exitRegionStatus" />
    <result column="customs_detail" jdbcType="VARCHAR" property="customsDetail" />
    <result column="last_customs_time" jdbcType="TIMESTAMP" property="lastCustomsTime" />
    <result column="last_declare_time" jdbcType="TIMESTAMP" property="lastDeclareTime" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
    <result column="after_sales_status" jdbcType="LONGVARCHAR" property="afterSalesStatus" />
    <result column="review_status" jdbcType="LONGVARCHAR" property="reviewStatus" />
    <result column="user_id" jdbcType="LONGVARCHAR" property="userId" />
  </resultMap>


  <select id="selectPage" resultMap="BaseResultMap">
    select * from #{tableName} where deleted = 0 order by create_time asc limit #{offset}, #{pageSize}
  </select>

  <select id="findCount" parameterType="java.lang.String" resultType="java.lang.Integer">
    select count(*) from #{tableName} where deleted = 0
  </select>
</mapper>