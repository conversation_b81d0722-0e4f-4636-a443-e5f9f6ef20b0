<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.exportorder.impl.mapper.ExportItemMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.exportorder.impl.entity.ExportItemDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="export_order_id" jdbcType="BIGINT" property="exportOrderId" />
    <result column="endorsement_order_id" jdbcType="BIGINT" property="endorsementOrderId" />
    <result column="customs_inventory_sn" jdbcType="VARCHAR" property="customsInventorySn" />
    <result column="express_id" jdbcType="BIGINT" property="expressId" />
    <result column="mail_no" jdbcType="VARCHAR" property="mailNo" />
    <result column="tray_no" jdbcType="VARCHAR" property="trayNo" />
    <result column="operator_no" jdbcType="VARCHAR" property="operatorNo" />
    <result column="station_no" jdbcType="VARCHAR" property="stationNo" />
    <result column="biz_id" jdbcType="VARCHAR" property="bizId" />
    <result column="sku_json" jdbcType="VARCHAR" property="skuJson" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />
    <result column="net_weight" jdbcType="DECIMAL" property="netWeight" />
  </resultMap>
</mapper>