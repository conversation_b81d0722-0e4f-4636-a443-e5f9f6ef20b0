<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.refund.impl.mapper.RefundOrderInfoMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.refund.impl.entity.RefundOrderInfo">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="refund_no" jdbcType="VARCHAR" property="refundNo" />
    <result column="ref_list_bill_id" jdbcType="INTEGER" property="refListBillId" />
    <result column="ref_list_bill_no" jdbcType="VARCHAR" property="refListBillNo" />
    <result column="ref_declare_id" jdbcType="INTEGER" property="refDeclareId" />
    <result column="ref_declare_no" jdbcType="VARCHAR" property="refDeclareNo" />
    <result column="apply_path" jdbcType="VARCHAR" property="applyPath" />
    <result column="refund_express" jdbcType="VARCHAR" property="refundExpress" />
    <result column="refund_check_status" jdbcType="INTEGER" property="refundCheckStatus" />
    <result column="refund_check_time" jdbcType="TIMESTAMP" property="refundCheckTime" />
    <result column="refund_status" jdbcType="INTEGER" property="refundStatus" />
    <result column="refund_status_time" jdbcType="TIMESTAMP" property="refundStatusTime" />
    <result column="refund_tracking_no" jdbcType="VARCHAR" property="refundTrackingNo" />
    <result column="refund_call_back" jdbcType="INTEGER" property="refundCallBack" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="app_type" jdbcType="VARCHAR" property="appType" />
    <result column="app_time" jdbcType="TIMESTAMP" property="appTime" />
    <result column="app_status" jdbcType="VARCHAR" property="appStatus" />
    <result column="customs_code" jdbcType="VARCHAR" property="customsCode" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="logistics_no" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="logistics_code" jdbcType="VARCHAR" property="logisticsCode" />
    <result column="logistics_name" jdbcType="VARCHAR" property="logisticsName" />
    <result column="cop_no" jdbcType="VARCHAR" property="copNo" />
    <result column="pre_no" jdbcType="VARCHAR" property="preNo" />
    <result column="invt_no" jdbcType="VARCHAR" property="invtNo" />
    <result column="buyer_id_type" jdbcType="VARCHAR" property="buyerIdType" />
    <result column="buyer_id_number" jdbcType="VARCHAR" property="buyerIdNumber" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_telephone" jdbcType="VARCHAR" property="buyerTelephone" />
    <result column="agent_code" jdbcType="VARCHAR" property="agentCode" />
    <result column="agent_name" jdbcType="VARCHAR" property="agentName" />
    <result column="reason" jdbcType="LONGVARCHAR" property="reason" />
    <result column="note" jdbcType="LONGVARCHAR" property="note" />
  </resultMap>
</mapper>