<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.invenorder.impl.mapper.InventoryOrderTallyReportMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.invenorder.impl.entity.InventoryOrderTallyReportDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="inve_order_id" jdbcType="BIGINT" property="inveOrderId" />
    <result column="inve_order_sn" jdbcType="VARCHAR" property="inveOrderSn" />
    <result column="tally_order_no" jdbcType="VARCHAR" property="tallyOrderNo" />
    <result column="out_bound_no" jdbcType="VARCHAR" property="outBoundNo" />
    <result column="tally_json" jdbcType="LONGVARCHAR" property="tallyJson" />
  </resultMap>
</mapper>