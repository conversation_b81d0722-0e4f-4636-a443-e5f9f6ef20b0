<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.order.impl.mapper.CustomsStatusMappingMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.order.impl.entity.CustomsStatusMappingDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="action" jdbcType="VARCHAR" property="action" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="detail" jdbcType="VARCHAR" property="detail" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="exception_flag" jdbcType="BIT" property="exceptionFlag" />
  </resultMap>
</mapper>