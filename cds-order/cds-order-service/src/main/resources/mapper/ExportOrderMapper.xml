<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.exportorder.impl.mapper.ExportOrderMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.exportorder.impl.entity.ExportOrderDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="express_list" jdbcType="VARCHAR" property="expressList" />
    <result column="account_book_id" jdbcType="BIGINT" property="accountBookId" />
    <result column="declare_company_id" jdbcType="BIGINT" property="declareCompanyId" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
  </resultMap>
</mapper>