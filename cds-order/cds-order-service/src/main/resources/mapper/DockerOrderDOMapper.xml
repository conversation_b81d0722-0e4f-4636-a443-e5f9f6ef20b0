<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.DockerOrderDOMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.DockerOrderDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="system_global_sn" jdbcType="VARCHAR" property="systemGlobalSn" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="route_code" jdbcType="VARCHAR" property="routeCode" />
    <result column="first_identify" jdbcType="VARCHAR" property="firstIdentify" />
    <result column="second_identify" jdbcType="VARCHAR" property="secondIdentify" />
    <result column="third_identify" jdbcType="VARCHAR" property="thirdIdentify" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
    <result column="insure_amount" jdbcType="DECIMAL" property="insureAmount" />
    <result column="tax_amount" jdbcType="DECIMAL" property="taxAmount" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="goods_sum_amount" jdbcType="DECIMAL" property="goodsSumAmount" />
    <result column="buyer_tel_number" jdbcType="VARCHAR" property="buyerTelNumber" />
    <result column="buyer_id_number" jdbcType="VARCHAR" property="buyerIdNumber" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="declare_pay_no" jdbcType="VARCHAR" property="declarePayNo" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="sender_name" jdbcType="VARCHAR" property="senderName" />
    <result column="pay_info_data_check_flag" jdbcType="INTEGER" property="payInfoDataCheckFlag" />
    <result column="trade_pay_no" jdbcType="VARCHAR" property="tradePayNo" />
    <result column="pay_transaction_id" jdbcType="VARCHAR" property="payTransactionId" />
    <result column="ver_dept" jdbcType="VARCHAR" property="verDept" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="pay_transaction_amount" jdbcType="DECIMAL" property="payTransactionAmount" />
    <result column="recp_code" jdbcType="VARCHAR" property="recpCode" />
    <result column="recp_name" jdbcType="VARCHAR" property="recpName" />
    <result column="recp_account" jdbcType="VARCHAR" property="recpAccount" />
    <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode" />
    <result column="pay_request_message" jdbcType="LONGVARCHAR" property="payRequestMessage" />
    <result column="pay_response_message" jdbcType="LONGVARCHAR" property="payResponseMessage" />
    <result column="orig_goods_info_json" jdbcType="LONGVARCHAR" property="origGoodsInfoJson" />
  </resultMap>
</mapper>