<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.suNingFP.impl.mapper.ChannelLoadInfoMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.suNingFP.impl.entity.ChannelLoadInfoDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="serial_no" jdbcType="VARCHAR" property="serialNo" />
    <result column="load_order_no" jdbcType="VARCHAR" property="loadOrderNo" />
    <result column="total_count" jdbcType="INTEGER" property="totalCount" />
    <result column="total_weight" jdbcType="DECIMAL" property="totalWeight" />
    <result column="total_tory_num" jdbcType="INTEGER" property="totalToryNum" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="audit_status" jdbcType="TINYINT" property="auditStatus" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
  </resultMap>
</mapper>