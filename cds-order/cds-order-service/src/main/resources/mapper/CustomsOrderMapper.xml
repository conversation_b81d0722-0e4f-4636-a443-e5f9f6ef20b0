<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.order.impl.mapper.CustomsOrderMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.order.impl.entity.CustomsOrderDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="customs_payment_sn" jdbcType="VARCHAR" property="customsPaymentSn" />
    <result column="customs_order_sn" jdbcType="VARCHAR" property="customsOrderSn" />
    <result column="customs_logistics_sn" jdbcType="VARCHAR" property="customsLogisticsSn" />
    <result column="customs_inventory_sn" jdbcType="VARCHAR" property="customsInventorySn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="ebp_id" jdbcType="BIGINT" property="ebpId" />
    <result column="ebc_id" jdbcType="BIGINT" property="ebcId" />
    <result column="pay_channel_id" jdbcType="BIGINT" property="payChannelId" />
    <result column="pay_company_id" jdbcType="BIGINT" property="payCompanyId" />
    <result column="agent_company_id" jdbcType="BIGINT" property="agentCompanyId" />
    <result column="declare_pay_no" jdbcType="VARCHAR" property="declarePayNo" />
    <result column="trade_pay_no" jdbcType="VARCHAR" property="tradePayNo" />
    <result column="freight" jdbcType="DECIMAL" property="freight" />
    <result column="tax" jdbcType="DECIMAL" property="tax" />
    <result column="discount" jdbcType="DECIMAL" property="discount" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="sender_name" jdbcType="VARCHAR" property="senderName" />
    <result column="express_id" jdbcType="BIGINT" property="expressId" />
    <result column="logistics_company_id" jdbcType="BIGINT" property="logisticsCompanyId" />
    <result column="buyer_id_number" jdbcType="VARCHAR" property="buyerIdNumber" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="consignee_tel" jdbcType="VARCHAR" property="consigneeTel" />
    <result column="consignee_email" jdbcType="VARCHAR" property="consigneeEmail" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="last_customs_time" jdbcType="TIMESTAMP" property="lastCustomsTime" />
    <result column="last_declare_time" jdbcType="TIMESTAMP" property="lastDeclareTime" />
    <result column="customs_detail" jdbcType="VARCHAR" property="customsDetail" />
    <result column="item_json" jdbcType="VARCHAR" property="itemJson" />
    <result column="note" jdbcType="VARCHAR" property="note" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
</mapper>