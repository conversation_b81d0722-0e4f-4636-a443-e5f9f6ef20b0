<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.endorsement.impl.mapper.EndorsementItemMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.endorsement.impl.entity.EndorsementItemDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="checklist_id" jdbcType="BIGINT" property="checklistId" />
    <result column="endorsement_id" jdbcType="BIGINT" property="endorsementId" />
    <result column="product_id" jdbcType="VARCHAR" property="productId" />
    <result column="goods_seq_no" jdbcType="VARCHAR" property="goodsSeqNo" />
    <result column="goods_name" jdbcType="VARCHAR" property="goodsName" />
    <result column="record_product_name" jdbcType="VARCHAR" property="recordProductName" />
    <result column="hs_code" jdbcType="VARCHAR" property="hsCode" />
    <result column="declare_unit_qfy" jdbcType="DECIMAL" property="declareUnitQfy" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />
    <result column="net_weight" jdbcType="DECIMAL" property="netWeight" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>

<!--  <update id="updateChecklist">-->
<!--    update `ccs_endorsement_item` set checklist_id = #{checklistId} where id = #{id}-->
<!--  </update>-->
</mapper>