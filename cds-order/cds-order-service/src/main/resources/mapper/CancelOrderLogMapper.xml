<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.inventory.impl.mapper.CancelOrderLogMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.inventory.impl.entity.CancelOrderLogDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="inventory_no" jdbcType="VARCHAR" property="inventoryNo" />
    <result column="audit_status" jdbcType="VARCHAR" property="auditStatus" />
    <result column="log_describe" jdbcType="VARCHAR" property="logDescribe" />
    <result column="log_detail" jdbcType="VARCHAR" property="logDetail" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="related_id" jdbcType="BIGINT" property="relatedId" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="deleted" jdbcType="TINYINT" property="deleted" />
  </resultMap>
</mapper>