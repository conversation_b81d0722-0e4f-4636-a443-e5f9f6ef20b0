<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.logistics.impl.mapper.CustomsLogisticsMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.logistics.impl.entity.CustomsLogisticsDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="logistics_status" jdbcType="TINYINT" property="logisticsStatus" />
    <result column="express_code" jdbcType="VARCHAR" property="expressCode" />
    <result column="express_id" jdbcType="BIGINT" property="expressId" />
    <result column="logistics_company_id" jdbcType="BIGINT" property="logisticsCompanyId" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="last_customs_time" jdbcType="TIMESTAMP" property="lastCustomsTime" />
    <result column="last_declare_time" jdbcType="TIMESTAMP" property="lastDeclareTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="consignee_name" jdbcType="VARCHAR" property="consigneeName" />
    <result column="consignee_province" jdbcType="VARCHAR" property="consigneeProvince" />
    <result column="consignee_city" jdbcType="VARCHAR" property="consigneeCity" />
    <result column="consignee_district" jdbcType="VARCHAR" property="consigneeDistrict" />
    <result column="consignee_address" jdbcType="VARCHAR" property="consigneeAddress" />
    <result column="consignee_tel" jdbcType="VARCHAR" property="consigneeTel" />
    <result column="logistics_no" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="fee_amount" jdbcType="DECIMAL" property="feeAmount" />
    <result column="gross_weight" jdbcType="DECIMAL" property="grossWeight" />
    <result column="item_json" jdbcType="VARCHAR" property="itemJson" />
    <result column="tags_json" jdbcType="VARCHAR" property="tagsJson" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
  </resultMap>
</mapper>