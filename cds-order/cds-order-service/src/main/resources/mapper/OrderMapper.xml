<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.order.impl.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.order.impl.entity.OrderDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="ebp_id" jdbcType="BIGINT" property="ebpId" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="exception_flag" jdbcType="BIT" property="exceptionFlag" />
    <result column="exception_type" jdbcType="TINYINT" property="exceptionType" />
    <result column="exception_detail" jdbcType="VARCHAR" property="exceptionDetail" />
    <result column="version" jdbcType="BIGINT" property="version" />
    <result column="action_json" jdbcType="VARCHAR" property="actionJson" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
  </resultMap>
</mapper>