<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.inventory.impl.mapper.CustomsInventoryCalloffMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.inventory.impl.entity.CustomsInventoryCalloff">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="exit_region_status" jdbcType="TINYINT" property="exitRegionStatus" />
    <result column="calloff_status" jdbcType="VARCHAR" property="calloffStatus" />
    <result column="calloff_type" jdbcType="TINYINT" property="calloffType" />
    <result column="calloff_reason" jdbcType="VARCHAR" property="calloffReason" />
    <result column="reject_reason" jdbcType="VARCHAR" property="rejectReason" />
    <result column="logistics_no" jdbcType="VARCHAR" property="logisticsNo" />
    <result column="ebc_id" jdbcType="BIGINT" property="ebcId" />
    <result column="agent_company_id" jdbcType="BIGINT" property="agentCompanyId" />
    <result column="user_name" jdbcType="VARCHAR" property="userName" />
    <result column="calloff_time" jdbcType="TIMESTAMP" property="calloffTime" />
  </resultMap>


</mapper>