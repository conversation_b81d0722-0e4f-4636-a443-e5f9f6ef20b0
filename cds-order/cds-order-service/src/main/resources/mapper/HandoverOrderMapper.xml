<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.handoverorder.impl.mapper.HandoverOrderMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.handoverorder.impl.entity.HandoverOrderDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="handoverSn" jdbcType="VARCHAR" property="handover_sn" />
    <result column="packageNum" jdbcType="INT" property="handover_sn" />
    <result column="totalWeight" jdbcType="DECIMAL" property="total_weight" />
    <result column="totalPalletsNum" jdbcType="INT" property="total_pallets_num" />
    <result column="wmsExpressName" jdbcType="VARCHAR" property="wms_express_name" />
    <result column="wmsExpressCode" jdbcType="VARCHAR" property="wms_express_code" />
    <result column="storeHouseName" jdbcType="VARCHAR" property="store_house_name" />
    <result column="storeHouseSn" jdbcType="VARCHAR" property="store_house_sn" />
    <result column="finish_status" jdbcType="TINYINT" property="finishStatus" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
</mapper>