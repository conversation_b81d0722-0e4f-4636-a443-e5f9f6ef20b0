<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.checklist.impl.mapper.ChecklistMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.checklist.impl.entity.ChecklistDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="pre_order_no" jdbcType="VARCHAR" property="preOrderNo" />
    <result column="real_order_no" jdbcType="VARCHAR" property="realOrderNo" />
    <result column="declare_company_id" jdbcType="BIGINT" property="declareCompanyId" />
    <result column="license_plate" jdbcType="VARCHAR" property="licensePlate" />
    <result column="license_frame" jdbcType="VARCHAR" property="licenseFrame" />
    <result column="car_weight" jdbcType="DECIMAL" property="carWeight" />
    <result column="frame_weight" jdbcType="DECIMAL" property="frameWeight" />
    <result column="account_book_id" jdbcType="BIGINT" property="accountBookId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="information_desc" jdbcType="VARCHAR" property="informationDesc" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="ie_flag" jdbcType="TINYINT" property="ieFlag" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
</mapper>