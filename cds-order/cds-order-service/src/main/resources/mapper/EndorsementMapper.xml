<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.endorsement.impl.mapper.EndorsementMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.endorsement.impl.entity.EndorsementDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="export_order_id" jdbcType="BIGINT" property="exportOrderId" />
    <result column="inventory_order_id" jdbcType="BIGINT" property="inventoryOrderId" />
    <result column="ie_flag" jdbcType="TINYINT" property="ieFlag" />
    <result column="checklists_flag" jdbcType="BIT" property="checklistsFlag" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="pre_order_no" jdbcType="VARCHAR" property="preOrderNo" />
    <result column="real_order_no" jdbcType="VARCHAR" property="realOrderNo" />
    <result column="account_book_id" jdbcType="BIGINT" property="accountBookId" />
    <result column="declare_company_id" jdbcType="BIGINT" property="declareCompanyId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="customs_status" jdbcType="VARCHAR" property="customsStatus" />
    <result column="information_desc" jdbcType="VARCHAR" property="informationDesc" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
</mapper>