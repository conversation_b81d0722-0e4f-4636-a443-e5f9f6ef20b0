<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.payinfo.impl.mapper.CustomsPaymentMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.payinfo.impl.entity.CustomsPaymentDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="out_order_no" jdbcType="VARCHAR" property="outOrderNo" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="pay_channel_id" jdbcType="BIGINT" property="payChannelId" />
    <result column="pay_company_id" jdbcType="BIGINT" property="payCompanyId" />
    <result column="ebp_id" jdbcType="BIGINT" property="ebpId" />
    <result column="merchant_code" jdbcType="VARCHAR" property="merchantCode" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="out_trade_no" jdbcType="VARCHAR" property="outTradeNo" />
    <result column="ver_dept" jdbcType="VARCHAR" property="verDept" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_id_type" jdbcType="VARCHAR" property="buyerIdType" />
    <result column="buyer_id_no" jdbcType="VARCHAR" property="buyerIdNo" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="trade_pay_no" jdbcType="VARCHAR" property="tradePayNo" />
    <result column="declare_pay_no" jdbcType="VARCHAR" property="declarePayNo" />
    <result column="pay_transaction_id" jdbcType="VARCHAR" property="payTransactionId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="tax_fee" jdbcType="DECIMAL" property="taxFee" />
    <result column="discount_fee" jdbcType="DECIMAL" property="discountFee" />
    <result column="transport_fee" jdbcType="DECIMAL" property="transportFee" />
    <result column="commodity_fee" jdbcType="DECIMAL" property="commodityFee" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="last_customs_time" jdbcType="TIMESTAMP" property="lastCustomsTime" />
    <result column="last_declare_time" jdbcType="TIMESTAMP" property="lastDeclareTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="tags_json" jdbcType="VARCHAR" property="tagsJson" />
    <result column="tenant_id" jdbcType="VARCHAR" property="tenantId" />
    <result column="umf_json" jdbcType="VARCHAR" property="umfJson" />
  </resultMap>
</mapper>