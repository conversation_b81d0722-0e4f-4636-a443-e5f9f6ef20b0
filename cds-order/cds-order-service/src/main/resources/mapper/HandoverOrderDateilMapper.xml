<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.handoverorder.impl.mapper.HandoverOrderDetailMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.handoverorder.impl.entity.HandoverOrderDetailDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="packageSn" jdbcType="VARCHAR" property="package_sn" />
    <result column="packageWeight" jdbcType="VARCHAR" property="package_weight" />
    <result column="wayBillSn" jdbcType="VARCHAR" property="way_bill_sn" />
    <result column="handoverSn" jdbcType="VARCHAR" property="handover_sn" />
    <result column="declareOrderNo" jdbcType="VARCHAR" property="declare_order_no" />
    <result column="expressCode" jdbcType="BIGINT" property="express_code" />
    <result column="expressName" jdbcType="VARCHAR" property="express_name" />
    <result column="outHouseTime" jdbcType="TIMESTAMP" property="out_house_time" />
    <result column="outboundOrder" jdbcType="VARCHAR" property="outbound_order" />
    <result column="storeHouseName" jdbcType="VARCHAR" property="store_house_name" />
    <result column="storeHouseSn" jdbcType="VARCHAR" property="store_house_sn" />
  </resultMap>
</mapper>