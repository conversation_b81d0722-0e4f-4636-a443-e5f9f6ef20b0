<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.payinfo.impl.mapper.ExtraPayInfoMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.payinfo.impl.entity.ExtraPayInfoDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="declare_order_sn" jdbcType="VARCHAR" property="declareOrderSn" />
    <result column="real_pay_no" jdbcType="VARCHAR" property="realPayNo" />
    <result column="trade_time" jdbcType="TIMESTAMP" property="tradeTime" />
    <result column="ebp_id" jdbcType="BIGINT" property="ebpId" />
    <result column="pay_company_id" jdbcType="BIGINT" property="payCompanyId" />
    <result column="pay_transaction_id" jdbcType="VARCHAR" property="payTransactionId" />
    <result column="ver_dept" jdbcType="VARCHAR" property="verDept" />
    <result column="pay_channel" jdbcType="VARCHAR" property="payChannel" />
    <result column="pay_way" jdbcType="VARCHAR" property="payWay" />
    <result column="pay_transaction_amount" jdbcType="DECIMAL" property="payTransactionAmount" />
    <result column="item_json" jdbcType="VARCHAR" property="itemJson" />
    <result column="recp_code" jdbcType="VARCHAR" property="recpCode" />
    <result column="recp_name" jdbcType="VARCHAR" property="recpName" />
    <result column="recp_account" jdbcType="VARCHAR" property="recpAccount" />
    <result column="pay_request_message" jdbcType="LONGVARCHAR" property="payRequestMessage" />
    <result column="pay_response_message" jdbcType="LONGVARCHAR" property="payResponseMessage" />
  </resultMap>
</mapper>