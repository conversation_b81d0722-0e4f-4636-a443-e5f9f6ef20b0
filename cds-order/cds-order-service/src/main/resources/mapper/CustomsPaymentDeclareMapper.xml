<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.common.generate.dao.CustomsPaymentDeclareMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.common.generate.DO.CustomsPaymentDeclare">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
    <result column="payment_sn" jdbcType="VARCHAR" property="paymentSn" />
    <result column="out_request_no" jdbcType="VARCHAR" property="outRequestNo" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="varchar" jdbcType="VARCHAR" property="varchar" />
    <result column="status" jdbcType="TINYINT" property="status" />
  </resultMap>
</mapper>