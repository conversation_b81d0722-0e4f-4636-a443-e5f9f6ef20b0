<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.payinfo.impl.mapper.CustomsPaymentMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.payinfo.impl.entity.CustomsPaymentDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="order_sn" jdbcType="VARCHAR" property="orderSn" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="declare_order_no" jdbcType="VARCHAR" property="declareOrderNo" />
    <result column="out_request_no" jdbcType="VARCHAR" property="outRequestNo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="customs" jdbcType="VARCHAR" property="customs" />
    <result column="channel" jdbcType="VARCHAR" property="channel" />
    <result column="trade_pay_no" jdbcType="VARCHAR" property="tradePayNo" />
    <result column="total_amount" jdbcType="DECIMAL" property="totalAmount" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="transpart_fee" jdbcType="DECIMAL" property="transpartFee" />
    <result column="commodity_fee" jdbcType="DECIMAL" property="commodityFee" />
    <result column="buyer_name" jdbcType="VARCHAR" property="buyerName" />
    <result column="buyer_id_type" jdbcType="VARCHAR" property="buyerIdType" />
    <result column="buyer_id_no" jdbcType="VARCHAR" property="buyerIdNo" />
    <result column="split_flag" jdbcType="TINYINT" property="splitFlag" />
    <result column="goods_info_json" jdbcType="VARCHAR" property="goodsInfoJson" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="last_declare_time" jdbcType="TIMESTAMP" property="lastDeclareTime" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
  </resultMap>
</mapper>