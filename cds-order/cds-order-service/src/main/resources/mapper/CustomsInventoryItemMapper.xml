<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.danding.cds.customs.inventory.impl.mapper.CustomsInventoryItemMapper">
  <resultMap id="BaseResultMap" type="com.danding.cds.customs.inventory.impl.entity.CustomsInventoryItemDO">
    <!--
      WARNING - @mbg.generated
    -->
    <result column="customs_inventory_id" jdbcType="BIGINT" property="customsInventoryId" />
    <result column="book_item_id" jdbcType="BIGINT" property="bookItemId" />
    <result column="item_no" jdbcType="VARCHAR" property="itemNo" />
    <result column="item_name" jdbcType="VARCHAR" property="itemName" />
    <result column="count" jdbcType="DECIMAL" property="count" />
    <result column="unit_price" jdbcType="DECIMAL" property="unitPrice" />
    <result column="note" jdbcType="LONGVARCHAR" property="note" />
    <result column="extra_json" jdbcType="LONGVARCHAR" property="extraJson" />
  </resultMap>
</mapper>