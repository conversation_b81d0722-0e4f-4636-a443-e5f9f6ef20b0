spring:
  main:
    allow-bean-definition-overriding: true
  cloud:
    nacos:
      config:
        encode: utf-8
        namespace: ${SPRING_CLOUD_NACOS_CONFIG_NAMESPACE:3dc6e16b-1bc3-4691-ab39-413cb67847e9}
        server-addr: ${SPRING_CLOUD_NACOS_CONFIG_SERVER_ADDR:nacos.conf.svc.cluster.local:8848}
        shared-configs:
          - data-id: application
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-dubbo
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-order
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-order-db
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-redis
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-job
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-rocketmq
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-es
            group: DEFAULT_GROUP
            refresh: true
          - data-id: cds-customs-config
            group: DEFAULT_GROUP
            refresh: true