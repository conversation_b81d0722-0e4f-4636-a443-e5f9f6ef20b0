from registry.cn-hangzhou.aliyuncs.com/danding/corp:openjdk-8-agent-1.2
VOLUME /home/<USER>
ADD target/ccs-order-service.jar ccs-order-service.jar
COPY src/main/resources/crt/edi1500.sditds.crt /etc/ssl/certs/
EXPOSE 8080
ENV JAVA_HOME="/usr/local/openjdk-8"
RUN curl -Os https://arthas.aliyun.com/arthas-boot.jar && \
ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
echo "Asia/Shanghai" > /etc/timezone && \
$JAVA_HOME/bin/keytool -import -keystore $JAVA_HOME/jre/lib/security/cacerts -storepass changeit -noprompt -file /etc/ssl/certs/edi1500.sditds.crt -alias edi1500.sditds
ENV JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF8  -Duser.timezone=GMT+08"
ENTRYPOINT exec java $JAVA_OPTS -Xms1g -Xmx2g -Djava.security.edg=file:/dev/./urandom -jar /ccs-order-service.jar
