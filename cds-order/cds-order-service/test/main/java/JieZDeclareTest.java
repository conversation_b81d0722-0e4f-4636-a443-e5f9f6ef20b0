package main.java;

import com.alibaba.fastjson.JSON;
import com.danding.cds.jieztech.JieztechCommonReq;
import com.danding.cds.jieztech.JieztechEwtpReq;
import com.github.kevinsawicki.http.HttpRequest;
import com.gvt.apollo.ApolloSdk;
import com.gvt.apollo.utils.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;

@Slf4j
public class JieZDeclareTest {

    @Test
    public void wrapSignAndRequest() throws Exception {
        String dxp = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><dxp:DxpMsg ver=\"1.0\"><dxp:TransInfo><dxp:CopMsgId>8ddbd382-b90a-4cec-a3f6-c9efcd09b650</dxp:CopMsgId><dxp:SenderId>DXPENT0000471915</dxp:SenderId><dxp:ReceiverIds><dxp:ReceiverId>DXPEDCCEB0000002</dxp:ReceiverId></dxp:ReceiverIds><dxp:CreatTime>2022-04-21T06:00:02.877+08:00</dxp:CreatTime><dxp:MsgType>CEB311Message</dxp:MsgType></dxp:TransInfo><dxp:Data>PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz48Y2ViOkNFQjMxMU1lc3NhZ2UgeG1sbnM6Y2ViPSJodHRwOi8vd3d3LmNoaW5hcG9ydC5nb3YuY24vY2ViIiB4bWxuczp4c2k9Imh0dHA6Ly93d3cudzMub3JnLzIwMDEvWE1MU2NoZW1hLWluc3RhbmNlIiBndWlkPSJGMzE1NUQ4NC03QjUyLTQ1NzktQTAxQS1FREQ2MTcxQ0Q1OEEiIHZlcnNpb249IjEuMCI+PGNlYjpPcmRlcj48Y2ViOk9yZGVySGVhZD48Y2ViOmd1aWQ+RjMxNTVEODQtN0I1Mi00NTc5LUEwMUEtRURENjE3MUNENThBPC9jZWI6Z3VpZD48Y2ViOmFwcFR5cGU+MTwvY2ViOmFwcFR5cGU+PGNlYjphcHBUaW1lPjIwMjIwNDIxMDYwMDAyPC9jZWI6YXBwVGltZT48Y2ViOmFwcFN0YXR1cz4yPC9jZWI6YXBwU3RhdHVzPjxjZWI6b3JkZXJUeXBlPkk8L2NlYjpvcmRlclR5cGU+PGNlYjpvcmRlck5vPjE4ODk2MTE2NTA0MzU2NzcwMDAwNTQzMzMyMTUxNzQwPC9jZWI6b3JkZXJObz48Y2ViOmVicENvZGU+MTEwNzk2MDkyMTwvY2ViOmVicENvZGU+PGNlYjplYnBOYW1lPuWkmueCueaWsOmynCjljJfkuqwp55S15a2Q5ZWG5Yqh5pyJ6ZmQ5YWs5Y+4PC9jZWI6ZWJwTmFtZT48Y2ViOmViY0NvZGU+MzMxODY2SzAwMjwvY2ViOmViY0NvZGU+PGNlYjplYmNOYW1lPuS5ieS5jOaloOasp+S+m+W6lOmTvueuoeeQhuaciemZkOWFrOWPuDwvY2ViOmViY05hbWU+PGNlYjpnb29kc1ZhbHVlPjE0NDAuMDA8L2NlYjpnb29kc1ZhbHVlPjxjZWI6ZnJlaWdodD4wLjAwMDA8L2NlYjpmcmVpZ2h0PjxjZWI6ZGlzY291bnQ+OTYuMDAwMDwvY2ViOmRpc2NvdW50PjxjZWI6dGF4VG90YWw+MC4wMDAwPC9jZWI6dGF4VG90YWw+PGNlYjphY3R1cmFsUGFpZD4xMzQ0LjAwMDA8L2NlYjphY3R1cmFsUGFpZD48Y2ViOmN1cnJlbmN5PjE0MjwvY2ViOmN1cnJlbmN5PjxjZWI6YnV5ZXJSZWdObz4xODM1NjA5ODE4MjwvY2ViOmJ1eWVyUmVnTm8+PGNlYjpidXllck5hbWU+6buE5pu85Li9PC9jZWI6YnV5ZXJOYW1lPjxjZWI6YnV5ZXJUZWxlcGhvbmU+MTgzNTYwOTgxODI8L2NlYjpidXllclRlbGVwaG9uZT48Y2ViOmJ1eWVySWRUeXBlPjE8L2NlYjpidXllcklkVHlwZT48Y2ViOmJ1eWVySWROdW1iZXI+MzQwNjAyMTk5MzA3MDgyNjI0PC9jZWI6YnV5ZXJJZE51bWJlcj48Y2ViOnBheUNvZGU+MzEyMjI2OTlTNzwvY2ViOnBheUNvZGU+PGNlYjpwYXlOYW1lPuaUr+S7mOWunSjkuK3lm70p572R57uc5oqA5pyv5pyJ6ZmQ5YWs5Y+4PC9jZWI6cGF5TmFtZT48Y2ViOnBheVRyYW5zYWN0aW9uSWQ+MTg4OTYxMTY1MDQzNTY3NzAwMDA1NDMzMzIxNTE3NDA8L2NlYjpwYXlUcmFuc2FjdGlvbklkPjxjZWI6Y29uc2lnbmVlPum7hOabvOS4vTwvY2ViOmNvbnNpZ25lZT48Y2ViOmNvbnNpZ25lZVRlbGVwaG9uZT4xODM1NjA5ODE4MjwvY2ViOmNvbnNpZ25lZVRlbGVwaG9uZT48Y2ViOmNvbnNpZ25lZUFkZHJlc3M+5a6J5b6955yBX+WQiOiCpeW4gl/ljIXmsrPljLpf5pif5rW35Z+OIDbmoIsyMzAz5a6kPC9jZWI6Y29uc2lnbmVlQWRkcmVzcz48L2NlYjpPcmRlckhlYWQ+PGNlYjpPcmRlckxpc3Q+PGNlYjpnbnVtPjE8L2NlYjpnbnVtPjxjZWI6aXRlbU5hbWU+YTLnmb3ph5HniYgz5q615am05YS/6YWN5pa55bm85YS/5aW257KJIO+8nyDvvJ8g77yfIO+8n+S4ieautTEtM+WygTkwMOWFizwvY2ViOml0ZW1OYW1lPjxjZWI6Z21vZGVsPiI0fDN86ISx6ISC5aW2IDI2Ljc0JSDjgIHkubPns5YgMjMuNjAl44CB5qSN54mp5rK5IDE0Ljk2JeetiXw5MDBHL+e9kHzkvpvlqbTlubzlhL/po5/nlKh8MS0z5bKBfGEyPC9jZWI6Z21vZGVsPjxjZWI6dW5pdD4xMjI8L2NlYjp1bml0PjxjZWI6cXR5PjY8L2NlYjpxdHk+PGNlYjpwcmljZT4yNDAuMDA8L2NlYjpwcmljZT48Y2ViOnRvdGFsUHJpY2U+MTQ0MC4wMDwvY2ViOnRvdGFsUHJpY2U+PGNlYjpjdXJyZW5jeT4xNDI8L2NlYjpjdXJyZW5jeT48Y2ViOmNvdW50cnk+NjA5PC9jZWI6Y291bnRyeT48L2NlYjpPcmRlckxpc3Q+PC9jZWI6T3JkZXI+PGNlYjpCYXNlVHJhbnNmZXI+PGNlYjpjb3BDb2RlPjMzMTg2NkswMDI8L2NlYjpjb3BDb2RlPjxjZWI6Y29wTmFtZT7kuYnkuYzmpaDmrKfkvpvlupTpk77nrqHnkIbmnInpmZDlhazlj7g8L2NlYjpjb3BOYW1lPjxjZWI6ZHhwTW9kZT5EWFA8L2NlYjpkeHBNb2RlPjxjZWI6ZHhwSWQ+RFhQRU5UMDAwMDQ3MTkxNTwvY2ViOmR4cElkPjwvY2ViOkJhc2VUcmFuc2Zlcj48ZHM6U2lnbmF0dXJlIHhtbG5zOmRzPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjIj48ZHM6U2lnbmVkSW5mbz48ZHM6Q2Fub25pY2FsaXphdGlvbk1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnL1RSLzIwMDEvUkVDLXhtbC1jMTRuLTIwMDEwMzE1I1dpdGhDb21tZW50cyIvPjxkczpTaWduYXR1cmVNZXRob2QgQWxnb3JpdGhtPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwLzA5L3htbGRzaWcjcnNhLXNoYTEiLz48ZHM6UmVmZXJlbmNlIFVSST0iIj48ZHM6VHJhbnNmb3Jtcz48ZHM6VHJhbnNmb3JtIEFsZ29yaXRobT0iaHR0cDovL3d3dy53My5vcmcvMjAwMC8wOS94bWxkc2lnI2VudmVsb3BlZC1zaWduYXR1cmUiLz48L2RzOlRyYW5zZm9ybXM+PGRzOkRpZ2VzdE1ldGhvZCBBbGdvcml0aG09Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvMDkveG1sZHNpZyNzaGExIi8+PGRzOkRpZ2VzdFZhbHVlPmluZzdmb1BtaG1UT3BBSll4VXNraGo4NkNrTT08L2RzOkRpZ2VzdFZhbHVlPjwvZHM6UmVmZXJlbmNlPjwvZHM6U2lnbmVkSW5mbz48ZHM6U2lnbmF0dXJlVmFsdWU+UGV0YnNDVTM0TGpqd01TWFNiaGM0MlhoZTBFRm5LVklNdHJibjN2aTNsUTFlUFpXTDBtZ0tpSG5QS1BiMDM1M0czQUt0alBBMTlwSQpobFVaZGlwczNZQlpnYzhXdlBYUExMV0tpb0kwbVFqV0dONjZSNGJOejUrVEdqOHRUWVZVbGNDWEswVWRTOW9nS1oxZVlRd21ReURuCjZ4NFhqYndEQTczZFVyUTRybHc9PC9kczpTaWduYXR1cmVWYWx1ZT48ZHM6S2V5SW5mbz48ZHM6S2V5TmFtZT43MjMzOTA2OTAxNDY2MTMwNzwvZHM6S2V5TmFtZT48ZHM6WDUwOURhdGE+PGRzOlg1MDlDZXJ0aWZpY2F0ZT5NSUlFcWpDQ0JCT2dBd0lCQWdJSUFRRUFBQUFBV0xzd0RRWUpLb1pJaHZjTkFRRUZCUUF3ZkRFTE1Ba0dBMVVFQmhNQ1kyNHhGVEFUCkJnTlZCQW9lREU0dFZ2MTFOVnRRVStOY3VERVZNQk1HQTFVRUN4NE1pOEZPWm51aGRBWk9MVi9ETVEwd0N3WURWUVFJSGdSVEYwNnMKTVNFd0h3WURWUVFESGhoT0xWYjlkVFZiVUU0YVVxR0x3VTVtZTZGMEJrNHRYOE14RFRBTEJnTlZCQWNlQkZNWFRxd3dIaGNOTWpFdwpPVEV6TURBd01EQXdXaGNOTkRFd09URXpNREF3TURBd1dqQ0J4VEVMTUFrR0ExVUVCaE1DUTA0eER6QU5CZ05WQkFnTUJ1YTFtZWF4Cm56RVBNQTBHQTFVRUJ3d0c1TG1KNUxtTU1UQXdMZ1lEVlFRS0RDZmt1WW5rdVl6bXBhRG1yS2ZrdnB2bHVwVHBrNzducnFIbmtJYm0KbklucG1aRGxoYXpsajdneE1EQXVCZ05WQkFzTUorUzVpZVM1ak9hbG9PYXNwK1MrbStXNmxPbVR2dWV1b2VlUWh1YWNpZW1aa09XRgpyT1dQdURFd01DNEdBMVVFQXd3bjVMbUo1TG1NNXFXZzVxeW41TDZiNWJxVTZaTys1NjZoNTVDRzVweUo2Wm1RNVlXczVZKzRNSUdmCk1BMEdDU3FHU0liM0RRRUJBUVVBQTRHTkFEQ0JpUUtCZ1FETUdVNVlyd05ycEFaRTN0YjU2ZUxueXJsaE0zekFPSk5IWVQ4d3lYRVoKTVdsN1lzWElWQ3pLOGdvWWY4b3pQcktZVzZuRkNMeHZoRDBMN2FpUU42dmRsNWxvWVg4Y3M4a2Rya1c5cms4c3NaT0FlMC9XdGpFSAovbGJXL216VU9VdDRZUEJDbHJYMW1Hck50ZnNsMnQyMW1NeTRNcWw4bnJzR0N3MjhKQkFyVHdJREFRQUJnUmtBMXRDNStyWG4xOVBTCnRjN3gxcVRLNmJuY3dPM1cwTkRFbzRJQnpqQ0NBY293RGdZRFZSMFBBUUgvQkFRREFnUDRNQXdHQTFVZEV3RUIvd1FDTUFBd0h3WUQKVlIwakJCZ3dGb0FVK1hXamVFVUxRbWpDQkZPcjY4Tk9QbFI0ZEdBd0hRWURWUjBPQkJZRUZEM1d0Vm56T1M4T3JwWXhsOVo5a2E4KwpqcHFmTUVRR0NTcUdTSWIzRFFFSkR3UTNNRFV3RGdZSUtvWklodmNOQXdJQ0FnQ0FNQTRHQ0NxR1NJYjNEUU1FQWdJQWdEQUhCZ1VyCkRnTUNCekFLQmdncWhraUc5dzBEQnpDQmdRWURWUjBmQkhvd2VEQjJvSFNnY29ad2JHUmhjRG92TDJ4a1lYQXVZMmhwYm1Gd2IzSjAKTG1kdmRpNWpiam96T0RrdlkyNDlZM0pzTURFd01UQXdMRzkxUFdOeWJEQXhMRzkxUFdOeWJDeGpQV051UDJObGNuUnBabWxqWVhSbApVbVYyYjJOaGRHbHZia3hwYzNRc0tqOWlZWE5sUDJOdVBXTnliREF4TURFd01EQWFCZ29yQmdFRUFhbERaQVVHQkF3TUNsTXdNREF3Ck1EYzNPVGd3R2dZS0t3WUJCQUdwUTJRRkNRUU1EQXBUTURBd01EQTNOems0TUJJR0Npc0dBUVFCcVVOa0FnRUVCQXdDTVRrd0VnWUsKS3dZQkJBR3BRMlFDQkFRRURBSkRRVEFlQmdoZ2hrZ0JodmhEQ1FRU0RCQXdNREF3TURBd01EQXdNREEzT0RnNU1DQUdDQ3FCSE5BVQpCQUVFQkJRVEVqa3hNek13TnpneVRVRXlUVEkyUmpNNFNqQU5CZ2txaGtpRzl3MEJBUVVGQUFPQmdRQnlFN202U3FVZWZnU1JaM2tFCitEbTJFZ0tjeDlHVjdqWGIzMDIweE1HK2xFTWZyYkJEOWxWWjZQSzY3SDBmWUhLUHlxRm11WVRJaG5rb0VwNHZLYmYxSGIyYktyRmwKSnE1TU8raTJ2YlQ3WlVJenRJL1J3aTQvWUVFTGJUMjZJSWlUOGU3b3FtdEFYSkpqL0R4UE0zT3FLTFJBTHRPTUxxZkV0T1VaRDNVTQpzQT09PC9kczpYNTA5Q2VydGlmaWNhdGU+PC9kczpYNTA5RGF0YT48L2RzOktleUluZm8+PC9kczpTaWduYXR1cmU+PC9jZWI6Q0VCMzExTWVzc2FnZT4=</dxp:Data></dxp:DxpMsg>";

//        String dxp = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><DxpMsg ver=\"1.0\"><TransInfo><CopMsgId>3e4094df-03ac-4dfd-948b-c9889b7db98b</CopMsgId><SenderId>DXPENT0000025678</SenderId><ReceiverIds><ReceiverId>DXPEDCCEB0000002</ReceiverId></ReceiverIds><CreatTime>2022-02-17T10:19:32.367+08:00</CreatTime><MsgType>CEB621Message</MsgType></TransInfo><Data>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</Data></DxpMsg>";
        JieztechEwtpReq req1 = new JieztechEwtpReq();
        req1.setSubMerchantId("207933138046289792");
        req1.setSubMerchantName("金华凡尔纳-清单通道");
        req1.setContent(dxp);
        req1.setAppid("6c0b855b55e34494a1d67dfb1809f34f");

        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey("MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2vbMY24hWWPOe045622JwWc6vuuXJ419UCn6sFKhoq69iTkhN/uSmXumurOxs8CjVfR+f4uHXoRt5mHMYO5jeBe8SgfvfXARf5AVL6d41PxH9I/ncIaRfWUWACF5svRKpf+Egv6k1JRe2sCelml5m+WtMEm4Tzo4cUs/qjJDTvAgMBAAECgYAbLvi3r0XXMiCoVBQKuslwFQeerCeHcCn+LNIVADh4Z45FC2DzymoOu9/Lbk6aKJCN9YmohMxqmU8OsDOPbrMvIVRLdFHuz4K6yfPzjE7C1S8GrBAvxL7LpN4DTff3pzCsGSsqRbyYgtPOmZpyJQ0EQa2EP2xjFHYB696xyBsxgQJBAP+RnsXqSy7w1M2jhZ9raOoGl6KJiu2wVf1oSLwsQMG1TPe4QbL65ioSlw1hGchypzVbwi43KXVpYMT47sZkCoMCQQC+AVmETRDsfeIBiJwRR+nZsq3H2cdWEpYiyXfDpksv1zv+gxfod/Rwax9Nuz5npxej/LlC97JBy6Ns7wIVF5AlAkEAvNiy1RZixeXpqaaad4mb9co3RECUazyw3dawYHPmyfyZSjdaPNIPP6mK+rT0o1ytV81c+F+EgCEFA6facLi91wJADl8Rw0UE+65F0vHfRBtZX8L5C/236xW6z2THrz+7viGcgxKtU0MHdR1VH88C2Fo2Gow6AwuzKvDZKpslYC0JFQJAP9aIG+B56DJb7CEyz5tnKeepmJ9a/dLFbd+iivDYKZxsMqBmpX+kZix0+e7NCf3wT1DAWdjScdWyTdH+ojsAkw=="), JSON.toJSONString(req1));
        req1.setSign(requestJson);
        log.info("响应报文 -{}", requestJson);
        HttpRequest httpRequest = HttpRequest.post("http://gwfat.kldidi.com/aoa/api/aoa/publicServicePlatformFacade/receiveInventorySignReport")
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .send(requestJson);
        String body = httpRequest.body();
        System.out.println(JSON.toJSONString(body));
        log.info("响应报文 - {}", body);
    }

    @Test
    public void sendOrder() throws Exception {
        String dxp = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><dxp:DxpMsg xmlns:dxp=\"http://www.chinaport.gov.cn/dxp\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\" xmlns=\"http://www.chinaport.gov.cn/dxp\" xmlns:ds=\"http://www.w3.org/2000/09/xmldsig#\" ver=\"1.0\"><dxp:TransInfo><dxp:CopMsgId>8ddbd382-b90a-4cec-a3f6-c9efcd09b650</dxp:CopMsgId><dxp:SenderId>DXPENT0000471915</dxp:SenderId><dxp:ReceiverIds><dxp:ReceiverId>DXPEDCCEB0000002</dxp:ReceiverId></dxp:ReceiverIds><dxp:CreatTime>2022-04-21T06:00:02.877+08:00</dxp:CreatTime><dxp:MsgType>CEB311Message</dxp:MsgType></dxp:TransInfo><dxp:Data>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</dxp:Data></dxp:DxpMsg>";

        String key = "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAMqKpgqqx6X9XEG0/BvbVYfklsepEH5EtZwu7AIn16n7Vjq/8UBWN62O3y1QXIwvqSq544Svtvwviyn4pkhV7VXRx7iobZAmDXY61jJ78FXZWIaH9mh3Y94gDRp+ixPtfGlqN2pyTuQX9ucKoIroh81hIjB7Ws/LPicujNAFXFC5AgMBAAECgYA3SGDJyJN12xGzXZLyh4tw7BO7q4m07UwktSn17KoQ22lN39YZmW7ZEIYZ+DVKjje+Ub6oGM35R4DUk+nPFKUSqrEV0ffx6Ls2a+xr2wsF54V4PCaCV/giwM0Jny3VGbPz9DmNdD1eWaO/Y+ng4jFIl8iDy1alcCdMp243GwxzgQJBAPp3+Q22hgUwhoJq0f+X0vhrnNZGMENa2s10CG/qErsMZq0uebU0wzZnwg8FlCdTZeN9MK5AGt2fMpAhYlq664MCQQDPA7g7uFuivG7uawldyvzjgqtjZSKzbWHDfPxbsEj9Lz4oASGLB4tBs2xQp0KaedwkGO0y2eTqsNyYFOUPyfITAkEArHRWoz59YK1csAdkuBuNQ0a1IkQJjxbLeJLgDE6avvd26t8JJd9CWf9u+nDY5YriqJPWJOQ2m0m1ws95rQbuiQJBAJgpt8XN4S1hLHj7HEtg5Tml1qycrkEM6Ytz11rd3QidLSbijfFPfkCwzbELAJ3jbLjryY5TMzx+++adlTFh0xkCQAvCFwX87ruBqFSTuppQULC5jQRIv0f6aAOsqyd+Vfu35QVmRrhg0MfJoUTtKltuIKxYZkW3Aqq2feTxrd48ubU=";

        String message = new String(dxp.getBytes(StandardCharsets.UTF_8));


        JieztechEwtpReq req1 = new JieztechEwtpReq();
        req1.setSubMerchantId("207933138046289792");
        req1.setSubMerchantName("金华凡尔纳-清单通道");
        req1.setContent(message);
        req1.setAppid("ebddb43c13974cb0b6e406b02529660f");

        ApolloSdk apolloSdk = new ApolloSdk();
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(key), JSON.toJSONString(req1));
        log.info("请求报文 -{}", requestJson);
        HttpRequest httpRequest = HttpRequest.post("http://apollo.jieztech.com/aoa/api/aoa/publicServicePlatformFacade/receiveOrderSignReport")
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .send(requestJson);
        String body = httpRequest.body();
        System.out.println(JSON.toJSONString(body));
        log.info("响应报文 - {}", body);
    }




}