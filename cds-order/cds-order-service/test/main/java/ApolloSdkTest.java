package main.java;
import java.math.BigDecimal;

import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.declare.sdk.utils.DateUtil;
import com.danding.cds.jieztech.JieztechCallbackReq;
import com.danding.cds.jieztech.JieztechInventoryHead;
import com.danding.cds.jieztech.JieztechInventoryItem;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.jieztech.JieztechInventoryReq;
import com.danding.common.utils.DateUtils;
import com.github.kevinsawicki.http.HttpRequest;
import com.gvt.apollo.ApolloSdk;
import com.gvt.apollo.bean.BizBean;
import com.gvt.apollo.utils.SecurityUtils;
import lombok.Data;
import lombok.ToString;
import org.junit.Test;

import javax.crypto.BadPaddingException;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import java.io.UnsupportedEncodingException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.security.SignatureException;
import java.security.spec.InvalidKeySpecException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ApolloSdkTest {
    @Data
    @ToString
    public class Shop implements BizBean {
        private String appid;
        private String shopName;
        private String shopLocation;
        private Member member=new Member();
        private String sign;
        @Override
        public void setSign(String sign) {
            this.sign=sign;
        }
    }
    @Data
    @ToString
    public class Member{
        private String memberId;
        private String memberName;
    }
    /**
     * 个人私钥
     */
    private String personPriKey="MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAL2vbMY24hWWPOe045622JwWc6vuuXJ419UCn6sFKhoq69iTkhN/uSmXumurOxs8CjVfR+f4uHXoRt5mHMYO5jeBe8SgfvfXARf5AVL6d41PxH9I/ncIaRfWUWACF5svRKpf+Egv6k1JRe2sCelml5m+WtMEm4Tzo4cUs/qjJDTvAgMBAAECgYAbLvi3r0XXMiCoVBQKuslwFQeerCeHcCn+LNIVADh4Z45FC2DzymoOu9/Lbk6aKJCN9YmohMxqmU8OsDOPbrMvIVRLdFHuz4K6yfPzjE7C1S8GrBAvxL7LpN4DTff3pzCsGSsqRbyYgtPOmZpyJQ0EQa2EP2xjFHYB696xyBsxgQJBAP+RnsXqSy7w1M2jhZ9raOoGl6KJiu2wVf1oSLwsQMG1TPe4QbL65ioSlw1hGchypzVbwi43KXVpYMT47sZkCoMCQQC+AVmETRDsfeIBiJwRR+nZsq3H2cdWEpYiyXfDpksv1zv+gxfod/Rwax9Nuz5npxej/LlC97JBy6Ns7wIVF5AlAkEAvNiy1RZixeXpqaaad4mb9co3RECUazyw3dawYHPmyfyZSjdaPNIPP6mK+rT0o1ytV81c+F+EgCEFA6facLi91wJADl8Rw0UE+65F0vHfRBtZX8L5C/236xW6z2THrz+7viGcgxKtU0MHdR1VH88C2Fo2Gow6AwuzKvDZKpslYC0JFQJAP9aIG+B56DJb7CEyz5tnKeepmJ9a/dLFbd+iivDYKZxsMqBmpX+kZix0+e7NCf3wT1DAWdjScdWyTdH+ojsAkw==";
    private String personPubKey="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCnIZpC42a4R5TdVtOF/F3bfZoHMmf6CQTuEfmYVgeCiVGv8fI1o+gLJ4x2M2JsGeSSU9GQLHD+KzUR8/4AmhjDpju8hiBxkPa/UpLWObJ+DK+7CJwf8TAPyCSfYuZ6yb8VNyvaw5jceke4SF2Q2wY9457Ar7RipEcpwWlZk3VPIwIDAQAB";

    @Test
    public void wrapJsonSign() throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException, SignatureException, InvalidKeyException {
        ApolloSdk apolloSdk=new ApolloSdk();
        String json="{\"appid\":\"rr333sss7788\",\"member\":{\"memberId\":\"11\",\"memberName\":\"张三\"},\"shopLocation\":\"深圳南山新区西丽街道\",\"shopName\":\"apos门店\"}";
        System.out.println(apolloSdk.wrapSign(SecurityUtils.getPriKey(personPriKey), json));
    }
    @Test
    public void validateSign() throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException, SignatureException, InvalidKeyException {
        ApolloSdk apolloSdk=new ApolloSdk();
        String json="{\"shopLocation\":\"深圳南山新区西丽街道\",\"appid\":\"rr333sss7788\",\"member\":{\"memberName\":\"张三\",\"memberId\":\"11\"},\"sign\":\"QEQLxPTNGLVgoQL/WD5qvsZPLNUisDR1vqJ/MsJ02eXhhSg9PAztk2Ju7yUrBmnhoWCDt6V3jy2KwifwqfjP5glBf76/5A4tR3CYzRp1Q4M2u0AfSiuI5mWptwqNBRUsg/5IazCeOlKEQn2emYiluaxYM2WF7BbIv1zmvjM5Zw4=\",\"shopName\":\"apos门店\"}";
        System.out.println(apolloSdk.validateSign(SecurityUtils.getPubKey(personPubKey), json));
    }

    @Test
    public void sendTest() throws InvalidKeySpecException, NoSuchAlgorithmException, NoSuchPaddingException, UnsupportedEncodingException, IllegalBlockSizeException, BadPaddingException, SignatureException, InvalidKeyException {
        ApolloSdk apolloSdk = new ApolloSdk();
        JieztechInventoryReq req = new JieztechInventoryReq();
        req.setAppid("6c0b855b55e34494a1d67dfb1809f34f");
        req.setSubMerchantId("195492459416389376");
        req.setSubMerchantName("商户19988880003");
        req.setMerchantId("167734445620070272");
        req.setMerchantName("全链路18888880003");
        List<JieztechInventoryHead> inventoryHeadList = new ArrayList<>();
        JieztechInventoryHead inventoryHead = new JieztechInventoryHead();
        inventoryHead.setCommitTime(DateUtil.dateToString(new Date(System.currentTimeMillis()), DateUtil.DATE_DEF_PATTERN));
        inventoryHead.setOrderNo("DO123456");
        inventoryHead.setEbpCode("ebp123");
        inventoryHead.setEbpName("电商平台代码");
        inventoryHead.setEbcCode("ebc123");
        inventoryHead.setEbcName("电商企业代码");
        inventoryHead.setPayCode("pay123");
        inventoryHead.setPayName("支付企业123");
        inventoryHead.setLogisticsNo("MOCKSF123456");
        inventoryHead.setLogisticsCode("logistics123");
        inventoryHead.setLogisticsName("物流企业123");
        inventoryHead.setCopNo("CI123456");
        inventoryHead.setAssureCode("Assure123");
        inventoryHead.setEmsNo("T2924W0000088");
        inventoryHead.setInvtNo("29242021I316778332");
        inventoryHead.setDeclTime(DateUtil.getCDateString("YYYYMMdd"));
        inventoryHead.setCustomsCode("2924");
        inventoryHead.setPortCode("2924");
        inventoryHead.setIeDate(DateUtil.getTCurrentDate("YYYYMMdd"));
        inventoryHead.setBuyerIdNumber("330283199705101111");
        inventoryHead.setBuyerName("沈辉");
        inventoryHead.setBuyerTelephone("15958006666");
        inventoryHead.setConsignee("沈辉");
        inventoryHead.setConsigneeTelephone("15958006666");
        inventoryHead.setConsigneePrvince("浙江省");
        inventoryHead.setConsigneeCity("杭州市");
        inventoryHead.setConsigneeCounty("下城区");
        inventoryHead.setConsigneeAddress("钱江新城");
        inventoryHead.setAgentCode("Agent123");
        inventoryHead.setAgentName("申报企业123");
        inventoryHead.setAreaCode("Area123");
        inventoryHead.setAreaName("区内企业123");
        inventoryHead.setLoctNo("292401");
        inventoryHead.setActuralPaid(new BigDecimal("100"));
        inventoryHead.setDiscount(new BigDecimal("0"));
        inventoryHead.setTaxTotal(new BigDecimal("8.34"));
        inventoryHead.setFreight(new BigDecimal("0"));
        inventoryHead.setInsuredFee(new BigDecimal("0"));
        inventoryHead.setGoodsValue(new BigDecimal("91.66"));
        inventoryHead.setPayTransactionId("42000000000000011111");
        inventoryHead.setGrossWeight("1.0");
        inventoryHead.setNetWeight("1.0");
        List<JieztechInventoryItem> inventoryList = new ArrayList<>();
        JieztechInventoryItem inventoryItem = new JieztechInventoryItem();
        inventoryItem.setGnum("1");
        inventoryItem.setItemRecordNo("1056");
        inventoryItem.setItemNo("69302768232");
        inventoryItem.setItemName("Earth's Best 爱思贝有机燕麦粉227g");
        inventoryItem.setGcode("1901109000");
        inventoryItem.setGname("Earth's Best 爱思贝有机燕麦粉227g");
        inventoryItem.setGmodel("227g/盒");
        inventoryItem.setCountry("301");
        inventoryItem.setCurrency("142");
        inventoryItem.setQty("1");
        inventoryItem.setQty1("1.0");
        inventoryItem.setQty2("");
        inventoryItem.setUnit("140");
        inventoryItem.setUnit1("035");
        inventoryItem.setUnit2("");
        inventoryItem.setPrice("91.66");
        inventoryItem.setTotalPrice("91.66");
        inventoryList.add(inventoryItem);
        inventoryHead.setList(inventoryList);
        inventoryHeadList.add(inventoryHead);
        req.setInventoryHeadList(inventoryHeadList);
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(personPriKey), JSON.toJSONString(req));
        System.out.println(requestJson);
        HttpRequest httpRequest = HttpRequest.post("http://gwfat.kldidi.com/aoa/api/aoa/publicServicePlatformFacade/inventory")
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .send(requestJson);
        if (httpRequest.ok()) {
            String body = httpRequest.body();
            System.out.println(body);
        }
    }

    @Test
    public void callBackTest() throws Exception {
        ApolloSdk apolloSdk = new ApolloSdk();
        JieztechCallbackReq req = new JieztechCallbackReq();
        req.setAppid("6c0b855b55e34494a1d67dfb1809f34f");
        req.setCustomsCode("2924");
        req.setEbpCode("ebp123");
        req.setEbcCode("ebc123");
        req.setAgentCode("Agent123");
        req.setCopNo("CopNo123");
        req.setPreNo("PreNo123");
        req.setInvtNo("InvtNo123");
        req.setReturnStatus(CustomsStat.CUSTOMS_PASS.getValue());
        req.setReturnTime(DateUtil.getCDateString("YYYYMMddHHmmssSSS"));
        req.setReturnInfo("放行");
        String requestJson = apolloSdk.wrapSign(SecurityUtils.getPriKey(personPriKey), JSON.toJSONString(req));
        System.out.println(requestJson);
        HttpRequest httpRequest = HttpRequest.post("http://gwfat.kldidi.com/aoa/api/declare/entry/hzCrossAgentCallback")
                .header("Content-Type", HttpRequest.CONTENT_TYPE_JSON)
                .send(requestJson);
        if (httpRequest.ok()) {
            String body = httpRequest.body();
            System.out.println(body);
        }
    }
}
