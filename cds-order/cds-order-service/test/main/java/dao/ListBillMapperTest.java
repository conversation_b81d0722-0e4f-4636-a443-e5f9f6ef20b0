package dao;

import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.order.api.test.BaseTest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.concurrent.CountDownLatch;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/26 14:53
 * @Description:
 */
@Slf4j
public class ListBillMapperTest extends BaseTest {

    @Autowired
    private SequenceService sequenceService;

    @Test
    public void seq()
    {
        long time = System.currentTimeMillis() ;
        final CountDownLatch countDownLatch = new CountDownLatch(5) ;
        for(int i=0;i<5;i++){
            final int num = i ;
            new Thread(new Runnable() {
                public void run() {

                    for(int i = 0; i<30; i++)
                        System.out.println("id = "+ sequenceService.nextSequence("order_code"));

                    /**
                     * 使用CountDownLatch时要注意异常情况,一旦没处理好导致countDownLatch.countDown()没执行会引起线程阻塞,导致CPU居高不下
                     if(num==3)
                     System.out.println(Integer.parseInt("1.233"));
                     **/
                    countDownLatch.countDown();
                    System.out.println(Thread.currentThread().getName()+"运行结束  运行时间为："+num
                            +"秒  countDownLatch="+countDownLatch.getCount());
                }
            }).start();
        }
        try {
            countDownLatch.await();
        } catch (InterruptedException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
        System.out.println("总耗时==="+(System.currentTimeMillis()-time));
    }
}
