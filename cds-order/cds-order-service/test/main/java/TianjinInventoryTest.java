package main.java;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.XmlUtil;
import com.danding.cds.declare.sdk.clear.base.callback.module.InventoryCancel;
import com.danding.cds.declare.sdk.clear.tianjin.builder.TJInventoryBuilder;
import com.danding.cds.declare.sdk.clear.tianjin.builder.TJInventoryCancelBuilder;
import com.danding.cds.declare.sdk.model.cancel.WarpCancelOrderInfo;
import com.danding.cds.declare.sdk.model.inventory.WrapInventoryOrderInfo;
import com.danding.cds.declare.tjport.domain.ent621.ENT621Message;
import com.danding.cds.declare.tjport.domain.ent622.ENT622Message;
import com.danding.cds.declare.tjport.domain.ent622.ENT622MessageReturn;
import com.danding.cds.declare.tjport.domain.ent623.ENT623Message;
import com.danding.cds.declare.tjport.domain.ent624.ENT624Message;
import com.danding.cds.declare.tjport.domain.ent624.ENT624MessageReturn;
import com.thoughtworks.xstream.XStream;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TianjinInventoryTest {
    @Test
    public void inventoryDeclare() {
        String sss = "{\"assureCompanyDTO\":{\"cebCode\":\"3201966A69\",\"cebName\":\"江苏苏宁易购电子商务有限公司\",\"code\":\"3201966A69\",\"name\":\"江苏苏宁易购电子商务有限公司\"},\"customsInventoryDto\":{\"bookNo\":\"T0213W000334\",\"buyerIdNumber\":\"441283198712276461\",\"buyerIdType\":\"1\",\"buyerName\":\"李燕贞\",\"buyerTelNumber\":\"13684923597\",\"clientCustoms\":\"tianjin\",\"consigneeAddress\":\"广东省_肇庆市_高要区_蚬岗镇蚬二三村\",\"createTime\":1617790543000,\"customs\":\"TIANJIN\",\"customsField\":\"\",\"discountFee\":0,\"ebpCode\":\"3201966A69\",\"freight\":0.0000,\"grossWeight\":1.3300,\"id\":586619311179694081,\"insureAmount\":0.0000,\"logisticsNo\":\"SF1900026568480\",\"mainGName\":\"帮宝适一级棒拉拉裤XL46\",\"netWeight\":1.3300,\"orderNo\":\"AAAA21010001116902087200\",\"payTransactionId\":\"420000094520210224616242948600\",\"sn\":\"CI2104071815574202\",\"status\":10,\"taxFee\":7.88},\"declareCompanyDTO\":{\"cebCode\":\"120766K058\",\"cebName\":\"天津怡塔供应链管理有限公司\",\"code\":\"120766K058\",\"dxpId\":\"DXPENT0000022917\",\"name\":\"天津怡塔供应链管理有限公司\"},\"declareLogisticsInSystem\":false,\"ebcCompanyDTO\":{\"cebCode\":\"3201966A69\",\"cebName\":\"江苏苏宁易购电子商务有限公司\",\"code\":\"3201966A69\",\"name\":\"江苏苏宁易购电子商务有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"3201966A69\",\"cebName\":\"江苏苏宁易购电子商务有限公司\",\"code\":\"3201966A69\",\"name\":\"江苏苏宁易购电子商务有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"1207662049\",\"cebName\":\"天津代塔供应链管理有限公司\",\"code\":\"1207662049\",\"name\":\"天津代塔供应链管理有限公司\"},\"listCustomsInventoryItemInfo\":[{\"barCode\":\"000000011696449994\",\"count\":1,\"country\":\"116\",\"declIINo\":\"12150012102030006\",\"firstCount\":\"1.3300\",\"gmodle\":\"4|3|婴儿用|帮宝适|一级棒|XL|46片/包|1.33kg\",\"goodsRegNo\":\"S121500202004000394\",\"hsCode\":\"9619001100\",\"id\":586619311192276992,\"ioGoodsSerialNo\":\"12150012102030006873E\",\"itemName\":\"帮宝适一级棒拉拉裤XL46\",\"itemNo\":\"000000011696449994\",\"itemRecordNo\":\"484\",\"originCountryCode\":\"392\",\"secondCount\":\"1\",\"unit\":\"125\",\"unit1\":\"035\",\"unitPrice\":86.6000,\"weight\":1.33}],\"logisticDeclareCompany\":{},\"logisticsCompanyDTO\":{\"cebCode\":\"4403180939\",\"cebName\":\"顺丰速运有限公司\",\"code\":\"4403180939\",\"name\":\"顺丰速运有限公司\"},\"payCompanyDTO\":{\"cebCode\":\"4403169D3W\",\"cebName\":\"财付通支付科技有限公司\",\"code\":\"4403169D3W\",\"name\":\"财付通支付科技有限公司\"},\"step\":1}";
        WrapInventoryOrderInfo info = JSON.parseObject(sss, WrapInventoryOrderInfo.class);
        TJInventoryBuilder builder = new TJInventoryBuilder(info);
        ENT621Message ent621Message = builder.build();

        try {
            String content = XmlUtil.buildXml(ent621Message, new Class[]{ENT621Message.class});
            System.out.println(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void inventoryCancelDeclare() {
        String sss = "{\"customsInventoryCancelInfo\":{\"id\":2106021446057824,\"sn\":\"CD2106021446057824\",\"status\":\"INT\"},\"customsInventoryDto\":{\"bookNo\":\"T0213W000334\",\"buyerIdNumber\":\"370602197712241015\",\"buyerIdType\":\"1\",\"buyerName\":\"禹啟海\",\"buyerTelNumber\":\"18953537123\",\"clientCustoms\":\"tianjin\",\"consigneeAddress\":\"山东省烟台市芝罘区烟台市芝罘区全区金沟寨五街9-9\",\"createTime\":1621390834000,\"customs\":\"TIANJIN\",\"customsField\":\"\",\"ebpCode\":\"3201966A69\",\"freight\":10.0000,\"id\":601720026029883393,\"inventoryNo\":\"02132021I405129538\",\"logisticsNo\":\"SF1900129508836\",\"mainGName\":\"德运DEVONDALE 全脂牛奶 10x1L\",\"orderNo\":\"AAAA21010001620264234200\",\"preNo\":\"B20210519049788905\",\"sn\":\"CI2105191020394150\"},\"declareCompanyDTO\":{\"cebCode\":\"120766K058\",\"cebName\":\"天津怡塔供应链管理有限公司\",\"code\":\"120766K058\",\"dxpId\":\"DXPENT0000022917\",\"name\":\"天津怡塔供应链管理有限公司\"},\"ebcCompanyDTO\":{\"cebCode\":\"3201966A69\",\"cebName\":\"江苏苏宁易购电子商务有限公司\",\"code\":\"3201966A69\",\"name\":\"江苏苏宁易购电子商务有限公司\"},\"ebpCompanyDTO\":{\"cebCode\":\"3201966A69\",\"cebName\":\"江苏苏宁易购电子商务有限公司\",\"code\":\"3201966A69\",\"name\":\"江苏苏宁易购电子商务有限公司\"},\"internalAreaCompany\":{\"cebCode\":\"1207662049\",\"cebName\":\"天津代塔供应链管理有限公司\",\"code\":\"1207662049\",\"name\":\"天津代塔供应链管理有限公司\"},\"logisticsCompanyDTO\":{\"cebCode\":\"4403180939\",\"cebName\":\"顺丰速运有限公司\",\"code\":\"4403180939\",\"name\":\"顺丰速运有限公司\"}}";
        WarpCancelOrderInfo info = JSON.parseObject(sss, WarpCancelOrderInfo.class);
        TJInventoryCancelBuilder builder = new TJInventoryCancelBuilder(info);
        ENT623Message ent621Message = builder.build();

        try {
            String content = XmlUtil.buildXml(ent621Message, new Class[]{ENT623Message.class});
            System.out.println(content);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void inventoryCallback() {
        String callback= "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<ENT622Message xmlns=\"http://www.chinaport.gov.cn/ENT\" guid=\"f13b56f7-a227-46e8-9b56-078e1d5fc2aa\" version=\"1.0\">\n" +
                "    <InventoryReturn>\n" +
                "        <guid>f13b56f7-a227-46e8-9b56-078e1d5fc2aa</guid>\n" +
                "        <customsCode>0213</customsCode>\n" +
                "        <ebpCode>3201966A69</ebpCode>\n" +
                "        <ebcCode>3201966A69</ebcCode>\n" +
                "        <agentCode>120766K058</agentCode>\n" +
                "        <copNo>341921022410923187</copNo>\n" +
                "        <returnStatus>-621041</returnStatus>\n" +
                "        <returnTime>20210406175315633</returnTime>\n" +
                "        <returnInfo>存在已申报清单[电商企业编码：3201966A69,订单编号：AAAA21010001116902087200,清单编号：02132021I134688050,清单状态：800-实货放行],无法进行新增申报操作;;</returnInfo>\n" +
                "    </InventoryReturn>\n" +
                "</ENT622Message>";
        ENT622Message ent622Message = new ENT622Message();
        XStream xStream = new XStream();
        xStream.alias("InventoryReturn", ENT622MessageReturn.class);
        xStream.alias("ENT622Message", ENT622Message.class);
        xStream.fromXML(callback, ent622Message);
        ENT622MessageReturn messageReturn = ent622Message.getInventoryReturn();
        System.out.println(messageReturn);
    }

    @Test
    public void inventoryCancelCallback() {
        List<InventoryCancel> cancelList = new ArrayList<>();
        String callback= "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?>\n" +
                "<ENT624Message\n" +
                "    xmlns=\"http://www.chinaport.gov.cn/ENT\" guid=\"431303D5-FBBE-4DF8-96D2-6E8F9202788D\" version=\"1.0\">\n" +
                "    <InvtCancelReturn>\n" +
                "        <guid>431303D5-FBBE-4DF8-96D2-6E8F9202788D</guid>\n" +
                "        <customsCode>0213</customsCode>\n" +
                "        <agentCode>120766K058</agentCode>\n" +
                "        <ebpCode>3201966A69</ebpCode>\n" +
                "        <ebcCode>3201966A69</ebcCode>\n" +
                "        <copNo>CD2106021446057824</copNo>\n" +
                "        <preNo>B20210519049788905</preNo>\n" +
                "        <invtNo>02132021I405129538</invtNo>\n" +
                "        <returnStatus>-623009</returnStatus>\n" +
                "        <returnTime>20210614101848984</returnTime>\n" +
                "        <returnInfo>撤销申请单[02132021I405129538]已经申报过</returnInfo>\n" +
                "    </InvtCancelReturn>\n" +
                "</ENT624Message>";
        ENT624Message ent624Message = new ENT624Message();
        XStream xStream = new XStream();
        xStream.alias("InvtCancelReturn", ENT622MessageReturn.class);
        xStream.alias("ENT624Message", ENT622Message.class);
        xStream.fromXML(callback, ent624Message);
        ENT624MessageReturn messageReturn = ent624Message.getInvtCancelReturn();
        InventoryCancel cancel = new InventoryCancel();
        cancel.setEbpCode(messageReturn.getEbpCode());
        cancel.setID(messageReturn.getCopNo());
        Date returnDate = null;
        try {
            returnDate = DateUtils.parseDate(messageReturn.getReturnTime(), "yyyyMMddHHmmssSSS");
        } catch (ParseException e) {
            e.printStackTrace();
        }
        cancel.setReturnTime(returnDate);
        cancel.setReturnStatus(messageReturn.getReturnStatus());
        cancel.setReturnInfo(messageReturn.getReturnInfo());
        cancelList.add(cancel);
        System.out.println(JSON.toJSONString(cancelList));
    }
}
