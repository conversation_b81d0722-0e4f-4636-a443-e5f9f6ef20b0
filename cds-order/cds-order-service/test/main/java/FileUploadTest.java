import com.alibaba.excel.EasyExcel;
import com.danding.cds.common.utils.FileOpsUtil;
import com.danding.cds.v2.bean.dto.CustomsDetailStatisticsDto;
import org.junit.Test;

import java.io.ByteArrayOutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

/**
 * @program: cds-center
 * @description: 文件上次测试
 * @author: 潘本乐（Belep）
 * @create: 2022-03-08 21:17
 **/
public class FileUploadTest {

    @Test
    public void upload() throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");

        Calendar calendar = Calendar.getInstance();
        calendar.setTime(formatter.parse("2022-03-08"));
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        System.out.println(calendar.getTime().getTime());

        String customsDetail = "人审";
        List<CustomsDetailStatisticsDto> statisticsDtoList = new ArrayList() {{
            CustomsDetailStatisticsDto statisticsDto = new CustomsDetailStatisticsDto();
            statisticsDto.setDeclareNo("12");
            statisticsDto.setInvtNo("223");
            statisticsDto.setLogisticsNo("3434");
            statisticsDto.setBuyerName("34354545");
            statisticsDto.setBuyerIdNumber("35454545");
            statisticsDto.setCreateTime("45454");
            add(statisticsDto);
        }};
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        EasyExcel.write(outputStream, CustomsDetailStatisticsDto.class).sheet(customsDetail).doWrite(statisticsDtoList);
        byte[] bytes = outputStream.toByteArray();
        String fileName = "ccs/" + customsDetail + "-" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + ".xlsx";
        FileOpsUtil.uploadFile2OssDownload(fileName, bytes);

        String path = "https://daita-oss.oss-cn-hangzhou.aliyuncs.com/" + fileName;
        System.out.println(path);
    }
}
