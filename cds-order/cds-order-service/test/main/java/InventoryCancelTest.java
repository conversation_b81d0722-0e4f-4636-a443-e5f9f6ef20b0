
import com.danding.cds.AppOrderServer;
import com.danding.cds.common.config.XxlJobConfig;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCancelService;
import com.danding.cds.customs.inventory.impl.service.CustomsInventoryCancelServiceImpl;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoSearch;
import com.danding.cds.customs.refund.impl.service.RefundOrderInfoServiceImpl;
import com.danding.cds.order.api.test.BaseTest;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.flyway.FlywayAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.EnableTransactionManagement;
import tk.mybatis.spring.annotation.MapperScan;

@RunWith(SpringRunner.class)
@SpringBootTest(classes = AppOrderServer.class)
public class InventoryCancelTest  {
	@BeforeClass
	public static void init()
	{
		if(!StringUtils.isEmpty(System.getProperty("env")) && "DEV".equals(System.getProperty("env").toUpperCase())){
			if (StringUtils.isEmpty(System.getProperty("local"))){
				System.setProperty("local","true");
			}
		}
		System.setProperty("es.set.netty.runtime.available.processors", "false");  // 解决SpringBoot netty与ES netty 相关jar冲突
	}
	@Test
	public void testDemo()
	{

	}
}
