package main.java.com.danding.cds.order.api.test.utils;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.declare.sdk.enums.Inv101TrspModecd;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import com.danding.cds.invenorder.impl.entity.InventoryOrderItemDO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.Test;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/4/24 17:15
 */
@Slf4j
public class InventoryOrderInfoTest {
    @Test
    @Transactional(rollbackFor = Exception.class)
    public void createCarryOverOrder() {
        String context = "{\"carryOverNo\":\"FQTF230424000008\",\"autoCreateEndorsement\":1,\"targetEnterprise\":\"B企业\",\"carryOverDetailList\":[{\"productId\":\"A\",\"barCode\":\"A\",\"name\":\"A\",\"changeQty\":10,\"sku\":\"A\"}],\"originEnterprise\":\"A企业\"}";
        CarryOverCreateDTO carryOverCreateDTO = JSON.parseObject(context, CarryOverCreateDTO.class);
        String originEnterprise = carryOverCreateDTO.getOriginEnterprise();
        String targetEnterprise = carryOverCreateDTO.getTargetEnterprise();
        CompanyDTO originCompany = new CompanyDTO();
        originCompany.setCode("origin");
        if (Objects.isNull(originCompany)) {
            throw new ArgsInvalidException("根据名称:" + originEnterprise + "未查询到企业信息");
        }
        CompanyDTO targetCompany = new CompanyDTO();
        targetCompany.setCode("target");
        if (Objects.isNull(targetCompany)) {
            throw new ArgsInvalidException("根据名称:" + targetEnterprise + "未查询到企业信息");
        }
        CustomsBookResVo originBook = new CustomsBookResVo();
        originBook.setId(1L);
        originBook.setCustomsDistrictCode("2924");
        if (Objects.isNull(originBook)) {
            throw new ArgsInvalidException("根据来源企业:" + originEnterprise + "未查询到关联账册");
        }
        CustomsBookResVo targetBook = new CustomsBookResVo();
        targetBook.setId(2L);
        targetBook.setCustomsDistrictCode("2924");
        if (Objects.isNull(targetBook)) {
            throw new ArgsInvalidException("根据目标企业:" + targetEnterprise + "未查询到关联账册");
        }
        if (!Objects.equals(originBook.getCustomsDistrictCode(), targetBook.getCustomsDistrictCode())) {
            throw new ArgsInvalidException("来源企业与目标企业的关联账册不属于同一关区");
        }
        String warehouseCode = carryOverCreateDTO.getWarehouseCode();
        List<EntityWarehouseDTO> entityWarehouseDTOList = new ArrayList<>();
        EntityWarehouseDTO entityWarehouseDTO1 = new EntityWarehouseDTO();
        entityWarehouseDTO1.setId(11L);
        entityWarehouseDTOList.add(entityWarehouseDTO1);
        if (CollectionUtils.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("未查询到实体仓信息");
        }
        EntityWarehouseDTO entityWarehouseDTO = entityWarehouseDTOList.get(0);
        log.debug("根据实体仓编码:{} 获取实体仓信息:{}", warehouseCode, JSON.toJSONString(entityWarehouseDTO));
        Integer autoCreateEndorsement = carryOverCreateDTO.getAutoCreateEndorsement();
        String carryOverNo = carryOverCreateDTO.getCarryOverNo();
        String inOutOrderNo = carryOverCreateDTO.getInOutOrderNo();
        if (autoCreateEndorsement == 0 && (Objects.isNull(carryOverNo) || Objects.isNull(inOutOrderNo))) {
            throw new ArgsInvalidException("非自动结转单据需要分区结转单号与出入库单号");
        }
        List<ReceiveCarryOverResDTO> receiveCarryOverResDTOS = new ArrayList<>();
        ReceiveCarryOverResDTO inResDTO = new ReceiveCarryOverResDTO();
        ReceiveCarryOverResDTO outResDTO = new ReceiveCarryOverResDTO();
        //构造区间结转入清关单
        List<CarryOverDetailDTO> carryOverDetailList = carryOverCreateDTO.getCarryOverDetailList();
        //true 表示有结转明细 false表示不用结转
        boolean isCarryOver = !CollectionUtils.isEmpty(carryOverDetailList);
        if (Objects.nonNull(inOutOrderNo)) {
            System.out.println("11111");
        } else {
            System.out.println("22222");
        }
//        this.updateCarryOverOrderNo(inOutOrderNo, carryOverNo,autoCreateEndorsement,isCarryOver);
        if (!isCarryOver) {
            //说明没有结转明细
            return;
        }
        InventoryOrderInfoDTO innerInOrder = this.buildCarryOverInOrder(carryOverNo, originCompany, targetCompany, originBook, targetBook, entityWarehouseDTO, carryOverDetailList);
        log.info("createCarryOverOrder innerInOrder={}", JSON.toJSONString(innerInOrder));
        InventoryOrderInfoDTO innerOutOrder = this.buildCarryOverOutOrder(carryOverNo, originCompany, targetCompany, originBook, targetBook, entityWarehouseDTO, carryOverDetailList);
        log.info("createCarryOverOrder innerOutOrder={}", JSON.toJSONString(innerOutOrder));
        inResDTO.setInventoryOrderSn(innerOutOrder.getInveCustomsSn());
        inResDTO.setBusinessType(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode());
        outResDTO.setInventoryOrderSn(innerOutOrder.getInveCustomsSn());
        outResDTO.setBusinessType(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode());
        if (autoCreateEndorsement == 1) {
            Map<String, String> snMap = new HashMap<>();
            String inEndorsementSn = snMap.get(innerInOrder.getInveCustomsSn());
            String outEndorsementSn = snMap.get(innerOutOrder.getInveCustomsSn());
            inResDTO.setEndorsementSn(inEndorsementSn);
            inResDTO.setEndorsementStatus(10);
            outResDTO.setEndorsementSn(outEndorsementSn);
            outResDTO.setEndorsementStatus(10);
        }
        receiveCarryOverResDTOS.add(inResDTO);
        receiveCarryOverResDTOS.add(outResDTO);
        log.info("createCarryOverOrder receiveCarryOverResDTOS={}", JSON.toJSONString(receiveCarryOverResDTOS));
//        return receiveCarryOverResDTOS;
    }
//
//    @Transactional(rollbackFor = Exception.class)
//    public Map<String, String> autoCreateEndorsementAndPush(InventoryOrderInfoDTO innerInOrder, InventoryOrderInfoDTO innerOutOrder) {
//        String inEndorsementSn = this.generateEndorsement(innerInOrder.getId());
//        String outEndorsementSn = this.generateEndorsement(innerOutOrder.getId());
//        log.info("autoCreateEndorsementAndPush inEndorsementSn={} outEndorsementSn={}", inEndorsementSn, outEndorsementSn);
//        Map<String, String> snMap = new HashMap<>();
//        snMap.put(innerInOrder.getInveCustomsSn(), inEndorsementSn);
//        snMap.put(innerOutOrder.getInveCustomsSn(), outEndorsementSn);
//        endorsementService.push(inEndorsementSn);
//        return snMap;
//    }

    /**
     * 根据出入库单号更新结转单号
     */
//    @Transactional(rollbackFor = Exception.class)
//    public void updateCarryOverOrderNo(String inOutOrderNo, String carryOverNo, Integer autoCreateEndorsement, boolean isCarryOver) {
//        InventoryOrderInfoDTO inventoryOrderInfoDTO = this.findByInOutOrderNo(inOutOrderNo);
//        if (isCarryOver&&Objects.equals(0,autoCreateEndorsement)){
//            Integer orderTag = inventoryOrderInfoDTO.getOrderTag();
//            InventoryOrderTagEnums.addOrderTag(orderTag, InventoryOrderTagEnums.RELATE);
//        }
//        Integer todoTag = inventoryOrderInfoDTO.getOrderTodoTag();
//        todoTag = InventoryOrderTodoTagEnums.remove(todoTag, InventoryOrderTodoTagEnums.WAIT_CARRYOVER_DETAIL);
//        inventoryOrderInfoDTO.setOrderTodoTag(todoTag);
//        inventoryOrderInfoDTO.setCarryOverNo(carryOverNo);
//        this.updateInventoryOrderInfoDTO(inventoryOrderInfoDTO);
//    }

    /**
     * 构造结转入清关单
     *
     * @param carryOverNo
     * @param targetCompany
     * @param originBook
     * @param targetBook
     * @param entityWarehouseDTO
     * @param carryOverDetailList
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public InventoryOrderInfoDTO buildCarryOverInOrder(String carryOverNo, CompanyDTO originCompany, CompanyDTO targetCompany, CustomsBookResVo originBook, CustomsBookResVo targetBook, EntityWarehouseDTO entityWarehouseDTO, List<CarryOverDetailDTO> carryOverDetailList) {
        InventoryOrderInfoDTO innerInOrderInfo = new InventoryOrderInfoDTO();
        String innerInOrderSn = "TEST1757857";
        innerInOrderInfo.setInveCustomsSn(innerInOrderSn);
        innerInOrderInfo.setId(getInventoryOrderId(innerInOrderSn));
        innerInOrderInfo.setBookId(targetBook.getId());
        innerInOrderInfo.setEntryExitCustoms(targetBook.getCustomsAreaCode());
        innerInOrderInfo.setInveBusinessType(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode());
        innerInOrderInfo.setInveCompanyId(targetCompany.getId());
        innerInOrderInfo.setOutAccountBook(originBook.getBookNo());
        innerInOrderInfo.setShipmentCountry(null);
        innerInOrderInfo.setTransportMode(Inv101TrspModecd.WAY_OTHER.getValue());
        innerInOrderInfo.setChannel(InventoryOrderChannel.WMS.getValue());
        innerInOrderInfo.setStatus(InventoryOrderEnum.STATUS_AUDITED.getCode());
        innerInOrderInfo.setWmsWarehouseCode(entityWarehouseDTO.getWmsWarehouseCode());
        innerInOrderInfo.setEntityWarehouseName(entityWarehouseDTO.getErpWarehouseName());
        innerInOrderInfo.setEntityWarehouseCode(entityWarehouseDTO.getErpWarehouseCode());
        innerInOrderInfo.setTransferee(targetCompany.getName());
        innerInOrderInfo.setTransferor(originCompany.getName());
        if (StringUtils.isEmpty(carryOverNo)) {
            innerInOrderInfo.setOrderTag(InventoryOrderTagEnums.AUTO_PART_CARRYOVER.getCode());
        } else {
            innerInOrderInfo.setOrderTag(InventoryOrderTagEnums.TO_B_PART_CARRYOVER.getCode());
            innerInOrderInfo.setUpstreamNo(carryOverNo);
        }
        innerInOrderInfo.setCreateTime(new Date());
        innerInOrderInfo.setUpdateTime(new Date());
        log.info("createCarryOverOrder innerInOrderInfo={}", JSON.toJSONString(innerInOrderInfo));
//        this.createInventoryOrderInfoDTO(innerInOrderInfo);
        List<InventoryOrderItemDO> inventoryOrderItemDOS = ConvertUtil.listConvert(carryOverDetailList, InventoryOrderItemDO.class);
        this.buildCarryOverInItem(inventoryOrderItemDOS, targetBook.getId(), innerInOrderSn);
        return innerInOrderInfo;
    }

    public static Long getInventoryOrderId(String inveCustomsSn) {
        return NumberUtils.createLong(StringUtils.replacePattern(inveCustomsSn, "[a-zA-Z]", ""));
    }

    @Transactional(rollbackFor = Exception.class)
    public InventoryOrderInfoDTO buildCarryOverOutOrder(String carryOverNo, CompanyDTO originCompany, CompanyDTO targetCompany, CustomsBookResVo originBook, CustomsBookResVo targetBook, EntityWarehouseDTO entityWarehouseDTO, List<CarryOverDetailDTO> carryOverDetailList) {
        InventoryOrderInfoDTO innerOutOrderInfo = new InventoryOrderInfoDTO();
        String innerOutOrderSn = "out123445666";
        innerOutOrderInfo.setInveCustomsSn(innerOutOrderSn);
        innerOutOrderInfo.setId(getInventoryOrderId(innerOutOrderSn));
        innerOutOrderInfo.setBookId(targetBook.getId());
        innerOutOrderInfo.setEntryExitCustoms(targetBook.getCustomsAreaCode());
        innerOutOrderInfo.setInveBusinessType(InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode());
        innerOutOrderInfo.setInveCompanyId(originCompany.getId());
        innerOutOrderInfo.setOutAccountBook(originBook.getBookNo());
        innerOutOrderInfo.setShipmentCountry(null);
        innerOutOrderInfo.setTransportMode(Inv101TrspModecd.WAY_OTHER.getValue());
        innerOutOrderInfo.setChannel(InventoryOrderChannel.WMS.getValue());
        innerOutOrderInfo.setStatus(InventoryOrderEnum.STATUS_AUDITED.getCode());
        innerOutOrderInfo.setWmsWarehouseCode(entityWarehouseDTO.getWmsWarehouseCode());
        innerOutOrderInfo.setEntityWarehouseName(entityWarehouseDTO.getErpWarehouseName());
        innerOutOrderInfo.setEntityWarehouseCode(entityWarehouseDTO.getErpWarehouseCode());
        innerOutOrderInfo.setTransferee(targetCompany.getName());
        innerOutOrderInfo.setTransferor(originCompany.getName());
        if (StringUtils.isEmpty(carryOverNo)) {
            innerOutOrderInfo.setOrderTag(InventoryOrderTagEnums.AUTO_PART_CARRYOVER.getCode());
        } else {
            innerOutOrderInfo.setOrderTag(InventoryOrderTagEnums.TO_B_PART_CARRYOVER.getCode());
            innerOutOrderInfo.setUpstreamNo(carryOverNo);
        }
        innerOutOrderInfo.setCreateTime(new Date());
        innerOutOrderInfo.setUpdateTime(new Date());
        log.info("createCarryOverOrder innerOutOrderInfo={}", JSON.toJSONString(innerOutOrderInfo));
//        this.createInventoryOrderInfoDTO(innerOutOrderInfo);
//        this.buildCarryOverOutItem(carryOverDetailList, targetBook.getId(), innerOutOrderSn);
        return innerOutOrderInfo;
    }


    /**
     * 1.根据统一查询备案 换取最终申报料号 没有就报错
     * 2.根据最终申报料号查询账册库存（多序号默认取最大序号） 没有就报错
     * 3.构建表体
     *
     * @param carryOverDetailDTOList
     * @param bookId
     * @param innerOutOrderSn
     */
//    public void buildCarryOverOutItem(List<CarryOverDetailDTO> carryOverDetailDTOList, Long bookId, String innerOutOrderSn) {
//        //分区结转出清关单表体
//        List<InventoryOrderItemDO> carryOverOutItemList = new ArrayList<>();
//        carryOverDetailDTOList.forEach(carryOverDetail -> {
//            InventoryOrderItemDO carryOverOutItemDO = new InventoryOrderItemDO();
//            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(bookId, carryOverDetail.getProductId());
//            //取统一料号映射后的料号
//            if (Objects.isNull(goodsRecordDTO)) {
//                log.info("buildCarryOverOutItem productId={} bookId={} 未查询到备案信息", carryOverDetail.getProductId(), bookId);
//                throw new ArgsInvalidException("根据统一料号:" + carryOverDetail.getProductId() + "未查询到备案信息");
//            } else {
//                String finalProductId = goodsRecordAssociateService.getFinalProductId(carryOverDetail.getProductId(), goodsRecordDTO.getId(), bookId);
//                if (Objects.nonNull(finalProductId)) {
//                    carryOverOutItemDO.setProductId(finalProductId);
//                }
//            }
//            List<CustomsBookItemDTO> customsBookItemDTOS = customsBookItemService.findByBookIdAndProId(bookId, carryOverOutItemDO.getProductId());
//            if (CollectionUtils.isEmpty(customsBookItemDTOS)) {
//                log.info("buildCarryOverOutItem productId={} bookId={} 未查询到账册库存", carryOverDetail.getProductId(), bookId);
//                throw new ArgsInvalidException("根据海关备案料号:" + carryOverDetail.getProductId() + "未查询到账册库存");
//            }
//            CustomsBookItemDTO bookItemDTO = customsBookItemDTOS.stream().max(Comparator.comparing(CustomsBookItemDTO::getGoodsSeqNo)).orElse(null);
//            if (Objects.isNull(bookItemDTO)) {
//                log.info("buildCarryOverOutItem productId={} bookId={} 获取账册库存失败", carryOverDetail.getProductId(), bookId);
//                throw new ArgsInvalidException("根据海关备案料号:" + carryOverDetail.getProductId() + "获取账册库存失败");
//            }
//            carryOverOutItemDO.setDeclareUnitQfy(carryOverDetail.getChangeQty());
//            carryOverOutItemDO.setRefInveOrderId(NumberUtils.createLong(StringUtils.replacePattern(innerOutOrderSn, "[a-zA-Z]", "")));
//            carryOverOutItemDO.setRefInveOrderSn(innerOutOrderSn);
//            //申报数量
//            carryOverOutItemDO.setOriginProductId(carryOverDetail.getProductId());
//            carryOverOutItemDO.setProductId(bookItemDTO.getProductId());
//            this.populateCarryOverOutItem(carryOverOutItemDO, bookItemDTO, goodsRecordDTO);
//            log.info("buildCarryOverOutItem carryOverOutItemDO={}", JSON.toJSONString(carryOverOutItemDO));
//            carryOverOutItemList.add(carryOverOutItemDO);
//        });
////        inventoryOrderItemMapper.insertList(carryOverOutItemList);
//    }

    /**
     * 构建分区结转入表体
     *
     * @param inventoryOrderItemDOS
     * @param bookId
     * @param innerInOrderSn
     */
    public void buildCarryOverInItem(List<InventoryOrderItemDO> inventoryOrderItemDOS, Long bookId, String innerInOrderSn) {
        this.buildTransitInItem(inventoryOrderItemDOS, bookId, innerInOrderSn);
    }

    public void buildTransitInItem(List<InventoryOrderItemDO> inventoryOrderItemDOS, Long bookId, String transitInSn) {
        //构建表体
        //中转调入清关单表体
        List<InventoryOrderItemDO> transitInItemList = new ArrayList<>();
        inventoryOrderItemDOS.forEach(masterItem -> {
            InventoryOrderItemDO itemDO = new InventoryOrderItemDO();
            itemDO.setId(null);
            itemDO.setRefInveOrderId(NumberUtils.createLong(StringUtils.replacePattern(transitInSn, "[a-zA-Z]", "")));
            itemDO.setRefInveOrderSn(transitInSn);
            //申报数量
            itemDO.setDeclareUnitQfy(masterItem.getDeclareUnitQfy());
            itemDO.setOriginProductId(masterItem.getOriginProductId());
            itemDO.setProductId(masterItem.getProductId());
            if (!Objects.equals(UserUtils.getUserId(), 0)) {
                UserUtils.setCreateAndUpdateBy(itemDO);
            }
            CustomsBookItemDTO customsBookItemDTO = null;
            GoodsRecordDTO goodsRecordDTO = new GoodsRecordDTO();
            goodsRecordDTO.setProductId("product12124124");
            goodsRecordDTO.setSkuId("sku");
            goodsRecordDTO.setGoodsRecordName("name");
            goodsRecordDTO.setFirstUnitAmount(BigDecimal.ONE);
            goodsRecordDTO.setNetWeight(BigDecimal.ONE);
            goodsRecordDTO.setGrossWeight(BigDecimal.ONE);
            goodsRecordDTO.setDeclarePrice(BigDecimal.ONE);
            //取统一料号映射后的料号
            if (Objects.nonNull(goodsRecordDTO)) {
                String finalProductId = "finalProduct123141";
                if (Objects.nonNull(finalProductId)) {
                    itemDO.setProductId(finalProductId);
                }
            }
            customsBookItemDTO = new CustomsBookItemDTO();
            customsBookItemDTO.setProductId("finalProduct");
            customsBookItemDTO.setFirstUnit("035");
            customsBookItemDTO.setFirstUnitAmount(BigDecimal.ONE);
            customsBookItemDTO.setDeclarePrice(BigDecimal.ONE);
            this.populateTransitInItem(masterItem, itemDO, customsBookItemDTO, goodsRecordDTO);
            log.info("buildTransitInItem itemDO={}", JSON.toJSONString(itemDO));
            transitInItemList.add(itemDO);
        });
        log.debug("buildTransitInItem list={}", JSON.toJSONString(transitInItemList));
//        inventoryOrderItemMapper.insertList(transitInItemList);
    }

    public void populateTransitInItem(InventoryOrderItemDO masterItem, InventoryOrderItemDO itemDO, CustomsBookItemDTO customsBookItemDTO, GoodsRecordDTO goodsRecordDTO) {
        InventoryOrderItemExtra itemExtra = new InventoryOrderItemExtra();
        if (Objects.nonNull(goodsRecordDTO)) {
            itemDO.setSkuId(goodsRecordDTO.getSkuId());
            itemDO.setGoodsName(goodsRecordDTO.getGoodsRecordName());
            itemDO.setHsCode(goodsRecordDTO.getHsCode());
            itemExtra.setGoodsModel(goodsRecordDTO.getModel());
            itemExtra.setUnit(goodsRecordDTO.getDeclareUnit());
            itemExtra.setFirstUnit(goodsRecordDTO.getFirstUnit());
            itemExtra.setFirstUnitQfy(goodsRecordDTO.getFirstUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
            if (Objects.nonNull(goodsRecordDTO.getSecondUnit())) {
                itemExtra.setSecondUnit(goodsRecordDTO.getSecondUnit());
                itemExtra.setSecondUnitQfy(goodsRecordDTO.getSecondUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
            }
            itemExtra.setOriginCountry(goodsRecordDTO.getOriginCountry());
            itemExtra.setNetweight(goodsRecordDTO.getNetWeight().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemExtra.setGrossWeight(goodsRecordDTO.getGrossWeight().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemDO.setDeclarePrice(goodsRecordDTO.getDeclarePrice().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemExtra.setGoodsBar(itemDO.getProductId());
            itemExtra.setCurrency(goodsRecordDTO.getDeclareCurrency());
        } else {
            String extraJson = masterItem.getExtraJson();
            InventoryOrderItemExtra masterItemExtra = JSON.parseObject(extraJson, InventoryOrderItemExtra.class);
            itemDO.setSkuId(masterItem.getSkuId());
            itemDO.setGoodsName(masterItem.getGoodsName());
            itemDO.setHsCode(masterItem.getHsCode());
            itemExtra.setGoodsModel(masterItemExtra.getGoodsModel());
            itemExtra.setUnit(masterItemExtra.getUnit());
            itemExtra.setFirstUnit(masterItemExtra.getFirstUnit());
            itemExtra.setFirstUnitQfy(masterItemExtra.getFirstUnitQfy());
            itemExtra.setSecondUnit(masterItemExtra.getSecondUnit());
            itemExtra.setSecondUnitQfy(masterItemExtra.getSecondUnitQfy());
            itemExtra.setOriginCountry(masterItemExtra.getOriginCountry());
            itemExtra.setNetweight(masterItemExtra.getNetweight().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemExtra.setGrossWeight(masterItemExtra.getGrossWeight().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemDO.setDeclarePrice(masterItem.getDeclarePrice().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemExtra.setGoodsBar(masterItemExtra.getGoodsBar());
            itemExtra.setCurrency(masterItemExtra.getCurrency());
        }
        if (Objects.nonNull(customsBookItemDTO)) {
            itemDO.setIsNew("old");
            itemDO.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
            itemDO.setGoodsName(customsBookItemDTO.getGoodsName());
            itemDO.setHsCode(customsBookItemDTO.getHsCode());
            itemExtra.setGoodsModel(customsBookItemDTO.getGoodsModel());
            itemExtra.setUnit(customsBookItemDTO.getGoodsUnit());
            itemExtra.setFirstUnit(customsBookItemDTO.getFirstUnit());
            itemExtra.setFirstUnitQfy(customsBookItemDTO.getFirstUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
            if (StringUtils.isNotEmpty(customsBookItemDTO.getSecondUnit())) {
                itemExtra.setSecondUnit(customsBookItemDTO.getSecondUnit());
                itemExtra.setSecondUnitQfy(customsBookItemDTO.getSecondUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
            }
            itemExtra.setOriginCountry(customsBookItemDTO.getOriginCountry());
            itemDO.setDeclarePrice(customsBookItemDTO.getDeclarePrice().setScale(4, BigDecimal.ROUND_HALF_UP));
            itemExtra.setGoodsBar(customsBookItemDTO.getProductId());
            itemExtra.setCurrency(customsBookItemDTO.getCurrCode());
        } else {
            itemDO.setIsNew("new");
        }
        itemExtra.setDestinationCountry("142");
        itemDO.setExtraJson(JSON.toJSONString(itemExtra));
        UserUtils.setCreateAndUpdateBy(itemDO);
        itemDO.setCreateTime(new Date());
        itemDO.setUpdateTime(new Date());
    }

    /**
     * 构造分区结转出表体
     *
     * @param itemDO
     * @param customsBookItemDTO
     * @param goodsRecordDTO
     */
    public void populateCarryOverOutItem(InventoryOrderItemDO itemDO, CustomsBookItemDTO customsBookItemDTO, GoodsRecordDTO goodsRecordDTO) {
        InventoryOrderItemExtra itemExtra = new InventoryOrderItemExtra();
        itemDO.setSkuId(goodsRecordDTO.getSkuId());
        itemExtra.setNetweight(goodsRecordDTO.getNetWeight().setScale(4, BigDecimal.ROUND_HALF_UP));
        itemExtra.setGrossWeight(goodsRecordDTO.getGrossWeight().setScale(4, BigDecimal.ROUND_HALF_UP));
        itemDO.setIsNew("old");
        itemDO.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
        itemDO.setGoodsName(customsBookItemDTO.getGoodsName());
        itemDO.setHsCode(customsBookItemDTO.getHsCode());
        itemExtra.setGoodsModel(customsBookItemDTO.getGoodsModel());
        itemExtra.setUnit(customsBookItemDTO.getGoodsUnit());
        itemExtra.setFirstUnit(customsBookItemDTO.getFirstUnit());
        itemExtra.setFirstUnitQfy(customsBookItemDTO.getFirstUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
        if (StringUtils.isNotEmpty(customsBookItemDTO.getSecondUnit())) {
            itemExtra.setSecondUnit(customsBookItemDTO.getSecondUnit());
            itemExtra.setSecondUnitQfy(customsBookItemDTO.getSecondUnitAmount().setScale(4, BigDecimal.ROUND_HALF_UP));
        }
        itemExtra.setOriginCountry(customsBookItemDTO.getOriginCountry());
        itemDO.setDeclarePrice(customsBookItemDTO.getDeclarePrice().setScale(4, BigDecimal.ROUND_HALF_UP));
        itemExtra.setGoodsBar(customsBookItemDTO.getProductId());
        itemExtra.setCurrency(customsBookItemDTO.getCurrCode());
        itemExtra.setDestinationCountry("142");
        itemDO.setExtraJson(JSON.toJSONString(itemExtra));
        UserUtils.setCreateAndUpdateBy(itemDO);
        itemDO.setCreateTime(new Date());
        itemDO.setUpdateTime(new Date());
    }
}
