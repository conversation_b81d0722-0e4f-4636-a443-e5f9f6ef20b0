package com.danding.cds.order.api.test.payment;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.declare.sdk.payment.alipay.AlipayChannel;
import com.danding.cds.declare.sdk.payment.alipay.model.AliCustomsPayQueryModel;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import org.junit.Test;

import java.util.Map;
import java.util.TreeMap;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/23 10:06
 * @Description:
 */
public class AliPaymentQuery {

    @Test
    public void name() {
        AliCustomsPayQueryModel model = new AliCustomsPayQueryModel();
        model.setService("alipay.overseas.acquire.customs.query");
        model.setPartner("2088231718158716");
        model.setInputCharset("UTF-8");
        model.setOutRequestNos("DOCP202010211405330001033842");
        JSONObject paramMap = JSON.parseObject(JSON.toJSONString(model));
        Map<String, Object> sortedParams = new TreeMap<>();
        sortedParams.putAll(paramMap);
        String key = "q7hhnl5r1ashr47g627rczl9mu831j6b";
        System.out.println(JSON.toJSONString(sortedParams));
        String md5Sign = Hashing
                .md5().newHasher().putString(AlipayChannel.createLinkString(sortedParams) + key, Charsets.UTF_8).hash().toString();
        paramMap.put("sign_type","MD5");
        paramMap.put("sign",md5Sign);
        HttpRequest httpRequest = HttpRequest.get("https://mapi.alipay.com/gateway.do",paramMap, true);
        if (httpRequest.ok()){
            System.out.println(httpRequest.body());
        }
    }
}
