package com.danding.cds.order.api.test.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.api.dto.OrderSubmitItem;
import com.danding.cds.order.api.test.utils.RandomInfoBuilder;
import com.github.kevinsawicki.http.HttpRequest;
import org.junit.Test;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OrderReceiveTest {
    @Test
    public void run() {
        CustomsReceive receive = new CustomsReceive();
        receive.setCustomsStatus("800");
        receive.setCustomsDetail("[Code:2600;Desc:放行]");
        receive.setCustomsTime(new java.util.Date().getTime());
        receive.setDeclareOrderNo("DN2006232011071172");
        receive.setEbpCode("3301964J31");
        System.out.println(JSON.toJSONString(receive));
        HttpRequest httpRequest = HttpRequest.post("http://127.0.0.1:8083/xhr/order/mockReceive")
                .header("Content-Type", "application/json;charset=utf-8").send(JSON.toJSONString(receive));
        if (httpRequest.ok()){
            System.out.println(httpRequest.body());
        }else {
            System.out.println("网络请求异常");
        }
    }
}
