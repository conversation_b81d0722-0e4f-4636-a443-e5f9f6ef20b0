package com.danding.cds.order.api.test.payment;

import com.alibaba.fastjson.JSON;
import com.danding.cds.declare.sdk.payment.wechat.WxPayClient;
import com.danding.cds.declare.sdk.payment.wechat.model.WxCustomsQueryModel;
import org.junit.Test;

import java.io.ByteArrayOutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * @Auther: Dante-Gxj
 * @Date: 2019/12/13 14:05
 * @Description:
 */
public class WxCustomsQueryTest {

    @Test
    public void shunjing() throws Exception {
        String appId = "wx9c8c5942069d93b8";
        String mchId= "1562056481";
        String partnerKey = "61777F2A924E9D9B2E211F583B392A94";
        String result = this.post(appId,mchId,partnerKey,"DOCP202010211313450001033829");
        System.out.println(result);
    }

    @Test
    public void danding() throws Exception {
        String result = this.post("wx86fe5657944bcaa2","1488913512","8ac1cbf290b0fe98ff1142acf94e7351","DOCP202001070913182001013160");
        System.out.println(result);
    }

    @Test
    public void beilami() throws Exception {
        String result = this.post("wx011d3783b52aa189","1539231791","yj5RnkfCPwTZ92ipH5t5vSBVui0xYspp","DOCP202001152154030001013883");
        System.out.println(result);
    }

    @Test
    public void alp() throws Exception {
        String result = this.post("wx742a0012cc701d79","1592728491","Aolipai8888888888888888888888888","DOCP202011041524330001034280");
        System.out.println(result);
    }

    @Test
    public void gaotan() throws Exception {
        String result = this.post("wxfdbf105303bd1ffb","1540436701","1gaogetantushangmaoyouxiangongsi","DOCP201912131146120001011612");
        System.out.println(result);
    }

    private String post(String appId, String mchId, String partnerKey,String orderNo) throws Exception {
        WxPayClient client = new WxPayClient();
        String gateway = "https://api.mch.weixin.qq.com/cgi-bin/mch/customs/customdeclarequery";
        WxCustomsQueryModel model = new WxCustomsQueryModel();
        model.setSubOrderNo(orderNo);
        model.setCustoms("CHONGQING");
        Map<String,String> reqData = JSON.parseObject(JSON.toJSONString(model), HashMap.class);
        Map<String,String> resultMap = client.customsQuery(gateway,appId,mchId,partnerKey,reqData);
        return JSON.toJSONString(resultMap);
    }


    private String loadOrig(byte[] rom){
        byte[] mark = {(byte) 0xef, (byte) 0xbf, (byte) 0xbd};
        ByteArrayOutputStream stream = new ByteArrayOutputStream();
        stream.write(rom, 1, rom.length - 1);
        int match = 0;
        stream.reset();
        for (int i = 0; i < rom.length; i++) {
            if (rom[i] == mark[match]) {
                match++;
            } else if (match == 0) {
                stream.write(rom[i]);
            } else {
                stream.write(mark, 0, match);
                stream.write(rom[i]);
            }
            if (match == mark.length)
                match = 0;
        }
        try {
            return stream.toString("gbk");
        } catch (Exception ex) {
            return "";
        }
    }
}
