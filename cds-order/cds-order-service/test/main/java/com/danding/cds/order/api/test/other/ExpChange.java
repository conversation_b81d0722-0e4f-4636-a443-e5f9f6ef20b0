package main.java.com.danding.cds.order.api.test.other;

import com.github.kevinsawicki.http.HttpRequest;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.util.List;

/**
 * @Auther: <PERSON>-<PERSON>XJ
 * @Date: 2020/10/23 13:56
 * @Description:
 */
public class ExpChange {

    @Test
    public void name() {
        String origStr = "" +
                "XP1020102119501181716951001718\n" +
                "XP1320102119501181728901006347\n" +
                "XP1120102119201556212946000948\n" +
                "XP1620102119500166726830005802\n" +
                "XP2520102119500166833796007486\n" +
                "XP2020102119501181747836000207\n" +
                "XP1520102119501181814915005946\n" +
                "XP1020102119501181755252007420\n" +
                "XP2520102119500166832094007259\n" +
                "XP1520102119500166844362007608\n" +
                "XP1420102119501181845952004593\n" +
                "XP1120102119500166854359006281\n" +
                "XP2020102119201556456722004455\n" +
                "XP2220102119500166770287005729\n" +
                "XP1820102119501181861214008034\n" +
                "XP1620102119200510367972004710";

        List<String> snList = Lists.newArrayList(origStr.split("\n"));

        for (String sn : snList) {
            System.out.println(sn);
            HttpRequest httpRequest = HttpRequest.get("http://npc.yang800.com/backend/xhr/order/changExp?declareOrderSns="+ sn +"&expCode=ZTO");
            if (httpRequest.ok()){
                System.out.println(httpRequest.body());
            }
            HttpRequest httpRequest1 = HttpRequest.get("http://storage.yang800.com/xhr/order/depot/cancel?sn=" + sn + "&shipperSn=DS15918629917682");
            if (httpRequest1.ok()){
                System.out.println(httpRequest1.body());
            }
        }
    }
}
