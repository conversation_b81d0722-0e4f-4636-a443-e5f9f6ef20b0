package com.danding.cds.order.api.test.order;

import com.alibaba.fastjson.JSON;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.api.dto.OrderSubmitItem;
import com.danding.cds.order.api.test.utils.RandomInfoBuilder;
import com.danding.cds.payinfo.api.dto.PayInfoGoodsInfo;
import com.github.kevinsawicki.http.HttpRequest;
import org.assertj.core.util.Lists;
import org.junit.Test;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class OrderCreateTest {

    @Test
    public void batch() {
        int i = 5;
        do {
            this.run();
            i--;
        }while (i > 0);
    }

    public static EnvConfig dev = new EnvConfig(
            "http://127.0.0.1:8083",
            "ZFD",
            "wechatpay",
            "HTKD",
            Lists.newArrayList(new String[]{"JHWX020525912756"}));


    public static EnvConfig test = new EnvConfig(
            "http://ccs-test.backend.yang800.com.cn",
            "1381234567",
            "wechatpay",
            "ZTO",
            Lists.newArrayList(new String[]{"JHY10131611"}));
    @Test
    public void run() {
        EnvConfig envConfig = test;
        OrderSubmit submit = new OrderSubmit();
        submit.setOutOrderNo(RandomInfoBuilder.getSn("ON"));
        submit.setDeclareOrderNo(RandomInfoBuilder.getSn("DN"));
        submit.setRouteCode(envConfig.routeCode);
        submit.setExpressCode(envConfig.expressCode);
        submit.setLogisticsNo(RandomInfoBuilder.getSn("WL"));
        submit.setFeeAmount(RandomInfoBuilder.getPrice(0,10));
        submit.setInsureAmount(BigDecimal.ZERO);
        List<OrderSubmitItem> itemList = new ArrayList<>();
        for (String skuCode : envConfig.skuList) {
            {
                OrderSubmitItem item = new OrderSubmitItem();
                item.setRecordNo(skuCode);
//            item.setRecordGnum("账册内商品序号");
                item.setItemNo(RandomInfoBuilder.getSn("IN"));
                item.setItemName("SESDERMA/白藜芦醇精华30ML/盒");
                item.setCount(RandomInfoBuilder.getNum(1,3));
                item.setUnitPrice(RandomInfoBuilder.getPrice(1,30));
                itemList.add(item);
            }
        }
        submit.setItemList(itemList);
        submit.setBuyerTelNumber(RandomInfoBuilder.getTel());
        submit.setBuyerIdNumber(RandomInfoBuilder.getIdNo(RandomInfoBuilder.getNum(0,1) == 0));
        submit.setBuyerName(RandomInfoBuilder.getChineseName());
        Map<String,String> area = RandomInfoBuilder.getArea();
        submit.setConsigneeProvince(area.get("province"));
        submit.setConsigneeCity(area.get("city"));
        submit.setConsigneeDistrict(area.get("district"));
        submit.setConsigneeAddress(RandomInfoBuilder.getRoad());
        submit.setTenantOuterId("OUT001");
        submit.setTenantName("SSS");
        submit.setCustomsCode("GUANGZHOU_NS");
        submit.setOutTradeNo("OTN" + System.currentTimeMillis());
        submit.setMerchantCode("MC2106151434000002");
        submit.setGrossWeight(BigDecimal.ONE);
        { // 订单相关参数
            submit.setPayChannel(envConfig.payChannel);
            submit.setDeclarePayNo(RandomInfoBuilder.getSn("DP"));
            submit.setTradePayNo(RandomInfoBuilder.getSn("TN"));
            submit.setTaxAmount(BigDecimal.ONE);
            submit.setDiscount(BigDecimal.ZERO);
            submit.setTradeTime(System.currentTimeMillis());
            submit.setConsigneeTel("***********");
            submit.setConsigneeName("郭献杰");
            submit.setSenderName("海关发件人");
        }
        {
            // 179相关参数
            submit.setPayInfoDataCheckFlag(true);
            submit.setPayTransactionId(RandomInfoBuilder.getSn("PT"));
            submit.setVerDept("网联");
            submit.setPayWay("1");
            submit.setPayTransactionAmount(BigDecimal.TEN);
            submit.setRecpAccount("收款账号");
            submit.setRecpCode("社会信用代码");
            submit.setRecpName("工商备案名称");
            submit.setPayRequestMessage("请求原始数据");
            submit.setPayResponseMessage("请求响应数据");
            List<PayInfoGoodsInfo> origGoodsInfoList = new ArrayList<>();
            {
                PayInfoGoodsInfo payInfoGoodsInfo = new PayInfoGoodsInfo();
                origGoodsInfoList.add(payInfoGoodsInfo);
            }
            submit.setOrigGoodsInfoList(origGoodsInfoList);
        }
        System.out.println(JSON.toJSONString(submit));
        System.out.println(envConfig.host + "/xhr/order/submit");
        HttpRequest httpRequest = HttpRequest.post(envConfig.host + "/xhr/order/submit")
                .header("Content-Type", "application/json;charset=utf-8").send(JSON.toJSONString(submit));
        if (httpRequest.ok()){
            System.out.println(httpRequest.body());
        }else {
            System.out.println("网络请求异常");
        }

    }

    public static class EnvConfig implements Serializable {
        private String host;

        private String routeCode;

        private String payChannel;

        private String expressCode;

        private List<String> skuList;

        public EnvConfig(String host, String routeCode, String payChannel, String expressCode, List<String> skuList) {
            this.host = host;
            this.routeCode = routeCode;
            this.payChannel = payChannel;
            this.expressCode = expressCode;
            this.skuList = skuList;
        }
    }
}
