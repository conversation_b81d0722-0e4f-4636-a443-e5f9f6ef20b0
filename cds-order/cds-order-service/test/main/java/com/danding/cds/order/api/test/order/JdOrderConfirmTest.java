package main.java.com.danding.cds.order.api.test.order;

import com.alibaba.fastjson.JSONObject;
import com.danding.common.utils.DateUtils;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.base.Charsets;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.joda.time.DateTime;
import org.junit.Test;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
public class JdOrderConfirmTest {
    @Test
    public void run() {
        Map params = new HashMap();
        params.put("method", "jingdong.pop.customs.center.OrderYnJsfService.getOrderYnJsfResult");
        params.put("access_token", "e1149500c966464497b8125c0264eb66ztex");
        params.put("app_key", "********************************");
        params.put("timestamp", new DateTime().toString(DateUtils.DATE_TIME_FORMAT));
        params.put("format", "json");
        params.put("v", "2.0");
        params.put("customsId", "hangzhou");
        params.put("serviceId", "020004");
        params.put("orderId", "146554485674");
        params.put("sign", jdSign(params));
        HttpRequest httpRequest = HttpRequest.post("https://api.jd.com/routerjson").acceptGzipEncoding().uncompress(true).form(params);
        JSONObject response = (JSONObject) JSONObject.parseObject(httpRequest.body()).get("jingdong_pop_customs_center_OrderYnJsfService_getOrderYnJsfResult_responce");
        JSONObject first = (JSONObject) response.get("orderYnJsfResult");
        String yn = first.getString("yn");
        String orderId = first.getString("orderId");
        System.out.println("订单" + orderId + ":yn=" + yn + "," + ("1".equals(yn) ? "订单正常" : "订单取消"));
    }

    public String jdSign(Map<String, String> params) {
        try {
            Set<String> keySet = params.keySet();
            String[] keyArray = keySet.toArray(new String[keySet.size()]);
            Arrays.sort(keyArray);
            StringBuilder sb = new StringBuilder();
            sb.append("11e19999b61e42ee80beff29d23171ac");
            for (String k : keyArray) {
                if (k.equals("sign")) {
                    continue;
                }
                if (params.get(k).toString().trim().length() > 0) {
                    sb.append(k).append(params.get(k).toString().trim());
                }
            }
            String sign = Hashing.sha1().newHasher().putString(sb.toString(), Charsets.UTF_8).hash().toString().toUpperCase();
            return sign;
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return "";
        }
    }
}
