# 库存流水批量插入异步化改造文档

## 改造背景

原有的`EndorsementServiceImpl.finish`方法在第1930行直接同步调用`itemStockListBatchInsert`方法进行库存流水批量插入，这种同步处理方式存在以下问题：

1. **阻塞主流程**：库存流水插入操作耗时较长，会阻塞核注单完成的主流程
2. **并发风险**：多个线程同时处理同一核注单时可能出现并发问题
3. **性能瓶颈**：同步处理影响整体系统性能

## 改造方案

采用RocketMQ消息队列 + Redis分布式锁的方式进行异步解耦改造：

### 1. 架构设计

```
原流程：
finish() -> itemStockListBatchInsert() (同步)

新流程：
finish() -> 发送MQ消息 (异步)
         -> MQ消费者 -> Redis锁 -> itemStockListBatchInsert()
```

### 2. 核心组件

#### 2.1 消息DTO

- **文件**: `cds-order/cds-order-api/src/main/java/com/danding/cds/endorsement/api/dto/ItemStockListBatchInsertDTO.java`
- **作用**: 封装需要传递的核注单ID、真实核注单号和跟踪ID（简化设计，减少消息体积）

#### 2.2 消息生产者

- **文件**:
  `cds-order/cds-order-service/src/main/java/com/danding/cds/v2/mq/producer/ItemStockListBatchInsertProducer.java`
- **作用**: 发送库存流水批量插入消息到RocketMQ
- **Topic**: `ccs-item-stock-list-batch-insert-topic`

#### 2.3 消息消费者

- **文件**:
  `cds-order/cds-order-service/src/main/java/com/danding/cds/v2/mq/consumer/ItemStockListBatchInsertConsumer.java`
- **作用**: 消费消息，根据核注单ID查询数据并执行库存流水批量插入逻辑
- **Consumer Group**: `ccs-item-stock-list-batch-insert-consumer`

### 3. 并发控制

使用Redis分布式锁确保同一核注单不会并发处理：

- **锁Key格式**: `ccs:item-stock-list:batch-insert:{endorsementId}`
- **锁等待时间**: 3秒
- **锁持有时间**: 30秒

### 4. 消息设计优化

采用轻量级消息设计：

- 只传递核注单ID、真实核注单号和跟踪ID
- 消费者根据核注单ID查询完整的核注单信息和明细
- 减少消息体积，提高传输效率
- 避免数据不一致问题（消费时查询最新数据）

## 改造内容

### 1. 新增文件

1. `ItemStockListBatchInsertDTO.java` - 消息DTO
2. `ItemStockListBatchInsertProducer.java` - 消息生产者
3. `ItemStockListBatchInsertConsumer.java` - 消息消费者
4. `ItemStockListBatchInsertMQTest.java` - 单元测试

### 2. 修改文件

1. **EndorsementService.java**
    - 添加itemStockListBatchInsert方法定义

2. **EndorsementServiceImpl.java**
    - 第1930行：将同步调用改为异步消息发送
    - 第4750行：同样改为异步消息发送
    - 第4779行：同样改为异步消息发送
    - 添加消息生产者依赖注入

3. **配置文件**
    - 所有环境配置文件中添加新的topic配置

### 3. 配置变更

在所有环境配置文件中添加：

```yaml
mq:
  consumer:
    item.stock.list:
      open: true
      tags:
```

## 部署说明

### 1. 部署顺序

1. 先部署消费者代码（确保消费者能处理消息）
2. 再部署生产者代码（开始发送消息）

### 2. 回滚方案

如果需要回滚，可以：

1. 将异步调用改回同步调用
2. 停止消费者
3. 清理未处理的消息

### 3. 监控要点

1. **消息积压监控**：监控topic中的消息积压情况
2. **消费失败监控**：监控消费失败的消息
3. **Redis锁监控**：监控锁获取失败的情况
4. **处理时间监控**：监控消息处理时间

## 测试验证

### 1. 单元测试

运行 `ItemStockListBatchInsertMQTest` 验证：

- 消息发送功能
- 消息消费功能
- Redis锁机制
- 异常处理

### 2. 集成测试

1. 创建核注单并完成
2. 验证消息是否正确发送
3. 验证消息是否正确消费
4. 验证库存流水是否正确插入
5. 验证并发场景下的锁机制

### 3. 性能测试

对比改造前后的性能指标：

- 核注单完成接口响应时间
- 系统整体吞吐量
- 资源使用情况

## 注意事项

1. **消息幂等性**：消费者需要处理重复消息的情况
2. **消息顺序性**：同一核注单的消息需要保证顺序处理
3. **异常处理**：消费失败时的重试和死信队列处理
4. **监控告警**：及时发现和处理异常情况

## 风险评估

### 高风险

- 消息丢失导致库存流水缺失
- Redis锁失效导致并发问题

### 中风险

- 消息积压导致处理延迟
- 消费者异常导致处理失败

### 低风险

- 配置错误导致消费者无法启动
- 网络问题导致消息发送失败

## 后续优化

1. **批量处理**：支持多个核注单的批量处理
2. **动态配置**：支持动态调整消费者数量和处理参数
3. **监控面板**：提供可视化的监控面板
4. **自动恢复**：异常情况下的自动恢复机制
