# 核注单回执特殊情况处理说明

## 问题描述

在区间流转入核注单关联申报了一票多车核放单的场景中，线下过卡时前面车辆会触发海关下发审批回执，出现以下情况：

- 处理结果：通过（已核扣）`<MANAGE_RESULT>1</MANAGE_RESULT>`
- 核扣方式代码：未核扣 `<vrfdedMarkcd>0</vrfdedMarkcd>`

这种情况下，CCS系统按照处理结果更新海关回执为通过（已核扣），但核扣方式是未核扣，导致核注单状态未正确更新，造成显示不一致。

## 解决方案

### 1. 修改 EndorsementResult 类

在 `EndorsementResult` 类中添加了 `skipStatusUpdate` 字段：

```java
/**
 * 是否跳过状态更新，只记录日志
 */
private Boolean skipStatusUpdate = false;
```

并添加了新的构造函数支持该字段。

### 2. 修改 INV201MessageConsumerProcessor

在处理核注清单回执时，增加特殊情况判断：

```java
// 特殊情况处理：处理结果是通过（已核扣）但核扣方式代码是未核扣
boolean isSpecialCase = "1".equals(manageResult) && "0".equals(outStatus);
```

当遇到特殊情况时：
- 不更新核注单状态（通过设置 `skipStatusUpdate = true`）
- 记录特殊的日志描述："核注清单审核回执：通过（已核扣），另实际核扣标记：未核扣"
- 使用 `STATUS_EXAMINED` 状态发送消息（避免 STATUS_INIT 的状态判断问题）

### 3. 修改 EndorsementReceiver

在处理 `STATUS_EXAMINED` 状态时，增加对 `skipStatusUpdate` 字段的判断：

```java
case EndorsementResult.STATUS_EXAMINED:{
        // 检查是否需要跳过状态更新，只记录日志
        if(Boolean.TRUE.

equals(resultInfo.getSkipStatusUpdate())){
// 只记录日志，不更新状态
String info = String.join("/", resultInfo.getInformation());
        endorsementTrackLogService.

buildFullLog(endorsementOrderDTO.getId(),
                EndorsementOrderStatus.

getEnum(endorsementOrderDTO.getStatus()),
info,
        resultInfo.

getMessage());
        }else{
        // 正常的EXAMINED状态处理逻辑
        endorsementService.

fillItemById(endorsementOrderDTO.getId(),resultInfo.

getRealOrderNo());
        endorsementService.

examinedPostProcess(endorsementOrderDTO, resultInfo.getInformation(), false);
        }
        break;
        }
```

## 设计优化

相比最初使用 `STATUS_INIT` 的方案，改用 `STATUS_EXAMINED` 有以下优势：

1. **避免状态判断问题**：`STATUS_INIT` 在 `EndorsementReceiver` 第101行有状态判断，如果核注单不是 `STORAGING` 或
   `DECALRING` 状态会直接返回，导致特殊情况无法处理
2. **语义更准确**：特殊情况本质上是审核回执，使用 `STATUS_EXAMINED` 更符合业务语义
3. **扩展性更好**：通过 `skipStatusUpdate` 字段控制，避免硬编码字符串判断

## 修改的文件

1. `cds-declare-sdk/src/main/java/com/danding/cds/declare/sdk/model/checkList/EndorsementResult.java`
2.
`cds-declare-app/src/main/java/com/danding/cds/declare/zjSpecial/processor/consumer/INV201MessageConsumerProcessor.java`
3. `cds-order-service/src/main/java/com/danding/cds/customs/declare/mq/EndorsementReceiver.java`

## 测试用例

创建了相应的测试用例来验证：

1. 特殊情况的处理逻辑（使用 STATUS_EXAMINED + skipStatusUpdate = true）
2. 正常情况的处理逻辑
3. 新字段的正确设置和使用

## 兼容性

- 新增的 `skipStatusUpdate` 字段默认为 `false`，不影响现有代码
- 保留了原有的构造函数，确保向后兼容
- 只在特殊情况下使用新的逻辑，正常流程保持不变

## 效果

1. 当处理结果是通过（已核扣）但核扣方式代码是未核扣时，不更新海关回执状态
2. 依然记录核注单日志，日志描述为："核注清单审核回执：通过（已核扣），另实际核扣标记：未核扣"
3. 避免了状态显示不一致的问题
4. 提供了灵活的扩展机制，便于后续类似场景的处理
5. 通过使用 STATUS_EXAMINED 避免了 STATUS_INIT 的状态判断限制
