package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description: 清关单附件上传
 * @author: houwenjie
 * @date: 2025/05/21
 */
@Data
public class InventoryOrderUploadReq implements Serializable {

    /**
     * 上游单据类型: ReadyOrder(备货) Distribution(配货)
     */
    private String channelBusinessType;

    /**
     * 上游单据编号(备货/配货单号)
     */
    private String channelBusinessSn;

    /**
     * 附件列表
     */
    private List<InventoryOrderAttachReqVo> attachList;

    /**
     * 清关单表体
     */
    private List<InventoryOrderItemReqVo> itemList;
}
