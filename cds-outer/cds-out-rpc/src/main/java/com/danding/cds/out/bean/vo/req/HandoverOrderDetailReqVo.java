package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description 交接单明细
 * @date 2021/12/14
 */
@Data
public class HandoverOrderDetailReqVo implements Serializable {

    /**
     * 包裹号
     */
    private String packageSn;

    /**
     * 包裹重量（kg）
     */
    private String packageWeight;


    /**
     * 运单编号
     */
    private String wayBillSn;

    /**
     * 交接单
     */
    private String handoverSn;

    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 快递编码
     */
    private String expressCode;

    /**
     * 快递公司
     */
    private String expressName;

    /**
     * 出库时间
     */
    private Date outHouseTime;

    /**
     * 仓库名称
     */
    private String storeHouseName;

    /**
     * 仓库编码
     */
    private String storeHouseSn;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 创建人
     */
    private Integer createBy;
    private Integer updateBy;

}
