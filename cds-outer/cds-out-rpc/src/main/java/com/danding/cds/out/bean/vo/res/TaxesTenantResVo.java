package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class TaxesTenantResVo implements Serializable {

    private static final long serialVersionUID = 2029301151746560890L;

    /**
     * 租户账户名称
     */
    private String name;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 企业对象
     */
    private List<CompanyTotal> company;

    @Data
    public static class CompanyTotal implements Serializable {
        /**
         * 担保企业 id
         */
        private Long companyId;
        /**
         * 担保企业名称
         */
        private String companyName;
        /**
         * 消费金额
         */
        private BigDecimal totalConsumedAmount;
    }
}


