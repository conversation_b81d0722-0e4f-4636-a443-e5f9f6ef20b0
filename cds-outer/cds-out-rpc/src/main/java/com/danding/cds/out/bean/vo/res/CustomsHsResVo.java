package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class CustomsHsResVo implements Serializable {
    private long id;
    /**
     * 海关HS编号
     */
    private String hsCode;
    /**
     * 海关HS名称
     */
    private String hsName;
    /**
     * 法定第一计量单位
     */
    private String firstLegalUnit;
    /**
     * 法定第二计量单位
     */
    private String secondLegalUnit;
    /**
     * 出口税率
     */
    private BigDecimal exportTaxRate;
    /**
     * 出口退税税率
     */
    private BigDecimal exportDrawbackTaxRate;
    /**
     * 出口暂定税率
     */
    private BigDecimal exportTentativeTaxRate;
    /**
     * 进口优惠税率
     */
    private BigDecimal importDiscountTaxRate;
    /**
     * 进口暂定税率
     */
    private BigDecimal importTentativeTaxRate;
    /**
     * 进口普通税率
     */
    private BigDecimal importGeneralTaxRate;
    /**
     * 增值税
     */
    private BigDecimal vat;
    /**
     * 关税
     */
    private BigDecimal tariff;
    /**
     * 消费税从价从量标志
     */
    private Integer consumptionFlag;
    /**
     * 消费税率从价税率
     */
    private BigDecimal consumptionTax;
    /**
     * 消费税从量税率
     */
    private BigDecimal consumptionNumTax;
    /**
     * 在consumption_flag是10时候启用,如果满足单位价格,启用从量税率,不满足启用从价税率,目前仅支持>=
     */
    private BigDecimal pricePerUnit;
    /**
     * 价/量的单位名称
     */
    private String uomName;

    private Integer enable;
}
