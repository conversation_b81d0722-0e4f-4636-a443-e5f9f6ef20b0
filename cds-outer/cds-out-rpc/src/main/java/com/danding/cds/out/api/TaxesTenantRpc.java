package com.danding.cds.out.api;

import com.danding.cds.out.bean.vo.res.TaxesTenantResVo;


import java.util.Date;
import java.util.List;

public interface TaxesTenantRpc {

    /**
     * 用户税金消费总额的查询接口
     *
     * @param tenantId  租户id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 税金消费额
     */
    TaxesTenantResVo getTotalTaxConsumption(String tenantId, Date startDate, Date endDate);

    /**
     * 用户税金消费总额的查询接口
     *
     * @param tenantId  租户id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 税金消费额
     */
    List<TaxesTenantResVo> getTotalTaxConsumptionBatch(List<String> tenantId, Date startDate, Date endDate);
}
