package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: CCS撤单对象
 * @date 2023/3/3 15:26
 */
@Data
public class CustomsInventoryCalloffResVo implements Serializable {

    private Long id;

    /**
     * 清单系统编号
     */
    private String sn;

    /**
     * 申报单系统编号
     */
    private String orderSn;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 账册ID
     */
    private Long accountBookId;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    /**
     * 清关状态
     */
    private String customsStatus;

    /**
     * 出区状态
     */
    private Integer exitRegionStatus;

    /**
     * 取消状态
     */
    private String calloffStatus;

    /**
     * 取消类型
     */
    private String calloffType;

    /**
     * 取消原因
     */
    private String calloffReason;

    /**
     * 海关售后回执
     */
    private String cusAfterSalesCallback;

    /**
     * 海关售后回执描述
     */
    private String cusAfterSalesCallbackDesc;

    /**
     * 海关售后回执详情
     */
    private String cusAfterSalesCallbackDetail;


    /**
     * 清单放行时间
     */
    private Date customsPassTime;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 物流运单编号
     */
    private String logisticsNo;

    /**
     * 退货运单编号
     */
    private String refundLogisticsNo;

    /**
     * 租户id
     */
    private String tenantId;

    /**
     * 清单号
     */
    private String inventoryNo;

    /**
     * 电商企业
     */
    private Long ebcId;


    /**
     * 申报企业
     */
    private Long agentCompanyId;

    /**
     * 区内企业
     */
    private Long areaCompanyId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 取消时间
     */
    private Date calloffTime;

    private Date createTime;

    private String oper;

    /**
     * 退款记录图片集合json串
     */
    private String picJson;

    private String entityWarehouseCode;

    private String entityWarehouseName;

    private String ownerCode;

    private String ownerName;


    /**
     * 取消单 位运算
     */
    private Integer orderTag;

    /**
     * 取消指令报文
     */
    private String linkCustomsContent;
}
