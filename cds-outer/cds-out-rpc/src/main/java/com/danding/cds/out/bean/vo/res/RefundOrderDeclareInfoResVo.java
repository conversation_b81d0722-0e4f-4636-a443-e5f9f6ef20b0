package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 退货二次入区OMS请求 返回参数
 * @date 2022/4/16 17:30
 */
@Data
public class RefundOrderDeclareInfoResVo implements Serializable {
    /**
     * 申报单号
     */
    private String declareOrderNo;

    /**
     * 申报企业
     */
    private String agentCompanyName;

    /**
     * 申报企业代码
     */
    private String agentCode;

    /**
     * 海关十位编码
     */
    private String cebCode;
}
