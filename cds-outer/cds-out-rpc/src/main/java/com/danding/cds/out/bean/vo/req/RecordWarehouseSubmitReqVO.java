package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 备案-实体仓关联参数 提交类
 * @date 2022/3/4 16:12
 */
@Data
public class RecordWarehouseSubmitReqVO implements Serializable {
    private static final long serialVersionUID = -160731412233174321L;
    /**
     * 海关备案料号
     */
    /**
     * 海关备案料号
     */
    private List<String> productId;

    /**
     * 口岸
     */
    /**
     * 口岸
     */
    private String customsCode;

    /**
     * WMS仓编码List
     */
    /**
     * WMS仓编码List
     */
    private List<String> wmsWarehouseList;
}
