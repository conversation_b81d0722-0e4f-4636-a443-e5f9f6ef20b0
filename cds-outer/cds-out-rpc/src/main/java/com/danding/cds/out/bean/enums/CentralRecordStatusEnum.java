package com.danding.cds.out.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 审核状态(0 : 全部 / 1 : 待提交 / 2 : 待审核 / 3 : 备案完成 / 4 : 备案驳回)
 * @date 2023/8/25 15:09
 */
@Getter
@AllArgsConstructor
public enum CentralRecordStatusEnum {
    NULL(-1, "无"),
    WAIT_EXAMINE(1, "待审核"),
    FINISH(2, "备案完成"),
    REJECT(4, "备案驳回"),
    WAIT_COMMIT(8, "待提交");

    private Integer code;
    private String desc;

    public static CentralRecordStatusEnum getEnums(Integer code) {
        for (CentralRecordStatusEnum c : CentralRecordStatusEnum.values()) {
            if (Objects.equals(c.getCode(), code)) {
                return c;
            }
        }
        return NULL;
    }
}
