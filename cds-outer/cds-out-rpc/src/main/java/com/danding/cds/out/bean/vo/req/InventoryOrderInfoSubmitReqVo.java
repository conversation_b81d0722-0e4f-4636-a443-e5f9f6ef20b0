package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 清关单提交
 * @date 2022/3/30 10:17
 */
@Data
public class InventoryOrderInfoSubmitReqVo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 清关单SN
     */
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    /**
     * 清关企业(第一步骤)
     */
    private Long inveCompanyId;

    /**
     * 清关企业编码
     */
    private String inveCompanyCode;

    /**
     * 单据类型
     */
    private String inveOrderType;

    /**
     * 业务类型
     */
    private String inveBusinessType;

    /**
     * 选择区间转出，区内转出的业务类型时 ->关联转入账册字段
     */
    private String inAccountBook;

    /**
     * 选择区间转入，区内转入的业务类型时 ->关联转出账册
     */
    private String outAccountBook;

    /**
     * 核放单编号
     */
    private String refCheckOrderNo;

    /**
     * 仓库
     */
    private String warehouseId;

    /**
     * 核注清单编号
     */
    private String refHzInveNo;

    /**
     * 提取号(第一步骤) 用作关联核注清单编号
     */
    private String pickUpNo;

    /**
     * 进出境关别
     */
    private String entryExitCustoms;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 启运国
     */
    private String shipmentCountry;

    /**
     * 租户
     */
    private String rentPerson;

    /**
     * 账册ID
     */
    /**
     * 账册ID(第一步骤)
     */
    private Long bookId;

    /**
     * 账册号
     */
    private String bookNo;

    /**
     * 申请人(第一步骤)
     */
    private String applyPerson;

    /**
     * 备注(第一步骤)
     */
    private String remark;

    /**
     * 清关单状态
     */
    private String status;

    /**
     * 清关单状态对应时间
     */
    private Date statusTime;

    /**
     * 预计出区时间
     */
    private Date expectedOutAreaTime;

    /**
     * 预计到港时间
     */
    private Date expectedToPortTime;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人ID
     */
    private Integer createBy;
    /**
     * 更新人ID
     */
    private Integer updateBy;
    /**
     * 逻辑删除
     */
    private Boolean deleted = false;
    /**
     * 清关单对应的分录信息
     */
    private List<InventoryOrderItemSubmitReqVo> listItems;

    /**
     * 上游单据类型: ReadyOrder(备货) Distribution(配货)
     */
    private String channelBusinessType;

    /**
     * 上游单据编号(备货/配货单号)
     */
    private String channelBusinessSn;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * WMS仓编码
     */
    private String wmsWarehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 用户id
     * ERP下发(上游商家用户中心id)
     */
    private Long userId;

    /**
     * 用户名称
     * ERP下发(上游商家用户中心name)
     */
    private String userName;

    /**
     * 外部单号
     */
    private String upstreamNo;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 是否关仓协同
     */
    private Boolean collaborateFlag;

    /**
     * 质押货主标志
     */
    private Boolean pledgeOwnerFlag;

    /**
     * 审核方邮箱地址（用 ',' 分割）
     */
    private String mailAddrList;

    /**
     * 附件
     */
    private List<InventoryOrderAttachReqVo> attachList;

    /**
     * 货主是否自备车辆
     */
    private Boolean selfOwnedVehicle;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 非保标记 (1:是非保清关单, 0:不是非保清关单)
     */
    private Integer fbFlag;

    /**
     * 是否同用户调拨标记
     */
    private Boolean sameUserTransferFlag;

}
