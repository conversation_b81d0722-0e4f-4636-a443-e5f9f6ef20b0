package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 结转明细
 * @date 2023/4/21 17:28
 */
@Data
public class ReceiveCarryOverResVO implements Serializable {
    /**
     * 结转单号
     */
    private String carryOverNo;

    /**
     * 清关单号
     */
    private String inventoryOrderSn;

    /**
     * 核注单号
     */
    private String endorsementSn;

    /**
     * 清关单类型
     * SECTIONINNER_OUT 区内流转(出)
     * SECTIONINNER_IN  区内流转(入)
     */
    private String businessType;

    /**
     * 10:待核注
     */
    private Integer endorsementStatus;
}
