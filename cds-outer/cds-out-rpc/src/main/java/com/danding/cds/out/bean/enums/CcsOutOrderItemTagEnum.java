package com.danding.cds.out.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/3/25 13:25
 */
@Getter
@AllArgsConstructor
public enum CcsOutOrderItemTagEnum {
    NULL(2 >> 2, "无"),
    FB_GIFTS(2 >> 1, "非保赠品");

    private Integer code;

    private String desc;

    public static Integer getResult(CcsOutOrderItemTagEnum... tagEnum) {
        int sum = Arrays.stream(tagEnum).mapToInt(CcsOutOrderItemTagEnum::getCode).sum();
        return sum;
    }

}
