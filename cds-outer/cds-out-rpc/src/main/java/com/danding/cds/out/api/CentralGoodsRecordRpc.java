package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.*;
import com.danding.cds.out.bean.vo.res.*;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: 货品中心化Rpc接口
 * @date 2023/8/14 13:41
 */
public interface CentralGoodsRecordRpc {

    /**
     * 保存备案信息
     *
     * @param submit 备案参数
     * @return 备案id
     */
    RpcResult<Long> saveGoodsRecord(GoodsRecordSubmitReqVO submit) throws Exception;

    /**
     * 提交备案审核
     *
     * @param goodsRecordId 备案id
     * @return 备案id
     */
    RpcResult<Long> auditGoodsRecord(Long goodsRecordId, String customsCode) throws Exception;

    /**
     * 提交并审核
     *
     * @param submit 备案参数
     * @return 备案id
     * @throws Exception
     */
    RpcResult<Long> saveAndAuditGoodsRecord(GoodsRecordSubmitReqVO submit) throws Exception;

    /**
     * 备案分页查询 es
     * 过滤erp已删除的tag
     * @param reqVo
     * @return
     * @throws Exception
     */
    RpcResult<ListVO<CentralGoodsRecordPagingResVO>> paging(CentralGoodsRecordSearchReqVo reqVo) throws Exception;

    RpcResult<CentralRecordStatusCountResVO> getStatusCount(CentralGoodsRecordSearchReqVo reqVo, List<Integer> statusList) throws Exception;

    /**
     * 备案查询 ES
     *
     * @param reqVo
     * @return
     * @throws Exception
     */
    RpcResult<List<CentralGoodsRecordPagingResVO>> search(CentralGoodsRecordSearchReqVo reqVo) throws Exception;

    /**
     * 备案查询 mysql
     *
     * @param reqVo
     * @return
     * @throws Exception
     */
    RpcResult<List<CentralGoodsRecordPagingResVO>> searchByDB(CentralGoodsRecordSearchReqVo reqVo) throws Exception;

    /**
     * 备案详情
     * 会将口岸json覆盖回基础数据
     * @param reqVo
     * @return
     * @throws Exception
     */
    RpcResult<CentralGoodsRecordDetailResVo> detail(CentralGoodsRecordDetailReqVo reqVo) throws Exception;

    /**
     * 携带所有的口岸信息
     *
     * @param reqVo
     * @return
     * @throws Exception
     */
    RpcResult<CentralGoodsRecordDetailResVo> selectOne(CentralGoodsRecordDetailReqVo reqVo) throws Exception;

    /**
     * 根据备案id获取口岸列表
     *
     * @param goodsRecordId
     * @return
     */
    RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListById(Long goodsRecordId);

    /**
     * 根据用户id获取口岸列表
     *
     * @param userId
     * @return
     */
    RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListByUserId(String userId);

    RpcResult<List<CentralGoodsRecordCustomsInfoResVO>> getCustomsListByUserIdAndGoodsCode(String userId, String goodsCode);

    /**
     * 同步备案-实体仓的关联关系 搬仓用
     */
    RpcResult<String> syncCentralEntityWarehouse(CentralRecordSyncWarehouseSubmitReqVO submit);

    /**
     * 删除备案
     */
    RpcResult<String> delete(Long id) throws Exception;

    RpcResult<String> delete(List<Long> ids) throws Exception;

    /**
     * 不做事务处理
     *
     * @param reqVoList
     * @return
     */
    RpcResult<String> updateCentralImgInfo(List<CentralUpdateImgInfoReqVo> reqVoList);

    /**
     * 更新备案名称与条码
     *
     * @param reqVoList
     * @return
     */
    RpcResult<String> updateCentralNameAndBarCode(List<CentralUpdateNameAndBarCodeReqVo> reqVoList);

    /**
     * 通过统一料号查询关联料号
     *
     * @param unifiedProductIds 统一料号list
     * @return Map<String, String> 统一料号:关联料号 为空表示未查询到
     */
    RpcResult<Map<String, String>> queryAssociateProductIdByUnifiedProductId(List<String> unifiedProductIds);

    /**
     * 备案查询 （得物）
     * sku sku
     * erpEntityWarehouseCode 实体仓编码(ERP)
     * userId 用户Id
     *
     * @param reqVo 备案查询参数
     * @return PoizonGoodsRecordDetailResVo 备案详情（得物）
     */
    RpcResult<PoizonGoodsRecordDetailResVo> queryByPoizon(PoizonGoodsRecordDetailReqVo reqVo);
}
