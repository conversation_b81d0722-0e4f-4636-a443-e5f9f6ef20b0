package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: Raymond
 * @Date: 2020/8/11 17:46
 * @Description: 2022-03-02 版本原因废弃
 */
@Deprecated
@Data
public class GoodsRecordSyncSubmitReqVO implements Serializable {
    private static final long serialVersionUID = -4727363287992069966L;
    /**
     * ID 更新时必填
     */
    private Long id;

    /**
     * SKU不能为空
     */
    private String skuId;

    /**
     * 料号
     */
    private String productId;

    /**
     * 账册id
     */
    private Long customsBookId;

    /**
     * 账册编号
     */
    private String customsBookNo;

    /**
     * 条码
     */
    private String barCode;

    /**
     * 备案名称
     */
    private String goodsRecordName;

    /**
     * 型号
     */
    private String model;

    /**
     * 品牌（中文）
     */
    private String brand;

    /**
     * 品牌（英文）
     */
    private String brandEn;

    /**
     * 申报单价
     */
    private BigDecimal declarePrice;

    /**
     * 申报单位
     */
    private String declareUnit;

    /**
     * 申报币制
     */
    private String declareCurrency;

    /**
     * 净重
     */
    private BigDecimal netWeight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    /**
     * 长
     */
    private Integer length;

    /**
     * 宽
     */
    private Integer width;

    /**
     * 高
     */
    private Integer height;

    /**
     * HS编码
     */
    private String hsCode;

    /**
     * 增值税率
     */
    private BigDecimal vatRate;

    /**
     * 消费税率
     */
    private BigDecimal taxRate;

    /**
     * 成分
     */
    private String composition;

    /**
     * 海关申报要素
     */
    private String hgsbys;

    /**
     * 海关原产国
     */
    private String originCountry;

    /**
     * 功能
     */
    private String recordFunction;

    /**
     * 用途
     */
    private String recordUsage;

    /**
     * 仓库
     */
    private String warehouseId;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;

    /**
     * 法定第一计量数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;

    /**
     * 法定第二计量数量
     */
    private BigDecimal secondUnitAmount;

    /**
     * 上游渠道（1：出入库系统）
     */
    private Integer channel;

    /**
     * 0:停用;1:启用(默认)
     */
    private Integer enable;
    /**
     * 口岸
     */
    private String customs;
    /**
     * 货品id
     */
    private String goodsCode;
    /**
     * 生产企业名称
     */
    private String productCompanyName;
    /**
     * 生产企业注册编号
     */
    private String productCompanyRegisterNumber;
    /**
     * 生产企业地址
     */
    private String productCompanyAddress;
    /**
     * 商品链接
     */
    private String productLink;
    /**
     * 附件
     */
    private String attachmentName;
    /**
     * 附件地址
     */
    private String attachmentUrl;

}
