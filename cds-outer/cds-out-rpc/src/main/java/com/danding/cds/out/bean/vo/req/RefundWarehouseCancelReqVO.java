package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 退货仓取消 外部接口请求参数
 *
 * <AUTHOR>
 * @date 2024/01/29
 */
@Data
public class RefundWarehouseCancelReqVO implements Serializable {

    /**
     * 正向运单号
     */
    private String logisticsNo;

    /**
     * 正向快递公司编码
     */
    private String expressCode;


    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 逆向运单号
     */
    private String refundLogisticsNo;

    /**
     * 退货商品明细
     */
    private List<RefundGoodsInfo> refundGoodsInfoList;

    @Data
    public static class RefundGoodsInfo implements Serializable {
        /**
         * 货品名称
         */
        private String goodsName;
        /**
         * sku
         */
        private String sku;
        /**
         * 条码
         */
        private String barCode;
        /**
         * 商品数量
         */
        private Integer count;
        /**
         * 贸易类型
         * 1-保税 2-完税
         */
        private Integer tradeType;
    }
}
