package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 备案-口岸-实体仓
 * @date 2022/3/8 10:55
 */
@Data
public class GoodsRecordWarehouseInfoReqVO implements Serializable {
    private static final long serialVersionUID = -8271180155942494190L;
    /**
     * 口岸
     */
    private String customsCode;

    /**
     * 关联WMS仓编码
     */
    private List<String> wmsWarehouseList;
}
