package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 清关单提交
 * @date 2022/3/30 10:17
 */
@Data
public class InventoryOrderItemSubmitReqVo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 清关单ID
     */
    private Long refInveOrderId;
    /**
     * 清关单SN
     */
    private String refInveOrderSn;
    /**
     * 是否是新的(old:旧,new:新)
     */
    private String oldOrNew;
    /**
     * sku
     */
    private String skuId;
    /**
     * 商品料号(统一料号)
     * 必传
     */
    private String productId;

    /**
     * 海关备案料号
     * 选填 由上游指定
     */
    private String customsRecordProductId;

    /**
     * 商品序号
     */
    private String goodsSeqNo;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 货品ID
     */
    private String goodsCode;

    /**
     * 商品备案名称
     */
    private String recordProductName;

    /**
     * HS代码
     */
    private String hsCode;

    /**
     * 计划申报数量
     */
    private BigDecimal planDeclareQty;

    /**
     * 出库单号，多个用/分开
     */
    private String outBoundNo;

    /**
     * 实际理货数量
     */
    private BigDecimal actualTallyQty;

    /**
     * 申报单位数量
     */
    private Double declareUnitQfy;

    /**
     * 申报单价
     */
    private Double declarePrice; //add

    /**
     * 计量单位
     */
    private String unit;
    /**
     * 第一单位
     */
    private String firstUnit;
    /**
     * 第一单位数量
     */
    private Double firstUnitQfy;
    /**
     * 第二单位
     */
    private String secondUnit;
    /**
     * 第二单位数量
     */
    private Double secondUnitQfy;
    /**
     * 净重
     */
    private Double netweight;
    /**
     * 申报要素
     */
    private String declareFactor;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 商品条码
     */
    private String goodsBar;
    /**
     * 生产企业
     */
    private String productCompany;
    /**
     * 国检备案号
     */
    private String countryRecordNo;
    /**
     * 报关单商品序号
     */
    private String declareCustomsGoodsSeqNo;
    /**
     * 规格型号
     */
    private String goodsModel;
    /**
     * 最终目的国
     */
    private String destinationCountry;
    /**
     * 币制
     */
    private String currency;
    /**
     * 免征方式
     */
    private String avoidTaxMethod = "3";
    /**
     * 单号版本号
     */
    private String orderVersion;
    /**
     * 行号
     */
    private String idx;
    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

}
