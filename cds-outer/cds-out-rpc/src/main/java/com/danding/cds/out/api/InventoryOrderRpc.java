package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.*;
import com.danding.cds.out.bean.vo.res.InventoryOrderDiscardResVo;
import com.danding.cds.out.bean.vo.res.InventoryOrderInfoResVo;
import com.danding.cds.out.bean.vo.res.ReceiveCarryOverResVO;
import com.danding.cds.out.bean.vo.res.TaotianLogisticsTraceInfoResVo;
import com.danding.logistics.api.common.response.SelectItemVO;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description: CCS清关单相关RPC调用接口
 * @date 2022/3/29 13:51
 */
public interface InventoryOrderRpc {

    /**
     * 创建清关单 erp调用
     *
     * @param infoParam
     * @return
     */
    RpcResult<String> createInventoryOrderInner(InventoryOrderInfoSubmitReqVo infoParam);

    /**
     * RPC查询接口
     *
     * @param searchReqVo
     * @return
     */
    RpcResult<List<InventoryOrderInfoResVo>> findList(InventoryOrderSearchReqVo searchReqVo);

    /**
     * 清关单作废接口
     *
     * @param discardReqVO 清关单作废请求对象
     * @return 是否完成取消
     */
    InventoryOrderDiscardResVo discardInventoryOrder(InventoryOrderDiscardReqVo discardReqVO);

    /**
     * 接受理货报告(如果状态为确认中/已确认)
     * 1.接收 客户确认中 状态节点
     * 2.接收 理货报告
     *
     * @param tallyReport
     * @return
     */
    RpcResult<Boolean> receiveTallyReport(TallyReportReqVO tallyReport);

    /**
     * 接收wms结转明细
     *
     * @param carryOverReqVo
     * @return
     */
    RpcResult<List<ReceiveCarryOverResVO>> receiveCarryOverDetail(CarryOverCreateReqVo carryOverReqVo);

    RpcResult<List<ReceiveCarryOverResVO>> receiveCarryOverDetailBatch(CarryOverBatchCreateReqVo carryOverBatchCreateReqVo);

    /**
     * 返回 MAP<单号,企业名>
     *
     * @param inOutOrderNoList
     * @return
     */
    RpcResult<Map<String, String>> findCompanyByInOutOrderNo(List<String> inOutOrderNoList);

    /**
     * cw下发清关单
     *
     * @param reqVO 请求参数
     * @return 清关单编号
     */
    RpcResult<String> createInventoryByCw(InventoryOrderInfoCwSubmitReqVO reqVO);

    /**
     * 理货完成 (wms调用)
     *
     * @param inOutOrderNo 出入库单号
     * @return 清关单编号
     */
    RpcResult<String> finishTallyByInOutOrderNo(String inOutOrderNo);


    /**
     * 出入库单号获取清关企业名称 (wms调用)
     *
     * @param inOutOrderNo 出入库单号
     * @return 清关企业名称
     */
    RpcResult<String> getInvCompanyNameByInOutOrderNo(String inOutOrderNo);

    /**
     * 作废理货报告
     *
     * @param inOutOrderNo 出入库单号 (OB...)
     * @return
     */
    RpcResult<String> cancelTallyReport(String inOutOrderNo);


    /**
     * 运单号获取清关企业名称 (wms调用)
     *
     * @param mailNoList 运单号List
     * @return 清关企业名称
     */
    RpcResult<Map<String, String>> getInvCompanyNameByMailNo(List<String> mailNoList);

    /**
     * 作废理货报告校验
     *
     * @param inOutOrderNo 出入库单号 (OB...)
     * @return
     */
    RpcResult<String> checkCancelTallyReport(String inOutOrderNo);

    /**
     * 淘天-溯源接口
     *
     * @param itemCode 商品编码（淘天）
     */
    @Deprecated
    RpcResult<TaotianLogisticsTraceInfoResVo> getTaotianLogisticsTraceInfo(String itemCode);

    /**
     * 淘天-溯源接口 （WMS调用）
     *
     * @param orderNo TOB_FULFILL=ToB履行单：即TFOC单号
     */
    RpcResult<List<TaotianLogisticsTraceInfoResVo>> getTaotianLogisticsTraceInfoV2(String orderNo);


    /**
     * 绑定约车单号
     * @param invCustomsSn 清关单号
     * @param ycNo 约车单号
     * @return
     */
    RpcResult<String> boundYcNo(String invCustomsSn, String ycNo);

    /**
     * 更新清关单表体信息（erp）
     */
    RpcResult<String> updateInventoryOrderItem(InventoryOrderItemUpdateReq request);

    /**
     * 清关资料上传（erp）
     */
    RpcResult<String> uploadAttachList(InventoryOrderAttachUploadReq request);

    /**
     * 更新清关单（ERP）
     * 功能：一起更新表体信息和附件信息（updateInventoryOrderItem & uploadAttachList）
     */
    RpcResult<String> uploadInventoryOrder(InventoryOrderUploadReq request);

    /**
     * 清关单附件类型下拉（erp）
     */
    RpcResult<List<SelectItemVO>> listAttachTypeErp();
}
