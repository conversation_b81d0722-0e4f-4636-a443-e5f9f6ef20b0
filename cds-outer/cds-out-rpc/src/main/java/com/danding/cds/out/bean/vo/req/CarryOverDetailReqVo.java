package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description: 分区结转明细
 * @date 2023/4/22 12:11
 */
@Data
public class CarryOverDetailReqVo implements Serializable {
    /**
     * sku
     */
    private String sku;
    /**
     * 统一料号
     */
    private String productId;
    /**
     * 条码
     */
    private String barCode;
    /**
     * 名称
     */
    private String name;
    /**
     * 结转数量
     */
    private BigDecimal changeQty;

}
