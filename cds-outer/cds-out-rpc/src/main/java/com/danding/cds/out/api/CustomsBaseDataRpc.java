package com.danding.cds.out.api;

import com.danding.cds.out.bean.vo.res.*;

import java.util.List;

/**
 * 用于外部系统获取海关单位等信息
 */
public interface CustomsBaseDataRpc {
    /**
     * 海关原产国
     *
     * @param name
     * @return
     */
    CustomsCountryResVo findCountryByName(String name);

    CustomsCountryResVo findCountryByCode(String code);

    List<CustomsCountryResVo> listCountry();

    /**
     * 币制
     *
     * @param name
     * @return
     */
    CustomsCurrencyResVo findCurrencyByName(String name);

    CustomsCurrencyResVo findCurrencyByCode(String code);

    List<CustomsCurrencyResVo> listCurrency();

    CustomsHsResVo findHsByCode(String code);

    List<CustomsHsResVo> listHs();

    /**
     * 根据code 获取单位
     *
     * @param code
     * @return
     */
    CustomsUomResVo findUomByCode(String code);

    CustomsUomResVo findUomByName(String name);

    /**
     * 获取口岸信息
     */
    CustomsDistrictResVo findDistrictByCode(String code);

    CustomsDistrictResVo findDistrictByName(String name);

    List<CustomsDistrictResVo> findDistrictByName(List<String> nameList);
}
