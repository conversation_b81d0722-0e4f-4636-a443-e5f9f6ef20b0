package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 料号口岸信息
 * @author: 潘本乐（Belep）
 * @create: 2022-03-11 09:55
 **/
@Data
public class MaterialCustomsInfoResVo implements Serializable {

    private static final long serialVersionUID = 7518499595842893612L;
    /**
     * 口岸名称
     */
    private String customs;
    /**
     * 口岸编码
     */
    private String customsCode;
    /**
     * HS编码
     */
    private String hsCode;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 增值税
     */
    private String vat;
    /**
     * 消费税
     */
    private String saleTax;
}
