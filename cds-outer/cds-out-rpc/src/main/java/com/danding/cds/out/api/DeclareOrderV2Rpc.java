package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.OrderSubmitReqVo;

/**
 * @Author: yousx
 * @Date: 2023/12/08
 * @Description:
 */
public interface DeclareOrderV2Rpc {


    /**
     * 申报单提交
     *
     * @param submit 提交数据
     * @return 申报单主键ID
     */
    Long submit(OrderSubmitReqVo submit);

    /**
     * 接受菜鸟wms清单出区
     *
     * @param globalSystemSn GS单号
     */
    RpcResult<String> receiveCainiaoInventoryExitArea(String globalSystemSn);
}
