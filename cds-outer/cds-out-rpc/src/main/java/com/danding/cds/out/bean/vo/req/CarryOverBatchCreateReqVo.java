package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分区结转请求明细
 * @date 2023/4/20 11:00
 */
@Data
public class CarryOverBatchCreateReqVo implements Serializable {
    /**
     * WMS实体仓编码
     */
    private String warehouseCode;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 是否自动创建核注
     * 0 否
     * 1 是
     */
    private Integer autoCreateEndorsement;

    private List<CarryOverCreateReqVo> carryOverCreateReqVoList;
}
