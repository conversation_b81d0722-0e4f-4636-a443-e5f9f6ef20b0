package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class InventoryOrderSearchReqVo implements Serializable {
    /**
     * 清关单号
     */
    private String inveOrderSn;
    /**
     * 提单号
     */
    private String pickUpNo;
    /**
     * 清关状态
     */
    private String status;
    /**
     * 审核状态
     */
    private String auditStatus;
    /**
     * 核放单编号
     */
    private String refCheckOrderNo;
    /**
     * 业务类型
     */
    private String bussinessType;
    /**
     * 备注
     */
    private String remark;
//    /**    * 类型: 0/创建时间，1/清关时间     */
//    private String noType;

    /**
     * 创建开始时间
     */
    private Long beginCreateTime;
    /**
     * 创建结束时间
     */
    private Long endCreateTime;
    /**
     * 完成开始时间
     */
    private Long beginCompleteTime;
    /**
     * 完成结束时间
     */
    private Long endCompleteTime;

    /**
     * 账册id
     */
    private Long customsBookId;
}
