package com.danding.cds.out.bean.vo.res;

import java.io.Serializable;

import lombok.Data;

/**
 * @Author: yousx
 * @Date: 2024/04/07
 * @Description:
 */
@Data
public class CompanyVO implements Serializable {

    private static final long serialVersionUID = -1L;

    private Long id;
    /**
     * 企业名称
     */
    private String name;
    /**
     * 海关十位备案编码
     */
    private String code;
    /**
     * 统一社会信用代码
     */
    private String uniformSocialCreditCode;

    /**
     * 企业资质列表
     */
    private String qualifyJson;
    /**
     * 地方关区信息
     */
    private String districtJson;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;

    /**
     * 申报方式
     */
    private String declareConfig;

    /**
     * 备注
     */
    private String remark;

    private String createTime;
    /**
     * json储存的其他属性键值对
     */
    private String extraJson;

    /**
     * 以json储存 跨境数据交换申请传输节点
     */
    private String crossBorderDataExchangeNode;

    /**
     * 以json储存 非跨境数据交换申请传输节点
     */
    private String nonCrossBorderDataExchangeNode;

    /**
     * 保函关区
     */
    private String guaranteeCustoms;

    /**
     * 进出口收发货人编码
     * 海关总署编码
     */
    private String shipperAndConsigneeCode;

    /**
     * 报关企业编码
     * 海关总署编码
     */
    private String customsCompanyCode;

    /**
     * 跨境进口统一编码
     * 业务配置类型
     */
    private String unifiedCrossBroderCode;

    /**
     * 保税物流管理系统编码
     */
    private String specialClientCode;

    /**
     * 分类监管编码
     * 浙电 非保
     */
    private String fbCode;
}
