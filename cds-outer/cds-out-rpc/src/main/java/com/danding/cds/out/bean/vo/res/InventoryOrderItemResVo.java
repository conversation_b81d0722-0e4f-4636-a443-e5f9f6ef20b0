package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class InventoryOrderItemResVo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 清关单ID
     */
    /**
     * 清关单ID
     */
    private Long refInveOrderId;
    /**
     * 清关单SN
     */
    /**
     * 清关单SN
     */
    private String refInveOrderSn;
    /**
     * 是否是新的(0:旧,1:新)
     */
    /**
     * 是否是新的(old:旧,new:新)
     */
    private String isNew;
    /**
     * sku
     */
    /**
     * sku
     */
    private String skuId;
    /**
     * 商品料号
     */
    /**
     * 商品料号
     */
    private String productId;
    /**
     * 账册项号
     */
    /**
     * 商品序号
     */
    private String goodsSeqNo;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品备案名称
     */
    private String recordProductName;
    /**
     * HS代码
     */
    /**
     * HS代码
     */
    private String hsCode;
    /**
     * 计划申报数量
     */
    /**
     * 计划申报数量
     */
    private BigDecimal planDeclareQty;
    /**
     * 出库单号
     */
    /**
     * 出库单号，多个用/分开
     */
    private String outBoundNo;
    /**
     * 实际理货数量
     */
    /**
     * 实际理货数量
     */
    private BigDecimal actualTallyQty;
    /**
     * 申报单位数量
     */
    /**
     * 申报单位数量
     */
    private BigDecimal declareUnitQfy;

    /**
     * json 属性
     */
    /**
     * 计量单位
     */
    private String unit;
    /**
     * 第一单位
     */
    private String firstUnit;
    /**
     * 第一单位数量
     */
    private BigDecimal firstUnitQfy;
    /**
     * 第二单位
     */
    private String secondUnit;
    /**
     * 第二单位数量
     */
    private BigDecimal secondUnitQfy;
    /**
     * 净重
     */
    private BigDecimal netweight;
    /**
     * 申报要素
     */
    private String declareFactor;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 商品条码
     */
    private String goodsBar;
    /**
     * 生产企业
     */
    private String productCompany;
    /**
     * 国检备案号
     */
    private String countryRecordNo;

    /**
     * 申报单价
     *///add
    private BigDecimal declarePrice;
    /**
     * 申报总价
     *///add
    private BigDecimal declareTotalPrice;

    /**
     * 报关单商品序号
     */
    private String declareCustomsGoodsSeqNo;//add
    /**
     * 规格型号
     *///add
    private String goodsModel;
    /**
     * 最终目的国
     */
    private String destinationCountry;//add
    /**
     * 币制
     *///add
    private String currency;
    /**
     * 币制
     *///add
    private String currencyDesc;
    /**
     * 免征方式
     *///add
    private String avoidTaxMethod = "3";
    /**
     * 单号版本号
     *///add
    private String orderVersion;

    /**
     * 计量单位描述
     */
    private String unitDesc;
    /**
     * 第一单位描述
     */
    private String firstUnitDesc;
    /**
     * 第二单位
     */
    private String secondUnitDesc;
    /**
     * 原产国描述
     */
    private String originCountryDesc;
    /**
     * 目的国描述
     */
    private String destinationCountryDesc;
    /**
     * 额外其他属性
     */
    ///**    * 额外其他属性     */
    private String extraJson;

    /**
     * 核注清单ID
     */
    /**
     * 核注清单ID
     */
    private Long refEndorsementId;

    /**
     * 核注清单SN
     */
    /**
     * 核注清单SN
     */
    private String refEndorsementSn;

    //---------------------------------------------------

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人ID
     */
    private Integer createBy;
    /**
     * 更新人ID
     */
    private Integer updateBy;
    /**
     * 逻辑删除
     */
    private Boolean deleted = false;
}
