package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 备案详情（得物）
 * @date 2025/5/7
 */
@Data
public class PoizonGoodsRecordDetailResVo implements Serializable {
    /**
     * Hs编码
     */
    private String hsCode;

    /**
     * 口岸实体仓关系
     */
    private List<FilingRecordInfo> recordList;

    @Data
    public static class FilingRecordInfo implements Serializable {
        /**
         * 口岸审核状态 状态 审核中-1 已申报-2 已驳回-4 待申报-8
         */
        private Integer recordStatus;

        /**
         * Hs编码
         */
        private String hsCode;

        /**
         * 法一数量
         */
        private BigDecimal firstQuantity;

        /**
         * 法一单位
         */
        private String firstUnit;

        /**
         * 法二数量
         */
        private BigDecimal secondQuantity;

        /**
         * 法二单位
         */
        private String secondUnit;

        /**
         * 条码
         */
        private String barCode;

        /**
         * 备案名称
         */
        private String itemName;

        /**
         * 规格型号
         */
        private String specification;
    }
}
