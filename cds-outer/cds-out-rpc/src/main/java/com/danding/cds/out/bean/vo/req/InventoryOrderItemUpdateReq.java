package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @description: 清关单表体更新请求
 * @author: hou<PERSON><PERSON>e
 * @date: 2025/05/21
 */
@Data
public class InventoryOrderItemUpdateReq implements Serializable {

    /**
     * 上游单据类型: ReadyOrder(备货) Distribution(配货)
     */
    private String channelBusinessType;

    /**
     * 上游单据编号(备货/配货单号)
     */
    private String channelBusinessSn;

    /**
     * 附件列表
     */
    private List<ItemVo> itemVoList;

    @Data
    public static class ItemVo implements Serializable {

        /**
         * 商品料号(统一料号)
         * 必传
         */
        private String productId;
        /**
         * 原产国
         */
        private String originCountry;
        /**
         * 币制
         */
        private String currency;
        /**
         * 单价
         */
        private BigDecimal declarePrice;
        /**
         * 总毛重
         */
        private BigDecimal totalGrossWeight;
        /**
         * 总净重
         */
        private BigDecimal totalNetWeight;
    }
}