package com.danding.cds.out.bean.vo.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 统一备案删除同步请求Vo
 * @date 2024/8/20 09:15
 */
@Data
@NoArgsConstructor
public class CentralGoodsRecordDeleteSyncReqVo implements Serializable {
    private static final long serialVersionUID = -3339602863714849343L;
    /**
     * 备案id
     */
    private Long goodsRecordId;
    /**
     * 用户id 有可能为空
     */
    private Long userId;
    /**
     * 货品id
     */
    private String goodsCode;

}
