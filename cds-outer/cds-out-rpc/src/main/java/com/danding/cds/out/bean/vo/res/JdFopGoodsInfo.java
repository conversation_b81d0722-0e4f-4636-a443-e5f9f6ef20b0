package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class JdFopGoodsInfo implements Serializable {

    /**
     * ERP商品货号*
     */
    private String goodsCode;

    /**
     * ERP sku*
     */
    private String sku;

    /**
     * 商品序号*
     */
    private Integer gnum;

    /**
     * 数量*
     */
    private Integer quantity;

    /**
     * 单价*
     */
    private BigDecimal price;

    /**
     * 商品展示链接地址
     */
    private String itemLink;

    /**
     * 条码
     */
    private String barcodes;

    /**
     * 商品名称*
     */
    private String goodsName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 商品毛重
     */
    private BigDecimal grossWeight;

    /**
     * 商品净重
     */
    private BigDecimal netWeight;

    /**
     * 长
     */
    private BigDecimal length;

    /**
     * 宽
     */
    private BigDecimal width;

    /**
     * 高
     */
    private BigDecimal height;

    /**
     * 保质期
     */
    private Integer safeDays;

    /**
     * 是否批次管理
     * 1-否， 2-是
     * 跨境进口商品均为批次管理商品，填2
     */
    private String batch;

    /**
     * 商品国检备案号
     */
    private String qiRecord;

    /**
     * 商品海关备案号*
     * 取统一料号
     */
    private String customRecord;

    /**
     * 跨境进口业务模式*
     * 默认 beihuo
     */
    private String pattern;

    /**
     * 商品货号
     * 统一料号
     */
    private String sellerRecord;

    /**
     * 型号
     */
    private String modelNumber;

    /**
     * 规格*
     */
    private String spe;

    /**
     * 增值税率
     */
    private Integer vatRate;

    /**
     * 消费税率
     */
    private Integer taxRate;

    /**
     * 行邮税号
     */
    private String taxNumberPost;

    /**
     * 行邮税率
     */
    private String postRate;

    /**
     * 海关10位编码*
     */
    private String hsCode;

    /**
     * 海关原产国 (名称)*
     */
    private String country;

    /**
     * 国检原产国 (名称)*
     */
    private String qiCountry;

    /**
     * 法定第一计量单位
     */
    private String legalUnit1;

    /**
     * 法定第一计量数量
     */
    private BigDecimal legalAmount1;

    /**
     * 法定第二计量单位
     */
    private String legalUnit2;

    /**
     * 法定第二计量数量
     */
    private BigDecimal legalAmount2;

    /**
     * 海关计量单位*
     */
    private String measurement;

    /**
     * 国检计量单位*
     */
    private String qiMeasurement;

    /**
     * 海关申报要素*
     */
    private String hgsbys;

    /**
     * 商品英文名
     */
    private String goodsNameEn;
}
