package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @program: cds-center
 * @description: 申报单提交商品项
 * @author: 潘本乐（Belep）
 * @create: 2021-12-17 09:36
 **/
@Data
public class OrderSubmitItemReqVo implements Serializable {

    private static final long serialVersionUID = 8930479261832104919L;
    /**
     * 商品备案料号
     */
    private String recordNo;

    /**
     * 商品备案序号 非必填，未填时随机分配序号
     */
    private String recordGnum;

    /**
     * 商品货号
     */
    private String itemNo;

    /**
     * 商品品名
     */
    private String itemName;

    /**
     * 数量
     */
    private Integer count;

    /**
     * 单价
     */
    private BigDecimal unitPrice;

    /**
     * 表体标记
     * 传参可以选择用枚举(有序列化风险)调用getResult方法、或者位运算传参
     * {@link com.danding.cds.out.bean.enums.CcsOutOrderItemTagEnum}
     */
    private Integer itemTag;

    /**
     * 条码
     */
    private String barCode;
}
