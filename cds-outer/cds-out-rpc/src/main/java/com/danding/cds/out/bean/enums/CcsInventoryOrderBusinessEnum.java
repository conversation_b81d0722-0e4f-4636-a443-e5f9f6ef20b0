package com.danding.cds.out.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 清关单业务类型 外部调用
 * 替换 InventoryOrderBusinessEnum
 */
@Getter
@AllArgsConstructor
public enum CcsInventoryOrderBusinessEnum {
    BUSSINESS_EMPTY("", "空", CcsInventoryInOutEnum.NULL),
    BUSSINESS_REFUND_INAREA("REFUND_INAREA", "退货入区", CcsInventoryInOutEnum.IN),
    BUSSINESS_SECTION_OUT("SECTION_OUT", "区间流转(出)", CcsInventoryInOutEnum.OUT),
    BUSSINESS_SECTION_IN("SECTION_IN", "区间流转(入)", CcsInventoryInOutEnum.IN),
    BUSSINESS_SECTIONINNER_OUT("SECTIONINNER_OUT", "区内流转(出)", CcsInventoryInOutEnum.OUT),
    BUSSINESS_SECTIONINNER_IN("SECTIONINNER_IN", "区内流转(入)", CcsInventoryInOutEnum.IN),
    BUSSINESS_ONELINE_IN("ONELINE_IN", "一线入境", CcsInventoryInOutEnum.IN),
    BUSSINESS_DESTORY("DESTORY", "销毁", CcsInventoryInOutEnum.OUT),
    BUSINESS_ONELINE_REFUND("ONLINE_REFUND", "一线退运", CcsInventoryInOutEnum.OUT),
    BUSINESS_BONDED_TO_TRADE("BONDED_TO_TRADE", "保税物流转大贸", CcsInventoryInOutEnum.OUT),
    BUSINESS_SUBSEQUENT_TAX("SUBSEQUENT_TAX", "后续补税", CcsInventoryInOutEnum.OUT),
    BUSINESS_FB_IN("FB_IN", "非保入区", CcsInventoryInOutEnum.IN),
    BUSINESS_FB_OUT("FB_OUT", "非保出区", CcsInventoryInOutEnum.OUT),
    ;

    private String code;
    private String desc;
    private CcsInventoryInOutEnum type;

    public static CcsInventoryOrderBusinessEnum getEnum(String value) {
        for (CcsInventoryOrderBusinessEnum orderStatus : CcsInventoryOrderBusinessEnum.values()) {
            if (orderStatus.getCode().equals(value)) {
                return orderStatus;
            }
        }
        return BUSSINESS_EMPTY;
    }


}
