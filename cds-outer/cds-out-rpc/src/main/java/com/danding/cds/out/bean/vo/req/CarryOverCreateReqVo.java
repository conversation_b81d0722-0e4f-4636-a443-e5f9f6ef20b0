package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 分区结转请求明细
 * @date 2023/4/20 11:00
 */
@Data
public class CarryOverCreateReqVo implements Serializable {
    /**
     * 分区结转单号
     */
    private String carryOverNo;

    /**
     * WMS实体仓编码
     */
    private String warehouseCode;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 来源企业
     */
    private String originEnterprise;

    /**
     * 目标企业
     */
    private String targetEnterprise;

    /**
     * 是否自动创建核注
     * 0 否
     * 1 是
     */
    private Integer autoCreateEndorsement;

    /**
     * 结转明细
     */
    private List<CarryOverDetailReqVo> carryOverDetailList;
}
