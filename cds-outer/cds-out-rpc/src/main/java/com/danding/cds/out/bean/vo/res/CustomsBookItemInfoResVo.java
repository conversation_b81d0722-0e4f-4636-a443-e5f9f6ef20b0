package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 外部接口 - 账册库存InfoResVo
 */
@Data
public class CustomsBookItemInfoResVo implements Serializable {

    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 申报币制
     */
    private String currCode;
    /**
     * 申报单价金额
     */
    private BigDecimal declarePrice;
}
