package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class InventoryOrderItemReqVo implements Serializable {
    /**
     * 商品料号(统一料号)
     * 必传
     */
    private String productId;
    /**
     * 原产国
     */
    private String originCountry;
    /**
     * 币制
     */
    private String currency;
    /**
     * 单价
     */
    private BigDecimal declarePrice;
    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;
    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;
}
