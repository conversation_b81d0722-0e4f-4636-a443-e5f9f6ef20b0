package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 货品状态计数
 * @date 2023/8/25 15:16
 */
@Data
public class CentralRecordStatusCountResVO implements Serializable {
    /**
     * 待提交计数
     */
    private Integer waitCommitCount;
    /**
     * 待审核
     */
    private Integer waitExamineCount;
    /**
     * 备案完成
     */
    private Integer finishCount;
    /**
     * 备案驳回
     */
    private Integer rejectCount;
}
