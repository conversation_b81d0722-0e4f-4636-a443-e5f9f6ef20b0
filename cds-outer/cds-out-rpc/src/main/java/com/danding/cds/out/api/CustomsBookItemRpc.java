package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.res.CustomsBookItemInfoResVo;

/**
 * 账册库存外部Rpc
 *
 * <AUTHOR>
 */
public interface CustomsBookItemRpc {

    /**
     * 根据【外部料号 + 账册】查询【账册库存货品名称】
     *
     * @param proId  外部料号(海关备案料号)
     * @param bookNo 账册
     * @return 货品名称
     */
    RpcResult<String> getGoodsNameByProIdAndBookNo(String proId, String bookNo);


    /**
     * 根据【外部料号 + 账册】 获取海关备案信息
     *
     * @param proId  外部料号(海关备案料号)
     * @param bookNo 账册
     * @return
     */
    RpcResult<CustomsBookItemInfoResVo> getCustomsBookItemInfoByProIdAndBookNo(String proId, String bookNo);
}
