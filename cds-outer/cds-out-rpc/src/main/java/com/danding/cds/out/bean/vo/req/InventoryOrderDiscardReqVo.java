package com.danding.cds.out.bean.vo.req;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/29 14:37
 */
@NoArgsConstructor
@Data
public class InventoryOrderDiscardReqVo implements Serializable {

    /**
     * 实体仓编码
     */
    @NotBlank(message = "实体仓编码不能为空")
    private String warehouseCode;

    /**
     * 货主编码
     */
    @NotBlank(message = "货主编码不能为空")
    private String ownerCode;

    /**
     * 入库单号/出库单号
     */
    @NotBlank(message = "入库单号/出库单号不能为空")
    private String inOutOrderNo;

    /**
     * 外部单号
     */
    @NotBlank(message = "外部单号不能为空")
    private String upstreamNo;

    /**
     * 单据类型( 1 : 入库单, 2:出库单)
     */
    @NotBlank(message = "单据类型不能为空")
    private String type;

    /**
     * 取消原因
     */
    private String cancelReason;
}
