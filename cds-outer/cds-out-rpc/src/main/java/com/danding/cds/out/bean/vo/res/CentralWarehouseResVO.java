package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 货品中心化实体仓信息
 * @date 2023/8/15 10:47
 */
@Data
public class CentralWarehouseResVO implements Serializable {
    private Long id;
    /**
     * 实体仓sn
     */
    private String warehouseSn;

    /**
     * 账册id
     */
    private Long customsBookId;

    /**
     * 账册号
     */
    private String customsBookNo;

    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;

    /**
     * erp实体仓名称
     */
    private String erpWarehouseName;

    /**
     * wms仓编码
     */
    private String wmsWarehouseCode;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Boolean deleted;

}
