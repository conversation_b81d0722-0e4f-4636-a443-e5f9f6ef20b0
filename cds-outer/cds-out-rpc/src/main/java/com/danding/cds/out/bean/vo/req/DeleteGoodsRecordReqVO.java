package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 删除备案请求体
 * @date 2022/9/28 09:46
 */
@Data
public class DeleteGoodsRecordReqVO implements Serializable {
    /**
     * 备案类型
     * 新:NEW /老:OLD
     */
    private String recordType;

    /**
     * 料号 新备案时必传
     */
    private String productId;

    /**
     * sku 旧备案时必传
     */
    private String skuId;

    /**
     * 用户 旧备案时必传
     */
    private String tenantId;

    /**
     * 账册 旧备案时必传
     */
    private String customsBookNo;
}
