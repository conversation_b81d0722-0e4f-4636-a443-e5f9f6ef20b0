package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class JdFopDetailResVO implements Serializable {

    /**
     * 全局订单号*
     */
    private String globalSystemSn;

    /**
     * 申报单号*
     */
    private String declareOrderNo;

    /**
     * 清关单号*
     */
    private String inventoryNo;

    /**
     * 是否申报订单*
     * 1-是；2-否
     */
    private String declareOrder;

    /**
     * 电商平台的订单类型*
     * I-进口商品订单；E-出口商品订单
     */
    private String postType;

    /**
     * 跨境业务模式*
     * (保税备货=beihuo，保税直邮=zhiyou，个人快件=grkuaijian，邮政=youzheng),长度不超过30
     * 默认 beihuo
     */
    private String pattern;

    /**
     * 保税区编码*
     */
    private String customs;

    /**
     * 电商平台代码
     */
    private String ebpCode;

    /**
     * 电商平台名称
     */
    private String ebpName;

    /**
     * 电商企业代码
     */
    private String ebcCode;

    /**
     * 电商企业名称
     */
    private String ebcName;

    /**
     * 是否申报支付单*
     * 1-是;2-否
     * 默认 2
     */
    private String declarePaymentList;

    /**
     * 支付企业代码*
     */
    private String payCode;

    /**
     * 支付企业名称*
     */
    private String payName;

    /**
     * 是否申报运单*
     * 1-是； 2-否
     */
    private String declareWayBill;

    /**
     * 申报商品明细*
     */
    private List<JdFopGoodsInfo> goodsList;

}
