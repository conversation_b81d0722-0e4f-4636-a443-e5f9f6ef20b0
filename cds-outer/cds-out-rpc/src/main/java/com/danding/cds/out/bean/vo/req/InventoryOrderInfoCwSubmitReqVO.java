package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * Cw下发清关单提交Req (外部调用)
 */
@Data
public class InventoryOrderInfoCwSubmitReqVO implements Serializable {

    /**
     * 出入库类型
     * eg: in/out
     */
    private String inOutFlag;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * WMS仓编码
     */
    private String wmsWarehouseCode;

    /**
     * 货主编码
     */
    private String ownerCode;

    /**
     * 货主名称
     */
    private String ownerName;

    /**
     * 提单号
     */
    private String pickUpNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品信息
     */
    private List<InventoryOrderItemCwSubmitReqVO> itemCwSubmitReqVOList;

}
