package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/29 15:40
 */
@Data
public class TallyReportDetailReqVo implements Serializable {

    /**
     * 理货编号
     */
    @NotBlank(message = "理货编号不能为空")
    private String tallyOrderNo;

    /**
     * 货品ID
     */
    private String goodsCode;

    /**
     * 货品名称
     */
    @NotBlank(message = "货品名称不能为空")
    private String goodsName;

    /**
     * 外部sku
     */
    @NotBlank(message = "外部sku不能为空")
    private String sku;

    /**
     * 条形码
     */
    @NotBlank(message = "条形码不能为空")
    private String barcode;

    /**
     * 申报料号
     */
    @NotBlank(message = "申报料号不能为空")
    private String productId;

    /**
     * 实际理货数量
     */
    @NotNull(message = "实际理货数量不能为空")
    private Integer tallyNum;

    /**
     * 毛重
     */
    @NotNull(message = "毛重不能为空")
    private BigDecimal weight;

}
