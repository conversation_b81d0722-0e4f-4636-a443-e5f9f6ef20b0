package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * 物流溯源
 */
@Data
public class TaotianLogisticsTraceInfoResVo implements Serializable {

    /**
     * 原产国（地区）
     */
    private String originRegion;

    /**
     * 原产国（地区）编码
     */
    private String originRegionCode;

    /**
     * 启运国（地区）
     */
    private String fromRegion;

    /**
     * 启运国（地区）编码
     */
    private String fromRegionCode;

    /**
     * 装货港
     */
    private String fromLocation;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 入区海关
     */
    private String entryPort;

    /**
     * 入区海关申报单号 （核注清单编号）
     */
    private String entryPortDeclareNo;

    /**
     * 出区海关
     */
    private String exitPort;

    /**
     * 商品编码
     */
    private String itemCode;

}
