package com.danding.cds.out.bean.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 清关单-出入区 外部调用
 */
@Getter
@AllArgsConstructor
public enum CcsInventoryInOutEnum {
    NULL("", "空"),
    IN("in", "入区"),
    OUT("out", "出区");

    private String code;
    private String desc;

    public static CcsInventoryInOutEnum getEnum(String code) {
        for (CcsInventoryInOutEnum inventoryInOutEnum : CcsInventoryInOutEnum.values()) {
            if (inventoryInOutEnum.getCode().equals(code)) {
                return inventoryInOutEnum;
            }
        }
        return null;
    }
}
