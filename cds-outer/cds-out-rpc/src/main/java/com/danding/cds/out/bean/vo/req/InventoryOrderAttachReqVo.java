package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryOrderAttachReqVo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;
    /**
     * 清关单ID
     */
    private Long refInveOrderId;
    /**
     * 清关单SN
     */
    private String refInveOrderSn;
    /**
     * 附件名称
     */
    private String attachName;
    /**
     * 存储文件名
     */
    private String storeName;
    /**
     * 存储路径
     */
    private String attachPath;

    /**
     * 文档类型
     */
    private String contentType;

    /**
     * 附件类型 0-其他 1-箱单/发票/合同
     */
    private String attachType;

    /**
     * 附件来源 1-ERP 2-CCS 3-WMS 4淘天
     */
    private Integer source;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人ID
     */
    private Integer createBy;
    /**
     * 更新人ID
     */
    private Integer updateBy;
    /**
     * 逻辑删除
     */
    private Boolean deleted = false;
}
