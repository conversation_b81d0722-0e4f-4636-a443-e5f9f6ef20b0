package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.CalloffOrderCountReqVo;
import com.danding.cds.out.bean.vo.req.RefundWarehouseCancelReqVO;
import com.danding.cds.out.bean.vo.res.CustomsCancelOrderMappingResVO;
import com.danding.cds.out.bean.vo.res.CustomsInventoryCalloffResVo;

import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/3/3 15:26
 */
public interface CalloffOrderRpc {
    CustomsInventoryCalloffResVo findByGlobalSnFull(String globalSn);

    /**
     * 退货仓取消接口
     *
     * @param reqVO
     * @return
     */
    RpcResult<String> refundWarehouseCancel(RefundWarehouseCancelReqVO reqVO);

    List<CustomsCancelOrderMappingResVO> getRefundOrderByOwnerCode(CalloffOrderCountReqVo reqVo) throws Exception;

    List<CustomsCancelOrderMappingResVO> getCallBackOrderByOwnerCode(CalloffOrderCountReqVo reqVo) throws Exception;

    /**
     * 京东退货取消接口
     *
     * @param data 请求JSON报文
     * @return
     */
    RpcResult<String> jdRefundCancel(String data);
}
