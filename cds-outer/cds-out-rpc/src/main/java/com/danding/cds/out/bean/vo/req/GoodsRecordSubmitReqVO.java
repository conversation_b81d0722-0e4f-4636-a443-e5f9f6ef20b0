package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;

/**
 * 备案ERP下发
 *
 * <AUTHOR>
 */
@Data
public class GoodsRecordSubmitReqVO implements Serializable {
    private static final long serialVersionUID = -2282903191574837449L;

    /**
     * 备案基础信息
     */
    private GoodsRecordBaseInfoReqVO goodsRecordBaseInfo;
    /**
     * OLD = "老备案",
     * NEW = "新备案",
     */
    private String recordType;

    /**
     * 备案口岸信息
     */
    private GoodsRecordWarehouseInfoReqVO goodsRecordWarehouseInfo;


    /**
     * 自动备案标记
     */
    private Boolean autoRecordFlag = false;

    /**
     * 来源
     * (0, "ccs系统导入"),
     * (1, "记账回执"),
     * //ERP下发
     * (2, "erp下发"),
     * (3, "erp下发(自动)"),
     * (4, "ccs系统导入(自动)");
     */
    private Integer source;

}
