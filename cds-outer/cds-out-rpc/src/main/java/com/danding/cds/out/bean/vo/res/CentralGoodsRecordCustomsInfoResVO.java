package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 货品中心化口岸数据
 * @date 2023/8/15 10:46
 */
@Data
public class CentralGoodsRecordCustomsInfoResVO implements Serializable {
    private List<CentralWarehouseResVO> warehouseResVOList;

    /**
     * 商品备案id
     */
    private Long recordId;

    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;

    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 状态 待审核0 /通过 1 /驳回 4
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * hs编码
     */
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;
    private String firstUnitDesc;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;
    private String secondUnitDesc;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;

    /**
     * 原产国
     */
    private String originCountry;

    private String originCountryName;

    /**
     * 增值税
     */
    private BigDecimal vat;

    /**
     * 消费税
     */
    private BigDecimal consumption;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Boolean deleted;


    /**
     * 审核方式
     */
    private Integer auditWay;
    private String auditWayDesc;

    /**
     * 口岸json快照
     */
    private String goodsRecordJson;

    /**
     * ERP提交审核状态
     * 1 表示保存未提交
     * 0 表示无保存 或已提交
     */
    private Integer erpCommitStatus;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
