package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @description: 查询详情（得物）
 */
@Data
public class PoizonGoodsRecordDetailReqVo implements Serializable {


    /**
     * sku
     */
    @NotBlank(message = "sku不能为空")
    private String sku;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long userId;

    /**
     * erp实体仓编码
     */
    @NotBlank(message = "erp实体仓编码不能为空")
    private String erpEntityWarehouseCode;
}
