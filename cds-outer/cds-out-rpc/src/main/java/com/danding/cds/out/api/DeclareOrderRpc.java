package com.danding.cds.out.api;

import com.danding.cds.out.bean.vo.req.OrderSubmitReqVo;
import com.danding.cds.out.bean.vo.res.JdFopDetailResVO;

/**
 * @program: cds-center
 * @description: 申报单相关Rpc调用
 * @author: 潘本乐（Belep）
 * @create: 2021-11-30 17:36
 **/
public interface DeclareOrderRpc {

    /**
     * 申报单提交
     *
     * @param submit 提交数据
     * @return 申报单主键ID
     * @link com.danding.cds.out.api.DeclareOrderV2Rpc#submit(com.danding.cds.out.bean.vo.req.OrderSubmitReqVo)
     */
    @Deprecated
    Long submit(OrderSubmitReqVo submit);

    /**
     * 获取京东FOP 订单主体和备案信息
     *
     * @param declareOrderNo 申报单号
     * @return 订单主体和备案信息
     */
    JdFopDetailResVO getJdFopDetailByDeclareOrderNo(String declareOrderNo);

}
