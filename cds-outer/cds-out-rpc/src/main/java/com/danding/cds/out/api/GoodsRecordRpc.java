package com.danding.cds.out.api;

import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.DeleteGoodsRecordReqVO;
import com.danding.cds.out.bean.vo.req.GoodsRecordSubmitReqVO;
import com.danding.cds.out.bean.vo.req.GoodsRecordSyncSubmitReqVO;
import com.danding.cds.out.bean.vo.req.RecordWarehouseSubmitReqVO;
import com.danding.cds.out.bean.vo.res.MaterialCustomsInfoResVo;

import java.util.List;

/**
 * @program: cds-center
 * @description: 商品配置相关Rpc调用
 * @author: 潘本乐（Belep）
 * @create: 2021-11-30 17:52
 **/
@Deprecated
public interface GoodsRecordRpc {


    /**
     * 根据料号查询对应的口岸信息
     * @param materialCode 料号
     * @return 料号对应口岸信息
     */
    List<MaterialCustomsInfoResVo> getCustomsInfoByMaterialCode(String materialCode);

    /**
     * 同步商品备案(已废弃)
     *
     * @param submit ERP下发字段
     * @return 返回备案ID
     */
    Long syncCustomsRecord(GoodsRecordSyncSubmitReqVO submit);

    /**
     * 同步商品备案
     * 添加了关联口岸、实体仓关系
     *
     * @param submit
     * @return
     */
    Long syncCustomsRecordV2(GoodsRecordSubmitReqVO submit);

    /**
     * 同步备案-实体仓的关联关系 搬仓用
     *
     * @param submit
     */
    void syncEntityWarehouse(RecordWarehouseSubmitReqVO submit);

    /**
     * 删除备案信息
     *
     * @return
     */
    RpcResult<String> deleteGoodsRecord(DeleteGoodsRecordReqVO reqVO);
}
