package com.danding.cds.out.bean.vo.req;

import com.danding.cds.out.bean.enums.CentralRecordStatusEnum;
import com.danding.logistics.api.common.page.Page;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 货品中心查询
 * @date 2023/8/14 15:48
 */
@Data
public class CentralGoodsRecordSearchReqVo extends Page implements Serializable {
    private static final long serialVersionUID = -6350030461004218059L;
    @ApiModelProperty("备案名称")
    private String goodsRecordName;

    @ApiModelProperty("备案时间（开始）")
    private Long recordTimeFrom;

    @ApiModelProperty("备案时间（结束）")
    private Long recordTimeTo;

    /**
     * {@link CentralRecordStatusEnum}
     * 审核状态(0:全部/1:待审核/2:备案完成/4:备案驳回/8:待提交)
     */
    @ApiModelProperty("审核状态(0:全部/1:待审核/2:备案完成/4:备案驳回/8:待提交)")
    private Integer recordStatus;

    @ApiModelProperty("口岸")
    private String customDistrict;

    @ApiModelProperty("账册id")
    private Long customsBookId;
    private String customsBookNo;

    @ApiModelProperty("货品ID")
    private List<String> goodsCodeList;

    @ApiModelProperty("统一料号")
    private List<String> productIdList;

    @ApiModelProperty("海关备案料号")
    private List<String> customsRecordProductIdList;

    @ApiModelProperty("SKU")
    private List<String> skuList;

    @ApiModelProperty("条码")
    private List<String> barCodeList;

    @ApiModelProperty("通关料号")
    private List<String> warehouseExternalProductIdList;

    @ApiModelProperty("外部料号")
    private List<String> externalProductIdList;

    @ApiModelProperty("已申报口岸数据")
    private List<String> examinedCustomsCodeList;

    @ApiModelProperty("已提交口岸数据")
    private List<String> submittedCustomsCodeList;

    @ApiModelProperty("待申报口岸数据")
    private List<String> waitExamineCustomsCodeList;

    @ApiModelProperty("审核中口岸数据")
    private List<String> underReviewCustomsCodeList;

    private List<String> customsCodeList;

    /**
     * true 为 过滤
     * false 为 不过滤
     */
    @ApiModelProperty("是否过滤已删除的erp数据")
    private boolean isFilterErpDeleted;

    /**
     * 已备案：备案idList
     */
    private List<Long> recordIdList;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
