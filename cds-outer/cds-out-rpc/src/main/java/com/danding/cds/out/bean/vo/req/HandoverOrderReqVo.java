package com.danding.cds.out.bean.vo.req;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/14
 */
@Data
public class HandoverOrderReqVo implements Serializable {

    /**
     * 交接单号
     */
    private String handoverSn;

    /**
     * 包裹数
     */
    private Integer packageNum;

    /**
     * 总重量(Kg)
     */
    private String totalWeight;

    /**
     *总托数
     */
    private Integer totalPalletsNum;

    /**
     * 快递公司名称(wms系统数据)
     */
    private String wmsExpressName;

    /**
     * 快递公司ID(wms系统数据)
     */
    private String wmsExpressCode;

    /**
     * 仓库名称
     */
    private String storeHouseName;

    /**
     * 仓库编码
     */
    private String storeHouseSn;

    /**
     * 出库状态0-待生成出库单 1-已生成出库单
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Long createTime;
    /**
     * 创建人
     */
    private Integer createBy;
    private Integer updateBy;

    /**
     * 交接单明细
     */
    private List<HandoverOrderDetailReqVo> handoverOrderDetailReqVo;


}
