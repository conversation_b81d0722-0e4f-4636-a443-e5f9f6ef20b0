package com.danding.cds.out.bean;

import com.danding.cds.out.common.constant.ResultCodeCons;
import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 封装
 * @author: 潘本乐（Belep）
 * @create: 2022-03-30 14:49
 **/
@Data
public class RpcResult<T> implements Serializable {

    private static final long serialVersionUID = 8351783528625556686L;

    private Integer code;

    private String message;

    private T data;

    /**
     * Instantiates a new Rpc result.
     */
    public RpcResult() {
    }

    /**
     * Instantiates a new Rpc result.
     *
     * @param code    the code
     * @param message the message
     * @param data    the data
     */
    public RpcResult(final Integer code, final String message, final T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * return success.
     *
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> success() {
        return success("success");
    }

    /**
     * return success.
     *
     * @param msg msg
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> success(final String msg) {
        return success(msg, null);
    }

    /**
     * return success.
     *
     * @param data this is result data.
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> success(final T data) {
        return success("success", data);
    }

    /**
     * return success.
     *
     * @param msg  this ext msg.
     * @param data this is result data.
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> success(final String msg, final T data) {
        return get(ResultCodeCons.SUCCESSFUL, msg, data);
    }


    /**
     * Success soul web result.
     *
     * @param code the code
     * @param msg  the msg
     * @param data the data
     * @return the rpc result
     */
    public static <T> RpcResult<T> success(final Integer code, final String msg, final T data) {
        return get(code, msg, data);
    }

    /**
     * return error .
     *
     * @param msg error msg
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> error(final String msg) {
        return error(ResultCodeCons.BUSINESS_ERROR, msg);
    }

    /**
     * return error .
     *
     * @param code error code
     * @param msg  error msg
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> error(final Integer code, final String msg) {
        return get(code, msg, null);
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> RpcResult<T> isSuccess(final Boolean isSuccess, final String errorMsg) {
        if (isSuccess) {
            return success();
        } else {
            return error(errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> RpcResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final Integer errorCode) {
        if (isSuccess) {
            return success();
        } else {
            return error(errorCode, errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> RpcResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final T data) {
        if (isSuccess) {
            return success(data);
        } else {
            return error(errorMsg);
        }
    }

    /**
     * Success soul web result.
     *
     * @param isSuccess the isSuccess
     * @return the rpc result
     */
    public static <T> RpcResult<T> isSuccess(final Boolean isSuccess, final String errorMsg, final Integer errorCode, final T data) {
        if (isSuccess) {
            return success(data);
        } else {
            return error(errorCode, errorMsg);
        }
    }

    /**
     * return error .
     *
     * @param code error code
     * @param msg  error msg
     * @param data the data
     * @return {@linkplain RpcResult}
     */
    public static <T> RpcResult<T> error(final Integer code, final String msg, final T data) {
        return get(code, msg, data);
    }

    private static <T> RpcResult<T> get(final Integer code, final String msg, final T data) {
        return new RpcResult(code, msg, data);
    }
}
