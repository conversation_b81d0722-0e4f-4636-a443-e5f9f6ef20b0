package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InventoryOrderInfoResVo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 清关单SN
     */
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    /**
     * 清关企业(第一步骤)
     */
    private Long inveCompanyId;

    /**
     * 单据类型
     */
    private String inveOrderType;
    /**
     * 业务类型
     */
    private String inveBusinessType;

    /**
     * 选择区间转出，区内转出的业务类型时 ->关联转入账册字段
     */
    private String inAccountBook;

    /**
     * 选择区间转入，区内转入的业务类型时 ->关联转出账册
     */
    private String outAccountBook;

    /**
     * 核放单编号
     */
    private String refCheckOrderNo;
    /**
     * 核注清单编号
     */
    /**
     * 核注清单编号
     */
    private String refHzInveNo;
    /**
     * 提取号
     */
    /**
     * 提取号(第一步骤) 用作关联核注清单编号
     */
    private String pickUpNo;
    /**
     * 进出境关别
     */
    /**
     * 进出境关别
     */
    private String entryExitCustoms;
    /**
     * 运输方式
     */
    /**
     * 运输方式
     */
    private String transportMode;
    /**
     * 启运国
     */
    /**
     * 启运国
     */
    private String shipmentCountry;
    /**
     * 租户
     */
    /**
     * 租户
     */
    private String rentPerson;

    /**
     * 账册ID
     */
    /**
     * 账册ID(第一步骤)
     */
    private Long bookId;
    /**
     * 申请人
     */
    /**
     * 申请人(第一步骤)
     */
    private String applyPerson;
    /**
     * 备注
     */
    /**
     * 备注(第一步骤)
     */
    private String remark;

    /**
     * 来源渠道
     */
    private Integer channel;
    /**
     * 清关单状态
     */
    /**
     * 清关单状态
     */
    private String status;
    /**
     * 审核状态
     */
    private String auditStatus;

    /**
     * 清关单状态对应时间
     */
    /**
     * 清关单状态对应时间
     */
    private Date statusTime;


    /**
     * 预计出区时间
     */
    private Date expectedOutAreaTime;

    /**
     * 预计到港时间
     */
    private Date expectedToPortTime;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    /**
     * 状态:0.停用;1.启用(默认)
     */
    private Integer enable;
    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 更新时间
     */
    private Date updateTime;
    /**
     * 创建人ID
     */
    private Integer createBy;
    /**
     * 更新人ID
     */
    private Integer updateBy;
    /**
     * 逻辑删除
     */
    private Boolean deleted = false;
    /**
     * 清关单对应的分录信息
     */
    private List<InventoryOrderItemResVo> listItems;
    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 上游单据类型: ReadyOrder(备货) Distribution(配货)
     */
    private String channelBusinessType;

    /**
     * 上游单据编号
     */
    private String channelBusinessSn;

    /**
     * 清单类型（指区港联动等）
     */
    private String customsInvtType;

    /**
     * 对应报关单号(区港联动时才有)
     */
    private String customsEntryNo;

    /**
     * 对应报关企业
     */
    private Long customsEntryCompany;

    /**
     * 对应报关单类型
     */
    private String customsEntryType;

    /**
     * 主单号
     */
    private String masterOrderSn;

    /**
     * 子单号
     */
    private String subOrderSn;

    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 货主编码/名称
     */
    private String ownerCode;

    /**
     * 货主编码/名称
     */
    private String ownerName;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 清关完成时间
     */
    private String inventoryCompleteTime;

    /**
     * 服务完成时间
     */
    private String inventoryFinishTime;
}
