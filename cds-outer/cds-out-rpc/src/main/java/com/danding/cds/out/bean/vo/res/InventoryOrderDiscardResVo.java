package com.danding.cds.out.bean.vo.res;

import lombok.Data;

import java.io.Serializable;

/**
 * @program: cds-center
 * @description: 清关单作废回传
 * @author: 潘本乐（Belep）
 * @create: 2022-03-30 13:48
 **/
@Data
public class InventoryOrderDiscardResVo implements Serializable {

    /**
     * 作废是否成功：true：作废成功；false：作废失败
     */
    private boolean discardSuccess;
    /**
     * 描述
     */
    private String message;
}
