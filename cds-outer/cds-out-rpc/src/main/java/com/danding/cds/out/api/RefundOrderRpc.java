package com.danding.cds.out.api;

import com.danding.cds.out.bean.vo.res.RefundOrderDeclareInfoResVo;

/**
 * <AUTHOR>
 * @description: 退货单RPC接口
 * @date 2022/4/16 16:49
 */
public interface RefundOrderRpc {
    /**
     * 获取二次入区包裹信息
     *
     * @param declareOrderNo
     * @return
     */
    RefundOrderDeclareInfoResVo getRefundOrderDeclareInfo(String declareOrderNo);

    RefundOrderDeclareInfoResVo getRefundOrderDeclareInfoByLogisticsNo(String logisticsNo);
}
