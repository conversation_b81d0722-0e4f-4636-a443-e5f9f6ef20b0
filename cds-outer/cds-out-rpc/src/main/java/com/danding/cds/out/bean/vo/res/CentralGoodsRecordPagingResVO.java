package com.danding.cds.out.bean.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 货品备案中心化分页返回结果
 * @date 2023/8/15 09:40
 */
@Data
public class CentralGoodsRecordPagingResVO implements Serializable {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("SKU")
    private String skuId;

    @ApiModelProperty("统一料号")
    private String productId;

    @ApiModelProperty("海关备案料号")
    private String customsRecordProductId;

    @ApiModelProperty("账册id")
    private Integer customsBookId;

    @ApiModelProperty("账册编号")
    private String customsBookNo;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("备案名称")
    private String goodsRecordName;

    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("审核状态编码")
    private Integer recordStatus;

    @ApiModelProperty("审核状态")
    private String recordStatusDesc;

    @ApiModelProperty("驳回理由")
    private String reason;

    @ApiModelProperty("备案时间")
    private Date createTime;

    @ApiModelProperty("是否启用（1：启用 0：禁用）")
    private Integer enable;

    @ApiModelProperty("用户id")
    private String userId;

    @ApiModelProperty("用户名称 新")
    private String userName;

    @ApiModelProperty("原产国")
    private String originCountry;

    @ApiModelProperty("原产国描述")
    private String originCountryDesc;

    @ApiModelProperty("口岸")
    private String customs;

    @ApiModelProperty("口岸描述")
    private String customsDesc;

    @ApiModelProperty("货品id")
    private String goodsCode;

    @ApiModelProperty("生产企业名称")
    private String productCompanyName;

    @ApiModelProperty("生产企业注册编号")
    private String productCompanyRegisterNumber;

    @ApiModelProperty("生产企业地址")
    private String productCompanyAddress;

    @ApiModelProperty("商品链接")
    private String productLink;

    @ApiModelProperty("附件名称")
    private String attachmentName;

    @ApiModelProperty("附件下载链接")
    private String attachmentUrl;

    @ApiModelProperty("新老备案")
    private String recordType;

    @ApiModelProperty("更新时间")
    private Date updateTime;

    @ApiModelProperty("外部料号")
    private String externalProductId;

    @ApiModelProperty("待申报口岸")
    private String waitCommitCustoms;

    @ApiModelProperty("审核中口岸")
    private String examiningCustoms;

    @ApiModelProperty("已审核口岸")
    private String examinedCustoms;

    @ApiModelProperty("来源")
    private String sourceStr;

    @ApiModelProperty("商品标记")
    private List<String> goodsRecordTagList;

    @ApiModelProperty("商品标记")
    private Integer goodsRecordTag;

    @ApiModelProperty("商品标记")
    private String goodsRecordTagDesc;

    @ApiModelProperty("备案完成时间")
    private Date recordFinishTime;

    @ApiModelProperty("驳回原因")
    private String rejectReason;

    /**
     * ERP提交审核状态
     * 1 表示保存未提交
     * 0 表示无保存 或已提交
     */
    @ApiModelProperty("erp提交状态")
    private Integer erpCommitStatus;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
