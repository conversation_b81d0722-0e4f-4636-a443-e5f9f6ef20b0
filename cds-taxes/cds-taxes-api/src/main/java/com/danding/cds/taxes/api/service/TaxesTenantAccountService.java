package com.danding.cds.taxes.api.service;

import com.danding.cds.taxes.api.dto.TaxesTenantAccountDTO;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountSearch;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountSubmit;
import com.danding.logistics.api.common.response.ListVO;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Author: Raymond
 * @Date: 2020/7/14 10:22
 * @Description:
 */
public interface TaxesTenantAccountService {
    /**
     * 根据id查询税金账户
     * @param id
     * @return
     */
    TaxesTenantAccountDTO findById(Long id);

    /**
     * 保存税金账户
     * @param submit TaxesCompanyAccountSubmit
     * @return
     */
    Long save(TaxesTenantAccountSubmit submit) ;

    /**
     * 更新税金账户
     * @param submit TaxesCompanyAccountSubmit
     * @return
     */
    Long update(TaxesTenantAccountSubmit submit);

    /**
     * 返回税金账户分页列表
     * @param search TaxesTenantAccountSearch
     * @return
     */
    ListVO<TaxesTenantAccountDTO> paging(TaxesTenantAccountSearch search);



    /**
     * 根据担保公司id和口岸编码返回有效期内的税金账户
     * @param search TaxesCompanyAccountSearch
     * @return
     */
    List<TaxesTenantAccountDTO> querylistTaxesTenantExport(TaxesTenantAccountSearch search);


    /**
     * 根据租户id查找税金账户
     * @param tenantId Long
     * @return
     */
    List<TaxesTenantAccountDTO> findByTenantId(Long tenantId);

    /**
     * 根据租户id和userId查找税金账户
     * @param userId Long
     * @param tenantId Long
     * @return
     */
    TaxesTenantAccountDTO findByUserIdAndTenantId(Long userId, String tenantId);

    /**
     * 根据租户账户id更新账户金额
     * @param id Long
     * @param available BigDecimal
     * @param rechargeTotal BigDecimal
     * @param consumedAmount BigDecimal
     * @return
     */
    Integer opsAccountBalance(Long id, BigDecimal available, BigDecimal rechargeTotal, BigDecimal consumedAmount);

    /**
     * 租户账户列表
     * @return
     */
    public List<TaxesTenantAccountDTO> taxesTenantAccountList();

    /**
     * 根据租户id获取租户name
     * @param tenantId
     * @return
     */
    String getTenantNameById(String tenantId);

    Map<String, String> getTenantNameById(List<String> tenantIdList);

    /**
     * 根据租户id查看担保企业的消费金额
     * @param tenantId 租户id
     * @param startDate 开始时间
     * @param endDate   结束时间
     * @return 数据列表
     */
    List<TaxesTenantAccountDTO> findByTenantIdAndTime(Long tenantId, Date startDate, Date endDate);
}
