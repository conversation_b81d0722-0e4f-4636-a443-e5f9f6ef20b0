package com.danding.cds.web.customs.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryReqVO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryResVO;
import com.danding.cds.web.checklist.rpc.param.RpcStringIdParam;
import com.danding.cds.web.customs.vo.CustomsHsCodeDetailVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface CustomsRpc {

    RpcResult<List<SelectOptionVO<String>>> listDistrict();

    RpcResult<List<SelectOptionVO<String>>> listCountry();

    RpcResult<List<SelectOptionVO<String>>> listCountryV2();

    RpcResult<List<SelectOptionVO<String>>> listCurrency();

    RpcResult<List<SelectOptionVO<String>>> listCurrencyV2();

    RpcResult<List<SelectOptionVO<String>>> listUom();

    RpcResult<List<SelectOptionVO<String>>> listUomWithName();

    RpcResult<List<SelectOptionVO<String>>> listUomV2();

    RpcResult<List<SelectOptionVO<String>>> listHs();

    RpcResult<List<SelectOptionVO<String>>> listHsWithName();

    RpcResult<List<SelectOptionVO<String>>> listHsV2();

    RpcResult<CustomsHsCodeDetailVO> hsTaxDetail(RpcStringIdParam id);

    RpcResult<CustomsHsCodeDetailVO> hsTaxDetailV2(RpcStringIdParam id);

    RpcResult<List<SelectOptionVO<Long>>> listEntryCompany();

    RpcResult<List<SelectOptionVO<Long>>> listEntryCompanyV2();

    RpcResult<List<SelectOptionVO<Integer>>> listEntryType();

    RpcResult<List<SelectOptionVO<Integer>>> listEntryTypeV2();

    RpcResult<List<SelectOptionVO<String>>> listCustomsDistrict();

    RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictV2();

    RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictCode();

    RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictCodeV2();

    /**
     * 海关字典数据分页
     *
     * @param search
     * @return
     */
    RpcResult<ListVO<CustomsDictionaryResVO>> paging(CustomsDictionaryReqVO search);

    /**
     * 海关字典数据新增
     *
     * @param param
     * @return
     */
    RpcResult<String> insert(CustomsDictionaryOperateParam param);

    /**
     * 海关字典数据编辑
     *
     * @param param
     * @return
     */
    RpcResult<String> update(CustomsDictionaryOperateParam param);

    RpcResult<String> delete(IdParam id);

    RpcResult<List<SelectOptionVO<String>>> listDictionaryType();

    RpcResult<List<SelectOptionVO<String>>> listDictionaryPort();

    RpcResult<List<SelectOptionVO<String>>> listDictionaryGuaranteeCustoms();

}
