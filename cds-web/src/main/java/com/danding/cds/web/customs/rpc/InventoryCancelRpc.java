package com.danding.cds.web.customs.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCancelDTO;
import com.danding.cds.customs.inventory.api.dto.InventoryCancelReport;
import com.danding.cds.customs.inventory.api.dto.InventoryCancelSearch;
import com.danding.cds.web.customs.rpc.param.RpcDeclareParam;
import com.danding.cds.web.customs.rpc.param.RpcManualOperationParam;
import com.danding.cds.web.customs.vo.InventoryCancelImportSubmit;
import com.danding.cds.web.customs.vo.InventoryCancelInfoSumVO;
import com.danding.cds.web.customs.vo.InventoryCancelResponseReport;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestParam;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

public interface InventoryCancelRpc {
     RpcResult<List<SelectOptionVO>> listStatus();
     RpcResult<ListVO<CustomsInventoryCancelDTO>> paging(InventoryCancelSearch search);
     RpcResult<InventoryCancelResponseReport> declare(RpcDeclareParam declareParam);
     RpcResult<InventoryCancelReport> importExcel(InventoryCancelImportSubmit submit);
     RpcResult<Response<Boolean>> declareInventoryCancel(RpcDeclareParam declareParam);
     RpcResult<InventoryCancelResponseReport> delete(RpcDeclareParam declareParam);
     RpcResult<InventoryCancelResponseReport> cancelInventoryCancelOrder(RpcDeclareParam declareParam);
     RpcResult<List<InventoryCancelInfoSumVO>> sumInventoryCancelInfo(Long beginTimeLong, Long endTimeLong);
     RpcResult<String> export(InventoryCancelSearch search);
     RpcResult<Response<Boolean>> manualReview( String ids);
     RpcResult manualOperation(RpcManualOperationParam param);
     RpcResult customsLogisticsStatus();
     RpcResult operateReasonEnums();
}
