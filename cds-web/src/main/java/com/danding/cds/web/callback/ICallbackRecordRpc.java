package com.danding.cds.web.callback;

import com.danding.cds.callback.api.dto.CallbackSearch;
import com.danding.cds.web.callback.vo.CallbackRecordVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface ICallbackRecordRpc {

    RpcResult<ListVO<CallbackRecordVO>> paging(CallbackSearch search);

    RpcResult<List<SelectItemVO>> listType();
}
