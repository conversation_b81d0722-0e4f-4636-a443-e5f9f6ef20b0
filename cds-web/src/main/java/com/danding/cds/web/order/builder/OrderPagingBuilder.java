package com.danding.cds.web.order.builder;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.dto.CalloffRefundGoodsInfoDTO;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.c.api.rpc.*;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.enums.DelayLevelConfigEnums;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.service.CustomsLogisticsService;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderItem;
import com.danding.cds.customs.order.api.service.CustomsOrderService;
import com.danding.cds.customs.payment.api.service.CustomsPaymentDeclareService;
import com.danding.cds.customs.payment.api.service.CustomsPaymentService;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.customs.refund.api.enums.RefundPartFlagEnum;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.dto.OrderSubmit;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.order.api.service.CustomsStatusMappingService;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.route.api.dto.RouteDTO;
import com.danding.cds.route.api.enums.RouteActionEnum;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.taxes.api.dto.TaxesTenantTaxListDTO;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.taxes.api.service.TaxesTenantTaxListService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.enums.DeclareOrderTagEnums;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.web.customsOrder.vo.CustomsOrderResult;
import com.danding.cds.web.logistics.vo.LogisticsSearchResult;
import com.danding.cds.web.order.OrderLogManager;
import com.danding.cds.web.order.vo.*;
import com.danding.cds.web.payment.vo.CustomsPaymentResult;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


@Slf4j
@Component
@RefreshScope
public class OrderPagingBuilder {
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private CustomsStatusMappingService customsStatusMappingService;
    @DubboReference
    private CustomsStatusMappingCRpc customsStatusMappingCRpc;
    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private RouteService routeService;
    @Autowired
    private OrderLogManager orderLogManager;
    @DubboReference
    private CustomsPaymentService customsPaymentService;
    @DubboReference
    private CustomsPaymentRpc customsPaymentRpc;
    @DubboReference
    private CustomsPaymentDeclareService customsPaymentDeclareService;
    @DubboReference
    private CustomsPaymentDeclareRpc customsPaymentDeclareRpc;
    @DubboReference
    private CustomsLogisticsService customsLogisticsService;
    @DubboReference
    private CustomsLogisticsRpc customsLogisticsRpc;
    @DubboReference
    private CustomsOrderService customsOrderService;
    @DubboReference
    private CustomsOrderCRpc customsOrderCRpc;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private TaxesTenantTaxListService taxListService;
    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private RefundOrderService refundOrderService;
    @DubboReference
    private RefundOrderRpc refundOrderRpc;

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfig;
    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;
    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    private Map<Long, CompanyDTO> companyDTOMap;
    private Map<Long, ExpressDTO> expressDTOMap;
    private Map<String, RouteDTO> routeDTOMap;
    private Map<String, GoodsRecordDTO> recordDTOMap;

    @Value("${msg.delayLevel.config:}")
    private String msgDelayLevelConfigJson;

//    public OrderPagingBuilder(CompanyService companyService, CustomsStatusMappingService customsStatusMappingService
//            , CustomsInventoryService customsInventoryService, ExpressService expressService, RouteService routeService
//            , OrderLogManager orderLogManager, CustomsPaymentService customsPaymentService, CustomsPaymentDeclareService customsPaymentDeclareService
//            , CustomsLogisticsService customsLogisticsService, CustomsOrderService customsOrderService, GoodsRecordService goodsRecordService
//            , TaxesTenantTaxListService taxListService, TaxesTenantAccountService taxesTenantAccountService) {
//        this.companyService = companyService;
//        this.customsStatusMappingService = customsStatusMappingService;
//        this.customsInventoryService = customsInventoryService;
//        this.expressService = expressService;
//        this.routeService = routeService;
//        this.orderLogManager = orderLogManager;
//        this.customsPaymentService = customsPaymentService;
//        this.customsPaymentDeclareService = customsPaymentDeclareService;
//        this.customsLogisticsService = customsLogisticsService;
//        this.customsOrderService = customsOrderService;
//        this.goodsRecordService = goodsRecordService;
//        this.taxListService =  taxListService;
//        this.taxesTenantAccountService = taxesTenantAccountService;
//    }

    public OrderSearchResult build(OrderDTO orderDTO) {
        List<OrderDTO> list = new ArrayList<>();
        list.add(orderDTO);
        List<OrderSearchResult> resultList = this.build(list);
        if (CollectionUtils.isEmpty(resultList)) {
            return new OrderSearchResult();
        }
        return resultList.get(0);
    }

    public List<OrderSearchResult> build(List<OrderDTO> orderDTOList) {
        List<OrderSearchResult> results = new ArrayList<>();
        try {
            for (OrderDTO orderDTO : orderDTOList) {
                // Step::基础参数准备
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "1");
                OrderSearchResult result = this.buildBase(orderDTO);
                // Step::支付单参数准备
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "2");
                this.fillPayment(orderDTO, result);
                // Step::订单参数准备
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "3");
                this.fillOrder(orderDTO, result);
                // Step::运单参数准备
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "4");
                this.fillLogistics(orderDTO, result);
                // Step::清单参数准备
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "5");
                this.fillInventory(orderDTO, result);
                //log.info("[op:OrderPaging] time watch, time={}, step={}", DateTime.now().toString("HH:mm:ss SSS"), "5");
                results.add(result);
            }
        } catch (Exception e) {
            log.warn("日志详情构建失败 error={}", e.getMessage(), e);
        } finally {
            this.resetBaseMap();
        }
        return results;
    }

    private void resetBaseMap() {
        this.companyDTOMap = null;
        this.expressDTOMap = null;
        this.routeDTOMap = null;
        this.recordDTOMap = null;
    }

    private OrderSearchResult buildBase(OrderDTO orderDTO) {
        OrderSearchResult result = new OrderSearchResult();
        try {
            BeanUtils.copyProperties(orderDTO, result);
            result.setId(orderDTO.getId().toString());
            CompanyDTO ebp = getCompany(orderDTO.getEbpId());
            result.setEbpName(ebp.getName());
            result.setStatusDesc(OrderStatus.getEnum(orderDTO.getStatus()).getDesc());
            result.setInternalStatus(OrderInternalEnum.getEnum(orderDTO.getInternalStatus()).getDesc());
            //先将异常信息置为空
            result.setExceptionDetail("");
            if (orderDTO.getExceptionFlag() && orderDTO.getExceptionType() != null && orderDTO.getExceptionType() > 0) {
                result.setExceptionDetail(orderDTO.getExceptionDetail());
                CustomsStatusMappingDTO mappingDTO;
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    mappingDTO = customsStatusMappingCRpc.findById(Long.valueOf(orderDTO.getExceptionType()));
                } else {
                    mappingDTO = customsStatusMappingService.findById(Long.valueOf(orderDTO.getExceptionType()));
                }
                result.setExceptionTypeDesc(mappingDTO.getDetail());
            }
            if (orderDTO.getFinishTime() != null) {
                result.setFinishAt(new DateTime(orderDTO.getFinishTime()).toString("yyyy/MM/dd HH:mm:ss"));
            } else {
                result.setFinishAt("");
            }
            if (orderDTO.getTenantOuterId() != null) {
                result.setTenantName(taxesTenantAccountService.getTenantNameById(orderDTO.getTenantOuterId()));
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            if (Objects.nonNull(orderExtra)) {
                OrderSubmit submit = orderExtra.getSubmit();
                if (Objects.nonNull(submit.getOrderTime())) {
                    result.setBusinessTime(new DateTime(submit.getOrderTime()).toString("yyyy/MM/dd HH:mm:ss"));
                }
                if (StringUtil.isNotBlank(submit.getErpPhyWarehouseSn())) {
                    result.setErpPhyWarehouseSn(submit.getErpPhyWarehouseSn());
                }
                if (StringUtil.isNotBlank(submit.getErpPhyWarehouseName())) {
                    result.setErpPhyWarehouseName(submit.getErpPhyWarehouseName());
                }
            }
//            Long orderTime = Optional.ofNullable(orderExtra).map(OrderExtra::getSubmit).map(OrderSubmit::getOrderTime).orElse(null);
//            if (orderTime != null) {
//                result.setBusinessTime(new DateTime(orderTime).toString("yyyy/MM/dd HH:mm:ss"));
//            }
            Integer orderTags = orderDTO.getOrderTags();
            Map<DeclareOrderTagEnums.DeclareOrderTagTypeEnum, List<String>> orderTagDescMap = DeclareOrderTagEnums.getOrderTagDescMap(orderTags);
            if (orderTagDescMap.containsKey(DeclareOrderTagEnums.DeclareOrderTagTypeEnum.GENERAL)) {
                //一般标签
                result.setOrderTagList(orderTagDescMap.get(DeclareOrderTagEnums.DeclareOrderTagTypeEnum.GENERAL));
            }
            if (orderTagDescMap.containsKey(DeclareOrderTagEnums.DeclareOrderTagTypeEnum.TO_DO)) {
                //待办标记
                result.setOrderTodoTagList(orderTagDescMap.get(DeclareOrderTagEnums.DeclareOrderTagTypeEnum.TO_DO));
            }
            result.setCreateAt(new DateTime(orderDTO.getCreateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            result.setUpdateAt(new DateTime(orderDTO.getUpdateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            if (orderCCallConfig.isOrderCCall(this.getClass())) {
                result.setIsByteDanceCloud(orderCRpc.isByteDanceDeclareWithinCloud(ebp.getCode(), orderDTO.getExtraJson()));
            } else {
                result.setIsByteDanceCloud(orderService.isByteDanceDeclareWithinCloud(ebp.getCode(), orderDTO.getExtraJson()));
            }
            //赋值延迟时间
        } catch (BeansException e) {
            log.info("[op:OrderPagingBuilder-buildBase] exception,orderSn={}, declareOrderNo={}, cause={}", orderDTO.getSn(), orderDTO.getDeclareOrderNo(), e.getMessage(), e);
        }
        return result;
    }

    private void fillInventory(OrderDTO orderDTO, OrderSearchResult result) {
        if (!orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_INVENTORY.getCode())) {
            return;
        }
        CustomsInventoryDTO customsInventoryDTO;
        List<CustomsInventoryItemDTO> itemDTOS;
        if (orderCCallConfig.isOrderCCall(this.getClass())) {
            customsInventoryDTO = customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            itemDTOS = customsInventoryRpc.listItemByIdSectionNoFilter(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        } else {
            customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            itemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
        }
        TaxesTenantTaxListDTO tenantTaxListDTO = taxListService.findByInvtNo(customsInventoryDTO.getInventoryNo());
        //查找退货信息
        RefundOrderInfoDto refundOrderInfoDto;
        if (orderCCallConfig.isOrderCCall(this.getClass())) {
            refundOrderInfoDto = refundOrderRpc.findByDeclareNo(orderDTO.getDeclareOrderNo());
        } else {
            refundOrderInfoDto = refundOrderService.findByDeclareNo(orderDTO.getDeclareOrderNo());
        }
        buildInventoryResult(orderDTO, result, customsInventoryDTO, itemDTOS, tenantTaxListDTO, refundOrderInfoDto);
    }

    public void buildInventoryResult(OrderDTO orderDTO, OrderSearchResult result, CustomsInventoryDTO customsInventoryDTO, List<CustomsInventoryItemDTO> itemDTOS,
                                     TaxesTenantTaxListDTO tenantTaxListDTO, RefundOrderInfoDto refundOrderInfoDto) {
        CustomsInventoryResult inventoryResult = new CustomsInventoryResult();
        try {
            if (customsInventoryDTO != null) {
                BeanUtils.copyProperties(customsInventoryDTO, inventoryResult);
                inventoryResult.setExitRegionStatusDesc(customsInventoryDTO.getExitRegionStatus() == 1 ? "已出区" : "未出区");
                if (!StringUtils.isEmpty(customsInventoryDTO.getCustomsStatus()) && !StringUtils.isEmpty(customsInventoryDTO.getCustomsDetail())) {
                    inventoryResult.setCustomsStatusDesc(CustomsStat.getEnum(customsInventoryDTO.getCustomsStatus()).getDesc());
                    inventoryResult.setCustomsDetail(customsInventoryDTO.getCustomsDetail());
                } else {
                    inventoryResult.setCustomsDetail("暂无");
                }
                inventoryResult.setOutOrderNo(orderDTO.getOutOrderNo());
                inventoryResult.setStatusDesc(CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc());
                result.setInventoryOrderStatus(inventoryResult.getStatusDesc());
                if (customsInventoryDTO.getLastDeclareTime() != null) {
                    inventoryResult.setDeclareTime(new DateTime(customsInventoryDTO.getLastDeclareTime()).toString("yyyy/MM/dd HH:mm:ss"));
                }
                if (!StringUtils.isEmpty(customsInventoryDTO.getCustomsStatus())) {
                    inventoryResult.setReceiveTime(new DateTime(customsInventoryDTO.getLastCustomsTime()).toString("yyyy/MM/dd HH:mm:ss"));
                }
                CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(customsInventoryDTO.getAccountBookId());
                if (Objects.nonNull(customsBookResVo)) {
                    inventoryResult.setBookNo(customsBookResVo.getBookNo());
                }
                ExpressDTO expressDTO = getExpress(customsInventoryDTO.getExpressId());
                inventoryResult.setExpressName(expressDTO.getName());
                inventoryResult.setInventoryNo(customsInventoryDTO.getInventoryNo());
                inventoryResult.setCustoms(CustomsDistrictEnum.getEnum(customsInventoryDTO.getCustoms()).getDesc());
                CompanyDTO ebcCompany = getCompany(customsInventoryDTO.getEbcId());
                inventoryResult.setEbcName(ebcCompany.getName());
                CompanyDTO ebpCompany = getCompany(customsInventoryDTO.getEbpId());
                inventoryResult.setEbpName(ebpCompany.getName());
                CompanyDTO agentCompany = getCompany(customsInventoryDTO.getAgentCompanyId());
                inventoryResult.setAgentCompanyName(agentCompany.getName());
                CompanyDTO assureCompany = getCompany(customsInventoryDTO.getAssureCompanyId());
                inventoryResult.setAssureCompanyName(assureCompany.getName());
                CompanyDTO logisticsCompany = getCompany(customsInventoryDTO.getLogisticsCompanyId());
                inventoryResult.setLogisticsCompanyName(logisticsCompany.getName());
                CompanyDTO areaCompany = getCompany(customsInventoryDTO.getAreaCompanyId());
                inventoryResult.setAreaCompanyName(areaCompany.getName());
                CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
                if (extra != null) {
                    inventoryResult.setConsigneeProvince(extra.getConsigneeProvince());
                    inventoryResult.setConsigneeCity(extra.getConsigneeCity());
                    inventoryResult.setConsigneeDistrict(extra.getConsigneeDistrict());
                    inventoryResult.setConsigneeStreet(extra.getConsigneeStreet());
                    RouteDTO routeDTO = getRoute(extra.getRouteCode());
                    if (Objects.nonNull(routeDTO)) {
                        result.setRouteName(routeDTO.getName());
                        //获取延时时间
                        if (!StringUtils.isEmpty(msgDelayLevelConfigJson)) {
                            List<MsgDelayLevelConfig> configList = JSON.parseArray(msgDelayLevelConfigJson, MsgDelayLevelConfig.class);
                            MsgDelayLevelConfig msgDelayLevelConfig = configList.stream().filter(d -> Objects.equals(d.getRouteCode(), routeDTO.getCode())).findFirst().orElse(null);
                            if (Objects.nonNull(msgDelayLevelConfig)) {
                                DelayLevelConfigEnums delayLevelConfigEnums = DelayLevelConfigEnums.getEnum(msgDelayLevelConfig.getDelayLevel());
                                if (Objects.nonNull(delayLevelConfigEnums)) {
                                    result.setDelayTime(delayLevelConfigEnums.getDesc());
                                }
                            }
                        }
                    }
                }
                List<CustomsInventoryItemResult> itemResultList = new ArrayList<>();
                BigDecimal totalPrice = BigDecimal.ZERO;
                BigDecimal taxPrice = BigDecimal.ZERO;
                Map<String, String> unitMap = new HashMap<>();
                Map<String, String> countryMap = new HashMap<>();
                for (CustomsInventoryItemDTO itemDTO : itemDTOS) {
                    CustomsInventoryItemResult itemResult = new CustomsInventoryItemResult();
                    BeanUtils.copyProperties(itemDTO, itemResult);
                    itemResult.setId(String.valueOf(itemDTO.getId()));
                    itemResult.setTotalPrice(itemResult.getUnitPrice().multiply(new BigDecimal(itemResult.getCount())));
                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                    if (itemExtra != null) {
//                        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(customsInventoryDTO.getAccountBookId(), itemExtra.getProductId());
//                        if (Objects.nonNull(goodsRecordDTO)) {
//                            //sku
//                            itemResult.setItemNo(goodsRecordDTO.getSkuId());
//                            //统一料号
//                            itemResult.setUnifiedProductId(goodsRecordDTO.getProductId());
//                        }
                        itemResult.setItemNo(itemDTO.getItemNo());
                        itemResult.setUnifiedProductId(itemExtra.getUnifiedProductId());
                        //申报单位
                        itemResult.setDeclareUnit(itemExtra.getGoodsUnit());
                        String declareUnitDesc = "";
                        if (unitMap.containsKey(itemExtra.getGoodsUnit())) {
                            declareUnitDesc = unitMap.get(itemExtra.getGoodsUnit());
                        } else {
                            CustomsDictionaryDTO declareUnit = customsDictionaryService.findByCodeAndType(itemExtra.getGoodsUnit(), DataDictionaryTypeEnums.UOM.getValue());
                            if (Objects.nonNull(declareUnit)) {
                                declareUnitDesc = declareUnit.getName();
                                unitMap.put(itemExtra.getGoodsUnit(), declareUnitDesc);
                            }
                        }
                        itemResult.setDeclareUnitDesc(declareUnitDesc);
                        itemResult.setGrossWeight(itemExtra.getGrossWeight());
                        itemResult.setNetWeight(itemExtra.getNetWeight());
                        itemResult.setTaxPrice(itemExtra.getTaxPrice());
                        itemResult.setProductId(itemExtra.getProductId());
                        itemResult.setGoodsSeqNo(itemExtra.getGoodsSeqNo());
                        itemResult.setGoodsName(itemExtra.getGoodsName());
                        if (StringUtils.isEmpty(itemResult.getGoodsName())) {
                            itemResult.setGoodsName(itemDTO.getItemName());
                        }
                        itemResult.setHsCode(itemExtra.getHsCode());
                        itemResult.setBarCode(itemExtra.getBarCode());
                        itemResult.setUnifiedProductId(itemExtra.getUnifiedProductId());
                        itemResult.setGoodsModel(itemExtra.getGoodsModel());
                        itemResult.setOriginCountry(itemExtra.getOriginCountry());
                        String originCountryDesc = "";
                        if (countryMap.containsKey(itemExtra.getOriginCountry())) {
                            originCountryDesc = countryMap.get(itemExtra.getOriginCountry());
                        } else {
                            CustomsDictionaryDTO originCountry = customsDictionaryService.findByCodeAndType(itemExtra.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
                            if (Objects.nonNull(originCountry)) {
                                originCountryDesc = originCountry.getName();
                                countryMap.put(itemExtra.getOriginCountry(), originCountryDesc);
                            }
                        }
                        itemResult.setOriginCountryDesc(originCountryDesc);
                        itemResult.setFirstUnit(itemExtra.getFirstUnit());
                        String firstUnitDesc = "";
                        if (unitMap.containsKey(itemExtra.getFirstUnit())) {
                            firstUnitDesc = unitMap.get(itemExtra.getFirstUnit());
                        } else {
                            CustomsDictionaryDTO firstUnit = customsDictionaryService.findByCodeAndType(itemExtra.getFirstUnit(), DataDictionaryTypeEnums.UOM.getValue());
                            if (Objects.nonNull(firstUnit)) {
                                firstUnitDesc = firstUnit.getName();
                                unitMap.put(itemExtra.getFirstUnit(), firstUnitDesc);
                            }
                        }
                        itemResult.setFirstUnitDesc(firstUnitDesc);
                        itemResult.setSecondUnit(itemExtra.getSecondUnit());
                        String secondUnitDesc = "";
                        if (unitMap.containsKey(itemExtra.getSecondUnit())) {
                            secondUnitDesc = unitMap.get(itemExtra.getSecondUnit());
                        } else {
                            CustomsDictionaryDTO secondUnit = customsDictionaryService.findByCodeAndType(itemExtra.getSecondUnit(), DataDictionaryTypeEnums.UOM.getValue());
                            if (Objects.nonNull(secondUnit)) {
                                secondUnitDesc = secondUnit.getName();
                                unitMap.put(itemExtra.getSecondUnit(), secondUnitDesc);
                            }
                        }
                        itemResult.setSecondUnitDesc(secondUnitDesc);
                        itemResult.setFirstUnitAmount(itemExtra.getFirstUnitAmount());
                        itemResult.setSecondUnitAmount(itemExtra.getSecondUnitAmount());
                        Integer itemTag = itemDTO.getItemTag();
                        List<String> tagsDesc = OrderItemTagEnum.getOrderItemTagsDesc(itemTag);
                        itemResult.setTagList(tagsDesc);
                        // 条码 申报数量汇总
                    }
                    itemResultList.add(itemResult);
                    totalPrice = totalPrice.add(itemResult.getTotalPrice());
                }
                taxPrice = taxPrice.add(tenantTaxListDTO == null ? BigDecimal.ZERO : tenantTaxListDTO.getAmount());
                inventoryResult.setItemList(itemResultList);
                inventoryResult.setTotalPrice(totalPrice);
                inventoryResult.setTaxPrice(taxPrice);
                inventoryResult.setTotalGrossWeight(customsInventoryDTO.getGrossWeight());
                inventoryResult.setTotalNetWeight(customsInventoryDTO.getNetWeight());
                if (tenantTaxListDTO != null) {
                    inventoryResult.setCustomsTax(tenantTaxListDTO.getCustomsTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getCustomsTax());
                    inventoryResult.setValueAddedTax(tenantTaxListDTO.getValueAddedTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getValueAddedTax());
                    inventoryResult.setConsumptionTax(tenantTaxListDTO.getConsumptionTax() == null ? BigDecimal.ZERO : tenantTaxListDTO.getConsumptionTax());
                }
                inventoryResult.setOrderStatusDesc(result.getStatusDesc());
                inventoryResult.setErpPhyWarehouseName(result.getErpPhyWarehouseName());
                inventoryResult.setFeeAmount(customsInventoryDTO.getFeeAmount());
                if (Objects.nonNull(extra) && Objects.nonNull(extra.getTaxFee())) {
                    inventoryResult.setTaxFee(extra.getTaxFee());
                }
                OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
                if (Objects.nonNull(orderExtra)) {
                    inventoryResult.setConsigneeName(orderExtra.getSubmit().getConsigneeName());
                    inventoryResult.setConsigneeTel(orderExtra.getSubmit().getConsigneeTel());
                }
                if (Objects.nonNull(customsInventoryDTO.getLastDeclareTime())) {
                    inventoryResult.setOrderDeclareTime(new DateTime(customsInventoryDTO.getLastDeclareTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                if (Objects.nonNull(orderDTO.getCreateTime())) {
                    inventoryResult.setOrderCreateTime(new DateTime(orderDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                inventoryResult.setTotalRefundTax(customsInventoryDTO.getTotalRefundTax());
                setInventoryRefundInfo(orderDTO, result, refundOrderInfoDto, inventoryResult, itemDTOS);
                List<CustomsInventoryItemDTO> fbGiftList = itemDTOS.stream().filter(item -> OrderItemTagEnum.containsFbGifts(item.getItemTag())).collect(Collectors.toList());
                inventoryResult.setContainFbGifts("否");
                if (!CollectionUtils.isEmpty(fbGiftList)) {
                    inventoryResult.setContainFbGifts("是");
                    if (StringUtils.isEmpty(customsInventoryDTO.getNote())) {
                        StringBuilder sb = new StringBuilder();
                        sb.append(customsInventoryDTO.getNote());
                        Map<String, CustomsInventoryItemDTO> filter = fbGiftList.stream()
                                .collect(Collectors.toMap(i -> i.getItemName() + "_" + i.getItemNo(), Function.identity(), (v1, v2) -> {
                                    v1.setCount(v1.getCount() + v2.getCount());
                                    return v1;
                                }));
                        filter.values().forEach(item -> sb.append(item.getItemName()).append(",").append(item.getCount()).append("件;"));
                        inventoryResult.setNote(sb.toString());
                    }
                }
            }
        } catch (Exception e) {
            log.info("[op:OrderPagingBuilder-fillInventory] exception,orderSn={}, declareOrderNo={}, cause={}", orderDTO.getSn(), orderDTO.getDeclareOrderNo(), e.getMessage(), e);
        }
        result.setCustomsInventory(inventoryResult);
    }

    private void setInventoryRefundInfo(OrderDTO orderDTO, OrderSearchResult result, RefundOrderInfoDto refundOrderInfoDto, CustomsInventoryResult inventoryResult, List<CustomsInventoryItemDTO> itemDTOList) {
        // 申报单详情退货商品信息
        CustomsInventoryCalloffDTO calloffDTO = customsInventoryCalloffCRpc.findByOrderId(orderDTO.getId());
        if (Objects.nonNull(calloffDTO)) {
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(calloffDTO.getOrderTag());
            List<Integer> list = Arrays.asList(InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE.getCode(), InventoryCalloffOrderTagEnums.JD_REFUND.getCode());
            if (!CollUtil.containsAny(orderTags, list)) {
                return;
            }
            Boolean isRefundWarehouse = InventoryCalloffOrderTagEnums.contains(calloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE);
            List<String> orderTagsDesc = InventoryCalloffOrderTagEnums.getOrderTagsDesc(calloffDTO.getOrderTag());
            inventoryResult.setCalloffOrderTagDescList(orderTagsDesc);
            inventoryResult.setRefundMailNo(calloffDTO.getRefundLogisticsNo());
            inventoryResult.setRefundCrossCustomsFlagDesc("否");
            inventoryResult.setRefundPartFlagDesc(Objects.equals(calloffDTO.getPartRefundFlag(), 1) ? "是" : "否");
            // 获取正向实体仓编码在【实体仓配置】中最新1条的口岸
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByErpCode(result.getErpPhyWarehouseSn());
            if (CollUtil.isNotEmpty(entityWarehouseDTOList)) {
                entityWarehouseDTOList.stream()
                        .filter(e -> Objects.equals(e.getEnable(), 1))
                        .max(Comparator.comparing(EntityWarehouseDTO::getCreateTime))
                        .ifPresent(entityWarehouseDTO -> inventoryResult.setRefundCustomCodeDesc(entityWarehouseDTO.getCustoms()));
            }
            Map<String, Integer> itemCountMap = new HashMap<>();
            if (isRefundWarehouse) {
                // 根据条码汇总申报数量
                itemDTOList.forEach(item -> {
                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(item.getExtraJson(), CustomsInventoryItemExtra.class);
                    itemCountMap.merge(itemExtra.getBarCode(), item.getCount(), Integer::sum);
                });
            }
            if (StrUtil.isNotBlank(calloffDTO.getRefundGoodsInfoJson())) {
                List<CalloffRefundGoodsInfoDTO> calloffRefundGoodsInfoDTOS = JSON.parseArray(calloffDTO.getRefundGoodsInfoJson(), CalloffRefundGoodsInfoDTO.class);
                List<CustomsInventoryResult.RefundGoodsInfo> refundGoodsInfoList = new ArrayList<>();
                for (CalloffRefundGoodsInfoDTO calloffRefundGoodsInfoDTO : calloffRefundGoodsInfoDTOS) {
                    if (Objects.equals(calloffRefundGoodsInfoDTO.getTradeType(), 2)) {
                        // 完税品不展示
                        continue;
                    }
                    CustomsInventoryResult.RefundGoodsInfo refundGoodsInfo = new CustomsInventoryResult.RefundGoodsInfo();
                    refundGoodsInfo.setId(calloffRefundGoodsInfoDTO.getId());
                    refundGoodsInfo.setSku(calloffRefundGoodsInfoDTO.getSku());
                    refundGoodsInfo.setBarCode(calloffRefundGoodsInfoDTO.getBarCode());
                    refundGoodsInfo.setProductId(calloffRefundGoodsInfoDTO.getCustomsProductId());
                    refundGoodsInfo.setGoodsName(calloffRefundGoodsInfoDTO.getGoodsName());
                    refundGoodsInfo.setRefundCount(calloffRefundGoodsInfoDTO.getRefundCount());
                    if (isRefundWarehouse) {
                        if (itemCountMap.containsKey(calloffRefundGoodsInfoDTO.getBarCode())) {
                            refundGoodsInfo.setDeclareCount(itemCountMap.get(calloffRefundGoodsInfoDTO.getBarCode()).toString());
                            if (calloffRefundGoodsInfoDTO.getRefundCount() > itemCountMap.get(calloffRefundGoodsInfoDTO.getBarCode())) {
                                refundGoodsInfo.setErrorMsg("退货数大于申报数！");
                            }
                        } else {
                            refundGoodsInfo.setDeclareCount("-");
                            refundGoodsInfo.setErrorMsg("查询清单条码失败！");
                        }
                    }
                    refundGoodsInfoList.add(refundGoodsInfo);
                }
                inventoryResult.setRefundGoodsInfo(refundGoodsInfoList);
            }
        }
        if (Objects.nonNull(refundOrderInfoDto)) {
            inventoryResult.setRefundMailNo(refundOrderInfoDto.getRefundMailNo());
            RefundPartFlagEnum refundPartFlagEnum = RefundPartFlagEnum.getEnum(refundOrderInfoDto.getRefundPartFlag());
            if (Objects.nonNull(refundPartFlagEnum)) {
                inventoryResult.setRefundPartFlagDesc(refundPartFlagEnum.getDesc());
            }
            if (!StringUtils.isEmpty(refundOrderInfoDto.getCustomsCode())) {
                CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(refundOrderInfoDto.getCustomsCode());
                if (Objects.nonNull(customsDistrictEnum)) {
                    inventoryResult.setRefundCustomCodeDesc(customsDistrictEnum.getDesc());
                }
            }
            inventoryResult.setRefundCrossCustomsFlagDesc(
                    Objects.equals(refundOrderInfoDto.getRefundCrossCustomsFlag(), 1) ? "是" : "否");
        }
    }

    private void fillPayment(OrderDTO orderDTO, OrderSearchResult result) {
        if (!orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_PAYMENT.getCode())) {
            return;
        }
        //支付申报单
        CustomsPaymentDTO customsPayment;
        if (orderCCallConfig.isOrderCCall(this.getClass())) {
            customsPayment = customsPaymentRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
        } else {
            customsPayment = customsPaymentService.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
        }
        CustomsPaymentResult customsPaymentResult = new CustomsPaymentResult();
        try {
            if (customsPayment != null) {
                customsPaymentResult.setBuyerIdNo(customsPayment.getBuyerIdNo());
                customsPaymentResult.setBuyerIdType(customsPayment.getBuyerIdType());
                customsPaymentResult.setBuyerName(customsPayment.getBuyerName());
                customsPaymentResult.setCurrencyCode(customsPayment.getCurrencyCode());
                CompanyDTO ebp = companyService.findUnifiedCrossInfoById(customsPayment.getEbpId());
                customsPaymentResult.setEbp(ebp.getCode());
                customsPaymentResult.setOutOrderNo(customsPayment.getOutOrderNo());
                CompanyDTO payCompany = companyService.findUnifiedCrossInfoById(customsPayment.getEbpId());
                customsPaymentResult.setPayCompany(payCompany.getCode());
                customsPaymentResult.setPayTime(customsPayment.getPayTime());
                customsPaymentResult.setTradePayNo(customsPayment.getTradePayNo());
                customsPaymentResult.setId(customsPayment.getId() + "");
                customsPaymentResult.setStatus(customsPayment.getStatus());
                List<CustomsPaymentReturnMsgResult> returnMsgResultList = new ArrayList<CustomsPaymentReturnMsgResult>();
                List<CustomsPaymentDeclareDTO> customsPaymentDeclareList;
                if (orderCCallConfig.isOrderCCall(this.getClass())) {
                    customsPaymentDeclareList = customsPaymentDeclareRpc.findByCustomsPaymentCode(customsPayment.getSn());
                } else {
                    customsPaymentDeclareList = customsPaymentDeclareService.findByCustomsPaymentCode(customsPayment.getSn());
                }
                for (CustomsPaymentDeclareDTO dto : customsPaymentDeclareList) {
                    CustomsPaymentReturnMsgResult customsPaymentReturnMsgResult = new CustomsPaymentReturnMsgResult();
                    customsPaymentReturnMsgResult.setCreateTime(dto.getCreateTime());
                    customsPaymentReturnMsgResult.setReturnMsg(dto.getReturnMsg());
                    customsPaymentReturnMsgResult.setReturnTime(dto.getReturnTime());
                    customsPaymentReturnMsgResult.setStatus(dto.getStatus());
                    returnMsgResultList.add(customsPaymentReturnMsgResult);
                }
                customsPaymentResult.setCustomsPaymentReturnMsgResultList(returnMsgResultList);
                //List<CustomsPaymentLogResult> paymentlist =  orderLogManager.builderPayment(customsPayment);
                //customsPaymentResult.setLogList(paymentlist);
            }

        } catch (Exception e) {
            log.info("[op:OrderPagingBuilder-fillPayment] exception,orderSn={}, declareOrderNo={}, cause={}", orderDTO.getSn(), orderDTO.getDeclareOrderNo(), e.getMessage(), e);
        }
        result.setPayment(customsPaymentResult);
    }

    private void fillLogistics(OrderDTO orderDTO, OrderSearchResult result) {
        if (!orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_LOGISTICS.getCode())) {
            return;
        }
        CustomsLogisticsDTO customsLogisticsDTO;
        if (orderCCallConfig.isOrderCCall(this.getClass())) {
            customsLogisticsDTO = customsLogisticsRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
        } else {
            customsLogisticsDTO = customsLogisticsService.findByOrder(orderDTO.getId(), orderDTO.getCustomsLogisticsSn());
        }
        LogisticsSearchResult customsLogisticsResult = new LogisticsSearchResult();
        try {
            if (customsLogisticsDTO != null) {
                customsLogisticsResult.setLogisticsNo(customsLogisticsDTO.getLogisticsNo());
                customsLogisticsResult.setStatus(customsLogisticsDTO.getStatus());
                customsLogisticsResult.setStatusDesc(CustomsActionStatus.getEnum(customsLogisticsDTO.getStatus()).getDesc());
                customsLogisticsResult.setLastDeclareAt(new DateTime(customsLogisticsDTO.getLastDeclareTime()).toString("yyyy/MM/dd HH:mm:ss"));
                customsLogisticsResult.setLastCustomsAt(new DateTime(customsLogisticsDTO.getLastCustomsTime()).toString("yyyy/MM/dd HH:mm:ss"));
                customsLogisticsResult.setCustomsDesc(CustomsStat.getEnum(customsLogisticsDTO.getCustomsStatus()).getDesc());
                customsLogisticsResult.setCustomsDetail(customsLogisticsDTO.getCustomsDetail());
            }
        } catch (Exception e) {
            log.info("[op:OrderPagingBuilder-fillLogistics] exception,orderSn={}, declareOrderNo={}, cause={}", orderDTO.getSn(), orderDTO.getDeclareOrderNo(), e.getMessage(), e);
        }
        result.setLogistics(customsLogisticsResult);
    }

    private void fillOrder(OrderDTO orderDTO, OrderSearchResult result) {
        if (!orderDTO.getActionJson().contains(RouteActionEnum.DECLARE_ORDER.getCode())) {
            return;
        }
        CustomsOrderDTO customsOrderDTO;
        if (orderCCallConfig.isOrderCCall(this.getClass())) {
            customsOrderDTO = customsOrderCRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        } else {
            customsOrderDTO = customsOrderService.findByOrder(orderDTO.getId(), orderDTO.getCustomsOrderSn());
        }
        buildCustomsOrderResult(orderDTO, result, customsOrderDTO);
    }

    public void buildCustomsOrderResult(OrderDTO orderDTO, OrderSearchResult result, CustomsOrderDTO customsOrderDTO) {
        CustomsOrderResult customsOrderResult = new CustomsOrderResult();
        try {
            if (customsOrderDTO != null) {
                customsOrderResult.setStatus(CustomsActionStatus.getEnum(customsOrderDTO.getStatus()).getValue());
                customsOrderResult.setId(String.valueOf(customsOrderDTO.getId()));
                customsOrderResult.setStatusDesc(CustomsActionStatus.getEnum(customsOrderDTO.getStatus()).getDesc());
                customsOrderResult.setCustomsStatusDesc(CustomsStat.getEnum(customsOrderDTO.getCustomsStatus()).getDesc());
                customsOrderResult.setCustomsDetail(customsOrderDTO.getCustomsDetail());
                if (customsOrderDTO.getLastDeclareTime() != null) {
                    customsOrderResult.setDeclareTime(new DateTime(customsOrderDTO.getLastDeclareTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                if (customsOrderDTO.getLastCustomsTime() != null) {
                    customsOrderResult.setReceiveTime(new DateTime(customsOrderDTO.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                CompanyDTO agentCompany = getCompany(customsOrderDTO.getAgentCompanyId());
                customsOrderResult.setAgentCompanyName(agentCompany.getName());
                CompanyDTO ebcCompany = getCompany(customsOrderDTO.getEbcId());
                customsOrderResult.setEbcName(ebcCompany.getName());
                customsOrderResult.setCustoms(CustomsDistrictEnum.getEnum(customsOrderDTO.getCustoms()).getDesc());
                List<CustomsOrderItemResult> itemResults = new ArrayList<>();
                List<CustomsOrderItem> itemList = JSON.parseArray(customsOrderDTO.getItemJson(), CustomsOrderItem.class);
                BigDecimal goodsTotal = BigDecimal.ZERO;
                int idx = 1;
                for (CustomsOrderItem orderItem : itemList) {
                    CustomsOrderItemResult itemResult = new CustomsOrderItemResult();
                    itemResult.setRecordNo(orderItem.getRecordNo());
                    itemResult.setRecordGnum(orderItem.getGoodsSeqNo());
                    itemResult.setHsCode(orderItem.getHsCode());
                    itemResult.setOriginCountry(orderItem.getOriginCountry());
                    CustomsDictionaryDTO originCountry = customsDictionaryService.findByCodeAndType(orderItem.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
                    if (Objects.nonNull(originCountry)) {
                        itemResult.setOriginCountryDesc(originCountry.getName());
                    }
                    itemResult.setUnitPrice(orderItem.getUnitPrice());
                    itemResult.setGoodsCount(orderItem.getGoodsCount());
                    itemResult.setGoodsAmount(orderItem.getUnitPrice().multiply(new BigDecimal(orderItem.getGoodsCount())));
                    itemResult.setDeclareUnit(orderItem.getGoodsUnit());
                    CustomsDictionaryDTO declareUnit = customsDictionaryService.findByCodeAndType(orderItem.getGoodsUnit(), DataDictionaryTypeEnums.UOM.getValue());
                    if (Objects.nonNull(declareUnit)) {
                        itemResult.setDeclareUnitDesc(declareUnit.getName());
                    }
                    if (!LongUtil.isNone(orderItem.getBookId()) && !StringUtils.isEmpty(orderItem.getRecordNo())) {
                        GoodsRecordDTO goodsRecordDTO = getRecord(orderItem.getBookId(), orderItem.getRecordNo());
                        if (goodsRecordDTO != null) {
                            itemResult.setGoodsNo(goodsRecordDTO.getSkuId());
                            itemResult.setUnifiedProductId(goodsRecordDTO.getProductId());
                            itemResult.setName(goodsRecordDTO.getGoodsRecordName());
                            itemResult.setBarcode(goodsRecordDTO.getBarCode());
                        }
                    }
                    itemResult.setSort(idx++);
                    itemResults.add(itemResult);
                    goodsTotal = goodsTotal.add(orderItem.getUnitPrice().multiply(new BigDecimal(orderItem.getGoodsCount())));
                }

                customsOrderResult.setItemList(itemResults);
                customsOrderResult.setTotalAmount(goodsTotal.add(customsOrderDTO.getFreight()));
                customsOrderResult.setPaidAmount(goodsTotal.add(customsOrderDTO.getFreight()).add(customsOrderDTO.getTax()).subtract(customsOrderDTO.getDiscount()));
                customsOrderResult.setFreight(customsOrderDTO.getFreight());
                customsOrderResult.setTax(customsOrderDTO.getTax());
                customsOrderResult.setDiscount(customsOrderDTO.getDiscount());
                customsOrderResult.setBuyerIdNumber(customsOrderDTO.getBuyerIdNumber());
                customsOrderResult.setBuyerName(customsOrderDTO.getBuyerName());
                customsOrderResult.setBuyerTelNumber(customsOrderDTO.getBuyerTelNumber());
                CustomsOrderExtra extra = JSON.parseObject(customsOrderDTO.getExtraJson(), CustomsOrderExtra.class);
                customsOrderResult.setConsigneeProvince(extra.getConsigneeProvince());
                customsOrderResult.setConsigneeCity(extra.getConsigneeCity());
                customsOrderResult.setConsigneeDistrict(extra.getConsigneeDistrict());
                customsOrderResult.setConsigneeStreet(extra.getConsigneeStreet());
                customsOrderResult.setConsigneeAddress(customsOrderDTO.getConsigneeAddress());
                CompanyDTO logisticsCompany = getCompany(customsOrderDTO.getLogisticsCompanyId());
                customsOrderResult.setLogisticsCompanyName(logisticsCompany.getName());
                CompanyDTO payCompany = getCompany(customsOrderDTO.getPayCompanyId());
                customsOrderResult.setPayCompanyName(payCompany.getName());
                customsOrderResult.setDeclarePayNo(customsOrderDTO.getDeclarePayNo());
                customsOrderResult.setTradePayNo(customsOrderDTO.getTradePayNo());
                customsOrderResult.setTradeAt(new DateTime(customsOrderDTO.getTradeTime()).toString("yyyy-MM-dd HH:mm:ss"));
                customsOrderResult.setSenderName(customsOrderDTO.getSenderName());
                customsOrderResult.setConsigneeName(customsOrderDTO.getConsigneeName());
                customsOrderResult.setConsigneeTel(customsOrderDTO.getConsigneeTel());
                customsOrderResult.setConsigneeEmail(customsOrderDTO.getConsigneeEmail());
                customsOrderResult.setNote(customsOrderDTO.getNote());
                //List<CustomsOrderLogResult> logResultList =  orderLogManager.builderOrder(customsOrderDTO);
                //customsOrderResult.setLogList(logResultList);
            }
        } catch (Exception e) {
            log.info("[op:OrderPagingBuilder-fillOrder] exception,orderSn={}, declareOrderNo={}, cause={}", orderDTO.getSn(), orderDTO.getDeclareOrderNo(), e.getMessage(), e);
        }
        result.setOrder(customsOrderResult);
    }

    private CompanyDTO getCompany(Long id) {
        if (companyDTOMap == null) {
            companyDTOMap = new HashMap<>();
        }
        CompanyDTO companyDTO = companyDTOMap.get(id);
        if (companyDTO == null) {
            companyDTO = companyService.findUnifiedCrossInfoById(id);
            companyDTOMap.put(companyDTO.getId(), companyDTO);
        }
        return companyDTO;
    }

    private RouteDTO getRoute(String code) {
        if (routeDTOMap == null) {
            routeDTOMap = new HashMap<>();
        }
        RouteDTO routeDTO = routeDTOMap.get(code);
        if (routeDTO == null) {
            routeDTO = routeService.findByCode(code);
            routeDTOMap.put(routeDTO.getCode(), routeDTO);
        }
        return routeDTO;
    }

    private ExpressDTO getExpress(Long id) {
        if (expressDTOMap == null) {
            expressDTOMap = new HashMap<>();
        }
        ExpressDTO expressDTO = expressDTOMap.get(id);
        if (expressDTO == null) {
            expressDTO = expressService.findById(id);
            expressDTOMap.put(expressDTO.getId(), expressDTO);
        }
        return expressDTO;
    }

    private GoodsRecordDTO getRecord(Long bookId, String recordNo) {
        if (recordDTOMap == null) {
            recordDTOMap = new HashMap<>();
        }
        GoodsRecordDTO recordDTO = recordDTOMap.get(bookId + "#" + recordNo);
        if (recordDTO == null) {
            recordDTO = goodsRecordService.findByBookIdAndProId(bookId, recordNo);
            recordDTOMap.put(bookId + "#" + recordNo, recordDTO);
        }
        return recordDTO;
    }
}
