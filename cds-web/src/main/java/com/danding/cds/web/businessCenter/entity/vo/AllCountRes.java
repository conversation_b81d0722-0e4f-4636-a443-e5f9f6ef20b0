package com.danding.cds.web.businessCenter.entity.vo;

import com.danding.cds.customs.inventory.api.dto.CalloffCountDTO;
import com.danding.cds.v2.bean.dto.GoodsRecordBusinessCount;
import lombok.Data;

/**
 * <AUTHOR>
 * @description 个人工作台统计
 * @date 2022/8/29
 */
@Data
public class AllCountRes {

    /**
     * 备案
     */
    private GoodsRecordBusinessCount goodsRecords;

    /**
     * 保税售后
     */
    private CalloffCountDTO postSale;

    /**
     * 一线入境
     */
    private InvBusinessCountResVO onelineInCount;

    /**
     * 区间流转入
     */
    private InvBusinessCountResVO sectionIn;

    /**
     * 区内流转入
     */
    private InvBusinessCountResVO sectionInnerIn;

    /**
     * 区间流转出
     */
    private InvBusinessCountResVO sectionOut;

    /**
     * 区内流转（出）
     */
    private InvBusinessCountResVO sectionInnerOut;

    /**
     * 退货入区
     */
    private InvBusinessCountResVO refundInArea;

    /**
     * 销毁
     */
    private InvBusinessCountResVO destroy;


    /**
     * 一线退运
     */
    private InvBusinessCountResVO onelineRefund;

    /**
     * 保税物流转大贸
     */
    private InvBusinessCountResVO bondedToTrade;

    /**
     * 后续补税
     */
    private InvBusinessCountResVO subsequentTax;
}
