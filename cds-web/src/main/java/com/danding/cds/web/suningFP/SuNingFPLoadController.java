package com.danding.cds.web.suningFP;

import java.math.BigDecimal;

import com.danding.cds.suningFP.api.dto.SuNingFPLoadInfoDTO;
import com.danding.cds.suningFP.api.service.SuNingFPLoadService;
import com.google.common.collect.Lists;

import com.alibaba.fastjson.JSON;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadInfoItemSubmit;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadInfoSubmit;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;


@Slf4j
@Api(tags = "苏宁FP装载单信息")
@RequestMapping("/suNingFPLoad")
@RestController
public class SuNingFPLoadController {

    @DubboReference
    private SuNingFPLoadService suNingFPLoadService;

    @ApiOperation(value = "接收装载单信息")
    @PostMapping("/receiveLoad")
    public String receiveLoad(HttpServletRequest request, String logistic_provider_id, String data_digest, String msg_type, String logistics_interface) throws Exception {
        log.info("[op:receiveLoad] suNingFPLoadRequest={}", JSON.toJSONString(logistics_interface));
        SuNingFPLoadInfo loadInfo = XMLUtil.converyToJavaBean(logistics_interface, SuNingFPLoadInfo.class);
        try {
            SuNingFPLoadInfoDTO loadInfoDTO = suNingFPLoadService.findByLoadOrderNo(loadInfo.getLoadId());
            SuNingFPResponse response = new SuNingFPResponse();
            response.setLoadId(loadInfo.getLoadId());
            response.setLoadHeadId(loadInfo.getLoadHeadId());
            response.setReturnMessage("接收成功");
            if (loadInfoDTO != null) {
                return XMLUtil.convertToXmlWithOutXmlHead(response);
            }
            SuNingFPLoadInfoSubmit submit = new SuNingFPLoadInfoSubmit();
            submit.setSerialNo(loadInfo.getLoadHeadId());
            submit.setLoadOrderNo(loadInfo.getLoadId());
            double total = Double.parseDouble(loadInfo.getTotal());
            submit.setTotalCount((int) total);
            submit.setTotalWeight(loadInfo.getTotalWeight());
            submit.setTotalToryNum(Integer.valueOf(loadInfo.getTracyNum()));
            submit.setLicensePlate(loadInfo.getCarEcNo());
            List<SuNingFPLoadContent> loadContents = loadInfo.getLoadContents().getLoadContents();
            List<SuNingFPLoadInfoItemSubmit> infoItemSubmitList = new ArrayList<>();
            for (SuNingFPLoadContent loadContent : loadContents) {
                SuNingFPLoadInfoItemSubmit infoItemSubmit = new SuNingFPLoadInfoItemSubmit();
                infoItemSubmit.setDeclareOrderNo(loadContent.getOutorderId());
                infoItemSubmitList.add(infoItemSubmit);
            }
            submit.setInfoItemSubmitList(infoItemSubmitList);
            suNingFPLoadService.submit(submit);
            return XMLUtil.convertToXmlWithOutXmlHead(response);
        } catch (Exception e) {
            log.error("[op:receiveLoadInfo] receiveLoadInfo fail. exception={}", e.getMessage(), e);
            SuNingFPResponse response = new SuNingFPResponse();
            response.setLoadId(loadInfo.getLoadId());
            response.setLoadHeadId(loadInfo.getLoadHeadId());
            response.setReturnMessage("接收失败");
            return XMLUtil.convertToXmlWithOutXmlHead(response);
        }
    }
}
