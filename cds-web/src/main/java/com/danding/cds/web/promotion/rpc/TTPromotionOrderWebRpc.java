package com.danding.cds.web.promotion.rpc;

import java.util.List;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderPageVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderSearch;
import com.danding.cds.v2.bean.vo.req.TTPromotionAuditReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 */
public interface TTPromotionOrderWebRpc {


    RpcResult<ListVO<TTPromotionOrderPageVO>> paging(TTPromotionOrderSearch search);


    /**
     * @param reqVo
     * @return
     */
    RpcResult<String> audit(TTPromotionAuditReqVo reqVo);

    RpcResult detail(IdParam id);


    RpcResult<String> exportItemExcel(TTPromotionOrderSearch search);


    RpcResult<List<SelectOptionVO<String>>> activityTypeList();


    RpcResult<List<SelectOptionVO<String>>> marketingTypeList();

    RpcResult<List<SelectOptionVO<String>>> sellerNickList();

    RpcResult<List<SelectOptionVO<String>>> orderSourceList();
}
