package com.danding.cds.web.message.job;

import com.alibaba.fastjson.JSON;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.enums.MessageStatus;
import com.danding.cds.message.api.service.MessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class MessageTaskGenerateJob extends IJobHandler {

    @DubboReference
    private MessageService messageService;

    private boolean lock = false;

    @Override
    @XxlJob(value = "MessageTaskGenerateJob", enableTenant = true)
    public ReturnT<String> execute(String s) throws Exception {
        if (lock) {
            XxlJobLogger.log("thread is lock");
            return ReturnT.SUCCESS;
        }
        lock = true;
        try {
            List<MessageDTO> messageDTOList = messageService.listByStatus(MessageStatus.INIT);
            for (MessageDTO messageDTO : messageDTOList) {
                log.info("消息任务生成：{}", JSON.toJSONString(messageDTO));
                try {
                    messageService.generateTask(messageDTO);
                } catch (Exception ex) {
                    XxlJobLogger.log("消息任务生成exception, 业务编码: {} ,cause={}", messageDTO.getBusinessCode(), ex.getMessage(), ex);
                    log.error("消息任务生成exception, 业务编码: {} ,cause={}", messageDTO.getBusinessCode(), ex.getMessage(), ex);
                }
            }
        } catch (Exception e) {
            XxlJobLogger.log("消息任务生成exception, cause={}", e.getMessage(), e);
            log.error("消息任务生成exception, cause={}", e.getMessage(), e);
        } finally {
            lock = false;
        }
        return ReturnT.SUCCESS;
    }
}
