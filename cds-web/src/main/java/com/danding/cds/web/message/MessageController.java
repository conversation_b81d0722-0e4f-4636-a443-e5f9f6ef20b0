package com.danding.cds.web.message;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.c.api.rpc.RefundOrderRpc;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.PagingBuilder;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.payment.api.service.CustomsPaymentService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.http.saas.annotation.TenantHttpMethod;
import com.danding.cds.http.saas.enums.TenantHttpType;
import com.danding.cds.log.api.dto.TrackLogDTO;
import com.danding.cds.log.api.service.TrackLogService;
import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.dto.MessageSearch;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.enums.MessageStatus;
import com.danding.cds.message.api.enums.MessageTaskStatus;
import com.danding.cds.message.api.process.*;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.OrderCallbackSubmit;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.TaxCallbackSubmit;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelDTO;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.web.callback.ListOrderCallback;
import com.danding.cds.web.message.vo.MercuryPayCustomsDeclareNotifyResult;
import com.danding.cds.web.message.vo.MessageSearchVO;
import com.danding.cds.web.taxes.vo.TaxResult;
import com.danding.cds.web.v2.saas.DeclareWebEsQuerier;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.github.kevinsawicki.http.HttpRequest;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.*;

import java.util.*;

@Slf4j
@Api(tags = "消息管理")
@RequestMapping("/message")
@RestController
@RefreshScope
public class MessageController {

    @DubboReference
    private MessageService messageService;

    @DubboReference
    private OrderService orderService;

    @DubboReference
    private CustomsPaymentService customsPaymentService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private PayMerchantAccountChannelService channelService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private RefundOrderRpc refundOrderRpc;

    @Value("${pangu.host}")
    private String panguHost;

    @Value("${outOrder.host}")
    private String OUT_ORDER_HOST;

    @Value("${websc.host:}")
    private String WEB_SC_HOST;

    @ApiOperation(value = "生成任务")
    @GetMapping("/generateTask")
    public String generateTask() {
        List<MessageDTO> messageDTOList = messageService.listByStatus(MessageStatus.INIT);
        for (MessageDTO messageDTO : messageDTOList) {
            messageService.generateTask(messageDTO);
        }
        return "SUCCESS";
    }

    @ApiOperation(value = "单个生成任务")
    @GetMapping("/generateTask/single")
    public String generateTaskSingle(@RequestParam String messageId) {

        MessageDTO messageDTO = messageService.findById(Long.valueOf(messageId));
        messageService.generateTask(messageDTO);
        return "SUCCESS";
    }

    @ApiOperation(value = "执行任务")
    @GetMapping("/executeTask")
    public String executeTask() {
        List<MessageTaskDTO> taskDTOList = messageService.listTaskByStatusAndTime(MessageTaskStatus.INIT, DateTime.now().minusMinutes(10).getMillis(), DateTime.now().getMillis());
        for (MessageTaskDTO messageTaskDTO : taskDTOList) {
            messageService.executeTask(messageTaskDTO);
        }
        return "SUCCESS";
    }

    @PostMapping("/proxyPangu")
    @TenantHttpMethod(type = TenantHttpType.DEFAULT)
    public String proxyPangu(@RequestBody OrderCustomsInventoryMessage message) {
        ListOrderCallback callback = new ListOrderCallback();
        callback.setMerchantOrderNo(message.getDeclareOrderNo());
        callback.setStatus(message.getCustomsStatus());
        callback.setDescription(message.getCustomsDetail());
        callback.setMessage(message.getCustomsDetail());
        callback.setDatetime(new DateTime(message.getTime()).toString("yyyy-MM-dd HH:mm:ss"));
        callback.setType("customs");
        Map<String, String> extra = new HashMap<>();
        extra.put("inventoryNo", message.getInventoryNo());
        callback.setExtraInfo(JSON.toJSONString(extra));
        HttpRequest httpRequest = HttpRequest.post(panguHost + "/xhr/customs/list/declare/notify/receive")
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", "application/json;charset=utf-8")
                .send(JSON.toJSONString(callback));
        if (httpRequest.ok()) {
            return "SUCCESS";
        } else {
            return "网络异常";
        }
    }

    @PostMapping("/proxyPanguDeliver")
    @TenantHttpMethod(type = TenantHttpType.DEFAULT)
    public String proxyPanguDeliver(@RequestBody OrderDeliverMessage message) {
//        CompanyDTO ebp = companyService.findByCode(message.getEbpCode());
        // 这里应该是c单业务 先查跨境进口统一编码
        CompanyDTO ebp = companyService.findByUnifiedCrossBroderCode(message.getEbpCode());
        OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(ebp.getId(), message.getDeclareOrderNo(), 0L);
        CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
        ExpressDTO expressDTO = expressService.findById(inventoryDTO.getExpressId());
        JSONObject bizMap = new JSONObject();
        bizMap.put("deliverTime", message.getShipmentTime());
        bizMap.put("shipperSn", "货主编码");
        bizMap.put("shippingCode", message.getLogisticsNo());
        bizMap.put("customsSerialNo", "");
        bizMap.put("orderSn", message.getDeclareOrderNo());
        bizMap.put("warehouseSn", "仓库编码");
        bizMap.put("depotDeliverOrderNo", message.getDepotOrderNo());
        bizMap.put("businessValue", orderDTO.getSystemGlobalSn());
        bizMap.put("expressCode", expressDTO.getCode());
        bizMap.put("weight", message.getWeight());
        bizMap.put("itemList", new ArrayList<>());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("method", "order.deliver.notify");
        resultMap.put("bizData", bizMap);
        HttpRequest httpRequest = HttpRequest.post(panguHost + "/xhr/depot/callback/receive")
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .form(resultMap);
        if (httpRequest.ok()) {
            return "SUCCESS";
        } else {
            return "网络异常";
        }
    }

    @PostMapping("/proxyLogisticsDeliver")
    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareWebEsQuerier.class)
    public String proxyLogisticsDeliver(@RequestBody OrderDeliverMessage message) {
        try {
            orderService.syncDeliver(message);
            return "SUCCESS";
        } catch (Exception e) {
            log.error("处理异常：{}", e.getMessage(), e);
            return "系统异常";
        }
    }

    @PostMapping("/proxyJinDong")
    public String proxyJinDong(@RequestBody OrderCustomsInventoryMessage message) {
        ListOrderCallback callback = new ListOrderCallback();
        callback.setMerchantOrderNo(message.getDeclareOrderNo());
        callback.setStatus(message.getCustomsStatus());
        callback.setDescription(message.getCustomsDetail());
        callback.setMessage(message.getCustomsDetail());
        callback.setDatetime(new DateTime(message.getTime()).toString("yyyy-MM-dd HH:mm:ss"));
        callback.setType("customs");
        Map<String, String> extra = new HashMap<>();
        extra.put("inventoryNo", message.getInventoryNo());
        callback.setExtraInfo(JSON.toJSONString(extra));
        HttpRequest httpRequest = HttpRequest.post(OUT_ORDER_HOST + "/open/cdsCallback")
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", "application/json;charset=utf-8")
                .send(JSON.toJSONString(callback));
        log.info("[op:proxyJinDong] declareSn = {},response={}", message.getDeclareOrderNo(), httpRequest.body());
        if (httpRequest.ok()) {
            /*if (!"SUCCESS".equals(httpRequest.body()) && !"IGNORE".equals(httpRequest.body())) {
                return "FALSE";
            }*/
            return "SUCCESS";
        } else {
            return "网络异常";
        }
    }

    @PostMapping("/proxyJinDongTax")
    public String proxyJinDongTax(@RequestBody OrderTaxMessage message) {
        Map<String, Object> param = new HashMap<>();
        param.put("merchantOrderNo", message.getDeclareOrderNo());
        param.put("invtNo", message.getInvtNo());
        param.put("portProcessTime", new DateTime(message.getReturnTime()).toString("yyyy-MM-dd HH:mm:ss"));
        param.put("totalTax", message.getConsumptionTax().add(message.getCustomsTax()).add(message.getValueAddedTax()));
        param.put("sign", "未签名");
        HttpRequest httpRequest = HttpRequest.post(OUT_ORDER_HOST + "/open/cdsOrderTaxCallback")
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header("Content-Type", "application/json;charset=utf-8")
                .send(JSON.toJSONString(param));
        if (httpRequest.ok()) {
            return "SUCCESS";
        } else {
            return "网络异常";
        }
    }

    @PostMapping("/proxyERPTax")
    public String proxyERPTax(@RequestBody OrderTaxMessage message) {
        log.info("[proxyERPTax] OrderTaxMessage={}", JSON.toJSONString(message));
//        CompanyDTO ebc = companyService.findByCode(message.getEbcCode());
        CompanyDTO ebc = companyService.findByUnifiedCrossBroderCode(message.getEbcCode());
        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByEbcAndNo(ebc.getId(), message.getDeclareOrderNo());
        log.info("[proxyERPTax] findByEbcAndNo customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
        if (customsInventoryDTO == null) {
            customsInventoryDTO = customsInventoryService.findByInventoryNo90Days(message.getInvtNo());
            log.info("[proxyERPTax] findByEbcAndNo 找不到对应清单改用清单编号查询 customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
        }
        if (customsInventoryDTO == null) {
            customsInventoryDTO = customsInventoryService.findByLogisticsNo90Days(message.getLogisticsNo());
            log.info("[proxyERPTax] findByInventoryNo90Days 改用运单编号查询 inventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
        }
        OrderDTO orderDTO = orderService.findBySnSection(customsInventoryDTO.getOrderSn());
        TaxCallbackSubmit submit = new TaxCallbackSubmit();
        submit.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
        submit.setDeclareOrderNo(message.getDeclareOrderNo());
        submit.setInvtNo(message.getInvtNo());
        submit.setProcessTime(message.getReturnTime());
        submit.setTotalTax(message.getConsumptionTax().add(message.getCustomsTax()).add(message.getValueAddedTax()));
        submit.setTaxNo(message.getTaxNo());
        submit.setCustomsTax(message.getCustomsTax());
        submit.setConsumptionTax(message.getConsumptionTax());
        submit.setValueAddedTax(message.getValueAddedTax());
        submit.setStatus(message.getStatus());
        List<TaxCallbackSubmit.TaxItem> taxItems = ConvertUtil.listConvert(message.getItemList(), TaxCallbackSubmit.TaxItem.class);
        submit.setItemList(taxItems);
        submit.setSendType(message.getSendType());
        if (StrUtil.isBlank(submit.getSendType())) {
            if (Objects.equals(message.getStatus(), "1") && message.getTaxNo().contains("_1")) {
                submit.setSendType("1");
            } else {
                submit.setSendType("0");
            }
        }
        orderService.syncTaxCallback(submit);
        return "SUCCESS";
    }

    @DubboReference
    private TrackLogService trackLogService;

    /**
     * 用于修复回传OMS税金中PDD加密单子 未匹配到清单导致的漏回传问题
     *
     * @param declaredOrderNoKeyWord
     * @param operateType            {@link com.danding.cds.log.api.enums.TrackLogEnums}
     * @param beginDate              yyyy-MM-dd
     * @param endDate                yyyy-MM-dd
     * @return
     */
    @PostMapping("/proxyERPTaxResend")
    public String proxyTaxResend(@RequestParam String declaredOrderNoKeyWord, @RequestParam String operateType, @RequestParam String beginDate, @RequestParam String endDate) {
        log.info("proxyERPTaxResend declaredOrderNoKeyWord={} operateType={} beginDate={} endDate={}", declaredOrderNoKeyWord, operateType, beginDate, endDate);
        List<TrackLogDTO> logDTOList = trackLogService.getByKeyWord(declaredOrderNoKeyWord, operateType, beginDate, endDate);
        logDTOList.forEach(l -> {
            List<TaxResult> taxResults = JSON.parseArray(l.getLogDes(), TaxResult.class);
            taxResults.forEach(taxResult -> {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByInventoryNo90Days(taxResult.getInvtNo());
                OrderDTO orderDTO = orderService.findByIdFull(customsInventoryDTO.getOrderId());
                TaxCallbackSubmit submit = new TaxCallbackSubmit();
                submit.setSystemGlobalSn(orderDTO.getSystemGlobalSn());
                submit.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                submit.setInvtNo(taxResult.getInvtNo());
                submit.setProcessTime(taxResult.getReturnTime().getTime());
                submit.setTotalTax(taxResult.getConsumptionTax().add(taxResult.getCustomsTax()).add(taxResult.getValueAddedTax()));
                submit.setTaxNo(taxResult.getTaxNo());
                submit.setCustomsTax(taxResult.getCustomsTax());
                submit.setConsumptionTax(taxResult.getConsumptionTax());
                submit.setValueAddedTax(taxResult.getValueAddedTax());
                log.info("proxyERPTaxResend submit={}", JSON.toJSONString(submit));
                orderService.syncTaxCallback(submit);
            });
        });
        return "SUCCESS";
    }

    /**
     * 芥舟清单回执的回调
     *
     * @param message
     * @throws Exception
     */
    @RequestMapping("/proxyJieztechInventory")
    public String proxyJieztechInventory(@RequestBody OrderCustomsInventoryMessage message) throws Exception {
        log.info("芥舟清单回执回调 :{}", JSON.toJSONString(message));
        customsInventoryService.jieztechCallback(message);
        log.info("清单编号 : {} , 芥舟清单回执回调成功", message.getInventoryNo());
        return "SUCCESS";
    }

    @Test
    public void test() {
        String l = "{\"agentCode\":\"3318W6K01A\",\"assureCode\":\"3318W6K01A\",\"consumptionTax\":0.0,\"customsCode\":\"2923\",\"customsTax\":0.0,\"ebcCode\":\"3318W6K01A\",\"entDutyNo\":\"\",\"invtNo\":\"29232021I733280110\",\"itemList\":[{\"consumptionTax\":0.0,\"customsTax\":0.0,\"gcode\":\"3305900000\",\"gnum\":1,\"taxPrice\":36.0,\"valueAddedTax\":3.28}],\"logisticsCode\":\"31209606ZC\",\"logisticsNo\":\"777055007448115\",\"note\":\"\",\"orderNo\":\"XP1121082319200761307513000191\",\"returnTime\":1629724641797,\"status\":\"1\",\"taxNo\":\"29232021I733280110_0\",\"valueAddedTax\":3.28}";
        TaxResult taxResult = JSON.parseObject(l, TaxResult.class);
        System.out.println(JSON.toJSONString(taxResult));
    }

    @PostMapping("/proxyLogistics")
    @TenantHttpMethod(type = TenantHttpType.ES_QUERY, handler = DeclareWebEsQuerier.class)
    public String proxyLogistics(@RequestBody OrderCustomsInventoryMessage message) {
        log.info("[op:MessageController] proxyLogistics message={}", JSON.toJSONString(message));
        ListOrderCallback callback = new ListOrderCallback();
        callback.setMerchantOrderNo(message.getDeclareOrderNo());
        callback.setStatus(message.getCustomsStatus());
        callback.setDescription(message.getCustomsDetail());
        callback.setMessage(message.getCustomsDetail());
        callback.setDatetime(new DateTime(message.getTime()).toString("yyyy-MM-dd HH:mm:ss"));
        callback.setType("customs");
        Map<String, String> extra = new HashMap<>();
        extra.put("inventoryNo", message.getInventoryNo());
        callback.setExtraInfo(JSON.toJSONString(extra));

//        CompanyDTO companyDTO = companyService.findByCode(message.getEbpCode());
        CompanyDTO companyDTO = companyService.findByUnifiedCrossBroderCode(message.getEbpCode());
        OrderDTO orderDTO = orderService.findByEbpAndNoAndVersionFull(companyDTO.getId(), message.getDeclareOrderNo(), 0L);
        OrderCallbackSubmit submit = new OrderCallbackSubmit();
        submit.setOrderId(orderDTO.getId());
        submit.setDatetime(callback.getDatetime());
        submit.setStatus(callback.getStatus());
        submit.setDescription(callback.getDescription());
        submit.setLogisticsNo(message.getLogisticsNo());
        log.info("[op:MessageController] proxyLogistics submit={}", JSON.toJSONString(submit));
        orderService.syncOrderCallback(submit);
        return "SUCCESS";
    }


    @PostMapping("/proxyMercuryPay")
    public String proxyMercuryPay(@RequestBody OrderCustomsPaymentMessage message) {
        OrderDTO orderDTO = orderService.findBySnSection(message.getOrderSn());
        CustomsPaymentDTO customsPaymentDTO = customsPaymentService.findByOrder(orderDTO.getId(), orderDTO.getCustomsPaymentSn());
        MercuryPayCustomsDeclareNotifyResult result = new MercuryPayCustomsDeclareNotifyResult();
        if (Objects.equals(customsPaymentDTO.getChannel(), "umf")) {
            PayMerchantAccountChannelDTO channelDTO = channelService.findByCodeAndChannel(customsPaymentDTO.getMerchantCode(), customsPaymentDTO.getChannel());
            result.setRecpAccount(channelDTO.getRecpAccount());
            result.setRecpCode(channelDTO.getRecpCode());
            result.setRecpName(channelDTO.getRecpName());
        }
        result.setCustoms("HANGZHOU");
        result.setCustomsPaymentCode(customsPaymentDTO.getSn());
        result.setOrderType(2);
        result.setOrderNo(customsPaymentDTO.getOutTradeNo());
        result.setDeclareOrderNo(customsPaymentDTO.getDeclareOrderNo());
        result.setBusinessOrderNo("");
        result.setBankNo(customsPaymentDTO.getTradePayNo());
        result.setSubBankNo(message.getSubBankNo());
        result.setSuccess(message.getSuccess());
        result.setIdentityCheck(message.getIdentityCheck());
        result.setVerDept(message.getVerDept());
        result.setPayTransactionId(message.getPayTransactionId());
        result.setErrorMsg(message.getErrorMsg());
        result.setExtra(message.getExtra());
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("success", true);
        requestMap.put("result", JSON.toJSONString(result));
        // http://admin.mall.yang800.com/backend
        log.info("url={} requestMap={}", WEB_SC_HOST, JSON.toJSONString(requestMap));
        try {
            HttpRequest httpRequest = HttpRequest.post(WEB_SC_HOST + "/api/customs/payment/declare/notify")
                    .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                    .form(JSON.parseObject(JSON.toJSONString(requestMap)));
            if (httpRequest.ok()) {
                String body = httpRequest.body();
                log.info("通知成功 body={}", JSON.toJSONString(body));
                return "SUCCESS";
            }
            String body = httpRequest.body();
            log.info("通知失败 params={}", JSON.toJSONString(body));
        } catch (Exception e) {
            log.error("请求失败 params={}", JSON.toJSONString(requestMap), e);
        }
        return "FAIL";
    }

    @GetMapping("/migration")
    public String migration(Long id) {
        messageService.migrationHistory(id);
        return "SUCCESS";
    }

    @PostMapping("/paging")
    public List<MessageSearchVO> paging(@RequestBody MessageSearch search) {
        ListVO<MessageDTO> paging = messageService.paging(search);
        ListVO<MessageSearchVO> result = new PagingBuilder<MessageDTO, MessageSearchVO>() {
            @Override
            protected MessageSearchVO convert(MessageDTO messageDTO) {
                MessageSearchVO vo = new MessageSearchVO();
                BeanUtils.copyProperties(messageDTO, vo);
                return vo;
            }
        }.build(paging);
        return result.getDataList();
    }

    @PostMapping("/retry")
    public String retry(@RequestBody IdsParam messageId) {
        for (Long id : messageId.getIdList()) {
            messageService.retry(id);
        }
        return "SUCCESS";
    }

    @PostMapping("/task/retry")
    public String taskRetry(@RequestBody IdsParam messageId) {
        for (Long id : messageId.getIdList()) {
            messageService.taskRetry(id);
        }
        return "SUCCESS";
    }

    @PostMapping(value = "/proxyRefundWarehouseCallback", produces = "application/json;charset=UTF-8")
    public String proxyRefundWarehouseCallback(@RequestBody OrderRefundWarehouseCallbackMessage message) {
        log.info("proxyRefundWarehouseCallback - message={}", JSON.toJSONString(message));
        try {
            refundOrderRpc.callbackRefundWarehouse(message);
            return "SUCCESS";
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return e.getMessage();
        }
    }
}
