package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.item.api.dto.CustomsGoodsItemInfoDTO;
import com.danding.cds.item.api.service.CustomsGoodsService;
import com.danding.cds.v2.api.BizDeclareFormService;
import com.danding.cds.v2.bean.dto.BizDeclareFormItemDTO;
import com.danding.cds.v2.enums.BizDeclareFormMtpckEndprdTypeEnums;
import com.danding.cds.web.v2.bean.vo.req.BizDeclareFormItemImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 业务申报表表体导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_BIZ_DECLARE_FORM_ITEM", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E4%B8%9A%E5%8A%A1%E7%94%B3%E6%8A%A5%E8%A1%A8%E8%A1%A8%E4%BD%93%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "业务申报表表体导入", classes = BizDeclareFormItemImportExcelVO.class),})
public class BizDeclareFormItemImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long bizDeclareFormId = Long.parseLong((String) extendMap.get("bizDeclareFormId"));
        long bookId = Long.parseLong((String) extendMap.get("bookId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        BizDeclareFormService bizDeclareFormService = this.getBean(BizDeclareFormService.class);
        CustomsGoodsService customsGoodsService = this.getBean(CustomsGoodsService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);

        List<BizDeclareFormItemImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("业务申报表表体导入")) {
                list = group.getDataList(BizDeclareFormItemImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("BizDeclareFormItemImportHandler-导入未获取到数据");
            return;
        }
        List<BizDeclareFormItemDTO> oldItemList = bizDeclareFormService.listItemsById(bizDeclareFormId);

        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;
        Set<String> uniqueFlagSet = oldItemList.stream().map(item ->
                        item.getMtpckEndprdMarkCd() + "_" + item.getProductId() + "_" + item.getGoodsSeqNo())
                .collect(Collectors.toSet());
        List<BizDeclareFormItemDTO> successList = new ArrayList<>();
        for (BizDeclareFormItemImportExcelVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            try {
                // 导入校验
                String mtpckEndprdMarkCd = vo.getMtpckEndprdMarkCd();
                if (StringUtils.isBlank(vo.getMtpckEndprdMarkCd())) {
                    errorMsg.add("料件成品标志不能为空");
                } else if ("成品/残次品".equalsIgnoreCase(vo.getMtpckEndprdMarkCd())) {
                    mtpckEndprdMarkCd = BizDeclareFormMtpckEndprdTypeEnums.END_PRODUCT.getCode();
                    if (StringUtils.isBlank(vo.getProductId())) {
                        errorMsg.add("料号不能为空");
                    }
                } else if ("料件/半成品".equalsIgnoreCase(vo.getMtpckEndprdMarkCd())) {
                    mtpckEndprdMarkCd = BizDeclareFormMtpckEndprdTypeEnums.MATERIAL_PACKAGE.getCode();
                    if (StringUtils.isBlank(vo.getGoodsSeqNo()) && StringUtils.isBlank(vo.getProductId())) {
                        errorMsg.add("料号或序号不能都为空");
                    }
                }
                if (Objects.isNull(vo.getQty())) {
                    errorMsg.add("申报数量不能为空");
                }
                CustomsGoodsItemInfoDTO itemInfoDTO = null;
                if (StrUtil.isNotBlank(vo.getGoodsSeqNo())) {
                    List<CustomsGoodsItemInfoDTO> itemInfoDTOList = customsGoodsService
                            .findItemDetailByBizDeclareForm(bookId, mtpckEndprdMarkCd, vo.getGoodsSeqNo());
                    itemInfoDTO = CollUtil.isNotEmpty(itemInfoDTOList) ? itemInfoDTOList.get(0) : null;
                }
                if (StrUtil.isNotBlank(vo.getProductId()) && Objects.isNull(itemInfoDTO)) {
                    List<CustomsGoodsItemInfoDTO> itemInfoDTOList = customsGoodsService
                            .findItemDetailByBizDeclareForm(bookId, mtpckEndprdMarkCd, vo.getProductId());
                    itemInfoDTO = CollUtil.isNotEmpty(itemInfoDTOList) ? itemInfoDTOList.get(0) : null;
                }
                if (Objects.isNull(itemInfoDTO)) {
                    errorMsg.add("料号或序号不存在");
                } else {
                    String uniqueFlag = mtpckEndprdMarkCd + "_" + itemInfoDTO.getProductId() + "_" + itemInfoDTO.getGoodsSeqNo();
                    if (uniqueFlagSet.contains(uniqueFlag)) {
                        errorMsg.add("料号：" + itemInfoDTO.getProductId() + "已存在");
                    } else {
                        uniqueFlagSet.add(uniqueFlag);
                    }
                }
                if (CollUtil.isNotEmpty(errorMsg)) {
                    this.callbackData(false, index++, String.join(";", errorMsg), vo);
                    continue;
                }
                // 拼装表体数据
                BizDeclareFormItemDTO dto = new BizDeclareFormItemDTO();
                dto.setMtpckEndprdMarkCd(mtpckEndprdMarkCd);
                dto.setProductId(itemInfoDTO.getProductId());
                dto.setGoodsSeqNo(itemInfoDTO.getGoodsSeqNo());
                dto.setGoodsCode(itemInfoDTO.getHsCode());
                dto.setGoodsName(itemInfoDTO.getGoodsName());
                dto.setGoodsModel(itemInfoDTO.getGoodsModel());
                dto.setDeclareUnit(itemInfoDTO.getUnit());
                dto.setCountry(itemInfoDTO.getOriginCountry());
                dto.setPrice(itemInfoDTO.getDeclarePrice());
                dto.setTotalPrice(itemInfoDTO.getDeclareTotalPrice());
                dto.setLawFirstUnit(itemInfoDTO.getFirstUnit());
                dto.setLawSecondUnit(itemInfoDTO.getSecondUnit());
                dto.setCurrency(itemInfoDTO.getCurrency());
                dto.setEditMark("3");
                dto.setQty(vo.getQty());
                if (Objects.nonNull(vo.getQty()) && Objects.nonNull(itemInfoDTO.getDeclarePrice())) {
                    dto.setTotalPrice(vo.getQty().multiply(itemInfoDTO.getDeclarePrice()));
                }
                successList.add(dto);
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("业务申报表表体导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            userInfo.getUserId();
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            bizDeclareFormService.importItemList(bizDeclareFormId, successList, operator);
        }
    }
}
