package com.danding.cds.web.customs.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.customs.hs.api.dto.CustomsHsSubmit;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.web.customs.vo.CustomsHsCodeDetailVO;
import com.danding.cds.web.customs.vo.CustomsHsCodeVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/9/27 15:14
 * @Description:
 */
public interface CustomsHsCodeRpc {

    RpcResult<ListVO<CustomsHsCodeVO>> paging(CustomsHsSearchCondition condition);

    RpcResult<CustomsHsCodeDetailVO> detail(Long id);

    RpcResult<Long> delete(Long id);

    RpcResult<Long> upset(CustomsHsSubmit submit);

    RpcResult<List<SelectOptionVO<Integer>>> listConsumptionFlag();

    RpcResult<List<SelectOptionVO<Integer>>> listFloatFlag();

    RpcResult<Long> exportExcel(CustomsHsSearchCondition condition) throws ServiceException;

    RpcResult<Long> importExcel(MultipartFile file) throws ArgsErrorException;
}
