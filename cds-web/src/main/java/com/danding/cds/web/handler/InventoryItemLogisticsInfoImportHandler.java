package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.enums.Inv101TrspModecd;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.v2.bean.dto.InventoryItemLogisticsInfoDTO;
import com.danding.cds.web.v2.bean.vo.req.InventoryItemLogisticsInfoImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.xxl.job.core.util.DateUtil;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 清关单表体溯源信息导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_INVENTORY_ITEM_LOGISTICS_INFO", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%B8%85%E5%85%B3%E6%BA%AF%E6%BA%90%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFV1.1.xls",
        groups = {@ParkImportsHandler.Group(name = "清关溯源", classes = InventoryItemLogisticsInfoImportExcelVO.class),})
public class InventoryItemLogisticsInfoImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long inventoryOrderId = Long.parseLong((String) extendMap.get("inventoryOrderId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        InventoryOrderInfoService inventoryOrderInfoService = this.getBean(InventoryOrderInfoService.class);
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);

        List<InventoryItemLogisticsInfoImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("清关溯源")) {
                list = group.getDataList(InventoryItemLogisticsInfoImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("InventoryItemLogisticsInfoImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;


        List<InventoryOrderItemDTO> itemDTOList = inventoryOrderInfoService.findListByInvenOrderId(inventoryOrderId);
        Map<String, List<InventoryOrderItemDTO>> itemDTOMap = itemDTOList.stream().collect(Collectors.groupingBy(InventoryOrderItemDTO::getProductId));
        for (InventoryItemLogisticsInfoImportExcelVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            if (StringUtils.isBlank(vo.getProductId())) {
                errorMsg.add("海关备案料号不能为空");
            } else if (!itemDTOMap.containsKey(vo.getProductId())) {
                errorMsg.add("未找到海关备案料号，请检查表格或清关单表体");
            }
            // 报关单号不超过 18位数字
            if (StringUtils.isBlank(vo.getCustomsEntryNo())) {
                errorMsg.add("报关单号不能为空");
            } else if (!Pattern.matches("^[0-9]{1,18}$", vo.getCustomsEntryNo())) {
                errorMsg.add("报关单号格式不正确");
            }
            Date customsEntryTime = null;
            if (StringUtil.isBlank(vo.getCustomsEntryTime())) {
                errorMsg.add("报关日期不能为空");
            } else {
                customsEntryTime = DateUtil.parseDate(vo.getCustomsEntryTime());
                if (Objects.isNull(customsEntryTime)) {
                    errorMsg.add("报关日期格式不正确");
                }
            }
            if (StringUtils.isBlank(vo.getFromLocation())) {
                errorMsg.add("起运港不能为空");
            } else if (!this.isChinese(vo.getFromLocation())) {
                errorMsg.add("起运港只允许填写中文");
            }
            if (StringUtils.isBlank(vo.getShipmentCountry())) {
                errorMsg.add("启运国不能为空");
            } else if (!this.isChinese(vo.getShipmentCountry())) {
                errorMsg.add("启运国只允许填写中文");
            } else {
                List<CustomsDictionaryDTO> shipmentCountry = customsDictionaryService
                        .findByTypeAndName(DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getShipmentCountry());
                if (CollUtil.isEmpty(shipmentCountry)) {
                    errorMsg.add("启运国不存在，请检查");
                }
            }
            if (StringUtils.isBlank(vo.getEntryPort())) {
                errorMsg.add("进境口岸不能为空");
            } else if (!this.isChinese(vo.getEntryPort())) {
                errorMsg.add("进境口岸只允许填写中文");
            }
            if (StringUtils.isBlank(vo.getTransportMode())) {
                errorMsg.add("运输方式不能为空");
            } else if (Arrays.stream(Inv101TrspModecd.values()).noneMatch(item -> item.getDesc().equalsIgnoreCase(vo.getTransportMode()))) {
                errorMsg.add("运输方式不存在，请检查");
            }
            if (StringUtils.isBlank(vo.getOriginCountry())) {
                errorMsg.add("原产国不能为空");
            } else {
                List<CustomsDictionaryDTO> originCountry = customsDictionaryService
                        .findByTypeAndName(DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getOriginCountry());
                if (CollUtil.isEmpty(originCountry)) {
                    errorMsg.add("原产国不存在，请检查");
                } else {
                    vo.setOriginCountry(originCountry.get(0).getCode());
                }
            }

            if (CollUtil.isNotEmpty(errorMsg)) {
                this.callbackData(false, index++, String.join(";", errorMsg), vo);
                continue;
            }
            try {
                InventoryItemLogisticsInfoDTO logisticsInfoDTO = new InventoryItemLogisticsInfoDTO();
                logisticsInfoDTO.setProductId(vo.getProductId());
                logisticsInfoDTO.setCustomsEntryNo(vo.getCustomsEntryNo());
                logisticsInfoDTO.setCustomsEntryTime(customsEntryTime);
                logisticsInfoDTO.setShipmentCountry(vo.getShipmentCountry());
                logisticsInfoDTO.setFromLocation(vo.getFromLocation());
                logisticsInfoDTO.setTransportMode(vo.getTransportMode());
                logisticsInfoDTO.setEntryPort(vo.getEntryPort());
                logisticsInfoDTO.setOriginCountry(vo.getOriginCountry());
                ImportResultResVo res = inventoryOrderInfoService.importInventoryItemLogisticsInfoExcel(inventoryOrderId, logisticsInfoDTO);
                log.info("清关单表体溯源信息导入,导入后的返回结果数据{}", JSON.toJSONString(res));
                this.callbackData(res.getFlag(), index++, res.getReason(), vo);
            } catch (Exception e) {
                log.error("清关单表体溯源信息导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }
        }
    }

    private boolean isChinese(String str) {
        String regex = "^[\\u4e00-\\u9fa5]+$";
        return Pattern.matches(regex, str);
    }

}
