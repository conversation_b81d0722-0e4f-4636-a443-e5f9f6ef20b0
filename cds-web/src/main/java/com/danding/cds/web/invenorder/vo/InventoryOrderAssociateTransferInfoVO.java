package com.danding.cds.web.invenorder.vo;

import lombok.Data;

@Data
public class InventoryOrderAssociateTransferInfoVO {

    /**
     * 仓库信息【出】
     */
    private WarehouseInfoVO outWarehouseInfoVO;

    /**
     * 仓库信息【入】
     */
    private WarehouseInfoVO inWarehouseInfoVO;


    @Data
    public static class WarehouseInfoVO {

        /**
         * 清关单id
         */
        private Long inventoryOrderId;

        /**
         * 仓库类型
         */
        private String warehouseTypeName;

        /**
         * 清关单号
         */
        private String inveCustomsSn;

        /**
         * 业务类型
         */
        private String businessTypeName;

        /**
         * 清关企业
         */
        private String inveCompanyName;

        /**
         * 实体仓名称
         */
        private String entityWarehouseName;

        /**
         * 货主
         */
        private String ownerName;
    }

}
