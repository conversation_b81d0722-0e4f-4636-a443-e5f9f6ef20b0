package com.danding.cds.web.customs.rpc;

import java.util.List;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictAddParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictEnableParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictSearch;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictUpdateParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

/**
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
public interface CustomsManagerRpc {

    /**
     * 获取口岸分页数据
     * @return
     */
    RpcResult<ListVO<CustomsDistrictDTO>> paging(CustomsDistrictSearch search);

    /**
     * 新增
     * @param param
     * @return
     */
    RpcResult<String> insert(CustomsDistrictAddParam param);

    /**
     * 修改
     * @param param
     * @return
     */
    RpcResult<String> update(CustomsDistrictUpdateParam param);


    /**
     * 删除
     * @param id
     * @return
     */
    RpcResult<String> delete(Long id);

    /**
     * 启用禁用
     * @param param
     * @return
     */
    RpcResult<String> enableSwitch(CustomsDistrictEnableParam param);


    /**
     * 口岸名称下拉
     * @return
     */
    public RpcResult<List<SelectOptionVO<String>>> nameSelect();
}
