package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.api.StockInventoryRpc;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.StockInventoryExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Create 2021/9/7  10:31
 * @Describe
 **/
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_STOCK_ADJUST", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E6%89%B9%E9%87%8F%E8%B0%83%E6%95%B4%E5%BA%93%E5%AD%98%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "账册库存批量调整", classes = StockInventoryExcelVO.class)})
public class StockInventoryAdjustImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        StockInventoryRpc stockInventoryRpc = this.getBean(StockInventoryRpc.class);
        List<StockInventoryExcelVO> list = null;
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("账册库存批量调整")) {
                list = group.getDataList(StockInventoryExcelVO.class);
            }
        }
        log.info("StockInventoryAdjustImportsHandler 读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            log.info("StockInventoryAdjustImportsHandler-导入未获取到数据");
            return;
        }
        int index = 1;
        String operator = getOperator();
        for (StockInventoryExcelVO stockInventoryExcelVO : list) {
            ImportResultResVo resultResVo = stockInventoryRpc.checkImport(stockInventoryExcelVO.getCustomsBookNo(),
                    stockInventoryExcelVO.getProductId(), stockInventoryExcelVO.getGoodsSeqNo(),
                    stockInventoryExcelVO.getChangeNum(), operator, stockInventoryExcelVO.getRemark());
            log.info("运单：{}，导入后的返回结果数据{}", stockInventoryExcelVO.getProductId(), resultResVo.toString());
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), stockInventoryExcelVO);
            index++;
        }
    }
}
