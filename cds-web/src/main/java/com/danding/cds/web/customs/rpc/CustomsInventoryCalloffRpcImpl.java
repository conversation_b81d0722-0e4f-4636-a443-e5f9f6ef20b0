package com.danding.cds.web.customs.rpc;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.vo.CalloffEditRefundGoodsInfoReqVo;
import com.danding.cds.c.api.rpc.CustomsInventoryCalloffCRpc;
import com.danding.cds.common.annotation.ParamValidator;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParam;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.inventory.api.enums.TimeOutDayEnum;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.service.UploadProcessService;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.web.annotation.LogParam;
import com.danding.cds.web.customs.CustomsInventoryCalloffController;
import com.danding.cds.web.customs.InventoryCancelController;
import com.danding.cds.web.customs.rpc.param.RejectCalloffBatchReqVO;
import com.danding.cds.web.customs.rpc.param.RpcDeclareParam;
import com.danding.cds.web.customs.vo.CalloffInfoSumVO;
import com.danding.cds.web.customs.vo.InventoryCancelResponseReport;
import com.danding.cds.web.refund.RefundOrderInfoController;
import com.danding.cds.web.refund.vo.RefundResponseReport;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @menu 取消单管理
 * @Author: Raymond
 * @Date: 2020/10/15 18:12
 * @Description:
 */
@DubboService
@Slf4j
public class CustomsInventoryCalloffRpcImpl implements CustomsInventoryCalloffRpc {

    @Autowired
    private CustomsInventoryCalloffController customsInventoryCalloffController;
    @Autowired
    private RefundOrderInfoController refundOrderInfoController;
    @Autowired
    private InventoryCancelController inventoryCancelController;
    @DubboReference
    private UploadProcessService uploadProcessService;
    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;
    @Resource
    private OrderCCallConfig orderCCallConfiguration;

    @Override
    @SoulClient(path = "/calloff/paging", desc = "取消单分页查询")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<CustomsInventoryCalloffVO>> paging(CustomsInventoryCalloffSearch search) {
        try {
            ListVO<CustomsInventoryCalloffVO> paging = customsInventoryCalloffController.paging(search);
            return RpcResult.success(paging);
        } catch (ArgsErrorException e) {
            log.error("CustomsInventoryCalloffRpcImpl paging error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("CustomsInventoryCalloffRpcImpl paging error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/calloff/inventoryCalloffOverTimeCount", desc = "取消单分页查询")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<InventoryCallOffOverTimeCountVO> inventoryCalloffOverTimeCount(CustomsInventoryCalloffSearch search) {
        try {
            InventoryCallOffOverTimeCountVO countVO = customsInventoryCalloffCRpc.inventoryCalloffOverTimeCount(search);
            return RpcResult.success(countVO);
        } catch (ArgsErrorException e) {
            log.error("CustomsInventoryCalloffRpcImpl inventoryCalloffOverTimeCount error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("CustomsInventoryCalloffRpcImpl inventoryCalloffOverTimeCount error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }


//    @Override
//    @SoulClient(path = "/calloff/create", desc = "生成取消单")
//    public RpcResult<Response<String>> createCalloff(Long orderId) {
//        return RpcResult.success(customsInventoryCalloffController.createCalloff(orderId));
//    }


    @Override
    @SoulClient(path = "/calloff/reject", desc = "驳回取消单")
    public RpcResult<Response<String>> rejectCalloff(Long id, String rejectReason) {
        return RpcResult.success(customsInventoryCalloffController.rejectCalloff(id, rejectReason));
    }

    /**
     * 批量驳回
     *
     * @param reqVO
     * @return
     * @path /calloff/rejectBatch
     */
    @Override
    @SoulClient(path = "/calloff/rejectBatch", desc = "批量驳回")
    public RpcResult<String> rejectCalloffBatch(RejectCalloffBatchReqVO reqVO) {
        return customsInventoryCalloffController.rejectCalloffBatch(reqVO);
    }

    @Override
    @SoulClient(path = "/calloff/cancelReject", desc = "取消驳回")
    public RpcResult<Response<String>> cancelReject(Long id) {
        return RpcResult.success(customsInventoryCalloffController.cancelRejectCalloff(id));
    }

    @Override
    @SoulClient(path = "/calloff/directCalloff", desc = "直接取消")
    public RpcResult<Response<String>> direct(Long id, String calloffReason) {
        return RpcResult.success(customsInventoryCalloffController.direct(id, calloffReason));
    }


//    @Override
//    @SoulClient(path = "/calloff/returnCalloff", desc = "取消单-退货")
//    public RpcResult<Response<String>> returnCalloff(Long id, String calloffReason) {
//        return RpcResult.success(customsInventoryCalloffController.returnCalloff(id, calloffReason));
//    }

//    @Override
//    @SoulClient(path = "/calloff/cancelCalloff", desc = "取消单-撤单")
//    public RpcResult<Response<String>> cancelCalloff(Long id, String calloffReason) {
//        return RpcResult.success(customsInventoryCalloffController.cancelCalloff(id, calloffReason));
//    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/applyRefundOrder", desc = "创建退货订单")
    public RpcResult<RefundResponseReport> applyRefundOrder(RpcDeclareParam declareParam) {
        try {
            if (Objects.isNull(declareParam) || StrUtil.isEmpty(declareParam.getSns())) {
                return RpcResult.error("申请创建退货参数为空");
            }
            RefundResponseReport report = refundOrderInfoController.applyRefundOrder(declareParam.getSns(), declareParam.getReason());
            return RpcResult.success(report);
        } catch (Exception e) {
            log.error("CustomsInventoryCalloffRpcImpl declareCancel error = {}", e.getMessage(), e);
            return RpcResult.error("申请创建撤单失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/applyCancel", desc = "申请创建撤单")
    public RpcResult<InventoryCancelResponseReport> applyCancel(RpcDeclareParam declareParam) {
        try {
            if (Objects.isNull(declareParam) || StrUtil.isEmpty(declareParam.getSns())) {
                return RpcResult.error("申请创建撤单参数为空");
            }
            InventoryCancelResponseReport declare = inventoryCancelController.applyCancel(declareParam.getSns(), declareParam.getReason());
            return RpcResult.success(declare);
        } catch (Exception e) {
            log.error("CustomsInventoryCalloffRpcImpl declareCancel error = {}", e.getMessage(), e);
            return RpcResult.error("申请创建撤单失败");
        }
    }


    @Override
    @SoulClient(path = "/calloff/calloffStatusCount", desc = "各状态取消单个数查询")
    public RpcResult<CustomsInventoryCalloffCountDTO> calloffStatusCount(CustomsInventoryCalloffSearch search) {
//        return RpcResult.success(customsInventoryCalloffController.calloffStatusCount(search));
        return RpcResult.success(new CustomsInventoryCalloffCountDTO());
    }

    @Override
    @SoulClient(path = "/calloff/calloffOrderCount", desc = "退货和撤单取消单个数查询")
    @RequestMapping("/calloff/calloffOrderCount")
    public RpcResult<CustomsInventoryCalloffCountDTO2> calloffOrderCount(@RequestBody CustomsInventoryCalloffSearch search) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return RpcResult.success(customsInventoryCalloffCRpc.calloffOrderCount(search));
        }
        return RpcResult.success(customsInventoryCalloffService.calloffOrderCount(search));
    }


    @Override
    @SoulClient(path = "/calloff/sumInventoryCalloff", desc = "统计-取消单个数")
    public RpcResult<List<CalloffInfoSumVO>> sumInventoryCalloff(Long beginTimeLong, Long endTimeLong) {
        return RpcResult.success(customsInventoryCalloffController.sumInventoryCalloff(beginTimeLong, endTimeLong));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/import/query", desc = "取消单导入查询")
    public RpcResult<LoadTaskInfoDTO> calloffImportQuery() {
        UploadType uploadType = UploadType.CUSTOMS_INVENTORY_CALLOFF;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setTemplateUrl(uploadType.getUrl());
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        return RpcResult.success(loadTaskInfoDTO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/import", desc = "取消单导入")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> calloffImport(UrlParam urlParam) {
        try {
            uploadProcessService.submitProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    urlParam.getUrl(), UploadType.CUSTOMS_INVENTORY_CALLOFF, urlParam);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/import/confirm", desc = "取消单导入确认")
    public RpcResult<String> calloffImportConfirm() {
        UploadType uploadType = UploadType.CUSTOMS_INVENTORY_CALLOFF;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success("提交成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/export", desc = "取消单导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> export(CustomsInventoryCalloffSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.CUSTOMS_INVENTORY_CALLOFF_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/calloff/uploadPic", desc = "取消凭证上传")
    public RpcResult<String> uploadPic(Long id, String picUrl) {

        // 1. 校验参数
        if (id == null) return RpcResult.error("取消单id不能为空");
        if (StrUtil.isBlank(picUrl)) return RpcResult.error("取消凭证不能为空");
        CustomsInventoryCalloffDTO info = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            info = customsInventoryCalloffCRpc.findById(id);
        } else {
            info = customsInventoryCalloffService.findById(id);
        }
        if (info == null) return RpcResult.error("取消单不存在");

        // 2. 更新数据
        info.setPicJson(JSON.toJSONString(Lists.newArrayList(picUrl)));
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.update(info);
        } else {
            customsInventoryCalloffService.update(info);
        }
        return RpcResult.success("上传成功");
    }

    @Override
    @SoulClient(path = "/calloff/uploadPic/del", desc = "清空京东取消单")
    public RpcResult jdCancelOrderSynDel() {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.delJd();
            customsInventoryCalloffCRpc.delJdDetail();
        } else {
            customsInventoryCalloffService.delJd();
            customsInventoryCalloffService.delJdDetail();
        }
        return RpcResult.success(true);
    }

    @Override
    @SoulClient(path = "/calloff/uploadPic/preview", desc = "取消单预览")
    public RpcResult jdCancelOrderSynPreview(CustomsInventoryCalloffSubmit submit) throws ArgsErrorException {

        if (submit != null && submit.getDeclareOrderNo() == null) {
            if (submit.getBeginCalloffTime() == null) {
                new ArgsErrorException("开始时间不能为空");
            }
            if (submit.getEndCalloffTime() == null) {
                new ArgsErrorException("结束时间不能为空");
            }
            Long diff = submit.getEndCalloffTime() - submit.getBeginCalloffTime();
            // 60天
            Long daysMills_30 = 60 * 24 * 60 * 60 * 1000L;
            if (diff > daysMills_30) {
                throw new ArgsErrorException("时间跨度不能超过60天");
            }
        }
        List<CustomsCancelOrderDTO> list = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = customsInventoryCalloffCRpc.jdCancelOrderSynPreview(submit);
        } else {
            list = customsInventoryCalloffService.jdCancelOrderSynPreview(submit);
        }
        list = list.stream().sorted(Comparator.comparing(CustomsCancelOrderDTO::getTaskId).reversed()).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/calloff/uploadPic/queryDetails", desc = "JD取消单明细")
    public RpcResult jdCancelOrderDetails(CustomsInventoryCalloffSubmit submit) {
        List<CustomsCancelOrderDetailsDTO> list = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = customsInventoryCalloffCRpc.jdCancelOrderDetails(submit);
        } else {
            list = customsInventoryCalloffService.jdCancelOrderDetails(submit);
        }
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/calloff/listTimeOutDayEnum", desc = "下拉-超时时间（天）")
    @RequestMapping("/calloff/listTimeOutDayEnum")
    public RpcResult listTimeOutDayEnum() {
        return RpcResult.success(EnumUtils.build(TimeOutDayEnum.class, "value", "desc"));
    }

    @Override
    @SoulClient(path = "/calloff/listCalloffTypeEnum", desc = "下拉-取消单类型")
    @RequestMapping("/calloff/listCalloffTypeEnum")
    public RpcResult listCalloffTypeEnum() {
        List<EnumUtils> enumUtils = EnumUtils.build(InventoryCalloffTypeEnum.class, "code", "desc");
        List<EnumUtils> result = enumUtils.stream()
                .filter(e -> !(Objects.equals(InventoryCalloffTypeEnum.CALLOFF_EMPTY.getCode(), e.getId())
                        || Objects.equals(InventoryCalloffTypeEnum.CALLOFF_DIRECT.getCode(), e.getId())
                        || Objects.equals(InventoryCalloffTypeEnum.CALLOFF_PRE.getCode(), e.getId()))
                ).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/calloff/listCalloffOrderTag", desc = "取消单订单标记下拉列表")
    public RpcResult<List<SelectOptionVO<Integer>>> listCalloffOrderTag() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(InventoryCalloffOrderTagEnums.values()).filter(tag -> !tag.equals(InventoryCalloffOrderTagEnums.NULL))
                .map(tag -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO();
                    optionDTO.setId(tag.getCode());
                    optionDTO.setName(tag.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 修改退货商品信息
     *
     * @param reqVO
     * @return
     * @path /calloff/editRefundGoodsInfo
     */
    @Override
    @ParamValidator
    @LogParam
    @SoulClient(path = "/calloff/editRefundGoodsInfo", desc = "修改退货商品信息")
    public RpcResult<Boolean> editRefundGoodsInfo(CalloffEditRefundGoodsInfoReqVo reqVO) {
        customsInventoryCalloffCRpc.editRefundGoodsInfo(reqVO);
        return RpcResult.success();
    }

}
