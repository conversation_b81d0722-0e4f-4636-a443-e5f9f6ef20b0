package com.danding.cds.web.v2.bean.vo.req;

import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProcessTradeBookConsumptionCreateReq implements Serializable {

    private Long id;

    /**
     * 关联账册id
     */
    @NotNull(message = "关联账册id不能为空")
    private Long refBookId;

    /**
     * 料件id
     */
    @NotNull(message = "料件id不能为空")
    private Long mtpckId;

    /**
     * 成品id
     */
    @NotNull(message = "成品id不能为空")
    private Long endPrdId;

    /**
     * 净耗
     */
    @NotNull(message = "净耗不能为空")
    @Max(value = 999, message = "净耗仅能填写大于0的整数，不超过3位")
    @Min(value = 1, message = "净耗仅能填写大于0的整数，不超过3位")
    private Integer netConsumption;

    /**
     * 单耗申报状态
     */
    @NotNull(message = "单耗申报状态不能为空")
    private Integer declareStatus;
}
