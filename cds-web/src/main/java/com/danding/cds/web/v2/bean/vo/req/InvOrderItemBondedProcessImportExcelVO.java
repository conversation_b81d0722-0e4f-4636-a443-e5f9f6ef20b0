package com.danding.cds.web.v2.bean.vo.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class InvOrderItemBondedProcessImportExcelVO {

    /**
     * 料件成品标志
     */
    @ExcelProperty(value = "*料件成品标志")
    @NotBlank(message = "料件成品标志不能为空")
    private String goodsType;

    /**
     * 料号
     */
    @ExcelProperty(value = "*料号")
    @NotBlank(message = "料号不能为空")
    private String productId;

    /**
     * *数量
     */
    @ExcelProperty(value = "*申报数量")
    @NotNull(message = "申报数量不能为空")
    private BigDecimal qty;
}
