package com.danding.cds.web.invenorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel
public class CheckItemInfo {

    @ApiModelProperty("预录核放单编号")
    private String preCheckOrderNo;

    @ApiModelProperty("核放单编号")
    private String checkOrderNo;

    @ApiModelProperty("核放状态")
    private String checkStatus;

    @ApiModelProperty("创建时间")
    private Date createTime;

}
