package com.danding.cds.web.cullOrder.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.cull.api.enums.CancelInfoEnums;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.cull.api.enums.EndorsementLabelEnums;
import com.danding.cds.cull.api.service.CullOrderService;
import com.danding.cds.cull.api.vo.CullOrderReqVO;
import com.danding.cds.cull.api.vo.CullOrderResVO;
import com.danding.cds.cull.api.vo.CullOrderUpdateReqVO;
import com.danding.cds.customs.inventory.api.enums.InventoryReviewStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/19
 */
@Slf4j
@DubboService
@RestController
public class CullOrderRpcImpl implements CullOrderRpc{

    @DubboReference
    private CullOrderService cullOrderService;

    @DubboReference
    private DownloadProcessService downloadProcessService;



    @Override
    @SoulClient(path = "/cull/endorsementLabelEnums",desc = "剔除标签下拉")
    public RpcResult endorsementLabelEnums(){
        return RpcResult.success(EnumUtils.build(EndorsementLabelEnums.class,"code","desc"));
    }

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @Override
    @SoulClient(path = "/cull/paging", desc = "剔除单分页查询")
    public RpcResult<ListVO<CullOrderResVO>> paging(CullOrderReqVO search) {
        ListVO<CullOrderResVO> cullOrderResVOListVO =cullOrderService.paging(search);
        return RpcResult.success(cullOrderResVOListVO);
    }

    @Override
    @SoulClient(path = "/cull/neglect", desc = "忽略|批量忽略")
    public RpcResult<String> neglect(CullOrderUpdateReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            log.warn("参数为空");
            return RpcResult.error("参数为空");
        }
        try {
            cullOrderService.neglect(reqVO.getIds());
            return RpcResult.success("忽略成功");
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/cull/listCullStatus", desc = "剔除单状态下拉")
    public RpcResult<List<SelectOptionVO>> listCullStatus() {
        List<SelectOptionVO> result = Arrays.stream(CullStatusEnums.values())
                .filter((CullStatusEnums item) -> !item.equals(CullStatusEnums.NULL))
                .map((CullStatusEnums item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/cull/listReviewStatus", desc = "核注状态下拉")
    public RpcResult<List<SelectOptionVO>> listReviewStatus() {
        List<SelectOptionVO> result = Arrays.stream(InventoryReviewStatus.values())
                .map((InventoryReviewStatus item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getValue());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/cull/listFinishStatus", desc = "是否撤单状态下拉")
    public RpcResult<List<SelectOptionVO>> listFinishStatus() {
        List<SelectOptionVO> result = Arrays.stream(CancelInfoEnums.values())
                .map((CancelInfoEnums item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/cull/export", desc = "剔除单导出")
    public RpcResult<String> export(CullOrderReqVO search){
        try {
            log.warn("剔除单导出 用户 - {}, 请求条件 - {}, 导出类型 - {}",UserInfoContext.getInstance().getUserInfo(), JSON.toJSONString(search),ReportType.CULL_ORDER_FOR_EXCEL.name);
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.CULL_ORDER_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

}
