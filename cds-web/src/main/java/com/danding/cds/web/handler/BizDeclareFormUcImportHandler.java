package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.v2.api.BizDeclareFormService;
import com.danding.cds.v2.bean.dto.BizDeclareFormDTO;
import com.danding.cds.v2.bean.dto.BizDeclareFormItemDTO;
import com.danding.cds.v2.bean.dto.BizDeclareFormUnitConsumptionDTO;
import com.danding.cds.v2.enums.BizDeclareFormStatusEnums;
import com.danding.cds.web.v2.bean.vo.req.BizDeclareFormUcImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 业务申报表单耗导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_BIZ_DECLARE_FORM_UC", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E4%B8%9A%E5%8A%A1%E7%94%B3%E6%8A%A5%E8%A1%A8%E5%8D%95%E8%80%97%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "业务申报表单耗导入", classes = BizDeclareFormUcImportExcelVO.class),})
public class BizDeclareFormUcImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long bizDeclareFormId = Long.parseLong((String) extendMap.get("bizDeclareFormId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        BizDeclareFormService bizDeclareFormService = this.getBean(BizDeclareFormService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);

        List<BizDeclareFormUcImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("业务申报表单耗导入")) {
                list = group.getDataList(BizDeclareFormUcImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("BizDeclareFormUcImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<BizDeclareFormUnitConsumptionDTO> successList = new ArrayList<>();
        BizDeclareFormDTO declareFormDTO = bizDeclareFormService.findById(bizDeclareFormId);
        List<BizDeclareFormItemDTO> itemDTOList = bizDeclareFormService.listItemsById(bizDeclareFormId);
        Map<Integer, BizDeclareFormItemDTO> itemDTOMap = itemDTOList.stream().collect(Collectors.toMap(BizDeclareFormItemDTO::getSeqNo, dto -> dto));
        List<BizDeclareFormUnitConsumptionDTO> oldUcList = bizDeclareFormService.listUnitConsumptionsById(bizDeclareFormId);
        List<BizDeclareFormUnitConsumptionDTO> newUcList = ConvertUtil.listConvert(list, BizDeclareFormUnitConsumptionDTO.class);
        oldUcList.addAll(newUcList);
        Map<String, Long> setGrouping = oldUcList.stream()
                .collect(Collectors.groupingBy(i -> i.getEndprdSeqNo() + "#" + i.getMtpckSeqNo(),
                        Collectors.counting()));
        Map<Integer, Long> endprdGroup = oldUcList.stream()
                .collect(Collectors.groupingBy(BizDeclareFormUnitConsumptionDTO::getEndprdSeqNo, Collectors.counting()));

        for (BizDeclareFormUcImportExcelVO vo : list) {
            try {
                List<String> errorMsg = new ArrayList<>();
                String errorMsgInfo = doValidator(vo);
                if (StrUtil.isNotBlank(errorMsgInfo)) {
                    errorMsg.add(errorMsgInfo);
                }
                // 损耗率仅能输入百分比小数，小数点后2位
                if (!isPercentage(vo.getConsumptionRate())) {
                    errorMsg.add("损耗率仅能输入百分比小数，小数点后2位");
                }
                //备案中、结案中、已结案、已作废，报错“申报表状态无法保存”
                if (BizDeclareFormStatusEnums.RECORDING.getCode().equalsIgnoreCase(declareFormDTO.getStatus()) ||
                        BizDeclareFormStatusEnums.FINISHING.getCode().equalsIgnoreCase(declareFormDTO.getStatus()) ||
                        BizDeclareFormStatusEnums.FINISHED.getCode().equalsIgnoreCase(declareFormDTO.getStatus()) ||
                        BizDeclareFormStatusEnums.DISCARD.getCode().equalsIgnoreCase(declareFormDTO.getStatus())) {
                    errorMsg.add("申报表状态无法保存");
                }
                if (Objects.equals(declareFormDTO.getEnable(), 1)) {
                    errorMsg.add("申报表状态启用中，无法保存");
                }
                if (setGrouping.containsKey(vo.getEndprdSeqNo() + "#" + vo.getMtpckSeqNo())
                        && setGrouping.get(vo.getEndprdSeqNo() + "#" + vo.getMtpckSeqNo()) > 1) {
                    errorMsg.add("成品和料件组合已存在");
                }
                if (endprdGroup.containsKey(vo.getEndprdSeqNo())
                        && endprdGroup.get(vo.getEndprdSeqNo()) > 1) {
                    errorMsg.add("成品序号已存在");
                }
                BizDeclareFormItemDTO endprdDTO = itemDTOMap.get(vo.getEndprdSeqNo());
                if (Objects.isNull(endprdDTO)) {
                    errorMsg.add("成品序号不存在");
                }
                BizDeclareFormItemDTO mtpckDTO = itemDTOMap.get(vo.getMtpckSeqNo());
                if (Objects.isNull(mtpckDTO)) {
                    errorMsg.add("料件序号不存在");
                }
                if (CollUtil.isNotEmpty(errorMsg)) {
                    this.callbackData(false, index++, String.join(";", errorMsg), vo);
                    continue;
                }
                BizDeclareFormUnitConsumptionDTO dto = new BizDeclareFormUnitConsumptionDTO();
                dto.setEndprdSeqNo(vo.getEndprdSeqNo());
                dto.setEndprdGoodsName(endprdDTO.getGoodsName());
                dto.setEndprdId(endprdDTO.getId());
                dto.setMtpckGoodsName(mtpckDTO.getGoodsName());
                dto.setMtpckSeqNo(mtpckDTO.getSeqNo());
                dto.setMtpckId(mtpckDTO.getId());
                dto.setNetConsumption(vo.getNetConsumption());
                dto.setConsumptionRate(vo.getConsumptionRate().setScale(2, RoundingMode.HALF_UP));
                dto.setEditMark("3");
                successList.add(dto);
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("业务申报表单耗导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            userInfo.getUserId();
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            bizDeclareFormService.importUnitConsumptions(bizDeclareFormId, successList, operator);
        }
    }

    /**
     * 百分比校验
     *
     * @param consumptionRate
     * @return
     */
    private boolean isPercentage(BigDecimal consumptionRate) {
        consumptionRate = consumptionRate.stripTrailingZeros();
        return consumptionRate.compareTo(BigDecimal.ZERO) >= 0 && consumptionRate.compareTo(BigDecimal.valueOf(1)) <= 0 && consumptionRate.scale() <= 2;
    }
}
