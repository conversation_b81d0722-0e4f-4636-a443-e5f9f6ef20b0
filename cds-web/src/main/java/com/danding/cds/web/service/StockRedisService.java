package com.danding.cds.web.service;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.danding.cds.item.api.dto.CustomsInitDTO;
import com.danding.cds.item.api.service.CustomsInitService;
import com.danding.cds.web.listener.CustomsInitDtoListener;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * @program: cds-center
 * @description:
 * @author: 潘本乐（Belep）
 * @create: 2021-07-30 10:24
 **/
@Service
@Slf4j
@RefreshScope
public class StockRedisService {

    @Value("${batchCount:50}")
    private Integer batchCount;

    @DubboReference
    private CustomsInitService customsInitService;

    /**
     * 账册商品锁库前缀
     */
    private static final String ACCOUNT_GOODS_LOCK_PRE = "ccs:stock:goods_";

    @Autowired
    private StringRedisTemplate redisTemplate;


    /**
     * 海关库存初始化
     *
     * @return
     */
    public void accountFileParse(HttpServletRequest httpServletRequest, String bookId,Integer type,String beginDate,String endDate) throws IOException {

        // 数据流
        log.info("字符编码：----------" + httpServletRequest.getCharacterEncoding());
        httpServletRequest.setCharacterEncoding("UTF-8");
        MultipartHttpServletRequest multipartHttpServletRequest = (MultipartHttpServletRequest) httpServletRequest;
        MultipartFile multipartFile = multipartHttpServletRequest.getFile("file");

        ExcelReader excelReader = null;
        try {
            excelReader = EasyExcel.read(multipartFile.getInputStream(), CustomsInitDTO.class,
                    new CustomsInitDtoListener(customsInitService, bookId, type,beginDate,endDate,batchCount)).build();
            ReadSheet readSheet = EasyExcel.readSheet(0).build();
            excelReader.read(readSheet);
        } finally {
            if (excelReader != null) {
                // 这里千万别忘记关闭，读的时候会创建临时文件，到时磁盘会崩的
                excelReader.finish();
            }
        }
    }

    /**
     * 批量删除
     *
     * @param keys
     */
    public void batchDelKey(List<String> keys) {

        if (StringUtils.isEmpty(keys)) {
            return;
        }
        keys.forEach(z -> {
            deleteKey(z);
        });
    }


    /**
     * 删除库存redis
     *
     * @param key
     * @return
     */
    public Boolean deleteKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new ArgsErrorException("key不允许为空");
        }
        return redisTemplate.delete(this.getRedisKey(key));
    }

    private String getRedisKey(String key) {
        return ACCOUNT_GOODS_LOCK_PRE + key;
    }

    /**
     * 查询库存redis
     *
     * @param key
     * @return
     */
    public String qryKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new ArgsErrorException("key不允许为空");
        }
        return redisTemplate.opsForValue().get(this.getRedisKey(key));
    }


    /**
     * 查询库存redis 有效期
     *
     * @param key
     * @return
     */
    public Long ttlKey(String key) {
        if (StringUtils.isEmpty(key)) {
            throw new ArgsErrorException("key不允许为空");
        }
        return redisTemplate.getExpire(this.getRedisKey(key));
    }


    public void batchDelKeyAll() {
        redisTemplate.delete(redisTemplate.keys("ccs:stock:goods*"));
    }
}
