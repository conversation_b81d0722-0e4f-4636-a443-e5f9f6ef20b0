package com.danding.cds.web.promotion.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.promotion.api.rpc.TTPromotionOrderRpc;
import com.danding.cds.promotion.api.vo.TTPromotionDetailVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderPageVO;
import com.danding.cds.promotion.api.vo.TTPromotionOrderSearch;
import com.danding.cds.v2.bean.vo.req.TTPromotionAuditReqVo;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author: yousx
 * @Date: 2024/03/14
 * @Description:
 * @menu 促销管理
 */
@Slf4j
@DubboService
public class TTPromotionOrderWebRpcImpl implements TTPromotionOrderWebRpc {

    @DubboReference
    private TTPromotionOrderRpc promotionOrderRpc;
    @DubboReference
    private DownloadProcessService downloadProcessService;

    /**
     * 分页
     * @param search
     * @return
     * @path /promotion/order/paging
     */
    @Override
    @SoulClient(path = "/promotion/order/paging", desc = "分页")
    public RpcResult<ListVO<TTPromotionOrderPageVO>> paging(TTPromotionOrderSearch search) {
        return RpcResult.success(promotionOrderRpc.paging(search));
    }

    @Override
    @SoulClient(path = "/promotionOrder/audit", desc = "审核")
    public RpcResult<String> audit(TTPromotionAuditReqVo reqVo) {
        try {
            promotionOrderRpc.audit(reqVo);
            return RpcResult.success("审核成功");
        } catch (ArgsInvalidException ex) {
            log.error("promotionOrder audit error={}", ex.getMessage(), ex);
            return RpcResult.error("审核失败-" + ex.getMessage());
        } catch (Exception e) {
            log.error("promotionOrder audit error={}", e.getMessage(), e);
            return RpcResult.error("审核失败");
        }
    }

    /**
     * 导出
     *
     * @param search
     * @return
     * @path /promotion/order/exportItemExcel
     */
    @Override
    @SoulClient(path = "/promotion/order/exportItemExcel", desc = "导出")
    public RpcResult<String> exportItemExcel(TTPromotionOrderSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.PROMOTION_ORDER_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @path /promotionOrder/detail
     */
    @Override
    @SoulClient(path = "/promotionOrder/detail", desc = "详情")
    public RpcResult<TTPromotionDetailVO> detail(IdParam id) {
        try {
            return RpcResult.success(promotionOrderRpc.detail(id.getId()));
        } catch (ArgsInvalidException ex) {
            log.error("promotionOrder detail error={}", ex.getMessage(), ex);
            return RpcResult.error(ex.getMessage());
        } catch (Exception e) {
            log.error("promotionOrder detail e={}", e.getMessage(), e);
            return RpcResult.error("查看失败");
        }
    }


    /**
     * 活动类型列表
     *
     * @return
     * @path /promotionOrder/activityTypeList
     */
    @Override
    @SoulClient(path = "/promotionOrder/activityTypeList", desc = "活动类型列表")
    public RpcResult<List<SelectOptionVO<String>>> activityTypeList() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        List<String> list = promotionOrderRpc.activityTypeList();
        for (String s : list) {
            res.add(new SelectOptionVO<>(s, s));
        }
        return RpcResult.success(res);
    }

    /**
     * 促销类型列表
     *
     * @return
     * @path /promotionOrder/marketingTypeList
     */
    @Override
    @SoulClient(path = "/promotionOrder/marketingTypeList", desc = "促销类型列表")
    public RpcResult<List<SelectOptionVO<String>>> marketingTypeList() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        List<String> list = promotionOrderRpc.marketingTypeList();
        for (String s : list) {
            res.add(new SelectOptionVO<>(s, s));
        }
        return RpcResult.success(res);
    }


    /**
     * 卖家昵称列表
     * @path /promotionOrder/sellerNickList
     * @return
     */
    @Override
    @SoulClient(path = "/promotionOrder/sellerNickList", desc = "卖家昵称列表")
    public RpcResult<List<SelectOptionVO<String>>> sellerNickList() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        List<String> list = promotionOrderRpc.sellerNickList();
        for (String s : list) {
            res.add(new SelectOptionVO<>(s, s));
        }
        return RpcResult.success(res);
    }

    /**
     * 单据来源列表
     * @path /promotionOrder/orderSourceList
     * @return
     */
    @Override
    @SoulClient(path = "/promotionOrder/orderSourceList", desc = "单据来源列表")
    public RpcResult<List<SelectOptionVO<String>>> orderSourceList() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        List<String> list = promotionOrderRpc.orderSourceList();
        for (String s : list) {
            res.add(new SelectOptionVO<>(s, s));
        }
        return RpcResult.success(res);
    }
}
