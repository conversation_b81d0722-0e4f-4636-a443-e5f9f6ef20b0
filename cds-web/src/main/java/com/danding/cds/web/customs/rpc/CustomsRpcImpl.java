package com.danding.cds.web.customs.rpc;

import com.danding.cds.common.enums.HsFloatTypeEnums;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.enmus.CompanyQualifyEnum;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryReqVO;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryResVO;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.order.api.enums.OrderDeclareEntryType;
import com.danding.cds.order.api.service.CustomsStatusMappingService;
import com.danding.cds.web.checklist.rpc.param.RpcStringIdParam;
import com.danding.cds.web.customs.vo.CustomsHsCodeDetailVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService
@Slf4j
@RestController
public class CustomsRpcImpl implements CustomsRpc {

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsStatusMappingService customsStatusMappingService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CustomsDistrictService customsDistrictService;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listDistrict", desc = "获取所有口岸")
    public RpcResult<List<SelectOptionVO<String>>> listDistrict() {
        List<CustomsDistrictDTO> districts = customsDistrictService.listAll();
        List<SelectOptionVO<String>> result = districts.stream().map(item -> {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getName());
            return optionDTO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listCountry", desc = "获取所有国家")
    public RpcResult<List<SelectOptionVO<String>>> listCountry() {
        List<CustomsCountryDTO> dataList = customsCountryService.listAll();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsCountryDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listCountryV2", desc = "获取所有国家V2")
    public RpcResult<List<SelectOptionVO<String>>> listCountryV2() {
        return this.listCountry();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listCurrency", desc = "获取所有币制单位")
    public RpcResult<List<SelectOptionVO<String>>> listCurrency() {
        List<CustomsCurrencyDTO> dataList = customsCurrencyService.listAll();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsCurrencyDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listCurrencyV2", desc = "获取所有币制单位V2")
    public RpcResult<List<SelectOptionVO<String>>> listCurrencyV2() {
        return this.listCurrency();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listUom", desc = "获取所有单位")
    public RpcResult<List<SelectOptionVO<String>>> listUom() {
        List<CustomsUomDTO> dataList = customsUomService.listAll();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsUomDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listUomWithName", desc = "获取所有单位-单位+名称")
    public RpcResult<List<SelectOptionVO<String>>> listUomWithName() {
        List<CustomsUomDTO> dataList = customsUomService.listAll();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsUomDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getCode() + ":" + item.getName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listUomV2", desc = "获取所有单位V2")
    public RpcResult<List<SelectOptionVO<String>>> listUomV2() {
        return this.listUom();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listHs", desc = "获取所有HS")
    public RpcResult<List<SelectOptionVO<String>>> listHs() {
        List<CustomsHsDTO> dataList = customsHsService.listEnable();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsHsDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getHsCode());
            optionDTO.setName(item.getHsName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listHsWithName", desc = "获取所有HS")
    public RpcResult<List<SelectOptionVO<String>>> listHsWithName() {
        List<CustomsHsDTO> dataList = customsHsService.listEnable();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsHsDTO item : dataList) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getHsCode());
            optionDTO.setName(item.getHsCode() + ":" + item.getHsName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listHsV2", desc = "获取所有HSV2")
    public RpcResult<List<SelectOptionVO<String>>> listHsV2() {
        return this.listHs();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/hsTaxDetail", desc = "获取所有HS含税详情")
    public RpcResult<CustomsHsCodeDetailVO> hsTaxDetail(RpcStringIdParam id) {
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(id.getId());
        CustomsHsCodeDetailVO customsHsCodeDetailVO = new CustomsHsCodeDetailVO();
        BeanUtils.copyProperties(customsHsDTO, customsHsCodeDetailVO);
        if (customsHsCodeDetailVO.getFloatType() != null) {
            HsFloatTypeEnums floatTypeEnums = HsFloatTypeEnums.getEnum(customsHsCodeDetailVO.getFloatType());
            customsHsCodeDetailVO.setFloatTypeDesc(floatTypeEnums != null ? floatTypeEnums.getDesc() : "");
        }
        return RpcResult.success(customsHsCodeDetailVO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/hsTaxDetailV2", desc = "获取所有HS含税详情V2")
    public RpcResult<CustomsHsCodeDetailVO> hsTaxDetailV2(RpcStringIdParam id) {
        return this.hsTaxDetail(id);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listEntryCompany", desc = "获取所有报关企业")
    public RpcResult<List<SelectOptionVO<Long>>> listEntryCompany() {
        List<CompanyDTO> companyDTOS = companyService.listWithQualify(CompanyQualifyEnum.BGQY.getCode());
        List<SelectOptionVO<Long>> result = new ArrayList<>();
        for (CompanyDTO item : companyDTOS) {
            SelectOptionVO<Long> optionDTO = new SelectOptionVO<>();
            optionDTO.setId(item.getId());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listEntryCompanyV2", desc = "获取所有报关企业V2")
    public RpcResult<List<SelectOptionVO<Long>>> listEntryCompanyV2() {
        return this.listEntryCompany();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listEntryType", desc = "获取所有报关类型")
    public RpcResult<List<SelectOptionVO<Integer>>> listEntryType() {
        List<OrderDeclareEntryType> list = Arrays.stream(OrderDeclareEntryType.values()).filter(o -> !o.equals(OrderDeclareEntryType.NULL)).collect(Collectors.toList());
        List<SelectOptionVO<Integer>> result = new ArrayList<>();
        for (OrderDeclareEntryType item : list) {
            SelectOptionVO<Integer> optionDTO = new SelectOptionVO<>();
            optionDTO.setId(item.getValue());
            optionDTO.setName(item.getDesc());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listEntryTypeV2", desc = "获取所有报关企业V2")
    public RpcResult<List<SelectOptionVO<Integer>>> listEntryTypeV2() {
        return this.listEntryType();
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listCustomsDistrict", desc = "获取所有口岸")
    public RpcResult<List<SelectOptionVO<String>>> listCustomsDistrict() {
        List<CustomsDistrictEnum> list = Arrays.stream(CustomsDistrictEnum.values()).filter(o -> !o.equals(CustomsDistrictEnum.NULL)).collect(Collectors.toList());
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsDistrictEnum item : list) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getDesc());
            optionDTO.setName(item.getDesc());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listCustomsDistrictV2", desc = "获取所有口岸V2")
    public RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictV2() {
        return this.listCustomsDistrict();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customs/listCustomsDistrictCode", desc = "获取所有口岸")
    public RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictCode() {
        List<CustomsDistrictEnum> list = Arrays.stream(CustomsDistrictEnum.values()).filter(o -> !o.equals(CustomsDistrictEnum.NULL)).collect(Collectors.toList());
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (CustomsDistrictEnum item : list) {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getDesc());
            result.add(optionDTO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customs/listCustomsDistrictCodeV2", desc = "获取所有口岸V2")
    public RpcResult<List<SelectOptionVO<String>>> listCustomsDistrictCodeV2() {
        return this.listCustomsDistrictCode();
    }

    @Override
    @SoulClient(path = "/customs/dictionary/paging", desc = "字典数据分页")
    @PostMapping("/customs/dictionary/paging")
    @Deprecated
    public RpcResult<ListVO<CustomsDictionaryResVO>> paging(@RequestBody CustomsDictionaryReqVO search) {
        if (Objects.isNull(search)) {
            return RpcResult.error("参数为空");
        }
        ListVO<CustomsDictionaryResVO> result = customsDictionaryService.paging(search);
        return RpcResult.success(result);
    }

    @Override
    @GetMapping("/customs/dictionary/insert")
    @SoulClient(path = "/customs/dictionary/insert", desc = "新增字典数据")
    @Deprecated
    public RpcResult<String> insert(@RequestBody CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.insert(param);
        } catch (ArgsErrorException e) {
            log.warn("插入失败: " + e.getMessage());
            return RpcResult.error("插入失败: " + e.getMessage());
        }
        return RpcResult.success("新增成功");
    }

    @Override
    @GetMapping("/customs/dictionary/update")
    @SoulClient(path = "/customs/dictionary/update", desc = "修改字典数据")
    @Deprecated
    public RpcResult<String> update(@RequestBody CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.update(param);
        } catch (ArgsErrorException e) {
            log.warn("编辑失败: " + e.getMessage());
            return RpcResult.error("编辑失败: " + e.getMessage());
        }
        return RpcResult.success("编辑成功");
    }

    @Override
    @GetMapping("/customs/dictionary/delete")
    @SoulClient(path = "/customs/dictionary/delete", desc = "删除字典数据")
    @Deprecated
    public RpcResult<String> delete(@RequestBody IdParam id) {
        if (Objects.isNull(id)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.delete(id.getId());
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
        return RpcResult.success("删除成功");
    }

    @Deprecated
    @Override
    @SoulClient(path = "/customs/dictionary/type", desc = "字典数据类型")
    public RpcResult listDictionaryType() {
        List<SelectOptionVO<String>> listDictionaryType = customsDictionaryService.listByType(DataDictionaryTypeEnums.TYPE.getValue());
        return RpcResult.success(listDictionaryType);
    }

    @Override
    @SoulClient(path = "/customs/dictionary/port", desc = "下拉列表")
    public RpcResult listDictionaryPort() {
        try {
            List<SelectOptionVO<String>> listPort = customsDictionaryService.listByType(DataDictionaryTypeEnums.PORT.getValue());
            return RpcResult.success(listPort);
        } catch (Exception e) {
            log.error("listDictionaryPort error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询关区列表失败");
    }

    @Override
    @SoulClient(path = "/customs/dictionary/guaranteeCustoms", desc = "保函关区下拉列表")
    public RpcResult listDictionaryGuaranteeCustoms() {
        try {
            List<SelectOptionVO<String>> listPort = customsDictionaryService.listByType(DataDictionaryTypeEnums.GUARANTEE_CUSTOMS.getValue());
            return RpcResult.success(listPort);
        } catch (Exception e) {
            log.error("listDictionaryGuaranteeCustoms error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询关区列表失败");
    }

}
