package com.danding.cds.web.payChannel.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSubmit;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSubmit;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountChannelResult;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface PayMerchantRpc {
    RpcResult<ListVO<PayMerchantAccountResult>> paging(PayMerchantAccountSearch search);

    RpcResult<Long> upset(PayMerchantAccountSubmit submit);

    RpcResult<ListVO<PayMerchantAccountChannelResult>> pagingChannel(PayMerchantAccountChannelSearch search);

    RpcResult<Long> upsetChannel(PayMerchantAccountChannelSubmit submit);

    RpcResult<PayMerchantAccountChannelResult> getById(IdParam idParam);
}
