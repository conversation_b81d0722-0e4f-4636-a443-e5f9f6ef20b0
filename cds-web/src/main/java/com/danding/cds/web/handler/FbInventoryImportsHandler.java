package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.download.api.vo.FbInventoryExcelVO;
import com.danding.cds.v2.api.FbInventoryService;
import com.danding.cds.v2.bean.dto.FbInventoryDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.validation.Validator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 非保库存记录
 * 1.导入成功，更新库位和批次号
 * 2.根据账册编号和账册项号和料号查，不存在则提示“未找到对应库存记录”
 * 3.根据账册编号和账册项号和料号查，有多条则提示“存在重复库存记录”
 * @date 2023/2/22 20:35
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_FB_INVENTORY", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%9B%B4%E6%96%B0%E5%BA%93%E4%BD%8D%E5%AF%BC%E5%85%A5v1.0.xls",
        groups = {@ParkImportsHandler.Group(name = "更新库位导入", classes = FbInventoryExcelVO.class),})
public class FbInventoryImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<FbInventoryExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("更新库位导入")) {
                list = group.getDataList(FbInventoryExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        FbInventoryService fbInventoryService = this.getBean(FbInventoryService.class);
        int index = 1;
        for (FbInventoryExcelVO excelVO : list) {
            String result = this.doValidator(excelVO);
            ImportResultResVo resultResVo = new ImportResultResVo();
            if (StringUtils.hasText(result)) {
                resultResVo.setFlag(false);
                resultResVo.setReason(result);
            } else {
                FbInventoryDTO fbInventoryDTO = ConvertUtil.beanConvert(excelVO, FbInventoryDTO.class);
                resultResVo = fbInventoryService.checkAndImport(fbInventoryDTO);
                log.info("料号:{} 导入后的返回结果数据{}", excelVO.getProductId(), resultResVo.toString());
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }
}
