package com.danding.cds.web.checklist.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class ChecklistExportVO implements Serializable {
    @Excel(name = "预录入核放编号", needMerge = true)
    private String orderNo;

    @Excel(name = "核放单号", needMerge = true)
    private String realOrderNo;

    @Excel(name = "车辆信息", needMerge = true)
    private String licensePlate;

    @Excel(name = "快递名称", needMerge = true)
    private String carrierName;

    @Excel(name = "创建时间", needMerge = true)
    private String createAt;

    @Excel(name = "录入时间", needMerge = true)
    private String enterFinishAt;

    @Excel(name = "状态", needMerge = true)
    private String checklistStatus;

    @Excel(name = "工作台号", needMerge = true)
    private String stationNo;

    @ExcelCollection(name = "")
    private List<ChecklistGroupExportVO> groupExportVOList;
}
