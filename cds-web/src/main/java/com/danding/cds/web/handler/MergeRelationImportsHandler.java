package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.download.api.vo.MergeRelationExcelVO;
import com.danding.cds.v2.api.MergeRelationService;
import com.danding.cds.v2.bean.dto.MergeRelationDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsGroup;
import com.danding.park.client.core.imports.bean.ImportsHandlerInfo;
import com.danding.park.client.core.imports.bean.ImportsHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @description: 归并关系导入
 * @date 2023/2/22 19:15
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_MERGE_RELATION", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%BD%92%E5%B9%B6%E5%85%B3%E7%B3%BB%E5%AF%BC%E5%85%A5v2.0.xls",
        groups = {@ParkImportsHandler.Group(name = "归并关系导入", classes = MergeRelationExcelVO.class),})
public class MergeRelationImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<MergeRelationExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("归并关系导入")) {
                list = group.getDataList(MergeRelationExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        MergeRelationService mergeRelationService = this.getBean(MergeRelationService.class);
        int index = 1;
        for (MergeRelationExcelVO excelVO : list) {
            ImportResultResVo resultResVo = new ImportResultResVo();
            String result = this.doValidator(excelVO);
            if (StringUtils.hasText(result)) {
                resultResVo.setFlag(false);
                resultResVo.setReason(result);
            } else {
                MergeRelationDTO mergeRelationDTO = ConvertUtil.beanConvert(excelVO, MergeRelationDTO.class);
                resultResVo = mergeRelationService.checkAndImport(mergeRelationDTO);
                log.info("料号:{} 导入后的返回结果数据{}", excelVO.getProductId(), resultResVo.toString());
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }

    public List<ImportsDataGroup> readData() {
        ImportsHandlerInfo handlerInfo = this.getHandlerInfo();
        if (handlerInfo == null) {
            return null;
        } else {
            List<ImportsGroup> handlerGroups = handlerInfo.getGroups();
            int total = 0;
            List<ImportsDataGroup> groups = new ArrayList();

            ImportsDataGroup importsDataGroup;
            for (Iterator var5 = handlerGroups.iterator(); var5.hasNext(); total += importsDataGroup.total()) {
                ImportsGroup handlerGroup = (ImportsGroup) var5.next();
                String name = handlerGroup.getName();
                Class<?> clazz = ClassUtil.loadClass(handlerGroup.getClassName());
                List<ImportsHeader> headerList = handlerGroup.getHeader();
                ExcelReader reader = ExcelUtil.getReader(this.download(), name);
                if (headerList != null && headerList.size() > 0) {
                    Iterator var11 = headerList.iterator();

                    while (var11.hasNext()) {
                        ImportsHeader importsHeader = (ImportsHeader) var11.next();
                        reader.addHeaderAlias(importsHeader.getName(), importsHeader.getKey());
                    }
                }

                importsDataGroup = new ImportsDataGroup();
                importsDataGroup.setName(name);
                importsDataGroup.setDataList(reader.readAll(clazz));
                groups.add(importsDataGroup);
            }

            this.updateTotal((String) null, total);
            return groups;
        }
    }
}
