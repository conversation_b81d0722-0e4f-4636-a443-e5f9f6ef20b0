package com.danding.cds.web.payChannel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 9:54
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountResult {
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码")
    private String sn;

    /**
     * 商户名称
     */
    @ApiModelProperty(value = "商户名称")
    private String name;

    /**
     * 收款企业
     */
    @ApiModelProperty(value = "收款企业")
    private String companyName;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
