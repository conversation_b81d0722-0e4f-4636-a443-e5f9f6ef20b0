package com.danding.cds.web.v2.api;


import com.danding.cds.common.model.IdEnableParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookDetailEditReq;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.cds.web.v2.bean.vo.req.*;
import com.danding.cds.web.v2.bean.vo.res.*;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

/**
 * <p>
 * 加工贸易账册 服务类
 * </p>
 *
 * <AUTHOR>
 * @menu 加工贸易账册
 * @since 2025-05-19
 */
public interface ProcessTradeBookWebRpc {

    RpcResult<ListVO<ProcessTradeBookVO>> paging(ProcessTradeBookSearch search);

    @SoulClient(path = "/processTradeBook/create", desc = "新增")
    RpcResult create(ProcessTradeBookCreateReq reqVO);

    @SoulClient(path = "/processTradeBook/enable", desc = "启用禁用")
    RpcResult enable(IdEnableParam idEnableParam);

    @SoulClient(path = "/processTradeBook/updateStatus", desc = "修改数据状态")
    RpcResult updateStatus(ProcessTradeBookUpdStatusReq param);

    @SoulClient(path = "/processTradeBook/detail", desc = "详情")
    RpcResult<ProcessTradeBookDetailVO> detail(Long id);

    @SoulClient(path = "/processTradeBook/edit", desc = "详情编辑")
    RpcResult<String> editDetail(ProcessTradeBookDetailEditReq editReq);

    @SoulClient(path = "/processTradeBook/matchItem", desc = "匹配申报表体")
    RpcResult<List<ProcessTradeBookItemVO>> matchItem(ProcessTradeBookMatchItemReqVO reqVO);

    @SoulClient(path = "/processTradeBook/item/paging", desc = "表体信息分页查询")
    RpcResult<ListVO<ProcessTradeBookItemVO>> itemPaging(ProcessTradeBookItemSearch param);

    @SoulClient(path = "/processTradeBook/item/createAndEdit", desc = "表体新增")
    RpcResult<String> createOrEditItem(ProcessTradeBookItemCreateReq param);

    @SoulClient(path = "/processTradeBook/item/delete", desc = "表体删除")
    RpcResult deleteItem(IdsParam param);

    @SoulClient(path = "/processTradeBook/consumption/paging", desc = "单损耗分页查询")
    RpcResult<ListVO<ProcessTradeBookConsumptionVO>> pagingConsumption(ProcessTradeBookConsumptionSearch param);

    @SoulClient(path = "/processTradeBook/consumption/createOrEdit", desc = "单损耗新增/编辑")
    RpcResult<String> createOrEditConsumption(ProcessTradeBookConsumptionCreateReq param);

    @SoulClient(path = "/processTradeBook/consumption/delete", desc = "单损耗删除")
    RpcResult<String> deleteConsumption(IdsParam param);

    @SoulClient(path = "/processTradeBook/item/export", desc = "加贸保税账册导出")
    RpcResult<String> export(ProcessTradeBookItemSearch param);

    @SoulClient(path = "/processTradeBook/listStatus", desc = "状态下拉")
    RpcResult<List<EnumUtils>> listStatus();

    @SoulClient(path = "/processTradeBook/listBookType", desc = "账册类型下拉")
    RpcResult<List<EnumUtils>> listBookType();

    @SoulClient(path = "/processTradeBook/listBookUsage", desc = "账册用途下拉")
    RpcResult<List<EnumUtils>> listBookUsage();

    @SoulClient(path = "/processTradeBook/listDeclareStatus", desc = "单耗申报状态下拉")
    RpcResult<List<EnumUtils>> listDeclareStatus();

    @SoulClient(path = "/processTradeBook/listConsumptionDeclarationLink", desc = "单耗申报环节下拉")
    RpcResult<List<EnumUtils>> listConsumptionDeclarationLink();

    @SoulClient(path = "/processTradeBook/listCompanyExecutionFlag", desc = "企业执行标志下拉")
    RpcResult<List<EnumUtils>> listCompanyExecutionFlag();

    @SoulClient(path = "/processTradeBook/listCustomsExecutionFlag", desc = "海关执行标志下拉")
    RpcResult<List<EnumUtils>> listCustomsExecutionFlag();

    @SoulClient(path = "/processTradeBook/listFocusMark", desc = "重点商品标识下拉")
    RpcResult<List<EnumUtils>> listFocusMark();

    @SoulClient(path = "/processTradeBook/listConsumptionQuestionFlag", desc = "单耗质疑标志下拉")
    RpcResult<List<EnumUtils>> listConsumptionQuestionFlag();

    @SoulClient(path = "/processTradeBook/listNegotiationFlag", desc = "磋商标志下拉")
    RpcResult<List<EnumUtils>> listNegotiationFlag();

    @SoulClient(path = "/processTradeBook/listQtyControlFlag", desc = "数量控制标志下拉")
    RpcResult<List<EnumUtils>> listQtyControlFlag();

    @SoulClient(path = "/processTradeBook/listDeclareCompanyType", desc = "申报企业类型下拉")
    RpcResult<List<EnumUtils>> listDeclareCompanyType();

    @SoulClient(path = "/processTradeBook/listVoucherClearanceMethod", desc = "核销方式下拉")
    RpcResult<List<EnumUtils>> listVoucherClearanceMethod();

    @SoulClient(path = "/processTradeBook/listVoucherClearanceType", desc = "核销类型下拉")
    RpcResult<List<EnumUtils>> listVoucherClearanceType();

    RpcResult<List<SelectOptionVO<Integer>>> listUpdateStatus();

    RpcResult<List<EnumUtils>> listDutyExemptionMethod();

    @SoulClient(path = "/processTradeBook/listEndprdSeqById", desc = "成品序号下拉")
    RpcResult<List<ProcessTradeBookItemSelectVO>> listEndprdSeqById(Long id);

    @SoulClient(path = "/processTradeBook/listMtpckSeqById", desc = "料件序号下拉")
    RpcResult<List<ProcessTradeBookItemSelectVO>> listMtpckSeqById(Long id);
}

