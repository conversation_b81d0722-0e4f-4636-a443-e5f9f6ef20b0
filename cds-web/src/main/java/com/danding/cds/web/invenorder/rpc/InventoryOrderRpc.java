package com.danding.cds.web.invenorder.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParamById;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.vo.req.InventoryOrderTransitReqVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderRelationResVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderStatusCountResVO;
import com.danding.cds.item.api.dto.CustomsGoodsItemInfoDTO;
import com.danding.cds.v2.bean.vo.req.FbChecklistAddReqVO;
import com.danding.cds.v2.bean.vo.req.InventoryBatchUrgentProcessReqVo;
import com.danding.cds.v2.bean.vo.req.InventoryOrderEditReqVo;
import com.danding.cds.v2.bean.vo.req.InventoryUrgentSortSaveReqVo;
import com.danding.cds.v2.bean.vo.res.InventoryOrderCountReqVo;
import com.danding.cds.v2.bean.vo.res.InventoryOrderInfoTransferorListResVO;
import com.danding.cds.v2.bean.vo.res.InventoryUrgentProcessResVO;
import com.danding.cds.web.checklist.rpc.param.RpcIdParam;
import com.danding.cds.web.invenorder.rpc.param.RpcAttachParam;
import com.danding.cds.web.invenorder.rpc.param.RpcIdsParam;
import com.danding.cds.web.invenorder.rpc.param.RpcRelParam;
import com.danding.cds.web.invenorder.rpc.param.SeqNoReqVO;
import com.danding.cds.web.invenorder.vo.*;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderAttachDownloadReq;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderAuditReqVo;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderCreateReqVo;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderItemGoodsEditVO;
import com.danding.cds.web.v2.bean.vo.res.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.soul.client.common.result.RpcResult;

import java.math.BigDecimal;
import java.util.List;

public interface InventoryOrderRpc {
    RpcResult<List<SelectOptionVO>> listBussinessType();

    RpcResult<List<SelectItemVO>> listTransport();

    RpcResult<List<SelectOptionVO>> listStatus();

    RpcResult<List<SelectOptionVO>> listRelationStatus();

    RpcResult<List<SelectOptionVO>> listCanInvertory();

    RpcResult<List<SelectOptionVO>> listCanInvertoryAuth();

    RpcResult<List<SelectOptionVO>> listBusinessTypeById(Long inventoryOrderId);

    @Deprecated
    RpcResult<Response<InventoryOrderInfoDTO>> createInventoryOrder(InventoryOrderInfoDTO infoDTO);

    /**
     * 新建清关单V2
     * V1版本用dto作为接收参数不合适
     *
     * @param reqVo
     * @return
     */
    RpcResult<String> createInventoryOrderV2(InventoryOrderCreateReqVo reqVo);

    RpcResult<Response<InventoryOrderInfoDTO>> buildInventoryOrderItem(InventoryOrderItemsParamV2 param);

    RpcResult editInventoryHead(InventoryOrderEditReqVo editReqVo);

    /**
     * 重新生成清关单表体
     *
     * @param idParam
     * @return
     */
    RpcResult regenerateItem(IdParam idParam);

    RpcResult generateEndorsement(IdParam idParam);

    /**
     * 生成调入调出清关单
     *
     * @param idParam
     * @return
     */
    RpcResult<String> generateTransitInOutOrder(IdParam idParam);

    RpcResult deleteInventoryOrderRelation(RpcIdsParam idsParam);

    RpcResult updateInventoryOrderRelation(RpcRelParam rpcRelParam);

    RpcResult audit(InventoryOrderAuditReqVo auditReqVo);

    @Deprecated
    RpcResult<ListVO<InventoryOrderInfoVO>> paging(InventoryOrderSearch search);

    RpcResult<Response<Boolean>> traceLogDownload(RpcIdsParam idsParam);

    RpcResult<Response<String>> discard(RpcIdsParam idsParam);

    RpcResult<InventoryOrderViewInfoVO> viewInventoryOrder(RpcIdsParam idsParam);

    RpcResult<InventoryOrderViewInfoV2VO> detail(RpcIdParam idParam);

    RpcResult<List<InventoryOrderItemDTO>> viewInventoryOrderItem(RpcIdParam idParam);

    RpcResult<List<InventoryOrderEndorsementVO>> viewInventoryOrderEndorsement(RpcIdParam idParam);

    RpcResult<List<InventoryOrderRelationDTO>> viewInventoryOrderRelation(RpcIdParam idParam);

    RpcResult<List<InventoryOrderAttachVO>> viewInventoryOrderAttach(IdParam idParam);

    RpcResult<Response<Boolean>> deleteAttach(RpcAttachParam rpcAttachParam);

    RpcResult<Response<Boolean>> uploadAttach(Long id, String url, String fileName, String attachType);

    RpcResult<String> downloadAttach(InventoryOrderAttachDownloadReq req);

    RpcResult<List<SelectOptionVO<String>>> listAttachType();

    RpcResult buildInventoryOrderRelation(InventoryOrderRelationParam param);

    RpcResult<InventoryOrderRelationResVO> preBuildInventoryOrderRelation(InventoryOrderRelationParam param);

    RpcResult<InventoryOrderItemWritingReport> preImport(UrlParamById urlParam);

    RpcResult<List<CustomsGoodsItemInfoDTO>> importExcel(InventoryOrderItemImportSubmit submit);

    /**
     * 根据料号找出序号
     *
     * @param productId
     * @return
     */
    RpcResult<List<SelectOptionVO>> listSeqNoByProductId(String productId, Long areaBookId);

    RpcResult<List<SeqNoReqVO>> listSeqNoByProductIdV2(String productId, Long areaBookId);

    RpcResult<List<SelectOptionVO>> listProductId(String originProductId, Long areaBookId);

    /**
     * 料号信息列表
     *
     * @param productIds 料号列表
     * @param areaBookId 账册ID
     * @return
     */
    RpcResult<List<ProductInfoVo>> listProductInfos(List<String> productIds, Long areaBookId);


    /**
     * 根据账册库存id生成清关单表体信息
     *
     * @param bookItemId
     * @return
     */
    RpcResult<InventoryOrderItemDTO> getInventoryOrderItemByItemId(Long bookItemId);

    RpcResult<InventoryOrderItemDTO> getInventoryOrderItemByItemIdV2(Long bookItemId, String originProductId, Long bookId, String oldOrNew);


    RpcResult<Response<String>> adminDiscard(RpcIdsParam idsParam);

    /**
     * 根据理货报告id获取明细
     *
     * @param idsParam
     * @return
     */
    RpcResult<List<InventoryOrderTallyReportDetailVO>> getTallyReportDetailById(RpcIdsParam idsParam);

    /**
     * 分页查询v2（正在使用）
     *
     * @param search
     * @return
     */
    RpcResult<ListVO<InventoryOrderInfoVO>> pagingV2(InventoryOrderSearchV2 search);

    RpcResult<Response<String>> mergeCheck(InventoryOrderMergeParam param);

    RpcResult<Response<InventoryOrderInfoDTO>> mergeInventoryOrder(InventoryOrderMergeParam param);

    RpcResult<Response<String>> unmerge(RpcIdsParam idsParam);

    RpcResult<InventoryOrderInfoDTO> getMasterOrderInfoBySn(String masterOrderSn);

    RpcResult<FlowStateResVo> getFlowState(IdParam idParam);

    RpcResult<String> exportItemExcel(IdParam idParam);

    RpcResult<String> exportItemGoodsExcel(IdParam idParam);

    RpcResult<String> exportExcel(InventoryOrderSearchV2 search);

    RpcResult<List<InventoryOrderCountReqVo>> inventoryStatusCount(IdParam idParam);

    /**
     * 获取ERP保税货主列表
     *
     * @return
     */
    RpcResult<List<SelectOptionVO>> listErpOwner();

    RpcResult<List<SelectOptionVO<Integer>>> listChannel();

    /**
     * 手动清关按钮
     *
     * @param
     * @return
     */
    RpcResult<String> manualDeal(Long id, String associatedInventorySn);

    RpcResult<String> manualFinish(Long id);

    /**
     * 下拉清关单标记列表
     *
     * @return
     */
    RpcResult<List<SelectOptionVO<Integer>>> listInventoryOrderTag();

    RpcResult<List<SelectOptionVO<Integer>>> listInventoryOrderTodoTag();

    /**
     * 下拉货代公司接口
     *
     * @return
     */
    RpcResult<List<SelectOptionVO<String>>> listFreightForwardingCompany();

    /**
     * 下拉到货港口/机场接口
     */
    RpcResult<List<SelectOptionVO<String>>> listArrivePort();

    /**
     * 下拉类目
     */
    RpcResult<List<SelectOptionVO<String>>> listCategory();

    /**
     * 获取WMS打托明细
     *
     * @param idParam
     * @return
     */
    RpcResult<List<WmsTallyDetailResVo>> getWmsTallyDetail(IdParam idParam);

    RpcResult<String> uploadCustomsEntryAttach(InventoryOrderCustomsEntryAttachVO attachVO);

    RpcResult listCallbackStatus();

    RpcResult<String> deleteCustomsEntryAttach(Long id);

    RpcResult retryCallBack(IdsParam ids);

    RpcResult<List<InventoryOrderStatusCountResVO>> getStatusCount(InventoryOrderSearchV2 searchV2);

    RpcResult<String> startTransit(InventoryOrderTransitReqVO reqVO);

    RpcResult<String> returnTransit(IdParam idParam);

    RpcResult<String> resendMail(IdParam idParam);

    RpcResult<String> auditMail(InventoryOrderAuditMailReqVO reqVO);

    RpcResult<String> retryCallbackRejectOrder(IdParam idParam);

    RpcResult<InventoryOrderInfoTransferorListResVO> getMailDetailByOrderNo(String orderNo);

    /**
     * 更新车辆信息
     *
     * @param inventoryOrderInfoDTO
     * @return
     */
    RpcResult<String> updateInventoryOrderCarInformation(InventoryOrderInfoDTO inventoryOrderInfoDTO);

    RpcResult<String> updateInventoryCompanyAndBook(Long id, Long companyId, Long bookId, Integer needCarryOver);

    RpcResult<String> generateFbChecklist(FbChecklistAddReqVO reqVO);

    RpcResult<List<SelectOptionVO>> listFbChecklistTypeByBusiType(String businessType);

    RpcResult<List<SelectOptionVO<String>>> listSelfBusinessType();

    RpcResult<List<List<InventoryVerifyResult>>> itemVerify(InventoryOrderItemsParamV2 param);

    RpcResult<String> updateStatusById(InventoryOrderUpdateDTO inventoryOrderUpdateDTO);

    RpcResult<List<SelectOptionVO<String>>> listModifyStatus();

    RpcResult callWmsGenerateCarryOver();

    RpcResult getItemLogisticsInfo(Long id);

    RpcResult<Boolean> updInventoryDetailHead(InventoryOrderDetailHeadEditVO editVO);

    RpcResult<Boolean> updInventoryDetailTransport(InventoryOrderDetailTransportEditVO editVO);

    RpcResult<Boolean> updInventoryDetailCustoms(InventoryOrderDetailCustomsEditVO editVO);

    RpcResult<Boolean> updInventoryDetailOthers(InventoryOrderDetailOthersEditVO editVO);

    RpcResult<InventoryOrderAssociateTransferInfoVO> viewAssociateTransferInfo(InventoryOrderAssociateTransferReqVO reqVO);

    RpcResult<Boolean> associateTransfer(InventoryOrderAssociateTransferReqVO reqVO);

    RpcResult<Boolean> disassociateTransfer(RpcIdParam idParam);

    RpcResult<List<DraftItemCompareResVO>> compareDraftItemList(Long id);

    RpcResult<List<DraftItemCompareResVO>> compareDraftItemListV2(DraftCompareReqVO reqVO);

    RpcResult<List<String>> getDraftCompareFilterList();

    RpcResult<Boolean> updateItemListByDraftCompare(DraftCompareUpdateItemListVO vo);

    RpcResult<Boolean> confirmDraftV2(DraftConfirmReqVO reqVO);

    RpcResult<List<InvOrderItemCompareResVO>> compareItemList(DraftCompareReqVO reqVO);

    RpcResult<Boolean> updateItemListByCompare(DraftCompareUpdateItemListVO vo);

    RpcResult<List<InventoryOrderInfoTrackLogVo>> getInventoryOrderInfoTrackLogs(Long inventoryOrderInfoId);

    RpcResult<Boolean> confirmDraft(Long id);

    RpcResult<List<InventoryOrderItemDTO>> buildItemsByAssociatedTransit(Long id);

    RpcResult<Boolean> takeOrder(Long id);

    RpcResult<List<SelectOptionVO<Integer>>> listDraftCompareType();

    RpcResult<List<InventoryUrgentProcessResVO>> listUrgentProcess();

    RpcResult<String> saveUrgentSort(InventoryUrgentSortSaveReqVo reqVo);

    RpcResult<String> batchChangeUrgentProcessInventoryOrder(InventoryBatchUrgentProcessReqVo reqVo);

    RpcResult<CustomsGoodsItemInfoDTO> findEndPrdDetail(Long bizDeclareFormItemId, Long bookId, BigDecimal qty);

    RpcResult<List<InventoryOrderItemGoodsVO>> viewInventoryOrderItemGoodsList(Long inventoryOrderId);

    RpcResult<Boolean> editInventoryOrderItemGoods(InventoryOrderItemGoodsEditVO editVO);

    RpcResult<List<SelectOptionVO<String>>> listDeclareFormNo();

    RpcResult<List<SelectOptionVO<String>>> listDeclareFormNoByBookId(Long bookId);

    RpcResult<Boolean> releaseItemLockStockById(IdParam idParam);
}
