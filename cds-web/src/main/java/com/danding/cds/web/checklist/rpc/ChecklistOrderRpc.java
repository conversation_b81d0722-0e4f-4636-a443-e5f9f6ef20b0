package com.danding.cds.web.checklist.rpc;

import com.danding.cds.checklist.api.dto.ChecklistSearch;
import com.danding.cds.checklist.api.dto.ChecklistSubmit;
import com.danding.cds.checklist.api.dto.ChecklistTrackLogDTO;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.OTMCItemExcelVo;
import com.danding.cds.v2.bean.vo.req.ChecklistAssociateItemReqVo;
import com.danding.cds.v2.bean.vo.req.ChecklistItemSaveReqVo;
import com.danding.cds.v2.bean.vo.res.CheckListViewResVo;
import com.danding.cds.v2.bean.vo.res.ChecklistAssociateItemResVo;
import com.danding.cds.vehicle.api.dto.CheckListVehicleDTO;
import com.danding.cds.web.checklist.rpc.param.RpcIdParam;
import com.danding.cds.web.checklist.rpc.param.RpcModifyStatusParam;
import com.danding.cds.web.checklist.vo.ChecklistFinishVO;
import com.danding.cds.web.checklist.vo.ChecklistPagingResult;
import com.danding.cds.web.checklist.vo.EndorsementItemVO;
import com.danding.cds.web.checklist.vo.PagingStatusCountVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @menu: 核注核放接口
 */
public interface ChecklistOrderRpc {
    RpcResult<List<SelectOptionVO>> listStatus();

    /**
     * 手动修改状态下拉的枚举状态
     *
     * @param type 表类型
     * @return  状态枚举
     */
    RpcResult<List<SelectOptionVO>> listModifyStatus(String type);

    RpcResult<List<SelectOptionVO>> listType();

    RpcResult<List<SelectOptionVO>> bindType();

    RpcResult<Response<String>> checkListTypeMapEndorsementBussiness();

    RpcResult<List<SelectOptionVO>>listVehicle();

    RpcResult<CheckListVehicleDTO> getVehicleById(RpcIdParam param);

    RpcResult<Long> create(ChecklistSubmit submit);

    RpcResult<Long> upset(ChecklistSubmit submit);

    RpcResult<String> bindEndorsement(ChecklistSubmit submit);

    RpcResult<Response<String>> push(RpcIdParam param);

    RpcResult<Response<String>> pushTemporaryStorage(RpcIdParam param);

    RpcResult<ListVO<ChecklistPagingResult>> paging(ChecklistSearch search);

    RpcResult<Response<String>> finish(@RequestBody ChecklistFinishVO checklistFinishVO);

    RpcResult<Long> discard(@RequestBody IdParam idParam);

    RpcResult<Response<String>> handlerCheck(IdsParam checkIdsParam);

    RpcResult<List<EndorsementItemVO>> loadEndorsementItem(RpcIdParam idParam);

    RpcResult<CheckListViewResVo> viewCheckList(RpcIdParam idParam);

    /**
     * 核放单新增表体 通过序号关联
     *
     * @param reqVo
     * @return
     */
    RpcResult<ChecklistAssociateItemResVo> associateItemBySerialNumber(@RequestBody ChecklistAssociateItemReqVo reqVo);

    /**
     * 编辑核放单表体
     *
     * @param idParam
     * @return
     */
    RpcResult<ChecklistAssociateItemResVo> viewChecklistItem(RpcIdParam idParam);

    /**
     * 删除核放单表体
     */
    RpcResult<String> deleteChecklistItem(RpcIdParam idParam);

    /**
     * @description: 保存一票多车核放单表体
     * @param: reqVo
     * @return: RpcResult<String>
     * @data: 2021/11/15
     */
    RpcResult<String> saveChecklistItem(@RequestBody ChecklistItemSaveReqVo reqVo);

    ImportResultResVo saveChecklistItemExport(Long checklistId, Long endorsementId, OTMCItemExcelVo vo);

    /**
     * 批量导出
     *
     * @param idParam
     * @return
     */
    RpcResult<String> excelExport(RpcIdParam idParam);

    /**
     * 手动修改参数核放单状态
     *
     * @param param 待修改核放单传递参数
     * @return 操作结果
     * <AUTHOR>
     */
    RpcResult<String> manualUpdStatus(RpcModifyStatusParam param);

    RpcResult<List<PagingStatusCountVO>> countPagingStatus(ChecklistSearch search);

    RpcResult<List<ChecklistTrackLogDTO>> listTrackLog(Long checklistId);
}
