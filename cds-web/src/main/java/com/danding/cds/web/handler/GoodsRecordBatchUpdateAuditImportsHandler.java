package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.download.api.vo.GoodsRecordExportVO;
import com.danding.cds.item.api.dto.GoodsRecordAuditSubmit;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.GoodsRecordSubmit;
import com.danding.cds.item.api.dto.submit.GoodsRecordAuditSubmitV2;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsTaskInfo;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 商品备案 批量更新审核结果导入
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_GOODS_RECORD_BATCH_UPDATE_AUDIT", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E6%89%B9%E9%87%8F%E6%9B%B4%E6%96%B0%E5%AE%A1%E6%A0%B8%E7%BB%93%E6%9E%9C.xlsx",
        groups = {@ParkImportsHandler.Group(name = "商品备案", classes = GoodsRecordExportVO.class),})
public class GoodsRecordBatchUpdateAuditImportsHandler extends ImportsBaseHandler {
    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        // 引入spring的bean,用于业务处理
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        CustomsDistrictService customsDistrictService = this.getBean(CustomsDistrictService.class);
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);

        ImportsTaskInfo taskInfo = this.getTaskInfo();
        ImportsUserInfo userInfo = taskInfo.getUserInfo();
        Long userId = userInfo.getUserId();


        List<GoodsRecordExportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("商品备案")) {
                list = group.getDataList(GoodsRecordExportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<String> productIdList = list.stream().map(GoodsRecordExportVO::getProductId).filter(StrUtil::isNotBlank).collect(Collectors.toList());
        List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findByProId(productIdList);
        Map<String, GoodsRecordDTO> goodsRecordDTOMap = goodsRecordDTOList.stream()
                .filter(dto -> {
                    Integer tags = Objects.nonNull(dto.getGoodsRecordTag()) ? dto.getGoodsRecordTag() : 0;
                    return (GoodsRecordTagEnums.ERP_DELETE.getCode() & tags) != GoodsRecordTagEnums.ERP_DELETE.getCode();
                })
                .collect(Collectors.toMap(GoodsRecordDTO::getProductId, dto -> dto));
        List<CustomsDistrictDTO> customsDistrictDTOS = customsDistrictService.listAll();
        Map<String, CustomsDistrictDTO> customsMap = customsDistrictDTOS.stream().collect(Collectors.toMap(CustomsDistrictDTO::getName, dto -> dto));
        Map<String, CustomsDictionaryDTO> countryMap = new HashMap<>();
        Map<String, CustomsDictionaryDTO> uomMap = new HashMap<>();
        Map<String, CustomsDictionaryDTO> currencyMap = new HashMap<>();
        int index = 1;
        for (GoodsRecordExportVO vo : list) {
            try {
                String errorMsg = this.check(vo, goodsRecordDTOMap, customsMap, countryMap, uomMap, currencyMap, customsDictionaryService);
                if (!StringUtil.isBlank(errorMsg)) {
                    callbackData(false, index++, errorMsg, vo);
                    continue;
                }
                if (StrUtil.isNotBlank(vo.getGoodsRecordName())) {
                    vo.setGoodsRecordName(StringUtils.trimWhitespace(vo.getGoodsRecordName()));
                }
                GoodsRecordAuditSubmitV2 submit = buildSubmit(vo,
                        goodsRecordDTOMap, customsMap, countryMap, uomMap, currencyMap, customsDictionaryService);
                goodsRecordService.batchAuditByImport(submit, userId);
                this.callbackData(true, index, null, vo);
            } catch (Exception e) {
                if (e instanceof ArgsInvalidException) {
                    callbackData(false, index, e.getMessage(), vo);
                } else {
                    log.error("商品备案批量更新审核结果导入异常", e);
                    callbackData(false, index, "商品备案审核异常", vo);
                }
            }
            index++;
        }
    }

    private GoodsRecordAuditSubmitV2 buildSubmit(GoodsRecordExportVO vo,
                                                 Map<String, GoodsRecordDTO> goodsRecordDTOMap,
                                                 Map<String, CustomsDistrictDTO> customsMap,
                                                 Map<String, CustomsDictionaryDTO> countryMap,
                                                 Map<String, CustomsDictionaryDTO> uomMap,
                                                 Map<String, CustomsDictionaryDTO> currencyMap,
                                                 CustomsDictionaryService customsDictionaryService
    ) {

        GoodsRecordAuditSubmitV2 submitV2 = new GoodsRecordAuditSubmitV2();
        GoodsRecordAuditSubmit goodsRecordAuditSubmit = new GoodsRecordAuditSubmit();
        GoodsRecordSubmit goodsRecordSubmit = new GoodsRecordSubmit();
        GoodsRecordDTO goodsRecordDTO = goodsRecordDTOMap.get(vo.getProductId());
        submitV2.setErpGoodsRecordDTO(goodsRecordDTO);
        goodsRecordAuditSubmit.setId(goodsRecordDTO.getId());
        if (Objects.nonNull(vo.getCustoms()) && Objects.nonNull(customsMap.get(vo.getCustoms()))) {
            goodsRecordAuditSubmit.setCustomsCode(customsMap.get(vo.getCustoms()).getCode());
        }
        goodsRecordAuditSubmit.setOpinion(vo.getOpinion());
        goodsRecordAuditSubmit.setReason(vo.getReason());
        goodsRecordAuditSubmit.setProductId(vo.getProductId());
        goodsRecordAuditSubmit.setCountryRecordNo(vo.getCountryRecordNo());
        goodsRecordAuditSubmit.setExternalProductId(vo.getExternalProductId());
        goodsRecordAuditSubmit.setHsCode(vo.getHsCode());
        goodsRecordAuditSubmit.setFirstUnitAmount(vo.getFirstUnitAmount());
        if (StrUtil.isNotBlank(vo.getFirstUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
            goodsRecordAuditSubmit.setFirstUnit(uomDTO.getCode());
        }
        goodsRecordAuditSubmit.setSecondUnitAmount(vo.getSecondUnitAmount());
        if (StrUtil.isNotBlank(vo.getSecondUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
            goodsRecordAuditSubmit.setSecondUnit(uomDTO.getCode());
        }
        goodsRecordAuditSubmit.setGrossWeight(vo.getGrossWeight());
        goodsRecordAuditSubmit.setNetWeight(vo.getNetWeight());

        goodsRecordSubmit.setId(goodsRecordDTO.getId());
        goodsRecordSubmit.setSkuId(vo.getSkuId());
        goodsRecordSubmit.setProductId(vo.getProductId());
        goodsRecordSubmit.setBarCode(goodsRecordDTO.getBarCode());
        goodsRecordSubmit.setGoodsRecordName(vo.getGoodsRecordName());
        goodsRecordSubmit.setModel(vo.getModel());
        goodsRecordSubmit.setBrand(vo.getBrand());
        goodsRecordSubmit.setBrandEn(vo.getBrandEn());
        goodsRecordSubmit.setDeclarePrice(vo.getDeclarePrice());
        if (StrUtil.isNotBlank(vo.getDeclareUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getDeclareUnit(), customsDictionaryService);
            goodsRecordSubmit.setDeclareUnit(uomDTO.getCode());
        }
        if (StrUtil.isNotBlank(vo.getDeclareCurrency())) {
            CustomsDictionaryDTO currencyDTO = getDictionaryDTO(
                    currencyMap, DataDictionaryTypeEnums.CURRENCY.getValue(), vo.getDeclareCurrency(), customsDictionaryService);
            goodsRecordSubmit.setDeclareCurrency(currencyDTO.getCode());
        }
        goodsRecordSubmit.setNetWeight(vo.getNetWeight());
        goodsRecordSubmit.setGrossWeight(vo.getGrossWeight());
        goodsRecordSubmit.setLength(vo.getLength());
        goodsRecordSubmit.setWidth(vo.getWidth());
        goodsRecordSubmit.setHeight(vo.getHeight());
        goodsRecordSubmit.setHsCode(vo.getHsCode());
        goodsRecordSubmit.setVatRate(vo.getVatRate());
        goodsRecordSubmit.setTaxRate(vo.getTaxRate());
        goodsRecordSubmit.setComposition(vo.getComposition());
        goodsRecordSubmit.setHgsbys(vo.getHgsbys());
        if (StrUtil.isNotBlank(vo.getOriginCountry())) {
            CustomsDictionaryDTO countryDTO = getDictionaryDTO(
                    countryMap, DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getOriginCountry(), customsDictionaryService);
            goodsRecordSubmit.setOriginCountry(countryDTO.getCode());
        }
        goodsRecordSubmit.setRecordFunction(vo.getRecordFunction());
        goodsRecordSubmit.setRecordUsage(vo.getRecordUsage());
        goodsRecordSubmit.setWarehouseId(vo.getWareHouseName());
        if (StrUtil.isNotBlank(vo.getFirstUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
            goodsRecordSubmit.setFirstUnit(uomDTO.getCode());
        }
        goodsRecordSubmit.setFirstUnitAmount(vo.getFirstUnitAmount());
        if (StrUtil.isNotBlank(vo.getSecondUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
            goodsRecordSubmit.setSecondUnit(uomDTO.getCode());
        }
        goodsRecordSubmit.setSecondUnitAmount(vo.getSecondUnitAmount());
        goodsRecordSubmit.setEnable(goodsRecordDTO.getEnable());
        goodsRecordSubmit.setOpinion(vo.getOpinion());
        goodsRecordSubmit.setReason(vo.getReason());
        goodsRecordSubmit.setCountryRecordNo(vo.getCountryRecordNo());
        if (StrUtil.isNotBlank(vo.getCustoms()) && Objects.nonNull(customsMap.get(vo.getCustoms()))) {
            goodsRecordSubmit.setCustoms(vo.getCustoms());
            goodsRecordSubmit.setCustomsCode(customsMap.get(vo.getCustoms()).getCode());
        }
        goodsRecordSubmit.setGoodsCode(vo.getGoodsCode());
        goodsRecordSubmit.setExternalGoodsId(vo.getExternalGoodsId());
        goodsRecordSubmit.setCainiaoGoodsId(vo.getCainiaoGoodsId());


        submitV2.setGoodsRecordAuditSubmit(goodsRecordAuditSubmit);
        submitV2.setGoodsRecordSubmit(goodsRecordSubmit);
        return submitV2;
    }

    private CustomsDictionaryDTO getDictionaryDTO(Map<String, CustomsDictionaryDTO> uomMap, String type, String name,
                                                  CustomsDictionaryService customsDictionaryService) {
        return uomMap.computeIfAbsent(name, key -> {
            List<CustomsDictionaryDTO> dictionaryDTOList = customsDictionaryService.findByTypeAndName(type, key);
            return CollUtil.isNotEmpty(dictionaryDTOList) ? dictionaryDTOList.get(0) : null;
        });
    }


    /**
     * 校验excel转换对象后的数据准确性
     *
     * @param vo                       excel转换对象
     * @param goodsRecordDTOMap
     * @param customsMap
     * @param countryMap
     * @param uomMap
     * @param currencyMap
     * @param customsDictionaryService
     * @return 校验结果
     */
    public String check(GoodsRecordExportVO vo,
                        Map<String, GoodsRecordDTO> goodsRecordDTOMap, Map<String, CustomsDistrictDTO> customsMap,
                        Map<String, CustomsDictionaryDTO> countryMap,
                        Map<String, CustomsDictionaryDTO> uomMap,
                        Map<String, CustomsDictionaryDTO> currencyMap,
                        CustomsDictionaryService customsDictionaryService) {
        CustomsHsService customsHsService = this.getBean(CustomsHsService.class);
        List<String> errorMsg = new ArrayList<>();
        // 校验必填项 统一料号、备案名称、规格型号、HS编码、海关原产国、法一单位、法一数量、口岸、0-驳回 1-通过；
        if (StrUtil.isEmpty(vo.getProductId())) {
            errorMsg.add("统一料号不能为空");
        }
        if (!goodsRecordDTOMap.containsKey(vo.getProductId())) {
            errorMsg.add("统一料号不存在或ERP已删除");
        }
        if (StrUtil.isBlank(vo.getGoodsRecordName())) {
            errorMsg.add("备案名称不能为空");
        }
        if (StrUtil.isBlank(vo.getModel())) {
            errorMsg.add("规格型号不能为空");
        }
        if (StrUtil.isBlank(vo.getHsCode())) {
            errorMsg.add("HS编码不能为空");
        } else {
            CustomsHsDTO hsDTO = customsHsService.findByCode(vo.getHsCode());
            if (Objects.isNull(hsDTO)) {
                errorMsg.add("hs：" + vo.getHsCode() + "，不存在正面清单");
            } else {
                if (StrUtil.isNotBlank(hsDTO.getSecondLegalUnit())) {
                    if (StrUtil.isBlank(vo.getSecondUnit())) {
                        errorMsg.add("法二单位不能为空");
                    } else {
                        CustomsDictionaryDTO uomDTO = getDictionaryDTO(uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
                        if (Objects.isNull(uomDTO) || !Objects.equals(uomDTO.getCode(), hsDTO.getSecondLegalUnit())) {
                            errorMsg.add("法二单位与HS编码法定第二计量单位不符");
                        }
                    }
                    if (Objects.isNull(vo.getSecondUnitAmount())) {
                        errorMsg.add("法二数量不能为空");
                    }
                }
                CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                        uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
                if (Objects.isNull(uomDTO) || !Objects.equals(uomDTO.getCode(), hsDTO.getFirstLegalUnit())) {
                    errorMsg.add("法一单位与HS编码法定计量单位不符");
                }
            }
        }
        if (StrUtil.isBlank(vo.getOriginCountry())) {
            errorMsg.add("海关原产国不能为空");
        } else {
            CustomsDictionaryDTO countryDTO = getDictionaryDTO(
                    countryMap, DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getOriginCountry(), customsDictionaryService);
            if (Objects.isNull(countryDTO)) {
                errorMsg.add("海关原产国填写错误");
            }
        }
        if (StrUtil.isBlank(vo.getFirstUnit())) {
            errorMsg.add("法一单位不能为空");
        } else {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("法一单位填写错误");
            }
        }
        if (Objects.isNull(vo.getFirstUnitAmount())) {
            errorMsg.add("法一数量不能为空");
        }
        if (StrUtil.isBlank(vo.getCustoms())) {
            errorMsg.add("口岸不能为空");
        } else {
            CustomsDistrictDTO customsDistrictDTO = customsMap.get(vo.getCustoms());
            if (Objects.isNull(customsDistrictDTO)) {
                errorMsg.add("口岸填写错误");
            }
        }
        if (StrUtil.isNotBlank(vo.getDeclareCurrency())) {
            CustomsDictionaryDTO currencyDTO = getDictionaryDTO(
                    currencyMap, DataDictionaryTypeEnums.CURRENCY.getValue(), vo.getDeclareCurrency(), customsDictionaryService);
            if (Objects.isNull(currencyDTO)) {
                errorMsg.add("申报币制填写错误");
            }
        }
        if (Objects.isNull(vo.getOpinion())) {
            errorMsg.add("0-驳回 1-通过 不能为空");
        } else if (Objects.equals(0, vo.getOpinion())) {
            if (StrUtil.isBlank(vo.getReason())) {
                errorMsg.add("驳回原因不能为空");
            }
        }
        if (StrUtil.isNotBlank(vo.getDeclareUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getDeclareUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("申报单位不存在，请核实");
            }
        }
        if (StrUtil.isNotBlank(vo.getSecondUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("法二单位填写错误");
            }
            if (Objects.isNull(vo.getSecondUnitAmount())) {
                errorMsg.add("法二单位存在，法二数量不能为空");
            }
        }
        return String.join(";", errorMsg);
    }
}
