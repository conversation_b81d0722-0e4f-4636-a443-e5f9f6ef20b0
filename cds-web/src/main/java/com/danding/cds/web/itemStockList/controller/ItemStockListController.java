package com.danding.cds.web.itemStockList.controller;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.util.DateUtils;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.WechatNotifyUtils;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.enums.InventoryInOutEnum;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CarryforwardCustomsBookService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockListParam;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummaryDTO;
import com.danding.cds.itemstocklist.api.enums.AbnormalTypeEnum;
import com.danding.cds.itemstocklist.api.enums.DataTypeNum;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.itemstocklist.api.service.ItemStockSummaryService;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;

@Slf4j
@RestController
@Api(tags = "料号库存流水")
@RequestMapping("/itemstocklist")
@RefreshScope
public class ItemStockListController {

    @DubboReference
    private ItemStockListService itemStockListService;

    @DubboReference
    private ItemStockSummaryService itemStockSummaryService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CarryforwardCustomsBookService carryforwardCustomsBookService;

    @DubboReference
    private EndorsementService endorsementService;

    @Value("${web.itemStockList.wechatUrl:}")
    private String weCharUrl;

    @ApiOperation(value = "海关数据批量插入")
    @PostMapping("/hgBatchInsert")
    public Response<String> HGBatchInsert(@RequestBody List<ItemStockListParam> itemStockLists) {
        try {
            Map<String, EndorsementDTO> endorsementDTOMap = new HashMap<>();
            Map<String, CustomsBookDTO> customsBookDTOMap = new HashMap<>();
            List<ItemStockListDTO> itemStockListInsert = new ArrayList<>();
            List<ItemStockListDTO> itemStockListUpdate = new ArrayList<>();
            List<ItemStockSummaryDTO> itemStockSummaryListTemp = new ArrayList<>();
            for (ItemStockListParam itemStockListParam : itemStockLists) {
                log.info("收到海关数据批量插入数据：JSON={}", JSON.toJSONString(itemStockListParam));
                ItemStockListDTO itemStockListDTO = new ItemStockListDTO();
                itemStockListDTO.setCustomsRecordProductId(itemStockListParam.getCustomsRecordProductId());
                itemStockListDTO.setGoodsName(itemStockListParam.getGoodsName());
                itemStockListDTO.setGoodsSeqNo(itemStockListParam.getGoodsSeqNo());
                itemStockListDTO.setSn(itemStockListParam.getSn());
                CustomsBookDTO customsBookDTO = getCustomsBook(itemStockListParam.getBookNo(), customsBookDTOMap);
                if (Objects.isNull(customsBookDTO)) {
                    log.error("海关数据批量插入失败：账册编号不存在={}", itemStockListParam.getBookNo());
                    sendWechatMessage("海关数据批量插入失败：账册编号不存在" + itemStockListParam.getBookNo());
                    return new Response<>(-1, "海关数据批量插入失败：账册编号不存在" + itemStockListParam.getBookNo());
                }
                itemStockListDTO.setBookId(customsBookDTO.getId());
                itemStockListDTO.setBookNo(customsBookDTO.getBookNo());
                itemStockListDTO.setRealOrderNo(itemStockListParam.getRealOrderNo());
                EndorsementDTO endorsementDTO = getEndorsement(itemStockListParam.getRealOrderNo(), endorsementDTOMap);
                if (Objects.isNull(endorsementDTO)) {
                    itemStockListDTO.setInveBusinessType(EndorsementBussiness.BUSSINESS_EMPTY.getCode());
                    itemStockListDTO.setAbnormalType(AbnormalTypeEnum.ABNORMAL_CCS_NOT_EXISTS.getCode());
                } else {
                    itemStockListDTO.setAbnormalType(AbnormalTypeEnum.ABNORMAL_EMPTY.getCode());
                    itemStockListDTO.setInveBusinessType(endorsementDTO.getBussinessType());
                    itemStockListDTO.setEndorsementId(endorsementDTO.getId());
                }
                if (itemStockListParam.getInOutType().contains("进口")) {
                    itemStockListDTO.setOutAccountBook(itemStockListParam.getAssociatedAccountBook());
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.IN.getCode());
                    itemStockListDTO.setAssociatedOutEndorsementNo(itemStockListParam.getAssociatedEndorsementNo());
                } else if (itemStockListParam.getInOutType().contains("出口")) {
                    itemStockListDTO.setInAccountBook(itemStockListParam.getAssociatedAccountBook());
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.OUT.getCode());
                    itemStockListDTO.setAssociatedInEndorsementNo(itemStockListParam.getAssociatedEndorsementNo());
                }
                itemStockListDTO.setEventQty(itemStockListParam.getEventQty());
                itemStockListDTO.setEventTime(DateUtil.parse(itemStockListParam.getEventTime().replace("\u00A0", " ")));
                itemStockListDTO.setDataType(DataTypeNum.HG.getCode());
                if (Objects.equals(itemStockListParam.getHgStatus(), "反核扣")) {
                    itemStockListDTO.setDeleted(true);
                    Integer deleteCount = itemStockListService.findDeleteCountByRBPGDD(itemStockListParam.getRealOrderNo(), customsBookDTO.getId(), itemStockListParam.getCustomsRecordProductId(),
                            itemStockListParam.getGoodsSeqNo(), DataTypeNum.HG.getCode());
                    if (deleteCount > 0) {
                        continue;
                    }
                } else {
                    itemStockListDTO.setDeleted(false);
                }
                ItemStockListDTO itemStockListDTOTemp = itemStockListService.findByRBPGD(itemStockListParam.getRealOrderNo(), customsBookDTO.getId(), itemStockListParam.getCustomsRecordProductId(),
                        itemStockListParam.getGoodsSeqNo(), DataTypeNum.HG.getCode());
                if (Objects.isNull(itemStockListDTOTemp)) {
                    //判断itemStockListInsert是否存在，如果存在则不放入itemStockListInsert中
                    boolean exists = itemStockListInsert.stream().anyMatch(d -> d.getRealOrderNo().equals(itemStockListDTO.getRealOrderNo()) && d.getBookId().equals(itemStockListDTO.getBookId())
                            && d.getGoodsSeqNo().equals(itemStockListDTO.getGoodsSeqNo()) && d.getCustomsRecordProductId().equals(itemStockListDTO.getCustomsRecordProductId()) && d.getDeleted().equals(false));
                    if (!exists) {
                        itemStockListInsert.add(itemStockListDTO);
                    }
                } else {
                    itemStockListDTO.setId(itemStockListDTOTemp.getId());
                    itemStockListUpdate.add(itemStockListDTO);
                }
            }
            if (itemStockListInsert.size() > 0 || itemStockListUpdate.size() > 0) {
                itemStockListService.batchInsertOrUpdateListAndSummary(itemStockListInsert, itemStockListUpdate);
            }
            return new Response<>("海关数据批量插入成功");
        } catch (ArgsErrorException e) {
            log.error("海关数据批量插入失败: error={}", e.getErrorMessage(), e);
            sendWechatMessage("海关数据批量插入失败：" + e.getErrorMessage());
            return new Response<>(-1, "海关数据批量插入失败：" + e.getErrorMessage());
        } catch (Exception ex) {
            log.error("海关数据批量插入失败: error={}", ex.getMessage(), ex);
            sendWechatMessage("海关数据批量插入失败：" + ex.getMessage());
            return new Response<>(-1, "海关数据批量插入失败：" + ex.getMessage());
        }

    }

    @ApiOperation(value = "海关数据批量更新")
    @PostMapping("/hgBatchUpdate")
    public Response<String> hgBatchUpdate(@RequestBody List<ItemStockListParam> itemStockLists) {
        try {
            Map<String, CustomsBookDTO> customsBookDTOMap = new HashMap<>();
            Map<String, EndorsementDTO> endorsementDTOMap = new HashMap<>();
            List<ItemStockListDTO> itemStockListInsert = new ArrayList<>();
            List<ItemStockListDTO> itemStockListUpdate = new ArrayList<>();
            for (ItemStockListParam itemStockListParam : itemStockLists) {
                log.info("收到海关数据批量更新数据：JSON={}", JSON.toJSONString(itemStockListParam));
                ItemStockListDTO itemStockListDTO = new ItemStockListDTO();
                CustomsBookDTO customsBookDTO = getCustomsBook(itemStockListParam.getBookNo(), customsBookDTOMap);
                if (Objects.isNull(customsBookDTO)) {
                    log.error("海关数据批量更新失败：账册编号不存在={}", itemStockListParam.getBookNo());
                    sendWechatMessage("海关数据批量更新失败：账册编号不存在" + itemStockListParam.getBookNo());
                    return new Response<>(-1, "海关数据批量更新失败：账册编号不存在" + itemStockListParam.getBookNo());
                }
                itemStockListDTO.setCustomsRecordProductId(itemStockListParam.getCustomsRecordProductId());
                itemStockListDTO.setGoodsName(itemStockListParam.getGoodsName());
                itemStockListDTO.setGoodsSeqNo(itemStockListParam.getGoodsSeqNo());
                itemStockListDTO.setSn(itemStockListParam.getSn());
                itemStockListDTO.setBookId(customsBookDTO.getId());
                itemStockListDTO.setBookNo(customsBookDTO.getBookNo());
                itemStockListDTO.setRealOrderNo(itemStockListParam.getRealOrderNo());
                EndorsementDTO endorsementDTO = getEndorsement(itemStockListParam.getRealOrderNo(), endorsementDTOMap);
                if (Objects.isNull(endorsementDTO)) {
                    itemStockListDTO.setAbnormalType(AbnormalTypeEnum.ABNORMAL_CCS_NOT_EXISTS.getCode());
                    itemStockListDTO.setInveBusinessType(EndorsementBussiness.BUSSINESS_EMPTY.getCode());
                } else {
                    itemStockListDTO.setAbnormalType(AbnormalTypeEnum.ABNORMAL_EMPTY.getCode());
                    itemStockListDTO.setInveBusinessType(endorsementDTO.getBussinessType());
                }
                if (itemStockListParam.getInOutType().contains("进口")) {
                    itemStockListDTO.setOutAccountBook(itemStockListParam.getAssociatedAccountBook());
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.IN.getCode());
                    itemStockListDTO.setAssociatedOutEndorsementNo(itemStockListParam.getAssociatedEndorsementNo());
                } else if (itemStockListParam.getInOutType().contains("出口")) {
                    itemStockListDTO.setInAccountBook(itemStockListParam.getAssociatedAccountBook());
                    itemStockListDTO.setInOutFlag(InventoryInOutEnum.OUT.getCode());
                    itemStockListDTO.setAssociatedInEndorsementNo(itemStockListParam.getAssociatedEndorsementNo());
                }
                itemStockListDTO.setEventQty(itemStockListParam.getEventQty());
                //传过来的字符串自带nbsp.所有替换，不然报错
                itemStockListDTO.setEventTime(DateUtil.parse(itemStockListParam.getEventTime().replace("\u00A0", " ")));
                itemStockListDTO.setDataType(DataTypeNum.HG.getCode());
                //如果数据库本身存在反可口的记录则不处理
                if (Objects.equals(itemStockListParam.getHgStatus(), "反核扣")) {
                    itemStockListDTO.setDeleted(true);
                    Integer deleteCount = itemStockListService.findDeleteCountByRBPGDD(itemStockListParam.getRealOrderNo(), customsBookDTO.getId(), itemStockListParam.getCustomsRecordProductId(),
                            itemStockListParam.getGoodsSeqNo(), DataTypeNum.HG.getCode());
                    if (deleteCount > 0) {
                        continue;
                    }
                } else {
                    itemStockListDTO.setDeleted(false);
                }
                ItemStockListDTO itemStockListDTOTemp = itemStockListService.findByRBPGD(itemStockListParam.getRealOrderNo(), customsBookDTO.getId(), itemStockListParam.getCustomsRecordProductId(),
                        itemStockListParam.getGoodsSeqNo(), DataTypeNum.HG.getCode());
                if (Objects.isNull(itemStockListDTOTemp)) {
                    //判断itemStockListInsert是否存在，如果存在则不放入itemStockListInsert中
                    boolean exists = itemStockListInsert.stream().anyMatch(d -> d.getRealOrderNo().equals(itemStockListDTO.getRealOrderNo()) && d.getBookId().equals(itemStockListDTO.getBookId())
                            && d.getGoodsSeqNo().equals(itemStockListDTO.getGoodsSeqNo()) && d.getCustomsRecordProductId().equals(itemStockListDTO.getCustomsRecordProductId()) && d.getDeleted().equals(false));
                    if (!exists) {
                        itemStockListInsert.add(itemStockListDTO);
                    }
                } else {
                    itemStockListDTO.setId(itemStockListDTOTemp.getId());
                    itemStockListUpdate.add(itemStockListDTO);
                }
            }
            if (itemStockListInsert.size() > 0 || itemStockListUpdate.size() > 0) {
                itemStockListService.batchInsertOrUpdateListAndSummary(itemStockListInsert, itemStockListUpdate);
            }
            return new Response<>("海关数据批量更新成功");
        } catch (ArgsErrorException e) {
            log.error("海关数据批量更新失败: error={}", e.getErrorMessage(), e);
            sendWechatMessage("海关数据批量更新失败：" + e.getErrorMessage());
            return new Response<>(-1, "海关数据批量更新失败：" + e.getErrorMessage());
        } catch (Exception ex) {
            log.error("海关数据批量更新失败: error={}", ex.getMessage(), ex);
            sendWechatMessage("海关数据批量更新失败：" + ex.getMessage());
            return new Response<>(-1, "海关数据批量更新失败：" + ex.getMessage());
        }
    }

    private void sendWechatMessage(String message) {
        //发送企微通知
        if (!StringUtils.isEmpty(weCharUrl)) {
            StringBuilder builder = new StringBuilder();
            builder.append("<font color=\\\"orange\\\">**料号库存流水异常提醒**</font>\\n");
            builder.append(String.format("当前时间：%s\\n\\r\\n", DateUtils.format(new Date(), "yyyy-MM-dd hh:mm:ss")));
            builder.append(message);
            WechatNotifyUtils.wechatNotifyMd(weCharUrl, null, builder.toString());
        }
    }

    private CustomsBookDTO getCustomsBook(String bookNo, Map<String, CustomsBookDTO> customsBookDTOMap) {
        if (customsBookDTOMap == null) {
            customsBookDTOMap = new HashMap<>();
        }
        CustomsBookDTO customsBookDTO = customsBookDTOMap.get(bookNo);
        if (customsBookDTO == null) {
            customsBookDTO = customsBookService.findByCode(bookNo);
            customsBookDTOMap.put(bookNo, customsBookDTO);
        }
        return customsBookDTO;
    }

    private EndorsementDTO getEndorsement(String realOrderNo, Map<String, EndorsementDTO> endorsementDTOMap) {
        if (endorsementDTOMap == null) {
            endorsementDTOMap = new HashMap<>();
        }
        EndorsementDTO endorsementDTO = endorsementDTOMap.get(realOrderNo);
        if (endorsementDTO == null) {
            endorsementDTO = endorsementService.findByRealOrderNo(realOrderNo);
            endorsementDTOMap.put(realOrderNo, endorsementDTO);
        }
        return endorsementDTO;
    }

}
