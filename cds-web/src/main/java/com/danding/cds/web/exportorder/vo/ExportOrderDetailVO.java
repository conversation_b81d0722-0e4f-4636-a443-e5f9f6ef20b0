package com.danding.cds.web.exportorder.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ExportOrderDetailVO implements Serializable {

    /**
     * 申报出库单id
     */
    private Long id;

    /**
     * 申报出库单号
     */
    private String sn;

    /**
     * 申报出库单状态
     */
    private Integer status;
    private String statusDesc;

    /**
     * 实体仓名称
     */
    private String entityWarehouseName;

    /**
     * 快递公司
     */
    private String expressNames;

    /**
     * 创建时间
     */
    private String createTimeStr;

    /**
     * 操作人
     */
    private String createByName;

    /**
     * 总毛重
     */
    private BigDecimal totalGrossWeight;

    /**
     * 总净重
     */
    private BigDecimal totalNetWeight;

}
