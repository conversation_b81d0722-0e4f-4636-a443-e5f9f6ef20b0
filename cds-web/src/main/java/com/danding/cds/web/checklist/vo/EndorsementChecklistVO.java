package com.danding.cds.web.checklist.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 允许做核放的核注清单
 */
@Data
@ApiModel
public class EndorsementChecklistVO {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("编号")
    private String sn;

    @ApiModelProperty("业务类型")
    private String bussinessType;

    @ApiModelProperty("是否一票多车，即是否允许部分选择")
    private Boolean allowCheckItem;

    @ApiModelProperty("一票多车的核注清单，第一次关联核放单时，不允许勾选全部表体信息")
    private Boolean allowCheckTable;

    @ApiModelProperty("清单表体列表")
    private List<EndorsementItemVO> itemList;

    /**
     * 核注清单编号
     */
    private String realOrderNo;
}
