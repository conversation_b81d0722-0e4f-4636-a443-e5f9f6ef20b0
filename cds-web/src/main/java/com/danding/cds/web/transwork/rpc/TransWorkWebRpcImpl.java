package com.danding.cds.web.transwork.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.transwork.api.enums.TransOrderTypeEnum;
import com.danding.cds.transwork.api.rpc.TransWorkRpc;
import com.danding.cds.transwork.api.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.ArrayList;
import java.util.List;

import lombok.extern.slf4j.Slf4j;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
@DubboService
@Slf4j
public class TransWorkWebRpcImpl implements TransWorkWebRpc {

    @DubboReference
    private TransWorkRpc transWorkRpc;

    /**
     * 运输作业单分页
     * @param search
     * @return
     * @path /transwork/paging
     */
    @Override
    @SoulClient(path = "/transwork/paging", desc = "运输作业单分页")
    public RpcResult<ListVO<TransWorkPageVO>> paging(TransWorkSearch search) {
        ListVO<TransWorkPageVO> transWorkPageVOListVO = transWorkRpc.paging(search);
        return RpcResult.success(transWorkPageVOListVO);
    }

    /**
     * 运输作业单详情
     * @param idParam
     * @return
     * @path /transwork/detail
     */
    @Override
    @SoulClient(path = "/transwork/detail", desc = "运输作业单详情")
    public RpcResult<TransWorkDetailVO> detail(IdParam idParam) {
        return RpcResult.success(transWorkRpc.detail(idParam.getId()));
    }

    /**
     * 运输作业单商品分页
     * @param idParam
     * @return
     * @path /transwork/item/paging
     */
    @Override
    @SoulClient(path = "/transwork/item/paging", desc = "运输作业单商品分页")
    public RpcResult<ListVO<TransWorkItemVO>> itemPaging(IdParam idParam) {
        return RpcResult.success(transWorkRpc.itemPaging(idParam.getId()));
    }

    /**
     * 运输作业单商品编辑
     * @param param
     * @return
     * @path /transwork/item/edit
     */
    @Override
    @SoulClient(path = "/transwork/item/edit", desc = "运输作业单商品编辑")
    public RpcResult<String> itemEdit(TransWorkItemEditVO param) {
        transWorkRpc.itemEdit(param);
        return RpcResult.success();
    }

    /**
     * 运输作业单商品编辑时间
     * @param param
     * @return
     * @path /transwork/editTimeInfo
     */
    @Override
    @SoulClient(path = "/transwork/editTimeInfo", desc = "运输作业单编辑时间")
    public RpcResult<String> editTimeInfo(TransWorkTimeEditVO param) {
        transWorkRpc.editTimeInfo(param);
        return RpcResult.success();
    }

    /**
     * 运输作业单添加车辆信息
     * @param param
     * @return
     * @path /transwork/addTruckInfo
     */
    @Override
    @SoulClient(path = "/transwork/addTruckInfo", desc = "运输作业单添加车辆信息")
    public RpcResult<String> addTruckInfo(TransTruckInfoVO param) {
        try {
            transWorkRpc.addTruckInfo(param);
        } catch (ArgsErrorException e) {
            log.error("添加车辆信息失败", e);
            return RpcResult.error(e.getErrorMessage());
        }
        return RpcResult.success();
    }

    /**
     * 运输作业单添加车辆信息
     * @param param
     * @return
     * @path /transwork/addTruckNo
     */
    @Override
    @SoulClient(path = "/transwork/addTruckNo", desc = "运输作业单添加车牌号")
    public RpcResult<String> addTruckNo(TransAddTruckNoVO param) {
        try {
            transWorkRpc.addTruckNo(param);
        } catch (ArgsErrorException e) {
            log.error("添加车辆信息失败", e);
            return RpcResult.error(e.getErrorMessage());
        }
        return RpcResult.success();
    }

    /**
     * 运输作业单类型列表
     * @return
     * @path /transwork/orderTypeList
     */
    @Override
    @SoulClient(path = "/transwork/orderTypeList", desc = "运输作业单类型列表")
    public RpcResult<List<SelectOptionVO<String>>> orderTypeList() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        for (TransOrderTypeEnum value : TransOrderTypeEnum.values()) {
            SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>(value.getCode(), value.getDesc());
            res.add(selectOptionVO);
        }
        return RpcResult.success(res);
    }

    /**
     * 完成运输作业单
     * @param idParam
     * @return
     * @path /transwork/finishTransWork
     */
    @Override
    @SoulClient(path = "/transwork/finishTransWork", desc = "完成运输作业单")
    public RpcResult<String> finishTransWork(IdParam idParam) {
        try {
            transWorkRpc.finishTransWork(idParam.getId());
        } catch (Exception e) {
            if (e instanceof ArgsErrorException) {
                return RpcResult.error(((ArgsErrorException) e).getErrorMessage());
            }
            return RpcResult.error(e.getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 获取包装明细
     * @param idParam
     * @return
     * @path /transwork/getPackagingItems
     */
    @Override
    @SoulClient(path = "/transwork/getPackagingItems", desc = "获取装装明细")
    public RpcResult<List<TransWorkOrderPackagingItemsVO>> getPackagingItems(IdParam idParam) {
        return RpcResult.success(transWorkRpc.getPackagingItems(idParam.getId()));
    }

    /**
     * 包装明细编辑
     * @param param
     * @return
     * @path /transwork/packagingItemEdit
     */
    @Override
    @SoulClient(path = "/transwork/packagingItemEdit", desc = "装箱明细编辑")
    public RpcResult<String> packagingItemEdit(TransWorkItemEditVO param) {
        transWorkRpc.packagingItemEdit(param);
        return RpcResult.success();
    }
}
