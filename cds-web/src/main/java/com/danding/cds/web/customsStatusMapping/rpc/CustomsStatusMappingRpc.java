package com.danding.cds.web.customsStatusMapping.rpc;

import java.util.List;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingAddReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingUpdReqVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;

public interface CustomsStatusMappingRpc {

    RpcResult paging(CustomsStatusMappingReqVO reqVO);

    RpcResult findPendingCount();

    RpcResult mapStatus();

    RpcResult listInternalStatus();

    RpcResult save(CustomsStatusMappingAddReqVO reqVO) throws ArgsErrorException;


    RpcResult edit(CustomsStatusMappingUpdReqVO reqVO) throws ArgsErrorException;

    RpcResult delete(CustomsStatusMappingUpdReqVO reqVO);

    RpcResult declareItemStatus();

    RpcResult yesOrNoStatus();

    RpcResult customsStat();

    RpcResult<List<SelectOptionVO<String>>> getModifyCustomsStat();

    RpcResult getCustomsReceipt();

    RpcResult listCode();
}
