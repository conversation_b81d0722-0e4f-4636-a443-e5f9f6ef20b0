package com.danding.cds.web.suningFP.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class SuNingFPLoadSearchResult {

    private Long id;

    private String loadOrderNo;

    private Integer totalCount;

    private BigDecimal totalWeight;

    private Integer totalToryNum;

    private String licensePlate;

    private Integer auditStatus;

    private String auditStatusDesc;

    private boolean allowAudit = true;

    private boolean allowGenerateExport = true;

    private String createTime;

    private String auditTime;

    private String updateTime;
}
