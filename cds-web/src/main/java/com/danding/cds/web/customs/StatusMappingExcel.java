package com.danding.cds.web.customs;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

@Data
public class StatusMappingExcel {

    @Excel(name = "动作代码")
    private String action;

    @Excel(name = "事件标识")
    private String code;

    @Excel(name = "描述")
    private String detail;

    @Excel(name = "申报项状态")
    private String status;

    @Excel(name = "异常处理方案")
    private String note;

    @Excel(name = "是否异常")
    private String exceptionFlag;

    @Excel(name = "是否启用")
    private String enableFlag;
}
