package com.danding.cds.web.v2.bean.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessTradeBookConsumptionVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    private Long refBookId;

    /**
     * 成品id
     */
    @ApiModelProperty(value = "成品id")
    private Long endPrdId;

    /**
     * 成品序号
     */
    private Integer endPrdSeqNo;

    /**
     * 成品料号
     */
    @ApiModelProperty(value = "成品料号")
    private String endPrdProductId;

    /**
     * 成品商品编码
     */
    private String endPrdHsCode;

    /**
     * 成品商品名称
     */
    private String endPrdGoodsName;

    /**
     * 料件id
     */
    @ApiModelProperty(value = "料件id")
    private Long mtpckId;

    /**
     * 料件序号
     */
    private Integer mtpckSeqNo;

    /**
     * 料件料号
     */
    private String mtpckProductId;

    /**
     * 料件商品编码
     */
    private String mtpckHsCode;

    /**
     * 料件商品名称
     */
    private String mtpckGoodsName;

    /**
     * 单耗版本号 成品序号-料件序号
     */
    @ApiModelProperty(value = "单耗版本号 成品序号-料件序号")
    private String consumptionVersionNo;

    /**
     * 单耗有效期
     */
    @ApiModelProperty(value = "单耗有效期")
    private Date consumptionValidity;
    private String consumptionValidityStr;

    /**
     * 净耗
     */
    @ApiModelProperty(value = "净耗")
    private Integer netConsumption;

    /**
     * 有形损耗率（%），默认0
     */
    @ApiModelProperty(value = "有形损耗率（%），默认0")
    private BigDecimal tangibleLossRate;

    /**
     * 无形损耗率（%），默认0
     */
    @ApiModelProperty(value = "无形损耗率（%），默认0")
    private BigDecimal intangibleLossRate;

    /**
     * 单耗申报状态 1-未申报 2-已申报 3-已确定
     */
    @ApiModelProperty(value = "单耗申报状态 1-未申报 2-已申报 3-已确定")
    private Integer declareStatus;
    private String declareStatusDesc;

    /**
     * 保税料件比例（%），默认100
     */
    @ApiModelProperty(value = "保税料件比例（%），默认100")
    private BigDecimal bondedMaterialRatio;

    /**
     * 修改标记
     */
    private String modifyFlag;
    private String modifyFlagDesc;

}
