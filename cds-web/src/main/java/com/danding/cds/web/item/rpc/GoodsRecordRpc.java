package com.danding.cds.web.item.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParam;
import com.danding.cds.item.api.dto.GoodsRecordAuditSubmit;
import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.api.dto.GoodsRecordSubmit;
import com.danding.cds.v2.bean.dto.GoodsRecordStatusCount;
import com.danding.cds.v2.bean.param.RecordModifyAssociateInfoSubmit;
import com.danding.cds.v2.bean.vo.req.GoodsRecordRejectReasonReqVo;
import com.danding.cds.v2.bean.vo.req.RecordTrackLogSearch;
import com.danding.cds.v2.bean.vo.req.UpdFinishGoodsRecordReqVo;
import com.danding.cds.v2.bean.vo.res.*;
import com.danding.cds.web.item.vo.*;
import com.danding.cds.web.v2.bean.es.RecordTrackLogEsVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface GoodsRecordRpc {

    /**
     * 分页查询
     * @param condition
     * @return
     */
    RpcResult<ListVO<GoodsRecordVO>> paging(GoodsRecordSearchCondition condition);

    RpcResult<Long> audit(GoodsRecordAuditSubmit submit);

    RpcResult<Long> auditV2(GoodsRecordAuditSubmit customsWarehouseInfoVO,GoodsRecordSubmit baseInfoVO);

    RpcResult<GoodsRecordAuditNextResVO> getNextWaitAuditRecord(GoodsRecordAuditNextReqVO reqVO);

    RpcResult<Long> upset(GoodsRecordSubmit submit);

    RpcResult updateGoodsRecord(GoodsRecordSubmit reqVo);

    RpcResult<Long> updateEnable(GoodsRecordSubmit submit);

    RpcResult<Long> deleteById(IdParam idParam)throws ArgsErrorException;

    RpcResult<GoodsRecordDetailVO> detail(IdParam idParam);

    RpcResult<GoodsRecordWarehouseDetailVO> detailV2(Long recordId,String customsCode);

    RpcResult<List<RecordCustomsResVO>> recordCustomsList(IdParam idParam);

    RpcResult<LoadTaskInfoDTO> goodsRecordImportQuery();

    RpcResult<String> goodsRecordImport(UrlParam urlParam);

    RpcResult<String> goodsRecordImportConfirm();

    RpcResult<List<SelectOptionVO<String>>> listWares();

    RpcResult<List<SelectOptionVO<String>>> listcWares();

    RpcResult<String> exportExcel(GoodsRecordSearchCondition condition);

    RpcResult<RecordItemAssociateInfoResVO> viewAssociatedInfo(IdParam idParam);

    RpcResult<List<SelectOptionVO<Long>>> recordCustomsBookList(IdParam idParam);

    RpcResult<String> modifyAssociatedInfo(RecordModifyAssociateInfoSubmit submit);

    RpcResult<GoodsRecordAuditPassResVO> auditPassPreCheck(IdParam idParam);

    RpcResult<GoodsRecordAuditPassResVO> auditPassPreCheckV2(Long recordId, String customsCode);

    RpcResult<List<AssociateCustomsBookItemResVO>> viewItemListByGoodsRecordId(IdParam idParam);

    RpcResult<ListVO<RecordTrackLogEsVO>> pagingTrackLog(RecordTrackLogSearch search);
//    RpcResult findRecord();

    RpcResult<GoodsRecordStatusCount> getRecordStatusCount();

    RpcResult<GoodsRecordStatusCount> getGoodsRecordStatusCount(GoodsRecordSearchCondition condition);

    RpcResult goodsRecordSource();

    RpcResult<String> deleteRecordCustomsProductById(Long id);

    RpcResult<String> saveNewWarehouseInfo(Long recordCustomsId, String entityWarehouseSn);

    RpcResult<List<SelectOptionVO<Integer>>> listGoodsRecordTag();

    RpcResult updateFinishGoodsRecord(UpdFinishGoodsRecordReqVo reqVo);

    RpcResult<List<SelectOptionVO<Integer>>> listAuditWay();

    RpcResult<String> retryAuditMsg(IdsParam idsParam);

    RpcResult<List<GoodsRecordRejectReasonResVo>> getRejectReason();

    RpcResult<String> addRejectReason(GoodsRecordRejectReasonReqVo recordRejectReasonReqVo);

    RpcResult<AiRecommendHsCodeResVo> aiRecommendHsCodeList(AiRecommendHsCodeReqVo reqVo);
}
