package com.danding.cds.web;

import java.io.File;
import java.io.IOException;
import java.lang.annotation.Annotation;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.net.URL;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/14 16:17
 */
public class ValueRefreshScopeClassScanner {

    public static void main(String[] args) throws Exception {
        findClassesWithValueAnnotationWithoutRefreshScope();

    }

    public static void findClassesWithValueAnnotationWithoutRefreshScope() throws Exception {
        List<Class<?>> serializableClasses = new ArrayList<>();
        String packageName = "com.danding.cds";
//        Pattern r = Pattern.compile(filter);
        Enumeration<URL> urls = ClassLoader.getSystemClassLoader().getResources(packageName.replace(".", "/"));
        while (urls.hasMoreElements()) {
            URL url = urls.nextElement();

            if (url.getPath().contains(packageName.replaceAll("\\.", "/"))) {
                List<Class<?>> classes = findClasses(packageName, url.getPath());
                // System.out.println(urls.nextElement());
                for (Class<?> clazz : classes) {
                    if (Modifier.isInterface(clazz.getModifiers())) {
                        continue;
                    }
                    Annotation[] annotations = clazz.getAnnotations();
                    boolean hasValueAnnotation = false;
                    boolean hasRefreshScopeAnnotation = false;
                    for (Annotation annotation : annotations) {
                        if (annotation.annotationType().equals(org.springframework.cloud.context.config.annotation.RefreshScope.class)) {
                            hasRefreshScopeAnnotation = true;
                        }
                    }
                    Field[] declaredFields = clazz.getDeclaredFields();
                    for (Field declaredField : declaredFields) {
                        Annotation[] declaredFieldAnnotations = declaredField.getAnnotations();
                        for (Annotation declaredFieldAnnotation : declaredFieldAnnotations) {
                            if (declaredFieldAnnotation.annotationType().equals(org.springframework.beans.factory.annotation.Value.class)) {
                                hasValueAnnotation = true;
                            }
                        }
                    }
                    if (hasValueAnnotation && !hasRefreshScopeAnnotation) {
                        System.out.println(clazz.getName());
                    }
                }
            }
        }
    }

    private static List<Class<?>> findClasses(String packageName, String packageDirName) throws ClassNotFoundException, IOException {
        List<Class<?>> classes = new ArrayList<>();
        File dir = new File(packageDirName);
        if (!dir.exists() || !dir.isDirectory()) {
            return classes;
        }
        File[] files = dir.listFiles();
        for (File file : files) {
            if (file.isDirectory()) {
                assert !file.getName().contains(".");
                classes.addAll(findClasses(packageName + "." + file.getName(), file.getAbsolutePath()));
            } else if (file.getName().endsWith(".class")) {
                classes.add(Class.forName(packageName + '.' + file.getName().substring(0, file.getName().length() - 6)));
            }
        }
        return classes;
    }

}
