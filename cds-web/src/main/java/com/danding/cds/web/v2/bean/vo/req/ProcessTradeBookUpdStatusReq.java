package com.danding.cds.web.v2.bean.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;


@Data
public class ProcessTradeBookUpdStatusReq implements Serializable {

    /**
     * 账册iDList
     */
    @NotNull(message = "账册id不能为空")
    private List<Long> idList;

    /**
     * 账册状态
     */
    @NotNull(message = "账册状态不能为空")
    private Integer status;

}
