package com.danding.cds.web.customs.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.HsConsumptionFlagEnums;
import com.danding.cds.common.enums.HsFloatTypeEnums;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.customs.hs.api.dto.CustomsHsSubmit;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.web.customs.CustomsHsCodeController;
import com.danding.cds.web.customs.vo.CustomsHsCodeDetailVO;
import com.danding.cds.web.customs.vo.CustomsHsCodeVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * @Author: Raymond
 * @Date: 2020/9/27 15:14
 * @Description:
 */
@Slf4j
@DubboService
public class CustomsHsCodeRpcImpl implements CustomsHsCodeRpc {

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @Autowired
    private CustomsHsCodeController customsHsCodeController;

    /**
     * 分页查询
     *
     * @param condition
     * @return
     * @path /hsCode/paging
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/paging", desc = "分页查询")
    public RpcResult<ListVO<CustomsHsCodeVO>> paging(CustomsHsSearchCondition condition) {
        try {
            ListVO<CustomsHsDTO> paging = customsHsService.paging(condition);
            ListVO<CustomsHsCodeVO> result = new ListVO<>();
            result.setPage(paging.getPage());
            List<CustomsHsCodeVO> dataList = new ArrayList<>();
            Map<String, CustomsUomDTO> uomDTOMap = new HashMap<>();
            for (CustomsHsDTO itemDTO : paging.getDataList()) {
                CustomsHsCodeVO vo = ConvertUtil.beanConvert(itemDTO, CustomsHsCodeVO.class);
//            vo.setId(itemDTO.getId());
//            vo.setHsCode(itemDTO.getHsCode());
//            vo.setHsName(itemDTO.getHsName());
//            vo.setEnable(itemDTO.getEnable());
                //数据库里存的为小数  前台展示百分比 乘以100
                vo.setVat(itemDTO.getVat().multiply(new BigDecimal(100)));
                if (Objects.nonNull(itemDTO.getFirstLegalUnit())) {
                    if (!uomDTOMap.containsKey(itemDTO.getFirstLegalUnit())) {
                        CustomsUomDTO customsUomDTO = customsUomService.findByCode(itemDTO.getFirstLegalUnit());
                        if (Objects.nonNull(customsUomDTO)) {
                            vo.setFirstLegalUnitDesc(customsUomDTO.getName());
                            uomDTOMap.put(customsUomDTO.getCode(), customsUomDTO);
                        }
                    } else {
                        String name = uomDTOMap.get(itemDTO.getFirstLegalUnit()).getName();
                        vo.setFirstLegalUnitDesc(name);
                    }
                }
                if (Objects.nonNull(itemDTO.getSecondLegalUnit())) {
                    if (!uomDTOMap.containsKey(itemDTO.getSecondLegalUnit())) {
                        CustomsUomDTO customsUomDTO = customsUomService.findByCode(itemDTO.getSecondLegalUnit());
                        if (Objects.nonNull(customsUomDTO)) {
                            vo.setSecondLegalUnitDesc(customsUomDTO.getName());
                            uomDTOMap.put(customsUomDTO.getCode(), customsUomDTO);
                        }
                    } else {
                        String name = uomDTOMap.get(itemDTO.getSecondLegalUnit()).getName();
                        vo.setSecondLegalUnitDesc(name);
                    }
                }
                HsConsumptionFlagEnums consumptionFlagEnum = null;
                if (Objects.nonNull(itemDTO.getConsumptionFlag())) {
                    consumptionFlagEnum = HsConsumptionFlagEnums.getEnum(itemDTO.getConsumptionFlag());
                    if (Objects.nonNull(consumptionFlagEnum)) {
                        vo.setConsumptionDesc(consumptionFlagEnum.getDesc());
                    }
                }
                HsFloatTypeEnums floatTypeEnums = null;
                if (Objects.nonNull(itemDTO.getFloatType())) {
                    floatTypeEnums = HsFloatTypeEnums.getEnum(itemDTO.getFloatType());
                    if (Objects.nonNull(floatTypeEnums)) {
                        vo.setFloatTypeDesc(floatTypeEnums.getDesc());
                    }
                }
                if (consumptionFlagEnum != null) {
                    switch (consumptionFlagEnum) {
                        case SPECIFIC:
                            vo.setConsumptionTax(BigDecimal.ZERO);
                            vo.setSpecificPricePerUnitAndUomName(itemDTO.getSpecificPricePerUnit().toString() + itemDTO.getSpecificUomName());
                            break;
                        case PRICE:
                            vo.setConsumptionTax(itemDTO.getConsumptionTax().multiply(BigDecimal.valueOf(100)));
                            if (Objects.nonNull(floatTypeEnums) && !Objects.equals(floatTypeEnums, HsFloatTypeEnums.NO_FLOAT)) {
                                if (Objects.nonNull(itemDTO.getPricePerUnit())) {
                                    itemDTO.setPricePerUnit(itemDTO.getPricePerUnit().setScale(0, RoundingMode.HALF_UP));
                                }
                                vo.setFloatPricePerUnitAndUomName(itemDTO.getPricePerUnit().toString() + itemDTO.getUomName());
                            }
                            break;
                        case COMPOSITE:
                            vo.setConsumptionTax(itemDTO.getConsumptionTax().multiply(BigDecimal.valueOf(100)));
                            if (Objects.nonNull(floatTypeEnums) && !Objects.equals(floatTypeEnums, HsFloatTypeEnums.NO_FLOAT)) {
                                if (Objects.nonNull(itemDTO.getPricePerUnit())) {
                                    itemDTO.setPricePerUnit(itemDTO.getPricePerUnit().setScale(0, RoundingMode.HALF_UP));
                                }
                                vo.setFloatPricePerUnitAndUomName(itemDTO.getPricePerUnit().toString() + itemDTO.getUomName());
                            }
                            if (Objects.nonNull(itemDTO.getSpecificPricePerUnit())) {
                                vo.setSpecificPricePerUnitAndUomName(itemDTO.getSpecificPricePerUnit() + itemDTO.getSpecificUomName());
                            }
                            vo.setSpecificPricePerUnitAndUomName(itemDTO.getSpecificPricePerUnit().toString() + itemDTO.getSpecificUomName());
                            break;
                        default:
                            break;
                    }
                }
                vo.setCreateTime(new DateTime(itemDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
                if (StrUtil.isNotBlank(itemDTO.getDeclareElementJson())) {
                    Map<String, String> map = JSON.parseObject(itemDTO.getDeclareElementJson(), Map.class);
                    String declareElementStr = String.join(",", map.values());
                    vo.setDeclareElementStr(declareElementStr);
                }
                dataList.add(vo);
            }
            result.setDataList(dataList);
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("分页查询失败 error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/detail", desc = "详情")
    public RpcResult<CustomsHsCodeDetailVO> detail(Long id) {
        try {
            CustomsHsCodeDetailVO detailVO = new CustomsHsCodeDetailVO();
            CustomsHsDTO hsDTO = customsHsService.findById(id);
            if (hsDTO == null) {
                return RpcResult.success(new CustomsHsCodeDetailVO());
            }
            BeanUtil.copyProperties(hsDTO, detailVO);
            detailVO.setVat(hsDTO.getVat().multiply(new BigDecimal(100)));
            detailVO.setExportTaxRate(hsDTO.getExportTaxRate().multiply(new BigDecimal(100)));
//        detailVO.setConsumptionNumTax(hsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)));
            detailVO.setConsumptionTax(hsDTO.getConsumptionTax().multiply(new BigDecimal(100)));
            detailVO.setExportDrawbackTaxRate(hsDTO.getExportDrawbackTaxRate().multiply(new BigDecimal(100)));
            if (hsDTO.getExportTentativeTaxRate() != null) {
                detailVO.setExportTentativeTaxRate(hsDTO.getExportTentativeTaxRate().multiply(new BigDecimal(100)));
            }
            detailVO.setImportDiscountTaxRate(hsDTO.getImportDiscountTaxRate().multiply(new BigDecimal(100)));
            detailVO.setImportGeneralTaxRate(hsDTO.getImportGeneralTaxRate().multiply(new BigDecimal(100)));
            if (hsDTO.getImportTentativeTaxRate() != null) {
                detailVO.setImportTentativeTaxRate(hsDTO.getImportTentativeTaxRate().multiply(new BigDecimal(100)));
            }
//        if (hsDTO.getConsumptionNumTax() != null) {
//            detailVO.setConsumptionNumTax(hsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)));
//        }
            detailVO.setUpdateTime(new DateTime(hsDTO.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            return RpcResult.success(detailVO);
        } catch (Exception e) {
            log.error("详情失败 error={}", e.getMessage(), e);
            return RpcResult.error("详情获取失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/delete", desc = "删除")
    public RpcResult<Long> delete(Long id) {
        try {
            customsHsCodeController.deleteById(id);
            return RpcResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除失败 error={}", e.getMessage(), e);
            return RpcResult.error("删除失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/upset", desc = "更新")
    public RpcResult<Long> upset(CustomsHsSubmit submit) {
        try {
            submit.setEnable(1);
            customsHsService.upset(submit);
            return RpcResult.success("更新成功");
        } catch (ArgsErrorException e) {
            log.error("更新失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("更新失败:" + e.getErrorMessage());
        } catch (Exception e) {
            log.error("更新失败 error={}", e.getMessage(), e);
            return RpcResult.error("更新失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/listConsumptionFlag", desc = "从价从量标志")
    public RpcResult<List<SelectOptionVO<Integer>>> listConsumptionFlag() {
        List<SelectOptionVO<Integer>> result = new ArrayList<>();
//        result.add(new SelectItemVO("5", "从量"));
//        result.add(new SelectItemVO("10", "从价"));
        Arrays.stream(HsConsumptionFlagEnums.values()).filter(e -> !Objects.equals(e, HsConsumptionFlagEnums.NO_TAX)).forEach(e -> {
            result.add(new SelectOptionVO<>(e.getCode(), e.getDesc()));
        });
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/listFloatFlag", desc = "浮动类型下拉接口")
    public RpcResult<List<SelectOptionVO<Integer>>> listFloatFlag() {
        List<SelectOptionVO<Integer>> result = new ArrayList<>();

        Arrays.stream(HsFloatTypeEnums.values()).forEach(e -> {
            result.add(new SelectOptionVO<>(e.getCode(), e.getDesc()));
        });
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/exportExcelByDownLoadCenter", desc = "导出")
    public RpcResult<Long> exportExcel(CustomsHsSearchCondition condition) throws ServiceException {
        downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                condition, ReportType.CUSTOMS_HS_CODE_LIST_FOR_EXCEL);
        return RpcResult.success(1L);
    }

    /**
     * 未使用
     *
     * @param file
     * @return
     * @throws ArgsErrorException
     */
    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/hsCode/importExcel", desc = "导入")
    public RpcResult<Long> importExcel(MultipartFile file) throws ArgsErrorException {
        return RpcResult.success(customsHsCodeController.importExcel(file));
    }
}
