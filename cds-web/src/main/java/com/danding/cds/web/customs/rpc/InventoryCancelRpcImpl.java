package com.danding.cds.web.customs.rpc;

import cn.hutool.core.bean.BeanUtil;
import com.danding.cds.c.api.rpc.CustomsInventoryCancelRpc;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCancelDTO;
import com.danding.cds.customs.inventory.api.dto.InventoryCancelReport;
import com.danding.cds.customs.inventory.api.dto.InventoryCancelSearch;
import com.danding.cds.customs.inventory.api.enums.InventoryCancelEnum;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCancelService;
import com.danding.cds.customs.logistics.api.enums.CancelOrderReceiptEnum;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.log.api.enums.OperateReasonEnums;
import com.danding.cds.web.customs.InventoryCancelController;
import com.danding.cds.web.customs.rpc.param.RpcDeclareParam;
import com.danding.cds.web.customs.rpc.param.RpcManualOperationParam;
import com.danding.cds.web.customs.vo.InventoryCancelImportSubmit;
import com.danding.cds.web.customs.vo.InventoryCancelInfoSumVO;
import com.danding.cds.web.customs.vo.InventoryCancelResponseReport;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class InventoryCancelRpcImpl implements InventoryCancelRpc {

    @Autowired
    private InventoryCancelController inventoryCancelController;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CustomsInventoryCancelService customsInventoryCancelService;

    @DubboReference
    private CustomsInventoryCancelRpc customsInventoryCancelRpc;

    @Resource
    private OrderCCallConfig orderCCallConfiguration;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/list-status", desc = "获取所状态")
    public RpcResult<List<SelectOptionVO>> listStatus() {
        List<SelectOptionVO> result = Arrays.stream(InventoryCancelEnum.STATUS_ENUM.values())
                .filter(i -> !Objects.equals(i, InventoryCancelEnum.STATUS_ENUM.NULL))
                .map((InventoryCancelEnum.STATUS_ENUM item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getValue());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/paging", desc = "撤单单分页查询")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<CustomsInventoryCancelDTO>> paging(InventoryCancelSearch search) {
        return RpcResult.success(inventoryCancelController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/declare", desc = "申请创建撤单")
    public RpcResult<InventoryCancelResponseReport> declare(RpcDeclareParam declareParam) {
        return RpcResult.success(inventoryCancelController.declare(declareParam.getIds(), declareParam.getReason()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/submit/import", desc = "批量导入运单")
    public RpcResult<InventoryCancelReport> importExcel(InventoryCancelImportSubmit submit) {
        return RpcResult.success(inventoryCancelController.importExcel(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/do-declare", desc = "撤单申报")
    public RpcResult<Response<Boolean>> declareInventoryCancel(RpcDeclareParam declareParam) {
        try {
            String ids = declareParam.getIds();
            if (StringUtils.isBlank(ids)) {
                return RpcResult.error("请选中申报单");
            }
            List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
            inventoryCancelController.declareInventoryCancel(idList);
            return RpcResult.success("撤单申报提交成功", null);
        } catch (ArgsInvalidException e) {
            log.error("declareInventoryCancel 撤单申报校验异常 error={}", e.getMessage(), e);
            return RpcResult.error("撤单申报校验异常:" + e.getMessage());
        } catch (Exception e) {
            log.error("declareInventoryCancel 撤单申报失败 error={}", e.getMessage(), e);
            return RpcResult.error("撤单申报异常:" + e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/delete", desc = "撤单删除")
    public RpcResult<InventoryCancelResponseReport> delete(RpcDeclareParam declareParam) {
        return RpcResult.success(inventoryCancelController.delete(declareParam.getIds()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/cancel", desc = "撤单取消")
    public RpcResult<InventoryCancelResponseReport> cancelInventoryCancelOrder(RpcDeclareParam declareParam) {
        return RpcResult.success(inventoryCancelController.cancelInventoryCancelOrder(declareParam.getIds()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/export", desc = "撤单导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> export(InventoryCancelSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.CUSTOMS_INVENTORY_CANCEL_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {

            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/inventroy-cancel/sumInventoryCancelInfo", desc = "统计-撤销单个数")
    public RpcResult<List<InventoryCancelInfoSumVO>> sumInventoryCancelInfo(Long beginTimeLong, Long endTimeLong) {
        return RpcResult.success(inventoryCancelController.sumInventoryCancelInfo(beginTimeLong, endTimeLong));
    }

    @Override
    @SoulClient(path = "/inventory-cancel/manualReview", desc = "手动审核更新状态")
    public RpcResult<Response<Boolean>> manualReview(@RequestParam String ids) {
        return RpcResult.success(inventoryCancelController.manualReview(ids));
    }

    @Override
    @SoulClient(path = "/inventory-cancel/manualOperation", desc = "批量手动操作")
    public RpcResult manualOperation(RpcManualOperationParam param) {
        if (StringUtils.isEmpty(param.getIds())) {
            return RpcResult.error(-1, "参数不能为空");
        }
        String[] idArray = StringUtils.split(param.getIds(), ",");
        for (String id : idArray) {
            CustomsInventoryCancelDTO cancelDTO = BeanUtil.copyProperties(param, CustomsInventoryCancelDTO.class);
            cancelDTO.setId(Long.valueOf(id));
            Response<Boolean> response;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                response = customsInventoryCancelRpc.editReviewStatus(cancelDTO);
            } else {
                response = customsInventoryCancelService.editReviewStatus(cancelDTO);
            }
            if (Objects.equals(response.getCode(), -1)) {
                return RpcResult.error(response.getCode(), response.getErrorMessage());
            }
        }
        return RpcResult.success(true);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/customsLogisticsStatus", desc = "获取手工回执")
    public RpcResult customsLogisticsStatus() {
        return RpcResult.success(EnumUtils.build(CancelOrderReceiptEnum.class, "value", "desc"));
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/inventroy-cancel/operateReasonEnums", desc = "获取手动操作原因")
    public RpcResult operateReasonEnums() {
        return RpcResult.success(EnumUtils.build(OperateReasonEnums.class, "value", "desc"));
    }


}
