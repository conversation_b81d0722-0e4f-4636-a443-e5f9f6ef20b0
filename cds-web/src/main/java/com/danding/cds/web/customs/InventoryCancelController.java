package com.danding.cds.web.customs;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.*;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.cull.api.dto.CullOrderDTO;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.cull.api.service.CullOrderService;
import com.danding.cds.customs.inventory.api.dto.InventoryCancelReport;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.*;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCancelService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.refund.api.dto.RefundOrderInfoDto;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.InventoryCancelExportVO;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.dto.OrderExtra;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.web.customs.vo.*;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.component.uc.model.CurrentUserInfo;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.ThreadFactoryBuilder;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Transformer;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

@Api(tags = "清单撤单管理")
@RestController
@RequestMapping("/customs")
@Slf4j
public class InventoryCancelController {

    private static final ThreadPoolExecutor taskExecutor = new ThreadPoolExecutor(10, 10, 10, TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(), new ThreadFactoryBuilder().setNameFormat("applyCancel%d").build());

    private static final String CREATE_CANCEL_ORDER_LOCK_PREFIX = "ccs:lock:createCancelOrder:";

    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference
    private CustomsInventoryCancelService customsInventoryCancelService;
    @DubboReference
    private CustomsInventoryCancelRpc customsInventoryCancelRpc;
    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;
    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private RefundOrderService refundOrderService;
    @DubboReference
    private RefundOrderRpc refundOrderRpc;

    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private MessageService messageService;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;


    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @ApiOperation(value = "获取所状态")
    @GetMapping("/inventroy-cancel/list-status")
    public List<SelectItemVO> listStatus() {
        List<SelectItemVO> result = Arrays.stream(InventoryCancelEnum.STATUS_ENUM.values()).map((InventoryCancelEnum.STATUS_ENUM item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getValue());
            optionDTO.setName(item.getDesc());
            return optionDTO;
        }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "撤单单分页查询", response = CustomsInventoryCancelDTO.class)
    @GetMapping("/inventroy-cancel/paging")
    public ListVO<CustomsInventoryCancelDTO> paging(InventoryCancelSearch search) {
        ListVO<CustomsInventoryCancelDTO> list;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = customsInventoryCancelRpc.paging(search);
        } else {
            list = customsInventoryCancelService.paging(search);
        }
        List<CustomsInventoryCancelDTO> datalist = list.getDataList();
        if (datalist != null) {
            Map<String, String> tenantIdName = new HashMap<>();
            for (CustomsInventoryCancelDTO info : datalist) {
                String customsStatus = info.getCustomsStatus();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(customsStatus)) {
                    CustomsStat customsStat = CustomsStat.getEnum(customsStatus);
                    if (customsStat != null) info.setCustomsStatus(customsStat.getDesc());
                }
                if (info.getAreaCompanyId() != null) {
                    CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(info.getAreaCompanyId());
                    if (companyDTO != null)
                        info.setAreaCompanyName(companyDTO.getName());
                }
                if (info.getCustomsBookId() != null) {
                    CustomsBookDTO customsBookDTO = customsBookService.findById(info.getCustomsBookId());
                    if (customsBookDTO != null) {
                        info.setCustomsBookNo(customsBookDTO.getBookNo());
                    }
                }
                if (!StringUtils.isEmpty(info.getTenantId())) {
                    if (tenantIdName.containsKey(info.getTenantId())) {
                        info.setLesseeNo(tenantIdName.get(info.getTenantId()));
                    } else {
                        String tenantName = taxesTenantAccountService.getTenantNameById(info.getTenantId());
                        info.setLesseeNo(tenantName);
                        tenantIdName.put(info.getTenantId(), tenantName);
                    }
                }
                info.setOrderId(String.valueOf(info.getRefOrderId()));
            }
        }
        return list;
    }

    @ApiOperation(value = "撤单导出")
    @GetMapping("/inventroy-cancel/export")
    public void export(InventoryCancelSearch search, HttpServletResponse response) {
        if (false) {
            try {
                downloadProcessService.submitDownloadProcess(1L, search, ReportType.CUSTOMS_INVENTORY_CANCEL_FOR_EXCEL);
            } catch (ServiceException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            return;
        }
        java.util.List<CustomsInventoryCancelDTO> list = new java.util.ArrayList<CustomsInventoryCancelDTO>();
        if (!StringUtils.isEmpty(search.getIds())) {
            List<String> idList = Lists.newArrayList(search.getIds().split(","));
            for (String idStr : idList) {
                Long id = Long.valueOf(idStr);
                CustomsInventoryCancelDTO infoDto;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    infoDto = customsInventoryCancelRpc.findById(id);
                } else {
                    infoDto = customsInventoryCancelService.findById(id);
                }
                list.add(infoDto);
            }
        } else {
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                list = customsInventoryCancelRpc.queryList(search);
            } else {
                list = customsInventoryCancelService.queryList(search);
            }
        }
        List<InventoryCancelExportVO> exportVOList = new ArrayList<InventoryCancelExportVO>();
        for (CustomsInventoryCancelDTO customsInventoryCancelDTO : list) {
            InventoryCancelExportVO item = new InventoryCancelExportVO();
            OrderDTO orderDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                orderDTO = orderCRpc.findByIdFull(customsInventoryCancelDTO.getRefOrderId());
            } else {
                orderDTO = orderService.findByIdFull(customsInventoryCancelDTO.getRefOrderId());
            }
            CustomsInventoryDTO customsInventoryDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryDTO = customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            } else {
                customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            }
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getAssureCompanyId());
            item.setAsureName(companyDTO.getName());
            item.setChannelNo(customsInventoryCancelDTO.getChannelNo());
            item.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
            item.setMailNo(customsInventoryDTO.getLogisticsNo());
            item.setInvo(customsInventoryCancelDTO.getInvoNo());
            companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getEbcId());
            item.setEpcName(companyDTO.getName());
            companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getAgentCompanyId());
            item.setInventoryCompanyName(companyDTO.getName());
            companyDTO = companyService.findUnifiedCrossInfoById(customsInventoryDTO.getAreaCompanyId());
            item.setAreaInnerCompanyName(companyDTO.getName());
            InventoryCancelEnum.STATUS_ENUM status_enum = InventoryCancelEnum.getEnum(customsInventoryCancelDTO.getStatus());
            if (status_enum != null) item.setStatus(status_enum.getDesc());
            if (!StringUtils.isEmpty(customsInventoryCancelDTO.getCustomsStatus())) {
                CustomsStat stat = CustomsStat.getEnum(customsInventoryCancelDTO.getCustomsStatus());
                if (stat != null) item.setCustomsStatus(stat.getDesc());
            }
            item.setCreateTime(customsInventoryCancelDTO.getCreateTime());
            item.setCompleteTime(customsInventoryCancelDTO.getCompleteTime());
            exportVOList.add(item);
        }
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("清单撤单数据列表");
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, InventoryCancelExportVO.class, exportVOList);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(
                    ("清单撤单数据导出" + DateTime.now().toString("yyMMddHHmm") + ".xlsx").getBytes(), "iso8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @DubboReference
    private CullOrderService cullOrderService;
    @DubboReference
    private HandoverOrderDetailService handoverOrderDetailService;

    @GetMapping("/inventroy-cancel/declare")
    @ApiOperation(value = "申请撤单")
    @ApiImplicitParams({@ApiImplicitParam(name = "ids", required = true, dataType = "String", value = "申报单ID,多个ID,逗号隔开"), @ApiImplicitParam(name = "reason", required = false, dataType = "String", value = "撤单原因")})
    public InventoryCancelResponseReport declare(@RequestParam(required = true) String ids, String reason) {
        InventoryCancelResponseReport inventoryCancelResponseReport = new InventoryCancelResponseReport();
        inventoryCancelResponseReport.setCode(0);
        List<InventoryCancelResponseInfoVo> successRecordList = new ArrayList<>(), failRecordList = new ArrayList<>();
        String _declareIds[] = org.apache.commons.lang3.StringUtils.split(ids, ",");
        List<CustomsInventoryCancelDTO> list = new java.util.ArrayList<>();
        CustomsInventoryDTO customsInventoryDTO = null;
        Map<String, CustomsInventoryDTO> declareOrderNoInventoryMap = new HashMap<>();
        Map<String, OrderDTO> declareOrderNoOrderMap = new HashMap<>();
        int i = 0;
        for (String orderId : _declareIds) {
            OrderDTO orderDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                orderDTO = orderCRpc.findByIdFull(NumberUtils.createLong(orderId));
            } else {
                orderDTO = orderService.findByIdFull(NumberUtils.createLong(orderId));
            }
            if (Objects.nonNull(orderDTO)) {
                declareOrderNoOrderMap.put(orderDTO.getDeclareOrderNo(), orderDTO);
            }
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryDTO = customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            } else {
                customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
            }
            if (Objects.nonNull(customsInventoryDTO)) {
                declareOrderNoInventoryMap.put(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO);
            }
            i++;
            if (customsInventoryDTO == null) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo("UNKNOW");
                inventoryCancelResponseInfoVo.setInveNo("-");
                inventoryCancelResponseInfoVo.setMessage("参数错误查询对象为空");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue()) || customsInventoryDTO.getExitRegionStatus() != 0) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("清单状态[" + CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc() + "],无法操作撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            } else if (customsInventoryDTO.getExitRegionStatus() != 0) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("清单目前出区状态无法操作撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            } else if (!InventoryCancelEnum.contains(customsInventoryDTO.getCustomsStatus(), CancelInventoryAction.INVENTORY_CANCEL_APPALY)) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]相关清单状态不支持撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            CustomsInventoryCancelDTO customsInventoryCancelDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCancelDTO = customsInventoryCancelRpc.findByInventoryId(customsInventoryDTO.getId());
            } else {
                customsInventoryCancelDTO = customsInventoryCancelService.findByInventoryId(customsInventoryDTO.getId());
            }
            if (customsInventoryCancelDTO != null) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]相关清单撤单已存在");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            boolean checkRefundOrderInfoIsExist;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                checkRefundOrderInfoIsExist = refundOrderRpc.checkRefundOrderInfoIsExist(customsInventoryDTO.getOrderId());
            } else {
                checkRefundOrderInfoIsExist = refundOrderService.checkRefundOrderInfoIsExist(customsInventoryDTO.getOrderId());
            }
            if (checkRefundOrderInfoIsExist) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在退货单,不能创建撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            boolean checkRefundOrderInfoIsCreate;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                checkRefundOrderInfoIsCreate = refundOrderRpc.checkRefundOrderInfoIsCreate(customsInventoryDTO.getOrderId(), true);
            } else {
                checkRefundOrderInfoIsCreate = refundOrderService.checkRefundOrderInfoIsCreate(customsInventoryDTO.getOrderId(), true);
            }
            if (checkRefundOrderInfoIsCreate) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在相关有效的退货单,不能创建撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            List<CustomsInventoryCalloffDTO> customsInventoryCalloffList;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCalloffList = customsInventoryCalloffCRpc.findListByOrderId(orderDTO.getId());
            } else {
                customsInventoryCalloffList = customsInventoryCalloffService.findListByOrderId(orderDTO.getId());
            }
            Boolean flag = false;
            for (CustomsInventoryCalloffDTO customsInventoryCalloff : customsInventoryCalloffList) {
                if (customsInventoryCalloff != null && (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equals(customsInventoryCalloff.getCalloffStatus()) || InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equals(customsInventoryCalloff.getCalloffStatus()))) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在相关状态的取消单,不能创建撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            CullOrderDTO cullOrderDTO = cullOrderService.findByCustomsLogisticsSnAndStatus(customsInventoryDTO.getLogisticsNo(), CullStatusEnums.PENDING);
            if (cullOrderDTO != null) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在相关状态的剔除单,不能创建撤单");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            }
            // 取消单发起撤单时，【小巨人】标记的取消单不校验，“有关联的交接单时而不允许撤单” 菜鸟逆向申报需求
            CustomsInventoryCalloffDTO customsInventoryCalloffDTO = customsInventoryCalloffList.get(0);
            Integer orderTag = customsInventoryCalloffDTO.getOrderTag();
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(orderTag);
            if (!orderTags.contains(InventoryCalloffOrderTagEnums.XIAOJUREN.getCode())) {
                List<HandoverOrderDetailDTO> handoverOrderDetail = handoverOrderDetailService.getHandoverOrderDetailByMailNo(customsInventoryDTO.getLogisticsNo());
                if (!CollectionUtils.isEmpty(handoverOrderDetail)) {
                    InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                    inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                    inventoryCancelResponseInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                    inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                    inventoryCancelResponseInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在关联的交接单，无法操作撤单");
                    inventoryCancelResponseInfoVo.setIdx("" + i);
                    failRecordList.add(inventoryCancelResponseInfoVo);
                    continue;
                }
            }

            CustomsInventoryCancelDTO _info;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                _info = customsInventoryCancelRpc.buildCustomsInventoryCancelDTO(customsInventoryDTO, reason);
            } else {
                _info = customsInventoryCancelService.buildCustomsInventoryCancelDTO(customsInventoryDTO, reason);
            }
            list.add(_info);
        }
        for (CustomsInventoryCancelDTO customsInventoryCancelDTO : list) {

            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCancelRpc.customsInventoryCancelInsert(customsInventoryCancelDTO);
                customsInventoryCalloffCRpc.upset(customsInventoryCancelDTO.getRefOrderId(), customsInventoryCancelDTO.getRefInvoSn(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode(), InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), reason);
            } else {
                customsInventoryCancelService.customsInventoryCancelInsert(customsInventoryCancelDTO);
                customsInventoryCalloffService.upset(customsInventoryCancelDTO.getRefOrderId(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode(), InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), reason);
            }
            InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
            inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
            inventoryCancelResponseInfoVo.setInveNo(customsInventoryCancelDTO.getInvoNo());
            inventoryCancelResponseInfoVo.setLogisticsNo(customsInventoryCancelDTO.getMailNo());
            inventoryCancelResponseInfoVo.setMessage("创建撤单成功");
            inventoryCancelResponseInfoVo.setIdx("" + i);
            OrderDTO orderDTO;
            if (declareOrderNoOrderMap.containsKey(customsInventoryCancelDTO.getRefOrderSn())) {
                orderDTO = declareOrderNoOrderMap.get(customsInventoryCancelDTO.getRefOrderSn());
            } else {
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    orderDTO = orderCRpc.findByDeclareOrderNo(customsInventoryCancelDTO.getRefOrderSn());
                } else {
                    orderDTO = orderService.findByDeclareOrderNo(customsInventoryCancelDTO.getRefOrderSn());
                }
            }
            if (Objects.isNull(orderDTO)) {
                continue;
            }
            if (Objects.nonNull(orderDTO)) {
                //修改内部流转状态
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    orderCRpc.updateOrderInternalStatus(orderDTO, OrderInternalEnum.CANCELING.getCode());
                } else {
                    orderService.updateOrderInternalStatus(orderDTO, OrderInternalEnum.CANCELING.getCode());
                }
            }
            OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
            ArrayList<String> subscribeList = org.assertj.core.util.Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel());
            if (declareOrderNoInventoryMap.containsKey(orderDTO.getDeclareOrderNo())) {
                customsInventoryDTO = declareOrderNoInventoryMap.get(orderDTO.getDeclareOrderNo());
            }
            //手动撤单发送回执
            String activeData = "";
            if (customsInventoryDTO != null) {
                if (!Objects.equals(customsInventoryDTO.getCustomsStatus(), CustomsStat.CUSTOMS_PASS.getValue())) {
                    List<Integer> statusList = Arrays.asList(CustomsActionStatus.DEC_WAIT.getValue(), CustomsActionStatus.DEC_ING.getValue());
                    if (statusList.contains(customsInventoryDTO.getStatus()) && orderDTO.getExceptionFlag()) {
                        activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.CLOSE_SUCCESS.getCode().toString(), customsInventoryCancelDTO.getCustomsCheckDetail(), new Date()));
                    }
                } else {
                    activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.START_CANCEL_ORDERS.getCode().toString(), customsInventoryCancelDTO.getCustomsCheckDetail(), new Date()));
                }
            }
            log.info("[op:createMessage] 发送回执 - {}", activeData);
            if (!StringUtils.isEmpty(activeData)) {
                messageService.createMessage(MessageType.ORDER_CUSTOMS_CANCEL, subscribeList, customsInventoryCancelDTO.getRefOrderSn(), activeData, "");
            }
            successRecordList.add(inventoryCancelResponseInfoVo);
        }
        inventoryCancelResponseReport.setFailRecordList(failRecordList);
        inventoryCancelResponseReport.setSuccessRecordList(successRecordList);
        inventoryCancelResponseReport.setSuccessCount(successRecordList.size());
        inventoryCancelResponseReport.setFailCount(failRecordList.size());
        inventoryCancelResponseReport.setTotalCount(failRecordList.size() + successRecordList.size());
        return inventoryCancelResponseReport;
    }

    @ApiOperation(value = "撤单海关申报")
    public void declareInventoryCancel(List<Long> idList) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCancelRpc.declareInventoryCancel(idList);
        } else {
            customsInventoryCancelService.declareInventoryCancel(idList);
        }
    }

    @PostMapping("/inventroy-cancel/do-delJdDetail")
    @ApiOperation(value = "清空京东取消单")
    public Boolean delJdDetail() {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.delJd();
            customsInventoryCalloffCRpc.delJdDetail();
        } else {
            customsInventoryCalloffService.delJd();
            customsInventoryCalloffService.delJdDetail();
        }
        return true;
    }

    @GetMapping("/inventroy-cancel/delete")
    @ApiOperation(value = "撤单删除")
    @ApiImplicitParams({@ApiImplicitParam(name = "ids", required = true, dataType = "String", value = "撤单ID,多个id，逗号分隔开")})
    public InventoryCancelResponseReport delete(@RequestParam(required = true) String ids) {
        InventoryCancelResponseReport inventoryCancelResponseReport = new InventoryCancelResponseReport();
        ids = org.apache.commons.lang3.StringUtils.strip(ids, ",");
        String _ids[] = org.apache.commons.lang3.StringUtils.split(ids, ",");
        if (ArrayUtils.isEmpty(_ids)) {
            inventoryCancelResponseReport.setCode(-1);
            inventoryCancelResponseReport.setErrosMessage("请选择撤单数据");
            return inventoryCancelResponseReport;
        }
        List<Long> list = Arrays.stream(_ids).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<InventoryCancelResponseInfoVo> successRecordList = new ArrayList<InventoryCancelResponseInfoVo>(), failRecordList = new ArrayList<InventoryCancelResponseInfoVo>();
        int i = 0;
        List<Long> idsList = new ArrayList<Long>();
        for (Long id : list) {
            i++;
            CustomsInventoryCancelDTO customsInventoryCancelDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCancelDTO = customsInventoryCancelRpc.findById(id);
            } else {
                customsInventoryCancelDTO = customsInventoryCancelService.findById(id);
            }
            if (!InventoryCancelEnum.contains(customsInventoryCancelDTO.getStatus(), CancelInventoryAction.INVENTORY_DELETE_APPALY)) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryCancelDTO.getInvoNo());
                inventoryCancelResponseInfoVo.setMessage("当前撤单状态不能操作删除");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            } else {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryCancelDTO.getInvoNo());
                inventoryCancelResponseInfoVo.setMessage("删除撤单成功");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                successRecordList.add(inventoryCancelResponseInfoVo);
                idsList.add(id);
            }
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCancelRpc.deleteByIds(idsList);
        } else {
            customsInventoryCancelService.deleteByIds(idsList);
        }
        inventoryCancelResponseReport.setCode(0);
        inventoryCancelResponseReport.setFailRecordList(failRecordList);
        inventoryCancelResponseReport.setSuccessRecordList(successRecordList);
        inventoryCancelResponseReport.setSuccessCount(successRecordList.size());
        inventoryCancelResponseReport.setFailCount(failRecordList.size());
        inventoryCancelResponseReport.setTotalCount(failRecordList.size() + successRecordList.size());
        return inventoryCancelResponseReport;
    }


    @ApiOperation(value = "手动审核更新状态")
    @GetMapping("/inventory-cancel/manualReview")
    @ApiImplicitParams({@ApiImplicitParam(name = "ids", required = true, dataType = "String", value = "撤单ID,多个id，逗号分隔开")})
    public Response<Boolean> manualReview(@RequestParam String ids) {
        ids = org.apache.commons.lang3.StringUtils.strip(ids, ",");
        String[] idArray = org.apache.commons.lang3.StringUtils.split(ids, ",");
        if (ArrayUtils.isEmpty(idArray)) {
            Response<Boolean> oret = new Response<>(false);
            oret.setCode(-1);
            oret.setErrorMessage("参数为空");
            return oret;
        }
        List<Long> list = Arrays.stream(idArray).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return customsInventoryCancelRpc.manualReview(list);
        } else {
            return customsInventoryCancelService.manualReview(list);
        }
    }

    @PostMapping("/inventroy-cancel/retryDeclare")
    @ApiOperation(value = "测试调试")
    public Response<String> retryDeclare(@RequestParam(required = true) String id) {
        System.out.println("id  === " + id);
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCancelRpc.retryDeclare(NumberUtils.createLong(id));
        } else {
            customsInventoryCancelService.retryDeclare(NumberUtils.createLong(id));
        }
        return new Response<>("重置成功");
    }

    @PostMapping("/inventroy-cancel/mockReceive")
    @ApiOperation(value = "模拟接受回执")
    public Response<String> mockReceive(CustomsReceive receive) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return new Response<>(customsInventoryCancelRpc.receive(receive));
        } else {
            return new Response<>(customsInventoryCancelService.receive(receive));
        }
    }

    @ApiOperation(value = "批量导入运单浏览")
    @PostMapping("/inventroy-cancel/pre-import")
    public InventoryCancelReport preImportExcel(MultipartFile file) throws ArgsErrorException {
        ImportParams importParams = new ImportParams();

        ExcelImportResult<ImportExcelInventoryCancel> result;
        try {
            result = ExcelImportUtil.importExcelMore(file.getInputStream(), ImportExcelInventoryCancel.class, importParams);
        } catch (Exception exception) {
            throw new ArgsErrorException("读取附件失败");
        }
        List<ImportExcelInventoryCancel> successList = result.getList();
        List<ImportExcelInventoryCancelDTO> list = new ArrayList<ImportExcelInventoryCancelDTO>();
        Transformer<ImportExcelInventoryCancel, ImportExcelInventoryCancelDTO> transformer = new Transformer<ImportExcelInventoryCancel, ImportExcelInventoryCancelDTO>() {
            public ImportExcelInventoryCancelDTO transform(ImportExcelInventoryCancel input) {
                ImportExcelInventoryCancelDTO importExcelInventoryCancelDTO = new ImportExcelInventoryCancelDTO();
                BeanUtils.copyProperties(input, importExcelInventoryCancelDTO);
                return importExcelInventoryCancelDTO;
            }
        };
        CollectionUtils.collect(successList, transformer, list);
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return customsInventoryCancelRpc.importExcel(list, false);
        } else {
            return customsInventoryCancelService.importExcel(list, false);
        }
    }

    @ApiOperation(value = "批量导入运单")
    @PostMapping("/inventroy-cancel/submit/import")
    public InventoryCancelReport importExcel(@RequestBody InventoryCancelImportSubmit submit) throws ArgsErrorException {
        List<ImportExcelInventoryCancelDTO> list = submit.getRecordList();
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return customsInventoryCancelRpc.importExcel(list, true);
        } else {
            return customsInventoryCancelService.importExcel(list, true);
        }
    }

    @ApiOperation(value = "统计-撤销单个数")
    @PostMapping("/inventroy-cancel/sumInventoryCancelInfo")
    public List<InventoryCancelInfoSumVO> sumInventoryCancelInfo(Long beginTimeLong, Long endTimeLong) {
        Date beginTime = null;
        Date endTime = null;
        if (beginTimeLong != null) {
            beginTime = new Date();
            beginTime.setTime(beginTimeLong);
        }
        if (endTimeLong != null) {
            endTime = new Date();
            endTime.setTime(endTimeLong);
        }
        List<Map<String, Object>> mapList;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            mapList = customsInventoryCancelRpc.sumInventoryCancelInfo(beginTime, endTime);
        } else {
            mapList = customsInventoryCancelService.sumInventoryCancelInfo(beginTime, endTime);
        }

        List<InventoryCancelInfoSumVO> inventoryCancelInfoSumVOList = new ArrayList<InventoryCancelInfoSumVO>();
        for (Map<String, Object> item : mapList) {
            InventoryCancelInfoSumVO vo = new InventoryCancelInfoSumVO();
            vo.setCount(Long.valueOf(item.get("c").toString()));
            vo.setDateStr(String.valueOf(item.get("d")));
            inventoryCancelInfoSumVOList.add(vo);
        }
        return inventoryCancelInfoSumVOList;
    }

    @ApiOperation(value = "取消撤货订单")
    @GetMapping("/inventroy-cancel/cancel")
    @ApiImplicitParams({@ApiImplicitParam(name = "id", required = true, dataType = "Long", value = "撤单订单ID", paramType = "query")})
    public InventoryCancelResponseReport cancelInventoryCancelOrder(@RequestParam(required = true) String ids) {
        InventoryCancelResponseReport inventoryCancelResponseReport = new InventoryCancelResponseReport();
        ids = org.apache.commons.lang3.StringUtils.strip(ids, ",");
        String _ids[] = org.apache.commons.lang3.StringUtils.split(ids, ",");
        if (ArrayUtils.isEmpty(_ids)) {
            inventoryCancelResponseReport.setCode(-1);
            inventoryCancelResponseReport.setErrosMessage("请选择撤单数据");
            return inventoryCancelResponseReport;
        }
        List<Long> list = Arrays.stream(_ids).map(s -> Long.parseLong(s.trim())).collect(Collectors.toList());
        List<InventoryCancelResponseInfoVo> successRecordList = new ArrayList<>();
        List<InventoryCancelResponseInfoVo> failRecordList = new ArrayList<>();
        int i = 0;
        for (Long id : list) {
            i++;
            CustomsInventoryCancelDTO customsInventoryCancelDTO;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCancelDTO = customsInventoryCancelRpc.findById(id);
            } else {
                customsInventoryCancelDTO = customsInventoryCancelService.findById(id);
            }
            String arrayStatus[] = {
                    InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_INIT.getValue(),
                    InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_FAIL.getValue(),
                    InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_REJECT.getValue()
            };
            if (!(ArrayUtils.indexOf(arrayStatus, customsInventoryCancelDTO.getStatus()) >= 0)) {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryCancelDTO.getInvoNo());
                inventoryCancelResponseInfoVo.setIdx("" + i);
                failRecordList.add(inventoryCancelResponseInfoVo);
                continue;
            } else {
                InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
                inventoryCancelResponseInfoVo.setDeclareNo(customsInventoryCancelDTO.getRefOrderSn());
                inventoryCancelResponseInfoVo.setInveNo(customsInventoryCancelDTO.getInvoNo());
                inventoryCancelResponseInfoVo.setMessage("操作取消撤单成功");
                inventoryCancelResponseInfoVo.setIdx("" + i);
                successRecordList.add(inventoryCancelResponseInfoVo);
                customsInventoryCancelDTO.setStatus(InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_CANCEL.getValue());
                customsInventoryCancelDTO.setCompleteTime(new Date());
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    customsInventoryCancelRpc.cancelInventoryCancelOrder(customsInventoryCancelDTO);
                } else {
                    customsInventoryCancelService.cancelInventoryCancelOrder(customsInventoryCancelDTO);
                }
            }
        }
        inventoryCancelResponseReport.setCode(0);
        inventoryCancelResponseReport.setFailRecordList(failRecordList);
        inventoryCancelResponseReport.setSuccessRecordList(successRecordList);
        inventoryCancelResponseReport.setSuccessCount(successRecordList.size());
        inventoryCancelResponseReport.setFailCount(failRecordList.size());
        inventoryCancelResponseReport.setTotalCount(failRecordList.size() + successRecordList.size());
        return inventoryCancelResponseReport;
    }

    public InventoryCancelResponseReport applyCancel(String sns, String reason) {
        InventoryCancelResponseReport inventoryCancelResponseReport = new InventoryCancelResponseReport();
        inventoryCancelResponseReport.setCode(0);
        List<InventoryCancelResponseInfoVo> failRecordList = new ArrayList<>();
        List<String> snList = StrUtil.split(sns, ',');
        List<String> lockSnList = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();

        try {
            List<OrderDTO> orderList = orderCRpc.findBySnSection(snList);
            this.checkCreateCancelInProgress(orderList, failRecordList, lockSnList, snList);
            // 全部失败的情况
            if (CollectionUtils.isEmpty(snList)) {
                // 处理idx
                failRecordList.forEach(record -> record.setIdx(String.valueOf(i.incrementAndGet())));
                inventoryCancelResponseReport.setFailRecordList(failRecordList);
                inventoryCancelResponseReport.setSuccessRecordList(new ArrayList<>());
                inventoryCancelResponseReport.setSuccessCount(0);
                inventoryCancelResponseReport.setFailCount(failRecordList.size());
                inventoryCancelResponseReport.setTotalCount(failRecordList.size());
                return inventoryCancelResponseReport;
            }
            //构建 申报单OS、清单CI、申报单 表
            HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap = HashBasedTable.create();
            for (OrderDTO orderDTO : orderList) {
                // row column
                orderSnInvSnOrderMap.put(orderDTO.getSn(), orderDTO.getCustomsInventorySn(), orderDTO);
            }
            // inventorySn list
            List<String> customsInventorySnList = orderList.stream().map(OrderDTO::getCustomsInventorySn).collect(Collectors.toList());
            // 查询订单对应的清单
            List<CustomsInventoryDTO> customsInventoryList = customsInventoryRpc.findBySnList(customsInventorySnList);
            // 对清单结果进行校验
            Map<String, CustomsInventoryDTO> invMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getOrderSn, Function.identity(), (k1, k2) -> k1));
            this.checkCustomsInventoryList(invMap, failRecordList, orderSnInvSnOrderMap);
            Map<Long, CustomsInventoryDTO> orderIdInventoryMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getOrderId, Function.identity(), (k1, k2) -> k1));

            List<Long> orderIdList = orderList.stream().map(OrderDTO::getId).collect(Collectors.toList());
            //查询撤单列表
            List<CustomsInventoryCancelDTO> cancelList = customsInventoryCancelRpc.findByOrderId(orderIdList);
            // 撤单校验
            this.checkInventoryCancel(cancelList, invMap, failRecordList, orderSnInvSnOrderMap);

            //取消单
            List<CustomsInventoryCalloffDTO> listByOrderIdList = customsInventoryCalloffCRpc.findListByOrderIdList(orderIdList);
            // 取消单校验
            this.checkInventoryCalloff(listByOrderIdList, invMap, failRecordList, orderSnInvSnOrderMap);
            // 退货单校验
            this.checkRefundOrder(orderList, invMap, failRecordList, orderSnInvSnOrderMap);
            // 创建取消单DTO
            List<CustomsInventoryCancelDTO> list = this.buildCustomsInventoryCancelDTOS(orderSnInvSnOrderMap, invMap, reason);

            // 创建
            List<InventoryCancelResponseInfoVo> successRecordList = batchCreateCancel(list, orderIdInventoryMap, orderSnInvSnOrderMap, reason, taskExecutor);

            // 处理idx
            successRecordList.forEach(record -> record.setIdx(String.valueOf(i.incrementAndGet())));
            failRecordList.forEach(record -> record.setIdx(String.valueOf(i.incrementAndGet())));

            inventoryCancelResponseReport.setFailRecordList(failRecordList);
            inventoryCancelResponseReport.setSuccessRecordList(successRecordList);
            inventoryCancelResponseReport.setSuccessCount(successRecordList.size());
            inventoryCancelResponseReport.setFailCount(failRecordList.size());
            inventoryCancelResponseReport.setTotalCount(failRecordList.size() + successRecordList.size());
        } finally {
            // 解锁
            for (String sn : lockSnList) {
                try {
                    RedissLockUtil.unlock(CREATE_CANCEL_ORDER_LOCK_PREFIX + sn);
                } catch (Exception e) {
                    log.error("[ 撤单创建 ] 解锁失败, sn: {}", sn);
                }
            }
        }
        return inventoryCancelResponseReport;
    }

    public List<InventoryCancelResponseInfoVo> batchCreateCancel(List<CustomsInventoryCancelDTO> list, Map<Long, CustomsInventoryDTO> orderIdInventoryMap, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap, String reason, ExecutorService executorService) {
        // 保存所有任务的CompletableFuture
        List<CompletableFuture<InventoryCancelResponseInfoVo>> futures = new ArrayList<>();

        list = list.stream().filter(cancel -> {
            CustomsInventoryDTO customsInventoryDTO = orderIdInventoryMap.get(cancel.getRefOrderId());
            if (Objects.isNull(customsInventoryDTO)) {
                return false;
            }
            OrderDTO orderDTO = orderSnInvSnOrderMap.get(customsInventoryDTO.getOrderSn(), cancel.getRefInvoSn());
            if (Objects.isNull(orderDTO)) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());

        Long tenantId = SimpleTenantHelper.getTenantId();
        for (CustomsInventoryCancelDTO customsInventoryCancelDTO : list) {

            CurrentUserInfo currentUserInfo = SimpleUserHelper.getCurrentUserInfo();
            CompletableFuture<InventoryCancelResponseInfoVo> future = CompletableFuture.supplyAsync(() -> {
                SimpleTenantHelper.setTenantId(tenantId);
                // 日志获取操作人丢失
                SimpleUserHelper.setCurrentUserInfo(currentUserInfo);
                stringRedisTemplate.opsForValue().set("ccs:inventory:cancel:create:operator:" + customsInventoryCancelDTO.getRefOrderId(),
                        UserUtils.getUserRealName(), 3, TimeUnit.SECONDS);
                // 你的业务逻辑
                customsInventoryCancelRpc.customsInventoryCancelInsert(customsInventoryCancelDTO);
                customsInventoryCalloffCRpc.upset(customsInventoryCancelDTO.getRefOrderId(), customsInventoryCancelDTO.getRefInvoSn(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode(), InventoryCalloffStatusEnum.CALLOFF_ING.getCode(), reason);
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryCancelDTO.getRefOrderSn(), customsInventoryCancelDTO.getInvoNo(), customsInventoryCancelDTO.getMailNo(), "创建撤单成功");

                //修改内部流转状态
                CustomsInventoryDTO customsInventoryDTO = orderIdInventoryMap.get(customsInventoryCancelDTO.getRefOrderId());
                OrderDTO orderDTO = orderSnInvSnOrderMap.get(customsInventoryDTO.getOrderSn(), customsInventoryCancelDTO.getRefInvoSn());
                orderCRpc.updateOrderInternalStatusBySnList(Lists.newArrayList(orderDTO.getSn()), OrderInternalEnum.CANCELING.getCode());
                OrderExtra orderExtra = JSON.parseObject(orderDTO.getExtraJson(), OrderExtra.class);
                List<String> subscribeList = Lists.newArrayList("PLAT-PANGU", "CHANNEL-" + orderExtra.getSubmit().getChannel());
                //手动撤单发送回执
                String activeData = "";
                if (!Objects.equals(customsInventoryDTO.getCustomsStatus(), CustomsStat.CUSTOMS_PASS.getValue())) {
                    List<Integer> statusList = Arrays.asList(CustomsActionStatus.DEC_WAIT.getValue(), CustomsActionStatus.DEC_ING.getValue());
                    if (statusList.contains(customsInventoryDTO.getStatus()) && orderDTO.getExceptionFlag()) {
                        activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.CLOSE_SUCCESS.getCode().toString(), customsInventoryCancelDTO.getCustomsCheckDetail(), new Date()));
                    }
                } else {
                    activeData = JSON.toJSONString(new OrderActiveInfo(orderDTO.getId()).buildCustomsInfo(InventoryCalloffStatus.START_CANCEL_ORDERS.getCode().toString(), customsInventoryCancelDTO.getCustomsCheckDetail(), new Date()));
                }

                log.info("[op:createMessage] 发送回执 - {}", activeData);
                if (!StringUtils.isEmpty(activeData)) {
                    messageService.createMessage(MessageType.ORDER_CUSTOMS_CANCEL, subscribeList, customsInventoryCancelDTO.getRefOrderSn(), activeData, "");
                }
                return inventoryCancelResponse;
            }, executorService);

            futures.add(future);
        }

        // 等待所有任务完成并收集结果
        return futures.stream()
                .map(CompletableFuture::join)
                .collect(Collectors.toList());
    }

    private List<CustomsInventoryCancelDTO> buildCustomsInventoryCancelDTOS(HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap, Map<String, CustomsInventoryDTO> invMap, String reason) {
//        List<CustomsInventoryDTO> customsInventory = new ArrayList<>();
        Map<CustomsInventoryDTO, OrderDTO> customsInventoryOrderDTOMap = new HashMap<>();
        for (String orderSn : orderSnInvSnOrderMap.rowKeySet()) {
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            if (customsInventoryDTO != null) {
                customsInventoryOrderDTOMap.put(customsInventoryDTO, orderSnInvSnOrderMap.get(orderSn, customsInventoryDTO.getSn()));
            }
        }
        return customsInventoryCancelRpc.batchBuildCustomsInventoryCancelDTO(customsInventoryOrderDTOMap, reason);
    }

    private void checkRefundOrder(List<OrderDTO> orderList, Map<String, CustomsInventoryDTO> invMap, List<InventoryCancelResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        List<Long> orderIdList = orderList.stream().map(OrderDTO::getId).distinct().collect(Collectors.toList());
        List<RefundOrderInfoDto> refundOrderInfoDtoList = refundOrderRpc.findByOrderId(orderIdList);
        Map<Long, List<RefundOrderInfoDto>> map = refundOrderInfoDtoList.stream().collect(Collectors.groupingBy(RefundOrderInfoDto::getRefDeclareId));
        for (OrderDTO orderDTO : orderList) {
//            boolean checkRefundOrderInfoIsExist = refundOrderRpc.checkRefundOrderInfoIsExist(orderDTO.getId());
            if (map.containsKey(orderDTO.getId())) {
//            if (checkRefundOrderInfoIsExist) {
                CustomsInventoryDTO customsInventoryDTO = invMap.get(orderDTO.getCustomsInventorySn());
                if (customsInventoryDTO != null) {
                    InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), orderDTO.getCustomsInventorySn(),
                            "申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在退货单,不能创建撤单");
                    failRecordList.add(inventoryCancelResponse);
                }
                orderSnInvSnOrderMap.remove(orderDTO.getSn(), orderDTO.getCustomsInventorySn());
            }
        }
    }

    private void checkInventoryCalloff(List<CustomsInventoryCalloffDTO> listByOrderIdList, Map<String, CustomsInventoryDTO> invMap, List<InventoryCancelResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Map<String, List<CustomsInventoryCalloffDTO>> snCalloffMap = listByOrderIdList.stream().collect(Collectors.groupingBy(CustomsInventoryCalloffDTO::getOrderSn));
        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String orderSn = iterator.next();
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            List<CustomsInventoryCalloffDTO> callOffList = snCalloffMap.get(orderSn);
            boolean flag = false;
            for (CustomsInventoryCalloffDTO callOff : callOffList) {
                if (callOff != null &&
                        (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equals(callOff.getCalloffStatus()) || InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equals(callOff.getCalloffStatus()))) {
                    flag = true;
                    break;
                }
            }
            if (flag) {
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(),
                        "申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在相关状态的取消单,不能创建撤单");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            }
        }

        List<String> logisticsNoList = new ArrayList<>();
        for (String orderSn : orderSnInvSnOrderMap.rowKeySet()) {
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            if (customsInventoryDTO != null) {
                logisticsNoList.add(customsInventoryDTO.getLogisticsNo());
            }
        }
        List<HandoverOrderDetailDTO> handoverOrderDetail = handoverOrderDetailService.getHandoverOrderDetailByMailNo(logisticsNoList);
        Map<String, List<HandoverOrderDetailDTO>> collect = handoverOrderDetail.stream().collect(Collectors.groupingBy(HandoverOrderDetailDTO::getDeclareOrderNo));

        Iterator<String> iter = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iter.hasNext()) {
            String orderSn = iter.next();
            List<CustomsInventoryCalloffDTO> callOffList = snCalloffMap.get(orderSn);
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);

            List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = collect.get(customsInventoryDTO.getDeclareOrderNo());
            // 取消单发起撤单时，【小巨人】标记的取消单不校验，“有关联的交接单时而不允许撤单” 菜鸟逆向申报需求
            CustomsInventoryCalloffDTO customsInventoryCalloffDTO = callOffList.get(0);
            Integer orderTag = customsInventoryCalloffDTO.getOrderTag();
            List<Integer> orderTags = InventoryCalloffOrderTagEnums.getOrderTags(orderTag);
            if (!orderTags.contains(InventoryCalloffOrderTagEnums.XIAOJUREN.getCode())) {
                if (!CollectionUtils.isEmpty(handoverOrderDetailDTOS)) {
                    InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(),
                            "申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在关联的交接单，无法操作撤单");
                    failRecordList.add(inventoryCancelResponse);
                    iter.remove();
                }
            }
        }
    }

    private void checkInventoryCancel(List<CustomsInventoryCancelDTO> cancelList, Map<String, CustomsInventoryDTO> invMap, List<InventoryCancelResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Map<String, CustomsInventoryCancelDTO> cancelMap = cancelList.stream().collect(Collectors.toMap(CustomsInventoryCancelDTO::getRefOrderSn, Function.identity(), (k1, k2) -> k1));

        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String orderSn = iterator.next();
            CustomsInventoryCancelDTO customsInventoryCancelDTO = cancelMap.get(orderSn);
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            if (customsInventoryCancelDTO != null) {
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(), "申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]相关清单撤单已存在");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            }
        }
    }

    private void checkCustomsInventoryList(Map<String, CustomsInventoryDTO> invMap, List<InventoryCancelResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String sn = iterator.next();
            CustomsInventoryDTO customsInventoryDTO = invMap.get(sn);
            // 如果获取到的海关清单数据对象为null，说明存在参数错误，查询对象为空的情况
            if (customsInventoryDTO == null) {
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse("UNKNOW", "-", ",", "参数错误查询对象为空");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                // 检查海关清单的状态，如果清单状态不等于成功状态（CustomsActionStatus.DEC_SUCCESS.getValue()），说明当前清单状态不符合撤单要求
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(),
                        "清单状态[" + CustomsActionStatus.getEnum(customsInventoryDTO.getStatus()).getDesc() + "],无法操作撤单");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            } else if (customsInventoryDTO.getExitRegionStatus() != 0) {
                // 检查海关清单的出区状态，如果出区状态不等于0（具体含义需根据业务定义，可能表示出区存在异常等不符合撤单的情况）
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(),
                        "清单目前出区状态无法操作撤单");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            } else if (!InventoryCancelEnum.contains(customsInventoryDTO.getCustomsStatus(), CancelInventoryAction.INVENTORY_CANCEL_APPALY)) {
                // 检查海关清单的海关状态是否在支持撤单的状态范围内（通过InventoryCancelEnum工具类或相关逻辑来判断），如果不支持撤单操作
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(customsInventoryDTO.getDeclareOrderNo(), customsInventoryDTO.getInventoryNo(), customsInventoryDTO.getLogisticsNo(),
                        "申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]相关清单状态不支持撤单");
                failRecordList.add(inventoryCancelResponse);
                iterator.remove();
            }
        }

    }

    private InventoryCancelResponseInfoVo createInventoryCancelResponse(String declareNo, String invNo, String logisticsNo, String message) {
        InventoryCancelResponseInfoVo inventoryCancelResponseInfoVo = new InventoryCancelResponseInfoVo();
        inventoryCancelResponseInfoVo.setDeclareNo(declareNo);
        inventoryCancelResponseInfoVo.setInveNo(invNo);
        inventoryCancelResponseInfoVo.setLogisticsNo(logisticsNo);
        inventoryCancelResponseInfoVo.setMessage(message);
        return inventoryCancelResponseInfoVo;
    }


    /**
     * @param orderList
     * @param failRecordList
     * @param lockSnList
     * @param snList
     */
    private void checkCreateCancelInProgress(List<OrderDTO> orderList, List<InventoryCancelResponseInfoVo> failRecordList, List<String> lockSnList, List<String> snList) {
        Iterator<OrderDTO> orderIterator = orderList.iterator();
        List<String> snsToRemove = new ArrayList<>();
        //循环中直接操作集合可能导致并发修改异常（如果该方法在多线程环境下被调用的话），可以考虑使用迭代器来安全地移除元素
        while (orderIterator.hasNext()) {
            OrderDTO orderDTO = orderIterator.next();
            String orderSn = orderDTO.getSn();
            if (!RedissLockUtil.tryLock(CREATE_CANCEL_ORDER_LOCK_PREFIX + orderSn, TimeUnit.SECONDS, 0, -1)) {
                InventoryCancelResponseInfoVo inventoryCancelResponse = createInventoryCancelResponse(orderDTO.getDeclareOrderNo(), null, null, "撤单生成中，请勿重复生成");
                failRecordList.add(inventoryCancelResponse);
                snsToRemove.add(orderSn);
            } else {
                lockSnList.add(orderSn);
            }
        }
        // 统一移除需要移除的sn
        snList.removeAll(snsToRemove);
    }
}
