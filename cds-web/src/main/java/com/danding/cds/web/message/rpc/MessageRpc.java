package com.danding.cds.web.message.rpc;

import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.message.api.dto.MessageSearch;
import com.danding.cds.message.api.dto.MessageSubscribeSearch;
import com.danding.cds.message.api.dto.MessageTaskSearch;
import com.danding.cds.message.api.vo.CreateMessageReqVO;
import com.danding.cds.web.message.vo.MessageSearchVO;
import com.danding.cds.web.message.vo.MessageSubscribeSearchVO;
import com.danding.cds.web.message.vo.MessageTaskSearchVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface MessageRpc {

    RpcResult<ListVO<MessageSubscribeSearchVO>> subscribePaging(MessageSubscribeSearch search);

    RpcResult<ListVO<MessageTaskSearchVO>> taskPaging(MessageTaskSearch search);

    RpcResult<ListVO<MessageSearchVO>> paging(MessageSearch search);

    RpcResult retry(IdsParam param);

    RpcResult taskRetry(IdsParam param);

    RpcResult<List<SelectOptionVO<Long>>> listSubscribe();

    RpcResult<List<SelectOptionVO<String>>> listMessageType();

    RpcResult<List<SelectOptionVO<Integer>>> listTaskStatus();

    RpcResult<List<SelectOptionVO<Integer>>> listStatus();

    RpcResult<String> createMessageBatch(CreateMessageReqVO reqVO);

    RpcResult<List<SelectOptionVO<String>>> listMessageContentType(String messageType);
}
