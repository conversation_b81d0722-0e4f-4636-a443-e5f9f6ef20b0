package com.danding.cds.web.suningFP;

import lombok.Data;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"loadContentId", "outorderId"})
@XmlRootElement(name = "loadContent")
public class SuNingFPLoadContent {
    @XmlElement(name = "loadContentId")
    private String loadContentId;
    @XmlElement(name = "outorderId")
    private String outorderId;
}
