package com.danding.cds.web.payChannel;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.danding.cds.payChannel.api.dto.*;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.customs.payment.api.service.CustomsPaymentService;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountResult;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountExcelVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.business.common.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 9:48
 * @Description:
 */
@Slf4j
@RestController
@Api( tags = "商户管理" )
@RequestMapping("/pay/payMerchantAccount")
public class PayMerchantAccountController {
    @DubboReference
    private PayMerchantAccountService payMerchantAccountService;
    @DubboReference
    private CustomsPaymentService customsPaymentService;

    @GetMapping("/paging")
    @ApiOperation(value = "分页查询")
    public ListVO<PayMerchantAccountResult> paging(PayMerchantAccountSearch search){
        ListVO<PayMerchantAccountDTO> paging = payMerchantAccountService.paging(search);
        ListVO<PayMerchantAccountResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((PayMerchantAccountDTO item)->{
            PayMerchantAccountResult vo = new PayMerchantAccountResult();
            BeanUtils.copyProperties(item,vo);
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }

    @ApiOperation(value = "新增|更新商户")
    @PostMapping("/upset")
    public Long upset(@RequestBody PayMerchantAccountSubmit submit) throws ArgsErrorException {
        return payMerchantAccountService.upset(submit);
    }

//    @ApiOperation(value = "根据ID查询",response = PayMerchantAccountResult.class)
//    @GetMapping("/getById")
//    public PayMerchantAccountResult getById(Long id){
////        customsPaymentService.saveTestCustomsPayment();
////        customsPaymentService.saveTestSendPayment();
//        customsPaymentService.rePush(489062561606533120L);
//        PayMerchantAccountResult result = new PayMerchantAccountResult();
//        PayMerchantAccountDTO item = payMerchantAccountService.findById(id);
//        BeanUtils.copyProperties(item,result);
//
//        return result;
//    }

    @ApiOperation(value = "导出商户列表Excel")
    @GetMapping("/exportPayMerchantAccount")
    public void exportPayMerchantAccount(PayMerchantAccountSearch search, HttpServletResponse httpServletResponse) {
        List<PayMerchantAccountDTO> list = payMerchantAccountService.queryListPayMerchantAccountExport(search);
        List<PayMerchantAccountExcelVO> excelList = new ArrayList();
        for (PayMerchantAccountDTO item : list) {
            PayMerchantAccountExcelVO payMerchantAccountVO = new PayMerchantAccountExcelVO();
            BeanUtils.copyProperties(item, payMerchantAccountVO);
            payMerchantAccountVO.setCreateTime(item.getCreateTime() != null? DateUtils.formatDate(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"):"");
            excelList.add(payMerchantAccountVO);
        }
        String currentDateStr = DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        String title = "商户管理" + currentDateStr;
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PayMerchantAccountExcelVO.class, excelList);
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(title.getBytes(), "iso8859-1") + ".xlsx");
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.getOutputStream().flush();
        } catch (Exception e) {
            log.error("商户管理Excel导出失败", e);
        }
    }
}
