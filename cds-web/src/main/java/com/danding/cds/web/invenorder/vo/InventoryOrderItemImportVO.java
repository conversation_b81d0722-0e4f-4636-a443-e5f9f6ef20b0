package com.danding.cds.web.invenorder.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
public class InventoryOrderItemImportVO {
    @Excel(name = "账册备案序号")
    @ExcelProperty(value = "账册备案序号")
    private String goodsSeqNo;

    @Excel(name = "*统一料号")
    @ExcelProperty(value = "*统一料号")
    @NotBlank(message = "统一料号不能为空")
    private String productId;

    @Excel(name = "海关备案料号")
    @ExcelProperty(value = "海关备案料号")
    private String customsRecordProductId;

    @Excel(name = "*申报数量")
    @ExcelProperty(value = "*申报数量")
    @NotNull(message = "申报数量不能为空")
    private BigDecimal declareQty;

    @Excel(name = "申报单价")
    @ExcelProperty(value = "申报单价")
    private BigDecimal declarePrice;

    @Excel(name = "币制【编码】")
    @ExcelProperty(value = "币制【编码】")
    private String currency;

    @Excel(name = "规格型号")
    @ExcelProperty(value = "规格型号")
    private String goodsModel;

}
