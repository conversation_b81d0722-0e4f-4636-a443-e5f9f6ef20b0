package com.danding.cds.web.callback.vo;

import lombok.Data;

import java.util.Date;

@Data
public class CallbackLogVO {
    /**
     * id
     */
    private String id;

    /**
     * 回调地址
     */
    private String retryUrl;

    /**
     * 回执海关类型
     */
    private String customsType;

    private String customsTypeDesc;

    /**
     * 回执类型
     */
    private String callbackType;

    private String callbackTypeDesc;

    /**
     * 回执内容
     */
    private String content;

    /**
     * 请求头
     */
    private String requestHead;

    /**
     * 请求参数
     */
    private String requestParams;

    /**
     * 是否需要加密
     */
    private Integer decipher;

    private String decipherDesc;

    /**
     * 业务编号
     */
    private String businessCode;

    /**
     * 业务类型
     */
    private String businessType;

    /**
     * 口岸
     */
    private String customs;

    private String customsDesc;

    /**
     * 重试次数
     */
    private Integer retryTime;

    /**
     * 海关回执时间
     */
    private Date customsTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 回执性质
     */
    private Integer callbackProperty;
    private String callbackPropertyDesc;
}
