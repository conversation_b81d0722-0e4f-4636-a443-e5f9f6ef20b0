package com.danding.cds.web.payment.rpc;

import com.danding.cds.customs.payment.api.service.dto.PaymentSearch;
import com.danding.cds.web.payment.rpc.param.RpcPaymentStatusParam;
import com.danding.cds.web.payment.vo.CustomsPaymentResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface PaymentRpc {

    RpcResult<ListVO<CustomsPaymentResult>> paging(PaymentSearch search);

    RpcResult<String> rePush(String sns);

    RpcResult<String> manualUpdStatus(RpcPaymentStatusParam param);

}
