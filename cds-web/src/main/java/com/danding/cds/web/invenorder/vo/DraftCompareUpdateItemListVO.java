package com.danding.cds.web.invenorder.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 草单比对更新表体列表
 */
@Data
public class DraftCompareUpdateItemListVO {

    /**
     * 比对类型
     */
    private Integer compareType;

    /**
     * 表体列表
     */
    List<ItemVO> itemList;

    @Data
    public static class ItemVO {

        /**
         * 表体id
         */
        private Long id;

        /**
         * 来源
         */
        private String source;

        /**
         * 申报数量
         */
        private BigDecimal declareUnitQfy;

        /**
         * 法定数量（总）
         */
        private BigDecimal firstUnitQfy;

        /**
         * 第二法定数量（总）
         */
        private BigDecimal secondUnitQfy;

        /**
         * 币制
         */
        private String currency;

        /**
         * 申报单价
         */
        private BigDecimal declarePrice;

        /**
         * 原产国
         */
        private String originCountry;

        /**
         * 总毛重
         */
        private BigDecimal totalGrossWeight;

        /**
         * 总净重
         */
        private BigDecimal totalNetWeight;
    }
}
