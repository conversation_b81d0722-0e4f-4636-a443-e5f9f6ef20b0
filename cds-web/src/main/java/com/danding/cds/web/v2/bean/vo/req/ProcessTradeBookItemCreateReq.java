package com.danding.cds.web.v2.bean.vo.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessTradeBookItemCreateReq implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    @NotNull(message = "关联账册id不能为空")
    private Long refBookId;

    /**
     * 序号
     */
    @NotNull(message = "序号不能为空")
    private Integer seqNo;


    /**
     * 表体类型 1-料件 2-成品
     */
    @ApiModelProperty(value = "表体类型 1-料件 2-成品")
    @NotNull(message = "表体类型不能为空")
    private Integer goodsType;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    @NotBlank(message = "料号不能为空")
    private String productId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    @NotBlank(message = "商品编码不能为空")
    private String hsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    @NotBlank(message = "商品名称不能为空")
    private String goodsName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    @NotBlank(message = "规格型号不能为空")
    private String goodsModel;

    /**
     * 法定计量单位
     */
    @ApiModelProperty(value = "法定计量单位")
    @NotBlank(message = "法定计量单位不能为空")
    private String legalUnit;

    /**
     * 法定第二计量单位
     */
    @ApiModelProperty(value = "法定第二计量单位")
    private String legalSecondUnit;

    /**
     * 修改标志，默认3-增加
     */
    @ApiModelProperty(value = "修改标志，默认3-增加")
    @NotNull(message = "修改标志不能为空")
    private Integer modifyFlag;

    /**
     * 申报单价
     */
    @ApiModelProperty(value = "申报单价")
    private BigDecimal declareUnitPrice;

    /**
     * 币制
     */
    @ApiModelProperty(value = "币制")
    private String currency;

    /**
     * 申报计量单位
     */
    @ApiModelProperty(value = "申报计量单位")
    @NotBlank(message = "申报计量单位不能为空")
    private String declareUnit;

    /**
     * 申报数量
     */
    @ApiModelProperty(value = "申报数量")
    private Long declareQty;

    /**
     * 征免方式，默认3-全免
     */
    @ApiModelProperty(value = "征免方式，默认3-全免")
    private Integer dutyExemptionMethod;

    /**
     * 企业执行标志，默认1-运行
     */
    @ApiModelProperty(value = "企业执行标志，默认1-运行")
    @NotNull(message = "企业执行标志不能为空")
    private Integer companyExecutionFlag;

    /**
     * 重点商品标识，默认0-非重点商品
     */
    @ApiModelProperty(value = "重点商品标识，默认0-非重点商品")
    @NotNull(message = "重点商品标识不能为空")
    private Integer focusMark;

    /**
     * 国别(地区)
     */
    @ApiModelProperty(value = "国别(地区)")
    private String countryRegion;

    /**
     * 海关执行标志，默认1-正常执行
     */
    @ApiModelProperty(value = "海关执行标志，默认1-正常执行")
    private Integer customsExecutionFlag;

    /**
     * 期初数量，默认0
     */
    @ApiModelProperty(value = "期初数量，默认0")
    private Integer initialQty;

    /**
     * 数量控制标志，默认2-不控制数量
     */
    @ApiModelProperty(value = "数量控制标志，默认2-不控制数量")
    private Integer qtyControlFlag;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 批准最大余数量
     */
    @ApiModelProperty(value = "批准最大余数量")
    private BigDecimal maxRemainQty;

    /**
     * 单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0
     */
    @ApiModelProperty(value = "单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0")
    private Integer consumptionQuestionFlag;

    /**
     * 磋商标志 0-表示未磋商  1-表示磋商中 默认为0
     */
    @ApiModelProperty(value = "磋商标志 0-表示未磋商  1-表示磋商中 默认为0")
    private Integer negotiationFlag;

}
