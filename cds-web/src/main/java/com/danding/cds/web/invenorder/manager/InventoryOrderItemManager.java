package com.danding.cds.web.invenorder.manager;

import com.danding.cds.common.enums.InventoryTransitEnums;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemRecord;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemWritingReport;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.JdGoodsRecordCustoms;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.JdGoodsRecordService;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service
public class InventoryOrderItemManager {

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private JdGoodsRecordService jdGoodsRecordService;

    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;

    @DubboReference
    private JdServProviderService jdServProviderService;

    public InventoryOrderItemWritingReport writing(Long id, List<InventoryOrderItemRecord> importList) throws ArgsErrorException {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        Boolean isTransitMaster = this.isTransitMaster(inventoryOrderInfoDTO);
        CustomsBookDTO customsBookDTO = customsBookService.findById(inventoryOrderInfoDTO.getBookId());
        JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(inventoryOrderInfoDTO.getBookId());
        List<InventoryOrderItemRecord> addSuccessList = new ArrayList<>();
        List<InventoryOrderItemRecord> addFailList = new ArrayList<>();
        List<String> newAddList = new ArrayList<>();
        boolean isNew;
        for (InventoryOrderItemRecord record : importList) {
            String errorMsg = "";
            if (StringUtils.isEmpty(record.getProductId())) {
                errorMsg += "统一料号为空;";
            }
            //判断申报单价和币值是否都为空 或者都填了
            if ((record.getDeclarePrice() == null) == (record.getCurrency() != null)) {
                errorMsg += "统一料号[" + record.getProductId() + "],申报单价或币制填充时，两者任意不可为空;";
            } else if (record.getCurrency() != null) {
                CustomsCurrencyDTO currency = customsCurrencyService.findByCode(record.getCurrency());
                if (currency == null) {
                    errorMsg += "统一料号[" + record.getProductId() + "]币制编码不存在;";
                }
            }
            if (record.getDeclarePrice() != null && record.getDeclarePrice().compareTo(BigDecimal.ZERO) <= 0) {
                    errorMsg += "统一料号[" + record.getProductId() + "]申报单价不能小于0;";
            }
            if (Objects.isNull(record.getDeclareQty())) {
                errorMsg += "统一料号[" + record.getProductId() + "]申报数量不能为空;";
            }
            GoodsRecordDTO goodsRecordDTO = null;
            if (isTransitMaster) {
                //中转新品，没有账册维度
                List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(record.getProductId());
                if (CollectionUtils.isEmpty(goodsRecordDTOList)) {
                    errorMsg += "系统商品备案不存在统一料号为[" + record.getProductId() + "]的商品信息;";
                } else {
                    goodsRecordDTO = goodsRecordDTOList.get(0);
                }
//            } else if (StringUtils.isEmpty(record.getGoodsSeqNo()) && 10 == inventoryOrderInfoDTO.getBookId() && EnvironmentConfig.isOnline()) {
            } else if (StringUtils.isEmpty(record.getGoodsSeqNo()) && Objects.nonNull(jdServProviderDTO)) {
                // 线上78账册的新品直接去京东备案找 && 中转账册不校验京东备案
                try {
                    JdGoodsRecordCustoms jdGoodsRecordCustoms = jdGoodsRecordService.getGoodsInfoByItemCode(record.getProductId(), jdServProviderDTO.getCode(), true);
                    if (jdGoodsRecordCustoms == null) {
                        errorMsg += "京东备案管理不存在商品货号为[" + record.getProductId() + "]备案完成的备案信息;";
                    }

                } catch (ArgsErrorException e) {
                    errorMsg += e.getErrorMessage();
                }
            } else {
                goodsRecordDTO = goodsRecordService.findByBookIdAndProId(inventoryOrderInfoDTO.getBookId(), record.getProductId());
                if (Objects.isNull(goodsRecordDTO)) {
                    errorMsg += "系统商品备案内不存在统一料号为[" + record.getProductId() + "],账册编号为[" + customsBookDTO.getBookNo() + "]的商品信息";
                }
            }
            //根据备案序号是否存在区分是否为新品
            if (!StringUtils.isEmpty(record.getGoodsSeqNo())) {
                isNew = false; //老品
                CustomsBookItemDTO customsBookItemDTO;
                if (StringUtils.isNotEmpty(record.getCustomsRecordProductId())) {
                    customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(inventoryOrderInfoDTO.getBookId(), record.getGoodsSeqNo(), record.getCustomsRecordProductId());
                } else {
                    customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNo(inventoryOrderInfoDTO.getBookId(), record.getGoodsSeqNo());
                }
                if (customsBookItemDTO == null) {
                    isNew = true;
                    errorMsg += "系统海关账册[" + customsBookDTO.getBookNo() + "]内不存在备案序号为[" + record.getGoodsSeqNo() + "]的商品信息;";
                }

            } else {
                isNew = true; //新品
                List<CustomsBookItemDTO> bookItemDTOList = customsBookItemService.findByBookIdAndProId(inventoryOrderInfoDTO.getBookId(), record.getProductId());
                if (CollectionUtils.isNotEmpty(bookItemDTOList)) {
                    isNew = false;
                    errorMsg += "系统海关账册[" + customsBookDTO.getBookNo() + "]内已存在料号为[" + record.getProductId() + "]的商品信息，不属于新品;";
                }
            }
            if (isNew) {
                if (!isTransitMaster && Objects.nonNull(goodsRecordDTO)) {
                    String finalProductId = goodsRecordAssociateService.getFinalProductId(goodsRecordDTO, customsBookDTO.getId());
                    //新品
                    if (!StringUtils.isEmpty(record.getCustomsRecordProductId())) {
                        //海关备案存在 - 校验是否一致
                        if (!Objects.equals(record.getCustomsRecordProductId(), finalProductId)) {
                            errorMsg += "系统商品备案不存在账册编号为[" + customsBookDTO.getBookNo() + "],统一料号为[" + record.getProductId() + "],海关备案料号为[" + record.getCustomsRecordProductId() + "]的商品信息";
                        }
                    }
                }
            }
            String tagTemp = (!StringUtils.isEmpty(record.getGoodsSeqNo()) ? record.getGoodsSeqNo() : "无") + "-" + record.getProductId();
            if (newAddList.contains(tagTemp)) {
                errorMsg += "重复的商品;";
            }
            if (StringUtils.isEmpty(errorMsg)) {
                String tag = (!StringUtils.isEmpty(record.getGoodsSeqNo()) ? record.getGoodsSeqNo() : "无") + "-" + record.getProductId();
                newAddList.add(tag);
                addSuccessList.add(record);
            } else {
                record.setErrorMsg(errorMsg);
                addFailList.add(record);
            }
        }

        InventoryOrderItemWritingReport report = new InventoryOrderItemWritingReport();
        report.setTotalCount(importList.size());
        report.setSuccessCount(addSuccessList.size());
        report.setFailCount(addFailList.size());
        report.setSuccessRecordList(addSuccessList);
        report.setFailRecordList(addFailList);
        return report;
    }

    private Boolean isTransitMaster(InventoryOrderInfoDTO infoDTO) {
        if (Objects.isNull(infoDTO)) {
            return false;
        }
        return Objects.equals(infoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode());
    }
}
