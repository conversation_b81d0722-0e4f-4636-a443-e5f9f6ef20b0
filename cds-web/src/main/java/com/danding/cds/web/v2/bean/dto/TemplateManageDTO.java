package com.danding.cds.web.v2.bean.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 模板管理DTO
 * @date 2025/7/31
 */
@Data
public class TemplateManageDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String templateName;

    /**
     * 用途
     */
    private String purpose;

    /**
     * 口岸（多个以逗号分隔）
     */
    private String ports;

    /**
     * 备注
     */
    private String remark;

    /**
     * 文件路径
     */
    private String filePath;

    /**
     * 原始文件名
     */
    private String originalFileName;

    /**
     * 创建者ID
     */
    private Long creatorId;

    /**
     * 创建者姓名
     */
    private String creatorName;

    /**
     * 上传时间
     */
    private Date uploadTime;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 是否删除
     */
    private Boolean deleted;
}
