package com.danding.cds.web.payChannel.rpc;

import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSubmit;
import com.danding.cds.web.payChannel.PayMerchantCustomsInfoController;
import com.danding.cds.web.payChannel.vo.PayMerchantCustomsInfoResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/11/3 13:08
 * @Description:
 */

@DubboService
public class PayMerchantCustomsInfoRpcImpl implements PayMerchantCustomsInfoRpc {
    @Autowired
    private PayMerchantCustomsInfoController payMerchantCustomsInfoController;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantCustomsInfo/paging", desc = "报关资质分页查询")
    public RpcResult<ListVO<PayMerchantCustomsInfoResult>> paging(PayMerchantCustomsInfoSearch search) {
        return RpcResult.success(payMerchantCustomsInfoController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantCustomsInfo/upset", desc = "新增|更新报关资质")
    public RpcResult<Long> upset(@RequestBody PayMerchantCustomsInfoSubmit submit) {
        return RpcResult.success(payMerchantCustomsInfoController.upset(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantCustomsInfo/getById", desc = "根据ID查询")
    public RpcResult<PayMerchantCustomsInfoResult> getById(Long id) {
        return RpcResult.success(payMerchantCustomsInfoController.getById(id));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantCustomsInfo/exportPayMerchantCustomsInfo", desc = "导出报关资质列表Excel")
    public void exportPayMerchantCustomsInfo(PayMerchantCustomsInfoSearch search, HttpServletResponse httpServletResponse) {
        payMerchantCustomsInfoController.exportPayMerchantCustomsInfo(search, httpServletResponse);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantCustomsInfo/listCustoms", desc = "报关资质-口岸列表")
    public RpcResult<List<SelectItemVO>> listCustoms() {
        return RpcResult.success(payMerchantCustomsInfoController.listCustoms());
    }
}
