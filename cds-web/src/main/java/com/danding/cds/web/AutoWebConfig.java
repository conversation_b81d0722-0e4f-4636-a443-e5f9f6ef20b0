package com.danding.cds.web;

import com.danding.business.client.rpc.user.facade.IUserDataRpcFacade;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.concurrent.ThreadPoolExecutor;

@Slf4j
@Component("autoWebConfig")
@SpringBootConfiguration
public class AutoWebConfig {

    @DubboReference
    private IUserDataRpcFacade iUserDataRpcFacade;


    @Bean(name = "reconciliationThreadExecutor")
    public ThreadPoolTaskExecutor reconciliationThreadExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        executor.setCorePoolSize(8);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(20000);
        executor.setWaitForTasksToCompleteOnShutdown(true);
        executor.setThreadNamePrefix("reconciliationThreadExecutor-");
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        return executor;
    }
}
