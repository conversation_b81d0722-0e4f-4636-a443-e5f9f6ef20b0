package com.danding.cds.web.exception.rpc;

import com.danding.cds.exception.api.enums.ExceptionTypeEnums;
import com.danding.cds.exception.api.service.ExceptionService;
import com.danding.cds.exception.api.vo.ExceptionReqVO;
import com.danding.cds.exception.api.vo.ExceptionUpsetReqVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/9
 */
@DubboService
@Slf4j
public class ExceptionRpcImpl implements ExceptionRpc{

    @DubboReference
    private ExceptionService exceptionService;

    @Override
    @SoulClient(path = "/exception/paging",desc = "异常管理分页查询")
    public RpcResult paging(ExceptionReqVO reqVO){
        return RpcResult.success(exceptionService.paging(reqVO));
    }

    @Override
    @SoulClient(path = "/exception/exceptionName",desc = "异常名称下拉")
    public RpcResult exceptionName(){
        return RpcResult.success(EnumUtils.build(exceptionService.getExceptionDOList(),"id","exceptionName"));
    }

    @Override
    @SoulClient(path = "/exception/upsetException",desc = "新增或编辑")
    public RpcResult upsetException(ExceptionUpsetReqVO upsetReqVO)throws ArgsErrorException {
        try {
            exceptionService.upsetException(upsetReqVO);
            return RpcResult.success(true);
        }catch (ArgsErrorException ex){
            return RpcResult.error(ex.getErrorMessage());
        }
    }


    @Override
    @SoulClient(path = "/exception/exceptionType",desc = "异常类型下拉")
    public RpcResult ExceptionType(){
        return RpcResult.success(EnumUtils.build(ExceptionTypeEnums.class,"code","desc"));
    }
}
