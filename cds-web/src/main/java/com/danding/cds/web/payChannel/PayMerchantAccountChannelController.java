package com.danding.cds.web.payChannel;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.common.utils.EncryptionUtil;
import com.danding.cds.payChannel.api.dto.*;
import com.danding.cds.payChannel.api.service.PayMerchantAccountChannelService;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountChannelExcelVO;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountChannelResult;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountExcelVO;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.business.common.utils.DateUtils;
import com.google.gson.JsonObject;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 10:09
 * @Description:
 */

@Slf4j
@RestController
@Api( tags = "收款渠道管理" )
@RequestMapping("/pay/payMerchantAccountChannel")
public class PayMerchantAccountChannelController {
    @DubboReference
    private PayMerchantAccountChannelService payMerchantAccountChannelService;

    @DubboReference
    private PayMerchantAccountService payMerchantAccountService;


    @GetMapping("/paging")
    @ApiOperation(value = "分页查询")
    public ListVO<PayMerchantAccountChannelResult> paging(PayMerchantAccountChannelSearch search) {
        ListVO<PayMerchantAccountChannelDTO> paging = payMerchantAccountChannelService.paging(search);
        ListVO<PayMerchantAccountChannelResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((PayMerchantAccountChannelDTO item) -> {
            PayMerchantAccountChannelResult vo = new PayMerchantAccountChannelResult();
            BeanUtils.copyProperties(item, vo);
            PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
            if (account != null) {
                vo.setMerchantName(account.getName());
            }
            AppKey appKey = JSONObject.parseObject(item.getTokenJson(), AppKey.class);
            vo.setAppId(appKey.getAppId());
            if (Objects.nonNull(appKey.getKey())) {
                vo.setKey(EncryptionUtil.getStringBuffer(appKey.getKey()));
                vo.setPartner(appKey.getPartner());
            }
            if (Objects.nonNull(appKey.getPartnerKey())) {
                vo.setKey(EncryptionUtil.getStringBuffer(appKey.getPartnerKey()));
                vo.setPartner(appKey.getMchId());
            }
            if (Objects.nonNull(appKey.getClientSecret())) {
                vo.setKey(EncryptionUtil.getStringBuffer(appKey.getClientSecret()));
                vo.setPartner(appKey.getClientId());
            }
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }


    @ApiOperation(value = "新增|更新收款渠道")
    @PostMapping("/upset")
    public Long upset(@RequestBody PayMerchantAccountChannelSubmit submit) throws ArgsErrorException {
        return payMerchantAccountChannelService.upset(submit);
    }

    @ApiOperation(value = "根据ID查询",response = PayMerchantAccountChannelResult.class)
    @GetMapping("/getById")
    public PayMerchantAccountChannelResult getById(Long id){
        PayMerchantAccountChannelResult result = new PayMerchantAccountChannelResult();
        PayMerchantAccountChannelDTO item = payMerchantAccountChannelService.findById(id);
        BeanUtils.copyProperties(item,result);
        PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
        if (account != null) {
            result.setMerchantName(account.getName());
        }
        return result;
    }

    @ApiOperation(value = "导出收款渠道列表Excel")
    @GetMapping("/exportPayMerchantAccountChannel")
    public void exportPayMerchantAccountChannel(PayMerchantAccountChannelSearch search, HttpServletResponse httpServletResponse) {
        List<PayMerchantAccountChannelDTO> list = payMerchantAccountChannelService.queryListPayMerchantAccountChannelExport(search);
        List<PayMerchantAccountChannelExcelVO> excelList = new ArrayList();
        for (PayMerchantAccountChannelDTO item : list) {
            PayMerchantAccountChannelExcelVO payMerchantAccountVO = new PayMerchantAccountChannelExcelVO();
            BeanUtils.copyProperties(item, payMerchantAccountVO);
            PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
            if (account != null) {
                payMerchantAccountVO.setMerchantName(account.getName());
            }
            excelList.add(payMerchantAccountVO);
        }
        String currentDateStr = DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        String title = "收款渠道管理" + currentDateStr;
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PayMerchantAccountChannelExcelVO.class, excelList);
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(title.getBytes(), "iso8859-1") + ".xlsx");
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.getOutputStream().flush();
        } catch (Exception e) {
            log.error("收款渠道管理Excel导出失败", e);
        }
    }
}

@Data
class AppKey {
    private String appId;
    private String key;
    private String partner;

    private String mchId;
    private String partnerKey;

    private String clientId;

    private String clientSecret;

}