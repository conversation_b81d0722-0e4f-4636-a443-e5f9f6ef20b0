package com.danding.cds.web.customs.rpc;

import com.danding.cds.c.api.bean.vo.CalloffEditRefundGoodsInfoReqVo;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParam;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.web.customs.rpc.param.RejectCalloffBatchReqVO;
import com.danding.cds.web.customs.rpc.param.RpcDeclareParam;
import com.danding.cds.web.customs.vo.CalloffInfoSumVO;
import com.danding.cds.web.customs.vo.InventoryCancelResponseReport;
import com.danding.cds.web.refund.vo.RefundResponseReport;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/10/15 18:11
 * @Description:
 */
public interface CustomsInventoryCalloffRpc {

    RpcResult<ListVO<CustomsInventoryCalloffVO>> paging(CustomsInventoryCalloffSearch search);

    RpcResult<InventoryCallOffOverTimeCountVO> inventoryCalloffOverTimeCount(CustomsInventoryCalloffSearch search);

//    RpcResult<Response<String>> createCalloff(Long orderId) ;

    RpcResult<Response<String>> rejectCalloff(Long id, String rejectReason);

    RpcResult<String> rejectCalloffBatch(RejectCalloffBatchReqVO reqVO);

    RpcResult<Response<String>> cancelReject(Long id);

    RpcResult<Response<String>> direct(Long id, String calloffReason);

//    RpcResult<Response<String>> returnCalloff(Long id, String calloffReason) ;
//
//    RpcResult<Response<String>> cancelCalloff(Long id, String calloffReason) ;

    RpcResult<CustomsInventoryCalloffCountDTO> calloffStatusCount(CustomsInventoryCalloffSearch search);

    RpcResult<CustomsInventoryCalloffCountDTO2> calloffOrderCount(CustomsInventoryCalloffSearch search);

    RpcResult<List<CalloffInfoSumVO>> sumInventoryCalloff(Long beginTimeLong, Long endTimeLong);

    RpcResult<RefundResponseReport> applyRefundOrder(RpcDeclareParam declareParam);

    RpcResult<InventoryCancelResponseReport> applyCancel(RpcDeclareParam declareParam);

    RpcResult<LoadTaskInfoDTO> calloffImportQuery();

    RpcResult<String> calloffImport(UrlParam urlParam);

    RpcResult<String> calloffImportConfirm();

    RpcResult<String> export(CustomsInventoryCalloffSearch search);

    RpcResult<String> uploadPic(Long id, String picUrl);

    RpcResult jdCancelOrderSynDel();

    RpcResult jdCancelOrderSynPreview(CustomsInventoryCalloffSubmit submit) throws ArgsErrorException;

    RpcResult jdCancelOrderDetails(CustomsInventoryCalloffSubmit submit);

    RpcResult listTimeOutDayEnum();

    RpcResult listCalloffTypeEnum();

    /**
     * 下拉清关单标记列表
     *
     * @return
     */
    RpcResult<List<SelectOptionVO<Integer>>> listCalloffOrderTag();

    RpcResult<Boolean> editRefundGoodsInfo(CalloffEditRefundGoodsInfoReqVo reqVO);
}