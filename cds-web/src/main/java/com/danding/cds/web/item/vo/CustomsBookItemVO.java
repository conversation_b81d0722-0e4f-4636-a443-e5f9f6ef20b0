package com.danding.cds.web.item.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Author: shenhui
 * @Date: 2020/5/11 17:17
 */
@Data
@ApiModel
public class CustomsBookItemVO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("商品料号")
    private String productId;

    @ApiModelProperty("账册id")
    private Integer customsBookId;

    @ApiModelProperty("账册编号")
    private String customsBookNo;

    @ApiModelProperty("金二序号")
    private String goodsSeqNo;

    @ApiModelProperty("HS编码")
    private String hsCode;

    @ApiModelProperty("料号库存id")
    private Long productStockId;

    @ApiModelProperty("统一料号")
    private String unifiedProductId;

    @ApiModelProperty("商品名称")
    private String goodsName;

    @ApiModelProperty("商品规格型号")
    private String goodsModel;

    @ApiModelProperty("申报币制")
    private String currCode;

    @ApiModelProperty("国别")
    private String originCountry;

    @ApiModelProperty("国别名称")
    private String originCountryName;

    @ApiModelProperty("申报计量单位")
    private String goodsUnit;

    @ApiModelProperty("申报计量单位名称")
    private String goodsUnitName;

    @ApiModelProperty("法定计量单位")
    private String firstUnit;

    @ApiModelProperty("法定计量单位名称")
    private String firstUnitName;

    @ApiModelProperty("法定第一计量单位数量")
    private BigDecimal firstUnitAmount;

    @ApiModelProperty("第二法定计量单位")
    private String secondUnit;

    @ApiModelProperty("第二法定计量单位名称")
    private String secondUnitName;

    @ApiModelProperty("法定第二计量单位数量")
    private BigDecimal secondUnitAmount;

    @ApiModelProperty("申报单价金额")
    private BigDecimal declarePrice = BigDecimal.ZERO;

    @ApiModelProperty("记账清单商品序号")
    private String invtGoodsNo;

    @ApiModelProperty("记账清单编号")
    private String invtNo;

    @ApiModelProperty("入仓数量")
    private Integer inQty;

    @ApiModelProperty("账册库存")
    private Integer accountNum;

    @ApiModelProperty("占用库存")
    private Integer occupiedNum;

    @ApiModelProperty("已用库存数量")
    private Integer usedNum;

    @ApiModelProperty("可用库存数量")
    private Integer availableNum;

    @ApiModelProperty("入仓法定数量")
    private BigDecimal inLegalQty;

    @ApiModelProperty("第二入仓法定数量")
    private BigDecimal inSecondLegalQty;

    @ApiModelProperty("最近入仓（核增）日期")
    private String inDate;

    @ApiModelProperty("平均美元单价")
    private BigDecimal avgPrice;

    @ApiModelProperty("库存美元总价")
    private BigDecimal totalAmt;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("是否启用（1：启用 0：禁用）")
    private Integer enable;

    private String createTime;
    private String updateTime;

    @ApiModelProperty("重复料号")
    private Boolean isRepeated = false;

    @ApiModelProperty("重复标签信息")
    private String repeatMessage;

    @ApiModelProperty("口岸")
    private String customsDistrictName;


    @ApiModelProperty("总入数量")
    private Integer totalInQty;

    @ApiModelProperty("总出数量")
    private Integer totalOutQty;


    @ApiModelProperty("汇总库存")
    private Integer totalDiffQty;


    @ApiModelProperty("差异数")
    private Integer diffQty;

    @ApiModelProperty("差异类型")
    private String diffType;

    /**
     * 锁定库存
     */
    private Integer lockedNum;

    /**
     * 商品来源标识:1-境外重点料件，2-境外普通料件，3-国内采购料件，4-专账成品转入料件
     */
    @ApiModelProperty(name = "商品来源标识")
    private String goodsSource;
}
