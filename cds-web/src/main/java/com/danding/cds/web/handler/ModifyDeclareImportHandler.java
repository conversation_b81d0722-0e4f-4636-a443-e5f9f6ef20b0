package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.ModifyDeclareExcleVo;
import com.danding.cds.v2.api.InventoryModifyDeclareRpc;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * @program: cds-center
 * @description: 变更申报导入
 * @author: 潘本乐（Belep）
 * @create: 2021-08-17 14:56
 **/
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_MODIFY_DECLARE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/MODIFY_DECLARE.xlsx",
        groups = {@ParkImportsHandler.Group(name = "变更申报", classes = ModifyDeclareExcleVo.class),})
public class ModifyDeclareImportHandler extends ImportsBaseHandler {


    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        InventoryModifyDeclareRpc customsCountryService = this.getBean(InventoryModifyDeclareRpc.class);

        List<ModifyDeclareExcleVo> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("变更申报")) {
                list = group.getDataList(ModifyDeclareExcleVo.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);

        if (CollUtil.isEmpty(list)) return;
        int index = 1;
        for (ModifyDeclareExcleVo modifyDeclareExcleVo : list) {
            ImportResultResVo resultResVo = customsCountryService.checkAndImport(modifyDeclareExcleVo.getLogisticsNo());
            log.info("运单：{}，导入后的返回结果数据{}", modifyDeclareExcleVo.getLogisticsNo(), resultResVo.toString());
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), modifyDeclareExcleVo);
            index++;
        }
    }
}
