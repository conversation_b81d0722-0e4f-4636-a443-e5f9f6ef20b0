package com.danding.cds.web.checklist.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BodyItemInfo {
    @ApiModelProperty("账册序号")
    private String accountSeqNo;

    @ApiModelProperty("商品料号")
    private String productId;

    @ApiModelProperty("申报数量")
    private long declareQty;

    @ApiModelProperty("是否异常料号")
    private boolean isException = false;
}
