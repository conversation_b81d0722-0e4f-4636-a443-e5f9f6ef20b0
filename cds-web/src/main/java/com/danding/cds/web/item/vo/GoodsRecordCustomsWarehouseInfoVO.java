package com.danding.cds.web.item.vo;

import com.danding.cds.v2.bean.vo.res.CustomsWarehouseResVO;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description: 备案关联的所有实体仓与特殊字段
 * @date 2022/3/7 15:44
 */
@Data
public class GoodsRecordCustomsWarehouseInfoVO {
    private List<CustomsWarehouseResVO> customsWarehouseResVOList;
    /**
     * 口岸code
     */
    private String customsCode;

    /**
     * 口岸
     */
    private String customs;

    /**
     * 驳回原因
     */
    private String reason;

    /**
     * 状态 待审核0 /通过 1 /驳回 4
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusDesc;

    /**
     * hs编码
     */
    private String hsCode;

    /**
     * 法定第一计量单位
     */
    private String firstUnit;

    /**
     * 法定第一计量单位数量
     */
    private BigDecimal firstUnitAmount;

    /**
     * 法定第二计量单位
     */
    private String secondUnit;

    /**
     * 法定第二计量单位数量
     */
    private BigDecimal secondUnitAmount;
    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    private Boolean deleted;


    /**
     * 审核方式
     */
    private Integer auditWay;
    private String auditWayDesc;

    /**
     * 关务备注
     */
    private String guanWuRemark;
}
