package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.enums.FbChecklistTypeEnums;
import com.danding.cds.common.enums.FbChecklistWrapTypeEnums;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.CustomsCharLengthUtils;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.upload.api.vo.FbChecklistImportVO;
import com.danding.cds.v2.api.FbChecklistService;
import com.danding.cds.v2.bean.dto.FbChecklistDTO;
import com.danding.cds.v2.bean.dto.FbChecklistItemDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/2/23 13:30
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_FB_CHECKLIST", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E9%9D%9E%E4%BF%9D%E6%A0%B8%E6%94%BE%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFv2.0.xlsx",
        groups = {@ParkImportsHandler.Group(name = "导入模板", classes = FbChecklistImportVO.class)})
public class FbChecklistImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<FbChecklistImportVO> headList = null;
        List<FbChecklistImportVO> itemList = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> headGroups = this.readData(1, 2, 2);
        List<ImportsDataGroup> itemGroups = this.readData(5, 6);

        for (ImportsDataGroup group : headGroups) {
            if (group.getName().equalsIgnoreCase("导入模板")) {
                headList = group.getDataList(FbChecklistImportVO.class);
            }
        }
        for (ImportsDataGroup group : itemGroups) {
            if (group.getName().equalsIgnoreCase("导入模板")) {
                itemList = group.getDataList(FbChecklistImportVO.class);
            }
        }
        log.info("读取到的excel对象数据,表头:{}, 表体:{}", JSON.toJSONString(headList), JSON.toJSONString(itemList));
        if (CollUtil.isEmpty(headList) || CollUtil.isEmpty(itemList)) {
            return;
        }
        this.updateTotal(null, headGroups.size() + itemGroups.size());

        FbChecklistService fbChecklistService = this.getBean(FbChecklistService.class);
        CustomsBookService customsBookService = this.getBean(CustomsBookService.class);
        int index = 0;
        boolean allowImport = true;
        FbChecklistDTO fbChecklistDTO = new FbChecklistDTO();
        List<FbChecklistItemDTO> fbChecklistItemDTOS = new ArrayList<>();
        for (FbChecklistImportVO excelVO : headList) {
            ImportResultResVo resultResVo = new ImportResultResVo();
            resultResVo.setFlag(true);
            index++;
            if (index == 1) {
                // 表头数据
                String errorMsg = "";
                String validatorResult = this.doValidator(excelVO);
                if (!StringUtils.isEmpty(validatorResult)) {
                    errorMsg = errorMsg + validatorResult;
                }
                BeanUtils.copyProperties(excelVO, fbChecklistDTO);
                fbChecklistDTO.setCarWeight(String.valueOf(excelVO.getCarWeight()));
                FbChecklistTypeEnums typeEnum = FbChecklistTypeEnums.getEnums(excelVO.getType());
                if (FbChecklistTypeEnums.NULL.equals(typeEnum)) {
                    errorMsg = buildErrorMsg(errorMsg) + "核放单类型不存在";
                }
                FbChecklistWrapTypeEnums wrapTypeEnums = FbChecklistWrapTypeEnums.getEnums(Integer.valueOf(excelVO.getWrapType()));
                if (FbChecklistWrapTypeEnums.NULL.equals(wrapTypeEnums)) {
                    errorMsg = buildErrorMsg(errorMsg) + "包装类型不存在";
                }
                CustomsBookDTO customsBookDTO = customsBookService.findByCode(excelVO.getCustomsBookNo());
                if (Objects.isNull(customsBookDTO)) {
                    errorMsg = buildErrorMsg(errorMsg) + "账册编号不存在";
                }
                if (!CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode().equals(customsBookDTO.getBookType())) {
                    errorMsg = buildErrorMsg(errorMsg) + "账册类型不正确";
                }
                String remark = excelVO.getRemark();
                if (!StringUtils.isEmpty(excelVO.getRemark())) {
                    if (CustomsCharLengthUtils.overLengthCheck(remark, 255)) {
                        errorMsg = buildErrorMsg(errorMsg) + "备注不允许大于255字";
                    }
                }
                if (StringUtils.hasText(errorMsg)) {
                    resultResVo.setFlag(false);
                    resultResVo.setReason(errorMsg);
                    allowImport = false;
                    this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
                    break;
                }
                fbChecklistDTO.setType(typeEnum.getCode());
                fbChecklistDTO.setCustomsBookId(customsBookDTO.getId());
                fbChecklistDTO.setWrapType(wrapTypeEnums.getCode());
                this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            }
        }
        index = 5;
        for (FbChecklistImportVO excelVO : itemList) {
            index++;
            // 表体数据
            ImportResultResVo resultResVo = new ImportResultResVo();
            resultResVo.setFlag(true);
            Long customsBookId = fbChecklistDTO.getCustomsBookId();
            FbChecklistItemDTO fbChecklistItemDTO = new FbChecklistItemDTO();
            fbChecklistItemDTO.setProductId(excelVO.getProductId());
            fbChecklistItemDTO.setItemNoNew(excelVO.getItemNo());
            fbChecklistItemDTO.setCount(excelVO.getCount());
            fbChecklistItemDTO.setUnitPrice(excelVO.getUnitPrice());
            fbChecklistItemDTOS.add(fbChecklistItemDTO);
            resultResVo = fbChecklistService.importCheck(customsBookId, fbChecklistItemDTO);
            if (Boolean.FALSE.equals(resultResVo.getFlag())) {
                allowImport = false;
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
        }
        if (allowImport) {
            fbChecklistService.importExcel(fbChecklistDTO, fbChecklistItemDTOS);
        }
    }


    private String buildErrorMsg(String errorMsg) {
        return errorMsg + (StringUtils.hasText(errorMsg) ? "," : "");
    }
}
