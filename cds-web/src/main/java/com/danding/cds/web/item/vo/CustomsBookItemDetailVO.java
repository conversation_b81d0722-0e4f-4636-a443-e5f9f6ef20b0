package com.danding.cds.web.item.vo;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class CustomsBookItemDetailVO {
    private Long id;
    private String goodsSeqNo;//商品序号
    private String productId;//料号
    private String hsCode;//HS编码
    private String goodsName;//商品名称
    private String goodsSource;
    private String goodsSourceDesc;
    private String currCode;//申报币制
    private String currCodeDesc;//申报币制
    private BigDecimal declarePrice;//申报单价金额
    private String goodsModel;//商品规格型号
    private String originCountry;
    private String originCountryDesc;
    private String goodsUnit;//申报计量单位
    private String goodsUnitDesc;
    private String firstUnit;
    private String firstUnitDesc;
    private BigDecimal firstUnitAmount;
    private String secondUnit;
    private String secondUnitDesc;
    private BigDecimal secondUnitAmount;
    private Long customsBookId;
    //账册编号
    private String customsBookNo;
    private String invtGoodsNo;//记账清单商品序号
    private String invtNo;//记账清单编号
    private Integer inQty;//入仓数量
    private BigDecimal inLegalQty;//入仓法定数量
    private BigDecimal inSecondLegalQty;//第二入仓法定数量
    private String inDate;//最近入仓（核增）日期
    private BigDecimal avgPrice;//平均美元单价
    private BigDecimal totalAmt;//库存美元总价
    private String remark;
    private Integer enable;
}
