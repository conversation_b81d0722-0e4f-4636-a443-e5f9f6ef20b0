package com.danding.cds.web.item.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class GoodsRecordVO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("SKU")
    private String skuId;

    @ApiModelProperty("统一料号")
    private String productId;

    @ApiModelProperty("海关备案料号")
    private String customsRecordProductId;

    @ApiModelProperty("账册id")
    private Integer customsBookId;

    @ApiModelProperty("账册编号")
    private String customsBookNo;

    @ApiModelProperty("条码")
    private String barCode;

    @ApiModelProperty("备案名称")
    private String goodsRecordName;

    @ApiModelProperty("租户名称")
    private String lesseeName;

    @ApiModelProperty("国检备案号")
    private String countryRecordNo;

    @ApiModelProperty("审核状态编码")
    private Integer recordStatus;

    @ApiModelProperty("审核状态")
    private String recordStatusDesc;

    @ApiModelProperty("驳回理由")
    private String reason;

    @ApiModelProperty("备案时间")
    private String createTime;

    @ApiModelProperty("是否启用（1：启用 0：禁用）")
    private Integer enable;

    @ApiModelProperty("租户名称 新")
    private String tenantName;

    @ApiModelProperty("原产国")
    private String originCountry;

    @ApiModelProperty("原产国描述")
    private String originCountryDesc;

    @ApiModelProperty("口岸")
    private String customs;

    @ApiModelProperty("口岸描述")
    private String customsDesc;

    @ApiModelProperty("货品id")
    private String goodsCode;

    @ApiModelProperty("生产企业名称")
    private String productCompanyName;

    @ApiModelProperty("生产企业注册编号")
    private String productCompanyRegisterNumber;

    @ApiModelProperty("生产企业地址")
    private String productCompanyAddress;

    @ApiModelProperty("商品链接")
    private String productLink;

    @ApiModelProperty("附件名称")
    private String attachmentName;

    @ApiModelProperty("附件下载链接")
    private String attachmentUrl;

    @ApiModelProperty("新老备案")
    private String recordType;

    @ApiModelProperty("更新时间")
    private String updateTime;

    @ApiModelProperty("外部料号")
    private String externalProductId;

    @ApiModelProperty("待审核口岸")
    private String waitExamineCustoms;

    @ApiModelProperty("已审核口岸")
    private String examinedCustoms;

    @ApiModelProperty("已驳回口岸")
    private String refuseCustoms;

    @ApiModelProperty("来源")
    private String sourceStr;

    @ApiModelProperty("商品标记")
    private List<String> goodsRecordTagList;

    @ApiModelProperty("商品标记")
    private Integer goodsRecordTag;

    @ApiModelProperty("商品标记")
    private String goodsRecordTagDesc;

    /**
     * 关务备注
     */
    private String guanWuRemark;

    /**
     * 审核方式 0-人工审核， 1-系统审核
     */
    private Integer auditWay;
    private String auditWayDesc;
}
