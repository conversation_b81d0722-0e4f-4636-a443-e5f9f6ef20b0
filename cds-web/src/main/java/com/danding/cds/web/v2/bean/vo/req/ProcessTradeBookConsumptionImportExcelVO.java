package com.danding.cds.web.v2.bean.vo.req;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class ProcessTradeBookConsumptionImportExcelVO {

    //*成品申报序号	*料件申报序号	*净耗	*单耗申报状态（1-未申报、2-已申报、3-已确定）
    /**
     * 料号
     */
    @ExcelProperty(value = "*成品申报序号")
    @NotNull(message = "成品申报序号不能为空")
    private Integer endprdSeqNo;

    @ExcelProperty(value = "*料件申报序号")
    @NotNull(message = "料件申报序号不能为空")
    private Integer mtpckSeqNo;

    @ExcelProperty(value = "*净耗")
    @NotNull(message = "净耗不能为空")
    private Integer netConsumption;

    @ExcelProperty(value = "*单耗申报状态（1-未申报、2-已申报、3-已确定）")
    @NotNull(message = "单耗申报状态不能为空")
    private Integer declareStatus;
}
