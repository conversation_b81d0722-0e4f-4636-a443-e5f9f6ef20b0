package com.danding.cds.web.invenorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class InventoryOrderTallyReportDetailVO {

    @ApiModelProperty("理货编号")
    private String tallyOrderNo;

    @ApiModelProperty("出库单号")
    private String outBoundNo;

    @ApiModelProperty("货品名称")
    private String goodsName;

    @ApiModelProperty("SKU")
    private String sku;

    @ApiModelProperty("料号")
    private String productId;

    @ApiModelProperty("计划理货数量")
    private Integer planTallyQty;

    @ApiModelProperty("实际理货数量")
    private Integer actualTallyQty;

    @ApiModelProperty("备注")
    private String remark;
}
