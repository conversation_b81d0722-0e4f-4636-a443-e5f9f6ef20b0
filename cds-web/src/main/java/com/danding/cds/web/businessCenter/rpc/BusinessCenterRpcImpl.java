package com.danding.cds.web.businessCenter.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.enmus.CompanyQualifyEnum;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.itemstocklist.api.vo.BcItemStockListPagingResVO;
import com.danding.cds.itemstocklist.api.vo.BcItemStockListSearch;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.vo.req.OverViewSearch;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.web.businessCenter.entity.vo.AllCountRes;
import com.danding.cds.web.businessCenter.entity.vo.PersonWorkbenchReqVO;
import com.danding.cds.web.businessCenter.entity.vo.UpdBusinessTypeVO;
import com.danding.cds.web.controller.BusinessCenterController;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 关务中台
 */
@DubboService
@Slf4j
public class BusinessCenterRpcImpl implements BusinessCenterRpc {

    @Autowired
    private BusinessCenterController businessCenterController;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    /**
     * 个人工作台统计
     *
     * @param reqVO
     * @return
     * @path /businessCenter/dataCount
     */
    @Override
    @SoulClient(path = "/businessCenter/dataCount", desc = "个人工作台统计")
    public RpcResult<AllCountRes> getAllDataCount(PersonWorkbenchReqVO reqVO) {
        return RpcResult.success(businessCenterController.getAllDataCount(reqVO));
    }

    @Override
    @SoulClient(path = "/goodsRecord/statusCount", desc = "备案统计")
    public RpcResult getStatusCountByGoodsRecord(OverViewSearch search) {
        return RpcResult.success(businessCenterController.getStatusCountByGoodsRecord(search));
    }

    @Override
    @SoulClient(path = "/businessCenter/goodsRecord/export", desc = "备案统计-导出")
    public RpcResult<String> goodsRecordExport(OverViewSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.BUSINESS_CENTER_GOODS_RECORD_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/bonded/postSale", desc = "保税售后统计")
    public RpcResult getStatusCountByBondedPostSale(OverViewSearch search) {
        return RpcResult.success(businessCenterController.getStatusCountByBondedPostSale(search));
    }

    @Override
    @SoulClient(path = "/businessCenter/postSale/export", desc = "保税售后统计-导出")
    public RpcResult<String> bondedPostSaleExport(OverViewSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.BUSINESS_CENTER_BONDED_POST_SALE_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/customs/complete", desc = "清关完成数统计")
    public RpcResult getStatusCountByCustomsComplete(OverViewSearch search) {
        return RpcResult.success(businessCenterController.getStatusCountByCustomsComplete(search));
    }

    @Override
    @SoulClient(path = "/businessCenter/customsComplete/export", desc = "清关完成数统计-导出")
    public RpcResult<String> customsCompleteExport(OverViewSearch search) {
        try {
            String typeName = null;
            switch (search.getType()) {
                case 1:
                    typeName = "清关单";
                    break;
                case 2:
                    typeName = "核注单";
                    break;
                case 3:
                    typeName = "核放单";
                    break;
            }
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.BUSINESS_CENTER_CUSTOMS_COMPLETE_FOR_EXCEL, typeName);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/dictionary/businessType", desc = "数据字典-业务类型-下拉")
    public RpcResult listBusinessTypeFromDict() {
        return RpcResult.success(businessCenterController.listBusinessTypeFromDict());
    }

    @Override
    @SoulClient(path = "/dictionary/businessType/update", desc = "数据字典-业务类型-更新")
    public RpcResult updBusinessTypeInDict(UpdBusinessTypeVO businessTypeVO) {
        try {
            businessCenterController.updBusinessTypeInDict(businessTypeVO.getDataList());
            return RpcResult.success("保存成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("保存失败: " + e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/dictionary/businessType/delete", desc = "数据字典-业务类型-删除")
    public RpcResult delBusinessTypeInDict(SelectOptionVO vo) {
        if (Objects.isNull(vo)) {
            log.warn("delBusinessTypeInDict 参数不能为空");
            return RpcResult.error("删除失败");
        }
        try {
            businessCenterController.delBusinessTypeInDict(vo);
            return RpcResult.success("删除成功");
        } catch (Exception e) {
            log.warn(e.getMessage(), e);
            return RpcResult.error("删除失败" + e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/dictionary/handler", desc = "数据字典-处理人-下拉")
    public RpcResult listHandlerFromDict() {
        return RpcResult.success(businessCenterController.listHandlerFromDict());
    }

    @Override
    @SoulClient(path = "/dictionary/handler/filter", desc = "数据字典-处理人(过滤当前用户)-下拉")
    public RpcResult listHandlerFilterFromDict() {
        return RpcResult.success(businessCenterController.listHandlerFilterFromDict());
    }

    /**
     * 区内企业下拉
     *
     * @return
     * @path /businessCenter/listAreaCompany
     */
    @Override
    @SoulClient(path = "/businessCenter/listAreaCompany", desc = "区内企业下拉")
    public RpcResult<List<SelectOptionVO<String>>> listAreaCompany() {
        List<SelectOptionVO<String>> result = new ArrayList<>();
        result.add(new SelectOptionVO<>("all", "全部"));
        List<CompanyDTO> companyDTOS = companyService.listWithQualifyAll(CompanyQualifyEnum.QNQY.getCode());
        for (CompanyDTO companyDTO : companyDTOS) {
            SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setName(companyDTO.getName());
            selectOptionVO.setId(companyDTO.getId().toString());
            result.add(selectOptionVO);
        }
        return RpcResult.success(result);
    }

    /**
     * 仓库下拉
     *
     * @return
     * @path /businessCenter/listWareHouse
     */
    @Override
    @SoulClient(path = "/businessCenter/listWareHouse", desc = "仓库下拉")
    public RpcResult<List<SelectOptionVO<String>>> listWareHouse() {
        try {
            List<EntityWarehouseDTO> warehouseDTOList = entityWarehouseService.listWarehouse();
            List<SelectOptionVO<String>> result = new ArrayList<>();
            result.add(new SelectOptionVO<>("all", "全部"));
            for (EntityWarehouseDTO item : warehouseDTOList) {
                SelectOptionVO<String> optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getWmsWarehouseCode());
                optionDTO.setName(item.getErpWarehouseName());
                result.add(optionDTO);
            }
            result = result.stream().distinct().collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("获取实体仓列表失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取实体仓列表失败");
    }

    /**
     * 个人工作台-料号流水待处理分页
     *
     * @param search
     * @return
     * @path /businessCenter/itemStockListPaging
     */
    @SoulClient(path = "/businessCenter/itemStockListPaging", desc = "料号流水待处理分页查询")
    @Override
    public RpcResult<ListVO<BcItemStockListPagingResVO>> itemStockListPaging(BcItemStockListSearch search) {
        return RpcResult.success(businessCenterController.itemStockListPaging(search));
    }


}
