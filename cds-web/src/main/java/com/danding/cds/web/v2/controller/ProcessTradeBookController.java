package com.danding.cds.web.v2.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.danding.cds.common.annotation.ParamValidatorV2;
import com.danding.cds.common.model.IdEnableParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.endorsement.api.enums.EndorsementModfMarkEnums;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.bean.enums.*;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookDetailEditReq;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.cds.web.v2.bean.vo.req.*;
import com.danding.cds.web.v2.bean.vo.res.*;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 加贸账册管理
 */
@Slf4j
@RestController
@Service
@RequestMapping("/processTradeBook")
public class ProcessTradeBookController {

    @DubboReference
    private ProcessTradeBookService processTradeBookService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsDictionaryService dictionaryService;

    @DubboReference
    private CustomsBookService customsBookService;


    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @PostMapping("/paging")
    public RpcResult<ListVO<ProcessTradeBookVO>> paging(@RequestBody ProcessTradeBookSearch search) {
        ListVO<ProcessTradeBookDTO> paging = processTradeBookService.paging(search);
        ListVO<ProcessTradeBookVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(this.buildVO(paging.getDataList()));
        return RpcResult.success(result);
    }

    private List<ProcessTradeBookVO> buildVO(List<ProcessTradeBookDTO> dataList) {
        if (CollUtil.isEmpty(dataList)) {
            return new ArrayList<>();
        }
        return dataList.stream().map((ProcessTradeBookDTO dto) -> {
            ProcessTradeBookVO vo = new ProcessTradeBookVO();
            BeanUtils.copyProperties(dto, vo);
            vo.setStatusDesc(ProcessTradeBookStatusEnums.getDesc(dto.getStatus()));
            vo.setBookTypeDesc(ProcessTradeBookTypeEnums.getDesc(dto.getBookType()));
            return vo;
        }).collect(Collectors.toList());
    }

    /**
     * 新增
     * @param reqVO
     * @return
     */
    @PostMapping("/create")
    @ParamValidatorV2
    public RpcResult<String> create(@RequestBody ProcessTradeBookCreateReq reqVO) {
        processTradeBookService.create(reqVO.getBookId(), reqVO.getBookType());
        return RpcResult.success();
    }

    /**
     * 启用禁用
     * @param idEnableParam
     * @return
     */
    @PostMapping("/enable")
    public RpcResult<String> enable(@RequestBody IdEnableParam idEnableParam) {
        processTradeBookService.enable(idEnableParam.getIdList(), idEnableParam.getEnable());
        return RpcResult.success();
    }

    /**
     * 修改数据状态
     * @param param
     * @return
     */
    @PostMapping("/updateStatus")
    @ParamValidatorV2
    public RpcResult<String> updateStatus(@RequestBody ProcessTradeBookUpdStatusReq param) {
        processTradeBookService.updateStatus(param.getIdList(), param.getStatus());
        return RpcResult.success();
    }

    /**
     * 详情
     * @param id
     * @return
     */
    @PostMapping("/detail")
    public RpcResult<ProcessTradeBookDetailVO> detail(Long id) {
        if (Objects.isNull(id)) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        ProcessTradeBookDetailVO vo = new ProcessTradeBookDetailVO();
        ProcessTradeBookDTO processTradeBookDTO = processTradeBookService.findById(id);
        if (processTradeBookDTO == null) {
            return RpcResult.error("账册不存在");
        }
        BeanUtils.copyProperties(processTradeBookDTO, vo);
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(processTradeBookDTO.getLogisticsBookId());
        if (Objects.nonNull(customsBookResVo)) {
            vo.setProcessTradeBookNo(customsBookResVo.getBookNo());
        }
        vo.setStatusDesc(ProcessTradeBookStatusEnums.getDesc(processTradeBookDTO.getStatus()));
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        CompanyDTO processCompanyDTO = companyService.findById(processTradeBookDTO.getProcessCompanyId());
        companyDTOMap.put(processCompanyDTO.getId(), processCompanyDTO);
        vo.setProcessCompanyName(processCompanyDTO.getName());
        vo.setProcessCompanyUSCC(processCompanyDTO.getUniformSocialCreditCode());
        vo.setProcessCompanyCode(processCompanyDTO.getCode());
        CompanyDTO operateCompanyDTO = companyDTOMap.computeIfAbsent(processTradeBookDTO.getOperateCompanyId(), i -> companyService.findById(i));
        vo.setOperateCompanyName(operateCompanyDTO.getName());
        vo.setOperateCompanyUSCC(operateCompanyDTO.getUniformSocialCreditCode());
        vo.setOperateCompanyCode(operateCompanyDTO.getCode());
        CompanyDTO declareCompanyDTO = companyDTOMap.computeIfAbsent(processTradeBookDTO.getDeclareCompanyId(), i -> companyService.findById(i));
        vo.setDeclareCompanyName(declareCompanyDTO.getName());
        vo.setDeclareCompanyUSCC(declareCompanyDTO.getUniformSocialCreditCode());
        vo.setDeclareCompanyCode(declareCompanyDTO.getCode());
        CompanyDTO inputCompanyDTO = companyDTOMap.computeIfAbsent(processTradeBookDTO.getInputCompanyId(), i -> companyService.findById(i));
        vo.setInputCompanyName(inputCompanyDTO.getName());
        vo.setInputCompanyUSCC(inputCompanyDTO.getUniformSocialCreditCode());
        vo.setInputCompanyCode(inputCompanyDTO.getCode());
        vo.setDeclareCompanyTypeDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getDeclareCompanyType()));
        vo.setBookTypeDesc(ProcessTradeBookTypeEnums.getDesc(processTradeBookDTO.getBookType()));
        vo.setConsumptionDeclarationLinkDesc(ProcessTradeBookConsumptionDeclareLinkEnums.getDesc(processTradeBookDTO.getConsumptionDeclarationLink()));
        vo.setBookUsageDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getBookUsage()));
        vo.setVoucherClearanceMethodDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getVoucherClearanceMethod()));
        vo.setVoucherClearanceTypeDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getVoucherClearanceType()));
        vo.setBookExecutionFlagDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getBookExecutionFlag()));
        vo.setConsumptionVersionControlFlagDesc(ProcessTradeBookDeclareCompanyTypeEnums.getDesc(processTradeBookDTO.getConsumptionVersionControlFlag()));
        vo.setMasterCustomsDesc(CustomsDistrictEnum.getEnum(vo.getMasterCustoms()).getDesc());
        return RpcResult.success(vo);
    }

    /**
     * 详情编辑
     * @param editReq
     * @return
     */
    @ParamValidatorV2
    @PostMapping("/editDetail")
    public RpcResult<String> editDetail(@RequestBody ProcessTradeBookDetailEditReq editReq) {
        processTradeBookService.updateDetail(editReq);
        return RpcResult.success();
    }

    /**
     * 表体信息分页查询
     * @param param
     * @return
     */
    @PostMapping("/item/paging")
    @ParamValidatorV2
    public RpcResult<ListVO<ProcessTradeBookItemVO>> itemPaging(@RequestBody ProcessTradeBookItemSearch param) {
        ListVO<ProcessTradeBookItemDTO> paging = processTradeBookService.itemPaging(param);
        ListVO<ProcessTradeBookItemVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map(this::buildItemVO).collect(Collectors.toList()));
        return RpcResult.success(result);
    }

    private ProcessTradeBookItemVO buildItemVO(ProcessTradeBookItemDTO dto) {
        Map<String, String> uomMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        Map<String, String> currencyMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> countryMap = dictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        ProcessTradeBookItemVO vo = new ProcessTradeBookItemVO();
        BeanUtils.copyProperties(dto, vo);
        vo.setLegalUnitDesc(uomMap.getOrDefault(dto.getLegalUnit(), ""));
        vo.setLegalSecondUnitDesc(uomMap.getOrDefault(dto.getLegalSecondUnit(), ""));
        vo.setDeclareUnitDesc(uomMap.getOrDefault(dto.getDeclareUnit(), ""));
        vo.setCountryRegionDesc(countryMap.getOrDefault(dto.getCountryRegion(), ""));
        vo.setCurrencyDesc(currencyMap.getOrDefault(dto.getCurrency(), ""));
        vo.setModifyFlagDesc(EndorsementModfMarkEnums.getDesc(String.valueOf(dto.getModifyFlag())));
        vo.setQtyControlFlagDesc(ProcessTradeBookItemQtyControlFlagEnums.getDesc(dto.getQtyControlFlag()));
        vo.setConsumptionQuestionFlagDesc(ProcessTradeBookConsumptionQuestionFlagEnums.getDesc(dto.getConsumptionQuestionFlag()));
        vo.setNegotiationFlagDesc(ProcessTradeBookNegotiationFlagEnums.getDesc(dto.getNegotiationFlag()));
        vo.setDutyExemptionMethodDesc(Objects.equals("3", dto.getDutyExemptionMethod()) ? "全免" : "");
        vo.setCompanyExecutionFlagDesc(ProcessTradeBookCompanyExecutionFlagEnums.getDesc(dto.getCompanyExecutionFlag()));
        vo.setFocusMarkDesc(ProcessTradeBookFocusMarkEnums.getDesc(dto.getFocusMark()));
        vo.setCustomsExecutionFlagDesc(ProcessTradeBookCustomsExecutionFlagEnums.getDesc(dto.getCustomsExecutionFlag()));
        return vo;
    }

    /**
     * 表体新增/编辑
     * @param param
     * @return
     */
    @PostMapping("/item/createOrEdit")
    @ParamValidatorV2
    public RpcResult<String> createOrEditItem(@RequestBody ProcessTradeBookItemCreateReq param) {
        if (Objects.isNull(param.getRefBookId())) {
            throw new ArgsInvalidException("账册id不能为空");
        }
        ProcessTradeBookItemDTO itemDTO = new ProcessTradeBookItemDTO();
        BeanUtils.copyProperties(param, itemDTO);
        processTradeBookService.createOrEditItem(itemDTO);
        return RpcResult.success();
    }

    /**
     * 表体删除
     * @param param
     * @return
     */
    @PostMapping("/item/delete")
    public RpcResult<String> deleteItem(@RequestBody IdsParam param) {
        processTradeBookService.deleteItem(param.getIdList());
        return RpcResult.success();
    }

    /**
     * 单耗分页查询
     * @param param
     * @return
     */
    @ParamValidatorV2
    @PostMapping("/consumption/paging")
    public RpcResult<ListVO<ProcessTradeBookConsumptionVO>> pagingConsumption(@RequestBody ProcessTradeBookConsumptionSearch param) {
        ListVO<ProcessTradeBookConsumptionDTO> paging = processTradeBookService.pagingConsumption(param);
        ListVO<ProcessTradeBookConsumptionVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        List<ProcessTradeBookConsumptionDTO> dataList = paging.getDataList();
        List<Long> itemIdList = dataList.stream().flatMap(i -> Stream.of(i.getEndPrdId(), i.getMtpckId())).collect(Collectors.toList());
        List<ProcessTradeBookItemDTO> itemDTOList = processTradeBookService.findItemByIdList(itemIdList);
        Map<Long, ProcessTradeBookItemDTO> itemDTOMap = itemDTOList.stream().collect(Collectors.toMap(ProcessTradeBookItemDTO::getId, i -> i));
        result.setDataList(dataList.stream().map((ProcessTradeBookConsumptionDTO dto) -> {
            ProcessTradeBookConsumptionVO vo = new ProcessTradeBookConsumptionVO();
            BeanUtils.copyProperties(dto, vo);
            ProcessTradeBookItemDTO endPrdItemDTO = itemDTOMap.get(dto.getEndPrdId());
            ProcessTradeBookItemDTO mtpckItemDTO = itemDTOMap.get(dto.getMtpckId());
            vo.setEndPrdSeqNo(endPrdItemDTO.getSeqNo());
            vo.setEndPrdProductId(endPrdItemDTO.getProductId());
            vo.setEndPrdHsCode(endPrdItemDTO.getHsCode());
            vo.setEndPrdGoodsName(endPrdItemDTO.getGoodsName());
            vo.setMtpckSeqNo(mtpckItemDTO.getSeqNo());
            vo.setMtpckProductId(mtpckItemDTO.getProductId());
            vo.setMtpckHsCode(mtpckItemDTO.getHsCode());
            vo.setMtpckGoodsName(mtpckItemDTO.getGoodsName());
            vo.setDeclareStatusDesc(ProcessTradeBookConsumptionDeclareStatusEnums.getDesc(dto.getDeclareStatus()));
            vo.setModifyFlagDesc(EndorsementModfMarkEnums.getDesc(dto.getModifyFlag()));
            vo.setConsumptionValidityStr(DateUtil.format(dto.getConsumptionValidity(), DatePattern.PURE_DATE_PATTERN));
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }

    /**
     * 新增/编辑单耗
     * @param param
     * @return
     */
    @PostMapping("/consumption/createOrEdit")
    @ParamValidatorV2
    public RpcResult<String> createOrEditConsumption(@RequestBody ProcessTradeBookConsumptionCreateReq param) {
        ProcessTradeBookConsumptionDTO consumptionDTO = new ProcessTradeBookConsumptionDTO();
        BeanUtils.copyProperties(param, consumptionDTO);
        processTradeBookService.createOrEditConsumption(consumptionDTO);
        return RpcResult.success();
    }

    /**
     * 删除单耗
     * @param param
     * @return
     */
    @PostMapping("/consumption/delete")
    public RpcResult<String> deleteConsumption(@RequestBody IdsParam param) {
        processTradeBookService.deleteConsumption(param.getIdList());
        return RpcResult.success();
    }

    /**
     * 匹配账册表体
     *
     * @param reqVO
     * @return
     */
    @ParamValidatorV2
    @PostMapping("/matchItem")
    public RpcResult<List<ProcessTradeBookItemVO>> matchItem(@RequestBody ProcessTradeBookMatchItemReqVO reqVO) {
        List<ProcessTradeBookItemDTO> itemDTOList = processTradeBookService.matchItem(reqVO.getId(), reqVO.getBookId(), reqVO.getGoodsType(), reqVO.getProductId());
        return RpcResult.success(itemDTOList.stream().map(this::buildItemVO).collect(Collectors.toList()));
    }

    @PostMapping("/listEndprdSeqById")
    public RpcResult<List<ProcessTradeBookItemSelectVO>> listEndprdSeqById(Long id) {
        return RpcResult.success(getItemSelectVOS(processTradeBookService.listEndprdSeqById(id)));
    }

    @PostMapping("/listMtpckSeqById")
    public RpcResult<List<ProcessTradeBookItemSelectVO>> listMtpckSeqById(Long id) {
        return RpcResult.success(getItemSelectVOS(processTradeBookService.listMtpckSeqById(id)));
    }

    private List<ProcessTradeBookItemSelectVO> getItemSelectVOS(List<ProcessTradeBookItemDTO> bizDeclareFormItemDTOS) {
        return bizDeclareFormItemDTOS.stream().map(i -> {
            ProcessTradeBookItemSelectVO selectVO = new ProcessTradeBookItemSelectVO();
            selectVO.setId(i.getId());
            selectVO.setSeqNo(i.getSeqNo());
            selectVO.setGoodsName(selectVO.getSeqNo() + "-" + i.getGoodsName());
            return selectVO;
        }).collect(Collectors.toList());
    }
}
