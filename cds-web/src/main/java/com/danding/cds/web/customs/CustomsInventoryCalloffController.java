package com.danding.cds.web.customs;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.c.api.rpc.CustomsInventoryCalloffCRpc;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.c.api.rpc.RefundOrderRpc;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.cull.api.dto.CullOrderDTO;
import com.danding.cds.cull.api.enums.CullStatusEnums;
import com.danding.cds.cull.api.enums.EndorsementLabelEnums;
import com.danding.cds.cull.api.service.CullOrderService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.inventory.api.enums.RefundCallbackMessageStatus;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.invenorder.api.enums.InventoryOrderStepEnum;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.v2.bean.dto.JdCustomsOrderMsgDataDTO;
import com.danding.cds.v2.bean.vo.req.RefundWarehouseCancelReq;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.web.customs.rpc.param.RejectCalloffBatchReqVO;
import com.danding.cds.web.customs.vo.CalloffInfoSumVO;
import com.danding.cds.web.user.UserService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Validator;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * @Author: Raymond
 * @Date: 2020/10/14 13:26
 * @Description:
 */
@Api(tags = "取消单管理")
@RestController
@RequestMapping("/calloff")
@Slf4j
public class CustomsInventoryCalloffController {
    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private OrderCRpc orderCRpc;

//    @DubboReference
//    private SequenceService sequenceService;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    @Autowired
    private UserService userService;

    @DubboReference
    private CullOrderService cullOrderService;

    @Autowired
    private Validator validator;

    @DubboReference
    private RefundOrderRpc refundOrderRpc;
//
//    @DubboReference
//    private OrderCRpc orderCRpc;
//
//    @DubboReference
//    private EntityWarehouseService entityWarehouseService;
//
//    @DubboReference
//    private MessageService messageService;


    @ApiOperation(value = "取消单分页查询", response = CustomsInventoryCalloffVO.class)
    @GetMapping("/paging")
    public ListVO<CustomsInventoryCalloffVO> paging(CustomsInventoryCalloffSearch search) {
        if (Objects.isNull(search)) {
            return new ListVO<>();
        }
        ListVO<CustomsInventoryCalloffDTOV2> list = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = customsInventoryCalloffCRpc.paging(search);
        } else {
            list = customsInventoryCalloffService.paging(search);
        }
        List<CustomsInventoryCalloffDTOV2> datalist = list.getDataList();
        List<Long> userIdList = datalist.stream().map(d -> d.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(userIdList);
        ListVO<CustomsInventoryCalloffVO> result = new ListVO<>();
        result.setPage(list.getPage());
        List<CustomsInventoryCalloffVO> dataVOList = new ArrayList<>();
        Map<String, String> tenantIdName = new HashMap<>();
        if (CollUtil.isNotEmpty(datalist)) {
            List<String> orderSnList = datalist.stream().map(CustomsInventoryCalloffDTOV2::getOrderSn).collect(Collectors.toList());
            List<OrderDTO> orderDTOS = orderCRpc.findBySnSection(orderSnList);
            if (Objects.isNull(orderDTOS)) {
                result.setPage(new PageResult());
                result.setDataList(new ArrayList<>());
                return result;
            }
            List<String> customsInventorySnList = orderDTOS.stream().map(OrderDTO::getCustomsInventorySn).collect(Collectors.toList());
            List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryRpc.findBySnList(customsInventorySnList);
            if (Objects.isNull(customsInventoryDTOS)) {
                result.setPage(new PageResult());
                result.setDataList(new ArrayList<>());
                return result;
            }
            Map<Long, CustomsInventoryDTO> customsInventoryDTOMap = customsInventoryDTOS.stream()
                    .collect(Collectors.toMap(CustomsInventoryDTO::getOrderId, Function.identity(), (v1, v2) -> v1));
            for (CustomsInventoryCalloffDTOV2 info : datalist) {
                CustomsInventoryCalloffVO vo = new CustomsInventoryCalloffVO();
                BeanUtils.copyProperties(info, vo);
                vo.setOrderId(String.valueOf(info.getOrderId()));
                if (StrUtil.isNotBlank(info.getPicJson())) {
                    vo.setPicList(JSON.parseArray(info.getPicJson(), String.class));
                }
                CustomsInventoryDTO customsInventoryDTO;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
//                    customsInventoryDTO = customsInventoryRpc.findByOrder(info.getOrderId(), null);
                    customsInventoryDTO = customsInventoryDTOMap.get(info.getOrderId());
                } else {
                    customsInventoryDTO = customsInventoryService.findByOrder(info.getOrderId(), null);
                }
                if (customsInventoryDTO == null) {
                    log.warn("取消单中申报单号: {} ,对应的OrderId:{}，没有找到清单数据", info.getDeclareOrderNo(), info.getOrderId());
                    continue;
                }
                String customsStatus = customsInventoryDTO.getCustomsStatus();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(customsStatus)) {
                    CustomsStat customsStat = CustomsStat.getEnum(customsStatus);
                    if (customsStat != null) {
                        vo.setCustomsStatus(customsStat.getDesc());
                    }
                }

                Integer exitRegionStatus = customsInventoryDTO.getExitRegionStatus();
                if (exitRegionStatus != null) {
                    vo.setExitRegionStatusDesc(exitRegionStatus == 1 ? "已出区" : "未出区");
                }
                String calloffStatus = info.getCalloffStatus();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(calloffStatus)) {
                    InventoryCalloffStatusEnum inventoryCalloffStatusEnum = InventoryCalloffStatusEnum.getEnum(calloffStatus);
                    if (inventoryCalloffStatusEnum != null) {
                        vo.setCalloffStatus(inventoryCalloffStatusEnum.getDesc());
                    }
                }
                String calloffType = info.getCalloffType();
                if (!org.apache.commons.lang3.StringUtils.isEmpty(calloffType)) {
                    InventoryCalloffTypeEnum inventoryCalloffTypeEnum = InventoryCalloffTypeEnum.getEnum(calloffType);
                    if (inventoryCalloffTypeEnum != null) {
                        vo.setCalloffType(inventoryCalloffTypeEnum.getDesc());
                    }
                }
                if (info.getEbcId() != null) {
                    CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(info.getEbcId());
                    if (companyDTO != null) {
                        vo.setEbcName(companyDTO.getName());
                    }
                }
                if (info.getAgentCompanyId() != null) {
                    CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(info.getAgentCompanyId());
                    if (companyDTO != null) {
                        vo.setAgentCompanyName(companyDTO.getName());
                    }
                }
                if (info.getAreaCompanyId() != null) {
                    CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(info.getAreaCompanyId());
                    if (companyDTO != null) {
                        vo.setAreaCompanyName(companyDTO.getName());
                    }
                }
                if (Objects.nonNull(info.getOrderTag())) {
                    List<String> orderTagsDesc = InventoryCalloffOrderTagEnums.getOrderTagsDesc(info.getOrderTag());
                    vo.setOrderTagList(orderTagsDesc);
                }
                if (CustomsStat.CUSTOMS_PASS.equals(info.getCustomsStatus()) || info.getExitRegionStatus() == 0) {
                    vo.setOrderStep(InventoryOrderStepEnum.STEP_CANCLE.getCode());
                } else if (CustomsStat.CUSTOMS_PASS.equals(info.getCustomsStatus()) || info.getExitRegionStatus() == 1) {
                    vo.setOrderStep(InventoryOrderStepEnum.STEP_RETURN.getCode());
                }

                if (info.getTenantId() != null) {
                    if (tenantIdName.containsKey(info.getTenantId())) {
                        vo.setUserName(tenantIdName.get(info.getTenantId()));
                    } else {
                        String tenantName = taxesTenantAccountService.getTenantNameById(info.getTenantId());
                        vo.setUserName(tenantName);
                        tenantIdName.put(info.getTenantId(), tenantName);
                    }
                }
                if (info.getCreateBy() != null) {
                    if (userRpcResultMap.containsKey(info.getCreateBy().longValue())) {
                        vo.setOper(userRpcResultMap.get(info.getCreateBy().longValue()).getUserName());
                    }
                }
                if (info.getLogisticsNo() != null) {
                    CullOrderDTO cullOrderDTO = cullOrderService.findByCustomsLogisticsSn(info.getLogisticsNo());
                    if (Objects.nonNull(cullOrderDTO) && Objects.equals(cullOrderDTO.getStatus(), CullStatusEnums.PENDING.getCode())) {
                        vo.setLabel(EndorsementLabelEnums.ENDORSEMENT_CULL.getDesc());
                    }
                }
                if (Objects.nonNull(info.getCusAfterSalesCallback())) {
                    vo.setCusAfterSalesCallbackDesc(CustomsStat.getEnum(info.getCusAfterSalesCallback()).getDesc());
                }
                vo.setEntityWarehouseName(info.getEntityWarehouseName());
                vo.setOwnerName(info.getOwnerName());
                dataVOList.add(vo);
            }
        }
        result.setDataList(dataVOList);
        return result;
    }

//    @ApiOperation(value = "生成取消单")
//    @PostMapping("/create")
//    public Response<String> createCalloff(@RequestParam(required=true) Long orderId) {
//        CustomsInventoryCalloffDTO calloffDto = customsInventoryCalloffService.findByOrderId(orderId);
//        if (calloffDto != null) {
//            return new Response<>(-1,"取消单已经生成");
//        }
//        OrderDTO orderDTO = orderService.findByIdFull(orderId);
//        CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), null);
//        //生成取消单
//        CustomsInventoryCalloffDTO customsInventoryCalloff = new CustomsInventoryCalloffDTO();
//        customsInventoryCalloff.setOrderId(orderDTO.getId());
//        customsInventoryCalloff.setOrderSn(orderDTO.getSn());
//        customsInventoryCalloff.setDeclareOrderNo(orderDTO.getDeclareOrderNo());
//        customsInventoryCalloff.setSn(sequenceService.generateCalloffOrderSn());
//        customsInventoryCalloff.setEbcId(customsInventoryDTO.getEbcId());
//        customsInventoryCalloff.setAgentCompanyId(customsInventoryDTO.getAgentCompanyId());
//        customsInventoryCalloff.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
//        customsInventoryCalloff.setInventoryNo(customsInventoryDTO.getInventoryNo());
//        customsInventoryCalloff.setCustomsStatus(customsInventoryDTO.getCustomsStatus());
//        customsInventoryCalloff.setExitRegionStatus(customsInventoryDTO.getExitRegionStatus());
//        if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(customsInventoryDTO.getCustomsStatus())) {
//            customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.CALLOFF_DIRECT.getCode());
//            customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode());
//        } else {
//            customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.CALLOFF_EMPTY.getCode());
//            customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//        }
//        if (InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equalsIgnoreCase(customsInventoryCalloff.getCalloffStatus())) {
//            customsInventoryCalloff.setCalloffTime(new Date());
//        }
//        customsInventoryCalloff.setUserName("");
//
//        customsInventoryCalloffService.create(customsInventoryCalloff);
//        return new Response<>(0,"取消单创建成功");
//    }

//    @ApiOperation(value = "导入取消单")
//    @PostMapping("/import")
//    public Response<String> importCalloff(MultipartFile file) {
//        ImportParams importParams = new ImportParams();
//        // 数据处理
//        importParams.setHeadRows(1);
//        importParams.setTitleRows(1);
//        List<CustomsInventoryCalloffExcelVO> list;
//        try {
//            ExcelImportResult<CustomsInventoryCalloffExcelVO> result = ExcelImportUtil.importExcelMore(file.getInputStream(), CustomsInventoryCalloffExcelVO.class,
//                    importParams);
//            list = result.getList();
//        } catch (Exception e) {
//            log.warn("处理异常：{}", e.getMessage(), e);
//            throw new ArgsErrorException("上传失败");
//        }
//
//        List<CustomsInventoryCalloffExcelVO> failList = new  ArrayList<CustomsInventoryCalloffExcelVO>();;
//        for (CustomsInventoryCalloffExcelVO item : list) {
//            CustomsInventoryCalloffDTO calloffDto = customsInventoryCalloffService.findByOrderId(item.getOrderId());
//            if (calloffDto != null) {
////                return new Response<>(-1,"取消单已经生成");
//                failList.add(item);
//                continue;
//            }
//            OrderDTO orderDTO = orderService.findByIdFull(item.getOrderId());
//            CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), null);
//            //生成取消单
//            CustomsInventoryCalloffDTO customsInventoryCalloff = new CustomsInventoryCalloffDTO();
//            customsInventoryCalloff.setOrderId(item.getOrderId());
//            customsInventoryCalloff.setDeclareOrderNo(item.getDeclareOrderNo());
//            customsInventoryCalloff.setSn(sequenceService.generateCalloffOrderSn());
//            customsInventoryCalloff.setEbcId(item.getEbcId());
//            customsInventoryCalloff.setAgentCompanyId(item.getAgentCompanyId());
//            customsInventoryCalloff.setLogisticsNo(item.getLogisticsNo());
//            customsInventoryCalloff.setInventoryNo(item.getInventoryNo());
//            customsInventoryCalloff.setCustomsStatus(item.getCustomsStatus());
//            customsInventoryCalloff.setExitRegionStatus(item.getExitRegionStatus());
//            if (CustomsStat.CUSTOMS_REFUSE.getValue().equalsIgnoreCase(item.getCustomsStatus())) {
//                customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.CALLOFF_DIRECT.getCode());
//                customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode());
//            } else {
//                customsInventoryCalloff.setCalloffType(InventoryCalloffTypeEnum.CALLOFF_EMPTY.getCode());
//                customsInventoryCalloff.setCalloffStatus(InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode());
//            }
//            customsInventoryCalloff.setUserName(item.getUserName());
//            customsInventoryCalloffService.create(customsInventoryCalloff);
//        }
//
//
//
//        return new Response<>(0,"取消单导入完毕,共失败" + failList.size() + "笔！");
//    }

    @ApiOperation(value = "驳回取消单")
    @PostMapping("/reject")
    public Response<String> rejectCalloff(@RequestParam(required = true) Long id, String rejectReason) {
        CustomsInventoryCalloffDTO calloffDto = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            calloffDto = customsInventoryCalloffCRpc.findById(id);
        } else {
            calloffDto = customsInventoryCalloffService.findById(id);
        }
        if (!InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
            return new Response<>(-1, "状态错误，驳回操作失败");
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.updateCustomsInventoryCalloffStatus(id, InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode(),
                    null, null, rejectReason);
        } else {
            customsInventoryCalloffService.updateCustomsInventoryCalloffStatus(id, InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode(),
                    null, null, rejectReason);
        }
        // 回告退货仓取消单驳回
        refundOrderRpc.sendMessageToRefundWarehouse(calloffDto, RefundCallbackMessageStatus.CALLOFF_REJECT.getCode());
        return new Response<>(0, "驳回成功");
    }

    @ApiOperation(value = "取消驳回")
    @PostMapping("/cancelReject")
    public Response<String> cancelRejectCalloff(@RequestParam(required = true) Long id) {
        CustomsInventoryCalloffDTO calloffDto = null;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            calloffDto = customsInventoryCalloffCRpc.findById(id);
        } else {
            calloffDto = customsInventoryCalloffService.findById(id);
        }
        if (!InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
            return new Response<>(-1, "状态错误，取消驳回操作失败");
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.updateCustomsInventoryCalloffStatus(id, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(),
                    null, null, "");
        } else {
            customsInventoryCalloffService.updateCustomsInventoryCalloffStatus(id, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode(),
                    null, null, "");
        }

        return new Response<>(0, "取消驳回成功");
    }

    @ApiOperation(value = "从取消单发起直接取消申报单")
    @PostMapping("/directCalloff")
    public Response<String> direct(@RequestParam Long id, String calloffReason) {
        try {
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsInventoryCalloffCRpc.direct(id, calloffReason);
            } else {
                customsInventoryCalloffService.direct(id, calloffReason);
            }
            return new Response<>("取消成功！");
        } catch (ArgsErrorException e) {
            return new Response<>(-1, e.getErrorMessage());
        } catch (Exception e) {
            return new Response<>(-1, e.getMessage());
        }
    }

//    @ApiOperation(value = "取消单-退货")
//    @PostMapping("/returnCalloff")
//    public Response<String> returnCalloff(@RequestParam(required=true) Long id, String calloffReason) {
//        CustomsInventoryCalloffDTO calloffDto = customsInventoryCalloffService.findById(id);
//        if (!InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
//            return new Response<>(-1,"状态错误，退货操作失败");
//        }
//        return customsInventoryCalloffService.returnCalloff(id, calloffReason);
//    }
//
//    @ApiOperation(value = "取消单-撤单")
//    @PostMapping("/cancelCalloff")
//    public Response<String> cancelCalloff(@RequestParam(required=true) Long id, String calloffReason) {
//        CustomsInventoryCalloffDTO calloffDto = customsInventoryCalloffService.findById(id);
//        if (!InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus())) {
//            return new Response<>(-1,"状态错误，撤单操作失败");
//        }
//        return customsInventoryCalloffService.cancelCalloff(id, calloffReason);
//    }

    @ApiOperation(value = "各状态取消单个数查询", response = CustomsInventoryCalloffCountDTO.class)
    @PostMapping("/calloffStatusCount")
    public CustomsInventoryCalloffCountDTO calloffStatusCount(CustomsInventoryCalloffSearch search) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return customsInventoryCalloffCRpc.selectCountByCondition(search);
        } else {
            return customsInventoryCalloffService.selectCountByCondition(search);
        }
    }

    @ApiOperation(value = "统计-取消单个数")
    @PostMapping("/sumInventoryCalloff")
    public List<CalloffInfoSumVO> sumInventoryCalloff(Long beginTimeLong, Long endTimeLong) {
        Date beginTime = null;
        Date endTime = null;
        if (beginTimeLong != null) {
            beginTime = new Date();
            beginTime.setTime(beginTimeLong);
        }
        if (endTimeLong != null) {
            endTime = new Date();
            endTime.setTime(endTimeLong);
        }
        List<Map<String, Object>> list;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = customsInventoryCalloffCRpc.sumInventoryCalloff(beginTime, endTime);
        } else {
            list = customsInventoryCalloffService.sumInventoryCalloff(beginTime, endTime);
        }
        List<CalloffInfoSumVO> refundOrderInfoSumList = new ArrayList<CalloffInfoSumVO>();
        for (Map<String, Object> item : list) {
            CalloffInfoSumVO vo = new CalloffInfoSumVO();
            vo.setCount(Long.valueOf(item.get("c").toString()));
            vo.setDateStr(String.valueOf(item.get("d")));
            refundOrderInfoSumList.add(vo);
        }
        return refundOrderInfoSumList;
    }

    @PostMapping("/calloff/rejectBatch")
    public RpcResult<String> rejectCalloffBatch(@RequestBody RejectCalloffBatchReqVO reqVO) {
        String errorMsg = ValidatorUtils.doValidator(validator, reqVO);
        if (StringUtils.isNotBlank(errorMsg)) {
            return RpcResult.error("参数异常");
        }
        List<CustomsInventoryCalloffDTO> calloffDtoList;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            calloffDtoList = customsInventoryCalloffCRpc.findById(reqVO.getIdList());
        } else {
            calloffDtoList = customsInventoryCalloffService.findById(reqVO.getIdList());
        }
        List<CustomsInventoryCalloffDTO> filter = calloffDtoList.stream()
                .filter(calloffDto -> !InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode().equalsIgnoreCase(calloffDto.getCalloffStatus()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(filter)) {
            return RpcResult.error("状态错误，驳回操作失败");
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            customsInventoryCalloffCRpc.updateCustomsInventoryCalloffStatus(reqVO.getIdList(), InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode(),
                    null, null, reqVO.getRejectReason());
        } else {
            customsInventoryCalloffService.updateCustomsInventoryCalloffStatus(reqVO.getIdList(), InventoryCalloffStatusEnum.CALLOFF_REJECT.getCode(),
                    null, null, reqVO.getRejectReason());
        }
        // 回告退货仓取消单驳回
        for (CustomsInventoryCalloffDTO calloffDTO : calloffDtoList) {
            refundOrderRpc.sendMessageToRefundWarehouse(calloffDTO, RefundCallbackMessageStatus.CALLOFF_REJECT.getCode());
        }
        return RpcResult.success("驳回成功");
    }

    @PostMapping("/refundWarehouseCancel")
    public RpcResult refundWarehouseCancel(@RequestBody RefundWarehouseCancelReq req) {
        log.info("refundWarehouseCancel req={}", JSON.toJSONString(req));
        customsInventoryCalloffCRpc.refundWarehouseCancel(req);
        return RpcResult.success("操作成功");
    }

    @PostMapping("/jdRefundCancel")
    public RpcResult jdRefundCancel(@RequestBody JdCustomsOrderMsgDataDTO req) {
        log.info("jdRefundCancel req={}", JSON.toJSONString(req));
        customsInventoryCalloffCRpc.jdRefundCancel(req);
        return RpcResult.success("操作成功");
    }
}
