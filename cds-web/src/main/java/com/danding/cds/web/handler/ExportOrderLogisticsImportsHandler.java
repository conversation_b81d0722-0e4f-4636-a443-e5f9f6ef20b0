package com.danding.cds.web.handler;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import com.danding.cds.exportorder.api.dto.ExportItemWritingReport;
import com.danding.cds.exportorder.api.dto.ExportOrderDTO;
import com.danding.cds.exportorder.api.dto.ExportSkuInfo;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.upload.api.vo.ExportItemImportVO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 申报出库单导入
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_EXPORT_ORDER_LOGISTICS", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E7%94%B3%E6%8A%A5%E5%87%BA%E5%BA%93%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "申报出库单", classes = ExportItemImportVO.class),})
public class ExportOrderLogisticsImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Long id = Long.valueOf((String) extendMap.get("id"));
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        ExportOrderService exportOrderService = this.getBean(ExportOrderService.class);
        CustomsInventoryRpc customsInventoryRpc = SpringUtil.getBean("@Reference com.danding.cds.c.api.rpc.CustomsInventoryRpc", CustomsInventoryRpc.class);
        EntityWarehouseService entityWarehouseService = this.getBean(EntityWarehouseService.class);
        ExpressService expressService = this.getBean(ExpressService.class);

        List<ExportItemImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("申报出库单")) {
                list = group.getDataList(ExportItemImportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));

        if (CollUtil.isEmpty(list)) {
            log.error("ExportOrderLogisticsImportsHandler-导入未获取到数据");
            return;
        }

        log.info("ExportOrderLogisticsImportsHandler exportId={} 数据预处理 start = {}", id, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(id);
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        List<Long> customsBookIdList = new ArrayList<>();
        String entityWarehouseErrorMsg = "";
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            entityWarehouseErrorMsg += ("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在");
        } else {
            entityWarehouseDTOList = entityWarehouseDTOList.stream()
                    .filter(entityWarehouseDTO -> entityWarehouseDTO.getEnable() == 1
                            && EntityWarehouseTagEnums.containsAny(entityWarehouseDTO.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                    .collect(Collectors.toList());
            if (CollUtil.isEmpty(entityWarehouseDTOList)) {
                entityWarehouseErrorMsg += ("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在可用清关账册");
            } else {
                customsBookIdList = entityWarehouseDTOList.stream()
                        .map(EntityWarehouseDTO::getCustomsBookId)
                        .distinct()
                        .collect(Collectors.toList());
            }
        }

        List<ExportItemRecord> recordList = new ArrayList<>();
        Map<String, ExpressDTO> expressDTOMap = new HashMap<>();
        List<String> expressNameList = list.stream().map(ExportItemImportVO::getExpressName).collect(Collectors.toList());
        List<ExpressDTO> expressDTOList = expressService.findByName(expressNameList);
        expressDTOList.forEach(i -> expressDTOMap.put(i.getName(), i));

        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = new HashMap<>();
        List<String> logisticsNoList = list.stream().map(ExportItemImportVO::getMailNo).collect(Collectors.toList());
        List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryRpc.listByLogistics90Days(logisticsNoList);
        customsInventoryDTOS.forEach(i -> customsInventoryDTOMap.put(i.getLogisticsNo(), i));

        Map<Long, List<CustomsInventoryItemDTO>> customsInventoryItemMap = customsInventoryRpc.listItemByInventoryS(customsInventoryDTOS);

        log.info("ExportOrderLogisticsImportsHandler exportId={} 数据预处理 end = {}", id, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));

        ExportItemImportVO lastVo = list.get(list.size() - 1);
        Set<String> mailNoSet = new HashSet<>();
        int index = 2;
        for (ExportItemImportVO vo : list) {
            ExportItemRecord record = new ExportItemRecord();
            BeanUtils.copyProperties(vo, record);
            if (mailNoSet.contains(vo.getMailNo())) {
                this.callbackData(false, index++, "运单号重复", vo);
                continue;
            }
            try {
                this.fillInfo(record, expressDTOMap, customsInventoryDTOMap, customsInventoryItemMap);
            } catch (Exception e) {
                log.error("ExportOrderLogisticsImportsHandler exportId={} 数据预处理对象转换失败 error={}", id, e.getMessage(), e);
                this.callbackData(false, index++, "数据预处理失败", vo);
                continue;
            }
            String errorMsg = entityWarehouseErrorMsg;
            List<Long> expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(), Long.class);

            if (StringUtils.isEmpty(record.getMailNo())) {
                errorMsg += "运单号为空;";
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn()) && !StringUtils.isEmpty(record.getMailNo())) {
                errorMsg += "运单号系统不存在;";
            } else if (StringUtils.isEmpty(record.getExpressName())) {
                errorMsg += "快递公司为空;";
            } else {
                CustomsInventoryDTO customsInventoryDTO = record.getCustomsInventoryDTO();
                log.info("[op:ExportOrderManager-writing] step 3-1, customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
                if (customsInventoryDTO == null) {
                    errorMsg += "清单未找到;";
                } else {
//                    if (!exportOrderDTO.getDeclareCompanyId().equals(customsInventoryDTO.getDeclareCompanyId())) {
//                        errorMsg += "运单和申报出库单不属于同一个清关企业";
//                    } else
                    if (LongUtil.isNone(record.getExpressId())) {
                        errorMsg += "快递公司填写不正确;";
                    } else if (!expressIdList.contains(record.getExpressId())) {
                        errorMsg += "当前申报出库单不支持该快递公司;";
                    } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                        errorMsg += "清单未放行;";
                    } else if (exportOrderService.findItemByCustomInventory(customsInventoryDTO.getSn()) != null) {
                        errorMsg += "该运单已关联出库单;";
                    } else if (!customsBookIdList.contains(customsInventoryDTO.getAccountBookId())) {
                        errorMsg += "对应放行清单的账册与申报出库单的账册不一致;";
                    } else if (StringUtils.isBlank(customsInventoryDTO.getInventoryNo())) {
                        errorMsg += "对应清单编号为空;";
                    }
                }
            }
            if (StrUtil.isNotBlank(errorMsg)) {
                this.callbackData(false, index++, String.join(";", errorMsg), vo);
                continue;
            }
            recordList.add(record);
            mailNoSet.add(vo.getMailNo());
            if (recordList.size() < list.size()) {
                this.callbackData(true, index++, null, vo);
            }
        }
        // 全成功才执行 批量保存
        if (!recordList.isEmpty() && recordList.size() == list.size()) {
            log.info("ExportOrderLogisticsImportsHandler exportId={} 数据预处理 end = {}", id, DateUtil.format(new Date(), DatePattern.NORM_DATETIME_PATTERN));
            ExportItemWritingReport report = new ExportItemWritingReport();
            report.setTotalCount(recordList.size());
            report.setSuccessCount(recordList.size());
            report.setFailCount(0);
            report.setSuccessRecordList(recordList);
            report.setFailRecordList(new ArrayList<>());
            try {
                exportOrderService.writingByItem(id, report);
                // 数据落库后回传最后一个处理结果，保证数据一致性
                this.callbackData(true, index + 1, null, lastVo);
            } catch (Exception e) {
                log.error("ExportOrderLogisticsImportsHandler exportId={} 数据写入失败 error={}", id, e.getMessage(), e);
                this.callbackData(false, index + 1, "数据写入失败", lastVo);
            }
        }
    }


    private void fillInfo(ExportItemRecord record, Map<String, ExpressDTO> expressDTOMap,
                          Map<String, CustomsInventoryDTO> customsInventoryDTOMap,
                          Map<Long, List<CustomsInventoryItemDTO>> customsInventoryItemMap) {

        if (StringUtils.isEmpty(record.getMailNo())) {
            return;
        }
        CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOMap.get(record.getMailNo());
        if (expressDTOMap.containsKey(record.getExpressName())) {
            record.setExpressId(expressDTOMap.get(record.getExpressName()).getId());
        }
        if (customsInventoryDTO != null) {
            List<CustomsInventoryItemDTO> customsInventoryItemDTOS = customsInventoryItemMap.get(customsInventoryDTO.getId());
            List<ExportSkuInfo> skuInfoList = new ArrayList<>();
            for (CustomsInventoryItemDTO customsInventoryItemDTO : customsInventoryItemDTOS) {
                ExportSkuInfo skuInfo = new ExportSkuInfo();
                skuInfo.setBookItemId(customsInventoryItemDTO.getBookItemId());
                skuInfo.setCount(customsInventoryItemDTO.getCount());
                skuInfoList.add(skuInfo);
            }
            record.setSkuInfoList(skuInfoList);
            record.setGrossWeight(customsInventoryDTO.getGrossWeight() == null ? BigDecimal.ZERO : customsInventoryDTO.getGrossWeight());
            record.setNetWeight(customsInventoryDTO.getNetWeight());
            record.setCustomsInventorySn(customsInventoryDTO.getSn());
            record.setBizId(customsInventoryDTO.getDeclareOrderNo());
            customsInventoryDTOMap.put(customsInventoryDTO.getSn(), customsInventoryDTO);
            record.setCustomsInventoryDTO(customsInventoryDTO);
        }
    }
}
