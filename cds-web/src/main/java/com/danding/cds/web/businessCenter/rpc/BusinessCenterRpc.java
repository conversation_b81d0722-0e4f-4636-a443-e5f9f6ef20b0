package com.danding.cds.web.businessCenter.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.itemstocklist.api.vo.BcItemStockListSearch;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.v2.bean.vo.req.OverViewSearch;
import com.danding.cds.web.businessCenter.entity.vo.PersonWorkbenchReqVO;
import com.danding.cds.web.businessCenter.entity.vo.UpdBusinessTypeVO;

import java.util.List;

public interface BusinessCenterRpc {

    RpcResult getAllDataCount(PersonWorkbenchReqVO reqVO);

    RpcResult getStatusCountByGoodsRecord(OverViewSearch search);

    RpcResult<String> goodsRecordExport(OverViewSearch search);

    RpcResult getStatusCountByBondedPostSale(OverViewSearch search);

    RpcResult<String> bondedPostSaleExport(OverViewSearch search);

    RpcResult getStatusCountByCustomsComplete(OverViewSearch search);

    RpcResult<String> customsCompleteExport(OverViewSearch search);

    RpcResult listBusinessTypeFromDict();

    RpcResult updBusinessTypeInDict(UpdBusinessTypeVO businessTypeList);

    RpcResult delBusinessTypeInDict(SelectOptionVO vo);

    RpcResult listHandlerFromDict();

    RpcResult listHandlerFilterFromDict();

    RpcResult listAreaCompany();

    RpcResult<List<SelectOptionVO<String>>> listWareHouse();

    RpcResult itemStockListPaging(BcItemStockListSearch search);
}
