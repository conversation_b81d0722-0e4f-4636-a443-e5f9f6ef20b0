package com.danding.cds.web.express.rpc;

import com.danding.cds.express.api.dto.ExpressFilterDTO;
import com.danding.cds.express.api.dto.ExpressFilterSearch;
import com.danding.cds.express.api.dto.ExpressFilterSubmit;
import com.danding.cds.web.express.ExpressFilterController;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/10/28 13:51
 * @Description:
 */
@DubboService
public class ExpressFilterRpcImpl implements ExpressFilterRpc {
    @Autowired
    private ExpressFilterController expressFilterController;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express-filter/list", desc = "获取所有的快递选择的下来框,id为当前快动快递公司id")
    public RpcResult<List<SelectItemVO>> list(Long id) {
        return RpcResult.success(expressFilterController.list(id));
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express-filter/paging", desc = "列表查询配置分页")
    public RpcResult<ListVO<ExpressFilterDTO>> paging(ExpressFilterSearch search) {
        return RpcResult.success(expressFilterController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express-filter/delete", desc = "删除当前配置id")
    public RpcResult<Response<String>> delete(Long id) throws ArgsErrorException {
        return RpcResult.success(expressFilterController.delete(id));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express-filter/upset", desc = "新增快递配置方式")
    public RpcResult<Response<String>> upset(ExpressFilterSubmit submit) {
        return RpcResult.success(expressFilterController.upset(submit));
    }
}
