package com.danding.cds.web.exportorder.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.c.api.service.CustomsInventoryService;
import com.danding.cds.checklist.api.dto.ChecklistDTO;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.utils.ListVOBuilder;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.ExportItem;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.dto.EndorsementSubmit;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.enums.ExportOrderStatus;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.upload.api.vo.ExportItemImportVO;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.vo.EndorsementVO;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.web.exportorder.manager.ExportOrderManager;
import com.danding.cds.web.exportorder.vo.*;
import com.danding.cds.web.user.UserService;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.ucenter.client.secruity.helper.UserHelper;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "申请出库单管理")
@RequestMapping("/exportOrder")
public class ExportOrderController {
    @Autowired
    private ExportOrderManager exportOrderManager;

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @Autowired
    private UserService userService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private CustomsInventoryRpc customsInventoryService;

    @DubboReference
    private ChecklistService checklistService;

    @ApiOperation(value = "获取所有状态")
    @GetMapping("/listStatus")
    public List<SelectItemVO> listStatus() {
        List<SelectItemVO> result = Arrays.stream(ExportOrderStatus.values())
                .filter((ExportOrderStatus item) -> !item.equals(ExportOrderStatus.NULL))
                .map((ExportOrderStatus item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getValue());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "新增出区单")
    @PostMapping("/upset")
    public Long upset(@RequestBody ExportOrderSubmit submit) {
        Long id = exportOrderService.create(submit);
        return id;
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<ExportOrderResult> paging(ExportOrderSearch search) {
        // Step::预处理运单号
        if (!StringUtils.isEmpty(search.getQueryInfo()) && "mailNo".equals(search.getQueryType())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            Set<String> mailNoSet = new HashSet<>(noList);
            List<ExportItemDTO> itemList = exportOrderService.listItemByMailNos(mailNoSet);
            Set<Long> orderIds = itemList.stream().map(ExportItemDTO::getExportOrderId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(orderIds)) {
                return ListVOBuilder.buildEmptyListVO(ExportOrderResult.class);
            } else {
                search.setIdSet(orderIds);
            }
        }
        // Step::预处理关联单证
        else if (!StringUtils.isEmpty(search.getQueryInfo()) && "endorsementSn".equals(search.getQueryType())) {
            List<String> noList = Lists.newArrayList(search.getQueryInfo().split(","));
            Set<Long> endorsementIds = endorsementService.listBySns(noList).stream().map(EndorsementDTO::getId).collect(Collectors.toSet());
            List<ExportItemDTO> itemList = exportOrderService.listItemByEndorsementIds(endorsementIds);
            Set<Long> orderIds = itemList.stream().map(ExportItemDTO::getExportOrderId).collect(Collectors.toSet());
            if (CollectionUtils.isEmpty(orderIds)) {
                return ListVOBuilder.buildEmptyListVO(ExportOrderResult.class);
            } else {
                search.setIdSet(orderIds);
            }
        }
        // Step::预处理快递
        if (!LongUtil.isNone(search.getExpressId())) {
            Set<Long> idSet = exportOrderService.listIdsByExpress(search.getExpressId());
            if (!CollectionUtils.isEmpty(search.getIdSet())) {
                idSet.retainAll(search.getIdSet());
            }
            if (CollectionUtils.isEmpty(idSet)) {
                return ListVOBuilder.buildEmptyListVO(ExportOrderResult.class);
            } else {
                search.setIdSet(idSet);
            }
        }

        ListVO<ExportOrderDTOV2> paging = exportOrderService.paging(search);
        List<Long> userIdList = paging.getDataList().stream().map(p -> p.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(userIdList);
        Map<String, String> entityWarehouseMap = new HashMap<>();
        ListVO<ExportOrderResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        List<ExportOrderResult> dataList = new ArrayList<>();
        for (ExportOrderDTOV2 order : paging.getDataList()) {
            ExportOrderResult orderVO = new ExportOrderResult();
            BeanUtils.copyProperties(order, orderVO);
            orderVO.setId(order.getId());
            orderVO.setSn(order.getSn());
            List<EndorsementDTO> endorsementDTOList = endorsementService.listByExport(order.getId());
            String endorsementSn = "";
            for (EndorsementDTO endorsementDTO : endorsementDTOList) {
                if (!StringUtils.isEmpty(endorsementSn)) {
                    endorsementSn += "/";
                }
                endorsementSn += endorsementDTO.getSn();
            }
            endorsementSn = StringUtils.strip(endorsementSn, "/");
            orderVO.setEndorsementSns(endorsementSn);
//            CustomsBookDTO customsBookDTO = customsBookService.findById(order.getAccountBookId());
//            orderVO.setBookNo(customsBookDTO.getBookNo());
            List<String> expressCodeList = JSON.parseArray(order.getExpressList(), String.class);
            orderVO.setExpressNameList(new ArrayList<>());
            for (String expressId : expressCodeList) {
                ExpressDTO expressDTO = expressService.findById(Long.valueOf(expressId));
                if (expressDTO != null) {
                    orderVO.getExpressNameList().add(expressDTO.getName());
                }
            }
            if (order.getCreateTime() != null) {
                orderVO.setCreateAt(new DateTime(order.getCreateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            }
            if (order.getFinishTime() != null) {
                orderVO.setFinishAt(new DateTime(order.getFinishTime()).toString("yyyy/MM/dd HH:mm:ss"));
            }
            if (order.getStatus().equals(ExportOrderStatus.WRITING.getValue())) {
                orderVO.setAllowImport(true);
                orderVO.setAllowDiscard(true);
                orderVO.setAllowDelete(true);
                orderVO.setAllowLoadMailNo(true);
                orderVO.setAllowGenerateEndorsement(true);
            } else if (order.getStatus().equals(ExportOrderStatus.DECLARING.getValue()) || order.getStatus().equals(ExportOrderStatus.FINISH.getValue())) {
                orderVO.setAllowLoadMailNo(true);
            } else if (order.getStatus().equals(ExportOrderStatus.DISCARD.getValue())) {
                orderVO.setAllowDelete(true);
                orderVO.setAllowLoadMailNo(true);
            }
            if (order.getCreateBy() != null) {
                if (userRpcResultMap.containsKey(order.getCreateBy().longValue())) {
                    orderVO.setCreateBy(userRpcResultMap.get(order.getCreateBy().longValue()).getUserName());
                }
            }
            List<ExportItemDTO> exportItemDTOS = exportOrderService.listItemById(orderVO.getId());
            orderVO.setPackageCount(exportItemDTOS.size());
            orderVO.setEntityWarehouseName(entityWarehouseMap.computeIfAbsent(order.getEntityWarehouseCode(),
                    entityWarehouseCode -> {
                        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(entityWarehouseCode);
                        return CollUtil.isEmpty(entityWarehouseDTOList) ? "" : entityWarehouseDTOList.get(0).getErpWarehouseName();
                    }));
            orderVO.setStatus(ExportOrderStatus.getEnum(order.getStatus()).getDesc());
            dataList.add(orderVO);
        }
        result.setDataList(dataList);
        return result;
    }

    @ApiOperation(value = "输入预览")
//    @PostMapping("/pre/input-import")
    public ExportItemWritingReport preExport(@RequestBody InputParams inputParams) throws ArgsErrorException {
        if (inputParams.getId() == null || StringUtils.isEmpty(inputParams.getContent())) {
            throw new ArgsErrorException("参数传递错误");
        }
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(inputParams.getId());
        if (Objects.isNull(exportOrderDTO)) {
            throw new ArgsErrorException("未查询到关联的出库单");
        }
        if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
            throw new ArgsErrorException("出库单当前状态无法导入和录入运单号");
        }
        String lockName = "CCS:EXPORT:IMPORT:" + exportOrderDTO.getSn();
        boolean locked = RedissLockUtil.tryLock(lockName, TimeUnit.SECONDS, 3, -1);
        if (!locked) {
            log.error("出库单录入单号中，加锁失败 单号:{}", exportOrderDTO.getSn());
            throw new ArgsErrorException("出库单录入单号中，请稍后再试");
        }
        try {
            String lineInfos[] = StringUtils.split(inputParams.getContent(), "|");
            ArrayList<ExportItemImportVO> successList = new ArrayList<ExportItemImportVO>();
            for (String lineInfo : lineInfos) {
                if (StringUtils.isEmpty(lineInfo)) continue;
                String[] infos = lineInfo.split("\\s+", 2);
                ExportItemImportVO exportItemImportVO = new ExportItemImportVO();
                exportItemImportVO.setMailNo(StringUtils.strip(infos[0]));
                exportItemImportVO.setExpressName(StringUtils.strip(infos[1]));
                successList.add(exportItemImportVO);
            }
            int i = 1; // 行号，从2开始
            List<ExportItemRecord> preList = new ArrayList<>();
            for (ExportItemImportVO demoExcel : successList) {
                i++;
                if (StringUtils.isEmpty(demoExcel.getExpressName()) && StringUtils.isEmpty(demoExcel.getMailNo()))
                    continue;
                ExportItemRecord record = new ExportItemRecord();
                BeanUtils.copyProperties(demoExcel, record);
                record.setIdx(i);
                preList.add(record);
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(preList)) {
                throw new ArgsErrorException("对不起！录入信息为空或录入不正确");
            }
            return exportOrderManager.writing(inputParams.getId(), preList, false, null);
        } catch (Exception e) {
            log.error("录入失败 error={}", e.getMessage(), e);
            throw e;
        } finally {
            try {
                RedissLockUtil.unlock(lockName);
                log.info("录入 解锁成功 lockName={}", lockName);
            } catch (Exception ex) {
                log.error("解锁异常 lockName={} error={}", lockName, ex.getMessage(), ex);
            }
        }
    }

    @ApiOperation(value = "输入预览")
//    @PostMapping("/pre/input-import")
    public ExportItemWritingReport preExportThenSave(@RequestBody InputParams inputParams) throws ArgsErrorException {
        if (inputParams.getId() == null || StringUtils.isEmpty(inputParams.getContent())) {
            throw new ArgsErrorException("参数传递错误");
        }
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(inputParams.getId());
        if (Objects.isNull(exportOrderDTO)) {
            throw new ArgsErrorException("未查询到关联的出库单");
        }
        if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
            throw new ArgsErrorException("出库单当前状态无法导入和录入运单号");
        }
        String lockName = "CCS:EXPORT:IMPORT:" + exportOrderDTO.getSn();
        boolean locked = RedissLockUtil.tryLock(lockName, TimeUnit.SECONDS, 3, -1);
        if (!locked) {
            log.error("出库单录入单号中，加锁失败 单号:{}", exportOrderDTO.getSn());
            throw new ArgsErrorException("出库单录入单号中，请稍后再试");
        }
        try {
            String lineInfos[] = StringUtils.split(inputParams.getContent(), "|");
            ArrayList<ExportItemImportVO> successList = new ArrayList<ExportItemImportVO>();
            for (String lineInfo : lineInfos) {
                if (StringUtils.isEmpty(lineInfo)) continue;
                String[] infos = lineInfo.split("\\s+", 2);
                ExportItemImportVO exportItemImportVO = new ExportItemImportVO();
                exportItemImportVO.setMailNo(StringUtils.strip(infos[0]));
                exportItemImportVO.setExpressName(StringUtils.strip(infos[1]));
                successList.add(exportItemImportVO);
            }
            int i = 1; // 行号，从2开始
            List<ExportItemRecord> preList = new ArrayList<>();
            for (ExportItemImportVO demoExcel : successList) {
                i++;
                if (StringUtils.isEmpty(demoExcel.getExpressName()) && StringUtils.isEmpty(demoExcel.getMailNo()))
                    continue;
                ExportItemRecord record = new ExportItemRecord();
                BeanUtils.copyProperties(demoExcel, record);
                record.setIdx(i);
                preList.add(record);
            }
            if (org.apache.commons.collections4.CollectionUtils.isEmpty(preList)) {
                throw new ArgsErrorException("对不起！录入信息为空或录入不正确");
            }
            ExportItemWritingReport report = exportOrderManager.writing(inputParams.getId(), preList, false, null);
            ExportItemWritingReport successReport = new ExportItemWritingReport();
            BeanUtils.copyProperties(report, successReport);
            successReport.setFailCount(0);
            successReport.setFailRecordList(new ArrayList<>());
            exportOrderService.writingBlockByItem(inputParams.getId(), successReport);
            return report;
        } catch (Exception e) {
            log.error("录入失败 error={}", e.getMessage(), e);
            throw e;
        } finally {
            try {
                RedissLockUtil.unlock(lockName);
                log.info("录入 解锁成功 lockName={}", lockName);
            } catch (Exception ex) {
                log.error("解锁异常 lockName={} error={}", lockName, ex.getMessage(), ex);
            }
        }
    }


    @ApiOperation(value = "导入预览-暂停使用")
    @PostMapping("/pre/import/wait")
    public ExportItemWritingReport preExport(Long id, MultipartFile file) throws ArgsErrorException {
        ImportParams importParams = new ImportParams();
        // 数据处理
        importParams.setHeadRows(1);
        importParams.setTitleRows(0);
        List<ExportItemImportVO> successList;
        try {
            ExcelImportResult<ExportItemImportVO> result = ExcelImportUtil.importExcelMore(file.getInputStream(), ExportItemImportVO.class,
                    importParams);
            successList = result.getList();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            throw new ArgsErrorException("上传失败");
        }

        int i = 1; // 行号，从2开始
        List<ExportItemRecord> preList = new ArrayList<>();
        for (ExportItemImportVO demoExcel : successList) {
            i++;
            if (StringUtils.isEmpty(demoExcel.getExpressName()) && StringUtils.isEmpty(demoExcel.getMailNo())) continue;
            ExportItemRecord record = new ExportItemRecord();
            BeanUtils.copyProperties(demoExcel, record);
            record.setIdx(i);
            preList.add(record);
        }
        if (org.apache.commons.collections4.CollectionUtils.isEmpty(preList)) {
            throw new ArgsErrorException("对不起！选择模板文件为空或模板不正确");
        }
        return exportOrderManager.writing(id, preList, false, null);
    }

    @ApiOperation(value = "导入预览")
    @PostMapping("/pre/import")
    public ExportItemWritingReport preExportV2(Long id, MultipartFile file) throws ArgsErrorException {
        try {
            List<ExportItemImportVO> successList = EasyExcel.read(file.getInputStream()).headRowNumber(1).head(ExportItemImportVO.class).sheet().doReadSync();
            int i = 1; // 行号，从2开始
            List<ExportItemRecord> preList = new ArrayList<>();
            for (ExportItemImportVO demoExcel : successList) {
                i++;
                if (StringUtils.isEmpty(demoExcel.getExpressName())
                        && StringUtils.isEmpty(demoExcel.getMailNo())) {
                    continue;
                }
                ExportItemRecord record = new ExportItemRecord();
                BeanUtils.copyProperties(demoExcel, record);
                record.setIdx(i);
                preList.add(record);
            }
            if (CollectionUtils.isEmpty(preList)) {
                throw new ArgsErrorException("对不起！选择模板文件为空或模板不正确");
            }
            return exportOrderManager.writing(id, preList, false, null);
        } catch (IOException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            throw new ArgsErrorException("上传失败");
        }
    }

    @ApiOperation(value = "导出提交")
    @GetMapping("/submit/export")
    public void exportExcel(Long id, HttpServletResponse response) throws ArgsErrorException {
//        try {
//            IdParam idParam = new IdParam();
//            idParam.setId(id);
//            downloadProcessService.submitDownloadProcess(idParam, ReportType.EXPORT_ORDER_FOR_EXCEL);
//            return "提交成功";
//        } catch (ServiceException e) {
//            log.warn("处理异常：{}", e.getMessage(), e);
//            throw new ArgsErrorException(e.getMessage());
//        }
        List<ExportItem> exportVOList = new ArrayList<ExportItem>();
        List<ExportItemDTO> list = exportOrderService.listItemById(id);
        for (ExportItemDTO exportItemDTO : list) {
            ExportItem item = new ExportItem();
            ExpressDTO expressDTO = expressService.findById(exportItemDTO.getExpressId());
            item.setExpressName(expressDTO.getName());
            item.setMailNo(exportItemDTO.getMailNo());
            exportVOList.add(item);
        }
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("运单，快递名称导出");
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, ExportItem.class, exportVOList);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(
                    ("运单，快递名称数据导出" + DateTime.now().toString("yyMMddHHmm") + ".xlsx").getBytes(), "iso8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }


    @ApiOperation(value = "导入提交")
    @PostMapping("/submit/import")
    public ExportItemWritingReport importExcel(@RequestBody ExportItemImportSubmit submit) throws ArgsErrorException {
        ExportItemWritingReport report = exportOrderManager.writing(submit.getId(), submit.getRecordList(), true, null);
        log.info("[op:ExportOrderController-submit-import] submit={}, report={}", JSON.toJSONString(submit), JSON.toJSONString(report));
        exportOrderService.writingBlockByItem(submit.getId(), report);
        return report;
    }

    @ApiOperation(value = "查看运单")
    @GetMapping("/findMailById")
    public ExportItemSimpleVO findMailById(Long id) {
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(id);
        ExportItemSimpleVO simpleVO = new ExportItemSimpleVO();
        List<ExportItemDTO> itemDTOList = exportOrderService.listItemById(exportOrderDTO.getId());
        Map<String, ExportItemGroup> groupMap = new HashMap<>();
        Set<Long> bookItemSet = new HashSet<>();
        simpleVO.setPackageWeight(BigDecimal.ZERO);
        simpleVO.setPackageCount(0);
        for (ExportItemDTO exportItemDTO : itemDTOList) {
            ExportItemGroup groupVO = groupMap.get(exportItemDTO.getTrayNo());
            if (groupVO == null) {
                groupVO = new ExportItemGroup();
                groupVO.setItemList(new ArrayList<>());
                groupVO.setPackageWeight(BigDecimal.ZERO);
                groupVO.setTrayNo(exportItemDTO.getTrayNo());
                groupVO.setBookItemSet(new HashSet<>());
                groupVO.setPackageCount(0);
            }
            List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
            for (ExportSkuInfo skuInfo : skuInfoList) {
                bookItemSet.add(skuInfo.getBookItemId());
                groupVO.getBookItemSet().add(skuInfo.getBookItemId());
                groupVO.setSkuCount(groupVO.getBookItemSet().size());
            }
            ExportItemRecord record = new ExportItemRecord();
            record.setMailNo(exportItemDTO.getMailNo());
            record.setGrossWeight(exportItemDTO.getGrossWeight());
            groupVO.setPackageWeight(groupVO.getPackageWeight().add(exportItemDTO.getGrossWeight()));
            groupVO.setPackageCount(groupVO.getPackageCount() + 1);
            groupVO.getItemList().add(record);
            groupMap.put(exportItemDTO.getTrayNo(), groupVO);

            simpleVO.setPackageWeight(simpleVO.getPackageWeight().add(exportItemDTO.getGrossWeight()));
            simpleVO.setPackageCount(simpleVO.getPackageCount() + 1);
            simpleVO.setStationNo(exportItemDTO.getStationNo());
        }
        simpleVO.setSkuCount(bookItemSet.size());
        simpleVO.setGroupList(new ArrayList<>(groupMap.values()));
        return simpleVO;
    }

    @ApiOperation(value = "下拉允许生成核注单的申请出库单")
    @GetMapping("/listForEndorsement")
    public List<SelectItemVO> listForEndorsement() {
        List<ExportOrderDTO> orderDTOList = exportOrderService.listByStatus(ExportOrderStatus.WRITING.getValue());
        List<SelectItemVO> result = new ArrayList<>();
        for (ExportOrderDTO exportOrderDTO : orderDTOList) {
            SelectItemVO iItemVO = new SelectItemVO();
            iItemVO.setName(exportOrderDTO.getSn());
            iItemVO.setValue(exportOrderDTO.getId());
            result.add(iItemVO);
        }
        return result;
    }

    @ApiOperation(value = "作废")
    @PostMapping("/discard")
    public Long discard(@RequestBody IdParam idParam) {
        exportOrderService.discardById(idParam.getId());
        return idParam.getId();
    }

    @ApiOperation(value = "删除")
    @PostMapping("/delete")
    public Long delete(@RequestBody IdParam idParam) {
        exportOrderService.deleteById(idParam.getId());
        return idParam.getId();
    }

    @ApiOperation(value = "查看详情")
    @GetMapping("/detail")
    public ExportOrderDetailVO detail(IdParam param) {
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(param.getId());
        ExportOrderDetailVO detailVO = new ExportOrderDetailVO();
        detailVO.setId(exportOrderDTO.getId());
        detailVO.setSn(exportOrderDTO.getSn());
        detailVO.setStatus(exportOrderDTO.getStatus());
        detailVO.setStatusDesc(ExportOrderStatus.getDesc(exportOrderDTO.getStatus()));
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        if (!CollectionUtils.isEmpty(entityWarehouseDTOList)) {
            detailVO.setEntityWarehouseName(entityWarehouseDTOList.get(0).getErpWarehouseName());
        }
        List<Long> expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(), Long.class);
        List<ExpressDTO> expressDTOList = expressService.findByIds(expressIdList);
        if (CollUtil.isNotEmpty(expressDTOList)) {
            detailVO.setExpressNames(expressDTOList.stream().map(ExpressDTO::getName).collect(Collectors.joining(",")));
        }
        detailVO.setCreateTimeStr(DateUtil.formatDateTime(exportOrderDTO.getCreateTime()));
        Long createById = Long.valueOf(exportOrderDTO.getCreateBy());
        Map<Long, UserRpcResult> userMap = userService.listByIds(Collections.singletonList(createById));
        if (userMap.containsKey(createById)) {
            detailVO.setCreateByName(userMap.get(createById).getUserName());
        }
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        List<ExportItemDTO> exportItemDTOS = exportOrderService.listItemById(param.getId());
        for (ExportItemDTO item : exportItemDTOS) {
            totalGrossWeight = totalGrossWeight.add(item.getGrossWeight());
            totalNetWeight = totalNetWeight.add(item.getNetWeight());
        }
        detailVO.setTotalGrossWeight(totalGrossWeight);
        detailVO.setTotalNetWeight(totalNetWeight);
        return detailVO;
    }

    @PostMapping("/generateEndorsement")
    public void generateEndorsement(Long id) {
        if (Objects.isNull(id)) {
            throw new ArgsInvalidException("id不能为空");
        }
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(id);
        if (Objects.isNull(exportOrderDTO)) {
            throw new ArgsInvalidException("申请出库单不存在");
        }
        if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
            throw new ArgsInvalidException("申报出库单非已创建，无法创建核注单");
        }
        List<ExportItemDTO> exportItemDTOS = exportOrderService.listItemById(id);
        if (CollUtil.isEmpty(exportItemDTOS)) {
            throw new ArgsInvalidException("未关联包裹，无法创建核注单");
        }
        EndorsementSubmit submit = new EndorsementSubmit();
        submit.setIeFlag(1);
        submit.setBussinessType(EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode());
        submit.setChecklistsFlag(false);
        submit.setExportOrderId(id);
        submit.setStockChangeEnable(1);
        endorsementService.generate(submit, UserHelper.getUserId());
    }

    @PostMapping("/itemPaging")
    public ListVO<ExportItemVO> itemPaging(ExportOrderItemSearch search) {
        return buildItemListVO(exportOrderService.itemPaging(search));
    }

    private ListVO<ExportItemVO> buildItemListVO(ListVO<ExportItemDTO> paging) {
        ListVO<ExportItemVO> result = new ListVO<>();
        result.setPage(paging.getPage());

        Map<Long, String> expressMap = new HashMap<>();
        Map<Long, String> bookMap = new HashMap<>();
        List<ExportItemDTO> dataList = paging.getDataList();
        if (CollUtil.isEmpty(dataList)) {
            return result;
        }
        List<String> customsInventorySnList = dataList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
        List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.findBySnList(customsInventorySnList);
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = customsInventoryDTOList.stream()
                .collect(Collectors.toMap(CustomsInventoryDTO::getSn, Function.identity(), (v1, v2) -> v1));
        result.setDataList(dataList.stream().map((ExportItemDTO dto) -> {
            ExportItemVO vo = new ExportItemVO();
            vo.setId(dto.getId());
            vo.setMailNo(dto.getMailNo());
            vo.setExpressName(expressMap.computeIfAbsent(dto.getExpressId(), (Long expressId) -> expressService.findById(expressId).getName()));
            if (customsInventoryDTOMap.containsKey(dto.getCustomsInventorySn())) {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOMap.get(dto.getCustomsInventorySn());
                vo.setDeclareOrderNo(customsInventoryDTO.getDeclareOrderNo());
                vo.setInventoryNo(customsInventoryDTO.getInventoryNo());
            }
            vo.setAccountBookNo(bookMap.computeIfAbsent(dto.getAccountBookId(), (Long accountBookId) -> customsBookService.findById(accountBookId).getBookNo()));
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }

    @PostMapping("/listEndorsementById")
    public List<EndorsementVO> listEndorsementById(IdParam params) {
        List<EndorsementDTO> endorsementDTOS = endorsementService.findByExportOrderId(params.getId());
        List<Long> endorsementIdList = endorsementDTOS.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        Map<Long, List<ChecklistDTO>> checklistDTOMap = checklistService.getChecklistByEndorsementList(endorsementIdList);
        Map<Long, String> customsBookMap = new HashMap<>();
        return endorsementDTOS.stream().map(dto -> {
            EndorsementVO endorsementVO = new EndorsementVO();
            BeanUtils.copyProperties(dto, endorsementVO);
            endorsementVO.setStatusDesc(EndorsementOrderStatus.getEnum(dto.getStatus()).getDesc());
            endorsementVO.setBussinessTypeDesc(EndorsementBussiness.getEnum(dto.getBussinessType()).getDesc());
            endorsementVO.setRealEndorsementOrderNo(dto.getRealOrderNo());
            endorsementVO.setPreOrderNo(dto.getPreOrderNo());
            if (checklistDTOMap.containsKey(dto.getId())) {
                List<ChecklistDTO> checklistDTOList = checklistDTOMap.get(dto.getId());
                String checklistSnList = checklistDTOList.stream().map(ChecklistDTO::getSn).collect(Collectors.joining(","));
                endorsementVO.setChecklistSn(checklistSnList);
            }
            endorsementVO.setCustomsBookNo(customsBookMap.computeIfAbsent(dto.getAccountBookId(),
                    bookId -> {
                        CustomsBookDTO customsBookDTO = customsBookService.findById(bookId);
                        return (Objects.isNull(customsBookDTO)) ? "" : customsBookDTO.getBookNo();
                    })
            );
            endorsementVO.setCreateTime(DateUtil.formatDateTime(dto.getCreateTime()));
            return endorsementVO;
        }).collect(Collectors.toList());
    }
}
