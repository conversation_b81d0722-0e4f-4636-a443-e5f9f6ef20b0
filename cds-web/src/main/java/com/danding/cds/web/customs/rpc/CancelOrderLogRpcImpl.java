package com.danding.cds.web.customs.rpc;

import com.danding.cds.common.utils.ListBeanUtil;
import com.danding.cds.customs.inventory.api.dto.CancelOrderLogDTO;
import com.danding.cds.customs.inventory.api.service.CancelOrderLogService;
import com.danding.cds.web.customs.rpc.param.RpcCancelOrderLogParam;
import com.danding.cds.web.customs.vo.CancelOrderLogVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description 取消单业务日志
 * @date 2021/11/9
 */
@DubboService
@Slf4j
public class CancelOrderLogRpcImpl implements CancelOrderLogRpc{

    @DubboReference
    private CancelOrderLogService cancelOrderLogService;


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/cancel_order_log/selectByRelatedId",desc = "获取业务日志")
    public RpcResult<List<CancelOrderLogVO>> selectByRelatedIdList(RpcCancelOrderLogParam param){
        if (Objects.isNull(param.getRelatedId())) {
            return RpcResult.error("业务单ID不能为空！");
        }
        List<CancelOrderLogDTO> cancelOrderLogDTOList =cancelOrderLogService.selectByRelatedIdList(Long.valueOf(param.getRelatedId()),param.getOrderType());
        return RpcResult.success(ListBeanUtil.copy(cancelOrderLogDTOList,CancelOrderLogVO.class));
    }




}
