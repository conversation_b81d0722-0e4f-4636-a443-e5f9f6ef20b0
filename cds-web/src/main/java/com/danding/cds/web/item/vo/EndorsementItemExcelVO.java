package com.danding.cds.web.item.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 核注单表体(成品)Excel导入VO
 */
@Data
public class EndorsementItemExcelVO implements Serializable {

    @ExcelProperty("序号")
    @NotNull(message = "序号不能为空")
    private Integer serialNumber;

    @ExcelProperty("备案序号")
    @NotNull(message = "备案序号不能为空")
    private String goodsSeqNo;

    @ExcelProperty("商品料号")
    @NotNull(message = "商品料号不能为空")
    private String productId;

    @ExcelProperty("报关单商品序号")
    private String orderSeqNo;

    @ExcelProperty("流转申报表序号")
    private String lineSeqNo;

    @ExcelProperty("商品编码")
    @NotNull(message = "商品编码不能为空")
    private String hsCode;

    @ExcelProperty("商品名称")
    @NotNull(message = "商品名称不能为空")
    private String goodsName;

    @ExcelProperty("规格型号")
    @NotNull(message = "规格型号不能为空")
    private String goodsModel;

    @ExcelProperty("币制")
    @NotNull(message = "币制不能为空")
    private String currency;

    @ExcelProperty("申报计量单位代码")
    @NotNull(message = "申报计量单位代码不能为空")
    private String declareUnit;

    @ExcelProperty("法定计量单位代码")
    @NotNull(message = "法定计量单位代码不能为空")
    private String firstUnit;

    @ExcelProperty("法定第二计量单位代码")
    private String secondUnit;

    @ExcelProperty("申报数量")
    @NotNull(message = "申报数量不能为空")
    private BigDecimal declareUnitQfy;

    @ExcelProperty("法定数量")
    @NotNull(message = "法定数量不能为空")
    private BigDecimal firstUnitQfy;

    @ExcelProperty("第二法定数量")
    private BigDecimal secondUnitQfy;

    @ExcelProperty("企业申报单价")
    private BigDecimal declarePrice;

    @ExcelProperty("企业申报总价")
    private BigDecimal declareTotalPrice;

    @ExcelProperty("原产国(地区）")
    @NotNull(message = "原产国(地区）不能为空")
    private String originCountry;

    @ExcelProperty("最终目的国（地区）")
    private String destinationCountry;

    @ExcelProperty("重量比例因子")
    private String weightModule;

    @ExcelProperty("第一比例因子")
    private String firstModule;

    @ExcelProperty("第二比例因子")
    private String secondModule;

    @ExcelProperty("毛重")
    private BigDecimal grossWeight;

    @ExcelProperty("净重")
    private BigDecimal netWeight;

    @ExcelProperty("征免方式")
    private String avoidTaxMethod;

    @ExcelProperty("单耗版本号")
    private String version;

    @ExcelProperty("危化品标志")
    private String dangerousFlag;

    @ExcelProperty("备注")
    private String remark;

    @ExcelProperty("来源标识")
    private String goodsSource;
}
