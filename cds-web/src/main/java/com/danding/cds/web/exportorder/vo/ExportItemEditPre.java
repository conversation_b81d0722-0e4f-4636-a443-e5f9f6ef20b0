package com.danding.cds.web.exportorder.vo;

import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class ExportItemEditPre {

    @ApiModelProperty("核注清单ID")
    private Long id;

    @ApiModelProperty("运单列表")
    private List<ExportItemRecord> recordList;
}
