package com.danding.cds.web.checklist.rpc;

import com.danding.cds.checklist.api.dto.ChecklistSubmit;
import com.danding.cds.checklist.api.enums.ChecklistType;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.annotation.ParamValidatorV2;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ThreadContextUtil;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.endorsement.api.dto.*;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.ExportItemEditReport;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationEditReport;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvAddParam;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvDeleteParam;
import com.danding.cds.v2.enums.EndorsementOrderTypeEnums;
import com.danding.cds.web.checklist.controller.EndorsementController;
import com.danding.cds.web.checklist.rpc.param.RpcEndorsementParam;
import com.danding.cds.web.checklist.rpc.param.RpcModifyStatusParam;
import com.danding.cds.web.checklist.vo.*;
import com.danding.cds.web.exportorder.vo.ExportItemEditPre;
import com.danding.cds.web.exportorder.vo.ExportItemEditSubmit;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditPre;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditSubmit;
import com.danding.cds.web.item.vo.EndorsementImportSubmit;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.BusinessErrorCode;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcException;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @menu 核注单管理
 */
@DubboService
@Slf4j
@RestController
public class EndorsementRpcImpl implements EndorsementRpc {

    @DubboReference
    private EndorsementService endorsementService;

    @Autowired
    private EndorsementController endorsementController;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private ChecklistService checklistService;

    @Override
    @SoulClient(path = "/endorsement/createByChecklist", desc = "根据关联单据生成核注单")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Response<String>> createByChecklist(EndorsementSubmit submit) {
        String lockKey = "ccs:endorsement:create:";
        if (StringUtils.hasText(submit.getBussinessType())) {
            lockKey = lockKey + submit.getBussinessType() + ":";
        }
        if (submit.getExportOrderId() != null) {
            lockKey = lockKey + submit.getExportOrderId().toString() + ":";
        }
        if (submit.getInventoryOrderId() != null) {
            lockKey = lockKey + submit.getInventoryOrderId().toString();
        }
        log.info("根据关联单据生成核注单，幂等锁key:{}", lockKey);
        try {
            boolean locked = RedissLockUtil.tryLock(lockKey, 1, 60);
            if (!locked) {
                return RpcResult.error("相同的任务正在执行，请勿多次提交");
            }
            return RpcResult.success(endorsementController.createByChecklist(submit));
        } finally {
            try {
                RedissLockUtil.unlock(lockKey);
            } catch (Exception ex) {
                log.warn("解锁异常，可能是根本就没有加到锁就解锁了");
            }
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/load-detail-info", desc = "核注清单明细")
    public RpcResult<EndorsementDetailWarp> loadDetailInfo(RpcEndorsementParam param) {
        return RpcResult.success(endorsementController.loadDetailInfo(param.getEndorsementId()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/list-bussiness-types", desc = "获取所有核注业务类型")
    public RpcResult<List<SelectOptionVO>> listBussinessTypes() {
        List<SelectOptionVO> result = Arrays.stream(EndorsementBussiness.values())
                .filter((EndorsementBussiness item) -> !item.equals(EndorsementBussiness.BUSSINESS_EMPTY))
                .map((EndorsementBussiness item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/list-bussiness-types-checklist", desc = "核放单获取所有核注业务类型")
    public RpcResult<List<SelectOptionVO>> listBussinessTypesChecklist() {
        List<SelectOptionVO> result = Arrays.stream(EndorsementBussiness.values())
                .filter((EndorsementBussiness item) -> !item.equals(EndorsementBussiness.BUSSINESS_EMPTY) && item.getShowInChecklist())
                .map((EndorsementBussiness item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 根据核放单类型获取核注业务类型
     *
     * @param checklistType
     * @return
     * @path /endorsement/listBusinessTypesByChecklist
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/listBusinessTypesByChecklist", desc = "核放单获取所有核注业务类型")
    public RpcResult<List<SelectOptionVO<String>>> listBusinessTypesByChecklist(Integer checklistType) {
        Map<Integer, EndorsementBussiness[]> checkListTypeMapEndorsementBussiness = ChecklistType.getCheckListTypeMapEndorsementBussiness();
        List<SelectOptionVO<String>> result = Arrays.stream(checkListTypeMapEndorsementBussiness.get(checklistType))
                .map((EndorsementBussiness item) -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/listStatus", desc = "获取所有核注状态")
    public RpcResult<List<SelectOptionVO>> listStatus() {
        List<SelectOptionVO> result = Arrays.stream(EndorsementOrderStatus.values())
                .filter((EndorsementOrderStatus item) -> !item.equals(EndorsementOrderStatus.NULL))
                .map((EndorsementOrderStatus item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     * @path /endorsement/paging
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/paging", desc = "分页查询")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<EndorsementSearchResult>> paging(EndorsementSearch search) {
        // 可以查看的账册的ID列表,与查询条件账册ID比较
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            Long bookId = search.getCustomsBookId();
            if (bookId != null && !accountBookIdList.contains(bookId)) {
                return RpcResult.success(new ListVO<>());
            }
        }
        Response<ListVO<EndorsementSearchResult>> response = endorsementController.paging(search);
        ListVO<EndorsementSearchResult> resultListVO = (ListVO<EndorsementSearchResult>) response.getResult();
        return RpcResult.success(resultListVO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/preRefundEditMailNo", desc = "(退货入区)编辑运单号预览")
    public RpcResult<InventoryOrderRelationEditReport> preRefundEditMailNo(InventoryOrderRelationEditPre submit) {
        return RpcResult.success(endorsementController.preRefundEditMailNo(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/submitRefundEditMailNo", desc = "(退货入区)编辑运单号提交")
    public RpcResult<InventoryOrderRelationEditReport> submitEditMailNo(InventoryOrderRelationEditSubmit submit) {
        return RpcResult.success(endorsementController.submitEditMailNo(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/preEditMailNo", desc = "编辑运单号预览")
    public RpcResult<ExportItemEditReport> preEditMailNo(ExportItemEditPre submit) {
        return RpcResult.success(endorsementController.preEditMailNo(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/edit/submit", desc = "核注编辑运单号提交")
    public RpcResult<ExportItemEditReport> hzEditSubmit(ExportItemEditSubmit submit) {
        return RpcResult.success(endorsementController.submitEditMailNo(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
//    @SoulClient(path = "/endorsement/submitEditMailNo", desc = "编辑运单号提交")
    public RpcResult<ExportItemEditReport> submitEditMailNo(ExportItemEditSubmit submit) {
        return RpcResult.success(endorsementController.submitEditMailNo(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/temporaryStorage", desc = "暂存核注")
    public RpcResult<Response<String>> temporaryStorage(IdsParam idsParam) {
        Response<String> response = endorsementController.temporaryStorage(idsParam);
        if (Objects.equals(response.getCode(), BusinessErrorCode.ARGS_ERROR.getCode())) {
            return RpcResult.error(response.getCode(), response.getErrorMessage());
        } else {
            return RpcResult.success(response);
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/saveRemark", desc = "备注保存")
    public RpcResult<Response<String>> saveRemark(EndorsementRemarkVO endorsementRemarkVO) {
        return RpcResult.success(endorsementController.saveRemark(endorsementRemarkVO));
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/push", desc = "推送核注")
    public RpcResult<Response<String>> retryException(IdsParam param) {
        String lockKey = "ccs:endorsement:push:" + param.getIdList();
        try {
            boolean locked = RedissLockUtil.tryLock(lockKey, 1, 60);
            if (!locked) {
                return RpcResult.error("相同的任务正在执行，请勿多次提交");
            }
            return RpcResult.success(endorsementController.retryException(param));
        } finally {
            try {
                RedissLockUtil.unlock(lockKey);
            } catch (Exception ex) {
                log.warn("解锁异常，可能是根本就没有加到锁就解锁了");
            }
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/handler-check", desc = "手动核扣")
    public RpcResult<Response<String>> handlerCheck(IdsParam param) {
        try {
            return RpcResult.success(endorsementController.handlerCheck(param.getIds()));
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/finish", desc = "完成核注审核")
    public RpcResult<Response<String>> finish(EndorsementFinishSubmit submit) {
        return RpcResult.success(endorsementController.finish(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/discard", desc = "作废核注清单")
    public RpcResult<Response<String>> discard(IdParam idParam) {
        return RpcResult.success(endorsementController.discard(idParam));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/listForChecklist", desc = "下拉允许做核放的核注清单")
    public RpcResult<List<EndorsementChecklistVO>> listForChecklist() {
        return RpcResult.success(endorsementController.listForChecklistByType());
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/endorsement/effective/book/auth/listForChecklist", desc = "下拉允许做核放的核注清单")
    public RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklist() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        return RpcResult.success(endorsementController.listForChecklistByType(accountBookList));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/endorsement/effective/book/auth/listForChecklistByType", desc = "下拉允许做核放的核注清单(加业务类型筛选)")
    public RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklistByType(String businessType) {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        return RpcResult.success(endorsementController.listForChecklistByType(accountBookList, businessType));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/endorsement/effective/book/auth/listForChecklistByTypeAndCompany", desc = "下拉允许做核放的核注清单(加业务类型和申报企业筛选)")
    public RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklistByTypeAndCompany(String businessType, Long companyId) {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        return RpcResult.success(endorsementController.listForChecklistByType(accountBookList, businessType, companyId));
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/endorsement/effective/book/auth/listByTypeAndCompanyWithBound", desc = "下拉允许做核放的核注清单(加业务类型和申报企业筛选)添加已绑定的核注单")
    public RpcResult<List<EndorsementChecklistVO>> listByTypeAndCompanyWithBound(String businessType, Long companyId, Long checklistId) {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        return RpcResult.success(endorsementController.listForChecklistByTypeWithBound(accountBookList, businessType, companyId, checklistId));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/excelExport", desc = "根据查询条件导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> excelExport(EndorsementSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.ENDORSEMENT_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 导出表体
     *
     * @param idParam
     * @return
     * @path /endorsement/singleExport
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/singleExport", desc = "指定ID导出")
    public RpcResult<String> singleExport(IdParam idParam) {
        EndorsementSearch search = new EndorsementSearch();
        Set<Long> idSet = new HashSet<>();
        idSet.add(idParam.getId());
        search.setIdSet(idSet);
        search.setCurrentPage(1);
        search.setPageSize(10);
        search.setPageIgnore(0);
        try {
            downloadProcessService.submitDownloadProcess(
                    UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.ENDORSEMENT_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 剔除异常
     *
     * @param idParam
     * @return
     * @path /endorsement/eliminateException
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/eliminateException", desc = "剔除异常")
    public RpcResult eliminateException(IdParam idParam) {
        try {
            return RpcResult.success(endorsementController.eliminateException(idParam));
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("剔除异常失败");
        }
    }

    @Override
    @SoulClient(path = "/endorsement/manualUpdStatus", desc = "批量手动修改核注单状态")
    public RpcResult<String> manualUpdStatus(RpcModifyStatusParam param) {
        if (ObjectUtils.isEmpty(param)) {
            return RpcResult.error("传入参数为空");
        }
        for (Long id : param.getIdList()) {
            EndorsementDTO endorsementDTO = new EndorsementDTO();
            BeanUtils.copyProperties(param, endorsementDTO);
            endorsementDTO.setId(id);
            endorsementService.manualUpdStatus(endorsementDTO);
        }
        return RpcResult.success("修改成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/manualDeletedApply", desc = "手动修删除申请")
    public RpcResult<String> manualDeletedApply(IdParam idParam) {
        if (Objects.isNull(idParam)) {
            return RpcResult.error("参数为空");
        }
        try {
            endorsementService.manualDeletedApply(idParam.getId());
            return RpcResult.success("删除成功");
        } catch (ArgsInvalidException e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("删除失败：" + e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("删除失败：" + e.getMessage());
        }

    }

    /**
     * 核注单详情
     *
     * @param id
     * @return
     * @path /endorsement/detail
     */
    @SoulClient(path = "/endorsement/detail", desc = "核注单详情")
    @Override
    public RpcResult<EndorsementDetailResVO> detail(Long id) {
        try {
            return RpcResult.success(endorsementController.detail(id));
        } catch (Exception e) {
            log.error("核注单详情 查看失败 e={}", e.getMessage(), e);
            return RpcResult.error("查看失败");
        }
    }

    /**
     * 核注单详情-编辑
     *
     * @param reqVO
     * @return
     * @path /endorsement/detail/edit
     */
    @SoulClient(path = "/endorsement/detail/edit", desc = "核注单详情-编辑")
    @Override
    public RpcResult<String> editDetail(EndorsementEditDetailReqVO reqVO) {
        try {
            endorsementController.editDetail(reqVO);
            return RpcResult.success("保存成功");
        } catch (Exception e) {
            log.error("核注单详情 保存失败 e={}", e.getMessage(), e);
            return RpcResult.error("保存失败");
        }
    }

    /**
     * 电商清单分页查询
     *
     * @param search
     * @return
     * @path /endorsement/pagingEbInvEs
     */
    @SoulClient(path = "/endorsement/pagingEbInvEs", desc = "电商清单分页查询")
    @Override
    public RpcResult<ListVO<EndorsementEbInvPagingResVO>> pagingEbInvEs(EndorsementEbInvSearch search) {
        try {
            return RpcResult.success(endorsementController.pagingEbInvEs(search));
        } catch (ArgsInvalidException e) {
            log.error(e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("电商清单查询失败 e={}", e.getMessage(), e);
            return RpcResult.error("电商清单查询失败");
        }
    }


    /**
     * 核注批量增加
     *
     * @param addParam
     * @return
     * @path /endorsement/addEbInvBatch
     */
    @SoulClient(path = "/endorsement/addEbInvBatch", desc = "核注电商清单-批量添加")
    @Override
    public RpcResult addEbInvBatch(EndorsementEbInvAddParam addParam) {
        try {
            return RpcResult.success(endorsementController.addEbInvBatch(addParam));
        } catch (com.danding.logistics.api.exceptions.businessException.ArgsErrorException e) {
            log.error("批量增加失败" + e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("批量增加失败" + e.getMessage(), e);
            return RpcResult.error("批量增加失败");
        }
    }

    /**
     * 核注批量增加
     *
     * @param addParam
     * @return
     * @path /endorsement/addEbInvBatch/preview
     */
    @SoulClient(path = "/endorsement/addEbInvBatch/preview", desc = "核注电商清单-批量添加预览")
    @Override
    public RpcResult addEbInvBatchPreview(EndorsementEbInvAddParam addParam) {
        try {
            return RpcResult.success(endorsementController.addEbInvBatchPreView(addParam));
        } catch (com.danding.logistics.api.exceptions.businessException.ArgsErrorException e) {
            log.error("批量增加失败" + e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("批量增加失败" + e.getMessage(), e);
            return RpcResult.error("批量增加失败");
        }
    }

    /**
     * 核注批量删除
     *
     * @param deleteParam
     * @return
     * @path /endorsement/deleteEbInvBatch
     */
    @SoulClient(path = "/endorsement/deleteEbInvBatch", desc = "核注电商清单-批量删除")
    @Override
    public RpcResult deleteEbInvBatch(EndorsementEbInvDeleteParam deleteParam) {
        try {
            return RpcResult.success(endorsementController.deleteEbInvBatch(deleteParam));
        } catch (com.danding.logistics.api.exceptions.businessException.ArgsErrorException e) {
            log.error("删除失败" + e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("删除失败" + e.getMessage(), e);
            return RpcResult.error("删除失败");
        }
    }

    /**
     * 核注批量删除预览
     *
     * @param deleteParam
     * @return
     * @path /endorsement/deleteEbInvBatch/preview
     */
    @SoulClient(path = "/endorsement/deleteEbInvBatch/preview", desc = "核注电商清单-批量删除预览")
    @Override
    public RpcResult deleteEbInvBatchPreview(EndorsementEbInvDeleteParam deleteParam) {
        try {
            return RpcResult.success(endorsementController.deleteEbInvBatchPreView(deleteParam));
        } catch (com.danding.logistics.api.exceptions.businessException.ArgsErrorException e) {
            log.error("删除失败" + e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("删除失败" + e.getMessage(), e);
            return RpcResult.error("删除失败");
        }
    }

    /**
     * 删除申请
     *
     * @param params
     * @return
     * @path /endorsement/deletedApply
     */
    @Override
    @SoulClient(path = "/endorsement/deletedApply", desc = "删除申请")
    public RpcResult<String> deletedApply(IdsParam params) {
        if (Objects.isNull(params)) {
            return RpcResult.error("参数为空");
        }
        try {
            endorsementService.deletedApply(params.getIdList());
            return RpcResult.success("删除申请发送成功");
        } catch (ArgsInvalidException e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("删除申请发送失败：" + e.getMessage());
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error("删除申请发送失败：" + e.getMessage());
        }
    }

    /**
     * 获取各状态的统计数量
     *
     * @param search
     * @return
     * @path /endorsement/countPagingStatus
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/countPagingStatus", desc = "获取各状态的统计数量")
    public RpcResult<List<PagingStatusCountVO>> countPagingStatus(EndorsementSearch search) {
        try {
            Map<String, Integer> countMap = endorsementService.countPagingStatus(search);
            Iterator<Map.Entry<String, Integer>> entryIterator = countMap.entrySet().iterator();
            List<PagingStatusCountVO> countList = new ArrayList<>();
            while (entryIterator.hasNext()) {
                Map.Entry<String, Integer> next = entryIterator.next();
                String status = next.getKey();
                Integer value = next.getValue();
                PagingStatusCountVO countVO = new PagingStatusCountVO();
                countVO.setStatus(status);
                countVO.setStatusDesc(EndorsementOrderStatus.getEnum(status).getDesc());
                countVO.setCount(value);
                countList.add(countVO);
            }
            return RpcResult.success(countList);
        } catch (ArgsInvalidException e) {
            log.error("获取统计数量失败 error={}", e.getErrorMessage(), e);
        }
        return RpcResult.error("获取统计数量失败");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/endorsement/updatePreOrderNo", desc = "更新预录入编号")
    @RequestMapping("/endorsement/updatePreOrderNo")
    public RpcResult<String> updatePreOrderNo(EndorsementPreOrderNoSubmit preOrderNoSubmit) {
        log.info("updatePreOrderNo 操作人={} submit={}", SimpleUserHelper.getRealUserName(), preOrderNoSubmit);
        try {
            String preOrderNo = StringUtils.trimAllWhitespace(preOrderNoSubmit.getPreOrderNo());
            endorsementService.updatePreNoAndSyncEs(preOrderNoSubmit.getId(), preOrderNo);
            return RpcResult.success("更新成功");
        } catch (ArgsInvalidException e) {
            log.error("updatePreOrderNo error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("updatePreOrderNo error={}", e.getMessage(), e);
            return RpcResult.error("更新预录入编号失败");
        }
    }

    @Override
    @SoulClient(path = "/endorsement/createChecklist", desc = "创建核放单")
    public RpcResult createChecklist(ChecklistSubmit submit) {
        try {
            checklistService.createChecklist(submit);
            return RpcResult.success("创建成功");
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        } catch (ArgsInvalidException e) {
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/endorsement/listChecklistType", desc = "获取核放单类型")
    public RpcResult listChecklistType(String bussinessType) {
        return RpcResult.success(checklistService.listTypeByEndorsement(bussinessType));
    }

    @Override
    @SoulClient(path = "/endorsement/listChecklistTypeByEndorsementId", desc = "获取核放单类型")
    public RpcResult listChecklistTypeByEndorsementId(String bussinessType, Long endorsementId) {
        return RpcResult.success(checklistService.listTypeByEndorsement(bussinessType, endorsementId));
    }

    /**
     * 导入核注清单
     *
     * @param submit
     * @return
     * @path /endorsement/importExcel
     */
    @Override
    @SoulClient(path = "/endorsement/importExcel", desc = "导入核注清单")
    @ParamValidatorV2
    public RpcResult importExcel(EndorsementImportSubmit submit) {
        try {
            endorsementController.importExcel(submit);
        } catch (RpcException e) {
            return RpcResult.error("核注单生成失败，" + e.getMessage());
        }
        return RpcResult.success();
    }

    /**
     * 获取允许导入的核注业务类型
     *
     * @return
     * @path /endorsement/listImportBusinessType
     */
    @SoulClient(path = "/endorsement/listImportBusinessType", desc = "获取允许导入的核注业务类型下拉")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listImportBusinessType() {
        return RpcResult.success(EndorsementBussiness.getAllowImportBusinessList().stream().map(e ->
                        new SelectOptionVO<>(e.getCode(), e.getDesc()))
                .collect(Collectors.toList()));
    }


    /**
     * 获取核注单类型
     *
     * @return
     * @path /endorsement/listOrderType
     */
    @SoulClient(path = "/endorsement/listOrderType", desc = "获取核注单类型")
    @Override
    public RpcResult<List<EnumUtils>> listOrderType() {
        return RpcResult.success(EnumUtils.build(EndorsementOrderTypeEnums.class, "code", "desc"));
    }

}
