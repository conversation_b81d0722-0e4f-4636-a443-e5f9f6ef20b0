package com.danding.cds.web.exception.rpc;

import com.danding.cds.exception.api.vo.ExceptionReqVO;
import com.danding.cds.exception.api.vo.ExceptionUpsetReqVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;

public interface ExceptionRpc {

    RpcResult paging(ExceptionReqVO reqVO);

    RpcResult upsetException(ExceptionUpsetReqVO upsetReqVO)throws ArgsErrorException;

    RpcResult ExceptionType();

    RpcResult exceptionName();
}
