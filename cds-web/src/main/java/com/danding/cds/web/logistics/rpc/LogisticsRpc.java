package com.danding.cds.web.logistics.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.logistics.api.dto.LogisticsSearch;
import com.danding.cds.web.logistics.vo.LogisticsSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface LogisticsRpc {

    RpcResult<ListVO<LogisticsSearchResult>> paging(LogisticsSearch search);

    RpcResult<List<SelectOptionVO<Integer>>> listStatus();

    RpcResult<String> rePush(String sns);

}
