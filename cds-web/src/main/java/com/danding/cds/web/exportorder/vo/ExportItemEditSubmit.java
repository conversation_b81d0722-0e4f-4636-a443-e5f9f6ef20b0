package com.danding.cds.web.exportorder.vo;

import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@ApiModel
@Data
public class ExportItemEditSubmit {

    @ApiModelProperty("核注清单ID")
    private Long id;

    @ApiModelProperty("新增列表")
    private List<ExportItemRecord> addList = new ArrayList<>();

    @ApiModelProperty("删除列表")
    private List<ExportItemRecord> deleteList = new ArrayList<>();
}
