package com.danding.cds.web.express.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.express.api.dto.ExpressConfigParam;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.dto.ExpressSubmit;
import com.danding.cds.web.express.vo.ExpressSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface ExpressRpc {

    RpcResult<List<SelectOptionVO<Long>>> list();

    RpcResult<List<SelectOptionVO<Long>>> listEnable();

    RpcResult<ListVO<ExpressSearchResult>> paging(ExpressSearch search);

    RpcResult<Long> upset( ExpressSubmit submit);

    RpcResult listExpress();

    RpcResult config(ExpressConfigParam param);

    RpcResult listLogisticsDeclareSystem();
}
