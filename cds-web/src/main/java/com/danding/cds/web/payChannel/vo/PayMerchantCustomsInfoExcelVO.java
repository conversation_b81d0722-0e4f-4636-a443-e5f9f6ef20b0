package com.danding.cds.web.payChannel.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 15:08
 * @Description:
 */
@Data
public class PayMerchantCustomsInfoExcelVO {
    /**
     * 商户编码
     */
    @Excel(name = "商户编码")
    private String merchantSn;

    @Excel(name = "商户名称")
    private String merchantName;

    /**
     * 口岸
     */
    @Excel(name = "口岸")
    private String customs;

    /**
     * 商户海关备案编号
     */
    @Excel(name = "商户海关备案编号")
    private String merchantCustomsCode;

    /**
     * 商户海关备案编号
     */
    @Excel(name = "商户海关备案名称")
    private String merchantCustomsName;


    @Excel(name = "创建时间")
    private String createTime;
}
