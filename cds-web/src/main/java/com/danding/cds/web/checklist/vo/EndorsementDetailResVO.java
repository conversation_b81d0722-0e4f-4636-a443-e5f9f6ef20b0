package com.danding.cds.web.checklist.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
public class EndorsementDetailResVO {

    // region ------------- 基本信息 -------------
    private Long id;
    /**
     * 企业内部核注清单编号
     */
    private String sn;

    /**
     * 预录入核注单号
     */
    private String preOrderNo;

    /**
     * 核注清单编号
     */
    private String realOrderNo;

    /**
     * 业务类型
     */
    private String bussinessType;
    private String bussinessTypeDesc;


    /**
     * 进出标志
     */
    private String ieFlag;

    /**
     * 清关单号
     */
    private String inventoryOrderSn;

    /**
     * 申报出库单号
     */
    private String exportOrderSn;

    /**
     * 账册编号
     */
    private String customsBookNo;

    /**
     * 核放企业单号
     */
    private List<String> checklistSnList;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 完成时间
     */
    private Date finishTime;

    /**
     * 核注单状态
     */
    private String status;
    private String statusDesc;

    /**
     * 海关回执
     */
    private String customsStatusDesc;

    /**
     * 是否核扣账册
     */
    private String stockChangeFlag;

    /**
     * 报关单统一编号
     */
    private String customsEntrySeqNo;

    /**
     * 报关单生成状态
     */
    private String generateDeclareStatusDesc;

    /**
     * 操作人
     */
    private String createBy;
    // endregion

    // region ------------- 核注单表头 -------------
    /**
     * 清单类型
     */
    private String customsInvtType;

    /**
     * 总件数
     */
    private BigDecimal totalDeclareQty;

    /**
     * 运输方式
     */
    private String transportMode;
    private String transportModeDesc;

    /**
     * 监管方式
     */
    private String supvMode;

    /**
     * 总净重（kg）
     */
    private BigDecimal totalNetWeight;

    /**
     * 总毛重（kg）
     */
    private BigDecimal totalGrossWeight;

    /**
     * 进出境关别
     */
    private String entryExitCustoms;

    /**
     * 主管海关
     */
    private String customsCode;

    /**
     * 启运/运抵国
     */
    private String shipmentCountry;

    /**
     * 关联核注清单编号
     */
    private String rltEndorsementNo;

    /**
     * 关联账册备案号
     */
    private String rltCustomsBookNo;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 报关标志(一线就默认都是报关，非一线就是非报关)(1:是 2:否)
     */
    private String customsFlag;

    /**
     * 是否生成报关单(1:是 0:否)
     */
    private String declarationFlag;

    /**
     * 报关单类型
     */
    private String customsEntryType;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private String customsType;

    /**
     * 录入员IC卡
     */
    private String inputerIcCard;

    /**
     * 备注
     */
    private String remark;

    /**
     * 经营单位id
     */
    private Long tradeCompanyId;

    /**
     * 经营单位名称
     */
    private String tradeCompanyName;

    /**
     * 经营单位USCC
     */
    private String tradeCompanyUSCC;

    /**
     * 经营单位编码
     */
    private String tradeCompanyCode;

    /**
     * 加工单位id
     */
    private Long processCompanyId;
    /**
     * 加工单位名称
     */
    private String processCompanyName;

    /**
     * 加工单位USCC
     */
    private String processCompanyUSCC;

    /**
     * 加工单位编码
     */
    private String processCompanyCode;

    /**
     * 申报单位id
     */
    private Long declareCompanyId;
    /**
     * 申报单位名称
     */
    private String declareCompanyName;

    /**
     * 申报单位USCC
     */
    private String declareCompanyUSCC;

    /**
     * 申报单位编码
     */
    private String declareCompanyCode;

    /**
     * 录入单位id
     */
    private Long inputCompanyId;
    /**
     * 录入单位名称
     */
    private String inputCompanyName;

    /**
     * 录入单位USCC
     */
    private String inputCompanyUSCC;

    /**
     * 录入单位编码
     */
    private String inputCompanyCode;

    /**
     * 对应报关单申报单位id
     */
    private Long corrCusDeclareCompanyId;
    /**
     * 对应报关单申报单位名称
     */
    private String corrCusDeclareCompanyName;

    /**
     * 对应报关单申报单位USCC
     */
    private String corrCusDeclareCompanyUSCC;

    /**
     * 对应报关单申报单位编码
     */
    private String corrCusDeclareCompanyCode;

    /**
     * 关联报关单境内收发货人id
     */
    private Long rltCusInnerSFHRCompanyId;
    /**
     * 关联报关单境内收发货人名称
     */
    private String rltCusInnerSFHRCompanyName;

    /**
     * 关联报关单境内收发货人USCC
     */
    private String rltCusInnerSFHRCompanyUSCC;

    /**
     * 关联报关单境内收发货人编码
     */
    private String rltCusInnerSFHRCompanyCode;

    /**
     * 关联报关单消费使用单位id
     */
    private Long rltCusXFDYCompanyId;
    /**
     * 关联报关单消费使用单位名称
     */
    private String rltCusXFDYCompanyName;
    /**
     * 关联报关单消费使用单位USCC
     */
    private String rltCusXFDYCompanyUSCC;
    /**
     * 关联报关单消费使用单位编码
     */
    private String rltCusXFDYCompanyCode;

    /**
     * 关联报关单申报单位id
     */
    private Long rltCusDeclareCompanyId;
    /**
     * 关联报关单申报单位名称
     */
    private String rltCusDeclareCompanyName;
    /**
     * 关联报关单申报单位USCC
     */
    private String rltCusDeclareCompanyUSCC;
    /**
     * 关联报关单申报单位编码
     */
    private String rltCusDeclareCompanyCode;

    /**
     * 单据类型 {@link com.danding.cds.v2.enums.EndorsementOrderTypeEnums}
     * INVENTORY_ORDER("1", "清关单"),
     * EXPORT_ORDER("2", "申报出库单"),
     * EXCEL_IMPORT("3", "导入");
     */
    private String orderType;

    // endregion
    /**
     * 核注单表体
     */
    private List<EndorsementItemDetailVO> endorsementItemDetailVOList;

    /**
     * 核注单日志
     */
    private List<EndorsementTrackLogVO> endorsementTrackLogVOList;


    /**
     * 核注单料件表体
     */
    private List<EndorsementItemDetailVO> endorsementItemGoodsDetailVOList;
}
