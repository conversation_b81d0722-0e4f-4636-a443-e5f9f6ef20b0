package com.danding.cds.web.payChannel.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 10:12
 * @Description:
 */
@Data
public class PayMerchantAccountExcelVO {

    /**
     * 商户编码
     */
    @Excel(name = "商户编码")
    private String sn;

    /**
     * 商户名称
     */
    @Excel(name = "商户名称")
    private String name;

    /**
     * 收款企业
     */
    @Excel(name = "收款企业")
    private String companyName;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String note;

    @Excel(name = "创建时间")
    private String createTime;
}
