package com.danding.cds.web;

import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/15 14:52
 * @Description:
 */
@Component
@Aspect
@Slf4j
public class DubboExceptionAspect {

    /**
     * 任何持有@Transactional注解的方法
     */
    @Pointcut(value = "@annotation(com.danding.soul.client.common.annotation.SoulClient)")
    private void transactionalPointcut() {
    }

    /**
     * 任何持有@Transactional注解的方法异常处理切面
     * 将自定义的业务异常转为RuntimeException:
     * 1.规避dubbo的包装，让customer可以正常获取message
     * 2.抛出RuntimeException使事务可以正确回滚
     * 其他异常不处理
     *
     * @param pjp 处理点
     * @return Object
     */
    @Around("transactionalPointcut()")
    public Object doTransactionalAround(ProceedingJoinPoint pjp) {
        return this.processException(pjp);
    }

    /**
     * 处理异常
     *
     * @param pjp 切点
     * @return
     */
    private Object processException(ProceedingJoinPoint pjp) {
        Object[] args = pjp.getArgs();
        try {
            return pjp.proceed();
        } catch (ArgsErrorException e) { // 自定义异常
            printlnException(pjp, args, e, "参数异常");
            throw new BusinessException(-1, e.getErrorMessage());
        } catch (BusinessException e) { // 自定义异常
            printlnException(pjp, args, e, "业务校验异常");
            throw new BusinessException(-1, e.getMessage());
        } catch (ArgsInvalidException invalidException) {
            printlnException(pjp, args, invalidException, "参数验证异常");
            throw new BusinessException(-1, invalidException.getMessage());
        } catch (Throwable e) {
            printlnException(pjp, args, e, "未知系统异常");
            throw new BusinessException(-1, "未知系统异常，请联系技术处理", e);
        }
    }

    /**
     * 异常信息打印
     *
     * @param joinPoint 切点
     * @param args      参数
     * @param throwable 异常
     */
    private void printlnException(final ProceedingJoinPoint joinPoint, final Object[] args, Throwable throwable, String desc) {
        MethodSignature signature = (MethodSignature) joinPoint.getSignature();
        Method method = signature.getMethod();
        String[] classNameArray = method.getDeclaringClass().getName().split("\\.");
        String methodName = classNameArray[classNameArray.length - 1] + "  >>> " + method.getName();
        String inputParam = "";
        if (args != null && args.length > 0) {
            StringBuilder sb = new StringBuilder();
            for (Object arg : args) {
                sb.append(",");
                sb.append(arg);
            }
            inputParam = sb.toString().substring(1);
        }
        log.warn("{} -【异常Message】：{} 【方法】: {} 【入参】: {}", desc, throwable.getMessage(), methodName, inputParam, throwable);
    }
}