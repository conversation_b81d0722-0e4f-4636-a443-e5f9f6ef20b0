package com.danding.cds.web.coverter;

import com.danding.logistics.api.common.response.ListResponse;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.web.common.convertor.InnerResponseMessageConverter;
import org.springframework.http.HttpOutputMessage;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageNotWritableException;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;

import java.io.IOException;

public class CdsResponseMessageConverter extends InnerResponseMessageConverter {

    @Override
    public void write(Object o, MediaType contentType, HttpOutputMessage outputMessage) throws IOException, HttpMessageNotWritableException {
        if (this.httpMessageConverter == null) {
            this.httpMessageConverter = new MappingJackson2HttpMessageConverter();
        }

        if (!(o instanceof Response) && !(o instanceof ListResponse)) {
            this.httpMessageConverter.write(new Response(o), contentType, outputMessage);
        } else {
            this.httpMessageConverter.write(o, contentType, outputMessage);
        }
    }
}
