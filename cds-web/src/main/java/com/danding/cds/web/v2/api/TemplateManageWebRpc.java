package com.danding.cds.web.v2.api;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.template.api.dto.TemplateManageSearch;
import com.danding.cds.template.api.dto.TemplateManageSubmit;
import com.danding.cds.web.v2.bean.result.TemplateManageResult;
import com.danding.logistics.api.common.response.ListVO;

import java.util.List;

/**
 * <AUTHOR>
 * @description: 模板管理
 * @date 2025/7/31 11:09
 */
public interface TemplateManageWebRpc {

    /**
     * 分页查询模板
     *
     * @param search 查询参数
     * @return 分页结果
     */
    RpcResult<ListVO<TemplateManageResult>> paging(TemplateManageSearch search);

    /**
     * 上传模板
     *
     * @param submit 提交参数
     * @return 模板ID
     */
    RpcResult<Long> upload(TemplateManageSubmit submit);

    /**
     * 编辑模板
     *
     * @param submit 提交参数
     * @return 模板ID
     */
    RpcResult<Long> edit(TemplateManageSubmit submit);

    /**
     * 根据ID查询模板详情
     *
     * @param idParam ID参数
     * @return 模板详情
     */
    RpcResult<TemplateManageResult> getById(IdParam idParam);

    /**
     * 获取模板用途下拉列表
     *
     * @return 用途列表
     */
    RpcResult<List<SelectOptionVO<String>>> listTemplatePurpose();

    /**
     * 获取启用口岸下拉列表
     *
     * @return 口岸列表
     */
    RpcResult<List<SelectOptionVO<String>>> listEnabledPorts();
}
