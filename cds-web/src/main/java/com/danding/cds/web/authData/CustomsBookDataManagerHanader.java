package com.danding.cds.web.authData;

import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.ucenter.client.data.base.BaseDataManagerHandler;
import com.danding.ucenter.core.api.data.annotation.DataHandler;
import com.danding.ucenter.core.api.data.model.DataModel;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 数据管理
 *
 * <AUTHOR>
 * @date 2021/4/6 9:54
 * @Description:
 */
@DataHandler(groupCode = CommonCons.GROUP_CUSTOMS_BOOK, groupName = CommonCons.GROUP_CUSTOMS_BOOK_NAME)
public class CustomsBookDataManagerHanader extends BaseDataManagerHandler {

    @DubboReference
    private CustomsBookService customsBookService;

    /**
     * 数据源
     *
     * @return
     */
    @Override
    public List<DataModel> dataSource() {
        List<CustomsBookDTO> customsBookDTOS = customsBookService.listAllBooks();
        if (CollectionUtils.isEmpty(customsBookDTOS)) {
            return Collections.emptyList();
        }
        List<DataModel> dataModelList = new ArrayList<>();
        // 口岸和账册
        customsBookDTOS.stream()
                // 口岸分组 --> Map<String,List<CustomsBookDTO>>
                .collect(Collectors.groupingBy(CustomsBookDTO::getCustomsDistrictCode))
                // 遍历处理，口岸及其对应的的账册列表
                .forEach((customsDistrict, CustomsBookList) -> {
                    final CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(customsDistrict);
                    if (CustomsDistrictEnum.NULL.equals(customsDistrictEnum)) {
                        return;
                    }
                    // 第一层数据：口岸
                    String code = customsDistrictEnum.getCode();
                    String desc = customsDistrictEnum.getDesc();
                    DataModel dataModel = DataModel.build(code, desc, code);
                    dataModel.setCodeType(CommonCons.CUSTOMS_PORT);
                    dataModel.setValueType(String.class.getName());

                    dataModel.setList(new ArrayList<>());
                    for (CustomsBookDTO customsBookDTO : CustomsBookList) {
                        // 第二层数据：账册
                        if (customsBookDTO.getCustomsDistrictCode().equalsIgnoreCase(code)) {
                            String bookNo = customsBookDTO.getBookNo();
                            Long value = customsBookDTO.getId();
                            customsBookDTO.getAreaCompanyId();

                            DataModel book = DataModel.build(customsBookDTO.getId().toString(), bookNo, "" + value);
                            book.setCodeType(CommonCons.CUSTOMS_ACC_BOOK);
                            book.setValueType(Long.class.getName());
                            book.toEnable(Objects.equals(customsBookDTO.getEnable(), 1) ? true : false);
                            dataModel.getList().add(book);
                        }
                    }
                    dataModelList.add(dataModel);

                });
        return dataModelList;
    }
}
