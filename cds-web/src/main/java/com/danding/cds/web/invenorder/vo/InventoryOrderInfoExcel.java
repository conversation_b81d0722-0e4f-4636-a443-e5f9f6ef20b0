package com.danding.cds.web.invenorder.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

import java.util.Date;
@Data
public class InventoryOrderInfoExcel {
    @Excel(name = "清关单", needMerge = true)
    private String inveCustomsSn;
    @Excel(name = "清关状态", needMerge = true)
    private String status;
    @Excel(name = "提单", needMerge = true)
    private String pickUpNo;
    @Excel(name = "备注", needMerge = true)
    private String remark;
    @Excel(name = "创建时间", needMerge = true ,format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    @Excel(name = "完成时间", needMerge = true ,format = "yyyy-MM-dd HH:mm:ss")
    private Date completeTime;

}
