package com.danding.cds.web.payChannel.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayChannelSearch;
import com.danding.cds.payChannel.api.dto.PayChannelSubmit;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.web.payChannel.PayChannelController;
import com.danding.cds.web.payChannel.vo.PayChannelSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.List;
import java.util.stream.Collectors;

@DubboService
public class PayChannelRpcImpl implements PayChannelRpc {

    @DubboReference
    private PayChannelService payChannelService;

    @Autowired
    private PayChannelController payChannelController;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/channel/list", desc = "支付渠道下拉")
    public RpcResult<List<SelectOptionVO<Long>>> list() {
        List<PayChannelDTO> dataList = payChannelService.listEnable();
        List<SelectOptionVO<Long>> result = dataList.stream().map((PayChannelDTO item) -> {
            return (SelectOptionVO<Long>) new SelectOptionVO(item.getId(),item.getName());
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/channel/paging", desc = "支付渠道分页查询")
    public RpcResult<ListVO<PayChannelSearchResult>> paging(PayChannelSearch search) {
        return RpcResult.success(payChannelController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/channel/upset", desc = "支付渠道编辑|更新")
    public RpcResult<Long> upset(PayChannelSubmit submit) {
        return RpcResult.success(payChannelController.upset(submit));
    }

}
