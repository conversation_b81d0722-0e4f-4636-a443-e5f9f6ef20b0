package com.danding.cds.web.message.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MessageTaskSearchVO {
    private Long id;

    /**
     * 消息id
     */
    private Long messageId;

    /**
     * 订阅ID
     */
    private String subscribeDetail;

    /**
     * 消息类型|冗余
     */
    private String type;
    private String typeDesc;

    /**
     * 任务类型
     */
    private String taskType;
    private String taskTypeDesc;

    /**
     * 业务编号|冗余
     */
    private String businessCode;

    /**
     * 执行回传地址
     */
    private String notifyUrl;

    /**
     * 发送记录
     */
    private String sendRecordJson;

    /**
     * 通知次数
     */
    private Integer count;

    /**
     * 最后回传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastNotifyTime;

    /**
     * 下次回传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date nextNotifyTime;

    /**
     * 执行状态
     */
    private Integer status;
    private String statusDesc;

    /**
     * 请求数据
     */
    private String requestData;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
