package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.download.api.vo.GoodsRecordBatchUpdateImportVO;
import com.danding.cds.download.api.vo.GoodsRecordExportVO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.submit.GoodsRecordImportUpdateReqVo;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 自动备案导入(导入平台备案)
 */
@Slf4j
@ParkImportsHandler(funcCode = "GOODS_RECORD_BATCH_UPDATE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%89%B9%E9%87%8F%E6%9B%B4%E6%96%B0%E5%A4%87%E6%A1%88.xlsx",
        groups = {@ParkImportsHandler.Group(name = "商品备案", classes = GoodsRecordBatchUpdateImportVO.class),})
public class GoodsRecordBatchUpdateImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        List<GoodsRecordBatchUpdateImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("商品备案")) {
                list = group.getDataList(GoodsRecordBatchUpdateImportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));


        if (CollUtil.isEmpty(list)) {
            log.error("GoodsRecordBatchUpdateImportHandler 导入未获取到数据");
            return;
        }
        int index = 1;
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        log.info("GoodsRecordBatchUpdateImportHandler operator={}", userInfo);
        for (GoodsRecordBatchUpdateImportVO vo : list) {
            try {
                if (StrUtil.isNotBlank(vo.getGoodsRecordName())) {
                    vo.setGoodsRecordName(StringUtils.trimWhitespace(vo.getGoodsRecordName()));
                }
                GoodsRecordImportUpdateReqVo goodsRecordImportUpdateReqVo = ConvertUtil.beanConvert(vo, GoodsRecordImportUpdateReqVo.class);
                ImportResultResVo res = goodsRecordService.goodsRecordImportUpdate(goodsRecordImportUpdateReqVo);
                log.info("备案更新导入后的返回结果数据{}", JSON.toJSONString(res));
                this.callbackData(res.getFlag(), index++, res.getReason(), vo);
            } catch (Exception e) {
                if (e instanceof ArgsInvalidException) {
                    callbackData(false, index, e.getMessage(), vo);
                } else {
                    log.error("商品备案批量更新导入异常", e);
                    callbackData(false, index, "商品备案批量更新异常", vo);
                }
            }
            index++;
        }
    }

    /**
     * 校验excel转换对象后的数据准确性
     *
     * @param vo                       excel转换对象
     * @param goodsRecordDTOMap
     * @param customsMap
     * @param countryMap
     * @param uomMap
     * @param currencyMap
     * @param customsDictionaryService
     * @return 校验结果
     */
    public String check(GoodsRecordExportVO vo,
                        Map<String, GoodsRecordDTO> goodsRecordDTOMap, Map<String, CustomsDistrictDTO> customsMap,
                        Map<String, CustomsDictionaryDTO> countryMap,
                        Map<String, CustomsDictionaryDTO> uomMap,
                        Map<String, CustomsDictionaryDTO> currencyMap,
                        CustomsDictionaryService customsDictionaryService) {
        CustomsHsService customsHsService = this.getBean(CustomsHsService.class);
        List<String> errorMsg = new ArrayList<>();
        // 校验必填项 统一料号、备案名称、规格型号、HS编码、海关原产国、法一单位、法一数量、口岸、0-驳回 1-通过；
        if (StrUtil.isEmpty(vo.getProductId())) {
            errorMsg.add("统一料号不能为空");
        }
        if (!goodsRecordDTOMap.containsKey(vo.getProductId())) {
            errorMsg.add("统一料号不存在或ERP已删除");
        }
        if (StrUtil.isBlank(vo.getGoodsRecordName())) {
            errorMsg.add("备案名称不能为空");
        }
        if (StrUtil.isBlank(vo.getModel())) {
            errorMsg.add("规格型号不能为空");
        }
        if (StrUtil.isBlank(vo.getHsCode())) {
            errorMsg.add("HS编码不能为空");
        } else {
            CustomsHsDTO hsDTO = customsHsService.findByCode(vo.getHsCode());
            if (Objects.isNull(hsDTO)) {
                errorMsg.add("hs：" + vo.getHsCode() + "，不存在正面清单");
            } else {
                if (StrUtil.isNotBlank(hsDTO.getSecondLegalUnit())) {
                    if (StrUtil.isBlank(vo.getSecondUnit())) {
                        errorMsg.add("法二单位不能为空");
                    } else {
                        CustomsDictionaryDTO uomDTO = getDictionaryDTO(uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
                        if (Objects.isNull(uomDTO) || !Objects.equals(uomDTO.getCode(), hsDTO.getSecondLegalUnit())) {
                            errorMsg.add("法二单位与HS编码法定第二计量单位不符");
                        }
                    }
                    if (Objects.isNull(vo.getSecondUnitAmount())) {
                        errorMsg.add("法二数量不能为空");
                    }
                }
                CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                        uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
                if (Objects.isNull(uomDTO) || !Objects.equals(uomDTO.getCode(), hsDTO.getFirstLegalUnit())) {
                    errorMsg.add("法一单位与HS编码法定计量单位不符");
                }
            }
        }
        if (StrUtil.isBlank(vo.getOriginCountry())) {
            errorMsg.add("海关原产国不能为空");
        } else {
            CustomsDictionaryDTO countryDTO = getDictionaryDTO(
                    countryMap, DataDictionaryTypeEnums.COUNTRY.getValue(), vo.getOriginCountry(), customsDictionaryService);
            if (Objects.isNull(countryDTO)) {
                errorMsg.add("海关原产国填写错误");
            }
        }
        if (StrUtil.isBlank(vo.getFirstUnit())) {
            errorMsg.add("法一单位不能为空");
        } else {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getFirstUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("法一单位填写错误");
            }
        }
        if (Objects.isNull(vo.getFirstUnitAmount())) {
            errorMsg.add("法一数量不能为空");
        }
        if (StrUtil.isBlank(vo.getCustoms())) {
            errorMsg.add("口岸不能为空");
        } else {
            CustomsDistrictDTO customsDistrictDTO = customsMap.get(vo.getCustoms());
            if (Objects.isNull(customsDistrictDTO)) {
                errorMsg.add("口岸填写错误");
            }
        }
        if (StrUtil.isNotBlank(vo.getDeclareCurrency())) {
            CustomsDictionaryDTO currencyDTO = getDictionaryDTO(
                    currencyMap, DataDictionaryTypeEnums.CURRENCY.getValue(), vo.getDeclareCurrency(), customsDictionaryService);
            if (Objects.isNull(currencyDTO)) {
                errorMsg.add("申报币制填写错误");
            }
        }
        if (Objects.isNull(vo.getOpinion())) {
            errorMsg.add("0-驳回 1-通过 不能为空");
        } else if (Objects.equals(0, vo.getOpinion())) {
            if (StrUtil.isBlank(vo.getReason())) {
                errorMsg.add("驳回原因不能为空");
            }
        }
        if (StrUtil.isNotBlank(vo.getDeclareUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getDeclareUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("申报单位不存在，请核实");
            }
        }
        if (StrUtil.isNotBlank(vo.getSecondUnit())) {
            CustomsDictionaryDTO uomDTO = getDictionaryDTO(
                    uomMap, DataDictionaryTypeEnums.UOM.getValue(), vo.getSecondUnit(), customsDictionaryService);
            if (Objects.isNull(uomDTO)) {
                errorMsg.add("法二单位填写错误");
            }
            if (Objects.isNull(vo.getSecondUnitAmount())) {
                errorMsg.add("法二单位存在，法二数量不能为空");
            }
        }
        return String.join(";", errorMsg);
    }


    private CustomsDictionaryDTO getDictionaryDTO(Map<String, CustomsDictionaryDTO> uomMap, String type, String name,
                                                  CustomsDictionaryService customsDictionaryService) {
        return uomMap.computeIfAbsent(name, key -> {
            List<CustomsDictionaryDTO> dictionaryDTOList = customsDictionaryService.findByTypeAndName(type, key);
            return CollUtil.isNotEmpty(dictionaryDTOList) ? dictionaryDTOList.get(0) : null;
        });
    }

}
