package com.danding.cds.web.handler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.web.v2.bean.vo.req.InventoryItemBatchUpdateImportVO;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 清关单表体批量修改导入
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_INVENTORY_ITEM_BATCH_UPDATE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E8%A1%A8%E4%BD%93%E6%89%B9%E9%87%8F%E4%BF%AE%E6%94%B9%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "修改表体导入", classes = InventoryItemBatchUpdateImportVO.class),})
public class InventoryItemBatchUpdateImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Long inventoryOrderId = Long.valueOf((String) extendMap.get("orderId"));
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        InventoryOrderInfoService inventoryOrderInfoService = this.getBean(InventoryOrderInfoService.class);

        List<InventoryItemBatchUpdateImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("修改表体导入")) {
                list = group.getDataList(InventoryItemBatchUpdateImportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));

        if (CollUtil.isEmpty(list)) {
            log.error("InventoryItemBatchUpdateImportsHandler-导入未获取到数据");
            return;
        }
        List<InventoryOrderItemDTO> itemDTOList = ConvertUtil.listConvert(list, InventoryOrderItemDTO.class);
        List<ImportResultResVo> resList = inventoryOrderInfoService.importExcelBatchUpdItem(inventoryOrderId, itemDTOList);
        int index = 1;
        for (ImportResultResVo res : resList) {
            res.setData(BeanUtils.copyProperties(res.getData(), InventoryItemBatchUpdateImportVO.class));
            log.info("清关单表体批量修改导入,导入后的返回结果数据{}", JSON.toJSONString(res));
            this.callbackData(res.getFlag(), index++, res.getReason(), res.getData());
        }
    }
}
