package com.danding.cds.web.express.rpc;

import com.danding.cds.express.api.dto.ExpressFilterDTO;
import com.danding.cds.express.api.dto.ExpressFilterSearch;
import com.danding.cds.express.api.dto.ExpressFilterSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @Author: Raymond
 * @Date: 2020/10/28 13:51
 * @Description:
 */
public interface ExpressFilterRpc {
    RpcResult<List<SelectItemVO>> list(Long id);

    RpcResult<ListVO<ExpressFilterDTO>> paging(ExpressFilterSearch search);

    RpcResult<Response<String>> delete(Long id) throws ArgsErrorException;

    RpcResult<Response<String>> upset(ExpressFilterSubmit submit);
}
