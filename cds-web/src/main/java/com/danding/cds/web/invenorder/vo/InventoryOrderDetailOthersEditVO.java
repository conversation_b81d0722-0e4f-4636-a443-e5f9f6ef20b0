package com.danding.cds.web.invenorder.vo;

import lombok.Data;

import java.util.Date;

/**
 * 编辑清关单详情其他信息
 */
@Data
public class InventoryOrderDetailOthersEditVO {

    private Long id;

    /**
     * 提单号
     */
    private String pickUpNo;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 起运港/始发机场
     */
    private String fromLocation;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;

    /**
     * 实际到港日期
     */
    private Date actualArrivalDate;
}
