package com.danding.cds.web.handler;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.OTMCItemExcelVo;
import com.danding.cds.web.checklist.rpc.ChecklistOrderRpc;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 一票多车表体导入
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_OTMC_ITEM", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E6%A0%B8%E6%94%BE%E5%8D%95%E8%A1%A8%E4%BD%93%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "一票多车表体导入", classes = OTMCItemExcelVo.class),})
public class OneTicketMultiCarItemImportsHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        ChecklistOrderRpc checklistOrderRpc = this.getBean(ChecklistOrderRpc.class);
        List<OTMCItemExcelVo> list = null;
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup g : groups) {
            list = g.getDataList(OTMCItemExcelVo.class);
        }
        log.info("一票多车表体导入 读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollectionUtils.isEmpty(list)) {
            log.info("一票多车表体导入 未读取到数据");
            return;
        }
        String checklistIdString = (String) extendMap.get("checklistId");
        Long checklistId = Long.valueOf(checklistIdString);
        String checklistSn = (String) extendMap.get("checklistSn");
        String endorsementIdString = (String) extendMap.get("endorsementId");
        Long endorsementId = Long.valueOf(endorsementIdString);
        log.info("checklistId={} checklistSn={} endorsementId={}", checklistId, checklistSn, endorsementId);
        if (Objects.isNull(checklistId) || Objects.isNull(checklistSn) || Objects.isNull(endorsementId)) {
            return;
        }
        int index = 1;
        for (OTMCItemExcelVo vo : list) {
            ImportResultResVo resultResVo = checklistOrderRpc.saveChecklistItemExport(checklistId, endorsementId, vo);
            log.info("核放单：{}，导入后的返回结果数据{}", checklistSn, resultResVo.toString());
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), vo);
            index++;
        }
    }
}
