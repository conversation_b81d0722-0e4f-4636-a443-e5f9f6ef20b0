package com.danding.cds.web.payChannel.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.payChannel.api.dto.PayChannelSearch;
import com.danding.cds.payChannel.api.dto.PayChannelSubmit;
import com.danding.cds.web.payChannel.vo.PayChannelSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface PayChannelRpc {
    RpcResult<List<SelectOptionVO<Long>>> list();

    RpcResult<ListVO<PayChannelSearchResult>> paging(PayChannelSearch search);

    RpcResult<Long> upset(PayChannelSubmit submit);
}
