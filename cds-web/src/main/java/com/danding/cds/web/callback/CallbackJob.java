package com.danding.cds.web.callback;

import com.alibaba.fastjson.JSON;
import com.danding.cds.callback.api.dto.CallbackRecordDTO;
import com.danding.cds.callback.api.dto.OrderActiveInfo;
import com.danding.cds.callback.api.enums.CallbackStatus;
import com.danding.cds.callback.api.enums.CallbackType;
import com.danding.cds.callback.api.service.CallbackRecordService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.order.api.dto.OrderCallbackSubmit;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.github.kevinsawicki.http.HttpRequest;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
@RefreshScope
public class CallbackJob extends IJobHandler {
    @DubboReference
    private CallbackRecordService callbackRecordService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private OrderService orderService;

    @Value("${pangu.host}")
    private String panguHost;

    @Override
    @ApiOperation(value = "执行回执")
    @GetMapping("/callback/execute")
    @XxlJob(value = "CallbackJob", enableTenant = false)
    public ReturnT<String> execute(String param) throws Exception {
        List<CallbackRecordDTO> recordDTOList = callbackRecordService.listByStatus(CallbackStatus.DEC_WAIT.getValue());
        for (CallbackRecordDTO dto : recordDTOList) {
            if (dto.getType().equals(CallbackType.ORDER.getCode())){
                ListOrderCallback callback = new ListOrderCallback();
                OrderActiveInfo activeInfo = JSON.parseObject(dto.getActiveData(),OrderActiveInfo.class);
                OrderDTO orderDTO = orderService.findByIdFull(activeInfo.getId());
                CustomsInventoryDTO inventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(),orderDTO.getCustomsInventorySn());
                callback.setMerchantOrderNo(inventoryDTO.getDeclareOrderNo());
                if (!StringUtils.isEmpty(activeInfo.getCustomsStatus())){
                    callback.setStatus(activeInfo.getCustomsStatus());
                    callback.setDescription(activeInfo.getCustomsDetail());
                    callback.setMessage(activeInfo.getCustomsDetail());
                    callback.setDatetime(new DateTime(activeInfo.getCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }else {
                    callback.setStatus(inventoryDTO.getCustomsStatus());
                    callback.setDescription(inventoryDTO.getCustomsDetail());
                    callback.setMessage(inventoryDTO.getCustomsDetail());
                    callback.setDatetime(new DateTime(inventoryDTO.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                callback.setType("customs");
                Map<String,String> extra = new HashMap<>();
                extra.put("inventoryNo",inventoryDTO.getInventoryNo());
                callback.setExtraInfo(JSON.toJSONString(extra));

                HttpRequest httpRequest = HttpRequest.post(panguHost + "/xhr/customs/list/declare/notify/receive")
                        .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                        .header("Content-Type", "application/json;charset=utf-8")
                        .send(JSON.toJSONString(callback));
                if (httpRequest.ok()){
                    callbackRecordService.updateStatusById(dto.getId(),CallbackStatus.DEC_ING.getValue());
                }else {
                    callbackRecordService.updateStatusById(dto.getId(),CallbackStatus.FAIL.getValue());
                }
                syncOrderCallback(activeInfo.getId(), callback);
            }
        }
        return ReturnT.SUCCESS;
    }

    private void syncOrderCallback(Long orderId, ListOrderCallback callback) {
        OrderCallbackSubmit submit = new OrderCallbackSubmit();
        submit.setOrderId(orderId);
        submit.setDatetime(callback.getDatetime());
//        submit.setStatus(callback.getStatus());
        submit.setDescription(callback.getDescription());
        orderService.syncOrderCallback(submit);
    }
}
