package com.danding.cds.web.invenorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class InventoryOrderInfoVO {
    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("清关单SN")
    private String inveCustomsSn;
    /**
     * 清关企业
     */
    @ApiModelProperty("清关企业")
    private Long inveCompanyId;

    @ApiModelProperty("业务类型")
    private String inveBusinessTypeDesc;
    private String inveBusinessType;

    @ApiModelProperty("上游业务单号")
    private String channelBusinessSn;
    /*
    单据类型
     */
    @ApiModelProperty("单据类型")
    private String inveOrderType;

    @ApiModelProperty("核放单编号")
    private String refCheckOrderNo;
    /**
     * 核注清单编号
     */
    @ApiModelProperty("核注清单编号")
    private String refHzInveNo;
    /**
     * 提取号
     */
    @ApiModelProperty("提取号")
    private String pickUpNo;
    /**
     * 租户
     */
    @ApiModelProperty("租户")
    private String rentPerson;

    /**
     * 账册ID
     */
    @ApiModelProperty("账册ID")
    private Long bookId;
    private String bookNo;
    /**
     * 申请人
     */
    @ApiModelProperty("申请人")
    private String applyPerson;
    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;
    /**
     * 清关单状态
     */
    @ApiModelProperty("清关单状态")
    private String status;

    /**
     * 状态描述
     */
    @ApiModelProperty("清关单状态描述")
    private String statusDesc;

    @ApiModelProperty("清关单状态")
    private String auditStatus;

    @ApiModelProperty("渠道来源")
    private Integer channel;

    //渠道来源描述
    private String channelDesc;

    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("清关企业名称")
    private String inveCompanyName;

    /**
     * 清关单状态对应时间
     */
    @ApiModelProperty("清关单状态对应时间")
    private Date statusTime;

    @ApiModelProperty("清关单状态完成时间")
    private Date completeTime;
    /**
     * 状态:0.停用;1.启用(默认)
     */
    @ApiModelProperty("状态:0.停用;1.启用(默认)")
    private Integer enable;
    @ApiModelProperty("创建时间")
    private Date createTime;
    @ApiModelProperty("更新时间")
    private Date updateTime;
    @ApiModelProperty("创建人ID")
    private Integer createBy;
    @ApiModelProperty("更新人ID")
    private Integer updateBy;
    @ApiModelProperty("逻辑删除")
    private Boolean deleted = false;

    @ApiModelProperty("主单号")
    private String masterOrderSn;

    @ApiModelProperty("子单号")
    private String subOrderSn;

    @ApiModelProperty("核放单列表")
    private List<CheckItemInfo> listCheckItemInfo;

    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;
    /**
     * 实体仓编码
     */
    private String entityWarehouseCode;

    @ApiModelProperty("货主名称")
    private String ownerName;
    /**
     * 货主编码
     */
    private String ownerCode;

    @ApiModelProperty("外部单号")
    private String upstreamNo;

    @ApiModelProperty("出入库单号")
    private String inOutOrderNo;


    @ApiModelProperty("操作人")
    private String operatorName;

    /**
     * 上游是否取消
     * 1.是
     * 0.否
     */
    private String upstreamCancel;

    /**
     * 核注清单编号
     */
    @ApiModelProperty("核注清单编号")
    private String endorsementRealOrderNo;

    /**
     * 关联草单附件名称
     */
    @ApiModelProperty("关联草单附件名称")
    private String draftListAttachmentName;

    /**
     * 关联草单附件链接
     */
    @ApiModelProperty("关联草单附件链接")
    private String draftListAttachmentUrl;

    @ApiModelProperty("清关单标记")
    private List<String> orderTagList;
    private List<Integer> orderTagCodeList;

    /**
     * 清关单待办标记
     */
    private List<String> orderTodoTagList;

    /**
     * 报关单附件链接
     */
    @ApiModelProperty("报关单附件链接")
    private String customsEntryAttachUrl;

    @ApiModelProperty("对应报关单号(区港联动时才有)")
    private String customsEntryNo;

    /**
     * 是否理货完成
     * 0-否 / 1-是
     */
    private String tallyCompleteDesc;

    /**
     * 清关回传状态
     */
    private String callbackStatusDesc;

    /**
     * 清关回执错误信息
     */
    private String callbackErrorMsg;

    /**
     * 是否开启中转 0:否 1:是
     */
    private Integer transitFlag;

    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     */
    private Integer twoStepFlag;

    /**
     * 质押货主标志
     */
    private Boolean pledgeOwnerFlag;

    /**
     * 邮箱地址
     */
    private String mailAddrList;

    /**
     * 邮件状态
     */
    private Integer mailStatus;
    private String mailStatusDesc;

    /**
     * 邮件驳回调用Erp接口状态
     * true 成功， false 失败
     */
    private Boolean callbackErpRejectOrderFlag;

    /**
     * 邮件驳回原因
     */
    private String mailRejectReason;


    /**
     * 货主是否自备车辆
     */
    private Integer selfOwnedVehicle;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;

    /**
     * 非保核放单流水号
     */
    private String fbChecklistSn;

    /**
     * 起运国
     */
    private String shipmentCountry;

    /**
     * 启运港（始发机场）
     */
    private String fromLocation;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 约车单号
     */
    private String ycNo;

    /**
     * 是否约车
     */
    private String isYcDesc;

    /**
     * 约车状态desc
     */
    private String ycStatusDesc;


    /**
     * 清关完成时间
     */
    private String inventoryCompleteTime;

    /**
     * 服务完成时间
     */
    private String inventoryFinishTime;

    /**
     * 加急处理
     */
    private Boolean urgentProcessFlag;
}
