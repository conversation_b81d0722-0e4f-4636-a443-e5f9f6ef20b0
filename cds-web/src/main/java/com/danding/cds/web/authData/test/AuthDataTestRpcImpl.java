package com.danding.cds.web.authData.test;

import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.client.UCenterClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @date 2021/4/6 11:26
 * @Description:
 */
@DubboService
public class AuthDataTestRpcImpl implements AuthDataTestRpc {

    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/authData/get", desc = "权限数据测试")
    @Override
    public RpcResult get() {
        // 当数据变更时，调用此方法，将重新推送数据到用户中心
        UCenterClient.dataClient().asynPushData("customsBook");
        // 此方法将获取当前用户所拥有的数据权限，应当与页面计算交集才可
        return null;
    }
}
