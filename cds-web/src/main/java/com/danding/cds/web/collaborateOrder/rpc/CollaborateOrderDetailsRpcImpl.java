package com.danding.cds.web.collaborateOrder.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.collaborateorder.api.dto.CollaborateLabelDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderResDetailsDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderResDetailsParam;
import com.danding.cds.collaborateorder.api.enums.DiffType;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderDetailsService;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderDetailsResVO;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.web.collaborateOrder.vo.CollaborateOrderDetailsVO;
import com.danding.cds.web.collaborateOrder.vo.InventoryOrderItemExtraJsonVO;
import com.danding.cds.web.invenorder.InventoryOrderController;
import com.danding.cds.web.invenorder.vo.InventoryOrderViewInfoVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/31
 */
@DubboService
public class CollaborateOrderDetailsRpcImpl implements CollaborateOrderDetailsRpc{

    @DubboReference
    private CollaborateOrderDetailsService detailsService;

    @Autowired
    private InventoryOrderController inventoryOrderController;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CollaborateOrderService orderService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private EndorsementService endorsementService;

    @Override
    @SoulClient(path = "/collaborateDetails/getByCollaborateId", desc = "查询协同单详情")
    public RpcResult getByCollaborateOrderId(CollaborateOrderResDetailsParam param) {
        CollaborateOrderDTO collaborateOrderDTO = orderService.selectByInveCustomsId(param.getCollaborateOrderId());
        List<CollaborateOrderDetailsResVO> detailsResVOS = detailsService.selectByCollaborateOrderId(Long.valueOf(param.getCollaborateOrderId()));
        List<Long> inveItemIds = detailsResVOS.stream().map(CollaborateOrderDetailsResVO::getInveItemId).collect(Collectors.toList());
        List<InventoryOrderItemDTO> inventoryOrderItemDTOS = inventoryOrderInfoService.listItemDTOByItemIdS(inveItemIds);
        Map<Long, InventoryOrderItemDTO> itemDTOMap = inventoryOrderItemDTOS.stream().collect(Collectors.toMap(InventoryOrderItemDTO::getId, Function.identity(), (v1, v2) -> v1));
        List<CollaborateOrderResDetailsDTO> detailsDTOS = new ArrayList<>();
        int actualTallyQty = 0;
        for (CollaborateOrderDetailsResVO detailsDTO : detailsResVOS) {
            CollaborateOrderResDetailsDTO resDetailsDTO = BeanUtil.copyProperties(detailsDTO, CollaborateOrderResDetailsDTO.class);
            Integer diff = detailsDTO.getDiffType();
            if (diff != null && !Objects.equals(diff, 0)) {
                resDetailsDTO.setDiffTypeDesc(DiffType.getEnum(detailsDTO.getDiffType()).getDesc());
            }
            Integer tallyQty = detailsDTO.getTallyQty();
            if (tallyQty != null) {
                actualTallyQty += tallyQty;
                resDetailsDTO.setTallyQty(tallyQty);
            }
            InventoryOrderItemDTO itemDTO = itemDTOMap.get(detailsDTO.getInveItemId());
            if (Objects.nonNull(itemDTO)) {
                resDetailsDTO.setCustomsRecordProductId(itemDTO.getProductId());
                CollaborateLabelDTO labelDTO = new CollaborateLabelDTO();
                labelDTO.setProductId(detailsDTO.getProductId());
                labelDTO.setTag(detailsDTO.getLabel());
                resDetailsDTO.setCollaborateLabel(labelDTO);
            }
            detailsDTOS.add(resDetailsDTO);
        }
        InventoryOrderInfoDTO infoDTO =inventoryOrderInfoService.findBySn(param.getInveCustomsSn());
        InventoryOrderViewInfoVO infoVO = inventoryOrderController.viewInventoryOrder(infoDTO.getId());
        //预计总净重
        BigDecimal estimatedNewWeight = BigDecimal.ZERO;
        //预计总毛重
        BigDecimal estimatedWeight = BigDecimal.ZERO;
        //预计到货数量
        BigDecimal estimatedQty = BigDecimal.ZERO;
        for (InventoryOrderItemDTO itemDTO: infoVO.getInventoryOrderItemDTOList()){
            InventoryOrderItemExtraJsonVO extraJsonVO = JSON.parseObject(itemDTO.getExtraJson(),InventoryOrderItemExtraJsonVO.class);
            BigDecimal planDeclareQty = itemDTO.getPlanDeclareQty();
            if (planDeclareQty == null) {
                continue;
            }
            if (Objects.nonNull(extraJsonVO.getGrossWeight())) {
                estimatedWeight =estimatedWeight.add(itemDTO.getPlanDeclareQty().multiply(extraJsonVO.getGrossWeight()));
            }
            if (Objects.nonNull(extraJsonVO.getNetweight())){
                estimatedNewWeight =estimatedNewWeight.add(itemDTO.getPlanDeclareQty().multiply(extraJsonVO.getNetweight()));
            }
            estimatedQty = estimatedQty.add(planDeclareQty);

        }
        CollaborateOrderDetailsVO detailsVO = BeanUtil.copyProperties(infoVO,CollaborateOrderDetailsVO.class);
        if (Objects.nonNull(infoDTO.getShipmentCountry())){
            CustomsCountryDTO customsCountryDTO = customsCountryService.findByCode(infoDTO.getShipmentCountry());
            detailsVO.setShipmentCountryName(customsCountryDTO.getName());
        }
        detailsVO.setCustomsFinishTime(collaborateOrderDTO.getInventoryFinishTime());
        detailsVO.setTransportModeName(infoVO.getTransportModeName());
        detailsVO.setActualTallyQty(actualTallyQty);
        detailsVO.setRefHzInveNo(infoVO.getRefHzInveNo());
        detailsVO.setEstimatedQty(estimatedQty);
        detailsVO.setEstimatedNewWeight(estimatedNewWeight);
        detailsVO.setEstimatedWeight(estimatedWeight);
        detailsVO.setDetailsVOS(detailsDTOS);
        detailsVO.setInOutOrderNo(infoDTO.getInOutOrderNo());
        detailsVO.setInveOrderId(infoDTO.getId().toString());
        detailsVO.setFightQty(collaborateOrderDTO.getFightQty());
        detailsVO.setShippingFee(collaborateOrderDTO.getShippingFee());
        detailsVO.setTallyOrderNo(collaborateOrderDTO.getTallyReportSn());
        detailsVO.setWarehouseTime(collaborateOrderDTO.getArrivalTime());
        detailsVO.setInOutOrderNo(infoVO.getInOutOrderNo());
        detailsVO.setAssociatedInveCustomsSn(collaborateOrderDTO.getAssociatedInveCustomsSn());
        if (Objects.nonNull(collaborateOrderDTO.getEntityInWarehouseName())) {
            detailsVO.setEntityInWarehouseName(collaborateOrderDTO.getEntityInWarehouseName());
        }
        if (Objects.nonNull(collaborateOrderDTO.getEntityOutWarehouseName())) {
            detailsVO.setEntityOutWarehouseName(collaborateOrderDTO.getEntityOutWarehouseName());
        }

        if(Objects.nonNull(infoDTO.getInveCompanyId())){
            CompanyDTO companyDTO =  companyService.findById(infoDTO.getInveCompanyId());
            detailsVO.setCustomsEntryCompany(companyDTO.getId());
            detailsVO.setCustomsEntryCompanyName(companyDTO.getName());
        }
        if (Objects.nonNull(collaborateOrderDTO.getTallyFinishTime())) {
            detailsVO.setTallyFinishTime(DateUtil.formatDateTime(collaborateOrderDTO.getTallyFinishTime()));
        }
        if (Objects.nonNull(infoDTO.getExpectedToPortTime())) {
            detailsVO.setExpectedToPortTime(DateUtil.formatDateTime(infoDTO.getExpectedToPortTime()));
        }
        if (Objects.nonNull(infoDTO.getActualArrivalDate())) {
            detailsVO.setActualArrivalDate(DateUtil.formatDateTime(infoDTO.getActualArrivalDate()));
        }
        if (Objects.nonNull(infoDTO.getInveBusinessType()) && Objects.equals(infoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode()) && Objects.isNull(infoDTO.getTwoStepFlag())) {
            return RpcResult.error("无法确定是否两步申报");
        }
        if (Objects.nonNull(infoDTO.getOwnerName())) {
            detailsVO.setOwnerName(infoDTO.getOwnerName());
        }
        if (Objects.nonNull(infoDTO.getEntityWarehouseName())) {
            detailsVO.setEntityWarehouseName(infoDTO.getEntityWarehouseName());
        }


        detailsVO.setStateRes(detailsService.getFlowState(infoDTO, param.getCollaborateOrderId()));
        return RpcResult.success(detailsVO);
    }


    @Override
    @SoulClient(path = "/collaborateDetails/updateDeclareQtyByInveSn",desc = "修改")
    public RpcResult updateDeclareQtyByInveSn(CollaborateOrderResDetailsParam param){
//        detailsService.updateByGrossWeightAndGoodsSeqNo(param.getInveCustomsSn());
        return RpcResult.success(true);
    }

}
