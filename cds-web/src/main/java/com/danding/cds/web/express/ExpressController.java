package com.danding.cds.web.express;

import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.dto.ExpressSubmit;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.web.express.vo.ExpressSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 10:51
 * @Description:
 */
@Api(tags = "快递方式管理")
@RestController
@RequestMapping("/express")
public class ExpressController {

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CompanyService companyService;

    @GetMapping("/list")
    @ApiOperation(value = "获取所有的快递下拉（包括，禁用，启用）")
    public List<SelectItemVO> list(){
        List<ExpressDTO> dataList = expressService.listAll();
        List<SelectItemVO> result = dataList.stream().map((ExpressDTO item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getId());
            optionDTO.setName(item.getName());
            return optionDTO;
        }).collect(Collectors.toList());
        return result;
    }

    @GetMapping("/listEnable")
    @ApiOperation(value = "获取已启用快递下拉")
    public List<SelectItemVO> listEnable(){
        List<ExpressDTO> dataList = expressService.listEnable();
        List<SelectItemVO> result = dataList.stream().map((ExpressDTO item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getId());
            optionDTO.setName(item.getName());
            return optionDTO;
        }).collect(Collectors.toList());
        return result;
    }


    @GetMapping("/paging")
    @ApiOperation(value = "分页查询")
    public ListVO<ExpressSearchResult> paging(ExpressSearch search){
        ListVO<ExpressDTO> paging = expressService.paging(search);
        ListVO<ExpressSearchResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((ExpressDTO item)->{
            ExpressSearchResult vo = new ExpressSearchResult();
            BeanUtils.copyProperties(item,vo);
            CompanyDTO companyDTO = companyService.findById(item.getExpressCompanyId());
            vo.setExpressCompanyName(companyDTO.getName());
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }


    @ApiOperation(value = "新增|更新快递方式")
    @PostMapping("/upset")
    public Long upset(@RequestBody ExpressSubmit submit) throws ArgsErrorException {
        return expressService.upset(submit);
    }

}
