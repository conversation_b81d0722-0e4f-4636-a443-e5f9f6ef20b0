package com.danding.cds.web.customs.rpc;

import java.util.ArrayList;
import java.util.List;

import javax.validation.constraints.NotNull;

import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import com.danding.cds.common.annotation.ParamValidator;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.customs.manager.api.service.CustomsDistrictManagerService;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictAddParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictEnableParam;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictSearch;
import com.danding.cds.customs.manager.api.vo.CustomsDistrictUpdateParam;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;

import lombok.extern.slf4j.Slf4j;

/**
 * @menu 口岸管理
 * @Author: yousx
 * @Date: 2023/11/23
 * @Description:
 */
@Slf4j
@DubboService
public class CustomsManagerRpcImpl implements CustomsManagerRpc {

    @DubboReference
    private CustomsDistrictManagerService customsDistrictManagerService;

    /**
     * 分页查询
     * @param search
     * @return
     * @path /customs/manager/paging
     */
    @Override
    @SoulClient(path = "/customs/manager/paging", desc = "口岸管理-分页查询")
    @ParamValidator
    public RpcResult<ListVO<CustomsDistrictDTO>> paging(CustomsDistrictSearch search) {
        ListVO<CustomsDistrictDTO> paging = customsDistrictManagerService.paging(search);
        return RpcResult.success(paging);
    }

    /**
     * 新增
     * @param param
     * @return
     * @path /customs/manager/insert
     */
    @Override
    @SoulClient(path = "/customs/manager/insert", desc = "口岸管理-新增")
    @ParamValidator
    public RpcResult<String> insert(CustomsDistrictAddParam param) {
        customsDistrictManagerService.insert(param);
        return RpcResult.success("新增成功");
    }

    /**
     * 修改
     * @param param
     * @return
     * @path /customs/manager/update
     */
    @Override
    @SoulClient(path = "/customs/manager/update", desc = "口岸管理-修改")
    @ParamValidator
    public RpcResult<String> update(CustomsDistrictUpdateParam param) {
        customsDistrictManagerService.update(param);
        return RpcResult.success("修改成功");
    }

    /**
     * 删除
     * @param id
     * @return
     * @path /customs/manager/delete
     */
    @Override
    @SoulClient(path = "/customs/manager/delete", desc = "口岸管理-删除")
    @ParamValidator
    public RpcResult<String> delete(@NotNull Long id) {
        customsDistrictManagerService.delete(id);
        return RpcResult.success("删除成功");
    }

    /**
     * 启用停用
     * @param param
     * @return
     * @path /customs/manager/enableSwitch
     */
    @Override
    @SoulClient(path = "/customs/manager/enableSwitch", desc = "口岸管理-启用停用")
    @ParamValidator
    public RpcResult<String> enableSwitch(CustomsDistrictEnableParam param) {
        customsDistrictManagerService.enableSwitch(param);
        return RpcResult.success();
    }


    /**
     * 名称下拉数据
     * @param
     * @return
     * @path /customs/manager/nameSelect
     */
    @Override
    @SoulClient(path = "/customs/manager/nameSelect", desc = "口岸管理-名称下拉数据")
    public RpcResult<List<SelectOptionVO<String>>> nameSelect() {
        List<SelectOptionVO<String>> res = new ArrayList<>();
        List<CustomsDistrictDTO> list =customsDistrictManagerService.list();
        for (CustomsDistrictDTO customsDistrictDTO : list) {
            res.add(new SelectOptionVO<>(customsDistrictDTO.getName(), customsDistrictDTO.getName()));
        }
        return RpcResult.success(res);
    }
}
