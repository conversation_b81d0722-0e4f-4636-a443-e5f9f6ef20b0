package com.danding.cds.web.exportorder.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("查看运单实体")
public class ExportItemSimpleVO {

    @ApiModelProperty("包裹数量")
    private Integer packageCount;

    @ApiModelProperty("SKU数量")
    private Integer skuCount;

    @ApiModelProperty("包裹毛重（kg）")
    private BigDecimal packageWeight;

    @ApiModelProperty("工作台号")
    private String stationNo;

    @ApiModelProperty("包裹列表")
    private List<ExportItemGroup> groupList;
}
