package com.danding.cds.web.invenorder.vo;

import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class InventoryOrderRelationEditPre {

    @ApiModelProperty("核注清关单ID")
    private Long id;
    @ApiModelProperty("关联单证")
    private List<InventoryOrderRelationDTO> recordList;
}
