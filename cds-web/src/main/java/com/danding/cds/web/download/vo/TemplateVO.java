package com.danding.cds.web.download.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class TemplateVO {

    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("上传时间")
    private String createTime;

    @ApiModelProperty("下载路径")
    private String filePath;

    @ApiModelProperty("下载次数")
    private Integer downloadCount;

    @ApiModelProperty("备注")
    private String remark;

}
