package com.danding.cds.web.checklist.controller;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.c.api.bean.enums.OrderItemTagEnum;
import com.danding.cds.checklist.api.dto.ChecklistDTO;
import com.danding.cds.checklist.api.enums.ChecklistBindType;
import com.danding.cds.checklist.api.enums.ChecklistType;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.enums.InventoryCustomsFlagEnum;
import com.danding.cds.common.enums.InventoryCustomsTypeEnums;
import com.danding.cds.common.enums.InventoryDeclarationEnum;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.EndorsementExcelVO;
import com.danding.cds.download.api.vo.EndorsementOrderExcelVO;
import com.danding.cds.endorsement.api.dto.*;
import com.danding.cds.endorsement.api.enums.*;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.*;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.order.api.dto.SingleInvtOrderSearch;
import com.danding.cds.v2.api.ChecklistAuthService;
import com.danding.cds.v2.bean.dto.ChecklistAuthDTO;
import com.danding.cds.v2.bean.dto.EndorsementExtraDTO;
import com.danding.cds.v2.bean.dto.InventoryOrderItemGoodsDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvAddParam;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvDeleteParam;
import com.danding.cds.v2.bean.vo.res.EndorsementEbInvEditResVO;
import com.danding.cds.v2.enums.ChecklistAuthCancelStatusEnum;
import com.danding.cds.v2.enums.EndorsementOrderTypeEnums;
import com.danding.cds.v2.enums.InventoryItemVerifyStrategy;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.cds.web.checklist.vo.*;
import com.danding.cds.web.exportorder.vo.ExportItemEditPre;
import com.danding.cds.web.exportorder.vo.ExportItemEditSubmit;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditPre;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditSubmit;
import com.danding.cds.web.item.vo.EndorsementEbInvExcelVO;
import com.danding.cds.web.item.vo.EndorsementImportSubmit;
import com.danding.cds.web.item.vo.EndorsementItemExcelVO;
import com.danding.cds.web.user.UserService;
import com.danding.component.common.rpc.common.utils.BeanUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.BusinessErrorCode;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.base.Splitter;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.io.*;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Slf4j
@RestController
@Api(tags = "核注管理")
@RequestMapping("/endorsement")
@RefreshScope
public class EndorsementController {

    /**
     * 清单不存在或不是放行状态
     */
    private static final Pattern INVENTORY_NO_EXIST_PATTERN = Pattern.compile("清单编号\\[[a-zA-Z0-9]+\\]不存在或清单不是放行状态");
    /**
     * 改hs
     */
    private static final Pattern CHANGE_HS_PATTERN = Pattern.compile("核注清单\\[[a-zA-Z0-9]+\\]中备案序号为\\[[a-zA-Z0-9]+\\]的商品编码\\[[a-zA-Z0-9]+\\]与对应跨境清单商品或退货申请单商品编码\\[[a-zA-Z0-9]+\\]不一致，核对检查失败");

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private ChecklistService checklistService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;


    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private RefundOrderService refundOrderService;

    @Autowired
    private UserService userService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private ChecklistAuthService checklistAuthService;

    @Autowired
    private FourCategoryGoodsService fourCategoryGoodsService;

    @DubboReference
    private ProcessTradeBookService processTradeBookService;

    @Autowired
    private Validator validator;

    @Value("${web.endorsement.wechatUrl}")
    private String wechatUrl;

    @Value("${web.endorsement.fliterDataList:}")
    private String fliterDataList;

    @Value("${endorsement.remove.inventory.by.es:false}")
    private Boolean inventoryByEs;

    @ApiOperation(value = "根据关联单据生成核注单")
    @PostMapping("/createByChecklist")
    public Response<String> createByChecklist(@RequestBody EndorsementSubmit submit) {
        try {
            IEType ieType = EndorsementBussiness.getIEType(submit.getBussinessType());
            if (ieType == null) {
                return new Response<>(-1, "提交的业务参数不正确");
            }
            submit.setIeFlag(ieType.getValue());
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(submit.getInventoryOrderId());
            //如果没传是否核扣库存 就默认给是
            if (Objects.isNull(submit.getStockChangeEnable())) {
                submit.setStockChangeEnable(1);
            }
            if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(submit.getBussinessType())) {
                submit.setChecklistsFlag(false);
//                List<ExportItemDTO> list = exportOrderService.listItemById(submit.getExportOrderId());
//                if (list == null) {
//                    list = new ArrayList<>();
//                }
//                if (org.apache.commons.collections4.CollectionUtils.isEmpty(list)) {
//                    return new Response<>(-1, "该申报出库单未导入运单信息");
//                }
            } else {
                String businessType = inventoryOrderInfoDTO.getInveBusinessType();
                if (businessType == null) businessType = "";
                if (!businessType.equalsIgnoreCase(submit.getBussinessType())) {
                    return new Response<>(-1, "清单业务类型跟清关单业务类型不匹配");
                }
                List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderId(submit.getInventoryOrderId());
                if (itemDTOS == null) itemDTOS = new ArrayList<>();
                //判断是否触发危化品标志
                if (Objects.equals(businessType, InventoryOrderBusinessEnum.BUSINESS_INVENTORY_PROFIT.getCode()) ||
                        Objects.equals(businessType, InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode())) {
                    for (InventoryOrderItemDTO inventoryOrderItem : itemDTOS) {
                        if (Objects.equals(inventoryOrderItem.getDangerousFlag(), "0") || Objects.equals(inventoryOrderItem.getDangerousFlag(), "1")) {
                            return new Response<>(-1, "统一料号:" + inventoryOrderItem.getProductId() + "的危化品标志保存错误，请检查！");
                        }
                    }
                }

                // 淘天跳过 清关单存在未关联备案的表体 校验
                if (!Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.TAO_TIAN.getValue())) {
                    if (itemDTOS.stream()
                            .anyMatch(inventoryOrderItemDTO -> StringUtil.isBlank(inventoryOrderItemDTO.getOriginProductId()))) {
                        return new Response<>(-1, "清关单存在未关联备案的表体");
                    }
                }
                if (new Integer(itemDTOS.size()).equals(1) && submit.getChecklistsFlag()) {
                    return new Response<>(-1, "清关单物料只有一条,不允许开启一票多车的选项");
                }
                if (Objects.equals(inventoryOrderInfoDTO.getPledgeOwnerFlag(), true)
                        && !Objects.equals(inventoryOrderInfoDTO.getMailStatus(), InventoryOrderMailStatusEnum.PASS.getCode())) {
                    return new Response<>(-1, "此清关单需经过审核方通过，当前邮件未发送/未通过，无法创建核注单");
                }
                List<String> productIdList = itemDTOS.stream().filter(i -> CollUtil.isNotEmpty(i.getVerifyResultList())
                                && i.getVerifyResultList().stream()
                                .anyMatch(v -> v.getCode().equals(InventoryItemVerifyStrategy.DIFF_DECLARE_UNIT.getCode())))
                        .map(InventoryOrderItemDTO::getProductId).collect(Collectors.toList());
                if (CollUtil.isNotEmpty(productIdList)) {
                    return new Response<>(-1,
                            "清关单中存在申报单位不一致的表体料号【" + JSON.toJSONString(productIdList) + "】，请检查后重新添加或重新生成表体");
                }
                Long bookId = inventoryOrderInfoDTO.getBookId();
                CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(bookId);
                Integer bookType = customsBookResVo.getBookType();
                if (Objects.equals(InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode(), businessType)
                        && Objects.equals(bookType, CustomsBookTypeEnums.IMPORT_BONDED_BOOKS.getCode())) {
                    for (InventoryOrderItemDTO i : itemDTOS) {
                        List<InventoryVerifyResult> verifyResultList = i.getVerifyResultList();
                        if (!CollectionUtils.isEmpty(verifyResultList)) {
                            boolean fourCategoryGoods = verifyResultList.stream().anyMatch(v -> Objects.equals(v.getCode(), InventoryItemVerifyStrategy.FOUR_CATEGORY_GOODS.getCode()));
                            if (fourCategoryGoods) {
                                boolean isOuterKeyMaterial = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(i.getOriginCountry(), i.getHsCode());
                                if (isOuterKeyMaterial) {
                                    return new Response<>(-1, "一线普通账册无法申报四类措施商品");
                                }
                            }
                        }
                    }
                }
                try {
                    inventoryOrderInfoService.endorsementGenerateTallyReportCheck(inventoryOrderInfoDTO);
                } catch (ArgsInvalidException e) {
                    return new Response<>(-1, e.getErrorMessage());
                }
                if (inventoryOrderInfoDTO.getMasterOrderSn() != null) {
                    return new Response<>(-1, "清关单已合单,不允许创建核注单");
                }
                if (StringUtil.isNotEmpty(inventoryOrderInfoDTO.getSubOrderSn())) {
                    //校验子单是否接受理货报告
                    List<String> subSnList = Splitter.on(",").splitToList(inventoryOrderInfoDTO.getSubOrderSn());
                    List<InventoryOrderInfoDTO> subOrderList = inventoryOrderInfoService.findBySn(subSnList);
                    for (InventoryOrderInfoDTO infoDTO : subOrderList) {
                        try {
                            inventoryOrderInfoService.endorsementGenerateTallyReportCheck(infoDTO);
                        } catch (ArgsInvalidException e) {
                            return new Response<>(-1, e.getErrorMessage());
                        }
                    }
                }
            }
            Long id = UserUtils.getUserId().longValue();
            List<String> snList = endorsementService.generate(submit, id);
            // 在 endorsementService.generate 中已处理
//            if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(submit.getBussinessType())) {
//                InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findById(submit.getInventoryOrderId());
//                orderInfoDTO.setStatus(InventoryOrderEnum.STATUS_ENDORSEMENT.getCode());
//                if (Objects.equals(submit.getStockChangeEnable(), 0)) {
//                    //若不核扣库存， 清关单状态为服务完成, 如果是关仓的会回传给 erp
//                    orderInfoDTO.setStatus(InventoryOrderEnum.STATUS_FINISH.getCode());
//                    inventoryOrderInfoService.manualCallBackErp(orderInfoDTO, InventoryOrderEnum.STATUS_FINISH.getCode());
//                }
//                orderInfoDTO.setEndorsementSn(snList.get(0));
//                inventoryOrderInfoService.updateInventoryOrderInfoDTO(orderInfoDTO, true);
//                if (Objects.equals(orderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode())) {
//                    refundOrderService.updateCustomsStatusByInveSn(inventoryOrderInfoDTO.getInveCustomsSn(), orderInfoDTO.getStatus());
//                }
//            }
        } catch (ArgsErrorException ex) {
            return new Response(-1, ex.getErrorMessage());
        } catch (ArgsInvalidException ex) {
            return new Response<>(-1, ex.getErrorMessage());
        }
        return new Response<>("创建成功");
    }

    @GetMapping("/load-detail-info")
    public EndorsementDetailWarp loadDetailInfo(Long endorsementId) {
        EndorsementDTO endorsementOrderDTO = endorsementService.findById(endorsementId);
        EndorsementDetailWarp endorsementDetailWarp = new EndorsementDetailWarp();
        endorsementDetailWarp.setItemList(new ArrayList<ExportItemDTO>());
        endorsementDetailWarp.setBodyItemInfos(new ArrayList<BodyItemInfo>());
        List<BodyItemInfo> bodyItemInfos = new ArrayList<BodyItemInfo>();

        endorsementDetailWarp.setBussinessType(endorsementOrderDTO.getBussinessType());
        endorsementDetailWarp.setBussinessTypeDesc(EndorsementBussiness.getEnum(endorsementOrderDTO.getBussinessType()).getDesc());
        if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())) {
            log.info("[op:EndorsementController] time-watch,step{}={}", 1, new DateTime().toString("yy-MM-dd HH:mm:ss SSS"));
            List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(endorsementOrderDTO.getId());
            endorsementDetailWarp.getItemList().addAll(itemDTOList);
//            List<String> sns = itemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
//            List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.listBySnsSection(sns);
//            Map<Long, List<CustomsInventoryItemDTO>> itemMap = customsInventoryService.listItemByInventoryS(inventoryDTOList);
            /*for (Map.Entry<Long, List<CustomsInventoryItemDTO>> entry : itemMap.entrySet()) {
                for (CustomsInventoryItemDTO itemDTO : entry.getValue()) {
                    CustomsInventoryItemExtra extra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                    BodyItemInfo bodyItemInfo = new BodyItemInfo();
                    bodyItemInfo.setProductId(extra.getProductId());
                    bodyItemInfo.setAccountSeqNo(StringUtils.isEmpty(extra.getGoodsSeqNo())?"无":extra.getGoodsSeqNo());
                    bodyItemInfos.add(bodyItemInfo);
                }
            }*/
            //找出异常料号
            List<Integer> errorItems = new ArrayList<>();
            if (EndorsementOrderStatus.EXCEPTION.getCode().equals(endorsementOrderDTO.getStatus())) {
                errorItems = getErrorItemList(endorsementOrderDTO.getInformationDesc());
            }
            List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.preItemView(endorsementId);
            int i = 1;
            for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                BodyItemInfo bodyItemInfo = new BodyItemInfo();
                bodyItemInfo.setProductId(endorsementItemDTO.getProductId());
                bodyItemInfo.setAccountSeqNo(StringUtils.isEmpty(endorsementItemDTO.getGoodsSeqNo()) ? "无" : endorsementItemDTO.getGoodsSeqNo());
                bodyItemInfo.setDeclareQty(endorsementItemDTO.getDeclareUnitQfy().longValue());
                if (!CollectionUtils.isEmpty(errorItems)) {
                    bodyItemInfo.setException(errorItems.contains(i));
                }
                bodyItemInfos.add(bodyItemInfo);
                i++;
            }
            log.info("[op:EndorsementController] time-watch,step{}={}", 4, new DateTime().toString("yy-MM-dd HH:mm:ss SSS"));
            endorsementDetailWarp.getBodyItemInfos().addAll(bodyItemInfos);
        } else {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(endorsementOrderDTO.getInventoryOrderId());
            List<InventoryOrderItemDTO> list = inventoryOrderInfoService.findListByInvenOrderIdAndEndorsementId(endorsementOrderDTO.getInventoryOrderId(), endorsementOrderDTO.getId());
            if (list == null) list = new ArrayList<InventoryOrderItemDTO>();
            endorsementDetailWarp.getBodyItemInfos().addAll(list.stream().map(s -> {
                BodyItemInfo bodyItemInfo = new BodyItemInfo();
                bodyItemInfo.setAccountSeqNo(StringUtils.isEmpty(s.getGoodsSeqNo()) ? "无" : s.getGoodsSeqNo());
                bodyItemInfo.setDeclareQty(s.getDeclareUnitQfy().longValue());
                bodyItemInfo.setProductId(s.getProductId());
                return bodyItemInfo;
            }).collect(Collectors.toList()));
            if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType()) &&
                    !StringUtils.isEmpty(endorsementOrderDTO.getBussinessType())) {
                if (inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())) {
                    List<InventoryOrderRelationDTO> listInventoryOrderRelationDTO = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(endorsementOrderDTO.getInventoryOrderId());
                    if (!org.apache.commons.collections4.CollectionUtils.isEmpty(listInventoryOrderRelationDTO)) {
                        // 一线入境
                        if (inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode())) {
                            if (listInventoryOrderRelationDTO == null) {
                                listInventoryOrderRelationDTO = new ArrayList<>();
                            }
                            listInventoryOrderRelationDTO = listInventoryOrderRelationDTO.stream().filter(s -> s.getRelType().equalsIgnoreCase(InventoryOrderRelationEnum.REL_TYPE_DECLARE.getCode())).collect(Collectors.toList());
                            endorsementDetailWarp.setListInventoryOrderRelationDTO(listInventoryOrderRelationDTO);
                        } else if (inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode())) {
                            // 退货入区
                            if (listInventoryOrderRelationDTO == null) {
                                listInventoryOrderRelationDTO = new ArrayList<InventoryOrderRelationDTO>();
                            }
                            listInventoryOrderRelationDTO = listInventoryOrderRelationDTO.stream().filter(s -> s.getRelType().equalsIgnoreCase(InventoryOrderRelationEnum.REL_TYPE_YUNDAN.getCode())).collect(Collectors.toList());
                            endorsementDetailWarp.setListInventoryOrderRelationDTO(listInventoryOrderRelationDTO);
                        }//区内流转(出),区内流转(入),区间流转(出),区间流转(入)
                        else if (inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode()) ||
                                inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode()) ||
                                inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode()) ||
                                inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode())) {
                        }
                    }
                }
            }
        }
        return endorsementDetailWarp;
    }

    /**
     * 分组查清单表体
     *
     * @param inventoryDTOS
     * @return
     */
    public Map<Long, List<CustomsInventoryItemDTO>> listItemByInventorySGroup(List<CustomsInventoryDTO> inventoryDTOS) {
        Map<Long, List<CustomsInventoryItemDTO>> result = new HashMap<>();
        //步长
        int step = 2000;
        for (int start = 0; start < inventoryDTOS.size(); start += step) {
            int end = start + step;
            if (end > inventoryDTOS.size()) {
                end = inventoryDTOS.size();
            }
            List<CustomsInventoryDTO> customsInventoryDTOGroup = inventoryDTOS.subList(start, end);
            result.putAll(customsInventoryService.listItemByInventoryS(customsInventoryDTOGroup));
        }
        return result;
    }

    private List<Integer> getErrorItemList(String errMsg) {
        List<String> errMsgList = JSONObject.parseArray(errMsg, String.class);
        List<Integer> result = new ArrayList<>();
        String strStart = "{";
        String strEnd = "}";
        for (String demo : errMsgList) {
            if (demo.contains("[8987]") && demo.contains("超过底账允许数量")) {
                int strStartIndex = demo.indexOf(strStart);
                int strEndIndex = demo.indexOf(strEnd);
                /* index 为负数 即表示该字符串中 没有该字符 */
                if (strStartIndex < 0) {
                    continue;
                }
                if (strEndIndex < 0) {
                    continue;
                }
                /* 开始截取 */
                String no = demo.substring(strStartIndex, strEndIndex).substring(strStart.length());
                result.add(Integer.valueOf(no));
            }
        }
        return result;
    }

    @ApiOperation(value = "获取所有核注业务类型")
    @GetMapping("/list-bussiness-types")
    public List<SelectItemVO> listBussinessTypes() {
        List<SelectItemVO> result = Arrays.stream(EndorsementBussiness.values())
                .filter((EndorsementBussiness item) -> !item.equals(EndorsementBussiness.BUSSINESS_EMPTY))
                .map((EndorsementBussiness item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取所有核注状态")
    @GetMapping("/listStatus")
    public List<SelectItemVO> listQualify() {
        List<SelectItemVO> result = Arrays.stream(EndorsementOrderStatus.values())
                .filter((EndorsementOrderStatus item) -> !item.equals(EndorsementOrderStatus.NULL))
                .map((EndorsementOrderStatus item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public Response<ListVO<EndorsementSearchResult>> paging(EndorsementSearch search) {
        ListVO<EndorsementDTOV2> paging = endorsementService.paging(search);
        List<Long> endorsementIdList = paging.getDataList().stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        Map<Long, List<ChecklistDTO>> checklistDTOMap = checklistService.getChecklistByEndorsementList(endorsementIdList);
        List<Long> userIdList = paging.getDataList().stream().map(p -> p.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(userIdList);
        ListVO<EndorsementSearchResult> result = new ListVO<>();
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        Map<Long, CustomsBookDTO> customsBookDTOMap = new HashMap<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((EndorsementDTOV2 endorsementOrderDTO) -> {

            EndorsementSearchResult data = new EndorsementSearchResult();
            data.setId(endorsementOrderDTO.getId());
            CustomsBookDTO customsBook = customsBookDTOMap.get(endorsementOrderDTO.getAccountBookId());
            if (customsBook == null) {
                customsBook = customsBookService.findById(endorsementOrderDTO.getAccountBookId());
                if (Objects.nonNull(customsBook)) {
                    customsBookDTOMap.put(customsBook.getId(), customsBook);
                } else {
                    log.warn("customsBookId={} 在账册表中不存在", endorsementOrderDTO.getId());
                }
            }
            if (Objects.nonNull(customsBook)) {
                data.setCustomsBookNo(customsBook.getBookNo());
            }
            CompanyDTO declareCompany = companyDTOMap.get(endorsementOrderDTO.getDeclareCompanyId());
            if (declareCompany == null) {
                declareCompany = companyService.findById(endorsementOrderDTO.getDeclareCompanyId());
                companyDTOMap.put(declareCompany.getId(), declareCompany);
            }
            if (endorsementOrderDTO.getCreateBy() != null) {
                if (userRpcResultMap.containsKey(endorsementOrderDTO.getCreateBy().longValue())) {
                    data.setCreateBy(userRpcResultMap.get(endorsementOrderDTO.getCreateBy().longValue()).getUserName());
                }
            }
            data.setDeclareCompanyName(declareCompany.getName());
//            List<ChecklistDTO> checklistDTOList = checklistService.checklistByEndorsementId(endorsementOrderDTO.getId());
//            List<EndorsementSearchResult.ChecklistOrderNo> checklistOrderNos = checklistDTOList.stream().filter(c -> Objects.nonNull(c.getPreOrderNo())).map(c -> new EndorsementSearchResult.ChecklistOrderNo(c.getPreOrderNo())).collect(Collectors.toList());
//            data.setChecklistOrderNo(checklistOrderNos);
            List<ChecklistDTO> checklistDTOS = checklistDTOMap.get(endorsementOrderDTO.getId());
            if (!CollectionUtils.isEmpty(checklistDTOS)) {
                List<String> checkListSnList = checklistDTOS.stream().map(ChecklistDTO::getSn).collect(Collectors.toList());
                data.setChecklistSnList(checkListSnList);
            }
            data.setSn(endorsementOrderDTO.getSn());
            data.setEndorsementOrderNo(endorsementOrderDTO.getPreOrderNo());
            data.setRealEndorsementOrderNo(endorsementOrderDTO.getRealOrderNo());
            data.setCreateAt(new DateTime(endorsementOrderDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            if (endorsementOrderDTO.getFinishTime() != null) {
                data.setFinishAt(new DateTime(endorsementOrderDTO.getFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            data.setStatusDesc(EndorsementOrderStatus.getEnum(endorsementOrderDTO.getStatus()).getDesc());
            String customsStatusDesc = EndorsementCustomsStatus.getEnum(endorsementOrderDTO.getCustomsStatus()).getDesc();
            if ("空".equalsIgnoreCase(customsStatusDesc)) {
                customsStatusDesc = "";
            }
            data.setCustomsStatusDesc(customsStatusDesc);
            data.setInformationDesc(endorsementOrderDTO.getInformationDesc());
            data.setWaitFinishFlag(endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode()));
            data.setIeType(endorsementOrderDTO.getIeFlag());
            data.setBussinessTypeDesc(EndorsementBussiness.getEnum(endorsementOrderDTO.getBussinessType()).getDesc());
            data.setStatus(endorsementOrderDTO.getStatus());
            data.setRemark(endorsementOrderDTO.getRemark());
            // 显示出库单号
            Long exportOrderId = endorsementOrderDTO.getExportOrderId();
            if (exportOrderId != null) {
                ExportOrderDTO exportOrderDTO = exportOrderService.findById(exportOrderId);
                if (Objects.nonNull(exportOrderDTO)) {
                    data.setExportOrderSn(exportOrderDTO.getSn());
                }
            }
            // 显示清关单号
            Long inventoryOrderId = endorsementOrderDTO.getInventoryOrderId();
            if (inventoryOrderId != null) {
                InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(endorsementOrderDTO.getInventoryOrderId());
                if (Objects.nonNull(infoDTO)) {
                    data.setInvOrderSn(infoDTO.getInveCustomsSn());
                }
            }
            data.setStockChangeEnableDesc(Boolean.TRUE.equals(endorsementOrderDTO.getStockChangeEnable()) ? "是" : "否");
            data.setUpdateTime(endorsementOrderDTO.getUpdateTime());
            data.setOrderType(endorsementOrderDTO.getOrderType());
            data.setOrderTypeDesc(EndorsementOrderTypeEnums.getDesc(endorsementOrderDTO.getOrderType()));
            /*
            EndorsementDetailWarp endorsementDetailWarp = new EndorsementDetailWarp();
            endorsementDetailWarp.setItemList(new ArrayList<ExportItemDTO>());
            endorsementDetailWarp.setBodyItemInfos(new ArrayList<BodyItemInfo>());
            List<BodyItemInfo> bodyItemInfos = new ArrayList<BodyItemInfo>();
            data.setEndorsementDetailWarp(endorsementDetailWarp);
            endorsementDetailWarp.setBussinessType(endorsementOrderDTO.getBussinessType());
            endorsementDetailWarp.setBussinessTypeDesc(EndorsementBussiness.getEnum(endorsementOrderDTO.getBussinessType()).getDesc());
            if(EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())){
                log.info("[op:EndorsementController] time-watch,step{}={}", 1, new DateTime().toString("yy-MM-dd HH:mm:ss SSS"));
                List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(endorsementOrderDTO.getId());
                endorsementDetailWarp.getItemList().addAll(itemDTOList);
                List<String> sns = itemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
                List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.listBySnsSection(sns);
                Map<Long,List<CustomsInventoryItemDTO>> itemMap = customsInventoryService.listItemByInventoryS(inventoryDTOList);
                for (Map.Entry<Long, List<CustomsInventoryItemDTO>> entry : itemMap.entrySet()) {
                    for (CustomsInventoryItemDTO itemDTO : entry.getValue()) {
                        CustomsInventoryItemExtra extra = JSON.parseObject(itemDTO.getExtraJson(), CustomsInventoryItemExtra.class);
                        BodyItemInfo bodyItemInfo = new BodyItemInfo();
                        bodyItemInfo.setProductId(extra.getProductId());
                        bodyItemInfo.setAccountSeqNo(StringUtils.isEmpty(extra.getGoodsSeqNo())?"无":extra.getGoodsSeqNo());
                        bodyItemInfos.add(bodyItemInfo);
                    }
                }
                log.info("[op:EndorsementController] time-watch,step{}={}", 4, new DateTime().toString("yy-MM-dd HH:mm:ss SSS"));
                endorsementDetailWarp.getBodyItemInfos().addAll(bodyItemInfos);
            }else{
                InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(endorsementOrderDTO.getInventoryOrderId());
                List<InventoryOrderItemDTO> list = inventoryOrderInfoService.findListByInvenOrderId(endorsementOrderDTO.getInventoryOrderId());
                if (list == null) list = new ArrayList<InventoryOrderItemDTO>();
                endorsementDetailWarp.getBodyItemInfos().addAll(list.stream().map(s->{
                    BodyItemInfo bodyItemInfo  = new BodyItemInfo();
                    bodyItemInfo.setAccountSeqNo(StringUtils.isEmpty(s.getGoodsSeqNo())?"无":s.getGoodsSeqNo());
                    bodyItemInfo.setProductId(s.getProductId());
                    return bodyItemInfo;
                }).collect(Collectors.toList()));
                if(!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())&&
                        !StringUtils.isEmpty(endorsementOrderDTO.getBussinessType())) {
                    if(inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())) {
                        List<InventoryOrderRelationDTO> listInventoryOrderRelationDTO = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(endorsementOrderDTO.getInventoryOrderId());
                        if (!org.apache.commons.collections4.CollectionUtils.isEmpty(listInventoryOrderRelationDTO)){
                             // 一线入境
                            if(inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_ONELINE_IN.getCode())){
                                if(listInventoryOrderRelationDTO==null){
                                    listInventoryOrderRelationDTO= new ArrayList<>();
                                }
                                listInventoryOrderRelationDTO = listInventoryOrderRelationDTO.stream().filter(s->s.getRelType().equalsIgnoreCase(InventoryOrderRelationEnum.REL_TYPE_DECLARE.getCode())).collect(Collectors.toList());
                                endorsementDetailWarp.setListInventoryOrderRelationDTO(listInventoryOrderRelationDTO);
                            }else if(inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode())){
                                // 退货入区
                                if(listInventoryOrderRelationDTO==null){
                                    listInventoryOrderRelationDTO= new ArrayList<InventoryOrderRelationDTO>();
                                }
                                listInventoryOrderRelationDTO = listInventoryOrderRelationDTO.stream().filter(s->s.getRelType().equalsIgnoreCase(InventoryOrderRelationEnum.REL_TYPE_YUNDAN.getCode())).collect(Collectors.toList());
                                endorsementDetailWarp.setListInventoryOrderRelationDTO(listInventoryOrderRelationDTO);
                            }//区内流转(出),区内流转(入),区间流转(出),区间流转(入)
                            else if(inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode())||
                                    inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTION_IN.getCode())||
                                    inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode())||
                                    inventoryOrderInfoDTO.getInveBusinessType().equalsIgnoreCase(EndorsementBussiness.BUSSINESS_SECTIONINNER_IN.getCode())){
                            }
                        }
                    }
                }
            }
            log.info("[op:EndorsementController] time-watch,step{}={}", 9, new DateTime().toString("yy-MM-dd HH:mm:ss SSS"));
            */
            /*
            if (endorsementOrderDTO.getIeFlag().equals(IEType.EXPORT.getValue())){
                data.setAllowExport(true); // 出区才有得导出
            }
            */
            //if (endorsementOrderDTO.getIeFlag().equals(IEType.EXPORT.getValue())){}
//            if(EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())){
//                data.setAllowExport(true); // 出区才有得导出
//            }
//
//            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())){
//                data.setAllowFinish(true); // 仅刚创建的支持手动完成核注
//                data.setAllowDiscard(true);
//            }
//            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())){
//                data.setAllowFinish(true); // 仅刚创建的支持手动完成核注
//            }
//            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())){
//                data.setAllowDiscard(true);
//                data.setAllowFinish(true);
//            }
            if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementOrderDTO.getBussinessType())) {

                if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode()) ||
                        endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowDiscard(true);
                    data.setAllowFinish(true);
                    data.setAllowView(true);
                    data.setAllowEliminateException(endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode()));//异常才有剔除的按钮
                } else if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXAMINE.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.FINISH.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DISCARD.getCode())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowView(true);
                }
            } else {
                if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode()) ||
                        endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowDiscard(true);
                    data.setAllowFinish(true);
                    data.setAllowView(true);
                } else if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXAMINE.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.FINISH.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DISCARD.getCode())
                ) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowView(true);
                }
                if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.STORAGING.getCode())
                        || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.STORAGED.getCode())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowView(true);
                    data.setAllowDiscard(true);
                }
                if (EndorsementOrderStatus.STORAGING.getCode().equals(endorsementOrderDTO.getStatus())) { //暂存中
                    data.setAllowStorage(true);
                }
                if (EndorsementOrderStatus.INIT.getCode().equals(endorsementOrderDTO.getStatus())
                        || EndorsementOrderStatus.STORAGED.getCode().equals(endorsementOrderDTO.getStatus())
                        || EndorsementOrderStatus.EXCEPTION.getCode().equals(endorsementOrderDTO.getStatus())) {
                    data.setAllowStorage(true);
                    data.setAllowView(true);
                }
                if (EndorsementOrderStatus.STORAGE_EXCEPTION.getCode().equals(endorsementOrderDTO.getStatus())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowDiscard(true);
                    data.setAllowStorage(true);
                    data.setAllowView(true);
                }
                if (EndorsementOrderStatus.DELETED.getCode().equals(endorsementOrderDTO.getStatus())) {
                    data.setAllowExport(true); // 出区才有得导出
                    data.setAllowView(true);
                }
            }
            if (Objects.equals(endorsementOrderDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
                data.setAllowView(false);
                data.setAllowEliminateException(false);
            }
            //手动删除申请
            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXAMINE.getCode())
                    || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.FINISH.getCode())) {
                data.setAllowDeletedApply(true);
            }

            // 报关单生成状态
            if (StrUtil.isNotBlank(endorsementOrderDTO.getGenerateDeclareStatus())) {
                data.setGenerateDeclareStatusDesc(Objects.equals("1", endorsementOrderDTO.getGenerateDeclareStatus()) ? "成功" : "失败");
                if (!Objects.equals("1", endorsementOrderDTO.getGenerateDeclareStatus())) {
                    data.setGenerateDeclareReason(endorsementOrderDTO.getGenerateDeclareReason());
                }
            }
            return data;
        }).collect(Collectors.toList()));
        return new Response<>(result);
    }


    @ApiOperation(value = "(退货入区)编辑运单号预览")
    @PostMapping("/preRefundEditMailNo")
    public InventoryOrderRelationEditReport preRefundEditMailNo(@RequestBody InventoryOrderRelationEditPre submit) {
        EndorsementDTO endorsementDTO = endorsementService.findById(submit.getId());
        if (endorsementDTO == null)
            throw new ArgsErrorException("查询核注清单为空");
        if (endorsementDTO.getInventoryOrderId() == null)
            throw new ArgsErrorException("核注清单相关清关单ID为空");

        if (!endorsementDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !endorsementDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
            throw new ArgsErrorException("仅未申报或异常的核注清单允许编辑运单");
        }
        if (endorsementDTO.getInventoryOrderId() == null) {
            throw new ArgsErrorException("相关的核注清单不非退货入区");
        }
        List<InventoryOrderRelationDTO> itemDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(endorsementDTO.getInventoryOrderId());
        Map<String, InventoryOrderRelationDTO> itemDTOMap = new HashMap();
        for (InventoryOrderRelationDTO itemDTO : itemDTOList) {
            itemDTOMap.put(itemDTO.getRelNo(), itemDTO);
        }
        /**
         * 去重
         */
        List<String> oldMailList = new ArrayList<>();
        itemDTOList.stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.groupingBy(Function.identity(), Collectors.counting())).forEach((k, v) -> {
            oldMailList.add(k);
        });
        /**
         * 去重
         */
        //List<String> oldMailList = new ArrayList<>();//itemDTOList.stream().map(ExportItemDTO::getMailNo).collect(Collectors.toList());
        List<String> newMailList = new ArrayList<>();
        submit.getRecordList().stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.groupingBy(Function.identity(), Collectors.counting())).forEach((k, v) -> {
            newMailList.add(k);
        });
        List<InventoryOrderRelationDTO> delList = new ArrayList<>();
        List<InventoryOrderRelationDTO> addList = new ArrayList<>();
        for (InventoryOrderRelationDTO exportItemDTO : itemDTOList) {
            if (!newMailList.contains(exportItemDTO.getRelNo())) {
                InventoryOrderRelationDTO record = new InventoryOrderRelationDTO();
                record.setRelNo(exportItemDTO.getRelNo());
                record.setRelType(InventoryOrderRelationEnum.REL_TYPE_YUNDAN.getCode());
                delList.add(record);
            }
        }
        for (InventoryOrderRelationDTO record : submit.getRecordList()) {
            if (!oldMailList.contains(record.getRelNo())) {
                addList.add(record);
            }
        }
        return inventoryOrderInfoService.editByEndorsement(endorsementDTO.getInventoryOrderId(), addList, delList, false);
    }

    @ApiOperation(value = "(退货入区)编辑运单号提交")
    @PostMapping("/submitRefundEditMailNo")
    public InventoryOrderRelationEditReport submitEditMailNo(@RequestBody InventoryOrderRelationEditSubmit submit) {
        EndorsementDTO endorsementDTO = endorsementService.findById(submit.getId());
        if (endorsementDTO == null)
            throw new ArgsErrorException("查询核注清单为空");
        if (endorsementDTO.getInventoryOrderId() == null)
            throw new ArgsErrorException("核注清单相关清关单ID为空");

        if (!endorsementDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                && !endorsementDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
            throw new ArgsErrorException("仅未申报或异常的核注清单允许编辑运单");
        }
        if (endorsementDTO.getInventoryOrderId() == null) {
            throw new ArgsErrorException("相关的核注清单不非退货入区");
        }
        InventoryOrderRelationEditReport report = inventoryOrderInfoService.editByEndorsement(endorsementDTO.getInventoryOrderId(), submit.getAddList(), submit.getDeleteList(), true);
        return report;
    }

    @ApiOperation(value = "编辑运单号预览")
    @PostMapping("/preEditMailNo")
    public ExportItemEditReport preEditMailNo(@RequestBody ExportItemEditPre submit) {

        // Step::取新增集和删除集
        List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(submit.getId());
//        Map<String, ExportItemDTO> itemDTOMap = new HashMap();
//        for (ExportItemDTO itemDTO : itemDTOList) {
//            itemDTOMap.put(itemDTO.getMailNo(), itemDTO);
//        }
//        Map<String, ExportItemDTO> itemDTOMap = itemDTOList.stream().collect(Collectors.toMap(ExportItemDTO::getMailNo, Function.identity(), (v1, v2) -> v2));
        /**
         * 去重
         */
        List<String> oldMailList = itemDTOList.stream().map(ExportItemDTO::getMailNo).distinct().collect(Collectors.toList());
        /**
         * 去重
         */
        //List<String> oldMailList = new ArrayList<>();//itemDTOList.stream().map(ExportItemDTO::getMailNo).collect(Collectors.toList());
        List<String> newMailList = submit.getRecordList().stream().map(ExportItemRecord::getMailNo).distinct().collect(Collectors.toList());

        List<ExportItemRecord> delList = new ArrayList<>();
        List<ExportItemRecord> addList = new ArrayList<>();

        for (ExportItemDTO exportItemDTO : itemDTOList) {
            if (!newMailList.contains(exportItemDTO.getMailNo())) {
                ExportItemRecord record = new ExportItemRecord();
                record.setMailNo(exportItemDTO.getMailNo());
                record.setExpressId(exportItemDTO.getExpressId());
                record.setCustomsInventorySn(exportItemDTO.getCustomsInventorySn());
                delList.add(record);
            }
        }
        for (ExportItemRecord record : submit.getRecordList()) {
            if (!oldMailList.contains(record.getMailNo())) {
                addList.add(record);
            }
        }
        ExportItemEditReport report = exportOrderService.editByEndorsement(submit.getId(), addList, delList, false);
        return report;
    }


    @ApiOperation(value = "编辑运单号提交")
    @PostMapping("/submitEditMailNo")
    public ExportItemEditReport submitEditMailNo(@RequestBody ExportItemEditSubmit submit) {
        ExportItemEditReport report = exportOrderService.editByEndorsement(submit.getId(), submit.getAddList(), submit.getDeleteList(), true);
        return report;
    }

    @PostMapping("/push")
    @ApiOperation(value = "推送核注")
    public Response<String> retryException(@RequestBody IdsParam param) {
        try {
            for (Long id : param.getIdList()) {
                endorsementService.push(id);
            }
        } catch (ArgsErrorException e) {
            return new Response<>(e);
        } catch (RuntimeException ex) {
            return new Response<>(-1, ex.getMessage());
        }
        return new Response<>("推送成功");
    }

    @PostMapping("/temporaryStorage")
    @ApiOperation(value = "暂存核注")
    public Response<String> temporaryStorage(@RequestBody IdsParam param) {
        try {
            for (Long id : param.getIdList()) {
                endorsementService.temporaryStorage(id);
            }
        } catch (ArgsErrorException e) {
            return new Response<>(e);
        } catch (RuntimeException ex) {
            log.error("暂存失败 error={}", ex.getMessage(), ex);
            return new Response<>(BusinessErrorCode.ARGS_ERROR.getCode(), "暂存失败");
        }
        return new Response<>("推送成功");
    }

    @PostMapping("/saveRemark")
    @ApiOperation(value = "增加备注")
    public Response<String> saveRemark(@RequestBody EndorsementRemarkVO endorsementRemarkVO) {
        try {
            endorsementService.saveRemark(endorsementRemarkVO.getId(), endorsementRemarkVO.getRemark());
        } catch (ArgsErrorException e) {
            return new Response<>(e);
        }
        return new Response<>("提交成功");
    }


    @ApiOperation(value = "手动核扣")
    @GetMapping("/handler-check")
    public Response<String> handlerCheck(String ids) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(ids)) {
            Response oret = new Response<>("提交成功");
            oret.setCode(-1);
            oret.setErrorMessage("请选择核注清单");
            return oret;
        }
        java.util.Map<String, EndorsementDTO> map = new java.util.HashMap<String, EndorsementDTO>();
        String _ids[] = org.apache.commons.lang3.StringUtils.split(ids, ",");
        for (String id : _ids) {
            EndorsementDTO endorsementDTO = endorsementService.findById(NumberUtils.createLong(id));
            if (!EndorsementOrderStatus.EXAMINE.getCode().equalsIgnoreCase(endorsementDTO.getStatus()) &&
                    !EndorsementOrderStatus.STORAGED.getCode().equalsIgnoreCase(endorsementDTO.getStatus())) {
                Response oret = new Response<>("提交成功");
                oret.setCode(-1);
                oret.setErrorMessage("对不起!单据[" + endorsementDTO.getSn() + "]当前状态不能完成手动核扣");
                return oret;
            } else if (StringUtils.isEmpty(endorsementDTO.getRealOrderNo())) {
                Response oret = new Response<>("提交成功");
                oret.setCode(-1);
                oret.setErrorMessage("对不起!单据[" + endorsementDTO.getSn() + "]当前真实核注清单编号为空,不能手动核扣");
                return oret;
            }
            map.put(id, endorsementDTO);
        }
        for (String id : _ids) {

            EndorsementDTO endorsementDTO = map.get(id);
            String lockKey = "endorsement:handler:check:" + endorsementDTO.getId();
            if (!RedissLockUtil.tryLock(lockKey, TimeUnit.SECONDS, 3, -1)) {
                log.info("endorsement 手动核扣 key:{} 加锁失败", lockKey);
                Response oret = new Response<>("提交成功");
                oret.setCode(-1);
                oret.setErrorMessage("手动核扣正在执行，请勿重复操作");
                return oret;
            }
            try {
                log.info("endorsement 手动核扣 key:{} 加锁成功", lockKey);
                endorsementService.finish(endorsementDTO.getId(), endorsementDTO.getRealOrderNo(), Arrays.asList("核注清单手动核扣"), true);
            } finally {
                try {
                    RedissLockUtil.unlock(lockKey);
                    log.info("endorsement 手动核扣 key:{} 释放锁成功", lockKey);
                } catch (Exception ex) {
                    log.error("解锁异常 key:{} 异常信息:{}", lockKey, ex.getMessage(), ex);
                }
            }
        }
        return new Response<>("提交成功");
    }

    @ApiOperation(value = "完成核注审核")
    @PostMapping("/finish")
    public Response<String> finish(@RequestBody EndorsementFinishSubmit submit) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(submit.getRealNo())) {
            Response oret = new Response<>("提交成功");
            oret.setCode(-1);
            oret.setErrorMessage("对不起!核注编号不能为空");
            return oret;
        }
        boolean _isNotExist = endorsementService.checkRealNoUnqiue(submit.getRealNo(), submit.getId());
        if (!_isNotExist) {
            Response oret = new Response<>("提交失败");
            oret.setCode(-1);
            oret.setErrorMessage("对不起!核注清单编号已存在");
            return oret;
        }
        EndorsementDTO old = endorsementService.findById(submit.getId());
        if (Objects.isNull(old)) {
            Response oret = new Response<>("提交失败");
            oret.setCode(-1);
            oret.setErrorMessage("对不起!核注清单未找到");
            return oret;
        }
        if (Objects.nonNull(old.getInventoryOrderId())) {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(old.getInventoryOrderId());
            if (Objects.nonNull(inventoryOrderInfoDTO)) {
                if (Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.TAO_TIAN.getValue())
                        && Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
                ) {
                    if (!inventoryOrderInfoService.checkTaotian2050Exist(inventoryOrderInfoDTO)) {
                        throw new ArgsErrorException("请先回传货物到港");
                    }
                }
            }
        }
//        if (EndorsementBussiness.BUSSINESS_SECTION_OUT.getCode().equals(old.getBussinessType()) ||
//                EndorsementBussiness.BUSSINESS_SECTIONINNER_OUT.getCode().equals(old.getBussinessType()) ||
//                EndorsementBussiness.BUSSINESS_DESTORY.getCode().equals(old.getBussinessType())) {
//            List<InventoryOrderItemDTO> itemDTOS = inventoryOrderInfoService.findListByInvenOrderId(old.getInventoryOrderId());
//            endorsementService.fillItemByIdStockVerify(itemDTOS, old, submit.getId(), submit.getRealNo());
//        } else {
        endorsementService.fillItemById(submit.getId(), submit.getRealNo());
//        }
        InventoryOrderInfoDTO infoDTO = new InventoryOrderInfoDTO();
        infoDTO.setId(old.getInventoryOrderId());
        infoDTO.setEndorsementRealOrderNo(submit.getRealNo());
        inventoryOrderInfoService.updateInventoryOrderInfoDTO(infoDTO);
        EndorsementDTO endorsementDTO = endorsementService.findById(submit.getId());
        endorsementService.examinedPostProcess(endorsementDTO, Arrays.asList("核注清单手动审核：核注清单编号为" + submit.getRealNo()), true);
        return new Response<>("提交成功");
    }

    @ApiOperation(value = "单个导出")
    @GetMapping("/singleExport")
    public void singleExport(Long id, HttpServletResponse response) {
        EndorsementDTO endorsementOrderDTO = endorsementService.findById(id);
        Workbook workbook = this.buildExport(endorsementOrderDTO);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String((endorsementOrderDTO.getSn() + ".xlsx").getBytes(), "iso8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
        }
    }

    @ApiOperation(value = "作废核注清单")
    @PostMapping("/discard")
    public Response<String> discard(@RequestBody IdParam idParam) {
        endorsementService.discard(idParam.getId());
        return new Response<>("作废成功");
    }

    public static Workbook mutiSheet(List<Map<String, Object>> mapListList) {
        return ExcelExportUtil.exportExcel(mapListList, ExcelType.XSSF);
    }

    public static Map<String, Object> createOneSheet(String sheetName, Class<?> clazz, List<?> data) {
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName(sheetName);
        exportParams.setType(ExcelType.XSSF);

        Map<String, Object> map = new HashMap<>();
        map.put("title", exportParams);//new ExportParams("title"+i, "sheetName"+i, ExcelType.XSSF)
        map.put("entity", clazz);
        map.put("data", data);
        return map;
    }

    @ApiOperation(value = "批量导出")
    @GetMapping("/excelExport")
    public void excelExport(HttpServletResponse response, EndorsementSearch search) throws ServiceException {
        search.setPageSize(30000);
//        ListVO<EndorsementDTOV2> paging = null;
//        if (!StringUtils.isEmpty(search.getMailNo())) {
//            List<ExportItemDTO> checklistOrderDTOList = exportOrderService.listItemByMailNos(new HashSet<>(Lists.newArrayList(search.getMailNo())));
//            Set<Long> idSet = checklistOrderDTOList.stream().map(ExportItemDTO::getEndorsementOrderId).collect(Collectors.toSet());
//            if (CollectionUtils.isEmpty(idSet)) {
//                paging = ListVOBuilder.buildEmptyListVO(EndorsementDTOV2.class);
//            } else {
//                search.setIdSet(idSet);
//            }
//        }
//        //清关单号
//        if (!StringUtils.isEmpty(search.getInvOrderSn())) {
//            List<String> invOrderSns = Splitter.on(",").splitToList(search.getInvOrderSn());
//            List<InventoryOrderInfoDTO> inventoryOrderInfoDTOS = inventoryOrderInfoService.findBySn(invOrderSns);
//            if (!CollectionUtils.isEmpty(inventoryOrderInfoDTOS)) {
//                search.setInventoryOrderIds(inventoryOrderInfoDTOS.stream().map(InventoryOrderInfoDTO::getId).collect(Collectors.toList()));
//            }
//        }
//        //出库单号
//        if (!StringUtils.isEmpty(search.getExportOrderSn())) {
//            List<String> exportOrderSns = Splitter.on(",").splitToList(search.getExportOrderSn());
//            List<ExportOrderDTO> exportOrderDTOS = exportOrderService.findBySn(exportOrderSns);
//            if (!CollectionUtils.isEmpty(exportOrderDTOS)) {
//                search.setExportOrderIds(exportOrderDTOS.stream().map(ExportOrderDTO::getId).collect(Collectors.toList()));
//            }
//        }
//        if (paging == null) {
        ListVO<EndorsementDTOV2> paging = endorsementService.paging(search);
//        }

        List<String> idList = paging.getDataList().stream().map((EndorsementDTO dto) -> {
            return dto.getId().toString();
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(idList) && idList.size() == 1) {
            this.singleExport(Long.valueOf(idList.get(0)), response);
            return;
        }
        List<String> filePaths = new ArrayList<>();
        //生成的ZIP文件名为Demo.zip
        String tmpFileName = "核注清单导出" + DateTime.now().toString("yyMMddHHmm") + ".zip";
        // zip文件路径
        String strZipPath = DownLoadUtil.FilePath + tmpFileName;
        filePaths.add(strZipPath);
        try {
            //生成excel文件集合
            File targetFile = new File(DownLoadUtil.FilePath);
            if (!targetFile.exists()) {
                targetFile.mkdirs();
            }
            //创建zip输出流
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(strZipPath));
            //声明文件集合用于存放excel文件
            List<File> fileList = new ArrayList<File>();
            for (String idStr : idList) {
                Long id = Long.valueOf(idStr);
                EndorsementDTO endorsementOrderDTO = endorsementService.findById(id);
                // 生成随机文件名
                String filename = DownLoadUtil.FilePath + endorsementOrderDTO.getSn() + ".xlsx";
                // 将文件路径保存
                fileList.add(DownLoadUtil.creatFile(filename));
                filePaths.add(filename);
                {

                    Workbook workbook = this.buildExport(endorsementOrderDTO);
                    // 导出
                    FileOutputStream excelOut = new FileOutputStream(filename);
                    workbook.write(excelOut);
                    excelOut.flush();
                    excelOut.close();
                }
            }
            byte[] buffer = new byte[1024];
            //将excel文件放入zip压缩包
            for (int i = 0; i < fileList.size(); i++) {
                File file = fileList.get(i);
                FileInputStream fis = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(file.getName()));
                //设置压缩文件内的字符编码，不然会变成乱码
//                out.setEncoding("GBK");
                int len;
                // 读入需要下载的文件的内容，打包到zip文件
                while ((len = fis.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
                out.closeEntry();
                fis.close();
            }
            out.close();
            //下载zip文件
            DownLoadUtil.downFile(response, tmpFileName, filePaths);
        } catch (Exception e) {
            // 下载失败删除生成的文件
            DownLoadUtil.deleteFile(filePaths);
            log.error("文件下载出错", e);
        }
    }

    private Workbook buildExport(EndorsementDTO endorsementOrderDTO) {
        // 生成随机文件名
        String filename = DownLoadUtil.FilePath + endorsementOrderDTO.getSn() + ".xlsx";
        // 将文件路径保存
        List<EndorsementOrderExcelVO> inventoryList = new ArrayList<>();
        List<EndorsementExcelVO> excelVOList = new ArrayList<>();
        //if(IEType.IMPORT.getValue().equals(endorsementOrderDTO.getIeFlag()))
        if (!EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(endorsementOrderDTO.getBussinessType())) {
            if (endorsementOrderDTO.getInventoryOrderId() != null) {
                InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(endorsementOrderDTO.getInventoryOrderId());
                List<InventoryOrderItemDTO> listInventoryOrderItemDTO = inventoryOrderInfoService.findListByInvenOrderId(endorsementOrderDTO.getInventoryOrderId());
                for (InventoryOrderItemDTO itemDTO : listInventoryOrderItemDTO) {
                    EndorsementExcelVO excelVO = new EndorsementExcelVO();
                    excelVO.setTotalCount(itemDTO.getDeclareUnitQfy().intValue());
                    BigDecimal unitPrice = excelVO.getUnitPrice();
                    if (unitPrice == null) unitPrice = new BigDecimal(0);
                    excelVO.setTotalAmount(unitPrice.multiply(new BigDecimal(excelVO.getTotalCount())));
                    excelVO.setFirstUnitAmount(itemDTO.getFirstUnitQfy());
                    excelVO.setSecondUnitAmount(itemDTO.getSecondUnitQfy());
                    excelVO.setFirstUnit(itemDTO.getFirstUnit());
                    excelVO.setSecondUnit(itemDTO.getSecondUnit());
                    excelVO.setOriginCountry(itemDTO.getOriginCountry());
                    excelVO.setHsCode(itemDTO.getHsCode());
                    excelVO.setProductId(itemDTO.getProductId());
                    excelVO.setGoodsUnit(itemDTO.getUnit());
                    excelVO.setGoodsName(itemDTO.getRecordProductName());
                    excelVO.setNetWeight(itemDTO.getNetweight());
                    GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(endorsementOrderDTO.getAccountBookId(), itemDTO.getProductId());
                    if (goodsRecordDTO != null) {
                        excelVO.setUnitPrice(goodsRecordDTO.getDeclarePrice());
                        unitPrice = excelVO.getUnitPrice();
                        if (unitPrice == null) unitPrice = new BigDecimal(0);
                        excelVO.setTotalAmount(unitPrice.multiply(new BigDecimal(excelVO.getTotalCount())));
                        excelVO.setGrossWeight(goodsRecordDTO.getGrossWeight());
                        excelVO.setGoodsModel(goodsRecordDTO.getModel());
                    } else {
                        excelVO.setUnitPrice(new BigDecimal(0));
                        excelVO.setGrossWeight(new BigDecimal(0));
                        excelVO.setGoodsModel("");
                    }
                    if (itemDTO.getIsNew().equalsIgnoreCase("old")) {
                        CustomsBookItemDTO bookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(
                                endorsementOrderDTO.getAccountBookId(),
                                itemDTO.getGoodsSeqNo(),
                                itemDTO.getProductId());
                        if (bookItemDTO != null) {
                            excelVO.setOriginCountry(bookItemDTO.getOriginCountry());
                            excelVO.setFirstUnit(bookItemDTO.getFirstUnit());
                            excelVO.setSecondUnit(bookItemDTO.getSecondUnit());
                            excelVO.setGoodsUnit(bookItemDTO.getGoodsUnit());
                            excelVO.setCurrCode(bookItemDTO.getCurrCode());
                            excelVO.setGoodsModel(bookItemDTO.getGoodsModel());
                            excelVO.setProductId(bookItemDTO.getProductId());
                            excelVO.setGoodsSeqNo(bookItemDTO.getGoodsSeqNo());
                            excelVO.setHsCode(bookItemDTO.getHsCode());
                            excelVO.setGoodsName(bookItemDTO.getGoodsName());
                        }
                    }
                    excelVOList.add(excelVO);
                }
            }
        } else {

            List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(endorsementOrderDTO.getId());
            for (ExportItemDTO itemDTO : itemDTOList) {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findBySnSection(itemDTO.getCustomsInventorySn());
                EndorsementOrderExcelVO orderExcelVO = new EndorsementOrderExcelVO();
                orderExcelVO.setInvtNo(customsInventoryDTO.getInventoryNo());
                inventoryList.add(orderExcelVO);


            }
            List<EndorsementItemDTO> endorsementItemDTOList;
            if (endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                    || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())
                    || endorsementOrderDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())) {
                endorsementItemDTOList = endorsementService.preItemView(endorsementOrderDTO.getId());
            } else {
                endorsementItemDTOList = endorsementService.listItemById(endorsementOrderDTO.getId());
            }
            for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOList) {
                GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(endorsementOrderDTO.getAccountBookId(), endorsementItemDTO.getProductId());
                if (goodsRecordDTO == null) {
                    continue;
                }
                EndorsementExcelVO excelVO = new EndorsementExcelVO();
                excelVO.setUnitPrice(goodsRecordDTO.getDeclarePrice());
                excelVO.setTotalCount(endorsementItemDTO.getDeclareUnitQfy().intValue());
                excelVO.setTotalAmount(excelVO.getUnitPrice().multiply(new BigDecimal(excelVO.getTotalCount())));
                excelVO.setGrossWeight(goodsRecordDTO.getGrossWeight());
                excelVO.setNetWeight(goodsRecordDTO.getNetWeight());
                excelVO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
                excelVO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
                CustomsBookItemDTO bookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(
                        endorsementOrderDTO.getAccountBookId(),
                        endorsementItemDTO.getGoodsSeqNo(),
                        endorsementItemDTO.getProductId());
                if (bookItemDTO == null) {
                    excelVO.setOriginCountry(goodsRecordDTO.getOriginCountry());
                    excelVO.setFirstUnit(goodsRecordDTO.getFirstUnit());
                    excelVO.setSecondUnit(goodsRecordDTO.getSecondUnit());
                    excelVO.setGoodsUnit(goodsRecordDTO.getDeclareUnit());
                    excelVO.setGoodsModel(goodsRecordDTO.getModel());
                    excelVO.setProductId(goodsRecordDTO.getProductId());
                    excelVO.setHsCode(goodsRecordDTO.getHsCode());
                    excelVO.setGoodsName(goodsRecordDTO.getGoodsRecordName());
                } else {
                    excelVO.setOriginCountry(bookItemDTO.getOriginCountry());
                    excelVO.setFirstUnit(bookItemDTO.getFirstUnit());
                    excelVO.setSecondUnit(bookItemDTO.getSecondUnit());
                    excelVO.setGoodsUnit(bookItemDTO.getGoodsUnit());
                    excelVO.setCurrCode(bookItemDTO.getCurrCode());
                    excelVO.setGoodsModel(bookItemDTO.getGoodsModel());
                    excelVO.setProductId(bookItemDTO.getProductId());
                    excelVO.setGoodsSeqNo(bookItemDTO.getGoodsSeqNo());
                    excelVO.setHsCode(bookItemDTO.getHsCode());
                    excelVO.setGoodsName(bookItemDTO.getGoodsName());
                }
                excelVOList.add(excelVO);
            }
        }
        List<Map<String, Object>> lists = new ArrayList<>();
        //sheet1
        Map<String, Object> temp1 = createOneSheet("表体(成品)", EndorsementExcelVO.class, excelVOList);
        lists.add(temp1);
        //sheet2
        if (!CollectionUtils.isEmpty(inventoryList)) {
            Map<String, Object> temp2 = createOneSheet("保税电商清单", EndorsementOrderExcelVO.class, inventoryList);
            lists.add(temp2);
        }
        Workbook workbook = mutiSheet(lists);
        return workbook;
    }

    public List<EndorsementChecklistVO> listForChecklistByTypeWithBound(List<Long> accountBookList, String businessType, Long companyId, Long checklistId) {
        List<EndorsementChecklistVO> checklistVOS = new ArrayList<>();
        List<EndorsementDTO> endorsementDTOList = endorsementService.listByStatusAndBookIdList(EndorsementOrderStatus.EXAMINE.getCode(), accountBookList, businessType, companyId);
        endorsementDTOList = CollUtil.reverse(endorsementDTOList);
        List<EndorsementDTO> boundEndorsement = checklistService.getEndorsementListByChecklistId(checklistId);
        boundEndorsement = boundEndorsement.stream().filter(b -> b.getBussinessType().equals(businessType)).collect(Collectors.toList());
        endorsementDTOList.addAll(boundEndorsement);

        // 补充授权核注单
        List<ChecklistAuthDTO> checklistAuthDTOList = checklistAuthService.findByBeAuthCompanyId(companyId, businessType);
        if (CollUtil.isNotEmpty(checklistAuthDTOList)) {
            List<String> authRealNoList = checklistAuthDTOList.stream()
                    .filter(i -> !ChecklistAuthCancelStatusEnum.SENT.getCode().equals(i.getCancelStatus()))
                    .flatMap(i -> i.getEndorsementRealOrderNoList().stream())
                    .collect(Collectors.toList());

            List<EndorsementDTO> authEndorsementDTOList = endorsementService.findByRealOrderNo(authRealNoList);
            authEndorsementDTOList = authEndorsementDTOList.stream()
                    .filter(i -> i.getStatus().equals(EndorsementOrderStatus.EXAMINE.getCode()))
                    .peek(i -> i.setSn(i.getSn() + "【授权】"))
                    .collect(Collectors.toList());
            if (CollUtil.isNotEmpty(authEndorsementDTOList)) {
                authEndorsementDTOList.addAll(endorsementDTOList);
                endorsementDTOList = authEndorsementDTOList;
            }
        }
        // 一线入区 特殊处理 - 区间流转入 只获取核注单运输方式为 【特殊综合保税区】
        ChecklistDTO checklistDTO = checklistService.findById(checklistId);
        if (Objects.equals(checklistDTO.getType(), ChecklistType.FIRST.getCode()) && Objects.equals(businessType, EndorsementBussiness.BUSSINESS_SECTION_IN.getCode())) {
            List<InventoryOrderInfoDTO> inventoryOrderInfoDTOList = inventoryOrderInfoService
                    .findById(endorsementDTOList.stream().map(EndorsementDTO::getInventoryOrderId).collect(Collectors.toList()));
            Set<Long> specialBondedAreaOrderIdSet = inventoryOrderInfoDTOList.stream()
                    .filter(i -> Objects.equals(i.getTransportMode(), Inv101TrspModecd.WAY_SPECIAL_BONDED_AREA.getKey()))
                    .map(InventoryOrderInfoDTO::getId).collect(Collectors.toSet());
            endorsementDTOList = endorsementDTOList.stream().filter(i -> specialBondedAreaOrderIdSet.contains(i.getInventoryOrderId())).collect(Collectors.toList());
        }

        if (Objects.equals(checklistDTO.getBindType(), ChecklistBindType.ONE_TICKET_ONE_CAR.getCode())) {
            // 一车一票 过滤已绑定的核注单
            List<Long> endorsementIdList = endorsementDTOList.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
            List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemByEndorsementIds(endorsementIdList);
            Set<Long> boundEndorsementIdList = endorsementItemDTOS.stream()
                    .filter(i -> Objects.nonNull(i.getChecklistId()) && !Objects.equals(checklistId, i.getChecklistId()))
                    .map(EndorsementItemDTO::getEndorsementId)
                    .collect(Collectors.toSet());
            // 一车多票映射表获取
            Map<Long, List<ChecklistDTO>> endorsementIdByMapping = checklistService.getChecklistByEndorsementList(endorsementIdList);
            for (Map.Entry<Long, List<ChecklistDTO>> entry : endorsementIdByMapping.entrySet()) {
                Long k = entry.getKey();
                List<ChecklistDTO> v = entry.getValue();
                if (v.stream().anyMatch(checklist -> !Objects.equals(checklist.getId(), checklistId))) {
                    boundEndorsementIdList.add(k);
                }
            }
            endorsementDTOList = endorsementDTOList.stream().filter(e -> !boundEndorsementIdList.contains(e.getId())).collect(Collectors.toList());
        }
        // 去重
        Set<Long> endorsementIdSet = new HashSet<>();
        List<EndorsementDTO> distinctEndorsementDTOList = new ArrayList<>();
        endorsementDTOList.forEach(i -> {
            if (endorsementIdSet.contains(i.getId())) {
                return;
            }
            endorsementIdSet.add(i.getId());
            distinctEndorsementDTOList.add(i);
        });
        return getEndorsementChecklistVOS(checklistVOS, distinctEndorsementDTOList);
    }


    public List<EndorsementChecklistVO> listForChecklistByType(List<Long> accountBookList, String businessType, Long companyId) {
        List<EndorsementChecklistVO> checklistVOS = new ArrayList<>();
        List<EndorsementDTO> endorsementDTOList = endorsementService.listByStatusAndBookIdList(EndorsementOrderStatus.EXAMINE.getCode(), accountBookList, businessType, companyId);
        return getEndorsementChecklistVOS(checklistVOS, endorsementDTOList);
    }

    public List<EndorsementChecklistVO> listForChecklistByType(List<Long> accountBookList, String businessType) {
        List<EndorsementChecklistVO> checklistVOS = new ArrayList<>();
        List<EndorsementDTO> endorsementDTOList = endorsementService.listByStatusAndBookIdList(EndorsementOrderStatus.EXAMINE.getCode(), accountBookList, businessType);
        return getEndorsementChecklistVOS(checklistVOS, endorsementDTOList);
    }

    private List<EndorsementChecklistVO> getEndorsementChecklistVOS(List<EndorsementChecklistVO> checklistVOS, List<EndorsementDTO> endorsementDTOList) {
        for (EndorsementDTO endorsementDTO : endorsementDTOList) {
            EndorsementChecklistVO vo = new EndorsementChecklistVO();
            vo.setId(endorsementDTO.getId());
            vo.setSn(endorsementDTO.getSn());
            vo.setBussinessType(endorsementDTO.getBussinessType());
            vo.setRealOrderNo(endorsementDTO.getRealOrderNo());
            checklistVOS.add(vo);
        }
        return checklistVOS;
    }

    /**
     * 核注单列表根据账册权限获取
     *
     * @param accountBookList
     * @return
     */
    public List<EndorsementChecklistVO> listForChecklistByType(List<Long> accountBookList) {
        List<EndorsementChecklistVO> checklistVOS = new ArrayList<>();
        List<EndorsementDTO> endorsementDTOList = endorsementService.listByStatusAndBookIdList(EndorsementOrderStatus.EXAMINE.getCode(), accountBookList);
        List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemByEndorsementIds(endorsementDTOList.stream().map(EndorsementDTO::getId).collect(Collectors.toList()));
        Map<Long, List<EndorsementItemDTO>> itemDTOMap = endorsementItemDTOS.stream().collect(Collectors.groupingBy(EndorsementItemDTO::getEndorsementId));
        for (EndorsementDTO endorsementDTO : endorsementDTOList) {
            EndorsementChecklistVO vo = new EndorsementChecklistVO();
            vo.setId(endorsementDTO.getId());
            vo.setSn(endorsementDTO.getSn());
            vo.setBussinessType(endorsementDTO.getBussinessType());
            vo.setAllowCheckItem(endorsementDTO.getChecklistsFlag());
            List<EndorsementItemDTO> itemDTOList = itemDTOMap.get(endorsementDTO.getId());
            if (itemDTOList == null) {
                itemDTOList = new ArrayList<>();
            }
            List<EndorsementItemVO> allowList = new ArrayList<>();
            CustomsBookDTO customsBookDTO = customsBookService.findById(endorsementDTO.getAccountBookId());
            for (EndorsementItemDTO endorsementItemDTO : itemDTOList) {
                if (LongUtil.isNone(endorsementItemDTO.getChecklistId())) {
                    EndorsementItemVO endorsementItemVO = new EndorsementItemVO();
                    endorsementItemVO.setId(endorsementItemDTO.getId());
                    endorsementItemVO.setAccountBookNo(customsBookDTO.getBookNo());
                    endorsementItemVO.setProductId(endorsementItemDTO.getProductId());
                    endorsementItemVO.setGoodsSeqNo(endorsementItemDTO.getGoodsSeqNo());
                    allowList.add(endorsementItemVO);
                }
            }
            // 无可关联表体
            if (CollectionUtils.isEmpty(allowList)) {
                continue;
            }
            //二线出区，一票多车拿掉,一票多车的核注清单，第一次关联核放单时，不允许勾选全部表体信息
            if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equalsIgnoreCase(endorsementDTO.getBussinessType())) {
                vo.setAllowCheckTable(true);
            } else {
                if (endorsementDTO.getChecklistsFlag()) {
                    if (new Integer(allowList.size()).equals(itemDTOList.size())) {
                        vo.setAllowCheckTable(false);
                    } else {
                        vo.setAllowCheckTable(true);
                    }
                }
            }
            vo.setItemList(allowList);
            checklistVOS.add(vo);
        }
        log.info("[op:EndorsementController-listForChecklist] result={}", JSON.toJSONString(checklistVOS));
        return checklistVOS;
    }

    @ApiOperation(value = "下拉允许做核放的核注清单")
    @GetMapping("/listForChecklist")
    public List<EndorsementChecklistVO> listForChecklistByType() {
        return this.listForChecklistByType(null);
    }

    @ApiOperation(value = "剔除异常")
    @PostMapping("/eliminateException")
    public Map<String, Object> eliminateException(@RequestBody IdParam idParam) throws ArgsErrorException {
        EndorsementDTO endorsementDTO = endorsementService.findById(idParam.getId());

        String informationDesc = endorsementDTO.getInformationDesc();
        if (StringUtils.isEmpty(endorsementDTO.getInformationDesc())) {
            throw new ArgsErrorException("异常信息为空，请联系技术处理");
        } else {
            if (endorsementDTO.getInformationDesc().contains("[8987]") && endorsementDTO.getInformationDesc().contains("超过底账允许数量")) {
                //找出异常料号
                List<Integer> errorItems = new ArrayList<>();
                if (EndorsementOrderStatus.EXCEPTION.getCode().equals(endorsementDTO.getStatus())) {
                    errorItems = getErrorItemList(endorsementDTO.getInformationDesc());
                }
                List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.preItemView(idParam.getId());
                int i = 1;
                //从清单商品表体中根据异常料号找到对应的清单主键id
                List<Long> bookItemIds = new ArrayList<>();
                Map<String, String> exceptionItemMap = new HashMap<>();
                for (EndorsementItemDTO itemDTO : endorsementItemDTOList) {
                    if (!CollectionUtils.isEmpty(errorItems) && errorItems.contains(i)) {
                        CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(endorsementDTO.getAccountBookId(), itemDTO.getGoodsSeqNo(), itemDTO.getProductId());
                        bookItemIds.add(customsBookItemDTO.getId());
                        exceptionItemMap.put(customsBookItemDTO.getGoodsSeqNo(), customsBookItemDTO.getProductId());
                    }
                    i++;
                }
                List<ExportItemDTO> errorExportItemDTOList = new ArrayList<>();
                List<ExportItemDTO> exportItemDTOS = exportOrderService.listItemByEndorsementId(idParam.getId());
                for (ExportItemDTO exportItemDTO : exportItemDTOS) {
                    List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
                    for (ExportSkuInfo exportSkuInfo : skuInfoList) {
                        if (bookItemIds.contains(exportSkuInfo.getBookItemId())) {
                            errorExportItemDTOList.add(exportItemDTO);
                            break;
                        }
                    }
                }
                Map<String, Object> resultMap = new HashMap<>();
                resultMap.put("exceptionItem", exceptionItemMap);
                resultMap.put("exceptionMailNos", errorExportItemDTOList.stream().map(ExportItemDTO::getMailNo).collect(Collectors.toList()));
                return resultMap;
            } else if (this.patternMatch(INVENTORY_NO_EXIST_PATTERN, informationDesc)) {
                return this.getMailNoList(informationDesc);
            } else if (patternMatch(CHANGE_HS_PATTERN, informationDesc)) {
                return this.getMailNoListByHzInventoryNoList(informationDesc);
            } else {
                throw new ArgsErrorException("异常信息不符合剔除规则，请联系技术处理");
            }
        }
    }

    /**
     * 通过核注预录入编号获取信息
     *
     * @param exception
     * @return
     */
    private Map<String, Object> getMailNoListByHzInventoryNoList(String exception) {

        final Map<String, List<String>> hzInventoryInfoMap = this.getHzInventoryInfoMap(exception);
        log.info("改HS,导致的核注异常：{}", hzInventoryInfoMap);
        if (CollectionUtils.isEmpty(hzInventoryInfoMap)) {
            return Collections.emptyMap();
        }

        Map<String, String> allExceptionItemMap = new HashMap<>();
        List<String> allMailNoList = new ArrayList<>();
        hzInventoryInfoMap.forEach((k, v) -> {

            final EndorsementDTO byPreOrderNo = endorsementService.findByPreOrderNo(k);
            if (byPreOrderNo == null) {
                return;
            }
            log.info("核注单信息：{}", byPreOrderNo);
            Long bookId = byPreOrderNo.getAccountBookId();
            List<CustomsBookItemDTO> bookItemDTOList = customsBookItemService.findByBookIdAndSeqNoList(bookId, v);
            if (CollectionUtils.isEmpty(bookItemDTOList)) {
                return;
            }
            Map<Long, CustomsBookItemDTO> bookItemDTOMap = bookItemDTOList.stream()
                    .collect(Collectors.toMap(CustomsBookItemDTO::getId, Function.identity(), (v1, v2) -> v1));

            // 获取此核注单下的出库单子项数据
            List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementId(byPreOrderNo.getId());
            if (CollectionUtils.isEmpty(exportItemDTOList)) {
                return;
            }
            log.info("出库单子项数据：{}", exportItemDTOList);
            List<String> mailNoList = exportItemDTOList.stream()
                    .filter(z -> {
                        List<ExportSkuInfo> exportSkuInfoList = JSON.parseArray(z.getSkuJson(), ExportSkuInfo.class);
                        boolean matched = false;
                        for (ExportSkuInfo skuInfo : exportSkuInfoList) {
                            CustomsBookItemDTO bookItemDTO = bookItemDTOMap.get(skuInfo.getBookItemId());
                            if (bookItemDTO != null) {
                                matched = true;
                                allExceptionItemMap.put(bookItemDTO.getGoodsSeqNo(), bookItemDTO.getProductId());
                            }
                        }
                        return matched;
                    })
                    .map(ExportItemDTO::getMailNo)
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(mailNoList)) {
                return;
            }
            log.info("改HS,导致的核注异常匹配到的运单编号：{}", mailNoList);
            allMailNoList.addAll(mailNoList);
        });

        return new HashMap<String, Object>() {{
            put("exceptionItem", allExceptionItemMap);
            put("exceptionMailNos", allMailNoList);
        }};
    }

    /**
     * 清单不存在异常
     *
     * @param inventoryNotException
     * @return
     */
    private Map<String, Object> getMailNoList(String inventoryNotException) {

        List<String> inventoryNoList = this.getInventoryNoList(inventoryNotException);
        log.info("清单编号不存在或清单不是放行状态异常，匹配到的清单编号为：{}", inventoryNoList);

        if (CollectionUtils.isEmpty(inventoryNoList)) {
            return Collections.emptyMap();
        }
        // 兼容之前返回，key 序号，value 料号
        Map<String, String> exceptionItemMap = new HashMap<>();
        List<String> mailNoList = new ArrayList<>();
        if (inventoryByEs) {
            for (String inventoryNo : inventoryNoList) {
                List<CustomsInventoryDTO> customsInventoryDTOList = customsInventoryService.getInventoryByInventoryNos(inventoryNo);
                if (CollectionUtils.isEmpty(customsInventoryDTOList)) {
                    continue;
                }
                CustomsInventoryDTO customsInventoryDTO = customsInventoryDTOList.get(0);
                mailNoList.add(customsInventoryDTO.getLogisticsNo());
                List<CustomsInventoryItemExtraEsDTO> itemExtras = customsInventoryDTO.getItemExtras();
                if (!CollectionUtils.isEmpty(itemExtras)) {
                    itemExtras.forEach(z -> {
                        exceptionItemMap.put(z.getGoodsSeqNo(), z.getProductId());
                    });
                }
            }
        } else {
            for (String inventoryNo : inventoryNoList) {
                CustomsInventoryDTO customsInventoryDTO = customsInventoryService.findByInventoryNo90Days(inventoryNo);
                mailNoList.add(customsInventoryDTO.getLogisticsNo());
                final List<CustomsInventoryItemDTO> itemByInventoryId90Days = customsInventoryService.getItemByInventoryId90Days(customsInventoryDTO.getId());
                if (!CollectionUtils.isEmpty(itemByInventoryId90Days)) {
                    itemByInventoryId90Days.forEach(z -> {
                        CustomsInventoryItemExtra itemExtra = JSON.parseObject(z.getExtraJson(), CustomsInventoryItemExtra.class);
                        if (itemExtra == null) {
                            return;
                        }
                        exceptionItemMap.put(itemExtra.getGoodsSeqNo(), itemExtra.getProductId());
                    });
                }
            }
        }

        return new HashMap<String, Object>() {{
            put("exceptionItem", exceptionItemMap);
            put("exceptionMailNos", mailNoList);
        }};
    }


    /**
     * 改HS问题
     * <p>
     * ["核注清单[202100000049793282]中备案序号为[596]的商品编码[2202990099]与对应跨境清单商品或退货申请单商品编码[2106909090]不一致，核对检查失败。",
     * "核注清单[202100000049793282]中备案序号为[6087]的商品编码[3401191000]与对应跨境清单商品或退货申请单商品编码[3402209000]不一致，核对检查失败。"]
     *
     * @param exception
     * @return key：核注清单，即核注的预录入编号；value：金二序号
     */
    private static Map<String, List<String>> getHzInventoryInfoMap(String exception) {


        Matcher matcher = CHANGE_HS_PATTERN.matcher(exception);

        Map<String, List<String>> hzInventoryMap = new HashMap<>();
        while (matcher.find()) {
            String message = matcher.group();
            String hzInventoryNo = message.substring(message.indexOf("核注清单[") + 5, message.indexOf("]中备案序号"));
            String goodsSeqno = message.substring(message.indexOf("备案序号为[") + 6, message.indexOf("]的商品编码"));

            List<String> goodsSeqnoList = hzInventoryMap.get(hzInventoryNo);
            if (CollectionUtils.isEmpty(goodsSeqnoList)) {
                goodsSeqnoList = new ArrayList<>();
            }
            goodsSeqnoList.add(goodsSeqno);
            hzInventoryMap.put(hzInventoryNo, goodsSeqnoList);
        }
        return hzInventoryMap;
    }


    /**
     * 获取清单编号
     * ["清单编号[29242021I451078216]不存在或清单不是放行状态;",
     * "清单编号[29242021I451297618]不存在或清单不是放行状态;",
     * "清单编号[29242021I472866208]不存在或清单不是放行状态;",
     * "清单编号[29242021I483501926]不存在或清单不是放行状态;"]
     *
     * @return
     */
    private static List<String> getInventoryNoList(String exception) {

        Matcher matcher = INVENTORY_NO_EXIST_PATTERN.matcher(exception);
        List<String> inventoryNoList = new ArrayList<>();
        while (matcher.find()) {
            String message = matcher.group();
            String inventoryNo = message.substring(message.indexOf("[") + 1, message.indexOf("]"));
            inventoryNoList.add(inventoryNo);
        }
        return inventoryNoList;
    }


    /**
     * 匹配
     *
     * @param exception
     * @return
     */
    private static boolean patternMatch(Pattern pattern, String exception) {

        if (StringUtils.isEmpty(exception) || pattern == null) {
            return false;
        }
        return pattern.matcher(exception).find();
    }

    /**
     * 同步清单出区
     *
     * @param type
     * @param status
     * @param createTime
     * @param endTime
     */
    @ApiOperation(value = "同步清单出区")
    @PostMapping("/synchronizeInventory")
    public Response<String> synchronizeInventory(String type, String status, String createTime, String endTime) {
        try {
            endorsementService.synchronizeInventory(type, status, createTime, endTime);
        } catch (Exception e) {
            return new Response<>("同步失败");
        }
        return new Response<>("同步成功");
    }

    @PostMapping("/retryCallbackERP")
    public String retryCallbackERP(String sn, String action) {
        endorsementService.retryCallBackERPStatus(sn, action);
        return "重试成功";
    }


    @ApiOperation(value = "接受CCS趴数据接口数据")
    @PostMapping("/ccsData")
    public Response<String> ccsDataReturn(@RequestBody OrderEndorsementCheckResult param) {
        try {
            log.info("ccsDataReturn:{}", JSON.toJSON(param));
            if (Objects.isNull(param)) {
                throw new ArgsErrorException("参数不能为空");
            }
            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String jobId = param.getJobId();
            String jsonArray = stringRedisTemplate.opsForValue().get("ENDORSEMENT" + jobId);
            List<String> realNoList = JSONObject.parseArray(jsonArray, String.class);
            if (Objects.isNull(realNoList) || realNoList.size() == 0) {
                throw new ArgsErrorException("redis找不到对应的数据");
            }
            String strArray = stringRedisTemplate.opsForValue().get("ENDORSEMENTREDISDATA" + jobId);
            OrderEndorsementCheckRequest.EndorsementRedisData endorsementRedisData = JSONObject.parseObject(strArray, OrderEndorsementCheckRequest.EndorsementRedisData.class);
            List<OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO> abnormalEndorsementCheckList = new ArrayList<>();
            List<String> companyStrs = new ArrayList<>();
            for (String strRealOrderNo : realNoList) {
                if (StringUtils.isEmpty(strRealOrderNo)) continue;
                EndorsementDTO endorsementDTO = endorsementService.findByRealOrderNo(strRealOrderNo);
                if (Objects.isNull(endorsementDTO)) continue;
                CompanyDTO companyDTO = companyService.findById(endorsementDTO.getDeclareCompanyId());
                if (param.getEndorsementReceiveDataList().stream().noneMatch(a -> Objects.equals(a.realOrderNo, endorsementDTO.getRealOrderNo()))) {
                    OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO abnormalEndorsementCheckDTO = new OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO();
                    abnormalEndorsementCheckDTO.setSnNo(endorsementDTO.getSn());
                    abnormalEndorsementCheckDTO.setPreOrderNo(endorsementDTO.getRealOrderNo());
                    abnormalEndorsementCheckDTO.setCompanyName(companyDTO.getName());
                    abnormalEndorsementCheckDTO.setAbnormalType("海关系统无对应核注清单编号");
                    abnormalEndorsementCheckDTO.setCompanyCode(companyDTO.getCode());
                    abnormalEndorsementCheckList.add(abnormalEndorsementCheckDTO);
                }
                if (param.getEndorsementReceiveDataList().stream().anyMatch(a -> Objects.equals(a.realOrderNo, endorsementDTO.getRealOrderNo()) && !Objects.equals(a.customsStatus, "已核扣"))) {
                    OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO abnormalEndorsementCheckDTO = new OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO();
                    abnormalEndorsementCheckDTO.setSnNo(endorsementDTO.getSn());
                    abnormalEndorsementCheckDTO.setPreOrderNo(endorsementDTO.getRealOrderNo());
                    abnormalEndorsementCheckDTO.setCompanyName(companyDTO.getName());
                    abnormalEndorsementCheckDTO.setAbnormalType("海关系统核注清单未核扣");
                    abnormalEndorsementCheckDTO.setCompanyCode(companyDTO.getCode());
                    abnormalEndorsementCheckList.add(abnormalEndorsementCheckDTO);
                }
                companyStrs.add(companyDTO.getCode());
            }
            log.info("CCSData B单异常数据条数:{}", abnormalEndorsementCheckList.size());
            if (abnormalEndorsementCheckList.size() > 0) {
                if (StringUtils.isEmpty(wechatUrl)) {
                    wechatUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=24b6ca44-234f-4c1c-887a-3d480bed48c6";
                }
                sendWechatMessage(companyStrs, abnormalEndorsementCheckList, endorsementRedisData, null, "1", wechatUrl);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream, OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO.class)
                        .sheet("单一窗口核注清单编号核对情况(以CCS为准)")
                        .doWrite(abnormalEndorsementCheckList);
                //发送企微消息
                WechatNotifyUtils.sendFileMsg(wechatUrl, byteArrayOutputStream,
                        String.format("单一窗口核注清单编号核对情况(以CCS为准)%s.xlsx", cn.hutool.core.date.DateTime.now().toString()));
            }
        } catch (Exception e) {
            log.error("接受CCS数据失败：{}", e.getMessage(), e);
            return new Response<>("接受CCS数据失败");
        }
        return new Response<>("接受CCS数据成功");
    }

    @ApiOperation(value = "接受海关数据趴数据接口数据")
    @PostMapping("/customsData")
    public Response<String> customsDataReturn(@RequestBody OrderEndorsementCheckResult param) {
        try {
            log.info("customsDataReturn:{}", JSON.toJSON(param));
            if (Objects.isNull(param)) {
                throw new ArgsErrorException("参数不能为空");
            }
            List<OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO> abnormalEndorsementCheckList = new ArrayList<>();
            //过滤已核扣的数据
            List<OrderEndorsementCheckResult.EndorsementReceiveData> receiveFinishDataList = param.getEndorsementReceiveDataList().stream().filter(d -> d.customsStatus.equals("已核扣")).collect(Collectors.toList());
            List<String> realNoDataList = new ArrayList<>();
            if (!StringUtils.isEmpty(fliterDataList)) {
                realNoDataList = Lists.newArrayList(fliterDataList.split(",")).stream().map(String::valueOf).collect(Collectors.toList());
            }
            for (String str : realNoDataList) {
                List<OrderEndorsementCheckResult.EndorsementReceiveData> filterDataList = receiveFinishDataList.stream().filter(d -> d.realOrderNo.equals(str)).collect(Collectors.toList());
                if (filterDataList.size() > 0) {
                    receiveFinishDataList.remove(receiveFinishDataList.get(0));
                }
            }
            List<String> companyStrs = new ArrayList<>();
            for (OrderEndorsementCheckResult.EndorsementReceiveData httpEndors : receiveFinishDataList) {
                EndorsementDTO endorsementDTO = endorsementService.findByRealOrderNo(httpEndors.getRealOrderNo());
                if (Objects.isNull(endorsementDTO)) {
                    OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO abnormalEndorsementCheckDTO = new OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO();
                    abnormalEndorsementCheckDTO.setSnNo(httpEndors.getSnNo());
                    abnormalEndorsementCheckDTO.setPreOrderNo(httpEndors.getRealOrderNo());
                    abnormalEndorsementCheckDTO.setCompanyName(httpEndors.getCompanyName());
                    abnormalEndorsementCheckDTO.setAbnormalType("清关系统无对应核注清单编号");
                    abnormalEndorsementCheckDTO.setCompanyCode(httpEndors.getCompanyCode());
                    abnormalEndorsementCheckList.add(abnormalEndorsementCheckDTO);
                }
                if (!Objects.isNull(endorsementDTO) && !Objects.equals(endorsementDTO.getStatus(), "FINISH")) {
                    OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO abnormalEndorsementCheckDTO = new OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO();
                    abnormalEndorsementCheckDTO.setSnNo(httpEndors.getSnNo());
                    abnormalEndorsementCheckDTO.setPreOrderNo(httpEndors.getRealOrderNo());
                    abnormalEndorsementCheckDTO.setCompanyName(httpEndors.getCompanyName());
                    abnormalEndorsementCheckDTO.setAbnormalType("清关系统核注清单未完成");
                    abnormalEndorsementCheckDTO.setCompanyCode(httpEndors.getCompanyCode());
                    abnormalEndorsementCheckList.add(abnormalEndorsementCheckDTO);
                }
                companyStrs.add(httpEndors.getCompanyCode());
            }
            //发送微信消息
            if (abnormalEndorsementCheckList.size() > 0) {
                if (StringUtils.isEmpty(wechatUrl)) {
                    wechatUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=24b6ca44-234f-4c1c-887a-3d480bed48c6";
                }
                sendWechatMessage(companyStrs, abnormalEndorsementCheckList, null, param, "2", wechatUrl);
                ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
                EasyExcel.write(byteArrayOutputStream, OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO.class)
                        .sheet("单一窗口核注清单编号核对情况(以海关为准)")
                        .doWrite(abnormalEndorsementCheckList);
                //发送企微消息
                WechatNotifyUtils.sendFileMsg(wechatUrl, byteArrayOutputStream,
                        String.format("单一窗口核注清单编号核对情况(以海关为准)%s.xlsx", cn.hutool.core.date.DateTime.now().toString()));
            }
        } catch (Exception e) {
            log.error("接受海关数据失败：{}", e.getMessage(), e);
            return new Response<>("接受海关数据失败");
        }
        return new Response<>("接受海关数据成功");
    }


    private static String endorsMentMdMessage(String currentDate, String startDate, String endDate, String content) {
        StringBuilder builder = new StringBuilder();
        builder.append("<font color=\\\"warning\\\">**" + "核注清单编号校准情况" + "**</font>\\n\n");
        builder.append(String.format("校准时间：%s\n", currentDate));
        builder.append(String.format("校准日期：%s 至 %s", startDate, endDate) + "\r\n");
        builder.append(content + "\n").append("\n");
        builder.append("<font color=\\\"warning\\\">" + "预警!存在单一窗口和清关服务系统的核注清单不一致,请及时核对!!!" + "</font>\\n");
        return builder.toString();
    }

    public void sendWechatMessage(List<String> companyStrs,
                                  List<OrderEndorsementCheckResult.AbnormalEndorsementCheckDTO> abnormalEndorsementCheckList,
                                  OrderEndorsementCheckRequest.EndorsementRedisData endorsementRedisData,
                                  OrderEndorsementCheckResult param, String sendType, String sendUrl) {

        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        StringBuilder message = new StringBuilder();
        companyStrs = companyStrs.stream().distinct().collect(Collectors.toList());
        for (String strCom : companyStrs) {
//            CompanyDTO companyDTO = companyService.findByCode(strCom);
            CompanyDTO companyDTO = companyService.findBySpecialClientCode(strCom);
            if (Objects.isNull(companyDTO)) {
                continue;
            }
            Integer errorNum = abnormalEndorsementCheckList.stream().filter(d -> d.companyCode.equals(strCom)).collect(Collectors.toList()).size();
            message.append("> **清关企业**：").append(companyDTO.getName()).append("\n")
                    .append("**账册编号**：").append("" + companyDTO.getCode() + "").append("\n")
                    .append("**清关系统和海关系统不一致：**：").append("**" + errorNum + "").append("单**\n\n");

        }
        String content;
        if (sendType.equals("1")) {
            content = endorsMentMdMessage(dateFormat.format(new Date()), endorsementRedisData.getStartDate(), endorsementRedisData.getEndDate()
                    , message.toString());
        } else {
            content = endorsMentMdMessage(dateFormat.format(new Date()), param.getStartDate(), param.getEndDate()
                    , message.toString());
        }
        WechatNotifyUtils.wechatNotifyMd(sendUrl, null, content);
    }

    @PostMapping("/endorsement/deletedApply")
    public RpcResult deletedApply(@RequestBody IdsParam idsParam) {
        endorsementService.deletedApply(idsParam.getIdList());
        return RpcResult.success();
    }

    @GetMapping("/detail")
    public EndorsementDetailResVO detail(Long id) {
        EndorsementDetailResVO resVO = new EndorsementDetailResVO();
        EndorsementDTO endorsementDTO = endorsementService.findById(id);
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        // 表头 & 基础数据
        buildHeadDetail(id, resVO, endorsementDTO, companyDTOMap);

        // 表体
        buildItemDetailList(endorsementDTO, resVO, companyDTOMap);

        // 料件表体
        if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSINESS_SIMPLE_PROCESSING.getCode())) {
            buildItemGoodsDetailList(endorsementDTO, resVO);
        }

        // 轨迹日志
        buildTrackLogList(id, resVO);
        return resVO;
    }

    private void buildItemGoodsDetailList(EndorsementDTO endorsementDTO, EndorsementDetailResVO resVO) {
        List<InventoryOrderItemGoodsDTO> inventoryOrderItemGoodsDTOS = inventoryOrderInfoService.listItemGoodsByInventoryId(endorsementDTO.getInventoryOrderId());
        List<EndorsementItemGoodsDTO> endorsementItemGoodsDTOList = endorsementService.listItemGoodsById(endorsementDTO.getId());
        Map<String, EndorsementItemDTO> endorsementItemMap = endorsementItemGoodsDTOList.stream()
                .map(z -> BeanUtils.copyProperties(z, EndorsementItemDTO.class))
                .collect(Collectors.toMap(EndorsementItemDTO::getProductId, Function.identity(), (v1, v2) -> v1));
        Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        int idx = 1;
        List<EndorsementItemDetailVO> endorsementItemGoodsDetailVOList = new ArrayList<>();
        for (InventoryOrderItemGoodsDTO itemDTO : inventoryOrderItemGoodsDTOS) {
            InventoryOrderItemDTO inventoryOrderItemDTO = BeanUtils.copyProperties(itemDTO, InventoryOrderItemDTO.class);
            EndorsementItemDetailVO itemDetailVO = getEndorsementItemDetailVO(inventoryOrderItemDTO, idx, countryMap, endorsementItemMap, currencyMap, uomMap);
            idx++;
            endorsementItemGoodsDetailVOList.add(itemDetailVO);
        }
        resVO.setEndorsementItemGoodsDetailVOList(endorsementItemGoodsDetailVOList);
    }

    private EndorsementItemDetailVO getEndorsementItemDetailVO(InventoryOrderItemDTO itemDTO, int idx, Map<String, String> countryMap,
                                                               Map<String, EndorsementItemDTO> endorsementItemMap,
                                                               Map<String, String> currencyMap, Map<String, String> uomMap) {
        EndorsementItemDetailVO itemDetailVO = new EndorsementItemDetailVO();
        itemDetailVO.setSerializeNo(idx);
        itemDetailVO.setIsNew("new".equalsIgnoreCase(itemDTO.getIsNew()) ? "是" : "否");
        itemDetailVO.setDeclareFormItemSeqNo(itemDTO.getDeclareFormItemSeqNo());
        itemDetailVO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
        itemDetailVO.setProductId(itemDTO.getProductId());
        itemDetailVO.setHsCode(itemDTO.getHsCode());
        itemDetailVO.setGoodsName(itemDTO.getGoodsName());
        itemDetailVO.setGoodsModel(itemDTO.getGoodsModel());
        itemDetailVO.setOriginCountry(itemDTO.getOriginCountry());
        String destinationCountryDesc = itemDTO.getDestinationCountryDesc();
        if (StringUtil.isEmpty(destinationCountryDesc)) {
            if (countryMap.containsKey(itemDTO.getDestinationCountry())) {
                destinationCountryDesc = countryMap.get(itemDTO.getDestinationCountry());
            }
        }
        itemDetailVO.setDestinationCountry(destinationCountryDesc);
        itemDetailVO.setUnit(itemDTO.getUnit());
        itemDetailVO.setDeclareUnitQfy(itemDTO.getDeclareUnitQfy());
        itemDetailVO.setDeclarePrice(itemDTO.getDeclarePrice());
        itemDetailVO.setDeclareTotalPrice(itemDTO.getDeclareTotalPrice());
        itemDetailVO.setCurrency(itemDTO.getCurrency());
        itemDetailVO.setFirstUnit(itemDTO.getFirstUnit());
        itemDetailVO.setSecondUnit(itemDTO.getSecondUnit());
        itemDetailVO.setFirstUnitQfy(itemDTO.getFirstUnitQfy());
        itemDetailVO.setSecondUnitQfy(itemDTO.getSecondUnitQfy());
        // 核注单展示需要乘数量
        if (Objects.nonNull(itemDTO.getTotalGrossWeight())) {
            itemDetailVO.setGrossWeight(itemDTO.getTotalGrossWeight());
        } else {
            itemDetailVO.setGrossWeight(itemDTO.getGrossWeight().multiply(itemDTO.getDeclareUnitQfy()));
        }
        if (Objects.nonNull(itemDTO.getTotalNetWeight())) {
            itemDetailVO.setNetWeight(itemDTO.getTotalNetWeight());
        } else {
            itemDetailVO.setNetWeight(itemDTO.getNetweight().multiply(itemDTO.getDeclareUnitQfy()));
        }
        //增加危化品标志
        if (!StringUtils.isEmpty(itemDTO.getDangerousFlag())) {
            if (itemDTO.getDangerousFlag().equals("0")) {
                itemDetailVO.setDangerousFlag("否");
            } else if (itemDTO.getDangerousFlag().equals("1")) {
                itemDetailVO.setDangerousFlag("是");
            }
        }
        itemDetailVO.setCustomsCallBackSeqNo(itemDTO.getCustomsCallBackSeqNo());
        itemDetailVO.setDeclareCustomsGoodsSeqNo(String.valueOf(idx));
        itemDetailVO.setAvoidTaxMethod(itemDTO.getAvoidTaxMethod());
        itemDetailVO.setVersion(itemDTO.getOrderVersion());
        itemDetailVO.setModfMark(EndorsementModfMarkEnums.NOT_MODIFY.getDesc());
        GoodsSourceEnums goodsSourceEnums = GoodsSourceEnums.getEnums(itemDTO.getGoodsSource());
        itemDetailVO.setGoodsSource(itemDTO.getGoodsSource());
        itemDetailVO.setGoodsSourceDesc(goodsSourceEnums != null ? goodsSourceEnums.getDesc() : "");
        if (endorsementItemMap.containsKey(itemDTO.getProductId())) {
            EndorsementItemDTO endorsementItemDTO = endorsementItemMap.get(itemDTO.getProductId());
            itemDetailVO.setDeclareFormItemSeqNo(endorsementItemDTO.getDeclareFormItemSeqNo());
            itemDetailVO.setModfMark(EndorsementModfMarkEnums.getEnums(endorsementItemDTO.getModfMark()).getDesc());
            itemDetailVO.setCustomsCallBackSeqNo(endorsementItemDTO.getCustomsCallBackSeqNo());
            // 核注单存的时候已经乘数量了
            itemDetailVO.setGrossWeight(endorsementItemDTO.getGrossWeight());
            itemDetailVO.setNetWeight(endorsementItemDTO.getNetWeight());
            if (StrUtil.isNotBlank(endorsementItemDTO.getExtraJson())) {
                EndorsementExtraDTO extraDTO = JSON.parseObject(endorsementItemDTO.getExtraJson(), EndorsementExtraDTO.class);
                itemDetailVO.setFirstUnit(extraDTO.getFirstUnit());
                itemDetailVO.setFirstUnitQfy(extraDTO.getFirstUnitQfy());
                itemDetailVO.setSecondUnit(extraDTO.getSecondUnit());
                itemDetailVO.setSecondUnitQfy(extraDTO.getSecondUnitQfy());
                itemDetailVO.setDeclarePrice(extraDTO.getDeclarePrice());
                itemDetailVO.setUnit(extraDTO.getDeclareUnit());
                itemDetailVO.setCurrency(extraDTO.getCurrency());
            }
            itemDetailVO.setDeclareUnitQfy(endorsementItemDTO.getDeclareUnitQfy());
            itemDetailVO.setHsCode(endorsementItemDTO.getHsCode());
            itemDetailVO.setGoodsName(endorsementItemDTO.getRecordProductName());
            goodsSourceEnums = GoodsSourceEnums.getEnums(endorsementItemDTO.getGoodsSource());
            itemDetailVO.setGoodsSourceDesc(goodsSourceEnums != null ? goodsSourceEnums.getDesc() : "");
        }
//                itemDetailVO.setDeclareTableNo();
//                itemDetailVO.setRemark();

        if (countryMap.containsKey(itemDTO.getOriginCountry())) {
            itemDetailVO.setOriginCountry(countryMap.get(itemDTO.getOriginCountry()));
        }
        if (uomMap.containsKey(itemDetailVO.getUnit())) {
            itemDetailVO.setUnit(uomMap.get(itemDetailVO.getUnit()));
        }
        if (uomMap.containsKey(itemDetailVO.getFirstUnit())) {
            itemDetailVO.setFirstUnit(uomMap.get(itemDetailVO.getFirstUnit()));
        }
        if (uomMap.containsKey(itemDetailVO.getSecondUnit())) {
            itemDetailVO.setSecondUnit(uomMap.get(itemDetailVO.getSecondUnit()));
        }
        if (currencyMap.containsKey(itemDetailVO.getCurrency())) {
            itemDetailVO.setCurrency(currencyMap.get(itemDetailVO.getCurrency()));
        }
        return itemDetailVO;
    }

    private void buildHeadDetail(Long id, EndorsementDetailResVO resVO, EndorsementDTO endorsementDTO, Map<Long, CompanyDTO> companyDTOMap) {
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(endorsementDTO.getExportOrderId());
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(endorsementDTO.getAccountBookId());
        Map<Long, List<ChecklistDTO>> checklistMap = checklistService.getChecklistByEndorsementList(Collections.singletonList(id));
        Map<String, String> transportModeMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.TRANSPORT_MODE.getValue());

        resVO.setId(endorsementDTO.getId());
        resVO.setSn(endorsementDTO.getSn());
        resVO.setPreOrderNo(endorsementDTO.getPreOrderNo());
        resVO.setRealOrderNo(endorsementDTO.getRealOrderNo());
        EndorsementBussiness businessEnum = EndorsementBussiness.getEnum(endorsementDTO.getBussinessType());
        resVO.setBussinessType(endorsementDTO.getBussinessType());
        resVO.setBussinessTypeDesc(businessEnum.getDesc());
        if (Objects.equals(1, endorsementDTO.getIeFlag())) {
            resVO.setIeFlag("出区");
        }
        if (Objects.equals(2, endorsementDTO.getIeFlag())) {
            resVO.setIeFlag("入区");
        }
        if (Objects.nonNull(exportOrderDTO)) {
            resVO.setExportOrderSn(exportOrderDTO.getSn());
        }

        if (checklistMap.containsKey(id)) {
            List<String> checklistSnList = checklistMap.get(id).stream().map(ChecklistDTO::getSn).collect(Collectors.toList());
            resVO.setChecklistSnList(checklistSnList);
        }
        resVO.setCreateTime(endorsementDTO.getCreateTime());
        resVO.setUpdateTime(endorsementDTO.getUpdateTime());
        resVO.setFinishTime(endorsementDTO.getFinishTime());
        resVO.setStatus(endorsementDTO.getStatus());
        resVO.setStatusDesc(EndorsementOrderStatus.getEnum(endorsementDTO.getStatus()).getDesc());
        resVO.setCustomsStatusDesc(EndorsementCustomsStatus.getEnum(endorsementDTO.getCustomsStatus()).getDesc());
        resVO.setStockChangeFlag(Boolean.TRUE.equals(endorsementDTO.getStockChangeEnableLogic()) ? "是" : "否");
        resVO.setCustomsEntrySeqNo(endorsementDTO.getCustomsEntrySeqNo());
        if (StrUtil.isNotBlank(endorsementDTO.getGenerateDeclareStatus())) {
            resVO.setGenerateDeclareStatusDesc(Objects.equals("1", endorsementDTO.getGenerateDeclareStatus()) ? "成功" : "失败");
        }
        if (endorsementDTO.getCreateBy() != null) {
            Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(Collections.singletonList(endorsementDTO.getCreateBy().longValue()));
            if (userRpcResultMap.containsKey(endorsementDTO.getCreateBy().longValue())) {
                resVO.setCreateBy(userRpcResultMap.get(endorsementDTO.getCreateBy().longValue()).getUserName());
            }
        }
        resVO.setCustomsInvtType(endorsementDTO.getInvtType());
        resVO.setSupvMode(endorsementDTO.getSupvMode());
        resVO.setInputerIcCard(endorsementDTO.getIcCardNo());
        resVO.setRemark(endorsementDTO.getRemark());
        if (Objects.nonNull(customsBookResVo)) {
            resVO.setCustomsBookNo(customsBookResVo.getBookNo());
            resVO.setCustomsCode(customsBookResVo.getCustomsAreaCode());
            CompanyDTO areaCompanyDTO = getCompanyDTOById(companyDTOMap, customsBookResVo.getAreaCompanyId());
            resVO.setTradeCompanyId(customsBookResVo.getAreaCompanyId());
            resVO.setTradeCompanyName(areaCompanyDTO.getName());
            resVO.setTradeCompanyUSCC(areaCompanyDTO.getUniformSocialCreditCode());
            resVO.setTradeCompanyCode(areaCompanyDTO.getCode());

            resVO.setProcessCompanyId(customsBookResVo.getAreaCompanyId());
            resVO.setProcessCompanyName(areaCompanyDTO.getName());
            resVO.setProcessCompanyUSCC(areaCompanyDTO.getUniformSocialCreditCode());
            resVO.setProcessCompanyCode(areaCompanyDTO.getCode());

            resVO.setDeclareCompanyId(customsBookResVo.getAreaCompanyId());
            resVO.setDeclareCompanyName(areaCompanyDTO.getName());
            resVO.setDeclareCompanyUSCC(areaCompanyDTO.getUniformSocialCreditCode());
            resVO.setDeclareCompanyCode(areaCompanyDTO.getCode());

            resVO.setInputCompanyId(customsBookResVo.getAreaCompanyId());
            resVO.setInputCompanyName(areaCompanyDTO.getName());
            resVO.setInputCompanyUSCC(areaCompanyDTO.getUniformSocialCreditCode());
            resVO.setInputCompanyCode(areaCompanyDTO.getCode());
            resVO.setEntryExitCustoms(customsBookResVo.getCustomsAreaCode());
        }
        resVO.setOrderType(endorsementDTO.getOrderType());
        resVO.setTransportMode(endorsementDTO.getTransportMode());
        resVO.setTransportModeDesc(transportModeMap.getOrDefault(endorsementDTO.getTransportMode(), ""));
    }

    private void buildTrackLogList(Long id, EndorsementDetailResVO resVO) {
        List<EndorsementTrackLogDTO> endorsementTrackLogDTOS = endorsementService.listTrackLogById(id);
        List<EndorsementTrackLogVO> trackLogVOList = endorsementTrackLogDTOS.stream().map(i -> {
            EndorsementTrackLogVO logVO = new EndorsementTrackLogVO();
            logVO.setStatus(i.getStatus());
            logVO.setStatusDesc(EndorsementOrderStatus.getEnum(i.getStatus()).getDesc());
            logVO.setLogInfo(i.getLogInfo());
            logVO.setCallbackDetail(i.getCallbackDetail());
            logVO.setCreateTime(i.getCreateTime());
            logVO.setOperator(i.getOperator());
            return logVO;
        }).collect(Collectors.toList());
        resVO.setEndorsementTrackLogVOList(trackLogVOList);
    }

    private void buildItemDetailList(EndorsementDTO endorsementDTO, EndorsementDetailResVO resVO, Map<Long, CompanyDTO> companyDTOMap) {
        BigDecimal totalNetWeight = BigDecimal.ZERO;
        BigDecimal totalGrossWeight = BigDecimal.ZERO;
        BigDecimal totalDeclareQty = BigDecimal.ZERO;

        Map<String, CustomsDictionaryDTO> currencyDTOMap = new HashMap<>();
        Map<String, CustomsDictionaryDTO> uomDTOMap = new HashMap<>();
        Map<String, CustomsDictionaryDTO> countryDTOMap = new HashMap<>();
        List<EndorsementItemDetailVO> endorsementItemDetailVOList = new ArrayList<>();
        Map<String, String> customsDecTypeMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CUSTOMS_DEC_TYPE.getValue());
        if (Objects.equals(endorsementDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemById(endorsementDTO.getId());
            for (EndorsementItemDTO dto : endorsementItemDTOS) {
                endorsementItemDetailVOList.add(buildItemDetailVO(dto));
                totalNetWeight = totalNetWeight.add(dto.getNetWeight());
                totalGrossWeight = totalGrossWeight.add(dto.getGrossWeight());
                totalDeclareQty = totalDeclareQty.add(dto.getDeclareUnitQfy());
            }
        } else if (EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode().equals(endorsementDTO.getBussinessType())) {
            // 二线出区
            List<EndorsementItemDTO> endorsementItemDTOList;
            if (endorsementDTO.getStatus().equals(EndorsementOrderStatus.INIT.getCode())
                    || endorsementDTO.getStatus().equals(EndorsementOrderStatus.EXCEPTION.getCode())
                    || endorsementDTO.getStatus().equals(EndorsementOrderStatus.DECALRING.getCode())) {
                endorsementItemDTOList = endorsementService.preItemView(endorsementDTO.getId());
            } else {
                endorsementItemDTOList = endorsementService.listItemById(endorsementDTO.getId());
            }
//            List<InventoryOrderItemDTO> inventoryOrderItemDTOList = inventoryOrderInfoService.findListByInvenOrderId(endorsementDTO.getInventoryOrderId());
//            Map<String, InventoryOrderItemDTO> inventoryOrderItemDTOMap = inventoryOrderItemDTOList.stream()
//                    .collect(Collectors.toMap(InventoryOrderItemDTO::getProductId, Function.identity(), (v1, v2) -> v1));

            List<ExportItemDTO> exportItemDTOS = exportOrderService.listItemByEndorsementId(endorsementDTO.getId());
            // 账册id 关联运单号
            Map<Long, List<String>> itemMailNoMap = new HashMap<>();
            for (ExportItemDTO exportItemDTO : exportItemDTOS) {
                List<ExportSkuInfo> skuInfoList = JSON.parseArray(exportItemDTO.getSkuJson(), ExportSkuInfo.class);
                for (ExportSkuInfo exportSkuInfo : skuInfoList) {
                    if (!itemMailNoMap.containsKey(exportSkuInfo.getBookItemId())) {
                        itemMailNoMap.put(exportSkuInfo.getBookItemId(), new ArrayList<>());
                    }
                    itemMailNoMap.get(exportSkuInfo.getBookItemId()).add(exportItemDTO.getMailNo());
                }
            }
            List<Long> customsBookItemIdList = new ArrayList<>(itemMailNoMap.keySet());
            List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findById(customsBookItemIdList);
            Map<String, CustomsBookItemDTO> customsBookItemDTOMap = customsBookItemDTOList.stream()
                    .collect(Collectors.toMap(i -> i.getCustomsBookId() + "_" + i.getGoodsSeqNo() + "_" + i.getProductId(), Function.identity(), (v1, v2) -> v1));
            for (EndorsementItemDTO itemDTO : endorsementItemDTOList) {
                CustomsBookItemDTO customsBookItemDTO = customsBookItemDTOMap.get(endorsementDTO.getAccountBookId() + "_" + itemDTO.getGoodsSeqNo() + "_" + itemDTO.getProductId());
                EndorsementItemDetailVO detailVO = new EndorsementItemDetailVO();
                detailVO.setSerializeNo(endorsementItemDTOList.indexOf(itemDTO) + 1);
                detailVO.setIsNew("否");
                detailVO.setGoodsSeqNo(itemDTO.getGoodsSeqNo());
                detailVO.setProductId(itemDTO.getProductId());
                detailVO.setHsCode(itemDTO.getHsCode());
                detailVO.setGoodsName(itemDTO.getGoodsName());
                detailVO.setDeclareUnitQfy(itemDTO.getDeclareUnitQfy());
                detailVO.setGrossWeight(itemDTO.getGrossWeight());
                detailVO.setNetWeight(itemDTO.getNetWeight());
                detailVO.setModfMark(EndorsementModfMarkEnums.getEnums(itemDTO.getModfMark()).getDesc());
                detailVO.setCustomsCallBackSeqNo(itemDTO.getCustomsCallBackSeqNo());
                detailVO.setGoodsSource(itemDTO.getGoodsSource());
                GoodsSourceEnums goodsSourceEnums = GoodsSourceEnums.getEnums(itemDTO.getGoodsSource());
                detailVO.setGoodsSourceDesc(goodsSourceEnums != null ? goodsSourceEnums.getDesc() : "");

                detailVO.setTagList(new ArrayList<>());
                List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(itemDTO.getItemTag());
                for (Integer orderItemTag : orderItemTags) {
                    OrderItemTagEnum anEnum = OrderItemTagEnum.getEnum(orderItemTag);
                    if (anEnum != null) {
                        detailVO.getTagList().add(anEnum.getDesc());
                    }
                }

//                InventoryOrderItemDTO inventoryOrderItemDTOtemp = inventoryOrderItemDTOMap.get(itemDTO.getProductId());
//                if (inventoryOrderItemDTOtemp != null && !StringUtils.isEmpty(inventoryOrderItemDTOtemp.getDangerousFlag())) {
//                    //增加危化品标志
//                    if (inventoryOrderItemDTOtemp.getDangerousFlag().equals("0")) {
//                        detailVO.setDangerousFlag("否");
//                    } else if (inventoryOrderItemDTOtemp.getDangerousFlag().equals("1")) {
//                        detailVO.setDangerousFlag("是");
//                    }
//                }
                detailVO.setDestinationCountry("中国"); // 最终目的国默认 中国

                if (Objects.nonNull(customsBookItemDTO)) {
                    detailVO.setPackageMailNoList(itemMailNoMap.get(customsBookItemDTO.getId()));
                    detailVO.setGoodsModel(customsBookItemDTO.getGoodsModel());
                    detailVO.setOriginCountry(customsBookItemDTO.getOriginCountry());
                    detailVO.setUnit(customsBookItemDTO.getGoodsUnit());
                    detailVO.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                    detailVO.setDeclareTotalPrice(customsBookItemDTO.getDeclarePrice().multiply(itemDTO.getDeclareUnitQfy()));
                    detailVO.setCurrency(customsBookItemDTO.getCurrCode());
                    detailVO.setFirstUnit(customsBookItemDTO.getFirstUnit());
                    detailVO.setSecondUnit(customsBookItemDTO.getSecondUnit());
                    detailVO.setFirstUnitQfy(customsBookItemDTO.getFirstUnitAmount());
                    detailVO.setSecondUnitQfy(customsBookItemDTO.getSecondUnitAmount());
                    if (StringUtil.isNotBlank(customsBookItemDTO.getOriginCountry())) {
                        CustomsDictionaryDTO country = getBaseDataByTypeAndCode(countryDTOMap, DataDictionaryTypeEnums.COUNTRY, customsBookItemDTO.getOriginCountry());
                        detailVO.setOriginCountry(country.getName());
                    }
                    if (StringUtil.isNotBlank(customsBookItemDTO.getGoodsUnit())) {
                        CustomsDictionaryDTO unit = getBaseDataByTypeAndCode(uomDTOMap, DataDictionaryTypeEnums.UOM, customsBookItemDTO.getGoodsUnit());
                        detailVO.setUnit(unit.getName());
                    }
                    if (StringUtil.isNotBlank(customsBookItemDTO.getFirstUnit())) {
                        CustomsDictionaryDTO firstUnit = getBaseDataByTypeAndCode(uomDTOMap, DataDictionaryTypeEnums.UOM, customsBookItemDTO.getFirstUnit());
                        detailVO.setFirstUnit(firstUnit.getName());
                    }
                    if (StringUtil.isNotBlank(customsBookItemDTO.getSecondUnit())) {
                        CustomsDictionaryDTO secondUnit = getBaseDataByTypeAndCode(uomDTOMap, DataDictionaryTypeEnums.UOM, customsBookItemDTO.getSecondUnit());
                        detailVO.setSecondUnit(secondUnit.getName());
                    }
                    if (StringUtil.isNotBlank(customsBookItemDTO.getCurrCode())) {
                        CustomsDictionaryDTO currency = getBaseDataByTypeAndCode(currencyDTOMap, DataDictionaryTypeEnums.CURRENCY, customsBookItemDTO.getCurrCode());
                        detailVO.setCurrency(currency.getName());
                    }
                }

//                detailVO.setDeclareCustomsGoodsSeqNo();
//                detailVO.setDeclareTableNo();
//                detailVO.setVersion();
//                detailVO.setRemark();

                totalNetWeight = totalNetWeight.add(itemDTO.getNetWeight());
                totalGrossWeight = totalGrossWeight.add(itemDTO.getGrossWeight());
                totalDeclareQty = totalDeclareQty.add(itemDTO.getDeclareUnitQfy());
                endorsementItemDetailVOList.add(detailVO);
            }
        } else {
            InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(endorsementDTO.getInventoryOrderId());
            if (Objects.nonNull(inventoryOrderInfoDTO)) {
                resVO.setInventoryOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
                if (StringUtil.isNotBlank(inventoryOrderInfoDTO.getTransportMode())) {
                    resVO.setTransportMode(inventoryOrderInfoDTO.getTransportMode());
                    Inv101TrspModecd trspModecdEnum = Inv101TrspModecd.getEnum(inventoryOrderInfoDTO.getTransportMode());
                    if (!Objects.equals(Inv101TrspModecd.NULL, trspModecdEnum)) {
                        resVO.setTransportModeDesc(trspModecdEnum.getDesc());
                    } else {
                        CustomsDictionaryDTO transportDTO = customsDictionaryService
                                .findByCodeAndType(inventoryOrderInfoDTO.getTransportMode(), DataDictionaryTypeEnums.TRANSPORT_MODE.getValue());
                        if (Objects.nonNull(transportDTO)) {
                            resVO.setTransportModeDesc(transportDTO.getName());
                        }
                    }
                }
                resVO.setEntryExitCustoms(inventoryOrderInfoDTO.getEntryExitCustoms());
                CustomsDictionaryDTO shipmentCountryDTO = getBaseDataByTypeAndCode(countryDTOMap, DataDictionaryTypeEnums.COUNTRY, inventoryOrderInfoDTO.getShipmentCountry());
                if (Objects.nonNull(shipmentCountryDTO)) {
                    resVO.setShipmentCountry(shipmentCountryDTO.getName());
                }
                resVO.setRltEndorsementNo(inventoryOrderInfoDTO.getAssociatedEndorsementNo());
                if (Objects.nonNull(inventoryOrderInfoDTO.getInAccountBook()) || Objects.nonNull(inventoryOrderInfoDTO.getOutAccountBook())) {
                    resVO.setRltCustomsBookNo(Objects.nonNull(inventoryOrderInfoDTO.getInAccountBook())
                            ? inventoryOrderInfoDTO.getInAccountBook() : inventoryOrderInfoDTO.getOutAccountBook());
                }
                resVO.setCustomsEntryNo(inventoryOrderInfoDTO.getCustomsEntryNo());
                if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsFlag())) {
                    resVO.setCustomsFlag(InventoryCustomsFlagEnum.getEnum(inventoryOrderInfoDTO.getCustomsFlag()).getDesc());
                }
                if (Objects.nonNull(inventoryOrderInfoDTO.getDeclarationFlag())) {
                    resVO.setDeclarationFlag(InventoryDeclarationEnum.getEnum(inventoryOrderInfoDTO.getDeclarationFlag()).getDesc());
                }
                if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsEntryType())) {
                    resVO.setCustomsEntryType(customsDecTypeMap.get(inventoryOrderInfoDTO.getCustomsEntryType()));
                }
                resVO.setCustomsType(InventoryCustomsTypeEnums.getEnum(inventoryOrderInfoDTO.getCustomsType()).getDesc());
                if (Objects.nonNull(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId())) {
                    CompanyDTO corrCusDeclareCompanyDTO = getCompanyDTOById(companyDTOMap, inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
                    resVO.setCorrCusDeclareCompanyId(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
                    resVO.setCorrCusDeclareCompanyName(corrCusDeclareCompanyDTO.getName());
                    resVO.setCorrCusDeclareCompanyUSCC(corrCusDeclareCompanyDTO.getUniformSocialCreditCode());
                    resVO.setCorrCusDeclareCompanyCode(corrCusDeclareCompanyDTO.getCode());
                }
                if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId())) {
                    CompanyDTO rltCusInnerSFHRCompanyDTO = getCompanyDTOById(companyDTOMap, inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
                    resVO.setRltCusInnerSFHRCompanyId(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
                    resVO.setRltCusInnerSFHRCompanyName(rltCusInnerSFHRCompanyDTO.getName());
                    resVO.setRltCusInnerSFHRCompanyUSCC(rltCusInnerSFHRCompanyDTO.getUniformSocialCreditCode());
                    resVO.setRltCusInnerSFHRCompanyCode(rltCusInnerSFHRCompanyDTO.getCode());
                }
                if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusXFDYCompanyId())) {
                    CompanyDTO rltCusXFDYCompanyDTO = getCompanyDTOById(companyDTOMap, inventoryOrderInfoDTO.getRltCusXFDYCompanyId());
                    resVO.setRltCusXFDYCompanyId(inventoryOrderInfoDTO.getRltCusXFDYCompanyId());
                    resVO.setRltCusXFDYCompanyName(rltCusXFDYCompanyDTO.getName());
                    resVO.setRltCusXFDYCompanyUSCC(rltCusXFDYCompanyDTO.getUniformSocialCreditCode());
                    resVO.setRltCusXFDYCompanyCode(rltCusXFDYCompanyDTO.getCode());
                }
                if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusDeclareCompanyId())) {
                    CompanyDTO rltCusDeclareCompanyDTO = getCompanyDTOById(companyDTOMap, inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
                    resVO.setRltCusDeclareCompanyId(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
                    resVO.setRltCusDeclareCompanyName(rltCusDeclareCompanyDTO.getName());
                    resVO.setRltCusDeclareCompanyUSCC(rltCusDeclareCompanyDTO.getUniformSocialCreditCode());
                    resVO.setRltCusDeclareCompanyCode(rltCusDeclareCompanyDTO.getCode());
                }

                List<InventoryOrderItemDTO> inventoryOrderItemDTOList = inventoryOrderInfoService.findListByInvenOrderId(inventoryOrderInfoDTO.getId());
                List<EndorsementItemDTO> endorsementItemDTOList = endorsementService.listItemById(endorsementDTO.getId());
                Map<String, EndorsementItemDTO> endorsementItemMap = endorsementItemDTOList.stream()
                        .collect(Collectors.toMap(EndorsementItemDTO::getProductId, Function.identity(), (v1, v2) -> v1));
                Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
                Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
                Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
                int idx = 1;
                for (InventoryOrderItemDTO itemDTO : inventoryOrderItemDTOList) {
                    EndorsementItemDetailVO itemDetailVO = getEndorsementItemDetailVO(itemDTO, idx, countryMap,
                            endorsementItemMap, currencyMap, uomMap);
                    // 保税加工 单耗版本号
                    if (endorsementDTO.getBussinessType().equals(EndorsementBussiness.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode())) {
                        List<ProcessTradeBookConsumptionDTO> consumptionDTOList = processTradeBookService
                                .findConsumptionByProductIdAndBookId(2, itemDetailVO.getProductId(), endorsementDTO.getAccountBookId());
                        if (Objects.nonNull(consumptionDTOList)) {
                            for (ProcessTradeBookConsumptionDTO consumptionDTO : consumptionDTOList) {
                                EndorsementItemDetailVO duplicateItemDetailVO = new EndorsementItemDetailVO();
                                org.springframework.beans.BeanUtils.copyProperties(itemDetailVO, duplicateItemDetailVO);
                                duplicateItemDetailVO.setVersion(consumptionDTO.getConsumptionVersionNo());
                                duplicateItemDetailVO.setSerializeNo(idx++);
                                endorsementItemDetailVOList.add(duplicateItemDetailVO);
                            }
                            idx--;
                        } else {
                            endorsementItemDetailVOList.add(itemDetailVO);
                        }
                        totalNetWeight = totalNetWeight.add(itemDetailVO.getNetWeight());
                        totalGrossWeight = totalGrossWeight.add(itemDetailVO.getGrossWeight());
                        totalDeclareQty = totalDeclareQty.add(itemDetailVO.getDeclareUnitQfy());
                    } else {
                        totalNetWeight = totalNetWeight.add(itemDetailVO.getNetWeight());
                        totalGrossWeight = totalGrossWeight.add(itemDetailVO.getGrossWeight());
                        totalDeclareQty = totalDeclareQty.add(itemDetailVO.getDeclareUnitQfy());
                        endorsementItemDetailVOList.add(itemDetailVO);
                    }
                    idx++;
                }
            }
        }
        resVO.setTotalDeclareQty(totalDeclareQty);
        resVO.setTotalNetWeight(totalNetWeight);
        resVO.setTotalGrossWeight(totalGrossWeight);
        resVO.setEndorsementItemDetailVOList(endorsementItemDetailVOList);
    }

    private EndorsementItemDetailVO buildItemDetailVO(EndorsementItemDTO dto) {
        Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        EndorsementItemDetailVO detailVO = new EndorsementItemDetailVO();
        detailVO.setSerializeNo(dto.getSerialNumber());
        detailVO.setIsNew("否");
        detailVO.setGoodsSeqNo(dto.getGoodsSeqNo());
        detailVO.setProductId(dto.getProductId());
        detailVO.setHsCode(dto.getHsCode());
        detailVO.setGoodsName(dto.getGoodsName());
        detailVO.setGoodsModel(dto.getGoodsModel()); // EndorsementItemDTO中没有对应字段
        detailVO.setOriginCountry(countryMap.getOrDefault(dto.getOriginCountry(), dto.getOriginCountry())); // 需要从其他地方获取
        detailVO.setDestinationCountry(countryMap.getOrDefault(dto.getDestinationCountry(), dto.getDestinationCountry())); // 需要从其他地方获取
        detailVO.setUnit(uomMap.getOrDefault(dto.getDeclareUnit(), dto.getDeclareUnit()));
        detailVO.setDeclareUnitQfy(dto.getDeclareUnitQfy());
        detailVO.setDeclarePrice(dto.getDeclarePrice());
        detailVO.setDeclareTotalPrice(dto.getDeclareTotalPrice());
        detailVO.setCurrency(currencyMap.getOrDefault(dto.getCurrency(), dto.getCurrency()));
        detailVO.setFirstUnit(uomMap.getOrDefault(dto.getFirstUnit(), dto.getFirstUnit()));
        detailVO.setSecondUnit(uomMap.getOrDefault(dto.getSecondUnit(), dto.getSecondUnit()));
        detailVO.setFirstUnitQfy(dto.getFirstUnitQfy());
        detailVO.setSecondUnitQfy(dto.getSecondUnitQfy());
        detailVO.setGrossWeight(dto.getGrossWeight());
        detailVO.setNetWeight(dto.getNetWeight());
        detailVO.setModfMark(dto.getModfMark());
        if (!StringUtils.isEmpty(dto.getDangerousFlag())) {
            if (dto.getDangerousFlag().equals("0")) {
                detailVO.setDangerousFlag("否");
            } else if (dto.getDangerousFlag().equals("1")) {
                detailVO.setDangerousFlag("是");
            }
        }
        detailVO.setCustomsCallBackSeqNo(dto.getCustomsCallBackSeqNo());
        detailVO.setDeclareCustomsGoodsSeqNo(String.valueOf(dto.getSerialNumber())); // EndorsementItemDTO中没有对应字段
        detailVO.setAvoidTaxMethod(dto.getAvoidTaxMethod()); // EndorsementItemDTO中没有对应字段
        detailVO.setVersion(dto.getVersion()); // EndorsementItemDTO中没有对应字段
        detailVO.setRemark(dto.getRemark()); // EndorsementItemDTO中没有对应字段
        detailVO.setPackageMailNoList(null);
        detailVO.setTagList(new ArrayList<>());
        List<Integer> orderItemTags = OrderItemTagEnum.getOrderItemTags(dto.getItemTag());
        for (Integer orderItemTag : orderItemTags) {
            OrderItemTagEnum anEnum = OrderItemTagEnum.getEnum(orderItemTag);
            if (anEnum != null) {
                detailVO.getTagList().add(anEnum.getDesc());
            }
        }
        detailVO.setDeclareFormItemSeqNo(dto.getDeclareFormItemSeqNo());
        detailVO.setGoodsSource(dto.getGoodsSource());
        GoodsSourceEnums goodsSourceEnums = GoodsSourceEnums.getEnums(dto.getGoodsSource());
        detailVO.setGoodsSourceDesc(goodsSourceEnums != null ? goodsSourceEnums.getDesc() : "");
        return detailVO;
    }

    private CompanyDTO getCompanyDTOById(Map<Long, CompanyDTO> companyDTOMap, Long companyId) {
        if (!companyDTOMap.containsKey(companyId)) {
            CompanyDTO companyDTO = companyService.findById(companyId);
            if (Objects.isNull(companyDTO)) {
                throw new ArgsInvalidException("企业不存在");
            }
            if (Objects.nonNull(companyDTO.getSpecialClientCode())) {
                companyDTO.setCode(companyDTO.getSpecialClientCode());
            }
            companyDTOMap.put(companyId, companyDTO);
        }
        return companyDTOMap.get(companyId);
    }

    private CustomsDictionaryDTO getBaseDataByTypeAndCode(Map<String, CustomsDictionaryDTO> dataMap, DataDictionaryTypeEnums type, String code) {
        if (dataMap.containsKey(code)) {
            return dataMap.get(code);
        }
        CustomsDictionaryDTO data = customsDictionaryService.findByCodeAndType(code, type.getValue());
        if (Objects.isNull(data)) {
            log.error("code=" + code + ",type=" + type.getDesc() + "不存在");
        } else {
            dataMap.put(code, data);
        }
        return data;
    }

    @PostMapping("/pagingEbInvEs")
    public ListVO<EndorsementEbInvPagingResVO> pagingEbInvEs(@RequestBody EndorsementEbInvSearch search) {
        EndorsementDTO endorsementDTO = endorsementService.findById(search.getId());
        if (Objects.isNull(endorsementDTO)) {
            throw new ArgsInvalidException("核注单不存在");
        }
        if (Objects.equals(endorsementDTO.getOrderType(), EndorsementOrderTypeEnums.EXCEL_IMPORT.getCode())) {
            ListVO<EndorsementRelationDTO> paging = endorsementService.pagingRelation(search);
            ListVO<EndorsementEbInvPagingResVO> result = new ListVO<>();
            result.setDataList(paging.getDataList().stream().map(i -> {
                EndorsementEbInvPagingResVO resVO = new EndorsementEbInvPagingResVO();
                resVO.setId(String.valueOf(i.getId()));
                resVO.setInventoryNo(i.getInventoryNo());
                resVO.setDeclareOrderNo(i.getDeclareOrderNo());
                resVO.setLogisticsNo(i.getLogisticsNo());
                resVO.setCustomsInventorySn(i.getCustomsInventorySn());
                return resVO;
            }).collect(Collectors.toList()));
            result.setPage(paging.getPage());
            return result;
        }
        SingleInvtOrderSearch invSearch = new SingleInvtOrderSearch();
        invSearch.setPageSize(search.getPageSize());
        invSearch.setCurrentPage(search.getCurrentPage());
        invSearch.setQueryInfo(search.getQueryInfo());
        invSearch.setQueryType(search.getQueryType());

        List<String> customsInventorySnList = new ArrayList<>();
        if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
            List<ExportItemDTO> exportItemDTOList = exportOrderService.listItemByEndorsementId(search.getId());
            customsInventorySnList = exportItemDTOList.stream().map(ExportItemDTO::getCustomsInventorySn).collect(Collectors.toList());
            invSearch.setCustomsInventorySnList(customsInventorySnList);
        } else if (Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_REFUND_INAREA.getCode())) {
            List<InventoryOrderRelationDTO> invenOrderRelationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(endorsementDTO.getInventoryOrderId());
            List<String> logisticsNoList = invenOrderRelationDTOList.stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.toList());
            List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryService.listByLogistics90Days(logisticsNoList);
            customsInventorySnList = customsInventoryDTOS.stream().map(CustomsInventoryDTO::getSn).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(customsInventorySnList)) {
            // 清单编号为空时不请求分页查询，直接返回结果
            ListVO<EndorsementEbInvPagingResVO> result = new ListVO<>();
            result.setDataList(new ArrayList<>());
            PageResult pageResult = new PageResult();
            pageResult.setTotalCount(0);
            pageResult.setTotalPage(0);
            pageResult.setCurrentPage(search.getCurrentPage());
            pageResult.setPageSize(search.getPageSize());
            result.setPage(pageResult);
            return result;
        }

        invSearch.setCustomsInventorySnList(customsInventorySnList);
        ListVO<CustomsSingleInventoryEsDTO> invPaging = customsInventoryService.pagingES(invSearch);
        List<CustomsSingleInventoryEsDTO> invDataList = invPaging.getDataList();
        List<EndorsementEbInvPagingResVO> dataList = invDataList.stream().map(i -> {
            EndorsementEbInvPagingResVO resVO = new EndorsementEbInvPagingResVO();
            resVO.setInventoryNo(i.getInventoryNo());
            resVO.setDeclareOrderNo(i.getDeclareOrderNo());
            resVO.setLogisticsNo(i.getLogisticsNo());
            resVO.setCustomsInventorySn(i.getSn());
            resVO.setId(i.getId());
            return resVO;
        }).collect(Collectors.toList());
        ListVO<EndorsementEbInvPagingResVO> result = new ListVO<>();
        result.setDataList(dataList);
        result.setPage(invPaging.getPage());
        return result;
    }

    @PostMapping("/addEbInvBatch")
    public EndorsementEbInvEditResVO addEbInvBatch(@RequestBody EndorsementEbInvAddParam addParam) {
        return endorsementService.addEbInvBatch(addParam, true);
    }

    @PostMapping("/addEbInvBatchPreView")
    public EndorsementEbInvEditResVO addEbInvBatchPreView(@RequestBody EndorsementEbInvAddParam addParam) {
        return endorsementService.addEbInvBatch(addParam, false);
    }

    @PostMapping("/deleteEbInvBatch")
    public EndorsementEbInvEditResVO deleteEbInvBatch(@RequestBody EndorsementEbInvDeleteParam deleteParam) {
        return endorsementService.deleteEbInvBatch(deleteParam, true);
    }

    @PostMapping("/deleteEbInvBatchPreView")
    public EndorsementEbInvEditResVO deleteEbInvBatchPreView(@RequestBody EndorsementEbInvDeleteParam deleteParam) {
        return endorsementService.deleteEbInvBatch(deleteParam, false);
    }

    @PostMapping("/editDetail")
    public void editDetail(@RequestBody EndorsementEditDetailReqVO reqVO) {
        endorsementService.editDetail(reqVO);
    }

    //测试模拟京东回执 返回
    @PostMapping("/mock/jdReceiveOutRegion")
    public Response<String> jdReceiveOutRegion(@RequestBody EndorsementSearch search) {
        if (Objects.isNull(search) || StringUtils.isEmpty(search.getRealOrderNos())) {
            return new Response<>(-1, "请输入正确的核注清单编号");
        }
        try {
            EndorsementDTO endorsementDTO = endorsementService.findByRealOrderNo(search.getRealOrderNos());
            if (Objects.isNull(endorsementDTO)) {
                return new Response<>(-1, "找不到对应的核注单,核注清单编号：" + search.getRealOrderNos());
            }
            if (!Objects.equals(endorsementDTO.getBussinessType(), EndorsementBussiness.BUSSINESS_SECONDE_OUT.getCode())) {
                return new Response<>(-1, "核注清单业务类型不是二线出区,核注清单编号：" + search.getRealOrderNos());
            }

            if (!Objects.equals(endorsementDTO.getStatus(), EndorsementOrderStatus.FINISH.getCode())) {
                return new Response<>(-1, "核注清单状态不是清关完成,核注清单编号：" + search.getRealOrderNos());
            }
            CustomsBookDTO customsBookDTO = customsBookService.findById(endorsementDTO.getAccountBookId());
            if (Objects.isNull(customsBookDTO) || Objects.isNull(customsBookDTO.getBookTag())) {
                return new Response<>(-1, "请设置京东出区回传,账册编号：" + customsBookDTO.getBookNo());
            }
            Integer bookTag = customsBookDTO.getBookTag();
            List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(bookTag);
            if (!bookTagList.contains(CustomsBookTagEnums.JD_RECEIVE_OUT_REGIN.getCode())) {
                return new Response<>(-1, "请设置京东出区回传,账册编号：" + customsBookDTO.getBookNo());
            }
            endorsementService.createMockPushReceiveOutRegionMsg(endorsementDTO, Integer.valueOf(search.getStatus()));
        } catch (Exception e) {
            return new Response<>(-1, "京东出区回传失败：" + e.getMessage());
        }
        return new Response<>("回传成功");
    }

    public void importExcel(EndorsementImportSubmit submit) {
        if (!EndorsementBussiness.getAllowImportBusinessList().contains(EndorsementBussiness.getEnum(submit.getBusinessType()))) {
            throw new ArgsInvalidException("业务类型暂不支持");
        }

        try {
            // 第一步：解析Excel文件
            InputStream inputStream = DownLoadUtil.downloadNet(submit.getFileUrl());
            if (inputStream == null) {
                throw new ArgsInvalidException("文件下载失败");
            }

            // 读取第一个sheet：表体(成品)
            List<EndorsementItemExcelVO> productList = EasyExcel.read(inputStream)
                    .headRowNumber(1)
                    .head(EndorsementItemExcelVO.class)
                    .sheet(0)
                    .doReadSync();

            // 重新获取输入流读取第二个sheet：保税电商清单
            inputStream = DownLoadUtil.downloadNet(submit.getFileUrl());
            List<EndorsementEbInvExcelVO> ebInvList = EasyExcel.read(inputStream)
                    .headRowNumber(1)
                    .head(EndorsementEbInvExcelVO.class)
                    .sheet(1)
                    .doReadSync();

            // 第二步：数据校验
            if (CollUtil.isEmpty(productList)) {
                throw new ArgsInvalidException("表体（成品）为空");
            }
            if (CollUtil.isEmpty(ebInvList)) {
                throw new ArgsInvalidException("跨境电商清单为空");
            }
            Set<String> errorMsgSet = new HashSet<>();
            Set<String> productSet = new HashSet<>();
            productList.forEach(vo -> {
                String errorMsg = ValidatorUtils.doValidator(validator, vo);
                if (StrUtil.isNotBlank(errorMsg)) {
                    List<String> errorMsgList = Splitter.on(",").splitToList(errorMsg);
                    errorMsgSet.addAll(errorMsgList);
                }
                String prdKey = vo.getProductId() + vo.getGoodsSeqNo();
                if (productSet.contains(prdKey)) {
                    errorMsgSet.add("表体存在重复数据：" + vo.getProductId());
                } else {
                    productSet.add(prdKey);
                }
            });

            List<String> inventoryNos = ebInvList.stream().map(EndorsementEbInvExcelVO::getInventoryNo).distinct().collect(Collectors.toList());
            List<EndorsementRelationDTO> oldRelationList = endorsementService.listRelationByInventoryNos(inventoryNos);
            if (CollUtil.isNotEmpty(oldRelationList)) {
                Map<Long, List<EndorsementRelationDTO>> relationMap = oldRelationList.stream().collect(Collectors.groupingBy(EndorsementRelationDTO::getEndorsementId));
                List<EndorsementDTO> endorsementDTOList = endorsementService.findByIdList(new ArrayList<Long>(relationMap.keySet()));
                endorsementDTOList.stream().filter(endorsementDTO ->
                                Objects.equals(submit.getBusinessType(), endorsementDTO.getBussinessType())
                                        && !EndorsementOrderStatus.DISCARD.getCode().equals(endorsementDTO.getStatus()))
                        .forEach(endorsementDTO -> {
                            if (relationMap.containsKey(endorsementDTO.getId())) {
                                List<String> existInventoryNoList = relationMap.get(endorsementDTO.getId()).stream()
                                        .map(EndorsementRelationDTO::getInventoryNo).collect(Collectors.toList());
                                errorMsgSet.add("保税电商清单已存在核注单：【" + endorsementDTO.getSn() + "-" + String.join(",", existInventoryNoList) + "】");
                            }
                        });
            }
            Set<String> inventoryNoSet = new HashSet<>();
            ebInvList.forEach(vo -> {
                String errorMsg = ValidatorUtils.doValidator(validator, vo);
                if (StrUtil.isNotBlank(errorMsg)) {
                    List<String> errorMsgList = Splitter.on(",").splitToList(errorMsg);
                    errorMsgSet.addAll(errorMsgList);
                }
                if (inventoryNoSet.contains(vo.getInventoryNo())) {
                    errorMsgSet.add("保税电商清单存在重复数据：" + vo.getInventoryNo());
                } else {
                    inventoryNoSet.add(vo.getInventoryNo());
                }
            });

            if (CollUtil.isNotEmpty(errorMsgSet)) {
                throw new ArgsInvalidException(String.join(",", errorMsgSet));
            }

            if (ebInvList.size() > 5000) {
                throw new ArgsInvalidException("保税电商清单超过5000，请分批创建核注单");
            }

            if (productList.size() > 1000) {
                throw new ArgsInvalidException("表体超过1000，请分批创建核注单");
            }

            // 第三步：构建核注单对象，调用EndorsementService.importExcel方法
            List<EndorsementItemDTO> itemList = null;
            List<EndorsementRelationDTO> relationList = null;
            itemList = productList.stream()
                    .map(vo -> BeanUtils.copyProperties(vo, EndorsementItemDTO.class))
                    .collect(Collectors.toList());
            relationList = ebInvList.stream()
                    .map(vo -> {
                        EndorsementRelationDTO relationDTO = new EndorsementRelationDTO();
                        relationDTO.setInventoryNo(vo.getInventoryNo());
                        return relationDTO;
                    })
                    .collect(Collectors.toList());
            endorsementService.importExcel(submit.getBusinessType(), submit.getDeclareCompanyId(),
                    submit.getAccountBookId(), itemList, relationList);

        } catch (Exception e) {
            log.error("Excel导入失败：{}", e.getMessage(), e);
            if (e instanceof ArgsInvalidException) {
                throw e;
            }
            throw new ArgsInvalidException("Excel导入失败：" + e.getMessage());
        }
    }
}
