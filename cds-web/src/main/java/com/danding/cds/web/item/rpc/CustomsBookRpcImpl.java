package com.danding.cds.web.item.rpc;

import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.CustomsDistrictCodeParam;
import com.danding.cds.common.model.IdEnableParam;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ThreadContextUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookSearchCondition;
import com.danding.cds.item.api.dto.CustomsBookSubmit;
import com.danding.cds.item.api.dto.param.CustomsBookConfigParam;
import com.danding.cds.item.api.enums.EnableSwitchEnum;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookInfoResVo;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.v2.bean.enums.CustomsBookTagEnums;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.ErpTradeTypeEnums;
import com.danding.cds.v2.bean.enums.StorageAttrEnums;
import com.danding.cds.web.item.vo.CustomsBookVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.client.UCenterClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@DubboService
@Slf4j
public class CustomsBookRpcImpl implements CustomsBookRpc {

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CompanyService companyService;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/paging", desc = "分页查询")
    @PostMapping("/customsBook/paging")
    public RpcResult<ListVO<CustomsBookVO>> paging(CustomsBookSearchCondition condition) {
        ListVO<CustomsBookResVo> pageResult = customsBookService.newPaging(condition);
        ListVO<CustomsBookVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<CustomsBookVO> dataList = new ArrayList<>();
        for (CustomsBookResVo customsBookResVo : pageResult.getDataList()) {
            CustomsBookVO customsBookVO = new CustomsBookVO();
            customsBookVO.setId(customsBookResVo.getId());
            customsBookVO.setBookNo(customsBookResVo.getBookNo());
            customsBookVO.setAreaCompanyId(customsBookResVo.getAreaCompanyId());
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(customsBookResVo.getAreaCompanyId());
            if (companyDTO != null) {
                customsBookVO.setAreaCompanyCode(companyDTO.getCode());
                customsBookVO.setAreaCompanyName(companyDTO.getName());
            }
            customsBookVO.setCustomsDistrictCode(customsBookResVo.getCustomsDistrictCode());
            customsBookVO.setCustomsDistrictName(CustomsDistrictEnum.getEnum(customsBookResVo.getCustomsDistrictCode()).getDesc());
            customsBookVO.setCreateTime(new DateTime(customsBookResVo.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            customsBookVO.setCreateBy("");
            customsBookVO.setRemark(customsBookResVo.getRemark());
            customsBookVO.setEnable(customsBookResVo.getEnable());
            customsBookVO.setCustomsAreaCode(customsBookResVo.getCustomsAreaCode());
            customsBookVO.setCustomsAreaName(customsBookResVo.getCustomsAreaName());
            customsBookVO.setCollaborateEnable(customsBookResVo.getCollaborateEnable());
            customsBookVO.setBookType(customsBookResVo.getBookType());
            customsBookVO.setBookTypeDesc(CustomsBookTypeEnums.getEnums(customsBookResVo.getBookType()).getDesc());
            List<Integer> bookTagList = CustomsBookTagEnums.getBookTag(customsBookResVo.getBookTag());
            customsBookVO.setBookTagList(bookTagList);
            dataList.add(customsBookVO);
        }
        result.setDataList(dataList);
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/customsBook/listAllInUseBookNo", desc = "获取启用保税账册")
    public RpcResult<List<SelectOptionVO>> listAllInUseBookNo() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        List<CustomsBookDTO> result = customsBookService.listAllInUseBooksAuth(accountBookList, null);
        List<SelectOptionVO> selectList = new ArrayList<>();
        SelectOptionVO iItemVO;
        for (CustomsBookDTO dto : result) {
            iItemVO = new SelectOptionVO();
            iItemVO.setId(dto.getId());
            iItemVO.setName(dto.getBookNo());
            selectList.add(iItemVO);
        }
        return RpcResult.success(selectList);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/customsBook/listAllInUseBookNoByNoV2", desc = "获取所有启用保税账册编码(key为bookno)V2")
    public RpcResult<List<SelectOptionVO>> listAllInUseBookNoByNo() {
        List<CustomsBookDTO> result = customsBookService.listAllInUseBooks(null);
        List<SelectOptionVO> selectList = new ArrayList<>();
        SelectOptionVO<String> iItemVO;
        for (CustomsBookDTO dto : result) {
            iItemVO = new SelectOptionVO<String>();
            iItemVO.setId(dto.getBookNo());
            iItemVO.setName(dto.getBookNo());
            selectList.add(iItemVO);
        }
        return RpcResult.success(selectList);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/listAllBookNo", desc = "获取所有保税账册")
    public RpcResult<List<SelectOptionVO>> listAllBookNo() {
        List<SelectOptionVO> optionVOList = this.listBookNo(null, null);
        return RpcResult.success(optionVOList);
    }

    /**
     * 获取账册数据
     *
     * @param accountBookList 账册ID列表
     * @return
     */
    private List<SelectOptionVO> listBookNo(List<Long> accountBookList, Integer bookType) {

        List<CustomsBookDTO> result;
        if (CollectionUtils.isEmpty(accountBookList)) {
            result = customsBookService.listAllBooksByType(bookType);
        } else {
            result = customsBookService.listBooksByIds(accountBookList);
        }
        if (CollectionUtils.isEmpty(result)) {
            return Collections.EMPTY_LIST;
        }
        List<SelectOptionVO> selectList = new ArrayList<>();
        SelectOptionVO iItemVO;
        for (CustomsBookDTO dto : result) {
            iItemVO = new SelectOptionVO();
            iItemVO.setId(dto.getId());
            iItemVO.setName(dto.getBookNo());
            selectList.add(iItemVO);
        }
        return selectList;
    }

    /**
     * 用户账册列表，当前用户所有角色配置的账册
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/listBookNoByAccBookAuth", desc = "获取所有保税账册")
    @UcAccountBookAuthGetAndCheck(addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<List<SelectOptionVO>> listBookNoByAccBookAuth() {

        // 用户权限
        List<Long> ucAccountBookIdList = ThreadContextUtil.getAccountBookList();
        log.info("[CustomsBookRpcImpl-listBookNoByAccBookAuth ucAccountBookIdList={}]", ucAccountBookIdList);
        // 获取数据
        final List<SelectOptionVO> listRpcResult = this.listBookNo(ucAccountBookIdList, null);
        if (CollectionUtils.isEmpty(listRpcResult)) {
            return RpcResult.success(new ArrayList<>());
        }
        List<SelectOptionVO> optionVOList = listRpcResult.stream()
                .filter(z -> CollectionUtils.isEmpty(ucAccountBookIdList) || ucAccountBookIdList.contains(z.getId()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(optionVOList)) {
            return RpcResult.success(new ArrayList<>());
        }
        return RpcResult.success(optionVOList);
    }

    /**
     * 用户账册列表，当前用户所有角色配置的账册
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/effective/listBookNoByAccBookAuth", desc = "获取所有保税账册")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<List<SelectOptionVO>> listEffectiveBookNoByAccBookAuth() {
        return this.listBookNoByAccBookAuth();
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/detail", desc = "详情")
    public RpcResult<CustomsBookDTO> detail(IdParam idParam) {
        return RpcResult.success(customsBookService.findById(idParam.getId()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/upset", desc = "新增/编辑账册(启用、禁用)")
    public RpcResult<Long> upset(CustomsBookSubmit submit) {
        try {
            Long update = customsBookService.update(submit);
            // 基于用户中心实现的方式，在账册编辑时需要通知用户中心维护账册数据
            UCenterClient.dataClient().asynPushData(CommonCons.GROUP_CUSTOMS_BOOK);
            return RpcResult.success(update);
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/enable", desc = "启用、禁用")
    public RpcResult<String> enable(IdEnableParam idEnableParam) {
        try {
            List<Long> idList = idEnableParam.getIdList();
            Integer enable = idEnableParam.getEnable();
            customsBookService.enable(idList, enable);
            // 基于用户中心实现的方式，在账册编辑时需要通知用户中心维护账册数据
            UCenterClient.dataClient().asynPushData(CommonCons.GROUP_CUSTOMS_BOOK);
            return RpcResult.success(enable == 1 ? "启用成功" : "禁用成功");
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/findBookList", desc = "根据口岸编码获取保税账册")
    public RpcResult findBookList(CustomsDistrictCodeParam param) {
        List<CustomsBookInfoResVo> resVos = customsBookService.findBookList(param.getCustomsDistrictCode(), CustomsBookTypeEnums.IMPORT_BONDED_BOOKS.getCode());
        return RpcResult.success(EnumUtils.build(resVos, "id", "bookNo"));
    }

    /**
     * 根据口岸编码获取保税账册
     *
     * @param param
     * @return
     * @path /customsBook/findBookListV2
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/findBookListV2", desc = "根据口岸编码获取保税账册")
    public RpcResult<List<CustomsBookInfoResVo>> findBookListV2(CustomsDistrictCodeParam param) {
        Integer bookType = CustomsBookTypeEnums.IMPORT_BONDED_BOOKS.getCode();
        if (ErpTradeTypeEnums.DUTY_PAID.getCode().equals(param.getTradeType())) { // 完税仓查非保账册
            bookType = CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode();
        }
        List<CustomsBookInfoResVo> resVos = customsBookService.findBookList(param.getCustomsDistrictCode(), bookType);
        return RpcResult.success(resVos);
    }

    /**
     * 根据口岸编码获取保税账册
     *
     * @param param
     * @return
     * @path /customsBook/findBookListV3
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/findBookListV3", desc = "根据口岸编码获取账册")
    public RpcResult<List<CustomsBookInfoResVo>> findBookListAll(CustomsDistrictCodeParam param) {
        List<CustomsBookInfoResVo> resVos = new ArrayList<>();
        if (ErpTradeTypeEnums.DUTY_PAID.getCode().equals(param.getTradeType())) { // 完税仓查非保账册
            resVos = customsBookService.findBookList(param.getCustomsDistrictCode(), CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode());
        } else { //保税的实体仓可带出保税和非保的账册下拉列表
            resVos = customsBookService.findBookList(param.getCustomsDistrictCode());
        }
        return RpcResult.success(resVos);
    }

    @Override
    @SoulClient(path = "/customsBook/updConfigParam", desc = "参数配置")
    @PostMapping("/customsBook/updConfigParam")
    @ApiOperation("参数配置")
    public RpcResult updConfigParam(@RequestBody CustomsBookConfigParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsBookService.updConfigParam(param);
        } catch (ArgsInvalidException e) {
            log.info("updConfigParam error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        }
        return RpcResult.success("配置成功");
    }

    @Override
    @SoulClient(path = "/customsBook/updateBookTag", desc = "标签配置")
    @PostMapping("/customsBook/updateBookTag")
    public RpcResult updateBookTag(CustomsBookConfigParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsBookService.updateBookTag(param);
        } catch (ArgsInvalidException e) {
            log.info("updateBookTag error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        }
        return RpcResult.success("标签配置成功");
    }

    @Override
    @SoulClient(path = "/customsBook/getConfigParam", desc = "参数配置")
    public RpcResult<CustomsBookConfigParam> getConfigParam(IdParam bookId) {
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(bookId.getId());
        if (Objects.isNull(customsBookResVo)) {
            return RpcResult.error("账册id[" + bookId.getId() + "]不存在");
        }
        CustomsBookConfigParam param = new CustomsBookConfigParam();
        param.setId(bookId.getId());
//        param.setCollaborateEnable(customsBookResVo.getCollaborateEnable());
//        param.setStockAutoSwitchEnable(customsBookResVo.getStockAutoSwitchEnable());
//        param.setNegativeStockCheckEnable(customsBookResVo.getNegativeStockCheckEnable());
        param.setStorageAttr(customsBookResVo.getStorageAttr());
//        param.setCarryOverEnable(customsBookResVo.getCarryOverEnable());
//        param.setBookTagList(customsBookResVo.getBookTagList());
        param.setStockListGenerateEnable(customsBookResVo.getStockListGenerateEnable());
        return RpcResult.success(param);
    }

    @Override
    @SoulClient(path = "/customsBook/listEnableSwitch", desc = "启用开关下拉")
    @GetMapping("/customsBook/listEnableSwitch")
    public RpcResult listEnableSwitch() {
        List<EnumUtils> list = EnumUtils.build(EnableSwitchEnum.class, "code", "desc")
                .stream().filter(i -> i.getId() != null).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/listBookType", desc = "账册类型下拉")
    public RpcResult listBookType() {
        List<EnumUtils> list = EnumUtils.build(CustomsBookTypeEnums.class, "code", "desc")
                .stream().filter(i -> !Objects.equals(i.getId(), CustomsBookTypeEnums.NULL.getCode())).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/listBookTags", desc = "账册标签下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listBookTags() {
        try {
            CustomsBookTagEnums[] customsBookTagEnums = CustomsBookTagEnums.values();
            List<SelectOptionVO<Integer>> selectList = new ArrayList<>();
            for (CustomsBookTagEnums dto : customsBookTagEnums) {
                SelectOptionVO<Integer> iItemVO = new SelectOptionVO<>();
                iItemVO.setId(dto.getCode());
                iItemVO.setName(dto.getDesc());
                selectList.add(iItemVO);
            }
            return RpcResult.success(selectList);
        } catch (Exception e) {
            log.error("获取账册标签下拉失败 error={}", e.getMessage(), e);
            return RpcResult.error("获取账册标签下拉失败");
        }
    }

    @Override
    @SoulClient(path = "/customsBook/listBookNoByType", desc = "根据账册类型下拉账册编号")
    @RequestMapping("/customsBook/listBookNoByType")
    @ApiOperation("根据账册类型下拉账册编号")
    public RpcResult<List<SelectOptionVO>> listBookNoByType(Integer bookType) {
        List<SelectOptionVO> list = this.listBookNo(null, bookType);
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/listBookNoFb", desc = "非保账册下拉接口")
    public RpcResult<List<SelectOptionVO>> listBookNoFb() {
        List<SelectOptionVO> list = this.listBookNo(null, CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/findBookByAreaCompanyId", desc = "根据清关企业查询账册id")
    public RpcResult<Long> findBookByAreaCompanyId(Long companyId) {
        try {
            List<CustomsBookResVo> list = customsBookService.findByAreaCompanyId(companyId);
            list = list.stream().filter(l -> Objects.nonNull(l) && Objects.equals(l.getBookType(), CustomsBookTypeEnums.IMPORT_BONDED_BOOKS.getCode())).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(list)) {
                return RpcResult.error("根据清关企业未查询到进口保税账册信息");
            }
            if (list.size() > 1) {
                return RpcResult.error("根据清关企业未查询到多条进口保税账册信息");
            }
            CustomsBookResVo customsBookResVo = list.get(0);
            return RpcResult.success(customsBookResVo.getId());
        } catch (Exception e) {
            log.error("findBookByAreaCompanyId error={}", e.getMessage(), e);
            return RpcResult.error("查询失败");

        }
    }


    @Override
    @SoulClient(path = "/customsBook/listStorageAttr", desc = "仓储性质下拉")
    @GetMapping("/customsBook/listStorageAttr")
    @ApiOperation("仓储性质下拉")
    public RpcResult listStorageAttr() {
        List<EnumUtils> list = EnumUtils.build(StorageAttrEnums.class, "code", "desc")
                .stream().filter(i -> !StorageAttrEnums.NULL.getCode().equals(i.getId())).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/listBookByStorageAttr", desc = "根据仓储性质账册下拉")
    @GetMapping("customsBook/listBookByStorageAttr")
    @ApiOperation("根据仓储性质账册下拉")
    public RpcResult<List<SelectOptionVO<Long>>> listBookByStorageAttr(Integer storageAttr) {
        List<CustomsBookResVo> customsBookResVos = customsBookService.listByStorageAttr(storageAttr);
        List<SelectOptionVO<Long>> list = customsBookResVos.stream().map(i -> {
            SelectOptionVO<Long> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i.getId());
            selectOptionVO.setName(i.getBookNo());
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    @Override
    @SoulClient(path = "/customsBook/listBookByAreaCompany", desc = "根据区内企业账册下拉")
    @GetMapping("customsBook/listBookByAreaCompany")
    @ApiOperation("根据区内企业账册下拉")
    public RpcResult<List<SelectOptionVO<Long>>> listBookByAreaCompany(Long areaCompanyId) {
        List<CustomsBookDTO> customsBookDTOList = customsBookService.findListByCompanyId(areaCompanyId);
        List<SelectOptionVO<Long>> list = customsBookDTOList.stream().map(i -> {
            SelectOptionVO<Long> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i.getId());
            selectOptionVO.setName(i.getBookNo());
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 加工贸易账册下拉
     *
     * @return
     * @path /customsBook/listBookWithProcessTrade
     */
    @Override
    @SoulClient(path = "/customsBook/listBookWithProcessTrade", desc = "加工贸易账册下拉")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<List<SelectOptionVO<Long>>> listBookWithProcessTrade() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        List<CustomsBookDTO> customsBookDTOList = customsBookService.listAllInUseBooksAuth(accountBookList, CustomsBookTypeEnums.PROCESSING_TRADE_BOOKS.getCode());
        List<SelectOptionVO<Long>> list = customsBookDTOList.stream().map(i -> {
            SelectOptionVO<Long> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i.getId());
            selectOptionVO.setName(i.getBookNo());
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }


    /**
     * 启用账册区内企业下拉
     *
     * @return
     * @path /customsBook/listAreaCompanyAuth
     */
    @SoulClient(path = "/customsBook/listAreaCompanyAuth", desc = "启用账册区内企业下拉（权限）")
    @UcAccountBookAuthGetAndCheck(addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @Override
    public RpcResult<List<SelectOptionVO<Long>>> listAreaCompanyAuth() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        List<CustomsBookDTO> customsBookDTOList = customsBookService.listAllInUseBooksAuth(accountBookList);
        if (CollectionUtils.isEmpty(customsBookDTOList)) {
            return RpcResult.success(new ArrayList<>());
        }
        List<Long> areaCompanyIdList = customsBookDTOList.stream().map(CustomsBookDTO::getAreaCompanyId).collect(Collectors.toList());
        List<CompanyDTO> companyDTOList = companyService.findById(areaCompanyIdList);
        return RpcResult.success(companyDTOList.stream().map(i -> {
            SelectOptionVO<Long> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i.getId());
            selectOptionVO.setName(i.getName());
            return selectOptionVO;
        }).collect(Collectors.toList()));
    }
}
