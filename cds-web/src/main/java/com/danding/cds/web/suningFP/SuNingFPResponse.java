package com.danding.cds.web.suningFP;

import lombok.Data;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"loadHeadId","loadId","returnMessage"})
@XmlRootElement(name = "LoadReturn")
public class SuNingFPResponse {

    @XmlElement(name = "loadHeadId")
    private String loadHeadId;

    @XmlElement(name = "loadId")
    private String loadId;

    @XmlElement(name = "ReturnMessage")
    private String returnMessage;
}
