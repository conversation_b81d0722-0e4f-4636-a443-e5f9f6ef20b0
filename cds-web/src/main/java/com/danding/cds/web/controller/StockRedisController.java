package com.danding.cds.web.controller;

import com.danding.cds.web.service.StockRedisService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.util.List;

/**
 * @program: cds-center
 * @description: 库存redis操作
 * @author: 潘本乐（Belep）
 * @create: 2021-07-30 09:22
 **/
@RestController
public class StockRedisController {

    @Autowired
    private StockRedisService stockRedisService;


    /**
     * 海关库存初始化
     * type : 1 = 计算历史记录 2 = 直接修改
     * beginDate ('yyyy-MM-dd')
     * @return
     */
    @PostMapping("/stock/customs/init")
    public String accountFileParse(@RequestParam String bookId,@RequestParam Integer type,@RequestParam String beginDate,@RequestParam String endDate,
                                   HttpServletRequest httpRequest) throws IOException {

        stockRedisService.accountFileParse(httpRequest, bookId,type,beginDate,endDate);
        return "SUCCESS";
    }

    /**
     * 删除key
     *
     * @param key
     * @return
     */
    @GetMapping("/stock/redis/key/delete/{key}")
    public Boolean delKey(@PathVariable String key) {
        return stockRedisService.deleteKey(key);
    }

    /**
     * 批量删除key
     *
     * @param keys
     * @return
     */
    @PostMapping("/stock/redis/key/batch/delete")
    public Boolean batchDelKey(@RequestBody List<String> keys) {
        stockRedisService.batchDelKey(keys);
        return true;
    }

    @RequestMapping("/stock/redis/key/batch/deleteAll")
    public Boolean batchDelKeyAll() {
        stockRedisService.batchDelKeyAll();
        return true;
    }

    /**
     * 查询key
     *
     * @param key
     * @return
     */
    @GetMapping("/stock/redis/key/qry/{key}")
    public String qryKey(@PathVariable String key) {
        return stockRedisService.qryKey(key);
    }

    /**
     * key的有效期
     *
     * @param key
     * @return
     */
    @GetMapping("/stock/redis/key/ttl/{key}")
    public Long ttlKey(@PathVariable String key) {
        return stockRedisService.ttlKey(key);
    }


}
