package com.danding.cds.web;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/9/13 13:54
 */
@RestController
@RefreshScope
@RequestMapping("/nacos/test")
public class NacosRefreshTestController {
    @Value("${nacos.test.print:1}")
    private String print;

    @RequestMapping("/print")
    public String print() {
        return print;
    }

}
