package com.danding.cds.web.suningFP.vo;

import lombok.Data;

import javax.xml.bind.annotation.*;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"customsResult", "finishedDate", "loadId"})
@XmlRootElement(name = "loadRec")
public class SuNingFPLoadRec {
    @XmlElement(name = "customsResult")
    private String customsResult;

    @XmlElement(name = "finishedDate")
    private String finishedDate;

    @XmlElement(name = "loadId")
    private String loadId;
}
