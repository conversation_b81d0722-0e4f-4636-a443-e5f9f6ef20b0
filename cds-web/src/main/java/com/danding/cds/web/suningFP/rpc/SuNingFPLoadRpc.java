package com.danding.cds.web.suningFP.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadDetailSearch;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadSearch;
import com.danding.cds.web.suningFP.vo.SuNingFPLoadDetailSearchResult;
import com.danding.cds.web.suningFP.vo.SuNingFPLoadSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface SuNingFPLoadRpc {

    RpcResult<ListVO<SuNingFPLoadSearchResult>> paging(SuNingFPLoadSearch search);

    RpcResult<ListVO<SuNingFPLoadDetailSearchResult>> detailPaging(SuNingFPLoadDetailSearch search);

    RpcResult<String> audit(IdParam idParam) throws Exception;

    RpcResult<String> exportLoadExcelByDownLoadCenter(SuNingFPLoadSearch search);

    RpcResult<String> exportLoadDetailExcelByDownLoadCenter(SuNingFPLoadDetailSearch search);

    RpcResult<String> generateExportOrder(IdParam idParam);
}
