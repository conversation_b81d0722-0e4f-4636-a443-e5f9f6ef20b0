package com.danding.cds.web.logistics.rpc;


import cn.hutool.core.date.DateTime;

import com.danding.cds.c.api.rpc.CustomsLogisticsRpc;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.logistics.api.dto.CustomsLogisticsDTO;
import com.danding.cds.customs.logistics.api.dto.LogisticsSearch;
import com.danding.cds.customs.logistics.api.service.CustomsLogisticsService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountDTO;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.web.logistics.vo.LogisticsSearchResult;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@DubboService
@RestController
public class LogisticsRpcImpl implements LogisticsRpc{

    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference
    private CustomsLogisticsService customsLogisticsService;
    @DubboReference
    private CustomsLogisticsRpc customsLogisticsRpc;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private TaxesTenantAccountService tenantAccountService;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/logistics/paging", desc = "运单分页查询")
    public RpcResult<ListVO<LogisticsSearchResult>> paging(LogisticsSearch search) {
        ListVO<CustomsLogisticsDTO> dtoListVO;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            dtoListVO = customsLogisticsRpc.paging(search);
        } else {
            dtoListVO = customsLogisticsService.paging(search);
        }
        ListVO<LogisticsSearchResult> result = new ListVO<>();
        result.setPage(dtoListVO.getPage());
        Map<Long, ExpressDTO> expressDTOMap = new HashMap<>();
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        Map<String, TaxesTenantAccountDTO> accountDTOMap = new HashMap<>();
        result.setDataList(dtoListVO.getDataList().stream().map((CustomsLogisticsDTO dto) ->{
            LogisticsSearchResult vo = new LogisticsSearchResult();
            vo.setId(dto.getId() + "");
            vo.setStatus(dto.getStatus());
            vo.setOutOrderNo(dto.getOutOrderNo());
            vo.setDeclareOrderNo(dto.getDeclareOrderNo());
            vo.setOrderId(String.valueOf(dto.getOrderId()));
            vo.setStatusDesc(CustomsActionStatus.getEnum(dto.getStatus()).getDesc());
            vo.setLogisticsNo(dto.getLogisticsNo());
            vo.setCustomsDesc(CustomsDistrictEnum.getEnum(dto.getCustoms()).getDesc());
            ExpressDTO expressDTO = expressDTOMap.get(dto.getExpressId());
            if (expressDTO == null) {
                expressDTO = expressService.findById(dto.getExpressId());
                expressDTOMap.put(expressDTO.getId(),expressDTO);
            }
            vo.setExpressName(expressDTO.getName());
            CompanyDTO logisticsCompany = companyDTOMap.get(dto.getLogisticsCompanyId());
            if (logisticsCompany == null){
                logisticsCompany = companyService.findUnifiedCrossInfoById(dto.getLogisticsCompanyId());
                companyDTOMap.put(logisticsCompany.getId(),logisticsCompany);
            }
            vo.setLogisticsCompanyName(logisticsCompany.getName());
            vo.setFeeAmount(dto.getFeeAmount());
            if(!StringUtils.isEmpty(dto.getTenantId())){
                TaxesTenantAccountDTO tenantAccountDTO = accountDTOMap.get(dto.getTenantId());
                if (tenantAccountDTO == null){
                    tenantAccountDTO = tenantAccountService.findByUserIdAndTenantId(1L, dto.getTenantId());
                    accountDTOMap.put(dto.getTenantId(), tenantAccountDTO);
                }
                if (tenantAccountDTO != null){
                    vo.setTenantName(tenantAccountDTO.getName());
                }
            }
//            if (dto.getFinishTime() != null){
//                vo.setFinishAt(new DateTime(dto.getFinishTime()).toString("yyyy/MM/dd HH:mm:ss"));
//            }
            if (dto.getCreateTime() != null){
                vo.setCreateAt(new DateTime(dto.getCreateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            }
            if (dto.getLastDeclareTime() != null){
                vo.setLastDeclareAt(new DateTime(dto.getLastDeclareTime()).toString("yyyy/MM/dd HH:mm:ss"));
            }
            if (dto.getLastCustomsTime() != null){
                vo.setLastCustomsAt(new DateTime(dto.getLastCustomsTime()).toString("yyyy/MM/dd HH:mm:ss"));
                if (dto.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue()) &&
                        CustomsStat.CUSTOMS_RECEIVE.getValue().equals(dto.getCustomsStatus())){
                    vo.setFinishAt(new DateTime(dto.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
            }
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/logistics/listStatus", desc = "运单状态下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listStatus() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(CustomsActionStatus.values())
                .filter((CustomsActionStatus item) -> !item.equals(CustomsActionStatus.NULL))
                .map((CustomsActionStatus item) -> {
                    return (SelectOptionVO<Integer>) new SelectOptionVO(item.getValue(),item.getDesc());
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/logistics/rePush", desc = "运单批量重推")
    @PostMapping("/logistics/rePush")
    public RpcResult<String> rePush(String sns) {
        List<String> idList = Lists.newArrayList(sns.split(","));
        StringBuilder errorMsg = new StringBuilder();
        Integer idx = 1;
        for (String sn : idList) {
            try {
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    customsLogisticsRpc.rePush(sn,true);
                } else {
                    customsLogisticsService.rePush(sn,true);
                }
            } catch (ArgsErrorException e) {
                errorMsg.append("第").append(idx).append("项重推失败:").append(e.getErrorMessage()).append(";");
            }
            idx ++;
        }
        if (StringUtils.isEmpty(errorMsg.toString())){
            return RpcResult.success("重推成功");
        }else {
            return RpcResult.error(errorMsg.toString());
        }
    }
}
