package com.danding.cds.web.download;

import com.danding.cds.download.api.dto.TemplateSearchCondition;
import com.danding.cds.download.api.dto.TemplateSubmit;
import com.danding.cds.download.api.dto.TemplateUploadDTO;
import com.danding.cds.download.api.service.FileOperationService;
import com.danding.cds.download.api.service.TemplateUploadService;
import com.danding.cds.web.download.vo.TemplateVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

@Api(tags = "模板管理")
@RestController
@RequestMapping("/template")
public class TemplateUploadController {
//    protected static final SimpleDateFormat YYYYMMDDHHMMSS_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");

    @DubboReference
    private TemplateUploadService templateUploadService;

    @DubboReference
    private FileOperationService fileOperationService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<TemplateVO> paging(TemplateSearchCondition condition) {
        ListVO<TemplateUploadDTO> pageResult = templateUploadService.paging(condition);
        ListVO<TemplateVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<TemplateVO> list = new ArrayList<>();
        for (TemplateUploadDTO downloadDTO : pageResult.getDataList()) {
            TemplateVO itemVO = new TemplateVO();
            itemVO.setId(downloadDTO.getId());
            itemVO.setTemplateName(downloadDTO.getTemplateName());
            itemVO.setCreateTime(new DateTime(downloadDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            itemVO.setDownloadCount(downloadDTO.getDownloadCount());
            itemVO.setFilePath(downloadDTO.getFilePath());
            itemVO.setRemark(downloadDTO.getRemark());
            list.add(itemVO);
        }
        result.setDataList(list);
        return result;
    }

    @ApiOperation(value = "增加下载次数")
    @GetMapping("/updateDownloadCount")
    public Long updateDownloadCount(Long id) {
        TemplateUploadDTO dto = templateUploadService.findById(id);
        if (dto == null) {
            throw new ArgsErrorException("该模板不存在");
        }
        return templateUploadService.updateDownLoadCount(id, dto.getDownloadCount() + 1);
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public Long delete(Long id) {
        return templateUploadService.updateDeleted(id);
    }

    @ApiOperation(value = "上传")
    @PostMapping("/upload")
    public Long upload(String templateName, String remark, MultipartFile file) throws IOException {
        if (StringUtils.isEmpty(templateName)) {
            throw new ArgsErrorException("模板名称不能为空");
        }
        //该模板是否存在
        TemplateUploadDTO dto = templateUploadService.findByTemplateName(templateName);
        if (dto != null) {
            throw new ArgsErrorException("该模板已存在");
        }

        String originalFileName = file.getOriginalFilename();
        if (!file.isEmpty() && !StringUtils.isEmpty(originalFileName)) {
            //对文文件的全名进行截取然后在后缀名进行删选。
            int begin = originalFileName.indexOf(".");
            int last = originalFileName.length();
            //获得文件后缀名
            String suffix = originalFileName.substring(begin, last);
            if (suffix.endsWith(".xlsx") || suffix.endsWith(".xls")) {
                //上传模板到oss
                String fileName = "ccs/" + generateExcelFileName();
                byte[] byteArr = file.getBytes();
                fileOperationService.uploadFile2OSS(fileName, byteArr,
                        byteArr.length, "application/vnd.ms-excel", null);
                String filePath = "https://daita-oss.oss-cn-hangzhou.aliyuncs.com/" + fileName;
                //插入数据
                TemplateSubmit submit = new TemplateSubmit();
                submit.setId(null);
                submit.setRemark(remark);
                submit.setTemplateName(templateName);
                submit.setFilePath(filePath);
                templateUploadService.upset(submit);
            } else {
                throw new ArgsErrorException("上传模板格式错误");
            }
        } else {
            throw new ArgsErrorException("上传模板不能为空");
        }
        return 1L;
    }


    private String generateExcelFileName() {
        // 当前时间
        SimpleDateFormat YYYYMMDDHHMMSS_FORMAT = new SimpleDateFormat("yyyyMMddHHmmss");
        String currentDate = YYYYMMDDHHMMSS_FORMAT.format(new Date());
        return currentDate + "-" + UUID.randomUUID() + ".xlsx";
    }
}
