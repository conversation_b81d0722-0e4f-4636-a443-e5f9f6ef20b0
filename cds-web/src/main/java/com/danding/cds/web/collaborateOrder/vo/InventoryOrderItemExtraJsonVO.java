package com.danding.cds.web.collaborateOrder.vo;

import com.danding.cds.collaborateorder.api.dto.CollaborateOrderResDetailsDTO;
import com.danding.cds.web.v2.bean.vo.res.FlowStateResVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/31
 */
@Data
public class InventoryOrderItemExtraJsonVO {

    /**
     * 净重
     */
   private BigDecimal netweight;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;
}
