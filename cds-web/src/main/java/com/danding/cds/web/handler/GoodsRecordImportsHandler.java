package com.danding.cds.web.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserDataRpcFacade;
import com.danding.business.client.rpc.user.param.DataRpcParam;
import com.danding.business.client.rpc.user.result.DataRpcResult;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.download.api.vo.GoodsRecordImportVO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.GoodsRecordStatusEnum;
import com.danding.cds.item.api.dto.GoodsRecordSubmit;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsTaskInfo;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_GOODS_RECORD", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%95%86%E5%93%81%E5%A4%87%E6%A1%88%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFv2.0.xlsx",
        groups = {@ParkImportsHandler.Group(name = "商品档案信息", classes = GoodsRecordImportVO.class)})
public class GoodsRecordImportsHandler extends ImportsBaseHandlerPlus {

    public List<Long> accBookAuthGet(ImportsUserInfo userInfo) {
        if (Objects.isNull(userInfo)) {
            return new ArrayList<>();
        }
        try {
            DataRpcParam param = new DataRpcParam();
            param.setGroupCode(CommonCons.GROUP_CUSTOMS_BOOK);
            param.setCodeType(CommonCons.CUSTOMS_ACC_BOOK);
            param.setUserId(userInfo.getUserId());
            IUserDataRpcFacade userDataRpcFacade = this.getBean(IUserDataRpcFacade.class);
            log.info("param = {}", JSON.toJSONString(param));
            List<DataRpcResult> result = userDataRpcFacade.list(param);
            log.info("result = {}", JSON.toJSONString(result));
            List<Long> filterAuthData = result.stream().filter(d -> Objects.equals(d.getCodeType(), "customs_acc_book")).map(d -> {
                String value = d.getValue();
                return Long.valueOf(value);
            }).collect(Collectors.toList());
            log.info("dataAuthFilterResult = {}", JSON.toJSON(filterAuthData));
            return filterAuthData;
        } catch (Exception e) {
            log.error("获取用户账册权限失败", e);
        }
        return new ArrayList<>();
    }

    @Override
    public void work() {
        ImportsTaskInfo taskInfo = this.getTaskInfo();
        Map<String, Object> extendMap = taskInfo.getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        // 【获取用户信息】
        ImportsUserInfo userInfo = taskInfo.getUserInfo();

        // 引入spring的bean,用于业务处理
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        Validator validator = SpringUtil.getBean("validator", Validator.class);
        CustomsBookService customsBookService = this.getBean(CustomsBookService.class);
        CustomsHsService customsHsService = this.getBean(CustomsHsService.class);
        CustomsCurrencyService customsCurrencyService = this.getBean(CustomsCurrencyService.class);

        List<GoodsRecordImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("商品档案信息")) {
                list = group.getDataList(GoodsRecordImportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        List<Long> accountBookList = accBookAuthGet(userInfo);
        int index = 1;


        List<GoodsRecordImportVO> delList = new ArrayList<>();
        Set<String> skuKeySet = new HashSet<>();
        for (GoodsRecordImportVO goodsRecordImportVO : list) {
            try {
                String skuKey = goodsRecordImportVO.getSkuId() + goodsRecordImportVO.getCustomsBookNo();
                if (skuKeySet.contains(skuKey)) {
                    throw new ArgsErrorException("sku:" + goodsRecordImportVO.getSkuId() + ",账册编码:" + goodsRecordImportVO.getCustomsBookNo() + "导入重复数据！");
                } else {
                    skuKeySet.add(skuKey);
                }
                String check = this.check(goodsRecordImportVO, validator, customsBookService,
                        customsCurrencyService, customsHsService, accountBookList);
                if (StrUtil.isNotBlank(check)) {
                    this.callbackData(false, index, check, goodsRecordImportVO);
                    delList.add(goodsRecordImportVO);
                    log.info("参数校验不通过 error={} 导入数据={}", check, goodsRecordImportVO);
                }
            } catch (ArgsErrorException e) {
                this.callbackData(false, index, e.getErrorMessage(), goodsRecordImportVO);
                delList.add(goodsRecordImportVO);
                log.info("参数校验异常 error={} 导入数据={}", e.getErrorMessage(), goodsRecordImportVO);
            } catch (Exception e) {
                this.callbackData(false, index, e.getMessage(), goodsRecordImportVO);
                delList.add(goodsRecordImportVO);
                log.info("参数校验异常 error={} 导入数据={}", e.getMessage(), goodsRecordImportVO);
            }
            index++;
        }
        list.removeAll(delList);

        for (GoodsRecordImportVO importVO : list) {
            if (StrUtil.isNotBlank(importVO.getGoodsRecordName())) {
                importVO.setGoodsRecordName(StringUtils.trimWhitespace(importVO.getGoodsRecordName()));
            }
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(importVO.getCustomsBookNo());
            GoodsRecordDTO old = goodsRecordService.findByBookIdAndProId(customsBookDTO.getId(), importVO.getProductId());
            GoodsRecordSubmit submit = new GoodsRecordSubmit();
            BeanUtil.copyProperties(importVO, submit);
            submit.setCustomsBookId(customsBookDTO.getId());
            //新品
            if (old == null) {
                if (submit.getOpinion() == null) {
                    //不会走到这里 前置校验住了
//                    submit.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
//                    submit.setReason("");
                } else {
                    if (submit.getOpinion() == 0) {
                        //新品驳回不做处理
                        this.callbackData(false, index++, "备案导入不支持驳回", importVO);
                        continue;
                    } else if (submit.getOpinion() == 1) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                        submit.setReason("");
                        submit.setRecordFinishTime(System.currentTimeMillis());
                    }
                }
            } else {
                if (Objects.equals(old.getRecordStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())) {//备案完成不做更新
                    //老品如果是备案完成，也不处理
                    this.callbackData(false, index++, "备案完成不做更新", importVO);
                    continue;
                }
                if (submit.getOpinion() != null) {
                    if (submit.getOpinion() == 1) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                        submit.setReason("");
                        submit.setRecordFinishTime(System.currentTimeMillis());
                    } else if (submit.getOpinion() == 0) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
                        submit.setReason(submit.getReason());
                    }
                }
            }
            ImportResultResVo importResultResVo = goodsRecordService.submitUpdateListNew(submit);
            log.info("商品备案结果:{}", JSON.toJSONString(importResultResVo));
            this.callbackData(importResultResVo.getFlag(), index++, importResultResVo.getReason(), importVO);
        }
    }

    /**
     * 校验excel转换对象后的数据准确性
     *
     * @param data            excel转换对象
     * @param accountBookList
     * @return 校验结果  字符串不是空则代表有错误信息
     */
    public String check(GoodsRecordImportVO data, Validator validator, CustomsBookService customsBookService,
                        CustomsCurrencyService customsCurrencyService, CustomsHsService
                                customsHsService, List<Long> accountBookList) {
        String result = "";
        String validators = ValidatorUtils.doValidator(validator, data);

        if (StrUtil.isNotBlank(validators)) {
            result += validators + "，";
        }
        CustomsBookDTO customsBookDTO = customsBookService.findByCode(data.getCustomsBookNo());
        if (customsBookDTO == null) {
            result += "账册编号错误，";
        } else {
            if (CustomsDistrictEnum.TIANJIN.getCode().equals(customsBookDTO.getCustomsDistrictCode())) {
                if (StringUtils.isEmpty(data.getIoGoodsSerialNo()) || StringUtils.isEmpty(data.getGoodsRegNo())
                        || StringUtils.isEmpty(data.getImportEntryDeclareNo()) || StringUtils.isEmpty(data.getCiqOriginCountry())) {
                    result += "天津口岸账册出区进口商品流水号|商品备案号|进口入区申报号|国检原产国不能为空";
                }
            }
        }
        // 账册权限校验
        if (CollectionUtils.isEmpty(accountBookList)) {
            log.info("文件上传处理，当前提供的账册权限列表为空，不做上传账册权限的校验了");
            return "无账册权限";
        }
        log.info("当前用户账册权限列表：{}" + "，此账册ID:{}" + "", accountBookList, customsBookDTO.getId());

        if (accountBookList.stream().noneMatch(z -> Objects.equals(z, customsBookDTO.getId()))) {
            log.info("用户没有权限操作此账册或账册不可用");
            return "用户没有权限操作此账册或账册不可用";
        }

        if (!StringUtils.isEmpty(data.getDeclareCurrency())) {
            CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(data.getDeclareCurrency());
            if (currencyDTO == null) {
                result += "申报币制错误";
            }
        }
        CustomsHsDTO customsHsDTO = customsHsService.findByCode(data.getHsCode());
        if (customsHsDTO == null) {
            result += "hsCode错误";
        } else {
            if (customsHsDTO.getTariff() == null || customsHsDTO.getVat() == null) {
                result += "hsCode" + customsHsDTO.getHsCode() + "税率未完善";
            }
        }
        if (data.getOpinion() != null) {
            if (data.getOpinion() == 0 && StringUtils.isEmpty(data.getReason())) {
                result += "审核驳回时，驳回原因不能为空！";
            }
        }
        return result;
    }
}
