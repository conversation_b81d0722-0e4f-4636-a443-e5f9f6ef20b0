package com.danding.cds.web.checklist.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 核注单详情 - 表体
 */
@Data
public class EndorsementItemDetailVO {

    /**
     * 序号
     */
    private Integer serializeNo;

    /**
     * 是否新品
     */
    private String isNew;

    /**
     * 备案序号
     */
    private String goodsSeqNo;

    /**
     * 商品料号
     */
    private String productId;

    /**
     * 商品编码
     */
    private String hsCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 规格型号
     */
    private String goodsModel;

    /**
     * 原产国
     */
    private String originCountry;


    /**
     * 最终目的国（地区）
     */
    private String destinationCountry;

    /**
     * 申报计量单位
     */
    private String unit;

    /**
     * 申报数量
     */
    private BigDecimal declareUnitQfy;

    /**
     * 申报单价
     */
    private BigDecimal declarePrice;

    /**
     * 申报总价
     */
    private BigDecimal declareTotalPrice;

    /**
     * 币制
     */
    private String currency;

    /**
     * 法定计量单位
     */
    private String firstUnit;

    /**
     * 法二计量单位
     */
    private String secondUnit;

    /**
     * 法定数量
     */
    private BigDecimal firstUnitQfy;

    /**
     * 第二法定数量
     */
    private BigDecimal secondUnitQfy;

    /**
     * 毛重(kg)
     */
    private BigDecimal grossWeight;

    /**
     * 净重(kg)
     */
    private BigDecimal netWeight;

    /**
     * 修改标志
     */
    private String modfMark;

    /**
     * 危化品标志
     */
    private String dangerousFlag;

    /**
     * 记账金二序号
     */
    private String customsCallBackSeqNo;

    /**
     * 报关单商品序号
     */
    private String declareCustomsGoodsSeqNo;

    /**
     * 申报表序号
     */
    private String declareTableNo;

    /**
     * 征免方式
     */
    private String avoidTaxMethod;

    /**
     * 单耗版本号
     */
    private String version;

    /**
     * 备注
     */
    private String remark;

    /**
     * 关联包裹运单号
     */
    private List<String> packageMailNoList;

    /**
     * 标签列表
     */
    private List<String> tagList;

    /**
     * 申报表序号
     */
    private Integer declareFormItemSeqNo;

    /**
     * 来源标识
     */
    private String goodsSource;
    private String goodsSourceDesc;

}
