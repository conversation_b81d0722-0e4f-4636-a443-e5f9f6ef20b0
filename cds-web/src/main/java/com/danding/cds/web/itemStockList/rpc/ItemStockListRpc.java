package com.danding.cds.web.itemStockList.rpc;

import com.danding.cds.common.model.IdsParam;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockListParam;
import com.danding.cds.itemstocklist.api.dto.ItemStockListSearch;
import com.danding.cds.itemstocklist.api.vo.ItemStockListResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface ItemStockListRpc {

    RpcResult<ListVO<ItemStockListResVO>> paging(ItemStockListSearch condition);

    RpcResult<String> deleteByIds(IdsParam param);

    RpcResult<String> cancelAbnormalTypeByIds(IdsParam param);

    RpcResult<String> excelExport(ItemStockListSearch search);

}
