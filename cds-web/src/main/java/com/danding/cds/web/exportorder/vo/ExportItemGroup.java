package com.danding.cds.web.exportorder.vo;

import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.Set;

@ApiModel
@Data
public class ExportItemGroup {

    @ApiModelProperty("托盘号")
    private String trayNo;

    @ApiModelProperty("运单项")
    private List<ExportItemRecord> itemList;

    @ApiModelProperty("包裹数量")
    private Integer packageCount;

    @ApiModelProperty("SKU数量")
    private Integer skuCount;

    @ApiModelProperty("包裹毛重（kg）")
    private BigDecimal packageWeight;

    private Set<Long> bookItemSet;
}
