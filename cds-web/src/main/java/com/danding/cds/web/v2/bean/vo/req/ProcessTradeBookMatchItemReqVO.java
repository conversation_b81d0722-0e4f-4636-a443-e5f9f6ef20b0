package com.danding.cds.web.v2.bean.vo.req;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class ProcessTradeBookMatchItemReqVO implements Serializable {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 申报表料件成品标志
     */
    @NotNull(message = "料件成品标志不能为空")
    private Integer goodsType;

    /**
     * 料号
     */
    @NotNull(message = "料号不能为空")
    private String productId;

    /**
     * 账册id
     */
    @NotNull(message = "账册id不能为空")
    private Long bookId;
}
