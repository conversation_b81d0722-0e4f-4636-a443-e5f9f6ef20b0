package com.danding.cds.web.payment.vo;

import com.danding.cds.web.order.vo.CustomsPaymentLogResult;
import com.danding.cds.web.order.vo.CustomsPaymentReturnMsgResult;
import com.danding.encrypt.annotation.DesensitizeField;
import com.danding.encrypt.constant.RegexConstant;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
public class CustomsPaymentResult {
    @ApiModelProperty(value = "id")
    private String id ;

    @ApiModelProperty(value = "渠道订单号")
    private String outOrderNo;

    @ApiModelProperty(value = "申报单号")
    private String declareOrderNo;

    @ApiModelProperty(value = "支付流水号")
    private String tradePayNo;

    @ApiModelProperty(value = "申报单id")
    private String orderId;

    @ApiModelProperty(value = "清单状态 成功 100 失败 -1,该项不申报 0，其余都是待审申报或申报中")
    private Integer status = 0;

    private String statusDesc;

    @ApiModelProperty(value = "支付企业")
    private String payCompany;

    private String payCompanyName;

    @ApiModelProperty(value = "电商平台")
    private String ebp;

    private String ebpName;

    @ApiModelProperty(value = "支付人证件类型")
    private String buyerIdType;

    @ApiModelProperty(value = "支付人身份证号")
    @DesensitizeField(regex = RegexConstant.IDCARD,replace = RegexConstant.IDCARD_REPLACE)
    private String buyerIdNo;

    @ApiModelProperty(value = "支付人姓名")
    @DesensitizeField(regex = RegexConstant.NAME,replace = RegexConstant.NAME_REPLACE)
    private String buyerName;

    @ApiModelProperty(value = "币制")
    private String currencyCode;

    @ApiModelProperty(value = "支付时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date payTime;

    private List<CustomsPaymentReturnMsgResult> customsPaymentReturnMsgResultList;

    @ApiModelProperty(value = "支付渠道")
    private String payChannel;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTIme;

    @ApiModelProperty(value = "申报时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastDeclareTime;

    @ApiModelProperty(value = "回执时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date lastCustomsTime;

    @ApiModelProperty(value = "申报成功时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date finishTime;

    @ApiModelProperty(value = "操作日志")
    private List<CustomsPaymentLogResult> logList;

}
