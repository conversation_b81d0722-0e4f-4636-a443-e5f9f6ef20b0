package com.danding.cds.web;

import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.ucenter.client.UCenterClient;
import com.danding.ucenter.client.secruity.helper.UserHelper;
import com.danding.ucenter.core.api.role.model.RoleModel;

import java.util.List;

public class CustomsDistrictHelper {

    public static CustomsDistrictEnum getCustomsDistrictByUserRole(){
        List<Long> roles = UserHelper.getSystemInfo().getRoles();
        for (Long roleId : roles) {
            RoleModel role = UCenterClient.roleClient().getRole(roleId);
            String code = role.getCode();
            if ("CCS_ADMIN#0#HAIKOU".equals(code)){
                return CustomsDistrictEnum.HAIKOU;
            }
            if ("CCS_ADMIN#0#JINYI".equals(code)){
                return CustomsDistrictEnum.JINYI;
            }
        }
        return CustomsDistrictEnum.NULL;
    }
}
