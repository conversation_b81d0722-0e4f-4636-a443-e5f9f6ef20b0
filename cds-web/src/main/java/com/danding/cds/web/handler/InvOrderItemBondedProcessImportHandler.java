package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemParam;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.v2.enums.InvOrderItemGenTypeEnums;
import com.danding.cds.web.v2.bean.vo.req.InvOrderItemBondedProcessImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import com.danding.soul.client.common.exception.BusinessException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * 清关单表体保税加工导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_INV_ORDER_ITEM_ITEM_BONDED_PROCESS", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%B8%85%E5%85%B3%E5%8D%95%E4%BF%9D%E7%A8%8E%E5%8A%A0%E5%B7%A5%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "清关单表体导入", classes = InvOrderItemBondedProcessImportExcelVO.class),})
public class InvOrderItemBondedProcessImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long inventoryOrderId = Long.parseLong((String) extendMap.get("inventoryOrderId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        InventoryOrderInfoService inventoryOrderInfoService = this.getBean(InventoryOrderInfoService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(inventoryOrderId);

        List<InvOrderItemBondedProcessImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("清关单表体导入")) {
                list = group.getDataList(InvOrderItemBondedProcessImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("InvOrderItemBondedProcessImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<InventoryOrderItemDTO> successList = new ArrayList<>();
        Map<String, Integer> goodsTypeMap = new HashMap<>();
        goodsTypeMap.put("料件", 1);
        goodsTypeMap.put("成品", 2);
        Set<String> productIdSet = new HashSet<>();
        for (InvOrderItemBondedProcessImportExcelVO vo : list) {
            List<String> errorMsgList = new ArrayList<>();
            try {
                String errorMsg = doValidator(vo);
                if (StrUtil.isNotBlank(errorMsg)) {
                    errorMsgList.add(errorMsg);
                }
                if (!goodsTypeMap.containsKey(vo.getGoodsType())) {
                    errorMsgList.add("料件成品标志错误");
                } else {
                    if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_IN.getCode())
                            && !Objects.equals(goodsTypeMap.get(vo.getGoodsType()), 1)) {
                        errorMsgList.add("保税加工一线入境仅允许导入料件");
                    }
                    if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_BONDED_PROCESSING_ONELINE_OUT.getCode())
                            && !Objects.equals(goodsTypeMap.get(vo.getGoodsType()), 2)) {
                        errorMsgList.add("保税加工一线出境仅允许导入成品");
                    }
                }
                if (productIdSet.contains(vo.getProductId())) {
                    errorMsgList.add("料号重复");
                }
                InventoryOrderItemParam inventoryOrderItemParam = new InventoryOrderItemParam();
                inventoryOrderItemParam.setRefInveOrderId(inventoryOrderId);
                inventoryOrderItemParam.setRefInveOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
                inventoryOrderItemParam.setProductId(vo.getProductId());
                inventoryOrderItemParam.setDeclareUnitQfy(vo.getQty().doubleValue());
                inventoryOrderItemParam.setGoodsType(goodsTypeMap.get(vo.getGoodsType()));
                InventoryOrderItemDTO itemDTO = null;
                try {
                    itemDTO = inventoryOrderInfoService.buildInventoryOrderItem(
                            InvOrderItemGenTypeEnums.PROCESSING_TRADE, inventoryOrderInfoDTO, inventoryOrderItemParam);
                    if (itemDTO == null || itemDTO.getGoodsSeqNo() == null) {
                        errorMsgList.add("加贸账册料号不存在");
                    }
                } catch (BusinessException e) {
                    errorMsgList.add(e.getMessage());
                }
                if (CollUtil.isNotEmpty(errorMsgList)) {
                    this.callbackData(false, index++, String.join(";", errorMsgList), vo);
                    continue;
                }
                successList.add(itemDTO);
                productIdSet.add(vo.getProductId());
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("清关单表体保税加工导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            inventoryOrderInfoService.importExcel(inventoryOrderId, successList, operator);
        }
    }
}
