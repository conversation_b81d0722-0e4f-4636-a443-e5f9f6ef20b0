package com.danding.cds.web.invenorder.vo;

import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderLogDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;
@Data
@ApiModel
public class InventoryOrderViewInfoVO {
    @ApiModelProperty("预录入统一编号")
    private String preNo;
    @ApiModelProperty("清关单号")
    private String inveCustomsSn;
    @ApiModelProperty("核放单编号")
    private String refCheckOrderNo;
    @ApiModelProperty("进出标志")
    private String inOrOutFlag;
    /**
     * 进出区标志
     * {@link com.danding.cds.invenorder.api.enums.InventoryInOutEnum}
     */
    private String inOrOutFlagCode;
    @ApiModelProperty("核注清单编号")
    private String refHzInveNo;
    @ApiModelProperty("业务类型")
    private String inveBusinessType;
    @ApiModelProperty("业务类型描述")
    private String inveBusinessTypeDesc;

    @ApiModelProperty("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    private String inAccountBook;
    @ApiModelProperty("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    private String outAccountBook;

//    @ApiModelProperty("绑定类型")
//    private String bindType;
//    @ApiModelProperty("区内企业")
//    private String areaCompanyName;

    @ApiModelProperty("区内账册Id")
    private Long areaBookId;
    @ApiModelProperty("区内账册编号")
    private String areaBookNo;

    @ApiModelProperty("进出境关别")
    private String entryExitCustoms;

    @ApiModelProperty("运输方式")
    private String transportMode;

    @ApiModelProperty("运输方式名称")
    private String transportModeName;

    @ApiModelProperty("启运国")
    private String shipmentCountry;

    @ApiModelProperty("启运国名称")
    private String shipmentCountryName;

    @ApiModelProperty("清关单来源")
    private Integer channel;

    @ApiModelProperty("关联核注清单编号")
    private String associatedEndorsementNo;

    @ApiModelProperty("预计出区时间")
    private String expectedOutAreaTime;

    @ApiModelProperty("预计到港时间")
    private String expectedToPortTime;
//    @ApiModelProperty("车自重")
//    private String carWeight;
//    @ApiModelProperty("货物总毛重")
//    private String goodTotalGrossWeight;
//    @ApiModelProperty("货物总毛净重")
//    private String goodTotalNetWeight;
//    @ApiModelProperty("总重量")
//    private String totalWeight;

    @ApiModelProperty("表体信息")
    private List<InventoryOrderItemDTO>  inventoryOrderItemDTOList;

    @ApiModelProperty("日志信息")
    private List<InventoryOrderLogDTO>  inventoryOrderLogDTOList;
    @ApiModelProperty("关联单证信息")
    private List<InventoryOrderRelationDTO>  inventoryOrderRelationDTOList;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("清单类型（指区港联动等）")
    private String customsInvtType;

    @ApiModelProperty("对应报关单编号（区港联动时需要）")
    private String customsEntryNo;

    @ApiModelProperty("对应报关企业")
    private Long customsEntryCompany;

    @ApiModelProperty("对应报关单类型")
    private String customsEntryType;

    @ApiModelProperty("清关单状态")
    private String status;

    @ApiModelProperty("子单单号")
    private String subOrderSn;

    @ApiModelProperty("主单单号")
    private String masterOrderSn;

    @ApiModelProperty("理货报告")
    private List<InventoryOrderTallyReportVO> inventoryOrderTallyReportVOList;

//    @ApiModelProperty("实体仓名称")
//    private String entityWarehouseName;
//
//    @ApiModelProperty("货主名称")
//    private String ownerName;

    @ApiModelProperty("提单号")
    private String pickUpNo;

    /**
     * 核注企业单号
     */
    private String endorsementSn;

    /**
     * 报关标志(一线就默认都是报关，非一线就是非报关)(1:是 2:否)
     */
    private Integer customsFlag;

    private String customsFlagDesc;

    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     */
    private Integer twoStepFlag;

    private String twoStepFlagDesc;

    /**
     * 清关方式
     */
    private Integer declareWay;
    /**
     * 是否生成报关单(1:是 2:否)
     */
    private Integer declarationFlag;

    private String declarationFlagDesc;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 实际到港日期
     */
    private Date actualArrivalDate;

    /**
     * 清关单状态对应时间
     */
    private Date statusTime;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 理货报告号
     */
    private String tallyOrderNo;

    /**
     * 是否关仓协同
     */
    private Boolean collaborateFlag;

    /**
     * 质押货主标志
     */
    private Boolean pledgeOwnerFlag;

    /**
     * 邮件状态
     */
    private Integer mailStatus;

    /**
     * 起点仓货主
     */
    private String outsetOwnerName;

    /**
     * 起点仓
     */
    private String outsetEntityWarehouseName;

    /**
     * 目的地货主
     */
    private String destinationOwnerName;

    /**
     * 目的仓
     */
    private String destinationEntityWarehouseName;

    /**
     * 货主是否自备车辆
     */
    private Integer selfOwnedVehicle;

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;

    /**
     * 托数
     */
    private Integer palletsNums;

    /**
     * 清关企业
     */
    private Long inveCompanyId;
    private String inveCompanyName;

    /**
     * 关联核注清单编号
     */
    private String endorsementRealOrderNo;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;
    /**
     * 是否开启中转 0:否 1:是
     */
    private Integer transitFlag;
    /**
     * 是否开启中转 0:否 1:是
     */
    private String transitFlagDesc;
    /**
     * 清关企业(终点)
     */
    private Long finalInveCompanyId;
    /**
     * 清关企业(终点)
     */
    private String finalInveCompanyName;
    /**
     * 账册ID(终点)
     */
    private Long finalBookId;
    /**
     * 账册ID(终点)
     */
    private String finalBookNo;
    /**
     * 实体仓编码(终点)
     */
    private String finalEntityWarehouseCode;
    /**
     * 实体仓名称(终点)
     */
    private String finalEntityWarehouseName;
    /**
     * 货主编码(终点)
     */
    private String finalOwnerCode;
    /**
     * 货主名称(终点)
     */
    private String finalOwnerName;
    /**
     * 关联中转清关单号
     */
    private String associatedTransitOrderSn;
    /**
     * 关联调入清关单号
     */
    private String associatedInOrderSn;
    /**
     * 关联调出清关单号
     */
    private String associatedOutOrderSn;

    private String carryOverNo;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private Integer customsType;
    private String customsTypeDesc;

    /**
     * 对应报关单申报单位
     */
    private Long corrCusDeclareCompanyId;
    private String corrCusDeclareCompanyName;
    private String corrCusDeclareCompanyCode;
    private String corrCusDeclareCompanyUSCC;

    /**
     * 关联报关单境内收发货人
     */
    private Long rltCusInnerSFHRCompanyId;
    private String rltCusInnerSFHRCompanyName;
    private String rltCusInnerSFHRCompanyCode;
    private String rltCusInnerSFHRCompanyUSCC;

    /**
     * 关联报关单消费使用单位
     */
    private Long rltCusXFDYCompanyId;
    private String rltCusXFDYCompanyName;
    private String rltCusXFDYCompanyCode;
    private String rltCusXFDYCompanyUSCC;

    /**
     * 关联报关单申报单位
     */
    private Long rltCusDeclareCompanyId;
    private String rltCusDeclareCompanyName;
    private String rltCusDeclareCompanyCode;
    private String rltCusDeclareCompanyUSCC;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 清关单标记
     */
    private List<Integer> orderTagList;

    /**
     * 非保标记 (1-是, 0-否)
     */
    private Integer fbFlag;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 起运港（始发机场）
     */
    private String fromLocation;
}
