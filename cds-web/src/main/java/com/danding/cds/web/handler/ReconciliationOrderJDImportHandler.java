package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.c.api.rpc.ReconciliationOrderItemRpc;
import com.danding.cds.c.api.rpc.ReconciliationOrderRpc;
import com.danding.cds.common.utils.SpringContextUtil;
import com.danding.cds.v2.bean.dto.ReconciliationOrderDTO;
import com.danding.cds.v2.bean.dto.ReconciliationOrderImportInfoDTO;
import com.danding.cds.v2.bean.vo.req.ReconciliationJDImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 车辆资源导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_RECONCILIATION_ORDER_JD", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%87%BA%E5%8C%BA%E5%AF%B9%E8%B4%A6%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "sheet1", classes = ReconciliationJDImportExcelVO.class),})
public class ReconciliationOrderJDImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        Long areaCompanyId = Long.valueOf((String) extendMap.get("areaCompanyId"));
        Long reconciliationDate = Long.valueOf((String) extendMap.get("reconciliationTime"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        ReconciliationOrderRpc reconciliationOrderRpc = this.getBean(ReconciliationOrderRpc.class);
        ReconciliationOrderItemRpc reconciliationOrderItemRpc = this.getBean(ReconciliationOrderItemRpc.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);

        List<ReconciliationJDImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        log.info("ReconciliationOrderJDImportHandler 读取数据 start");
        List<ImportsDataGroup> groups = this.readDataBySheetIdx(0, 1, 0);
        for (ImportsDataGroup group : groups) {
            list = group.getDataList(ReconciliationJDImportExcelVO.class);
            break;
        }

        if (CollUtil.isEmpty(list)) {
            log.error("ReconciliationOrderJDImportHandler-导入未获取到数据");
            return;
        }
        log.info("ReconciliationOrderJDImportHandler - 读取到的excel对象数据 {} 条， 开始校验数据", list.size());
        int index = 2;
        List<String> logisticsNoList = list.stream().map(ReconciliationJDImportExcelVO::getWaybillNumber).collect(Collectors.toList());
        Set<String> importLogisticsNoSet = new HashSet<>();
        List<String> existLogisticsOutBoundCodeList = reconciliationOrderItemRpc.findByLogisticsNo(logisticsNoList);
        boolean allRight = true;
        List<ReconciliationJDImportExcelVO> importList = new ArrayList<>();
        for (ReconciliationJDImportExcelVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            boolean skip = false;
            if (StringUtils.isEmpty(vo.getProductCode())) {
                errorMsg.add("商品编码存在字段为空！");
            }
            if (Objects.isNull(vo.getCheckQuantity())) {
                errorMsg.add("复核数量存在字段为空！");
            }
            if (StringUtils.isEmpty(vo.getCargoOwnerCode())) {
                errorMsg.add("货主编码存在字段为空！");
            } else {
                if (vo.getCargoOwnerCode().startsWith("EBU")) {
                    // 京东pop
                    if (StrUtil.isBlank(vo.getMerchantOrderNumber())) {
                        errorMsg.add("京东POP商家订单号不能为空！");
                    }
                } else {
                    // 京东直营
                    if (StringUtils.isEmpty(vo.getOrderNumber())) {
                        errorMsg.add("订单号存在字段为空！");
                    }
                }
            }
//            if (StringUtils.isEmpty(vo.getCargoOwnerName())) {
//                errorMsg.add("货主名称存在字段为空！");
//            }
            if (StringUtils.isEmpty(vo.getWaybillNumber())) {
                errorMsg.add("运单号存在字段为空！");
            }
            if (existLogisticsOutBoundCodeList.contains(vo.getWaybillNumber() + "_" + vo.getProductCode())) {
                errorMsg.add("存在非作废的运单号+商品编码组合（过滤）");
                skip = true;
            }
//            if (importLogisticsNoSet.contains(vo.getWaybillNumber())) {
//                errorMsg.add("excel存在重复运单号（过滤）");
//                skip = true;
//            }
            if (CollUtil.isNotEmpty(errorMsg)) {
                this.callbackData(false, index++, String.join(";", errorMsg), vo);
                if (!skip) {
                    allRight = false;
                }
                continue;
            }
            importList.add(vo);
            importLogisticsNoSet.add(vo.getWaybillNumber());
            this.callbackData(true, index++, null, vo);
        }
        if (allRight && CollUtil.isNotEmpty(importList)) {
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            // 创建出区对账单
            log.info("ReconciliationOrderJDImportHandler 创建出区对账单");
            ReconciliationOrderDTO reconciliationOrderDTO = reconciliationOrderRpc.create(areaCompanyId,
                    reconciliationDate, importList.size(), operator);
            String sn = reconciliationOrderDTO.getSn();

            // 提交任务到线程池
            log.info("ReconciliationOrderJDImportHandler 提交导入表体任务到线程池 sn={}", sn);
            List<List<ReconciliationJDImportExcelVO>> partitionedLists = partitionList(importList, 2000);
            ThreadPoolTaskExecutor reconciliationThreadExecutor =
                    (ThreadPoolTaskExecutor) SpringContextUtil.getBean("reconciliationThreadExecutor");
            List<CompletableFuture<Void>> futureList = new ArrayList<>();
            for (List<ReconciliationJDImportExcelVO> partition : partitionedLists) {
                futureList.add(CompletableFuture.runAsync(() -> {
                    ReconciliationOrderImportInfoDTO importInfoDTO = new ReconciliationOrderImportInfoDTO();
                    importInfoDTO.setExcelVOList(partition);
                    importInfoDTO.setAreaCompanyId(areaCompanyId);
                    importInfoDTO.setReconciliationDate(reconciliationDate);
                    importInfoDTO.setOperator(operator);
                    importInfoDTO.setReconciliationSn(sn);
                    reconciliationOrderItemRpc.excelImport(importInfoDTO);
                }, reconciliationThreadExecutor));
            }
            try {
                CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).get();
            } catch (Exception e) {
                log.error("ReconciliationOrderJDImportHandler 表体导入CompletableFuture结果异常：{}", e.getMessage(), e);
            }
            log.info("ReconciliationOrderJDImportHandler 导入完成 调用数据中台进行对账 sn={}", sn);
            reconciliationOrderRpc.invokeDataCenterBalanceAccount(Collections.singletonList(sn));
            log.info("ReconciliationOrderJDImportHandler 导入完成 调用数据中台成功 sn={}", sn);
        }
    }


    private List<List<ReconciliationJDImportExcelVO>> partitionList(List<ReconciliationJDImportExcelVO> list, int size) {
        List<List<ReconciliationJDImportExcelVO>> partitions = new ArrayList<>();
        for (int i = 0; i < list.size(); i += size) {
            partitions.add(new ArrayList<>(list.subList(i, Math.min(i + size, list.size()))));
        }
        return partitions;
    }

}
