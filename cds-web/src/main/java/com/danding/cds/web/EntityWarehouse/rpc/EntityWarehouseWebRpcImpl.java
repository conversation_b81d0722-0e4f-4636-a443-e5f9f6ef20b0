package com.danding.cds.web.EntityWarehouse.rpc;

import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ThreadContextUtil;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.dto.RecordWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.bean.vo.req.DelEntityWarehouseReqVO;
import com.danding.cds.v2.bean.vo.req.EntityWarehouseReqVO;
import com.danding.cds.v2.bean.vo.req.EntityWarehouseSearch;
import com.danding.cds.v2.bean.vo.req.UpdEntityWarehouseTagReqVo;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCData;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @description
 * @date 2022/3/7
 */
@Slf4j
@DubboService
public class EntityWarehouseWebRpcImpl implements EntityWarehouseWebRpc {

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @DubboReference
    private RecordWarehouseService recordWarehouseService;

    /**
     * 分页查询
     *
     * @param search
     * @return
     */
    @Override
    @SoulClient(path = "/entityWarehouse/paging", desc = "实体仓配置查询")
    public RpcResult paging(EntityWarehouseSearch search) {
        return RpcResult.success(entityWarehouseService.paging(search));
    }

    @Override
    @SoulClient(path = "/entityWarehouse/upset", desc = "实体仓配置")
    public RpcResult upsetEntityWarehouse(EntityWarehouseReqVO reqVO) throws ArgsErrorException {
        try {
            entityWarehouseService.upsetEntityWarehouse(reqVO);
            return RpcResult.success(true);
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        }

    }


    @Override
    @SoulClient(path = "/entityWarehouse/entityWarehouse", desc = "获取实体仓")
    public RpcResult queryERPEntityWarehouse() {
        return RpcResult.success(entityWarehouseService.queryERPEntityWarehouse());
    }

    @Override
    @SoulClient(path = "/entityWarehouse/listEntityByCustomsBook", desc = "根据账册获取CCS实体仓")
    public RpcResult<List<SelectOptionVO<String>>> listEntityByCustomsBook(Long customsBookId) {
        try {
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findByCustomsBookId(customsBookId);
            List<SelectOptionVO<String>> result = new ArrayList<>();
            for (EntityWarehouseDTO item : entityWarehouseDTOList) {
                SelectOptionVO<String> optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getErpWarehouseCode());
                optionDTO.setName(item.getErpWarehouseName());
                result.add(optionDTO);
            }
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("获取实体仓列表失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取实体仓列表失败");


    }

    /**
     * 根据账册权限获取实体仓
     *
     * @return
     * @path /entityWarehouse/listEntityByCustomsBookAuth
     */
    @Override
    @UcAccountBookAuthGetAndCheck(addAccBookToField = false, onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/entityWarehouse/listEntityByCustomsBookAuth", desc = "根据账册权限获取实体仓")
    public RpcResult<List<SelectOptionVO<String>>> listEntityByCustomsBookAuth() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        try {
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findByCustomsBookId(accountBookList);
            List<SelectOptionVO<String>> result = entityWarehouseDTOList.stream().map(item -> {
                SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
                optionDTO.setId(item.getErpWarehouseCode());
                optionDTO.setName(item.getErpWarehouseName());
                return optionDTO;
            }).distinct().collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("获取实体仓列表失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取实体仓列表失败");


    }

    /**
     * id 为wms仓编码
     *
     * @return
     */
    @Override
    @SoulClient(path = "/entityWarehouse/listWarehouseSn", desc = "获取CCS实体仓")
    public RpcResult<List<SelectOptionVO<String>>> listEntityWms() {
        try {
            List<EntityWarehouseDTO> warehouseDTOList = entityWarehouseService.listWarehouse();
            List<SelectOptionVO<String>> result = new ArrayList<>();
            for (EntityWarehouseDTO item : warehouseDTOList) {
                SelectOptionVO<String> optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getWmsWarehouseCode());
                optionDTO.setName(item.getErpWarehouseName());
                result.add(optionDTO);
            }
            result = result.stream().distinct().collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("获取实体仓列表失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取实体仓列表失败");
    }

    @Override
    @SoulClient(path = "/entityWarehouse/listWarehouseErpCode", desc = "获取CCS实体仓(ERP-code)")
    public RpcResult<List<SelectOptionVO<String>>> listWarehouseErpCode() {
        try {
            List<EntityWarehouseDTO> warehouseDTOList = entityWarehouseService.listWarehouse();
            List<SelectOptionVO<String>> result = new ArrayList<>();
            for (EntityWarehouseDTO item : warehouseDTOList) {
                SelectOptionVO<String> optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getErpWarehouseCode());
                optionDTO.setName(item.getErpWarehouseName());
                result.add(optionDTO);
            }
            result = result.stream().distinct().collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("获取实体仓列表失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取实体仓列表失败");
    }

    @Override
    @SoulClient(path = "/entityWarehouse/listWarehouseByRecordCustomsId", desc = "根据备案和口岸获取可用实体仓")
    public RpcResult<List<SelectOptionVO<String>>> listWarehouseByRecordCustomsId(Long recordCustomsId, String customsCode) {
        try {
            //获取该口岸下已有的实体仓
            List<RecordWarehouseDTO> existRecordWarehouseDTOList = recordWarehouseService.getRecordWarehouseInfo(recordCustomsId);
            //获取所有的实体仓
            List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.listWarehouse(customsCode);
            if (!CollectionUtils.isEmpty(existRecordWarehouseDTOList)) {
                List<String> snList = existRecordWarehouseDTOList.stream().map(RecordWarehouseDTO::getWarehouseSn).distinct().collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(snList)) {
                    //过滤已有的
                    entityWarehouseDTOList = entityWarehouseDTOList.stream().filter(e -> !snList.contains(e.getSn())).collect(Collectors.toList());
                }
            }
            List<SelectOptionVO<String>> result = new ArrayList<>();
            for (EntityWarehouseDTO item : entityWarehouseDTOList) {
                SelectOptionVO<String> optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getSn());
                optionDTO.setName(item.getErpWarehouseName() + "-" + item.getCustomsBookNo());
                result.add(optionDTO);
            }
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("根据备案和口岸获取可用实体仓失败 error={}", e.getMessage());
        }
        return RpcResult.error("获取实体仓列表失败");
    }


    @Override
    @SoulClient(path = "/entityWarehouse/del", desc = "删除实体仓")
    public RpcResult delEntityWarehouse(DelEntityWarehouseReqVO reqVO) {
        try {
            entityWarehouseService.delEntityWarehouse(reqVO);
            return RpcResult.success(true);
        } catch (Exception e) {
            return RpcResult.error("操作失败");
        }
    }

    @Override
    @SoulClient(path = "/entityWarehouse/syncRecordWarehouseCustomsBook", desc = "同步备案")
    public RpcResult<String> syncRecordWarehouseCustomsBook(DelEntityWarehouseReqVO idParam) {
        try {
            entityWarehouseService.syncRecordWarehouseCustomsBook(Long.valueOf(idParam.getId()));
            return RpcResult.success("同步成功");
        } catch (ArgsInvalidException e) {
            log.error("syncRecordWarehouseCustomsBook error={}", e.getMessage(), e);
            return RpcResult.error("同步备案失败:" + e.getErrorMessage());
        } catch (Exception e) {
            log.error("syncRecordWarehouseCustomsBook error={}", e.getMessage(), e);
            return RpcResult.error("同步备案失败");
        }
    }

    /**
     * 实体仓标签下拉
     *
     * @return
     * @path /entityWarehouse/listTag
     */
    @Override
    @SoulClient(path = "/entityWarehouse/listTag", desc = "实体仓标签下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listWarehouseTag() {
        List<SelectOptionVO<Integer>> list = EntityWarehouseTagEnums.valuesV2().stream()
                .map(i -> {
                    SelectOptionVO<Integer> selectOptionVO = new SelectOptionVO<>();
                    selectOptionVO.setId(i.getCode());
                    selectOptionVO.setName(i.getDesc());
                    return selectOptionVO;
                }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 商品来源标识列表
     *
     * @return
     * @path /entityWarehouse/listTag
     */
    @Override
    @SoulClient(path = "/goodsSource/list", desc = "商品来源标识列表")
    public RpcResult<List<SelectOptionVO<String>>> listGoodSourceList() {
        List<SelectOptionVO<String>> list = Arrays.stream(GoodsSourceEnums.values())
                .map(i -> {
                    SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();
                    selectOptionVO.setId(i.getCode());
                    selectOptionVO.setName(i.getDesc());
                    return selectOptionVO;
                }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 标签配置
     *
     * @param reqVo
     * @return
     * @path /entityWarehouse/updateTag
     */
    @Override
    @SoulClient(path = "/entityWarehouse/updateTag", desc = "标签配置")
    @PostMapping("/entityWarehouse/updateTag")
    public RpcResult updateTag(UpdEntityWarehouseTagReqVo reqVo) {
        if (Objects.isNull(reqVo)) {
            return RpcResult.error("参数为空");
        }
        try {
            entityWarehouseService.updateTag(reqVo);
        } catch (ArgsInvalidException e) {
            log.info("updateTag error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        }
        return RpcResult.success("标签配置成功");
    }

    /**
     * 根据实体仓编码获取wms编码
     * @param reqVO
     * @return
     * @path /entityWarehouse/getWarehouseByErpCode
     */
    @Override
    @SoulClient(path = "/entityWarehouse/getWarehouseByErpCode", desc = "根据实体仓编码获取wms编码")
    public RpcResult<String> getWmsWarehouseCodeByErpCode(EntityWarehouseReqVO reqVO) throws ArgsErrorException {
        List<EntityWarehouseDTO> dtoByErpCode = entityWarehouseService.findDTOByErpCode(reqVO.getErpWarehouseCode());
        if (!CollectionUtils.isEmpty(dtoByErpCode)) {
            return RpcResult.success("", dtoByErpCode.get(0).getWmsWarehouseCode());
        }
        return RpcResult.error("未找到该编码对应的实体仓");
    }
}
