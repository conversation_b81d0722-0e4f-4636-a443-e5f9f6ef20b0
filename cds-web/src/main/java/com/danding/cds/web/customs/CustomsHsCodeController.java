package com.danding.cds.web.customs;

import cn.hutool.core.bean.BeanUtil;
import com.danding.cds.common.enums.HsConsumptionFlagEnums;
import com.danding.cds.common.enums.HsFloatTypeEnums;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.customs.hs.api.dto.CustomsHsSubmit;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.web.customs.vo.CustomsHsCodeDetailVO;
import com.danding.cds.web.customs.vo.CustomsHsCodeVoOld;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Api(tags = "hs编码管理")
@RestController
@RequestMapping("/hsCode")
@Slf4j
public class CustomsHsCodeController {

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private DownloadProcessService downloadProcessService;


    @Autowired
    private Validator validator;

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<CustomsHsCodeVoOld> paging(CustomsHsSearchCondition condition) {
        ListVO<CustomsHsDTO> paging = customsHsService.paging(condition);
        ListVO<CustomsHsCodeVoOld> result = new ListVO<>();
        result.setPage(paging.getPage());
        List<CustomsHsCodeVoOld> dataList = new ArrayList<>();
        for (CustomsHsDTO itemDTO : paging.getDataList()) {
            CustomsHsCodeVoOld vo = new CustomsHsCodeVoOld();
            vo.setId(itemDTO.getId());
            vo.setHsCode(itemDTO.getHsCode());
            vo.setHsName(itemDTO.getHsName());
            vo.setEnable(itemDTO.getEnable());
            //数据库里存的为小数  前台展示百分比 乘以100
            vo.setVat(itemDTO.getVat().multiply(new BigDecimal(100)));
            if (itemDTO.getConsumptionFlag() == 0) {
                vo.setConsumptionDesc("不征");
            } else if (itemDTO.getConsumptionFlag() == 5) {
                vo.setConsumptionDesc("从量");
            } else if (itemDTO.getConsumptionFlag() == 10) {
                vo.setConsumptionDesc("从价");
            }
            // 消费税计征标准(0=不征；5=从量；10=从价)
            if (Objects.nonNull(itemDTO.getFloatType())) {
                vo.setConsumptionDesc("不征");
                //原从量-5，都改为<从价定率>、<浮动类型：化妆品浮动或面膜浮动>
                if (Objects.equals(itemDTO.getConsumptionFlag(), HsConsumptionFlagEnums.PRICE.getCode())) {
                    if (Objects.equals(itemDTO.getFloatType(), HsFloatTypeEnums.HZP_FLOAT.getCode()) ||
                            Objects.equals(itemDTO.getFloatType(), HsFloatTypeEnums.MM_FLOAT.getCode())) {
                        vo.setConsumptionDesc("从量");
                    } else if (Objects.equals(itemDTO.getFloatType(), HsFloatTypeEnums.NO_FLOAT.getCode())) {
                        //原从价-10，都改为<从价定率>、<浮动类型：不浮动>
                        vo.setConsumptionDesc("从价");
                    }
                }
            }
            vo.setCreateTime(new DateTime(itemDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            dataList.add(vo);
        }
        result.setDataList(dataList);
        return result;
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public Long deleteById(Long id) {
        return customsHsService.deleteById(id);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public CustomsHsCodeDetailVO detail(Long id) {
        CustomsHsCodeDetailVO detailVO = new CustomsHsCodeDetailVO();
        CustomsHsDTO hsDTO = customsHsService.findById(id);
        if (hsDTO == null) {
            return new CustomsHsCodeDetailVO();
        }
        BeanUtil.copyProperties(hsDTO, detailVO);
        detailVO.setVat(hsDTO.getVat().multiply(new BigDecimal(100)));
        detailVO.setExportTaxRate(hsDTO.getExportTaxRate().multiply(new BigDecimal(100)));
//        detailVO.setConsumptionNumTax(hsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)));
        detailVO.setConsumptionTax(hsDTO.getConsumptionTax().multiply(new BigDecimal(100)));
        detailVO.setExportDrawbackTaxRate(hsDTO.getExportDrawbackTaxRate().multiply(new BigDecimal(100)));
        if (hsDTO.getExportTentativeTaxRate() != null) {
            detailVO.setExportTentativeTaxRate(hsDTO.getExportTentativeTaxRate().multiply(new BigDecimal(100)));
        }
        detailVO.setImportDiscountTaxRate(hsDTO.getImportDiscountTaxRate().multiply(new BigDecimal(100)));
        detailVO.setImportGeneralTaxRate(hsDTO.getImportGeneralTaxRate().multiply(new BigDecimal(100)));
        if (hsDTO.getImportTentativeTaxRate() != null) {
            detailVO.setImportTentativeTaxRate(hsDTO.getImportTentativeTaxRate().multiply(new BigDecimal(100)));
        }
        if (hsDTO.getConsumptionNumTax() != null) {
            detailVO.setConsumptionNumTax(hsDTO.getConsumptionNumTax().multiply(new BigDecimal(100)));
        }
        if (Objects.nonNull(hsDTO.getFloatType())) {
            //原从量-5，都改为<从价定率>、<浮动类型：化妆品浮动或面膜浮动>
            if (Objects.equals(hsDTO.getConsumptionFlag(), HsConsumptionFlagEnums.PRICE.getCode())) {
                if (!Objects.equals(hsDTO.getFloatType(), HsFloatTypeEnums.NO_FLOAT.getCode())) {
                    //从价+面膜/化妆品/手表 映射回 从量税率
                    detailVO.setConsumptionFlag(5);
                    detailVO.setConsumptionNumTax(hsDTO.getConsumptionTax().multiply(new BigDecimal(100)));
                    detailVO.setConsumptionTax(BigDecimal.ZERO);
                } else {
                    detailVO.setConsumptionFlag(10);
                    detailVO.setConsumptionNumTax(BigDecimal.ZERO);
                }
            }
        }
        detailVO.setUpdateTime(new DateTime(hsDTO.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
        return detailVO;
    }

    @ApiOperation(value = "更新")
    @PostMapping("/upset")
    public Long upset(@RequestBody CustomsHsSubmit submit) {
        submit.setEnable(1);
        return customsHsService.upset(submit);
    }


    @ApiOperation(value = "从价从量标志")
    @GetMapping("/listConsumptionFlag")
    public List<SelectItemVO> listConsumptionFlag() {
        List<SelectItemVO> result = new ArrayList<>();
        result.add(new SelectItemVO("5", "从量"));
        result.add(new SelectItemVO("10", "从价"));
        return result;
    }

    @ApiOperation(value = "导出")
    @GetMapping("/exportExcelByDownLoadCenter")
    public Long exportExcel(CustomsHsSearchCondition condition) throws ServiceException {
        downloadProcessService.submitDownloadProcess(condition, ReportType.CUSTOMS_HS_CODE_LIST_FOR_EXCEL);
        return 1L;
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public Long importExcel(MultipartFile file) throws ArgsErrorException {
        return null;
//        ImportParams importParams = new ImportParams();
//        // 数据处理
//        importParams.setHeadRows(1);
//        importParams.setTitleRows(1);
//        List<CustomsHsCodeExportVO> successList;
//        try {
//            ExcelImportResult<CustomsHsCodeExportVO> result = ExcelImportUtil.importExcelMore(file.getInputStream(), CustomsHsCodeExportVO.class,
//                    importParams);
//            successList = result.getList();
//        } catch (Exception e) {
//            log.warn("处理异常：{}", e.getMessage(), e);
//            throw new ArgsErrorException("上传失败");
//        }
//        log.info("[op:CustomsHsCodeController-import] data={}", JSON.toJSONString(successList));
//        int i = 3;
//        for (CustomsHsCodeExportVO exportVO : successList) {
//            String title = "第" + i + "行";
//            //校验参数必填项
//            String inputError = ValidatorUtils.doValidator(validator, exportVO);
//            if (!StringUtils.isEmpty(inputError)) {
//                throw new ArgsErrorException(title + inputError);
//            }
//            CustomsUomDTO uomDTO = customsUomService.findByCode(exportVO.getFirstLegalUnit());
//            if (uomDTO == null) {
//                throw new ArgsErrorException(title + "法定第一计量单位错误");
//            }
//            if (!StringUtils.isEmpty(exportVO.getSecondLegalUnit())) {
//                uomDTO = customsUomService.findByCode(exportVO.getSecondLegalUnit());
//                if (uomDTO == null) {
//                    throw new ArgsErrorException(title + "法定第二计量单位错误");
//                }
//            }
//            List<String> consumptionFlags = Lists.newArrayList(new String[]{"5", "10"});
//            if (!consumptionFlags.contains(exportVO.getConsumptionFlag())) {
//                throw new ArgsErrorException(title + "从价/从量标志错误");
//            }
//            if ("5".equals(exportVO.getConsumptionFlag())) {
//                if (exportVO.getConsumptionNumTax() == null || StringUtils.isEmpty(exportVO.getUomName()) || exportVO.getPricePerUnit() == null) {
//                    throw new ArgsErrorException(title + "从量时，消费税率、每单位价格界限、计量单位名称不能为空");
//                }
//                if (exportVO.getConsumptionNumTax() == 0) {
//                    throw new ArgsErrorException(title + "从量时，消费税率(从量)不能等于0");
//                }
//                if (exportVO.getConsumptionTax() != null) {
//                    throw new ArgsErrorException(title + "从量时，消费税率(从价)不能有值");
//                }
//            } else if ("10".equals(exportVO.getConsumptionFlag())) {
//                if (exportVO.getConsumptionTax() == null) {
//                    throw new ArgsErrorException(title + "从价时，消费税率(从价)不能为空");
//                }
//                if (exportVO.getConsumptionNumTax() != null || exportVO.getPricePerUnit() != null || !StringUtils.isEmpty(exportVO.getUomName())) {
//                    throw new ArgsErrorException(title + "从价时，消费税率(从量)、每单位价格界限、计量单位名称不能有值");
//                }
//            }
//            i++;
//        }
//        for (CustomsHsCodeExportVO exportVO : successList) {
//            CustomsHsSubmit submit = new CustomsHsSubmit();
//            submit.setIsImported(true);
//            BeanUtil.copyProperties(exportVO, submit);
//            customsHsService.upset(submit);
//        }
//        return 1L;
    }
}
