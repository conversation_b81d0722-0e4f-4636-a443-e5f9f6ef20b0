package com.danding.cds.web.item.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/3/8 11:28
 */
@Data
public class GoodsRecordDetailBaseInfoVO {
    private String id;
    @ApiModelProperty("sku")
    private String skuId;
    @ApiModelProperty("料号")
    private String productId;
    @ApiModelProperty("账册编号")
    private Integer customsBookId;
    @ApiModelProperty("账册号")
    private String customsBookNo;
    @ApiModelProperty("渠道")
    private String channel;
    @ApiModelProperty("条码")
    private String barCode;
    @ApiModelProperty("备案名称")
    private String goodsRecordName;
    @ApiModelProperty("规格型号")
    private String model;
    @ApiModelProperty("品牌")
    private String brand;
    @ApiModelProperty("品牌英文")
    private String brandEn;
    @ApiModelProperty("申报价格")
    private BigDecimal declarePrice;
    @ApiModelProperty("申报单位")
    private String declareUnit;
    @ApiModelProperty("申报币制")
    private String declareCurrency;
    @ApiModelProperty("申报币制描述")
    private String declareCurrencyDesc;
    @ApiModelProperty("净重")
    private BigDecimal netWeight;
    @ApiModelProperty("草重")
    private BigDecimal grossWeight;
    @ApiModelProperty("")
    private String lesseeNo;
    @ApiModelProperty("")
    private String lesseeName;
    @ApiModelProperty("仓库id")
    private String warehouseId;
    @ApiModelProperty("租户名")
    private String tenantId;
    @ApiModelProperty("租户名描述")
    private String tenantNameDesc;
    @ApiModelProperty("仓库名")
    private String warehouseName;
    @ApiModelProperty("备案状态")
    private Integer recordStatus;
    @ApiModelProperty("备案状态")
    private String recordStatusDesc;
    @ApiModelProperty("长")
    private BigDecimal length;
    @ApiModelProperty("宽")
    private BigDecimal width;
    @ApiModelProperty("高")
    private BigDecimal height;
    @ApiModelProperty("hs编码")
    private String hsCode;
    @ApiModelProperty("增值税")
    private Integer vatRate;
    @ApiModelProperty("税率")
    private Integer taxRate;
    @ApiModelProperty("成分")
    private String composition;
    @ApiModelProperty("海关申报要素")
    private String hgsbys;
    @ApiModelProperty("原产国")
    private String originCountry;
    @ApiModelProperty("原产国名")
    private String originCountryName;

    @ApiModelProperty("功能")
    private String recordFunction;
    @ApiModelProperty("用途")
    private String recordUsage;
    @ApiModelProperty("法定第一计量单位")
    private String firstUnit;
    @ApiModelProperty("法定第一计量单位数量")
    private BigDecimal firstUnitAmount;
    @ApiModelProperty("法定第二计量单位")
    private String secondUnit;
    @ApiModelProperty("法定第二计量单位数量")
    private BigDecimal secondUnitAmount;
    @ApiModelProperty("是否开启")
    private Integer enable;
    @ApiModelProperty("创建时间")
    private String createTime;
    @ApiModelProperty("更新时间")
    private String updateTime;
    @ApiModelProperty("备案完成时间")
    private String recordFinishTime;
    @ApiModelProperty("国检备案号")
    private String countryRecordNo;
    @ApiModelProperty("申报要素")
    private String declareElement;
    @ApiModelProperty("原因")
    private String reason;
    @ApiModelProperty("口岸")
    private String customs;
    @ApiModelProperty("口岸")
    private String customsDesc;
    @ApiModelProperty("货品id")
    private String goodsCode;
    @ApiModelProperty("生产企业名称")
    private String productCompanyName;
    @ApiModelProperty("生产企业注册编号")
    private String productCompanyRegisterNumber;
    @ApiModelProperty("生产企业地址")
    private String productCompanyAddress;
    @ApiModelProperty("商品链接")
    private String productLink;
    @ApiModelProperty("附件")
    private String attachmentName;
    @ApiModelProperty("附件url")
    private String attachmentUrl;
    @ApiModelProperty("正面图片")
    private String frontImage;
    @ApiModelProperty("侧面图片")
    private String sideImage;
    @ApiModelProperty("背面图片")
    private String backImage;
    @ApiModelProperty("标签图片")
    private String labelImage;
    @ApiModelProperty("保质期")
    private Integer shelfLife;
    @ApiModelProperty("外部料号")
    private String externalProductId;
    @ApiModelProperty("商品备案标记")
    private Integer goodsRecordTag;
    @ApiModelProperty("商品备案标记描述")
    private String goodsRecordTagDesc;
    @ApiModelProperty(value = "备案规则")
    private List<EndangeredRuleDetailVO> rules;
    /**
     * 外部货品id
     */
    private String externalGoodsId;

    /**
     * 菜鸟货品id
     */
    private String cainiaoGoodsId;

    /**
     * 原产国放到口岸上维护
     */
    private List<String> goodsRecordTagList;
}
