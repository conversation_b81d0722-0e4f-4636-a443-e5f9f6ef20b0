package com.danding.cds.web.itemStockList.rpc;

import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockListSearch;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummaryDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummarySerach;
import com.danding.cds.itemstocklist.api.enums.DiffTypeNum;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.itemstocklist.api.service.ItemStockSummaryService;
import com.danding.cds.itemstocklist.api.vo.ItemStockListResVO;
import com.danding.cds.itemstocklist.api.vo.ItemStockSummaryResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@DubboService
@Slf4j
@RestController
@ApiModel("料号库存汇总")
public class ItemStockSummaryRpcImpl implements ItemStockSummaryRpc {

    @DubboReference
    private ItemStockListService itemStockListService;

    @DubboReference
    private ItemStockSummaryService itemStockSummaryService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @Override
    @ApiOperation(
            value = "分页查询",
            notes = "分页查询"
    )
    @SoulClient(path = "/itemstocksummary/paging", desc = "查看料号库存汇总")
    public RpcResult<ListVO<ItemStockSummaryResVO>> paging(ItemStockSummarySerach condition) {
        try {
            if (Objects.isNull(condition) || StringUtils.isEmpty(condition.getDataType())) {
                return RpcResult.error("请检查入参的数据类型是否正确");
            }
            ListVO<ItemStockSummaryDTO> pageResult = itemStockSummaryService.paging(condition);
            ListVO<ItemStockSummaryResVO> result = new ListVO<>();
            result.setPage(pageResult.getPage());
            List<ItemStockSummaryResVO> dataList = new ArrayList<>();
            for (ItemStockSummaryDTO itemStockSummaryDTO : pageResult.getDataList()) {
                ItemStockSummaryResVO itemStockSummaryResVO = new ItemStockSummaryResVO();
                BeanUtils.copyProperties(itemStockSummaryDTO, itemStockSummaryResVO);
//                itemStockSummaryResVO.setDiffType(DiffTypeNum.getEnum(itemStockSummaryDTO.getDiffType()).getDesc());
                if (Objects.nonNull(itemStockSummaryDTO.getAccountQty())) {
                    Integer diffQty = itemStockSummaryDTO.getTotalDiffQty() - itemStockSummaryDTO.getAccountQty();
                    itemStockSummaryResVO.setDiffQty(diffQty);
                }
                dataList.add(itemStockSummaryResVO);
            }
            result.setDataList(dataList);
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("查看料号库存汇总 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看料号库存汇总:" + e.getErrorMessage());
        }
    }


    @Override
    @ApiOperation(
            value = "根据查询条件导出",
            notes = "根据查询条件导出"
    )
    @SoulClient(path = "/itemstocksummary/excelExport", desc = "根据查询条件导出")
    public RpcResult<String> excelExport(ItemStockSummarySerach search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.ITEMSTOCKSUMMARY_FOR_EXCEL);
            return RpcResult.success("导出成功");
        } catch (ServiceException e) {
            log.error("料号库存汇总导出异常：{}", e.getMessage(), e);
            return RpcResult.error("导出失败");
        }
    }


    @Override
    @ApiOperation(
            value = "查看料号库存汇总总入明细",
            notes = "查看料号库存汇总总入明细"
    )
    @SoulClient(path = "/itemstocksummary/findindetails", desc = "查看料号库存汇总总入明细")
    public RpcResult<ListVO<ItemStockListResVO>> findInDetails(ItemStockSummarySerach condition) {
        try {
            if (Objects.isNull(condition) || StringUtils.isEmpty(condition.getDataType())) {
                return RpcResult.error("请检查入参的数据类型是否正确");
            }
            ListVO<ItemStockListDTO> pageResult = itemStockListService.findInDetails(condition);
            ListVO<ItemStockListResVO> result = new ListVO<>();
            result.setPage(pageResult.getPage());
            List<ItemStockListResVO> dataList = new ArrayList<>();
            for (ItemStockListDTO itemStockListDTO : pageResult.getDataList()) {
                ItemStockListResVO itemStockListResVO = new ItemStockListResVO();
                BeanUtils.copyProperties(itemStockListDTO, itemStockListResVO);
                dataList.add(itemStockListResVO);
            }
            result.setDataList(dataList);
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("查看料号库存汇总总入明细 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看料号库存汇总总入明细:" + e.getErrorMessage());
        }
    }


    @Override
    @ApiOperation(
            value = "查看料号库存汇总总出明细",
            notes = "查看料号库存汇总总出明细"
    )
    @SoulClient(path = "/itemstocksummary/findoutdetails", desc = "查看料号库存汇总总出明细")
    public RpcResult<ListVO<ItemStockListResVO>> findOutDetails(ItemStockSummarySerach condition) {
        try {
            if (Objects.isNull(condition) || StringUtils.isEmpty(condition.getDataType())) {
                return RpcResult.error("请检查入参的数据类型是否正确");
            }
            ListVO<ItemStockListDTO> pageResult = itemStockListService.findOutDetails(condition);
            ListVO<ItemStockListResVO> result = new ListVO<>();
            result.setPage(pageResult.getPage());
            List<ItemStockListResVO> dataList = new ArrayList<>();
            for (ItemStockListDTO itemStockListDTO : pageResult.getDataList()) {
                ItemStockListResVO itemStockListResVO = new ItemStockListResVO();
                BeanUtils.copyProperties(itemStockListDTO, itemStockListResVO);
                dataList.add(itemStockListResVO);
            }
            result.setDataList(dataList);
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("查看料号库存汇总总出明细 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看料号库存汇总总出明细:" + e.getErrorMessage());
        }
    }
}
