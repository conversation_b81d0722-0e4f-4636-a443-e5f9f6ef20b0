package com.danding.cds.web.item.vo;

import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

@Data
public class EndorsementImportSubmit implements Serializable {

    /**
     * 业务类型
     */
    @NotNull(message = "业务类型不能为空")
    private String businessType;

    /**
     * 清关企业ID
     */
    @NotNull(message = "清关企业不能为空")
    private Long declareCompanyId;

    /**
     * 账册ID
     */
    @NotNull(message = "账册不能为空")
    private Long accountBookId;

    /**
     * 附件url
     */
    @NotNull(message = "附件不能为空")
    private String fileUrl;
}
