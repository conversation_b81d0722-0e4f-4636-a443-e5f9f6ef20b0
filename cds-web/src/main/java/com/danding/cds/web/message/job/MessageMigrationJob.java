package com.danding.cds.web.message.job;

import com.danding.cds.message.api.dto.MessageDTO;
import com.danding.cds.message.api.service.MessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/21 11:46
 * @Description:
 */
@Slf4j
@Component
public class MessageMigrationJob extends IJobHandler {


    @DubboReference
    private MessageService messageService;

    private boolean lock = false;

    @Override
    @XxlJob(value = "MessageMigrationJob", enableTenant = false)
    public ReturnT<String> execute(String s) throws Exception {
        if (lock){
            XxlJobLogger.log("thread is lock");
            return ReturnT.SUCCESS;
        }
        lock = true;
        try {
            Date toDate = DateTime.now().minusDays(14).toDate();
            Date fromDate = DateTime.now().minusYears(3).toDate();
            Integer limit = 1000;
            List<MessageDTO> messageDTOList = messageService.listByCreateTime(fromDate,toDate,limit);
            XxlJobLogger.log("total={}", messageDTOList.size());
            for (MessageDTO messageDTO : messageDTOList) {
                messageService.migrationHistory(messageDTO.getId());
            }
        }catch (Exception e){
            log.warn("处理异常：{}", e.getMessage(), e);
            XxlJobLogger.log("exception, cause={}", e.getMessage(), e);
        }finally {
            lock = false;
        }
        return ReturnT.SUCCESS;
    }
}
