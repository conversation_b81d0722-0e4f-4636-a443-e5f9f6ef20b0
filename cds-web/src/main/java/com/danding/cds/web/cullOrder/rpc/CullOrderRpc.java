package com.danding.cds.web.cullOrder.rpc;

import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.cull.api.vo.CullOrderReqVO;
import com.danding.cds.cull.api.vo.CullOrderResVO;
import com.danding.cds.cull.api.vo.CullOrderUpdateReqVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.cds.cull.api.dto.CullOrderDTO;

import java.util.List;

public interface CullOrderRpc {

    RpcResult<ListVO<CullOrderResVO>> paging(CullOrderReqVO search);


    public RpcResult endorsementLabelEnums();


    /**
     * 忽略|批量忽略
     * @param ids 剔除单id
     */
    RpcResult<String> neglect(CullOrderUpdateReqVO reqVO);

    /**
     * 剔除单状态下拉
     * @return
     */
    RpcResult<List<SelectOptionVO>> listCullStatus();

    /**
     * 核注状态下拉
     */
    RpcResult<List<SelectOptionVO>> listReviewStatus();

    /**
     * 撤单状态下拉
     */
    RpcResult<List<SelectOptionVO>> listFinishStatus();



    /**
     * 剔除单导出
     * @param search
     * @return
     */
    RpcResult<String> export(CullOrderReqVO search);
}
