package com.danding.cds.web.v2.bean.vo.res;

import lombok.Data;

import java.util.Date;

@Data
public class ProcessTradeBookVO implements java.io.Serializable {

    /**
     * id
     */
    private Long id;


    /**
     * 企业内部编号
     */
    private String sn;

    /**
     * 预录入统一编号
     */
    private String preNo;

    /**
     * 加工贸易账册编号
     */
    private String processTradeBookNo;

    /**
     * 账册企业名称
     */
    private String companyName;

    /**
     * 数据状态
     */
    private String statusDesc;
    private Integer status;

    /**
     * 账册类型（显示名称）
     */
    private String bookTypeDesc;
    private Integer bookType;

    /**
     * 海关状态
     */
    private String customsStatus;
    private String customsStatusDesc;

    /**
     * 海关回执
     */
    private String customsReceipt;

    /**
     * 结束有效期
     */
    private Date bookEndValidity;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 启用状态
     */
    private Integer enable;
}
