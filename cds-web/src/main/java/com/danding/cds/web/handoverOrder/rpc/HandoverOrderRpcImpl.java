package com.danding.cds.web.handoverOrder.rpc;

import cn.hutool.core.bean.BeanUtil;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.exportorder.api.dto.ExportItemWritingReport;
import com.danding.cds.exportorder.api.dto.ExportOrderSubmit;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handoverOrder.api.dto.*;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.web.exportorder.manager.ExportOrderManager;
import com.danding.cds.web.handoverOrder.vo.HandoverPreviewVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.danding.ucenter.core.common.util.CopyUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

/**
 * <AUTHOR>
 * @description 提供WMS推送交接单
 * @date 2021/12/15
 */
@Slf4j
@DubboService
public class HandoverOrderRpcImpl implements HandoverOrderRpc {

    @DubboReference
    private HandoverOrderService handoverOrderService;

    @Autowired
    private ExportOrderManager exportOrderManager;

    @DubboReference
    private HandoverOrderDetailService detailService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private OrderService orderService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private ExportOrderService exportOrderService;


    @SoulClient(path = "/handoverOrder/getWarehouseInfo", desc = "获取仓库列表")
    @Override
    public RpcResult getWarehouseInfo() {
        List<WarehouseDTO> warehouseDTOList = handoverOrderService.getWarehouseInfo();
        return RpcResult.success(EnumUtils.build(warehouseDTOList, "warehouseCode", "warehouseName"));
    }

    @Override
    @SoulClient(path = "/handoverOrder/synHandoverOrder", desc = "同步交接单")
    public RpcResult handoverOrderSyn(HandoverOrderSubmit submit) throws ArgsErrorException {
        List<HandoverOrderDTO> handoverOrderDTOS = handoverOrderService.handoverOrderSyn(BeanUtil.copyProperties(submit, HandoverOrderDTO.class));
        return RpcResult.success(CopyUtil.copy(handoverOrderDTOS, HandoverPreviewVO.class));
    }

    @Override
    @SoulClient(path = "/handoverOrder/paging", desc = "分页查询交接单")
    public RpcResult<ListVO<HandoverOrderVO>> paging(HandoverOrderParam param) {
        ListVO<HandoverOrderVO> handoverOrderVOListVO = handoverOrderService.paging(param);
        return RpcResult.success(handoverOrderVOListVO);
    }

    @Override
    @SoulClient(path = "/handoverOrder/getHandoverStatusCount", desc = "获取交接单状态数据")
    public RpcResult<HandoverStatusCountResult> getHandoverStatusCount(HandoverOrderParam param) {
        try {
            HandoverStatusCountResult handoverStatusCount = handoverOrderService.getHandoverStatusCount(param);
            return RpcResult.success(handoverStatusCount);
        } catch (Exception e) {
            log.error("获取交接单状态数据 error={}", e.getMessage(), e);
            return RpcResult.error("获取交接单状态数据失败");
        }
    }

    /**
     * 生成出库单预览
     *
     * @param report
     * @return
     * @path /handoverOrder/outboundOrderPreview
     */
    @Override
    @SoulClient(path = "/handoverOrder/outboundOrderPreview", desc = "出库单预览")
    public RpcResult outboundOrderPreview(HandoverWritingReport report) throws ArgsErrorException {
        try {
            HandoverExportItemRecord exportItemRecords = handoverOrderService.outboundOrderPreview(report);
            ExportOrderSubmit submit = new ExportOrderSubmit();
            submit.setExpressIdList(exportItemRecords.getExpressList());
//            submit.setAccountBookId(report.getAccountBookId());
            submit.setEntityWarehouseCode(report.getEntityWarehouseCode());
            ExportItemWritingReport writingReport = exportOrderManager.writing(null, exportItemRecords.getExportItemRecords(), false, submit);
            log.info("[HandoverOrderDetail] 预览结果 - {}", writingReport);
            return RpcResult.success(writingReport);
        } catch (ArgsErrorException e) {
            log.error("outboundOrderPreview error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        }
    }

    /**
     * 生成出库单提交预览
     *
     * @param param
     * @return
     * @path /handoverOrder/saveExportOrder
     */
    @Override
    @SoulClient(path = "/handoverOrder/saveExportOrder", desc = "提交预览")
    public RpcResult saveExportOrder(HandoverOrderParam param) {
        //不需要这个校验了
//        if (Objects.nonNull(param.getExportItemWritingReport())){
//            if (param.getExportItemWritingReport().getFailCount() > 0){
//                return RpcResult.error("存在校验失败数据，处理后重试!");
//            }
//        }
        try {
            handoverOrderService.saveExportOrder(param);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/handoverOrder/verifyExpressCode", desc = "验证是否同一快递")
    public RpcResult verifyExpressCode(HandoverOrderParam param) {
        if (Objects.nonNull(param.getExpressCode())) {
            List<String> codes = Arrays.asList(param.getExpressCode().split(","));
            Set<String> code = new HashSet<>();
            for (String co : codes) {
                code.add(co);
            }
            if (code.size() != 1) {
                return RpcResult.error("请检查快递是否不一致！");
            }
        }
        return RpcResult.success(true);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/handoverOrder/excelExport", desc = "交接单导出")
    public RpcResult<String> excelExport(HandoverOrderParam param) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    param, ReportType.HONDOVER_ORDER_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }


    @Override
    @SoulClient(path = "/handoverOrder/packageExcelExport", desc = "包裹数据导出")
    public RpcResult<String> packageExcelExport(HandoverOrderParam param) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    param, ReportType.HANDOVER_ORDER_PACKAGE_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/handoverOrder/refresh", desc = "刷新出库单状态")
    public RpcResult<String> refresh(IdParam idParam) {
        try {
            handoverOrderService.refresh(idParam.getId());
            return RpcResult.success("刷新出库单状态成功");
        } catch (ArgsInvalidException e) {
            log.warn("处理异常：{}", e.getErrorMessage(), e);
            return RpcResult.error("刷新出库单状态失败 " + e.getErrorMessage());
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("刷新出库单状态失败");
        }
    }

}
