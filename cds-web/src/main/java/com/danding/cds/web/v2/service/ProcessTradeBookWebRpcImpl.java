package com.danding.cds.web.v2.service;

import com.danding.cds.common.model.IdEnableParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.v2.bean.enums.*;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookConsumptionSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookDetailEditReq;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookItemSearch;
import com.danding.cds.v2.bean.vo.req.ProcessTradeBookSearch;
import com.danding.cds.web.v2.api.ProcessTradeBookWebRpc;
import com.danding.cds.web.v2.bean.vo.req.*;
import com.danding.cds.web.v2.bean.vo.res.*;
import com.danding.cds.web.v2.controller.ProcessTradeBookController;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.ApiModel;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 加工贸易账册 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @menu 加工贸易账册
 * @since 2025-05-19
 */
@Slf4j
@ApiModel("加工贸易账册")
@DubboService
public class ProcessTradeBookWebRpcImpl implements ProcessTradeBookWebRpc {

    @Autowired
    private ProcessTradeBookController processTradeBookController;

    @DubboReference
    private DownloadProcessService downloadProcessService;


    /**
     * 分页查询
     *
     * @param search
     * @return
     * @path /processTradeBook/paging
     */
    @Override
    @SoulClient(path = "/processTradeBook/paging", desc = "分页查询")
    public RpcResult<ListVO<ProcessTradeBookVO>> paging(ProcessTradeBookSearch search) {
        return processTradeBookController.paging(search);
    }

    /**
     * 新增
     *
     * @param reqVO
     * @return
     * @path /processTradeBook/create
     */
    @SoulClient(path = "/processTradeBook/create", desc = "新增")
    @Override
    public RpcResult<String> create(ProcessTradeBookCreateReq reqVO) {
        return processTradeBookController.create(reqVO);
    }

    /**
     * 启用禁用
     *
     * @param idEnableParam
     * @return
     * @path /processTradeBook/enable
     */
    @SoulClient(path = "/processTradeBook/enable", desc = "启用禁用")
    @Override
    public RpcResult<String> enable(IdEnableParam idEnableParam) {
        return processTradeBookController.enable(idEnableParam);
    }

    /**
     * 修改数据状态
     *
     * @param param
     * @return
     * @path /processTradeBook/updateStatus
     */
    @SoulClient(path = "/processTradeBook/updateStatus", desc = "修改数据状态")
    @Override
    public RpcResult<String> updateStatus(ProcessTradeBookUpdStatusReq param) {
        return processTradeBookController.updateStatus(param);
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @path /processTradeBook/detail
     */
    @SoulClient(path = "/processTradeBook/detail", desc = "详情")
    @Override
    public RpcResult<ProcessTradeBookDetailVO> detail(Long id) {
        return processTradeBookController.detail(id);
    }

    /**
     * 详情编辑
     *
     * @param editReq
     * @return
     * @path /processTradeBook/editDetail
     */
    @SoulClient(path = "/processTradeBook/editDetail", desc = "详情编辑")
    @Override
    public RpcResult<String> editDetail(ProcessTradeBookDetailEditReq editReq) {
        return processTradeBookController.editDetail(editReq);
    }

    /**
     * 匹配申报表体
     *
     * @param reqVO
     * @return
     * @path /processTradeBook/matchItem
     */
    @SoulClient(path = "/processTradeBook/matchItem", desc = "匹配申报表体")
    @Override
    public RpcResult<List<ProcessTradeBookItemVO>> matchItem(ProcessTradeBookMatchItemReqVO reqVO) {
        return processTradeBookController.matchItem(reqVO);
    }

    /**
     * 表体信息分页查询
     *
     * @param param
     * @return
     * @path /processTradeBook/item/paging
     */
    @SoulClient(path = "/processTradeBook/item/paging", desc = "表体信息分页查询")
    @Override
    public RpcResult<ListVO<ProcessTradeBookItemVO>> itemPaging(ProcessTradeBookItemSearch param) {
        return processTradeBookController.itemPaging(param);
    }

    /**
     * 表体新增/编辑
     *
     * @param param
     * @return
     * @path /processTradeBook/item/createOrEdit
     */
    @SoulClient(path = "/processTradeBook/item/createOrEdit", desc = "表体新增")
    @Override
    public RpcResult<String> createOrEditItem(ProcessTradeBookItemCreateReq param) {
        return processTradeBookController.createOrEditItem(param);
    }

    /**
     * 表体删除
     *
     * @param param
     * @return
     * @path /processTradeBook/item/delete
     */
    @SoulClient(path = "/processTradeBook/item/delete", desc = "表体删除")
    @Override
    public RpcResult<String> deleteItem(IdsParam param) {
        return processTradeBookController.deleteItem(param);
    }

    /**
     * 单损耗分页查询
     *
     * @param param
     * @return
     * @path /processTradeBook/consumption/paging
     */
    @SoulClient(path = "/processTradeBook/consumption/paging", desc = "单损耗分页查询")
    @Override
    public RpcResult<ListVO<ProcessTradeBookConsumptionVO>> pagingConsumption(ProcessTradeBookConsumptionSearch param) {
        return processTradeBookController.pagingConsumption(param);
    }

    /**
     * 单损耗新增/编辑
     *
     * @param param
     * @return
     * @path /processTradeBook/consumption/createOrEdit
     */
    @SoulClient(path = "/processTradeBook/consumption/createOrEdit", desc = "单损耗新增/编辑")
    @Override
    public RpcResult<String> createOrEditConsumption(ProcessTradeBookConsumptionCreateReq param) {
        return processTradeBookController.createOrEditConsumption(param);
    }

    /**
     * 单损耗删除
     *
     * @param param
     * @return
     * @path /processTradeBook/consumption/delete
     */
    @SoulClient(path = "/processTradeBook/consumption/delete", desc = "单损耗删除")
    @Override
    public RpcResult<String> deleteConsumption(IdsParam param) {
        return processTradeBookController.deleteConsumption(param);
    }

    /**
     * 加贸账册料件导出
     *
     * @param param
     * @path /processTradeBook/item/export
     */
    @SoulClient(path = "/processTradeBook/item/export", desc = "加贸保税账册导出")
    @Override
    public RpcResult<String> export(ProcessTradeBookItemSearch param) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    param, ReportType.PROCESS_BOOK_ITEM_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 状态下拉
     *
     * @return
     * @path /processTradeBook/listStatus
     */
    @SoulClient(path = "/processTradeBook/listStatus", desc = "状态下拉")
    @Override
    public RpcResult<List<EnumUtils>> listStatus() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookStatusEnums.class, "code", "desc"));
    }

    /**
     * 账册类型下拉
     *
     * @return
     * @path /processTradeBook/listBookType
     */
    @SoulClient(path = "/processTradeBook/listBookType", desc = "账册类型下拉")
    @Override
    public RpcResult<List<EnumUtils>> listBookType() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookTypeEnums.class, "code", "desc"));
    }

    /**
     * 账册用途下拉
     *
     * @return
     * @path /processTradeBook/listBookUsage
     */
    @SoulClient(path = "/processTradeBook/listBookUsage", desc = "账册用途下拉")
    @Override
    public RpcResult<List<EnumUtils>> listBookUsage() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookUsageEnums.class, "code", "desc"));
    }

    /**
     * 单耗申报状态下拉
     *
     * @return
     * @path /processTradeBook/listDeclareStatus
     */
    @SoulClient(path = "/processTradeBook/listDeclareStatus", desc = "单耗申报状态下拉")
    @Override
    public RpcResult<List<EnumUtils>> listDeclareStatus() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookConsumptionDeclareStatusEnums.class, "code", "desc"));
    }

    /**
     * 单耗申报环节下拉
     *
     * @return
     * @path /processTradeBook/listConsumptionDeclarationLink
     */
    @SoulClient(path = "/processTradeBook/listConsumptionDeclarationLink", desc = "单耗申报环节下拉")
    @Override
    public RpcResult<List<EnumUtils>> listConsumptionDeclarationLink() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookConsumptionDeclareLinkEnums.class, "code", "desc"));
    }

    /**
     * 企业执行标志下拉
     *
     * @return
     * @path /processTradeBook/listCompanyExecutionFlag
     */
    @SoulClient(path = "/processTradeBook/listCompanyExecutionFlag", desc = "企业执行标志下拉")
    @Override
    public RpcResult<List<EnumUtils>> listCompanyExecutionFlag() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookCompanyExecutionFlagEnums.class, "code", "desc"));
    }

    /**
     * 海关执行标志下拉
     *
     * @return
     * @path /processTradeBook/listCustomsExecutionFlag
     */
    @SoulClient(path = "/processTradeBook/listCustomsExecutionFlag", desc = "海关执行标志下拉")
    @Override
    public RpcResult<List<EnumUtils>> listCustomsExecutionFlag() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookCustomsExecutionFlagEnums.class, "code", "desc"));
    }

    /**
     * 重点商品标识下拉
     *
     * @return
     * @path /processTradeBook/listFocusMark
     */
    @SoulClient(path = "/processTradeBook/listFocusMark", desc = "重点商品标识下拉")
    @Override
    public RpcResult<List<EnumUtils>> listFocusMark() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookFocusMarkEnums.class, "code", "desc"));
    }

    /**
     * 单耗质疑标志下拉
     *
     * @return
     * @path /processTradeBook/listConsumptionQuestionFlag
     */
    @SoulClient(path = "/processTradeBook/listConsumptionQuestionFlag", desc = "单耗质疑标志下拉")
    @Override
    public RpcResult<List<EnumUtils>> listConsumptionQuestionFlag() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookConsumptionQuestionFlagEnums.class, "code", "desc"));
    }

    /**
     * 磋商标志下拉
     *
     * @return
     * @path /processTradeBook/listNegotiationFlag
     */
    @SoulClient(path = "/processTradeBook/listNegotiationFlag", desc = "磋商标志下拉")
    @Override
    public RpcResult<List<EnumUtils>> listNegotiationFlag() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookNegotiationFlagEnums.class, "code", "desc"));
    }

    /**
     * 数量控制标志下拉
     *
     * @return
     * @path /processTradeBook/listQtyControlFlag
     */
    @SoulClient(path = "/processTradeBook/listQtyControlFlag", desc = "数量控制标志下拉")
    @Override
    public RpcResult<List<EnumUtils>> listQtyControlFlag() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookItemQtyControlFlagEnums.class, "code", "desc"));
    }

    /**
     * 申报企业类型下拉
     *
     * @return
     * @path /processTradeBook/listDeclareCompanyType
     */
    @SoulClient(path = "/processTradeBook/listDeclareCompanyType", desc = "申报企业类型下拉")
    @Override
    public RpcResult<List<EnumUtils>> listDeclareCompanyType() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookDeclareCompanyTypeEnums.class, "code", "desc"));
    }

    /**
     * 核销方式下拉
     *
     * @return
     * @path /processTradeBook/listVoucherClearanceMethod
     */
    @SoulClient(path = "/processTradeBook/listVoucherClearanceMethod", desc = "核销方式下拉")
    @Override
    public RpcResult<List<EnumUtils>> listVoucherClearanceMethod() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookVClearanceMethodEnums.class, "code", "desc"));
    }

    /**
     * 核销类型下拉
     *
     * @return
     * @path /processTradeBook/listVoucherClearanceType
     */
    @SoulClient(path = "/processTradeBook/listVoucherClearanceType", desc = "核销类型下拉")
    @Override
    public RpcResult<List<EnumUtils>> listVoucherClearanceType() {
        return RpcResult.success(EnumUtils.build(ProcessTradeBookVClearanceTypeEnums.class, "code", "desc"));
    }

    /**
     * 可编辑账册状态下拉
     *
     * @return
     * @path /processTradeBook/listUpdateStatus
     */
    @Override
    @SoulClient(path = "/processTradeBook/listUpdateStatus", desc = "可编辑账册状态下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listUpdateStatus() {
        List<ProcessTradeBookStatusEnums> statusList = Arrays.asList(ProcessTradeBookStatusEnums.CREATED,
                ProcessTradeBookStatusEnums.RECORDED, ProcessTradeBookStatusEnums.RECORD_EXCEPTION);
        List<SelectOptionVO<Integer>> list = statusList.stream().map(e -> {
            SelectOptionVO<Integer> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(e.getCode());
            selectOptionVO.setName(e.getDesc());
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 征免方式下拉
     *
     * @return
     * @path /processTradeBook/listDutyExemptionMethod
     */
    @SoulClient(path = "/processTradeBook/listDutyExemptionMethod", desc = "征免方式下拉")
    @Override
    public RpcResult<List<EnumUtils>> listDutyExemptionMethod() {
        return RpcResult.success(EnumUtils.build(CustomsLvrlfModeEnums.class, "code", "desc"));
    }

    /**
     * 成品序号下拉
     *
     * @param id 加贸账册id
     * @return
     * @path /processTradeBook/listEndprdSeqById
     */
    @SoulClient(path = "/processTradeBook/listEndprdSeqById", desc = "成品序号下拉")
    @Override
    public RpcResult<List<ProcessTradeBookItemSelectVO>> listEndprdSeqById(Long id) {
        return processTradeBookController.listEndprdSeqById(id);
    }


    /**
     * 料件序号下拉
     *
     * @param id 加贸账册id
     * @return
     * @path /processTradeBook/listMtpckSeqById
     */
    @SoulClient(path = "/processTradeBook/listMtpckSeqById", desc = "料件序号下拉")
    @Override
    public RpcResult<List<ProcessTradeBookItemSelectVO>> listMtpckSeqById(Long id) {
        return processTradeBookController.listMtpckSeqById(id);
    }
}
