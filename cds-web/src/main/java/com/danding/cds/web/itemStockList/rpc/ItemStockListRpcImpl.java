package com.danding.cds.web.itemStockList.rpc;

import cn.hutool.core.date.DateTime;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.enums.InventoryInOutEnum;
import com.danding.cds.item.api.dto.CarryforwardCustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CarryforwardCustomsBookService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockListSearch;
import com.danding.cds.itemstocklist.api.enums.AbnormalTypeEnum;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.itemstocklist.api.vo.ItemStockListResVO;
import com.danding.cds.web.itemStockList.controller.ItemStockListController;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;


@DubboService
@Slf4j
@RestController
@ApiModel("料号库存流水")
public class ItemStockListRpcImpl implements ItemStockListRpc {

    @Autowired
    private ItemStockListController itemStockListController;

    @DubboReference
    private ItemStockListService itemStockListService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CarryforwardCustomsBookService carryforwardCustomsBookService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private EndorsementService endorsementService;

    @Override
    @ApiOperation(
            value = "分页查询",
            notes = "分页查询"
    )
    @SoulClient(path = "/itemstocklist/paging", desc = "查看料号库存流水")
    public RpcResult<ListVO<ItemStockListResVO>> paging(ItemStockListSearch condition) {
        try {
            ListVO<ItemStockListDTO> pageResult = itemStockListService.paging(condition);
            ListVO<ItemStockListResVO> result = new ListVO<>();
            result.setPage(pageResult.getPage());
            List<ItemStockListResVO> dataList = new ArrayList<>();
            for (ItemStockListDTO itemStockListDTO : pageResult.getDataList()) {
                ItemStockListResVO itemStockListResVO = new ItemStockListResVO();
                itemStockListResVO.setId(itemStockListDTO.getId());
                itemStockListResVO.setCustomsRecordProductId(itemStockListDTO.getCustomsRecordProductId());
                itemStockListResVO.setGoodsSeqNo(itemStockListDTO.getGoodsSeqNo());
                itemStockListResVO.setGoodsName(itemStockListDTO.getGoodsName());
                CustomsBookDTO customsBookDTO = customsBookService.findById(itemStockListDTO.getBookId());
                if (Objects.nonNull(customsBookDTO)) {
                    itemStockListResVO.setBookNo(customsBookDTO.getBookNo());
                }
                itemStockListResVO.setSn(itemStockListDTO.getSn());
                itemStockListResVO.setRealOrderNo(itemStockListDTO.getRealOrderNo());
                if (!StringUtils.isEmpty(itemStockListDTO.getInveBusinessType())) {
                    itemStockListResVO.setInveBusinessTypeName(EndorsementBussiness.getEnum(itemStockListDTO.getInveBusinessType()).getDesc());
                }
                itemStockListResVO.setInOutFlagName(InventoryInOutEnum.getEnum(itemStockListDTO.getInOutFlag()).getDesc());
                itemStockListResVO.setEventQty(itemStockListDTO.getEventQty());
                if (!StringUtils.isEmpty(itemStockListDTO.getAbnormalType())) {
                    itemStockListResVO.setAbnormalTypeName(AbnormalTypeEnum.getEnum(itemStockListDTO.getAbnormalType()).getDesc());
                }
                itemStockListResVO.setAssociatedInEndorsementNo(itemStockListDTO.getAssociatedInEndorsementNo());
                itemStockListResVO.setAssociatedOutEndorsementNo(itemStockListDTO.getAssociatedOutEndorsementNo());
                itemStockListResVO.setInAccountBook(itemStockListDTO.getInAccountBook());
                itemStockListResVO.setOutAccountBook(itemStockListDTO.getOutAccountBook());
                if (!StringUtils.isEmpty(itemStockListDTO.getInAccountBook())) {
                    itemStockListResVO.setInCustomsArea(itemStockListDTO.getInAccountBook().substring(1, 5));
                }
                if (!StringUtils.isEmpty(itemStockListDTO.getOutAccountBook())) {
                    itemStockListResVO.setOutCustomsArea(itemStockListDTO.getOutAccountBook().substring(1, 5));
                }
                if (!StringUtils.isEmpty(itemStockListDTO.getInAccountBook())) {
                    CarryforwardCustomsBookDTO carryforwardCustomsBookDTO = carryforwardCustomsBookService.findByCode(itemStockListDTO.getInAccountBook());
                    if (Objects.nonNull(carryforwardCustomsBookDTO)) {
                        itemStockListResVO.setInPlatformName(carryforwardCustomsBookDTO.getPlatformName());
                    }
                }
                if (!StringUtils.isEmpty(itemStockListDTO.getOutAccountBook())) {
                    CarryforwardCustomsBookDTO carryforwardCustomsBookDTO = carryforwardCustomsBookService.findByCode(itemStockListDTO.getOutAccountBook());
                    if (Objects.nonNull(carryforwardCustomsBookDTO)) {
                        itemStockListResVO.setOutPlatformName(carryforwardCustomsBookDTO.getPlatformName());
                    }
                }
                if (!StringUtils.isEmpty(itemStockListDTO.getEventTime())) {
                    itemStockListResVO.setEventTime(new DateTime(itemStockListDTO.getEventTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                dataList.add(itemStockListResVO);
            }
            result.setDataList(dataList);
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("查看料号库存流水 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看料号库存流水错误:" + e.getErrorMessage());
        }
    }


    @Override
    @ApiOperation(
            value = "删除料号库存流水",
            notes = "删除料号库存流水"
    )
    @SoulClient(path = "/itemstocklist/deleteByIds", desc = "删除料号库存流水")
    public RpcResult<String> deleteByIds(IdsParam param) {
        try {
            for (Long id : param.getIdList()) {
                itemStockListService.deletedById(id);
            }
            return RpcResult.success("删除料号库存流水操作成功");
        } catch (ArgsErrorException e) {
            return RpcResult.error("删除料号库存流水操作失败：" + e.getErrorMessage());
        }
    }

    @Override
    @ApiOperation(
            value = "批量撤销异常",
            notes = "批量撤销异常"
    )
    @SoulClient(path = "/itemstocklist/cancelAbnormalTypeByIds", desc = "批量撤销异常")
    public RpcResult<String> cancelAbnormalTypeByIds(IdsParam param) {
        try {
            for (Long id : param.getIdList()) {
                itemStockListService.cancelAbnormalTypeById(id);
            }
            return RpcResult.success("批量撤销异常操作成功");
        } catch (ArgsErrorException e) {
            return RpcResult.error("批量撤销异常操作失败：" + e.getErrorMessage());
        }
    }

    @Override
    @ApiOperation(
            value = "根据查询条件导出",
            notes = "根据查询条件导出"
    )
    @SoulClient(path = "/itemstocklist/excelExport", desc = "根据查询条件导出")
    public RpcResult<String> excelExport(ItemStockListSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.ITEMSTOCKLIST_FOR_EXCEL);
            return RpcResult.success("导出成功");
        } catch (ServiceException e) {
            log.error("料号库存流水导出异常：{}", e.getMessage(), e);
            return RpcResult.error("导出失败");
        }
    }


}
