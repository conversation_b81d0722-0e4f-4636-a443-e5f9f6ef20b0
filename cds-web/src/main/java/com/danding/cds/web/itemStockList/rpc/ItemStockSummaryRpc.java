package com.danding.cds.web.itemStockList.rpc;

import com.danding.cds.itemstocklist.api.dto.ItemStockSummarySerach;
import com.danding.cds.itemstocklist.api.vo.ItemStockListResVO;
import com.danding.cds.itemstocklist.api.vo.ItemStockSummaryResVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface ItemStockSummaryRpc {
    RpcResult<ListVO<ItemStockSummaryResVO>> paging(ItemStockSummarySerach condition);
    RpcResult<String> excelExport(ItemStockSummarySerach search);
    RpcResult<ListVO<ItemStockListResVO>> findInDetails(ItemStockSummarySerach condition);
    RpcResult<ListVO<ItemStockListResVO>> findOutDetails(ItemStockSummarySerach condition);
}
