package com.danding.cds.web.invenorder.vo;

import com.danding.cds.invenorder.api.dto.InventoryOrderItemRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class InventoryOrderItemImportSubmit {
    @ApiModelProperty("清关单ID")
    private Long id;

    @ApiModelProperty("要导入的列表")
    private List<InventoryOrderItemRecord> recordList;
}
