package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.common.enums.InventoryTransitEnums;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemExtra;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.service.*;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.cds.web.invenorder.vo.InventoryOrderItemImportVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * 清关单表体导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_INV_ORDER_ITEM", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%B8%85%E5%85%B3%E5%8D%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFV2.0.xls",
        groups = {@ParkImportsHandler.Group(name = "sheet1", classes = InventoryOrderItemImportVO.class),})
public class InvOrderItemImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long inventoryOrderId = Long.parseLong((String) extendMap.get("inventoryOrderId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        InventoryOrderInfoService inventoryOrderInfoService = this.getBean(InventoryOrderInfoService.class);
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);
        GoodsRecordAssociateService goodsRecordAssociateService = this.getBean(GoodsRecordAssociateService.class);
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        JdServProviderService jdServProviderService = this.getBean(JdServProviderService.class);
        JdGoodsRecordService jdGoodsRecordService = this.getBean(JdGoodsRecordService.class);
        CustomsBookItemService customsBookItemService = this.getBean(CustomsBookItemService.class);
        CustomsBookService customsBookService = this.getBean(CustomsBookService.class);

        List<InventoryOrderItemImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("sheet1")) {
                list = group.getDataList(InventoryOrderItemImportVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("BizDeclareFormItemImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<InventoryOrderItemDTO> successList = new ArrayList<>();
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        boolean isLockStock = Objects.equals(infoDTO.getLockStockFlag(), 1);
        List<InventoryOrderItemDTO> itemDTOList = inventoryOrderInfoService.findListByInvenOrderId(inventoryOrderId);
        Map<String, BigDecimal> itemProductIdCountMap = new HashMap<>();
        for (InventoryOrderItemDTO inventoryOrderItemDTO : itemDTOList) {
            itemProductIdCountMap.put(inventoryOrderItemDTO.getOriginProductId(), inventoryOrderItemDTO.getDeclareUnitQfy());
        }
        Boolean isTransitMaster = this.isTransitMaster(infoDTO);
        JdServProviderDTO jdServProviderDTO = jdServProviderService.getServProviderByBookId(infoDTO.getBookId());
        CustomsBookDTO customsBookDTO = customsBookService.findById(infoDTO.getBookId());

        List<String> newAddList = new ArrayList<>();
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        boolean isNew;
        for (InventoryOrderItemImportVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            try {
                String validErrorMsg = this.doValidator(vo);
                if (StrUtil.isNotBlank(validErrorMsg)) {
                    this.callbackData(false, index++, String.join(";", validErrorMsg), vo);
                    continue;
                }
                if (isLockStock) {
                    this.callbackData(false, index++, "库存已锁定，请先释放锁定库存", vo);
                    continue;
                }
                //判断申报单价和币值是否都为空 或者都填了
                if ((vo.getDeclarePrice() == null) == (vo.getCurrency() != null)) {
                    errorMsg.add("统一料号[" + vo.getProductId() + "],申报单价或币制填充时，两者任意不可为空;");
                } else if (vo.getCurrency() != null) {
                    if (!currencyMap.containsKey(vo.getCurrency())) {
                        errorMsg.add("统一料号[" + vo.getProductId() + "]币制编码不存在;");
                    }
                }
                if (vo.getDeclarePrice() != null && vo.getDeclarePrice().compareTo(BigDecimal.ZERO) <= 0) {
                    errorMsg.add("统一料号[" + vo.getProductId() + "]申报单价不能小于0;");
                }
                if (Objects.isNull(vo.getDeclareQty())) {
                    errorMsg.add("统一料号[" + vo.getProductId() + "]申报数量不能为空;");
                }
                GoodsRecordDTO goodsRecordDTO = null;
                if (isTransitMaster) {
                    //中转新品，没有账册维度
                    List<GoodsRecordDTO> goodsRecordDTOList = goodsRecordService.findDescListByProId(vo.getProductId());
                    if (CollectionUtils.isEmpty(goodsRecordDTOList)) {
                        errorMsg.add("系统商品备案不存在统一料号为[" + vo.getProductId() + "]的商品信息;");
                    } else {
                        goodsRecordDTO = goodsRecordDTOList.get(0);
                    }
                } else if (StringUtils.isEmpty(vo.getGoodsSeqNo()) && Objects.nonNull(jdServProviderDTO)) {
                    try {
                        JdGoodsRecordCustoms jdGoodsRecordCustoms = jdGoodsRecordService.getGoodsInfoByItemCode(vo.getProductId(), jdServProviderDTO.getCode(), true);
                        if (jdGoodsRecordCustoms == null) {
                            errorMsg.add("京东备案管理不存在商品货号为[" + vo.getProductId() + "]备案完成的备案信息;");
                        }
                    } catch (ArgsErrorException e) {
                        errorMsg.add(e.getErrorMessage());
                    }
                } else {
                    goodsRecordDTO = goodsRecordService.findByBookIdAndProId(infoDTO.getBookId(), vo.getProductId());
                    if (Objects.isNull(goodsRecordDTO)) {
                        errorMsg.add("系统商品备案内不存在统一料号为[" + vo.getProductId() + "],账册编号为[" + customsBookDTO.getBookNo() + "]的商品信息");
                    }
                }
                //根据备案序号是否存在区分是否为新品
                if (!StringUtils.isEmpty(vo.getGoodsSeqNo())) {
                    isNew = false; //老品
                    CustomsBookItemDTO customsBookItemDTO;
                    if (StringUtils.isNotEmpty(vo.getCustomsRecordProductId())) {
                        customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(infoDTO.getBookId(), vo.getGoodsSeqNo(), vo.getCustomsRecordProductId());
                    } else {
                        customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNo(infoDTO.getBookId(), vo.getGoodsSeqNo());
                    }
                    if (customsBookItemDTO == null) {
                        isNew = true;
                        errorMsg.add("系统海关账册[" + customsBookDTO.getBookNo() + "]内不存在备案序号为[" + vo.getGoodsSeqNo() + "]的商品信息;");
                    }

                } else {
                    isNew = true; //新品
                    List<CustomsBookItemDTO> bookItemDTOList = customsBookItemService.findByBookIdAndProId(infoDTO.getBookId(), vo.getProductId());
                    if (CollectionUtils.isNotEmpty(bookItemDTOList)) {
                        isNew = false;
                        errorMsg.add("系统海关账册[" + customsBookDTO.getBookNo() + "]内已存在料号为[" + vo.getProductId() + "]的商品信息，不属于新品;");
                    }
                }
                if (isNew) {
                    if (!isTransitMaster && Objects.nonNull(goodsRecordDTO)) {
                        String finalProductId = goodsRecordAssociateService.getFinalProductId(goodsRecordDTO, customsBookDTO.getId());
                        //新品
                        if (!StringUtils.isEmpty(vo.getCustomsRecordProductId())) {
                            //海关备案存在 - 校验是否一致
                            if (!Objects.equals(vo.getCustomsRecordProductId(), finalProductId)) {
                                errorMsg.add("系统商品备案不存在账册编号为[" + customsBookDTO.getBookNo() + "],统一料号为[" + vo.getProductId() + "],海关备案料号为[" + vo.getCustomsRecordProductId() + "]的商品信息");
                            }
                        }
                    }
                }
                String tagTemp = (!StringUtils.isEmpty(vo.getGoodsSeqNo()) ? vo.getGoodsSeqNo() : "无") + "-" + vo.getProductId();
                if (newAddList.contains(tagTemp)) {
                    errorMsg.add("重复的商品;");
                }
                if (newAddList.size() >= 999) {
                    errorMsg.add("清关单表体不允许超999条");
                }
                if (CollUtil.isNotEmpty(errorMsg)) {
                    this.callbackData(false, index++, String.join(";", errorMsg), vo);
                    continue;
                }
                InventoryOrderItemDTO inventoryOrderItemDTO = buildInventoryOrderItemDTO(vo, infoDTO, isNew);
                String tag = (!StringUtils.isEmpty(vo.getGoodsSeqNo()) ? vo.getGoodsSeqNo() : "无") + "-" + vo.getProductId();
                newAddList.add(tag);
                successList.add(inventoryOrderItemDTO);
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("清关单表体导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        if (successList.size() == list.size()) {
            String operator = this.getOperator();
            inventoryOrderInfoService.importExcel(inventoryOrderId, successList, operator);
        }
    }

    private Boolean isTransitMaster(InventoryOrderInfoDTO infoDTO) {
        if (Objects.isNull(infoDTO)) {
            return false;
        }
        return Objects.equals(infoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode());
    }

    private InventoryOrderItemDTO buildInventoryOrderItemDTO(InventoryOrderItemImportVO vo, InventoryOrderInfoDTO infoDTO, Boolean isNew) {
        String oldOrNew = StrUtil.isBlank(vo.getGoodsSeqNo()) ? "new" : "old";
        CustomsGoodsService customsGoodsService = this.getBean(CustomsGoodsService.class);
        List<CustomsGoodsItemInfoDTO> customsGoodsItemInfoDTOList = customsGoodsService.findItemDetailByV3(oldOrNew,
                vo.getProductId(), vo.getCustomsRecordProductId(), null,
                String.valueOf(infoDTO.getBookId()), vo.getGoodsSeqNo(), infoDTO.getId());
        CustomsGoodsItemInfoDTO customsGoodsItemInfoDTO = customsGoodsItemInfoDTOList.get(0);
        InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
        BeanUtils.copyProperties(customsGoodsItemInfoDTO, inventoryOrderItemDTO);
        inventoryOrderItemDTO.setIsNew(customsGoodsItemInfoDTO.getOldOrNew());
        inventoryOrderItemDTO.setRefInveOrderId(infoDTO.getId());
        inventoryOrderItemDTO.setRefInveOrderSn(infoDTO.getInveCustomsSn());
        inventoryOrderItemDTO.setPlanDeclareQty(vo.getDeclareQty());
        if (this.isTransitMaster(infoDTO) && StringUtil.isNotEmpty(vo.getCustomsRecordProductId())) {
            inventoryOrderItemDTO.setProductId(vo.getCustomsRecordProductId());
        }
        if (StringUtil.isNotBlank(vo.getCurrency())) {
            inventoryOrderItemDTO.setCurrency(vo.getCurrency());
        }
        if (StringUtil.isNotBlank(vo.getGoodsModel())) {
            inventoryOrderItemDTO.setGoodsModel(vo.getGoodsModel());
        }
        BigDecimal declarePrice = vo.getDeclarePrice() == null ? inventoryOrderItemDTO.getDeclarePrice() : vo.getDeclarePrice();
        BigDecimal declareQfy = vo.getDeclareQty() == null ? BigDecimal.ZERO : vo.getDeclareQty();
        inventoryOrderItemDTO.setDeclareUnitQfy(declareQfy);
        inventoryOrderItemDTO.setTotalGrossWeight(declareQfy.multiply(inventoryOrderItemDTO.getGrossWeight()).setScale(5, RoundingMode.HALF_UP));
        inventoryOrderItemDTO.setTotalNetWeight(declareQfy.multiply(inventoryOrderItemDTO.getNetweight()).setScale(5, RoundingMode.HALF_UP));
        if (Objects.nonNull(declarePrice)) {
            inventoryOrderItemDTO.setDeclareTotalPrice(declarePrice.multiply(declareQfy).setScale(4, RoundingMode.HALF_UP));
        }
        if (vo.getDeclarePrice() != null) {
            inventoryOrderItemDTO.setDeclarePrice(vo.getDeclarePrice());
        }
        InventoryOrderItemExtra extra = new InventoryOrderItemExtra();
        BeanUtils.copyProperties(inventoryOrderItemDTO, extra);
        inventoryOrderItemDTO.setExtraJson(JSON.toJSONString(extra));
        return inventoryOrderItemDTO;
    }
}
