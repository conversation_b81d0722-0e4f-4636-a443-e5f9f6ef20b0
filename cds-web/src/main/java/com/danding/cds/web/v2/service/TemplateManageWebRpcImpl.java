package com.danding.cds.web.v2.service;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.district.api.service.CustomsDistrictService;
import com.danding.cds.customs.manager.api.dto.CustomsDistrictDTO;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.template.api.dto.TemplateManageDTO;
import com.danding.cds.template.api.dto.TemplateManageSearch;
import com.danding.cds.template.api.dto.TemplateManageSubmit;
import com.danding.cds.template.api.service.TemplateManageService;
import com.danding.cds.web.v2.api.TemplateManageWebRpc;
import com.danding.cds.web.v2.bean.result.TemplateManageResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description: 模板管理中心
 * @date 2025/7/31 11:11
 */
@Slf4j
@DubboService
@RestController
public class TemplateManageWebRpcImpl implements TemplateManageWebRpc {

    @DubboReference
    private TemplateManageService templateManageService;
    @DubboReference
    private CustomsDictionaryService customsDictionaryService;
    @DubboReference
    private CustomsDistrictService customsDistrictService;

    @Override
    @SoulClient(path = "/template/paging", desc = "模板管理分页查询")
    @PostMapping("/template/paging")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<ListVO<TemplateManageResult>> paging(TemplateManageSearch search) {
        try {
            if (search == null) {
                return RpcResult.error("查询参数不能为空");
            }

            ListVO<TemplateManageDTO> serviceResult = templateManageService.paging(search);

            // 转换service层结果为web层结果
            ListVO<TemplateManageResult> result = new ListVO<>();
            result.setPage(serviceResult.getPage());

            List<TemplateManageResult> dataList = serviceResult.getDataList().stream()
                    .map(dto -> ConvertUtil.beanConvert(dto, TemplateManageResult.class))
                    .collect(Collectors.toList());
            result.setDataList(dataList);

            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("模板管理分页查询参数错误: {}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("模板管理分页查询异常", e);
            return RpcResult.error("查询失败");
        }
    }

    @Override
    @SoulClient(path = "/template/upload", desc = "上传模板")
    @PostMapping("/template/upload")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Long> upload(TemplateManageSubmit submit) {
        try {
            // 参数校验
            RpcResult<String> validationResult = validateSubmit(submit, false);
            if (!validationResult.getCode().equals(200)) {
                return RpcResult.error(validationResult.getMessage());
            }

            // 文件URL校验
            if (!StringUtils.hasText(submit.getFileUrl())) {
                return RpcResult.error("文件URL不能为空");
            }

            // 文件名校验
            if (!StringUtils.hasText(submit.getFileName())) {
                return RpcResult.error("文件名不能为空");
            }

            // 转换web层参数为service层参数
            TemplateManageSubmit serviceSubmit = ConvertUtil.beanConvert(submit, TemplateManageSubmit.class);
            serviceSubmit.setId(null); // 确保是新增

            Long templateId = templateManageService.upload(serviceSubmit);
            return RpcResult.success(templateId);
        } catch (ArgsErrorException e) {
            log.error("上传模板参数错误: {}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("上传模板异常", e);
            return RpcResult.error("上传失败");
        }
    }

    @Override
    @SoulClient(path = "/template/edit", desc = "编辑模板")
    @PostMapping("/template/edit")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Long> edit(TemplateManageSubmit submit) {
        try {
            // 参数校验
            RpcResult<String> validationResult = validateSubmit(submit, true);
            if (!validationResult.getCode().equals(200)) {
                return RpcResult.error(validationResult.getMessage());
            }

            // 如果有新文件URL，进行校验
            if (StringUtils.hasText(submit.getFileUrl()) && !StringUtils.hasText(submit.getFileName())) {
                return RpcResult.error("文件URL和文件名必须同时提供");
            }

            // 转换web层参数为service层参数
            TemplateManageSubmit serviceSubmit = ConvertUtil.beanConvert(submit, TemplateManageSubmit.class);

            Long templateId = templateManageService.edit(serviceSubmit);
            return RpcResult.success(templateId);
        } catch (ArgsErrorException e) {
            log.error("编辑模板参数错误: {}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("编辑模板异常", e);
            return RpcResult.error("编辑失败");
        }
    }

    @Override
    @SoulClient(path = "/template/getById", desc = "根据ID查询模板详情")
    @PostMapping("/template/getById")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<TemplateManageResult> getById(IdParam idParam) {
        try {
            if (idParam == null || idParam.getId() == null) {
                return RpcResult.error("模板ID不能为空");
            }

            TemplateManageDTO dto = templateManageService.getById(idParam.getId());
            if (dto == null) {
                return RpcResult.error("模板不存在");
            }

            // 转换service层结果为web层结果
            TemplateManageResult result = ConvertUtil.beanConvert(dto, TemplateManageResult.class);

            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("查询模板详情异常", e);
            return RpcResult.error("查询失败");
        }
    }

    @Override
    @SoulClient(path = "/template/listTemplatePurpose", desc = "获取模板用途下拉列表")
    @PostMapping("/template/listTemplatePurpose")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO<String>>> listTemplatePurpose() {
        try {
            // 从数据字典获取模板用途数据
            List<SelectOptionVO<String>> purposeList = customsDictionaryService.listByType(DataDictionaryTypeEnums.TEMPLATE_PURPOSE.getValue());
            return RpcResult.success(purposeList);
        } catch (Exception e) {
            log.error("获取模板用途下拉列表异常", e);
            return RpcResult.error("获取用途列表失败");
        }
    }

    @Override
    @SoulClient(path = "/template/listEnabledPorts", desc = "获取启用口岸下拉列表")
    @PostMapping("/template/listEnabledPorts")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO<String>>> listEnabledPorts() {
        try {
            // 获取启用的口岸列表（listAll方法已经过滤了启用状态的口岸）
            List<CustomsDistrictDTO> enabledPorts = customsDistrictService.listAll();

            List<SelectOptionVO<String>> portList = enabledPorts.stream()
                    .map(port -> {
                        SelectOptionVO<String> option = new SelectOptionVO<>();
                        option.setId(port.getCode());
                        option.setName(port.getName());
                        return option;
                    })
                    .collect(Collectors.toList());

            return RpcResult.success(portList);
        } catch (Exception e) {
            log.error("获取启用口岸下拉列表异常", e);
            return RpcResult.error("获取口岸列表失败");
        }
    }

    @Override
    @SoulClient(path = "/template/delete", desc = "删除模板")
    @PostMapping("/template/delete")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Boolean> delete(IdParam idParam) {
        try {
            if (idParam == null || idParam.getId() == null) {
                return RpcResult.error("模板ID不能为空");
            }

            Boolean result = templateManageService.delete(idParam.getId());
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("删除模板参数错误: {}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("删除模板异常", e);
            return RpcResult.error("删除失败");
        }
    }

    /**
     * 校验提交参数
     *
     * @param submit 提交参数
     * @param isEdit 是否为编辑操作
     * @return 校验结果
     */
    private RpcResult<String> validateSubmit(TemplateManageSubmit submit, boolean isEdit) {
        if (submit == null) {
            return RpcResult.error("提交参数不能为空");
        }

        if (isEdit && submit.getId() == null) {
            return RpcResult.error("编辑时模板ID不能为空");
        }

        // 模板名称校验
        if (!StringUtils.hasText(submit.getTemplateName())) {
            return RpcResult.error("模板名称不能为空");
        }
        if (submit.getTemplateName().length() > 64) {
            return RpcResult.error("模板名称长度不能超过64个字符");
        }
        if (submit.getTemplateName().contains(" ")) {
            return RpcResult.error("模板名称不允许包含空格");
        }

        // 用途校验
        if (!StringUtils.hasText(submit.getPurpose())) {
            return RpcResult.error("用途不能为空");
        }

        // 口岸校验
        if (!StringUtils.hasText(submit.getPorts())) {
            return RpcResult.error("口岸不能为空");
        }

        // 备注校验（非必填）
        if (StringUtils.hasText(submit.getRemark())) {
            if (submit.getRemark().length() > 64) {
                return RpcResult.error("备注长度不能超过64个字符");
            }
            if (submit.getRemark().contains(" ")) {
                return RpcResult.error("备注不允许包含空格");
            }
        }

        return RpcResult.success("校验通过");
    }


}
