package com.danding.cds.web.invenorder;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.danding.cds.checklist.api.dto.ChecklistDTO;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.common.enums.*;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.common.utils.ThreadContextUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.refund.api.enums.RefundOrderEnum;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.service.FileOperationService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.*;
import com.danding.cds.invenorder.api.service.InventoryOrderAttachService;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.api.service.InventoryOrderLogService;
import com.danding.cds.invenorder.api.service.InventoryOrderTallyReportService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.CustomsGoodsItemInfoDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.CustomsGoodsService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.out.bean.RpcResult;
import com.danding.cds.out.bean.vo.req.InventoryOrderAttachUploadReq;
import com.danding.cds.out.bean.vo.req.InventoryOrderInfoCwSubmitReqVO;
import com.danding.cds.out.bean.vo.req.InventoryOrderInfoSubmitReqVo;
import com.danding.cds.out.bean.vo.req.InventoryOrderItemUpdateReq;
import com.danding.cds.ownerMapping.api.service.OrderOwnerMappingService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.api.BizDeclareFormService;
import com.danding.cds.v2.bean.dto.BizDeclareFormItemDTO;
import com.danding.cds.v2.bean.dto.InventoryOrderItemGoodsDTO;
import com.danding.cds.v2.bean.enums.*;
import com.danding.cds.v2.bean.vo.req.*;
import com.danding.cds.v2.enums.InventoryOrderDraftCompareTypeEnums;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.cds.web.invenorder.manager.InventoryOrderItemManager;
import com.danding.cds.web.invenorder.rpc.InventoryOrderRpc;
import com.danding.cds.web.invenorder.vo.*;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderCreateReqVo;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderItemGoodsEditVO;
import com.danding.cds.web.v2.bean.vo.res.InventoryOrderItemGoodsVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Predicate;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Consumer;
import java.util.stream.Collectors;

@Api(tags = "清关单管理")
@RestController
@RequestMapping("/invenorder")
@Slf4j
public class InventoryOrderController {
    @Autowired
    private InventoryOrderItemManager inventoryOrderItemManager;
    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private SequenceService sequenceService;
    @DubboReference
    private InventoryOrderLogService inventoryOrderLogService;
    @DubboReference
    private InventoryOrderAttachService inventoryOrderAttachService;
    @DubboReference
    private FileOperationService fileOperationService;
    @DubboReference
    private CustomsUomService customsUomService;
    @DubboReference
    private EndorsementService endorsementService;
    @DubboReference
    private CustomsInventoryService customsInventoryService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private CustomsCountryService customsCountryService;
    @DubboReference
    private CustomsGoodsService customsGoodsService;
    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private RefundOrderService refundOrderService;

    @DubboReference
    private InventoryOrderTallyReportService inventoryOrderTallyReportService;
    @DubboReference
    private OrderOwnerMappingService orderOwnerMappingService;

    @DubboReference
    private CollaborateOrderService collaborateOrderService;

    @DubboReference
    private InventoryOrderRpc inventoryOrderRpc;

    @DubboReference
    private ChecklistService checklistService;

    @DubboReference
    private BizDeclareFormService bizDeclareFormService;
    @DubboReference
    private GoodsRecordAssociateService goodsRecordAssociateService;
    @DubboReference
    private CustomsDictionaryService customsDictionaryService;


    @PostMapping("/test")
    public void test(@RequestBody InventoryOrderCreateReqVo reqVo) {
//        InventoryOrderCreateReqVo req = new InventoryOrderCreateReqVo();
//        req.setApplyPerson("111")
//        	.setBookId(2L)
//        	.setInveBusinessType("ONLINE_IN")
//        	.setInveCompanyId(1L)
//        	.setTransportMode("OTHER")
//        	.setEntryExitCustoms("1221")
//        	.setShipmentCountry("142")
//        	.setEntityWarehouseCode("HKSc552d3")
//        	.setEntityWarehouseName("海南一号仓")
//        	.setOwnerCode("HainanHuozhu1")
//        	.setOwnerName("海南保税货主1")
//        	.setTransitFlag(1)
//        	.setFinalInveCompanyId(2L)
//        	.setFinalBookId(9L)
//        	.setFinalEntityWarehouseCode("JHS9fb15c")
//        	.setFinalEntityWarehouseName("金义保税二号仓")
//        	.setFinalOwnerCode("test")
//        	.setFinalOwnerName("22222")
//        	;
        System.out.println(JSON.toJSONString(reqVo));
        inventoryOrderRpc.createInventoryOrderV2(reqVo);
    }

    @ApiOperation(value = "清关单单分页查询", response = InventoryOrderInfoVO.class)
    @GetMapping("/paging")
    @Deprecated
    public ListVO<InventoryOrderInfoVO> paging(InventoryOrderSearch search) {
        ListVO<InventoryOrderInfoDTO> list = inventoryOrderInfoService.paging(search);
        ListVO<InventoryOrderInfoVO> result = new ListVO<>();
        result.setPage(list.getPage());
        result.setDataList(list.getDataList().stream().map((InventoryOrderInfoDTO item) -> {
            InventoryOrderInfoVO vo = new InventoryOrderInfoVO();
            BeanUtils.copyProperties(item, vo);
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(item.getChannel())) {
                vo.setChannelBusinessSn(!StringUtils.isEmpty(item.getChannelBusinessSn()) ? item.getChannelBusinessSn() : "如需要联系技术添加");
            }
            if (InventoryOrderEnum.STATUS_COMPLETE.getCode().equalsIgnoreCase(vo.getStatus())) {
                vo.setCompleteTime(vo.getStatusTime());
            }
            vo.setInveBusinessTypeDesc(InventoryOrderBusinessEnum.getEnum(item.getInveBusinessType()).getDesc());
            List<EndorsementDTO> endorsementDTOList = endorsementService.listByInventory(item.getId());
            List<CheckItemInfo> checkItemInfoList = endorsementDTOList.stream().map(e -> {
                CheckItemInfo checkItemInfo = new CheckItemInfo();
                checkItemInfo.setPreCheckOrderNo(e.getPreOrderNo());
                checkItemInfo.setCheckOrderNo(e.getSn());
                checkItemInfo.setCheckStatus(EndorsementOrderStatus.getEnum(e.getStatus()).getDesc());
                checkItemInfo.setCreateTime(e.getCreateTime());
                return checkItemInfo;
            }).collect(Collectors.toList());
            vo.setListCheckItemInfo(checkItemInfoList);
            CompanyDTO company = companyService.findById(item.getInveCompanyId());
            if (company != null) {
                vo.setInveCompanyName(company.getName());
            }
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }


    @ApiOperation(value = "获取相关业务类型")
    @GetMapping("/list-bussiness-type")
    public List<SelectItemVO> listBussinessType() {
        List<SelectItemVO> result = Arrays.stream(InventoryOrderBusinessEnum.values()).filter((InventoryOrderBusinessEnum item) -> !item.equals(InventoryOrderBusinessEnum.BUSSINESS_EMPTY))
                .map((InventoryOrderBusinessEnum item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取所状态")
    @GetMapping("/list-status")
    public List<SelectItemVO> listStatus() {
        List<SelectItemVO> result = Arrays.stream(InventoryOrderEnum.values()).filter((InventoryOrderEnum item) -> !item.equals(InventoryOrderEnum.EMPTY))
                .map((InventoryOrderEnum item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取相关单类型")
    @GetMapping("/list-relation-status")
    public List<SelectItemVO> listRelationStatus() {
        List<SelectItemVO> result = Arrays.stream(InventoryOrderRelationEnum.values()).filter((InventoryOrderRelationEnum item) -> !item.equals(InventoryOrderRelationEnum.REL_TYPE_EMPTY))
                .map((InventoryOrderRelationEnum item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取可用清关单列表")
    @GetMapping("/list-select-invertory-order")
    public List<SelectItemVO> listCanInvertory() {
        List<InventoryOrderInfoDTO> list = inventoryOrderInfoService.findCanRefEndorsement();
        List<SelectItemVO> result = list.stream()
                .map((InventoryOrderInfoDTO item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getId() + "[" + item.getInveBusinessType() + "]");
                    optionDTO.setName(item.getInveCustomsSn());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "清关单导出", response = InventoryOrderInfoVO.class)
    @GetMapping("/export")
    public void export(InventoryOrderSearch search, HttpServletResponse response) {
        List<InventoryOrderInfoDTO> list = inventoryOrderInfoService.findList(search);
        if (list == null) {
            list = new ArrayList<>();
        }
        /**
         *
         */
        List<InventoryOrderInfoExcel> tempList =
                list.stream().map((InventoryOrderInfoDTO item) -> {
                    InventoryOrderInfoExcel vo = new InventoryOrderInfoExcel();
                    BeanUtils.copyProperties(item, vo);
                    if (InventoryOrderEnum.STATUS_COMPLETE.getCode().equalsIgnoreCase(item.getStatus())) {
                        vo.setCompleteTime(item.getStatusTime());
                    }
                    InventoryOrderEnum orderEnum = InventoryOrderEnum.getEnum(item.getStatus());
                    vo.setStatus(orderEnum.getDesc());
                    return vo;
                }).collect(Collectors.toList());
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("清关单数据列表");
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, InventoryOrderInfoExcel.class, tempList);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(
                    ("清关单数据导出" + DateTime.now().toString("yyMMddHHmm") + ".xlsx").getBytes(), "iso8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
        }
    }

    @Deprecated
    @ApiOperation(value = "创建清关单(第一步)")
    @GetMapping("/create-inventory-order")
    public Response<InventoryOrderInfoDTO> createInventoryOrder(InventoryOrderInfoDTO infoDTO) {
        Response<InventoryOrderInfoDTO> response = new Response<>();
        if (infoDTO.getInveCompanyId() == null) {
            response.setCode(-1);
            response.setErrorMessage("清关企业不能为空");
            return response;
        }
        CompanyDTO companyDTO = companyService.findById(infoDTO.getInveCompanyId());
        if (companyDTO == null) {
            response.setCode(-1);
            response.setErrorMessage("清关企业参数非法");
            return response;
        }
        if (infoDTO.getBookId() == null) {
            response.setCode(-1);
            response.setErrorMessage("账册不能为空");
            return response;
        }
        CustomsBookDTO customsBookDTO = customsBookService.findById(infoDTO.getBookId());
        if (customsBookDTO == null) {
            response.setCode(-1);
            response.setErrorMessage("账册参数非法");
            return response;
        }
        if (Objects.equals(infoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())) {
            if (StringUtil.isEmpty(infoDTO.getOwnerCode())) {
                response.setCode(-1);
                response.setErrorMessage("货主不能为空");
                return response;
            }
            if (StringUtil.isEmpty(infoDTO.getEntityWarehouseCode())) {
                response.setCode(-1);
                response.setErrorMessage("实体仓不能为空");
                return response;
            }
        }
        infoDTO.setRentPerson("");
        infoDTO.setInveCustomsSn(sequenceService.generateInventoryOrderSn());
        infoDTO.setStatus(InventoryOrderEnum.STATUS_CREATED.getCode());
        infoDTO.setStatusTime(new Date());
        infoDTO.setCreateTime(new Date());
        infoDTO.setUpdateTime(new Date());
        infoDTO.setEnable(1);
        infoDTO.setChannel(InventoryOrderChannel.CCS_SELF.getValue());
        //自建的单子不会有InveCustomsSn 不知道为什么这样写
        infoDTO.setId(NumberUtils.createLong(org.apache.commons.lang3.StringUtils.replacePattern(infoDTO.getInveCustomsSn(), "[a-zA-Z]", "")));
        InventoryOrderBusinessEnum orderBusinessEnum = InventoryOrderBusinessEnum.getEnum(infoDTO.getInveBusinessType());
        if (Objects.nonNull(orderBusinessEnum)) {
            if (orderBusinessEnum.equals(InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN)) {
                infoDTO.setCustomsFlag(InventoryCustomsFlagEnum.CUSTOMS.getCode());
            } else {
                infoDTO.setCustomsFlag(InventoryCustomsFlagEnum.NOT_CUSTOMS.getCode());
            }
        }
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.createInventoryOrderInfoDTO(infoDTO);
        response = new Response<>(inventoryOrderInfoDTO);
        //去创建协同单
        switch (orderBusinessEnum) {
            case BUSSINESS_SECTION_IN:
            case BUSSINESS_SECTIONINNER_IN:
            case BUSSINESS_ONELINE_IN:
                collaborateOrderService.saveCollaborateOrder(inventoryOrderInfoDTO.getInveCustomsSn());
                break;
            default:
                break;
        }
        return response;
    }

    @Deprecated
    @ApiOperation(value = "创建清关单(第二步)", response = InventoryOrderInfoDTO.class)
    @PostMapping("/build-inventory-order-item")
    public Response<InventoryOrderInfoDTO> buildInventoryOrderItem(@RequestBody InventoryOrderItemsParam param) {
        log.info("保存清关单 param={}", JSON.toJSONString(param));
        Response<InventoryOrderInfoDTO> oret;
        Long invenOrderId = param.getInvenOrderId();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(invenOrderId);
        List<InventoryOrderItemDTO> oldListOrderItems = inventoryOrderInfoService.findListByInvenOrderId(invenOrderId);
        inventoryOrderInfoDTO.setListItems(oldListOrderItems);
        if (inventoryOrderInfoDTO == null) {
            oret = new Response<InventoryOrderInfoDTO>();
            oret.setCode(-1);
            oret.setErrorMessage("校验清关单失败");
            return oret;
        }
        if (!inventoryOrderInfoDTO.getStatus().equalsIgnoreCase(InventoryOrderEnum.STATUS_CREATED.getCode())
                && !inventoryOrderInfoDTO.getStatus().equalsIgnoreCase(InventoryOrderEnum.STATUS_PERFECT.getCode())) {
            oret = new Response<InventoryOrderInfoDTO>();
            oret.setCode(-1);
            oret.setErrorMessage("清关单当前状态不能操作表体");
            return oret;
        }
        List<InventoryOrderItemParam> listOrderItems = param.getListOrderItems();
        if (CollectionUtils.isEmpty(listOrderItems)) {
            oret = new Response<InventoryOrderInfoDTO>();
            oret.setCode(-1);
            oret.setErrorMessage("清关单明细为空");
            return oret;
        }
        Map<String, Long> productCount = listOrderItems.stream().collect(Collectors.groupingBy(InventoryOrderItemParam::getProductId, Collectors.counting()));
        List<String> repeatProductIdList = productCount.keySet().stream().filter(k -> productCount.get(k) > 1).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(repeatProductIdList)) {
            String repeatProductId = repeatProductIdList.stream().collect(Collectors.joining("/"));
            oret = new Response<>();
            oret.setCode(-1);
            oret.setErrorMessage("料号:" + repeatProductId + "存在重复");
            return oret;
        }
        Map<String, Long> skuCount = listOrderItems.stream().collect(Collectors.groupingBy(InventoryOrderItemParam::getSkuId, Collectors.counting()));
        List<String> repeatSkuList = skuCount.keySet().stream().filter(k -> skuCount.get(k) > 1).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(repeatSkuList)) {
            String repeatSkuId = repeatSkuList.stream().collect(Collectors.joining("/"));
            oret = new Response<>();
            oret.setCode(-1);
            oret.setErrorMessage("料号:" + repeatSkuId + "存在重复");
            return oret;
        }
        List<InventoryOrderItemDTO> listItems = new ArrayList<InventoryOrderItemDTO>();
        for (InventoryOrderItemParam inventoryOrderItemParam : listOrderItems) {
            inventoryOrderItemParam.setRefInveOrderId(invenOrderId);
            inventoryOrderItemParam.setRefInveOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
            InventoryOrderItemExtra extra = new InventoryOrderItemExtra();
            BeanUtils.copyProperties(inventoryOrderItemParam, extra);
            if (Objects.isNull(inventoryOrderItemParam.getFirstUnitQfy())) {
                extra.setFirstUnitQfy(BigDecimal.ZERO.setScale(5, BigDecimal.ROUND_HALF_UP));
            } else {
                extra.setFirstUnitQfy(new BigDecimal(inventoryOrderItemParam.getFirstUnitQfy()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.isNull(inventoryOrderItemParam.getSecondUnitQfy())) {
                extra.setSecondUnitQfy(BigDecimal.ZERO.setScale(5, BigDecimal.ROUND_HALF_UP));
            } else {
                extra.setSecondUnitQfy(new BigDecimal(inventoryOrderItemParam.getSecondUnitQfy()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.isNull(inventoryOrderItemParam.getNetweight())) {
                extra.setNetweight(BigDecimal.ZERO.setScale(5, BigDecimal.ROUND_HALF_UP));
            } else {
                extra.setNetweight(new BigDecimal(inventoryOrderItemParam.getNetweight()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.isNull(inventoryOrderItemParam.getGrossWeight())) {
                extra.setGrossWeight(BigDecimal.ZERO.setScale(5, BigDecimal.ROUND_HALF_UP));
            } else {
                extra.setGrossWeight(new BigDecimal(inventoryOrderItemParam.getGrossWeight()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
            inventoryOrderItemDTO.setId(inventoryOrderItemParam.getId());
            inventoryOrderItemDTO.setRefInveOrderId(inventoryOrderItemParam.getRefInveOrderId());
            inventoryOrderItemDTO.setRefInveOrderSn(inventoryOrderItemParam.getRefInveOrderSn());
            inventoryOrderItemDTO.setIsNew(inventoryOrderItemParam.getOldOrNew());
            inventoryOrderItemDTO.setProductId(inventoryOrderItemParam.getProductId());
            inventoryOrderItemDTO.setGoodsSeqNo(inventoryOrderItemParam.getGoodsSeqNo());
            inventoryOrderItemDTO.setGoodsName(inventoryOrderItemParam.getGoodsName());
            inventoryOrderItemDTO.setDeclarePrice(new BigDecimal(inventoryOrderItemParam.getDeclarePrice()).setScale(4, BigDecimal.ROUND_HALF_UP));
            inventoryOrderItemDTO.setRecordProductName(inventoryOrderItemParam.getRecordProductName());
            inventoryOrderItemDTO.setHsCode(inventoryOrderItemParam.getHsCode());
            inventoryOrderItemDTO.setDeclareUnitQfy(new BigDecimal(inventoryOrderItemParam.getDeclareUnitQfy()).setScale(4, BigDecimal.ROUND_HALF_UP));
            inventoryOrderItemDTO.setExtraJson(JSON.toJSONString(extra));
            Integer id = UserUtils.getUserId();
            inventoryOrderItemDTO.setCreateBy(id);
            inventoryOrderItemDTO.setUpdateBy(id);
            inventoryOrderItemDTO.setCreateTime(new Date());
            inventoryOrderItemDTO.setUpdateTime(new Date());
            inventoryOrderItemDTO.setDangerousFlag(inventoryOrderItemParam.getDangerousFlag());
            log.info("保存清关单 inventoryOrderItemDTO={}", JSON.toJSONString(inventoryOrderItemDTO));
            listItems.add(inventoryOrderItemDTO);
        }
        inventoryOrderInfoService.updateInventoryOrderInfoDTO(inventoryOrderInfoDTO, listItems);
        inventoryOrderInfoDTO.setListItems(listItems);
        oret = new Response<InventoryOrderInfoDTO>(inventoryOrderInfoDTO);
        return oret;
    }


    @ApiOperation(value = "编辑清关单表头", response = InventoryOrderInfoDTO.class)
    @PostMapping("/editInventoryHead")
    public void editInventoryHead(@RequestBody InventoryOrderEditReqVo editReqVo) {
        inventoryOrderInfoService.updateInventoryOrderInfoHead(editReqVo);
    }


    @ApiOperation(value = "删除关联单证号", response = InventoryOrderInfoDTO.class)
    @GetMapping("/delete-inventory-order-relation")
    public Response deleteInventoryOrderRelation(@RequestParam(required = true) Long id) {
        InventoryOrderRelationDTO inventoryOrderRelationDTO = inventoryOrderInfoService.findInventoryOrderRelationDtoById(id);
        inventoryOrderInfoService.deleteInventoryOrderRelation(Collections.singletonList(id));
        //获取清关单详情
        return new Response<>(buildCustomsGoodsItemInfoDTOList(inventoryOrderRelationDTO.getRefInveOrderId()));
    }

    @ApiOperation(value = "编辑关联单证号")
    @GetMapping("/update-inventory-order-relation")
    public Response updateInventoryOrderRelation(@RequestParam(required = true, name = "id") Long id, @RequestParam(required = true, name = "relNo") String relNo) {
        Response oret;
        InventoryOrderRelationDTO inventoryOrderRelationDTO = inventoryOrderInfoService.findInventoryOrderRelationDtoById(id);
        if (inventoryOrderRelationDTO == null) {
            oret = new Response<Boolean>(false);
            oret.setErrorMessage("查询对象为空");
            oret.setCode(-1);
            return oret;
        }
        List<InventoryOrderRelationDTO> relationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(inventoryOrderRelationDTO.getRefInveOrderId());
        for (InventoryOrderRelationDTO relationDTO : relationDTOList) {
            if (relationDTO.getRelNo().equals(relNo) && !relationDTO.getId().equals(id)) {
                oret = new Response<Boolean>(false);
                oret.setErrorMessage("与原有关联运单重复");
                oret.setCode(-1);
                return oret;
            }
        }
        String relType = inventoryOrderRelationDTO.getRelType();
        if (InventoryOrderRelationEnum.REL_TYPE_YUNDAN.getCode().equalsIgnoreCase(relType)) {
            List<CustomsInventoryDTO> listCustomsInventory = customsInventoryService.listByLogistics90Days(relNo);
            if (CollectionUtils.isEmpty(listCustomsInventory)) {
                oret = new Response<Boolean>(false);
                oret.setErrorMessage("运单无法查询到相关的清单信息");
                oret.setCode(-1);
                return oret;
            } else if (listCustomsInventory.size() > 1) {
                oret = new Response<Boolean>(false);
                oret.setErrorMessage("运单查询到相关的清单信息多条");
                oret.setCode(-1);
                return oret;
            }
        }
        InventoryOrderRelationDTO _inventoryOrderRelationDTO = new InventoryOrderRelationDTO();
        _inventoryOrderRelationDTO.setId(id);
        _inventoryOrderRelationDTO.setRelNo(relNo);
        if (!Objects.equals(UserUtils.getUserId(), 0)) {
            _inventoryOrderRelationDTO.setUpdateBy(UserUtils.getUserId());
        }
        inventoryOrderInfoService.update(_inventoryOrderRelationDTO);
        //获取清关单详情
        return new Response<>(buildCustomsGoodsItemInfoDTOList(inventoryOrderRelationDTO.getRefInveOrderId()));
    }

    @ApiOperation(value = "批量创建相关单证号", response = InventoryOrderInfoDTO.class)
    @PostMapping("/build-inventory-order-relation")
    public Response buildInventoryOrderRelation(@RequestBody InventoryOrderRelationParam param) {
        Response oret;
        Long invenOrderId = param.getInvenOrderId();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(invenOrderId);
        if (inventoryOrderInfoDTO == null) {
            oret = new Response();
            oret.setCode(-1);
            oret.setErrorMessage("校验清关单失败");
            return oret;
        }
        if (org.apache.commons.lang3.StringUtils.isEmpty(param.getRelType())) {
            oret = new Response();
            oret.setCode(-1);
            oret.setErrorMessage("单证类型不能为空");
            return oret;
        }
        List<String> listRelNo = param.getRelNos();
        if (CollectionUtils.isEmpty(listRelNo)) {
            oret = new Response();
            oret.setCode(-1);
            oret.setErrorMessage("单证表体不能为空");
            return oret;
        }
        long count = listRelNo.stream().distinct().count();
        boolean isRepeat = count < listRelNo.size();
        if (isRepeat) {
            oret = new Response();
            oret.setCode(-1);
            oret.setErrorMessage("单证表体有重复数据");
            return oret;
        }
        List<InventoryOrderRelationDTO> relationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(invenOrderId);
        List<String> dbRelNoList = relationDTOList.stream().map(InventoryOrderRelationDTO::getRelNo).collect(Collectors.toList());
        if (!Collections.disjoint(dbRelNoList, listRelNo)) {
            oret = new Response();
            oret.setCode(-1);
            oret.setErrorMessage("提交单证表体跟原有数据有重复");
            return oret;
        }
        List<InventoryOrderRelationDTO> listItems = new ArrayList<InventoryOrderRelationDTO>();
        /**
         * 如果是运单，就判断该运单是否存在
         */
        if (InventoryOrderRelationEnum.REL_TYPE_YUNDAN.getCode().equalsIgnoreCase(param.getRelType())) {
            for (String relNo : listRelNo) {
                if (!StringUtils.isEmpty(relNo) && CollectionUtils.isEmpty(this.customsInventoryService.listByLogistics90Days(relNo))) {
                    oret = new Response();
                    oret.setCode(-1);
                    oret.setErrorMessage("运单号[" + relNo + "]无法查询到相关清单数据");
                    return oret;
                }
            }
            refundOrderService.updateStatusBatchByMailNo(listRelNo, RefundOrderEnum.STATUS_ENUM.STATUS_ENDORSEMENT_WAIT_CREATE.getValue(),
                    inventoryOrderInfoDTO.getStatus(), inventoryOrderInfoDTO.getInveCustomsSn());
        }
        for (String relNo : listRelNo) {
            if (!StringUtils.isEmpty(relNo)) {
                InventoryOrderRelationDTO inventoryOrderRelationDTO = new InventoryOrderRelationDTO();
                inventoryOrderRelationDTO.setRefInveOrderId(invenOrderId);
                inventoryOrderRelationDTO.setRefInveOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
                inventoryOrderRelationDTO.setRelNo(relNo);
                inventoryOrderRelationDTO.setRelType(param.getRelType());
                inventoryOrderRelationDTO.setCreateBy(UserUtils.getUserId());
                inventoryOrderRelationDTO.setUpdateBy(UserUtils.getUserId());
                inventoryOrderRelationDTO.setCreateTime(new Date());
                inventoryOrderRelationDTO.setUpdateTime(new Date());
                listItems.add(inventoryOrderRelationDTO);
            }
        }
        inventoryOrderInfoService.upset(listItems);
        //获取清关单详情
        return new Response<>(buildCustomsGoodsItemInfoDTOList(invenOrderId));
    }

    /**
     * 根据退货入区清关单构建清关单详情
     *
     * @param invenOrderId
     * @return
     */
    private List<CustomsGoodsItemInfoDTO> buildCustomsGoodsItemInfoDTOList(Long invenOrderId) {
        log.info("[buildCustomsGoodsItemInfoDTOList-step1] invenOrderId={}", invenOrderId);
        Map<Long, CustomsGoodsItemInfoDTO> customsGoodsItemInfoDTOMap = new HashMap<>();
        List<InventoryOrderRelationDTO> relationDTOList = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(invenOrderId);
        log.info("[buildCustomsGoodsItemInfoDTOList-step2] relationDTOList={}", JSON.toJSONString(relationDTOList));
        for (InventoryOrderRelationDTO relationDTO : relationDTOList) {
            List<CustomsInventoryDTO> inventoryDTOList = customsInventoryService.listByLogistics90Days(relationDTO.getRelNo());
            if (inventoryDTOList.size() < 1) {
                continue;
            }
            CustomsInventoryDTO customsInventoryDTO = inventoryDTOList.get(0);
            List<CustomsInventoryItemDTO> itemDTOList = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
            log.info("[buildCustomsGoodsItemInfoDTOList-step3] relationDTO={},itemDTOList={}", JSON.toJSONString(relationDTO), JSON.toJSONString(itemDTOList));
            for (CustomsInventoryItemDTO customsInventoryItemDTO : itemDTOList) {
                CustomsGoodsItemInfoDTO goodsItemInfoDTO;
                if (customsGoodsItemInfoDTOMap.containsKey(customsInventoryItemDTO.getBookItemId())) {
                    //存在只更新数量并覆盖
                    goodsItemInfoDTO = customsGoodsItemInfoDTOMap.get(customsInventoryItemDTO.getBookItemId());
                    BigDecimal declareUnitQfy = goodsItemInfoDTO.getDeclareUnitQfy().add(BigDecimal.valueOf(customsInventoryItemDTO.getCount()));
                    goodsItemInfoDTO.setDeclareUnitQfy(declareUnitQfy);
                } else {
                    //不存在新增
                    CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(customsInventoryItemDTO.getBookItemId());
                    String productId = customsBookItemDTO.getProductId();
                    String extraJson = customsInventoryItemDTO.getExtraJson();
                    CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
                    if (Objects.nonNull(customsInventoryItemExtra.getUnifiedProductId())) {
                        productId = customsInventoryItemExtra.getUnifiedProductId();
                    }
                    GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(customsBookItemDTO.getCustomsBookId(), productId);
                    goodsItemInfoDTO = new CustomsGoodsItemInfoDTO();
                    goodsItemInfoDTO.setProductId(customsBookItemDTO.getProductId());
                    goodsItemInfoDTO.setOldOrNew("old");
                    goodsItemInfoDTO.setGoodsSeqNo(customsBookItemDTO.getGoodsSeqNo());
                    goodsItemInfoDTO.setNetweight(new BigDecimal(0));
                    goodsItemInfoDTO.setOriginCountry(customsBookItemDTO.getOriginCountry());
                    CustomsCountryDTO countryDTO = customsCountryService.findByCode(customsBookItemDTO.getOriginCountry());
                    if (Objects.nonNull(countryDTO)) {
                        goodsItemInfoDTO.setOriginCountryDesc(countryDTO.getName());
                    }
                    goodsItemInfoDTO.setDeclareTotalPrice(customsInventoryItemDTO.getUnitPrice().multiply(new BigDecimal(customsInventoryItemDTO.getCount())));
                    goodsItemInfoDTO.setHsCode(customsBookItemDTO.getHsCode());
                    goodsItemInfoDTO.setUnit(customsBookItemDTO.getGoodsUnit());
                    CustomsUomDTO customsUomDTO = customsUomService.findByCode(customsBookItemDTO.getGoodsUnit());
                    if (Objects.nonNull(customsUomDTO)) {
                        goodsItemInfoDTO.setUnitDesc(customsUomDTO.getName());
                    }
                    goodsItemInfoDTO.setGoodsModel(customsBookItemDTO.getGoodsModel());
                    goodsItemInfoDTO.setDeclareUnitQfy(BigDecimal.valueOf(customsInventoryItemDTO.getCount()));
                    goodsItemInfoDTO.setFirstUnit(customsBookItemDTO.getFirstUnit());
                    customsUomDTO = customsUomService.findByCode(customsBookItemDTO.getFirstUnit());
                    if (Objects.nonNull(customsUomDTO)) {
                        goodsItemInfoDTO.setFirstUnitDesc(customsUomDTO.getName());
                    }
                    goodsItemInfoDTO.setSecondUnit(customsBookItemDTO.getSecondUnit());
                    customsUomDTO = customsUomService.findByCode(customsBookItemDTO.getSecondUnit());
                    if (Objects.nonNull(customsUomDTO)) {
                        goodsItemInfoDTO.setSecondUnitDesc(customsUomDTO.getName());
                    }
                    goodsItemInfoDTO.setGoodsName(customsBookItemDTO.getGoodsName());
                    goodsItemInfoDTO.setCountryRecordNo("");
                    goodsItemInfoDTO.setCurrency(customsBookItemDTO.getCurrCode());
                    CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(customsBookItemDTO.getCurrCode());
                    if (Objects.nonNull(currencyDTO)) {
                        goodsItemInfoDTO.setCurrencyDesc(currencyDTO.getName());
                    }
                    goodsItemInfoDTO.setDeclarePrice(customsBookItemDTO.getDeclarePrice());
                    goodsItemInfoDTO.setProductCompany("");
                    goodsItemInfoDTO.setOrderVersion("");//add
                    goodsItemInfoDTO.setDestinationCountry("142");//add
                    goodsItemInfoDTO.setDestinationCountryDesc("中国");
                    goodsItemInfoDTO.setAvoidTaxMethod("3");//add
                    goodsItemInfoDTO.setRecordProductName(customsBookItemDTO.getGoodsName());
                    if (goodsRecordDTO != null) {
                        goodsItemInfoDTO.setOriginProductId(goodsRecordDTO.getProductId());
                        goodsItemInfoDTO.setSkuId(goodsRecordDTO.getSkuId());
                        goodsItemInfoDTO.setFirstUnitQfy(goodsRecordDTO.getFirstUnitAmount());
                        goodsItemInfoDTO.setSecondUnitQfy(goodsRecordDTO.getSecondUnitAmount());
                        goodsItemInfoDTO.setNetweight(goodsRecordDTO.getNetWeight());
                        goodsItemInfoDTO.setGrossWeight(goodsRecordDTO.getGrossWeight());
                        goodsItemInfoDTO.setDeclareFactor(goodsRecordDTO.getHgsbys());
                        goodsItemInfoDTO.setCountryRecordNo(goodsRecordDTO.getCountryRecordNo());
                        goodsItemInfoDTO.setRecordProductName(goodsRecordDTO.getGoodsRecordName());
                        goodsItemInfoDTO.setGoodsBar(goodsRecordDTO.getBarCode());
                    }
                }
                customsGoodsItemInfoDTOMap.put(customsInventoryItemDTO.getBookItemId(), goodsItemInfoDTO);
            }
        }
        return new ArrayList<>(customsGoodsItemInfoDTOMap.values());
    }

    @ApiOperation(value = "下载附件记录日志")
    @GetMapping("/trace-log-download")
    public Response<Boolean> traceLogDownload(@RequestParam(required = true) Long id) {
        Response<Boolean> oret;
        InventoryOrderAttachDTO _findItem = inventoryOrderAttachService.findById(id);
        if (_findItem == null) {
            oret = new Response<>(false);
            oret.setCode(-1);
            oret.setErrorMessage("非法参数，操作失败");
            return oret;
        }
        inventoryOrderAttachService.traceLogDownload(id);
        oret = new Response<>(true);
        return oret;
    }

    @ApiOperation(value = "作废清关单")
    @GetMapping("/discard")
    public Response<String> discard(@RequestParam(required = true) Long id) {
        Response<String> oret;
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        if (inventoryOrderInfoDTO == null) {
            oret = new Response<>("查询对象为空");
            oret.setErrorMessage("查询对象为空");
            oret.setCode(-1);
            return oret;
        }
        List<String> carryOutAllowStatusList = Arrays.asList(InventoryOrderEnum.STATUS_CONFIRMING.getCode(),
                InventoryOrderEnum.STATUS_AUDITED.getCode(),
                InventoryOrderEnum.STATUS_ENDORSEMENT.getCode(),
                InventoryOrderEnum.STATUS_FAILURE.getCode());
        if (Boolean.TRUE.equals(InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(),
                InventoryOrderTagEnums.TO_B_PART_CARRYOVER))) {
            // B单分区结转 作废校验
            if (!carryOutAllowStatusList.contains(inventoryOrderInfoDTO.getStatus())) {
                oret = new Response<>("当前结转单状态不允许作废");
                oret.setErrorMessage("当前结转单状态不允许作废");
                oret.setCode(-1);
                return oret;
            } else {
                inventoryOrderInfoService.discard(id);
                oret = new Response<>("作废成功");
                return oret;
            }
        }
        if (!inventoryOrderInfoDTO.getStatus().equalsIgnoreCase(InventoryOrderEnum.STATUS_CREATED.getCode()) &&
                !inventoryOrderInfoDTO.getStatus().equalsIgnoreCase(InventoryOrderEnum.STATUS_PERFECT.getCode())) {
            oret = new Response<>("非法操作作废");
            oret.setErrorMessage("非法操作作废");
            oret.setCode(-1);
            return oret;
        } else {
            inventoryOrderInfoService.discard(id);
            oret = new Response<>("作废成功");
            return oret;
        }
    }

    @ApiOperation(value = "查看清关单详细", response = InventoryOrderViewInfoVO.class)
    @GetMapping("/view-inventory-order")
    public InventoryOrderViewInfoVO viewInventoryOrder(@RequestParam(required = true) Long id) {
        InventoryOrderViewInfoVO viewInfoVO = new InventoryOrderViewInfoVO();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        if (Objects.isNull(inventoryOrderInfoDTO)) {
            throw new ArgsErrorException("未查询到清关单信息");
        }
        this.buildViewInfoVO(viewInfoVO, inventoryOrderInfoDTO);
        return viewInfoVO;
    }

    private void buildViewInfoVO(InventoryOrderViewInfoVO viewInfoVO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        this.populateBaseInfo(viewInfoVO, inventoryOrderInfoDTO);
        this.populateRelationInfo(viewInfoVO, inventoryOrderInfoDTO);
    }

    /**
     * 填充基础信息
     *
     * @param viewInfoVO
     * @param inventoryOrderInfoDTO
     */
    private void populateBaseInfo(InventoryOrderViewInfoVO viewInfoVO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
//        inventoryOrderInfoDTO = orderOwnerMappingService.getWareHouseAndOwner(inventoryOrderInfoDTO);
        BeanUtils.copyProperties(inventoryOrderInfoDTO, viewInfoVO);
        if (!StringUtils.isEmpty(inventoryOrderInfoDTO.getCustomsInvtType())) {
            viewInfoVO.setCustomsInvtType(inventoryOrderInfoDTO.getCustomsInvtType());
            viewInfoVO.setCustomsEntryNo(inventoryOrderInfoDTO.getCustomsEntryNo());
        }
        // TODO: 2022/4/6 这个地方上游给的是国家，不是国家code所以查不到，是不是还有其他原因
//        if (!StringUtils.isEmpty(inventoryOrderInfoDTO.getShipmentCountry())) {
//            CustomsCountryDTO customsCountryDTO = customsCountryService.findByCode(inventoryOrderInfoDTO.getShipmentCountry());
//            viewInfoVO.setShipmentCountryName(customsCountryDTO.getName());
//        }
        viewInfoVO.setTransportModeName(Inv101TrspModecd.getEnum(inventoryOrderInfoDTO.getTransportMode()).getDesc());
        viewInfoVO.setInveBusinessTypeDesc(InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType()).getDesc());
        if (inventoryOrderInfoDTO.getExpectedOutAreaTime() != null) {
            viewInfoVO.setExpectedOutAreaTime(DateFormatUtils.format(inventoryOrderInfoDTO.getExpectedOutAreaTime(), "yyyy/MM/dd"));
        }
        if (inventoryOrderInfoDTO.getExpectedToPortTime() != null) {
            viewInfoVO.setExpectedToPortTime(DateFormatUtils.format(inventoryOrderInfoDTO.getExpectedToPortTime(), "yyyy/MM/dd"));
        }
        //报关企业
        if (inventoryOrderInfoDTO.getCustomsEntryCompany() != null) {
            viewInfoVO.setCustomsEntryCompany(inventoryOrderInfoDTO.getCustomsEntryCompany());
        }
        //报关单类型
        if (inventoryOrderInfoDTO.getCustomsEntryType() != null) {
            viewInfoVO.setCustomsEntryType(inventoryOrderInfoDTO.getCustomsEntryType());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsFlag())) {
            InventoryCustomsFlagEnum customsFlagEnum = InventoryCustomsFlagEnum.getEnum(inventoryOrderInfoDTO.getCustomsFlag());
            viewInfoVO.setCustomsFlagDesc(customsFlagEnum.getDesc());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getTwoStepFlag())) {
            viewInfoVO.setTwoStepFlagDesc("否");
            if (Objects.equals(inventoryOrderInfoDTO.getTwoStepFlag(), InventoryTwoStepEnum.TWO_STEP.getCode())) {
                viewInfoVO.setTwoStepFlagDesc("是");
            }
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getDeclarationFlag())) {
            InventoryDeclarationEnum declarationEnum = InventoryDeclarationEnum.getEnum(inventoryOrderInfoDTO.getDeclarationFlag());
            viewInfoVO.setDeclarationFlagDesc(declarationEnum.getDesc());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getSelfOwnedVehicle())) {
            if (inventoryOrderInfoDTO.getSelfOwnedVehicle()) {
                viewInfoVO.setSelfOwnedVehicle(1);
            } else {
                viewInfoVO.setSelfOwnedVehicle(0);
            }
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getInveCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getInveCompanyId());
            viewInfoVO.setInveCompanyName(Objects.nonNull(companyDTO) ? companyDTO.getName() : null);
        }
        viewInfoVO.setCarryOverNo(inventoryOrderInfoDTO.getCarryOverNo());
        if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())) {
            viewInfoVO.setCustomsType(inventoryOrderInfoDTO.getCustomsType());
            viewInfoVO.setCustomsTypeDesc(InventoryCustomsTypeEnums.getEnum(inventoryOrderInfoDTO.getCustomsType()).getDesc());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            viewInfoVO.setCorrCusDeclareCompanyName(companyDTO.getName());
            viewInfoVO.setCorrCusDeclareCompanyCode(companyDTO.getCode());
            viewInfoVO.setCorrCusDeclareCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusDeclareCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
            viewInfoVO.setRltCusDeclareCompanyName(companyDTO.getName());
            viewInfoVO.setRltCusDeclareCompanyCode(companyDTO.getCode());
            viewInfoVO.setRltCusDeclareCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusXFDYCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusXFDYCompanyId());
            viewInfoVO.setRltCusXFDYCompanyName(companyDTO.getName());
            viewInfoVO.setRltCusXFDYCompanyCode(companyDTO.getCode());
            viewInfoVO.setRltCusXFDYCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
            viewInfoVO.setRltCusInnerSFHRCompanyName(companyDTO.getName());
            viewInfoVO.setRltCusInnerSFHRCompanyCode(companyDTO.getCode());
            viewInfoVO.setRltCusInnerSFHRCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getOrderTag())) {
            viewInfoVO.setOrderTagList(InventoryOrderTagEnums.getOrderTags(inventoryOrderInfoDTO.getOrderTag()));
        }
        this.buildRefInfo(viewInfoVO, inventoryOrderInfoDTO);
    }

    /**
     * 填充核注单相关的信息
     *
     * @param viewInfoVO
     * @param inventoryOrderInfoDTO
     */
    @Deprecated
    private void buildRefInfo(InventoryOrderViewInfoVO viewInfoVO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        List<EndorsementDTO> endorsementDTOList;
        if (inventoryOrderInfoDTO.getMasterOrderSn() != null) {
            InventoryOrderInfoDTO masterDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getMasterOrderSn());
            endorsementDTOList = endorsementService.listByInventory(masterDTO.getId());
        } else {
            endorsementDTOList = endorsementService.listByInventory(inventoryOrderInfoDTO.getId());
        }
        if (!CollectionUtils.isEmpty(endorsementDTOList)) {
            String refHzInveNo = endorsementDTOList.stream().map(EndorsementDTO::getRealOrderNo).collect(Collectors.joining("/"));
            viewInfoVO.setRefHzInveNo(refHzInveNo);
            String preNo = endorsementDTOList.stream().map(EndorsementDTO::getPreOrderNo).filter(Objects::nonNull).collect(Collectors.joining("/"));
            viewInfoVO.setPreNo(preNo);
        }
        if (Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.LOGISTICS.getValue()) && Objects.isNull(inventoryOrderInfoDTO.getInveBusinessType())) {
            if (Objects.equals("ReadyOrder", inventoryOrderInfoDTO.getChannelBusinessType())) {
                viewInfoVO.setInOrOutFlag(InventoryInOutEnum.IN.getDesc());
                viewInfoVO.setInOrOutFlagCode(InventoryInOutEnum.IN.getCode());
            } else if (Objects.equals("Distribution", inventoryOrderInfoDTO.getChannelBusinessType())) {
                viewInfoVO.setInOrOutFlag(InventoryInOutEnum.OUT.getDesc());
                viewInfoVO.setInOrOutFlagCode(InventoryInOutEnum.OUT.getCode());
            }
        } else {
            InventoryInOutEnum inOutEnum = InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType()).getType();
            if (StringUtil.isNotBlank(inventoryOrderInfoDTO.getInOutFlag())) {
                inOutEnum = InventoryInOutEnum.getEnum(inventoryOrderInfoDTO.getInOutFlag());
            }
            viewInfoVO.setInOrOutFlag(inOutEnum.getDesc());
            viewInfoVO.setInOrOutFlagCode(inOutEnum.getCode());
        }
        if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode())) {
            viewInfoVO.setTransitFlagDesc("是");
        } else {
            viewInfoVO.setTransitFlagDesc("否");
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getFinalInveCompanyId())) {
            CompanyDTO finalCompanyDTO = companyService.findById(inventoryOrderInfoDTO.getFinalInveCompanyId());
            if (Objects.nonNull(finalCompanyDTO)) {
                viewInfoVO.setFinalInveCompanyName(finalCompanyDTO.getName());
            }
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getFinalBookId())) {
            CustomsBookResVo finalBookDTO = customsBookService.findByIdV2(inventoryOrderInfoDTO.getFinalBookId());
            if (Objects.nonNull(finalBookDTO)) {
                viewInfoVO.setFinalBookNo(finalBookDTO.getBookNo());
            }
        }
        if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_IN.getCode())
                || Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_OUT.getCode())
        ) {
            String associatedTransitOrderSn = inventoryOrderInfoDTO.getAssociatedTransitOrderSn();
            if (Objects.nonNull(associatedTransitOrderSn)) {
                InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findBySn(associatedTransitOrderSn);
                viewInfoVO.setOutsetEntityWarehouseName(orderInfoDTO.getEntityWarehouseName());
                viewInfoVO.setOutsetOwnerName(orderInfoDTO.getOwnerName());
                viewInfoVO.setDestinationEntityWarehouseName(orderInfoDTO.getFinalEntityWarehouseName());
                viewInfoVO.setDestinationOwnerName(orderInfoDTO.getFinalOwnerName());
            }
        }
//            else if(Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT_OUT.getCode())){
//                String associatedTransitOrderSn = inventoryOrderInfoDTO.getAssociatedTransitOrderSn();
//                InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findBySn(associatedTransitOrderSn);
//                viewInfoVO.setOutsetEntityWarehouseName(orderInfoDTO.getEntityWarehouseName());
//                viewInfoVO.setOutsetOwnerName(orderInfoDTO.getOwnerName());
//                viewInfoVO.setDestinationEntityWarehouseName(orderInfoDTO.getFinalEntityWarehouseName());
//                viewInfoVO.setDestinationOwnerName(orderInfoDTO.getFinalOwnerName());
//            }
        else {
            if (Objects.equals(viewInfoVO.getInOrOutFlag(), InventoryInOutEnum.IN.getDesc())) {
                viewInfoVO.setDestinationOwnerName(inventoryOrderInfoDTO.getOwnerName());
                viewInfoVO.setDestinationEntityWarehouseName(inventoryOrderInfoDTO.getEntityWarehouseName());
            } else if (Objects.equals(viewInfoVO.getInOrOutFlag(), InventoryInOutEnum.OUT.getDesc())) {
                viewInfoVO.setOutsetOwnerName(inventoryOrderInfoDTO.getOwnerName());
                viewInfoVO.setOutsetEntityWarehouseName(inventoryOrderInfoDTO.getEntityWarehouseName());
            }
        }
        if (inventoryOrderInfoDTO.getBookId() != null) {
            CustomsBookResVo bookResVo = customsBookService.findByIdV2(inventoryOrderInfoDTO.getBookId());
            if (bookResVo != null) {
                viewInfoVO.setAreaBookId(inventoryOrderInfoDTO.getBookId());
                viewInfoVO.setAreaBookNo(bookResVo.getBookNo());
            }
        }
    }

    private void populateRelationInfo(InventoryOrderViewInfoVO viewInfoVO, InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        List<InventoryOrderItemDTO> listItem = inventoryOrderInfoService.findListByInvenOrderId(inventoryOrderInfoDTO.getId());
        int idx = 1;
        for (InventoryOrderItemDTO inventoryOrderItemDTO : listItem) {
            inventoryOrderItemDTO.setDeclareCustomsGoodsSeqNo(String.valueOf(idx++));
        }
        viewInfoVO.setInventoryOrderItemDTOList(listItem);
        List<InventoryOrderLogDTO> listLog = inventoryOrderLogService.findList(inventoryOrderInfoDTO.getId());
        viewInfoVO.setInventoryOrderLogDTOList(listLog);
        List<InventoryOrderRelationDTO> listInventoryOrderRelationDTO = inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(inventoryOrderInfoDTO.getId());
        viewInfoVO.setInventoryOrderRelationDTOList(listInventoryOrderRelationDTO);
        //理货报告(ERP下发、区间转出、区内转出才有)
        if (InventoryOrderChannel.LOGISTICS.getValue().equals(inventoryOrderInfoDTO.getChannel())
                && (InventoryOrderBusinessEnum.BUSSINESS_SECTION_OUT.getCode().equals(inventoryOrderInfoDTO.getInveBusinessType()) || InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_OUT.getCode().equals(inventoryOrderInfoDTO.getInveBusinessType()))) {
            List<InventoryOrderTallyReportDTO> inventoryOrderTallyReportDTOList = inventoryOrderTallyReportService.findListByInveOrderId(inventoryOrderInfoDTO.getId());
            if (CollectionUtils.isNotEmpty(inventoryOrderTallyReportDTOList)) {
                List<InventoryOrderTallyReportVO> inventoryOrderTallyReportVOList = inventoryOrderTallyReportDTOList.stream().map((InventoryOrderTallyReportDTO inventoryOrderTallyReportDTO) -> {
                    InventoryOrderTallyReportVO inventoryOrderTallyReportVO = new InventoryOrderTallyReportVO();
                    inventoryOrderTallyReportVO.setId(inventoryOrderTallyReportDTO.getId());
                    inventoryOrderTallyReportVO.setTallyOrderNo(inventoryOrderTallyReportDTO.getTallyOrderNo());
                    inventoryOrderTallyReportVO.setOutBoundNo(inventoryOrderTallyReportDTO.getOutBoundNo());
                    return inventoryOrderTallyReportVO;
                }).collect(Collectors.toList());
                viewInfoVO.setInventoryOrderTallyReportVOList(inventoryOrderTallyReportVOList);
            }
        }
    }


    @ApiOperation(value = "清关单附件删除")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "attachId", required = true, dataType = "Long", value = "附件ID"),
            @ApiImplicitParam(name = "orderId", required = true, dataType = "Long", value = "清关单ID")
    })
    @GetMapping("/delete-attach")
    public Response<Boolean> deleteAttach(@RequestParam(required = true) Long attachId, @RequestParam(required = true) Long orderId) {
        Response<Boolean> oret;
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(orderId);
        List<InventoryOrderAttachDTO> listAttach = inventoryOrderAttachService.findList(orderId);
        if (inventoryOrderInfoDTO == null || CollectionUtils.isEmpty(listAttach)) {
            oret = new Response<>(false);
            oret.setCode(-1);
            oret.setErrorMessage("非法参数，操作失败");
            return oret;
        }
        Predicate predicate = new Predicate<InventoryOrderAttachDTO>() {
            public boolean evaluate(InventoryOrderAttachDTO object) {
                return object.getId().equals(attachId);
            }
        };
        InventoryOrderAttachDTO _findItem = CollectionUtils.find(listAttach, predicate);
        if (_findItem == null) {
            oret = new Response<>(false);
            oret.setCode(-1);
            oret.setErrorMessage("非法参数，操作失败");
            return oret;
        }
        inventoryOrderAttachService.deleteById(attachId);
        oret = new Response<>(true);
        return oret;
    }

    public Response<Boolean> uploadAttachUrl(Long id, String url, String fileName, String attachType) {
        Response<Boolean> oret;
        try {
            InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findById(id);
            inventoryOrderAttachService.saveAttachUrl(orderInfoDTO, url, fileName, attachType);
        } catch (Exception e) {
            log.info("[op:InventoryOrderRpcImpl-uploadAttachUrl error={}]", e.getMessage());
            oret = new Response<>(false);
            oret.setCode(-1);
            oret.setErrorMessage("上传失败");
            return oret;
        }
        oret = new Response<>(true);
        return oret;
    }

    @ApiOperation(value = "上传附件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "invenOrderId", required = true, dataType = "Long", value = "清关单ID"),
            @ApiImplicitParam(name = "file", required = true, dataType = "File", value = "上传附件")
    })
    @PostMapping("/upload-attach")
    public Response<Boolean> uploadAttach(@RequestParam(required = true) Long invenOrderId, @RequestParam(required = true) MultipartFile file) throws IOException {
        if (file == null) {
            throw new ArgsErrorException("附件不能为空");
        }
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(invenOrderId);
        if (inventoryOrderInfoDTO == null) {
            throw new ArgsErrorException("参数传递有误");
        }
        String originalFileName = file.getOriginalFilename();
        if (!file.isEmpty() && !StringUtils.isEmpty(originalFileName)) {
            //对文文件的全名进行截取然后在后缀名进行删选。
            int begin = originalFileName.indexOf(".");
            int last = originalFileName.length();
            //获得文件后缀名
            String suffix = originalFileName.substring(begin, last);
            String storeName = createFileName(suffix);
            String contentType = file.getContentType();
            String fileName = "ccs/inve-order/" + storeName;
            byte[] byteArr = file.getBytes();
            fileOperationService.uploadFile2OSS(fileName, byteArr,
                    byteArr.length, contentType, null);
            String filePath = "https://daita-oss.oss-cn-hangzhou.aliyuncs.com/" + fileName;
            InventoryOrderAttachDTO inventoryOrderAttachDTO = new InventoryOrderAttachDTO();
            inventoryOrderAttachDTO.setRefInveOrderId(inventoryOrderInfoDTO.getId());
            inventoryOrderAttachDTO.setRefInveOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
            inventoryOrderAttachDTO.setAttachName(originalFileName);
            inventoryOrderAttachDTO.setStoreName(storeName);
            inventoryOrderAttachDTO.setUpdateBy(UserUtils.getUserId());
            inventoryOrderAttachDTO.setCreateBy(UserUtils.getUserId());
            inventoryOrderAttachDTO.setContentType(contentType);
            inventoryOrderAttachDTO.setAttachPath(filePath);
            inventoryOrderAttachService.createInventoryOrderAttachDTO(inventoryOrderAttachDTO);
        } else {
            throw new ArgsErrorException("附件不能为空");
        }
        Response<Boolean> oret = new Response<Boolean>(true);
        return oret;
    }

    private String createFileName(String suffix) {
        String currentDate = DateFormatUtils.format(new Date(), "yyyyMMddHHmmss");
        return currentDate + "-" + UUID.randomUUID() + suffix;
    }

    @ApiOperation(value = "获取运输方式")
    @GetMapping("/listTransport")
    public List<SelectItemVO> listTransport() {
        return inventoryOrderInfoService.listTransport();
    }

    @ApiOperation(value = "导入预览")
    @PostMapping("/pre/import")
    public InventoryOrderItemWritingReport preImport(Long id, MultipartFile file) throws ArgsErrorException {
        try {
            List<InventoryOrderItemImportVO> successList = EasyExcel.read(file.getInputStream()).headRowNumber(1).head(InventoryOrderItemImportVO.class).sheet().doReadSync();
            int i = 1; // 行号，从2开始
            List<InventoryOrderItemRecord> preList = new ArrayList<>();
            for (InventoryOrderItemImportVO demoExcel : successList) {
                i++;
                if (StringUtils.isEmpty(demoExcel.getGoodsSeqNo()) && StringUtils.isEmpty(demoExcel.getProductId())) {
                    continue;
                }
                InventoryOrderItemRecord record = new InventoryOrderItemRecord();
                BeanUtils.copyProperties(demoExcel, record);
                record.setIdx(i);
                preList.add(record);
            }
            if (org.springframework.util.CollectionUtils.isEmpty(preList)) {
                throw new ArgsErrorException("对不起！选择模板文件为空或模板不正确");
            }
            return inventoryOrderItemManager.writing(id, preList);
        } catch (IOException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
            throw new ArgsErrorException("上传失败");
        }
    }

    @ApiOperation(value = "导入提交")
    @PostMapping("/submit/import")
    public List<CustomsGoodsItemInfoDTO> importExcel(@RequestBody InventoryOrderItemImportSubmit submit) throws ArgsErrorException {
        InventoryOrderItemWritingReport report = inventoryOrderItemManager.writing(submit.getId(), submit.getRecordList());
        if (!CollectionUtils.isEmpty(report.getFailRecordList())) {
            throw new ArgsErrorException("提交的列表还存在错误，请重新预览导入");
        }

        return buildCustomsGoodsItemInfoDTO(submit.getRecordList(), submit.getId());
    }


    private List<CustomsGoodsItemInfoDTO> buildCustomsGoodsItemInfoDTO(List<InventoryOrderItemRecord> recordList, Long id) throws ArgsErrorException {
        List<CustomsGoodsItemInfoDTO> result = new ArrayList<>();
        Map<String, CustomsCurrencyDTO> currencyMap = new HashMap<>();
        Map<String, CustomsCountryDTO> countryDTOMap = new HashMap<>();
        Map<String, CustomsUomDTO> uomDTOMap = new HashMap<>();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        Long bookId = inventoryOrderInfoDTO.getBookId();
        Boolean isTransitMaster = Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode());

        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);
        List<Future<CustomsGoodsItemInfoDTO>> futures = new ArrayList<>();
        Long tenantId = SimpleTenantHelper.getTenantId();
        for (InventoryOrderItemRecord record : recordList) {
            Future<CustomsGoodsItemInfoDTO> future = executorService.submit(() -> {
                SimpleTenantHelper.setTenantId(tenantId);
                return this.processRecord(id, record, bookId, isTransitMaster, uomDTOMap, countryDTOMap, currencyMap);
            });
            futures.add(future);
        }
        // 等待所有任务完成并按顺序检索结果
        for (Future<CustomsGoodsItemInfoDTO> future : futures) {
            try {
                CustomsGoodsItemInfoDTO info = future.get();
                result.add(info);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace(); // 适当处理异常
            }
        }
        executorService.shutdown();
        return result;
    }

    private CustomsGoodsItemInfoDTO processRecord(Long id, InventoryOrderItemRecord record, Long bookId, Boolean isTransitMaster, Map<String, CustomsUomDTO> uomDTOMap, Map<String, CustomsCountryDTO> countryDTOMap, Map<String, CustomsCurrencyDTO> currencyMap) {
        String oldOrNew;
        if (StringUtils.isEmpty(record.getGoodsSeqNo())) {
            oldOrNew = "new";
        } else {
            oldOrNew = "old";
        }
        // 海关备案料号
        String customsRecordProductId = record.getCustomsRecordProductId();
        // 统一料号
        String productId = record.getProductId();
        // 老品取海关备案料号， 新品取统一料号
//            String productId = Objects.nonNull(record.getGoodsSeqNo()) ? record.getCustomsRecordProductId() : record.getProductId();
        List<CustomsGoodsItemInfoDTO> list = customsGoodsService.findItemDetailByV3(oldOrNew, productId, customsRecordProductId, "", bookId.toString(), record.getGoodsSeqNo(), id);
        if (CollectionUtils.isEmpty(list)) {
            throw new ArgsErrorException("查询数据为空");
        } else if (list.size() > 1) {
            throw new ArgsErrorException("查询数据多条");
        }
        CustomsGoodsItemInfoDTO info = list.get(0);
        info.setOriginProductId(record.getProductId());
        if (isTransitMaster && StringUtil.isNotEmpty(record.getCustomsRecordProductId())) {
            info.setProductId(record.getCustomsRecordProductId());
        }
        if (StringUtil.isNotBlank(record.getCurrency())) {
            info.setCurrency(record.getCurrency());
        }
        if (StringUtil.isNotBlank(record.getGoodsModel())) {
            info.setGoodsModel(record.getGoodsModel());
        }
        BigDecimal declarePrice = record.getDeclarePrice() == null ? info.getDeclarePrice() : record.getDeclarePrice();
        BigDecimal delcareQfy = record.getDeclareQty() == null ? BigDecimal.ZERO : record.getDeclareQty();
        info.setDeclareUnitQfy(delcareQfy);
        info.setTotalGrossWeight(delcareQfy.multiply(info.getGrossWeight()).setScale(5, RoundingMode.HALF_UP));
        info.setTotalNetWeight(delcareQfy.multiply(info.getNetweight()).setScale(5, RoundingMode.HALF_UP));
        if (Objects.nonNull(declarePrice)) {
            info.setDeclareTotalPrice(declarePrice.multiply(delcareQfy).setScale(4, RoundingMode.HALF_UP));
        }
        if (record.getDeclarePrice() != null) {
            info.setDeclarePrice(record.getDeclarePrice());
        }

        this.setUomDesc(info.getUnit(), uomDTOMap, info::setUnitDesc);
        this.setUomDesc(info.getFirstUnit(), uomDTOMap, info::setFirstUnitDesc);
        this.setUomDesc(info.getSecondUnit(), uomDTOMap, info::setSecondUnitDesc);
        this.setCountryDesc(info.getOriginCountry(), countryDTOMap, info::setOriginCountryDesc);
        this.setCountryDesc(info.getDestinationCountry(), countryDTOMap, info::setDestinationCountryDesc);
        this.setCurrencyDesc(info.getCurrency(), currencyMap, info::setCurrencyDesc);
        return info;
    }

    private void setUomDesc(String code, Map<String, CustomsUomDTO> uomMap, Consumer<String> descSetter) {
        if (StringUtil.isNotBlank(code)) {
            CustomsUomDTO uomDTO = uomMap.computeIfAbsent(code, customsUomService::findByCode);
            descSetter.accept(Objects.nonNull(uomDTO) ? uomDTO.getName() : "");
        }
    }

    private void setCountryDesc(String code, Map<String, CustomsCountryDTO> countryDTOMap, Consumer<String> descSetter) {
        if (StringUtil.isNotBlank(code)) {
            CustomsCountryDTO countryDTO = countryDTOMap.computeIfAbsent(code, customsCountryService::findByCode);
            descSetter.accept(Objects.nonNull(countryDTO) ? countryDTO.getName() : "");
        }
    }

    private void setCurrencyDesc(String code, Map<String, CustomsCurrencyDTO> currencyDTOMap, Consumer<String> descSetter) {
        if (StringUtil.isNotBlank(code)) {
            CustomsCurrencyDTO currencyDTO = currencyDTOMap.computeIfAbsent(code, customsCurrencyService::findByCode);
            descSetter.accept(Objects.nonNull(currencyDTO) ? currencyDTO.getName() : "");
        }
    }

    @ApiOperation(value = "ERP清关单作废模拟")
    @GetMapping("/erpCancel")
    public boolean erpCancel(String inveCustomsSn) {
        return inventoryOrderInfoService.discard(inveCustomsSn);
    }

    /**
     * cw清关 创建清关单
     *
     * @param reqVO
     * @return
     */
    @PostMapping("/createInventoryByCw")
    public RpcResult<String> createInventoryByCw(@RequestBody InventoryOrderInfoCwSubmitReqVO reqVO) {
        log.info("createInventoryByCw - reqVO={}", JSON.toJSONString(reqVO));
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("参数为空");
        }
        try {
            InventoryOrderInfoCwSubmit inventoryOrderInfoCwSubmit = new InventoryOrderInfoCwSubmit();
            BeanUtils.copyProperties(reqVO, inventoryOrderInfoCwSubmit, "itemCwSubmitReqVOList");
            inventoryOrderInfoCwSubmit.setItemCwSubmitReqVOList(ConvertUtil.listConvert(reqVO.getItemCwSubmitReqVOList(), InventoryOrderItemCwSubmit.class));
            String sn = inventoryOrderInfoService.createInventoryByCw(inventoryOrderInfoCwSubmit);
            return RpcResult.success("成功", sn);
        } catch (ArgsInvalidException e) {
            log.error("createInventoryByCw error={}", e.getMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("createInventoryByCw error={}", e.getMessage(), e);
            return RpcResult.error("失败");
        }
    }

    /**
     * cw清关 理货完成
     *
     * @param inOutOrderNo
     * @return
     */
    @PostMapping("/finishTallyByInOutOrderNo")
    public RpcResult<String> finishTallyByInOutOrderNo(String inOutOrderNo) {
        log.info("finishTallyByInOutOrderNo  inOutOrderNo={}", inOutOrderNo);
        if (StringUtil.isBlank(inOutOrderNo)) {
            return RpcResult.error("出入库单号为空");
        }
        try {
            String sn = inventoryOrderInfoService.finishTallyByInOutOrderNo(inOutOrderNo);
            return RpcResult.success("处理成功", sn);
        } catch (Exception e) {
            log.error("处理失败," + e.getMessage(), e);
            return RpcResult.error("处理失败," + e.getMessage());
        }
    }

    /**
     * 出入库单号获取清关企业名称
     *
     * @param inOutOrderNo
     * @return
     */
    @PostMapping("/getInvCompanyNameByInOutOrderNo")
    public RpcResult<String> getInvCompanyNameByInOutOrderNo(String inOutOrderNo) {
        log.info("getInvCompanyByInOutOrderNo inOutOrderNo={}", inOutOrderNo);
        if (StringUtils.isEmpty(inOutOrderNo)) {
            return RpcResult.error("出入库单号为空");
        }
        try {
            InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findByInOutOrderNo(inOutOrderNo);
            if (Objects.nonNull(infoDTO) && Objects.nonNull(infoDTO.getInveCompanyId())) {
                CompanyDTO companyDTO = companyService.findById(infoDTO.getInveCompanyId());
                if (Objects.nonNull(companyDTO)) {
                    return RpcResult.success("成功", companyDTO.getName());
                }
            }
            return RpcResult.success("失败");
        } catch (Exception e) {
            log.error("getInvCompanyByInOutOrderNo 清关企业查询失败={}", e.getMessage(), e);
            return RpcResult.error("失败");
        }
    }

    @PostMapping("/getInvCompanyNameByMailNo")
    public RpcResult<Map<String, String>> getInvCompanyNameByMailNo(@RequestBody MailListReq req) {
        log.info("getInvCompanyNameByMailNo mailNoList={}", JSON.toJSONString(req.getMailNoList()));
        try {
            Map<String, String> result = inventoryOrderInfoService.getInvCompanyNameByMailNo(req.getMailNoList());
            log.info("getInvCompanyNameByMailNo result={}", JSON.toJSONString(result));
            return RpcResult.success(result);
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    public List<InventoryOrderEndorsementVO> viewInventoryOrderEndorsement(Long id) {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        List<EndorsementDTO> endorsementDTOList = endorsementService.listByInventory(inventoryOrderInfoDTO.getId());
//        if (inventoryOrderInfoDTO.getMasterOrderSn() != null) {
//            InventoryOrderInfoDTO masterDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getMasterOrderSn());
//            endorsementDTOList = endorsementService.listByInventory(masterDTO.getId());
//        } else {
//            endorsementDTOList = endorsementService.listByInventory(inventoryOrderInfoDTO.getId());
//        }
        endorsementDTOList = CollUtil.reverse(endorsementDTOList);
        List<Long> endorsementIdList = endorsementDTOList.stream().map(EndorsementDTO::getId).collect(Collectors.toList());
        Map<Long, List<ChecklistDTO>> checklistDTOMap = checklistService.getChecklistByEndorsementList(endorsementIdList);
        return endorsementDTOList.stream().map((endorsementDTO) -> {
            InventoryOrderEndorsementVO vo = new InventoryOrderEndorsementVO();
            vo.setEndorsementId(endorsementDTO.getId());
            vo.setEndorsementSn(endorsementDTO.getSn());
            vo.setPreOrderNo(endorsementDTO.getPreOrderNo());
            vo.setRealEndorsementNo(endorsementDTO.getRealOrderNo());
            vo.setEndorsementStatus(endorsementDTO.getStatus());
            vo.setEndorsementStatusDesc(EndorsementOrderStatus.getEnum(endorsementDTO.getStatus()).getDesc());
            vo.setCreateTime(endorsementDTO.getCreateTime());
            List<ChecklistDTO> checklistDTOS = checklistDTOMap.get(endorsementDTO.getId());
            if (!CollectionUtils.isEmpty(checklistDTOS)) {
                String checkListSnList = checklistDTOS.stream().map(ChecklistDTO::getSn).collect(Collectors.joining(","));
                vo.setChecklistSn(checkListSnList);
            }
            vo.setStockChangeEnable(Boolean.TRUE.equals(endorsementDTO.getStockChangeEnable()) ? "是" : "否");
            return vo;
        }).collect(Collectors.toList());
    }

    public CustomsGoodsItemInfoDTO findEndProdDetail(Long bizDeclareFormItemId, Long bookId, BigDecimal qty) {
        BizDeclareFormItemDTO bizDeclareFormItemDTO = bizDeclareFormService.findItemByItemId(bizDeclareFormItemId);
        String productId = bizDeclareFormItemDTO.getProductId();
        GoodsRecordDTO goodsRecordDTO = goodsRecordAssociateService
                .findByBookIdAndProIdAndMappingWay(bookId, productId, GoodsRecordMappingWayEnums.WAREHOUSE_UNIFIED.getValue());
        if (Objects.isNull(goodsRecordDTO)) {
            throw new ArgsInvalidException("料号：" + productId + "，不存在商品备案");
        }
        CustomsBookDTO customsBookDTO = customsBookService.findById(bookId);
        Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        CustomsGoodsItemInfoDTO infoDTO = new CustomsGoodsItemInfoDTO();
        //来源申报表：申报表序号、商品料号、商品名称、商品编码、规格型号、申报计量单位、法定计量单位、法定第二计量单位、申报单价、原产国、币制
        infoDTO.setOldOrNew("new");
        infoDTO.setDeclareFormItemSeqNo(bizDeclareFormItemDTO.getSeqNo());
        infoDTO.setProductId(bizDeclareFormItemDTO.getProductId());
        infoDTO.setOriginProductId(bizDeclareFormItemDTO.getProductId());
        infoDTO.setGoodsName(bizDeclareFormItemDTO.getGoodsName());
        infoDTO.setHsCode(bizDeclareFormItemDTO.getGoodsCode());
        infoDTO.setGoodsModel(bizDeclareFormItemDTO.getGoodsModel());
        infoDTO.setUnit(bizDeclareFormItemDTO.getDeclareUnit());
        infoDTO.setUnitDesc(uomMap.get(bizDeclareFormItemDTO.getDeclareUnit()));
        infoDTO.setFirstUnit(bizDeclareFormItemDTO.getLawFirstUnit());
        infoDTO.setFirstUnitDesc(uomMap.get(bizDeclareFormItemDTO.getLawFirstUnit()));
        infoDTO.setSecondUnit(bizDeclareFormItemDTO.getLawSecondUnit());
        infoDTO.setSecondUnitDesc(uomMap.get(bizDeclareFormItemDTO.getLawSecondUnit()));
        infoDTO.setDeclarePrice(bizDeclareFormItemDTO.getPrice());
        infoDTO.setOriginCountry(bizDeclareFormItemDTO.getCountry());
        infoDTO.setOriginCountryDesc(countryMap.get(bizDeclareFormItemDTO.getCountry()));
        infoDTO.setCurrency(bizDeclareFormItemDTO.getCurrency());
        infoDTO.setCurrencyDesc(currencyMap.get(bizDeclareFormItemDTO.getCurrency()));
        //来源商品备案：法定数量(单)、法定第二数量(单)、净重、毛重、商品条码、sku
        infoDTO.setSkuId(goodsRecordDTO.getSkuId());
        infoDTO.setFirstUnitQfy(goodsRecordDTO.getFirstUnitAmount());
        infoDTO.setSecondUnitQfy(goodsRecordDTO.getSecondUnitAmount());
        infoDTO.setNetweight(goodsRecordDTO.getNetWeight());
        infoDTO.setTotalNetWeight(goodsRecordDTO.getNetWeight().multiply(qty));
        infoDTO.setGrossWeight(goodsRecordDTO.getGrossWeight());
        infoDTO.setTotalGrossWeight(goodsRecordDTO.getGrossWeight().multiply(qty));
        infoDTO.setGoodsBar(goodsRecordDTO.getBarCode());
        //以下字段取默认值(参考清关单表体)：最终目的国、免征方式
        infoDTO.setDestinationCountry("142");
        infoDTO.setAvoidTaxMethod("3");

        infoDTO.setDeclareUnitQfy(qty);

        Integer bookType = customsBookDTO.getBookType();
        if (Objects.equals(bookType, CustomsBookTypeEnums.IMPORT_BONDED_SPECIAL_BOOKS.getCode())) {
            Integer goodsRecordTag = goodsRecordDTO.getGoodsRecordTag();
            List<Integer> recordTag = GoodsRecordTagEnums.getGoodsRecordTag(goodsRecordTag);
            if (recordTag.contains(GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getCode())) {
                infoDTO.setGoodsSource(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode());
                infoDTO.setGoodsSourceDesc(GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getDesc());
                if (recordTag.contains(GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getCode())) {
                    if (Objects.equals(goodsRecordDTO.getOriginCountry(), "502")) {
                        infoDTO.setGoodsSource(GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode());
                        infoDTO.setGoodsSourceDesc(GoodsSourceEnums.OUTER_KEY_MATERIAL.getDesc());
                    }
                }
            }
        }

        if (Objects.nonNull(bizDeclareFormItemDTO.getPrice()) && Objects.nonNull(qty)) {
            infoDTO.setDeclareTotalPrice(bizDeclareFormItemDTO.getPrice().multiply(qty));
        }
        return infoDTO;
    }

    public List<InventoryOrderItemGoodsVO> viewInventoryOrderItemGoodsList(Long inventoryOrderId) {
        List<InventoryOrderItemGoodsDTO> inventoryOrderItemGoodsDTOS = inventoryOrderInfoService.listItemGoodsByInventoryId(inventoryOrderId);
        if (CollUtil.isEmpty(inventoryOrderItemGoodsDTOS)) {
            return new ArrayList<>();
        }
        Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        return inventoryOrderItemGoodsDTOS.stream().map(dto -> {
            InventoryOrderItemGoodsVO vo = new InventoryOrderItemGoodsVO();
            BeanUtils.copyProperties(dto, vo);
            vo.setDestinationCountryDesc(countryMap.get(dto.getDestinationCountry()));
            vo.setCurrencyDesc(currencyMap.get(dto.getCurrency()));
            vo.setUnitDesc(uomMap.get(dto.getUnit()));
            vo.setFirstUnitDesc(uomMap.get(dto.getFirstUnit()));
            vo.setSecondUnitDesc(uomMap.get(dto.getSecondUnit()));
            vo.setOriginCountryDesc(countryMap.get(dto.getOriginCountry()));
            if (Objects.nonNull(dto.getVerifyResult())) {
                List<InventoryVerifyResult> verifyResultList = JSON.parseArray(dto.getVerifyResult(), InventoryVerifyResult.class);
                vo.setVerifyResultList(verifyResultList);
            }
            return vo;
        }).collect(Collectors.toList());
    }

    public void editItemGoods(InventoryOrderItemGoodsEditVO editVO) {
        InventoryOrderItemGoodsDTO inventoryOrderItemGoodsDTO = new InventoryOrderItemGoodsDTO();
        BeanUtils.copyProperties(editVO, inventoryOrderItemGoodsDTO);
        inventoryOrderInfoService.updateItemGoods(editVO.getInventoryOrderId(), inventoryOrderItemGoodsDTO);
    }

    public List<SelectOptionVO<String>> listDeclareFormNo() {
        List<String> declareFormNoList = bizDeclareFormService.listDeclareFormNo();
        return declareFormNoList.stream().map(s -> {
            SelectOptionVO<String> vo = new SelectOptionVO<>();
            vo.setId(s);
            vo.setName(s);
            return vo;
        }).collect(Collectors.toList());
    }

    public List<SelectOptionVO<String>> listDeclareFormNoByBookId(Long bookId) {
        List<String> declareFormNoList = bizDeclareFormService.listDeclareFormNoByBookId(bookId);
        return declareFormNoList.stream().map(s -> {
            SelectOptionVO<String> vo = new SelectOptionVO<>();
            vo.setId(s);
            vo.setName(s);
            return vo;
        }).collect(Collectors.toList());
    }

    @PostMapping("/download-attach")
    public String downloadAttach(Long id, Integer isUpdateCompanyInfo) {
        return inventoryOrderInfoService.downloadAttach(id, isUpdateCompanyInfo);
    }

    @Data
    public static class MailListReq implements Serializable {
        private List<String> mailNoList;
    }

    @PostMapping("/getTaotianLogisticsTraceInfo")
    public RpcResult<TaotianLogisticsTraceInfoDTO> getTaotianLogisticsTraceInfo(@RequestBody String itemCode) {
        log.info("getTaotianLogisticsTraceInfo itemCode={}", itemCode);
        try {
            TaotianLogisticsTraceInfoDTO taotianLogisticsTraceInfo = inventoryOrderInfoService.getTaotianLogisticsTraceInfo(itemCode);
            log.info("getTaotianLogisticsTraceInfo result={}", JSON.toJSONString(taotianLogisticsTraceInfo));
            return RpcResult.success(taotianLogisticsTraceInfo);
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @GetMapping("/detail")
    public InventoryOrderViewInfoV2VO detail(Long id) {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(id);
        if (Objects.isNull(inventoryOrderInfoDTO)) {
            throw new ArgsInvalidException("清关单不存在");
        }
        InventoryOrderViewInfoV2VO infoV2VO = new InventoryOrderViewInfoV2VO();
        infoV2VO.setInveCompanyId(inventoryOrderInfoDTO.getInveCompanyId());
        infoV2VO.setInveCustomsSn(inventoryOrderInfoDTO.getInveCustomsSn());
        infoV2VO.setOrderTagList(InventoryOrderTagEnums.getOrderTags(inventoryOrderInfoDTO.getOrderTag()));
        infoV2VO.setOrderTagDescList(InventoryOrderTagEnums.getOrderTagsDesc(inventoryOrderInfoDTO.getOrderTag()));
        infoV2VO.setInOutOrderNo(inventoryOrderInfoDTO.getInOutOrderNo());
        infoV2VO.setUpstreamNo(inventoryOrderInfoDTO.getUpstreamNo());
        infoV2VO.setInOrOutFlag(inventoryOrderInfoDTO.getInOutFlag());
        infoV2VO.setInOrOutFlagDesc(InventoryInOutEnum.getEnum(inventoryOrderInfoDTO.getInOutFlag()).getDesc());
        InventoryOrderInfoDTO associatedDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedInveCustomsSn());
        if (Objects.nonNull(associatedDTO)) {
            infoV2VO.setAssociatedInveCustomsSn(inventoryOrderInfoDTO.getAssociatedInveCustomsSn());
            infoV2VO.setAssociatedInOutOrderNo(associatedDTO.getInOutOrderNo());
            boolean isSameUserTransfer = InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.SAME_USER_TRANSFER)
                    && InventoryOrderTagEnums.contains(associatedDTO.getOrderTag(), InventoryOrderTagEnums.SAME_USER_TRANSFER);
            InventoryOrderDraftCompareTypeEnums draftCompareType = InventoryOrderDraftCompareTypeEnums.getEnums(inventoryOrderInfoDTO.getDraftCompareType());
            if (draftCompareType == null) {
                infoV2VO.setDraftCompareType(isSameUserTransfer
                        ? InventoryOrderDraftCompareTypeEnums.COMPARE_PRODUCT_ID.getCode() : InventoryOrderDraftCompareTypeEnums.COMPARE_HS_CODE.getCode());
            } else {
                infoV2VO.setDraftCompareType(draftCompareType.getCode());
            }
        }
        if (StringUtil.isNotBlank(inventoryOrderInfoDTO.getMasterOrderSn()) || StringUtil.isNotEmpty(inventoryOrderInfoDTO.getSubOrderSn())) {
            infoV2VO.setMasterOrSubOrderSn(StringUtil.isNotBlank(inventoryOrderInfoDTO.getMasterOrderSn()) ?
                    inventoryOrderInfoDTO.getMasterOrderSn() : inventoryOrderInfoDTO.getSubOrderSn());
        }

        infoV2VO.setAssociatedTransitOrderSn(inventoryOrderInfoDTO.getAssociatedTransitOrderSn());
        infoV2VO.setAssociatedInOrderSn(inventoryOrderInfoDTO.getAssociatedInOrderSn());
        infoV2VO.setAssociatedOutOrderSn(inventoryOrderInfoDTO.getAssociatedOutOrderSn());
        if (Objects.equals(inventoryOrderInfoDTO.getTransitFlag(), InventoryTransitEnums.TRANSIT.getCode())) {
            infoV2VO.setTransitFlagDesc("是");
        } else {
            infoV2VO.setTransitFlagDesc("否");
        }
        infoV2VO.setFinalEntityWarehouseCode(inventoryOrderInfoDTO.getFinalEntityWarehouseCode());
        infoV2VO.setFinalEntityWarehouseName(inventoryOrderInfoDTO.getFinalEntityWarehouseName());
        infoV2VO.setFinalOwnerCode(inventoryOrderInfoDTO.getFinalOwnerCode());
        infoV2VO.setFinalOwnerName(inventoryOrderInfoDTO.getFinalOwnerName());
        infoV2VO.setFinalInveCompanyId(inventoryOrderInfoDTO.getFinalInveCompanyId());
        if (Objects.nonNull(inventoryOrderInfoDTO.getFinalInveCompanyId())) {
            CompanyDTO finalCompanyDTO = companyService.findById(inventoryOrderInfoDTO.getFinalInveCompanyId());
            if (Objects.nonNull(finalCompanyDTO)) {
                infoV2VO.setFinalInveCompanyName(finalCompanyDTO.getName());
            }
        }
        infoV2VO.setFinalBookId(!Objects.equals(inventoryOrderInfoDTO.getFinalBookId(), 0L) ? inventoryOrderInfoDTO.getFinalBookId() : null);
        CustomsBookResVo finalBookDTO = customsBookService.findByIdV2(inventoryOrderInfoDTO.getFinalBookId());
        if (Objects.nonNull(finalBookDTO)) {
            infoV2VO.setFinalBookNo(finalBookDTO.getBookNo());
        }
        infoV2VO.setCarryOverNo(inventoryOrderInfoDTO.getCarryOverNo());
        infoV2VO.setCreateTime(inventoryOrderInfoDTO.getCreateTime());
        infoV2VO.setStatus(inventoryOrderInfoDTO.getStatus());
        infoV2VO.setStatusDesc(InventoryOrderEnum.getEnum(inventoryOrderInfoDTO.getStatus()).getDesc());
        infoV2VO.setRemark(inventoryOrderInfoDTO.getRemark());

        InventoryOrderViewInfoV2VO.WarehouseInfoVO outsetEntityWarehouseInfo = new InventoryOrderViewInfoV2VO.WarehouseInfoVO();
        InventoryOrderViewInfoV2VO.WarehouseInfoVO destinationEntityWarehouseInfo = new InventoryOrderViewInfoV2VO.WarehouseInfoVO();
        InventoryOrderViewInfoV2VO.WarehouseInfoVO warehouseInfoVO = new InventoryOrderViewInfoV2VO.WarehouseInfoVO();
        InventoryOrderViewInfoV2VO.WarehouseInfoVO associatedWarehouseInfoVO = new InventoryOrderViewInfoV2VO.WarehouseInfoVO();

        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(inventoryOrderInfoDTO.getBookId());
        warehouseInfoVO.setEntityWarehouseName(inventoryOrderInfoDTO.getEntityWarehouseName());
        warehouseInfoVO.setOwnerName(inventoryOrderInfoDTO.getOwnerName());
        warehouseInfoVO.setEntityWarehouseType(StorageAttrEnums.getEnums(customsBookResVo.getStorageAttr()).getTypeName());
        CompanyDTO areaCompanyDTO = companyService.findById(customsBookResVo.getAreaCompanyId());
        warehouseInfoVO.setInveCompanyName(areaCompanyDTO.getName());
        warehouseInfoVO.setAreaBookNo(customsBookResVo.getBookNo());
        warehouseInfoVO.setMasterCustoms(customsBookResVo.getCustomsAreaName());

        if (Objects.nonNull(associatedDTO)) {
            CustomsBookResVo associatedCustomsBookResVo = customsBookService.findByIdV2(associatedDTO.getBookId());
            associatedWarehouseInfoVO.setEntityWarehouseName(associatedDTO.getEntityWarehouseName());
            associatedWarehouseInfoVO.setOwnerName(associatedDTO.getOwnerName());
            associatedWarehouseInfoVO.setEntityWarehouseType(StorageAttrEnums.getEnums(associatedCustomsBookResVo.getStorageAttr()).getTypeName());
            CompanyDTO associatedAreaCompanyDTO = companyService.findById(associatedCustomsBookResVo.getAreaCompanyId());
            associatedWarehouseInfoVO.setInveCompanyName(associatedAreaCompanyDTO.getName());
            associatedWarehouseInfoVO.setAreaBookNo(associatedCustomsBookResVo.getBookNo());
            associatedWarehouseInfoVO.setMasterCustoms(associatedCustomsBookResVo.getCustomsAreaName());
        }
        if (Objects.equals(InventoryInOutEnum.IN.getCode(), inventoryOrderInfoDTO.getInOutFlag())) {
            destinationEntityWarehouseInfo = warehouseInfoVO;
            outsetEntityWarehouseInfo = associatedWarehouseInfoVO;
        } else if (Objects.equals(inventoryOrderInfoDTO.getInOutFlag(), InventoryInOutEnum.OUT.getCode())) {
            outsetEntityWarehouseInfo = warehouseInfoVO;
            destinationEntityWarehouseInfo = associatedWarehouseInfoVO;
        }

        // 中转调出调入
        if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.TRANSIT_IN_ORDER)
                || InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.TRANSIT_OUT_ORDER)
        ) {
            String associatedTransitOrderSn = inventoryOrderInfoDTO.getAssociatedTransitOrderSn();
            if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.AUTO_TRANSFER)) {
                // 全自动调拨 & 中转调入/调出
                if (InventoryOrderTagEnums.contains(inventoryOrderInfoDTO.getOrderTag(), InventoryOrderTagEnums.TRANSIT_IN_ORDER)) {
                    InventoryOrderInfoDTO transitOutDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedOutOrderSn());
                    if (Objects.nonNull(transitOutDTO)) {
                        CustomsBookResVo transitCustomsBookResVo = customsBookService.findByIdV2(transitOutDTO.getBookId());
                        CompanyDTO transitAreaCompanyDTO = companyService.findById(transitCustomsBookResVo.getAreaCompanyId());
                        outsetEntityWarehouseInfo.setEntityWarehouseType(StorageAttrEnums.getEnums(transitCustomsBookResVo.getStorageAttr()).getTypeName());
                        outsetEntityWarehouseInfo.setEntityWarehouseName(transitOutDTO.getEntityWarehouseName());
                        outsetEntityWarehouseInfo.setOwnerName(transitOutDTO.getOwnerName());
                        outsetEntityWarehouseInfo.setInveCompanyName(transitAreaCompanyDTO.getName());
                        outsetEntityWarehouseInfo.setAreaBookNo(transitCustomsBookResVo.getBookNo());
                        outsetEntityWarehouseInfo.setMasterCustoms(transitCustomsBookResVo.getCustomsAreaName());
                    }
                } else {
                    InventoryOrderInfoDTO transitInDTO = inventoryOrderInfoService.findBySn(inventoryOrderInfoDTO.getAssociatedInOrderSn());
                    if (Objects.nonNull(transitInDTO)) {
                        CustomsBookResVo transitCustomsBookResVo = customsBookService.findByIdV2(transitInDTO.getBookId());
                        CompanyDTO transitAreaCompanyDTO = companyService.findById(transitCustomsBookResVo.getAreaCompanyId());
                        destinationEntityWarehouseInfo.setEntityWarehouseType(StorageAttrEnums.getEnums(transitCustomsBookResVo.getStorageAttr()).getTypeName());
                        destinationEntityWarehouseInfo.setEntityWarehouseName(transitInDTO.getEntityWarehouseName());
                        destinationEntityWarehouseInfo.setOwnerName(transitInDTO.getOwnerName());
                        destinationEntityWarehouseInfo.setInveCompanyName(transitAreaCompanyDTO.getName());
                        destinationEntityWarehouseInfo.setAreaBookNo(transitCustomsBookResVo.getBookNo());
                        destinationEntityWarehouseInfo.setMasterCustoms(transitCustomsBookResVo.getCustomsAreaName());
                    }
                }
            } else {
                if (Objects.nonNull(associatedTransitOrderSn)) {
                    InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findBySn(associatedTransitOrderSn);
                    outsetEntityWarehouseInfo.setEntityWarehouseName(orderInfoDTO.getEntityWarehouseName());
                    outsetEntityWarehouseInfo.setOwnerName(orderInfoDTO.getOwnerName());
                    if (Objects.nonNull(orderInfoDTO.getBookId())) {
                        CustomsBookResVo transitCustomsBookResVo = customsBookService.findByIdV2(orderInfoDTO.getBookId());
                        outsetEntityWarehouseInfo.setEntityWarehouseType(StorageAttrEnums.getEnums(transitCustomsBookResVo.getStorageAttr()).getTypeName());
                        CompanyDTO transitAreaCompanyDTO = companyService.findById(transitCustomsBookResVo.getAreaCompanyId());
                        outsetEntityWarehouseInfo.setInveCompanyName(transitAreaCompanyDTO.getName());
                        outsetEntityWarehouseInfo.setAreaBookNo(transitCustomsBookResVo.getBookNo());
                        outsetEntityWarehouseInfo.setMasterCustoms(transitCustomsBookResVo.getCustomsAreaName());
                    }
                    if (Objects.nonNull(orderInfoDTO.getFinalBookId())) {
                        CustomsBookResVo transitCustomsBookResVo = customsBookService.findByIdV2(orderInfoDTO.getFinalBookId());
                        destinationEntityWarehouseInfo.setEntityWarehouseName(orderInfoDTO.getFinalEntityWarehouseName());
                        destinationEntityWarehouseInfo.setOwnerName(orderInfoDTO.getFinalOwnerName());
                        destinationEntityWarehouseInfo.setEntityWarehouseType(StorageAttrEnums.getEnums(transitCustomsBookResVo.getStorageAttr()).getTypeName());
                        CompanyDTO transitAreaCompanyDTO = companyService.findById(customsBookResVo.getAreaCompanyId());
                        destinationEntityWarehouseInfo.setInveCompanyName(transitAreaCompanyDTO.getName());
                        destinationEntityWarehouseInfo.setAreaBookNo(transitCustomsBookResVo.getBookNo());
                        destinationEntityWarehouseInfo.setMasterCustoms(transitCustomsBookResVo.getCustomsAreaName());
                    }
                }
            }
        }
        infoV2VO.setOutsetEntityWarehouseInfo(outsetEntityWarehouseInfo);
        infoV2VO.setDestinationEntityWarehouseInfo(destinationEntityWarehouseInfo);
        infoV2VO.setLicensePlate(inventoryOrderInfoDTO.getLicensePlate());
        if (Objects.nonNull(inventoryOrderInfoDTO.getSelfOwnedVehicle())) {
            infoV2VO.setSelfOwnedVehicle(inventoryOrderInfoDTO.getSelfOwnedVehicle() ? 1 : 0);
        }
        infoV2VO.setVehicleCostRemark(inventoryOrderInfoDTO.getVehicleCostRemark());
        infoV2VO.setPalletsNums(inventoryOrderInfoDTO.getPalletsNums());
        infoV2VO.setExpectedOutAreaTime(inventoryOrderInfoDTO.getExpectedOutAreaTime());
        infoV2VO.setExpectedToPortTime(inventoryOrderInfoDTO.getExpectedToPortTime());
        infoV2VO.setInveBusinessType(inventoryOrderInfoDTO.getInveBusinessType());
        infoV2VO.setInveBusinessTypeDesc(InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType()).getDesc());
        infoV2VO.setTransportMode(inventoryOrderInfoDTO.getTransportMode());
        infoV2VO.setShipmentCountry(inventoryOrderInfoDTO.getShipmentCountry());
        infoV2VO.setEntryExitCustoms(inventoryOrderInfoDTO.getEntryExitCustoms());
        infoV2VO.setTwoStepFlag(inventoryOrderInfoDTO.getTwoStepFlag());
        infoV2VO.setTwoStepFlagDesc(Objects.equals(1, inventoryOrderInfoDTO.getTwoStepFlag()) ? "是" : "否");
        infoV2VO.setDeclareWay(inventoryOrderInfoDTO.getDeclareWay());
        infoV2VO.setCustomsEntryNo(inventoryOrderInfoDTO.getCustomsEntryNo());
        infoV2VO.setCustomsFlag(inventoryOrderInfoDTO.getCustomsFlag());
        if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsFlag())) {
            infoV2VO.setCustomsFlagDesc(InventoryCustomsFlagEnum.getEnum(inventoryOrderInfoDTO.getCustomsFlag()).getDesc());
        }
        infoV2VO.setDeclarationFlag(inventoryOrderInfoDTO.getDeclarationFlag());
        if (Objects.nonNull(inventoryOrderInfoDTO.getDeclarationFlag())) {
            infoV2VO.setDeclarationFlagDesc(InventoryDeclarationEnum.getEnum(inventoryOrderInfoDTO.getDeclarationFlag()).getDesc());
        }
        infoV2VO.setCustomsEntryType(inventoryOrderInfoDTO.getCustomsEntryType());
        infoV2VO.setCustomsType(inventoryOrderInfoDTO.getCustomsType());
        if (Objects.nonNull(inventoryOrderInfoDTO.getCustomsType())) {
            infoV2VO.setCustomsTypeDesc(InventoryCustomsTypeEnums.getEnum(inventoryOrderInfoDTO.getCustomsType()).getDesc());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            infoV2VO.setCorrCusDeclareCompanyId(inventoryOrderInfoDTO.getCorrCusDeclareCompanyId());
            infoV2VO.setCorrCusDeclareCompanyName(companyDTO.getName());
            infoV2VO.setCorrCusDeclareCompanyCode(companyDTO.getCode());
            infoV2VO.setCorrCusDeclareCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        infoV2VO.setInAccountBook(inventoryOrderInfoDTO.getInAccountBook());
        infoV2VO.setOutAccountBook(inventoryOrderInfoDTO.getOutAccountBook());
        infoV2VO.setAssociatedEndorsementNo(inventoryOrderInfoDTO.getAssociatedEndorsementNo());
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusDeclareCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
            infoV2VO.setRltCusDeclareCompanyId(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
            infoV2VO.setRltCusDeclareCompanyName(companyDTO.getName());
            infoV2VO.setRltCusDeclareCompanyCode(companyDTO.getCode());
            infoV2VO.setRltCusDeclareCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusXFDYCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusXFDYCompanyId());
            infoV2VO.setRltCusXFDYCompanyId(inventoryOrderInfoDTO.getRltCusDeclareCompanyId());
            infoV2VO.setRltCusXFDYCompanyName(companyDTO.getName());
            infoV2VO.setRltCusXFDYCompanyCode(companyDTO.getCode());
            infoV2VO.setRltCusXFDYCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        if (Objects.nonNull(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId())) {
            CompanyDTO companyDTO = companyService.findById(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
            infoV2VO.setRltCusInnerSFHRCompanyId(inventoryOrderInfoDTO.getRltCusInnerSFHRCompanyId());
            infoV2VO.setRltCusInnerSFHRCompanyName(companyDTO.getName());
            infoV2VO.setRltCusInnerSFHRCompanyCode(companyDTO.getCode());
            infoV2VO.setRltCusInnerSFHRCompanyUSCC(companyDTO.getUniformSocialCreditCode());
        }
        infoV2VO.setEndorsementRemark(inventoryOrderInfoDTO.getEndorsementRemark());
        infoV2VO.setPickUpNo(inventoryOrderInfoDTO.getPickUpNo());
        infoV2VO.setForwardingCompany(inventoryOrderInfoDTO.getForwardingCompany());
        infoV2VO.setConNo(inventoryOrderInfoDTO.getConNo());
        infoV2VO.setEntryPort(inventoryOrderInfoDTO.getEntryPort());
        infoV2VO.setFromLocation(inventoryOrderInfoDTO.getFromLocation());
        infoV2VO.setArrivalPort(inventoryOrderInfoDTO.getArrivalPort());
        infoV2VO.setProductName(inventoryOrderInfoDTO.getProductName());
        infoV2VO.setCategory(inventoryOrderInfoDTO.getCategory());
        infoV2VO.setTransferor(inventoryOrderInfoDTO.getTransferor());
        infoV2VO.setTransferee(inventoryOrderInfoDTO.getTransferee());
        infoV2VO.setActualArrivalDate(inventoryOrderInfoDTO.getActualArrivalDate());
        infoV2VO.setChannel(inventoryOrderInfoDTO.getChannel());
        infoV2VO.setAreaBookId(inventoryOrderInfoDTO.getBookId());
        infoV2VO.setAreaBookType(customsBookResVo.getBookType());
        infoV2VO.setTransitFlag(inventoryOrderInfoDTO.getTransitFlag());
        infoV2VO.setDeclareFormNo(inventoryOrderInfoDTO.getDeclareFormNo());
        infoV2VO.setLockStockFlag(inventoryOrderInfoDTO.getLockStockFlag());
        return infoV2VO;
    }

    @PostMapping("/confirmDraft")
    public RpcResult<Boolean> confirmDraft(DraftConfirmReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("id不能为空");
        }
        inventoryOrderInfoService.confirmDraft(reqVO.getId(), reqVO.getCompareType());
        return RpcResult.success();
    }

    @PostMapping("/createByOuter")
    public RpcResult<String> createInventoryOrderInner(@RequestBody InventoryOrderInfoSubmitReqVo submitReqVo) {
        log.info("createByOuter 提交清关单 info={}", JSON.toJSONString(submitReqVo));
        try {
            ThreadContextUtil.setRequestInfo(JSON.toJSONString(submitReqVo));
            InventoryOrderInfoParam inventoryOrderInfoParam = ConvertUtil.beanConvert(submitReqVo, InventoryOrderInfoParam.class);
            List<InventoryOrderItemParam> inventoryOrderItemParams = ConvertUtil.listConvert(submitReqVo.getListItems(), InventoryOrderItemParam.class);
            List<InventoryOrderAttachDTO> inventoryOrderAttachDTOS = ConvertUtil.listConvert(submitReqVo.getAttachList(), InventoryOrderAttachDTO.class);
            inventoryOrderInfoParam.setListItems(inventoryOrderItemParams);
            inventoryOrderInfoParam.setAttachList(inventoryOrderAttachDTOS);

            String result = inventoryOrderInfoService.createInventoryOrder(inventoryOrderInfoParam);
            return RpcResult.success("新增成功", result);
        } catch (ArgsInvalidException e) {
            log.error("提交清关单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (ArgsErrorException e) {
            log.error("提交清关单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("提交清关单失败 error={}", e.getMessage(), e);
            return RpcResult.error("提交清关单失败 请联系CCS技术处理");
        }
    }

    @PostMapping("/outer/updateInventoryOrderItem")
    public RpcResult<String> updateInventoryOrderItemOuter(@RequestBody InventoryOrderItemUpdateReq request) {
        log.info("InventoryOrderRpc updateInventoryOrderItem request={}", JSON.toJSONString(request));
        InventoryOrderItemUpdateReqVO reqVO = new InventoryOrderItemUpdateReqVO();
        reqVO.setChannelBusinessType(request.getChannelBusinessType());
        reqVO.setChannelBusinessSn(request.getChannelBusinessSn());
        List<InventoryOrderItemUpdateReqVO.ItemVo> itemVos = ConvertUtil.listConvert(request.getItemVoList(), InventoryOrderItemUpdateReqVO.ItemVo.class);
        reqVO.setItemVoList(itemVos);
        inventoryOrderInfoService.updateInventoryOrderItemOuter(reqVO);
        return RpcResult.success("更新成功");
    }


    @PostMapping("/outer/uploadAttachList")
    public RpcResult<String> uploadAttachListOuter(@RequestBody InventoryOrderAttachUploadReq request) {
        log.info("InventoryOrderRpc uploadAttachListOuter reqVo={}", JSON.toJSONString(request));
        InventoryOrderAttachUploadReqVO reqVo = new InventoryOrderAttachUploadReqVO();
        reqVo.setChannelBusinessType(request.getChannelBusinessType());
        reqVo.setChannelBusinessSn(request.getChannelBusinessSn());
        List<InventoryOrderAttachDTO> inventoryOrderAttachDTOS = ConvertUtil.listConvert(request.getAttachList(), InventoryOrderAttachDTO.class);
        reqVo.setAttachList(inventoryOrderAttachDTOS);
        inventoryOrderInfoService.uploadAttachListOuter(reqVo);
        return RpcResult.success();
    }

}
