package com.danding.cds.web.customsStatusMapping.rpc;

import com.danding.cds.common.enums.OrderInternalEnum;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.enums.DeclareItemStatusEnums;
import com.danding.cds.order.api.enums.MapStatusEnums;
import com.danding.cds.order.api.enums.YesOrNoStatusEnum;
import com.danding.cds.order.api.service.CustomsStatusMappingService;
import com.danding.cds.order.api.vo.CustomsStatusMappingAddReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingReqVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingResVO;
import com.danding.cds.order.api.vo.CustomsStatusMappingUpdReqVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.collect.Lists;

import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/5/7
 * @menu 回执映射
 */
@DubboService
@Slf4j
public class CustomsStatusMappingRpcimpl implements CustomsStatusMappingRpc {


    @DubboReference
    private CustomsStatusMappingService mappingService;


    /**
     * 映射回执分页
     *
     * @param reqVO
     * @return ListVO<CustomsStatusMappingResVO>
     * @path /receiptMapping/paging
     */
    @Override
    @SoulClient(path = "/receiptMapping/paging", desc = "映射回执分页")
    public RpcResult<ListVO<CustomsStatusMappingResVO>> paging(CustomsStatusMappingReqVO reqVO) {
        return RpcResult.success(mappingService.paging(reqVO));
    }


    @Override
    @SoulClient(path = "/receiptMapping/findPendingCount", desc = "待处理总数量")
    public RpcResult findPendingCount() {
        return RpcResult.success(mappingService.findPendingCount());
    }

    @Override
    @SoulClient(path = "/receiptMapping/mapStatus", desc = "映射状态下拉")
    public RpcResult mapStatus() {
        return RpcResult.success(EnumUtils.build(MapStatusEnums.class, "value", "desc"));
    }

    @Override
    @SoulClient(path = "/receiptMapping/listInternalStatus", desc = "内部流转状态下拉")
    public RpcResult listInternalStatus() {
        return RpcResult.success(EnumUtils.build(OrderInternalEnum.class, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/receiptMapping/declareItemStatus", desc = "申报项状态下拉")
    public RpcResult declareItemStatus() {
        return RpcResult.success(EnumUtils.build(DeclareItemStatusEnums.class, "value", "desc"));
    }


    @Override
    @SoulClient(path = "/receiptMapping/getCustomsReceipt", desc = "回执映射下拉")
    public RpcResult getCustomsReceipt() {
        List<CustomsStatusMappingDTO> mappingDTOS = mappingService.getCustomsReceipt();
        List<SelectOptionVO> result = mappingDTOS.stream().map(a -> {
            SelectOptionVO selectItemVO = new SelectOptionVO();
            selectItemVO.setId(a.getId());
            selectItemVO.setName(a.getId() + "-" + a.getNote());
            return selectItemVO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/receiptMapping/customsStat", desc = "海关状态")
    public RpcResult customsStat() {
        List<SelectOptionVO> result = Arrays.stream(CustomsStat.values()).filter((CustomsStat customsStatus) -> !"".equals(customsStatus.getValue())).map((CustomsStat customsStatus) -> {
            SelectOptionVO selectItemVO = new SelectOptionVO();
            selectItemVO.setId(customsStatus.getValue());
            selectItemVO.setName(customsStatus.getValue() + "-" + customsStatus.getDesc());
            return selectItemVO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/receiptMapping/modify/customsStat", desc = "修改海关状态枚举")
    public RpcResult<List<SelectOptionVO<String>>> getModifyCustomsStat() {
        List<CustomsStat> modifyCustomsStat = Lists.newArrayList(CustomsStat.CUSTOMS_PASS, CustomsStat.CUSTOMS_PERSON, CustomsStat.CUSTOMS_HANG_UP);
        List<SelectOptionVO<String>> result = modifyCustomsStat.stream().map(customsStatus -> {
            SelectOptionVO<String> selectItemVO = new SelectOptionVO<>();
            selectItemVO.setId(customsStatus.getValue());
            selectItemVO.setName(customsStatus.getDesc());
            return selectItemVO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/receiptMapping/yesOrNo", desc = "是否异常")
    public RpcResult yesOrNoStatus() {
        return RpcResult.success(EnumUtils.build(YesOrNoStatusEnum.class, "value", "desc"));
    }


    /**
     * 新增回执映射
     *
     * @param reqVO
     * @return
     * @path /receiptMapping/save
     */
    @Override
    @SoulClient(path = "/receiptMapping/save", desc = "新增回执映射")
    public RpcResult save(CustomsStatusMappingAddReqVO reqVO) throws ArgsErrorException {
        try {
            if (Objects.isNull(reqVO.getReceiptExplain())) {
                throw new ArgsErrorException("映射说明不能为空！");
            }
            if (Objects.isNull(reqVO.getStatus())) {
                throw new ArgsErrorException("请选择申报项状态！");
            }
            if (Objects.isNull(reqVO.getExceptionFlag())) {
                throw new ArgsErrorException("请选择异常状态！");
            }
            mappingService.save(reqVO);
            return RpcResult.success(true);
        } catch (ArgsErrorException ex) {
            return RpcResult.error(ex.getErrorMessage());
        }
    }

    /**
     * 回执编辑
     *
     * @param reqVO
     * @return
     * @path /receiptMapping/edit
     */
    @Override
    @SoulClient(path = "/receiptMapping/edit", desc = "回执编辑")
    public RpcResult edit(CustomsStatusMappingUpdReqVO reqVO) throws ArgsErrorException {
        try {
            mappingService.edit(reqVO);
            return RpcResult.success(true);
        } catch (ArgsErrorException ex) {
            return RpcResult.error(ex.getErrorMessage());
        }
    }

    @Override
    @SoulClient(path = "/receiptMapping/delete", desc = "删除回执")
    public RpcResult delete(CustomsStatusMappingUpdReqVO reqVO) {
        try {
            mappingService.delete(reqVO);
            return RpcResult.success(true);
        } catch (Exception ex) {
            return RpcResult.error(ex.getMessage());
        }
    }

    /**
     * code下拉
     *
     * @return
     * @path /receiptMapping/listCode
     */
    @Override
    @SoulClient(path = "/receiptMapping/listCode", desc = "code下拉")
    public RpcResult listCode() {
        List<CustomsStatusMappingDTO> customsStatusMappingDTOS = mappingService.listAll();
        List<String> codeList = customsStatusMappingDTOS.stream().map(CustomsStatusMappingDTO::getCode).distinct().collect(Collectors.toList());
        List<SelectOptionVO<String>> list = codeList.stream().map(i -> {
            SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i);
            selectOptionVO.setName(i);
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

}
