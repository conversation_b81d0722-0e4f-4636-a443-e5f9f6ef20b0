package com.danding.cds.web.checklist.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel
public class ChecklistPagingResult {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("企业内部编号")
    private String sn;

    @ApiModelProperty("预录入编号")
    private String orderNo;

    @ApiModelProperty("核放单号")
    private String realOrderNo;

    @ApiModelProperty("核注清单编号")
    private String endorsementSns;

    @ApiModelProperty("报关单号")
    private String declareOrderNo;

    @ApiModelProperty("IC卡号")
    private String vehicleIcNo;

    @ApiModelProperty("绑定类型")
    private String bindType;

    @ApiModelProperty("类型")
    private Integer type;

    @ApiModelProperty("类型")
    private String typeDesc;

    @ApiModelProperty("关联车辆信息")
    private String licensePlate;

    @ApiModelProperty("创建时间")
    private String createAt;

    @ApiModelProperty("回执时间")
    private String lastCustomsAt;

    @ApiModelProperty("录入时间")
    private String enterFinishAt;

    @ApiModelProperty("状态")
    private String checklistStatus;

    //核放单状态码
    private String checklistStatusCode;

    @ApiModelProperty("海关回执状态描述")
    private String customsStatusDesc;

    @ApiModelProperty("信息描述")
    private String informationDesc = "";

    @ApiModelProperty("清关企业ID")
    private Long declareCompanyID;
    private String declareCompanyName;

    @ApiModelProperty("申请人")
    private String applicant;

    @ApiModelProperty("已关联的核注清单")
    List<EndorsementChecklistVO> endorsementList;

    @ApiModelProperty("允许编辑")
    private Boolean allowEdit = false;

    @ApiModelProperty("允许作废")
    private Boolean allowDiscard = false;

    @ApiModelProperty("允许推送")
    private Boolean allowPush = false;

    @ApiModelProperty("允许查看表体")
    private Boolean allowLoadItem = false;

    @ApiModelProperty("允许完成核放")
    private Boolean allowFinish = false;

    @ApiModelProperty("允许暂存")
    private Boolean allowTemporaryStorage = false;

    @ApiModelProperty("操作人")
    private String createBy;

}
