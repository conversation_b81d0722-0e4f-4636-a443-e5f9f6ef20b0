package com.danding.cds.web.item;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.GoodsRecordExportVO;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.enums.GoodsRecordChannel;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeDTO;
import com.danding.cds.v2.bean.dto.AiRecommendHsCodeRefreshDTO;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.bean.vo.req.UpdFinishGoodsRecordReqVo;
import com.danding.cds.v2.service.AiRecommendHsCodeService;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.warehouse.api.WarehouseService;
import com.danding.cds.web.item.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Validator;
import java.util.*;
import java.util.stream.Collectors;

@Api(tags = "商品备案管理")
@RestController
@RequestMapping("/goodsRecord")
@Slf4j
public class GoodsRecordController {


    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private WarehouseService warehouseService;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    @Autowired
    private Validator validator;
    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private AiRecommendHsCodeService aiRecommendHsCodeService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private MessageSender messageSender;
    @DubboReference
    private RecordCustomsService recordCustomsService;

//    @PostMapping("/item/book/callback/goods/record/create")
//    public String invokeGenerateGoodsRecord(String resultInfo, String customsBookDTO) {
//        goodsRecordService.invokeGenerateGoodsRecord(resultInfo, customsBookDTO);
//        return "success";
//    }

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<GoodsRecordVO> paging(GoodsRecordSearchCondition condition) {
        ListVO<GoodsRecordDTO> pageResult = goodsRecordService.paging(condition);
        ListVO<GoodsRecordVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<GoodsRecordVO> list = new ArrayList<>();
        Map<String, String> tenantIdName = new HashMap<>();
        for (GoodsRecordDTO dto : pageResult.getDataList()) {
            GoodsRecordVO recordVO = new GoodsRecordVO();
            BeanUtil.copyProperties(dto, recordVO);
            if (!LongUtil.isNone(dto.getCreateTime())) {
                recordVO.setCreateTime(new DateTime(dto.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            if (!StringUtils.isEmpty(dto.getTenantId())) {
                if (tenantIdName.containsKey(dto.getTenantId())) {
                    recordVO.setTenantName(tenantIdName.get(dto.getTenantId()));
                } else {
                    String tenantName = taxesTenantAccountService.getTenantNameById(dto.getTenantId());
                    recordVO.setTenantName(tenantName);
                    tenantIdName.put(dto.getTenantId(), tenantName);
                }
            }
            CustomsBookDTO customsBookDTO = customsBookService.findById(dto.getCustomsBookId());
            recordVO.setCustomsBookNo(customsBookDTO.getBookNo());
            recordVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(dto.getRecordStatus()).getDesc());
            //租户暂时为空
            recordVO.setLesseeName("");
            list.add(recordVO);
        }
        result.setDataList(list);
        return result;
    }


    @ApiOperation(value = "导出")
    @GetMapping("/exportExcel")
    public void exportExcel(GoodsRecordSearchCondition condition, HttpServletResponse httpServletResponse) {
        if (false) {
            try {
                downloadProcessService.submitDownloadProcess(1L,
                        condition, ReportType.GOODS_RECORD_FOR_EXCEL);
            } catch (ServiceException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            return;
        }
        condition.setPageIgnore(1);
        ListVO<GoodsRecordDTO> paging = goodsRecordService.paging(condition);
        List<GoodsRecordExportVO> exportVOS = Lists.newArrayList();
        for (GoodsRecordDTO dto : paging.getDataList()) {
            GoodsRecordExportVO exportVO = new GoodsRecordExportVO();
            BeanUtil.copyProperties(dto, exportVO);
            if (!LongUtil.isNone(dto.getCreateTime())) {
                exportVO.setCreateTime(new DateTime(dto.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            if (!LongUtil.isNone(dto.getUpdateTime())) {
                exportVO.setRecordUpdateTime(new DateTime(dto.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            exportVO.setCustomsBookNo(customsBookService.findById(dto.getCustomsBookId()).getBookNo());
            exportVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(dto.getRecordStatus()).getDesc());
            exportVOS.add(exportVO);
        }
        ExportParams exportParams = new ExportParams("商品档案", "商品档案信息",
                ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, GoodsRecordExportVO.class, exportVOS);
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(exportParams.getTitle().getBytes(), "iso8859-1") + ".xlsx");
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "导入")
    @PostMapping("/importExcel")
    public Long importExcel(MultipartFile file) {
        ImportParams importParams = new ImportParams();
        // 数据处理
        importParams.setHeadRows(1);
        importParams.setTitleRows(1);
        List<GoodsRecordExportVO> list;
        try {
            ExcelImportResult<GoodsRecordExportVO> result = ExcelImportUtil.importExcelMore(file.getInputStream(), GoodsRecordExportVO.class,
                    importParams);
            list = result.getList();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            throw new ArgsErrorException("上传失败");
        }
        int i = 3;
        Set<String> keySet = new HashSet<String>();
        Set<String> skuKeySet = new HashSet<String>();
        for (GoodsRecordExportVO demoExcel : list) {
            //料号去除前后空格
            String productId = demoExcel.getProductId().trim();
            demoExcel.setProductId(productId);
            String key = demoExcel.getProductId() + demoExcel.getCustomsBookNo();
            if (keySet.contains(key)) {
                throw new ArgsErrorException("料号:" + demoExcel.getProductId() + ",账册编码:" + demoExcel.getCustomsBookNo() + "导入重复数据！");
            }
            String skuKey = demoExcel.getSkuId() + demoExcel.getCustomsBookNo();
            if (skuKeySet.contains(skuKey)) {
                throw new ArgsErrorException("sku:" + demoExcel.getSkuId() + ",账册编码:" + demoExcel.getCustomsBookNo() + "导入重复数据！");
            }
            skuKeySet.add(skuKey);
            String title = "第" + i + "行";
            //校验参数必填项
            String inputError = ValidatorUtils.doValidator(validator, demoExcel);
            if (!StringUtils.isEmpty(inputError)) {
                throw new ArgsErrorException(title + inputError);
            }
            //数据校验
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(demoExcel.getCustomsBookNo());
            if (customsBookDTO == null) {
                throw new ArgsErrorException(title + "账册编号错误");
            }
            CustomsHsDTO customsHsDTO = customsHsService.findByCode(demoExcel.getHsCode());
            if (customsHsDTO == null) {
                throw new ArgsErrorException(title + "hsCode不存在");
            } else {
                if (customsHsDTO.getTariff() == null || customsHsDTO.getVat() == null) {
                    throw new ArgsErrorException(title + "hsCode" + customsHsDTO.getHsCode() + "相关信息未完善");
                }
            }
            if (demoExcel.getOpinion() != null) {
                if (demoExcel.getOpinion() == 0 && StringUtils.isEmpty(demoExcel.getReason())) {
                    throw new ArgsErrorException(title + "审核驳回时，驳回原因不能为空！");
                }
//                if (demoExcel.getOpinion() == 1 && StringUtils.isEmpty(demoExcel.getCountryRecordNo())) {
//                    throw new ArgsErrorException(title + "国检备案号不能为空！");
//                }
            }

            i++;
        }
        for (GoodsRecordExportVO demoExcel : list) {
            CustomsBookDTO customsBookDTO = customsBookService.findByCode(demoExcel.getCustomsBookNo());
            GoodsRecordDTO old = goodsRecordService.findByBookIdAndProId(customsBookDTO.getId(), demoExcel.getProductId());
            GoodsRecordSubmit submit = new GoodsRecordSubmit();
            BeanUtil.copyProperties(demoExcel, submit);
            submit.setCustomsBookId(customsBookDTO.getId());
            if (old == null) {
                if (submit.getOpinion() == null) {
                    submit.setRecordStatus(GoodsRecordStatusEnum.WAIT_EXAMINE.getCode());
                    submit.setReason("");
                } else {
                    if (submit.getOpinion() == 0) {
                        continue;
                    } else if (submit.getOpinion() == 1) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                        submit.setReason("");
                        submit.setRecordFinishTime(System.currentTimeMillis());
                    }
                }
            } else {
                if (old.getRecordStatus() == GoodsRecordStatusEnum.RECORD_SUCCESS.getCode()) {//备案完成不做更新
                    continue;
                }
                if (submit.getOpinion() != null) {
                    if (submit.getOpinion() == 1) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.RECORD_SUCCESS.getCode());
                        submit.setReason("");
                        submit.setRecordFinishTime(System.currentTimeMillis());
                    } else if (submit.getOpinion() == 0) {
                        submit.setRecordStatus(GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode());
                        submit.setReason(submit.getReason());
                    }
                }
            }
            Long recordId = goodsRecordService.upset(submit);
        }
        return 1L;
    }

    @ApiOperation(value = "启用/禁用")
    @PostMapping("/updateEnable")
    public Long updateEnable(@RequestBody GoodsRecordSubmit submit) throws ArgsErrorException {
        return goodsRecordService.updateEnable(submit.getId(), submit.getEnable());
    }

    @ApiOperation(value = "删除")
    @PostMapping("/deleteById")
    public Long deleteById(@RequestBody GoodsRecordSubmit submit) throws ArgsErrorException {
        return goodsRecordService.deleteById(submit.getId());
    }

    @ApiOperation(value = "保存/编辑")
    @PostMapping("/upset")
    public Long upset(@RequestBody GoodsRecordSubmit submit) throws ArgsErrorException {
        return goodsRecordService.upset(submit);
    }

    @ApiOperation(value = "详情")
    @GetMapping("/detail")
    public GoodsRecordDetailVO detail(Long id) {
        GoodsRecordDetailVO detailVO = new GoodsRecordDetailVO();
        GoodsRecordDTO recordDTO = goodsRecordService.findById(id);
        if (recordDTO == null) {
            return null;
        }
        BeanUtil.copyProperties(recordDTO, detailVO);
        detailVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(recordDTO.getRecordStatus()).getDesc());
        if (!LongUtil.isNone(recordDTO.getCreateTime())) {
            detailVO.setCreateTime(new DateTime(recordDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(recordDTO.getRecordFinishTime())) {
            detailVO.setRecordFinishTime(new DateTime(recordDTO.getRecordFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        detailVO.setOriginCountryName(customsCountryService.findByCode(recordDTO.getOriginCountry()).getName());
        detailVO.setCustomsBookNo(customsBookService.findById(recordDTO.getCustomsBookId()).getBookNo());
        return detailVO;
    }

    @ApiOperation(value = "根据料号查找")
    @GetMapping("/findByProId")
    public GoodsRecordDTO findByProId(String productId) {
        return goodsRecordService.findByProId(productId);
    }

    @ApiOperation(value = "货品备案信息查询接口")
    @PostMapping("/CCS_QUERY_CUSTOMS_RECORD")
    public GoodsRecordDetailVO queryCustomsRecord(@RequestBody GoodsRecordQuerySubmit submit) {
        GoodsRecordDTO recordDTO = goodsRecordService.queryCustomsRecord(submit);

        GoodsRecordDetailVO detailVO = new GoodsRecordDetailVO();
        BeanUtil.copyProperties(recordDTO, detailVO);
        detailVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(recordDTO.getRecordStatus()).getDesc());
        if (!LongUtil.isNone(recordDTO.getCreateTime())) {
            detailVO.setCreateTime(new DateTime(recordDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(recordDTO.getRecordFinishTime())) {
            detailVO.setRecordFinishTime(new DateTime(recordDTO.getRecordFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        detailVO.setOriginCountryName(customsCountryService.findByCode(recordDTO.getOriginCountry()).getName());
        detailVO.setCustomsBookNo(customsBookService.findById(recordDTO.getCustomsBookId()).getBookNo());
        return detailVO;
    }

    @ApiOperation(value = "货品备案信息同步接口")
    @PostMapping("/CCS_SYNC_CUSTOMS_RECORD")
    public Long syncCustomsRecord(@RequestBody GoodsRecordSyncSubmit submit) {
        GoodsRecordSubmit savedSubmit = new GoodsRecordSubmit();
        BeanUtil.copyProperties(submit, savedSubmit);
        savedSubmit.setEnable(1);
        submit.setChannel(GoodsRecordChannel.LOGISTICS.getValue());
        return goodsRecordService.upset(savedSubmit);

    }

    @ApiOperation(value = "审核")
    @PostMapping("/audit")
    public Long audit(@RequestBody GoodsRecordAuditSubmit submit) throws ArgsErrorException {
        return goodsRecordService.audit(submit);

    }

    @ApiOperation(value = "商品备案迁移")
    @PostMapping("/transferOmsGoodsRecord")
    public Long transferOmsGoodsRecord(String originType) {
        GoodsRecordSubmit submit = new GoodsRecordSubmit();
        if ("OMS-ITEM".equals(originType)) {
            submit.setCustomsBookId(9L);//来源oms的商品备案 账册编号先写死为88账册
        }
        return 1L;
    }

    @ApiOperation(value = "获取仓库列表")
    @GetMapping("/listWares")
    public List<SelectItemVO> listWares() throws ArgsErrorException {
        Map<String, String> warehouseDesMap = warehouseService.getWarehouseDesMap();
        List<SelectItemVO> result = new ArrayList<>();
        for (String key : warehouseDesMap.keySet()) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(key);
            optionDTO.setName(warehouseDesMap.get(key));
            result.add(optionDTO);
        }

        return result;
    }

    @PostMapping("/updateFinishGoodsRecord")
    public RpcResult updateFinishGoodsRecord(@RequestBody UpdFinishGoodsRecordReqVo reqVo) {
        try {
            goodsRecordService.updateFinishGoodsRecord(reqVo);
            return RpcResult.success();
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @PostMapping("/aiRecommendHsCodeList")
    public RpcResult<AiRecommendHsCodeResVo> aiRecommendHsCodeList(@RequestBody AiRecommendHsCodeReqVo reqVo) {
        log.info("aiRecommendHsCodeList reqVo={}", JSON.toJSONString(reqVo));
        String taskStatus = stringRedisTemplate.opsForValue().get("ccs:ai:recommend:hs:code:task:" + reqVo.getRecordId());
        AiRecommendHsCodeResVo resVo = new AiRecommendHsCodeResVo();
        resVo.setTaskStatus(taskStatus);
        if (StrUtil.isNotBlank(taskStatus) && !"2".equals(taskStatus)) {
            if ("-1".equals(taskStatus)) {
                stringRedisTemplate.delete("ccs:ai:recommend:hs:code:task:" + reqVo.getRecordId());
            }
            return RpcResult.success(resVo);
        }
        if (reqVo.isRefresh()) {
            AiRecommendHsCodeRefreshDTO refreshDTO = new AiRecommendHsCodeRefreshDTO();
            refreshDTO.setRecordId(reqVo.getRecordId());
            refreshDTO.setCustomsCode(reqVo.getCustomsCode());
            refreshDTO.setRecordGoodsName(reqVo.getRecordGoodsName());
            refreshDTO.setRecordComposition(reqVo.getRecordComposition());
            refreshDTO.setForce(true);
            aiRecommendHsCodeService.refreshCustomsRecommend(refreshDTO);
            resVo.setTaskStatus("1");
            return RpcResult.success(resVo);
        }
        List<AiRecommendHsCodeDTO> list = aiRecommendHsCodeService.recommendHsCodeList(reqVo.getRecordId(), reqVo.getCustomsCode(),
                reqVo.getRecordGoodsName(), reqVo.getRecordComposition());
        if (CollUtil.isEmpty(list)) {
            resVo.setTaskStatus("1");
            return RpcResult.success(resVo);
        }
        List<String> hsCodeList = list.stream().map(AiRecommendHsCodeDTO::getRecommendHsCode).collect(Collectors.toList());
        List<CustomsHsDTO> customsHsDTOList = customsHsService.findByCode(hsCodeList);
        Map<String, CustomsHsDTO> hsDTOMap = new HashMap<>();
        customsHsDTOList.forEach(customsHsDTO -> hsDTOMap.put(customsHsDTO.getHsCode(), customsHsDTO));
        List<AiRecommendHsCodeResVo.AiRecommendHsCodeVO> voList = list.stream().map(i -> {
            AiRecommendHsCodeResVo.AiRecommendHsCodeVO vo = new AiRecommendHsCodeResVo.AiRecommendHsCodeVO();
            BeanUtils.copyProperties(i, vo);
            vo.setExistFlag(hsDTOMap.containsKey(i.getRecommendHsCode()) ? 1 : 0);
            vo.setRecommendHsCode(vo.getRecommendHsCode());
            return vo;
        }).collect(Collectors.toList());
        resVo.setTaskStatus("2");
        resVo.setAiRecommendHsCodeVOList(voList);
        return RpcResult.success(resVo);
    }

    @PostMapping("/getNextWaitAuditRecord")
    public RpcResult<GoodsRecordAuditNextResVO> getNextWaitAuditRecord(GoodsRecordAuditNextReqVO reqVO) {
        log.info("getNextWaitAuditRecord reqVO={}", JSON.toJSONString(reqVO));
        List<Long> accountBookIdList = reqVO.getAccountBookIdList();
        if (CollectionUtils.isEmpty(accountBookIdList)) {
            return RpcResult.success();
        }
        RecordCustomsDTO recordCustomsDTO = recordCustomsService.getNextWaitAuditRecord(accountBookIdList, reqVO.getCustomsCode(), reqVO.getRecordId());
        log.info("getNextWaitAuditRecord goodsRecordEsDTOList={}", JSON.toJSONString(recordCustomsDTO));
        if (Objects.nonNull(recordCustomsDTO)) {
            // 找到同一个口岸下下一个待审核的备案
            GoodsRecordAuditNextResVO resVO = new GoodsRecordAuditNextResVO();
            resVO.setRecordId(recordCustomsDTO.getRecordId());
            resVO.setCustomsCode(recordCustomsDTO.getCustomsCode());
            return RpcResult.success(resVO);
        } else {
            // 同口岸不存在，则找下一个待审核的备案
            RecordCustomsDTO nextWaitAuditRecord = recordCustomsService.getNextWaitAuditRecord(accountBookIdList, null, null);
            if (Objects.nonNull(nextWaitAuditRecord)) {
                GoodsRecordAuditNextResVO resVO = new GoodsRecordAuditNextResVO();
                resVO.setRecordId(nextWaitAuditRecord.getRecordId());
                resVO.setCustomsCode(nextWaitAuditRecord.getCustomsCode());
                return RpcResult.success(resVO);
            }
            return RpcResult.success();
        }
    }
}
