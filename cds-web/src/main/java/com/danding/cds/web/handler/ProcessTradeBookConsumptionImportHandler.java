package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.v2.bean.dto.ProcessTradeBookConsumptionDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.cds.web.v2.bean.vo.req.ProcessTradeBookConsumptionImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 加贸账册单耗导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_PROCESS_TRADE_BOOK_CONSUMPTION", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%8A%A0%E8%B4%B8%E8%B4%A6%E5%86%8C%E5%8D%95%E6%8D%9F%E8%80%97%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "sheet1", classes = ProcessTradeBookConsumptionImportExcelVO.class),})
public class ProcessTradeBookConsumptionImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long refBookId = Long.parseLong((String) extendMap.get("refBookId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        ProcessTradeBookService processTradeBookService = this.getBean(ProcessTradeBookService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);
        ProcessTradeBookDTO processTradeBookDTO = processTradeBookService.findById(refBookId);
        List<ProcessTradeBookItemDTO> endprdList = processTradeBookService.listEndprdSeqById(refBookId);
        Map<Integer, ProcessTradeBookItemDTO> endprdMap = endprdList.stream().collect(Collectors.toMap(ProcessTradeBookItemDTO::getSeqNo, i -> i));
        List<ProcessTradeBookItemDTO> mtpckList = processTradeBookService.listMtpckSeqById(refBookId);
        Map<Integer, ProcessTradeBookItemDTO> mtpckMap = mtpckList.stream().collect(Collectors.toMap(ProcessTradeBookItemDTO::getSeqNo, i -> i));
        List<ProcessTradeBookConsumptionDTO> consumptionDTOS = processTradeBookService.listConsumption(refBookId);
        Set<String> consumptionSet = consumptionDTOS.stream()
                .map(item -> item.getEndPrdId() + "_" + item.getMtpckId())
                .collect(Collectors.toSet());

        List<ProcessTradeBookConsumptionImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("sheet1")) {
                list = group.getDataList(ProcessTradeBookConsumptionImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("InvOrderItemBondedProcessImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<ProcessTradeBookConsumptionDTO> successList = new ArrayList<>();
        for (ProcessTradeBookConsumptionImportExcelVO vo : list) {
            List<String> errorMsgList = new ArrayList<>();
            try {
                String errorMsg = doValidator(vo);
                if (StrUtil.isNotBlank(errorMsg)) {
                    errorMsgList.add(errorMsg);
                }
                if (!endprdMap.containsKey(vo.getEndprdSeqNo())) {
                    errorMsgList.add("成品序号不存在");
                }
                ProcessTradeBookItemDTO endprdItemDTO = endprdMap.get(vo.getEndprdSeqNo());
                if (!mtpckMap.containsKey(vo.getMtpckSeqNo())) {
                    errorMsgList.add("料件序号不存在");
                }
                ProcessTradeBookItemDTO mtpckItemDTO = mtpckMap.get(vo.getMtpckSeqNo());
                if (endprdMap.containsKey(vo.getEndprdSeqNo()) && mtpckMap.containsKey(vo.getMtpckSeqNo())
                        && consumptionSet.contains(endprdItemDTO.getId() + "_" + mtpckItemDTO.getId())) {
                    errorMsgList.add("成品和料件组合已存在");
                }
                if (CollUtil.isNotEmpty(errorMsgList)) {
                    this.callbackData(false, index++, String.join(";", errorMsgList), vo);
                    continue;
                }
                ProcessTradeBookConsumptionDTO insertDTO = new ProcessTradeBookConsumptionDTO();
                insertDTO.setRefBookId(refBookId);
                insertDTO.setEndPrdId(endprdItemDTO.getId());
                ProcessTradeBookItemDTO mtpckDTO = mtpckItemDTO;
                insertDTO.setMtpckId(mtpckDTO.getId());
                insertDTO.setNetConsumption(vo.getNetConsumption());
                insertDTO.setDeclareStatus(vo.getDeclareStatus());
                insertDTO.setConsumptionVersionNo(mtpckDTO.getProductId());
                successList.add(insertDTO);

                consumptionSet.add(insertDTO.getEndPrdId() + "_" + insertDTO.getMtpckId());
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("加贸账册表体导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            processTradeBookService.importConsumptionExcel(refBookId, operator, successList);
        }
    }
}
