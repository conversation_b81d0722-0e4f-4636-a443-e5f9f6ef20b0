package com.danding.cds.web.checklist.rpc;

import com.danding.cds.checklist.api.dto.ChecklistSubmit;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.endorsement.api.dto.EndorsementEbInvSearch;
import com.danding.cds.endorsement.api.dto.EndorsementEditDetailReqVO;
import com.danding.cds.endorsement.api.dto.EndorsementSearch;
import com.danding.cds.endorsement.api.dto.EndorsementSubmit;
import com.danding.cds.exportorder.api.dto.ExportItemEditReport;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationEditReport;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvAddParam;
import com.danding.cds.v2.bean.vo.req.EndorsementEbInvDeleteParam;
import com.danding.cds.web.checklist.rpc.param.RpcEndorsementParam;
import com.danding.cds.web.checklist.rpc.param.RpcModifyStatusParam;
import com.danding.cds.web.checklist.vo.*;
import com.danding.cds.web.exportorder.vo.ExportItemEditPre;
import com.danding.cds.web.exportorder.vo.ExportItemEditSubmit;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditPre;
import com.danding.cds.web.invenorder.vo.InventoryOrderRelationEditSubmit;
import com.danding.cds.web.item.vo.EndorsementImportSubmit;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface EndorsementRpc {
    RpcResult<Response<String>> createByChecklist(EndorsementSubmit submit);

    RpcResult<EndorsementDetailWarp> loadDetailInfo(RpcEndorsementParam param);

    RpcResult<List<SelectOptionVO>> listBussinessTypes();

    RpcResult<List<SelectOptionVO>> listBussinessTypesChecklist();

    RpcResult<List<SelectOptionVO<String>>> listBusinessTypesByChecklist(Integer checklistType);

    RpcResult<List<SelectOptionVO>> listStatus();

    RpcResult<ListVO<EndorsementSearchResult>> paging(EndorsementSearch search);

    RpcResult<InventoryOrderRelationEditReport> preRefundEditMailNo(InventoryOrderRelationEditPre submit);

    RpcResult<InventoryOrderRelationEditReport> submitEditMailNo(InventoryOrderRelationEditSubmit submit);

    RpcResult<ExportItemEditReport> preEditMailNo(ExportItemEditPre submit);

    /**
     * 核注编辑提交，功能同{@link EndorsementRpc#hzEditSubmit(ExportItemEditSubmit)}
     * 由于网关总是提示问题，所以换个地址
     *
     * @param submit
     * @return
     */
    @Deprecated
    RpcResult<ExportItemEditReport> submitEditMailNo(ExportItemEditSubmit submit);

    /**
     * 核注编辑提交
     *
     * @param submit
     * @return
     */
    RpcResult<ExportItemEditReport> hzEditSubmit(ExportItemEditSubmit submit);

    RpcResult<Response<String>> retryException(IdsParam param);

    RpcResult<Response<String>> temporaryStorage(IdsParam idsParam);

    RpcResult<Response<String>> saveRemark(EndorsementRemarkVO endorsementRemarkVO);

    RpcResult<Response<String>> handlerCheck(IdsParam param);

    RpcResult<Response<String>> finish(EndorsementFinishSubmit submit);

    RpcResult<Response<String>> discard(IdParam idParam);

    RpcResult<List<EndorsementChecklistVO>> listForChecklist();

    /**
     * 核注单列表，过滤有效的账册权限
     *
     * @return
     */
    RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklist();

    RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklistByType(String businessType);

    RpcResult<List<EndorsementChecklistVO>> bookAuthlistForChecklistByTypeAndCompany(String businessType, Long companyId);

    /**
     * 在业务类型+清关企业的基础上返回已返回的核注单号
     *
     * @param businessType
     * @param companyId
     * @return
     */
    RpcResult<List<EndorsementChecklistVO>> listByTypeAndCompanyWithBound(String businessType, Long companyId, Long checklistId);

    /**
     * 批量导出
     *
     * @param search
     */
    RpcResult<String> excelExport(EndorsementSearch search);

    /**
     * 单个导出
     *
     * @param idParam
     */
    RpcResult<String> singleExport(IdParam idParam);

    /**
     * 剔除异常
     *
     * @param idParam
     * @return
     */
    RpcResult eliminateException(IdParam idParam);

    /**
     * 手动修改核注单状态
     *
     * @param param 核注单状态相关参数
     * @return 执行结果
     */
    RpcResult<String> manualUpdStatus(RpcModifyStatusParam param);

    /**
     * 手动删除申请
     *
     * @param idParam
     * @return
     */
    RpcResult<String> manualDeletedApply(IdParam idParam);

    RpcResult<EndorsementDetailResVO> detail(Long id);

    RpcResult<String> editDetail(EndorsementEditDetailReqVO reqVO);

    RpcResult<ListVO<EndorsementEbInvPagingResVO>> pagingEbInvEs(EndorsementEbInvSearch search);

    RpcResult addEbInvBatch(EndorsementEbInvAddParam addReqParam);

    RpcResult addEbInvBatchPreview(EndorsementEbInvAddParam addParam);

    RpcResult deleteEbInvBatch(EndorsementEbInvDeleteParam deleteParam);

    RpcResult deleteEbInvBatchPreview(EndorsementEbInvDeleteParam deleteParam);
    RpcResult<String> deletedApply(IdsParam params);
    RpcResult<List<PagingStatusCountVO>> countPagingStatus(EndorsementSearch search);
    /**
     * 补充预录入编号
     *
     * @param preOrderNoSubmit
     * @return
     */
    RpcResult<String> updatePreOrderNo(EndorsementPreOrderNoSubmit preOrderNoSubmit);

    RpcResult createChecklist(ChecklistSubmit submit);

    RpcResult listChecklistType(String endorsementType);

    RpcResult listChecklistTypeByEndorsementId(String bussinessType, Long endorsementId);

    RpcResult importExcel(EndorsementImportSubmit submit);

    RpcResult listImportBusinessType();

    @SoulClient(path = "/endorsement/listOrderType", desc = "获取核注单类型")
    RpcResult listOrderType();
}
