package com.danding.cds.web.suningFP.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.common.config.EnvironmentConfig;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.utils.MD5AndBase64Util;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadDetailSearch;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadInfoDTO;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadInfoItemDTO;
import com.danding.cds.suningFP.api.dto.SuNingFPLoadSearch;
import com.danding.cds.suningFP.api.service.SuNingFPLoadService;
import com.danding.cds.web.suningFP.SuNingFPLoadAuditRequest;
import com.danding.cds.web.suningFP.SuNingFPLoadAuditResult;
import com.danding.cds.web.suningFP.XMLUtil;
import com.danding.cds.web.suningFP.vo.SuNingFPLoadDetailSearchResult;
import com.danding.cds.web.suningFP.vo.SuNingFPLoadRec;
import com.danding.cds.web.suningFP.vo.SuNingFPLoadSearchResult;
import com.danding.component.http.SaasHttpConstant;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.github.kevinsawicki.http.HttpRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.github.kevinsawicki.http.HttpRequest.CONTENT_TYPE_FORM;
import static com.github.kevinsawicki.http.HttpRequest.HEADER_CONTENT_TYPE;

@DubboService
@Slf4j
@RefreshScope
public class SuNingFPLoadRpcImpl implements SuNingFPLoadRpc {

    @DubboReference
    private SuNingFPLoadService suNingFPLoadService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @Value("${http.time.out:}")
    private String TIME_OUT;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/paging", desc = "苏宁装载单分页查询")
    public RpcResult<ListVO<SuNingFPLoadSearchResult>> paging(SuNingFPLoadSearch search) {
        ListVO<SuNingFPLoadInfoDTO> paging = suNingFPLoadService.paging(search);
        log.info("[op:suNingFPLoad-paging] paging={}", JSON.toJSONString(paging));
        ListVO<SuNingFPLoadSearchResult> result = new ListVO<>();
        List<SuNingFPLoadSearchResult> dataList = new ArrayList<>();
        for (SuNingFPLoadInfoDTO suNingFPLoadInfoDTO : paging.getDataList()) {
            SuNingFPLoadSearchResult searchResult = new SuNingFPLoadSearchResult();
            BeanUtils.copyProperties(suNingFPLoadInfoDTO, searchResult);
            searchResult.setCreateTime(new DateTime(suNingFPLoadInfoDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            searchResult.setUpdateTime(new DateTime(suNingFPLoadInfoDTO.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            if (suNingFPLoadInfoDTO.getAuditTime() != null) {
                searchResult.setAuditTime(new DateTime(suNingFPLoadInfoDTO.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            searchResult.setAuditStatusDesc(suNingFPLoadInfoDTO.getAuditStatus() == 10 ? "待审核" : "审核通过");
            if (10 != suNingFPLoadInfoDTO.getAuditStatus()) {
                searchResult.setAllowAudit(false);
            }
            dataList.add(searchResult);
        }
        result.setPage(paging.getPage());
        result.setDataList(dataList);
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/detailPaging", desc = "苏宁装载单明细分页查询")
    public RpcResult<ListVO<SuNingFPLoadDetailSearchResult>> detailPaging(SuNingFPLoadDetailSearch search) {
        ListVO<SuNingFPLoadInfoItemDTO> paging = suNingFPLoadService.detailPaging(search);
        log.info("[op:suNingFPLoad-detailPaging] paging={}", JSON.toJSONString(paging));
        ListVO<SuNingFPLoadDetailSearchResult> result = new ListVO<>();
        List<SuNingFPLoadDetailSearchResult> dataList = new ArrayList<>();
        Map<Long, SuNingFPLoadInfoDTO> loadInfoDTOMap = new HashMap<>();
        for (SuNingFPLoadInfoItemDTO infoItemDTO : paging.getDataList()) {
            SuNingFPLoadDetailSearchResult searchResult = new SuNingFPLoadDetailSearchResult();
            BeanUtils.copyProperties(infoItemDTO, searchResult);
            searchResult.setInvtNo("");
            searchResult.setMailNo("");
            if (loadInfoDTOMap.get(infoItemDTO.getLoadOrderId()) == null) {
                SuNingFPLoadInfoDTO suNingFPLoadInfoDTO = suNingFPLoadService.findById(infoItemDTO.getLoadOrderId());
                searchResult.setLoadOrderNo(suNingFPLoadInfoDTO.getLoadOrderNo());
                loadInfoDTOMap.put(suNingFPLoadInfoDTO.getId(), suNingFPLoadInfoDTO);
            } else {
                searchResult.setLoadOrderNo(loadInfoDTOMap.get(infoItemDTO.getLoadOrderId()).getLoadOrderNo());
            }
            searchResult.setStatusDesc(infoItemDTO.getStatus() == 10 ? "初始化" : "已完成");
            searchResult.setCreateTime(new DateTime(infoItemDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            if (infoItemDTO.getFinishTime() != null) {
                searchResult.setFinishTime(new DateTime(infoItemDTO.getFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            dataList.add(searchResult);
        }
        result.setPage(paging.getPage());
        result.setDataList(dataList);
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/audit", desc = "审核通过")
    public RpcResult<String> audit(IdParam idParam) throws Exception {
        String url;
        String appSecret;
        String appKey;
        if (EnvironmentConfig.isOnline()) {
            url = "http://fpapi.suning.com";
            appSecret = "SOtwdyCDSxGADzIv3+VixA==";
            appKey = "00800094";
        } else {
            url = "http://fpapisit.cnsuning.com";
            appSecret = "";
            appKey = "";
        }
        SuNingFPLoadInfoDTO suNingFPLoadInfoDTO = suNingFPLoadService.findById(idParam.getId());

        SuNingFPLoadAuditRequest auditRequest = new SuNingFPLoadAuditRequest();
        List<SuNingFPLoadRec> loadRecList = new ArrayList<>();
        SuNingFPLoadRec loadRec = new SuNingFPLoadRec();
        loadRec.setLoadId(suNingFPLoadInfoDTO.getLoadOrderNo());
        loadRec.setCustomsResult("申报成功");
        loadRec.setFinishedDate(DateTime.now().toString("yyyy-MM-dd HH:mm:ss"));
        loadRecList.add(loadRec);
        auditRequest.setLoadRec(loadRecList);

        String logistics_interface = XMLUtil.convertToXml(auditRequest);
        String origFullSinInfo = appSecret + logistics_interface + appSecret;
        String sign = MD5AndBase64Util.getEncode(origFullSinInfo);
        Map<String, Object> param = new HashMap<>();
        param.put("logistics_interface", logistics_interface);
        param.put("logistic_provider_id", appKey);
        param.put("msg_type", "LOAD_LIST_RETURN");
        param.put("data_digest", sign);
        String fullUrl = HttpRequest.append(url + "/fpapi-web/gateway/olp_message_receiver.htm", param);
        fullUrl = HttpRequest.encode(fullUrl);
        HttpRequest httpRequest = HttpRequest.post(fullUrl)
                .header(SaasHttpConstant.HEADER_TENANT_ID, SimpleTenantHelper.getTenantIdStr())
                .header(HEADER_CONTENT_TYPE, CONTENT_TYPE_FORM);
        log.info("[op:suNingFPLoadAudit] loadOrderNo={} url={}", suNingFPLoadInfoDTO.getLoadOrderNo(), url);
        if (httpRequest.ok()) {
            String body = httpRequest.body();
            log.info("[op:suNingFPLoadAudit] loadOrderNo={} body={}", suNingFPLoadInfoDTO.getLoadOrderNo(), body);
            SuNingFPLoadAuditResult auditResult = JSON.parseObject(body, SuNingFPLoadAuditResult.class);
            if (auditResult.getSuccess()) {
                if (!auditResult.getContent().contains("接收成功")) {
                    return RpcResult.error(auditResult.getContent());
                }
                // 更新装载单信息为已审核
                suNingFPLoadService.updateAuditStatus(suNingFPLoadInfoDTO.getId(), 20);
            } else {
                return RpcResult.error("审核失败：" + body);
            }
            return RpcResult.success("审核通过");
        } else {
            return RpcResult.error("网络请求失败");
        }

    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/exportLoadExcelByDownLoadCenter", desc = "苏宁装载单导出")
    public RpcResult<String> exportLoadExcelByDownLoadCenter(SuNingFPLoadSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.LOAD_ORDER_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/exportLoadDetailExcelByDownLoadCenter", desc = "苏宁装载单明细导出")
    public RpcResult<String> exportLoadDetailExcelByDownLoadCenter(SuNingFPLoadDetailSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.LOAD_ORDER_DETAIL_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/suNingFPLoad/generateExportOrder", desc = "装载单生成出库单")
    public RpcResult<String> generateExportOrder(IdParam idParam) {

        return RpcResult.success("生成出库单成功");
    }

}
