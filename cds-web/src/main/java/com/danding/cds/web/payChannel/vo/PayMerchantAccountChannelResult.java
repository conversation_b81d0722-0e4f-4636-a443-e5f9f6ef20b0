package com.danding.cds.web.payChannel.vo;


import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.rmi.registry.Registry;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 11:41
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantAccountChannelResult {

    @ApiModelProperty(value = "id")
    private Long id;
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码")
    private String merchantSn;

    /**
     * 渠道
     */
    @ApiModelProperty(value = "渠道")
    private String channel;

    /**
     * 收款账号
     */
    @ApiModelProperty(value = "收款账号")
    private String recpAccount;

    /**
     * 收款企业编号
     */
    @ApiModelProperty(value = "收款企业编号")
    private String recpCode;

    /**
     * 收款企业名称
     */
    @ApiModelProperty(value = "收款企业名称")
    private String recpName;

    /**
     * 验签令牌Json字符串，根据渠道由不同类转化而来
     */
//    @ApiModelProperty(value = "令牌")
//    private String tokenJson;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String appId;
    private String key;
    private String partner;
}
