package com.danding.cds.web.businessCenter.entity.vo;

import lombok.Data;

/**
 * 个人工作台 ReqVO
 */
@Data
public class PersonWorkbenchReqVO {

    //之后参数多了，可以包个对象，用对象去获取查
    /**
     * 清关业务待处理 - 区内企业
     */
    private String invBusinessAreaCompanyId;

    /**
     * 保税售后待处理 - 区内企业
     */
    private String afterSalesAreaCompanyId;

    /**
     * 备案待处理 - 仓库
     */
    private String goodsRecordWareHouseCode;
}
