package com.danding.cds.web.handoverOrder.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.handoverOrder.api.dto.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;

public interface HandoverOrderRpc {

    RpcResult getWarehouseInfo();

    RpcResult handoverOrderSyn(HandoverOrderSubmit submit) throws ArgsErrorException;

    RpcResult<ListVO<HandoverOrderVO>> paging(HandoverOrderParam param);

    RpcResult outboundOrderPreview(HandoverWritingReport report) throws ArgsErrorException;

    RpcResult saveExportOrder(HandoverOrderParam param);

    RpcResult verifyExpressCode(HandoverOrderParam param);

    RpcResult<String> excelExport(HandoverOrderParam param);

    RpcResult<HandoverStatusCountResult> getHandoverStatusCount(HandoverOrderParam param);

    RpcResult<String> packageExcelExport(HandoverOrderParam param);

    RpcResult<String> refresh(IdParam idParam);

}
