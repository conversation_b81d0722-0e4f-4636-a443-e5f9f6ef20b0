package com.danding.cds.web.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.download.api.vo.JdGoodsRecordNewPopOfflineExcelVO;
import com.danding.cds.item.api.dto.result.JdNewPopOfflineExcelImportResult;
import com.danding.cds.item.api.dto.submit.JdGoodsRecordNewPopOfflineImportDTO;
import com.danding.cds.item.api.service.JdGoodsRecordService;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description:
 * @date 2024/12/18 14:33
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_JD_NEW_POP_OFFLINE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E4%BA%AC%E4%B8%9C%E5%A4%87%E6%A1%88%E5%88%B7%E6%95%B0%E6%8D%AE%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "京东新POP", classes = JdGoodsRecordNewPopOfflineExcelVO.class),})
public class JdGoodsRecordNewPopOfflineImportsHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        // 引入spring的bean,用于业务处理
        JdGoodsRecordService taskService = this.getBean(JdGoodsRecordService.class);
        JdServProviderService jdServProviderService = this.getBean(JdServProviderService.class);

        List<JdGoodsRecordNewPopOfflineExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("京东新POP")) {
                list = group.getDataList(JdGoodsRecordNewPopOfflineExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        int index = 1;
        Map<String, List<JdGoodsRecordNewPopOfflineExcelVO>> map = list.stream().collect(Collectors.groupingBy(JdGoodsRecordNewPopOfflineExcelVO::getJdRecordNo));

        List<JdGoodsRecordNewPopOfflineExcelVO> delList = new ArrayList<>();
        for (JdGoodsRecordNewPopOfflineExcelVO excelVO : list) {
            List<JdGoodsRecordNewPopOfflineExcelVO> jdGoodsRecordExcelVOS = map.get(excelVO.getJdRecordNo());
            if (jdGoodsRecordExcelVOS.size() > 1) {
                this.callbackData(false, index, "Excel中包含重复数据", excelVO);
                delList.add(excelVO);
                index++;
                log.info("参数校验不通过 error={} 导入数据={}", "Excel中包含重复数据", excelVO);
                continue;
            }
            String check = this.check(excelVO);
            if (StrUtil.isNotBlank(check)) {
                this.callbackData(false, index, check, excelVO);
                delList.add(excelVO);
                index++;
                log.info("参数校验不通过 error={} 导入数据={}", check, excelVO);
            }
        }
        list.removeAll(delList);
        List<JdServProviderDTO> jdServProviderDTOS = jdServProviderService.listAll();
        Map<String, JdServProviderDTO> jdServProviderDTOMap = jdServProviderDTOS.stream().collect(Collectors.toMap(JdServProviderDTO::getCode, Function.identity()));
        List<JdGoodsRecordNewPopOfflineImportDTO> importDTOList = list.stream().map(i -> {
            JdGoodsRecordNewPopOfflineImportDTO dto = ConvertUtil.beanConvert(i, JdGoodsRecordNewPopOfflineImportDTO.class);
            JdServProviderDTO jdServProviderDTO = jdServProviderDTOMap.get(i.getCcProvider());
            if (Objects.nonNull(jdServProviderDTO)) {
                dto.setServProviderId(jdServProviderDTO.getId());
                dto.setProviderId(jdServProviderDTO.getCode());
            }
            return dto;
        }).collect(Collectors.toList());
        List<JdNewPopOfflineExcelImportResult> results = taskService.importJdNewPopOfflineExcel(importDTOList);
        log.info("导入后的返回结果数据{}", results);
        for (JdNewPopOfflineExcelImportResult result : results) {
            JdGoodsRecordNewPopOfflineExcelVO vo = new JdGoodsRecordNewPopOfflineExcelVO();
            JdGoodsRecordNewPopOfflineImportDTO data = result.getData();
            BeanUtil.copyProperties(data, vo);
            vo.setCcProvider(data.getProviderId());
            this.callbackData(result.isFlag(), index, result.getReason(), vo);
            index++;
        }
    }

    private String check(JdGoodsRecordNewPopOfflineExcelVO excelVO) {
        if (Objects.isNull(excelVO)) {
            return "读取对象为空";
        }
        if (Objects.isNull(excelVO.getJdRecordNo())) {
            return "京东备案号为空";
        }
        if (Objects.isNull(excelVO.getEmg())) {
            return "商品编码为空";
        }
        if (Objects.isNull(excelVO.getItemCode())) {
            return "商品货号为空";
        }
        if (Objects.isNull(excelVO.getCcProvider())) {
            return "服务商为空";
        }
        return null;
    }
}
