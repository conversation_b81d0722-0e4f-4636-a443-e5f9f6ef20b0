package com.danding.cds.web.handler;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.excel.EasyExcel;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.park.client.core.imports.bean.*;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import javax.validation.Validator;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/3/29 15:28
 */
@Slf4j
public abstract class ImportsBaseHandlerPlus extends ImportsBaseHandler {
    public List<ImportsDataGroup> readData(Integer headerRowIndex, Integer startRowIndex, Integer endRowIndex) {
        ImportsHandlerInfo handlerInfo = this.getHandlerInfo();
        if (handlerInfo == null) {
            return null;
        } else {
            List<ImportsGroup> handlerGroups = handlerInfo.getGroups();
            // 记录总数量
            int total = 0;
            List<ImportsDataGroup> groups = new ArrayList();
            for (ImportsGroup handlerGroup : handlerGroups) {
                String name = handlerGroup.getName();
                Class<?> clazz = ClassUtil.loadClass(handlerGroup.getClassName());
                List<ImportsHeader> headerList = handlerGroup.getHeader();
                ExcelReader reader = ExcelUtil.getReader(this.download(), name);
                if (headerList != null && headerList.size() > 0) {
                    Iterator<ImportsHeader> headerIterator = headerList.iterator();
                    while (headerIterator.hasNext()) {
                        ImportsHeader importsHeader = headerIterator.next();
                        reader.addHeaderAlias(importsHeader.getName(), importsHeader.getKey());
                    }
                }

                ImportsDataGroup importsDataGroup = new ImportsDataGroup();
                importsDataGroup.setName(name);
                importsDataGroup.setDataList(reader.read(headerRowIndex, startRowIndex, endRowIndex, clazz));
                groups.add(importsDataGroup);
                // 累加总数量
                total += importsDataGroup.total();
            }

            this.updateTotal((String) null, total);
            return groups;
        }
    }

    public List<ImportsDataGroup> readData(Integer headerRowIndex, Integer startRowIndex) {
        return this.readData(headerRowIndex, startRowIndex, Integer.MAX_VALUE);
    }

    public List<ImportsDataGroup> readDataBySheetIdx(Integer headerRowIndex, Integer startRowIndex, Integer sheetIdx) {
        log.info("readDataAsyncBySheetIdx headRowIndex={}, startRowIndex={}, sheetIdx={}", headerRowIndex, startRowIndex, sheetIdx);
        ImportsHandlerInfo handlerInfo = this.getHandlerInfo();
        if (handlerInfo == null) {
            return null;
        } else {
            List<ImportsGroup> handlerGroups = handlerInfo.getGroups();
            // 记录总数量
            int total = 0;
            List<ImportsDataGroup> groups = new ArrayList();
            for (ImportsGroup handlerGroup : handlerGroups) {
                Class<?> clazz = ClassUtil.loadClass(handlerGroup.getClassName());
                log.info("读取excel sheetIdx={}", sheetIdx);
                List<?> list = EasyExcel.read(this.download())
                        .headRowNumber(1).head(clazz).sheet(sheetIdx).doReadSync();
                ImportsDataGroup importsDataGroup = new ImportsDataGroup();
                importsDataGroup.setName("sheet1");
                log.info("sheetIdx:{}, excel文件读取完毕", sheetIdx);
                importsDataGroup.setDataList(list);
                groups.add(importsDataGroup);
                // 累加总数量
                total += importsDataGroup.total();
            }

            this.updateTotal((String) null, total);
            return groups;
        }
    }

    public <T> String doValidator(T t) {
        Validator validator = SpringUtil.getBean("validator");
        String result = ValidatorUtils.doValidator(validator, t);
        return result;
    }

    public String getOperator() {
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);
        UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
        return userRpcResult == null ? "SYSTEM" : userRpcResult.getUserName();
    }
}
