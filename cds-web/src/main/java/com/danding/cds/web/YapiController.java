package com.danding.cds.web;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.logistics.api.common.response.Response;
import com.github.kevinsawicki.http.HttpRequest;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RefreshScope
public class YapiController {
    @Value("${yapi.token:default}")
    private String token;

    public YapiController() {
    }

    @RequestMapping({"/swagger/yapi"})
    public Response<String> swagger(HttpServletRequest request) {
        if (this.token.equals("default")) {
            return new Response<>(-1,"yapi token未配置");
        } else {
            HttpRequest httpRequest = HttpRequest.get("http://localhost:" + request.getServerPort() + "/v2/api-docs");
            if (httpRequest.ok()) {
                Map map = Maps.newHashMap();
                map.put("type", "swagger");
                map.put("merge", "good");
                map.put("token", this.token);
                map.put("json", httpRequest.body());
                httpRequest = HttpRequest.post("http://yapi.yang800.cn/api/open/import_data").form(map);
                if (httpRequest.ok()) {
                    return new Response<>(httpRequest.body());
                }
            }
            return new Response<>(-1,"yapi请求失败");
        }
    }

    public static void main(String[] args) {
        String origMenu = "{\"code\":0,\"data\":[{\"id\":1600933651051703,\"icon\":\"&#xe89f;\",\"title\":\"清关业务\",\"url\":\"\",\"list\":[{\"id\":160248202011678,\"title\":\"申报总览\",\"url\":\"/declaration-manage?config_id=1602480232145456&page_title=申报总览\"},{\"id\":1601281823967681,\"title\":\"清单申报\",\"url\":\"/config-center?config_id=1601281273842701&page_title=清单申报\"},{\"id\":1600936652822856,\"title\":\"订单申报\",\"url\":\"/order-declaration?config_id=1600936159723239&page_title=订单申报\"},{\"id\":1600933696038695,\"title\":\"支付申报\",\"url\":\"/payment-declaration?config_id=1600932534062563&page_title=支付申报\"},{\"id\":1600933683025943,\"title\":\"运单申报\",\"url\":\"/waybill-declaration?config_id=1600924916349869&page_title=运单申报\"}]},{\"id\":1602733315352853,\"icon\":\"&#xe89f;\",\"title\":\"保税售后\",\"url\":\"\",\"list\":[{\"id\":1602733366607618,\"title\":\"撤单管理\",\"url\":\"/cancel-lations-manage?config_id=1601376450428833&page_title=撤单管理\"},{\"id\":1602733407560600,\"title\":\"退货管理\",\"url\":\"/goods-return-manage?config_id=1601377399277658&page_title=退货管理\"}]},{\"id\":1600933642607469,\"icon\":\"&#xe89f;\",\"title\":\"跨境进口\",\"url\":\"\",\"list\":[{\"id\":1601376016924886,\"title\":\"清关单管理\",\"url\":\"/customs-clearance-manage?config_id=1601379213708615&page_title=清关单管理\"},{\"id\":****************,\"title\":\"申报出库单\",\"url\":\"/declare-outbound-manage?config_id=****************&page_title=申报出库单\"},{\"id\":****************,\"title\":\"核注单管理\",\"url\":\"/config-center?config_id=****************&page_title=核注单管理\"},{\"id\":****************,\"title\":\"核放单管理\",\"url\":\"/config-center?config_id=****************&page_title=核放单管理\"},{\"id\":****************,\"title\":\"企业税金管理\",\"url\":\"/tax-account-manage?config_id=****************&page_title=企业税金管理\"},{\"id\":****************,\"title\":\"租户税金管理\",\"url\":\"/tenant-taxes-manage?config_id=****************&page_title=租户税金管理\"}]},{\"id\":****************,\"icon\":\"&#xe89f;\",\"title\":\"准入管理\",\"url\":\"\",\"list\":[{\"id\":****************,\"title\":\"账册管理\",\"url\":\"/books-manage?config_id=****************&page_title=账册管理\"},{\"id\":****************,\"title\":\"商品备案\",\"url\":\"/goods-record?config_id=****************&page_title=商品备案\"},{\"id\":****************,\"title\":\"账册库存\",\"url\":\"/books-inventory?config_id=****************&page_title=账册库存\"}]},{\"id\":****************,\"icon\":\"&#xe89f;\",\"title\":\"通关设置\",\"url\":\"\",\"list\":[{\"id\":****************,\"title\":\"企业管理\",\"url\":\"/enterprise-manage?config_id=****************&page_title=企业管理\"},{\"id\":****************,\"title\":\"申报路由\",\"url\":\"/declare-route?config_id=1601175816503398&page_title=申报路由\"},{\"id\":1601175530247447,\"title\":\"申报路径\",\"url\":\"/declare-path?config_id=1598418773818618&page_title=申报路径\"},{\"id\":1601176208226540,\"title\":\"支付渠道\",\"url\":\"/payment-channel?config_id=1601176022720613&page_title=支付渠道\"},{\"id\":1601177828871332,\"title\":\"快递管理\",\"url\":\"/express-manage?config_id=1601177775263716&page_title=快递管理\"},{\"id\":1601354250175218,\"title\":\"商户管理\",\"url\":\"/merchants-manage?config_id=1601349523880856&page_title=商户管理\"},{\"id\":1601349317237435,\"title\":\"HS编码\",\"url\":\"/hs-code?config_id=1601349028900457&page_title=HS编码\"}]},{\"id\":1601357567244858,\"icon\":\"&#xe89f;\",\"title\":\"系统运维\",\"url\":\"\",\"list\":[{\"id\":1601357738309846,\"title\":\"消息订阅管理\",\"url\":\"/config-center?config_id=1601357585570472&page_title=消息订阅管理\"},{\"id\":160135843590452,\"title\":\"消息执行管理\",\"url\":\"/config-center?config_id=1601358139739148&page_title=消息执行管理\"}]},{\"id\":1602212839519763,\"icon\":\"&#xe89f;\",\"title\":\"用户管理\",\"url\":\"\",\"list\":[{\"id\":****************,\"title\":\"账户管理\",\"url\":\"/admin-user-center?config_id=****************&page_title=账号管理-小二&systemCode=CCS_ADMIN\"},{\"id\":****************,\"title\":\"角色管理\",\"url\":\"/admin-roles-manage?config_id=***************&page_title=角色管理-小二&systemCode=CCS_ADMIN\"}]},{\"id\":***************,\"icon\":\"&#xe89f;\",\"title\":\"企业用户管理\",\"url\":\"\",\"list\":[{\"id\":***************,\"title\":\"账号管理\",\"url\":\"/account-manage?config_id=****************&page_title=账号管理\"},{\"id\":****************,\"title\":\"审核管理\",\"url\":\"/audit-manage?config_id=****************&page_title=审核管理\"}]},{\"id\":****************,\"icon\":\"&#xe89f;\",\"title\":\"权限管理\",\"url\":\"\",\"list\":[]}]}";
        List<Map<String,Object>> childrenList = new ArrayList<>();
        for (Object o : JSON.parseArray(JSON.parseObject(origMenu).getString("data"))) {
            JSONObject data = (JSONObject) o;
            Map<String,Object> children = new HashMap<>();
            List<Map<String,Object>> list = new ArrayList<>();
            for (Object b : JSON.parseArray(data.getString("list"))){
                JSONObject i = (JSONObject) b;
                Map<String,Object> il = new HashMap<>();
                il.put("title",i.getString("title"));
                il.put("code",i.getString("id"));
                il.put("path",i.getString("url"));
                il.put("icon",i.getString("icon"));
                il.put("apis",new ArrayList<>());
                list.add(il);
            }
            children.put("title",data.getString("title"));
            children.put("code",data.getString("id"));
            children.put("path",data.getString("url"));
            children.put("icon",data.getString("icon"));
            children.put("apis",new ArrayList<>());
            children.put("children",list);
            childrenList.add(children);
        }
        System.out.println(JSON.toJSONString(childrenList));
    }
}