package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ClassUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.OverdueFeedBackExcelVO;
import com.danding.cds.v2.api.InventoryOrderOverdueService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsGroup;
import com.danding.park.client.core.imports.bean.ImportsHandlerInfo;
import com.danding.park.client.core.imports.bean.ImportsHeader;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;


/**
 * <AUTHOR>
 * @description: toB清关超时批量反馈
 * @date 2023/2/22 19:15
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_INVENTORY_ORDER_OVERDUE_FEEDBACK", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%89%B9%E9%87%8F%E5%8F%8D%E9%A6%88%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "toB超时", classes = OverdueFeedBackExcelVO.class),})
public class InventoryOrderOverdueFeedBackImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<OverdueFeedBackExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("toB超时")) {
                list = group.getDataList(OverdueFeedBackExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        InventoryOrderOverdueService inventoryOrderOverdueService = this.getBean(InventoryOrderOverdueService.class);
        int index = 1;
        for (OverdueFeedBackExcelVO excelVO : list) {
            ImportResultResVo resultResVo = new ImportResultResVo();
            String result = this.doValidator(excelVO);
            if (StringUtils.hasText(result)) {
                resultResVo.setFlag(false);
                resultResVo.setReason(result);
            } else {
                resultResVo.setFlag(true);
                try {
                    inventoryOrderOverdueService.feedbackByExceptionId(excelVO.getExceptionId(), excelVO.getOverdueReason());
                } catch (ArgsInvalidException exception) {
                    log.error("异常反馈失败:{}", exception.getMessage(), exception);
                    resultResVo.setFlag(false);
                    resultResVo.setReason(exception.getMessage());
                } catch (Exception e) {
                    log.error("异常反馈失败:{}", e.getMessage(), e);
                    resultResVo.setFlag(false);
                    resultResVo.setReason("异常反馈失败");
                }
                log.info("异常id:{} 导入后的返回结果数据{}", excelVO.getExceptionId(), JSON.toJSONString(resultResVo));
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }

    public List<ImportsDataGroup> readData() {
        ImportsHandlerInfo handlerInfo = this.getHandlerInfo();
        if (handlerInfo == null) {
            return null;
        } else {
            List<ImportsGroup> handlerGroups = handlerInfo.getGroups();
            int total = 0;
            List<ImportsDataGroup> groups = new ArrayList();

            ImportsDataGroup importsDataGroup;
            for (Iterator var5 = handlerGroups.iterator(); var5.hasNext(); total += importsDataGroup.total()) {
                ImportsGroup handlerGroup = (ImportsGroup) var5.next();
                String name = handlerGroup.getName();
                Class<?> clazz = ClassUtil.loadClass(handlerGroup.getClassName());
                List<ImportsHeader> headerList = handlerGroup.getHeader();
                ExcelReader reader = ExcelUtil.getReader(this.download(), name);
                if (headerList != null && headerList.size() > 0) {
                    Iterator var11 = headerList.iterator();

                    while (var11.hasNext()) {
                        ImportsHeader importsHeader = (ImportsHeader) var11.next();
                        reader.addHeaderAlias(importsHeader.getName(), importsHeader.getKey());
                    }
                }

                importsDataGroup = new ImportsDataGroup();
                importsDataGroup.setName(name);
                importsDataGroup.setDataList(reader.readAll(clazz));
                groups.add(importsDataGroup);
            }

            this.updateTotal((String) null, total);
            return groups;
        }
    }
}
