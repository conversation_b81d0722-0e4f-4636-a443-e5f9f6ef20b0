package com.danding.cds.web.v2.bean.result;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description: 模板管理查询结果
 * @date 2025/7/31
 */
@Data
@ApiModel("模板管理查询结果")
public class TemplateManageResult implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("主键ID")
    private Long id;

    @ApiModelProperty("模板名称")
    private String templateName;

    @ApiModelProperty("用途")
    private String purpose;

    @ApiModelProperty("用途名称")
    private String purposeName;

    @ApiModelProperty("口岸（多个以逗号分隔）")
    private String ports;

    @ApiModelProperty("口岸名称（多个以逗号分隔）")
    private String portNames;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("文件URL（OSS路径）")
    private String fileUrl;

    @ApiModelProperty("文件名")
    private String fileName;

    @ApiModelProperty("创建者ID")
    private Long creatorId;

    @ApiModelProperty("创建者姓名")
    private String creatorName;

    @ApiModelProperty("上传时间")
    private Date uploadTime;

    @ApiModelProperty("创建时间")
    private Date createTime;

    @ApiModelProperty("更新时间")
    private Date updateTime;
}
