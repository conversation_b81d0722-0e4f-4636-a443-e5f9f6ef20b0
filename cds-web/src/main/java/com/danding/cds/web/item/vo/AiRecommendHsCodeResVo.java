package com.danding.cds.web.item.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class AiRecommendHsCodeResVo implements Serializable {

    /**
     * 任务状态 1-查询中 2-完成 -1-失败
     */
    private String taskStatus;

    private List<AiRecommendHsCodeVO> aiRecommendHsCodeVOList;

    @Data
    public static class AiRecommendHsCodeVO {
        /**
         * id
         */
        private Long id;

        /**
         * Ai推荐HS编码
         */
        private String recommendHsCode;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 所属章节
         */
        private String goodsChapter;

        /**
         * 推荐理由
         */
        private String recommendReason;

        /**
         * 选用标记 0-未选 1-已选
         */
        private Integer chooseFlag;

        /**
         * 系统内是否存在 1 存在 0 不存在
         */
        private Integer existFlag;
    }
}
