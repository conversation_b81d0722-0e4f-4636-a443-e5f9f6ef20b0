package com.danding.cds.web.payChannel.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 11:42
 * @Description:
 */
@Data
public class PayMerchantAccountChannelExcelVO {
    @Excel(name  = "商户名称")
    private String merchantName;

    /**
     * 商户编码
     */
    @Excel(name = "商户编码")
    private String merchantSn;

    /**
     * 渠道
     */
    @Excel(name = "渠道")
    private String channel;

    /**
     * 收款账号
     */
    @Excel(name = "收款账号")
    private String recpAccount;

    /**
     * 收款企业编号
     */
    @Excel(name = "收款企业编号")
    private String recpCode;



    @Excel(name = "创建时间", format = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
