package com.danding.cds.web.v2.controller;

import com.danding.cds.template.api.dto.TemplateManageSearch;
import com.danding.cds.template.api.dto.TemplateManageSubmit;
import com.danding.cds.web.v2.api.TemplateManageWebRpc;
import com.danding.cds.web.v2.bean.result.TemplateManageResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @description: 模板管理控制器
 * @date 2025/7/31
 */
@Slf4j
@RestController
@RequestMapping("/template")
@Api(tags = "模板管理")
public class TemplateManageController {

    @DubboReference
    private TemplateManageWebRpc templateManageWebRpc;

    @ApiOperation(value = "分页查询模板")
    @PostMapping("/paging")
    public ListVO<TemplateManageResult> paging(@RequestBody TemplateManageSearch search) {
        return templateManageWebRpc.paging(search).getData();
    }

    @ApiOperation(value = "上传模板")
    @PostMapping("/upload")
    public Long upload(@RequestBody TemplateManageSubmit submit) {
        return templateManageWebRpc.upload(submit).getData();
    }

    @ApiOperation(value = "编辑模板")
    @PostMapping("/edit")
    public Long edit(@RequestBody TemplateManageSubmit submit) {
        return templateManageWebRpc.edit(submit).getData();
    }

    @ApiOperation(value = "根据ID查询模板详情")
    @GetMapping("/getById")
    public TemplateManageResult getById(@RequestParam Long id) {
        if (id == null) {
            throw new ArgsErrorException("模板ID不能为空");
        }
        com.danding.cds.common.model.IdParam idParam = new com.danding.cds.common.model.IdParam();
        idParam.setId(id);
        return templateManageWebRpc.getById(idParam).getData();
    }

    @ApiOperation(value = "删除模板")
    @PostMapping("/delete")
    public Boolean delete(@RequestParam Long id) {
        if (id == null) {
            throw new ArgsErrorException("模板ID不能为空");
        }
        com.danding.cds.common.model.IdParam idParam = new com.danding.cds.common.model.IdParam();
        idParam.setId(id);
        return templateManageWebRpc.delete(idParam).getData();
    }
}
