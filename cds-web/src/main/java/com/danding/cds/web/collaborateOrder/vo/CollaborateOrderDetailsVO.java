package com.danding.cds.web.collaborateOrder.vo;

import com.danding.cds.collaborateorder.api.dto.CollaborateOrderResDetailsDTO;
import com.danding.cds.v2.bean.dto.InventoryFlowStateDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/31
 */
@Data
public class CollaborateOrderDetailsVO {

    /**
     * 协同单子单
     */
    List<CollaborateOrderResDetailsDTO> detailsVOS;


    @ApiModelProperty("实体仓名称")
    private String entityWarehouseName;

    @ApiModelProperty("货主名称")
    private String ownerName;

    @ApiModelProperty("清关单号")
    private String inveCustomsSn;

    @ApiModelProperty("业务类型")
    private String inveBusinessTypeDesc;
    private String inveBusinessType;


    @ApiModelProperty("区内账册编号")
    private String areaBookNo;

    @ApiModelProperty("进出标志")
    private String inOrOutFlag;

    @ApiModelProperty("运输方式")
    private String transportModeName;

    @ApiModelProperty("核注清单编号")
    private String refHzInveNo;

    @ApiModelProperty("启运国名称")
    private String shipmentCountryName;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 清关状态时间
     */
    private Date statusTime;

    /**
     * 提取号
     */
    private String pickUpNo;


    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    @ApiModelProperty("预计到港时间")
    private String expectedToPortTime;

    /**
     * 实际到港日期
     */
    private String actualArrivalDate;


    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    @ApiModelProperty("备注")
    private String remark;

    /**
     * 业务单号
     */
    private String channelBusinessSn;

    /**
     * 理货报告号
     */
    private String tallyOrderNo;
    /**
     * 实际理货数量
     */
    private Integer actualTallyQty;

    /**
     * 仓库托数
     */
    private Integer warehouseFightQty;

    /**
     * 实际到库时间
     */
    private Date warehouseTime;


    /**
     * 预计到货毛重
     */
    private BigDecimal estimatedWeight;

    /**
     * 预计到货净重
     */
    private BigDecimal estimatedNewWeight;

    /**
     * 预计到货数量
     */
    private BigDecimal estimatedQty;

    /**
     * 申报企业
     */
    private Long customsEntryCompany;
    private String customsEntryCompanyName;

    /**
     * 托数
     */
    private Integer fightQty;

    /**
     * 运输费
     */
    private BigDecimal shippingFee;

    /**
     * 转入实体仓
     */
    private String entityInWarehouseName;

    /**
     * 转出实体仓
     */
    private String entityOutWarehouseName;


    /**
     * 清关单id
     */
    private String inveOrderId;

    /**
     * 理货完成时间
     */
    private String tallyFinishTime;

    /**
     * 清关完成时间
     */
    private Date customsFinishTime;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 状态机
     */
    public InventoryFlowStateDTO stateRes;

    /**
     * 关联清关单号
     */
    private String associatedInveCustomsSn;
}
