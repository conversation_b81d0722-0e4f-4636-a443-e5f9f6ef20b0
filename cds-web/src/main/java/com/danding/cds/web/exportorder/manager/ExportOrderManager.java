package com.danding.cds.web.exportorder.manager;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsInventoryRpc;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryCalloffDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemDTO;
import com.danding.cds.customs.inventory.api.dto.CustomsInventoryItemExtra;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffTypeEnum;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.enums.ExportOrderStatus;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailDTO;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.cds.handoverOrder.api.service.HandoverOrderService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.route.api.service.RouteService;
import com.danding.cds.v2.bean.dto.EntityWarehouseDTO;
import com.danding.cds.v2.bean.enums.EntityWarehouseTagEnums;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
@RefreshScope
public class ExportOrderManager {

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private CustomsInventoryRpc customsInventoryRpc;

    @DubboReference
    private RouteService routeService;

    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private HandoverOrderService handoverOrderService;
    @DubboReference
    private HandoverOrderDetailService handoverOrderDetailService;
    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @Value("${export.order.item.check.group.size:500}")
    private Integer exportOrderCheckItemGroupSize;

    public ExportItemWritingReport writing(Long id, List<ExportItemRecord> importList, Boolean save, ExportOrderSubmit submit) throws ArgsErrorException {
        log.info("[op:ExportOrderManager-writing] step 1 submit={}", JSON.toJSONString(submit));
        // Step::分析已有数据
        List<Long> expressIdList;
        List<Long> customsBookIdList = new ArrayList<>();
        ExportOrderDTO exportOrderDTO = new ExportOrderDTO();
        if (Objects.nonNull(id)) {
            exportOrderDTO = exportOrderService.findById(id);
            log.info("[op:ExportOrderManager-writing] step 2 id={} exportDTO={}", id, JSON.toJSONString(exportOrderDTO));
            if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
                throw new ArgsErrorException("出库单当前状态无法导入和录入运单号");
            }
            expressIdList = JSON.parseArray(exportOrderDTO.getExpressList(), Long.class);
        } else {
//            exportOrderDTO.setAccountBookId(submit.getAccountBookId());
            exportOrderDTO.setEntityWarehouseCode(submit.getEntityWarehouseCode());
            expressIdList = submit.getExpressIdList();
        }
        List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findDTOByWmsCode(exportOrderDTO.getEntityWarehouseCode());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在");
        }
        entityWarehouseDTOList = entityWarehouseDTOList.stream()
                .filter(entityWarehouseDTO -> entityWarehouseDTO.getEnable() == 1
                        && EntityWarehouseTagEnums.containsAny(entityWarehouseDTO.getWarehouseTag(), EntityWarehouseTagEnums.COMMON_BOOKS_QG, EntityWarehouseTagEnums.SPECIAL_BOOKS_QG))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(entityWarehouseDTOList)) {
            throw new ArgsInvalidException("实体仓编码【" + exportOrderDTO.getEntityWarehouseCode() + "】不存在可用清关账册");
        }
        customsBookIdList = entityWarehouseDTOList.stream()
                .map(EntityWarehouseDTO::getCustomsBookId)
                .distinct()
                .collect(Collectors.toList());
        // Step::补全出区订单信息
        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = this.handleRecordFillInfo(importList);
        // 账册ID，禁用需要过滤
        List<Long> accountBookIdList = customsInventoryDTOMap.values().stream()
                .map(CustomsInventoryDTO::getAccountBookId)
                .distinct()
                .collect(Collectors.toList());
        List<CustomsBookDTO> accountBookList = customsBookService.listBooksByIds(accountBookIdList);
        List<Long> orderIdList = customsInventoryDTOMap.values().stream().map(CustomsInventoryDTO::getOrderId)
                .distinct()
                .collect(Collectors.toList());
        // Step::数据校验及预录入
        // Step::预录入新增列表
        List<ExportItemRecord> addSuccessList = new ArrayList<>();
        List<ExportItemRecord> addFailList = new ArrayList<>();
        List<String> newAddList = new ArrayList<>();
        log.info("[op:ExportOrderManager-writing] step 3 id={} customsInventoryDTOMap={}", id, JSON.toJSONString(customsInventoryDTOMap));
//        Boolean skipCheckFlag = handoverOrderService.skipCheckHandoverByBookId(exportOrderDTO.getAccountBookId());
        Boolean skipCheckFlag = customsBookIdList.stream().allMatch(bookId -> handoverOrderService.skipCheckHandoverByBookId(bookId));
        List<String> mailNoList = new ArrayList<>();
        List<String> customsInventorySnList = new ArrayList<>();
        importList.forEach(record -> {
            mailNoList.add(record.getMailNo());
            customsInventorySnList.add(record.getCustomsInventorySn());
        });
        Map<String, HandoverOrderDetailDTO> mailNoHandoverMap = new HashMap<>();
        if (!skipCheckFlag) {
            List<HandoverOrderDetailDTO> handoverOrderDetailDTOS = handoverOrderDetailService.getHandoverOrderDetailByMailNo(mailNoList);
            mailNoHandoverMap = handoverOrderDetailDTOS.stream().collect(Collectors.toMap(HandoverOrderDetailDTO::getWayBillSn, Function.identity(), (v1, v2) -> v1));
        }
        Map<String, ExportItemDTO> invetSnExportItemMap = exportOrderService.getInvetSnItemMapByCustomInventoryList(customsInventorySnList);
        List<CustomsInventoryCalloffDTO> calloffDTOList = customsInventoryCalloffService.findListByOrderIdList(orderIdList);
        Map<Long, CustomsInventoryCalloffDTO> orderIdCalloffMap = calloffDTOList.stream().collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderId, Function.identity(), (v1, v2) -> v1));
        Integer successIdx = 0;
        Integer failIdx = 0;
        for (ExportItemRecord record : importList) {
            String errorMsg = "";
            if (StringUtils.isEmpty(record.getMailNo())) {
                errorMsg += "运单号为空;";
            } else if (StringUtils.isEmpty(record.getCustomsInventorySn()) && !StringUtils.isEmpty(record.getMailNo())) {
                errorMsg += "运单号系统不存在;";
            } else if (StringUtils.isEmpty(record.getExpressName())) {
                errorMsg += "快递公司为空;";
            } else {
                CustomsInventoryDTO customsInventoryDTO = record.getCustomsInventoryDTO();
                log.info("[op:ExportOrderManager-writing] step 3-1 id={} customsInventoryDTO={}", id, JSON.toJSONString(customsInventoryDTO));
                if (Objects.isNull(customsInventoryDTO)) {
                    errorMsg += "清单未找到;";
                } else {
                    if (LongUtil.isNone(record.getExpressId())) {
                        errorMsg += "快递公司填写不正确;";
                    } else if (!expressIdList.contains(record.getExpressId())) {
                        errorMsg += "当前申报出库单不支持该快递公司;";
                    } else if (StringUtils.isEmpty(record.getCustomsInventorySn())) {
                        errorMsg += "清单未找到;";
                    } else if (!customsInventoryDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue())) {
                        errorMsg += "清单未放行;";
                    } else if (newAddList.contains(record.getCustomsInventorySn())) {
                        errorMsg += "重复的运单号;"; // 重复的运单才会找到重复的清单
                    } else if (invetSnExportItemMap.get(customsInventoryDTO.getSn()) != null) {
                        errorMsg += "该运单申报记录已存在;";
                    } else if (!customsBookIdList.contains(customsInventoryDTO.getAccountBookId())) {
                        errorMsg += "对应放行清单的账册与申报出库单的账册不一致;";
                    } else if (StringUtils.isBlank(customsInventoryDTO.getInventoryNo())) {
                        errorMsg += "对应清单编号为空;";
                    } else if (!accountBookEffective(customsInventoryDTO.getAccountBookId(), accountBookList)) {
                        errorMsg += "账册无效";
                    }
                    if (!skipCheckFlag) {
                        if (mailNoHandoverMap.containsKey(record.getMailNo())) {
                            HandoverOrderDetailDTO handoverOrderDetailDTO = mailNoHandoverMap.get(record.getMailNo());
                            if (Objects.nonNull(handoverOrderDetailDTO.getOutboundOrder())) {
                                errorMsg += "该运单已关联出库单;";
                            }
                        } else {
                            errorMsg += "该运单未关联交接单;";
                        }
                    }
                    Long orderId = customsInventoryDTO.getOrderId();
                    if (orderIdCalloffMap.containsKey(orderId)) {
                        CustomsInventoryCalloffDTO customsInventoryCalloffDTO = orderIdCalloffMap.get(orderId);
                        if (Objects.nonNull(customsInventoryCalloffDTO)) {
                            if (Objects.equals(customsInventoryCalloffDTO.getCalloffType(), InventoryCalloffTypeEnum.CALLOFF_CACEL.getCode())
                                    || Objects.equals(customsInventoryCalloffDTO.getCalloffType(), InventoryCalloffTypeEnum.INTERCEPTION_DECLARE.getCode())) {
                                String calloffStatus = customsInventoryCalloffDTO.getCalloffStatus();
                                if (Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_WAITING.getCode()) ||
                                        Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_ING.getCode()) ||
                                        Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode()) ||
                                        Objects.equals(calloffStatus, InventoryCalloffStatusEnum.CALLOFF_FAIL.getCode())) {
                                    errorMsg += "存在撤单，无法汇单;";
                                }
                            }
                        }
                    }
                    log.info("[op:ExportOrderManager-writing] step 3-2, customsInventoryDTO={}", JSON.toJSONString(customsInventoryDTO));
                }
            }
            if (StringUtils.isEmpty(errorMsg)) {
                record.setIdx(++successIdx);
                newAddList.add(record.getCustomsInventorySn());
                addSuccessList.add(record);
            } else {
                record.setIdx(++failIdx);
                record.setErrorMsg(errorMsg);
                addFailList.add(record);
            }
        }
        ExportItemWritingReport report = new ExportItemWritingReport();
        report.setTotalCount(importList.size());
        report.setSuccessCount(addSuccessList.size());
        report.setFailCount(addFailList.size());
        report.setSuccessRecordList(addSuccessList);
        report.setFailRecordList(addFailList);
        log.info("[op:ExportOrderManager-writing] step 5, report={}", JSON.toJSONString(report));
        return report;
    }

    /**
     * 账册是否有效
     *
     * @param accBookId       账册id
     * @param accountBookList 账册数据列表
     * @return
     */
    private boolean accountBookEffective(Long accBookId, List<CustomsBookDTO> accountBookList) {

        if (accBookId == null || CollectionUtils.isEmpty(accountBookList)) {
            return false;
        }
        return accountBookList.stream().anyMatch(z -> z.getId().equals(accBookId) && Objects.equals(z.getEnable(), 1));
    }


    /**
     * 循环补充exportItem信息，并返回customsInventoryDTO
     *
     * @param exportItemRecordList
     * @return
     */
    private Map<String, CustomsInventoryDTO> handleRecordFillInfo(List<ExportItemRecord> exportItemRecordList) {
        Map<String, ExpressDTO> expressDTOMap = new HashMap<>();
//        Map<String, RouteDTO> declareDTOMap = new HashMap<>();
        List<String> mailNoList = exportItemRecordList.stream().map(ExportItemRecord::getMailNo)
                .filter(StringUtils::isNotEmpty).collect(Collectors.toList());
        Map<String, CustomsInventoryDTO> mailNoInvetDTOMap = this.getInventoryDTOByMailList(mailNoList);
        Map<Long, List<CustomsInventoryItemDTO>> inventoryItemMap = customsInventoryRpc.listItemByInventoryS(new ArrayList<>(mailNoInvetDTOMap.values()));

        Set<String> allUnifiedProductIds = new HashSet<>();
        for (ExportItemRecord record : exportItemRecordList) {
            if (StringUtils.isEmpty(record.getMailNo())) {
                continue;
            }
            ExpressDTO expressDTO = null;
            if (!StringUtils.isEmpty(record.getExpressName())) {
                expressDTO = expressDTOMap.get(record.getExpressName());
                if (expressDTO == null) {
                    expressDTO = expressService.findOneByName(record.getExpressName());
                    expressDTOMap.put(record.getExpressName(), expressDTO);
                }
            }
            if (expressDTO != null) {
                record.setExpressId(expressDTO.getId());
            }
            CustomsInventoryDTO customsInventoryDTO = mailNoInvetDTOMap.get(record.getMailNo());
            if (customsInventoryDTO != null) {
//                try {
//                    if (!StringUtils.isEmpty(customsInventoryDTO.getExtraJson())) {
//                        CustomsInventoryExtra extra = JSON.parseObject(customsInventoryDTO.getExtraJson(), CustomsInventoryExtra.class);
//                        if (!StringUtils.isEmpty(extra.getRouteCode())) {
//                            RouteDTO routeDTO = declareDTOMap.get(extra.getRouteCode());
//                            if (routeDTO == null) {
//                                routeDTO = routeService.findByCode(extra.getRouteCode());
//                                if (routeDTO != null) {
//                                    declareDTOMap.put(extra.getRouteCode(), routeDTO);
//                                }
//                            }
//                            if (routeDTO != null) {
//                                customsInventoryDTO.setDeclareCompanyId(routeDTO.getListDeclareCompanyId());
//                            }
//                        }
//                    }
//                } catch (Exception ex) {
//                    log.error("fillInfo ex={}", ex.getMessage(), ex);
//                }
                List<CustomsInventoryItemDTO> customsInventoryItemDTOS = inventoryItemMap.get(customsInventoryDTO.getId());
                if (!CollectionUtils.isEmpty(customsInventoryItemDTOS)) {
                    List<ExportSkuInfo> skuInfoList = customsInventoryItemDTOS.stream()
                            .map(itemDTO -> {
                                ExportSkuInfo skuInfo = new ExportSkuInfo();
                                skuInfo.setBookItemId(itemDTO.getBookItemId());
                                skuInfo.setCount(itemDTO.getCount());
                                String extraJson = itemDTO.getExtraJson();
                                if (StringUtils.isNotEmpty(extraJson)) {
                                    CustomsInventoryItemExtra customsInventoryItemExtra = JSON.parseObject(extraJson, CustomsInventoryItemExtra.class);
                                    String productId = customsInventoryItemExtra.getProductId();
                                    String unifiedProductId = customsInventoryItemExtra.getUnifiedProductId();
                                    skuInfo.setProductId(productId);
                                    skuInfo.setUnifiedProductId(unifiedProductId);
                                    if (StringUtils.isNotEmpty(unifiedProductId)) {
                                        allUnifiedProductIds.add(unifiedProductId);
                                    }
                                }
                                skuInfo.setItemTag(itemDTO.getItemTag());
                                skuInfo.setGoodsName(itemDTO.getItemName());
                                return skuInfo;
                            }).collect(Collectors.toList());
                    record.setSkuInfoList(skuInfoList);
                }
            }
        }
        Map<String, GoodsRecordDTO> goodsRecordMap = new HashMap<>();
        if (!allUnifiedProductIds.isEmpty()) {
            List<GoodsRecordDTO> goodsRecordDTOS = goodsRecordService.findByProId(new ArrayList<>(allUnifiedProductIds));
            goodsRecordMap = goodsRecordDTOS.stream()
                    .collect(Collectors.toMap(GoodsRecordDTO::getProductId, Function.identity(), (k1, k2) -> k1));
        }
        for (ExportItemRecord record : exportItemRecordList) {
            if (StringUtils.isEmpty(record.getMailNo())) {
                continue;
            }
            CustomsInventoryDTO customsInventoryDTO = mailNoInvetDTOMap.get(record.getMailNo());
            List<ExportSkuInfo> skuInfoList = record.getSkuInfoList();
            if (!CollectionUtils.isEmpty(skuInfoList)) {
                for (ExportSkuInfo skuInfo : skuInfoList) {
                    GoodsRecordDTO goodsRecordDTO = goodsRecordMap.get(skuInfo.getUnifiedProductId());
                    if (goodsRecordDTO != null) {
                        if (goodsRecordDTO.getGrossWeight() != null && skuInfo.getCount() != null) {
                            skuInfo.setGrossWeight(goodsRecordDTO.getGrossWeight().multiply(new BigDecimal(skuInfo.getCount().toString())));
                        }
                        if (goodsRecordDTO.getNetWeight() != null && skuInfo.getCount() != null) {
                            skuInfo.setNetWeight(goodsRecordDTO.getNetWeight().multiply(new BigDecimal(skuInfo.getCount().toString())));
                        }
                    }
                }
                record.setSkuInfoList(skuInfoList);
                record.setGrossWeight(customsInventoryDTO.getGrossWeight() == null ? BigDecimal.ZERO : customsInventoryDTO.getGrossWeight());
                record.setNetWeight(customsInventoryDTO.getNetWeight());
                record.setCustomsInventorySn(customsInventoryDTO.getSn());
                record.setBizId(customsInventoryDTO.getDeclareOrderNo());
                record.setCustomsInventoryDTO(customsInventoryDTO);
            }
        }

        Map<String, CustomsInventoryDTO> customsInventoryDTOMap = new HashMap<>();
        // 单独的循环将 CustomsInventoryDTO 放入 customsInventoryDTOMap
        for (ExportItemRecord record : exportItemRecordList) {
            if (StringUtils.isEmpty(record.getMailNo())) {
                continue;
            }
            CustomsInventoryDTO customsInventoryDTO = mailNoInvetDTOMap.get(record.getMailNo());
            if (customsInventoryDTO != null) {
                customsInventoryDTOMap.put(customsInventoryDTO.getSn(), customsInventoryDTO);
            }
        }
        return customsInventoryDTOMap;
    }

    /**
     * 获取运单 - 清单 map
     * 分步长批量查询
     *
     * @param mailNoList
     * @return
     */
    public Map<String, CustomsInventoryDTO> getInventoryDTOByMailList(List<String> mailNoList) {
        int step = exportOrderCheckItemGroupSize;
        log.info("ExportOrderManager getInventoryDTOByMailList group={}", step);
        // 创建一个线程池，线程数量可以根据实际情况调整
        ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        List<Future<Map<String, CustomsInventoryDTO>>> futures = new ArrayList<>();

        for (int i = 0; i < mailNoList.size(); i += step) {
            int start = i;
            int end = Math.min(i + step, mailNoList.size());
            List<String> subList = mailNoList.subList(start, end);
            // 提交任务到线程池
            futures.add(executorService.submit(() -> {
                List<CustomsInventoryDTO> customsInventoryDTOS = customsInventoryRpc.listByLogistics90Days(subList);
                return customsInventoryDTOS.stream()
                        .collect(Collectors.toMap(CustomsInventoryDTO::getLogisticsNo, Function.identity(), (v1, v2) -> v1));
            }));
        }

        Map<String, CustomsInventoryDTO> result = new HashMap<>();
        for (Future<Map<String, CustomsInventoryDTO>> future : futures) {
            try {
                // 获取每个任务的结果并合并到最终结果中
                result.putAll(future.get());
            } catch (InterruptedException | ExecutionException e) {
                log.error("Error getting result from future", e);
                Thread.currentThread().interrupt();
            }
        }
        // 关闭线程池
        executorService.shutdown();
        return result;
    }

}