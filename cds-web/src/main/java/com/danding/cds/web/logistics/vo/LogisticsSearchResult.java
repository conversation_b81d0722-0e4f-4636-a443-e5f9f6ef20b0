package com.danding.cds.web.logistics.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class LogisticsSearchResult {
    private Integer status = 0;

    private String id;

    /**
     * 上游单号
     */
    private String outOrderNo;

    /**
     * 订单编号 申报单号
     */
    private String declareOrderNo;

    private String orderId;
    /**
     * 物流运单编号
     */
    private String logisticsNo;

    private String expressName;

    private String statusDesc;

    private String customsDesc;

    private String customsDetail;

    private String logisticsCompanyName;


    /**
     * 运费
     */
    private BigDecimal feeAmount;


    /**
     * 租户ID
     */
    private String tenantName;
    /**
     * 完成时间
     */
    private String finishAt;

    private String createAt;

    private String lastCustomsAt;

    private String lastDeclareAt;

}
