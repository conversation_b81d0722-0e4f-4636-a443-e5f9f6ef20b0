package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.danding.cds.facade.IOSSClientFacade;
import com.danding.cds.item.api.dto.submit.CwInventoryExcelVO;
import com.danding.cds.item.api.dto.submit.CwInventoryExportExcelVO;
import com.danding.cds.v2.bean.dto.CwInventoryDifferenceDTO;
import com.danding.cds.v2.bean.dto.CwLocationCustomsBookRelationDTO;
import com.danding.cds.v2.bean.dto.CwProductRelationDTO;
import com.danding.cds.v2.bean.dto.RecordProductStockDTO;
import com.danding.cds.v2.bean.vo.req.RecordProductStockSearch;
import com.danding.cds.v2.service.CwInventoryDifferenceService;
import com.danding.cds.v2.service.CwLocationCustomsBookRelationService;
import com.danding.cds.v2.service.CwProductRelationService;
import com.danding.cds.v2.service.RecordProductStockService;
import com.danding.cds.web.item.vo.CwDataMergeVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * CW库存导入
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_CW_INVENTORY", templateUrl = "https://dante-img.oss-cn-hangzhou.aliyuncs.com/20559338654.xlsx",
        groups = {@ParkImportsHandler.Group(name = "CW库存", classes = CwInventoryExcelVO.class),})
@Component
@RefreshScope
public class CwInventoryImportHandler extends ImportsBaseHandler {

    @Value("${cwUserId:}")
    private String cwUserId;

    @Override
    public void work() {
        List<CwInventoryExcelVO> list = new ArrayList<>();
        CwInventoryDifferenceService inventoryDifferenceService = this.getBean(CwInventoryDifferenceService.class);
        CwInventoryDifferenceDTO differenceDTO = new CwInventoryDifferenceDTO();
        differenceDTO.setInventorySource("CW");//目前写死
        try {
            Map<String, Object> extendMap = this.getTaskInfo().getExtend();
            Integer tenantId = (Integer) extendMap.get("tenantId");
            if (Objects.nonNull(tenantId)) {
                SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
            }
            list = readCwInventoryData();
            if (CollUtil.isEmpty(list)) {
                log.error("CwInventoryImportHandler-导入未获取到数据");
                return;
            }
            differenceDTO.setStatus(1);
            Long id = inventoryDifferenceService.addCwInventoryDifference(differenceDTO);
            differenceDTO.setId(id);
            List<CwDataMergeVO> cwDataMergeListNew = processCwData(list);//cw库存
            List<RecordProductStockDTO> ccsDataList = getCustomsBookItem();//ccs库存
            List<CwInventoryExportExcelVO> inventoryExportExcelVOList = dataMerge(ccsDataList, cwDataMergeListNew);
            //按照料号排序
            inventoryExportExcelVOList.sort(Comparator.comparing(CwInventoryExportExcelVO::getProductId));
            // 生成excel
            ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
            EasyExcel.write(byteArrayOutputStream, CwInventoryExportExcelVO.class)
                    .sheet("库存差异表")
                    .doWrite(inventoryExportExcelVOList);
            IOSSClientFacade iossClientFacade = this.getBean(IOSSClientFacade.class);
            String ossAddress = iossClientFacade.uploadObject("库存差异表.xlsx", byteArrayOutputStream, true);
            differenceDTO.setFileUrl(ossAddress);
            differenceDTO.setStatus(2);
            inventoryDifferenceService.addCwInventoryDifference(differenceDTO);
            log.info("CwInventoryImportHandler-导入成功，oss地址：{}", ossAddress);
        } catch (Exception e) {
            this.callbackData(false, list.size(), "导入失败", null);
            log.error("CwInventoryImportHandler-导入异常{}", e.getMessage(), e);
            differenceDTO.setStatus(3);
            inventoryDifferenceService.addCwInventoryDifference(differenceDTO);
        }
    }

    private List<CwInventoryExcelVO> readCwInventoryData() {
        List<CwInventoryExcelVO> list = new ArrayList<>();
        List<ImportsDataGroup> groups = this.readData();
        groups.stream()
                .filter(group -> group.getName().equalsIgnoreCase("CW库存"))
                .findFirst()
                .ifPresent(group -> list.addAll(group.getDataList(CwInventoryExcelVO.class)));
        return list;
    }

    /**
     * 获取cw基础数据
     *
     * @return
     */
    private List<CwDataMergeVO> processCwData(List<CwInventoryExcelVO> list) {
        List<CwDataMergeVO> dataMergeList = new ArrayList<>();
        CwProductRelationService productRelationService = this.getBean(CwProductRelationService.class);
        CwLocationCustomsBookRelationService locationCustomsBookRelationService = this.getBean(CwLocationCustomsBookRelationService.class);

        List<CwProductRelationDTO> productRelationDTOList = productRelationService.queryList();
        List<CwLocationCustomsBookRelationDTO> locationCustomsBookRelationDTOList = locationCustomsBookRelationService.queryList();

        List<CwDataMergeVO> dataMergeVOList = list.stream()
                .map(vo -> createCwDataMergeVO(vo, productRelationDTOList, locationCustomsBookRelationDTOList))
                .collect(Collectors.toList());

        //dataMergeVOList赛选productId为空或者customsBookNo为空的数据
        List<CwDataMergeVO> cwProductIdAndCustomsBookNull = dataMergeVOList.stream()
                .filter(cw -> StringUtils.isEmpty(cw.getProductId()) || StringUtils.isEmpty(cw.getCustomsBookNo()))
                .collect(Collectors.toList());

        //dataMergeVOList赛选productId不为空customsBookNo不为空的数据
        List<CwDataMergeVO> cwProductIdAndCustomsBookNotNull = dataMergeVOList.stream()
                .filter(cw -> StringUtils.isNotEmpty(cw.getProductId()) && StringUtils.isNotEmpty(cw.getCustomsBookNo()))
                .collect(Collectors.toList());

        //按料号、海关账册、产品名称、公司名称分组
        List<CwDataMergeVO> productIdAndCustomsBookNotNull = cwProductIdAndCustomsBookNotNull.stream()
                .collect(Collectors.groupingBy(cw -> cw.getProductId() + ";" + cw.getCustomsBookNo() + ";" + cw.getCwProduct() + ";" + cw.getCompanyName(),
                        Collectors.mapping(CwDataMergeVO::getInventoryNum, Collectors.summingInt(Integer::intValue))))
                .entrySet().stream()
                .map(entry -> {
                    String[] productIdAndCustomsBookNo = entry.getKey().split(";");
                    CwDataMergeVO cwDataMergeVO = new CwDataMergeVO();
                    cwDataMergeVO.setProductId(productIdAndCustomsBookNo[0].equals("null") ? "" : productIdAndCustomsBookNo[0]);
                    cwDataMergeVO.setCustomsBookNo(productIdAndCustomsBookNo[1].equals("null") ? "" : productIdAndCustomsBookNo[1]);
                    cwDataMergeVO.setCwProduct(productIdAndCustomsBookNo[2].equals("null") ? "" : productIdAndCustomsBookNo[2]);
                    cwDataMergeVO.setCompanyName(productIdAndCustomsBookNo[3].equals("null") ? "" : productIdAndCustomsBookNo[3]);
                    cwDataMergeVO.setInventoryNum(entry.getValue().intValue());
                    return cwDataMergeVO;
                })
                .collect(Collectors.toList());
        dataMergeList.addAll(cwProductIdAndCustomsBookNull);
        dataMergeList.addAll(productIdAndCustomsBookNotNull);
        log.info("productRelationDTOList:{}", productRelationDTOList);  //cw基础数据
        log.info("locationCustomsBookRelationDTOList:{}", locationCustomsBookRelationDTOList);  //cw基础数据
        log.info("dataMergeVOList:{}", dataMergeVOList);  //cw导入数据和基础数据合并
        return dataMergeList;
    }

    /**
     * cw导入数据和基础数据合并
     *
     * @param vo
     * @param productRelationDTOList
     * @param locationCustomsBookRelationDTOList
     * @return
     */
    private CwDataMergeVO createCwDataMergeVO(CwInventoryExcelVO vo,
                                              List<CwProductRelationDTO> productRelationDTOList,
                                              List<CwLocationCustomsBookRelationDTO> locationCustomsBookRelationDTOList) {
        CwDataMergeVO cwDataMergeVO = new CwDataMergeVO();
        cwDataMergeVO.setCwProduct(vo.getCwProduct());
        cwDataMergeVO.setWarehouseLocation(vo.getWarehouseLocation());
        cwDataMergeVO.setInventoryNum(vo.getInventoryNum());
        if(StringUtils.isEmpty(vo.getCwProduct())||StringUtils.isEmpty(vo.getWarehouseLocation())||vo.getInventoryNum()==null){
            this.callbackData(false, 1, "导入数据不能为空!", vo);
            throw new RuntimeException("导入数据不能为空!");
        }
        productRelationDTOList.stream()
                .filter(productRelationDTO -> productRelationDTO.getCwProduct().equals(vo.getCwProduct()))
                .findFirst()
                .ifPresent(productRelationDTO -> cwDataMergeVO.setProductId(productRelationDTO.getProductId()));

        locationCustomsBookRelationDTOList.stream()
                .filter(locationCustomsBookRelationDTO -> {
                    String[] split = vo.getWarehouseLocation().split("-");
                    return locationCustomsBookRelationDTO.getWarehouseLocation().equals(split[0]);
                })
                .findFirst()
                .ifPresent(locationCustomsBookRelationDTO -> {
                    cwDataMergeVO.setCompanyName(locationCustomsBookRelationDTO.getCompanyName());
                    cwDataMergeVO.setCustomsBookNo(locationCustomsBookRelationDTO.getCustomsBookNo());
                });
        this.callbackData(true, 1, "导入成功", vo);
        return cwDataMergeVO;
    }

    /**
     * 获取APEX用户下所有的料号库存
     *
     * @return
     */
    public List<RecordProductStockDTO> getCustomsBookItem() {
        RecordProductStockService recordProductStockService = this.getBean(RecordProductStockService.class);
        RecordProductStockSearch recordProductStockSearch = new RecordProductStockSearch();
//        String cwUserId = getPortalProperty("cwUserId");//从配置文件获取APEX用户
        recordProductStockSearch.setTenantId(cwUserId);//获取APEX用户下所有的料号库存
        ListVO<RecordProductStockDTO> recordProductStockDTOListVO = new ListVO<>();
        List<RecordProductStockDTO> dataList = new ArrayList<>();
        List<RecordProductStockDTO> ccsDataList = new ArrayList<>();
        recordProductStockSearch.setCurrentPage(1);
        recordProductStockSearch.setPageSize(2000);
        recordProductStockDTOListVO = recordProductStockService.paging(recordProductStockSearch);
        dataList = recordProductStockDTOListVO.getDataList();
        if (CollectionUtils.isNotEmpty(dataList)) {
            ccsDataList.addAll(dataList);
        }
        int i = 2;
        while (CollectionUtils.isNotEmpty(dataList)) {
            recordProductStockSearch.setCurrentPage(i);
            recordProductStockDTOListVO = recordProductStockService.paging(recordProductStockSearch);
            dataList = recordProductStockDTOListVO.getDataList();
            if (CollectionUtils.isNotEmpty(dataList)) {
                ccsDataList.addAll(dataList);
            }
            i++;
        }
        return ccsDataList;
    }

    /**
     * cw导出数据和APEX数据合并
     *
     * @param ccsDataList
     * @param cwDataMergeVOList
     * @return
     */
    public List<CwInventoryExportExcelVO> dataMerge(List<RecordProductStockDTO> ccsDataList, List<CwDataMergeVO> cwDataMergeVOList) {
        List<CwInventoryExportExcelVO> cwInventoryExcelVOList = new ArrayList<>();
        if (ccsDataList == null) {
            ccsDataList = new ArrayList<>();
        }
        if (cwDataMergeVOList == null) {
            cwDataMergeVOList = new ArrayList<>();
        }
        //根据料号和海关账册号合并数据
        for (CwDataMergeVO cwDataMergeVO : cwDataMergeVOList) {
            int flag = 0;
            for (RecordProductStockDTO recordProductStockDTO : ccsDataList) {
                if (cwDataMergeVO.getProductId() != null
                        && recordProductStockDTO.getProductId() != null
                        && cwDataMergeVO.getProductId().equals(recordProductStockDTO.getProductId())
                        && cwDataMergeVO.getCustomsBookNo() != null
                        && recordProductStockDTO.getCustomsBookNo() != null
                        && cwDataMergeVO.getCustomsBookNo().equals(recordProductStockDTO.getCustomsBookNo())) {
                    CwInventoryExportExcelVO inventoryExportExcelVO = new CwInventoryExportExcelVO();
                    inventoryExportExcelVO.setCwProduct(cwDataMergeVO.getCwProduct());
                    inventoryExportExcelVO.setProductId(cwDataMergeVO.getProductId()==null?"":cwDataMergeVO.getProductId());
                    inventoryExportExcelVO.setSkuId(recordProductStockDTO.getSkuId());
                    inventoryExportExcelVO.setGoodsName(recordProductStockDTO.getGoodsName());
                    inventoryExportExcelVO.setCompanyName(cwDataMergeVO.getCompanyName());
                    inventoryExportExcelVO.setCustomsBookNo(cwDataMergeVO.getCustomsBookNo());
                    inventoryExportExcelVO.setCwInventoryNum(cwDataMergeVO.getInventoryNum());
                    inventoryExportExcelVO.setCcsInventoryNum(recordProductStockDTO.getAccountNum());
                    cwInventoryExcelVOList.add(inventoryExportExcelVO);
                    flag++;
                }
            }
            //cw有，ccs没有的数据
            if (flag == 0) {
                CwInventoryExportExcelVO inventoryExportExcelVO = new CwInventoryExportExcelVO();
                inventoryExportExcelVO.setCwProduct(cwDataMergeVO.getCwProduct());
                inventoryExportExcelVO.setProductId(cwDataMergeVO.getProductId()==null?"":cwDataMergeVO.getProductId());
                inventoryExportExcelVO.setCompanyName(cwDataMergeVO.getCompanyName());
                inventoryExportExcelVO.setCustomsBookNo(cwDataMergeVO.getCustomsBookNo());
                inventoryExportExcelVO.setCwInventoryNum(cwDataMergeVO.getInventoryNum());
                cwInventoryExcelVOList.add(inventoryExportExcelVO);
            }
        }
        //ccs有，cw没有的数据
        for (RecordProductStockDTO recordProductStockDTO : ccsDataList) {
            int flag = 0;
            for (CwDataMergeVO cwDataMergeVO : cwDataMergeVOList) {
                if (cwDataMergeVO.getProductId() != null
                        && recordProductStockDTO.getProductId() != null
                        && cwDataMergeVO.getProductId().equals(recordProductStockDTO.getProductId())
                        && cwDataMergeVO.getCustomsBookNo() != null
                        && recordProductStockDTO.getCustomsBookNo() != null
                        && cwDataMergeVO.getCustomsBookNo().equals(recordProductStockDTO.getCustomsBookNo())) {
                    flag++;
                }
            }
            if (flag == 0) {
                CwInventoryExportExcelVO inventoryExportExcelVO = new CwInventoryExportExcelVO();
                inventoryExportExcelVO.setCustomsBookNo(recordProductStockDTO.getCustomsBookNo());
                inventoryExportExcelVO.setCcsInventoryNum(recordProductStockDTO.getAccountNum());
                inventoryExportExcelVO.setSkuId(recordProductStockDTO.getSkuId());
                inventoryExportExcelVO.setGoodsName(recordProductStockDTO.getGoodsName());
                inventoryExportExcelVO.setProductId(recordProductStockDTO.getProductId());
                cwInventoryExcelVOList.add(inventoryExportExcelVO);
            }
        }
        return cwInventoryExcelVOList;
    }

//    public static String getPortalProperty(String key) {
//        String property = "";
//        try {
//            Config config = ConfigService.getConfig("cds-web");
//            property = config.getProperty(key, "");
//            log.info("apollo的读取参数，key:{}, value:{}", key, property);
//            return property;
//        } catch (Exception e) {
//            log.error("apollo读取配置异常：", e);
//            return property;
//        }
//    }

}
