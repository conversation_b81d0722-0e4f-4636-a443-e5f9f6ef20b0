package com.danding.cds.web.customsOrder.vo;

import com.danding.cds.web.order.vo.CustomsOrderItemResult;
import com.danding.cds.web.order.vo.CustomsOrderLogResult;
import com.danding.encrypt.annotation.DesensitizeField;
import com.danding.encrypt.constant.RegexConstant;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class CustomsOrderResult {

    @ApiModelProperty(value = "订单ID")
    private String id;

    private String declareOrderNo;

    private String outOrderNo;

    private String createTime;

    private String ebpName;

    @ApiModelProperty(value = "申报单id")
    private String orderId;

    @ApiModelProperty(value = "申报状态 成功 100 失败 -1,该项不申报 0，其余都是待审申报或申报中")
    private Integer status = 0;

    @ApiModelProperty(value = "状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "清关回执")
    private String customsStatusDesc;

    @ApiModelProperty(value = "清关回执详情")
    private String customsDetail;

    @ApiModelProperty(value = "申报时间")
    private String declareTime;

    @ApiModelProperty(value = "申报成功时间")
    private String customsPassTo;

    @ApiModelProperty(value = "回执时间")
    private String receiveTime;

    @ApiModelProperty(value = "创建时间")
    private String createAt;

    @ApiModelProperty(value = "报文传输企业")
    private String agentCompanyName;

    @ApiModelProperty(value = "电商企业")
    private String ebcName;

    @ApiModelProperty(value = "关区口岸")
    private String customs;

    @ApiModelProperty(value = "订单总金额")
    private BigDecimal totalAmount;

    @ApiModelProperty(value = "实际支付金额")
    private BigDecimal paidAmount;

    @ApiModelProperty(value = "总运费")
    private BigDecimal freight;

    @ApiModelProperty(value = "总税金")
    private BigDecimal tax;

    @ApiModelProperty(value = "总净重")
    private BigDecimal totalNetWeight;

    @ApiModelProperty(value = "总毛重")
    private BigDecimal totalGrossWeight;

    @ApiModelProperty(value = "折扣 正数")
    private BigDecimal discount;

    @ApiModelProperty(value = "订购人证件号码")
    @DesensitizeField(regex = RegexConstant.IDCARD, replace = RegexConstant.IDCARD_REPLACE)
    private String buyerIdNumber;

    @ApiModelProperty(value = "订购人姓名")
    @DesensitizeField(regex = RegexConstant.NAME, replace = RegexConstant.NAME_REPLACE)
    private String buyerName;

    @ApiModelProperty(value = "订购人电话")
    @DesensitizeField(regex = RegexConstant.NAME, replace = RegexConstant.NAME_REPLACE)
    private String buyerTelNumber;

    @ApiModelProperty(value = "收货省")
    private String consigneeProvince;

    @ApiModelProperty(value = "收货市")
    private String consigneeCity;

    @ApiModelProperty(value = "收货区")
    private String consigneeDistrict;

    @ApiModelProperty(value = "收货街道")
    private String consigneeStreet;

    @ApiModelProperty(value = "详细地址")
    @DesensitizeField(regex = RegexConstant.ADDRESS, replace = RegexConstant.ADDRESS_REPLACE)
    private String consigneeAddress;

    @ApiModelProperty(value = "物流企业")
    private String logisticsCompanyName;

    @ApiModelProperty(value = "支付企业")
    private String payCompanyName;

    @ApiModelProperty(value = "支付申报流水号")
    private String declarePayNo;

    @ApiModelProperty(value = "支付交易流水号")
    private String tradePayNo;

    @ApiModelProperty(value = "交易时间")
    private String tradeAt;

    @ApiModelProperty(value = "发件人名称")
    @DesensitizeField(regex = RegexConstant.NAME,replace = RegexConstant.NAME_REPLACE)
    private String senderName;

    @ApiModelProperty(value = "收件人姓名")
    @DesensitizeField(regex = RegexConstant.NAME,replace = RegexConstant.NAME_REPLACE)
    private String consigneeName;

    @ApiModelProperty(value = "收件人电话")
    @DesensitizeField(regex = RegexConstant.PHONE,replace = RegexConstant.PHONE_REPLACE)
    private String consigneeTel;

    @ApiModelProperty(value = "收件人邮箱")
    private String consigneeEmail;

    @ApiModelProperty(value = "备注")
    private String note;

    @ApiModelProperty(value = "商品详情")
    private List<CustomsOrderItemResult> itemList;
    @ApiModelProperty(value = "订单日志")
    private List<CustomsOrderLogResult> logList;
}
