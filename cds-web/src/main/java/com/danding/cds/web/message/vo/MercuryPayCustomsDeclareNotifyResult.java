package com.danding.cds.web.message.vo;

import lombok.Data;

@Data
public class MercuryPayCustomsDeclareNotifyResult{

    /**
     * 申报海关编号
     */
    private String customs;

    /**
     * 申报支付单的系统编码
     */
    private String customsPaymentCode;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 系统订单类型：系统支付单号；外部订单类型：外部订单号（一般指代支付单号）
     */
    private String orderNo;

    /**
     * 申报订单编号 -拆单时为子订单号，不拆单时为主订单号
     */
    private String declareOrderNo;

    /**
     * 业务订单号，用于外部标识订单
     */
    private String businessOrderNo;

    /**
     * 第三方支付流水号
     */
    private String bankNo;

    /**
     * 第三方子订单流水号
     */
    private String subBankNo;

    /**
     * 收款账号
     */
    private String recpAccount;

    /**
     * 收款企业社会信用代码
     */
    private String recpCode;

    /**
     * 收款企业名称
     */
    private String recpName;

    /**
     * 申报是否成功
     */
    private Boolean success;

    /**
     * 身份证验证是否失败
     */
    private Boolean identityCheck = true;

    /**
     * 验核机构
     */
    private String verDept;

    /**
     * 验核交易流水号
     */
    private String payTransactionId;

    /**
     * 备注信息
     */
    private String extra;

    /**
     * 失败信息
     */
    private String errorMsg;
}
