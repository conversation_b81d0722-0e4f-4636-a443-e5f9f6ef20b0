package com.danding.cds.web.checklist.controller;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.checklist.api.dto.ChecklistDTO;
import com.danding.cds.checklist.api.dto.ChecklistDTOV2;
import com.danding.cds.checklist.api.dto.ChecklistSearch;
import com.danding.cds.checklist.api.dto.ChecklistSubmit;
import com.danding.cds.checklist.api.enums.CheckListCustomsStatusEnum;
import com.danding.cds.checklist.api.enums.ChecklistBindType;
import com.danding.cds.checklist.api.enums.ChecklistStatusEnum;
import com.danding.cds.checklist.api.enums.ChecklistType;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.common.utils.CustomsCharLengthUtils;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.dto.EndorsementItemDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.IEType;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.v2.bean.vo.req.ChecklistAssociateItemReqVo;
import com.danding.cds.v2.bean.vo.req.ChecklistItemSaveReqVo;
import com.danding.cds.v2.bean.vo.res.CheckListViewResVo;
import com.danding.cds.v2.bean.vo.res.ChecklistAssociateItemResVo;
import com.danding.cds.vehicle.api.dto.CheckListVehicleDTO;
import com.danding.cds.vehicle.api.dto.CheckListVehicleSubmit;
import com.danding.cds.vehicle.api.service.CheckListVehicleService;
import com.danding.cds.web.checklist.vo.ChecklistFinishVO;
import com.danding.cds.web.checklist.vo.ChecklistPagingResult;
import com.danding.cds.web.checklist.vo.EndorsementItemVO;
import com.danding.cds.web.user.UserService;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@RestController
@Api(tags = "核放管理")
@RequestMapping("/checklist")
public class ChecklistOrderController {

    @DubboReference
    private ChecklistService checklistService;

    @DubboReference
    private CustomsInventoryService customsInventoryService;

    @DubboReference
    private ExpressService expressService;

    @DubboReference
    private ExportOrderService exportOrderService;

    @DubboReference
    private EndorsementService endorsementService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CheckListVehicleService checkListVehicleService;

    @DubboReference
    private CompanyService companyService;


    @ApiOperation(value = "获取所有核放状态")
    @GetMapping("/listStatus")
    public List<SelectItemVO> listQualify() {
        List<SelectItemVO> result = Arrays.stream(ChecklistStatusEnum.values())
                .filter((ChecklistStatusEnum item) -> !item.equals(ChecklistStatusEnum.NULL))
                .map((ChecklistStatusEnum item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取所有类型")
    @GetMapping("/listType")
    public List<SelectItemVO> listType() {
        List<SelectItemVO> result = Arrays.stream(ChecklistType.values())
                .filter((ChecklistType item) -> !item.equals(ChecklistType.NULL))
                .map((ChecklistType item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取核放单与清单的映射关系")
    @GetMapping("/check-list-type-map-bussiness")
    public Response<String> checkListTypeMapEndorsementBussiness() {
        Map<Integer, EndorsementBussiness[]> map = ChecklistType.getCheckListTypeMapEndorsementBussiness();
        Map<Integer, String[]> toMap = new HashMap<>();
        map.forEach((key, value) ->
        {
            String arrays[] = Arrays.stream(value).map(s -> {
                return s.getCode();
            }).collect(Collectors.toList()).toArray(new String[]{});
            toMap.put(key, arrays);
        });
        return new Response<String>(JSON.toJSONString(toMap));
    }

    @ApiOperation(value = "获取全部车辆信息")
    @GetMapping("/listVehicle")
    public List<SelectItemVO> listVehicle() {
        List<CheckListVehicleDTO> dtoList = checkListVehicleService.listAll();
        return dtoList.stream().map(dto -> {
            SelectItemVO selectItemVO = new SelectItemVO();
            selectItemVO.setName(dto.getVehicleLicensePlate());
            selectItemVO.setValue(dto.getId());
            return selectItemVO;
        }).collect(Collectors.toList());
    }

    @ApiOperation(value = "获取指定车辆信息")
    @GetMapping("/getVehicleById")
    public CheckListVehicleDTO getVehicleById(Long id) {
        CheckListVehicleDTO dto = new CheckListVehicleDTO();
        BeanUtil.copyProperties(checkListVehicleService.findById(id), dto);
        return dto;
    }

    public Long create(ChecklistSubmit submit) {
        String licensePlate = submit.getLicensePlate();
        if (Objects.isNull(licensePlate)) {
            throw new ArgsErrorException("车牌号不能为空");
        }
        //校验车牌格式
//        try {
//            LicensePlateUtils.validateLicensePlate(licensePlate);
//        } catch (Exception e) {
//            throw new ArgsErrorException(e.getMessage());
//        }
        IEType ieType = ChecklistType.getIEType(submit.getType());
        if (ieType == null) {
            throw new ArgsErrorException("核放单类型参数错误");
        }
        ChecklistBindType bindType = ChecklistBindType.getEnum(submit.getBindType());
        if (submit.getType().equals(ChecklistType.EMPTY.getCode()) ||
                submit.getType().equals(ChecklistType.EMPTY_OUT.getCode()) ||
                submit.getType().equals(ChecklistType.TWO_STEP.getCode())) {
            if (!Objects.equals(bindType, ChecklistBindType.NULL)) {
                throw new ArgsErrorException("空车出入区、两步申报 绑定类型只允许选择无");
            }
        }
        submit.setIeFlag(ieType.getValue());
        Long userId = UserUtils.getUserId().longValue();
        Long id = checklistService.create(submit, userId);
        CheckListVehicleDTO vehicleDTO = checkListVehicleService.findByVehicleLicense(submit.getLicensePlate());
        //车辆不存在保存车辆信息
        if (vehicleDTO == null) {
            CheckListVehicleSubmit vehicleSubmit = new CheckListVehicleSubmit();
            vehicleSubmit.setVehicleFrameNo(submit.getLicenseFrame());
            vehicleSubmit.setVehicleFrameWeight(submit.getFrameWeight());
            vehicleSubmit.setVehicleLicensePlate(submit.getLicensePlate());
            vehicleSubmit.setVehicleWeight(submit.getCarWeight());
            checkListVehicleService.upset(vehicleSubmit);
        }
        return id;
    }

    @ApiOperation(value = "新增|更新核放单")
    @PostMapping("/upset")
    public Long upset(@RequestBody ChecklistSubmit submit) {
        IEType ieType = ChecklistType.getIEType(submit.getType());
        if (ieType == null) {
            throw new ArgsErrorException("核放单类型参数错误");
        }
        Long id = submit.getId();
        ChecklistDTO checklistDTO = checklistService.findById(id);
        if (Objects.isNull(checklistDTO)) {
            throw new ArgsErrorException("核放单不存在");
        }
        String status = checklistDTO.getStatus();
        ChecklistStatusEnum statusEnum = ChecklistStatusEnum.getEnum(status);
        if (Objects.isNull(statusEnum)) {
            throw new ArgsErrorException("核放单状态错误");
        }
        if (!Objects.equals(statusEnum, ChecklistStatusEnum.CREATED) && !Objects.equals(statusEnum, ChecklistStatusEnum.EXCEPTION) && !Objects.equals(statusEnum, ChecklistStatusEnum.STORED)) {
            throw new ArgsErrorException("当前核放单状态不允许编辑");
        }
        String remark = submit.getRemark();
        if (Objects.nonNull(submit.getRemark())) {
            if (CustomsCharLengthUtils.overLengthCheck(remark, 512)) {
                throw new ArgsErrorException("备注不允许大于512字");
            }
        }
        if (Objects.nonNull(submit.getContainerType())) {
            if (CustomsCharLengthUtils.overLengthCheck(submit.getContainerType(), 255)) {
                throw new ArgsErrorException("集装箱箱型不允许大于255位");
            }
        }
        submit.setIeFlag(ieType.getValue());
//        this.preCheckBindType(submit);
        ChecklistBindType bindType = ChecklistBindType.getEnum(submit.getBindType());
        List<Integer> checkWeightBindType = Arrays.asList(ChecklistBindType.MULTI_TICKET_ONE_CAR.getCode(),
                ChecklistBindType.ONE_TICKET_ONE_CAR.getCode(),
                ChecklistBindType.ONE_TICKET_MULTI_CAR.getCode());
        if (checkWeightBindType.contains(bindType.getCode())) {
            if (Objects.isNull(submit.getTotalNetWeight())) {
                throw new ArgsErrorException("绑定类型:" + bindType.getDesc() + "货物总净重必填");
            }
            if (Objects.isNull(submit.getTotalGrossWeight())) {
                throw new ArgsErrorException("绑定类型:" + bindType.getDesc() + "货物总毛重必填");
            }
        }
        if (Objects.equals(submit.getType(), ChecklistType.EMPTY.getCode())) {
            if (Objects.isNull(submit.getEmptyCarPicUrl()) || Objects.isNull(submit.getEmptyCarPicName())) {
                throw new ArgsErrorException("空车入区时空车照片/磅单必填");
            }
        }
        String licensePlate = submit.getLicensePlate();
        if (Objects.isNull(licensePlate)) {
            throw new ArgsErrorException("车牌号不能为空");
        }
//        //校验车牌格式
//        try {
//            LicensePlateUtils.validateLicensePlate(licensePlate);
//        } catch (Exception e) {
//            throw new ArgsErrorException(e.getMessage());
//        }
        BigDecimal totalGrossWeight = submit.getTotalGrossWeight();
        BigDecimal totalNetWeight = submit.getTotalNetWeight();
//        if (Objects.nonNull(totalGrossWeight)) {
//            if (totalGrossWeight.precision() > 19) {
//                throw new ArgsErrorException("货物总毛重不能超过19位");
//            }
//            if (totalGrossWeight.scale() > 3) {
//                throw new ArgsErrorException("货物总毛重小数点仅支持3位数");
//            }
//        }
//        if (Objects.nonNull(totalNetWeight)) {
//            if (totalNetWeight.precision() > 19) {
//                throw new ArgsErrorException("货物总净重不能超过19位");
//            }
//            if (totalNetWeight.scale() > 3) {
//                throw new ArgsErrorException("货物总净重小数点仅支持3位数");
//            }
//        }
        if (Objects.nonNull(totalGrossWeight) && Objects.nonNull(totalNetWeight)) {
            if (totalGrossWeight.compareTo(totalNetWeight) < 0) {
                throw new ArgsErrorException("货物总毛重小于等于货物总净重，请检查");
            }
        }
//        BigDecimal carWeight = submit.getCarWeight();
//        BigDecimal frameWeight = submit.getFrameWeight();
//        if (Objects.nonNull(carWeight)) {
//            if (carWeight.precision() > 16) {
//                throw new ArgsErrorException("车辆自重不能超过16位");
//            }
//            if (carWeight.scale() > 3) {
//                throw new ArgsErrorException("车辆自重小数点仅支持3位数");
//            }
//        }
//        if (Objects.nonNull(frameWeight)) {
//            if (frameWeight.precision() > 16) {
//                throw new ArgsErrorException("车架重不能超过16位");
//            }
//            if (frameWeight.scale() > 3) {
//                throw new ArgsErrorException("车架重小数点仅支持3位数");
//            }
//        }
        id = checklistService.update(submit);
        return id;
    }


    public void bindEndorsement(ChecklistSubmit submit) {
        checklistService.bindEndorsement(submit);
    }



    @GetMapping("/push")
    @ApiOperation(value = "推送核放|重推核放")
    public Response<String> retryException(Long ids) {
        checklistService.push(ids);
        return new Response<>("推送成功");
    }

    public Response<String> pushTemporaryStorage(Long id) {
        checklistService.pushTemporaryStorage(id);
        return new Response<>("推送成功");
    }

    @Autowired
    private UserService userService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<ChecklistPagingResult> paging(ChecklistSearch search) {
        ListVO<ChecklistDTOV2> paging = checklistService.paging(search);
        ListVO<ChecklistPagingResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        List<ChecklistPagingResult> dataList = new ArrayList<>();
        List<ChecklistDTOV2> checklistDTOS = paging.getDataList();
        List<Long> userIdList = checklistDTOS.stream().map(c -> c.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(userIdList);
        for (ChecklistDTOV2 checklistDTO : paging.getDataList()) {
            ChecklistPagingResult orderVO = new ChecklistPagingResult();
            orderVO.setId(checklistDTO.getId());
            orderVO.setSn(checklistDTO.getSn());
            orderVO.setOrderNo(checklistDTO.getPreOrderNo());
            orderVO.setRealOrderNo(checklistDTO.getRealOrderNo());
            orderVO.setLicensePlate(checklistDTO.getLicensePlate());
            if (checklistDTO.getCreateTime() != null) {
                orderVO.setCreateAt(new DateTime(checklistDTO.getCreateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            }
//            if (checklistDTO.getLastCustomsTime() != null){
//                orderVO.setLastCustomsAt(new DateTime(checklistDTO.getLastCustomsTime()).toString("yyyy/MM/dd HH:mm:ss"));
//            }
            orderVO.setDeclareCompanyID(checklistDTO.getDeclareCompanyId());
            CompanyDTO declareCompanyDTO = companyService.findById(checklistDTO.getDeclareCompanyId());
            if (Objects.nonNull(declareCompanyDTO)) {
                orderVO.setDeclareCompanyName(declareCompanyDTO.getName());
            }
            orderVO.setApplicant(checklistDTO.getApplicant());
            String checkStatusDesc = ChecklistStatusEnum.getEnum(checklistDTO.getStatus()).getDesc();
            orderVO.setChecklistStatusCode(checklistDTO.getStatus());
            orderVO.setChecklistStatus(checkStatusDesc.equalsIgnoreCase("空") ? "" : checkStatusDesc);
            String customsStatusDesc = CheckListCustomsStatusEnum.getEnum(checklistDTO.getCustomsStatus()).getDesc();
            orderVO.setCustomsStatusDesc(customsStatusDesc.equalsIgnoreCase("空") ? "" : customsStatusDesc);
            orderVO.setInformationDesc(checklistDTO.getInformationDesc());
            orderVO.setType(checklistDTO.getType());
            orderVO.setTypeDesc(ChecklistType.getEnum(checklistDTO.getType()).getDesc());
            orderVO.setVehicleIcNo(checklistDTO.getVehicleIcNo());
            if (checklistDTO.getCreateBy() != null) {
                if (userRpcResultMap.containsKey(checklistDTO.getCreateBy().longValue())) {
                    orderVO.setCreateBy(userRpcResultMap.get(checklistDTO.getCreateBy().longValue()).getUserName());
                }
            }
            /**
             * 状态：已创建                 操作：完成核放、编辑、作废、推送
             * 状态：申报中                 操作：完成核放、作废
             * 状态：已审批                操作：作废
             * 状态：已完成               操作栏为空
             * 状态：申报失败            操作：完成核放、编辑、作废、推送
             * 状态：作废申报中         操作栏为空
             * 状态：已作废                操作栏为空
             * 状态：已暂存+两步申报      操作：推送
             * 状态：已创建/申请失败+两步申报      操作：推送、暂存
             */
            if (checklistDTO.getType().equals(ChecklistType.EMPTY.getCode()) ||
                    checklistDTO.getType().equals(ChecklistType.EMPTY_OUT.getCode())) {
                orderVO.setBindType("空车核放");
            }
            if (ChecklistType.TWO_STEP.getCode().equals(checklistDTO.getType())) {
                //两步申报
                //返回报关单号
                orderVO.setDeclareOrderNo(checklistDTO.getDeclareOrderNo());
                ChecklistStatusEnum checklistStatusEnum = ChecklistStatusEnum.getEnum(checklistDTO.getStatus());
                orderVO.setAllowLoadItem(true);
                switch (checklistStatusEnum) {
                    case CREATED:
                        orderVO.setAllowLoadItem(false);
                        orderVO.setAllowEdit(true);
                        orderVO.setAllowTemporaryStorage(true);
                        orderVO.setAllowDiscard(true);
                        orderVO.setAllowFinish(true);
                        orderVO.setAllowPush(true);
                        break;
                    case AUDITING:
                        orderVO.setAllowDiscard(true);
                        break;
                    case STORED:
                        orderVO.setAllowPush(true);
                        orderVO.setAllowFinish(true);
                        orderVO.setAllowDiscard(true);
                        break;
                    case EXCEPTION:
                        orderVO.setAllowLoadItem(false);
                        orderVO.setAllowEdit(true);
                        orderVO.setAllowTemporaryStorage(true);
                        orderVO.setAllowFinish(true);
                        orderVO.setAllowDiscard(true);
                        orderVO.setAllowPush(true);
                        break;
                    case DECALRING:
                        orderVO.setAllowPush(true);
                        break;
                    case STORING:
                    case FINISH:
                    case DELETING:
                    case DELETE:
                    default:
                        break;
                }
            } else {
                List<EndorsementDTO> endorsementDTOList = checklistService.getEndorsementListByChecklistId(checklistDTO.getId());
                List<String> snList = endorsementDTOList.stream().map(EndorsementDTO::getSn).collect(Collectors.toList());
                orderVO.setEndorsementSns(String.join("/", snList));
                ChecklistStatusEnum checklistStatusEnum = ChecklistStatusEnum.getEnum(checklistDTO.getStatus());
                orderVO.setAllowLoadItem(true);
                switch (checklistStatusEnum) {
                    case CREATED:
                        orderVO.setAllowLoadItem(false);
                        orderVO.setAllowEdit(true);
                        orderVO.setAllowPush(true);
                        orderVO.setAllowDiscard(true);
                        orderVO.setAllowFinish(true);
                        break;
                    case AUDITING:
                        orderVO.setAllowDiscard(true);
                        break;
                    case STORED:
                        orderVO.setAllowPush(true);
                        orderVO.setAllowDiscard(true);
                        orderVO.setAllowFinish(true);
                        break;
                    case EXCEPTION:
                        orderVO.setAllowLoadItem(false);
                        orderVO.setAllowEdit(true);
                        orderVO.setAllowPush(true);
                        orderVO.setAllowFinish(true);
                        orderVO.setAllowDiscard(true);
                        break;
                    case DECALRING:
                        orderVO.setAllowPush(true);
                        break;
                    case STORING:
                    case DELETING:
                    case DELETE:
                    case FINISH:
                    default:
                        break;
                }
            }
            if (!Objects.isNull(checklistDTO.getBindType())) {
                orderVO.setBindType(ChecklistBindType.getEnum(checklistDTO.getBindType()).getDesc());
            }
            dataList.add(orderVO);
        }
        result.setDataList(dataList);
        return result;
    }

    @ApiOperation(value = "手动完成核放")
    @PostMapping("/finish")
    public Response<String> finish(@RequestBody ChecklistFinishVO checklistFinishVO) {
        try {
            checklistService.finish(checklistFinishVO.getId(), checklistFinishVO.getRealNo());
        } catch (ArgsErrorException e) {
            return new Response<>(e);
        }
        return new Response<>("操作成功");
    }

    @ApiOperation(value = "作废")
    @PostMapping("/discard")
    public Long discard(@RequestBody IdParam idParam) {
        return checklistService.discard(idParam.getId());
    }


    @ApiOperation(value = "手动过卡")
    @GetMapping("/handler-check")
    public Response<String> handlerCheck(String ids) {
        if (org.apache.commons.lang3.StringUtils.isEmpty(ids)) {
            Response oret = new Response<>("提交成功");
            oret.setCode(-1);
            oret.setErrorMessage("请选择核放单");
            return oret;
        }
        List<Long> idList = Arrays.stream(ids.split(",")).map(Long::valueOf).collect(Collectors.toList());
        for (Long id : idList) {
            ChecklistDTO checklistDTO = checklistService.findById(id);
            if (!checklistDTO.getStatus().equals(ChecklistStatusEnum.AUDITING.getCode())) {
                Response oret = new Response<>("提交成功");
                oret.setCode(-1);
                oret.setErrorMessage("对不起!单据[" + checklistDTO.getSn() + "]当前状态不能完成手动过卡");
                return oret;
                //
            } else if (StringUtils.isEmpty(checklistDTO.getRealOrderNo())) {
                Response oret = new Response<>("提交成功");
                oret.setCode(-1);
                oret.setErrorMessage("对不起!单据[" + checklistDTO.getSn() + "]当前真真实核放单号为空,不能手动过卡");
                return oret;
            }
        }
        checklistService.handlerCheck(idList);
        return new Response<>("提交成功");
    }


    @ApiOperation(value = "查看核放清单表体信息")
    @GetMapping("/loadEndorsementItem")
    public List<EndorsementItemVO> loadEndorsementItem(Long id) {
        ChecklistDTO checklistDTO = checklistService.findById(id);
        CustomsBookDTO customsBookDTO = customsBookService.findById(checklistDTO.getAccountBookId());
        List<EndorsementItemDTO> endorsementItemDTOS = endorsementService.listItemByChecklist(id);
        List<EndorsementItemVO> result = new ArrayList<>();
        for (EndorsementItemDTO endorsementItemDTO : endorsementItemDTOS) {
            EndorsementItemVO vo = new EndorsementItemVO() {
                @Override
                public boolean equals(Object _obj) {
                    EndorsementItemVO _this = (EndorsementItemVO) _obj;
                    return org.apache.commons.lang3.StringUtils.equalsIgnoreCase(_this.getAccountBookNo(), this.getAccountBookNo())
                            && org.apache.commons.lang3.StringUtils.equalsIgnoreCase(_this.getGoodsSeqNo(), this.getGoodsSeqNo())
                            && org.apache.commons.lang3.StringUtils.equalsIgnoreCase(_this.getProductId(), this.getProductId());
                }
            };
            vo.setId(endorsementItemDTO.getEndorsementId());
            vo.setAccountBookNo(customsBookDTO.getBookNo());
            vo.setProductId(endorsementItemDTO.getProductId());
            vo.setGoodsSeqNo(endorsementItemDTO.getGoodsSeqNo());
            if (!result.contains(vo))
                result.add(vo);
        }
        return result;
    }

    public CheckListViewResVo viewCheckList(Long id) {
        return checklistService.viewCheckList(id);
    }

    public ChecklistAssociateItemResVo associateItemBySerialNumber(ChecklistAssociateItemReqVo reqVo) throws ArgsErrorException {
        return checklistService.associateItemBySerialNumber(reqVo);
    }

    public void saveChecklistItem(ChecklistItemSaveReqVo reqVo) throws ArgsErrorException {
        checklistService.saveChecklistItem(reqVo);
    }

    public ChecklistAssociateItemResVo viewChecklistItem(Long id) throws ArgsErrorException {
        return checklistService.viewChecklistItem(id);
    }

    public void deleteChecklistItem(Long id) {
        checklistService.deleteChecklistItem(id);
    }

}
