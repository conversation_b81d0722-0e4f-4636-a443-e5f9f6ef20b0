package com.danding.cds.web.authData.test;

import com.danding.cds.customs.hs.api.dto.CustomsHsSearchCondition;
import com.danding.cds.web.customs.vo.CustomsHsCodeVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.api.data.model.DataModel;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2021/4/6 11:25
 * @Description:
 */
public interface AuthDataTestRpc {


    RpcResult get() ;
}
