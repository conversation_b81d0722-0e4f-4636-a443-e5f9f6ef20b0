package com.danding.cds.web.customsOrder.rpc;

import com.danding.cds.customs.order.api.dto.CustomsOrderSearch;
import com.danding.cds.order.api.dto.CustomsOrderBaseInfoSubmit;
import com.danding.cds.web.customsOrder.vo.CustomsOrderResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface CustomsOrderRpc {

    RpcResult<ListVO<CustomsOrderResult>> paging(CustomsOrderSearch search);

    RpcResult<String> save(CustomsOrderBaseInfoSubmit customsOrderBaseInfo);
}
