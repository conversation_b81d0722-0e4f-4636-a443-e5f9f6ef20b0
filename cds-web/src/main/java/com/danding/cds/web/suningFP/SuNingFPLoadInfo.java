package com.danding.cds.web.suningFP;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.math.BigDecimal;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"loadHeadId", "loadId", "total", "totalWeight", "carEcNo", "tracyNum","loadContents"})
@XmlRootElement(name = "LoadHead")
public class SuNingFPLoadInfo {

    @XmlElement(name = "loadHeadId")
    private String loadHeadId;

    @XmlElement(name = "loadId")
    private String loadId;

    @XmlElement(name = "total")
    private String total;

    @XmlElement(name = "TotalWeight")
    private BigDecimal totalWeight;

    @XmlElement(name = "CarEcNo")
    private String carEcNo;

    @XmlElement(name = "tracyNum")
    private String tracyNum;

    @XmlElement(name = "loadContents")
    private SuNingFPLoadContents loadContents;
}
