package com.danding.cds.web.checklist.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.afterturn.easypoi.excel.annotation.ExcelCollection;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
public class ChecklistGroupExportVO implements Serializable {
    @Excel(name = "托盘号", needMerge = true)
    private String trayNo;

    @Excel(name = "操作人", needMerge = true)
    private String operatorNo;

    @ExcelCollection(name = "")
    private List<ChecklistItemExportVO> itemList;

    @Excel(name = "对应托盘包裹总重量（kg）", needMerge = true)
    private BigDecimal trayWeight;

    @Excel(name = "对应托盘sku种类", needMerge = true)
    private Integer traySkuType;
}
