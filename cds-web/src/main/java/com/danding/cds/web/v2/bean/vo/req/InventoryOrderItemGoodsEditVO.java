package com.danding.cds.web.v2.bean.vo.req;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class InventoryOrderItemGoodsEditVO {

    /**
     * 清关单id
     */
    private Long inventoryOrderId;

    /**
     * id
     */
    private Long id;

    /**
     * 法定单位数量
     */
    private BigDecimal firstUnitQfy;

    /**
     * 法定第二数量(单）
     */
    private BigDecimal secondUnitQfy;

    /**
     * 申报单价
     */
    private BigDecimal declarePrice;

    /**
     * 毛重
     */
    private BigDecimal grossWeight;

    private BigDecimal totalGrossWeight;

    /**
     * 净重
     */
    private BigDecimal netweight;

    private BigDecimal totalNetWeight;

    /**
     * 原产国
     */
    private String originCountry;

    /**
     * 来源标识
     */
    private String goodsSource;

}
