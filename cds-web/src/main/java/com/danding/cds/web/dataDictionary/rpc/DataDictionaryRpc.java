package com.danding.cds.web.dataDictionary.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.customs.country.api.dto.DataDictionaryReport;
import com.danding.cds.customs.dictionary.api.vo.*;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.soul.client.common.result.RpcResult;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

public interface DataDictionaryRpc {

    /**
     * 海关字典数据分页
     *
     * @param search
     * @return
     */
    RpcResult<ListVO<CustomsDictionaryResVO>> paging(CustomsDictionaryReqVO search);

    RpcResult<ListVO<CustomsDictionaryResVO>> typePaging(@RequestBody CustomsDictionaryReqVO search);

    /**
     * 海关字典数据新增
     *
     * @param param
     * @return
     */
    RpcResult<String> insert(CustomsDictionaryOperateParam param);

    /**
     * 海关字典数据编辑
     *
     * @param param
     * @return
     */
    RpcResult<String> update(CustomsDictionaryOperateParam param);

    RpcResult<String> delete(IdParam id);

    RpcResult<List<SelectOptionVO<String>>> listDictionaryType();

    RpcResult<String> enableSwitch(DataDictionaryEnableSwitchVO switchVO);

    RpcResult listMainType();

    RpcResult listTypeCode();

    RpcResult listTypeName();

    RpcResult<DataDictionaryReport> importExcel(DataDictionaryImportSubmit param);

    RpcResult<DataDictionaryReport> preImport(ImportDictionaryParam param);

    RpcResult<LoadTaskInfoDTO> importQuery();

    RpcResult<String> importExcelV2(ImportDictionaryParam param);

    RpcResult<String> importConfirm();

    RpcResult<List<SelectOptionVO<String>>> listByType(String type);

    RpcResult<List<SelectOptionVO<String>>> listFreightForwardingCompany();

    RpcResult<List<SelectOptionVO<String>>> listCustomsInvtType();

    RpcResult<List<SelectOptionVO<String>>> listSupvMode();

    RpcResult<List<SelectOptionVO<String>>> listArrivePort();

    RpcResult<List<SelectOptionVO<String>>> listIntryWhere(Integer transType);

    RpcResult listJdWarehouseName(String customsCode);

    RpcResult listTransportMode();

    RpcResult<List<SelectOptionVO<String>>> listRecordRuleType();

    RpcResult<List<SelectOptionVO<String>>> listRouteTag();

    RpcResult<List<SelectOptionVO<String>>> listCustomsDecType();
}
