package com.danding.cds.web.itemStockList.controller;

import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.itemstocklist.api.dto.ItemStockListDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummaryDTO;
import com.danding.cds.itemstocklist.api.dto.ItemStockSummarySerach;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.itemstocklist.api.service.ItemStockSummaryService;
import com.danding.cds.itemstocklist.api.vo.ItemStockListResVO;
import com.danding.cds.itemstocklist.api.vo.ItemStockSummaryResVO;
import com.danding.logistics.api.common.response.ListVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@RestController
@Api(tags = "料号库存汇总")
@RequestMapping("/itemstocksummary")
public class ItemStockSummaryController {

    @DubboReference
    private ItemStockSummaryService itemStockSummaryService;

    @DubboReference
    private ItemStockListService itemStockListService;

    @DubboReference
    private CustomsBookService customsBookService;
    @Deprecated
    @ApiOperation(value = "料号库存汇总查询")
    @PostMapping("/paging")
    public ListVO<ItemStockSummaryResVO> paging(@RequestBody ItemStockSummarySerach condition) {
        ListVO<ItemStockSummaryDTO> pageResult = itemStockSummaryService.paging(condition);
        ListVO<ItemStockSummaryResVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<ItemStockSummaryResVO> dataList = new ArrayList<>();
        for (ItemStockSummaryDTO itemStockSummaryDTO : pageResult.getDataList()) {
            ItemStockSummaryResVO itemStockSummaryResVO = new ItemStockSummaryResVO();
            BeanUtils.copyProperties(itemStockSummaryDTO, itemStockSummaryResVO);
            dataList.add(itemStockSummaryResVO);
        }
        result.setDataList(dataList);
        return result;
    }

    @Deprecated
    @ApiOperation(value = "查看料号库存汇总总入明细")
    @PostMapping("/findindetails")
    public ListVO<ItemStockListResVO> findInDetails(@RequestBody ItemStockSummarySerach condition) {
        ListVO<ItemStockListDTO> pageResult = itemStockListService.findInDetails(condition);
        ListVO<ItemStockListResVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<ItemStockListResVO> dataList = new ArrayList<>();
        for (ItemStockListDTO itemStockListDTO : pageResult.getDataList()) {
            ItemStockListResVO itemStockListResVO = new ItemStockListResVO();
            BeanUtils.copyProperties(itemStockListDTO, itemStockListResVO);
            dataList.add(itemStockListResVO);
        }
        result.setDataList(dataList);
        return result;
    }
    @Deprecated
    @ApiOperation(value = "查看料号库存汇总总出明细")
    @PostMapping("/findoutdetails")
    public ListVO<ItemStockListResVO> findOutDetails(@RequestBody ItemStockSummarySerach condition) {
        ListVO<ItemStockListDTO> pageResult = itemStockListService.findOutDetails(condition);
        ListVO<ItemStockListResVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<ItemStockListResVO> dataList = new ArrayList<>();
        for (ItemStockListDTO itemStockListDTO : pageResult.getDataList()) {
            ItemStockListResVO itemStockListResVO = new ItemStockListResVO();
            BeanUtils.copyProperties(itemStockListDTO, itemStockListResVO);
            dataList.add(itemStockListResVO);
        }
        result.setDataList(dataList);
        return result;

    }

}
