package com.danding.cds.web.businessCenter.entity.vo;

import com.danding.cds.customs.inventory.api.dto.CalloffAfterSalesCount;
import com.danding.cds.customs.inventory.api.dto.CancelAndRefundAfterSaleCount;
import lombok.Data;

@Data
public class AfterSalesStatusCountVO {

    /**
     * 用户名称
     */
    private String userName;
    private String userId;

    /**
     * 取消单统计
     */
    private CalloffAfterSalesCount calloffCount;

    /**
     * 撤单统计
     */
    private CancelAndRefundAfterSaleCount cancelCount;

    /**
     * 退货单统计
     */
    private CancelAndRefundAfterSaleCount refundCount;
}
