package com.danding.cds.web.exportorder.rpc;

import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParamById;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.v2.bean.vo.EndorsementVO;
import com.danding.cds.web.exportorder.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCData;

import java.util.List;
public interface ExportOrderRpc {
      RpcResult<List<SelectOptionVO>>  listStatus();
      RpcResult<Long> upset(ExportOrderSubmit submit);
      RpcResult<ListVO<ExportOrderResult>> paging(ExportOrderSearch search);
      RpcResult<ExportItemWritingReport> preExport(InputParams inputParams);
      RpcResult<ExportItemWritingReport> importExcel(ExportItemImportSubmit submit);
      RpcResult<ExportItemSimpleVO> findMailById(IdParam params);
      RpcResult<List<SelectItemVO>> listForEndorsement();
      RpcResult<Long>discard(IdParam idParam);
      RpcResult<Long> delete(IdParam idParam);
      RpcResult<String> export(IdParam idParam);


      RpcResult<LoadTaskInfoDTO> importQuery();

      RpcResult<String> importExcelFile(ImportSubmitParam importWebForm);

      RpcResult<String> importConfirm();

      RpcResult<ExportItemWritingReport> preImport(UrlParamById urlParam);

      RpcResult<ExportOrderDetailVO> detail(IdParam id);

      RpcResult<ListVO<ExportItemVO>> pagingItems(ExportOrderItemSearch search);

    @SoulClient(path = "/exportOrder/exportItems", desc = "表体导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    RpcResult<String> exportItems(ExportOrderItemSearch search);

    RpcResult<List<EndorsementVO>> listEndorsementById(IdParam params);

      RpcResult<String> generateEndorsement(IdParam params);
}
