package com.danding.cds.web.v2.bean.vo.res;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class ProcessTradeBookItemVO implements Serializable {

    @ApiModelProperty("id")
    private Long id;

    /**
     * 关联账册id
     */
    @ApiModelProperty(value = "关联账册id")
    private Long refBookId;

    /**
     * 序号
     */
    private Integer seqNo;

    /**
     * 表体类型 1-料件 2-成品
     */
    @ApiModelProperty(value = "表体类型 1-料件 2-成品")
    private Integer goodsType;

    /**
     * 料号
     */
    @ApiModelProperty(value = "料号")
    private String productId;

    /**
     * 商品编码
     */
    @ApiModelProperty(value = "商品编码")
    private String hsCode;

    /**
     * 商品名称
     */
    @ApiModelProperty(value = "商品名称")
    private String goodsName;

    /**
     * 规格型号
     */
    @ApiModelProperty(value = "规格型号")
    private String goodsModel;

    /**
     * 法定计量单位
     */
    @ApiModelProperty(value = "法定计量单位")
    private String legalUnit;
    private String legalUnitDesc;

    /**
     * 法定第二计量单位
     */
    @ApiModelProperty(value = "法定第二计量单位")
    private String legalSecondUnit;
    private String legalSecondUnitDesc;

    /**
     * 修改标志，默认3-增加
     */
    @ApiModelProperty(value = "修改标志，默认3-增加")
    private String modifyFlag;
    private String modifyFlagDesc;

    /**
     * 申报单价
     */
    @ApiModelProperty(value = "申报单价")
    private BigDecimal declareUnitPrice;

    /**
     * 币制
     */
    @ApiModelProperty(value = "币制")
    private String currency;
    private String currencyDesc;

    /**
     * 申报计量单位
     */
    @ApiModelProperty(value = "申报计量单位")
    private String declareUnit;
    private String declareUnitDesc;

    /**
     * 申报数量
     */
    @ApiModelProperty(value = "申报数量")
    private Long declareQty;

    /**
     * 征免方式，默认3-全免
     */
    @ApiModelProperty(value = "征免方式，默认3-全免")
    private String dutyExemptionMethod;
    private String dutyExemptionMethodDesc;

    /**
     * 企业执行标志，默认1-运行
     */
    @ApiModelProperty(value = "企业执行标志，默认1-运行")
    private Integer companyExecutionFlag;
    private String companyExecutionFlagDesc;

    /**
     * 重点商品标识，默认0-非重点商品
     */
    @ApiModelProperty(value = "重点商品标识，默认0-非重点商品")
    private Integer focusMark;
    private String focusMarkDesc;

    /**
     * 国别(地区)
     */
    @ApiModelProperty(value = "国别(地区)")
    private String countryRegion;
    private String countryRegionDesc;

    /**
     * 海关执行标志，默认1-正常执行
     */
    @ApiModelProperty(value = "海关执行标志，默认1-正常执行")
    private Integer customsExecutionFlag;
    private String customsExecutionFlagDesc;

    /**
     * 期初数量，默认0
     */
    @ApiModelProperty(value = "期初数量，默认0")
    private Integer initialQty;

    /**
     * 数量控制标志，默认2-不控制数量
     */
    @ApiModelProperty(value = "数量控制标志，默认2-不控制数量")
    private Integer qtyControlFlag;
    private String qtyControlFlagDesc;

    /**
     * 备注
     */
    @ApiModelProperty(value = "备注")
    private String remark;

    /**
     * 批准最大余数量
     */
    @ApiModelProperty(value = "批准最大余数量")
    private BigDecimal maxRemainQty;

    /**
     * 单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0
     */
    @ApiModelProperty(value = "单耗质疑标志 0-表示不质疑  1-表示质疑 默认为0")
    private Integer consumptionQuestionFlag;
    private String consumptionQuestionFlagDesc;

    /**
     * 磋商标志 0-表示未磋商  1-表示磋商中 默认为0
     */
    @ApiModelProperty(value = "磋商标志 0-表示未磋商  1-表示磋商中 默认为0")
    private Integer negotiationFlag;
    private String negotiationFlagDesc;
}
