package com.danding.cds.web.EntityWarehouse.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.v2.bean.vo.req.DelEntityWarehouseReqVO;
import com.danding.cds.v2.bean.vo.req.EntityWarehouseReqVO;
import com.danding.cds.v2.bean.vo.req.EntityWarehouseSearch;
import com.danding.cds.v2.bean.vo.req.UpdEntityWarehouseTagReqVo;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.result.RpcResult;

import java.util.List;

public interface EntityWarehouseWebRpc {

    /**
     * 分页查询
     * @param search
     * @return
     */
    RpcResult paging(EntityWarehouseSearch search);


    /**
     * 新增/编辑(禁用/启用)
     * @param reqVO
     * @return
     */
    RpcResult upsetEntityWarehouse(EntityWarehouseReqVO reqVO) throws ArgsErrorException;

    /**
     * 获取实体仓
     *
     * @return
     */
    RpcResult queryERPEntityWarehouse();

    RpcResult<List<SelectOptionVO<String>>> listEntityByCustomsBook(Long customsBookId);


    RpcResult<List<SelectOptionVO<String>>> listEntityByCustomsBookAuth();

    /**
     * 商品备案实体仓下拉列表
     *
     * @return
     */
    RpcResult<List<SelectOptionVO<String>>> listEntityWms();

    RpcResult<List<SelectOptionVO<String>>> listWarehouseErpCode();

    /**
     * 根据备案和口岸获取可添加的仓库列表
     *
     * @param recordCustomsId
     * @return
     */
    RpcResult<List<SelectOptionVO<String>>> listWarehouseByRecordCustomsId(Long recordCustomsId, String customsCode);

    /**
     * 实体仓删除
     *
     * @param reqVO
     * @return
     */
    RpcResult delEntityWarehouse(DelEntityWarehouseReqVO reqVO);

    RpcResult<String> syncRecordWarehouseCustomsBook(DelEntityWarehouseReqVO idParam);

    RpcResult<List<SelectOptionVO<Integer>>> listWarehouseTag();

    RpcResult<List<SelectOptionVO<String>>> listGoodSourceList();

    RpcResult updateTag(UpdEntityWarehouseTagReqVo reqVo);

    RpcResult<String> getWmsWarehouseCodeByErpCode(EntityWarehouseReqVO reqVO) throws ArgsErrorException;
}
