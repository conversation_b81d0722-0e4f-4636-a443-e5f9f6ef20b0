package com.danding.cds.web.message.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

@Data
public class MessageSearchVO {

    /**
     * 消息 id
     */
    private Long id;

    /**
     * 执行回传地址
     */
    private String notifyUrl;

    /**
     * 消息类型|冗余
     */
    private String type;
    private String typeDesc;

    /**
     * 业务编号|冗余
     */
    private String businessCode;

    /**
     * 多个消息标签
     */
    private String tagJson;

    /**
     * 执行状态
     */
    private Integer status;
    private String statusDesc;

    /**
     * 触发回传的初始参数
     */
    private String activeData;

    /**
     * 请求数据
     */
    private String requestData;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
