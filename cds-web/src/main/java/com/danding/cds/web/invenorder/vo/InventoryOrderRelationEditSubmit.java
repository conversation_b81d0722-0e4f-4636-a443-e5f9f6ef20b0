package com.danding.cds.web.invenorder.vo;

import com.danding.cds.exportorder.api.dto.ExportItemRecord;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@ApiModel
@Data
public class InventoryOrderRelationEditSubmit {

    @ApiModelProperty("核注清单ID")
    private Long id;

    @ApiModelProperty("新增列表")
    private List<InventoryOrderRelationDTO> addList = new ArrayList<>();

    @ApiModelProperty("删除列表")
    private List<InventoryOrderRelationDTO> deleteList = new ArrayList<>();
}
