package com.danding.cds.web.customsOrder.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.rpc.CustomsOrderCRpc;
import com.danding.cds.c.api.rpc.OrderCRpc;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.CustomsStat;
import com.danding.cds.customs.order.api.dto.CustomsOrderDTO;
import com.danding.cds.customs.order.api.dto.CustomsOrderExtra;
import com.danding.cds.customs.order.api.dto.CustomsOrderItem;
import com.danding.cds.customs.order.api.dto.CustomsOrderSearch;
import com.danding.cds.customs.order.api.service.CustomsOrderService;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.order.api.dto.CustomsOrderBaseInfoSubmit;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.web.customsOrder.vo.CustomsOrderResult;
import com.danding.cds.web.order.OrderLogManager;
import com.danding.cds.web.order.vo.CustomsOrderItemResult;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.encrypt.annotation.DataSecurity;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@RestController
@DubboService(timeout = 30000)
@Slf4j
public class CustomsOrderRpcImpl implements CustomsOrderRpc{
    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference
    private CustomsOrderService customsOrderService;
    @DubboReference
    private CustomsOrderCRpc customsOrderCRpc;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @Autowired
    private OrderLogManager orderLogManager;

    @DubboReference
    private OrderService orderService;
    @DubboReference
    private OrderCRpc orderCRpc;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @PostMapping("paging")
    @SoulClient(path = "/customsOrder/paging", desc = "海关订单分页查询")
    @DataSecurity
    public RpcResult<ListVO<CustomsOrderResult>> paging(@RequestBody CustomsOrderSearch search) {
        log.info("[op:CustomsOrderRpcImpl-paging] CustomsOrderSearch={}", search);
        ListVO<CustomsOrderDTO> dtoListVO;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            dtoListVO = customsOrderCRpc.paging(search);
        } else {
            dtoListVO = customsOrderService.paging(search);
        }
        ListVO<CustomsOrderResult> result = new ListVO<>();
        result.setPage(dtoListVO.getPage());
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        Map<String,GoodsRecordDTO> recordDTOMap = new HashMap<>();
        result.setDataList(dtoListVO.getDataList().stream().map((CustomsOrderDTO customsOrderDTO) ->{
            CustomsOrderResult customsOrderResult = new CustomsOrderResult();
            try {
                OrderDTO orderDTO;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    orderDTO = orderCRpc.findBySnSection(customsOrderDTO.getOrderSn());
                } else {
                    orderDTO = orderService.findBySnSection(customsOrderDTO.getOrderSn());
                }
                customsOrderResult.setDeclareOrderNo(customsOrderDTO.getDeclareOrderNo());
                customsOrderResult.setOutOrderNo(orderDTO.getOutOrderNo());
                customsOrderResult.setOrderId(String.valueOf(orderDTO.getId()));
                customsOrderResult.setCreateTime(new DateTime(customsOrderDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
                CompanyDTO ebp = getCompany(orderDTO.getEbpId(),companyDTOMap);
                customsOrderResult.setEbpName(ebp.getName());
                customsOrderResult.setStatus(CustomsActionStatus.getEnum(customsOrderDTO.getStatus()).getValue());
                customsOrderResult.setId(String.valueOf(customsOrderDTO.getId()));
                customsOrderResult.setStatusDesc(CustomsActionStatus.getEnum(customsOrderDTO.getStatus()).getDesc());
                customsOrderResult.setCustomsStatusDesc(CustomsStat.getEnum(customsOrderDTO.getCustomsStatus()).getDesc());
                customsOrderResult.setCustomsDetail(customsOrderDTO.getCustomsDetail());
                if (customsOrderDTO.getLastDeclareTime() != null){
                    customsOrderResult.setDeclareTime(new DateTime(customsOrderDTO.getLastDeclareTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                if (customsOrderDTO.getCreateTime() != null){
                    customsOrderResult.setCreateAt(new DateTime(customsOrderDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
                }
                if (customsOrderDTO.getLastCustomsTime() != null){
                    customsOrderResult.setReceiveTime(new DateTime(customsOrderDTO.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                    if (customsOrderDTO.getStatus().equals(CustomsActionStatus.DEC_SUCCESS.getValue()) &&
                            CustomsStat.CUSTOMS_RECEIVE.getValue().equals(customsOrderDTO.getCustomsStatus())){
                        customsOrderResult.setCustomsPassTo(new DateTime(customsOrderDTO.getLastCustomsTime()).toString("yyyy-MM-dd HH:mm:ss"));
                    }
                }
                CompanyDTO agentCompany = getCompany(customsOrderDTO.getAgentCompanyId(),companyDTOMap);
                customsOrderResult.setAgentCompanyName(agentCompany.getName());
                CompanyDTO ebcCompany = getCompany(customsOrderDTO.getEbcId(),companyDTOMap);
                customsOrderResult.setEbcName(ebcCompany.getName());
                customsOrderResult.setCustoms(CustomsDistrictEnum.getEnum(customsOrderDTO.getCustoms()).getDesc());
                List<CustomsOrderItemResult> itemResults = new ArrayList<>();
                BigDecimal goodsTotal = BigDecimal.ZERO;
                if (!StringUtils.isEmpty(customsOrderDTO.getItemJson())) {
                    List<CustomsOrderItem> itemList = JSON.parseArray(customsOrderDTO.getItemJson(),CustomsOrderItem.class);

                    for (CustomsOrderItem orderItem : itemList) {
                        CustomsOrderItemResult itemResult = new CustomsOrderItemResult();
                        itemResult.setGoodsNo(orderItem.getGoodsNo());
                        itemResult.setRecordNo(orderItem.getRecordNo());
                        itemResult.setRecordGnum(orderItem.getGoodsSeqNo());
                        itemResult.setHsCode(orderItem.getHsCode());
                        itemResult.setOriginCountry(orderItem.getOriginCountry());
                        itemResult.setUnitPrice(orderItem.getUnitPrice());
                        itemResult.setGoodsCount(orderItem.getGoodsCount());
                        itemResult.setGoodsAmount(orderItem.getUnitPrice().multiply(new BigDecimal(orderItem.getGoodsCount())));
                        if (!LongUtil.isNone(orderItem.getBookId()) && !StringUtils.isEmpty(orderItem.getRecordNo())){
                            GoodsRecordDTO goodsRecordDTO = getRecord(orderItem.getBookId(),orderItem.getRecordNo(),recordDTOMap);
                            if (goodsRecordDTO != null){
                                itemResult.setName(goodsRecordDTO.getGoodsRecordName());
                                itemResult.setBarcode(goodsRecordDTO.getBarCode());
                            }
                        }
                        itemResults.add(itemResult);
                        goodsTotal = goodsTotal.add(orderItem.getUnitPrice().multiply(new BigDecimal(orderItem.getGoodsCount())));
                    }
                }
                customsOrderResult.setItemList(itemResults);
                customsOrderResult.setTotalAmount(goodsTotal.add(customsOrderDTO.getFreight()));
                customsOrderResult.setPaidAmount(goodsTotal.add(customsOrderDTO.getFreight()).add(customsOrderDTO.getTax()).subtract(customsOrderDTO.getDiscount()));
                customsOrderResult.setFreight(customsOrderDTO.getFreight());
                customsOrderResult.setTax(customsOrderDTO.getTax());
                customsOrderResult.setDiscount(customsOrderDTO.getTax());
                customsOrderResult.setBuyerIdNumber(customsOrderDTO.getBuyerIdNumber());
                customsOrderResult.setBuyerName(customsOrderDTO.getBuyerName());
                if (!StringUtils.isEmpty(customsOrderDTO.getExtraJson())) {
                    CustomsOrderExtra extra = JSON.parseObject(customsOrderDTO.getExtraJson(),CustomsOrderExtra.class);
                    customsOrderResult.setConsigneeProvince(extra.getConsigneeProvince());
                    customsOrderResult.setConsigneeCity(extra.getConsigneeCity());
                    customsOrderResult.setConsigneeDistrict(extra.getConsigneeDistrict());
                }

                customsOrderResult.setConsigneeAddress(customsOrderDTO.getConsigneeAddress());
                CompanyDTO logisticsCompany = getCompany(customsOrderDTO.getLogisticsCompanyId(),companyDTOMap);
                customsOrderResult.setLogisticsCompanyName(logisticsCompany.getName());
                CompanyDTO payCompany = getCompany(customsOrderDTO.getPayCompanyId(),companyDTOMap);
                customsOrderResult.setPayCompanyName(payCompany.getName());
                customsOrderResult.setDeclarePayNo(customsOrderDTO.getDeclarePayNo());
                customsOrderResult.setTradePayNo(customsOrderDTO.getTradePayNo());
                customsOrderResult.setTradeAt(new DateTime(customsOrderDTO.getTradeTime()).toString("yyyy-MM-dd HH:mm:ss"));
                customsOrderResult.setSenderName(customsOrderDTO.getSenderName());
                customsOrderResult.setConsigneeName(customsOrderDTO.getConsigneeName());
                customsOrderResult.setConsigneeTel(customsOrderDTO.getConsigneeTel());
                customsOrderResult.setConsigneeEmail(customsOrderDTO.getConsigneeEmail());
                customsOrderResult.setNote(customsOrderDTO.getNote());
    //            List<CustomsOrderLogResult> logResultList =  orderLogManager.builderOrder(customsOrderDTO);
    //            customsOrderResult.setLogList(logResultList);
            } catch (Exception e) {
                log.info("[op:CustomsOrderRpcImpl-paging] e", e);
            }
//            List<CustomsOrderLogResult> logResultList =  orderLogManager.builderOrder(customsOrderDTO);
//            customsOrderResult.setLogList(logResultList);
            return customsOrderResult;
            }).collect(Collectors.toList()));
            return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsOrder/save", desc = "保存订单信息")
    public RpcResult<String> save(CustomsOrderBaseInfoSubmit customsOrderBaseInfo) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            orderCRpc.customsOrderInfoUpdate(customsOrderBaseInfo);
        } else {
            orderService.customsOrderInfoUpdate(customsOrderBaseInfo);
        }
        return RpcResult.success("订单信息修改成功");
    }

    private CompanyDTO getCompany(Long id, Map<Long, CompanyDTO> companyDTOMap){
        if (companyDTOMap == null){
            companyDTOMap = new HashMap<>();
        }
        CompanyDTO companyDTO = companyDTOMap.get(id);
        if (companyDTO == null){
            companyDTO = companyService.findUnifiedCrossInfoById(id);
            companyDTOMap.put(companyDTO.getId(),companyDTO);
        }
        return companyDTO;
    }

    private GoodsRecordDTO getRecord(Long bookId, String recordNo,Map<String,GoodsRecordDTO> recordDTOMap){
        if (recordDTOMap == null){
            recordDTOMap = new HashMap<>();
        }
        GoodsRecordDTO recordDTO = recordDTOMap.get(bookId + "#" + recordNo);
        if (recordDTO == null){
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(bookId,recordNo);
            recordDTOMap.put(bookId + "#" + recordNo,goodsRecordDTO);
        }
        return recordDTO;
    }
}
