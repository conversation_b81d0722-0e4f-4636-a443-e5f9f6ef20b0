package com.danding.cds.web.suningFP;

import lombok.Data;

import javax.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"loadContents"})
@XmlRootElement(name = "loadContents")
public class SuNingFPLoadContents {
    @XmlElements({ @XmlElement(name = "loadContent", type = SuNingFPLoadContent.class) })
    private List<SuNingFPLoadContent> loadContents;
}
