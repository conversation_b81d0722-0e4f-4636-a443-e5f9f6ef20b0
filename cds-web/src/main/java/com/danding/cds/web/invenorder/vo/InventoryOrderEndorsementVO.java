package com.danding.cds.web.invenorder.vo;

import lombok.Data;

import java.util.Date;

@Data
public class InventoryOrderEndorsementVO {

    /**
     * 核注单id
     */
    private Long endorsementId;

    /**
     * 核注企业单号
     */
    private String endorsementSn;

    /**
     * 预录入核注编号
     */
    private String preOrderNo;

    /**
     * 核注清单编号
     */
    private String realEndorsementNo;

    /**
     * 核注单状态
     */
    private String endorsementStatus;
    private String endorsementStatusDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 企业核放单号
     */
    private String checklistSn;

    /**
     * 是否核扣账册
     */
    private String stockChangeEnable;
}
