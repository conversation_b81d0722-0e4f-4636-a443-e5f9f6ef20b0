package com.danding.cds.web.message.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.cds.message.api.dto.MessageTaskDTO;
import com.danding.cds.message.api.enums.MessageTaskStatus;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

@Slf4j
@Component
public class MessageTaskExecuteJob extends IJob<PERSON>andler {

    @DubboReference
    private MessageService messageService;

    private boolean lock = false;

    @Override
    @XxlJob(value = "MessageTaskExecuteJob", enableTenant = true)
    public ReturnT<String> execute(String s) throws Exception {
        if (lock){
            XxlJobLogger.log("thread is lock");
            return ReturnT.SUCCESS;
        }
        lock = true;
        try {
            Long startTime = DateTime.now().minusMinutes(10).getMillis();
            Long endTime = DateTime.now().getMillis();
            if (!StringUtils.isEmpty(s)){
                JSONObject config = JSON.parseObject(s);
                if (!StringUtils.isEmpty(config.getString("start"))) {
                    startTime = config.getLong("start");
                }
                if (!StringUtils.isEmpty(config.getString("end"))) {
                    endTime = config.getLong("end");
                }
            }
            List<MessageTaskDTO> taskDTOList = messageService.listTaskByStatusAndTime(
                    MessageTaskStatus.INIT, startTime, endTime);
            XxlJobLogger.log("本次执行重试数量：{}", taskDTOList.size());
            List<MessageType> needRegenerateReqDataEnum = Arrays.asList(MessageType.ORDER_CUSTOMS_INVENTORY_TO_ERP,
                    MessageType.ORDER_CUSTOMS_CANCEL_TO_ERP,
                    MessageType.ORDER_CUSTOMS_REFUND_TO_ERP,
                    MessageType.ORDER_TAX_TO_ERP
            );
            for (MessageTaskDTO messageTaskDTO : taskDTOList) {
                MessageType typeEnum = MessageType.getEnum(messageTaskDTO.getType());
                if (needRegenerateReqDataEnum.contains(typeEnum)) {
                    messageService.regenerateReqData(messageTaskDTO);
                }
                messageService.executeTask(messageTaskDTO);
            }
        }catch (Exception e){
            log.error("处理异常：{}", e.getMessage(), e);
            XxlJobLogger.log("exception, cause={}", e.getMessage(), e);
        }finally {
            lock = false;
        }
        return ReturnT.SUCCESS;
    }
}
