package com.danding.cds.web.download.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DownloadItemVO {
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("报表名称")
    private String reportName;

    @ApiModelProperty("报表名称")
    private String reportType;

    @ApiModelProperty("开始时间")
    private String createTime;

    @ApiModelProperty("完成时间")
    private String finishTime;

    @ApiModelProperty("状态(99为处理完成)")
    private Integer status;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("下载次数")
    private Integer downloadCount;

    @ApiModelProperty("文件路径")
    private String filePath;
}
