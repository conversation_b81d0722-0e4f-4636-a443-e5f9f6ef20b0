package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.record.api.dto.param.EndangeredFactorSaveParam;
import com.danding.cds.record.api.dto.param.RecordRuleImportVO;
import com.danding.cds.record.api.service.EndangeredFactorService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 备案规则维护 导入 （原摄濒危 EndangeredFactor）
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_RECORD_RULE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E5%A4%87%E6%A1%88%E8%A7%84%E5%88%99%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "Sheet1", classes = RecordRuleImportVO.class),})
public class RecordRuleImportHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        List<RecordRuleImportVO> list = new ArrayList<>();
        EndangeredFactorService endangeredFactorService = this.getBean(EndangeredFactorService.class);
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);

        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("Sheet1")) {
                list = group.getDataList(RecordRuleImportVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("RecordRuleImportHandler-导入未获取到数据");
            return;
        }
        List<CustomsDictionaryDTO> ruleTypeEnums = customsDictionaryService.findByType(DataDictionaryTypeEnums.RECORD_RULE_TYPE.getValue());
        Map<String, String> ruleTypeMap = ruleTypeEnums.stream().collect(Collectors.toMap(CustomsDictionaryDTO::getName, CustomsDictionaryDTO::getCode));
        int index = 1;
        for (RecordRuleImportVO recordRuleImportVO : list) {
            StringBuilder errorMsg = new StringBuilder();
            if (StringUtil.isEmpty(recordRuleImportVO.getFactorName())) {
                errorMsg.append("成分名称不能为空;");
            }
            if (StringUtil.isEmpty(recordRuleImportVO.getRuleTypeName())) {
                errorMsg.append("备案规则类型不能为空;");
            } else if (!ruleTypeMap.containsKey(recordRuleImportVO.getRuleTypeName())) {
                errorMsg.append("备案规则类型不存在;");
            }
            if (errorMsg.length() > 0) {
                this.callbackData(false, index++, errorMsg.toString(), recordRuleImportVO);
                continue;
            }
            try {
                EndangeredFactorSaveParam param = new EndangeredFactorSaveParam();
                param.setEndangeredDesc(recordRuleImportVO.getFactorName());
                param.setRuleType(ruleTypeMap.get(recordRuleImportVO.getRuleTypeName()));
                endangeredFactorService.saveOne(param);
                this.callbackData(true, index++, null, recordRuleImportVO);
            } catch (Exception e) {
                if (e instanceof ArgsErrorException) {
                    ArgsErrorException ex = (ArgsErrorException) e;
                    this.callbackData(false, index++, ex.getErrorMessage(), recordRuleImportVO);
                    log.error("RecordRuleImportHandler-导入异常{}", ex.getErrorMessage(), e);
                } else {
                    this.callbackData(false, index++, "系统异常", recordRuleImportVO);
                    log.error("RecordRuleImportHandler-导入异常{}", e.getMessage(), e);
                }
            }
        }

    }

}
