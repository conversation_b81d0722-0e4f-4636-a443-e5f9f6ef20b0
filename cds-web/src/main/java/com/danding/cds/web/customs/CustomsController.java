package com.danding.cds.web.customs;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.order.api.dto.CustomsStatusMappingDTO;
import com.danding.cds.order.api.service.CustomsStatusMappingService;
import com.danding.cds.order.api.service.OrderService;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/28 11:12
 * @Description:
 */
@Api(tags = "海关信息管理")
@RestController
@RequestMapping("/customs")
@Slf4j
public class CustomsController {

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsStatusMappingService customsStatusMappingService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private OrderService orderService;


    @PostMapping("/accept")
    public String accept(String data){
        orderService.acceptCustomsSupport(data);
        return "接收成功";
    }


    @ApiOperation(value = "获取所有口岸")
    @GetMapping("/listDistrict")
    public List<SelectItemVO> listDistrict() {
        List<SelectItemVO> result = Arrays.stream(CustomsDistrictEnum.values()).filter((CustomsDistrictEnum item) -> {
            return !item.equals(CustomsDistrictEnum.NULL);
        }).map((CustomsDistrictEnum item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getCode());
            optionDTO.setName(item.getDesc());
            return optionDTO;
        }).collect(Collectors.toList());
        return result;
    }

    @ApiOperation(value = "获取所有国家")
    @GetMapping("/listCountry")
    public List<SelectItemVO> listCountry() {
        List<CustomsCountryDTO> dataList = customsCountryService.listAll();
        List<SelectItemVO> result = new ArrayList<>();
        for (CustomsCountryDTO item : dataList) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return result;
    }

    @ApiOperation(value = "获取所有币制单位")
    @GetMapping("/listCurrency")
    public List<SelectItemVO> listCurrency() {
        List<CustomsCurrencyDTO> dataList = customsCurrencyService.listAll();
        List<SelectItemVO> result = new ArrayList<>();
        for (CustomsCurrencyDTO item : dataList) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return result;
    }

    @ApiOperation(value = "获取所有单位")
    @GetMapping("/listUom")
    public List<SelectItemVO> listUom() {
        List<CustomsUomDTO> dataList = customsUomService.listAll();
        List<SelectItemVO> result = new ArrayList<>();
        for (CustomsUomDTO item : dataList) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getCode());
            optionDTO.setName(item.getName());
            result.add(optionDTO);
        }
        return result;
    }

    @ApiOperation(value = "获取所有HS")
    @GetMapping("/listHs")
    public List<SelectItemVO> listHs() {
        List<CustomsHsDTO> dataList = customsHsService.listEnable();
        List<SelectItemVO> result = new ArrayList<>();
        for (CustomsHsDTO item : dataList) {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getHsCode());
            optionDTO.setName(item.getHsName());
            result.add(optionDTO);
        }
        return result;
    }

    @ApiOperation(value = "导入状态映射表")
    @PostMapping("/importStatusMapping")
    public Response<String> importStatusMapping(MultipartFile file) {
        ImportParams importParams = new ImportParams();
        // 数据处理
        importParams.setTitleRows(1);
        importParams.setHeadRows(1);
        try {
            ExcelImportResult<StatusMappingExcel> result = ExcelImportUtil.importExcelMore(
                    file.getInputStream(),
                    StatusMappingExcel.class,
                    importParams);
            List<StatusMappingExcel> successList = result.getList();
            for (StatusMappingExcel excel : successList) {
                CustomsStatusMappingDTO mappingDTO = new CustomsStatusMappingDTO();
                if ("1".equals(excel.getEnableFlag())){
                    CustomsStatusMappingDTO old = customsStatusMappingService.findByCode(excel.getCode());

                    CustomsStatusMappingDTO model = new CustomsStatusMappingDTO();
                    model.setAction(excel.getAction());
                    model.setCode(excel.getCode());
                    model.setDetail(excel.getDetail());
                    if ("申报完成".equals(excel.getStatus())){
                        model.setStatus(CustomsActionStatus.DEC_SUCCESS.getValue());
                        model.setExceptionFlag(false);
                    }else if ("申报终止".equals(excel.getStatus())){
                        model.setStatus(CustomsActionStatus.DEC_FAIL.getValue());
                        if ("是".equals(excel.getExceptionFlag())){
                            model.setExceptionFlag(true);
                        }else {
                            model.setExceptionFlag(false);
                        }
                    }else {
                        model.setStatus(CustomsActionStatus.DEC_ING.getValue());
                        model.setExceptionFlag(false);
                    }
                    model.setNote(excel.getNote());
                    if (old == null){
                        customsStatusMappingService.create(model);
                    }else {
                        model.setId(old.getId());
                        customsStatusMappingService.update(model);
                    }
                }
            }
            return new Response<>("导入成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return new Response<>("导入异常");
        }
    }
}