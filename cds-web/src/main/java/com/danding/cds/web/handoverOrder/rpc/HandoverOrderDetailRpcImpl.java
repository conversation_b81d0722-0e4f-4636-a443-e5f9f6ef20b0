package com.danding.cds.web.handoverOrder.rpc;

import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailParam;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailVO;
import com.danding.cds.handoverOrder.api.enums.HandoverDetailStatus;
import com.danding.cds.handoverOrder.api.service.HandoverOrderDetailService;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * <AUTHOR>
 * @description
 * @date 2021/12/21
 */
@Slf4j
@DubboService
public class HandoverOrderDetailRpcImpl implements HandoverOrderDetailRpc{
    // TODO: 2021/12/22  BaseDataConsumer
    @DubboReference
    private HandoverOrderDetailService handoverOrderDetailService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @Override
    @SoulClient(path = "/handoverOrderDetail/paging",desc = "交接单明细分页查询")
    public RpcResult paging(HandoverOrderDetailParam param) {
        try {
            ListVO<HandoverOrderDetailVO> result = handoverOrderDetailService.paging(param);
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("交接单明细分页查询失败:" + e.getMessage());
            return RpcResult.error("交接单明细分页查询失败:" + e.getMessage());
        }
    }

    @SoulClient(path = "/handoverOrderDetail/getStatus",desc = "下拉框状态")
    @Override
    public RpcResult getStatus(){
        return RpcResult.success(EnumUtils.build(HandoverDetailStatus.class, "value", "desc"));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/handoverOrderDetail/excelExport", desc = "交接单明细导出")
    public RpcResult<String> excelExport(HandoverOrderDetailParam param) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    param, ReportType.HONDOVER_ORDER_DETAIL_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

}
