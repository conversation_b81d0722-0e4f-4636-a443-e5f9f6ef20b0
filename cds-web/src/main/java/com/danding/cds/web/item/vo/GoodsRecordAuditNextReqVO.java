package com.danding.cds.web.item.vo;

import com.danding.cds.common.annotations.UcRoleAccountBookIdList;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class GoodsRecordAuditNextReqVO implements Serializable {

    /**
     * 备案id
     */
    private Long recordId;

    /**
     * 口岸编码
     */
    private String customsCode;

    /**
     * 账册ID列表
     */
    @UcRoleAccountBookIdList
    private List<Long> accountBookIdList;
}
