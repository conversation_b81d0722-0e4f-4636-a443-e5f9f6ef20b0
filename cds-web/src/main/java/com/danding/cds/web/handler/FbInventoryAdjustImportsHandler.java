package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.download.api.vo.FbInventoryAdjustExcelVO;
import com.danding.cds.v2.api.FbInventoryService;
import com.danding.cds.v2.bean.vo.req.FbInventoryAdjustReqVo;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description: 非保库存调整
 * @date 2023/2/22 20:35
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_FB_INVENTORY_ADJUST", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E8%B0%83%E6%95%B4%E5%BA%93%E5%AD%98%E5%AF%BC%E5%85%A5v1.0.xls",
        groups = {@ParkImportsHandler.Group(name = "库存调整导入", classes = FbInventoryAdjustExcelVO.class),})
public class FbInventoryAdjustImportsHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<FbInventoryAdjustExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("库存调整导入")) {
                list = group.getDataList(FbInventoryAdjustExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        FbInventoryService fbInventoryService = this.getBean(FbInventoryService.class);
        int index = 1;
        for (FbInventoryAdjustExcelVO excelVO : list) {
            String result = this.doValidator(excelVO);
            ImportResultResVo resultResVo = new ImportResultResVo();
            if (StringUtils.hasText(result)) {
                resultResVo.setFlag(false);
                resultResVo.setReason(result);
            } else {
                FbInventoryAdjustReqVo reqVo = ConvertUtil.beanConvert(excelVO, FbInventoryAdjustReqVo.class);
                resultResVo = fbInventoryService.checkAndImportAdjust(reqVo);
                log.info("料号:{} 库存调整导入后的返回结果数据{}", excelVO.getProductId(), resultResVo.toString());
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }
}
