package com.danding.cds.web.v2.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
public class InvOrderItemCompareResVO implements Serializable {

    /**
     * 是否存在差异
     */
    private Boolean exitsDiff = false;

    /**
     * 是否存在比对失败
     */
    private Boolean exitsFail = false;

    /**
     * 统一料号
     */
    private String productId;

    /**
     * 当前表体
     */
    private CompareItem item;

    /**
     * 比较表体
     */
    private CompareItem compareItem;

    /**
     * 商品备案/账册库存
     */
    private CompareItem recordItem;

    @Data
    public static class CompareItem implements Serializable {

        /**
         * id
         */
        private Long id;

        /**
         * HS编码
         */
        private String hsCode;

        /**
         * 原产国（地区）
         */
        private String originCountry;
        private String originCountryName;

        /**
         * 商品名称
         */
        private String goodsName;

        /**
         * 申报单位
         */
        private String unit;
        private String unitName;

        /**
         * 申报数量
         */
        private BigDecimal declareUnitQfy;

        /**
         * 法定数量（总）
         */
        private BigDecimal firstUnitQfy;

        /**
         * 法定计量单位
         */
        private String firstUnit;
        private String firstUnitName;

        /**
         * 第二法定数量（总）
         */
        private BigDecimal secondUnitQfy;

        /**
         * 法定第二计量单位
         */
        private String secondUnit;
        private String secondUnitName;

        /**
         * 币制
         */
        private String currency;
        private String currencyName;

        /**
         * 申报单价
         */
        private BigDecimal declarePrice;

        /**
         * 申报总价
         */
        private BigDecimal declareTotalPrice;

        /**
         * 单据来源
         */
        private Integer channel;

        /**
         * 数据来源
         */
        private String source;

        /**
         * 总毛重
         */
        private BigDecimal totalGrossWeight;

        /**
         * 总净重
         */
        private BigDecimal totalNetWeight;
    }
}
