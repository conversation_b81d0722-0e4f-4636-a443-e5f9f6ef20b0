package com.danding.cds.web.checklist.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.checklist.api.dto.ChecklistDTO;
import com.danding.cds.checklist.api.dto.ChecklistSearch;
import com.danding.cds.checklist.api.dto.ChecklistSubmit;
import com.danding.cds.checklist.api.dto.ChecklistTrackLogDTO;
import com.danding.cds.checklist.api.enums.ChecklistBindType;
import com.danding.cds.checklist.api.enums.ChecklistStatusEnum;
import com.danding.cds.checklist.api.enums.ChecklistType;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.OTMCItemExcelVo;
import com.danding.cds.endorsement.api.dto.EndorsementItemDTO;
import com.danding.cds.endorsement.api.enums.EndorsementBussiness;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.vo.req.ChecklistAssociateItemReqVo;
import com.danding.cds.v2.bean.vo.req.ChecklistItemSaveReqVo;
import com.danding.cds.v2.bean.vo.res.CheckListViewResVo;
import com.danding.cds.v2.bean.vo.res.ChecklistAssociateItemResVo;
import com.danding.cds.vehicle.api.dto.CheckListVehicleDTO;
import com.danding.cds.vehicle.api.service.CheckListVehicleService;
import com.danding.cds.web.checklist.controller.ChecklistOrderController;
import com.danding.cds.web.checklist.rpc.param.RpcIdParam;
import com.danding.cds.web.checklist.rpc.param.RpcModifyStatusParam;
import com.danding.cds.web.checklist.vo.ChecklistFinishVO;
import com.danding.cds.web.checklist.vo.ChecklistPagingResult;
import com.danding.cds.web.checklist.vo.EndorsementItemVO;
import com.danding.cds.web.checklist.vo.PagingStatusCountVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @menu 核注核放接口
 */
@Slf4j
@DubboService
@RestController
public class ChecklistOrderRpcImpl implements ChecklistOrderRpc {
    @Autowired
    private ChecklistOrderController checklistOrderController;
    @DubboReference
    private ChecklistService checklistService;
    @DubboReference
    private CheckListVehicleService checkListVehicleService;
    @DubboReference
    private EndorsementService endorsementService;
    @DubboReference
    private DownloadProcessService downloadProcessService;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @Override
    @SoulClient(path = "/checklist/listStatus", desc = "获取所有核放状态")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO>> listStatus() {
        List<SelectOptionVO> result = Arrays.stream(ChecklistStatusEnum.values())
                .filter((ChecklistStatusEnum item) -> !item.equals(ChecklistStatusEnum.NULL))
                .map((ChecklistStatusEnum item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/modify/status/list", desc = "获取手动修改状态")
    @PostMapping(value = "/modify/status/list")
    public RpcResult<List<SelectOptionVO>> listModifyStatus(String type) {
        if (StringUtils.equals(type, "Checklist")) { // 核放状态
            List<SelectOptionVO> result = Arrays.asList(ChecklistStatusEnum.CREATED, ChecklistStatusEnum.STORED,
                    ChecklistStatusEnum.AUDITING, ChecklistStatusEnum.EXCEPTION).stream()
                    .map((ChecklistStatusEnum item) -> {
                        SelectOptionVO optionDTO = new SelectOptionVO();
                        optionDTO.setId(item.getCode());
                        optionDTO.setName(item.getDesc());
                        return optionDTO;
                    }).collect(Collectors.toList());
            return RpcResult.success(result);
        }
        if (StringUtils.equals(type, "Endorsement")) { // 核注状态
            List<SelectOptionVO> result = Arrays.asList(EndorsementOrderStatus.INIT, EndorsementOrderStatus.STORAGED,
                    EndorsementOrderStatus.EXAMINE, EndorsementOrderStatus.EXCEPTION).stream()
                    .map((EndorsementOrderStatus item) -> {
                        SelectOptionVO optionDTO = new SelectOptionVO();
                        optionDTO.setId(item.getCode());
                        optionDTO.setName(item.getDesc());
                        return optionDTO;
                    }).collect(Collectors.toList());
            return RpcResult.success(result);
        }
        if (StringUtils.equals(type, "Payment")) { // 支付单状态
            List<SelectOptionVO> result = new ArrayList<>();
            SelectOptionVO waitDeclare = new SelectOptionVO(1, "待申报");
            SelectOptionVO fail = new SelectOptionVO(3, "申报失败");
            result.add(waitDeclare);
            result.add(fail);
            return RpcResult.success(result);
        }
        return new RpcResult<>();
    }

    @Override
    @SoulClient(path = "/checklist/listType", desc = "获取所有类型")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO>> listType() {
        List<SelectOptionVO> result = Arrays.stream(ChecklistType.values())
                .filter((ChecklistType item) -> !item.equals(ChecklistType.NULL))
                .map((ChecklistType item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 获取所有绑定类型
     *
     * @return: RpcResult<List < SelectOptionVO>>
     * @data: 2021/11/15
     */
    @Override
    @SoulClient(path = "/checklist/bindType", desc = "获取所有绑定类型")
    public RpcResult<List<SelectOptionVO>> bindType() {
        List<SelectOptionVO> result = Arrays.stream(ChecklistBindType.values())
                .map((ChecklistBindType item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/checklist/check-list-type-map-bussiness", desc = "获取核放单与清单的映射关系")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Response<String>> checkListTypeMapEndorsementBussiness() {
        Map<Integer, EndorsementBussiness[]> map = ChecklistType.getCheckListTypeMapEndorsementBussiness();
        Map<Integer, String[]> toMap = new HashMap<>();
        map.forEach((key, value) ->
        {
            String arrays[] = Arrays.stream(value).map(s -> {
                return s.getCode();
            }).collect(Collectors.toList()).toArray(new String[]{});
            toMap.put(key, arrays);
        });
        return RpcResult.success(new Response<String>(JSON.toJSONString(toMap)));
    }

    @Override
    @SoulClient(path = "/checklist/listVehicle", desc = "获取全部车辆信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO>> listVehicle() {
        List<CheckListVehicleDTO> dtoList = checkListVehicleService.listAll();
        return RpcResult.success(dtoList.stream().map(dto -> {
            SelectOptionVO selectItemVO = new SelectOptionVO();
            selectItemVO.setName(dto.getVehicleLicensePlate());
            selectItemVO.setId(dto.getId());
            return selectItemVO;
        }).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/checklist/getVehicleById", desc = "获取全部车辆信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<CheckListVehicleDTO> getVehicleById(RpcIdParam param) {
        return RpcResult.success(checkListVehicleService.findById(param.getId()));
    }

    @Override
    @SoulClient(path = "/checklist/create", desc = "新增核放单")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Long> create(ChecklistSubmit submit) {
        try {
            Long id = checklistOrderController.create(submit);
            return RpcResult.success("新增成功");
        } catch (ArgsErrorException e) {
            log.error("upset error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("upset error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/checklist/upset", desc = "新增|更新核放单")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Long> upset(ChecklistSubmit submit) {
        try {
            Long id = checklistOrderController.upset(submit);
            return RpcResult.success(id);
        } catch (ArgsErrorException e) {
            log.error("upset error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("upset error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 绑定核注单
     *
     * @param submit
     * @return
     */
    @Override
    @SoulClient(path = "/checklist/bindEndorsement", desc = "绑定核放单")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<String> bindEndorsement(ChecklistSubmit submit) {
        try {
            checklistOrderController.bindEndorsement(submit);
            return RpcResult.success("绑定成功");
        } catch (ArgsErrorException e) {
            log.error("bindEndorsement error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("bindEndorsement error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/checklist/retryException", desc = "推送核放|重推核放")
    public RpcResult<Response<String>> push(RpcIdParam param) {
        try {
            checklistService.push(param.getId());
            return RpcResult.success("推送成功");
        } catch (ArgsErrorException e) {
            log.error("upset error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("upset error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/checklist/pushTemporaryStorage", desc = "推送核放|重推核放 (暂存)")
    public RpcResult<Response<String>> pushTemporaryStorage(RpcIdParam param) {
        return RpcResult.success(checklistOrderController.pushTemporaryStorage(param.getId()));
    }

    @Override
    @SoulClient(path = "/checklist/paging", desc = "分页查询")
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<ChecklistPagingResult>> paging(ChecklistSearch search) {
        // 这里核放单列表，创建核放单的时候程序默认给账册0L，所以直接赋值进去
        final List<Long> roleAccountBookIdList = search.getRoleAccountBookIdList();
        if (CollectionUtils.isEmpty(roleAccountBookIdList)) {
            search.setRoleAccountBookIdList(Collections.singletonList(0L));
        } else {
            roleAccountBookIdList.add(0L);
        }
        return RpcResult.success(checklistOrderController.paging(search));
    }

    @Override
    @SoulClient(path = "/checklist/finish", desc = "手动完成核放")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Response<String>> finish(ChecklistFinishVO checklistFinishVO) {
        return RpcResult.success(checklistOrderController.finish(checklistFinishVO));
    }

    @Override
    @SoulClient(path = "/checklist/discard", desc = "作废")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Long> discard(IdParam idParam) {
        return RpcResult.success(checklistOrderController.discard(idParam));
    }

    @Override
    @SoulClient(path = "/checklist/handler-check", desc = "手动过卡")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<Response<String>> handlerCheck(IdsParam checkIdsParam) {
        return RpcResult.success(checklistOrderController.handlerCheck(checkIdsParam.getIds()));
    }

    @Override
    @SoulClient(path = "/checklist/loadEndorsementItem", desc = "查看核放清单表体信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<EndorsementItemVO>> loadEndorsementItem(RpcIdParam idParam) {
        return RpcResult.success(checklistOrderController.loadEndorsementItem(idParam.getId()));
    }

    /**
     * @param idParam
     * @return
     * @description: 查看核放单详情
     */
    @Override
    @SoulClient(path = "/checklist/checklistViewInfo", desc = "查看核放单信息")
    @RequestMapping("checklist/checklistViewInfo")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<CheckListViewResVo> viewCheckList(RpcIdParam idParam) {
        try {
            CheckListViewResVo checkListViewResVo = checklistService.viewCheckList(idParam.getId());
            return RpcResult.success(checkListViewResVo);
        } catch (ArgsErrorException e) {
            log.error("viewCheckList error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getMessage());
        } catch (Exception e) {
            log.error("viewCheckList error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * @description: 核放单表体关联
     * @param: ChecklistAssociateItemReqVo
     * @return: RpcResult<ChecklistAssociateItemResVo>
     * @data: 2021/11/15
     */
    @Override
    @SoulClient(path = "/checklist/checklistAssociateItem", desc = "核放单表体关联")
    @RequestMapping("checklist/checklistAssociateItem")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<ChecklistAssociateItemResVo> associateItemBySerialNumber(ChecklistAssociateItemReqVo reqVo) {
        ChecklistAssociateItemResVo resVo;
        try {
            resVo = checklistOrderController.associateItemBySerialNumber(reqVo);
        } catch (ArgsErrorException ex) {
            log.warn("associateItemByGoodsSeqNo error", ex);
            return RpcResult.error(ex.getErrorMessage());
        }
        return RpcResult.success(resVo);
    }

    @Override
    @SoulClient(path = "/checklist/viewChecklistItem", desc = "核放单表体查看/编辑")
    @RequestMapping("checklist/viewChecklistItem")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<ChecklistAssociateItemResVo> viewChecklistItem(RpcIdParam idParam) {
        ChecklistAssociateItemResVo resVo;
        try {
            resVo = checklistOrderController.viewChecklistItem(idParam.getId());
        } catch (ArgsErrorException ex) {
            log.warn("viewChecklistItem error", ex);
            return RpcResult.error(ex.getErrorMessage());
        }
        return RpcResult.success(resVo);
    }

    @Override
    @SoulClient(path = "/checklist/deleteChecklistItem", desc = "核放单表体删除")
    @RequestMapping("checklist/deleteChecklistItem")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<String> deleteChecklistItem(RpcIdParam idParam) {
        try {
            checklistOrderController.deleteChecklistItem(idParam.getId());
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
        return RpcResult.success("删除成功");
    }

    /**
     * @description: 保存一票多车核放单表体
     * @param: reqVo
     * @return: RpcResult<String>
     * @data: 2021/11/15
     */
    @Override
    @SoulClient(path = "/checklist/saveChecklistItem", desc = "保存一票多车核放单表体")
    @RequestMapping("checklist/saveChecklistItem")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<String> saveChecklistItem(ChecklistItemSaveReqVo reqVo) {
        try {
            checklistOrderController.saveChecklistItem(reqVo);
        } catch (ArgsErrorException ex) {
            log.warn("saveChecklistItem error", ex);
            return RpcResult.error(ex.getErrorMessage());
        }
        return RpcResult.success("保存成功");
    }


    /**
     * 核放单表体导入
     *
     * @param checklistId
     * @param endorsementId
     * @param vo
     * @return
     */
    @Override
    public ImportResultResVo saveChecklistItemExport(Long checklistId, Long endorsementId, OTMCItemExcelVo vo) {
        ImportResultResVo resVo = new ImportResultResVo();
        resVo.setFlag(true);
        String reason = null;
        try {
            ChecklistDTO checklistDTO = checklistService.findById(checklistId);
            EndorsementItemDTO endorsementItem = endorsementService.getEndorsementItem(endorsementId, vo.getSerialNumber());
            if (Objects.isNull(endorsementItem)) {
                reason = "新增核放单表体失败，输入的关联商品序号不存在";
            }
            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(checklistDTO.getAccountBookId(), endorsementItem.getProductId());
            if (Objects.isNull(goodsRecordDTO)) {
                reason = "未找到对应商品备案";
            }
            if (!Objects.isNull(reason)) {
                throw new ArgsErrorException(reason);
            }
            ChecklistItemSaveReqVo saveReqVo = new ChecklistItemSaveReqVo();
            saveReqVo.setChecklistId(checklistId)
                    .setEndorsementId(endorsementId)
                    .setEndorsementItemId(endorsementItem.getId())
                    .setGoodsRecordId(goodsRecordDTO.getId())
                    .setSerialNumber(vo.getSerialNumber())
                    .setDeclareUnitQfy(vo.getDeclareUnitQfy())
                    .setTotalGrossWeight(vo.getTotalGrossWeight())
                    .setTotalNetWeight(vo.getTotalNetWeight())
                    .setRemark(vo.getRemark());
            if (!Objects.isNull(endorsementItem.getGoodsSeqNo())) {
                CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findByBookIdAndSeqNoAndProId(checklistDTO.getAccountBookId(), endorsementItem.getGoodsSeqNo(), endorsementItem.getProductId());
                if (!Objects.isNull(customsBookItemDTO)) {
                    saveReqVo.setCustomsBookItemId(customsBookItemDTO.getId());
                }
            }
            checklistService.saveChecklistItem(saveReqVo);
        } catch (ArgsErrorException ex) {
            log.info("saveChecklistItemExport error", ex);
            resVo.setFlag(false);
            resVo.setReason(Objects.isNull(reason) ? ex.getErrorMessage() : reason);
        }
        return resVo;
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/checklist/itemExcelExport", desc = "核放单表体导出")
    public RpcResult<String> excelExport(RpcIdParam idParam) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    idParam.getId(), ReportType.CHECKLIST_ITEM_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 手动批量修改核放单状态
     *
     * @param param 待修改核放单传递参数
     * @return 操作结果
     * <AUTHOR>
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/checklist/manualUpdStatus", desc = "核放单状态手动修改")
    public RpcResult<String> manualUpdStatus(RpcModifyStatusParam param) {
        if (ObjectUtils.isEmpty(param)) {
            return RpcResult.error("参数为空");
        }
        for (Long id : param.getIdList()) {
            ChecklistDTO checklistDTO = new ChecklistDTO();
            BeanUtils.copyProperties(param, checklistDTO);
            checklistDTO.setId(id);
            checklistService.manualUpdStatus(checklistDTO);
        }
        return RpcResult.success("更新成功");
    }

    /**
     * 获取各状态的统计数量
     *
     * @param search
     * @return
     * @path /checklist/countPagingStatus
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/checklist/countPagingStatus", desc = "获取各状态的统计数量")
    public RpcResult<List<PagingStatusCountVO>> countPagingStatus(ChecklistSearch search) {
        try {
            Map<String, Integer> countMap = checklistService.countPagingStatus(search);
            Iterator<Map.Entry<String, Integer>> entryIterator = countMap.entrySet().iterator();
            List<PagingStatusCountVO> countList = new ArrayList<>();
            while (entryIterator.hasNext()) {
                Map.Entry<String, Integer> next = entryIterator.next();
                String status = next.getKey();
                Integer value = next.getValue();
                PagingStatusCountVO countVO = new PagingStatusCountVO();
                countVO.setStatus(status);
                countVO.setStatusDesc(ChecklistStatusEnum.getEnum(status).getDesc());
                countVO.setCount(value);
                countList.add(countVO);
            }
            return RpcResult.success(countList);
        } catch (ArgsInvalidException e) {
            log.error("获取统计数量失败 error={}", e.getErrorMessage(), e);
        }
        return RpcResult.error("获取统计数量失败");
    }

    @Override
    @SoulClient(path = "/checklist/listTrackLog", desc = "核放单轨迹日志列表")
    public RpcResult<List<ChecklistTrackLogDTO>> listTrackLog(Long checklistId) {
        List<ChecklistTrackLogDTO> checklistTrackLogDTOS = checklistService.listChecklistTrackLog(checklistId);
        return RpcResult.success(checklistTrackLogDTOS);
    }
}
