package com.danding.cds.web.v2.schedule;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.dto.GoodsRecordSearchCondition;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dto.RecordCustomsDTO;
import com.danding.cds.v2.service.AiRecommendHsCodeService;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.web.v2.bean.vo.req.AiRecommendHsCodeJobParam;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.xxl.job.core.log.XxlJobLogger;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * ai推荐 定时任务
 */
@Component
@Slf4j
public class AiRecommendSchedule {


    @DubboReference
    private RecordCustomsService recordCustomsService;

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private AiRecommendHsCodeService aiRecommendHsCodeService;

    @XxlJob(value = "AiRecommendHsCodeJob", enableTenant = false)
    public ReturnT<String> aiRecommendHsCodeJob(String s) {
        XxlJobLogger.log("aiRecommendHsCode start s={}", s);
        SimpleTenantHelper.setTenantId(1001L);
        AiRecommendHsCodeJobParam param = JSON.parseObject(s, AiRecommendHsCodeJobParam.class);
        if (param == null) {
            XxlJobLogger.log("参数type为空");
            return ReturnT.FAIL;
        }
        List<Long> recordIdList = new ArrayList<>();
        if (param.getType() == null) {
            XxlJobLogger.log("参数type为空");
            return ReturnT.FAIL;
        } else if ("all".equals(param.getType())) {
            GoodsRecordSearchCondition condition = new GoodsRecordSearchCondition();
            condition.setPageSize(500);
            condition.setCurrentPage(1);
            ListVO<GoodsRecordDTO> paging = goodsRecordService.paging(condition);
            recordIdList = paging.getDataList().stream().map(GoodsRecordDTO::getId).collect(Collectors.toList());
            PageResult page = paging.getPage();
            XxlJobLogger.log("aiRecommendHsCode start all currentPage={} totalPage={} recordIdList={}", condition.getCurrentPage(), page.getTotalPage(), recordIdList);
            for (Long recordId : recordIdList) {
                aiRecommendHsCodeService.init(recordId);
                XxlJobLogger.log("初始化 recordId={} 进度：{}/{}", recordId, recordIdList.indexOf(recordId) + 1, recordIdList.size());
            }
            while (page.getTotalPage() != condition.getCurrentPage()) {
                condition.setCurrentPage(condition.getCurrentPage() + 1);
                ListVO<GoodsRecordDTO> nextPaging = goodsRecordService.paging(condition);
                recordIdList = nextPaging.getDataList().stream().map(GoodsRecordDTO::getId).collect(Collectors.toList());
                XxlJobLogger.log("aiRecommendHsCode start all currentPage={} recordIdList={}", condition.getCurrentPage(), recordIdList);
                for (Long recordId : recordIdList) {
                    aiRecommendHsCodeService.init(recordId);
                    XxlJobLogger.log("初始化 recordId={} 进度：{}/{}", recordId, recordIdList.indexOf(recordId) + 1, recordIdList.size());
                }
            }
            return ReturnT.SUCCESS;
        } else if ("status".equals(param.getType())) {
            List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsService.findByStatus(param.getStatus());
            if (CollUtil.isNotEmpty(recordCustomsDTOList)) {
                recordCustomsDTOList.stream().map(RecordCustomsDTO::getRecordId).distinct().forEach(recordIdList::add);
            }
        } else if ("recordIdList".equals(param.getType())) {
            if (CollUtil.isNotEmpty(param.getRecordIdList())) {
                recordIdList = param.getRecordIdList();
            }
        } else {
            XxlJobLogger.log("参数type错误");
            return ReturnT.FAIL;
        }
        XxlJobLogger.log("aiRecommendHsCode start recordIdList={}", recordIdList);
        if (CollUtil.isEmpty(recordIdList)) {
            return ReturnT.SUCCESS;
        }
        for (Long recordId : param.getRecordIdList()) {
            aiRecommendHsCodeService.init(recordId);
            XxlJobLogger.log("初始化 recordId={} 进度：{}/{}", recordId, recordIdList.indexOf(recordId) + 1, recordIdList.size());
        }
        return ReturnT.SUCCESS;
    }
}
