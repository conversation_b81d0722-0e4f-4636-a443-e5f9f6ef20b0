package com.danding.cds.web.order.vo;

import com.danding.encrypt.annotation.DesensitizeField;
import com.danding.encrypt.constant.RegexConstant;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/14 16:15
 * @Description:
 */
@Data
@ApiModel
public class CustomsInventoryResult implements Serializable {

    @ApiModelProperty(value = "清单ID")
    private String id;

    @ApiModelProperty(value = "渠道订单号")
    private String outOrderNo;

    @ApiModelProperty(value = "清单状态 成功 100 失败 -1,该项不申报 0，其余都是待审申报或申报中")
    private Integer status = 0;

    @ApiModelProperty(value = "清单状态描述")
    private String statusDesc;

    @ApiModelProperty(value = "清关回执")
    private String customsStatusDesc;

    @ApiModelProperty(value = "清关回执详情")
    private String customsDetail;

    @ApiModelProperty(value = "出区状态：1：已出区 0：未出区")
    private Integer exitRegionStatus;

    @ApiModelProperty(value = "出区状态描述：1：已出区 0：未出区")
    private String exitRegionStatusDesc;

    @ApiModelProperty(value = "申报时间")
    private String declareTime;

    @ApiModelProperty(value = "回执时间")
    private String receiveTime;

    @ApiModelProperty(value = "电商企业")
    private String ebcName;

    @ApiModelProperty(value = "电商平台")
    private String ebpName;

    @ApiModelProperty(value = "担保企业")
    private String assureCompanyName;

    @ApiModelProperty(value = "运单号")
    private String logisticsNo;

    @ApiModelProperty(value = "承运商")
    private String logisticsCompanyName;

    @ApiModelProperty(value = "快递公司")
    private String expressName;

    @ApiModelProperty(value = "清单编号")
    private String inventoryNo;

    @ApiModelProperty(value = "账册编号")
    private String bookNo;

    @ApiModelProperty(value = "出库单号")
    private String outboundNo; // TODO：暂无

    @ApiModelProperty(value = "清关公司")
    private String agentCompanyName; // 申报企业

    @ApiModelProperty(value = "关区口岸")
    private String customs;

    @ApiModelProperty(value = "购买人电话")
    @DesensitizeField(regex = RegexConstant.PHONE,replace = RegexConstant.PHONE_REPLACE)
    private String buyerTelNumber;

    @ApiModelProperty(value = "订购人证件号码")
    @DesensitizeField(regex = RegexConstant.IDCARD,replace = RegexConstant.IDCARD_REPLACE)
    private String buyerIdNumber;

    @ApiModelProperty(value = "订购人姓名")
    @DesensitizeField(regex = RegexConstant.NAME,replace = RegexConstant.NAME_REPLACE)
    private String buyerName;

    @ApiModelProperty(value = "收件人省")
    private String consigneeProvince;

    @ApiModelProperty(value = "收件人市")
    private String consigneeCity;

    @ApiModelProperty(value = "收件人区")
    private String consigneeDistrict;

    @ApiModelProperty(value = "收件人街道")
    private String consigneeStreet;

    @ApiModelProperty(value = "收件人地址")
    @DesensitizeField(regex = RegexConstant.ADDRESS, replace = RegexConstant.ADDRESS_REPLACE)
    private String consigneeAddress;

    @ApiModelProperty(value = "仓储企业")
    private String areaCompanyName; // 区内企业

    @ApiModelProperty(value = "总金额")
    private BigDecimal totalPrice;

    @ApiModelProperty(value = "总净重")
    private BigDecimal totalNetWeight;

    @ApiModelProperty(value = "总毛重")
    private BigDecimal totalGrossWeight;

    @ApiModelProperty(value = "税费")
    private BigDecimal taxPrice;

    @ApiModelProperty(value = "应征关税")
    private BigDecimal customsTax;

    @ApiModelProperty(value = "应征消费税")
    private BigDecimal valueAddedTax;

    @ApiModelProperty(value = "应征增值税")
    private BigDecimal consumptionTax;

    @ApiModelProperty(value = "清单商品")
    private List<CustomsInventoryItemResult> itemList;
    @ApiModelProperty(value = "清单操作日志")
    private List<CustomsInventoryLogResult> logList;

    @ApiModelProperty(value = "申报单状态")
    private String orderStatusDesc;

    @ApiModelProperty(value = "实体仓名称")
    private String erpPhyWarehouseName;

    @ApiModelProperty(value = "申报单创建时间")
    private String orderCreateTime;

    @ApiModelProperty(value = "申报时间 customsOrderDTO.LastDeclareTime")
    private String orderDeclareTime;

    @ApiModelProperty(value = "运费")
    private BigDecimal feeAmount;

    @ApiModelProperty(value = "预扣税金")
    private BigDecimal taxFee;

    @ApiModelProperty(value = "收件人姓名")
    @DesensitizeField(regex = RegexConstant.NAME,replace = RegexConstant.NAME_REPLACE)
    private String consigneeName;

    @ApiModelProperty(value = "收件人电话")
    @DesensitizeField(regex = RegexConstant.PHONE,replace = RegexConstant.PHONE_REPLACE)
    private String consigneeTel;

    @ApiModelProperty(value = "退货后总税金")
    private BigDecimal totalRefundTax;

    @ApiModelProperty(value = "退货运单号")
    private String refundMailNo;

    @ApiModelProperty(value = "退货申报口岸描述")
    private String refundCustomCodeDesc;

    @ApiModelProperty("是否部分退  0否  1是")
    private String refundPartFlagDesc;

    @ApiModelProperty("备注")
    private String note;
    @ApiModelProperty("是否跨关区  0否  1是")
    private String refundCrossCustomsFlagDesc;

    @ApiModelProperty("是否包含非保赠品")
    private String containFbGifts;

    /**
     * 退货货品信息
     */
    private List<RefundGoodsInfo> refundGoodsInfo;

    /**
     * 取消单标记
     */
    private List<String> calloffOrderTagDescList;

    @Data
    public static class RefundGoodsInfo implements Serializable {

        /**
         * id
         */
        private Long id;
        /**
         * sku
         */
        private String sku;
        /**
         * 条码
         */
        private String barCode;
        /**
         * 商品货号
         */
        private String productId;
        /**
         * 货品名称
         */
        private String goodsName;
        /**
         * 正向申报数量
         */
        private String declareCount;
        /**
         * 退货数量
         */
        private Integer refundCount;
        /**
         * 异常信息
         */
        private String errorMsg;
    }
}
