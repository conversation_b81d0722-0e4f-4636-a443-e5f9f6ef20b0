package com.danding.cds.web.payChannel.rpc;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoDTO;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantCustomsInfoSubmit;
import com.danding.cds.web.payChannel.vo.PayMerchantCustomsInfoExcelVO;
import com.danding.cds.web.payChannel.vo.PayMerchantCustomsInfoResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.business.common.utils.DateUtils;
import com.danding.soul.client.common.result.RpcResult;
import io.swagger.annotations.ApiOperation;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/11/3 13:08
 * @Description:
 */
public interface PayMerchantCustomsInfoRpc {
    RpcResult<ListVO<PayMerchantCustomsInfoResult>> paging(PayMerchantCustomsInfoSearch search);

    RpcResult<Long> upset(@RequestBody PayMerchantCustomsInfoSubmit submit);

    RpcResult<PayMerchantCustomsInfoResult> getById(Long id);

    void exportPayMerchantCustomsInfo(PayMerchantCustomsInfoSearch search, HttpServletResponse httpServletResponse);

    RpcResult<List<SelectItemVO>> listCustoms();
}
