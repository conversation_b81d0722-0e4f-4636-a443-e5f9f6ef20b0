package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.v2.bean.dto.ProcessTradeBookDTO;
import com.danding.cds.v2.bean.dto.ProcessTradeBookItemDTO;
import com.danding.cds.v2.service.ProcessTradeBookService;
import com.danding.cds.web.v2.bean.vo.req.ProcessTradeBookItemImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 加贸账册表体导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_PROCESS_TRADE_BOOK_ITEM", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%8A%A0%E8%B4%B8%E8%B4%A6%E5%86%8C%E8%A1%A8%E4%BD%93%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "sheet1", classes = ProcessTradeBookItemImportExcelVO.class),})
public class ProcessTradeBookItemImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long bookId = Long.parseLong((String) extendMap.get("bookId"));
        int goodsType = Integer.parseInt((String) extendMap.get("goodsType"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        ProcessTradeBookService processTradeBookService = this.getBean(ProcessTradeBookService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);
        ProcessTradeBookDTO processTradeBookDTO = processTradeBookService.findById(bookId);

        List<ProcessTradeBookItemImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("sheet1")) {
                list = group.getDataList(ProcessTradeBookItemImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("InvOrderItemBondedProcessImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<ProcessTradeBookItemDTO> oldItemDTOList = processTradeBookService.listItemAllById(bookId);

        List<ProcessTradeBookItemDTO> successList = new ArrayList<>();
        Set<String> productIdSet = oldItemDTOList.stream().map(ProcessTradeBookItemDTO::getProductId).collect(Collectors.toSet());
        Map<String, List<ProcessTradeBookItemImportExcelVO>> excelProcductIdMap = list.stream().collect(Collectors.groupingBy(ProcessTradeBookItemImportExcelVO::getProductId));
        for (ProcessTradeBookItemImportExcelVO vo : list) {
            List<String> errorMsgList = new ArrayList<>();
            try {
                String errorMsg = doValidator(vo);
                if (StrUtil.isNotBlank(errorMsg)) {
                    errorMsgList.add(errorMsg);
                }
                if (excelProcductIdMap.containsKey(vo.getProductId()) && excelProcductIdMap.get(vo.getProductId()).size() > 1) {
                    errorMsgList.add("模版内存在重复数据");
                }
                if (productIdSet.contains(vo.getProductId())) {
                    errorMsgList.add(vo.getProductId() + "已存在，无法添加");
                }
                List<ProcessTradeBookItemDTO> itemDTOList = processTradeBookService.matchItem(bookId,
                        processTradeBookDTO.getLogisticsBookId(), goodsType, vo.getProductId());
                if (CollUtil.isEmpty(itemDTOList)) {
                    errorMsgList.add("料号不存在");
                }
                if (CollUtil.isNotEmpty(errorMsgList)) {
                    this.callbackData(false, index++, String.join(";", errorMsgList), vo);
                    continue;
                }
                ProcessTradeBookItemDTO itemDTO = itemDTOList.get(0);
                successList.add(itemDTO);
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("加贸账册表体导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            processTradeBookService.importItemExcel(bookId, goodsType, operator, successList);
        }
    }
}
