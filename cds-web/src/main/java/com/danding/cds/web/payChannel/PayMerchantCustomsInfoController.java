package com.danding.cds.web.payChannel;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import com.danding.cds.payChannel.api.dto.*;
import com.danding.cds.payChannel.api.service.PayMerchantAccountService;
import com.danding.cds.payChannel.api.service.PayMerchantCustomsInfoService;
import com.danding.cds.web.payChannel.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.business.common.utils.DateUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 14:53
 * @Description:
 */

@Slf4j
@RestController
@Api( tags = "报关资质管理" )
@RequestMapping("/pay/payMerchantCustomsInfo")
public class PayMerchantCustomsInfoController {
    @DubboReference
    private PayMerchantCustomsInfoService payMerchantCustomsInfoService;

    @DubboReference
    private PayMerchantAccountService payMerchantAccountService;

    @GetMapping("/paging")
    @ApiOperation(value = "分页查询")
    public ListVO<PayMerchantCustomsInfoResult> paging(PayMerchantCustomsInfoSearch search){
        ListVO<PayMerchantCustomsInfoDTO> paging = payMerchantCustomsInfoService.paging(search);
        ListVO<PayMerchantCustomsInfoResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((PayMerchantCustomsInfoDTO item)->{
            PayMerchantCustomsInfoResult vo = new PayMerchantCustomsInfoResult();
            BeanUtils.copyProperties(item,vo);
            PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
            if (account != null) {
                vo.setMerchantName(account.getName());
            }
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }

    @ApiOperation(value = "新增|更新报关资质")
    @PostMapping("/upset")
    public Long upset(@RequestBody PayMerchantCustomsInfoSubmit submit) throws ArgsErrorException {
        return payMerchantCustomsInfoService.upset(submit);
    }

    @ApiOperation(value = "根据ID查询",response = PayMerchantCustomsInfoResult.class)
    @GetMapping("/getById")
    public PayMerchantCustomsInfoResult getById(Long id){
        PayMerchantCustomsInfoResult result = new PayMerchantCustomsInfoResult();
        PayMerchantCustomsInfoDTO item = payMerchantCustomsInfoService.findById(id);
        BeanUtils.copyProperties(item,result);
        PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
        if (account != null) {
            result.setMerchantName(account.getName());
        }
        return result;
    }

    @ApiOperation(value = "导出报关资质列表Excel")
    @GetMapping("/exportPayMerchantCustomsInfo")
    public void exportPayMerchantCustomsInfo(PayMerchantCustomsInfoSearch search, HttpServletResponse httpServletResponse) {
        List<PayMerchantCustomsInfoDTO> list = payMerchantCustomsInfoService.queryListPayMerchantCustomsInfoExport(search);
        List<PayMerchantCustomsInfoExcelVO> excelList = new ArrayList();
        for (PayMerchantCustomsInfoDTO item : list) {
            PayMerchantCustomsInfoExcelVO payMerchantCustomsInfoVO = new PayMerchantCustomsInfoExcelVO();
            BeanUtils.copyProperties(item, payMerchantCustomsInfoVO);
            payMerchantCustomsInfoVO.setCreateTime(item.getCreateTime() != null? DateUtils.formatDate(item.getCreateTime(), "yyyy-MM-dd HH:mm:ss"):"");
            PayMerchantAccountDTO account = payMerchantAccountService.findById(item.getMerchantId());
            if (account != null) {
                payMerchantCustomsInfoVO.setMerchantName(account.getName());
            }
            excelList.add(payMerchantCustomsInfoVO);
        }
        String currentDateStr = DateUtils.formatDate(new Date(), "yyyyMMddHHmmss");
        String title = "报关资质管理" + currentDateStr;
        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.XSSF);
        Workbook workbook = ExcelExportUtil.exportExcel(exportParams, PayMerchantCustomsInfoExcelVO.class, excelList);
        try {
            httpServletResponse.setHeader("Content-Disposition", "attachment;filename=" + new String(title.getBytes(), "iso8859-1") + ".xlsx");
            httpServletResponse.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(httpServletResponse.getOutputStream());
            httpServletResponse.getOutputStream().flush();
        } catch (Exception e) {
            log.error("报关资质管理Excel导出失败", e);
        }
    }

    @ApiOperation(value = "口岸列表")
    @GetMapping("/listCustoms")
    public List<SelectItemVO> listCustoms() throws ArgsErrorException {
        return payMerchantCustomsInfoService.listPayCustoms();
    }
}
