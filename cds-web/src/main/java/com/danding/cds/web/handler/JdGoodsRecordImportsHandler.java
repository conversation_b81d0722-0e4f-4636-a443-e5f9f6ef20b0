package com.danding.cds.web.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.ciq.country.api.dto.CiqCountryDTO;
import com.danding.cds.ciq.country.api.service.CiqCountryService;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.vo.JdGoodsRecordExcelVO;
import com.danding.cds.item.api.dto.JdGoodsRecordDTO;
import com.danding.cds.item.api.dto.JdGoodsRecordOverSeaBoxDTO;
import com.danding.cds.item.api.dto.result.JdExcelImportResult;
import com.danding.cds.item.api.enums.JdGoodsRecordStatusEnum;
import com.danding.cds.item.api.enums.JdGoodsRecordType;
import com.danding.cds.item.api.service.JdGoodsRecordService;
import com.danding.cds.v2.bean.dto.JdServProviderDTO;
import com.danding.cds.v2.service.JdServProviderService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.StringUtils;

import javax.validation.Validator;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_JD_ZHIYING", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E4%BA%AC%E4%B8%9C%E5%A4%87%E6%A1%88%E5%AF%BC%E5%85%A5V1.4.xlsx",
        groups = {@ParkImportsHandler.Group(name="京东商品备案", classes = JdGoodsRecordExcelVO.class),})
public class JdGoodsRecordImportsHandler extends ImportsBaseHandler {
    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        // 引入spring的bean,用于业务处理
        JdGoodsRecordService taskService = this.getBean(JdGoodsRecordService.class);
        Validator validator = SpringUtil.getBean("validator", Validator.class);
        CustomsCountryService customsCountryService = this.getBean(CustomsCountryService.class);
        CiqCountryService ciqUomService = this.getBean(CiqCountryService.class);
        CustomsUomService customsUomService = this.getBean(CustomsUomService.class);
        CustomsHsService customsHsService = this.getBean(CustomsHsService.class);
        JdServProviderService jdServProviderService = this.getBean(JdServProviderService.class);

        List<JdGoodsRecordExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("京东商品备案")) {
                list = group.getDataList(JdGoodsRecordExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        int index = 1;
        Map<String, List<JdGoodsRecordExcelVO>> groupedRecords = list.stream()
                .filter(record -> record.getBeianType() == 1)
                .collect(Collectors.groupingBy(record -> record.getRecordType() + "_" + record.getUpc() + "_" +
                        record.getSkuId() + "_" + record.getCcProvider() + "_" + record.getShangJiaId()));
        List<JdGoodsRecordExcelVO> delList = new ArrayList<>();
        for (JdGoodsRecordExcelVO jdGoodsRecordExcelVO : list) {
            if (jdGoodsRecordExcelVO.getBeianType() == 1) {
                String key = jdGoodsRecordExcelVO.getRecordType() + "_" + jdGoodsRecordExcelVO.getUpc() + "_" +
                        jdGoodsRecordExcelVO.getSkuId() + "_" + jdGoodsRecordExcelVO.getCcProvider() + "_" + jdGoodsRecordExcelVO.getShangJiaId();
                if (!Objects.equals(JdGoodsRecordType.POP_NEW.getDesc(), jdGoodsRecordExcelVO.getRecordType()) && groupedRecords.containsKey(key)) {
                    List<JdGoodsRecordExcelVO> jdGoodsRecordExcelVOS = groupedRecords.get(key);
                    if (jdGoodsRecordExcelVOS.size() > 1) {
                        this.callbackData(false, index, "Excel中包含重复数据", jdGoodsRecordExcelVO);
                        delList.add(jdGoodsRecordExcelVO);
                        index++;
                        log.info("参数校验不通过 error={} 导入数据={}", "Excel中包含重复数据", jdGoodsRecordExcelVO);
                        continue;
                    }
                }
            }
            String check = this.check(jdGoodsRecordExcelVO, validator, customsCountryService,
                    ciqUomService, customsUomService, customsHsService, jdServProviderService);
            if (StrUtil.isNotBlank(check)) {
                this.callbackData(false, index, check, jdGoodsRecordExcelVO);
                delList.add(jdGoodsRecordExcelVO);
                index++;
                log.info("参数校验不通过 error={} 导入数据={}", check, jdGoodsRecordExcelVO);
            }
        }
        list.removeAll(delList);
        List<JdGoodsRecordDTO> jdGoodsRecordDTOList = list.stream().map(i -> {
            JdGoodsRecordDTO jdGoodsRecordDTO = new JdGoodsRecordDTO();
            BeanUtils.copyProperties(i, jdGoodsRecordDTO);
            if (Objects.nonNull(i.getCcProvider())) {
                jdGoodsRecordDTO.setProviderId(i.getCcProvider());
            }
            if (Objects.equals(1, i.getIfOverSeaBox())) {
                JdGoodsRecordOverSeaBoxDTO overSeaBoxDTO = new JdGoodsRecordOverSeaBoxDTO();
                overSeaBoxDTO.setGoodsId(i.getOverSeaBoxGoodsId());
                overSeaBoxDTO.setPrice(i.getOverSeaBoxPrice());
                overSeaBoxDTO.setQuantity(i.getOverSeaBoxQuantity());
                overSeaBoxDTO.setGoodsName(i.getOverSeaBoxGoodsName());
                jdGoodsRecordDTO.setOverSeaBoxDTO(overSeaBoxDTO);
                jdGoodsRecordDTO.setOverSeaBoxJson(JSON.toJSONString(overSeaBoxDTO));
            }
            jdGoodsRecordDTO.setType(JdGoodsRecordType.getEnum(i.getRecordType()));
            //将excel的商品编码存入jdEmg，分页接口为商品编码字段
            jdGoodsRecordDTO.setJdEmg(i.getEmg());
            jdGoodsRecordDTO.setEmg(null);
            return jdGoodsRecordDTO;
        }).collect(Collectors.toList());
        List<JdExcelImportResult> results = taskService.importExcel(jdGoodsRecordDTOList);
        log.info("导入后的返回结果数据{}", results);
        for (JdExcelImportResult result : results) {
            JdGoodsRecordExcelVO vo = new JdGoodsRecordExcelVO();
            JdGoodsRecordDTO data = result.getData();
            BeanUtil.copyProperties(data, vo);
            if (Objects.nonNull(data.getType())) {
                vo.setRecordType(data.getType().getDesc());
            }
            //这边展示切换回来
            if (Objects.nonNull(data.getJdEmg())) {
                vo.setEmg(data.getJdEmg());
            }
            this.callbackData(result.isFlag(), index, result.getReason(), vo);
            index++;
        }
    }

    /**
     * 校验excel转换对象后的数据准确性
     * @param data      excel转换对象
     * @return          校验结果  字符串不是空则代表有错误信息
     */
    public String check(JdGoodsRecordExcelVO data, Validator validator, CustomsCountryService customsCountryService,
                        CiqCountryService ciqCountryService, CustomsUomService customsUomService, CustomsHsService customsHsService,
                        JdServProviderService jdServProviderService) {
        String result = "";
        String validators = ValidatorUtils.doValidator(validator, data);
        String checkResult = data.checkParam();
        if (StrUtil.isNotBlank(validators)) {
            result += validators + "，";
        }
        if (StrUtil.isNotBlank(checkResult)) {
            result += checkResult + "，";
        }
        if (Objects.isNull(data.getOpinion())) {
            result += "审批回执不能为空，";
        }
//        if (StringUtil.isNotBlank(data.getFinishTime())) {
//            try {
//                DateUtil.parse(data.getFinishTime(), DatePattern.NORM_DATETIME_FORMAT);
//            } catch (DateException e) {
//                result += "备案完成时间格式错误，请检查重新导入，";
//            }
//        }
        JdGoodsRecordType recordType = JdGoodsRecordType.getEnum(data.getRecordType());
        if (Objects.isNull(recordType)) {
            result += "备案类型不符，请检查重新导入，";
        }
        if (Objects.equals(recordType, JdGoodsRecordType.DIRECT) ||
                Objects.equals(recordType, JdGoodsRecordType.POP) ||
                Objects.equals(recordType, JdGoodsRecordType.INDEPENDENT)
        ) {
            if (Objects.isNull(data.getSkuId())) {
                result += "SKU不能为空;";
            }
        }
        String shangJiaId = data.getShangJiaId();
        if (Objects.equals(recordType, JdGoodsRecordType.DIRECT)) {
            if (!StrUtil.isBlank(shangJiaId) && !Objects.equals(shangJiaId, "0")) {
                result += "备案类型属于京东直营，则商家ID必须是0或空；";
            }
        } else if (Objects.equals(recordType, JdGoodsRecordType.POP) || Objects.equals(recordType, JdGoodsRecordType.INDEPENDENT)) {
            if (StrUtil.isBlank(shangJiaId) || Objects.equals(shangJiaId, "0")) {
                result += "备案类型属于" + recordType.getDesc() + "，则商家ID必须有值且非0；";
            }
        } else if (Objects.equals(recordType, JdGoodsRecordType.POP_NEW)) {
            if (StrUtil.isBlank(data.getEmg())) {
                result += "新POP商品编码不能为空;";
            }
            if (StrUtil.isBlank(data.getJdRecordNo())) {
                result += "JD商品备案号不能为空;";
            }
        }
        if (Objects.equals(data.getBeianType(), 0)) {
            if (!Objects.equals(data.getFlowStateLabel(), JdGoodsRecordStatusEnum.RECORD_SUCCESS.getDesc())) {
                result += "线下备案导入失败，审核状态不属于备案完成";
            }
        }
        if (Objects.equals(data.getOpinion(), 0) && StringUtils.isEmpty(data.getBackReason())) {
            result += "审批回执为驳回时，驳回原因不能为空";
        }
        // 判断值的有效性  数据库中是否维护了
        if (StrUtil.isNotBlank(data.getHs())) {
            CustomsHsDTO hs = customsHsService.findByCode(data.getHs());
            result = hs == null ? result + "hs编码不存在，" : result;
        }
        if (StrUtil.isNotBlank(data.getHgOriginCountry())) {
            CustomsCountryDTO company = customsCountryService.findByCode(data.getHgOriginCountry());
            result = company == null ? result + "海关原产国对应编码不存在，" : result;
        }
        if (StrUtil.isNotBlank(data.getGjOriginCountry())) {
            CiqCountryDTO company_ = ciqCountryService.findByCode(data.getGjOriginCountry());
            result = company_ == null ? result + "国检原产国对应编码不存在，" : result;
        }
        // 3.2 根据单位表查询
        if (StrUtil.isNotBlank(data.getHgMeteringUnit())) {
            CustomsUomDTO uomHG = customsUomService.findByCode(data.getHgMeteringUnit());
            result = uomHG == null ? result + "海关计量单位编码不存在，" : result;
        }
        if (StrUtil.isNotBlank(data.getGjMeteringUnit())) {
            CustomsUomDTO uomGJ = customsUomService.findByCode(data.getGjMeteringUnit());
            result = uomGJ == null ? result + "国检计量单位对应编码不存在，" : result;
        }
        if (StrUtil.isNotBlank(data.getLegalFirstUnit())) {
            CustomsUomDTO firstUnit = customsUomService.findByCode(data.getLegalFirstUnit());
            result = firstUnit == null ? result + "法定第一计量单位对应编码不存在，" : result;
        }
        if (StrUtil.isNotBlank(data.getLegalSecondUnit())) {
            CustomsUomDTO secondUnit = customsUomService.findByCode(data.getLegalSecondUnit());
            result = secondUnit == null ? result + "法定第二计量单位对应编码不存在，" : result;
        }
        if (StringUtil.isNotBlank(data.getCcProvider())) {
            JdServProviderDTO jdServProviderDTO = jdServProviderService.findByCode(data.getCcProvider());
            if (Objects.nonNull(jdServProviderDTO)) {
                data.setServProviderId(jdServProviderDTO.getId());
                if (!StringUtils.isEmpty(data.getCustomsRegionCode())) {
                    if (!Objects.equals(data.getCustomsRegionCode(), jdServProviderDTO.getCustomsCode())) {
                        result = result + "服务商对应关区与输入关区不匹配，";
                    }
                } else {
                    data.setCustomsRegionCode(jdServProviderDTO.getCustomsCode());
                }
            } else {
                result = result + "服务商对应编码不存在，";
            }
        }
        if (Objects.nonNull(data.getAdValoremConsumption())) {
            BigDecimal adValoremConsumption = data.getAdValoremConsumption();
            int scale = adValoremConsumption.scale();
            if (scale > 4) {
                result = result + "从价消费税不能超过4位小数，";
            }
        }
        if (Objects.nonNull(data.getConsumption())) {
            BigDecimal consumption = data.getConsumption();
            int scale = consumption.scale();
            if (scale > 4) {
                result = result + "从量消费税不能超过4位小数，";
            }
        }
        // 境外套盒，商品货号须与货品id一致
        if (Objects.equals(data.getRecordType(), JdGoodsRecordType.DIRECT.getDesc())
                && Objects.equals(data.getIfOverSeaBox(), 1)
                && !Objects.equals("ZP" + data.getOverSeaBoxGoodsId(), data.getItemCode())) {
            result = result + "境外套盒，商品货号须与(ZP+货品id)一致，";
        }
        return result;
    }

}
