package com.danding.cds.web.message.vo;

import lombok.Data;

import javax.persistence.Column;

@Data
public class MessageSubscribeSearchVO {
    private Long id;
    /**
     * 订阅标签
     */
    private String subscribeTag;

    /**
     * 接收的消息类型列表
     */
    private String messageTypeJson;
    private String messageTypes;

    /**
     * 回传地址
     */
    private String notifyUrl;

    /**
     * 订阅说明
     */
    private String detail;

    /**
     * 报警邮箱
     */
    private String alertEmails;

    /**
     * 最大通知次数
     */
    private Integer maxCount;

    /**
     * 重试策略
     */
    private String retryStrategy;

    /**
     * 状态:0.停用;1.启用(默认)
     */
    private String enable;
}
