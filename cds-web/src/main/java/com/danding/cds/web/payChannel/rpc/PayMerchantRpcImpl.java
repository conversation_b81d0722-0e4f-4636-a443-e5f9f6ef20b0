package com.danding.cds.web.payChannel.rpc;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountChannelSubmit;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSearch;
import com.danding.cds.payChannel.api.dto.PayMerchantAccountSubmit;
import com.danding.cds.web.payChannel.PayMerchantAccountChannelController;
import com.danding.cds.web.payChannel.PayMerchantAccountController;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountChannelResult;
import com.danding.cds.web.payChannel.vo.PayMerchantAccountResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

@Slf4j
@DubboService
public class PayMerchantRpcImpl implements PayMerchantRpc{

    @Autowired
    private PayMerchantAccountController payMerchantAccountController;

    @Autowired
    private PayMerchantAccountChannelController payMerchantAccountChannelController;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantAccount/paging", desc = "收款商户分页查询")
    public RpcResult<ListVO<PayMerchantAccountResult>> paging(PayMerchantAccountSearch search) {
        return RpcResult.success(payMerchantAccountController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantAccount/upset", desc = "新增|更新商户")
    public RpcResult<Long> upset(PayMerchantAccountSubmit submit) {
        return RpcResult.success(payMerchantAccountController.upset(submit));
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantAccountChannel/paging", desc = "商户收款渠道分页查询")
    public RpcResult<ListVO<PayMerchantAccountChannelResult>> pagingChannel(PayMerchantAccountChannelSearch search) {
        return RpcResult.success(payMerchantAccountChannelController.paging(search));

    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantAccountChannel/upset", desc = "商户收款渠道新增|编辑")
    public RpcResult<Long> upsetChannel(PayMerchantAccountChannelSubmit submit) {
        return RpcResult.success(payMerchantAccountChannelController.upset(submit));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/pay/payMerchantAccountChannel/getById", desc = "根据ID查询")
    public RpcResult<PayMerchantAccountChannelResult> getById(IdParam idParam) {
        return RpcResult.success(payMerchantAccountChannelController.getById(idParam.getId()));
    }
}
