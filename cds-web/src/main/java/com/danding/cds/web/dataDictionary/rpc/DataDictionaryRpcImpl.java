package com.danding.cds.web.dataDictionary.rpc;

import com.alibaba.excel.EasyExcel;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.customs.country.api.dto.DataDictionaryReport;
import com.danding.cds.customs.country.api.dto.ImportExcelDataDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryMainTypeEnums;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.dictionary.api.vo.*;
import com.danding.cds.item.api.enums.TransType;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.service.UploadProcessService;
import com.danding.cds.upload.api.vo.DataDictionaryImportVO;
import com.danding.component.common.api.common.response.ListVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @menu 数据字典
 */
@Slf4j
@DubboService
@RestController
public class DataDictionaryRpcImpl implements DataDictionaryRpc {

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private UploadProcessService uploadProcessService;

    @Override
    @SoulClient(path = "/dictionary/paging", desc = "字典数据详情分页")
    @PostMapping("/dictionary/paging")
    public RpcResult<ListVO<CustomsDictionaryResVO>> paging(@RequestBody CustomsDictionaryReqVO search) {
        if (Objects.isNull(search)) {
            return RpcResult.error("参数为空");
        }
        ListVO<CustomsDictionaryResVO> result = customsDictionaryService.paging(search);
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/dictionary/typePaging", desc = "字典数据类型分页")
    @RequestMapping("/dictionary/typePaging")
    public RpcResult<ListVO<CustomsDictionaryResVO>> typePaging(@RequestBody CustomsDictionaryReqVO search) {
        if (Objects.isNull(search)) {
            return RpcResult.error("参数为空");
        }
        search.setType(DataDictionaryTypeEnums.TYPE.getValue());
        ListVO<CustomsDictionaryResVO> result = customsDictionaryService.paging(search);
        return RpcResult.success(result);
    }

    @Override
    @RequestMapping("/dictionary/insert")
    @SoulClient(path = "/dictionary/insert", desc = "新增字典数据")
    public RpcResult<String> insert(@RequestBody CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.insert(param);
        } catch (BusinessException e) {
            log.warn("新增失败: " + e.getMessage());
            return RpcResult.error("新增失败: " + e.getMessage());
        }
        return RpcResult.success("新增成功");
    }

    @Override
    @RequestMapping("/dictionary/update")
    @SoulClient(path = "/dictionary/update", desc = "修改字典数据")
    public RpcResult<String> update(@RequestBody CustomsDictionaryOperateParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.update(param);
        } catch (BusinessException e) {
            log.warn("编辑失败: " + e.getMessage());
            return RpcResult.error("编辑失败: " + e.getMessage());
        }
        return RpcResult.success("编辑成功");
    }

    @Override
    @RequestMapping("/dictionary/delete")
    @SoulClient(path = "/dictionary/delete", desc = "删除字典数据")
    public RpcResult<String> delete(@RequestBody IdParam id) {
        if (Objects.isNull(id)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.delete(id.getId());
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
        return RpcResult.success("删除成功");
    }

    @Override
    @SoulClient(path = "/dictionary/type", desc = "字典数据类型")
    public RpcResult listDictionaryType() {
        List<SelectOptionVO<String>> listDictionaryType = customsDictionaryService.listByType(DataDictionaryTypeEnums.TYPE.getValue());
        return RpcResult.success(listDictionaryType);
    }

    @Override
    @SoulClient(path = "/dictionary/enableSwitch", desc = "启用状态开关")
    @RequestMapping("/dictionary/enableSwitch")
    public RpcResult<String> enableSwitch(@RequestBody DataDictionaryEnableSwitchVO switchVO) {
        if (Objects.isNull(switchVO)) {
            return RpcResult.error("参数为空");
        }
        try {
            customsDictionaryService.enableSwitch(switchVO);
            return RpcResult.success("启用状态切换成功");
        } catch (Exception e) {
            log.error("数据字典 - 启用状态切换失败" + e.getMessage(), e);
            return RpcResult.error("启用状态切换失败");
        }
    }

    @Override
    @SoulClient(path = "/dictionary/listMainType", desc = "数据类型下拉")
    @RequestMapping("/dictionary/listMainType")
    public RpcResult listMainType() {
        return RpcResult.success(EnumUtils.build(DataDictionaryMainTypeEnums.class, "value", "desc"));
    }

    @Override
    @SoulClient(path = "/dictionary/listTypeName", desc = "数据类型名称下拉")
    @RequestMapping("/dictionary/listTypeName")
    public RpcResult listTypeName() {
        List<SelectOptionVO<String>> selectOptionVOS = customsDictionaryService.listByType(DataDictionaryTypeEnums.TYPE.getValue());
        List<SelectOptionVO> result = selectOptionVOS.stream().peek(s -> s.setId(s.getName())).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/dictionary/listTypeCode", desc = "数据类型标识下拉")
    @RequestMapping("/dictionary/listTypeCode")
    public RpcResult listTypeCode() {
        List<SelectOptionVO<String>> selectOptionVOS = customsDictionaryService.listByType(DataDictionaryTypeEnums.TYPE.getValue());
        List<SelectOptionVO> result = selectOptionVOS.stream().peek(s -> s.setName((String) s.getId())).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/dictionary/importExcel", desc = "字典数据导入")
    public RpcResult<DataDictionaryReport> importExcel(DataDictionaryImportSubmit param) {
        try {
            return RpcResult.success("导入成功", customsDictionaryService.importExcel(param.getImportList(), param.getType(), true));
        } catch (Exception e) {
            log.error("数据字典导入失败" + e.getMessage(), e);
            return RpcResult.error("导入失败");
        }
    }

    @Override
    @SoulClient(path = "/dictionary/preImport", desc = "字典数据导入预览")
    public RpcResult<DataDictionaryReport> preImport(ImportDictionaryParam param) {
        if (Objects.isNull(param.getType())) {
            return RpcResult.error("数据字典类型为空");
        }
        List<DataDictionaryImportVO> successList = EasyExcel.read(DownLoadUtil.downloadNet(param.getUrl())).headRowNumber(1).head(DataDictionaryImportVO.class).sheet().doReadSync();
        int i = 1; // 行号，从2开始
        List<ImportExcelDataDictionaryDTO> preList = new ArrayList<>();
        for (DataDictionaryImportVO demoExcel : successList) {
            i++;
            if (StringUtils.isBlank(demoExcel.getCode())
                    || StringUtils.isBlank(demoExcel.getName())) {
                continue;
            }
            ImportExcelDataDictionaryDTO record = new ImportExcelDataDictionaryDTO();
            BeanUtils.copyProperties(demoExcel, record);
            record.setIdx(i);
            preList.add(record);
        }
        if (CollectionUtils.isEmpty(preList)) {
            return RpcResult.error("对不起！选择模板文件为空或模板不正确");
        }
        return RpcResult.success(customsDictionaryService.importExcel(preList, param.getType(), false));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/dictionary/import/query", desc = "导入字典查询")
    @RequestMapping("/dictionary/import/query")
    public RpcResult<LoadTaskInfoDTO> importQuery() {
        UploadType uploadType = UploadType.DATA_DICTIONARY;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setTemplateUrl(uploadType.getUrl());
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        return RpcResult.success(loadTaskInfoDTO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/dictionary/import", desc = "导入字典数据")
    @RequestMapping("/dictionary/import")
    public RpcResult<String> importExcelV2(@RequestBody ImportDictionaryParam param) {
        try {
            uploadProcessService.submitProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    param.getUrl(), UploadType.DATA_DICTIONARY, param.getType());
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/dictionary/import/confirm", desc = "导入确认")
    @RequestMapping("/dictionary/import/confirm")
    public RpcResult<String> importConfirm() {
        UploadType uploadType = UploadType.DATA_DICTIONARY;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success("提交成功");
    }


    @Override
    @SoulClient(path = "/dictionary/listByType", desc = "通过数据类型展示下拉")
    @RequestMapping("/dictionary/listByType")
    public RpcResult<List<SelectOptionVO<String>>> listByType(String type) {
        if (StringUtils.isEmpty(type)) {
            return RpcResult.error("参数为空");
        }
        return RpcResult.success(customsDictionaryService.listByType(type));
    }

    /**
     * 清单类型下拉
     *
     * @return
     * @path
     */
    @Override
    @SoulClient(path = "/dictionary/listCustomsInvtType", desc = "清单类型下拉")
    public RpcResult<List<SelectOptionVO<String>>> listCustomsInvtType() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.CUSTOMS_INVT_TYPE.getValue()));
    }

    /**
     * 清单类型下拉
     *
     * @return
     */
    @SoulClient(path = "/dictionary/listSupvMode", desc = "监管方式下拉")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listSupvMode() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.SUPERVISE_MODE.getValue()));
    }

    @Override
    @SoulClient(path = "/dictionary/listFreightForwardingCompany", desc = "货代公司下拉")
    public RpcResult<List<SelectOptionVO<String>>> listFreightForwardingCompany() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.FREIGHT_FORWARDING_COMPANY.getValue()));
    }

    @Override
    @SoulClient(path = "/dictionary/listArrivePort", desc = "到货港口/机场下拉")
    public RpcResult<List<SelectOptionVO<String>>> listArrivePort() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.ARRIVE_PORT.getValue()));
    }

    /**
     * 入关地点下拉
     *
     * @param transType 运输方式
     * @return RpcResult
     * @path /dictionary/listIntryWhere
     */
    @Override
    @SoulClient(path = "/dictionary/listIntryWhere", desc = "入关地点下拉")
    public RpcResult<List<SelectOptionVO<String>>> listIntryWhere(Integer transType) {
        if (Objects.isNull(transType)) {
            List<String> typeList = Arrays.asList(DataDictionaryTypeEnums.INTRY_WHERE_AIRPORT.getValue(),
                    DataDictionaryTypeEnums.INTRY_WHERE_CARTAGE.getValue(),
                    DataDictionaryTypeEnums.INTRY_WHERE_RAILWAY.getValue(),
                    DataDictionaryTypeEnums.INTRY_WHERE_HARBOR.getValue());
            return RpcResult.success(customsDictionaryService.listByTypeBatch(typeList));
        }
        TransType transTypeEnum = TransType.getEnum(transType);
        String type = null;
        if (transTypeEnum != null) {
            switch (transTypeEnum) {
                case AIR:
                    type = DataDictionaryTypeEnums.INTRY_WHERE_AIRPORT.getValue();
                    break;
                case SEA:
                    type = DataDictionaryTypeEnums.INTRY_WHERE_HARBOR.getValue();
                    break;
                case RAILWAY:
                    type = DataDictionaryTypeEnums.INTRY_WHERE_RAILWAY.getValue();
                    break;
                case ZG_CAR:
                    type = DataDictionaryTypeEnums.INTRY_WHERE_CARTAGE.getValue();
                    break;
            }
        }
        return RpcResult.success(customsDictionaryService.listByType(type));
    }


    /**
     * 京东库房下拉
     *
     * @param customsCode
     * @return
     * @path /dictionary/listJdWarehouseName
     */
    @Override
    @SoulClient(path = "/dictionary/listJdWarehouseName", desc = "京东库房下拉")
    public RpcResult listJdWarehouseName(String customsCode) {
        if (StringUtil.isBlank(customsCode)) {
            List<String> typeList = Arrays.asList(DataDictionaryTypeEnums.JD_WAREHOUSE_JINYI.getValue(),
                    DataDictionaryTypeEnums.JD_WAREHOUSE_YIWU.getValue());
            return RpcResult.success(customsDictionaryService.listByTypeBatch(typeList));
        }
        String type = null;
        switch (customsCode) {
            case "2924":
                type = DataDictionaryTypeEnums.JD_WAREHOUSE_JINYI.getValue();
                break;
            case "2925":
                type = DataDictionaryTypeEnums.JD_WAREHOUSE_YIWU.getValue();
                break;
        }
        return RpcResult.success(customsDictionaryService.listByType(type));
    }

    /**
     * 运输方式下拉
     *
     * @return
     * @path /dictionary/listTransportMode
     */
    @Override
    @SoulClient(path = "/dictionary/listTransportMode", desc = "运输方式")
    public RpcResult<List<SelectOptionVO<String>>> listTransportMode() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.TRANSPORT_MODE.getValue()));
    }

    /**
     * 备案规则类型下拉
     *
     * @return
     * @path /dictionary/listRecordRuleType
     */
    @Override
    @SoulClient(path = "/dictionary/listRecordRuleType", desc = "备案规则类型下拉")
    public RpcResult<List<SelectOptionVO<String>>> listRecordRuleType() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.RECORD_RULE_TYPE.getValue()));
    }
    /**
     * 路由标签下拉
     *
     * @return
     * @path /dictionary/listRouteTag
     */
    @SoulClient(path = "/dictionary/listRouteTag", desc = "路由标签下拉")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listRouteTag() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.ROUTE_TAG.getValue()));
    }


    @SoulClient(path = "/dictionary/listCustomsDecType", desc = "海关报关单类型下拉")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listCustomsDecType() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.CUSTOMS_DEC_TYPE.getValue()));
    }

}
