package com.danding.cds.web.collaborateOrder.rpc;

import cn.hutool.core.bean.BeanUtil;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderCountDTO;
import com.danding.cds.collaborateorder.api.dto.CollaborateOrderParam;
import com.danding.cds.collaborateorder.api.enums.CollaborateOrderTypeEnum;
import com.danding.cds.collaborateorder.api.enums.CollaborateStatus;
import com.danding.cds.collaborateorder.api.service.CollaborateOrderService;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderReqVO;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderResVO;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.out.api.InventoryOrderRpc;
import com.danding.cds.out.bean.vo.req.TallyReportReqVO;
import com.danding.cds.web.collaborateOrder.vo.CollaborateOrderCountVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 * @date 2022/3/30
 */
@Slf4j
@DubboService
public class CollaborateOrderRpcImpl implements CollaborateOrderRpc {

    @DubboReference
    private CollaborateOrderService collaborateOrderService;

    @DubboReference
    private InventoryOrderRpc inventoryOrderRpc;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    /**
     * 分页查询
     *
     * @param param
     * @return
     */
    @Override
    @SoulClient(path = "/collaborateOrder/paging", desc = "协同单分页查询")
    @RequestMapping("/collaborateOrder/paging")
    public RpcResult<ListVO<CollaborateOrderResVO>> paging(@RequestBody CollaborateOrderParam param) {
        try {
            CollaborateOrderReqVO reqVO = getCollaborateOrderReqVO(param);
            ListVO<CollaborateOrderResVO> paging = collaborateOrderService.paging(reqVO);
            return RpcResult.success(paging);
        } catch (Exception e) {
            log.error("协同单分页查询失败 error={}", e.getMessage(), e);
            return RpcResult.error("协同单分页查询失败 " + e.getMessage());
        }
    }

    /**
     * 构建协同单查询参数
     *
     * @param param
     * @return
     */
    private CollaborateOrderReqVO getCollaborateOrderReqVO(CollaborateOrderParam param) {
        SimpleDateFormat dateSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        CollaborateOrderReqVO reqVO = BeanUtil.copyProperties(param, CollaborateOrderReqVO.class);
        if (Objects.nonNull(param.getCollaborateSn())) {
            reqVO.setCollaborateSns(Arrays.asList(param.getCollaborateSn().split(",")));
        }
        if (Objects.nonNull(param.getInOutOrderNo())) {
            reqVO.setInOutOrderNos(Arrays.asList(param.getInOutOrderNo().split(",")));
        }
        if (Objects.nonNull(param.getCreateStaTime())) {
            reqVO.setCreateStaTime(dateSdf.format(new Date(param.getCreateStaTime())));
        }
        if (Objects.nonNull(param.getCreateEndTime())) {
            reqVO.setCreateEndTime(dateSdf.format(new Date(param.getCreateEndTime())));
        }
        if (Objects.nonNull(param.getFinishStaTime())) {
            reqVO.setFinishStaTime(dateSdf.format(new Date(param.getFinishStaTime())));
        }
        if (Objects.nonNull(param.getFinishEndTime())) {
            reqVO.setFinishEndTime(dateSdf.format(new Date(param.getFinishEndTime())));
        }
        if (Objects.nonNull(param.getTallyStaTime())) {
            reqVO.setTallyStaTime(dateSdf.format(new Date(param.getTallyStaTime())));
        }
        if (Objects.nonNull(param.getTallyEndTime())) {
            reqVO.setTallyEndTime(dateSdf.format(new Date(param.getTallyEndTime())));
        }
        if (Objects.nonNull(param.getInvStaFinishTime())) {
            reqVO.setInvStaFinishTime(dateSdf.format(new Date(param.getInvStaFinishTime())));
        }
        if (Objects.nonNull(param.getInvEndFinishTime())) {
            reqVO.setInvEndFinishTime(dateSdf.format(new Date(param.getInvEndFinishTime())));
        }
        return reqVO;
    }


    @Override
    @SoulClient(path = "/collaborateOrder/collaborateStatus", desc = "协同单状态")
    public RpcResult collaborateStatus() {
        List<SelectOptionVO<String>> result = Arrays.stream(CollaborateStatus.values())
                .filter((CollaborateStatus item) -> !item.equals(CollaborateStatus.TALLY_WAIT))
                .map((CollaborateStatus item) -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/collaborateOrder/inventoryOrder", desc = "协同单状态")
    public RpcResult inventoryOrderStatus() {
        return RpcResult.success(EnumUtils.build(CollaborateOrderTypeEnum.class, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/collaborateOrder/inventor", desc = "理货报告")
    public RpcResult inventor(TallyReportReqVO reqVO) {
        return RpcResult.success(inventoryOrderRpc.receiveTallyReport(reqVO));
    }

    @Override
    @SoulClient(path = "/collaborateOrder/export", desc = "导出协同单")
    @RequestMapping("/collaborateOrder/export")
    public RpcResult export(@RequestBody CollaborateOrderParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("导出失败，参数为空");
        }
        try {
            CollaborateOrderReqVO reqVO = getCollaborateOrderReqVO(param);
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    reqVO, ReportType.COLLABORATE_ORDER_FOR_EXCEL);
            return RpcResult.success("导出成功");
        } catch (ServiceException e) {
            log.warn("导出协同单 处理异常：{}", e.getMessage(), e);
            return RpcResult.error("导出失败");
        }
    }

    @Override
    @SoulClient(path = "/collaborateOrder/inveBusinessTypeCount", desc = "统计协同单状态数量")
    @RequestMapping("/collaborateOrder/inveBusinessTypeCount")
    public RpcResult<CollaborateOrderCountVO> inveBusinessTypeCount(@RequestBody CollaborateOrderParam param) {
        if (Objects.isNull(param)) {
            return RpcResult.error("统计失败，参数为空");
        }
        try {
            CollaborateOrderReqVO reqVO = getCollaborateOrderReqVO(param);
            if (StringUtils.isNotEmpty(reqVO.getInveCustomsSn())) {
                reqVO.setInveCustomsSnList(Splitter.on(",").splitToList(reqVO.getInveCustomsSn()));
            }
            List<CollaborateOrderCountDTO> collaborateOrderCountDTOS = collaborateOrderService.selectCountByPaging(reqVO);
            CollaborateOrderCountVO result = new CollaborateOrderCountVO();
            for (CollaborateOrderCountDTO dto : collaborateOrderCountDTOS) {
                result.allCount += dto.getNum();
                if (InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode().equals(dto.getInveBusinessType())) {
                    result.oneLineInCount = dto.getNum();
                } else {
                    result.notOneLineCount += dto.getNum();
                }
            }
            return RpcResult.success(result);
        } catch (Exception e) {
            log.warn("统计协同单状态数量 处理异常：{}", e.getMessage(), e);
            return RpcResult.error("统计失败");
        }
    }


}
