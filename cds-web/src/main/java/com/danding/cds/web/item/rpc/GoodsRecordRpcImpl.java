package com.danding.cds.web.item.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.danding.business.client.rpc.goods.facade.IGoodsRecordRpcFacade;
import com.danding.business.client.rpc.goods.result.GoodsRecordRpcResult;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.ImportReturn;
import com.danding.cds.common.model.excel.UrlParam;
import com.danding.cds.common.utils.*;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.hs.api.dto.CustomsHsDTO;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.dto.submit.GoodsRecordAuditSubmitV2;
import com.danding.cds.item.api.enums.GoodsRecordEnum;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.service.RecordWarehouseService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.record.api.dto.EndangeredCheckResultDTO;
import com.danding.cds.record.api.service.EndangeredFactorService;
import com.danding.cds.taxes.api.service.TaxesTenantAccountService;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.service.UploadProcessService;
import com.danding.cds.v2.bean.ItemTrackLogConfig;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.GoodsRecordAuditWayEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTagEnums;
import com.danding.cds.v2.bean.enums.GoodsRecordTypeEnums;
import com.danding.cds.v2.bean.es.RecordTrackLogEsDTO;
import com.danding.cds.v2.bean.param.RecordModifyAssociateInfoSubmit;
import com.danding.cds.v2.bean.vo.req.GoodsRecordRejectReasonReqVo;
import com.danding.cds.v2.bean.vo.req.RecordTrackLogSearch;
import com.danding.cds.v2.bean.vo.req.UpdFinishGoodsRecordReqVo;
import com.danding.cds.v2.bean.vo.res.*;
import com.danding.cds.v2.service.EntityWarehouseService;
import com.danding.cds.v2.service.RecordCustomsService;
import com.danding.cds.warehouse.api.WarehouseService;
import com.danding.cds.web.item.GoodsRecordController;
import com.danding.cds.web.item.vo.*;
import com.danding.cds.web.v2.bean.es.RecordTrackLogEsVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.logistics.mq.common.handler.MessageSender;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.eventbus.EventBus;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @menu 商品备案
 */
@Data
@DubboService
@Slf4j
@RefreshScope
public class GoodsRecordRpcImpl implements GoodsRecordRpc {

    @DubboReference
    private GoodsRecordService goodsRecordService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private EndangeredFactorService endangeredFactorService;

    @Autowired
    private EventBus eventBus;

    public static Map<Long, ImportReturn> importReturnMap = new HashMap<>();

    @DubboReference
    private WarehouseService warehouseService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private UploadProcessService uploadProcessService;

    @DubboReference
    private IGoodsRecordRpcFacade iGoodsRecordRpcFacade;

    @DubboReference
    private TaxesTenantAccountService taxesTenantAccountService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private RecordCustomsService recordCustomsService;

    @DubboReference
    private RecordWarehouseService recordWarehouseService;

    @DubboReference
    private EntityWarehouseService entityWarehouseService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @Autowired
    private FourCategoryGoodsService fourCategoryGoodsService;

    @Resource
    private GoodsRecordController goodsRecordController;

    @Value("${central.goodsRecord.examining.dataSource:ccs}")
    private String examiningDataSource;

    @Resource
    private MessageSender messageSender;

    /**
     * 分页查询
     *
     * @param condition
     * @return
     * @path /goodsRecord/paging
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/paging", desc = "分页查询")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<GoodsRecordVO>> paging(GoodsRecordSearchCondition condition) {
        try {
            // 可以查看的账册的ID列表,与查询条件账册ID比较
            List<Long> accountBookIdList = condition.getRoleAccountBookIdList();
            if (!CollectionUtils.isEmpty(accountBookIdList)) {
                Long bookId = condition.getCustomsBookId();
                if (bookId != null && !accountBookIdList.contains(bookId)) {
                    return RpcResult.success(new ListVO<>());
                }
            }
            ListVO<GoodsRecordDTO> pageResult = goodsRecordService.paging(condition);
            ListVO<GoodsRecordVO> result = new ListVO<>();
            result.setPage(pageResult.getPage());
            List<GoodsRecordVO> list = this.getGoodsRecordVOS(pageResult);
            result.setDataList(list);
            return RpcResult.success(result);
        } catch (ArgsInvalidException e) {
            log.error(e.getErrorMessage());
            return RpcResult.error(e.getErrorMessage());
        }
    }


    private List<GoodsRecordVO> getGoodsRecordVOS(ListVO<GoodsRecordDTO> pageResult) {
        List<GoodsRecordVO> list = new ArrayList<>();
        Map<String, String> tenantIdName = new HashMap<>();
        List<Long> idList = pageResult.getDataList().stream().map(GoodsRecordDTO::getId).collect(Collectors.toList());
        List<RecordCustomsDTO> customsDTOS = recordCustomsService.findByRecordId(idList);
        final Map<Long, List<String>> recordIdProductIdMap = goodsRecordService.getCustomsProductIdByRecordId(idList);
        Map<Long, List<RecordCustomsDTO>> recordCustomsDTOMap = customsDTOS.stream().collect(Collectors.groupingBy(RecordCustomsDTO::getRecordId));
        for (GoodsRecordDTO dto : pageResult.getDataList()) {
            GoodsRecordVO recordVO = new GoodsRecordVO();
            BeanUtil.copyProperties(dto, recordVO);
            if (Objects.nonNull(dto.getGoodsRecordTag())) {
                List<String> goodsRecordDesc = GoodsRecordTagEnums.getGoodsRecordDesc(dto.getGoodsRecordTag());
                recordVO.setGoodsRecordTagList(goodsRecordDesc);
                //当GoodsRecordTagEnums == 2是才显示具体原因
                List<Integer> goodsRecordTagList = GoodsRecordTagEnums.getGoodsRecordTag(dto.getGoodsRecordTag());
                if (goodsRecordTagList.contains(GoodsRecordTagEnums.GTIN_NO_RULE.getCode())) {
                    recordVO.setGoodsRecordTagDesc(GTINCheck.checkRulesReason(dto.getBarCode()));
                }
            }
            if (Objects.nonNull(dto.getGoodsSource())) {
                recordVO.setSourceStr(GoodsRecordEnum.getEnum(dto.getGoodsSource()).getDesc());
            }
            if (recordCustomsDTOMap.containsKey(dto.getId())) {
                List<RecordCustomsDTO> recordCustomsDTOList = recordCustomsDTOMap.get(dto.getId());
                String wait = recordCustomsDTOList.stream().filter(r -> Objects.equals(r.getStatus(), GoodsRecordStatusEnum.WAIT_EXAMINE.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                String success = recordCustomsDTOList.stream().filter(r -> Objects.equals(r.getStatus(), GoodsRecordStatusEnum.RECORD_SUCCESS.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                String refuse = recordCustomsDTOList.stream().filter(r -> Objects.equals(r.getStatus(), GoodsRecordStatusEnum.EXAMINE_REFUSE.getCode())).map(RecordCustomsDTO::getCustoms).collect(Collectors.joining(","));
                recordVO.setWaitExamineCustoms(wait);
                recordVO.setExaminedCustoms(success);
                recordVO.setRefuseCustoms(refuse);

                // 展示关务备注
                recordCustomsDTOList.stream()
                        .filter(i -> StrUtil.isNotEmpty(i.getGuanWuRemark()))
                        .max(Comparator.comparing(RecordCustomsDTO::getUpdateTime))
                        .ifPresent(newerCustomsDTO -> recordVO.setGuanWuRemark(newerCustomsDTO.getGuanWuRemark()));
                recordVO.setAuditWay(recordCustomsDTOList.stream().max(Comparator.comparing(RecordCustomsDTO::getUpdateTime)).map(RecordCustomsDTO::getAuditWay).orElse(null));
                if (Objects.nonNull(recordVO.getAuditWay())) {
                    recordVO.setAuditWayDesc(GoodsRecordAuditWayEnums.getEnums(recordVO.getAuditWay()).getDesc());
                }
            }
            if (recordIdProductIdMap.containsKey(dto.getId())) {
                List<String> customsRecordProductIdList = recordIdProductIdMap.get(dto.getId());
                String customsRecordProductId = customsRecordProductIdList.stream().distinct().collect(Collectors.joining(","));
                recordVO.setCustomsRecordProductId(customsRecordProductId);
            }
            if (!LongUtil.isNone(dto.getCreateTime())) {
                recordVO.setCreateTime(new DateTime(dto.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            if (!LongUtil.isNone(dto.getUpdateTime())) {
                recordVO.setUpdateTime(new DateTime(dto.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            if (!StringUtils.isEmpty(dto.getOriginCountry())) {
                CustomsCountryDTO code = customsCountryService.findByCode(dto.getOriginCountry());
                if (Objects.nonNull(code)) {
                    recordVO.setOriginCountryDesc(code.getName());
                }
            }
            if (!StringUtils.isEmpty(dto.getCustoms())) {
                CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(dto.getCustoms());
                if (!StringUtils.isEmpty(customsDistrictEnum.getCustoms())) {
                    recordVO.setCustomsDesc(customsDistrictEnum.getDesc());
                }
            }
            if (!StringUtils.isEmpty(dto.getTenantId())) {
                if (tenantIdName.containsKey(dto.getTenantId())) {
                    recordVO.setTenantName(tenantIdName.get(dto.getTenantId()));
                } else {
                    String tenantName = taxesTenantAccountService.getTenantNameById(dto.getTenantId());
                    recordVO.setTenantName(tenantName);
                    tenantIdName.put(dto.getTenantId(), tenantName);
                }
            }
            Long customsBookId = dto.getCustomsBookId();
            if (customsBookId != null) {
                CustomsBookResVo bookResVo = customsBookService.findByIdV2(customsBookId);
                recordVO.setCustomsBookNo(bookResVo.getBookNo());
            }
            recordVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(dto.getRecordStatus()).getDesc());
            //租户暂时为空
            recordVO.setLesseeName("");
            list.add(recordVO);
        }
        return list;
    }

    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/audit", desc = "审核")
    public RpcResult<Long> audit(GoodsRecordAuditSubmit submit) {
        try {
            return RpcResult.success(goodsRecordService.audit(submit));
        } catch (ArgsInvalidException e) {
            log.error("[op:GoodsRecordRpcImpl audit={}]", e.getErrorMessage());
            return RpcResult.error(e.getErrorMessage());
        }
    }

    /**
     * 审核
     *
     * @param customsWarehouseInfoVO
     * @param baseInfoVO
     * @return
     * @path /goodsRecord/auditV2
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/auditV2", desc = "审核")
    public RpcResult<Long> auditV2(GoodsRecordAuditSubmit customsWarehouseInfoVO, GoodsRecordSubmit baseInfoVO) {
        log.info("auditV2 customsWarehouseInfoVO={}", JSON.toJSONString(customsWarehouseInfoVO));
        log.info("auditV2 baseInfoVO={}", JSON.toJSONString(baseInfoVO));
        try {
            if (StrUtil.isNotBlank(baseInfoVO.getGoodsRecordName())) {
                baseInfoVO.setGoodsRecordName(StringUtils.trimWhitespace(baseInfoVO.getGoodsRecordName()));
            }
            GoodsRecordAuditSubmitV2 submit = new GoodsRecordAuditSubmitV2();
            submit.setGoodsRecordSubmit(baseInfoVO);
            customsWarehouseInfoVO.setExternalProductId(baseInfoVO.getExternalProductId());
            customsWarehouseInfoVO.setGrossWeight(baseInfoVO.getGrossWeight());
            customsWarehouseInfoVO.setNetWeight(baseInfoVO.getNetWeight());
            submit.setGoodsRecordAuditSubmit(customsWarehouseInfoVO);
            GoodsRecordDTO recordDTO = goodsRecordService.findById(baseInfoVO.getId());
            if (Objects.equals(recordDTO.getRecordType(), GoodsRecordTypeEnums.NEW.getCode())) {
                GoodsRecordDetailBaseInfoVO erpBaseInfo = this.getGoodsRecordBaseInfo(customsWarehouseInfoVO.getCustomsCode(), recordDTO);
                GoodsRecordDTO before = ConvertUtil.beanConvert(erpBaseInfo, GoodsRecordDTO.class);
                before.setId(recordDTO.getId());
                before.setChannel(InventoryOrderChannel.LOGISTICS.getValue());
                before.setExternalGoodsId(recordDTO.getExternalGoodsId());
                before.setCainiaoGoodsId(recordDTO.getCainiaoGoodsId());
                before.setTaxRate(recordDTO.getTaxRate());
                submit.setErpGoodsRecordDTO(before);
            }
            Long res = this.auditProxy(submit);
            this.aiRecommendHsCodeSendMsg(baseInfoVO.getId(), customsWarehouseInfoVO.getCustomsCode(),
                    customsWarehouseInfoVO.getHsCode(), customsWarehouseInfoVO.getAiRecommendHsCode());
            return RpcResult.success(res);
        } catch (ArgsInvalidException e) {
            log.error("[op:GoodsRecordRpcImpl audit={}]", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (ArgsErrorException e) {
            log.error("[op:GoodsRecordRpcImpl audit={}]", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("[op:GoodsRecordRpcImpl audit={}]", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 获取待审核备案
     *
     * @param reqVO
     * @return
     * @path /goodsRecord/getNextWaitAuditRecord
     */
    @Override
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/goodsRecord/getNextWaitAuditRecord", desc = "获取下一条待审核备案")
    public RpcResult<GoodsRecordAuditNextResVO> getNextWaitAuditRecord(GoodsRecordAuditNextReqVO reqVO) {
        return goodsRecordController.getNextWaitAuditRecord(reqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public Long auditProxy(GoodsRecordAuditSubmitV2 submit) {
        return goodsRecordService.auditV2(submit);
    }

    /**
     * 新增/编辑
     *
     * @param submit
     * @return
     * @path /goodsRecord/upset
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/upset", desc = "新增/编辑")
    public RpcResult<Long> upset(GoodsRecordSubmit submit) {
        GoodsRecordDTO recordDTO = goodsRecordService.findById(submit.getId());
        if (recordDTO != null) {
            if (GoodsRecordStatusEnum.WAIT_EXAMINE.getCode().equals(recordDTO.getRecordStatus())) {
                try {
                    if (StrUtil.isNotBlank(submit.getGoodsRecordName())) {
                        submit.setGoodsRecordName(StringUtils.trimWhitespace(submit.getGoodsRecordName()));
                    }
                    goodsRecordService.upset(submit);
                } catch (ArgsErrorException e) {
                    log.error("[op:GoodsRecordRpcImpl upset={} error={}，stack={}]", submit, e.getErrorMessage(), e);
                    return RpcResult.error(e.getErrorMessage());
                } catch (Exception e) {
                    log.error("[op:GoodsRecordRpcImpl upset={} error={}，stack={}]", submit, e.getMessage(), e);
                    return RpcResult.error(e.getMessage());
                }
                return RpcResult.success("更新成功");
            }
            aiRecommendHsCodeSendMsg(submit.getId(), submit.getCustomsCode(), submit.getHsCode(), submit.getAiRecommendHsCode());
        }
        return RpcResult.error("已审核备案不允许编辑");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/updateGoodsRecord", desc = "更新ERP的备案")
    public RpcResult updateGoodsRecord(GoodsRecordSubmit reqVo) {
        try {
            log.info("商品备案编辑 reqVo={}", JSON.toJSONString(reqVo));
            GoodsRecordDTO recordDTO = goodsRecordService.findById(reqVo.getId());
            if (Objects.isNull(recordDTO)) {
                throw new Exception("未查询到备案信息");
            }
            if (StrUtil.isNotBlank(reqVo.getGoodsRecordName())) {
                reqVo.setGoodsRecordName(StringUtils.trimWhitespace(reqVo.getGoodsRecordName()));
            }
            //新备案调用ERP更新缓存
            if (Objects.equals(recordDTO.getRecordType(), GoodsRecordTypeEnums.NEW.getCode())) {
                RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(recordDTO.getId(), reqVo.getCustomsCode());
                if (Objects.nonNull(recordCustomsDTO) && !StringUtils.isEmpty(recordCustomsDTO.getBaseInfoJson())) {
                    GoodsRecordDTO goodsRecordDTO = JSON.parseObject(recordCustomsDTO.getBaseInfoJson(), GoodsRecordDTO.class);
                    BeanUtils.copyProperties(reqVo, goodsRecordDTO, "id");
                    recordCustomsService.updateBaseJson(reqVo.getId(), reqVo.getCustomsCode(), JSON.toJSONString(goodsRecordDTO));
                    if (StrUtil.isNotBlank(reqVo.getGuanWuRemark())) {
                        recordCustomsService.updateGuanWuRemark(reqVo.getId(), reqVo.getCustomsCode(), reqVo.getGuanWuRemark());
                        // 同步一下es
                        messageSender.sendMsg(reqVo.getId(), "ccs-goods-record-dump-topic", goodsRecordDTO.getProductId());
                    }
                } else {
                    goodsRecordService.update(reqVo, ItemTrackLogConfig.EDIT);
                }
            } else if (Objects.equals(recordDTO.getRecordType(), GoodsRecordTypeEnums.OLD.getCode())) {
                //老备案更新库
                if (GoodsRecordStatusEnum.WAIT_EXAMINE.getCode().equals(recordDTO.getRecordStatus())) {
                    goodsRecordService.update(reqVo, ItemTrackLogConfig.EDIT);
                }
            }
            aiRecommendHsCodeSendMsg(reqVo.getId(), reqVo.getCustomsCode(), reqVo.getHsCode(), reqVo.getAiRecommendHsCode());
        } catch (ArgsErrorException e) {
            log.error("备案编辑失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("编辑失败(" + e.getErrorMessage() + ")");
        } catch (Exception e) {
            log.error("备案编辑失败 error={}", e.getMessage(), e);
            return RpcResult.error("编辑失败(" + e.getMessage() + ")");
        }
        return RpcResult.success("编辑成功");
    }

    private GoodsRecordDTO buildDiffRecordDTO(GoodsRecordSubmit reqVo) {
        GoodsRecordDTO goodsRecordDTO = ConvertUtil.beanConvert(reqVo, GoodsRecordDTO.class);
        if (Objects.nonNull(reqVo.getVatRate())) {
            goodsRecordDTO.setVatRate(reqVo.getVatRate().intValue());
        }
        if (Objects.nonNull(reqVo.getTaxRate())) {
            goodsRecordDTO.setTaxRate(reqVo.getTaxRate().intValue());
        }
        if (Objects.nonNull(reqVo.getSecondUnitAmount())) {
            goodsRecordDTO.setSecondUnitAmount(reqVo.getSecondUnitAmount());
        }
        return goodsRecordDTO;
    }

    private GoodsRecordRpcResult buildErpUpdateRecordCache(GoodsRecordSubmit submit) {
        GoodsRecordRpcResult rpcResult = ConvertUtil.beanConvert(submit, GoodsRecordRpcResult.class);
        rpcResult.setUserId(Long.valueOf(submit.getTenantId()));
        String userName = taxesTenantAccountService.getTenantNameById(submit.getTenantId());
        if (StringUtil.isNotBlank(userName)) {
            rpcResult.setUserName(userName);
        }
        rpcResult.setSku(submit.getSkuId());
        rpcResult.setBarcode(submit.getBarCode());
        rpcResult.setGoodsName(submit.getGoodsRecordName());
        rpcResult.setDeclaredUnit(submit.getDeclareUnit());
        rpcResult.setDeclaredUnitPrice(submit.getDeclarePrice());
        rpcResult.setDeclaredCurrency(submit.getDeclareCurrency());
        rpcResult.setDeclareElement(submit.getHgsbys());
        rpcResult.setFirstQuantity(submit.getFirstUnitAmount().doubleValue());
        rpcResult.setSecondQuantity(Objects.isNull(submit.getSecondUnitAmount()) ? null : submit.getSecondUnitAmount().doubleValue());
        rpcResult.setGrossWeight(submit.getGrossWeight().doubleValue());
        rpcResult.setNetWeight(submit.getNetWeight().doubleValue());
        rpcResult.setLength(Objects.isNull(submit.getLength()) ? null : submit.getLength().doubleValue());
        rpcResult.setWidth(Objects.isNull(submit.getWidth()) ? null : submit.getWidth().doubleValue());
        rpcResult.setHeight(Objects.isNull(submit.getHeight()) ? null : submit.getHeight().doubleValue());
        rpcResult.setCompanyName(submit.getProductCompanyName());
        rpcResult.setCompanyCode(submit.getProductCompanyRegisterNumber());
        rpcResult.setCompanyAdd(submit.getProductCompanyAddress());
        rpcResult.setProLink(submit.getProductLink());
        rpcResult.setLogicWarehouseCode(submit.getWarehouseId());
        rpcResult.setAccountCode(null);
        rpcResult.setChineseBrandName(submit.getBrand());
        rpcResult.setEnglishBrandName(submit.getBrandEn());
        rpcResult.setVAT(Objects.isNull(submit.getVatRate()) ? null : submit.getVatRate().doubleValue());
        rpcResult.setConsumptionTax(Objects.isNull(submit.getTaxRate()) ? null : submit.getTaxRate().doubleValue());
        rpcResult.setEffect(submit.getRecordFunction());
        rpcResult.setPurpose(submit.getRecordUsage());
        rpcResult.setMaterialCode(submit.getProductId());
        rpcResult.setExternalMaterialCode(submit.getExternalProductId());
        rpcResult.setRecordCode(submit.getCountryRecordNo());
        //不要吐槽 erp那边不想只更新url 只能这样回传json
        if (Objects.nonNull(submit.getAttachmentName()) && Objects.nonNull(submit.getAttachmentUrl())) {
            Map<String, String> fileJson = new HashMap<>();
            fileJson.put("name", submit.getAttachmentName());
            fileJson.put("url", submit.getAttachmentUrl());
            rpcResult.setFileJson(JSON.toJSONString(fileJson));
        }
        if (Objects.nonNull(submit.getFrontImage())) {
            Map<String, String> frontJson = new HashMap<>();
            frontJson.put("src", submit.getFrontImage());
            frontJson.put("name", FilenameUtils.getName(submit.getFrontImage()));
            frontJson.put("url", submit.getFrontImage());
            rpcResult.setFrontJson(JSON.toJSONString(frontJson));
        }
        if (Objects.nonNull(submit.getLabelImage())) {
            Map<String, String> labelJson = new HashMap<>();
            labelJson.put("src", submit.getLabelImage());
            labelJson.put("name", FilenameUtils.getName(submit.getLabelImage()));
            labelJson.put("url", submit.getLabelImage());
            rpcResult.setLabelJson(JSON.toJSONString(labelJson));
        }
        if (Objects.nonNull(submit.getBackImage())) {
            Map<String, String> backJson = new HashMap<>();
            backJson.put("src", submit.getBackImage());
            backJson.put("name", FilenameUtils.getName(submit.getBackImage()));
            backJson.put("url", submit.getBackImage());
            rpcResult.setBackJson(JSON.toJSONString(backJson));
        }
        if (Objects.nonNull(submit.getSideImage())) {
            Map<String, String> sideJson = new HashMap<>();
            sideJson.put("src", submit.getSideImage());
            sideJson.put("name", FilenameUtils.getName(submit.getSideImage()));
            sideJson.put("url", submit.getSideImage());
            rpcResult.setSideJson(JSON.toJSONString(sideJson));
        }
        return rpcResult;
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/updateEnable", desc = "禁用/启用")
    public RpcResult<Long> updateEnable(GoodsRecordSubmit submit) {
        return RpcResult.success(goodsRecordService.updateEnable(submit.getId(), submit.getEnable()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/deleteById", desc = "删除")
    public RpcResult<Long> deleteById(IdParam idParam) throws ArgsErrorException {
        //todo 取消商品备案删除向ERP推送
//        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(idParam.getId());
//        if (goodsRecordDTO != null && GoodsRecordChannel.LOGISTICS.getValue().equals(goodsRecordDTO.getChannel())) {
//            GoodsRecordRpcDeleteParam deleteParam = new GoodsRecordRpcDeleteParam();
//            deleteParam.setGoodsCode(goodsRecordDTO.getSkuId());
//            CustomsBookDTO customsBookDTO = customsBookService.findById(goodsRecordDTO.getCustomsBookId());
//            deleteParam.setAccountCode(customsBookDTO.getBookNo());
//            RpcResult rpcResult = iGoodsRecordRpcFacade.deleteGoodsRecord(deleteParam);
//            if (200 != rpcResult.getCode()) {
//                return RpcResult.error(rpcResult.getMessage());
//            }
//        }
        if (Objects.isNull(idParam.getId())) {
            return RpcResult.error("参数ID未传入");
        }
        try {
            goodsRecordService.deleteById(idParam.getId());
            return RpcResult.success("删除成功");
        } catch (ArgsInvalidException e) {
            log.error("备案删除失败 id={}, e={}", idParam.getId(), e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("备案删除失败 id={}, e={}", idParam.getId(), e.getMessage(), e);
            return RpcResult.error("删除失败");
        }
    }

    /**
     * 上线备案-口岸后废弃
     * 改用V2版本 {@link GoodsRecordRpc#detailV2(Long, String)}
     *
     * @param idParam
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/detail", desc = "详情")
    @Deprecated
    public RpcResult<GoodsRecordDetailVO> detail(IdParam idParam) {
        GoodsRecordDetailVO detailVO = this.getGoodsRecordDetailVO(idParam.getId());
        return RpcResult.success(detailVO);
    }

    private GoodsRecordDetailVO getGoodsRecordDetailVO(Long id) {
        GoodsRecordDetailVO detailVO = new GoodsRecordDetailVO();
        GoodsRecordDTO recordDTO = goodsRecordService.findById(id);
        if (recordDTO == null) {
            return null;
        }
        BeanUtil.copyProperties(recordDTO, detailVO);
        detailVO.setRecordStatusDesc(GoodsRecordStatusEnum.getEnum(recordDTO.getRecordStatus()).getDesc());
        if (!LongUtil.isNone(recordDTO.getCreateTime())) {
            detailVO.setCreateTime(new DateTime(recordDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!LongUtil.isNone(recordDTO.getRecordFinishTime())) {
            detailVO.setRecordFinishTime(new DateTime(recordDTO.getRecordFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
        }
        if (!StringUtils.isEmpty(recordDTO.getDeclareCurrency())) {
            CustomsCurrencyDTO code = customsCurrencyService.findByCode(recordDTO.getDeclareCurrency());
            if (code != null) {
                detailVO.setDeclareCurrencyDesc(recordDTO.getDeclareCurrency() + ":" + code.getName());
            }
        }
        if (Objects.nonNull(recordDTO.getOriginCountry())) {
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(recordDTO.getOriginCountry());
            if (Objects.nonNull(countryDTO)) {
                detailVO.setOriginCountryName(recordDTO.getOriginCountry() + ":" + countryDTO.getName());
            }
        }
        if (!StringUtils.isEmpty(recordDTO.getCustoms())) {
            CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(recordDTO.getCustoms());
            if (!StringUtils.isEmpty(customsDistrictEnum.getCustoms())) {
                detailVO.setCustomsDesc(customsDistrictEnum.getDesc());
            }
        }
        if (!StringUtils.isEmpty(recordDTO.getTenantId())) {
            String tenantName = taxesTenantAccountService.getTenantNameById(recordDTO.getTenantId());
            detailVO.setTenantNameDesc(tenantName);
        }

        Long accountId = recordDTO.getCustomsBookId();
        if (accountId != null) {
            detailVO.setCustomsBookNo(customsBookService.findById(accountId).getBookNo());
        }
        return detailVO;
    }


    /**
     * 详情
     *
     * @param recordId
     * @param customsCode
     * @return RpcResult<GoodsRecordWarehouseDetailVO>
     * @path /goodsRecord/detailV2
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/detailV2", desc = "详情")
    public RpcResult<GoodsRecordWarehouseDetailVO> detailV2(Long recordId, String customsCode) {
        log.info("detailV2 recordId={} customsCode={}", recordId, customsCode);
        if (Objects.isNull(recordId) || Objects.isNull(customsCode)) {
            throw new ArgsInvalidException("参数错误");
        }
        //获取备案和口岸
        GoodsRecordDTO recordDTO = goodsRecordService.findById(recordId);
        RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(recordId, customsCode);
        log.info("detailV2 recordCustomsDTO={}", JSON.toJSONString(recordCustomsDTO));
        if (Objects.isNull(recordCustomsDTO)) {
            if (GoodsRecordTypeEnums.NEW.getCode().equals(recordDTO.getRecordType())) {
                throw new ArgsInvalidException("未查询到口岸信息");
            } else if (GoodsRecordTypeEnums.OLD.getCode().equals(recordDTO.getRecordType())
                    && Objects.isNull(recordDTO.getProductId())
                    && Objects.nonNull(recordDTO.getCustomsBookId())) {
                recordCustomsDTO = new RecordCustomsDTO();
                //兼容老品-料号为空-账册不为空的情况
                recordCustomsDTO.setCustoms(recordDTO.getCustoms());
                recordCustomsDTO.setCustomsCode(customsCode);
                recordCustomsDTO.setHsCode(recordDTO.getHsCode());
                recordCustomsDTO.setOriginCountry(recordDTO.getOriginCountry());
                recordCustomsDTO.setFirstUnit(recordDTO.getFirstUnit());
                recordCustomsDTO.setFirstUnitAmount(recordDTO.getFirstUnitAmount());
                recordCustomsDTO.setSecondUnit(recordDTO.getSecondUnit());
                recordCustomsDTO.setSecondUnitAmount(recordDTO.getSecondUnitAmount());
                recordCustomsDTO.setStatus(recordDTO.getRecordStatus());
            }
        }
        GoodsRecordWarehouseDetailVO detailVO = new GoodsRecordWarehouseDetailVO();
        // 构造备案详情
        GoodsRecordDetailBaseInfoVO detailBaseInfoVO;
        //新备案的待审核与驳回取ERP数据
        List<EndangeredRuleDetailVO> rules;
        if (!GoodsRecordStatusEnum.RECORD_SUCCESS.getCode().equals(recordCustomsDTO.getStatus()) && GoodsRecordTypeEnums.NEW.getCode().equals(recordDTO.getRecordType())) {
            detailBaseInfoVO = this.getGoodsRecordBaseInfo(customsCode, recordDTO);
            //外部货品id 和 菜鸟货品id 为 ccs 专属字段
            detailBaseInfoVO.setExternalGoodsId(recordDTO.getExternalGoodsId());
            detailBaseInfoVO.setCainiaoGoodsId(recordDTO.getCainiaoGoodsId());
            // 这里把CCS系统的ID返回给前端，后面的逻辑处理需要依据此唯一ID
            detailBaseInfoVO.setId(recordId.toString());
            // 使用ERP的数据生成备案规则
            GoodsRecordDTO ruleGoodRecordDTO = new GoodsRecordDTO();
            ruleGoodRecordDTO.setBarCode(detailBaseInfoVO.getBarCode());
            ruleGoodRecordDTO.setGoodsRecordName(detailBaseInfoVO.getGoodsRecordName());
            ruleGoodRecordDTO.setComposition(detailBaseInfoVO.getComposition());
            ruleGoodRecordDTO.setHgsbys(detailBaseInfoVO.getHgsbys());
            rules = buildRules(ruleGoodRecordDTO);
        } else {
            //备案成功取CCS数据
            GoodsRecordDetailVO goodsRecordDetailVO = this.getGoodsRecordDetailVO(recordId);
            detailBaseInfoVO = ConvertUtil.beanConvert(goodsRecordDetailVO, GoodsRecordDetailBaseInfoVO.class);
            CustomsHsDTO customsHsDTO = customsHsService.findByCode(recordCustomsDTO.getHsCode());
            detailBaseInfoVO.setId(goodsRecordDetailVO.getId().toString());
            detailBaseInfoVO.setHsCode(recordCustomsDTO.getHsCode());
            detailBaseInfoVO.setOriginCountry(recordCustomsDTO.getOriginCountry());
            detailBaseInfoVO.setVatRate(customsHsDTO.getVat().multiply(new BigDecimal(100)).intValue());
            detailBaseInfoVO.setTaxRate(customsHsDTO.getTariff().multiply(new BigDecimal(100)).intValue());
            detailBaseInfoVO.setFirstUnit(recordCustomsDTO.getFirstUnit());
            detailBaseInfoVO.setFirstUnitAmount(recordCustomsDTO.getFirstUnitAmount());
            detailBaseInfoVO.setSecondUnit(recordCustomsDTO.getSecondUnit());
            detailBaseInfoVO.setSecondUnitAmount(recordCustomsDTO.getSecondUnitAmount());
            rules = buildRules(recordDTO);
        }
        //商品备案标记描述
        if (Objects.nonNull(recordDTO.getGoodsRecordTag())) {
            detailBaseInfoVO.setGoodsRecordTag(recordDTO.getGoodsRecordTag());
            List<Integer> goodsRecordTagList = GoodsRecordTagEnums.getGoodsRecordTag(recordDTO.getGoodsRecordTag());
            if (goodsRecordTagList.contains(GoodsRecordTagEnums.GTIN_NO_RULE.getCode())) {
                detailBaseInfoVO.setGoodsRecordTagDesc(GTINCheck.checkRulesReason(recordDTO.getBarCode()));
            }
        }

        // 基本信息加下商品标签列表
        Boolean isFourCategoryGoods = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(detailBaseInfoVO.getOriginCountry(), detailBaseInfoVO.getHsCode());
        if (isFourCategoryGoods) {
            List<String> goodsRecordTagList = new ArrayList() {{
                add(GoodsRecordTagEnums.FOUR_CATEGORY_GOODS.getDesc());
            }};
            detailBaseInfoVO.setGoodsRecordTagList(goodsRecordTagList);
        }
        detailBaseInfoVO.setRules(rules);
        detailVO.setBaseInfoVO(detailBaseInfoVO);
        //构造商品列表
        List<RecordProductDTO> recordProductDTOList = goodsRecordService.getRecordCustomsProduct(recordCustomsDTO.getRecordId(), recordCustomsDTO.getId());
        List<GoodsRecordCustomsProductInfoVO> customsProductInfoVOS = ConvertUtil.listConvert(recordProductDTOList, GoodsRecordCustomsProductInfoVO.class);
        detailVO.setProductInfoVO(customsProductInfoVOS);
        //构造仓库信息
        List<RecordWarehouseDTO> recordWarehouseDTOList = goodsRecordService.getRecordWarehouseInfo(recordCustomsDTO.getId());
        //构造通关料号
        List<Long> warehouseIdList = recordWarehouseDTOList.stream().map(RecordWarehouseDTO::getId).collect(Collectors.toList());
        List<RecordWarehouseProductIdDTO> recordWarehouseProductIdList = goodsRecordService.getRecordWarehouseProductIdList(recordId, warehouseIdList);
        Map<Long, List<String>> warehouseMap = recordWarehouseProductIdList.stream().collect(
                Collectors.groupingBy(
                        RecordWarehouseProductIdDTO::getRecordWarehouseId,
                        Collectors.mapping(RecordWarehouseProductIdDTO::getCustomsDeclareProductId, Collectors.toList())));
        List<CustomsWarehouseResVO> warehouseResVOList = ConvertUtil.listConvert(recordWarehouseDTOList, CustomsWarehouseResVO.class);
        warehouseResVOList.forEach(w -> {
            if (warehouseMap.containsKey(w.getId())) {
                List<String> warehouseProductIdDTOList = warehouseMap.get(w.getId());
                w.setCustomsDeclareProductIdList(warehouseProductIdDTOList);
            }
        });
        GoodsRecordCustomsWarehouseInfoVO warehouseInfoVO = ConvertUtil.beanConvert(recordCustomsDTO, GoodsRecordCustomsWarehouseInfoVO.class);
        warehouseInfoVO.setStatusDesc(GoodsRecordStatusEnum.getEnum(warehouseInfoVO.getStatus()).getDesc());
        if (Objects.nonNull(warehouseInfoVO.getAuditWay())) {
            warehouseInfoVO.setAuditWayDesc(GoodsRecordAuditWayEnums.getEnums(warehouseInfoVO.getAuditWay()).getDesc());
        }
        warehouseInfoVO.setCustomsWarehouseResVOList(warehouseResVOList);
        detailVO.setCustomsWarehouseInfoVO(warehouseInfoVO);
        return RpcResult.success(detailVO);
    }

    /**
     * 备案规则VO封装
     *
     * @param recordDTO
     * @return
     */
    private List<EndangeredRuleDetailVO> buildRules(GoodsRecordDTO recordDTO) {

        List<EndangeredRuleDetailVO> rules = new ArrayList<>();
        EndangeredCheckResultDTO endangeredResultDTO = endangeredFactorService.isEndangered(recordDTO, Boolean.FALSE);
        EndangeredRuleDetailVO detailVO1 = new EndangeredRuleDetailVO();
        detailVO1.setRuleName("含濒危成分禁止进口");
        if (!CollectionUtils.isEmpty(endangeredResultDTO.getEndangeredFactors())) {
            detailVO1.setEndangeredFactors(StrUtil.join("、", endangeredResultDTO.getEndangeredFactors()));
            detailVO1.setIsEndangered(Boolean.TRUE);
        } else {
            detailVO1.setEndangeredFactors("");
            detailVO1.setIsEndangered(Boolean.FALSE);
        }
        rules.add(detailVO1);

        EndangeredRuleDetailVO detailVO2 = new EndangeredRuleDetailVO();
        detailVO2.setRuleName("含输华限制禁止进口");
        if (!CollectionUtils.isEmpty(endangeredResultDTO.getTransportCNLimitFactors())) {
            detailVO2.setEndangeredFactors(StrUtil.join("、", endangeredResultDTO.getTransportCNLimitFactors()));
            detailVO2.setIsEndangered(Boolean.TRUE);
        } else {
            detailVO2.setEndangeredFactors("");
            detailVO2.setIsEndangered(Boolean.FALSE);
        }
        rules.add(detailVO2);

        EndangeredRuleDetailVO detailVO4 = new EndangeredRuleDetailVO();
        detailVO4.setRuleName("含涉药成分禁止进口");
        if (!CollectionUtils.isEmpty(endangeredResultDTO.getDrugFactors())) {
            detailVO4.setEndangeredFactors(StrUtil.join("、", endangeredResultDTO.getDrugFactors()));
            detailVO4.setIsEndangered(Boolean.TRUE);
        } else {
            detailVO4.setEndangeredFactors("");
            detailVO4.setIsEndangered(Boolean.FALSE);
        }
        rules.add(detailVO4);

        EndangeredRuleDetailVO detailVO3 = new EndangeredRuleDetailVO();
        detailVO3.setRuleName("申报要素成分判断");
        detailVO3.setIsEndangered(Boolean.FALSE);
        detailVO3.setEndangeredFactors("");
        String hgsbys = recordDTO.getHgsbys();
        if (StringUtil.isNotEmpty(hgsbys)) {
            if (!hgsbys.contains("成分")) {
                detailVO3.setEndangeredFactors("缺少成分字样");
                detailVO3.setIsEndangered(Boolean.TRUE);
            } else if (!checkPercentagesSumTo100(hgsbys)) {
                detailVO3.setEndangeredFactors("成分总和不等于100%");
                detailVO3.setIsEndangered(Boolean.TRUE);
            }
        }
        rules.add(detailVO3);
//        EndangeredRuleDetailVO detailVO2 = new EndangeredRuleDetailVO();
//        detailVO2.setRuleName("备案库涉濒危禁止进口");
//        if (!CollectionUtils.isEmpty(endangeredResultDTO.getRecordBaseEndangered())) {
//            detailVO2.setIsEndangered(Boolean.TRUE);
//            detailVO2.setEndangeredFactors(StrUtil.join("、", endangeredResultDTO.getRecordBaseEndangered()));
//        } else {
//            detailVO2.setIsEndangered(Boolean.FALSE);
//            detailVO2.setEndangeredFactors("");
//        }
//        rules.add(detailVO2);
        return rules;
    }

    /**
     * 使用正则表达式提取字符串中的所有百分比数字，并检查其总和是否为100%
     *
     * @param text 包含百分比的文本字符串
     * @return 如果总和等于100%，返回true；否则，返回false
     */
    public boolean checkPercentagesSumTo100(String text) {
        // 正则表达式匹配百分比数字
        Pattern pattern = Pattern.compile("\\d+(\\.\\d+)?%");
        Matcher matcher = pattern.matcher(text);

        BigDecimal totalPercentage = BigDecimal.ZERO;
        while (matcher.find()) {
            // 提取百分比数字（支持小数），去除百分号并转换为double类型
            BigDecimal percentage = new BigDecimal(matcher.group().replace("%", ""));
            totalPercentage = totalPercentage.add(percentage);
        }

        if (totalPercentage.compareTo(BigDecimal.valueOf(100.0)) != 0) {
            return false;
        }
        return true;
    }

    @Test
    public void t() {
        String text = "品牌类型:4|出口享惠情况:3|成分含量:低聚果糖20%，赤藓糖醇18%，卷心菜提取物16.8%，约氏乳杆菌、唾液乳杆菌、鼠李糖乳杆菌、嗜酸乳杆菌、植物乳杆菌、副干酪乳杆菌共15.5%，菠萝提取物14.8%，后生元9.5%，益生菌发酵剂2.5%，酸奶香精2.4%，柠檬酸0.5%；|包装规格:30袋/盒|品牌（中文或外文名称）:Life'sFlora/舒伯特|GTIN:5060730760204|CAS:|其他:";
        boolean b = checkPercentagesSumTo100(text);
        System.out.println(b);
    }


    private GoodsRecordDetailBaseInfoVO getGoodsRecordBaseInfo(String customsCode, GoodsRecordDTO recordDTO) {
        log.info("getERPBaseInfo customsCode={} recordDTO={}", customsCode, JSON.toJSONString(recordDTO));
        GoodsRecordDetailBaseInfoVO detailBaseInfoVO = new GoodsRecordDetailBaseInfoVO();
        if (Objects.equals("erp", examiningDataSource)) {
            GoodsRecordRpcResult goodsRecord = iGoodsRecordRpcFacade.getGoodsRecordCache(recordDTO.getGoodsCode(), customsCode);
            log.info("getERPBaseInfo goodsRecord={}", JSON.toJSONString(goodsRecord));
            if (Objects.isNull(goodsRecord)) {
                throw new ArgsInvalidException("未查询到备案信息");
            }
            detailBaseInfoVO = ConvertUtil.beanConvert(goodsRecord, GoodsRecordDetailBaseInfoVO.class);
            detailBaseInfoVO.setTenantId(String.valueOf(goodsRecord.getUserId()));
            detailBaseInfoVO.setSkuId(goodsRecord.getSku());
            detailBaseInfoVO.setGoodsRecordName(goodsRecord.getGoodsName());
            detailBaseInfoVO.setGoodsCode(goodsRecord.getGoodsCode());
            // 申报单位单价币值  申报要素
            detailBaseInfoVO.setDeclareUnit(goodsRecord.getDeclaredUnit());
            detailBaseInfoVO.setDeclarePrice(goodsRecord.getDeclaredUnitPrice());
            detailBaseInfoVO.setDeclareCurrency(goodsRecord.getDeclaredCurrency());
            if (Objects.nonNull(detailBaseInfoVO.getDeclareCurrency())) {
                CustomsCurrencyDTO currencyDTO = customsCurrencyService.findByCode(detailBaseInfoVO.getDeclareCurrency());
                if (Objects.nonNull(currencyDTO)) {
                    detailBaseInfoVO.setDeclareCurrencyDesc(currencyDTO.getCode() + ":" + currencyDTO.getName());
                }
            }
            detailBaseInfoVO.setHgsbys(goodsRecord.getDeclareElement());
            // 第一第二计量单位
            detailBaseInfoVO.setFirstUnit(goodsRecord.getFirstUnit());
            detailBaseInfoVO.setFirstUnitAmount(BigDecimal.valueOf(goodsRecord.getFirstQuantity()));
            detailBaseInfoVO.setSecondUnit(goodsRecord.getSecondUnit());
            detailBaseInfoVO.setSecondUnitAmount(goodsRecord.getSecondQuantity() != null ? BigDecimal.valueOf(goodsRecord.getSecondQuantity()) : null);
            // 净重毛重
            detailBaseInfoVO.setGrossWeight(BigDecimal.valueOf(goodsRecord.getGrossWeight()));
            detailBaseInfoVO.setNetWeight(BigDecimal.valueOf(goodsRecord.getNetWeight()));
            // 长宽高
            detailBaseInfoVO.setLength(goodsRecord.getLength() == null ? null : BigDecimal.valueOf(goodsRecord.getLength()));
            detailBaseInfoVO.setWidth(goodsRecord.getWidth() == null ? null : BigDecimal.valueOf(goodsRecord.getWidth()));
            detailBaseInfoVO.setHeight(goodsRecord.getHeight() == null ? null : BigDecimal.valueOf(goodsRecord.getHeight()));
            // 条码
            detailBaseInfoVO.setBarCode(goodsRecord.getBarcode());
            // 生产企业相关
            detailBaseInfoVO.setProductCompanyName(goodsRecord.getCompanyName());
            detailBaseInfoVO.setProductCompanyRegisterNumber(goodsRecord.getCompanyCode());
            detailBaseInfoVO.setProductCompanyAddress(goodsRecord.getCompanyAdd());
            // 商品链接
            detailBaseInfoVO.setProductLink(goodsRecord.getProLink());
            // 其他
            detailBaseInfoVO.setHsCode(goodsRecord.getHsCode());
            detailBaseInfoVO.setOriginCountry(goodsRecord.getOriginCountry());
            if (Objects.nonNull(goodsRecord.getOriginCountry())) {
                CustomsCountryDTO countryDTO = customsCountryService.findByCode(goodsRecord.getOriginCountry());
                if (Objects.nonNull(countryDTO)) {
                    detailBaseInfoVO.setOriginCountryName(goodsRecord.getOriginCountry() + ":" + countryDTO.getName());
                }
            }
            detailBaseInfoVO.setWarehouseId(goodsRecord.getLogicWarehouseCode());
            detailBaseInfoVO.setCustomsBookNo(goodsRecord.getAccountCode());
            detailBaseInfoVO.setModel(goodsRecord.getModel());
            detailBaseInfoVO.setBrand(goodsRecord.getChineseBrandName());
            detailBaseInfoVO.setBrandEn(goodsRecord.getEnglishBrandName());
            detailBaseInfoVO.setVatRate(goodsRecord.getVAT() != null ? goodsRecord.getVAT().intValue() : null);
            detailBaseInfoVO.setTaxRate(goodsRecord.getConsumptionTax() != null ? goodsRecord.getConsumptionTax().intValue() : null);
            detailBaseInfoVO.setRecordFunction(goodsRecord.getEffect());
            detailBaseInfoVO.setRecordUsage(goodsRecord.getPurpose());
            detailBaseInfoVO.setComposition(goodsRecord.getComposition());
            detailBaseInfoVO.setProductId(!StringUtils.isEmpty(goodsRecord.getMaterialCode()) ? goodsRecord.getMaterialCode() : null);
            detailBaseInfoVO.setDeclareCurrency(goodsRecord.getDeclaredCurrency());
            detailBaseInfoVO.setTenantId(String.valueOf(goodsRecord.getUserId()));
            detailBaseInfoVO.setTenantNameDesc(goodsRecord.getUserName());
            detailBaseInfoVO.setCountryRecordNo(goodsRecord.getRecordCode());
            // 附件
            if (!StringUtils.isEmpty(goodsRecord.getFileJson())) {
                JSONObject jsonObject = JSON.parseObject(goodsRecord.getFileJson());
                if (Objects.nonNull(jsonObject)) {
                    detailBaseInfoVO.setAttachmentName(jsonObject.getString("name"));
                    detailBaseInfoVO.setAttachmentUrl(jsonObject.getString("url"));
                }
            }
            if (!StringUtils.isEmpty(goodsRecord.getFrontJson())) {
                JSONObject jsonObject = JSON.parseObject(goodsRecord.getFrontJson());
                if (Objects.nonNull(jsonObject)) {
                    detailBaseInfoVO.setFrontImage(jsonObject.getString("url"));
                }
            }
            if (!StringUtils.isEmpty(goodsRecord.getSideJson())) {
                JSONObject jsonObject = JSON.parseObject(goodsRecord.getSideJson());
                if (Objects.nonNull(jsonObject)) {
                    detailBaseInfoVO.setSideImage(jsonObject.getString("url"));

                }
            }
            if (!StringUtils.isEmpty(goodsRecord.getBackJson())) {
                JSONObject jsonObject = JSON.parseObject(goodsRecord.getBackJson());
                if (Objects.nonNull(jsonObject)) {
                    detailBaseInfoVO.setBackImage(jsonObject.getString("url"));
                }
            }
            if (!StringUtils.isEmpty(goodsRecord.getLabelJson())) {
                JSONObject jsonObject = JSON.parseObject(goodsRecord.getLabelJson());
                if (Objects.nonNull(jsonObject)) {
                    detailBaseInfoVO.setLabelImage(jsonObject.getString("url"));
                }
            }
            detailBaseInfoVO.setExternalProductId(goodsRecord.getExternalMaterialCode());
        } else if (Objects.equals("ccs", examiningDataSource)) {
            detailBaseInfoVO = ConvertUtil.beanConvert(recordDTO, GoodsRecordDetailBaseInfoVO.class);
            RecordCustomsDTO recordCustomsDTO = recordCustomsService.findByRecordIdAndCustomsCode(recordDTO.getId(), customsCode);
            if (Objects.nonNull(recordCustomsDTO)) {
                String baseInfoJson = recordCustomsDTO.getBaseInfoJson();
                GoodsRecordDTO goodsRecordDTO = JSON.parseObject(baseInfoJson, GoodsRecordDTO.class);
                if (Objects.nonNull(goodsRecordDTO)) {
                    BeanUtils.copyProperties(goodsRecordDTO, detailBaseInfoVO, "id", "createTime", "updateTime", "createBy", "updateBy");
                }
                if (!StringUtils.isEmpty(detailBaseInfoVO.getTenantId())) {
                    String tenantName = taxesTenantAccountService.getTenantNameById(detailBaseInfoVO.getTenantId());
                    detailBaseInfoVO.setTenantNameDesc(tenantName);
                }
                detailBaseInfoVO.setCreateTime(new DateTime(recordCustomsDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
        }
        return detailBaseInfoVO;
    }

    /**
     * 查询备案下全部的口岸信息
     *
     * @param idParam@return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/recordCustomsList", desc = "备案关联口岸列表")
    public RpcResult<List<RecordCustomsResVO>> recordCustomsList(IdParam idParam) {

        try {
            List<RecordCustomsDTO> customsDTOList = goodsRecordService.recordCustomsDTOList(idParam.getId());
            List<RecordCustomsResVO> customsResVOS = customsDTOList.stream().filter(c -> !Objects.equals(c.getStatus(), GoodsRecordStatusEnum.WAIT_COMMIT.getCode())).map(c -> {
                RecordCustomsResVO resVO = ConvertUtil.beanConvert(c, RecordCustomsResVO.class);
                resVO.setId(c.getId().toString());
                resVO.setStatusDesc(GoodsRecordStatusEnum.getEnum(c.getStatus()).getDesc());
                return resVO;
            }).sorted(Comparator.comparing(RecordCustomsResVO::getStatus)).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(customsResVOS)) {
                return RpcResult.success(customsResVOS);
            }

            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findById(idParam.getId());
            // 老备案，料号为空，根据账册找对应口岸
            if (StringUtils.isEmpty(goodsRecordDTO.getProductId())
                    && Objects.equals(goodsRecordDTO.getRecordType(), GoodsRecordTypeEnums.OLD.getCode())) {
                // 根据账册获取口岸，一本账册只会有一个口岸，所以可以直接取一个
                List<EntityWarehouseDTO> entityWarehouseDTOList = entityWarehouseService.findByCustomsBookId(goodsRecordDTO.getCustomsBookId());
                if (CollectionUtils.isEmpty(entityWarehouseDTOList)) {
                    throw new ArgsInvalidException(String.format("料号[%s]账册对应的实体仓未配置", goodsRecordDTO.getProductId()));
                }
                EntityWarehouseDTO entityWarehouseDTO = entityWarehouseDTOList.get(0);
                RecordCustomsResVO customsResVO = new RecordCustomsResVO();
                customsResVO.setRecordId(goodsRecordDTO.getId());
                customsResVO.setCustomsCode(entityWarehouseDTO.getCustomsCode());
                customsResVO.setCustoms(entityWarehouseDTO.getCustoms());
                customsResVO.setHsCode(goodsRecordDTO.getHsCode());
                customsResVO.setFirstUnit(goodsRecordDTO.getFirstUnit());
                customsResVO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
                customsResVO.setSecondUnit(goodsRecordDTO.getSecondUnit());
                customsResVO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
                customsResVO.setStatus(goodsRecordDTO.getRecordStatus());
                customsResVO.setStatusDesc(GoodsRecordStatusEnum.getEnum(goodsRecordDTO.getRecordStatus()).getDesc());

                return RpcResult.success(new ArrayList<RecordCustomsResVO>() {{
                    add(customsResVO);
                }});
            }
            return RpcResult.success();

        } catch (Exception e) {
            log.error("recordCustomsList error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询备案关联口岸失败");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/import/query", desc = "备案导入查询")
    public RpcResult<LoadTaskInfoDTO> goodsRecordImportQuery() {
        UploadType uploadType = UploadType.CUSTOMS_GOODS_RECORD;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setTemplateUrl(uploadType.getUrl());
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        return RpcResult.success(loadTaskInfoDTO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/import", desc = "备案导入")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> goodsRecordImport(UrlParam urlParam) {
        try {
            uploadProcessService.submitProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    urlParam.getUrl(), UploadType.CUSTOMS_GOODS_RECORD, urlParam);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            ;
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/import/confirm", desc = "备案导入确认")
    public RpcResult<String> goodsRecordImportConfirm() {
        UploadType uploadType = UploadType.CUSTOMS_GOODS_RECORD;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success("提交成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/listWares", desc = "获取仓库列表")
    public RpcResult<List<SelectOptionVO<String>>> listWares() throws ArgsErrorException {
        Map<String, String> warehouseDesMap = warehouseService.getWarehouseDesMap();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (String key : warehouseDesMap.keySet()) {
            SelectOptionVO optionDTO = new SelectOptionVO();
            optionDTO.setId(key);
            optionDTO.setName(warehouseDesMap.get(key));
            result.add(optionDTO);
        }

        return RpcResult.success(result);
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/listcWares", desc = "获取仓库列表c")
    public RpcResult<List<SelectOptionVO<String>>> listcWares() throws ArgsErrorException {
        Map<String, String> warehouseDesMap = warehouseService.getWarehouseDesMap();
        List<SelectOptionVO<String>> result = new ArrayList<>();
        for (String key : warehouseDesMap.keySet()) {
            SelectOptionVO optionDTO = new SelectOptionVO();
            optionDTO.setId(key);
            optionDTO.setName(warehouseDesMap.get(key));
            result.add(optionDTO);
        }

        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/exportExcel", desc = "导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> exportExcel(GoodsRecordSearchCondition condition) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    condition, ReportType.GOODS_RECORD_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/viewAssociatedInfo", desc = "查看关联清关信息")
    public RpcResult<RecordItemAssociateInfoResVO> viewAssociatedInfo(IdParam idParam) {
        RecordItemAssociateInfoResVO info;
        try {
            info = goodsRecordService.viewAssociatedInfoVO(idParam.getId());
            log.info("viewAssociatedInfo info={}", JSON.toJSONString(info));
            return RpcResult.success(info);
        } catch (ArgsErrorException e) {
            log.warn("viewAssociatedInfo id={} 查看关联信息失败：{}", idParam.getId(), e.getErrorMessage(), e);
            return RpcResult.error("查看关联信息失败");
        } catch (Exception e) {
            log.warn("viewAssociatedInfo id={} 查看关联信息失败：{}", idParam.getId(), e.getMessage(), e);
            return RpcResult.error("查看关联信息失败");
        }
    }

    /**
     * 获取该备案下所有账册 返回list
     *
     * @param idParam
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/recordCustomsBookList", desc = "查看备案关联账册")
    public RpcResult<List<SelectOptionVO<Long>>> recordCustomsBookList(IdParam idParam) {
        try {
            List<CustomsBookDTO> customsBookList = recordWarehouseService.recordCustomsBookList(idParam.getId());
            List<SelectOptionVO<Long>> optionVOList = new ArrayList<>();
            for (CustomsBookDTO book : customsBookList) {
                SelectOptionVO<Long> vo = new SelectOptionVO<>();
                vo.setId(book.getId());
                vo.setName(book.getBookNo());
                optionVOList.add(vo);
            }
            return RpcResult.success(optionVOList);
        } catch (Exception e) {
            log.error("recordCustomsBookList 获取账册列表失败");
        }
        return RpcResult.error("未查询到关联账册");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/modifyAssociatedInfo", desc = "修改关联料号")
    public RpcResult<String> modifyAssociatedInfo(RecordModifyAssociateInfoSubmit submit) {
        try {
            goodsRecordService.modifyAssociatedInfo(submit);
            return RpcResult.success("保存成功");
        } catch (ArgsErrorException e) {
            log.warn("modifyAssociatedInfo 修改关联料号 保存失败：{}", e.getErrorMessage(), e);
            return RpcResult.error("保存失败");
        }
    }

    /**
     * 备案+口岸新版上线后 废弃
     *
     * @param idParam
     * @return
     */
    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/auditPassPreCheck", desc = "审核通过预校验")
    public RpcResult<GoodsRecordAuditPassResVO> auditPassPreCheck(IdParam idParam) {
        try {
            GoodsRecordAuditPassResVO res = goodsRecordService.auditPassPreCheck(idParam.getId());
            return RpcResult.success(res);
        } catch (Exception e) {
            log.warn("auditPassPreCheck 商品备案审核通过校验失败", e);
            return RpcResult.error("审核通过失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/viewItemListByGoodsRecordId", desc = "备案审核查看账册信息")
    public RpcResult<List<AssociateCustomsBookItemResVO>> viewItemListByGoodsRecordId(IdParam idParam) {
        try {
            List<AssociateCustomsBookItemResVO> list = goodsRecordService.viewItemListByGoodsRecordId(idParam.getId());
            return RpcResult.success(list);
        } catch (Exception e) {
            log.warn("viewItemListByGoodsRecordId 备案审核查看账册信息失败");
            return RpcResult.error("查询账册信息失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/auditPassPreCheckV2", desc = "审核通过预校验")
    public RpcResult<GoodsRecordAuditPassResVO> auditPassPreCheckV2(Long recordId, String customsCode) {
        try {
            GoodsRecordAuditPassResVO res = goodsRecordService.auditPassPreCheck(recordId, customsCode);
            return RpcResult.success(res);
        } catch (Exception e) {
            log.warn("auditPassPreCheck 商品备案审核通过校验失败", e);
            return RpcResult.error("审核通过失败");
        }
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/goodsRecord/pagingTrackLog", desc = "商品备案日志(ES)")
    public RpcResult<ListVO<RecordTrackLogEsVO>> pagingTrackLog(RecordTrackLogSearch search) {
        ListVO<RecordTrackLogEsDTO> paging = goodsRecordService.pagingTrackLog(search);
        ListVO<RecordTrackLogEsVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map(p -> {
            RecordTrackLogEsVO vo = new RecordTrackLogEsVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }


    @Override
    @SoulClient(path = "/goodsRecord/goodsRecordSource", desc = "来源下拉")
    public RpcResult goodsRecordSource() {
        return RpcResult.success(EnumUtils.build(GoodsRecordEnum.class, "code", "desc"));
    }

    @Override
    @SoulClient(path = "/goodsRecord/deleteRecordCustomsProductById", desc = "删除商品列表")
    public RpcResult<String> deleteRecordCustomsProductById(Long id) {
        try {
            goodsRecordService.deleteRecordCustomsProduct(id);
            return RpcResult.success("删除成功");
        } catch (Exception e) {
            log.error("删除商品列表异常 error={}", e.getMessage(), e);
        }
        return RpcResult.error("删除失败");
    }

    /**
     * 手动添加实体仓
     *
     * @param recordCustomsId
     * @param entityWarehouseSn
     * @return
     */
    @Override
    @SoulClient(path = "/goodsRecord/saveNewWarehouseInfo", desc = "手动保存实体仓")
    public RpcResult<String> saveNewWarehouseInfo(Long recordCustomsId, String entityWarehouseSn) {
        try {
            goodsRecordService.saveNewWarehouseInfo(recordCustomsId, entityWarehouseSn);
            return RpcResult.success("保存成功");
        } catch (Exception e) {
            log.error("saveNewWarehouseInfo 手动保存实体仓失败 error={}", e.getMessage(), e);
            return RpcResult.error("保存失败 " + e.getMessage());
        }
    }

    /**
     * 获取商品列表标记
     *
     * @return
     */
    @Override
    @SoulClient(path = "/goodsRecord/listGoodsRecordTag", desc = "手动保存实体仓")
    public RpcResult<List<SelectOptionVO<Integer>>> listGoodsRecordTag() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(GoodsRecordTagEnums.values()).filter(tag -> !tag.equals(GoodsRecordTagEnums.NULL))
                .map(tag -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO();
                    optionDTO.setId(tag.getCode());
                    optionDTO.setName(tag.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 获取备案各状态的计数统计
     *
     * @return
     */
    @Override
    @SoulClient(path = "/goodsRecord/getRecordStatusCount", desc = "获取备案各状态的计数统计")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<GoodsRecordStatusCount> getRecordStatusCount() {
        try {
            List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
            GoodsRecordStatusCount count = goodsRecordService.getRecordStatusCount(accountBookList);
            log.info("获取备案状态计数统计 " + JSON.toJSONString(count));
            return RpcResult.success(count);
        } catch (Exception e) {
            log.error("获取备案状态计数统计失败 error={}", e.getMessage(), e);
            return RpcResult.error("获取备案状态计数统计失败");
        }
    }

    /**
     * 获取备案各状态的计数统计
     *
     * @return
     */
    @Override
    @SoulClient(path = "/goodsRecord/getGoodsRecordStatusCount", desc = "获取商品备案各状态的计数统计")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true, addAccBookToField = false)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<GoodsRecordStatusCount> getGoodsRecordStatusCount(GoodsRecordSearchCondition condition) {
        try {
            List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
            GoodsRecordStatusCount count = goodsRecordService.getRecordStatusCount(condition, accountBookList);
            log.info("获取备案状态计数统计 " + JSON.toJSONString(count));
            return RpcResult.success(count);
        } catch (Exception e) {
            log.error("获取备案状态计数统计失败 error={}", e.getMessage(), e);
            return RpcResult.error("获取备案状态计数统计失败");
        }
    }


    /**
     * 更新备案 （备案完成的备案 更新同步erp）
     *
     * @param reqVo
     * @return RpcResult
     * @path /goodsRecord/updateFinishGoodsRecord
     */
    @Override
    @SoulClient(path = "/goodsRecord/updateFinishGoodsRecord", desc = "更新备案(备案完成)")
    public RpcResult updateFinishGoodsRecord(UpdFinishGoodsRecordReqVo reqVo) {
        if (Objects.isNull(reqVo)) {
            return RpcResult.error("参数为空");
        }
        try {
            if (StrUtil.isNotBlank(reqVo.getGoodsName())) {
                reqVo.setGoodsName(StringUtils.trimWhitespace(reqVo.getGoodsName()));
            }
            goodsRecordService.updateFinishGoodsRecord(reqVo);
            aiRecommendHsCodeSendMsg(reqVo.getId(), reqVo.getCustomsCode(), reqVo.getHsCode(), reqVo.getAiRecommendHsCode());
            return RpcResult.success();
        } catch (RuntimeException e) {
            return RpcResult.error(e.getMessage());
        }
    }

    private void aiRecommendHsCodeSendMsg(Long recordId, String customsCode, String hsCode, String recommendHsCode) {
        if (StrUtil.isNotBlank(recommendHsCode) && Objects.equals(recommendHsCode, hsCode)) {
            AiRecommendHsCodeDTO aiRecommendHsCodeDTO = new AiRecommendHsCodeDTO();
            aiRecommendHsCodeDTO.setRecordId(recordId);
            aiRecommendHsCodeDTO.setCustomsCode(customsCode);
            aiRecommendHsCodeDTO.setRecommendHsCode(recommendHsCode);
            messageSender.sendMsg(JSON.toJSONString(aiRecommendHsCodeDTO), "ccs-ai-recommend-hsCode-choose-topic");
        }
    }

    /**
     * 审核方式下拉
     *
     * @return
     * @path /goodsRecord/listAuditWay
     */
    @SoulClient(path = "/goodsRecord/listAuditWay", desc = "审核方式下拉")
    @Override
    public RpcResult<List<SelectOptionVO<Integer>>> listAuditWay() {
        List<SelectOptionVO<Integer>> list = Arrays.stream(GoodsRecordAuditWayEnums.values())
                .filter(i -> !Objects.equals(GoodsRecordAuditWayEnums.NULL, i))
                .map(e -> {
                    SelectOptionVO<Integer> selectOptionVO = new SelectOptionVO<>();
                    selectOptionVO.setId(e.getCode());
                    selectOptionVO.setName(e.getDesc());
                    return selectOptionVO;
                }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 重试备案回传
     *
     * @param idsParam 备案id
     * @return
     * @path /goodsRecord/retryAuditMsg
     */
    @Override
    @SoulClient(path = "/goodsRecord/retryAuditMsg", desc = "重试备案回传")
    public RpcResult<String> retryAuditMsg(IdsParam idsParam) {
        if (Objects.isNull(idsParam)) {
            return RpcResult.error("参数不能为空");
        }
        try {
            goodsRecordService.retryAuditMsg(idsParam.getIdList());
            return RpcResult.success("重试成功");
        } catch (Exception e) {
            log.error("重试备案回传失败 - {}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/goodsRecord/getRejectReason", desc = "查看驳回原因")
    public RpcResult<List<GoodsRecordRejectReasonResVo>> getRejectReason() {
        try {
            List<CustomsDictionaryDTO> customsDictionaryDTOList = customsDictionaryService.findByType(DataDictionaryTypeEnums.GOODS_RECORD_REJECT_REASON.getValue());
            List<GoodsRecordRejectReasonResVo> resVoList = customsDictionaryDTOList.stream().map(c -> {
                GoodsRecordRejectReasonResVo resVo = new GoodsRecordRejectReasonResVo();
                resVo.setReason(c.getName());
                resVo.setId(c.getId().toString());
                return resVo;
            }).collect(Collectors.toList());
            return RpcResult.success(resVoList);
        } catch (Exception e) {
            log.error("getRejectReason error={}", e.getMessage(), e);
            return RpcResult.error("获取驳回原因失败");
        }
    }

    @Override
    @SoulClient(path = "/goodsRecord/addRejectReason", desc = "添加驳回原因")
    public RpcResult<String> addRejectReason(GoodsRecordRejectReasonReqVo recordRejectReasonReqVo) {
        try {
            List<String> addRejectReasonList = recordRejectReasonReqVo.getAddRejectReasonList();
            List<CustomsDictionaryOperateParam> paramList = addRejectReasonList.stream().map(reason -> {
                CustomsDictionaryOperateParam param = new CustomsDictionaryOperateParam();
                param.setCode(UUID.randomUUID().toString().replaceAll("-", ""))
                        .setName(reason)
                        .setType(DataDictionaryTypeEnums.GOODS_RECORD_REJECT_REASON.getValue());
                return param;
            }).collect(Collectors.toList());
            customsDictionaryService.insert(paramList);
            return RpcResult.success("添加驳回原因成功");
        } catch (Exception e) {
            log.error("addRejectReason error={}", e.getMessage(), e);
            return RpcResult.error("添加驳回原因失败");
        }
    }

    /**
     * ai推荐 商品编码
     *
     * @param reqVo
     * @path /goodsRecord/aiRecommendHsCodeList
     * @result
     */
    @Override
    @SoulClient(path = "/goodsRecord/aiRecommendHsCodeList", desc = "ai推荐 商品编码")
    public RpcResult<AiRecommendHsCodeResVo> aiRecommendHsCodeList(@RequestBody AiRecommendHsCodeReqVo reqVo) {
        return goodsRecordController.aiRecommendHsCodeList(reqVo);
    }

//    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
//    @SoulClient(path = "/goodsRecord/findRecord", desc = "test(ES)")
//    public RpcResult findRecord() {
//        Long booId1 = 2L;
//        Long booId2 = 2L;
//        String productId = "010015722031750624";
//        String userId = "100157";
//        String skuId = "G1619156355799";
//        goodsRecordService.findByBookIdAndProId(booId1,productId);
//        goodsRecordService.findByBookIdAndSkuId(booId2,skuId);
////        GoodsRecordDTO paging = goodsRecordService.findByBookIdUserIdAndSkuId(booId2,userId,skuId);
//        return RpcResult.success(true);
//    }
}
