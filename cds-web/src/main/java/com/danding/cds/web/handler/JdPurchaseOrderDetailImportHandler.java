package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.download.api.vo.JdPurchaseOrderDetailExcelVO;
import com.danding.cds.v2.api.JdPurchaseOrderNewService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/11/14 16:51
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_JD_PURCHASE_DETAIL", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E4%BA%AC%E4%B8%9C%E9%87%87%E8%B4%AD%E5%8D%95%E6%98%8E%E7%BB%86%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "京东采购单明细", classes = JdPurchaseOrderDetailExcelVO.class),})
public class JdPurchaseOrderDetailImportHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<JdPurchaseOrderDetailExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("京东采购单明细")) {
                list = group.getDataList(JdPurchaseOrderDetailExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            return;
        }
        int index = 1;
        JdPurchaseOrderNewService jdPurchaseOrderNewService = this.getBean(JdPurchaseOrderNewService.class);
        String orderIdString = (String) extendMap.get("orderId");
        Long orderId = Long.valueOf(orderIdString);
        for (JdPurchaseOrderDetailExcelVO excelVO : list) {
            ImportResultResVo resultResVo = jdPurchaseOrderNewService.editDetailByExcel(orderId, excelVO.getSku(), excelVO.getEmg());
            log.info("京东采购单商品明细编辑：{} sku:{} emg:{} 导入后的返回结果数据{}", excelVO.getSku(), excelVO.getEmg(), resultResVo.toString());
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }
}
