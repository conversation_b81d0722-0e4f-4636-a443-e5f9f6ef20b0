package com.danding.cds.web.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.transwork.api.enums.TransOrderTypeEnum;
import com.danding.cds.vehicleResource.dto.VehicleResourceDTO;
import com.danding.cds.vehicleResource.enums.LoadableContainerTypeEnums;
import com.danding.cds.vehicleResource.enums.VehicleResourceTypeEnums;
import com.danding.cds.vehicleResource.service.VehicleResourceService;
import com.danding.cds.web.v2.bean.vo.req.VehicleResourceImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.github.pagehelper.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 车辆资源导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_VEHICLE_RESOURCE", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E8%BD%A6%E8%BE%86%E8%B5%84%E6%BA%90%E7%BC%96%E7%A0%81%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "sheet1", classes = VehicleResourceImportExcelVO.class),})
public class VehicleResourceImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        VehicleResourceService vehicleResourceService = this.getBean(VehicleResourceService.class);

        List<VehicleResourceImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("sheet1")) {
                list = group.getDataList(VehicleResourceImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("VehicleResourceImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 3;
        Set<String> vehicleResourceCodeSet = new HashSet<>();
        List<String> plateNoList = list.stream().map(VehicleResourceImportExcelVO::getVehiclePlate)
                .filter(StringUtils::isNotBlank).collect(Collectors.toList());
        List<VehicleResourceDTO> dtoListByPlate = vehicleResourceService.findByVehiclePlate(plateNoList);
        Map<String, List<VehicleResourceDTO>> plateNoMap = dtoListByPlate.stream()
                .filter(i -> Objects.equals(i.getEnable(), 1))
                .collect(Collectors.groupingBy(VehicleResourceDTO::getVehiclePlate));


        for (VehicleResourceImportExcelVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            if (StringUtils.isBlank(vo.getVehicleResourceCode())) {
                errorMsg.add("车辆资源code不能为空");
            }
            VehicleResourceTypeEnums vehicleResourceTypeEnums = VehicleResourceTypeEnums.getEnumByDesc(vo.getVehicleType());
            if (StringUtils.isBlank(vo.getVehicleType())) {
                errorMsg.add("车辆类型不能为空");
            } else {
                if (Objects.isNull(vehicleResourceTypeEnums)) {
                    errorMsg.add("车辆类型不存在");
                }
            }
//            if (StringUtils.isBlank(vo.getVehiclePlate())) {
//                errorMsg.add("车牌号不能为空");
//            }
            if (StringUtils.isBlank(vo.getGoodsTransportType())) {
                errorMsg.add("货品运输类型不能为空");
            }
            if (Objects.equals("集装箱车", vo.getVehicleType())
                    && LoadableContainerTypeEnums.getEnum(vo.getLoadableContainerType()).equals(LoadableContainerTypeEnums.EMPTY)) {
                errorMsg.add("车辆类型为集装箱车时，可装载集装箱类型不能为空");
            }
            if (vehicleResourceCodeSet.contains(vo.getVehicleResourceCode() + "_" + vo.getVehiclePlate())) {
                errorMsg.add("【车辆资源code + 车牌号】 存在重复");
            } else {
                vehicleResourceCodeSet.add(vo.getVehicleResourceCode() + "_" + vo.getVehiclePlate());
            }
            TransOrderTypeEnum vehiclePurposeEnum = TransOrderTypeEnum.getEnumByDesc(vo.getVehiclePurpose());
            if (StringUtils.isBlank(vo.getVehiclePurpose())) {
                errorMsg.add("车辆用途不能为空");
            } else {
                if (Objects.isNull(vehiclePurposeEnum)) {
                    errorMsg.add("车辆用途不存在");
                }
            }
            if (StringUtil.isNotEmpty(vo.getVehiclePlate()) && !isValidPlateNumber(vo.getVehiclePlate())) {
                errorMsg.add("车牌号校验失败，请输入正确的车牌号");
            }
            if (plateNoMap.containsKey(vo.getVehiclePlate())) {
                List<VehicleResourceDTO> vehicleResourceDTOS = plateNoMap.get(vo.getVehiclePlate());
                if (CollUtil.isNotEmpty(vehicleResourceDTOS)
                        && vehicleResourceDTOS.stream().anyMatch(i -> !Objects.equals(vo.getVehicleResourceCode(), i.getVehicleResourceCode()))) {
                    errorMsg.add("已存在启用的车牌号");
                }
            }
            if (CollUtil.isNotEmpty(errorMsg)) {
                this.callbackData(false, index++, String.join(";", errorMsg), vo);
                continue;
            }

            VehicleResourceDTO vehicleResourceDTO = new VehicleResourceDTO();
            BeanUtil.copyProperties(vo, vehicleResourceDTO);
            vehicleResourceDTO.setVehicleType(Objects.requireNonNull(vehicleResourceTypeEnums).getValue());
            vehicleResourceDTO.setVehiclePurpose(Objects.requireNonNull(vehiclePurposeEnum).getCode());
            vehicleResourceDTO.setLength(Optional.ofNullable(vo.getLength()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vehicleResourceDTO.setWidth(Optional.ofNullable(vo.getWidth()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vehicleResourceDTO.setHeight(Optional.ofNullable(vo.getHeight()).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP));
            vehicleResourceDTO.setVehicleDimension(String.format("车长：%.2f，车宽：%.2f，车高：%.2f",
                    vehicleResourceDTO.getLength(), vehicleResourceDTO.getWidth(), vehicleResourceDTO.getHeight()));
            ImportResultResVo res = vehicleResourceService.importExcel(vehicleResourceDTO);
            log.info("车辆资源导入,导入后的返回结果数据{}", JSON.toJSONString(res));
            this.callbackData(res.getFlag(), index++, res.getReason(), vo);
        }
    }

    // 定义用于匹配车牌号的正则表达式（简化版，不包含武警车牌、港澳车牌等特殊格式）
    private static final String PLATE_NUMBER_PATTERN =
            "^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z]{1}[A-Z0-9]{5}$|" +
                    "^([京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[A-Z]{1}[A-D0-9]{5})[挂学警军港澳]$";

    public static boolean isValidPlateNumber(String plateNumber) {
        Pattern pattern = Pattern.compile(PLATE_NUMBER_PATTERN);
        Matcher matcher = pattern.matcher(plateNumber);

        // 使用matcher对象进行匹配
        return matcher.matches();
    }

}
