package com.danding.cds.web.payChannel.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.util.Date;

/**
 * @Author: Raymond
 * @Date: 2020/8/24 15:08
 * @Description:
 */
@Data
@ApiModel
public class PayMerchantCustomsInfoResult {
    @ApiModelProperty(value = "id")
    private Long id;
    /**
     * 商户id
     */
    @ApiModelProperty(value = "商户id")
    private Long merchantId;

    /**
     * 商户编码
     */
    @ApiModelProperty(value = "商户编码")
    private String merchantSn;

    @ApiModelProperty(value = "商户名称")
    private String merchantName;

    /**
     * 口岸
     */
    @ApiModelProperty(value = "口岸")
    private String customs;

    /**
     * 商户海关备案编号
     */
    @ApiModelProperty(value = "商户海关备案编号")
    private String merchantCustomsCode;

    /**
     * 商户海关备案编号
     */
    @ApiModelProperty(value = "商户海关备案名称")
    private String merchantCustomsName;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
