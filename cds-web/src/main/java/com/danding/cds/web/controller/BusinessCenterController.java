package com.danding.cds.web.controller;

import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.user.UserUtils;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.dictionary.api.vo.CustomsDictionaryOperateParam;
import com.danding.cds.customs.inventory.api.dto.CalloffAfterSalesCount;
import com.danding.cds.customs.inventory.api.dto.CalloffCountDTO;
import com.danding.cds.customs.inventory.api.dto.CancelAndRefundAfterSaleCount;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCancelService;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.dto.CustomsCompleteCountDTO;
import com.danding.cds.invenorder.api.dto.InvBusinessCountDTO;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.GoodsRecordBusinessCountDetail;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.itemstocklist.api.service.ItemStockListService;
import com.danding.cds.itemstocklist.api.vo.BcItemStockListPagingResVO;
import com.danding.cds.itemstocklist.api.vo.BcItemStockListSearch;
import com.danding.cds.v2.bean.dto.GoodsRecordBusinessCount;
import com.danding.cds.v2.bean.vo.req.OverViewSearch;
import com.danding.cds.web.businessCenter.entity.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.PageResult;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.exception.BusinessException;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 业务中台
 * @date 2022/8/29
 */

@Slf4j
@Api(tags = "ccs业务统计")
@RestController
@RequestMapping("/ccs")
public class BusinessCenterController {

    // getRecordBusinessCount 备案业务统计
    @DubboReference
    private GoodsRecordService goodsRecordService;
    // selectCalloffCount 售后保税业务统计
    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    // inventoryOrderInfoCount 清关单业务统计
    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;
    //selectEndorsementBussinessCount 核注统计
    @DubboReference
    private EndorsementService endorsementService;
    //selectEndorsementBussinessCount 核放统计
    @DubboReference
    private ChecklistService checklistService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @DubboReference
    private CustomsInventoryCancelService customsInventoryCancelService;

    @DubboReference
    private RefundOrderService refundOrderService;

    @DubboReference
    private ItemStockListService itemStockListService;

    @PostMapping("/businessCenter/dataCount")
    public AllCountRes getAllDataCount(@RequestBody PersonWorkbenchReqVO reqVO) {
        AllCountRes allCountRes = new AllCountRes();
        InvBusinessCountDTO infoCountDTO = inventoryOrderInfoService.selectInventoryOrderInfoCount(reqVO.getInvBusinessAreaCompanyId());
        InvBusinessCountDTO endorsementCountDTO = endorsementService.selectEndorsementBussinessCount(reqVO.getInvBusinessAreaCompanyId());
        InvBusinessCountDTO checklistCountDTO = checklistService.selectChecklistBussinessCount(reqVO.getInvBusinessAreaCompanyId());

        //备案
        GoodsRecordBusinessCount goodsRecordBusinessCount = goodsRecordService.getRecordBusinessCount(reqVO.getGoodsRecordWareHouseCode());
        allCountRes.setGoodsRecords(goodsRecordBusinessCount);
        //保税售后
        CalloffCountDTO calloffCountDTO = customsInventoryCalloffService.selectCalloffCount(reqVO.getAfterSalesAreaCompanyId());
        allCountRes.setPostSale(calloffCountDTO);

        InvBusinessCountResVO onelineInCount = new InvBusinessCountResVO();
        InvBusinessCountResVO sectionIn = new InvBusinessCountResVO();
        InvBusinessCountResVO sectionInnerIn = new InvBusinessCountResVO();
        InvBusinessCountResVO sectionOut = new InvBusinessCountResVO();
        InvBusinessCountResVO sectionInnerOut = new InvBusinessCountResVO();
        InvBusinessCountResVO refundInArea = new InvBusinessCountResVO();
        InvBusinessCountResVO destroy = new InvBusinessCountResVO();
        InvBusinessCountResVO onelineRefund = new InvBusinessCountResVO();
        InvBusinessCountResVO bondedToTrade = new InvBusinessCountResVO();
        InvBusinessCountResVO subsequentTax = new InvBusinessCountResVO();


        onelineInCount.setInventoryOrderInfo(infoCountDTO.getOnelineInCount());
        onelineInCount.setEndorsement(endorsementCountDTO.getOnelineInCount());
        onelineInCount.setChecklist(checklistCountDTO.getOnelineInCount());

        sectionIn.setInventoryOrderInfo(infoCountDTO.getSectionInCount());
        sectionIn.setChecklist(checklistCountDTO.getSectionInCount());
        sectionIn.setEndorsement(endorsementCountDTO.getSectionInCount());

        sectionInnerIn.setInventoryOrderInfo(infoCountDTO.getSectionInnerInCount());
        sectionInnerIn.setEndorsement(endorsementCountDTO.getSectionInnerInCount());
        sectionInnerIn.setChecklist(checklistCountDTO.getSectionInnerInCount());

        sectionInnerOut.setInventoryOrderInfo(infoCountDTO.getSectionInnerOutCount());
        sectionInnerOut.setEndorsement(endorsementCountDTO.getSectionInnerOutCount());
        sectionInnerOut.setChecklist(checklistCountDTO.getSectionInnerOutCount());

        sectionOut.setInventoryOrderInfo(infoCountDTO.getSectionOutCount());
        sectionOut.setEndorsement(endorsementCountDTO.getSectionOutCount());
        sectionOut.setChecklist(checklistCountDTO.getSectionOutCount());

        refundInArea.setInventoryOrderInfo(infoCountDTO.getRefundInAreaCount());
        refundInArea.setEndorsement(endorsementCountDTO.getRefundInAreaCount());
        refundInArea.setChecklist(checklistCountDTO.getRefundInAreaCount());

        destroy.setInventoryOrderInfo(infoCountDTO.getDestroyCount());
        destroy.setEndorsement(endorsementCountDTO.getDestroyCount());
        destroy.setChecklist(checklistCountDTO.getDestroyCount());

        onelineRefund.setInventoryOrderInfo(infoCountDTO.getOneLineRefund());
        onelineRefund.setEndorsement(endorsementCountDTO.getOneLineRefund());
        onelineRefund.setChecklist(checklistCountDTO.getOneLineRefund());

        bondedToTrade.setInventoryOrderInfo(infoCountDTO.getBondedToTrade());
        bondedToTrade.setEndorsement(endorsementCountDTO.getBondedToTrade());
        bondedToTrade.setChecklist(checklistCountDTO.getBondedToTrade());

        subsequentTax.setInventoryOrderInfo(infoCountDTO.getSubsequentTax());
        subsequentTax.setEndorsement(endorsementCountDTO.getSubsequentTax());
        subsequentTax.setChecklist(checklistCountDTO.getSubsequentTax());


        allCountRes.setOnelineInCount(onelineInCount);
        allCountRes.setRefundInArea(refundInArea);
        allCountRes.setSectionIn(sectionIn);
        allCountRes.setSectionOut(sectionOut);
        allCountRes.setSectionInnerIn(sectionInnerIn);
        allCountRes.setSectionInnerOut(sectionInnerOut);
        allCountRes.setDestroy(destroy);
        allCountRes.setOnelineRefund(onelineRefund);
        allCountRes.setBondedToTrade(bondedToTrade);
        allCountRes.setSubsequentTax(subsequentTax);

        return allCountRes;
    }

    private List<CustomsDictionaryDTO> getCurrentPageHandlerList(int currentPage, int pageSize) {
        List<CustomsDictionaryDTO> handlerList = customsDictionaryService.findByType(DataDictionaryTypeEnums.HANDLER.getValue());
        List<CustomsDictionaryDTO> currentPageHandlerList = new ArrayList<>();
        int startPage = (currentPage - 1) * pageSize;
        for (int i = startPage, j = 0; i < handlerList.size() && j < pageSize; i++, j++) {
            currentPageHandlerList.add(handlerList.get(i));
        }
        return currentPageHandlerList;
    }


    @GetMapping("/goodsRecord/statusCount")
    public ListVO<GoodsRecordBusinessCountDetailVO> getStatusCountByGoodsRecord(@RequestBody OverViewSearch search) {
        ListVO<GoodsRecordBusinessCountDetailVO> result = new ListVO<>();
        List<CustomsDictionaryDTO> handlerList = this.getCurrentPageHandlerList(search.getCurrentPage(), search.getPageSize());
        List<String> handlerIdList = handlerList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        Integer handlerSize = customsDictionaryService.getCountByType(DataDictionaryTypeEnums.HANDLER.getValue());
        Map<String, GoodsRecordBusinessCountDetail> map = goodsRecordService.getRecordBusinessCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());

        //拼出页参数
        int pageSize = search.getPageSize();
        int totalPage = handlerSize / pageSize + 1;

        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(pageSize);
        pageResult.setTotalPage(totalPage);
        pageResult.setTotalCount(handlerSize);
        result.setPage(pageResult);

        List<GoodsRecordBusinessCountDetailVO> voList = handlerList.stream().map(handler -> {
            GoodsRecordBusinessCountDetailVO countVO = new GoodsRecordBusinessCountDetailVO();
            GoodsRecordBusinessCountDetail detail = map.get(handler.getCode());
            BeanUtils.copyProperties(detail, countVO);
            countVO.setUserName(handler.getName());
            return countVO;
        }).collect(Collectors.toList());
        result.setDataList(voList);
        return result;
    }

    @GetMapping("/bonded/postSale")
    public ListVO<AfterSalesStatusCountVO> getStatusCountByBondedPostSale(@RequestBody OverViewSearch search) {
        ListVO<AfterSalesStatusCountVO> result = new ListVO<>();
        List<CustomsDictionaryDTO> handlerList = this.getCurrentPageHandlerList(search.getCurrentPage(), search.getPageSize());
        List<String> handlerIdList = handlerList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        Integer handlerSize = customsDictionaryService.getCountByType(DataDictionaryTypeEnums.HANDLER.getValue());

        //拼出页参数
        int pageSize = search.getPageSize();
        int totalPage = handlerSize / pageSize + 1;

        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(pageSize);
        pageResult.setTotalPage(totalPage);
        pageResult.setTotalCount(handlerSize);
        result.setPage(pageResult);

        Map<String, CalloffAfterSalesCount> calloffAfterSalesCountMap = customsInventoryCalloffService.selectCalloffCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());
        Map<String, CancelAndRefundAfterSaleCount> cancelAfterSaleCountMap = customsInventoryCancelService.selectCancelDetail(handlerIdList, search.getStartTime(), search.getEndTime());
        Map<String, CancelAndRefundAfterSaleCount> refundAfterSaleCountMap = refundOrderService.selectRefundOrderInfoCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());

        //组装页数据
        List<AfterSalesStatusCountVO> voList = handlerList.stream().map(handler -> {
            AfterSalesStatusCountVO afterSalesStatusCountVO = new AfterSalesStatusCountVO();
            afterSalesStatusCountVO.setUserId(handler.getCode());
            afterSalesStatusCountVO.setUserName(handler.getName());
            afterSalesStatusCountVO.setCalloffCount(calloffAfterSalesCountMap.get(handler.getCode()));
            afterSalesStatusCountVO.setCancelCount(cancelAfterSaleCountMap.get(handler.getCode()));
            afterSalesStatusCountVO.setRefundCount(refundAfterSaleCountMap.get(handler.getCode()));
            return afterSalesStatusCountVO;
        }).collect(Collectors.toList());
        result.setDataList(voList);
        return result;
    }

    @GetMapping("/customs/complete")
    public ListVO<CustomsCompleteCountVO> getStatusCountByCustomsComplete(@RequestBody OverViewSearch search) {
        ListVO<CustomsCompleteCountVO> result = new ListVO<>();

        List<CustomsDictionaryDTO> handlerList = this.getCurrentPageHandlerList(search.getCurrentPage(), search.getPageSize());
        List<String> handlerIdList = handlerList.stream().map(CustomsDictionaryDTO::getCode).collect(Collectors.toList());
        Integer handlerSize = customsDictionaryService.getCountByType(DataDictionaryTypeEnums.HANDLER.getValue());

        //拼出页参数
        int pageSize = search.getPageSize();
        int totalPage = handlerSize / pageSize + 1;

        PageResult pageResult = new PageResult();
        pageResult.setCurrentPage(search.getCurrentPage());
        pageResult.setPageSize(pageSize);
        pageResult.setTotalPage(totalPage);
        pageResult.setTotalCount(handlerSize);
        result.setPage(pageResult);

        //根据订单类型统计数量
        Map<String, CustomsCompleteCountDTO> map = new HashMap<>();
        switch (search.getType()) {
            case 1:
                map = inventoryOrderInfoService.inventoryOrderInfoCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());
                break;
            case 2:
                map = endorsementService.selectEndorsementBussinessCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());
                break;
            case 3:
                map = checklistService.selectChecklistBussinessCountDetail(handlerIdList, search.getStartTime(), search.getEndTime());
                break;
        }

        Map<String, CustomsCompleteCountDTO> finalMap = map;
        List<CustomsCompleteCountVO> voList = handlerList.stream().map(handler -> {
            CustomsCompleteCountVO countVO = new CustomsCompleteCountVO();
            countVO.setUserName(handler.getName());

            CustomsCompleteCountDTO customsCompleteCountDTO = finalMap.get(handler.getCode());
            countVO.setDestroy(customsCompleteCountDTO.getDestroy());
            countVO.setOneLineIn(customsCompleteCountDTO.getOneLineIn());
            countVO.setRefundInArea(customsCompleteCountDTO.getRefundInArea());
            countVO.setSectionIn(customsCompleteCountDTO.getSectionIn());
            countVO.setSectionOut(customsCompleteCountDTO.getSectionOut());
            countVO.setSectionInnerIn(customsCompleteCountDTO.getSectionInnerIn());
            countVO.setSectionInnerOut(customsCompleteCountDTO.getSectionInnerOut());
            return countVO;
        }).collect(Collectors.toList());
        result.setDataList(voList);

        return result;
    }

    @GetMapping("/dictionary/businessType")
    public List<SelectOptionVO<String>> listBusinessTypeFromDict() {
        return customsDictionaryService.listByType(DataDictionaryTypeEnums.BUSINESS_TYPE.getValue());
    }

    @GetMapping("/dictionary/businessType/update")
    public void updBusinessTypeInDict(@RequestBody List<SelectOptionVO> businessTypeList) {
        List<CustomsDictionaryOperateParam> list = new ArrayList<>();
        businessTypeList.forEach(s -> {
            CustomsDictionaryOperateParam param = new CustomsDictionaryOperateParam();
            param.setCode((String) s.getId());
            param.setName(s.getName());
            list.add(param);
        });
        try {
            customsDictionaryService.updateBatchByType(list, DataDictionaryTypeEnums.BUSINESS_TYPE.getValue());
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @GetMapping("/dictionary/handler/filter")
    public List<SelectOptionVO> listHandlerFilterFromDict() {
        List<SelectOptionVO<String>> handlerList = customsDictionaryService.listByType(DataDictionaryTypeEnums.HANDLER.getValue());
        if (CollectionUtils.isEmpty(handlerList)) {
            return new ArrayList<>();
        }
        Integer userId = UserUtils.getUserId();
        //过滤当前用户
        return handlerList.stream().filter(i -> !Objects.equals(userId.toString(), i.getId())).collect(Collectors.toList());
    }

    @GetMapping("/dictionary/handler")
    public List<SelectOptionVO<String>> listHandlerFromDict() {
        return customsDictionaryService.listByType(DataDictionaryTypeEnums.HANDLER.getValue());
    }

    @GetMapping("/dictionary/businessType/delete")
    public void delBusinessTypeInDict(@RequestBody SelectOptionVO vo) {
        CustomsDictionaryOperateParam param = new CustomsDictionaryOperateParam();
        param.setType(DataDictionaryTypeEnums.BUSINESS_TYPE.getValue());
        param.setName(vo.getName());
        param.setCode((String) vo.getId());
        try {
            customsDictionaryService.deleteByCodeType(param);
        } catch (Exception e) {
            throw new BusinessException(e.getMessage());
        }
    }

    @PostMapping("/businessCenter/itemStockListPaging")
    public ListVO<BcItemStockListPagingResVO> itemStockListPaging(@RequestBody BcItemStockListSearch search) {
        if (Objects.isNull(search)) {
            throw new ArgsInvalidException("参数为空");
        }
        return itemStockListService.bcPaging(search);
    }
}
