package com.danding.cds.web.item.rpc;

import com.danding.cds.common.model.CustomsDistrictCodeParam;
import com.danding.cds.common.model.IdEnableParam;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.item.api.dto.CustomsBookDTO;
import com.danding.cds.item.api.dto.CustomsBookSearchCondition;
import com.danding.cds.item.api.dto.CustomsBookSubmit;
import com.danding.cds.item.api.dto.param.CustomsBookConfigParam;
import com.danding.cds.item.api.vo.CustomsBookInfoResVo;
import com.danding.cds.web.item.vo.CustomsBookVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;

import java.util.List;

public interface CustomsBookRpc {
    /**
     * 分页查询
     *
     * @param condition
     * @return
     */
    RpcResult<ListVO<CustomsBookVO>> paging(CustomsBookSearchCondition condition);

    /**
     * 获取启用账册
     *
     * @return
     */
    RpcResult<List<SelectOptionVO>> listAllInUseBookNo();

    RpcResult<List<SelectOptionVO>> listAllInUseBookNoByNo();

    /**
     * 获取所有账册
     *
     * @return
     */
    RpcResult<List<SelectOptionVO>> listAllBookNo();

    /**
     * 用户账册列表，当前用户所有角色配置的账册
     */
    RpcResult<List<SelectOptionVO>> listBookNoByAccBookAuth();

    /**
     * 用户账册列表，当前用户所有角色配置的有效的账册
     */
    RpcResult<List<SelectOptionVO>> listEffectiveBookNoByAccBookAuth();


    /**
     * 详情
     *
     * @param idParam
     * @return
     */
    RpcResult<CustomsBookDTO> detail(IdParam idParam);

    /**
     * 新增/编辑账册(启用、禁用)
     *
     * @param submit
     * @return
     */
    RpcResult<Long> upset(CustomsBookSubmit submit);

    /**
     * 启用禁用
     *
     * @param idEnableParam
     * @return
     */
    RpcResult<String> enable(IdEnableParam idEnableParam);

    /**
     * 根据口岸查询账册
     *
     * @param param
     * @return
     */
    RpcResult findBookList(CustomsDistrictCodeParam param);

    RpcResult findBookListV2(CustomsDistrictCodeParam param);

    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBook/findBookListAll", desc = "根据口岸编码获取保税账册")
    RpcResult<List<CustomsBookInfoResVo>> findBookListAll(CustomsDistrictCodeParam param);

    /**
     * 参数配置（启用/禁用 协同单）
     *
     * @return
     */
    RpcResult updConfigParam(CustomsBookConfigParam paramVO);

    RpcResult updateBookTag(CustomsBookConfigParam paramVO);

    RpcResult<CustomsBookConfigParam> getConfigParam(IdParam bookId);

    /**
     * 启用开关下拉
     *
     * @return
     */
    RpcResult listEnableSwitch();

    RpcResult listStorageAttr();

    RpcResult listBookByStorageAttr(Integer storageAttr);

    /**
     * 账册类型下拉
     *
     * @return
     */
    public RpcResult listBookType();

    RpcResult<List<SelectOptionVO<Integer>>> listBookTags();

    /**
     * 根据账册类型下拉
     *
     * @param bookType
     * @return
     */
    RpcResult<List<SelectOptionVO>> listBookNoByType(Integer bookType);

    RpcResult<List<SelectOptionVO>> listBookNoFb();

    RpcResult<Long> findBookByAreaCompanyId(Long companyId);

    RpcResult<List<SelectOptionVO<Long>>> listBookByAreaCompany(Long areaCompanyId);

    RpcResult<List<SelectOptionVO<Long>>> listBookWithProcessTrade();

    RpcResult<List<SelectOptionVO<Long>>> listAreaCompanyAuth();
}
