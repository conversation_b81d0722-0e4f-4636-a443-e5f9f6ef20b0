package com.danding.cds.web.invenorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

@Data
public class InventoryOrderAttachVO implements Serializable {

    @ApiModelProperty("主键ID")
    private Long id;
    /**
     * 清关单ID
     */
    @ApiModelProperty("清关单ID")
    private Long refInveOrderId;
    /**
     * 清关单SN
     */
    @ApiModelProperty("清关单SN")
    private String refInveOrderSn;
    /**
     * 附件名称
     */
    @ApiModelProperty("附件名称")
    private String attachName;

    /**
     * 存储文件名
     */
    @ApiModelProperty("存储文件名")
    private String storeName;
    /**
     * 存储路径
     */
    @ApiModelProperty("存储路径")
    private String attachPath;

    @ApiModelProperty("文档类型")
    private String contentType;
    /**
     * 附件类型 0-其他 1-箱单/发票/合同
     */
    private Integer attachType;
    private String attachTypeDesc;

    /**
     * 附件来源
     */
    private Integer source;
    private String sourceDesc;

    @ApiModelProperty("接收时间")
    private Date createTime;
}
