package com.danding.cds.web.handoverOrder.rpc;

import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailParam;
import com.danding.cds.handoverOrder.api.dto.HandoverOrderDetailVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface HandoverOrderDetailRpc {

    RpcResult<ListVO<HandoverOrderDetailVO>> paging(HandoverOrderDetailParam param);

    RpcResult getStatus();

    RpcResult<String> excelExport(HandoverOrderDetailParam param);
}
