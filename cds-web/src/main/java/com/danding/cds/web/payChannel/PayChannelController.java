package com.danding.cds.web.payChannel;

import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.dto.PayChannelSearch;
import com.danding.cds.payChannel.api.dto.PayChannelSubmit;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.web.payChannel.vo.PayChannelSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/5/6 09:30
 * @Description:
 */
@Slf4j
@RestController
@Api( tags = "支付渠道管理" )
@RequestMapping("/pay/channel")
public class PayChannelController {

    @DubboReference
    private PayChannelService payChannelService;

    @DubboReference
    private CompanyService companyService;

    @GetMapping("/list")
    @ApiOperation(value = "获取已启用支付渠道下拉")
    public List<SelectItemVO> list(){
        List<PayChannelDTO> dataList = payChannelService.listAll();
        List<SelectItemVO> result = dataList.stream().map((PayChannelDTO item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getId());
            optionDTO.setName(item.getName());
            return optionDTO;
        }).collect(Collectors.toList());
        return result;
    }

    @GetMapping("/paging")
    @ApiOperation(value = "分页查询")
    public ListVO<PayChannelSearchResult> paging(PayChannelSearch search){



        ListVO<PayChannelDTO> paging = payChannelService.paging(search);
        ListVO<PayChannelSearchResult> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map((PayChannelDTO item)->{
            PayChannelSearchResult vo = new PayChannelSearchResult();
            BeanUtils.copyProperties(item,vo);
            CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(item.getPayCompanyId());
            if (companyDTO!=null){
                vo.setPayCompanyName(companyDTO.getName());
            }else {
                vo.setPayCompanyName("");
            }
            return vo;
        }).collect(Collectors.toList()));
        return result;
    }

    @ApiOperation(value = "新增|更新支付渠道")
    @PostMapping("/upset")
    public Long upset(@RequestBody PayChannelSubmit submit) throws ArgsErrorException {
        return payChannelService.upset(submit);
    }

}
