package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.user.facade.IUserRpcFacade;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.invenorder.api.dto.InventoryOrderInfoDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderItemExtra;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.v2.api.BizDeclareFormService;
import com.danding.cds.v2.bean.dto.BizDeclareFormDTO;
import com.danding.cds.v2.bean.dto.BizDeclareFormItemDTO;
import com.danding.cds.v2.bean.enums.GoodsRecordMappingWayEnums;
import com.danding.cds.v2.service.GoodsRecordAssociateService;
import com.danding.cds.web.v2.bean.vo.req.InvOrderItemEndprdImportExcelVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 清关单表体成品导入
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_INV_ORDER_ITEM_ITEM_END_PRD", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%B8%85%E5%85%B3%E5%8D%95%E6%88%90%E5%93%81%E8%A1%A8%E4%BD%93%E5%AF%BC%E5%85%A5.xlsx",
        groups = {@ParkImportsHandler.Group(name = "清关单成品表体导入", classes = InvOrderItemEndprdImportExcelVO.class),})
public class InvOrderItemEndprdImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        long inventoryOrderId = Long.parseLong((String) extendMap.get("inventoryOrderId"));
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        BizDeclareFormService bizDeclareFormService = this.getBean(BizDeclareFormService.class);
        InventoryOrderInfoService inventoryOrderInfoService = this.getBean(InventoryOrderInfoService.class);
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);
        GoodsRecordAssociateService goodsRecordAssociateService = this.getBean(GoodsRecordAssociateService.class);
        IUserRpcFacade iUserRpcFacade = this.getBean(IUserRpcFacade.class);

        List<InvOrderItemEndprdImportExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(0, 1);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("清关单成品表体导入")) {
                list = group.getDataList(InvOrderItemEndprdImportExcelVO.class);
            }
        }

        if (CollUtil.isEmpty(list)) {
            log.error("BizDeclareFormItemImportHandler-导入未获取到数据");
            return;
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        int index = 2;

        List<InventoryOrderItemDTO> successList = new ArrayList<>();
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(inventoryOrderId);
        Map<Integer, BizDeclareFormItemDTO> endprdMap = new HashMap<>();
        boolean absentDeclareFormNo = true;
        if (StrUtil.isNotBlank(infoDTO.getDeclareFormNo())) {
            BizDeclareFormDTO bizDeclareFormDTO = bizDeclareFormService.findByDeclareFormNo(infoDTO.getDeclareFormNo());
            List<BizDeclareFormItemDTO> endprdItemList = bizDeclareFormService.listEndprdSeqById(bizDeclareFormDTO.getId());
            endprdMap = endprdItemList.stream()
                    .collect(Collectors.toMap(BizDeclareFormItemDTO::getSeqNo, dto -> dto));
            absentDeclareFormNo = false;
        }

        Map<String, String> uomMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.UOM.getValue());
        Map<String, String> currencyMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.CURRENCY.getValue());
        Map<String, String> countryMap = customsDictionaryService.getMapByType(DataDictionaryTypeEnums.COUNTRY.getValue());
        Set<Integer> endprdSeqNoSet = new HashSet<>();
        for (InvOrderItemEndprdImportExcelVO vo : list) {
            List<String> errorMsg = new ArrayList<>();
            try {
                if (absentDeclareFormNo) {
                    this.callbackData(false, index++, "申报表编号不存在", vo);
                    continue;
                }
                if (Objects.isNull(vo.getEndprdSeqNo())) {
                    errorMsg.add("成品申报序号不能为空");
                } else if (!endprdMap.containsKey(vo.getEndprdSeqNo())) {
                    errorMsg.add("申报表编号【" + infoDTO.getDeclareFormNo() + "】未找到成品申报序号");
                }
                if (endprdSeqNoSet.contains(vo.getEndprdSeqNo())) {
                    errorMsg.add("重复成品申报序号");
                } else {
                    endprdSeqNoSet.add(vo.getEndprdSeqNo());
                }
                if (Objects.isNull(vo.getQty())) {
                    errorMsg.add("数量不能为空");
                }
                if (CollUtil.isNotEmpty(errorMsg)) {
                    this.callbackData(false, index++, String.join(";", errorMsg), vo);
                    continue;
                }
                BizDeclareFormItemDTO bizDeclareFormItemDTO = endprdMap.get(vo.getEndprdSeqNo());
                GoodsRecordDTO goodsRecordDTO = goodsRecordAssociateService
                        .findByBookIdAndProIdAndMappingWay(infoDTO.getBookId(), bizDeclareFormItemDTO.getProductId(),
                                GoodsRecordMappingWayEnums.WAREHOUSE_UNIFIED.getValue());
                if (Objects.isNull(goodsRecordDTO)) {
                    errorMsg.add("料号：" + bizDeclareFormItemDTO.getProductId() + "，不存在商品备案");
                    this.callbackData(false, index++, String.join(";", errorMsg), vo);
                    continue;
                }
                InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
                inventoryOrderItemDTO.setIsNew("new");
                inventoryOrderItemDTO.setRefInveOrderId(inventoryOrderId);
                inventoryOrderItemDTO.setRefInveOrderSn(infoDTO.getDeclareFormNo());
                inventoryOrderItemDTO.setOriginProductId(bizDeclareFormItemDTO.getProductId());
                //来源申报表：申报表序号、商品料号、商品名称、商品编码、规格型号、申报计量单位、法定计量单位、法定第二计量单位、申报单价、原产国、币制
                inventoryOrderItemDTO.setDeclareFormItemSeqNo(bizDeclareFormItemDTO.getSeqNo());
                inventoryOrderItemDTO.setProductId(bizDeclareFormItemDTO.getProductId());
                inventoryOrderItemDTO.setGoodsName(bizDeclareFormItemDTO.getGoodsName());
                inventoryOrderItemDTO.setGoodsModel(bizDeclareFormItemDTO.getGoodsModel());
                inventoryOrderItemDTO.setHsCode(bizDeclareFormItemDTO.getGoodsCode());
                inventoryOrderItemDTO.setUnit(bizDeclareFormItemDTO.getDeclareUnit());
                inventoryOrderItemDTO.setUnitDesc(uomMap.get(bizDeclareFormItemDTO.getDeclareUnit()));
                inventoryOrderItemDTO.setFirstUnit(bizDeclareFormItemDTO.getLawFirstUnit());
                inventoryOrderItemDTO.setFirstUnitDesc(uomMap.get(bizDeclareFormItemDTO.getLawFirstUnit()));
                inventoryOrderItemDTO.setSecondUnit(bizDeclareFormItemDTO.getLawSecondUnit());
                inventoryOrderItemDTO.setSecondUnitDesc(uomMap.get(bizDeclareFormItemDTO.getLawSecondUnit()));
                inventoryOrderItemDTO.setDeclarePrice(bizDeclareFormItemDTO.getPrice());
                inventoryOrderItemDTO.setOriginCountry(bizDeclareFormItemDTO.getCountry());
                inventoryOrderItemDTO.setOriginCountryDesc(countryMap.get(bizDeclareFormItemDTO.getCountry()));
                inventoryOrderItemDTO.setCurrency(bizDeclareFormItemDTO.getCurrency());
                inventoryOrderItemDTO.setCurrencyDesc(currencyMap.get(bizDeclareFormItemDTO.getCurrency()));
                //来源商品备案：法定数量(单)、法定第二数量(单)、净重、毛重、商品条码
                inventoryOrderItemDTO.setFirstUnitQfy(goodsRecordDTO.getFirstUnitAmount());
                inventoryOrderItemDTO.setSecondUnitQfy(goodsRecordDTO.getSecondUnitAmount());
                inventoryOrderItemDTO.setNetweight(goodsRecordDTO.getNetWeight());
                inventoryOrderItemDTO.setGrossWeight(goodsRecordDTO.getGrossWeight());
                if (Objects.nonNull(goodsRecordDTO.getGrossWeight())) {
                    inventoryOrderItemDTO.setTotalGrossWeight(goodsRecordDTO.getGrossWeight().multiply(vo.getQty()));
                }
                if (Objects.nonNull(goodsRecordDTO.getNetWeight())) {
                    inventoryOrderItemDTO.setTotalNetWeight(goodsRecordDTO.getNetWeight().multiply(vo.getQty()));
                }
                inventoryOrderItemDTO.setGoodsBar(goodsRecordDTO.getBarCode());
                inventoryOrderItemDTO.setSkuId(goodsRecordDTO.getSkuId());
                //以下字段取默认值(参考清关单表体)：最终目的国、免征方式
                inventoryOrderItemDTO.setDestinationCountry("142");
                inventoryOrderItemDTO.setAvoidTaxMethod("3");

                inventoryOrderItemDTO.setDeclareUnitQfy(vo.getQty());
                if (Objects.nonNull(bizDeclareFormItemDTO.getPrice()) && Objects.nonNull(vo.getQty())) {
                    inventoryOrderItemDTO.setDeclareTotalPrice(bizDeclareFormItemDTO.getPrice().multiply(vo.getQty()));
                }
                InventoryOrderItemExtra extra = new InventoryOrderItemExtra();
                BeanUtils.copyProperties(inventoryOrderItemDTO, extra);
                inventoryOrderItemDTO.setRefInveOrderId(inventoryOrderId);
                inventoryOrderItemDTO.setRefInveOrderSn(infoDTO.getInveCustomsSn());
                inventoryOrderItemDTO.setExtraJson(JSON.toJSONString(extra));
                successList.add(inventoryOrderItemDTO);
                this.callbackData(true, index++, null, vo);
            } catch (Exception e) {
                log.error("业务申报表表体导入异常", e);
                this.callbackData(false, index++, "系统异常", vo);
            }

        }
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        if (successList.size() == list.size()) {
            userInfo.getUserId();
            UserRpcResult userRpcResult = iUserRpcFacade.getById(userInfo.getUserId());
            String operator = userRpcResult.getUserName();
            inventoryOrderInfoService.importExcel(inventoryOrderId, successList, operator);
        }
    }
}
