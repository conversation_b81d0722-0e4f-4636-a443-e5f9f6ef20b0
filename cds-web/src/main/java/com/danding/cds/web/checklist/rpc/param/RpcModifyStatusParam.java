package com.danding.cds.web.checklist.rpc.param;

import com.google.common.collect.Lists;
import lombok.Data;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

@Data
public class RpcModifyStatusParam {

    /**
     * id
     */
    //ids 以‘，’ 逗号进行分割存储所有id， ids 和 idList 只用读取一种
    private String ids;
    private List<Long> idList;

    public List<Long> getIdList() {
        if (CollectionUtils.isEmpty(idList) && !StringUtils.isEmpty(ids)){
            idList = Lists.newArrayList(ids.split(",")).stream().map(Long::valueOf).collect(Collectors.toList());
        }
        return idList;
    }

    /**
     * 核放单状态
     * {@link com.danding.cds.checklist.api.enums.ChecklistStatusEnum}
     * 核注单状态
     * {@link com.danding.cds.endorsement.api.enums.EndorsementOrderStatus}
     */
    private String status;

    /**
     * 修改原因
     */
    private Integer operateReason;

    /**
     * 回执信息
     */
    private String informationDesc;
}
