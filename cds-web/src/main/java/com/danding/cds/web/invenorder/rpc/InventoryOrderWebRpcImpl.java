package com.danding.cds.web.invenorder.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.danding.business.client.rpc.goods.facade.IDistributionOrderRpcFacade;
import com.danding.business.client.rpc.goods.facade.IReadyOrderRpcFacade;
import com.danding.business.client.rpc.goods.param.CancelParam;
import com.danding.business.client.rpc.goods.param.DistributionOrderCancel;
import com.danding.business.client.rpc.goods.result.ResultResponse;
import com.danding.business.client.rpc.user.result.UserRpcResult;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.enums.ArrivePortEnums;
import com.danding.cds.common.enums.CategoryEnums;
import com.danding.cds.common.enums.FbChecklistTypeEnums;
import com.danding.cds.common.enums.FreightForwardingCompanyEnums;
import com.danding.cds.common.exception.ArgsErrorRpcException;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParamById;
import com.danding.cds.common.utils.*;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.enums.DataDictionaryTypeEnums;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.endorsement.api.dto.EndorsementDTO;
import com.danding.cds.endorsement.api.enums.EndorsementOrderStatus;
import com.danding.cds.endorsement.api.service.EndorsementService;
import com.danding.cds.invenorder.api.dto.*;
import com.danding.cds.invenorder.api.enums.*;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.invenorder.api.service.InventoryOrderTallyReportService;
import com.danding.cds.invenorder.api.vo.req.InventoryOrderTransitReqVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderRelationResVO;
import com.danding.cds.invenorder.api.vo.res.InventoryOrderStatusCountResVO;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.CustomsGoodsItemInfoDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.enums.GoodsRecordChannel;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.v2.api.FbChecklistService;
import com.danding.cds.v2.api.MergeRelationService;
import com.danding.cds.v2.bean.dto.*;
import com.danding.cds.v2.bean.enums.CustomsBookTypeEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.bean.enums.StorageAttrEnums;
import com.danding.cds.v2.bean.vo.req.FbChecklistAddReqVO;
import com.danding.cds.v2.bean.vo.req.InventoryBatchUrgentProcessReqVo;
import com.danding.cds.v2.bean.vo.req.InventoryOrderEditReqVo;
import com.danding.cds.v2.bean.vo.req.InventoryUrgentSortSaveReqVo;
import com.danding.cds.v2.bean.vo.res.InventoryOrderCountReqVo;
import com.danding.cds.v2.bean.vo.res.InventoryOrderInfoTransferorListResVO;
import com.danding.cds.v2.bean.vo.res.InventoryUrgentProcessResVO;
import com.danding.cds.v2.enums.InventoryOrderDraftCompareTypeEnums;
import com.danding.cds.v2.enums.InventoryOrderTagEnums;
import com.danding.cds.v2.enums.InventoryOrderTodoTagEnums;
import com.danding.cds.web.annotation.LogParam;
import com.danding.cds.web.checklist.rpc.param.RpcIdParam;
import com.danding.cds.web.config.WebBaseConfig;
import com.danding.cds.web.invenorder.InventoryOrderController;
import com.danding.cds.web.invenorder.manager.InventoryOrderItemManager;
import com.danding.cds.web.invenorder.rpc.param.RpcAttachParam;
import com.danding.cds.web.invenorder.rpc.param.RpcIdsParam;
import com.danding.cds.web.invenorder.rpc.param.RpcRelParam;
import com.danding.cds.web.invenorder.rpc.param.SeqNoReqVO;
import com.danding.cds.web.invenorder.vo.*;
import com.danding.cds.web.user.UserService;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderAttachDownloadReq;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderAuditReqVo;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderCreateReqVo;
import com.danding.cds.web.v2.bean.vo.req.InventoryOrderItemGoodsEditVO;
import com.danding.cds.web.v2.bean.vo.res.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.exception.BusinessException;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.junit.Test;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @menu 清关单管理
 */
@DubboService
@Slf4j
@RefreshScope
public class InventoryOrderWebRpcImpl implements InventoryOrderRpc {
    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;
    @Autowired
    private InventoryOrderController inventoryOrderController;
    @Autowired
    private InventoryOrderItemManager inventoryOrderItemManager;
    @DubboReference
    private IDistributionOrderRpcFacade iDistributionOrderRpcFacade;
    @DubboReference
    private IReadyOrderRpcFacade iReadyOrderRpcFacade;
    @DubboReference
    private CustomsBookService customsBookService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @DubboReference
    private CustomsUomService customsUomService;
    @DubboReference
    private CustomsCountryService customsCountryService;
    @DubboReference
    private CustomsCurrencyService customsCurrencyService;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private InventoryOrderTallyReportService inventoryOrderTallyReportService;
    @DubboReference
    private EndorsementService endorsementService;
    @DubboReference
    private DownloadProcessService downloadProcessService;
    @DubboReference
    private CompanyService companyService;
    @Autowired
    private UserService userService;
    @DubboReference
    private FbChecklistService fbChecklistService;

    @DubboReference
    private MergeRelationService mergeRelationService;

    @DubboReference
    private CustomsDictionaryService customsDictionaryService;

    @Autowired
    private WebBaseConfig webBaseConfig;

    @Value("${inventory.item.draft.compare.ignore.fields:[]}")
    private String[] draftCompareIgnoreFields;

    @Autowired
    private FourCategoryGoodsService fourCategoryGoodsService;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/list-bussiness-type", desc = "获取相关业务类型")
    public RpcResult<List<SelectOptionVO>> listBussinessType() {
        List<SelectOptionVO> result = Arrays.stream(InventoryOrderBusinessEnum.values()).filter((InventoryOrderBusinessEnum item) -> !item.equals(InventoryOrderBusinessEnum.BUSSINESS_EMPTY))
                .map((InventoryOrderBusinessEnum item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/invenorder/listTransportV2", desc = "获取运输方式")
    public RpcResult<List<SelectItemVO>> listTransport() {
        return RpcResult.success(inventoryOrderController.listTransport());
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/list-status", desc = "获取所有状态")
    public RpcResult<List<SelectOptionVO>> listStatus() {
        List<SelectOptionVO> result = Arrays.stream(InventoryOrderEnum.values()).filter((InventoryOrderEnum item) -> !item.equals(InventoryOrderEnum.EMPTY))
                .map((InventoryOrderEnum item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/invenorder/listModifyStatus", desc = "获取修改清关状态列表")
    public RpcResult<List<SelectOptionVO<String>>> listModifyStatus() {
        List<InventoryOrderEnum> inventoryOrderEnums = Lists.newArrayList(InventoryOrderEnum.STATUS_PERFECT, InventoryOrderEnum.STATUS_CONFIRMING, InventoryOrderEnum.STATUS_AUDITED, InventoryOrderEnum.STATUS_FINISH);
        List<SelectOptionVO<String>> result = inventoryOrderEnums.stream().map((InventoryOrderEnum item) -> {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getDesc());
            return optionDTO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 触发WMS生成结转
     *
     * @return
     * @path /invenorder/callWmsGenerateCarryOver
     */
    @Override
    @SoulClient(path = "/invenorder/callWmsGenerateCarryOver", desc = "触发WMS生成结转")
    public RpcResult callWmsGenerateCarryOver() {
        try {
            inventoryOrderInfoService.callWmsGenerateCarryOver();
            return RpcResult.success("触发wms生成结转成功");
        } catch (Exception e) {
            if (e instanceof ArgsInvalidException) {
                return RpcResult.error(((ArgsInvalidException) e).getErrorMessage());
            }
            return RpcResult.error("触发wms生成结转失败，请重试");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/list-relation-status", desc = "获取相关单类型")
    public RpcResult<List<SelectOptionVO>> listRelationStatus() {
        List<SelectOptionVO> result = Arrays.stream(InventoryOrderRelationEnum.values()).filter((InventoryOrderRelationEnum item) -> !item.equals(InventoryOrderRelationEnum.REL_TYPE_EMPTY))
                .map((InventoryOrderRelationEnum item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/list-select-invertory-order", desc = "获取可用清关单列表")
    public RpcResult<List<SelectOptionVO>> listCanInvertory() {
        List<InventoryOrderInfoDTO> list = inventoryOrderInfoService.findCanRefEndorsement();
        List<SelectOptionVO> result = list.stream()
                .map((InventoryOrderInfoDTO item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getId() + "[" + item.getInveBusinessType() + "]");
                    optionDTO.setName(item.getInveCustomsSn());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @SoulClient(path = "/invenorder/list-select-invertory-order-auth", desc = "获取可用清关单列表")
    public RpcResult<List<SelectOptionVO>> listCanInvertoryAuth() {
        List<Long> accountBookList = ThreadContextUtil.getAccountBookList();
        List<InventoryOrderInfoDTO> list = inventoryOrderInfoService.findCanRefEndorsement(accountBookList);
        List<SelectOptionVO> result = list.stream()
                .map((InventoryOrderInfoDTO item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getId() + "[" + item.getInveBusinessType() + "]");
                    optionDTO.setName(item.getInveCustomsSn());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listBusinessTypeById", desc = "根据id获取相关业务类型")
    public RpcResult<List<SelectOptionVO>> listBusinessTypeById(Long inventoryOrderId) {
        List<InventoryOrderBusinessEnum> typeList = inventoryOrderInfoService.listBusinessTypeById(inventoryOrderId);
        List<SelectOptionVO> result = typeList.stream().filter((InventoryOrderBusinessEnum item) -> !item.equals(InventoryOrderBusinessEnum.BUSSINESS_EMPTY))
                .map((InventoryOrderBusinessEnum item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/create-inventory-order", desc = "创建清关单(第一步)")
    public RpcResult<Response<InventoryOrderInfoDTO>> createInventoryOrder(InventoryOrderInfoDTO infoDTO) {
        return RpcResult.success(inventoryOrderController.createInventoryOrder(infoDTO));
    }

    /**
     * 创建清关单
     *
     * @param reqVo
     * @return
     * @path /invenorder/createInventoryOrderV2
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/createInventoryOrderV2", desc = "创建清关单(第一步)")
    public RpcResult<String> createInventoryOrderV2(InventoryOrderCreateReqVo reqVo) {
        try {
            log.info("清关单创建参数 reqVo={}", JSON.toJSONString(reqVo));
            InventoryOrderInfoCreateDTO inventoryOrderInfoCreateDTO = ConvertUtil.beanConvert(reqVo, InventoryOrderInfoCreateDTO.class);
            String sn = inventoryOrderInfoService.createInventoryOrderSelf(inventoryOrderInfoCreateDTO);
            log.info("清关单创建成功 sn={}", sn);
            return RpcResult.success(sn);
        } catch (Exception e) {
            log.error("清关单创建失败 error={}", e.getMessage(), e);
            return RpcResult.error("清关单创建失败" + e.getMessage());
        }
    }

    private List<String> itemAllowEditStatusList = Arrays.asList(
            InventoryOrderEnum.STATUS_CREATED.getCode(),
            InventoryOrderEnum.STATUS_CONFIRMING.getCode(),
            InventoryOrderEnum.STATUS_PERFECT.getCode(),
            InventoryOrderEnum.STATUS_AUDITED.getCode(),
            InventoryOrderEnum.STATUS_ENDORSEMENT.getCode(),
            InventoryOrderEnum.STATUS_START_STORAGED.getCode(),
            InventoryOrderEnum.STATUS_FAILURE.getCode()
    );

    @Test
    public void test1() {
        System.out.println(new BigDecimal("1.938")
                .subtract(BigDecimal.valueOf(0.017f * 114)));
        InventoryOrderItemParam inventoryOrderItemParam = new InventoryOrderItemParam();
        inventoryOrderItemParam.setTotalNetWeight(BigDecimal.valueOf(1.938));
        inventoryOrderItemParam.setTotalGrossWeight(BigDecimal.valueOf(1.937));
        if (inventoryOrderItemParam.getTotalNetWeight().compareTo(inventoryOrderItemParam.getTotalGrossWeight()) > 0) {
            System.out.println("1");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/build-inventory-order-item", desc = "创建清关单(第二步)")
    public RpcResult<Response<InventoryOrderInfoDTO>> buildInventoryOrderItem(InventoryOrderItemsParamV2 param) {
        log.info("[op:buildInventoryOrderItem] param={}", JSON.toJSONString(param));
        if (Objects.nonNull(param) && Objects.nonNull(param.getListOrderItems())) {
            Integer groupSizeSku = webBaseConfig.getExportItemWritingGroupSizeSku();
            if (param.getListOrderItems().size() > groupSizeSku) {
                return RpcResult.error("清关单表体不允许超" + groupSizeSku + "条");
            }
        }
        Response<InventoryOrderInfoDTO> oret = new Response<>();
        Long invenOrderId = param.getInvenOrderId();
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(invenOrderId);
        if (inventoryOrderInfoDTO == null) {
            oret.setCode(-1);
            oret.setErrorMessage("校验清关单失败");
            return RpcResult.success(oret);
        }
        List<EndorsementDTO> endorsementDTOS = endorsementService.listByInventory(inventoryOrderInfoDTO.getId());
        if (Objects.equals(inventoryOrderInfoDTO.getStatus(), InventoryOrderEnum.STATUS_SERVERING.getCode())) {
            if (!CollectionUtils.isEmpty(endorsementDTOS)) {
                for (EndorsementDTO e : endorsementDTOS) {
                    if (EndorsementOrderStatus.EXAMINE.getCode().equals(e.getStatus()) ||
                            EndorsementOrderStatus.FINISH.getCode().equals(e.getStatus())) {
                        oret.setCode(-1);
                        oret.setErrorMessage("清关单生成核注已审核、已完成状态不支持修改");
                        return RpcResult.success(oret);
                    }
                }
            }
        } else if (!itemAllowEditStatusList.contains(inventoryOrderInfoDTO.getStatus())) {
            oret.setCode(-1);
            oret.setErrorMessage("清关单当前状态不能操作表体");
            return RpcResult.success(oret);
        }
        List<InventoryOrderItemDTO> oldListOrderItems = inventoryOrderInfoService.findListByInvenOrderId(invenOrderId);
        inventoryOrderInfoDTO.setListItems(oldListOrderItems);
        List<InventoryOrderItemParamV2> listOrderItems = param.getListOrderItems();
        if (CollectionUtils.isEmpty(listOrderItems)) {
            oret.setCode(-1);
            oret.setErrorMessage("清关单明细为空");
//            inventoryOrderInfoService.saveInventoryItems(inventoryOrderInfoDTO, new ArrayList<>());
            oret = new Response<>(inventoryOrderInfoDTO);
            return RpcResult.success(oret);
        }
        if (!Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_REFUND_INAREA.getCode())) {
            List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(inventoryOrderInfoDTO.getOrderTag());
            // 判断是否是中转主单
            boolean isMasterOrder = orderTags.contains(InventoryOrderTagEnums.TRANSIT_MASTER_ORDER.getCode());
            Map<String, Long> skuProductCount;
            if (InventoryOrderChannel.CCS_SELF.getValue().equals(inventoryOrderInfoDTO.getChannel()) && !isMasterOrder) {
                skuProductCount = listOrderItems.stream().collect(Collectors.groupingBy(i -> "sku:" + i.getSkuId() + "料号:" + i.getProductId() + "序号:" + i.getGoodsSeqNo(), Collectors.counting()));
            } else {
                skuProductCount = listOrderItems.stream().collect(Collectors.groupingBy(i -> "sku:" + i.getSkuId() + "料号:" + i.getProductId(), Collectors.counting()));
            }
            List<String> repeatProductIdList = skuProductCount.keySet().stream().filter(k -> skuProductCount.get(k) > 1).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(repeatProductIdList)) {
                String repeatProductId = repeatProductIdList.stream().collect(Collectors.joining("/"));
                oret.setCode(-1);
                if (isMasterOrder) {
                    oret.setErrorMessage("中转主标记的清关单不允许多序号保存");
                } else {
                    oret.setErrorMessage(repeatProductId + "存在重复");
                }
                return RpcResult.success(oret);
            }
        }
        boolean needCheckLockStock = (Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.CCS_SELF.getValue())
                || Objects.equals(inventoryOrderInfoDTO.getChannel(), InventoryOrderChannel.WMS.getValue()))
                && Objects.equals(inventoryOrderInfoDTO.getInOutFlag(), InventoryInOutEnum.OUT.getCode());
        boolean isItemLockStock = Objects.equals(inventoryOrderInfoDTO.getLockStockFlag(), 1);
        Map<String, InventoryOrderItemDTO> oldItemMap = new HashMap<>();
        if (needCheckLockStock) {
            oldItemMap = oldListOrderItems.stream()
                    .collect(Collectors.toMap((i -> "料号:" + i.getProductId() + "序号:" + i.getGoodsSeqNo()),
                            Function.identity(), (v1, v2) -> v1));
        }
        Map<Long, InventoryOrderItemDTO> oldItemDTOMap = oldListOrderItems.stream()
                .collect(Collectors.toMap(InventoryOrderItemDTO::getId, Function.identity(), (v1, v2) -> v1));
        List<InventoryOrderItemDTO> listItems = new ArrayList<>();
        // 行号
        int idx = 1;
        for (InventoryOrderItemParamV2 inventoryOrderItemParam : listOrderItems) {
            if (oldItemDTOMap.containsKey(inventoryOrderItemParam.getId())) {
                // 实际理货数量不能编辑，取数据库的实际理货数量
                InventoryOrderItemDTO oldItemDTO = oldItemDTOMap.get(inventoryOrderItemParam.getId());
                inventoryOrderItemParam.setActualTallyQty(oldItemDTO.getActualTallyQty());
            }
            inventoryOrderItemParam.setRefInveOrderId(invenOrderId);
            inventoryOrderItemParam.setRefInveOrderSn(inventoryOrderInfoDTO.getInveCustomsSn());
            InventoryOrderItemExtra extra = new InventoryOrderItemExtra();
            BeanUtils.copyProperties(inventoryOrderItemParam, extra);
            Double _double = inventoryOrderItemParam.getFirstUnitQfy();
            if (_double == null) {
                _double = 0d;
            }
            extra.setFirstUnitQfy(new BigDecimal(_double).setScale(5, BigDecimal.ROUND_HALF_UP));
            _double = inventoryOrderItemParam.getNetweight();
            if (_double == null) {
                _double = 0d;
            }
            extra.setNetweight(new BigDecimal(_double).setScale(5, RoundingMode.HALF_UP));
            if (Objects.nonNull(inventoryOrderItemParam.getTotalNetWeight())) {
                extra.setTotalNetWeight(inventoryOrderItemParam.getTotalNetWeight().setScale(5, RoundingMode.HALF_UP));
            } else {
                extra.setTotalNetWeight(new BigDecimal(_double * inventoryOrderItemParam.getDeclareUnitQfy()).setScale(5, RoundingMode.HALF_UP));
            }
            _double = inventoryOrderItemParam.getGrossWeight();
            if (_double == null) {
                _double = 0d;
            }
            extra.setGrossWeight(new BigDecimal(_double).setScale(5, RoundingMode.HALF_UP));
            if (Objects.nonNull(inventoryOrderItemParam.getTotalGrossWeight())) {
                extra.setTotalGrossWeight(inventoryOrderItemParam.getTotalGrossWeight().setScale(5, RoundingMode.HALF_UP));
            } else {
                extra.setTotalGrossWeight(new BigDecimal(_double * inventoryOrderItemParam.getDeclareUnitQfy()).setScale(5, RoundingMode.HALF_UP));
            }
            //如果法一为千克 法一数量不能大于毛重
            if (Objects.equals(inventoryOrderItemParam.getFirstUnit(), "035") && Objects.nonNull(inventoryOrderItemParam.getTotalGrossWeight())) {
                if (BigDecimal.valueOf(inventoryOrderItemParam.getFirstUnitQfy() * inventoryOrderItemParam.getDeclareUnitQfy())
                        .subtract(inventoryOrderItemParam.getTotalGrossWeight()).compareTo(new BigDecimal("0.000001")) > 0) {
                    // double 有精度问题
                    oret.setCode(-1);
                    oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "法定数量大于毛重");
                    return RpcResult.success(oret);
                }
            }
            if (Objects.nonNull(inventoryOrderItemParam.getSecondUnit())) {
                if (Objects.isNull(inventoryOrderItemParam.getSecondUnitQfy())) {
                    oret.setCode(-1);
                    oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "法二数量为空");
                    return RpcResult.success(oret);
                } else if (inventoryOrderItemParam.getSecondUnitQfy() <= 0) {
                    oret.setCode(-1);
                    oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "法二数量必须大于0");
                    return RpcResult.success(oret);
                }
                //如果法二为千克 法二数量不能大于毛重
                if (Objects.equals(inventoryOrderItemParam.getSecondUnit(), "035") && Objects.nonNull(inventoryOrderItemParam.getTotalGrossWeight())) {
                    if (BigDecimal.valueOf(inventoryOrderItemParam.getSecondUnitQfy() * inventoryOrderItemParam.getDeclareUnitQfy())
                            .subtract(inventoryOrderItemParam.getTotalGrossWeight()).compareTo(new BigDecimal("0.000001")) > 0) {
                        oret.setCode(-1);
                        oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "法二数量大于毛重");
                        return RpcResult.success(oret);
                    }
                }
                extra.setSecondUnitQfy(new BigDecimal(inventoryOrderItemParam.getSecondUnitQfy()).setScale(5, BigDecimal.ROUND_HALF_UP));
            }
            if (Objects.isNull(inventoryOrderItemParam.getDeclarePrice()) || inventoryOrderItemParam.getDeclarePrice() == 0) {
                oret.setCode(-1);
                oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "申报单价不能为空或为0");
                return RpcResult.success(oret);
            }
            if (Objects.isNull(inventoryOrderItemParam.getCurrency())) {
                oret.setCode(-1);
                oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "申报币制不能为空");
                return RpcResult.success(oret);
            }
            if (Objects.nonNull(inventoryOrderItemParam.getTotalNetWeight()) && Objects.nonNull(inventoryOrderItemParam.getTotalGrossWeight())) {
                if (inventoryOrderItemParam.getTotalNetWeight().compareTo(inventoryOrderItemParam.getTotalGrossWeight()) > 0) {
                    oret.setCode(-1);
                    oret.setErrorMessage("料号:" + inventoryOrderItemParam.getProductId() + "净重大于毛重");
                    return RpcResult.success(oret);
                }
            }
            if (Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_INVENTORY_PROFIT.getCode()) ||
                    Objects.equals(inventoryOrderInfoDTO.getInveBusinessType(), InventoryOrderBusinessEnum.BUSINESS_RANDOM_INSPECTION_DECLARATION.getCode())) {
                if (Objects.equals(inventoryOrderItemParam.getDangerousFlag(), "0") || Objects.equals(inventoryOrderItemParam.getDangerousFlag(), "1")) {
                    oret.setCode(-1);
                    oret.setErrorMessage("统一料号:" + inventoryOrderItemParam.getProductId() + "的危化品标志保存错误，请检查！");
                    return RpcResult.success(oret);
                }
            }
            if (needCheckLockStock && CollUtil.isNotEmpty(oldItemMap)) {
                String key = "料号:" + inventoryOrderItemParam.getProductId() + "序号:" + inventoryOrderItemParam.getGoodsSeqNo();
                InventoryOrderItemDTO inventoryOrderItemDTO = oldItemMap.get(key);
                if (isItemLockStock) {
                    if (Objects.isNull(inventoryOrderItemDTO)
                            || inventoryOrderItemDTO.getDeclareUnitQfy().compareTo(BigDecimal.valueOf(inventoryOrderItemParam.getDeclareUnitQfy())) != 0) {
                        oret.setCode(-1);
                        oret.setErrorMessage("修改表体数量，请先释放锁定库存");
                        return RpcResult.success(oret);
                    }
                }
            }


            String originCountry = inventoryOrderItemParam.getOriginCountry();
            String hsCode = inventoryOrderItemParam.getHsCode();
            //1-境外重点料件，判断每个品是否原产国都是美国
            if (Objects.equals(inventoryOrderItemParam.getGoodsSource(), GoodsSourceEnums.OUTER_KEY_MATERIAL.getCode())) {
                boolean isFourCategoryGoods = fourCategoryGoodsService.inventoryOuterKeyMaterialCheck(originCountry, hsCode);
                if (!isFourCategoryGoods) {
                    oret.setCode(-1);
                    oret.setErrorMessage("海关备案料号：" + inventoryOrderItemParam.getProductId()
                            + "，不属于四类措施商品，请重新选择");
                    return RpcResult.success(oret);
                }
            }
            //2-境外普通料件，判断是否存在原产国是美国
            if (Objects.equals(inventoryOrderItemParam.getGoodsSource(), GoodsSourceEnums.OUTER_NORMAL_MATERIAL.getCode())) {
                boolean isNormalMaterial = fourCategoryGoodsService.inventoryOuterNormalMaterialCheck(originCountry, hsCode);
                if (!isNormalMaterial) {
                    oret.setCode(-1);
                    oret.setErrorMessage("海关备案料号：" + inventoryOrderItemParam.getProductId()
                            + "，属于四类措施商品，请重新选择");
                    return RpcResult.success(oret);
                }
            }

            InventoryOrderItemDTO inventoryOrderItemDTO = this.buildInventoryOrderItemDTO(endorsementDTOS, inventoryOrderItemParam, extra);
            // 重新生成表体再保存会行号会丢失，这里按照页面行号重新生成， 前端那边就不加字段了
            inventoryOrderItemDTO.setIdx(String.valueOf(idx++));
            listItems.add(inventoryOrderItemDTO);
        }

        try {
            ResultDTO result = inventoryOrderInfoService.saveInventoryItems(inventoryOrderInfoDTO, listItems);
            inventoryOrderInfoDTO.setListItems(listItems);
            oret = new Response<>(inventoryOrderInfoDTO);
            if (CollUtil.isNotEmpty(result.getWarnMessage())) {
                oret.setCode(2);
                oret.setErrorMessage(result.getMessageDetail());
            }
            return RpcResult.success(result.getMessageDetail(), oret);
        } catch (ArgsErrorRpcException | ArgsInvalidException e) {
            oret = new Response<>(inventoryOrderInfoDTO);
            oret.setCode(-1);
            oret.setErrorMessage(e.getMessage());
            return RpcResult.success(oret);
        } catch (Exception e) {
            return RpcResult.error("保存失败");
        }
    }

    /**
     * 构造清关单表体
     *
     * @param endorsementDTOS
     * @param inventoryOrderItemParam
     * @param extra
     * @return
     */
    private InventoryOrderItemDTO buildInventoryOrderItemDTO(List<EndorsementDTO> endorsementDTOS, InventoryOrderItemParamV2 inventoryOrderItemParam, InventoryOrderItemExtra extra) {
        InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
        inventoryOrderItemDTO.setOriginProductId(inventoryOrderItemParam.getOriginProductId());
        inventoryOrderItemDTO.setId(inventoryOrderItemParam.getId());
        inventoryOrderItemDTO.setRefInveOrderId(inventoryOrderItemParam.getRefInveOrderId());
        inventoryOrderItemDTO.setRefInveOrderSn(inventoryOrderItemParam.getRefInveOrderSn());
        inventoryOrderItemDTO.setIsNew(inventoryOrderItemParam.getOldOrNew());
        inventoryOrderItemDTO.setSkuId(inventoryOrderItemParam.getSkuId());
        inventoryOrderItemDTO.setProductId(inventoryOrderItemParam.getProductId());
        inventoryOrderItemDTO.setGoodsSeqNo(inventoryOrderItemParam.getGoodsSeqNo());
        inventoryOrderItemDTO.setGoodsName(inventoryOrderItemParam.getGoodsName());
        if (Objects.nonNull(inventoryOrderItemParam.getDeclarePrice())) {
            inventoryOrderItemDTO.setDeclarePrice(new BigDecimal(inventoryOrderItemParam.getDeclarePrice()).setScale(4, BigDecimal.ROUND_HALF_UP));
        }
        inventoryOrderItemDTO.setRecordProductName(inventoryOrderItemParam.getRecordProductName());
        inventoryOrderItemDTO.setHsCode(inventoryOrderItemParam.getHsCode());
        if (Objects.nonNull(inventoryOrderItemParam.getDeclareUnitQfy())) {
            inventoryOrderItemDTO.setDeclareUnitQfy(new BigDecimal(inventoryOrderItemParam.getDeclareUnitQfy()).setScale(4, BigDecimal.ROUND_HALF_UP));
        }
        if (Objects.nonNull(inventoryOrderItemParam.getActualTallyQty())) {
            inventoryOrderItemDTO.setActualTallyQty(inventoryOrderItemParam.getActualTallyQty().setScale(4, RoundingMode.HALF_UP));
        }
        inventoryOrderItemDTO.setPlanDeclareQty(inventoryOrderItemParam.getPlanDeclareQty());
        inventoryOrderItemDTO.setExtraJson(JSON.toJSONString(extra));
        inventoryOrderItemDTO.setCreateTime(new Date());
        inventoryOrderItemDTO.setUpdateTime(new Date());
        inventoryOrderItemDTO.setGoodsModel(inventoryOrderItemParam.getGoodsModel());
        if (Objects.nonNull(inventoryOrderItemParam.getNetweight())) {
            inventoryOrderItemDTO.setNetweight(new BigDecimal(inventoryOrderItemParam.getNetweight()).setScale(5, BigDecimal.ROUND_HALF_UP));
        }
        inventoryOrderItemDTO.setCurrency(inventoryOrderItemParam.getCurrency());
        inventoryOrderItemDTO.setOriginCountry(inventoryOrderItemParam.getOriginCountry());
        if (!LongUtil.isNone(inventoryOrderItemParam.getRefEndorsementId()) && !StringUtils.isEmpty(inventoryOrderItemParam.getRefEndorsementSn())) {
            inventoryOrderItemDTO.setRefEndorsementId(inventoryOrderItemParam.getRefEndorsementId());
            inventoryOrderItemDTO.setRefEndorsementSn(inventoryOrderItemParam.getRefEndorsementSn());
        }
        if (!CollectionUtils.isEmpty(endorsementDTOS)) {
            EndorsementDTO endorsementDTO = endorsementDTOS.get(0);
            inventoryOrderItemDTO.setRefEndorsementId(endorsementDTO.getId());
            inventoryOrderItemDTO.setRefEndorsementSn(endorsementDTO.getSn());
        }
        inventoryOrderItemDTO.setSpeciesCertificateAttachmentName(inventoryOrderItemParam.getSpeciesCertificateAttachmentName());
        inventoryOrderItemDTO.setSpeciesCertificateAttachmentUrl(inventoryOrderItemParam.getSpeciesCertificateAttachmentUrl());
        inventoryOrderItemDTO.setDangerousFlag(inventoryOrderItemParam.getDangerousFlag());
        inventoryOrderItemDTO.setDeclareFormItemSeqNo(inventoryOrderItemParam.getDeclareFormItemSeqNo());
        inventoryOrderItemDTO.setGoodsSource(inventoryOrderItemParam.getGoodsSource());
        return inventoryOrderItemDTO;
    }

    /**
     * 编辑清关单表头
     *
     * @param editReqVo
     * @return
     * @path /invenorder/editInventoryHead
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/editInventoryHead", desc = "编辑清关单表头")
    public RpcResult editInventoryHead(InventoryOrderEditReqVo editReqVo) {
        try {
            log.info("编辑清关单表头开始 editReqVo={}", editReqVo);
            inventoryOrderController.editInventoryHead(editReqVo);
            return RpcResult.success();
        } catch (ArgsErrorException e) {
            log.error("编辑清关单表头失败:{}", e.getErrorMessage(), e);
            return RpcResult.error("编辑清关单表头失败:" + e.getErrorMessage());
        } catch (ArgsInvalidException e) {
            log.error("编辑清关单表头失败:{}", e.getErrorMessage(), e);
            return RpcResult.error("编辑清关单表头失败:" + e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/regenerateItem", desc = "重新生成表体")
    public RpcResult regenerateItem(IdParam idParam) {
        try {
            inventoryOrderInfoService.regenerateItem(idParam.getId());
            return RpcResult.success("重新生成表体成功");
        } catch (ArgsErrorException e) {
            log.error("重新生成表体失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("重新生成表体失败" + e.getErrorMessage());
        }
    }

    /**
     * 根据清关单生成核注单
     *
     * @param idParam
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/generateEndorsement", desc = "生成核注单")
    public RpcResult generateEndorsement(IdParam idParam) {
        try {
            inventoryOrderInfoService.generateEndorsement(idParam.getId());
            return RpcResult.success("生成核注单成功");
        } catch (ArgsErrorException e) {
            log.error("生成核注单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("生成核注单失败:" + e.getErrorMessage());
        } catch (ArgsInvalidException e) {
            log.error("生成核注单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("生成核注单失败:" + e.getErrorMessage());
        }
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/generateTransitInOutOrder", desc = "生成中转调入调出清关单")
    public RpcResult<String> generateTransitInOutOrder(IdParam idParam) {
        try {
            inventoryOrderInfoService.checkAndAutoCreateTransitInventoryOrder(idParam.getId());
            return RpcResult.success("生成成功");
        } catch (ArgsErrorException e) {
            log.error("生成中转调入调出清关单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("生成失败:" + e.getErrorMessage());
        } catch (ArgsInvalidException e) {
            log.error("生成中转调入调出清关单失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("生成失败:" + e.getErrorMessage());
        } catch (Exception e) {
            log.error("生成中转调入调出清关单失败 error={}", e.getMessage(), e);
            return RpcResult.error("生成失败:" + e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/delete-inventory-order-relation", desc = "删除关联单证号")
    public RpcResult deleteInventoryOrderRelation(RpcIdsParam idsParam) {
        return RpcResult.success(inventoryOrderController.deleteInventoryOrderRelation(idsParam.getId()));
    }

    /**
     * 审核
     *
     * @param auditReqVo
     * @return
     * @path /invenorder/audit
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/audit", desc = "审核")
    public RpcResult<Response> audit(InventoryOrderAuditReqVo auditReqVo) {
        Response oret = new Response<>();
        oret.setCode(1);
        try {
            ResultDTO result = inventoryOrderInfoService.audit(auditReqVo.getId());
            if (result.isSuccess()) {
                if (CollUtil.isNotEmpty(result.getWarnMessage())) {
                    oret.setCode(2);
                    oret.setErrorMessage(result.getMessageDetail());
                }
            }
            return RpcResult.success("审核通过", oret);
        } catch (ArgsErrorException e) {
            log.error("清关单审核失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("清关单审核失败:" + e.getErrorMessage());
        } catch (ArgsErrorRpcException e) {
            log.error("清关单审核失败 error={}", e.getErrorMessage(), e);
            oret.setErrorMessage("审核失败：" + e.getErrorMessage());
            oret.setCode(-1);
            return RpcResult.success(oret);
        } catch (ArgsInvalidException e) {
            log.error("清关单审核失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (BusinessException e) {
            log.error("清关单审核失败 error={}", e.getMessage(), e);
            return RpcResult.error("清关单审核失败:" + e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/update-inventory-order-relation", desc = "编辑关联单证号")
    public RpcResult updateInventoryOrderRelation(RpcRelParam rpcRelParam) {
        return RpcResult.success(inventoryOrderInfoService.updateInventoryOrderRelation(rpcRelParam.getId(), rpcRelParam.getRelNo()));
    }

    @Deprecated
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/paging", desc = "分页查询")
    public RpcResult<ListVO<InventoryOrderInfoVO>> paging(InventoryOrderSearch search) {
        return RpcResult.success(inventoryOrderController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/trace-log-download", desc = "附件记录日志")
    public RpcResult<Response<Boolean>> traceLogDownload(RpcIdsParam idsParam) {
        return RpcResult.success(inventoryOrderController.traceLogDownload(idsParam.getId()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/discard", desc = "作废清关单")
    public RpcResult<Response<String>> discard(RpcIdsParam idsParam) {
        return RpcResult.success(inventoryOrderController.discard(idsParam.getId()));
    }

    /**
     * 查看清关单详细
     *
     * @param idsParam 清关单id
     * @return RpcResult<InventoryOrderViewInfoVO>
     * @path /invenorder/view-inventory-order
     */
    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/view-inventory-order", desc = "查看清关单详细")
    public RpcResult<InventoryOrderViewInfoVO> viewInventoryOrder(RpcIdsParam idsParam) {
        try {
            InventoryOrderViewInfoVO inventoryOrderViewInfoVO = inventoryOrderController.viewInventoryOrder(idsParam.getId());
            log.info("清关单号: {} ,明细为：{}", inventoryOrderViewInfoVO.getInveCustomsSn(), JSON.toJSONString(inventoryOrderViewInfoVO));
            return RpcResult.success(inventoryOrderViewInfoVO);
        } catch (ArgsErrorException e) {
            log.error("查看清关单详细 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看清关单详细发生错误:" + e.getErrorMessage());
        }
    }

    /**
     * 查看清关单附件
     *
     * @param idParam
     * @return
     * @path /invenorder/viewInventoryOrderAttach
     */
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/viewInventoryOrderAttach", desc = "查看清关单附件")
    public RpcResult<List<InventoryOrderAttachVO>> viewInventoryOrderAttach(IdParam idParam) {
        try {
            List<InventoryOrderAttachDTO> inventoryOrderAttachDTOList = inventoryOrderInfoService.viewInventoryOrderAttach(idParam.getId());
            List<InventoryOrderAttachVO> list = inventoryOrderAttachDTOList.stream().map(dto -> {
                InventoryOrderAttachVO vo = new InventoryOrderAttachVO();
                BeanUtils.copyProperties(dto, vo);
                vo.setSourceDesc(InventoryOrderChannel.getEnum(dto.getSource()).getDesc());
                vo.setAttachTypeDesc(InventoryOrderAttachTypeEnums.getDesc(dto.getAttachType()));
                return vo;
            }).collect(Collectors.toList());
            return RpcResult.success(list);
        } catch (ArgsErrorException e) {
            log.error("查看清关单附件 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看清关单附件发生错误:" + e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/delete-attach", desc = "清关单附件删除")
    public RpcResult<Response<Boolean>> deleteAttach(RpcAttachParam rpcAttachParam) {
        return RpcResult.success(inventoryOrderController.deleteAttach(rpcAttachParam.getAttachId(), rpcAttachParam.getOrderId()));
    }

    /**
     * 清关单附件上传
     *
     * @param id
     * @param url
     * @param fileName
     * @param attachType
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/upload-attach", desc = "清关单附件上传")
    public RpcResult<Response<Boolean>> uploadAttach(Long id, String url, String fileName, String attachType) {
        return RpcResult.success(inventoryOrderController.uploadAttachUrl(id, url, fileName, attachType));
    }

    /**
     * 清关单附件下载
     *
     * @return
     * @path /invenorder/download-attach
     */
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/download-attach", desc = "清关单附件下载")
    @Override
    public RpcResult<String> downloadAttach(InventoryOrderAttachDownloadReq req) {
        return RpcResult.success("success", inventoryOrderController.downloadAttach(req.getId(), req.getIsUpdateCompanyInfo()));
    }

    /**
     * 附件类型下拉
     *
     * @return
     * @path /invenorder/list-attach-type
     */
    @SoulClient(path = "/invenorder/list-attach-type", desc = "附件类型下拉")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listAttachType() {
        return RpcResult.success(customsDictionaryService.listByType(DataDictionaryTypeEnums.INV_ORDER_ATTACH_TYPE.getValue()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/build-inventory-order-relation", desc = "批量创建相关单证号")
    public RpcResult buildInventoryOrderRelation(InventoryOrderRelationParam param) {
        return RpcResult.success(inventoryOrderInfoService.buildInventoryOrderRelation(param));
    }

    /**
     * 预览-批量创建相关单号
     *
     * @param param
     * @return InventoryOrderRelationResVO
     * @path /invenorder/pre-build-inventory-order-relation
     */
    @SoulClient(path = "/invenorder/pre-build-inventory-order-relation", desc = "预览-批量创建相关单号")
    @Override
    public RpcResult<InventoryOrderRelationResVO> preBuildInventoryOrderRelation(InventoryOrderRelationParam param) {
        try {
            return RpcResult.success(inventoryOrderInfoService.preBuildInventoryOrderRelation(param));
        } catch (IllegalArgumentException e) {
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/preImport", desc = "导入预览")
    public RpcResult<InventoryOrderItemWritingReport> preImport(UrlParamById urlParam) {
        List<InventoryOrderItemImportVO> successList = EasyExcel.read(DownLoadUtil.downloadNet(urlParam.getUrl())).headRowNumber(1).head(InventoryOrderItemImportVO.class).sheet().doReadSync();
        Integer groupSizeSku = webBaseConfig.getExportItemWritingGroupSizeSku();
        if (successList.size() > groupSizeSku) {
            throw new ArgsErrorException("清关单表体不允许超" + groupSizeSku + "条");
        }
        int i = 1; // 行号，从2开始
        List<InventoryOrderItemRecord> preList = new ArrayList<>();
        for (InventoryOrderItemImportVO demoExcel : successList) {
            i++;
            if (StringUtils.isEmpty(demoExcel.getGoodsSeqNo()) && StringUtils.isEmpty(demoExcel.getProductId())) {
                continue;
            }
            InventoryOrderItemRecord record = new InventoryOrderItemRecord();
            BeanUtils.copyProperties(demoExcel, record);
            record.setIdx(i);
            preList.add(record);
        }
        if (org.springframework.util.CollectionUtils.isEmpty(preList)) {
            throw new ArgsErrorException("对不起！选择模板文件为空或模板不正确");
        }
        try {
            return RpcResult.success(inventoryOrderItemManager.writing(urlParam.getId(), preList));
        } catch (Exception e) {
            if (e instanceof ArgsErrorException) {
                return RpcResult.error(((ArgsErrorException) e).getErrorMessage());
            }
            throw e;
        }
    }

    @Override
    @SoulClient(path = "/invenorder/importExcel", desc = "导入")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<CustomsGoodsItemInfoDTO>> importExcel(InventoryOrderItemImportSubmit submit) {
        return RpcResult.success(inventoryOrderController.importExcel(submit));
    }

    @Override
    @SoulClient(path = "/invenorder/listSeqNoByProductId", desc = "根据料号找出序号")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO>> listSeqNoByProductId(String productId, Long areaBookId) {
        if (Objects.isNull(productId) || Objects.isNull(areaBookId)) {
            return RpcResult.success(new ArrayList<>());
        }
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(areaBookId, productId);
        if (!CollectionUtils.isEmpty(customsBookItemDTOList)) {
            List<SelectOptionVO> result = customsBookItemDTOList.stream().map((CustomsBookItemDTO item) -> {
                SelectOptionVO optionDTO = new SelectOptionVO();
                optionDTO.setId(item.getId());
                optionDTO.setName(item.getGoodsSeqNo());
                return optionDTO;
            }).collect(Collectors.toList());
            return RpcResult.success(result);
        }
        return RpcResult.success(new ArrayList<>());
    }

    @Override
    @SoulClient(path = "/invenorder/listSeqNoByProductIdV2", desc = "根据料号找出序号")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SeqNoReqVO>> listSeqNoByProductIdV2(String productId, Long areaBookId) {
        if (Objects.isNull(productId) || Objects.isNull(areaBookId)) {
            return RpcResult.success(new ArrayList<>());
        }
        CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(areaBookId);
        if (CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode().equals(customsBookResVo.getBookType())) {
            // 非保账册 取 归并关系
            List<MergeRelationDTO> mergeRelationDTOList = mergeRelationService.findByBookIdAndProductId(areaBookId, productId);
            List<SeqNoReqVO> list = mergeRelationDTOList.stream().map(i -> {
                SeqNoReqVO seqNoReqVO = new SeqNoReqVO();
                seqNoReqVO.setId(i.getItemNoNew());
                seqNoReqVO.setName(i.getItemNoNew());
                seqNoReqVO.setCustomsBookId(i.getId());
                return seqNoReqVO;
            }).collect(Collectors.toList());
            return RpcResult.success(list);
        }
        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(areaBookId, productId);
        if (!CollectionUtils.isEmpty(customsBookItemDTOList)) {
            List<SeqNoReqVO> result = customsBookItemDTOList.stream().map((CustomsBookItemDTO item) -> {
                SeqNoReqVO seqNoReqVO = new SeqNoReqVO();
                seqNoReqVO.setId(item.getGoodsSeqNo());
                seqNoReqVO.setName(item.getGoodsSeqNo());
                //这边命名写错了 暂时不改了吧
                seqNoReqVO.setCustomsBookId(item.getId());
                return seqNoReqVO;
            }).collect(Collectors.toList());
            return RpcResult.success(result);
        }
        return RpcResult.success(new ArrayList<>());
    }

    @Override
    @SoulClient(path = "/invenorder/listProductId", desc = "根据统一料号获取商品列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<SelectOptionVO>> listProductId(String originProductId, Long areaBookId) {
        try {
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(areaBookId);
            if (CustomsBookTypeEnums.IMPORT_UNBONDED_BOOKS.getCode().equals(customsBookResVo.getBookType())) {
                // 非保账册 取 归并关系
                List<MergeRelationDTO> mergeRelationDTOList = mergeRelationService.findByBookIdAndProductId(areaBookId, originProductId);
                List<SelectOptionVO> list = mergeRelationDTOList.stream().map(i -> {
                    SelectOptionVO selectOptionVO = new SelectOptionVO();
                    selectOptionVO.setId(i.getProductId());
                    selectOptionVO.setName(i.getProductId());
                    return selectOptionVO;
                }).distinct().collect(Collectors.toList());
                return RpcResult.success(list);
            }
            List<String> productIdList = goodsRecordService.getProductIdListByUnifiedProductId(originProductId, areaBookId);
            List<SelectOptionVO> result = productIdList.stream().map(p -> {
                SelectOptionVO optionDTO = new SelectOptionVO();
                optionDTO.setId(p);
                optionDTO.setName(p);
                return optionDTO;
            }).collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (Exception e) {
            log.error("根据统一料号获取商品列表 error={}", e.getMessage(), e);
        }
        return RpcResult.error("获取料号列表失败");
    }

    @Override
    @SoulClient(path = "/invenorder/product/info/list", desc = "料号信息列表")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<List<ProductInfoVo>> listProductInfos(List<String> productIds, Long areaBookId) {

        if (CollectionUtils.isEmpty(productIds) || areaBookId == null) {
            return null;
        }

        List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProIds(areaBookId, productIds);
        if (CollectionUtils.isEmpty(customsBookItemDTOList)) {
            return null;
        }
        List<ProductInfoVo> productInfoVos = new ArrayList<>();
        customsBookItemDTOList.stream()
                .collect(Collectors.groupingBy(CustomsBookItemDTO::getProductId))
                .forEach((k, v) -> {
                    ProductInfoVo infoVo = new ProductInfoVo();
                    infoVo.setProductId(k);
                    List<SelectOptionVO> infoList = v.stream().map(z -> {
                        SelectOptionVO optionDTO = new SelectOptionVO();
                        optionDTO.setId(z.getId());
                        optionDTO.setName(z.getGoodsSeqNo());
                        return optionDTO;
                    }).collect(Collectors.toList());
                    infoVo.setInfoList(infoList);
                    productInfoVos.add(infoVo);
                });

        return RpcResult.success(productInfoVos);
    }

    @Override
    @SoulClient(path = "/invenorder/getInventoryOrderItemByItemId", desc = "根据账册库存id生成清关单表体信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @Deprecated
    public RpcResult<InventoryOrderItemDTO> getInventoryOrderItemByItemId(Long bookItemId) {
        CustomsBookItemDTO old = customsBookItemService.findById(bookItemId);
        GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(old.getCustomsBookId(), old.getProductId());
        InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
        inventoryOrderItemDTO.setHsCode(old.getHsCode());
        inventoryOrderItemDTO.setNetweight(goodsRecordDTO.getNetWeight());
        inventoryOrderItemDTO.setCurrency(old.getCurrCode());
        inventoryOrderItemDTO.setCurrencyDesc(customsCurrencyService.findByCode(old.getCurrCode()).getName());
        inventoryOrderItemDTO.setUnit(old.getGoodsUnit());
        inventoryOrderItemDTO.setUnitDesc(customsUomService.findByCode(old.getGoodsUnit()).getName());
        inventoryOrderItemDTO.setFirstUnit(old.getFirstUnit());
        inventoryOrderItemDTO.setFirstUnitDesc(customsUomService.findByCode(old.getFirstUnit()).getName());
        inventoryOrderItemDTO.setFirstUnitQfy(goodsRecordDTO.getFirstUnitAmount());
        if (!StringUtils.isEmpty(old.getSecondUnit())) {
            inventoryOrderItemDTO.setSecondUnit(old.getSecondUnit());
            inventoryOrderItemDTO.setSecondUnitDesc(customsUomService.findByCode(old.getSecondUnit()).getName());
            inventoryOrderItemDTO.setSecondUnitQfy(goodsRecordDTO.getSecondUnitAmount());
        }
        inventoryOrderItemDTO.setGoodsName(old.getGoodsName());
        inventoryOrderItemDTO.setGoodsModel(old.getGoodsModel());
        inventoryOrderItemDTO.setOriginCountry(old.getOriginCountry());
        inventoryOrderItemDTO.setOriginCountryDesc(customsCountryService.findByCode(old.getOriginCountry()).getName());
        return RpcResult.success(inventoryOrderItemDTO);
    }

    @Override
    @SoulClient(path = "/invenorder/getInventoryOrderItemByItemIdV2", desc = "根据账册库存id生成清关单表体信息")
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    public RpcResult<InventoryOrderItemDTO> getInventoryOrderItemByItemIdV2(Long bookItemId, String originProductId, Long bookId, String oldOrNew) {
        try {
            InventoryOrderItemDTO inventoryOrderItemDTO = inventoryOrderInfoService.getInventoryOrderItemByItemIdV2(bookItemId, originProductId, bookId, oldOrNew);
            return RpcResult.success(inventoryOrderItemDTO);
        } catch (ArgsInvalidException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/adminDiscard", desc = "作废清关单（管理员）")
    public RpcResult<Response<String>> adminDiscard(RpcIdsParam idsParam) {
        InventoryOrderInfoDTO inventoryOrderInfo = inventoryOrderInfoService.findById(idsParam.getId());
        if (inventoryOrderInfo != null && !StringUtils.isEmpty(inventoryOrderInfo.getChannelBusinessSn()) && GoodsRecordChannel.LOGISTICS.getValue().equals(inventoryOrderInfo.getChannel())) {
            if ("ReadyOrder".equals(inventoryOrderInfo.getChannelBusinessType())) {
                CancelParam cancelParam = new CancelParam();
                cancelParam.setReadyNo(inventoryOrderInfo.getChannelBusinessSn());
                cancelParam.setMessage("备货单作废");
                ResultResponse resultResponse = iReadyOrderRpcFacade.cancel(cancelParam);
                if (!resultResponse.isSuccess()) {
                    return RpcResult.error(resultResponse.getErrorMessage());
                }
            } else if ("Distribution".equals(inventoryOrderInfo.getChannelBusinessType())) {
                DistributionOrderCancel distributionOrderCancel = new DistributionOrderCancel();
                distributionOrderCancel.setDistributionNo(inventoryOrderInfo.getChannelBusinessSn());
                distributionOrderCancel.setRevocation("配货单作废");
                RpcResult result = iDistributionOrderRpcFacade.cancelOrder(distributionOrderCancel);
                if (200 != result.getCode()) {
                    return RpcResult.error(result.getMessage());
                }
            }
            inventoryOrderInfoService.discardInventoryOrderAndEndorsement(inventoryOrderInfo.getId());
        }
        if (inventoryOrderInfo != null && StringUtils.isEmpty(inventoryOrderInfo.getChannelBusinessSn())) {
            return RpcResult.success(inventoryOrderController.discard(idsParam.getId()));
        }
        return RpcResult.success("作废成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/getTallyReportDetailById", desc = "根据理货报告id获取明细（预览）")
    public RpcResult<List<InventoryOrderTallyReportDetailVO>> getTallyReportDetailById(RpcIdsParam idsParam) {
        InventoryOrderTallyReportDTO inventoryOrderTallyReportDTO = inventoryOrderTallyReportService.findById(idsParam.getId());
        List<InventoryOrderTallyReportDetail> tallyReportDetailList = JSON.parseArray(inventoryOrderTallyReportDTO.getTallyJson(), InventoryOrderTallyReportDetail.class);
        List<InventoryOrderTallyReportDetailVO> tallyReportDetailVOList = new ArrayList<>();
        for (InventoryOrderTallyReportDetail tallyReportDetail : tallyReportDetailList) {
            InventoryOrderTallyReportDetailVO detailVO = new InventoryOrderTallyReportDetailVO();
            detailVO.setGoodsName(tallyReportDetail.getGoodsName());
            detailVO.setPlanTallyQty(tallyReportDetail.getPlanTallyQty());
            detailVO.setActualTallyQty(tallyReportDetail.getActualTallyQty());
            detailVO.setOutBoundNo(inventoryOrderTallyReportDTO.getOutBoundNo());
            detailVO.setRemark(tallyReportDetail.getRemark());
            detailVO.setProductId(tallyReportDetail.getProductId());
            detailVO.setTallyOrderNo(inventoryOrderTallyReportDTO.getTallyOrderNo());
            detailVO.setSku(tallyReportDetail.getSku());
            tallyReportDetailVOList.add(detailVO);
        }
        return RpcResult.success(tallyReportDetailVOList);
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/pagingV2", desc = "分页查询（正在使用）")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @RequestMapping("/invenorder/pagingV2")
    public RpcResult<ListVO<InventoryOrderInfoVO>> pagingV2(@RequestBody InventoryOrderSearchV2 search) {

        // 可以查看的账册的ID列表,与查询条件账册ID比较
        List<Long> accountBookIdList = search.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            Long bookId = search.getCustomsBookId();
            if (bookId != null && !accountBookIdList.contains(bookId)) {
                return RpcResult.success(new ListVO<>());
            }
        }
        ListVO<InventoryOrderInfoDTO> list = inventoryOrderInfoService.pagingV2(search);
        List<InventoryOrderInfoDTO> orderInfoDTOList = list.getDataList();
        List<Long> userIdList = orderInfoDTOList.stream().map(o -> o.getCreateBy().longValue()).collect(Collectors.toList());
        Map<Long, UserRpcResult> userRpcResultMap = userService.listByIds(userIdList);
        ListVO<InventoryOrderInfoVO> result = new ListVO<>();
        result.setPage(list.getPage());
        result.setDataList(this.getVoList(list, userRpcResultMap));
        return RpcResult.success(result);
    }

    private List<InventoryOrderInfoVO> getVoList(ListVO<InventoryOrderInfoDTO> list, Map<Long, UserRpcResult> userRpcResultMap) {
        return list.getDataList().stream().map(item -> {
            InventoryOrderInfoVO vo = new InventoryOrderInfoVO();
            BeanUtils.copyProperties(item, vo);
            if (InventoryOrderChannel.LOGISTICS.getValue().equals(item.getChannel())) {
                vo.setChannelBusinessSn(!StringUtils.isEmpty(item.getChannelBusinessSn()) ? item.getChannelBusinessSn() : "如需要联系技术添加");
                vo.setChannelDesc(InventoryOrderChannel.LOGISTICS.getDesc());
            } else {
                InventoryOrderChannel inventoryOrderChannel = InventoryOrderChannel.getEnum(item.getChannel());
                vo.setChannelDesc(inventoryOrderChannel.getDesc());
            }
            if (InventoryOrderEnum.STATUS_COMPLETE.getCode().equalsIgnoreCase(vo.getStatus())
                    || InventoryOrderEnum.STATUS_FINISH.getCode().equalsIgnoreCase(vo.getStatus())) {
                vo.setCompleteTime(vo.getStatusTime());
            }
            vo.setStatusDesc(InventoryOrderEnum.getEnum(item.getStatus()).getDesc());
            vo.setInveBusinessTypeDesc(InventoryOrderBusinessEnum.getEnum(item.getInveBusinessType()).getDesc());
            if (Objects.nonNull(item.getUpstreamCancel())) {
                vo.setUpstreamCancel(Boolean.TRUE.equals(item.getUpstreamCancel()) ? "是" : "否");
            }
            if (Objects.nonNull(item.getOrderTag())) {
                Integer orderTag = item.getOrderTag();
                List<String> orderTags = InventoryOrderTagEnums.getOrderTagsShortName(orderTag);
                if (CollectionUtils.isNotEmpty(orderTags)) {
                    vo.setOrderTagList(orderTags);
                    vo.setOrderTagCodeList(InventoryOrderTagEnums.getOrderTags(orderTag));
                }
            }
            if (Objects.nonNull(item.getOrderTodoTag())) {
                Integer orderTodoTag = item.getOrderTodoTag();
                List<String> orderTodoTags = InventoryOrderTodoTagEnums.getOrderTodoTagsDesc(orderTodoTag);
                if (CollectionUtils.isNotEmpty(orderTodoTags)) {
                    vo.setOrderTodoTagList(orderTodoTags);
                }
            }
            if (Objects.nonNull(item.getTallyComplete())) { //理货完成
                vo.setTallyCompleteDesc(Objects.equals(item.getTallyComplete(), 1) ? "是" : "否");
            }
            if (Objects.nonNull(item.getCallbackStatus())) { //回传状态
                vo.setCallbackStatusDesc(InventoryOrderCallbackStatusEnum.getEnum(item.getCallbackStatus()).getDesc());
            }
            // 展示有效的核注单号
            EndorsementDTO endorsementDTO = endorsementService.findBySn(item.getEndorsementSn());
            if (Objects.nonNull(endorsementDTO)) {
                List<EndorsementDTO> endorsementDTOList = Collections.singletonList(endorsementDTO);
//            List<EndorsementDTO> endorsementDTOList = endorsementService.listByInventory(item.getId());
                List<CheckItemInfo> checkItemInfoList = endorsementDTOList.stream().map(e -> {
                    CheckItemInfo checkItemInfo = new CheckItemInfo();
                    checkItemInfo.setPreCheckOrderNo(e.getPreOrderNo());
                    checkItemInfo.setCheckOrderNo(e.getSn());
                    checkItemInfo.setCheckStatus(EndorsementOrderStatus.getEnum(e.getStatus()).getDesc());
                    checkItemInfo.setCreateTime(e.getCreateTime());
                    return checkItemInfo;
                }).collect(Collectors.toList());
                vo.setListCheckItemInfo(checkItemInfoList);
            }
            CompanyDTO company = companyService.findById(item.getInveCompanyId());
            if (company != null) {
                vo.setInveCompanyName(company.getName());
            }
//            if (vo.getCreateBy() != null) {
//                if (userRpcResultMap.containsKey(vo.getCreateBy().longValue())) {
//                    vo.setOperatorName(userRpcResultMap.get(vo.getCreateBy().longValue()).getUserName());
//                }
//                if (StringUtil.isBlank(vo.getOperatorName()) && Objects.equals(vo.getCreateBy(), 0)) {
//                    vo.setOperatorName("System");
//                }
//            }
            if (!LongUtil.isNone(vo.getBookId())) {
                CustomsBookResVo bookResVo = customsBookService.findByIdV2(vo.getBookId());
                vo.setBookNo(Objects.nonNull(bookResVo) ? bookResVo.getBookNo() : null);
            }
            if (Objects.nonNull(vo.getMailStatus())) {
                vo.setMailStatusDesc(InventoryOrderMailStatusEnum.getEnum(vo.getMailStatus()).getDesc());
            }
            vo.setCallbackErpRejectOrderFlag(StringUtil.isBlank(item.getCallbackErpRejectOrderErrMsg()));
            if (Objects.equals(item.getSelfOwnedVehicle(), Boolean.TRUE)) {
                vo.setSelfOwnedVehicle(1);
            } else if (Objects.equals(item.getSelfOwnedVehicle(), Boolean.FALSE)) {
                vo.setSelfOwnedVehicle(0);
            }
            if (Objects.nonNull(item.getInventoryFinishTime())) {
                vo.setInventoryFinishTime(DateUtil.format(item.getInventoryFinishTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            if (Objects.nonNull(item.getInventoryCompleteTime())) {
                vo.setInventoryCompleteTime(DateUtil.format(item.getInventoryCompleteTime(), DatePattern.NORM_DATETIME_PATTERN));
            }
            List<Integer> orderTags = InventoryOrderTagEnums.getOrderTags(item.getOrderTag());
            if (orderTags.contains(InventoryOrderTagEnums.URGENT_PROCESS.getCode()) && Objects.nonNull(item.getUrgentSort())) {
                vo.setUrgentProcessFlag(true);
            }

            return vo;
        }).collect(Collectors.toList());
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/mergeCheck", desc = "合并检查")
    public RpcResult<Response<String>> mergeCheck(InventoryOrderMergeParam param) {
        if (param.getIdList().isEmpty()) {
            return RpcResult.error("获取不到清关单id");
        }
        return RpcResult.success(inventoryOrderInfoService.mergeCheck(param.getIdList()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/mergeInventoryOrder", desc = "合并清关单")
    public RpcResult<Response<InventoryOrderInfoDTO>> mergeInventoryOrder(InventoryOrderMergeParam param) {
        Response<InventoryOrderInfoDTO> oret = new Response<>();
        try {
            oret = inventoryOrderInfoService.mergeInventoryOrder(param);
        } catch (Exception e) {
            oret.setCode(-1);
            oret.setErrorMessage(e.getMessage());
        }
        return RpcResult.success(oret);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/unmerge", desc = "解除合并")
    public RpcResult<Response<String>> unmerge(RpcIdsParam param) {
        InventoryOrderInfoDTO orderInfoDTO = inventoryOrderInfoService.findById(param.getId());
        if (!InventoryOrderEnum.STATUS_PERFECT.getCode().equals(orderInfoDTO.getStatus())) {
            return RpcResult.error("解除合单失败:清关单状态不允许解除合单");
        }
//        List<EndorsementDTO> endorsementDTOList = endorsementService.listByInventory(param.getId());
//        if(endorsementDTOList.parallelStream().anyMatch(e -> !EndorsementOrderStatus.INIT.getCode().equals(e.getStatus()))){
//            return RpcResult.error("该清关单关联的核注单当前状态无法解除合单");
//        }
        inventoryOrderInfoService.unMerge(param.getId());
        return RpcResult.success("解除合并成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/getMasterOrderInfoBySn", desc = "根据主单sn获取信息")
    public RpcResult<InventoryOrderInfoDTO> getMasterOrderInfoBySn(String masterOrderSn) {
        if (StringUtils.isEmpty(masterOrderSn)) {
            return RpcResult.error("主单sn为空");
        }
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findBySn(masterOrderSn);
        return RpcResult.success(inventoryOrderInfoDTO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/getFlowState", desc = "根据id获取状态节点")
    public RpcResult<FlowStateResVo> getFlowState(IdParam idParam) {
        try {
            InventoryFlowStateDTO flowStateDTO = inventoryOrderInfoService.getFlowState(idParam.getId());
            List<FlowStateResVo.StatusResVo> statusResVoList = ConvertUtil.listConvert(flowStateDTO.getFlowStateList(), FlowStateResVo.StatusResVo.class);
            FlowStateResVo flowStateResVo = new FlowStateResVo();
            flowStateResVo.setCurrNode(flowStateDTO.getCurrNode());
            flowStateResVo.setStatusList(statusResVoList);
            return RpcResult.success(flowStateResVo);
        } catch (Exception e) {
            log.error("getFlowState error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/exportItemExcel", desc = "导出表体")
    public RpcResult<String> exportItemExcel(IdParam idParam) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    idParam.getId(), ReportType.INVENTORY_ITEM_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/exportItemGoodsExcel", desc = "导出料件表体")
    @Override
    public RpcResult<String> exportItemGoodsExcel(IdParam idParam) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    idParam.getId(), ReportType.INVENTORY_ITEM_GOODS_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/invenorder/export", desc = "导出")
    public RpcResult<String> exportExcel(InventoryOrderSearchV2 search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    search, ReportType.INVENTORY_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    /**
     * 获取各状态的统计数量
     *
     * @param idParam
     * @return
     * @path /invenorder/upload-attach
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/inventoryStatusCount", desc = "获取各状态的统计数量")
    public RpcResult<List<InventoryOrderCountReqVo>> inventoryStatusCount(IdParam idParam) {
        try {
            Map<String, Integer> countMap = inventoryOrderInfoService.inventoryStatusCount();
            Iterator<Map.Entry<String, Integer>> entryIterator = countMap.entrySet().iterator();
            List<InventoryOrderCountReqVo> countList = new ArrayList<>();
            while (entryIterator.hasNext()) {
                Map.Entry<String, Integer> next = entryIterator.next();
                String status = next.getKey();
                Integer value = next.getValue();
                InventoryOrderCountReqVo totalCount = new InventoryOrderCountReqVo();
                totalCount.setStatus(status);
                totalCount.setCount(value);
                countList.add(totalCount);
            }
            return RpcResult.success(countList);
        } catch (ArgsInvalidException e) {
            log.error("获取统计数量失败 error={}", e.getErrorMessage(), e);
        }
        return RpcResult.error("获取统计数量失败");
    }

    /**
     * 获取ERP保税货主列表
     *
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listErpOwner", desc = "获取ERP保税货主列表")
    public RpcResult<List<SelectOptionVO>> listErpOwner() {
        try {
            Map<String, String> codeNameMap = inventoryOrderInfoService.listErpOwner();
            Iterator<Map.Entry<String, String>> entryIterator = codeNameMap.entrySet().iterator();
            List<SelectOptionVO> list = new ArrayList<>();
            while (entryIterator.hasNext()) {
                Map.Entry<String, String> next = entryIterator.next();
                String ownerCode = next.getKey();
                String ownerName = next.getValue();
                SelectOptionVO optionDTO = new SelectOptionVO();
                optionDTO.setId(ownerCode);
                optionDTO.setName(ownerName);
                list.add(optionDTO);
            }
            return RpcResult.success(list);
        } catch (Exception e) {
            log.error("获取ERP保税货主失败 error={}", e.getMessage(), e);
            return RpcResult.error("获取ERP保税货主失败");
        }
    }

    @ApiOperation(value = "获取所有单据来源")
    @SoulClient(path = "/invenorder/listChannel", desc = "获取所有单据来源")
    public RpcResult<List<SelectOptionVO<Integer>>> listChannel() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(InventoryOrderChannel.values())
                .filter(tag -> !Objects.equals(tag.getValue(), 0)).map(tag -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(tag.getValue());
                    optionDTO.setName(tag.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/manualDeal", desc = "手动清关")
    public RpcResult<String> manualDeal(Long id, String associatedInventorySn) {
        try {
            inventoryOrderInfoService.manualDeal(id, associatedInventorySn);
            return RpcResult.success("手动清关成功");
        } catch (ArgsInvalidException e) {
            log.error("手动清关失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("手动清关失败:" + e.getErrorMessage());
        } catch (Exception e) {
            log.error("手动清关失败 error={}", e.getMessage(), e);
            return RpcResult.error("手动清关失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/manualFinish", desc = "手动服务完成")
    public RpcResult<String> manualFinish(Long id) {
        try {
            inventoryOrderInfoService.manualFinish(id);
            return RpcResult.success("手动服务完成成功");
        } catch (ArgsInvalidException e) {
            log.error("手动服务完成失败 error={}", e.getErrorMessage(), e);
            return RpcResult.error("手动服务完成失败:" + e.getErrorMessage());
        } catch (Exception e) {
            log.error("手动服务完成失败 error={}", e.getMessage(), e);
            return RpcResult.error("手动服务完成失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listInventoryOrderTag", desc = "清关单标记下拉列表")
    public RpcResult<List<SelectOptionVO<Integer>>> listInventoryOrderTag() {
        List<SelectOptionVO<Integer>> result = InventoryOrderTagEnums.valuesV2().stream()
                .map(tag -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO();
                    optionDTO.setId(tag.getCode());
                    optionDTO.setName(tag.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listInventoryOrderTodoTag", desc = "清关单待办标记下拉列表")
    public RpcResult<List<SelectOptionVO<Integer>>> listInventoryOrderTodoTag() {
        List<SelectOptionVO<Integer>> result = InventoryOrderTodoTagEnums.valuesV2().stream()
                .map(tag -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO<>();
                    optionDTO.setId(tag.getCode());
                    optionDTO.setName(tag.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    // 采用数据字典
    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listFreightForwardingCompany", desc = "下拉货代公司接口")
    public RpcResult<List<SelectOptionVO<String>>> listFreightForwardingCompany() {
        List<SelectOptionVO<String>> selectOptionVOList = Arrays.stream(FreightForwardingCompanyEnums.values())
                .filter(f -> !Objects.equals(f.getCode(), null))
                .map(f -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO();
                    optionDTO.setId(f.getCode());
                    optionDTO.setName(f.getName());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(selectOptionVOList);
    }

    // 采用数据字典
    @Deprecated
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listArrivePort", desc = "下拉到货港口/机场接口")
    public RpcResult<List<SelectOptionVO<String>>> listArrivePort() {
        List<SelectOptionVO<String>> selectOptionVOList = Arrays.stream(ArrivePortEnums.values())
                .filter(f -> !Objects.equals(f.getCode(), null))
                .map(f -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO();
                    optionDTO.setId(f.getCode());
                    optionDTO.setName(f.getName());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(selectOptionVOList);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/invenorder/listCategory", desc = "下拉类目")
    public RpcResult<List<SelectOptionVO<String>>> listCategory() {
        List<SelectOptionVO<String>> selectOptionVOList = Arrays.stream(CategoryEnums.values())
                .filter(f -> !Objects.equals(f.getCode(), null))
                .map(f -> {
                    SelectOptionVO<String> optionDTO = new SelectOptionVO();
                    optionDTO.setId(f.getCode());
                    optionDTO.setName(f.getName());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(selectOptionVOList);
    }

    @Override
    @SoulClient(path = "/invenorder/getWmsTallyDetail", desc = "获取wms打托明细")
    public RpcResult<List<WmsTallyDetailResVo>> getWmsTallyDetail(IdParam idParam) {
        try {
            List<WmsTallyDetailDTO> wmsTallyDetail = inventoryOrderInfoService.getWmsTallyDetail(idParam.getId());
            List<WmsTallyDetailResVo> wmsTallyDetailResVos = wmsTallyDetail.stream().map(w -> {
                WmsTallyDetailResVo wmsTallyDetailResVo = new WmsTallyDetailResVo();
                BeanUtils.copyProperties(w, wmsTallyDetailResVo);
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                if (Objects.nonNull(w.getManufDate())) {
                    String manufDate = sdf.format(new Date(w.getManufDate()));
                    wmsTallyDetailResVo.setManufDate(manufDate);
                }
                if (Objects.nonNull(w.getExpireDate())) {
                    String expireDate = sdf.format(new Date(w.getExpireDate()));
                    wmsTallyDetailResVo.setExpireDate(expireDate);
                }
                return wmsTallyDetailResVo;
            }).collect(Collectors.toList());
            return RpcResult.success(wmsTallyDetailResVos);
        } catch (ArgsInvalidException e) {
            log.error("getWmsTallyDetail error={}", e.getErrorMessage());
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("getWmsTallyDetail error={}", e.getMessage());
            return RpcResult.error("获取打托明细失败");
        }
    }

    @Override
    @SoulClient(path = "/invenorder/uploadCustomsEntryAttach", desc = "上传报关单附件")
    @RequestMapping("/invenorder/uploadCustomsEntryAttach")
    public RpcResult<String> uploadCustomsEntryAttach(@RequestBody InventoryOrderCustomsEntryAttachVO attachVO) {
        if (Objects.isNull(attachVO)) {
            log.warn("上传报关单失败 - 传入参数为空");
            return RpcResult.error("上传报关单失败");
        }
        // 1. 校验参数
        if (LongUtil.isNone(attachVO.getId())) return RpcResult.error("清关单id不能为空");
        InventoryOrderInfoDTO info = inventoryOrderInfoService.findById(attachVO.getId());
        if (info == null) {
            return RpcResult.error("清关单不存在");
        }
        if (!Objects.equals(info.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())
                && !Objects.equals(info.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTION_IN.getCode())
                && !Objects.equals(info.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_SECTIONINNER_IN.getCode())) {
            return RpcResult.error("业务类型错误");
        }
        if (Objects.equals(info.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode()) && StrUtil.isBlank(attachVO.getCustomsEntryAttachUrl())) {
            return RpcResult.error("一线入境,报关单附件不能为空");
        }
        // 2. 更新数据
        InventoryItemLogisticsInfoDTO logisticsInfo = new InventoryItemLogisticsInfoDTO();
        logisticsInfo.setCustomsEntryNo(attachVO.getCustomsEntryNo());
        logisticsInfo.setCustomsEntryTime(new Date(attachVO.getCustomsEntryTime()));
        logisticsInfo.setFromLocation(attachVO.getFromLocation());
        logisticsInfo.setEntryPort(attachVO.getEntryPort());
        CustomsDictionaryDTO transportModeDTO = customsDictionaryService.findByCodeAndType(attachVO.getTransportMode(), DataDictionaryTypeEnums.TRANSPORT_MODE.getValue());
        logisticsInfo.setTransportMode(Objects.nonNull(transportModeDTO) ? transportModeDTO.getName() : attachVO.getTransportMode());
        logisticsInfo.setShipmentCountry(attachVO.getShipmentCountry());
        inventoryOrderInfoService.updateInventoryItemLogisticsInfo(info.getId(), logisticsInfo);

        info.setCustomsEntryAttachUrl(attachVO.getCustomsEntryAttachUrl());
        inventoryOrderInfoService.updateInventoryOrderInfoDTO(info);
        return RpcResult.success("上传成功");
    }

    @Override
    @SoulClient(path = "/invenorder/deleteCustomsEntryAttach", desc = "删除报关单附件")
    @RequestMapping("/invenorder/deleteCustomsEntryAttach")
    public RpcResult<String> deleteCustomsEntryAttach(Long id) {
        if (LongUtil.isNone(id)) {
            log.warn("删除报关单附件失败 - 传入参数为空");
            return RpcResult.error("删除报关单附件失败");
        }
        // 1. 校验参数
        InventoryOrderInfoDTO info = inventoryOrderInfoService.findById(id);
        if (info == null) return RpcResult.error("清关单不存在");
        if (!Objects.equals(info.getInveBusinessType(), InventoryOrderBusinessEnum.BUSSINESS_ONELINE_IN.getCode())) {
            return RpcResult.error("业务类型错误");
        }

        // 2. 更新数据
        info.setCustomsEntryNo("");
        info.setCustomsEntryDate(null);
        info.setCustomsEntryAttachUrl("");
        inventoryOrderInfoService.updateInventoryOrderInfoDTOAllArgs(info);
        return RpcResult.success("删除成功");
    }

    @Override
    @SoulClient(path = "/invenorder/listCallbackStatus", desc = "回传状态下拉")
    @RequestMapping("/invenorder/listCallbackStatus")
    public RpcResult listCallbackStatus() {
        List<SelectOptionVO> result = Arrays.stream(InventoryOrderCallbackStatusEnum.values())
                .filter(i -> !Objects.equals(InventoryOrderCallbackStatusEnum.EMPTY.getCode(), i.getCode()))
                .map(i -> {
                    SelectOptionVO selectOptionVO = new SelectOptionVO();
                    selectOptionVO.setName(i.getDesc());
                    selectOptionVO.setId(i.getCode());
                    return selectOptionVO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/invenorder/retryCallBack", desc = "重试清关回传状态")
    @RequestMapping("/invenorder/retryCallBack")
    public RpcResult retryCallBack(IdsParam ids) {
        try {
            inventoryOrderInfoService.retryCallBack(ids.getIdList());
            return RpcResult.success("重试清关回传成功");
        } catch (Exception e) {
            log.error("重试清关回传失败" + e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/invenorder/getStatusCount", desc = "获取清关单状态订单数")
    @RequestMapping("/invenorder/getStatusCount")
    @ApiOperation("获取清关单状态订单数")
    public RpcResult<List<InventoryOrderStatusCountResVO>> getStatusCount(@RequestBody InventoryOrderSearchV2 searchV2) {
        if (Objects.isNull(searchV2)) {
            return RpcResult.error("参数为空");
        }
        return RpcResult.success(inventoryOrderInfoService.getStatusCount(searchV2));
    }

    @SoulClient(path = "/invenorder/startTransit", desc = "发起中转")
    @Override
    public RpcResult<String> startTransit(InventoryOrderTransitReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("参数为空");
        }
        try {
            inventoryOrderInfoService.startTransit(reqVO);
            return RpcResult.success("发起中转成功");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/invenorder/returnTransit", desc = "中转回退")
    public RpcResult<String> returnTransit(@RequestBody IdParam idParam) {
        if (Objects.isNull(idParam)) {
            return RpcResult.error("参数为空");
        }
        try {
            inventoryOrderInfoService.returnTransit(idParam.getId());
            return RpcResult.success("中转回退成功");
        } catch (Exception e) {
            log.error("中转回退失败 -{}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/invenorder/resendMail", desc = "重发邮件")
    public RpcResult<String> resendMail(IdParam idParam) {
        if (Objects.isNull(idParam)) {
            return RpcResult.error("参数为空");
        }
        try {
            inventoryOrderInfoService.resendMail(idParam.getId());
            return RpcResult.success("邮件重发成功");
        } catch (Exception e) {
            log.error("邮件重发失败 -{}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/invenorder/auditMail", desc = "邮件审核")
    @RequestMapping("/invenorder/auditMail")
    @ApiOperation("邮件审核")
    public RpcResult<String> auditMail(@RequestBody InventoryOrderAuditMailReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("参数为空");
        }
        inventoryOrderInfoService.auditMail(reqVO.getSn(), reqVO.getAuditStatus(), reqVO.getRejectReason());
        return RpcResult.success(Objects.equals(reqVO.getAuditStatus(), 1) ? "审核成功" : "驳回成功");
    }

    @Override
    @SoulClient(path = "/invenorder/retryCallbackRejectOrder", desc = "驳回重试")
    public RpcResult<String> retryCallbackRejectOrder(IdParam idParam) {
        if (Objects.isNull(idParam)) {
            return RpcResult.error("参数为空");
        }
        inventoryOrderInfoService.retryCallbackRejectOrder(idParam.getId());
        return RpcResult.success("重试驳回成功");
    }

    /**
     * 邮件链接获取货主转出清单明细
     *
     * @param orderNo
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.OPEN)
    @SoulClient(path = "/invenorder/getMailDetailByOrderNo", desc = "邮件链接获取货主转出清单明细")
    public RpcResult<InventoryOrderInfoTransferorListResVO> getMailDetailByOrderNo(String orderNo) {
        try {
            // FIXME: 2023/1/4 之后上了Saas后需要进行租户改造
            InventoryOrderInfoTransferorListResVO resVO = inventoryOrderInfoService.getMailDetailByOrderNo(orderNo);
            return RpcResult.success(resVO);
        } catch (Exception e) {
            log.error("getMailDetailByOrderNo error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * @param id            清关单id
     * @param companyId     清关企业id
     * @param bookId        账册id
     * @param needCarryOver 结转明细待确认 1:有 0:无
     * @return
     */
    @Override
    @SoulClient(path = "/invenorder/updateInventoryCompanyAndBook", desc = "更新清关企业")
    public RpcResult<String> updateInventoryCompanyAndBook(Long id, Long companyId, Long bookId, Integer needCarryOver) {
        try {
            inventoryOrderInfoService.updateInventoryCompanyAndBook(id, companyId, bookId, needCarryOver);
            return RpcResult.success("更新成功");
        } catch (Exception e) {
            log.error("updateInventoryCompanyAndBook error={}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    @Override
    @SoulClient(path = "/invenorder/updateInventoryOrderCarInformation", desc = "更新车辆费用信息")
    public RpcResult<String> updateInventoryOrderCarInformation(InventoryOrderInfoDTO inventoryOrderInfoDTO) {
        if (Objects.isNull(inventoryOrderInfoDTO)) {
            return RpcResult.error("入参参数为空");
        }
        try {
            inventoryOrderInfoService.updateInventoryOrderCarInformation(inventoryOrderInfoDTO);
            return RpcResult.success("更新车辆费用信息成功");
        } catch (Exception e) {
            log.error("更新车辆费用信息失败：{}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 生成非保核放单
     *
     * @param reqVO
     * @return
     * @path /invenorder/generateFbChecklist
     */
    @Override
    @SoulClient(path = "/invenorder/generateFbChecklist", desc = "生成非保核放单")
    public RpcResult<String> generateFbChecklist(FbChecklistAddReqVO reqVO) {
        if (Objects.isNull(reqVO) || Objects.isNull(reqVO.getInventoryOrderId())) {
            return RpcResult.error("参数异常");
        }
        try {
            fbChecklistService.add(reqVO);
            return RpcResult.success("生成非保核放单成功");
        } catch (ArgsInvalidException e) {
            log.warn("清关单生成非保核放单失败-{}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.warn("清关单生成非保核放单失败-{}", e.getMessage(), e);
            return RpcResult.error("生成非保核放单失败");
        }
    }

    /**
     * 根据清关单类型获取核放单类型下拉
     *
     * @param businessType
     * @return
     * @path /invenorder/listFbChecklistTypeByBusiType
     */
    @Override
    @SoulClient(path = "/invenorder/listFbChecklistTypeByBusiType", desc = "根据清关单业务类型获取非保核放单类型下拉")
    public RpcResult listFbChecklistTypeByBusiType(String businessType) {
        FbChecklistTypeEnums fbType = null;
        if (InventoryOrderBusinessEnum.BUSINESS_FB_OUT.getCode().equals(businessType)) {
            fbType = FbChecklistTypeEnums.SECOND_LINE_FB_GOODS_OUT;
        }
        if (InventoryOrderBusinessEnum.BUSINESS_FB_IN.getCode().equals(businessType)) {
            fbType = FbChecklistTypeEnums.SECOND_LINE_FB_GOODS_IN;
        }
        List<SelectOptionVO> result = new ArrayList<>();
        if (Objects.nonNull(fbType)) {
            SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(fbType.getCode());
            selectOptionVO.setName(fbType.getDesc());
            result.add(selectOptionVO);
        }
        return RpcResult.success(result);
    }

    /**
     * 自建单业务类型下拉
     *
     * @return
     */
    @Override
    @SoulClient(path = "/invenorder/listSelfBusinessType", desc = "自建单业务类型下拉")
    public RpcResult<List<SelectOptionVO<String>>> listSelfBusinessType() {
        List<InventoryOrderBusinessEnum> inventoryOrderBusinessEnums = InventoryOrderBusinessEnum.listSelfBusinessType();
        List<SelectOptionVO<String>> list = inventoryOrderBusinessEnums.stream().map(i -> {
            SelectOptionVO<String> selectOptionVO = new SelectOptionVO<>();
            selectOptionVO.setId(i.getCode());
            selectOptionVO.setName(i.getDesc());
            return selectOptionVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 清关单表体通关校验
     *
     * @param param
     * @return
     * @path /invenorder/itemVerify
     */
    @Override
    @SoulClient(path = "/invenorder/itemVerify", desc = "清关单表体通关校验")
    public RpcResult<List<List<InventoryVerifyResult>>> itemVerify(InventoryOrderItemsParamV2 param) {
        InventoryOrderInfoDTO inventoryOrderInfoDTO = inventoryOrderInfoService.findById(param.getInvenOrderId());
        List<InventoryOrderItemParamV2> listOrderItems = param.getListOrderItems();
        List<InventoryOrderItemDTO> itemDTOList = listOrderItems.stream().map(i -> {
            InventoryOrderItemExtra extra = new InventoryOrderItemExtra();
            BeanUtils.copyProperties(i, extra);
            return this.buildInventoryOrderItemDTO(new ArrayList<>(), i, extra);
        }).collect(Collectors.toList());
        List<List<InventoryVerifyResult>> result = inventoryOrderInfoService
                .itemBatchVerify(inventoryOrderInfoDTO, itemDTOList, inventoryOrderInfoDTO.getBookId());
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/invenorder/updateStatusByIds", desc = "修改清关状态")
    public RpcResult<String> updateStatusById(InventoryOrderUpdateDTO inventoryOrderUpdateDTO) {
        if (StrUtil.isBlank(inventoryOrderUpdateDTO.getIds()) || StrUtil.isBlank(inventoryOrderUpdateDTO.getStatus())) {
            return RpcResult.error("修改清关状态失败：请检查参数");
        }
        try {
            List<String> idList = StrUtil.split(inventoryOrderUpdateDTO.getIds(), StrUtil.C_COMMA);
            for (String id : idList) {
                inventoryOrderInfoService.updateStatusById(Long.valueOf(id), inventoryOrderUpdateDTO.getStatus());
            }
            return RpcResult.success("修改清关状态成功");
        } catch (Exception e) {
            log.error("修改清关状态失败：{}", e.getMessage(), e);
            if (e instanceof ArgsErrorException) {
                return RpcResult.error(((ArgsErrorException) e).getErrorMessage());
            }
            return RpcResult.error("修改清关状态失败");
        }
    }

    /**
     * 获取表体溯源信息
     *
     * @param id 清关单表体id
     * @return 溯源信息
     * @path /invenorder/getItemLogisticsInfo
     */
    @Override
    @SoulClient(path = "/invenorder/getItemLogisticsInfo", desc = "获取清关单表体溯源信息")
    public RpcResult<InventoryItemLogisticsInfoVO> getItemLogisticsInfo(Long id) {
        InventoryOrderItemDTO itemDTO = inventoryOrderInfoService.findItemById(id);
        InventoryItemLogisticsInfoVO logisticsInfoVO = new InventoryItemLogisticsInfoVO();
        logisticsInfoVO.setSkuId(itemDTO.getSkuId());
        logisticsInfoVO.setOriginProductId(itemDTO.getOriginProductId());
        logisticsInfoVO.setProductId(itemDTO.getProductId());
        logisticsInfoVO.setCustomsEntryNo(itemDTO.getCustomsEntryNo());
        logisticsInfoVO.setCustomsEntryTime(DateUtil.format(itemDTO.getCustomsEntryTime(), "yyyy-MM-dd"));
        CustomsDictionaryDTO originCountryDTO = customsDictionaryService.findByCodeAndType(itemDTO.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
        if (Objects.nonNull(originCountryDTO)) {
            logisticsInfoVO.setOriginCountry(originCountryDTO.getName());
        }
        logisticsInfoVO.setShipmentCountry(itemDTO.getShipmentCountry());
        logisticsInfoVO.setFromLocation(itemDTO.getFromLocation());
        logisticsInfoVO.setTransportMode(itemDTO.getTransportMode());
        logisticsInfoVO.setEntryPort(itemDTO.getEntryPort());

        return RpcResult.success(logisticsInfoVO);
    }

    /**
     * 查看清关单详细V2
     *
     * @param idParam
     * @return
     * @path /invenorder/detail
     */
    @Override
    @SoulClient(path = "/invenorder/detail", desc = "查看清关单详细V2")
    public RpcResult<InventoryOrderViewInfoV2VO> detail(RpcIdParam idParam) {
        try {
            InventoryOrderViewInfoV2VO inventoryOrderViewInfoVO = inventoryOrderController.detail(idParam.getId());
            log.info("清关单号: {} ,明细为：{}", inventoryOrderViewInfoVO.getInveCustomsSn(), JSON.toJSONString(inventoryOrderViewInfoVO));
            return RpcResult.success(inventoryOrderViewInfoVO);
        } catch (ArgsErrorException e) {
            log.error("查看清关单详细 error={}", e.getErrorMessage(), e);
            return RpcResult.error("查看清关单详细发生错误:" + e.getErrorMessage());
        }
    }

    /**
     * 查看清关单表体
     *
     * @param idParam
     * @return
     * @path /invenorder/viewInventoryOrderItem
     */
    @Override
    @SoulClient(path = "/invenorder/viewInventoryOrderItem", desc = "查看清关单表体")
    public RpcResult<List<InventoryOrderItemDTO>> viewInventoryOrderItem(RpcIdParam idParam) {
        List<InventoryOrderItemDTO> itemDTOList = inventoryOrderInfoService.findListByInvenOrderId(idParam.getId());
        int idx = 1;
        for (InventoryOrderItemDTO itemDTO : itemDTOList) {
            itemDTO.setDeclareCustomsGoodsSeqNo(String.valueOf(idx++));
        }
        return RpcResult.success(itemDTOList);
    }

    /**
     * 查看清关单保税核注单
     *
     * @param idParam
     * @return
     * @path /invenorder/viewInventoryOrderEndorsement
     */
    @Override
    @SoulClient(path = "/invenorder/viewInventoryOrderEndorsement", desc = "查看清关单保税核注单")
    public RpcResult<List<InventoryOrderEndorsementVO>> viewInventoryOrderEndorsement(RpcIdParam idParam) {
        return RpcResult.success(inventoryOrderController.viewInventoryOrderEndorsement(idParam.getId()));
    }

    /**
     * 查看清关单关联单证
     *
     * @param idParam
     * @return
     * @path /invenorder/viewInventoryOrderRelation
     */
    @Override
    @SoulClient(path = "/invenorder/viewInventoryOrderRelation", desc = "查看清关单关联单证")
    public RpcResult<List<InventoryOrderRelationDTO>> viewInventoryOrderRelation(RpcIdParam idParam) {
        return RpcResult.success(inventoryOrderInfoService.findInventoryOrderRelationListByInvenOrderId(idParam.getId()));
    }

    /**
     * 修改清关单详情顶部信息
     *
     * @param editVO
     * @return
     * @path /invenorder/detail/updHead
     */
    @Override
    @SoulClient(path = "/invenorder/detail/updHead", desc = "修改清关单详情顶部信息")
    public RpcResult<Boolean> updInventoryDetailHead(InventoryOrderDetailHeadEditVO editVO) {
        InventoryOrderEditReqVo editReqVo = new InventoryOrderEditReqVo();
        // 补全内容，防止被覆盖
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(editVO.getId());
        BeanUtils.copyProperties(infoDTO, editReqVo);
        BeanUtils.copyProperties(editVO, editReqVo);
        inventoryOrderInfoService.updateInventoryOrderInfoHead(editReqVo);
        return RpcResult.success();
    }

    /**
     * 修改清关单详情运输信息
     *
     * @param editVO
     * @return
     * @path /invenorder/detail/updTransport
     */
    @Override
    @SoulClient(path = "/invenorder/detail/updTransport", desc = "修改清关单详情运输信息")
    public RpcResult<Boolean> updInventoryDetailTransport(InventoryOrderDetailTransportEditVO editVO) {
        InventoryOrderEditReqVo editReqVo = new InventoryOrderEditReqVo();
        // 补全内容，防止被覆盖
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(editVO.getId());
        BeanUtils.copyProperties(infoDTO, editReqVo);
        BeanUtils.copyProperties(editVO, editReqVo);
        inventoryOrderInfoService.updateInventoryOrderInfoHead(editReqVo);
        return RpcResult.success();
    }

    /**
     * 修改清关单详情报关信息
     *
     * @param editVO
     * @return
     * @path /invenorder/detail/updCustoms
     */
    @Override
    @SoulClient(path = "/invenorder/detail/updCustoms", desc = "修改清关单详情报关信息")
    public RpcResult<Boolean> updInventoryDetailCustoms(InventoryOrderDetailCustomsEditVO editVO) {
        InventoryOrderEditReqVo editReqVo = new InventoryOrderEditReqVo();
        // 补全内容，防止被覆盖
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(editVO.getId());
        BeanUtils.copyProperties(infoDTO, editReqVo);
        BeanUtils.copyProperties(editVO, editReqVo);
        inventoryOrderInfoService.updateInventoryOrderInfoHead(editReqVo);
        return RpcResult.success();
    }

    /**
     * 修改清关单详情其他信息
     *
     * @param editVO
     * @return
     * @path /invenorder/detail/updOthers
     */
    @Override
    @SoulClient(path = "/invenorder/detail/updOthers", desc = "修改清关单详情其他信息")
    public RpcResult<Boolean> updInventoryDetailOthers(InventoryOrderDetailOthersEditVO editVO) {
        InventoryOrderEditReqVo editReqVo = new InventoryOrderEditReqVo();
        // 补全内容，防止被覆盖
        InventoryOrderInfoDTO infoDTO = inventoryOrderInfoService.findById(editVO.getId());
        BeanUtils.copyProperties(infoDTO, editReqVo);
        BeanUtils.copyProperties(editVO, editReqVo);
        inventoryOrderInfoService.updateInventoryOrderInfoHead(editReqVo);
        return RpcResult.success();
    }


    /**
     * 关联调拨详情展示
     *
     * @param reqVO
     * @return
     * @path /invenorder/viewAssociateTransferInfo
     */
    @Override
    @SoulClient(path = "/invenorder/viewAssociateTransferInfo", desc = "关联调拨详情展示")
    public RpcResult<InventoryOrderAssociateTransferInfoVO> viewAssociateTransferInfo(InventoryOrderAssociateTransferReqVO reqVO) {
        List<String> snList = Arrays.asList(reqVO.getInveOrderSn(), reqVO.getAssociatedOrderSn());
        List<InventoryOrderInfoDTO> infoDTOList = inventoryOrderInfoService.findBySn(snList);
        inventoryOrderInfoService.checkAssociateTransfer(infoDTOList);
        InventoryOrderAssociateTransferInfoVO infoVO = new InventoryOrderAssociateTransferInfoVO();
        for (InventoryOrderInfoDTO inventoryOrderInfoDTO : infoDTOList) {
            InventoryOrderAssociateTransferInfoVO.WarehouseInfoVO warehouseInfoVO = new InventoryOrderAssociateTransferInfoVO.WarehouseInfoVO();
            warehouseInfoVO.setInventoryOrderId(inventoryOrderInfoDTO.getId());
            warehouseInfoVO.setInveCustomsSn(inventoryOrderInfoDTO.getInveCustomsSn());
            warehouseInfoVO.setBusinessTypeName(InventoryOrderBusinessEnum.getEnum(inventoryOrderInfoDTO.getInveBusinessType()).getDesc());
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(inventoryOrderInfoDTO.getBookId());
            if (Objects.nonNull(customsBookResVo)) {
                warehouseInfoVO.setWarehouseTypeName(StorageAttrEnums.getEnums(customsBookResVo.getStorageAttr()).getTypeName());
                CompanyDTO companyDTO = companyService.findById(customsBookResVo.getAreaCompanyId());
                if (Objects.nonNull(companyDTO)) {
                    warehouseInfoVO.setInveCompanyName(companyDTO.getName());
                }
            }
            warehouseInfoVO.setEntityWarehouseName(inventoryOrderInfoDTO.getEntityWarehouseName());
            warehouseInfoVO.setOwnerName(inventoryOrderInfoDTO.getOwnerName());
            if (InventoryInOutEnum.IN.getCode().equals(inventoryOrderInfoDTO.getInOutFlag())) {
                infoVO.setInWarehouseInfoVO(warehouseInfoVO);
            } else if (InventoryInOutEnum.OUT.getCode().equals(inventoryOrderInfoDTO.getInOutFlag())) {
                infoVO.setOutWarehouseInfoVO(warehouseInfoVO);
            }
        }
        return RpcResult.success(infoVO);
    }

    /**
     * 关联调拨
     *
     * @param reqVO
     * @return
     * @path /invenorder/associateTransfer
     */
    @Override
    @SoulClient(path = "/invenorder/associateTransfer", desc = "关联调拨")
    public RpcResult<Boolean> associateTransfer(InventoryOrderAssociateTransferReqVO reqVO) {
        inventoryOrderInfoService.associateTransfer(reqVO.getInveOrderSn(), reqVO.getAssociatedOrderSn());
        return RpcResult.success();
    }

    /**
     * 解除关联调拨
     *
     * @param idParam
     * @return
     * @path /invenorder/disassociateTransfer
     */
    @Override
    @SoulClient(path = "/invenorder/disassociateTransfer", desc = "解除关联调拨")
    public RpcResult<Boolean> disassociateTransfer(RpcIdParam idParam) {
        inventoryOrderInfoService.disassociateTransfer(idParam.getId());
        return RpcResult.success();
    }

    /**
     * 草单比对
     *
     * @param id
     * @return
     * @path /invenorder/compareDraftItemList
     */
    @Override
    @SoulClient(path = "/invenorder/compareDraftItemList", desc = "草单比对")
    public RpcResult<List<DraftItemCompareResVO>> compareDraftItemList(Long id) {
        if (Objects.isNull(id)) {
            return RpcResult.error("id不存在");
        }
        List<DraftItemCompareDTO> draftItemCompareDTOS = inventoryOrderInfoService.compareDraftItemList(id);
        List<DraftItemCompareResVO> list = draftItemCompareDTOS.stream().map(dto -> {
            DraftItemCompareResVO resVO = new DraftItemCompareResVO();
            resVO.setProductId(dto.getProductId());
            resVO.setHsCode(dto.getHsCode());
            resVO.setExitsDiff(dto.getExitsDiff());
            resVO.setExitsFail(dto.getExitsFail());
            resVO.setTransmitInItem(dto.getTransmitInItem().stream().map(this::buildCompareItem).collect(Collectors.toList()));
            resVO.setTransmitOutItem(dto.getTransmitOutItem().stream().map(this::buildCompareItem).collect(Collectors.toList()));
            return resVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    /**
     * 草单比对
     *
     * @param reqVO
     * @return
     * @path /invenorder/compareDraftItemListV2
     */
    @Override
    @SoulClient(path = "/invenorder/compareDraftItemListV2", desc = "草单比对")
    public RpcResult<List<DraftItemCompareResVO>> compareDraftItemListV2(DraftCompareReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("id不存在");
        }
        List<DraftItemCompareDTO> draftItemCompareDTOS = inventoryOrderInfoService.compareDraftItemList(reqVO.getId(), reqVO.getCompareType());
        List<DraftItemCompareResVO> list = draftItemCompareDTOS.stream().map(dto -> {
            DraftItemCompareResVO resVO = new DraftItemCompareResVO();
            resVO.setProductId(dto.getProductId());
            resVO.setHsCode(dto.getHsCode());
            resVO.setExitsDiff(dto.getExitsDiff());
            resVO.setExitsFail(dto.getExitsFail());
            resVO.setTransmitInItem(dto.getTransmitInItem().stream().map(this::buildCompareItem).collect(Collectors.toList()));
            resVO.setTransmitOutItem(dto.getTransmitOutItem().stream().map(this::buildCompareItem).collect(Collectors.toList()));
            return resVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    private DraftItemCompareResVO.CompareItem buildCompareItem(DraftItemCompareDTO.CompareItem origin) {
        if (Objects.isNull(origin)) {
            return new DraftItemCompareResVO.CompareItem();
        }
        DraftItemCompareResVO.CompareItem compareItem = new DraftItemCompareResVO.CompareItem();
        BeanUtil.copyProperties(origin, compareItem);
        if (StrUtil.isNotBlank(origin.getCurrency())) {
            CustomsDictionaryDTO currencyDTO = customsDictionaryService.findByCodeAndType(origin.getCurrency(), DataDictionaryTypeEnums.CURRENCY.getValue());
            compareItem.setCurrencyName(currencyDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getOriginCountry())) {
            CustomsDictionaryDTO originCountryDTO = customsDictionaryService.findByCodeAndType(origin.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
            compareItem.setOriginCountryName(originCountryDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getFirstUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getFirstUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setFirstUnitName(unitDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getSecondUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getSecondUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setSecondUnitName(unitDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setUnitName(unitDTO.getName());
        }
        return compareItem;
    }

    /**
     * 获取草单比对需要过滤字段
     *
     * @return
     */
    @Override
    @SoulClient(path = "/invenorder/getDraftCompareFilterList", desc = "获取草单比对需要过滤字段")
    public RpcResult<List<String>> getDraftCompareFilterList() {
        return RpcResult.success(Arrays.asList(draftCompareIgnoreFields));
    }

    /**
     * 草单比对更新表体
     *
     * @param vo
     * @return
     * @path /invenorder/updateItemListByDraftCompare
     */
    @Override
    @SoulClient(path = "/invenorder/updateItemListByDraftCompare", desc = "草单比对更新")
    public RpcResult<Boolean> updateItemListByDraftCompare(DraftCompareUpdateItemListVO vo) {
        if (Objects.isNull(vo)) {
            return RpcResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(vo.getItemList())) {
            return RpcResult.error("参数不能为空");
        }
        List<InventoryOrderItemDTO> itemDTOList = vo.getItemList().stream().map(item -> {
            InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
            BeanUtil.copyProperties(item, inventoryOrderItemDTO);
            return inventoryOrderItemDTO;
        }).collect(Collectors.toList());
        inventoryOrderInfoService.updateItemListByDraftCompare(itemDTOList, vo.getCompareType());
        return RpcResult.success();
    }

    /**
     * 草单确认
     *
     * @param id 清关单id
     * @return
     * @path /invenorder/confirmDraft
     */
    @SoulClient(path = "/invenorder/confirmDraft", desc = "草单确认")
    @Override
    @Deprecated
    public RpcResult<Boolean> confirmDraft(Long id) {
        if (Objects.isNull(id)) {
            return RpcResult.error("id不能为空");
        }
        inventoryOrderInfoService.confirmDraft(id, null);
        return RpcResult.success();
    }

    /**
     * 草单确认
     *
     * @param reqVO
     * @return
     * @path /invenorder/confirmDraftV2
     */
    @SoulClient(path = "/invenorder/confirmDraftV2", desc = "草单确认")
    @Override
    public RpcResult<Boolean> confirmDraftV2(DraftConfirmReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("id不能为空");
        }
        inventoryOrderInfoService.confirmDraft(reqVO.getId(), reqVO.getCompareType());
        return RpcResult.success();
    }

    /**
     * 表体比对
     *
     * @param reqVO
     * @return
     * @path /invenorder/compareItemList
     */
    @SoulClient(path = "/invenorder/compareItemList", desc = "表体比对")
    @Override
    public RpcResult<List<InvOrderItemCompareResVO>> compareItemList(DraftCompareReqVO reqVO) {
        if (Objects.isNull(reqVO)) {
            return RpcResult.error("id不存在");
        }
        List<InvOrderItemCompareDTO> draftItemCompareDTOS = inventoryOrderInfoService.compareItemList(reqVO.getId(), reqVO.getCompareType());
        List<InvOrderItemCompareResVO> list = draftItemCompareDTOS.stream().map(dto -> {
            InvOrderItemCompareResVO resVO = new InvOrderItemCompareResVO();
            resVO.setProductId(dto.getProductId());
            resVO.setExitsDiff(dto.getExitsDiff());
            resVO.setExitsFail(dto.getExitsFail());
            resVO.setItem(buildCompareItem(dto.getItem()));
            resVO.setCompareItem(buildCompareItem(dto.getCompareItem()));
            resVO.setRecordItem(buildCompareItem(dto.getRecordItem()));
            return resVO;
        }).collect(Collectors.toList());
        return RpcResult.success(list);
    }

    private InvOrderItemCompareResVO.CompareItem buildCompareItem(InvOrderItemCompareDTO.CompareItem origin) {
        if (Objects.isNull(origin)) {
            return new InvOrderItemCompareResVO.CompareItem();
        }
        InvOrderItemCompareResVO.CompareItem compareItem = new InvOrderItemCompareResVO.CompareItem();
        BeanUtil.copyProperties(origin, compareItem);
        if (StrUtil.isNotBlank(origin.getCurrency())) {
            CustomsDictionaryDTO currencyDTO = customsDictionaryService.findByCodeAndType(origin.getCurrency(), DataDictionaryTypeEnums.CURRENCY.getValue());
            compareItem.setCurrencyName(currencyDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getOriginCountry())) {
            CustomsDictionaryDTO originCountryDTO = customsDictionaryService.findByCodeAndType(origin.getOriginCountry(), DataDictionaryTypeEnums.COUNTRY.getValue());
            compareItem.setOriginCountryName(originCountryDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getFirstUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getFirstUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setFirstUnitName(unitDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getSecondUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getSecondUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setSecondUnitName(unitDTO.getName());
        }
        if (StrUtil.isNotBlank(origin.getUnit())) {
            CustomsDictionaryDTO unitDTO = customsDictionaryService.findByCodeAndType(origin.getUnit(), DataDictionaryTypeEnums.UOM.getValue());
            compareItem.setUnitName(unitDTO.getName());
        }
        return compareItem;
    }

    /**
     * 表体比对更新表体
     *
     * @param vo
     * @return
     * @path /invenorder/updateItemListByCompare
     */
    @SoulClient(path = "/invenorder/updateItemListByCompare", desc = "表体比对更新")
    @Override
    public RpcResult<Boolean> updateItemListByCompare(DraftCompareUpdateItemListVO vo) {
        if (Objects.isNull(vo)) {
            return RpcResult.error("参数不能为空");
        }
        if (CollUtil.isEmpty(vo.getItemList())) {
            return RpcResult.error("参数不能为空");
        }
        List<InventoryOrderItemDTO> itemDTOList = vo.getItemList().stream().map(item -> {
            InventoryOrderItemDTO inventoryOrderItemDTO = new InventoryOrderItemDTO();
            BeanUtil.copyProperties(item, inventoryOrderItemDTO);
            return inventoryOrderItemDTO;
        }).collect(Collectors.toList());
        inventoryOrderInfoService.updateItemListByCompare(itemDTOList, vo.getCompareType());
        return RpcResult.success();
    }

    @Override
    @SoulClient(path = "/invenorder/getInventoryOrderInfoTrackLogs", desc = "获取轨迹日志")
    public RpcResult<List<InventoryOrderInfoTrackLogVo>> getInventoryOrderInfoTrackLogs(Long inventoryOrderInfoId) {
        try {
            List<InventoryOrderTrackLogDTO> inventoryOrderTrackLogDTOS = inventoryOrderInfoService.inventoryOrderInfoTrackLogs(inventoryOrderInfoId);
            List<InventoryOrderInfoTrackLogVo> vos = ConvertUtil.listConvert(inventoryOrderTrackLogDTOS, InventoryOrderInfoTrackLogVo.class);
            vos.forEach(v -> v.setInventoryStatus(InventoryOrderEnum.getEnum(v.getInventoryStatus()).getDesc()));
            return RpcResult.success(vos);
        } catch (Exception e) {
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 获取表体
     *
     * @param id
     * @return
     * @path /invenorder/buildItemsByAssociatedTransit
     */
    @Override
    @SoulClient(path = "/invenorder/buildItemsByAssociatedTransit", desc = "获取表体")
    public RpcResult<List<InventoryOrderItemDTO>> buildItemsByAssociatedTransit(Long id) {
        return RpcResult.success(inventoryOrderInfoService.buildItemsByAssociatedTransit(id));
    }

    /**
     * 接单
     *
     * @param id
     * @return
     * @path /invenorder/takeOrder
     */
    @Override
    @SoulClient(path = "/invenorder/takeOrder", desc = "接单")
    public RpcResult<Boolean> takeOrder(Long id) {
        inventoryOrderInfoService.takeOrder(id);
        return RpcResult.success();
    }

    /**
     * 获取比对类型
     *
     * @return
     * @path /invenorder/listDraftCompareType
     */
    @SoulClient(path = "/invenorder/listDraftCompareType", desc = "获取比对类型")
    @Override
    public RpcResult<List<SelectOptionVO<Integer>>> listDraftCompareType() {
        return RpcResult.success(Arrays.stream(InventoryOrderDraftCompareTypeEnums.values())
                .map(e -> new SelectOptionVO<>(e.getCode(), e.getDesc())).collect(Collectors.toList()));
    }

    @Override
    @SoulClient(path = "/invenorder/listUrgentProcess", desc = "获取加急排序弹窗")
    public RpcResult<List<InventoryUrgentProcessResVO>> listUrgentProcess() {
        try {
            List<InventoryUrgentProcessResVO> resVOS = inventoryOrderInfoService.listUrgentProcess();
            return RpcResult.success(resVOS);
        } catch (ArgsInvalidException ex) {
            log.error("listUrgentProcess error:{}", ex.getErrorMessage(), ex);
            return RpcResult.error(ex.getErrorMessage());
        } catch (Exception e) {
            log.error("listUrgentProcess error:{}", e.getMessage(), e);
            return RpcResult.error("系统异常");
        }
    }

    @Override
    @SoulClient(path = "/invenorder/saveUrgentSort", desc = "保存加急排序")
    public RpcResult<String> saveUrgentSort(InventoryUrgentSortSaveReqVo reqVo) {
        try {
            inventoryOrderInfoService.saveUrgentSort(reqVo);
            return RpcResult.success("保存成功");
        } catch (ArgsInvalidException ex) {
            log.error("saveUrgentSort error:{}", ex.getErrorMessage(), ex);
            return RpcResult.error("保存失败:" + ex.getErrorMessage());
        } catch (Exception e) {
            log.error("saveUrgentSort error:{}", e.getMessage(), e);
            return RpcResult.error("保存失败");
        }
    }

    @Override
    @SoulClient(path = "/invenorder/batchChangeUrgentProcessInventoryOrder", desc = "批量加急/取消")
    public RpcResult<String> batchChangeUrgentProcessInventoryOrder(InventoryBatchUrgentProcessReqVo reqVo) {
        try {
            List<Long> inventoryOrderIdList = reqVo.getInventoryOrderId();
            if (CollectionUtils.isEmpty(inventoryOrderIdList)) {
                return RpcResult.error("选择单据为空");
            }
            Boolean isUrgent = reqVo.getIsUrgent();
            if (Objects.isNull(isUrgent)) {
                return RpcResult.error("未选择加急或取消");
            }
            inventoryOrderInfoService.batchChangeUrgentProcessInventoryOrder(inventoryOrderIdList, isUrgent);
            return RpcResult.success("保存成功");
        } catch (ArgsInvalidException ex) {
            log.error("batchChangeUrgentProcessInventoryOrder error:{}", ex.getErrorMessage(), ex);
            return RpcResult.error("保存失败:" + ex.getErrorMessage());
        } catch (Exception e) {
            log.error("batchChangeUrgentProcessInventoryOrder error:{}", e.getMessage(), e);
            return RpcResult.error("保存失败:" + e.getMessage());
        }
    }

    /**
     * 获取成品表体商品详情
     *
     * @param bizDeclareFormItemId
     * @param qty
     * @return
     * @path /invenorder/findEndPrdDetail
     */
    @SoulClient(path = "/invenorder/findEndPrdDetail", desc = "获取成品表体商品详情")
    @Override
    @LogParam
    public RpcResult<CustomsGoodsItemInfoDTO> findEndPrdDetail(Long bizDeclareFormItemId, Long bookId, BigDecimal qty) {
        return RpcResult.success(inventoryOrderController.findEndProdDetail(bizDeclareFormItemId, bookId, qty));
    }


    /**
     * 料件表体详情
     *
     * @param inventoryOrderId
     * @return
     * @path /invenorder/viewInventoryOrderItemGoodsList
     */
    @SoulClient(path = "/invenorder/viewInventoryOrderItemGoodsList", desc = "料件表体详情")
    @Override
    @LogParam
    public RpcResult<List<InventoryOrderItemGoodsVO>> viewInventoryOrderItemGoodsList(Long inventoryOrderId) {
        return RpcResult.success(inventoryOrderController.viewInventoryOrderItemGoodsList(inventoryOrderId));
    }

    /**
     * 料件表体编辑
     *
     * @param editVO
     * @return
     * @path /invenorder/editInventoryOrderItemGoods
     */
    @SoulClient(path = "/invenorder/editInventoryOrderItemGoods", desc = "料件表体编辑")
    @Override
    public RpcResult<Boolean> editInventoryOrderItemGoods(InventoryOrderItemGoodsEditVO editVO) {
        inventoryOrderController.editItemGoods(editVO);
        return RpcResult.success();
    }

    /**
     * 获取申报表编号
     *
     * @return
     * @path /invenorder/listDeclareFormNo
     */
    @Override
    @SoulClient(path = "/invenorder/listDeclareFormNo", desc = "获取申报表编号")
    public RpcResult<List<SelectOptionVO<String>>> listDeclareFormNo() {
        return RpcResult.success(inventoryOrderController.listDeclareFormNo());
    }

    /**
     * 获取申报表编号
     *
     * @return
     * @path /invenorder/listDeclareFormNo
     */
    @SoulClient(path = "/invenorder/listDeclareFormNoByBookId", desc = "获取申报表编号")
    @Override
    public RpcResult<List<SelectOptionVO<String>>> listDeclareFormNoByBookId(Long bookId) {
        return RpcResult.success(inventoryOrderController.listDeclareFormNoByBookId(bookId));
    }

    /**
     * 释放全部表体锁定库存
     *
     * @param idParam 清关单id
     * @return
     * @path /invenorder/releaseLockStockById
     */
    @SoulClient(path = "/invenorder/releaseLockStockById", desc = "释放全部表体锁定库存")
    @Override
    @LogParam
    public RpcResult<Boolean> releaseItemLockStockById(IdParam idParam) {
        inventoryOrderInfoService.releaseLockStockManualById(idParam.getId());
        return RpcResult.success();
    }

    @Test
    public void test() throws IOException {
        StringBuffer buffer = new StringBuffer();
        BufferedReader bufferedReader = new BufferedReader(new FileReader("C:\\Users\\<USER>\\Desktop\\test.txt"));
        String s = null;
        while ((s = bufferedReader.readLine()) != null) {//使用readLine方法，一次读一行
            buffer.append(s.trim());
        }
        String xml = buffer.toString();
        List<itemList> itemList = JSON.parseArray(xml, itemList.class);
        List<Emg> emgList = new ArrayList<>();
        itemList.forEach(i -> emgList.addAll(i.getItemList()));
        List<String> collect = emgList.stream().map(e -> e.getSellerRecord()).collect(Collectors.toList());
//        System.out.println(JSON.toJSONString(collect));
        System.out.println(collect.size());
    }

    @Data
    public static class itemList implements Serializable {
        private List<Emg> itemList;
    }

    @Data
    public static class Emg {
        private String sellerRecord;
        private String skuId;
    }
}
