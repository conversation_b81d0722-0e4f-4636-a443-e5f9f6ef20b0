package com.danding.cds.web;


import com.danding.cds.common.config.XxlJobConfig;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.servlet.ServletComponentScan;
import org.springframework.context.annotation.Import;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/4/28 11:00
 * @Description:
 */
@SpringBootApplication(scanBasePackages = {
        "com.danding.logistics",
        "com.danding.cds"
})
@EnableDubbo
@ServletComponentScan(basePackages = "com.danding.cds.http.saas.filter")
@Import({XxlJobConfig.class})
public class WebApplication {
    public static void main(String[] args) {
        if(!StringUtils.isEmpty(System.getProperty("env")) && "DEV".equals(System.getProperty("env").toUpperCase())){
            if (StringUtils.isEmpty(System.getProperty("local"))){
                System.setProperty("local","true");
            }
        }
        System.setProperty("rocketmq.client.logLevel", "ERROR");
        SpringApplication.run(WebApplication.class, args);
    }
}
