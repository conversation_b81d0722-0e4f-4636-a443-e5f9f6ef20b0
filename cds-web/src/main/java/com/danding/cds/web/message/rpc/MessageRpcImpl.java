package com.danding.cds.web.message.rpc;

import com.alibaba.fastjson.JSON;
import com.danding.cds.callback.api.enums.SubscribeStrategy;
import com.danding.cds.common.model.IdsParam;
import com.danding.cds.common.model.PagingBuilder;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.invenorder.api.enums.WorkOrderStatueEnum;
import com.danding.cds.message.api.dto.*;
import com.danding.cds.message.api.enums.MessageContentEnums;
import com.danding.cds.message.api.enums.MessageStatus;
import com.danding.cds.message.api.enums.MessageTaskStatus;
import com.danding.cds.message.api.enums.MessageType;
import com.danding.cds.message.api.service.MessageService;
import com.danding.cds.message.api.vo.CreateMessageReqVO;
import com.danding.cds.web.message.vo.MessageSearchVO;
import com.danding.cds.web.message.vo.MessageSubscribeSearchVO;
import com.danding.cds.web.message.vo.MessageTaskSearchVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.base.Splitter;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @menu 消息管理
 */
@Slf4j
@DubboService(timeout = 30000)
@RestController
public class MessageRpcImpl implements MessageRpc {

    @DubboReference
    private MessageService messageService;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/subscribePaging", desc = "消息订阅分页查询")
    public RpcResult<ListVO<MessageSubscribeSearchVO>> subscribePaging(MessageSubscribeSearch search) {
        ListVO<MessageSubscribeDTO> paging = messageService.subscribePaging(search);
        ListVO<MessageSubscribeSearchVO> result = new PagingBuilder<MessageSubscribeDTO, MessageSubscribeSearchVO>() {
            @Override
            protected MessageSubscribeSearchVO convert(MessageSubscribeDTO messageSubscribeDTO) {
                MessageSubscribeSearchVO searchVO = new MessageSubscribeSearchVO();
                searchVO.setId(messageSubscribeDTO.getId());
                searchVO.setSubscribeTag(messageSubscribeDTO.getSubscribeTag());
                List<String> messageTypeList = JSON.parseArray(messageSubscribeDTO.getMessageTypeJson(), String.class);
                for (String type : messageTypeList) {
                    if (StringUtils.isEmpty(searchVO.getMessageTypes())) {
                        searchVO.setMessageTypes(MessageType.getEnum(type).getDesc());
                    } else {
                        searchVO.setMessageTypes(searchVO.getMessageTypes() + "|" + MessageType.getEnum(type).getDesc());
                    }
                }
                searchVO.setNotifyUrl(messageSubscribeDTO.getNotifyUrl());
                searchVO.setDetail(messageSubscribeDTO.getDetail());
                searchVO.setAlertEmails(messageSubscribeDTO.getAlertEmails());
                searchVO.setMaxCount(messageSubscribeDTO.getMaxCount());
                searchVO.setRetryStrategy(SubscribeStrategy.getEnum(messageSubscribeDTO.getRetryStrategy()).getDesc());
                if (messageSubscribeDTO.getEnable()) {
                    searchVO.setEnable("已启用");
                } else {
                    searchVO.setEnable("已禁用");
                }
                return searchVO;
            }
        }.build(paging);
        return RpcResult.success(result);
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/taskPaging", desc = "消息任务分页查询")
    public RpcResult<ListVO<MessageTaskSearchVO>> taskPaging(MessageTaskSearch search) {
        ListVO<MessageTaskDTO> paging = messageService.taskPaging(search);
        Map<Long, MessageSubscribeDTO> subscribeDTOMap = new HashMap<>();
        ListVO<MessageTaskSearchVO> result = new PagingBuilder<MessageTaskDTO, MessageTaskSearchVO>() {
            @Override
            protected MessageTaskSearchVO convert(MessageTaskDTO messageTaskDTO) {
                MessageTaskSearchVO vo = new MessageTaskSearchVO();
                vo.setId(messageTaskDTO.getId());
                vo.setMessageId(messageTaskDTO.getMessageId());
                MessageSubscribeDTO subscribeDTO = subscribeDTOMap.get(messageTaskDTO.getId());
                if (subscribeDTO == null) {
                    subscribeDTO = messageService.findSubscribeById(messageTaskDTO.getSubscribeId());
                    if (subscribeDTO != null) {
                        subscribeDTOMap.put(subscribeDTO.getId(), subscribeDTO);
                    }
                }
                if (subscribeDTO != null) {
                    vo.setSubscribeDetail(subscribeDTO.getDetail());
                }
                vo.setType(messageTaskDTO.getType());
                vo.setTypeDesc(MessageType.getEnum(messageTaskDTO.getType()).getDesc());
                vo.setTaskType(messageTaskDTO.getTaskType());
                vo.setTaskTypeDesc(MessageType.MessageTaskType.getEnum(messageTaskDTO.getTaskType()).getDesc());
                vo.setBusinessCode(messageTaskDTO.getBusinessCode());
                vo.setNotifyUrl(messageTaskDTO.getNotifyUrl());
                vo.setSendRecordJson(messageTaskDTO.getSendRecordJson());
                vo.setCount(messageTaskDTO.getCount());
                vo.setLastNotifyTime(messageTaskDTO.getLastNotifyTime());
                vo.setNextNotifyTime(messageTaskDTO.getNextNotifyTime());
                vo.setStatus(vo.getStatus());
                vo.setStatusDesc(MessageTaskStatus.getEnum(messageTaskDTO.getStatus()).getDesc());
                vo.setRequestData(messageTaskDTO.getRequestData());
                vo.setCreateTime(messageTaskDTO.getCreateTime());
                return vo;
            }
        }.build(paging);
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/message/paging", desc = "消息查询")
    public RpcResult<ListVO<MessageSearchVO>> paging(MessageSearch search) {
        if (Objects.isNull(search)) {
            return RpcResult.error("参数为空");
        }
        if (!StringUtils.isEmpty(search.getBusinessCode())) {
            List<String> businessCodeList = Splitter.on(",").splitToList(search.getBusinessCode());
            if (businessCodeList.size() > 500) {
                log.warn("单次查询业务编码数量过多，上限为500");
                return RpcResult.error("单次查询业务编码数量过多，上限为500");
            }
        }
        ListVO<MessageDTO> paging = messageService.paging(search);
        ListVO<MessageSearchVO> result = new PagingBuilder<MessageDTO, MessageSearchVO>() {
            @Override
            protected MessageSearchVO convert(MessageDTO messageDTO) {
                MessageSearchVO vo = new MessageSearchVO();
                BeanUtils.copyProperties(messageDTO, vo);
                vo.setTypeDesc(MessageType.getEnum((messageDTO.getType())).getDesc());
                vo.setStatusDesc(MessageStatus.getEnum(messageDTO.getStatus()).getDesc());
                return vo;
            }
        }.build(paging);
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/message/retry", desc = "出错的消息重试")
    public RpcResult retry(IdsParam messageId) {
        if (ObjectUtils.isEmpty(messageId)) {
            return RpcResult.error("提交参数为空");
        }
        for (Long id : messageId.getIdList()) {
            messageService.retry(id);
        }
        return RpcResult.success("重试成功");
    }

    @Override
    @SoulClient(path = "/message/task/retry", desc = "任务重试")
    public RpcResult taskRetry(IdsParam messageId) {
        if (ObjectUtils.isEmpty(messageId)) {
            return RpcResult.error("提交参数为空");
        }
        for (Long id : messageId.getIdList()) {
            messageService.taskRetry(id);
        }
        return RpcResult.success("重试成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/listSubscribe", desc = "消息订阅下拉")
    public RpcResult<List<SelectOptionVO<Long>>> listSubscribe() {
        List<MessageSubscribeDTO> dataList = messageService.listSubscribeAll();
        List<SelectOptionVO<Long>> result = dataList.stream().map((MessageSubscribeDTO item) -> {
            return (SelectOptionVO<Long>) new SelectOptionVO(item.getId(), item.getDetail());
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/listMessageType", desc = "消息类型下拉")
    public RpcResult<List<SelectOptionVO<String>>> listMessageType() {
        List<SelectOptionVO<String>> result = Arrays.stream(MessageType.values()).filter((MessageType item) -> {
            return !item.equals(MessageType.NULL);
        }).map((MessageType item) -> {
            SelectOptionVO<String> optionDTO = new SelectOptionVO<String>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getDesc());
            return optionDTO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/listTaskStatus", desc = "消息任务下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listTaskStatus() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(MessageTaskStatus.values()).filter((MessageTaskStatus item) -> {
            return !item.equals(MessageTaskStatus.NULL);
        }).map((MessageTaskStatus item) -> {
            SelectOptionVO<Integer> optionDTO = new SelectOptionVO<Integer>();
            optionDTO.setId(item.getCode());
            optionDTO.setName(item.getDesc());
            return optionDTO;
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/message/listStatus", desc = "消息状态下拉")
    public RpcResult<List<SelectOptionVO<Integer>>> listStatus() {
        List<SelectOptionVO<Integer>> result = Arrays.stream(MessageStatus.values())
                .map((MessageStatus item) -> {
                    SelectOptionVO<Integer> optionDTO = new SelectOptionVO<Integer>();
                    optionDTO.setId(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 消息管理-新增消息
     *
     * @param reqVO 新增消息参数
     * @return RpcResult
     * @path /message/createMessageBatch
     */
    @Override
    @SoulClient(path = "/message/createMessageBatch", desc = "新增消息")
    @PostMapping("/message/createMessageBatch")
    public RpcResult<String> createMessageBatch(@RequestBody CreateMessageReqVO reqVO) {
        try {
            messageService.manualCreateMessage(reqVO);
            return RpcResult.success("新增成功");
        } catch (ArgsInvalidException e) {
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 消息内容下拉
     *
     * @return List<EnumUtils>
     * @path /message/listContentType
     */
    @Override
    @SoulClient(path = "/message/listContentType", desc = "消息内容下拉")
    @GetMapping("/message/listContentType")
    public RpcResult<List<SelectOptionVO<String>>> listMessageContentType(String messageType) {


        List<SelectOptionVO<String>> list = Arrays.stream(MessageContentEnums.values())
                .filter(i -> Objects.equals(messageType, i.getMessageType().getCode()))
                .map(i -> {
                    SelectOptionVO<String> optionVO = new SelectOptionVO<>();
                    optionVO.setId(i.getCode());
                    optionVO.setName(i.getDesc());
                    return optionVO;
                }).collect(Collectors.toList());

        // todo 这个位置需要整体考虑，因为消息类型会有多个枚举，所以这里需要考虑优化，淘天版本先直接整合一下，后续版本在优化
        if (Objects.equals(messageType, MessageType.TAOTIAN_REPORT.getCode())) {
            List<SelectOptionVO<String>> taotianReportList = Arrays.stream(WorkOrderStatueEnum.values())
                    .map(i -> {
                        SelectOptionVO<String> optionVO = new SelectOptionVO<>();
                        optionVO.setId(i.getCode());
                        optionVO.setName(i.getDesc() + "-" + i.getCode());
                        return optionVO;
                    }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(list)) {
                list.addAll(taotianReportList);
            } else {
                list = taotianReportList;
            }
        }
        return RpcResult.success(list);
    }
}
