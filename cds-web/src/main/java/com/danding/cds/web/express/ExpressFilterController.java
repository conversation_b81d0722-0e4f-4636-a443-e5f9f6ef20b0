package com.danding.cds.web.express;

import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.express.api.dto.*;
import com.danding.cds.express.api.service.ExpressFilterService;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.web.express.vo.ExpressSearchResult;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Api(tags = "快递配置管理")
@RestController
@RequestMapping("/express-filter")
public class ExpressFilterController {
    @DubboReference
    private ExpressFilterService expressFilterService;
    @DubboReference
    private ExpressService expressService;
    public ExpressFilterController() {
    }

    @GetMapping("/list")
    @ApiOperation(value = "获取所有的快递选择的下来框,id为当前快动快递公司id")
    public List<SelectItemVO> list(@RequestParam(required = true,value ="id") Long id){
        List<ExpressDTO> dataList = expressService.listAll();
        List<SelectItemVO> oretList = new ArrayList<>();
        SelectItemVO _optionDTO = new SelectItemVO();
        _optionDTO.setValue(0L);
        _optionDTO.setName("不替换");
        oretList.add(_optionDTO);
        List<SelectItemVO> result = dataList.stream().filter((ExpressDTO item)->{
            return (new Integer(1).equals(item.getEnable())&&(!item.getId().equals(id)));
        }).map((ExpressDTO item) -> {
            SelectItemVO optionDTO = new SelectItemVO();
            optionDTO.setValue(item.getId());
            optionDTO.setName(item.getName());
            return optionDTO;
        }).collect(Collectors.toList());
        oretList.addAll(result);
        return oretList;
    }
    @GetMapping("/paging")
    @ApiOperation(value = "列表查询配置分页")
    public ListVO<ExpressFilterDTO> paging(ExpressFilterSearch search){
        ListVO<ExpressFilterDTO> list = expressFilterService.paging(search);
        for(int i=0;i<list.getDataList().size();i++)
        {
            ExpressFilterDTO s = list.getDataList().get(i);
            Long replaceId  = s.getReplaceExpressId();
            if(LongUtil.isNone(replaceId))
                s.setReplaceExpressName("");
            else {
                ExpressDTO expressDTO = expressService.findById(replaceId);
                if(expressDTO!=null)
                {
                    s.setReplaceExpressName(expressDTO.getName());
                }else
                {
                    s.setReplaceExpressName("");
                }
            }
            s.setIdx(i+1);
        }
        return list;
    }
    @ApiOperation(value = "删除当前配置id")
    @GetMapping("/delete")
    public   Response<String> delete(Long id) throws ArgsErrorException {
        Response<String> oret ;
        if(expressFilterService.deleteExpressFilter(id))
        {
            oret= new Response<>("删除成功");
            return oret;
        }
        else
        {
            oret= new Response<>("删除失败");
            oret.setCode(-1);
            oret.setErrorMessage("删除失败");
            return oret;
        }
    }
    @ApiOperation(value = "新增快递配置方式")
    @PostMapping("/upset")
    public   Response<String> upset(@RequestBody ExpressFilterSubmit submit) throws ArgsErrorException {
        ExpressFilterDTO expressFilterDTO  = new ExpressFilterDTO();
        BeanUtils.copyProperties(submit,expressFilterDTO);
        Response<String> oret ;
        if(expressFilterDTO.getRefExpressId()==null||expressFilterDTO.getRefExpressId()<=0)
        {
            oret= new Response<>("目标快递公司参数为空");
            oret.setCode(-1);
            oret.setErrorMessage("目标快递公司参数为空");
            return oret;
        }
        else if(expressFilterDTO.getReplaceExpressId()==null||expressFilterDTO.getReplaceExpressId()<0)
        {
            oret= new Response<>("替换快递公司参数为空");
            oret.setCode(-1);
            oret.setErrorMessage("替换快递公司参数为空");
            return oret;
        }
        if(expressFilterDTO.getProv()==null)
            expressFilterDTO.setProv("");
        if(expressFilterDTO.getCity()==null)
            expressFilterDTO.setCity("");
        if(expressFilterDTO.getArea()==null)
            expressFilterDTO.setArea("");
        expressFilterDTO.setProv(StringUtils.strip(expressFilterDTO.getProv(),""));
        if(!expressFilterService.checkUnquie(expressFilterDTO))
        {
            oret= new Response<>("对不起!重复添加记录");
            oret.setCode(-1);
            oret.setErrorMessage("对不起!重复添加记录");
            return oret;
        }
        if(expressFilterService.createExpressFilter(expressFilterDTO)) {
            oret = new Response<>("创建成功");
            oret.setErrorMessage("创建成功");
        }
        else
        {
            oret= new Response<>("创建失败");
            oret.setCode(-1);
            oret.setErrorMessage("创建失败");
        }
        return oret;
    }

}
