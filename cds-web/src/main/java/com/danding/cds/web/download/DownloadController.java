package com.danding.cds.web.download;

import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.download.api.dto.DownloadDTO;
import com.danding.cds.download.api.dto.DownloadSearchCondition;
import com.danding.cds.download.api.enums.DowmloadStatusEnum;
import com.danding.cds.download.api.service.DownloadService;
import com.danding.cds.web.download.vo.DownloadItemVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.dubbo.config.annotation.DubboReference;
import org.joda.time.DateTime;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

@Api(tags = "下载中心")
@RestController
@RequestMapping("/downloadCenter")
public class DownloadController {

    @DubboReference
    private DownloadService downloadService;

    @ApiOperation(value = "分页查询")
    @GetMapping("/paging")
    public ListVO<DownloadItemVO> paging(DownloadSearchCondition condition) {
        ListVO<DownloadDTO> pageResult = downloadService.paging(condition);
        ListVO<DownloadItemVO> result = new ListVO<>();
        result.setPage(pageResult.getPage());
        List<DownloadItemVO> list = new ArrayList<>();
        for (DownloadDTO downloadDTO : pageResult.getDataList()) {
            DownloadItemVO itemVO = new DownloadItemVO();
            itemVO.setId(downloadDTO.getId());
            itemVO.setReportName(downloadDTO.getReportName());
            itemVO.setCreateTime(new DateTime(downloadDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            if (!LongUtil.isNone(downloadDTO.getFinishTime())) {
                itemVO.setFinishTime(new DateTime(downloadDTO.getFinishTime()).toString("yyyy-MM-dd HH:mm:ss"));
            }
            itemVO.setStatus(downloadDTO.getStatus());
            if (DowmloadStatusEnum.getEnum(downloadDTO.getStatus()) != null) {
                itemVO.setStatusDesc(DowmloadStatusEnum.getEnum(downloadDTO.getStatus()).getDesc());
            }
            itemVO.setDownloadCount(downloadDTO.getDownloadCount());
            itemVO.setReportType(downloadDTO.getReportType());
            itemVO.setFilePath(downloadDTO.getFilePath());
            list.add(itemVO);
        }
        result.setDataList(list);
        return result;
    }

    @ApiOperation(value = "增加下载次数")
    @GetMapping("/updateDownloadCount")
    public Long updateDownloadCount(Long id) {
        DownloadDTO downloadDTO = downloadService.findById(id);
        if (downloadDTO == null) {
            throw new ArgsErrorException("该文档不存在");
        }
        return downloadService.updateDownLoadCount(id, downloadDTO.getDownloadCount() + 1);
    }

    @ApiOperation(value = "删除")
    @GetMapping("/delete")
    public Long delete(Long id) {
        return downloadService.updateDeleted(id);
    }


}
