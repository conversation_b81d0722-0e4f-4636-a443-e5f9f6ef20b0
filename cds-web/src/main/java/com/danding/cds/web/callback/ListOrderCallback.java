package com.danding.cds.web.callback;

import lombok.Data;

/**
 * @Auther: Dante-Gxj
 * @Date: 2020/3/11 18:22
 * @Description:
 */
@Data
public class ListOrderCallback {

    /**
     * 业务号 创建订单成功之后返回的订单号,或者分包之后的包裹号，可以根据type来进行区分
     */
    private String bizId;

    /**
     * 商户业务系统的订单号
     */
    private String merchantOrderNo;

    /**
     * 状态类型 订单状态，paymentStatus：支付状态, settleStatus: 结算状态,packageStatus:包裹状态， saleAfterStatus：售后状态，customs：海关申报状态
     */
    private String type;

    /**
     * 状态枚举值，详见订单状态枚举, 支付状态枚举以及结算状态枚举
     */
    private String status;

    /**
     * 状态描述 标识订单的状态描述，详见订单状态, 支付状态枚举以及结算状态枚举
     */
    private String description;

    /**
     * 状态变换的原因 订单的状态变化原因，譬如"用户取消关闭"
     */
    private String message;

    /**
     * ，格式: "yyyy-MM-dd HH:mm:ss"
     */
    private String datetime;

    /**
     * 额外的消息内容 一个json字符串，主要是包含一些附加信息
     */
    private String extraInfo;

    /**
     * 安全签名
     * PS::暂不验证
     */
    private String sign;
}
