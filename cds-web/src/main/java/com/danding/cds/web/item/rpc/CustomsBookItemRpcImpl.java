package com.danding.cds.web.item.rpc;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.utils.LongUtil;
import com.danding.cds.common.utils.ThreadContextUtil;
import com.danding.cds.customs.country.api.dto.CustomsCountryDTO;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.currency.api.dto.CustomsCurrencyDTO;
import com.danding.cds.customs.currency.api.service.CustomsCurrencyService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.hs.api.service.CustomsHsService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.invenorder.api.enums.InventoryOrderBusinessEnum;
import com.danding.cds.invenorder.api.enums.InventoryOrderChannel;
import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import com.danding.cds.invenorder.api.service.InventoryOrderInfoService;
import com.danding.cds.item.api.dto.*;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.CustomsBookService;
import com.danding.cds.item.api.vo.CustomsBookResVo;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.service.UploadProcessService;
import com.danding.cds.v2.bean.dto.CustomsBookItemAdjustLogDTO;
import com.danding.cds.v2.bean.dto.InventoryOrderItemLockStockDTO;
import com.danding.cds.v2.bean.enums.CustomsBookItemAdjustTypeEnums;
import com.danding.cds.v2.bean.enums.GoodsSourceEnums;
import com.danding.cds.v2.bean.es.ItemTrackLogEsDTO;
import com.danding.cds.v2.bean.vo.req.CustomsBookItemAdjustLogSearch;
import com.danding.cds.v2.bean.vo.req.ItemTrackLogSearch;
import com.danding.cds.v2.bean.vo.res.AssociateCustomsBookItemResVO;
import com.danding.cds.web.item.vo.CustomsBookItemAdjustLogVO;
import com.danding.cds.web.item.vo.CustomsBookItemDetailVO;
import com.danding.cds.web.item.vo.CustomsBookItemLockStockVO;
import com.danding.cds.web.item.vo.CustomsBookItemVO;
import com.danding.cds.web.v2.bean.es.ItemTrackLogEsVO;
import com.danding.component.common.utils.EnumUtils;
import com.danding.component.uc.helper.SimpleUserHelper;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.bean.ImportWebForm;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.annotation.UCLog;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@DubboService
@Slf4j
@ApiModel("账册库存")
/**
 * @menu 账册库存
 */
public class CustomsBookItemRpcImpl implements CustomsBookItemRpc {

    @DubboReference
    private CustomsBookItemService customsBookItemService;

    @DubboReference
    private CustomsBookService customsBookService;

    @DubboReference
    private CustomsCountryService customsCountryService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsHsService customsHsService;

    @DubboReference
    private CustomsCurrencyService customsCurrencyService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private UploadProcessService uploadProcessService;

    @DubboReference
    private InventoryOrderInfoService inventoryOrderInfoService;

    @Override
    @ApiOperation(
            value = "分页查询",
            notes = "分页查询"
    )
    @SoulClient(path = "/customsBookItem/paging", desc = "查看账册库存")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<CustomsBookItemVO>> paging(CustomsBookItemSearchCondition condition) {
        //前端控件用数字不好处理  用数字表达式判断库存账册是否存在特俗字符
        if (!StringUtils.isEmpty(condition.getAccountNum())) {
            Pattern pattern = Pattern.compile("^-?\\d+$");
            Matcher matcher = pattern.matcher(condition.getAccountNum());
            if (!matcher.matches()) {
                return RpcResult.error("账册库存数量入参含有非法字符");
            }
        }
        // 可以查看的账册的ID列表,与查询条件账册ID比较
        List<Long> accountBookIdList = condition.getRoleAccountBookIdList();
        if (!CollectionUtils.isEmpty(accountBookIdList)) {
            Long bookId = condition.getCustomsBookId();
            if (bookId != null && !accountBookIdList.contains(bookId)) {
                return RpcResult.success(new ListVO<>());
            }
        }

        ListVO<CustomsBookItemDTO> paging = customsBookItemService.paging(condition);
        ListVO<CustomsBookItemVO> result = new ListVO<>();

        result.setPage(paging.getPage());
        List<CustomsBookItemVO> dataList = new ArrayList<>();
        for (CustomsBookItemDTO itemDTO : paging.getDataList()) {
            CustomsBookItemVO itemVO = new CustomsBookItemVO();
            BeanUtil.copyProperties(itemDTO, itemVO);
            CustomsBookDTO customsBookDTO = customsBookService.findById(itemDTO.getCustomsBookId());
            itemVO.setCustomsBookNo(customsBookDTO.getBookNo());
            itemVO.setCustomsDistrictName(CustomsDistrictEnum.getEnum(customsBookDTO.getCustomsDistrictCode()).getDesc());
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(itemDTO.getOriginCountry());
            if (countryDTO != null) {
                itemVO.setOriginCountryName(countryDTO.getName());
            }
            CustomsUomDTO uomDTO = customsUomService.findByCode(itemDTO.getGoodsUnit());
            if (uomDTO != null) {
                itemVO.setGoodsUnitName(uomDTO.getName());
            }
            uomDTO = customsUomService.findByCode(itemDTO.getFirstUnit());
            if (uomDTO != null) {
                itemVO.setFirstUnitName(uomDTO.getName());
            }
            if (!StringUtils.isEmpty(itemDTO.getSecondUnit())) {
                uomDTO = customsUomService.findByCode(itemDTO.getSecondUnit());
                if (uomDTO != null) {
                    itemVO.setSecondUnitName(uomDTO.getName());
                }
            }
            itemVO.setCreateTime(new DateTime(itemDTO.getCreateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            itemVO.setUpdateTime(new DateTime(itemDTO.getUpdateTime()).toString("yyyy-MM-dd HH:mm:ss"));
            if (!LongUtil.isNone(itemDTO.getInDate())) {
                itemVO.setInDate(new DateTime(itemDTO.getInDate()).toString("yyyy-MM-dd"));
            } else {
                itemVO.setInDate("");
            }
            if (Objects.nonNull(itemDTO.getAccountNum())) {
                Integer diffQty = itemDTO.getTotalDiffQty() - itemDTO.getAccountNum();
                itemVO.setDiffQty(diffQty);
            }
            dataList.add(itemVO);
        }
        result.setDataList(dataList);
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/detail", desc = "账册库存详情")
    public RpcResult<CustomsBookItemDetailVO> detail(IdParam idParam) {
        CustomsBookItemDetailVO detailVO = new CustomsBookItemDetailVO();
        CustomsBookItemDTO itemDTO = customsBookItemService.findById(idParam.getId());
        if (itemDTO == null) {
            return null;
        }
        BeanUtil.copyProperties(itemDTO, detailVO);
        if (!LongUtil.isNone(itemDTO.getInDate())) {
            detailVO.setInDate(new DateTime(itemDTO.getInDate()).toString("yyyy-MM-dd"));
        } else {
            detailVO.setInDate(null);
        }

        if(itemDTO.getGoodsSource()!= null){
            GoodsSourceEnums goodsSourceEnums = GoodsSourceEnums.getEnums(itemDTO.getGoodsSource());
            if (goodsSourceEnums != null) {
                detailVO.setGoodsSourceDesc(goodsSourceEnums.getDesc());
            }
        }

        if (!StringUtils.isEmpty(itemDTO.getCurrCode())) {
            CustomsCurrencyDTO code = customsCurrencyService.findByCode(itemDTO.getCurrCode());
            if (code != null) {
                detailVO.setCurrCodeDesc(itemDTO.getCurrCode() + ":" + code.getName());
            }
        }
        if (Objects.nonNull(itemDTO.getOriginCountry())) {
            CustomsCountryDTO countryDTO = customsCountryService.findByCode(itemDTO.getOriginCountry());
            if (Objects.nonNull(countryDTO)) {
                detailVO.setOriginCountryDesc(itemDTO.getOriginCountry() + ":" + countryDTO.getName());
            }
        }
        if (Objects.nonNull(itemDTO.getFirstUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(itemDTO.getFirstUnit());
            if (Objects.nonNull(uomDTO)) {
                detailVO.setFirstUnitDesc(itemDTO.getFirstUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(itemDTO.getSecondUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(itemDTO.getSecondUnit());
            if (Objects.nonNull(uomDTO)) {
                detailVO.setSecondUnitDesc(itemDTO.getSecondUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(itemDTO.getGoodsUnit())) {
            CustomsUomDTO uomDTO = customsUomService.findByCode(itemDTO.getGoodsUnit());
            if (Objects.nonNull(uomDTO)) {
                detailVO.setGoodsUnitDesc(itemDTO.getGoodsUnit() + ":" + uomDTO.getName());
            }
        }
        if (Objects.nonNull(itemDTO.getCustomsBookId())) {
            CustomsBookResVo customsBookResVo = customsBookService.findByIdV2(itemDTO.getCustomsBookId());
            if (Objects.nonNull(customsBookResVo)) {
                detailVO.setCustomsBookNo(customsBookResVo.getBookNo());
            }
        }
        return RpcResult.success(detailVO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @UCLog
    @SoulClient(path = "/customsBookItem/batchEnable", desc = "分页查询")
    public RpcResult<String> batchEnable(CustomsBookItemEnableSubmit submit) throws Exception {
//        StringBuilder errMsg = new StringBuilder();
//        //存在重复料号已启用的备案序号标志
//        boolean existEnable = false;
//        for (Long id : submit.getIds()) {
//            //重复料号只能开启一个备案序号
//            if (submit.getEnable() == 1) {
//                CustomsBookItemDTO customsBookItemDTO = customsBookItemService.findById(id);
//                List<CustomsBookItemDTO> customsBookItemDTOList = customsBookItemService.findByBookIdAndProId(customsBookItemDTO.getCustomsBookId(), customsBookItemDTO.getProductId());
//                for (CustomsBookItemDTO temp : customsBookItemDTOList) {
//                    if (!temp.getId().equals(id) && temp.getEnable() == 1) {
//                        // 存在重复料号已启用的备案序号
//                        errMsg.append("料号:").append(temp.getProductId()).append("备案序号:").append(temp.getGoodsSeqNo()).append("已开启;");
//                        existEnable = true;
//                        break;
//                    }
//                }
//            }
//            if (submit.getEnable() == 0 || (submit.getEnable() == 1 && !existEnable)) {
//                CustomsBookItemSubmit itemSubmit = new CustomsBookItemSubmit();
//                itemSubmit.setId(id);
//                itemSubmit.setEnable(submit.getEnable());
        try {
            customsBookItemService.upsetEnable(submit.getIds(), submit.getEnable());
            log.info("[op:customsBookItem-batchEnable] realUserName={}", SimpleUserHelper.getRealUserName());
            String result = (submit.getEnable() == 1 ? "启用" : "禁用") + "成功";
            return RpcResult.success("", result);
        } catch (ArgsInvalidException e) {
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("账册库存切换失败 error={}", e.getMessage(), e);
        }
        return RpcResult.error((submit.getEnable() == 1 ? "启用" : "禁用") + "失败");
//            }
//        }
//        if (!StringUtils.isEmpty(errMsg.toString())) {
//            return RpcResult.error(errMsg.toString());
//        }
//        return RpcResult.success(1L);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/exportExcelByDownLoadCenter", desc = "导出（下载中心）")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> exportExcelByDownLoadCenter(CustomsBookItemSearchCondition condition) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    condition, ReportType.CUSTOMS_BOOK_ITEM_LIST_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/import/query", desc = "导入账册库存查询")
    public RpcResult<LoadTaskInfoDTO> importQuery() {
        UploadType uploadType = UploadType.CUSTOMS_BOOK_ITEM;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setTemplateUrl(uploadType.getUrl());
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        return RpcResult.success(loadTaskInfoDTO);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/import", desc = "导入账册库存")
    @UcAccountBookAuthGetAndCheck(onlyEffectiveData = true)
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<String> importExcel(ImportWebForm importWebForm) {
        try {
            List<Long> accBookIdList = ThreadContextUtil.getAccountBookList();
            CustomsBookItemSearchCondition itemSearchCondition = new CustomsBookItemSearchCondition();
            itemSearchCondition.setRoleAccountBookIdList(accBookIdList);
            uploadProcessService.submitProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    importWebForm.getUrl(), UploadType.CUSTOMS_BOOK_ITEM, itemSearchCondition);
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/import/confirm", desc = "导入账册库存确认")
    public RpcResult<String> importConfirm() {
        UploadType uploadType = UploadType.CUSTOMS_BOOK_ITEM;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success("提交成功");
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/updBook", desc = "更新账册")
    public RpcResult updBook(CustomsBookItemUpdBookSubmit submit) {
        try {
            if (StrUtil.isNotBlank(submit.getGoodsName())) {
                submit.setGoodsName(StringUtils.trimWhitespace(submit.getGoodsName()));
            }
            Long bookId = customsBookItemService.updBook(submit);
            return RpcResult.success(bookId);
        } catch (ArgsErrorException e) {
            log.warn("处理异常：{}", e.getMessage());
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error(e.getMessage());
        }
    }

    /**
     * 商品备案审核详情
     *
     * @param productId
     * @param accountBookNoId
     * @return
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/viewItemListByProductId", desc = "备案审核查看账册信息")
    public RpcResult<List<AssociateCustomsBookItemResVO>> viewItemListByProductId(String productId, Long accountBookNoId) {
        try {
            List<AssociateCustomsBookItemResVO> list = customsBookItemService.viewItemListByProductId(productId, accountBookNoId);
            return RpcResult.success(list);
        } catch (ArgsErrorException e) {
            log.error("CustomsBookItemRpcImpl viewItemListByProductId error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("CustomsBookItemRpcImpl viewItemListByProductId error={}", e.getMessage(), e);
        }
        return RpcResult.error("查询账册信息失败");
    }

    /**
     * 查询账册库存日志
     *
     * @param search
     * @return
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/customsBookItem/pagingTrackLog", desc = "账册库存日志(ES)")
    public RpcResult<ListVO<ItemTrackLogEsVO>> pagingTrackLog(ItemTrackLogSearch search) {
        ListVO<ItemTrackLogEsDTO> paging = customsBookItemService.pagingTrackLog(search);
        ListVO<ItemTrackLogEsVO> result = new ListVO<>();
        result.setPage(paging.getPage());
        result.setDataList(paging.getDataList().stream().map(p -> {
            ItemTrackLogEsVO vo = new ItemTrackLogEsVO();
            BeanUtils.copyProperties(p, vo);
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }

    /**
     * 锁定库存明细
     *
     * @param idParam
     * @return
     * @path /customsBookItem/listLockStock
     */
    @Override
    @SoulClient(path = "/customsBookItem/listLockStock", desc = "锁定库存明细")
    public RpcResult<List<CustomsBookItemLockStockVO>> listLockStock(IdParam idParam) {
        if (Objects.isNull(idParam) || Objects.isNull(idParam.getId())) {
            return RpcResult.error("参数为空");
        }
        try {
            List<InventoryOrderItemLockStockDTO> lockStockDetail = inventoryOrderInfoService.getLockStockDetail(idParam.getId());
            List<CustomsBookItemLockStockVO> result = lockStockDetail.stream().map(detail -> {
                CustomsBookItemLockStockVO vo = new CustomsBookItemLockStockVO();
                vo.setInventoryOrderSn(detail.getInventoryOrderSn());
                vo.setLockStockNum(detail.getLockStockNum());
                vo.setInventoryOrderStatus(InventoryOrderEnum.getDesc(detail.getInventoryOrderStatus()));
                vo.setInventoryOrderBizType(InventoryOrderBusinessEnum.getDesc(detail.getInventoryOrderBizType()));
                vo.setInventoryOrderChannel(InventoryOrderChannel.getDesc(detail.getInventoryOrderChannel()));
                return vo;
            }).collect(Collectors.toList());
            return RpcResult.success(result);
        } catch (ArgsErrorException e) {
            log.error("CustomsBookItemRpcImpl lockStockDetail error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("CustomsBookItemRpcImpl lockStockDetail error={}", e.getMessage(), e);
            return RpcResult.error("未知系统异常");
        }
    }

    /**
     * 调整日志分页
     *
     * @param search
     * @return
     * @path /customsBookItem/pagingAdjustLog
     */
    @SoulClient(path = "/customsBookItem/pagingAdjustLog", desc = "调整日志分页")
    @Override
    public RpcResult<ListVO<CustomsBookItemAdjustLogVO>> pagingAdjustLog(CustomsBookItemAdjustLogSearch search) {
        try {
            ListVO<CustomsBookItemAdjustLogDTO> paging = customsBookItemService.pagingAdjustLog(search);
            ListVO<CustomsBookItemAdjustLogVO> result = new ListVO<>();
            result.setPage(paging.getPage());
            result.setDataList(paging.getDataList().stream().map(p -> {
                CustomsBookItemAdjustLogVO vo = new CustomsBookItemAdjustLogVO();
                BeanUtils.copyProperties(p, vo);
                vo.setAdjustTypeDesc(CustomsBookItemAdjustTypeEnums.getDesc(p.getAdjustType()));
                return vo;
            }).collect(Collectors.toList()));
            return RpcResult.success(result);
        } catch (ArgsInvalidException e) {
            log.error("CustomsBookItemRpcImpl pagingAdjustLog error={}", e.getErrorMessage(), e);
            return RpcResult.error(e.getErrorMessage());
        } catch (Exception e) {
            log.error("CustomsBookItemRpcImpl pagingAdjustLog error={}", e.getMessage(), e);
            return RpcResult.error("未知系统异常");
        }
    }


    @Override
    @SoulClient(path = "/customsBookItem/listAdjustType", desc = "调整类型下拉")
    public RpcResult<List<EnumUtils>> listAdjustType() {
        return RpcResult.success(EnumUtils.build(CustomsBookItemAdjustTypeEnums.class, "code", "desc"));
    }
}
