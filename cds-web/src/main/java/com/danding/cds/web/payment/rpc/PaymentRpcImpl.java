package com.danding.cds.web.payment.rpc;

import com.danding.cds.c.api.rpc.CustomsPaymentDeclareRpc;
import com.danding.cds.c.api.rpc.CustomsPaymentRpc;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.payment.api.service.CustomsPaymentDeclareService;
import com.danding.cds.customs.payment.api.service.CustomsPaymentService;
import com.danding.cds.customs.payment.api.service.dto.PaymentSearch;
import com.danding.cds.payChannel.api.dto.PayChannelDTO;
import com.danding.cds.payChannel.api.service.PayChannelService;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDTO;
import com.danding.cds.payinfo.api.dto.CustomsPaymentDeclareDTO;
import com.danding.cds.taxes.api.dto.TaxesTenantAccountDTO;
import com.danding.cds.web.order.OrderLogManager;
import com.danding.cds.web.order.vo.CustomsPaymentReturnMsgResult;
import com.danding.cds.web.payment.rpc.param.RpcPaymentStatusParam;
import com.danding.cds.web.payment.vo.CustomsPaymentResult;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.encrypt.annotation.DataSecurity;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

@Slf4j
@RestController
@DubboService
public class PaymentRpcImpl implements PaymentRpc {
    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference
    private CustomsPaymentService customsPaymentService;
    @DubboReference
    private CustomsPaymentRpc customsPaymentRpc;

    @Autowired
    private OrderLogManager orderLogManager;

    @DubboReference
    private CustomsPaymentDeclareService customsPaymentDeclareService;
    @DubboReference
    private CustomsPaymentDeclareRpc customsPaymentDeclareRpc;

    @DubboReference
    private CompanyService companyService;

    @DubboReference
    private PayChannelService payChannelService;

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/payment/paging", desc = "支付单分页查询")
    @PostMapping("/payment/paging")
    @DataSecurity
    public RpcResult<ListVO<CustomsPaymentResult>> paging(PaymentSearch search) {
        ListVO<CustomsPaymentDTO> dtoListVO;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            dtoListVO = customsPaymentRpc.paging(search);
        } else {
            dtoListVO = customsPaymentService.paging(search);
        }
        ListVO<CustomsPaymentResult> result = new ListVO<>();
        result.setPage(dtoListVO.getPage());
        Map<Long, CompanyDTO> companyDTOMap = new HashMap<>();
        Map<String, TaxesTenantAccountDTO> accountDTOMap = new HashMap<>();
        result.setDataList(dtoListVO.getDataList().stream().map((CustomsPaymentDTO dto) ->{
            CustomsPaymentResult vo = new CustomsPaymentResult();
            vo.setBuyerIdNo(dto.getBuyerIdNo());
            vo.setBuyerIdType(dto.getBuyerIdType());
            vo.setBuyerName(dto.getBuyerName());
            vo.setCurrencyCode(dto.getCurrencyCode());
            vo.setOrderId(String.valueOf(dto.getOrderId()));
            CompanyDTO ebp = companyDTOMap.get(dto.getEbpId());
            if (ebp == null){
                ebp = companyService.findUnifiedCrossInfoById(dto.getEbpId());
                companyDTOMap.put(ebp.getId(),ebp);
            }
            vo.setEbp(ebp.getCode());
            vo.setEbpName(ebp.getName());
            vo.setOutOrderNo(dto.getOutOrderNo());
            vo.setDeclareOrderNo(dto.getDeclareOrderNo());
            CompanyDTO payCompany = companyDTOMap.get(dto.getPayCompanyId());
            if (payCompany == null){
                payCompany = companyService.findUnifiedCrossInfoById(dto.getPayCompanyId());
                if(Objects.nonNull(payCompany)) {
                    companyDTOMap.put(payCompany.getId(), payCompany);
                }
            }
            vo.setPayCompany(payCompany.getCode());
            vo.setPayCompanyName(payCompany.getName());
            vo.setPayTime(dto.getPayTime());
            vo.setTradePayNo(dto.getTradePayNo());
            vo.setId(dto.getId() + "");
            vo.setStatus(dto.getStatus());
            vo.setStatusDesc(CustomsActionStatus.getEnum(dto.getStatus()).getDesc());

            PayChannelDTO payChannelDTO = payChannelService.findById(dto.getPayChannelId());
            vo.setPayChannel(payChannelDTO.getName());
            vo.setCreateTIme(dto.getCreateTime());
            vo.setLastDeclareTime(dto.getLastDeclareTime());
            vo.setLastCustomsTime(dto.getLastCustomsTime());
            vo.setFinishTime(dto.getFinishTime());

            List<CustomsPaymentReturnMsgResult> returnMsgResultList = new ArrayList<CustomsPaymentReturnMsgResult>();
            List<CustomsPaymentDeclareDTO> customsPaymentDeclareList;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                customsPaymentDeclareList = customsPaymentDeclareRpc.findByCustomsPaymentCode(dto.getSn());
            } else {
                customsPaymentDeclareList = customsPaymentDeclareService.findByCustomsPaymentCode(dto.getSn());
            }
            for (CustomsPaymentDeclareDTO declareDTO : customsPaymentDeclareList) {
                CustomsPaymentReturnMsgResult customsPaymentReturnMsgResult = new CustomsPaymentReturnMsgResult();
                customsPaymentReturnMsgResult.setCreateTime(declareDTO.getCreateTime());
                customsPaymentReturnMsgResult.setReturnMsg(declareDTO.getReturnMsg());
                customsPaymentReturnMsgResult.setReturnTime(declareDTO.getReturnTime());
                customsPaymentReturnMsgResult.setStatus(declareDTO.getStatus());
                returnMsgResultList.add(customsPaymentReturnMsgResult);
            }
            vo.setCustomsPaymentReturnMsgResultList(returnMsgResultList);
//            List<CustomsPaymentLogResult> paymentlist =  orderLogManager.builderPayment(dto);
//            vo.setLogList(paymentlist);
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/payment/rePush", desc = "支付单批量重推")
    @PostMapping("/payment/rePush")
    public RpcResult<String> rePush(String sns) {
        List<String> idList = Lists.newArrayList(sns.split(","));
        StringBuilder errorMsg = new StringBuilder();
        Integer idx = 1;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            for (String sn : idList) {
                try {
                    customsPaymentRpc.rePush(sn,true);
                } catch (ArgsErrorException e) {
                    errorMsg.append("第").append(idx).append("项重推失败:").append(e.getErrorMessage()).append(";");
                }
                idx ++;
            }
        } else {
            for (String sn : idList) {
                try {
                    customsPaymentService.rePush(sn,true);
                } catch (ArgsErrorException e) {
                    errorMsg.append("第").append(idx).append("项重推失败:").append(e.getErrorMessage()).append(";");
                }
                idx ++;
            }
        }
        if (StringUtils.isEmpty(errorMsg.toString())){
            return RpcResult.success("重推成功");
        }else {
            return RpcResult.error(errorMsg.toString());
        }
    }

    @Override
    @SoulClient(path = "/payment/manualUpdStatus", desc = "手动修改支付单状态")
    @PostMapping("/payment/manualUpdStatus")
    public RpcResult<String> manualUpdStatus(RpcPaymentStatusParam param) {
        if(ObjectUtils.isEmpty(param)){
            return RpcResult.error("传入参数为空");
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            for(Long id : param.getIdList()){
                log.info("修改 paymentId = {}, 修改状态 = {}", id, param.getStatus());
                CustomsPaymentDeclareDTO customsPaymentDeclareDTO = new CustomsPaymentDeclareDTO();
                customsPaymentDeclareDTO.setStatus(param.getStatus());
                customsPaymentDeclareDTO.setPaymentId(id);
                customsPaymentDeclareRpc.manualUpdStatus(customsPaymentDeclareDTO);
            }
        } else {
            for(Long id : param.getIdList()){
                log.info("修改 paymentId = {}, 修改状态 = {}", id, param.getStatus());
                CustomsPaymentDeclareDTO customsPaymentDeclareDTO = new CustomsPaymentDeclareDTO();
                customsPaymentDeclareDTO.setStatus(param.getStatus());
                customsPaymentDeclareDTO.setPaymentId(id);
                customsPaymentDeclareService.manualUpdStatus(customsPaymentDeclareDTO);
            }
        }
        return RpcResult.success("修改成功");
    }
}
