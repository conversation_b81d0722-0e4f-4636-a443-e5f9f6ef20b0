package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.download.api.vo.FbStockInOutExcelVO;
import com.danding.cds.v2.api.FbStockInOutService;
import com.danding.cds.v2.bean.dto.FbStockInOutDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @description:
 * @date 2023/2/23 13:30
 */
@Slf4j
@Component
@ParkImportsHandler(funcCode = "IMPORT_FB_STOCK_IN_OUT", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E6%B5%B7%E5%85%B3%E5%87%BA%E5%85%A5%E5%BA%93%E8%AE%B0%E5%BD%95%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFv2.0.xlsx",
        groups = {@ParkImportsHandler.Group(name = "导入模板", classes = FbStockInOutExcelVO.class)})
public class FbStockInOutImportHandler extends ImportsBaseHandlerPlus {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        List<FbStockInOutExcelVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("导入模板")) {
                list = group.getDataList(FbStockInOutExcelVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));
        if (CollUtil.isEmpty(list)) {
            return;
        }
        FbStockInOutService fbStockInOutService = this.getBean(FbStockInOutService.class);
        int index = 1;
        for (FbStockInOutExcelVO excelVO : list) {
            String result = this.doValidator(excelVO);
            ImportResultResVo resultResVo = new ImportResultResVo();
            if (StringUtils.hasText(result)) {
                resultResVo.setFlag(false);
                resultResVo.setReason(result);
            } else {
                FbStockInOutDTO fbStockInOutDTO = ConvertUtil.beanConvert(excelVO, FbStockInOutDTO.class);
                resultResVo = fbStockInOutService.checkAndImport(fbStockInOutDTO);
                log.info("导入后的返回结果数据{}", resultResVo.toString());
            }
            this.callbackData(resultResVo.getFlag(), index, resultResVo.getReason(), excelVO);
            index++;
        }
    }
}
