package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.item.api.dto.submit.GoodsAutoRecordImportVo;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.bean.ImportsUserInfo;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.*;

/**
 * 自动备案导入(导入平台备案)
 */
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_GOODS_AUTO_RECORD", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/%E5%B9%B3%E5%8F%B0%E5%A4%87%E6%A1%88%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFv1.2.xlsx",
        groups = {@ParkImportsHandler.Group(name = "导入模板", classes = GoodsAutoRecordImportVo.class),})
public class GoodsAutoRecordImportHandler extends ImportsBaseHandlerPlus {


    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        List<GoodsAutoRecordImportVo> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData(1, 2);
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("导入模板")) {
                list = group.getDataList(GoodsAutoRecordImportVo.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));


        if (CollUtil.isEmpty(list)) {
            log.error("GoodsAutoRecordImportHandler-导入未获取到数据");
            return;
        }
        int index = 1;
        ImportsUserInfo userInfo = this.getTaskInfo().getUserInfo();
        log.info("GoodsAutoRecordImportHandler - operator={}", userInfo);
        Set<String> outGoodsIdSet = new HashSet<>();
        for (GoodsAutoRecordImportVo vo : list) {
            if (StringUtil.isNotBlank(vo.getOutGoodsId()) && outGoodsIdSet.contains(vo.getOutGoodsId())) {
                this.callbackData(false, index++, "外部货品ID重复", vo);
                continue;
            } else {
                outGoodsIdSet.add(vo.getOutGoodsId());
            }
            if (StrUtil.isNotBlank(vo.getGoodsName())) {
                vo.setGoodsName(StringUtils.trimWhitespace(vo.getGoodsName()));
            }
            ImportResultResVo res = goodsRecordService.importAutoRecord(vo, userInfo.getUserId());
            log.info("自动备案导入,导入后的返回结果数据{}", JSON.toJSONString(res));
            this.callbackData(res.getFlag(), index++, res.getReason(), vo);
        }
    }
}
