package com.danding.cds.web.handler;

import cn.hutool.core.collection.CollUtil;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.common.utils.ConvertUtil;
import com.danding.cds.download.api.vo.ProductStockExcelVO;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.v2.bean.dto.ProductStockImportDTO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Create 2021/9/7  10:31
 * @Describe
 **/
// TODO: 2022/7/23 excel模板没有替换!!!
@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_PRODUCT_STOCK", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E7%BB%9F%E4%B8%80%E6%96%99%E5%8F%B7%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BFV1.0.xlsx",
        groups = {@ParkImportsHandler.Group(name = "统一料号商品列表导入", classes = ProductStockExcelVO.class)})
public class ProductStockImportsHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        GoodsRecordService goodsRecordService = this.getBean(GoodsRecordService.class);
        List<ProductStockExcelVO> list = null;
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("统一料号商品列表导入")) {
                list = group.getDataList(ProductStockExcelVO.class);
            }
        }
        log.info("ProductStockImportsHandler 读取到的excel对象数据{}", list);
        if (CollUtil.isEmpty(list)) {
            log.info("ProductStockImportsHandler-导入未获取到数据");
            return;
        }
        int index = 1;
        for (ProductStockExcelVO excelVO : list) {
            ProductStockImportDTO productStockImportDTO = ConvertUtil.beanConvert(excelVO, ProductStockImportDTO.class);
            ImportResultResVo resultResVo = goodsRecordService.importProductStock(productStockImportDTO);
            log.info("海关备案料号：{}，导入后的返回结果数据{}", productStockImportDTO.getProductId(), resultResVo.toString());
            this.callbackData(resultResVo.getFlag(), index++, resultResVo.getReason(), excelVO);
        }
    }
}
