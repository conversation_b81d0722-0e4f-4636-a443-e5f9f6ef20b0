package com.danding.cds.web.refund;

import cn.afterturn.easypoi.excel.ExcelExportUtil;
import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import cn.afterturn.easypoi.excel.entity.TemplateExportParams;
import cn.afterturn.easypoi.excel.entity.enmus.ExcelType;
import cn.afterturn.easypoi.excel.entity.result.ExcelImportResult;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.c.api.bean.dto.CalloffRefundGoodsInfoDTO;
import com.danding.cds.c.api.rpc.*;
import com.danding.cds.checklist.api.service.ChecklistService;
import com.danding.cds.common.config.OrderCCallConfig;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.common.utils.FileUtil;
import com.danding.cds.company.api.dto.CompanyDTO;
import com.danding.cds.company.api.service.CompanyService;
import com.danding.cds.customs.country.api.service.CustomsCountryService;
import com.danding.cds.customs.district.api.enums.CustomsDistrictEnum;
import com.danding.cds.customs.inventory.api.dto.*;
import com.danding.cds.customs.inventory.api.enums.CustomsActionStatus;
import com.danding.cds.customs.inventory.api.enums.InventoryCalloffStatusEnum;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCalloffService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryCancelService;
import com.danding.cds.customs.inventory.api.service.CustomsInventoryService;
import com.danding.cds.customs.refund.api.dto.*;
import com.danding.cds.customs.refund.api.enums.CustomsRefundOrderStatus;
import com.danding.cds.customs.refund.api.enums.RefundOrderAction;
import com.danding.cds.customs.refund.api.enums.RefundOrderEnum;
import com.danding.cds.customs.refund.api.enums.RefundPartFlagEnum;
import com.danding.cds.customs.refund.api.service.RefundOrderService;
import com.danding.cds.customs.uom.api.dto.CustomsUomDTO;
import com.danding.cds.customs.uom.api.service.CustomsUomService;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.download.api.vo.CepInvoInfoExportVO;
import com.danding.cds.download.api.vo.EndorsementExcelVO;
import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.invenorder.api.enums.InventoryOrderEnum;
import com.danding.cds.item.api.dto.CustomsBookItemDTO;
import com.danding.cds.item.api.dto.GoodsRecordDTO;
import com.danding.cds.item.api.service.CustomsBookItemService;
import com.danding.cds.item.api.service.GoodsRecordService;
import com.danding.cds.order.api.dto.CustomsReceive;
import com.danding.cds.order.api.dto.OrderDTO;
import com.danding.cds.order.api.service.OrderService;
import com.danding.cds.sequence.api.service.SequenceService;
import com.danding.cds.v2.enums.InventoryCalloffOrderTagEnums;
import com.danding.cds.web.refund.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.Response;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.logistics.cache.common.config.RedissLockUtil;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Table;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.Workbook;
import org.joda.time.DateTime;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.propertyeditors.CustomDateEditor;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.WebDataBinder;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.context.request.WebRequest;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

@Api(tags = "退货订单管理")
@RestController
@RequestMapping("/refund")
@Slf4j
public class RefundOrderInfoController {

    private static final String CREATE_REFUND_ORDER_LOCK_PREFIX = "ccs:lock:createRefundOrder:";

    @Resource
    private OrderCCallConfig orderCCallConfiguration;
    @DubboReference(retries = 0)
    private RefundOrderService refundOrderService;
    @DubboReference(retries = 0)
    private RefundOrderRpc refundOrderRpc;
    @DubboReference(retries = 0)
    private OrderService orderService;
    @DubboReference(retries = 0)
    private OrderCRpc orderCRpc;
    @DubboReference(retries = 0)
    private CustomsInventoryService customsInventoryService;
    @DubboReference(retries = 0)
    private CustomsInventoryRpc customsInventoryRpc;
    @DubboReference
    private ExpressService expressService;
    @DubboReference
    private CompanyService companyService;
    @DubboReference
    private CustomsCountryService customsCountryService;
    @DubboReference
    private CustomsBookItemService customsBookItemService;
    @DubboReference
    private ChecklistService checklistService;
    @DubboReference
    private ExportOrderService exportOrderService;
    @DubboReference
    private GoodsRecordService goodsRecordService;
    @DubboReference
    private SequenceService sequenceService;

    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private CustomsUomService customsUomService;

    @DubboReference
    private CustomsInventoryCancelService customsInventoryCancelService;
    @DubboReference
    private CustomsInventoryCancelRpc customsInventoryCancelRpc;
    @DubboReference
    private CustomsInventoryCalloffService customsInventoryCalloffService;
    @DubboReference
    private CustomsInventoryCalloffCRpc customsInventoryCalloffCRpc;

    @InitBinder
    public void initBinder(WebDataBinder binder, WebRequest request) {
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        binder.registerCustomEditor(java.util.Date.class, new CustomDateEditor(dateFormat, true));// CustomDateEditor为自定义日期编辑器
    }

    @ApiOperation(value = "退货订单分页查询", response = RefundOrderInfoVO.class)
    @GetMapping("/paging")
    public ListVO<RefundOrderInfoVO> paging(RefundOrderInfoSearch search) {
        ListVO<RefundOrderInfoDto> paging;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            paging = refundOrderRpc.paging(search);
        } else {
            paging = refundOrderService.paging(search);
        }
        ListVO<RefundOrderInfoVO> result = new ListVO<>();
        result.setPage(paging.getPage());

        List<RefundOrderInfoDto> pagingDataList = paging.getDataList();
        List<String> expressCodes = pagingDataList.stream()
                .map(RefundOrderInfoDto::getRefundExpressCode)
                .filter(refundExpressCode -> !StringUtils.isEmpty(refundExpressCode))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> expressMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(expressCodes)) {
            List<CompanyDTO> expressCodeList = companyService.findByUnifiedCrossBroderCodes(expressCodes);
            expressMap = expressCodeList.stream().collect(Collectors.toMap(CompanyDTO::getUnifiedCrossBroderCode, CompanyDTO::getName, (k1, k2) -> k1));
        }

        List<String> logisticsCodes = pagingDataList.stream()
                .map(RefundOrderInfoDto::getLogisticsCode)
                .filter(logisticsCode -> !StringUtils.isEmpty(logisticsCode))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> logisticsMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(logisticsCodes)) {
            List<CompanyDTO> logisticsCodeList = companyService.findByUnifiedCrossBroderCodes(logisticsCodes);
            logisticsMap = logisticsCodeList.stream().collect(Collectors.toMap(CompanyDTO::getUnifiedCrossBroderCode, CompanyDTO::getName, (k1, k2) -> k1));
        }

        List<String> agentCodes = pagingDataList.stream()
                .map(RefundOrderInfoDto::getAgentCode)
                .filter(agentCode -> !StringUtils.isEmpty(agentCode))
                .distinct()
                .collect(Collectors.toList());
        Map<String, String> agentMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(agentCodes)) {
            List<CompanyDTO> agentCodeList = companyService.findByUnifiedCrossBroderCodes(agentCodes);
            agentMap = agentCodeList.stream().collect(Collectors.toMap(CompanyDTO::getUnifiedCrossBroderCode, CompanyDTO::getName, (k1, k2) -> k1));
        }

        List<Long> areaCompanyIds = pagingDataList.stream()
                .map(RefundOrderInfoDto::getAreaCompanyId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        Map<Long, String> areaCompanyMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(areaCompanyIds)) {
            List<CompanyDTO> areaCompanyList = companyService.findById(areaCompanyIds);
            areaCompanyMap = areaCompanyList.stream().collect(Collectors.toMap(CompanyDTO::getId, CompanyDTO::getName, (k1, k2) -> k1));
        }

        Map<String, String> finalExpressMap = expressMap;
        Map<String, String> finalLogisticsMap = logisticsMap;
        Map<String, String> finalAgentMap = agentMap;
        Map<Long, String> finalAreaCompanyMap = areaCompanyMap;
        List<RefundOrderInfoVO> dataList = pagingDataList.stream().map((RefundOrderInfoDto item) -> {
            RefundOrderInfoVO vo = new RefundOrderInfoVO();
            BeanUtils.copyProperties(item, vo);
            if (!org.apache.commons.lang3.StringUtils.isEmpty(vo.getRefundExpressCode())) {
                vo.setRefundExpressName(finalExpressMap.getOrDefault(vo.getRefundExpressCode(), ""));
            }
            if (!org.apache.commons.lang3.StringUtils.isEmpty(vo.getLogisticsCode())) {
                vo.setLogisticsName(finalLogisticsMap.getOrDefault(vo.getLogisticsCode(), ""));
            } else {
                vo.setLogisticsName("");
            }
            if (Objects.nonNull(item.getAgentCode())) {
                vo.setAgentName(finalAgentMap.getOrDefault(item.getAgentCode(), ""));
            }
            String customStatus = vo.getRefundCustomStatus();
            if (!org.apache.commons.lang3.StringUtils.isEmpty(customStatus)) {
                CustomsRefundOrderStatus statusEnum = CustomsRefundOrderStatus.getEnum(customStatus);
                if (statusEnum != null)
                    vo.setRefundCustomStatus(statusEnum.getDesc());
            }
            // 分页上看 已经弃用了
//            if (!StringUtils.isEmpty(item.getRefListBillSn())) {
//                CustomsInventoryDTO customsInventoryDTO;
//                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
//                    customsInventoryDTO = customsInventoryRpc.findBySnSection(item.getRefListBillSn());
//                } else {
//                    customsInventoryDTO = customsInventoryService.findBySnSection(item.getRefListBillSn());
//                }
//                if (customsInventoryDTO != null) {
//                    vo.setInvenDeclareTime(customsInventoryDTO.getLastDeclareTime());
//                    vo.setInvenCallBackTime(customsInventoryDTO.getLastCustomsTime());
//                    String status = "0";
//                    if (!org.apache.commons.lang3.StringUtils.isEmpty(customsInventoryDTO.getCustomsStatus()))
//                        status = customsInventoryDTO.getCustomsStatus();
//                    vo.setInvenCustomStatus(CustomsStat.getEnum(status).getDesc());
//                }
//            }
            RefundOrderEnum.STATUS_ENUM refundStatus = RefundOrderEnum.STATUS_ENUM.getEnum(item.getRefundStatus());
            if (Objects.nonNull(refundStatus)) {
                vo.setRefundStatusDesc(refundStatus.getDesc());
            }
            if (StringUtil.isNotBlank(item.getRefundCheckStatus())) {
                RefundOrderEnum.CHECK_STATUS_ENUM checkStatusEnum = RefundOrderEnum.CHECK_STATUS_ENUM.getEnum(item.getRefundCheckStatus());
                vo.setRefundCheckStatusDesc(Objects.nonNull(checkStatusEnum) ? checkStatusEnum.getDesc() : "");
            }
            if (StringUtil.isNotBlank(item.getCustomsStatus())) {
                vo.setCustomsStatusDesc(InventoryOrderEnum.getEnum(item.getCustomsStatus()).getDesc());
            }
            if (Objects.nonNull(item.getAreaCompanyId())) {
                vo.setAreaCompanyName(finalAreaCompanyMap.getOrDefault(item.getAreaCompanyId(), ""));
            }
            vo.setOrderId(String.valueOf(item.getRefDeclareId()));
            if (Objects.nonNull(item.getRefundPartFlag())) {
                RefundPartFlagEnum refundPartFlagEnum = RefundPartFlagEnum.getEnum(item.getRefundPartFlag());
                if (Objects.nonNull(refundPartFlagEnum)) {
                    vo.setRefundPartFlagDesc(refundPartFlagEnum.getDesc());
                }
            }
            if (!StringUtils.isEmpty(item.getCustomsCode())) {
                CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(item.getCustomsCode());
                if (Objects.nonNull(customsDistrictEnum)) {
                    vo.setRefundCustomCodeDesc(customsDistrictEnum.getDesc());
                }
            }
            if (Objects.nonNull(item.getRefundExceptionFlag())) {
                vo.setRefundExceptionFlagDesc(Objects.equals(item.getRefundExceptionFlag(), 1) ? "是" : "否");
            }
            vo.setRefundCrossCustomsFlagDesc(Objects.equals(vo.getRefundCrossCustomsFlag(), 1) ? "是" : "否");
            return vo;
        }).collect(Collectors.toList());
        result.setDataList(dataList);
        return result;
    }

    @ApiOperation(value = "查看退货订单", response = RefundOrderInfoVO.class)
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", required = true, dataType = "Long", value = "查询ID", paramType = "query")
    })
    @GetMapping("/viewRefundOrder")
    public RefundOrderInfoVO viewRefundOrder(@RequestParam(required = true) Long id) {
        RefundOrderInfoDto refundOrderInfoDto;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            refundOrderInfoDto = refundOrderRpc.findBy(id);
        } else {
            refundOrderInfoDto = refundOrderService.findBy(id);
        }
        RefundOrderInfoVO vo = new RefundOrderInfoVO();
        BeanUtils.copyProperties(refundOrderInfoDto, vo);
        return vo;
    }


    public RefundResponseReport applyRefundOrder(String sns, String reason) {
        List<String> snList = StrUtil.split(sns, ',');
        List<String> lockSnList = new ArrayList<>();
        List<RefundResponseInfoVo> successRecordList = new ArrayList<>(), failRecordList = new ArrayList<>();

        try {
            List<OrderDTO> orderList = orderCRpc.findBySnSection(snList);
            this.checkCreateRefundInProgress(orderList, failRecordList, lockSnList, snList);
            // 若全部失败
            if (CollectionUtils.isEmpty(snList)) {
                RefundResponseReport refundCreateReport = new RefundResponseReport();
                refundCreateReport.setCode(0);
                for (int i = 0; i < failRecordList.size(); i++) {
                    failRecordList.get(i).setIdx(String.valueOf(i + 1));
                }
                refundCreateReport.setFailRecordList(failRecordList);
                refundCreateReport.setFailCount(failRecordList.size());
                refundCreateReport.setSuccessRecordList(successRecordList);
                refundCreateReport.setSuccessCount(0);
                refundCreateReport.setTotalCount(failRecordList.size());
                return refundCreateReport;
            }
            HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap = HashBasedTable.create();
            for (OrderDTO orderDTO : orderList) {
                // row column
                orderSnInvSnOrderMap.put(orderDTO.getSn(), orderDTO.getCustomsInventorySn(), orderDTO);
            }
            // inventorySn list
            List<String> customsInventorySnList = orderList.stream().map(OrderDTO::getCustomsInventorySn).collect(Collectors.toList());
            // 查询订单对应的清单
            List<CustomsInventoryDTO> customsInventoryList = customsInventoryRpc.findBySnList(customsInventorySnList);
            // 对清单结果进行校验
            Map<String, CustomsInventoryDTO> invMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getOrderSn, Function.identity(), (k1, k2) -> k1));

            this.checkCustomsInventoryList(invMap, failRecordList, orderSnInvSnOrderMap);
            List<Long> orderIdList = orderList.stream().map(OrderDTO::getId).collect(Collectors.toList());
            //目前把退货和撤单拆分校验了，之后可以考虑合到一起
            List<RefundOrderInfoDto> refundList = refundOrderRpc.findByOrderId(orderIdList);
            this.checkRefundOrderInfoIsCreate(refundList, invMap, failRecordList, orderSnInvSnOrderMap);
            List<CustomsInventoryCancelDTO> cancelList = customsInventoryCancelRpc.findByOrderId(orderIdList);
            //之前判断了"status", InventoryCancelEnum.STATUS_ENUM.CHECK_STATUS_PASS.getValue() 但是我觉得没必要
            this.checkCancelOrderIsExist(cancelList, invMap, failRecordList, orderSnInvSnOrderMap);
            //判断取消单状态
            List<CustomsInventoryCalloffDTO> customsInventoryCalloffList = customsInventoryCalloffCRpc.findListByOrderIdList(orderIdList);
            this.createCheckByCalloff(customsInventoryCalloffList, invMap, failRecordList, orderSnInvSnOrderMap);
            RefundResponseReport refundCreateReport = new RefundResponseReport();
            try {
                List<RefundOrderInfoDto> refundOrderInfoDtoList = this.buildRefundOrderInfoList(orderSnInvSnOrderMap, invMap, customsInventoryCalloffList, reason);
                refundOrderRpc.createRefundOrderInfo(refundOrderInfoDtoList, orderList, customsInventoryList);

                Map<Long, CustomsInventoryDTO> inventoryIdMap = customsInventoryList.stream().collect(Collectors.toMap(CustomsInventoryDTO::getId, Function.identity()));
                refundOrderInfoDtoList.forEach(r -> {
                    CustomsInventoryDTO customsInventoryDTO = inventoryIdMap.get(r.getRefListBillId());
                    RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                    refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                    refundCreateInfoVo.setMessage("退单创建成功");
                    refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                    refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                    successRecordList.add(refundCreateInfoVo);
                });
                refundCreateReport.setCode(0);
                for (int i = 0; i < successRecordList.size(); i++) {
                    successRecordList.get(i).setIdx(String.valueOf(i + 1));
                }
                for (int i = 0; i < failRecordList.size(); i++) {
                    failRecordList.get(i).setIdx(String.valueOf(i + 1));
                }
                refundCreateReport.setFailRecordList(failRecordList);
                refundCreateReport.setFailCount(failRecordList.size());
                refundCreateReport.setSuccessRecordList(successRecordList);
                refundCreateReport.setSuccessCount(successRecordList.size());
                refundCreateReport.setTotalCount(failRecordList.size() + successRecordList.size());
                return refundCreateReport;
            } catch (Exception ex) {
                log.info("RefundOrderInfoController applyRefundOrder 退货单创建失败 error={}", ex.getMessage(), ex);
                refundCreateReport.setCode(-1);
                refundCreateReport.setErrosMessage("提交保存出错,错误原因=创建失败");
                return refundCreateReport;
            }
        } finally {
            // 解锁
            for (String sn : lockSnList) {
                try {
                    RedissLockUtil.unlock(CREATE_REFUND_ORDER_LOCK_PREFIX + sn);
                } catch (Exception e) {
                    log.error("退货单创建 解锁失败 sn: {} error={}", sn, e.getMessage(), e);
                }
            }
        }
    }

    private List<RefundOrderInfoDto> buildRefundOrderInfoList(HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap, Map<String, CustomsInventoryDTO> invMap, List<CustomsInventoryCalloffDTO> customsInventoryCalloffList, String reason) {
        Map<CustomsInventoryDTO, OrderDTO> customsInventoryOrderDTOMap = new HashMap<>();
        for (String orderSn : orderSnInvSnOrderMap.rowKeySet()) {
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            if (customsInventoryDTO != null) {
                customsInventoryOrderDTOMap.put(customsInventoryDTO, orderSnInvSnOrderMap.get(orderSn, customsInventoryDTO.getSn()));
            }
        }
        Map<String, CustomsInventoryCalloffDTO> calloffDTOMap = customsInventoryCalloffList.stream().collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderSn, Function.identity(), (v1, v2) -> v1));
        return refundOrderRpc.buildDTOList(customsInventoryOrderDTOMap, calloffDTOMap, reason);
    }


    /**
     * 检查退货单对应清单的状态
     *
     * @param invMap
     * @param failRecordList
     * @param orderSnInvSnOrderMap
     * @return
     */
    private void checkCustomsInventoryList(Map<String, CustomsInventoryDTO> invMap, List<RefundResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String sn = iterator.next();
            CustomsInventoryDTO customsInventoryDTO = invMap.get(sn);
            if (customsInventoryDTO == null) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo("UNKNOW");
                refundCreateInfoVo.setInveNo("-");
//            refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("清单为空，参数错误");
                failRecordList.add(refundCreateInfoVo);
                iterator.remove();
            }
            if (!CustomsActionStatus.DEC_SUCCESS.getValue().equals(customsInventoryDTO.getStatus()) ||
                    customsInventoryDTO.getExitRegionStatus() != 1) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
//            refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("对应的清单状态不满足退单");
                refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                failRecordList.add(refundCreateInfoVo);
                iterator.remove();
            }
        }
    }

    private void checkRefundOrderInfoIsCreate(List<RefundOrderInfoDto> refundList, Map<String, CustomsInventoryDTO> invMap, List<RefundResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Map<String, RefundOrderInfoDto> refundMap = refundList.stream().collect(Collectors.toMap(RefundOrderInfoDto::getRefListBillSn, Function.identity(), (k1, k2) -> k2));
        invMap.values().forEach(customsInventoryDTO -> {
            if (refundMap.containsKey(customsInventoryDTO.getSn())) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                refundCreateInfoVo.setMessage("发起退单已创建");
                refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                failRecordList.add(refundCreateInfoVo);
                orderSnInvSnOrderMap.remove(customsInventoryDTO.getOrderSn(), customsInventoryDTO.getSn());
            }
        });
    }

    private void checkCancelOrderIsExist(List<CustomsInventoryCancelDTO> cancelList, Map<String, CustomsInventoryDTO> invMap, List<RefundResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Map<String, CustomsInventoryCancelDTO> cancelMap = cancelList.stream().collect(Collectors.toMap(CustomsInventoryCancelDTO::getRefOrderSn, Function.identity(), (k1, k2) -> k2));
        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String orderSn = iterator.next();
            CustomsInventoryCancelDTO customsInventoryCancelDTO = cancelMap.get(orderSn);
            if (Objects.nonNull(customsInventoryCancelDTO)) {
                CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                refundCreateInfoVo.setMessage("发起退单已存在撤单");
                refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                failRecordList.add(refundCreateInfoVo);
                iterator.remove();
            }
        }
    }

    private void createCheckByCalloff(List<CustomsInventoryCalloffDTO> customsInventoryCalloffList, Map<String, CustomsInventoryDTO> invMap, List<RefundResponseInfoVo> failRecordList, HashBasedTable<String, String, OrderDTO> orderSnInvSnOrderMap) {
        Map<String, CustomsInventoryCalloffDTO> calloffDTOMap = customsInventoryCalloffList.stream().collect(Collectors.toMap(CustomsInventoryCalloffDTO::getOrderSn, Function.identity(), (v1, v2) -> v1));
        Iterator<String> iterator = orderSnInvSnOrderMap.rowKeySet().iterator();
        while (iterator.hasNext()) {
            String orderSn = iterator.next();
            CustomsInventoryCalloffDTO customsInventoryCalloffDTO = calloffDTOMap.get(orderSn);
            CustomsInventoryDTO customsInventoryDTO = invMap.get(orderSn);
            RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
            if (Objects.isNull(customsInventoryCalloffDTO)) {
                refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                refundCreateInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]取消单不存在");
                refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                failRecordList.add(refundCreateInfoVo);
                iterator.remove();
                continue;
            }
            if (InventoryCalloffStatusEnum.CALLOFF_ING.getCode().equals(customsInventoryCalloffDTO.getCalloffStatus()) ||
                    InventoryCalloffStatusEnum.CALLOFF_SUCCESS.getCode().equals(customsInventoryCalloffDTO.getCalloffStatus())) {
                refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                refundCreateInfoVo.setMessage("申报单[" + customsInventoryDTO.getDeclareOrderNo() + "]存在相关状态的取消单,不能创建撤单");
                refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                failRecordList.add(refundCreateInfoVo);
                iterator.remove();
                continue;
            }
            // 校验退货商品信息是否有误
            if (StrUtil.isNotBlank(customsInventoryCalloffDTO.getRefundGoodsInfoJson())) {
                List<CalloffRefundGoodsInfoDTO> calloffRefundGoodsInfoDTOS = JSON.parseArray(customsInventoryCalloffDTO.getRefundGoodsInfoJson(), CalloffRefundGoodsInfoDTO.class);
                if (InventoryCalloffOrderTagEnums.contains(customsInventoryCalloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.REFUND_WAREHOUSE)) {
                    for (CalloffRefundGoodsInfoDTO calloffRefundGoodsInfoDTO : calloffRefundGoodsInfoDTOS) {
                        if (StrUtil.isNotBlank(calloffRefundGoodsInfoDTO.getErrorMsg())) {
                            refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                            refundCreateInfoVo.setMessage("申报单号：" + customsInventoryCalloffDTO.getDeclareOrderNo() + "，" + calloffRefundGoodsInfoDTO.getErrorMsg());
                            refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                            refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                            failRecordList.add(refundCreateInfoVo);
                            iterator.remove();
                            break;
                        }
                    }
                }
                if (InventoryCalloffOrderTagEnums.contains(customsInventoryCalloffDTO.getOrderTag(), InventoryCalloffOrderTagEnums.JD_REFUND)) {
                    for (CalloffRefundGoodsInfoDTO calloffRefundGoodsInfoDTO : calloffRefundGoodsInfoDTOS) {
                        if (StrUtil.isNotBlank(calloffRefundGoodsInfoDTO.getErrorMsg())) {
                            refundCreateInfoVo.setDeclareNo(customsInventoryDTO.getDeclareOrderNo());
                            refundCreateInfoVo.setMessage(calloffRefundGoodsInfoDTO.getErrorMsg());
                            refundCreateInfoVo.setInveNo(customsInventoryDTO.getInventoryNo());
                            refundCreateInfoVo.setLogisticsNo(customsInventoryDTO.getLogisticsNo());
                            failRecordList.add(refundCreateInfoVo);
                            iterator.remove();
                            break;
                        }
                    }
                }
            }
        }
    }


    @GetMapping("/declareRefundOrder")
    @ApiOperation(value = "申请退货")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "declareIds", required = true, dataType = "Long", value = "退货单ID", paramType = "query")
    })
    public void declareRefundOrder(List<Long> idList) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            refundOrderRpc.declareRefundOrder(idList);
        } else {
            refundOrderService.declareRefundOrder(idList);
        }
    }

    @PostMapping("/retryTest")
    @ApiOperation(value = "测试调试")
    public Response<String> retryTest(@RequestParam(required = true) String ids) {
        System.out.println("ids  === " + ids);
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            refundOrderRpc.testDeclare(NumberUtils.createLong(ids));
        } else {
            refundOrderService.testDeclare(NumberUtils.createLong(ids));
        }
        return new Response<>("重置成功");
    }

    @PostMapping("/mockReceive")
    @ApiOperation(value = "模拟接受回执")
    public Response<String> mockReceive(@RequestBody CustomsReceive receive) {
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return new Response<>(refundOrderRpc.receive(receive));
        } else {
            return new Response<>(refundOrderService.receive(receive));
        }
    }

    @ApiOperation(value = "导出核注")
    @GetMapping("/exportEndorsement")
    public void exportEndorsement(RefundOrderInfoSearch search, HttpServletResponse response) {
        if (false) {
            try {
                downloadProcessService.submitDownloadProcess(1L,
                        search, ReportType.CUSTOMS_ENDORSEMENT_REFUND_FOR_EXCEL);
            } catch (ServiceException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            return;
        }
        java.util.List<RefundOrderInfoDto> list;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            list = refundOrderRpc.querylist(search);
        } else {
            list = refundOrderService.querylist(search);
        }
        java.util.List<EndorsementExcelVO> voList = new java.util.ArrayList<>();
        java.util.Set<CepInvoInfoExportVO> cepInvoInfoExportVOList = new java.util.HashSet<CepInvoInfoExportVO>();
        if (!CollectionUtils.isEmpty(list)) {
            for (RefundOrderInfoDto __refundOrderInfoDto : list) {
                OrderDTO orderDTO;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    orderDTO = orderCRpc.findByIdFull(__refundOrderInfoDto.getRefDeclareId());
                } else {
                    orderDTO = orderService.findByIdFull(__refundOrderInfoDto.getRefDeclareId());
                }
                CustomsInventoryDTO customsInventoryDTO;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    customsInventoryDTO = customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                } else {
                    customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                }
                if (customsInventoryDTO == null || customsInventoryDTO.getExpressId() == null) continue;
                ExpressDTO expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
                if (expressDTO == null) continue;
                ExportItemDTO exportItemDTO = exportOrderService.findItemByCustomInventory(customsInventoryDTO.getSn());
                if (exportItemDTO == null || exportItemDTO.getEndorsementOrderId() == null) {
                    continue;
                }
                List<ExportItemDTO> itemDTOList = exportOrderService.listItemByEndorsementId(exportItemDTO.getEndorsementOrderId());
                Map<Long, BigDecimal> priceMap = new HashMap<>();
                Map<Long, Integer> countMap = new HashMap<>();
                for (ExportItemDTO itemDTO : itemDTOList) {
                    CepInvoInfoExportVO cepInvoInfoExportVO = new CepInvoInfoExportVO();
                    cepInvoInfoExportVO.setCepInvoNo(customsInventoryDTO.getInventoryNo());
                    cepInvoInfoExportVOList.add(cepInvoInfoExportVO);
                    List<CustomsInventoryItemDTO> itemDTOS;
                    if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                        itemDTOS = customsInventoryRpc.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                    } else {
                        itemDTOS = customsInventoryService.listItemByIdSection(customsInventoryDTO.getId(), customsInventoryDTO.getCreateTime());
                    }
                    for (CustomsInventoryItemDTO dto : itemDTOS) {
                        priceMap.put(dto.getBookItemId(), dto.getUnitPrice());
                        Integer oldCount = countMap.get(dto.getBookItemId()) == null ? 0 : countMap.get(dto.getBookItemId());
                        countMap.put(dto.getBookItemId(), oldCount + dto.getCount());
                    }
                }
                for (Long proId : countMap.keySet()) {
                    EndorsementExcelVO excelVO = new EndorsementExcelVO();
                    excelVO.setUnitPrice(priceMap.get(proId));
                    excelVO.setTotalCount(countMap.get(proId));
                    excelVO.setTotalAmount(excelVO.getUnitPrice().multiply(new BigDecimal(excelVO.getTotalCount())));
                    CustomsBookItemDTO bookItemDTO = customsBookItemService.findById(proId);
                    GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(bookItemDTO.getCustomsBookId(), bookItemDTO.getProductId());
                    if (goodsRecordDTO != null) {
                        excelVO.setGrossWeight(goodsRecordDTO.getGrossWeight());
                        excelVO.setNetWeight(goodsRecordDTO.getNetWeight());
                        excelVO.setFirstUnitAmount(goodsRecordDTO.getFirstUnitAmount());
                        excelVO.setSecondUnitAmount(goodsRecordDTO.getSecondUnitAmount());
                    } else {
                        excelVO.setGrossWeight(new BigDecimal(0));
                        excelVO.setNetWeight(new BigDecimal(0));
                        excelVO.setFirstUnitAmount(new BigDecimal(0));
                        excelVO.setSecondUnitAmount(new BigDecimal(0));
                    }
                    excelVO.setOriginCountry(bookItemDTO.getOriginCountry());
                    excelVO.setFirstUnit(bookItemDTO.getFirstUnit());
                    excelVO.setSecondUnit(bookItemDTO.getSecondUnit());
                    excelVO.setGoodsUnit(bookItemDTO.getGoodsUnit());
                    excelVO.setCurrCode(bookItemDTO.getCurrCode());
                    excelVO.setGoodsModel(bookItemDTO.getGoodsModel());
                    excelVO.setProductId(bookItemDTO.getProductId());
                    excelVO.setGoodsSeqNo(bookItemDTO.getGoodsSeqNo());
                    excelVO.setHsCode(bookItemDTO.getHsCode());
                    excelVO.setGoodsName(bookItemDTO.getGoodsName());
                    voList.add(excelVO);
                }
            }
        }
        ExportParams exportParams = new ExportParams();
        exportParams.setSheetName("表体（成品）");
        exportParams.setType(ExcelType.XSSF);
        java.util.HashMap<String, Object> map1 = new java.util.HashMap<>();
        map1.put("title", exportParams);
        map1.put("entity", EndorsementExcelVO.class);
        map1.put("data", voList);
        ExportParams exportParams2 = new ExportParams();
        exportParams2.setSheetName("保税电商清单");
        exportParams2.setType(ExcelType.XSSF);
        java.util.HashMap<String, Object> map2 = new java.util.HashMap<>();
        map2.put("title", exportParams2);
        map2.put("entity", CepInvoInfoExportVO.class);
        map2.put("data", cepInvoInfoExportVOList);
        List<Map<String, Object>> k_list = new java.util.ArrayList<Map<String, Object>>();
        k_list.add(map1);
        k_list.add(map2);
        Workbook workbook = ExcelExportUtil.exportExcel(k_list, ExcelType.XSSF);
        try {
            response.setHeader("Content-Disposition", "attachment;filename=" + new String("核注导出.xlsx".getBytes(), "iso8859-1"));
            response.setContentType("application/vnd.ms-excel;charset=UTF-8");
            workbook.write(response.getOutputStream());
            response.getOutputStream().flush();
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
        }
    }

    @ApiOperation(value = "退货单导出")
    @GetMapping("/export")
    public void export(RefundOrderInfoSearch search, HttpServletResponse response) {
        if (false) {
            try {
                downloadProcessService.submitDownloadProcess(1L,
                        search, ReportType.CUSTOMS_INVENTORY_REFUND_FOR_EXCEL);
            } catch (ServiceException e) {
                log.warn("处理异常：{}", e.getMessage(), e);
            }
            return;
        }

        java.util.List<RefundOrderInfoDto> list = new java.util.ArrayList<RefundOrderInfoDto>();
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            if (!StringUtils.isEmpty(search.getIds())) {
                List<String> idList = Lists.newArrayList(search.getIds().split(","));
                for (String idStr : idList) {
                    Long id = Long.valueOf(idStr);
                    RefundOrderInfoDto infoDto = refundOrderRpc.findBy(id);
                    list.add(infoDto);
                }
            } else {
                list = refundOrderRpc.querylist(search);
            }
        } else {
            if (!StringUtils.isEmpty(search.getIds())) {
                List<String> idList = Lists.newArrayList(search.getIds().split(","));
                for (String idStr : idList) {
                    Long id = Long.valueOf(idStr);
                    RefundOrderInfoDto infoDto = refundOrderService.findBy(id);
                    list.add(infoDto);
                }
            } else {
                list = refundOrderService.querylist(search);
            }
        }
        List<String> filePaths = new ArrayList<>();
        //生成的ZIP文件名为Demo.zip
        String tmpFileName = "退货单导出" + DateTime.now().toString("yyMMddHHmm") + ".zip";
        // zip文件路径
        String strZipPath = DownLoadUtil.FilePath + tmpFileName;
        filePaths.add(strZipPath);
        try {
            File targetFile = new File(DownLoadUtil.FilePath);
            if (!targetFile.exists()) {
                targetFile.mkdirs();
            }
            //创建zip输出流
            ZipOutputStream out = new ZipOutputStream(new FileOutputStream(strZipPath));
            //声明文件集合用于存放excel文件
            List<File> fileList = new ArrayList<File>();
            Table<Long, String, List<Map<String, Object>>> table = HashBasedTable.create();
            int no = 1;
            for (RefundOrderInfoDto infoDto : list) {
                Long orderId = infoDto.getRefDeclareId();
                OrderDTO orderDTO;
                CustomsInventoryDTO customsInventoryDTO;
                List<CustomsInventoryItemDTO> listInventoryItemDTO;
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    orderDTO = orderCRpc.findByIdFull(orderId);
                    customsInventoryDTO = customsInventoryRpc.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                    if (customsInventoryDTO == null || StringUtils.isEmpty(customsInventoryDTO.getCustoms()))
                        continue;
                    listInventoryItemDTO = customsInventoryRpc.listItemByIdSection(infoDto.getRefListBillId(), customsInventoryDTO.getCreateTime());
                } else {
                    orderDTO = orderService.findByIdFull(orderId);
                    customsInventoryDTO = customsInventoryService.findByOrder(orderDTO.getId(), orderDTO.getCustomsInventorySn());
                    if (customsInventoryDTO == null || StringUtils.isEmpty(customsInventoryDTO.getCustoms()))
                        continue;
                    listInventoryItemDTO = customsInventoryService.listItemByIdSection(infoDto.getRefListBillId(), customsInventoryDTO.getCreateTime());
                }


                if (CollectionUtils.isEmpty(listInventoryItemDTO)) continue;
                ExpressDTO expressDTO = null;
                if (customsInventoryDTO.getExpressId() != null) {
                    expressDTO = expressService.findById(customsInventoryDTO.getExpressId());
                }
                List<Map<String, Object>> exportVOList = table.get(customsInventoryDTO.getEbcId(), customsInventoryDTO.getCustoms());
                if (exportVOList == null) {
                    exportVOList = new ArrayList<Map<String, Object>>();
                    table.put(customsInventoryDTO.getEbcId(), customsInventoryDTO.getCustoms(), exportVOList);
                }
                for (CustomsInventoryItemDTO item : listInventoryItemDTO) {
                    CustomsInventoryItemExtra itemExtra = JSON.parseObject(item.getExtraJson(), CustomsInventoryItemExtra.class);
                    Map<String, Object> emp1 = Maps.newHashMap();
                    emp1.put("no", itemExtra.getGoodsSeqNo());
                    emp1.put("ebpName", infoDto.getEbpName());
                    emp1.put("invo", infoDto.getInvtNo());
                    emp1.put("orderNo", infoDto.getRefDeclareNo());
                    emp1.put("mailNo", infoDto.getRefundMailNo());
                    emp1.put("srcMailNo", customsInventoryDTO.getLogisticsNo());
                    String expressName = "";
                    if (expressDTO != null) {
                        expressName = expressDTO.getName();
                    }
                    emp1.put("expressName", expressName);
                    emp1.put("goodsNo", itemExtra.getProductId());
                    emp1.put("goodsName", item.getItemName());
                    emp1.put("qfy", String.valueOf(item.getCount()));
                    emp1.put("goodQfy", String.valueOf(item.getCount()));
                    String unit = "";
                    if (item.getBookItemId() != null) {
                        CustomsBookItemDTO bookItemDTO = customsBookItemService.findById(item.getBookItemId());
                        if (bookItemDTO != null) {
                            GoodsRecordDTO goodsRecordDTO = goodsRecordService.findByBookIdAndProId(bookItemDTO.getCustomsBookId(), bookItemDTO.getProductId());
                            if (goodsRecordDTO != null) {
                                CustomsUomDTO customsUomDTO = customsUomService.findByCode(goodsRecordDTO.getFirstUnit());
                                unit = customsUomDTO.getName();
                            }
                        }
                    }
                    emp1.put("unit", unit);
                    exportVOList.add(emp1);
                    no++;
                }
            }
            if (table.isEmpty()) {
                File tempTemplateFile = FileUtil.loadTempConfig("refund-temp", ".xlsx");
                TemplateExportParams params = new TemplateExportParams(tempTemplateFile.getAbsolutePath(), 0);
                try {
                    String filename = DownLoadUtil.FilePath + "退货明细.xlsx";
                    fileList.add(DownLoadUtil.creatFile(filename));
                    Map<String, Object> data = new HashMap<String, Object>();
                    data.put("companyName", "");
                    data.put("currentDate", new Date());
                    data.put("customsName", "");
                    List<Map<String, Object>> exportVOList = new ArrayList<Map<String, Object>>();
                    data.put("employeeList", exportVOList);
                    Workbook workbook = ExcelExportUtil.exportExcel(params, data);
                    FileOutputStream excelOut = new FileOutputStream(filename);
                    workbook.write(excelOut);
                    excelOut.flush();
                    excelOut.close();

                } finally {
                    FileUtils.forceDelete(tempTemplateFile);
                }
            } else {
                Set<Long> rows = table.rowKeySet();
                int idxFile = 1;
                for (Long row : rows) {
                    CompanyDTO companyDTO = companyService.findUnifiedCrossInfoById(row);
                    if (companyDTO == null) continue;
                    Map<String, List<Map<String, Object>>> rowMap = table.row(row);
                    if (rowMap == null) continue;
                    java.util.Set<String> comstoms = rowMap.keySet();
                    File tempTemplateFile = FileUtil.loadTempConfig("refund-temp", ".xlsx");
                    TemplateExportParams params = new TemplateExportParams(tempTemplateFile.getAbsolutePath(), 0);
                    try {
                        for (String comstom : comstoms) {

                            CustomsDistrictEnum customsDistrictEnum = CustomsDistrictEnum.getEnum(comstom);
                            if (customsDistrictEnum == null) continue;
                            String filename = DownLoadUtil.FilePath + "退货明细-" + org.apache.commons.lang3.StringUtils.leftPad("" + idxFile, 4, "0") + ".xlsx";
                            idxFile++;
                            fileList.add(DownLoadUtil.creatFile(filename));
                            Map<String, Object> data = new HashMap<String, Object>();
                            data.put("companyName", companyDTO.getName());
                            data.put("currentDate", new Date());
                            data.put("customsName", customsDistrictEnum.getDesc());
                            List<Map<String, Object>> exportVOList = rowMap.get(comstom);
                            data.put("employeeList", exportVOList);
                            Workbook workbook = ExcelExportUtil.exportExcel(params, data);
                            FileOutputStream excelOut = new FileOutputStream(filename);
                            workbook.write(excelOut);
                            excelOut.flush();
                            excelOut.close();
                        }
                    } finally {
                        FileUtils.forceDelete(tempTemplateFile);
                    }
                }
            }
            byte[] buffer = new byte[1024];
            //将excel文件放入zip压缩包
            for (int i = 0; i < fileList.size(); i++) {
                File file = fileList.get(i);
                FileInputStream fis = new FileInputStream(file);
                out.putNextEntry(new ZipEntry(file.getName()));
                //设置压缩文件内的字符编码，不然会变成乱码
//                out.setEncoding("GBK");
                int len;
                // 读入需要下载的文件的内容，打包到zip文件
                while ((len = fis.read(buffer)) > 0) {
                    out.write(buffer, 0, len);
                }
                out.closeEntry();
                fis.close();
            }
            out.close();
            //下载zip文件
            DownLoadUtil.downFile(response, tmpFileName, filePaths);
        } catch (Exception e) {
            // 下载失败删除生成的文件
            DownLoadUtil.deleteFile(filePaths);
            log.error("文件下载出错", e);
        }

    }


    @ApiOperation(value = "批量导入预览")
    @PostMapping("/pre-import")
    public RefundResponseExcelReport preImportExcel(MultipartFile file) throws ArgsErrorException {
        ImportParams importParams = new ImportParams();
        MailOrderInfoImportReport importReport;
        ExcelImportResult<ImportExcelInfoRecord> result;
        try {
            result = ExcelImportUtil.importExcelMore(
                    file.getInputStream(),
                    ImportExcelInfoRecord.class,
                    importParams);
        } catch (Exception exception) {
            throw new ArgsErrorException("读取附件失败");
        }
        List<ImportExcelInfoRecord> successList = result.getList();
        List<MailNoInfoRecord> list = new java.util.ArrayList<MailNoInfoRecord>();
        for (ImportExcelInfoRecord input : successList) {
            MailNoInfoRecord mailNoInfoRecord = new MailNoInfoRecord();
            BeanUtils.copyProperties(input, mailNoInfoRecord);
            list.add(mailNoInfoRecord);
        }
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return refundOrderRpc.excelImport(list, false);
        } else {
            return refundOrderService.excelImport(list, false);
        }
    }

    @ApiOperation(value = "批量导入运单")
    @PostMapping("/import")
    public RefundResponseExcelReport importExcel(@RequestBody RefundOrderImportSubmit file) throws
            ArgsErrorException {
        List<MailNoInfoRecord> recordList = file.getRecordList();
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            return refundOrderRpc.excelImport(recordList, true);
        } else {
            return refundOrderService.excelImport(recordList, true);
        }
    }

    @ApiOperation(value = "取消退货订单")
    @GetMapping("/cancelRefund")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "id", required = true, dataType = "Long", value = "查询退货单ID", paramType = "query"),
            @ApiImplicitParam(name = "refundReason", required = false, dataType = "String", value = "退后原因", paramType = "query")
    })
    public RefundResponseReport cancelRefund(@RequestParam(required = true) String ids, String refundReason) {
        ids = org.apache.commons.lang3.StringUtils.strip(ids, ",");
        String _ids[] = org.apache.commons.lang3.StringUtils.split(ids, ",");
        List<RefundOrderInfoDto> list = new ArrayList<>();
        RefundResponseReport refundCreateReport = new RefundResponseReport();
        refundCreateReport.setCode(0);
        List<RefundResponseInfoVo> successList = new ArrayList<RefundResponseInfoVo>();
        List<RefundResponseInfoVo> failList = new ArrayList<RefundResponseInfoVo>();
        int i = 0;
        for (String id : _ids) {
            i++;
            RefundOrderInfoDto refundOrderInfoDto;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                refundOrderInfoDto = refundOrderRpc.findBy(NumberUtils.createLong(id));
            } else {
                refundOrderInfoDto = refundOrderService.findBy(NumberUtils.createLong(id));
            }
            if (refundOrderInfoDto == null) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo("UNKNOW");
                refundCreateInfoVo.setInveNo("-");
                refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("退单为空，参数错误");
                failList.add(refundCreateInfoVo);
                continue;
            }
            if (!RefundOrderEnum.refundOrder.contains(refundOrderInfoDto.getRefundCheckStatus(), refundOrderInfoDto.getRefundStatus(), RefundOrderAction.REFUND_CANCEL)) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(refundOrderInfoDto.getRefDeclareNo());
                refundCreateInfoVo.setInveNo(refundOrderInfoDto.getInvtNo());
                refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("所选退货单状态目前不能取消");
                failList.add(refundCreateInfoVo);
                continue;
            } else {
                refundOrderInfoDto.setReason(refundReason);
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(refundOrderInfoDto.getRefDeclareNo());
                refundCreateInfoVo.setInveNo(refundOrderInfoDto.getInvtNo());
                refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("退单取消成功");
                successList.add(refundCreateInfoVo);
            }
            list.add(refundOrderInfoDto);
        }
        if (!CollectionUtils.isEmpty(list)) {
            try {
                if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                    refundOrderRpc.cancelRefund(list);
                } else {
                    refundOrderService.cancelRefund(list);
                }
            } catch (Exception exception) {
                refundCreateReport.setCode(-1);
                refundCreateReport.setErrosMessage("提交保存失败");
                return refundCreateReport;
            }
        }
        refundCreateReport.setFailRecordList(failList);
        refundCreateReport.setSuccessRecordList(successList);
        refundCreateReport.setSuccessCount(successList.size());
        refundCreateReport.setFailCount(failList.size());
        refundCreateReport.setTotalCount(failList.size() + successList.size());
        return refundCreateReport;
    }

    @ApiOperation(value = "删除退货订单")
    @PostMapping("/deleteRefundOrder")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "declareIds", required = true, dataType = "Long", value = "删除退货单ID", paramType = "query"),
            @ApiImplicitParam(name = "refundReason", required = false, dataType = "String", value = "删除原因", paramType = "query")
    })
    public RefundResponseReport deleteRefundOrder(@RequestBody Map<String, Object> paramMap) {
        RefundResponseReport refundCreateReport = new RefundResponseReport();
        refundCreateReport.setCode(0);
        String declareIds = paramMap.get("declareIds").toString();
        declareIds = org.apache.commons.lang3.StringUtils.strip(declareIds, ",");
        if (org.apache.commons.lang3.StringUtils.isEmpty(declareIds)) {
            refundCreateReport.setCode(-1);
            refundCreateReport.setErrosMessage("请选择退单数据");
            return refundCreateReport;
        }
        List<RefundResponseInfoVo> successList = new ArrayList<RefundResponseInfoVo>();
        List<RefundResponseInfoVo> failList = new ArrayList<RefundResponseInfoVo>();
        String _declareIds[] = org.apache.commons.lang3.StringUtils.split(declareIds, ",");
        int i = 0;
        for (String _id : _declareIds) {
            i++;
            Long id = NumberUtils.createLong(_id);
            RefundOrderInfoDto refundOrderInfoDto;
            if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                refundOrderInfoDto = refundOrderRpc.findBy(id);
            } else {
                refundOrderInfoDto = refundOrderService.findBy(id);
            }
            if (refundOrderInfoDto == null) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo("UNKNOW");
                refundCreateInfoVo.setInveNo("-");
                refundCreateInfoVo.setIdx("" + i);
                refundCreateInfoVo.setMessage("退单为空，参数错误");
                failList.add(refundCreateInfoVo);
                continue;
            } else {
                if (refundOrderInfoDto.getRefundStatus().equals(RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue())
                        || (!refundOrderInfoDto.getRefundStatus().equals(RefundOrderEnum.STATUS_ENUM.STATUS_CLOSE.getValue()) && (refundOrderInfoDto.getRefundCheckStatus().equals(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_INIT.getValue())
                        || refundOrderInfoDto.getRefundCheckStatus().equals(RefundOrderEnum.CHECK_STATUS_ENUM.CHECK_STATUS_REJECT.getValue())))
                ) {
                    boolean deleteFlag;
                    if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
                        deleteFlag = refundOrderRpc.deleteRefundOrderInfo(refundOrderInfoDto);
                    } else {
                        deleteFlag = refundOrderService.deleteRefundOrderInfo(refundOrderInfoDto);
                    }
                    if (deleteFlag) {
                        RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                        refundCreateInfoVo.setDeclareNo(refundOrderInfoDto.getRefDeclareNo());
                        refundCreateInfoVo.setInveNo(refundOrderInfoDto.getInvtNo());
                        refundCreateInfoVo.setIdx("" + i);
                        refundCreateInfoVo.setMessage("退单删除成功");
                        successList.add(refundCreateInfoVo);
                    } else {
                        RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                        refundCreateInfoVo.setDeclareNo(refundOrderInfoDto.getRefDeclareNo());
                        refundCreateInfoVo.setInveNo(refundOrderInfoDto.getInvtNo());
                        refundCreateInfoVo.setIdx("" + i);
                        refundCreateInfoVo.setMessage("退单删除失败");
                        failList.add(refundCreateInfoVo);
                    }
                } else {
                    RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                    refundCreateInfoVo.setDeclareNo(refundOrderInfoDto.getRefDeclareNo());
                    refundCreateInfoVo.setInveNo(refundOrderInfoDto.getInvtNo());
                    refundCreateInfoVo.setIdx("" + i);
                    refundCreateInfoVo.setMessage("退单当前状态不满足删除状态");
                    failList.add(refundCreateInfoVo);
                }
            }
        }
        refundCreateReport.setFailRecordList(failList);
        refundCreateReport.setSuccessRecordList(successList);
        refundCreateReport.setSuccessCount(successList.size());
        refundCreateReport.setFailCount(failList.size());
        refundCreateReport.setTotalCount(failList.size() + successList.size());
        return refundCreateReport;
    }

    @ApiOperation(value = "统计-退货订单个数")
    @PostMapping("/sumRefundOrder")
    public List<RefundOrderInfoSumVO> sumRefundOrder(Long beginTimeLong, Long endTimeLong) {
        Date beginTime = null;
        Date endTime = null;
        if (beginTimeLong != null) {
            beginTime = new Date();
            beginTime.setTime(beginTimeLong);
        }
        if (endTimeLong != null) {
            endTime = new Date();
            endTime.setTime(endTimeLong);
        }
        List<Map<String, Object>> map;
        if (orderCCallConfiguration.isOrderCCall(this.getClass())) {
            map = refundOrderRpc.sumRefundOrder(beginTime, endTime);
        } else {
            map = refundOrderService.sumRefundOrder(beginTime, endTime);
        }
        List<RefundOrderInfoSumVO> refundOrderInfoSumList = new ArrayList<RefundOrderInfoSumVO>();
        for (Map<String, Object> item : map) {
            RefundOrderInfoSumVO vo = new RefundOrderInfoSumVO();
            vo.setCount(Long.valueOf(item.get("c").toString()));
            vo.setDateStr(String.valueOf(item.get("d")));
            refundOrderInfoSumList.add(vo);
        }
        return refundOrderInfoSumList;
    }

    private void checkCreateRefundInProgress
            (List<OrderDTO> orderList, List<RefundResponseInfoVo> failRecordList, List<String> lockSnList, List<String> snList) {
        for (OrderDTO orderDTO : orderList) {
            String sn = orderDTO.getSn();
            if (!RedissLockUtil.tryLock(CREATE_REFUND_ORDER_LOCK_PREFIX + sn, TimeUnit.SECONDS, 0, -1)) {
                RefundResponseInfoVo refundCreateInfoVo = new RefundResponseInfoVo();
                refundCreateInfoVo.setDeclareNo(orderDTO.getDeclareOrderNo());
                refundCreateInfoVo.setMessage("退货单生成中, 请勿重复生成");
                failRecordList.add(refundCreateInfoVo);
                snList.remove(sn);
            } else {
                log.info("checkCreateRefundInProgress 加锁成功 sn:{} threadId={}", sn, Thread.currentThread().getId());
                lockSnList.add(sn);
            }
        }
    }
}