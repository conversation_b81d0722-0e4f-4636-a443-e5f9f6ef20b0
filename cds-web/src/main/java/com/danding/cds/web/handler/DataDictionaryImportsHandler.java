package com.danding.cds.web.handler;


import cn.hutool.core.collection.CollUtil;
import com.alibaba.fastjson.JSON;
import com.danding.cds.common.model.excel.ImportResultResVo;
import com.danding.cds.customs.dictionary.api.dto.CustomsDictionaryDTO;
import com.danding.cds.customs.dictionary.api.service.CustomsDictionaryService;
import com.danding.cds.upload.api.vo.DataDictionaryImportVO;
import com.danding.core.tenant.SimpleTenantHelper;
import com.danding.park.client.core.annotation.ParkImportsHandler;
import com.danding.park.client.core.imports.bean.ImportsDataGroup;
import com.danding.park.client.core.imports.handler.ImportsBaseHandler;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@ParkImportsHandler(funcCode = "IMPORT_DATA_DICTIONARY", templateUrl = "https://cds-oss.oss-cn-hangzhou.aliyuncs.com/ccs/%E6%95%B0%E6%8D%AE%E5%AD%97%E5%85%B8%E5%AF%BC%E5%85%A5%E6%A8%A1%E6%9D%BF.xlsx",
        groups = {@ParkImportsHandler.Group(name = "Sheet1", classes = DataDictionaryImportVO.class),})
public class DataDictionaryImportsHandler extends ImportsBaseHandler {

    @Override
    public void work() {
        Map<String, Object> extendMap = this.getTaskInfo().getExtend();
        String type = (String) extendMap.get("type");
        Integer tenantId = (Integer) extendMap.get("tenantId");
        if (Objects.nonNull(tenantId)) {
            SimpleTenantHelper.setTenantId(Long.valueOf(tenantId));
        }
        CustomsDictionaryService customsDictionaryService = this.getBean(CustomsDictionaryService.class);

        List<DataDictionaryImportVO> list = null;
        // 读取数据返回多组数据，需要根据自己的设置来获取数据
        List<ImportsDataGroup> groups = this.readData();
        for (ImportsDataGroup group : groups) {
            if (group.getName().equalsIgnoreCase("Sheet1")) {
                list = group.getDataList(DataDictionaryImportVO.class);
            }
        }
        log.info("读取到的excel对象数据{}", JSON.toJSONString(list));


        if (CollUtil.isEmpty(list)) {
            log.error("DataDictionaryImportsHandler-导入未获取到数据");
            return;
        }
        int index = 1;
        for (DataDictionaryImportVO vo : list) {
            CustomsDictionaryDTO customsDictionaryDTO = new CustomsDictionaryDTO();
            customsDictionaryDTO.setCode(vo.getCode());
            customsDictionaryDTO.setName(vo.getName());
            ImportResultResVo res = customsDictionaryService.importExcel(customsDictionaryDTO, type);
            log.info("数据字典导入，类型={},导入后的返回结果数据{}", type, JSON.toJSONString(res));
            this.callbackData(res.getFlag(), index++, res.getReason(), vo);
        }
    }
}
