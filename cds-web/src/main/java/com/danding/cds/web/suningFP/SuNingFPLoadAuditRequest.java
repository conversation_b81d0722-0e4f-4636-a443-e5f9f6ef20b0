package com.danding.cds.web.suningFP;

import com.danding.cds.web.suningFP.vo.SuNingFPLoadRec;
import lombok.Data;

import javax.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(propOrder = {"loadRec"})
@XmlRootElement(name = "LoadRecList")
public class SuNingFPLoadAuditRequest {
    @XmlElements({@XmlElement(name = "loadRec", type = SuNingFPLoadRec.class)})
    private List<SuNingFPLoadRec> loadRec;
}
