package com.danding.cds.web.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.danding.cds.item.api.dto.CustomsInitDTO;
import com.danding.cds.item.api.service.CustomsInitService;
import lombok.extern.slf4j.Slf4j;

import java.util.ArrayList;
import java.util.List;

/**
 * @program: cds-center
 * @description: CustomsInitDTO监听
 * @author: 潘本乐（Belep）
 * @create: 2021-08-03 14:17
 **/
@Slf4j
public class CustomsInitDtoListener extends AnalysisEventListener<CustomsInitDTO> {

    private int BATCH_COUNT;
    List<CustomsInitDTO> list = new ArrayList();
    private CustomsInitService customsInitService;
    private String bookId;
    private Integer type;
    private String beginDate;
    private String endDate;

    public CustomsInitDtoListener(CustomsInitService customsInitService, String bookId, Integer type,String beginDate,String endDate,Integer batchCount) {
        this.customsInitService = customsInitService;
        this.bookId = bookId;
        this.type = type;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.BATCH_COUNT = batchCount == null ? 50 : batchCount;
    }

    /**
     * 这个每一条数据解析都会来调用
     *
     * @param data    one row value. Is is same as {@link AnalysisContext#readRowHolder()}
     * @param context
     */
    @Override
    public void invoke(CustomsInitDTO data, AnalysisContext context) {
        log.info("解析到一条数据:{}", JSON.toJSONString(data));
        list.add(data);
        // 达到BATCH_COUNT了，防止数据几万条数据在内存，容易OOM
        if (list.size() >= BATCH_COUNT) {
            dealData();
            // 存储完成清理 list
            list.clear();
        }
    }

    /**
     * 所有数据解析完成了 都会来调用
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        dealData();
        log.info("所有数据解析完成！");
    }

    /**
     * 处理数据
     */
    private void dealData() {
        log.info("{}条数据，开始存储数据库！", list.size());
//        customsInitService.customsBookInit(list, bookId);
        if (1==type){
            customsInitService.customsBookInit(list, bookId,beginDate,endDate);
        }
        if (2==type){
            customsInitService.customsBookInitWithoutHistory(list, bookId);
        }
        log.info("业务处理成功成功！");
    }
}