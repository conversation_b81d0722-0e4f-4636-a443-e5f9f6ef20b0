package com.danding.cds.web.invenorder.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

@Data
public class InventoryOrderViewInfoV2VO implements Serializable {

//region -----------------------------------详情顶部-----------------------------------
    /**
     * 清关单号
     */
    private String inveCustomsSn;

    /**
     * 清关单标记
     */
    private List<Integer> orderTagList;
    private List<String> orderTagDescList;

    /**
     * 出入库单号
     */
    private String inOutOrderNo;

    /**
     * 外部单号
     */
    private String upstreamNo;

    /**
     * 进出标志
     */
    private String inOrOutFlag;
    private String inOrOutFlagDesc;

    /**
     * 关联入库/出库单号
     */
    private String associatedInOutOrderNo;

    /**
     * 关联清关单号
     */
    private String associatedInveCustomsSn;

    /**
     * 子/主清关单号
     */
    private String masterOrSubOrderSn;

    /**
     * 关联中转清关单号
     */
    private String associatedTransitOrderSn;
    /**
     * 关联调入清关单号
     */
    private String associatedInOrderSn;
    /**
     * 关联调出清关单号
     */
    private String associatedOutOrderSn;

    /**
     * 是否为中转账册
     * 是否开启中转 0:否 1:是
     */
    private String transitFlagDesc;

    /**
     * 实体仓编码(终点)
     */
    private String finalEntityWarehouseCode;

    /**
     * 实体仓名称(终点)
     */
    private String finalEntityWarehouseName;

    /**
     * 货主编码(终点)
     */
    private String finalOwnerCode;
    /**
     * 货主名称(终点)
     */
    private String finalOwnerName;

    /**
     * 清关企业(终点)
     */
    private Long finalInveCompanyId;
    /**
     * 清关企业(终点)
     */
    private String finalInveCompanyName;
    /**
     * 账册ID(终点)
     */
    private Long finalBookId;
    /**
     * 账册编号(终点)
     */
    private String finalBookNo;

    /**
     * 分区结转单号
     */
    private String carryOverNo;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 清关状态
     */
    private String status;
    private String statusDesc;

    /**
     * 备注
     */
    private String remark;
//endregion -----------------------------------详情顶部-----------------------------------

//region -----------------------------------基本信息-----------------------------------

    /**
     * 调出仓库
     */
    private WarehouseInfoVO outsetEntityWarehouseInfo;

    /**
     * 调入仓库
     */
    private WarehouseInfoVO destinationEntityWarehouseInfo;

    @Data
    public static class WarehouseInfoVO implements Serializable {
        /**
         * 仓库类型
         */
        private String entityWarehouseType;

        /**
         * 实体仓名称
         */
        private String entityWarehouseName;

        /**
         * 货主名称
         */
        private String ownerName;

        /**
         * 清关企业
         */
        private String inveCompanyName;

        /**
         * 区内账册编号
         */
        private String areaBookNo;

        /**
         * 主管关区
         */
        private String masterCustoms;

    }
//endregion -----------------------------------基本信息-----------------------------------

//region -----------------------------------运输信息-----------------------------------

    /**
     * 车牌号
     */
    private String licensePlate;

    /**
     * 货主是否自备车辆
     */
    private Integer selfOwnedVehicle;

    /**
     * 车辆费用备注
     */
    private String vehicleCostRemark;

    /**
     * 托数
     */
    private Integer palletsNums;

    /**
     * 预计出区时间
     */
    private Date expectedOutAreaTime;

    /**
     * 预计到港时间
     */
    private Date expectedToPortTime;
//endregion -----------------------------------运输信息-----------------------------------

//region -----------------------------------通关信息-----------------------------------

    /**
     * 业务类型
     */
    private String inveBusinessType;
    private String inveBusinessTypeDesc;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 启运/运抵国
     */
    private String shipmentCountry;

    /**
     * 进出境关别
     */
    private String entryExitCustoms;

    /**
     * 申报表编号
     */
    private String declareFormNo;

    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     */
    private Integer twoStepFlag;

    private String twoStepFlagDesc;

    /**
     * 清关方式
     */
    private Integer declareWay;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 报关标志(一线就默认都是报关，非一线就是非报关)(1:是 2:否)
     */
    private Integer customsFlag;
    private String customsFlagDesc;

    /**
     * 是否生成报关单(1:是 2:否)
     */
    private Integer declarationFlag;
    private String declarationFlagDesc;

    @ApiModelProperty("对应报关单类型")
    private String customsEntryType;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private Integer customsType;
    private String customsTypeDesc;

    /**
     * 对应报关单申报单位名称
     */
    private String corrCusDeclareCompanyName;

    /**
     * 对应报关单申报单位ID
     */
    private Long corrCusDeclareCompanyId;
    /**
     * 对应报关单申报单位编码
     */
    private String corrCusDeclareCompanyCode;
    /**
     * 对应报关单申报单位USCC
     */
    private String corrCusDeclareCompanyUSCC;

    @ApiModelProperty("选择区间转出，区内转出的业务类型时 ->关联转入账册字段")
    private String inAccountBook;
    @ApiModelProperty("选择区间转入，区内转入的业务类型时 ->关联转出账册")
    private String outAccountBook;
    @ApiModelProperty("关联核注清单编号")
    private String associatedEndorsementNo;

    /**
     * 关联报关单境内收发货人
     */
    private Long rltCusInnerSFHRCompanyId;
    /**
     * 关联报关单境内收发货人名称
     */
    private String rltCusInnerSFHRCompanyName;
    /**
     * 关联报关单境内收发货人编码
     */
    private String rltCusInnerSFHRCompanyCode;
    /**
     * 关联报关单境内收发货人USCC
     */
    private String rltCusInnerSFHRCompanyUSCC;

    /**
     * 关联报关单消费使用单位
     */
    private Long rltCusXFDYCompanyId;
    /**
     * 关联报关单消费使用单位名称
     */
    private String rltCusXFDYCompanyName;
    /**
     * 关联报关单消费使用单位编码
     */
    private String rltCusXFDYCompanyCode;
    /**
     * 关联报关单消费使用单位USCC
     */
    private String rltCusXFDYCompanyUSCC;

    /**
     * 关联报关单申报单位
     */
    private Long rltCusDeclareCompanyId;
    /**
     * 关联报关单申报单位名称
     */
    private String rltCusDeclareCompanyName;
    /**
     * 关联报关单申报单位编码
     */
    private String rltCusDeclareCompanyCode;
    /**
     * 关联报关单申报单位USCC
     */
    private String rltCusDeclareCompanyUSCC;

    /**
     * 核注单备注
     */
    private String endorsementRemark;
//endregion -----------------------------------通关信息-----------------------------------

//region -----------------------------------其他信息-----------------------------------

    /**
     * 提单号
     */
    private String pickUpNo;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 起运港/始发机场
     */
    private String fromLocation;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;

    /**
     * 实际到港日期
     */
    private Date actualArrivalDate;

//endregion -----------------------------------其他信息-----------------------------------


    /**
     * 来源渠道
     * (0, "空"),
     * (1, "ERP"),
     * (2, "CCS"),
     * (3, "WMS"),
     * (4, "淘天"),
     */
    private Integer channel;

    /**
     * 区内账册id
     */
    private Long areaBookId;

    /**
     * 区内账册类型
     * {@link com.danding.cds.v2.bean.enums.CustomsBookTypeEnums}
     */
    private Integer areaBookType;

    /**
     * 是否开启中转 0:否 1:是
     */
    private Integer transitFlag;

    /**
     * 草单比对类型
     * 0：hs对比 1：统一料号对比
     */
    private Integer draftCompareType;

    /**
     * 是否锁定库存
     * 0：未锁定 1：锁定
     */
    private Integer lockStockFlag;

    /**
     * 清关企业
     */
    private Long inveCompanyId;
}
