package com.danding.cds.web.collaborateOrder.rpc;

import com.danding.cds.collaborateorder.api.dto.CollaborateOrderParam;
import com.danding.cds.collaborateorder.api.vo.CollaborateOrderResVO;
import com.danding.cds.out.bean.vo.req.TallyReportReqVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

public interface CollaborateOrderRpc {

    RpcResult<ListVO<CollaborateOrderResVO>> paging(CollaborateOrderParam param);

    RpcResult collaborateStatus();

    RpcResult inventoryOrderStatus();

    RpcResult inventor(TallyReportReqVO reqVO);

    RpcResult export(CollaborateOrderParam param);

    RpcResult inveBusinessTypeCount(CollaborateOrderParam param);
}
