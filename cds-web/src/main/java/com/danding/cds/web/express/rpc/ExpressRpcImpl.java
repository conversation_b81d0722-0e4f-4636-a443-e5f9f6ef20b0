package com.danding.cds.web.express.rpc;

import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.express.api.dto.ExpressConfigParam;
import com.danding.cds.express.api.dto.ExpressDTO;
import com.danding.cds.express.api.dto.ExpressSearch;
import com.danding.cds.express.api.dto.ExpressSubmit;
import com.danding.cds.express.api.enums.LogisticsDeclareSystemEnums;
import com.danding.cds.express.api.service.ExpressService;
import com.danding.cds.web.express.ExpressController;
import com.danding.cds.web.express.vo.ExpressSearchResult;
import com.danding.component.common.utils.EnumUtils;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.exceptions.rpcException.ArgsInvalidException;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;
import java.util.stream.Collectors;

@DubboService
public class ExpressRpcImpl implements ExpressRpc {

    @DubboReference
    private ExpressService expressService;

    @Autowired
    private ExpressController expressController;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express/list", desc = "快递公司下拉所有")
    public RpcResult<List<SelectOptionVO<Long>>> list() {
        List<ExpressDTO> dataList = expressService.listAll();
        List<SelectOptionVO<Long>> result = dataList.stream().map((ExpressDTO item) -> {
            return (SelectOptionVO<Long>) new SelectOptionVO(item.getId(),item.getName());
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }


    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express/listExpress", desc = "快递公司下拉")
    public RpcResult listExpress() {
        List<ExpressDTO> dataList = expressService.listEnable();
        return RpcResult.success(EnumUtils.build(dataList, "code", "name"));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express/listEnable", desc = "快递公司下拉可用")
    public RpcResult<List<SelectOptionVO<Long>>> listEnable() {
        List<ExpressDTO> dataList = expressService.listEnable();
        List<SelectOptionVO<Long>> result = dataList.stream().map((ExpressDTO item) -> {
            return (SelectOptionVO<Long>) new SelectOptionVO(item.getId(),item.getName());
        }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express/paging", desc = "快递公司分页查询")
    public RpcResult<ListVO<ExpressSearchResult>> paging(ExpressSearch search) {
        return RpcResult.success(expressController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/express/upset", desc = "快递公司编辑")
    public RpcResult<Long> upset(ExpressSubmit submit) {
        return RpcResult.success(expressController.upset(submit));
    }

    /**
     * 快递配置
     *
     * @param param
     * @return
     * @path /express/config
     */
    @Override
    @SoulClient(path = "/express/config", desc = "快递配置")
    public RpcResult config(ExpressConfigParam param) {
        try {
            expressService.config(param);
            return RpcResult.success("配置成功");
        } catch (ArgsInvalidException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }

    /**
     * 运单申报系统下拉
     *
     * @return
     * @path /express/listLogisticsDeclareSystem
     */
    @Override
    @SoulClient(path = "/express/listLogisticsDeclareSystem", desc = "运单申报系统下拉")
    public RpcResult listLogisticsDeclareSystem() {
        return RpcResult.success(EnumUtils.build(LogisticsDeclareSystemEnums.class, "code", "desc"));
    }
}
