package com.danding.cds.web.checklist.vo;

import com.danding.cds.exportorder.api.dto.ExportItemDTO;
import com.danding.cds.invenorder.api.dto.InventoryOrderRelationDTO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
@Data
public class EndorsementDetailWarp {

    @ApiModelProperty("业务类型")
    private String bussinessType;
    @ApiModelProperty("业务类型描述")
    private String bussinessTypeDesc;

//    @ApiModelProperty("是否可以编辑")
//    private boolean canEdit;

    @ApiModelProperty("运单项单据信息,单据信息")
    private List<ExportItemDTO> itemList;


//    @ApiModelProperty("报关单号 ,单据信息")
//    private String declareOrderNo;

    @ApiModelProperty("关联单证列表 ,单据信息")
    List<InventoryOrderRelationDTO> listInventoryOrderRelationDTO;

    @ApiModelProperty("表体信息")
    List<BodyItemInfo> bodyItemInfos = new ArrayList<BodyItemInfo>();
}
