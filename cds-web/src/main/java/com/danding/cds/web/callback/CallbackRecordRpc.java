package com.danding.cds.web.callback;

import cn.hutool.core.date.DateTime;
import com.danding.cds.callback.api.dto.CallbackRecordDTO;
import com.danding.cds.callback.api.dto.CallbackSearch;
import com.danding.cds.callback.api.enums.CallbackStatus;
import com.danding.cds.callback.api.enums.CallbackType;
import com.danding.cds.callback.api.service.CallbackRecordService;
import com.danding.cds.order.api.enums.OrderStatus;
import com.danding.cds.web.callback.vo.CallbackRecordVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.result.RpcResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@DubboService
public class CallbackRecordRpc implements ICallbackRecordRpc {

    @DubboReference
    private CallbackRecordService callbackRecordService;

    @Override
    @SoulClient(path = "/callback/paging", desc = "回传管理分页查询")
    public RpcResult<ListVO<CallbackRecordVO>> paging(CallbackSearch search) {
        ListVO<CallbackRecordDTO> dtoListVO = callbackRecordService.paging(search);
        ListVO<CallbackRecordVO> result = new ListVO<>();
        result.setPage(dtoListVO.getPage());
        result.setDataList(dtoListVO.getDataList().stream().map((CallbackRecordDTO recordDTO) ->{
            CallbackRecordVO vo = new CallbackRecordVO();
            vo.setId(recordDTO.getId());
            vo.setNotifyUrl(recordDTO.getNotifyUrl());
            vo.setBusinessCode(recordDTO.getBusinessCode());
            vo.setType(CallbackType.getEnum(recordDTO.getType()).getDesc());
            vo.setStatusDesc(CallbackStatus.getEnum(recordDTO.getStatus()).getDesc());
            vo.setCount(recordDTO.getCount());
            vo.setActiveData(recordDTO.getActiveData());
            vo.setRequestData(recordDTO.getRequestData());
            vo.setResponseData(recordDTO.getResponseData());
            vo.setLastNotifyTime(new DateTime(recordDTO.getLastNotifyTime()).toString("yyyy/MM/dd HH:mm:ss"));
            vo.setCreateTime(new DateTime(recordDTO.getCreateTime()).toString("yyyy/MM/dd HH:mm:ss"));
            return vo;
        }).collect(Collectors.toList()));
        return RpcResult.success(result);
    }

    @Override
    @SoulClient(path = "/callback/listType", desc = "回传类型")
    public RpcResult<List<SelectItemVO>> listType() {
        List<SelectItemVO> result = Arrays.stream(CallbackType.values())
                .filter((CallbackType item) -> !item.equals(CallbackType.NULL))
                .map((CallbackType item) -> {
                    SelectItemVO optionDTO = new SelectItemVO();
                    optionDTO.setValue(item.getCode());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }
}
