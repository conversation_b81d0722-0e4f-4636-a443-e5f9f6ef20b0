package com.danding.cds.web.checklist.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.Date;
import java.util.List;

@Data
@ApiModel
public class EndorsementSearchResult {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("核放单预录入编号")
    private List<ChecklistOrderNo> checklistOrderNo;

    /**
     * 核放企业单号
     */
    private List<String> checklistSnList;

    @ApiModelProperty("企业内部编号")
    private String sn;

    @ApiModelProperty("核注单预录入编号")
    private String endorsementOrderNo;

    @ApiModelProperty("真实核注清单编号")
    private String realEndorsementOrderNo;

    @ApiModelProperty("创建时间")
    private String createAt;

    @ApiModelProperty("录入时间")
    private String finishAt;

    @ApiModelProperty("状态描述")
    private String statusDesc;

    @ApiModelProperty("状态")
    private String status;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("海关回执状态描述")
    private String customsStatusDesc;

    @ApiModelProperty("信息描述")
    private String informationDesc = "";

    @ApiModelProperty("是否待完成")
    private Boolean waitFinishFlag;

    @ApiModelProperty("出入区类型 1出 2入")
    private Integer ieType;

    @ApiModelProperty("核注清单类型")
    private  String bussinessTypeDesc;
//    @ApiModelProperty("核注单详情")
//    private EndorsementDetailWarp endorsementDetailWarp;
//    @ApiModelProperty("出区数据")
//    private EndorsementOutputAreaData outputAreaData;
//
//    @ApiModelProperty("入区数据")
//    private EndorsementInputAreaData inputAreaData;
//
//    @ApiModelProperty("表体数据")
//    private List<BodyItemInfo> bodyItemInfo;
    @ApiModelProperty("是否允许完成核注")
    private Boolean allowFinish = false;

    @ApiModelProperty("是否允许导出")
    private Boolean allowExport = false;

    @ApiModelProperty("是否允许作废")
    private Boolean allowDiscard = false;

    @ApiModelProperty("是否允许查看")
    private Boolean allowView = false;

    @ApiModelProperty("是否允许编辑")
    private Boolean allowEdit = false;

    @ApiModelProperty("是否允许暂存")
    private Boolean allowStorage = false;

    @ApiModelProperty("是否允许剔除异常")
    private Boolean allowEliminateException = false;

    @ApiModelProperty("是否允许删单申请")
    private Boolean allowDeletedApply = false;

    @ApiModelProperty("账册编号")
    private String customsBookNo;

    @ApiModelProperty("清关企业")
    private String declareCompanyName;

    /**
     * 清关单号
     */
    private String invOrderSn;

    /**
     * 申报出库单号
     */
    private String exportOrderSn;

    @ApiModelProperty("操作人")
    private String createBy;

    /**
     * 是否核扣账册
     */
    private String stockChangeEnableDesc;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 报关单生成状态
     */
    private String generateDeclareStatusDesc;
    private String generateDeclareReason;

    /**
     * 单据类型
     */
    private String orderType;
    private String orderTypeDesc;

    @Data
    @AllArgsConstructor
    public static class ChecklistOrderNo {
        private String orderNo;
    }
}
