package com.danding.cds.web.exportorder.rpc;

import com.alibaba.excel.EasyExcel;
import com.danding.cds.common.annotations.UcAccountBookAuthGetAndCheck;
import com.danding.cds.common.constants.CommonCons;
import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.common.model.excel.UrlParamById;
import com.danding.cds.common.utils.DownLoadUtil;
import com.danding.cds.download.api.enums.ReportType;
import com.danding.cds.download.api.enums.ServiceException;
import com.danding.cds.download.api.service.DownloadProcessService;
import com.danding.cds.exportorder.api.dto.*;
import com.danding.cds.exportorder.api.enums.ExportOrderStatus;
import com.danding.cds.exportorder.api.service.ExportOrderService;
import com.danding.cds.upload.api.enums.UploadType;
import com.danding.cds.upload.api.service.UploadProcessService;
import com.danding.cds.upload.api.vo.ExportItemImportVO;
import com.danding.cds.v2.bean.vo.EndorsementVO;
import com.danding.cds.web.exportorder.controller.ExportOrderController;
import com.danding.cds.web.exportorder.manager.ExportOrderManager;
import com.danding.cds.web.exportorder.vo.*;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.logistics.api.common.response.SelectItemVO;
import com.danding.logistics.api.exceptions.businessException.ArgsErrorException;
import com.danding.park.client.ParkClient;
import com.danding.park.client.core.load.dto.LoadTaskInfoDTO;
import com.danding.park.client.core.load.query.CurrentLoadTaskQuery;
import com.danding.soul.client.common.annotation.SoulClient;
import com.danding.soul.client.common.context.UserInfoContext;
import com.danding.soul.client.common.result.RpcResult;
import com.danding.ucenter.core.annotation.UCApi;
import com.danding.ucenter.core.annotation.UCData;
import com.danding.ucenter.core.api.api.enums.AuthTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @menu 出库单管理
 */
@DubboService
@Slf4j
public class ExportOrderRpcImpl implements ExportOrderRpc {

    @DubboReference
    private ExportOrderService exportOrderService;
    @Autowired
    private ExportOrderController exportOrderController;
    @DubboReference
    private DownloadProcessService downloadProcessService;

    @DubboReference
    private UploadProcessService uploadProcessService;

    @Autowired
    private ExportOrderManager exportOrderManager;

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/listStatus", desc = "获取所有状态")
    public RpcResult<List<SelectOptionVO>> listStatus() {
        List<SelectOptionVO> result = Arrays.stream(ExportOrderStatus.values())
                .filter((ExportOrderStatus item) -> !item.equals(ExportOrderStatus.NULL))
                .map((ExportOrderStatus item) -> {
                    SelectOptionVO optionDTO = new SelectOptionVO();
                    optionDTO.setId(item.getValue());
                    optionDTO.setName(item.getDesc());
                    return optionDTO;
                }).collect(Collectors.toList());
        return RpcResult.success(result);
    }

    /**
     * 新增出库单
     *
     * @param submit
     * @return
     * @path /exportOrder/upset
     */
    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/upset", desc = "新增出区单")
    public RpcResult<Long> upset(ExportOrderSubmit submit) {
        return RpcResult.success(exportOrderService.create(submit));
    }

    /**
     * 分页查询
     *
     * @param search
     * @return
     * @path /exportOrder/paging
     */
    @Override
//    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/paging", desc = "分页查询")
//    @UcAccountBookAuthGetAndCheck
//    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    public RpcResult<ListVO<ExportOrderResult>> paging(ExportOrderSearch search) {
        return RpcResult.success(exportOrderController.paging(search));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/pre/input-import", desc = "输入预览")
    public RpcResult<ExportItemWritingReport> preExport(InputParams inputParams) {
        try {
            ExportItemWritingReport report = exportOrderController.preExportThenSave(inputParams);
            return RpcResult.success(report);
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/submit/import", desc = "输入预览")
    public RpcResult<ExportItemWritingReport> importExcel(ExportItemImportSubmit submit) {
        try {
            return RpcResult.success(exportOrderController.importExcel(submit));
        } catch (ArgsErrorException e) {
            return RpcResult.error(e.getErrorMessage());
        }
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/findMailById", desc = "查看运单")
    public RpcResult<ExportItemSimpleVO> findMailById(IdParam params) {
        return RpcResult.success(exportOrderController.findMailById(params.getId()));
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/listForEndorsement", desc = "下拉允许生成核注单的申请出库单")
    public RpcResult<List<SelectItemVO>> listForEndorsement() {
        List<ExportOrderDTO> orderDTOList = exportOrderService.listByStatus(ExportOrderStatus.WRITING.getValue());
        List<SelectItemVO> result = new ArrayList<>();
        for (ExportOrderDTO exportOrderDTO : orderDTOList) {
            SelectItemVO iItemVO = new SelectItemVO();
            iItemVO.setName(exportOrderDTO.getSn());
            iItemVO.setValue(exportOrderDTO.getId());
            result.add(iItemVO);
        }
        return RpcResult.success(result);
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/discard", desc = "作废")
    public RpcResult<Long> discard(IdParam idParam) {
        exportOrderService.discardById(idParam.getId());
        return RpcResult.success(idParam.getId());
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/delete", desc = "删除")
    public RpcResult<Long> delete(IdParam idParam) {
        exportOrderService.deleteById(idParam.getId());
        return RpcResult.success(idParam.getId());
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/submit/export", desc = "导出")
    public RpcResult<String> export(IdParam idParam) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    idParam, ReportType.EXPORT_ORDER_FOR_EXCEL);
            return RpcResult.success("提交成功");
        } catch (ServiceException e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/exportOrder/import/query", desc = "导入查询")
    public RpcResult<LoadTaskInfoDTO> importQuery() {
        UploadType uploadType = UploadType.EXPORT_ORDER_LOGISTICS;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setTemplateUrl(uploadType.getUrl());
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        LoadTaskInfoDTO loadTaskInfoDTO = ParkClient.loadClient().getCurrentTaskInfo(currentLoadTaskQuery);
        return RpcResult.success(loadTaskInfoDTO);
    }

    @Override
    @SoulClient(path = "/exportOrder/import", desc = "导入")
    public RpcResult<String> importExcelFile(ImportSubmitParam importWebForm) {
        try {
            uploadProcessService.submitProcess(UserInfoContext.getInstance().getUserInfo().getUserId(),
                    importWebForm.getUrl(), UploadType.EXPORT_ORDER_LOGISTICS, importWebForm.getId());
            return RpcResult.success("提交成功");
        } catch (Exception e) {
            log.warn("处理异常：{}", e.getMessage(), e);
            return RpcResult.error("提交失败");
        }
    }

    @Override
    @SoulClient(path = "/exportOrder/import/confirm", desc = "导入确认")
    public RpcResult<String> importConfirm() {
        UploadType uploadType = UploadType.EXPORT_ORDER_LOGISTICS;
        CurrentLoadTaskQuery currentLoadTaskQuery = new CurrentLoadTaskQuery();
        currentLoadTaskQuery.setFuncCode(uploadType.getValue());
        currentLoadTaskQuery.setMasterUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        currentLoadTaskQuery.setUserId(UserInfoContext.getInstance().getUserInfo().getUserId());
        ParkClient.loadClient().confirmCurrent(currentLoadTaskQuery);
        return RpcResult.success("提交成功");
    }

    @Override
    @UCApi(type = AuthTypeEnum.ID_CEHCK)
    @SoulClient(path = "/exportOrder/preImport", desc = "出库单导入预览")
    public RpcResult<ExportItemWritingReport> preImport(UrlParamById urlParam) {
        if (Objects.isNull(urlParam.getId())) {
            return RpcResult.error("出库单id为空");
        }
        ExportOrderDTO exportOrderDTO = exportOrderService.findById(urlParam.getId());
        if (Objects.isNull(exportOrderDTO)) {
            return RpcResult.error("未查询到关联出库单信息");
        }
        if (!Objects.equals(exportOrderDTO.getStatus(), ExportOrderStatus.WRITING.getValue())) {
            return RpcResult.error("出库单当前状态无法导入和录入运单号");
        }
//        String lockName = "CCS:EXPORT:IMPORT:" + exportOrderDTO.getSn();
        try {
//            boolean locked = RedissLockUtil.tryLock(lockName, TimeUnit.SECONDS, 3, -1);
//            if (!locked) {
//                log.error("出库单录入单号中，加锁失败 单号:{}", exportOrderDTO.getSn());
//                return RpcResult.error("出库单录入单号中，请稍后再试");
//            }
            List<ExportItemImportVO> successList = EasyExcel.read(DownLoadUtil.downloadNet(urlParam.getUrl())).headRowNumber(1).head(ExportItemImportVO.class).sheet().doReadSync();
            int i = 1; // 行号，从2开始
            List<ExportItemRecord> preList = new ArrayList<>();
            for (ExportItemImportVO demoExcel : successList) {
                i++;
                if (StringUtils.isEmpty(demoExcel.getExpressName())
                        && StringUtils.isEmpty(demoExcel.getMailNo())) {
                    continue;
                }
                ExportItemRecord record = new ExportItemRecord();
                BeanUtils.copyProperties(demoExcel, record);
                record.setIdx(i);
                preList.add(record);
            }
            if (CollectionUtils.isEmpty(preList)) {
                return RpcResult.error("对不起！选择模板文件为空或模板不正确");
            }
            ExportItemWritingReport report = exportOrderManager.writing(urlParam.getId(), preList, false, null);
            // 将校验成功的出库单表体直接导入，不走二次确认导入了
            ExportItemWritingReport successReport = new ExportItemWritingReport();
            BeanUtils.copyProperties(report, successReport);
            successReport.setFailCount(0);
            successReport.setFailRecordList(new ArrayList<>());
            exportOrderService.writingBlockByItem(urlParam.getId(), successReport);
            return RpcResult.success(report);
        } catch (Exception e) {
            log.error("录入失败 error={}", e.getMessage(), e);
            return RpcResult.error("录入失败");
        } finally {
//            try {
//                RedissLockUtil.unlock(lockName);
//                log.info("解锁成功 lockName={}", lockName);
//            } catch (Exception ex) {
//                log.error("解锁异常 lockName={} error={}", lockName, ex.getMessage(), ex);
//            }
        }
    }

    /**
     * 详情
     *
     * @param id
     * @return
     * @path /exportOrder/detail
     */
    @SoulClient(path = "/exportOrder/detail", desc = "详情")
    @Override
    public RpcResult<ExportOrderDetailVO> detail(IdParam id) {
        return RpcResult.success(exportOrderController.detail(id));
    }

    /**
     * 查询表体
     *
     * @param search
     * @return
     * @path /exportOrder/pagingItems
     */
    @SoulClient(path = "/exportOrder/pagingItems", desc = "查询表体")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @Override
    public RpcResult<ListVO<ExportItemVO>> pagingItems(ExportOrderItemSearch search) {
        return RpcResult.success(exportOrderController.itemPaging(search));
    }


    /**
     * 表体导出
     *
     * @param search
     * @return
     * @path /exportOrder/exportItems
     */
    @SoulClient(path = "/exportOrder/exportItems", desc = "表体导出")
    @UcAccountBookAuthGetAndCheck
    @UCData(group = CommonCons.GROUP_CUSTOMS_BOOK, types = {CommonCons.CUSTOMS_ACC_BOOK})
    @Override
    public RpcResult<String> exportItems(ExportOrderItemSearch search) {
        try {
            downloadProcessService.submitDownloadProcess(UserInfoContext.getInstance().getUserInfo().getUserId(), search, ReportType.EXPORT_ORDER_ITEM_FOR_EXCEL);
            return RpcResult.success();
        } catch (ServiceException e) {
            log.error("表体导出失败", e);
            return RpcResult.error("表体导出失败");
        }
    }

    /**
     * 关联核注单列表
     *
     * @param params
     * @path /exportOrder/listEndorsementById
     */
    @Override
    @SoulClient(path = "/exportOrder/listEndorsementById", desc = "关联核注单列表")
    public RpcResult<List<EndorsementVO>> listEndorsementById(IdParam params) {
        return RpcResult.success(exportOrderController.listEndorsementById(params));
    }

    /**
     * 生成核注单
     *
     * @param params
     * @return
     * @path /exportOrder/generateEndorsement
     */
    @SoulClient(path = "/exportOrder/generateEndorsement", desc = "生成核注单")
    @Override
    public RpcResult<String> generateEndorsement(IdParam params) {
        exportOrderController.generateEndorsement(params.getId());
        return RpcResult.success();
    }
}
