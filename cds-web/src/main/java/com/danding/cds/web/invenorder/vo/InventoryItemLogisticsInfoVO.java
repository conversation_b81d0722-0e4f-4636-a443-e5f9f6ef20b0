package com.danding.cds.web.invenorder.vo;

import lombok.Data;

/**
 * 清关单表体溯源信息
 */
@Data
public class InventoryItemLogisticsInfoVO {

    /**
     * SKU
     */
    private String skuId;
    /**
     * 统一料号
     */
    private String originProductId;
    /**
     * 海关备案料号
     */
    private String productId;
    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 报关日期
     */
    private String customsEntryTime;

    /**
     * 原产国
     */
    private String originCountry;

    /**
     * 启运国
     */
    private String shipmentCountry;

    /**
     * 起运港（始发机场）
     */
    private String fromLocation;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 进境口岸
     */
    private String entryPort;
}
