package com.danding.cds.web.v2.bean.vo.res;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
public class ProcessTradeBookDetailVO implements Serializable {

    /**
     * id
     */
    private Long id;

    /**
     * 状态
     */
    private Integer status;
    private String statusDesc;

    /**
     * 预录入统一编号
     */
    private String preNo;

    /**
     * 账册id（物流账册id）
     */
    private Long logisticsBookId;

    /**
     * 加工贸易账册编号
     */
    private String processTradeBookNo;

    /**
     * 企业内部编号
     */
    private String sn;

    /**
     * 主管海关
     */
    private String masterCustoms;
    private String masterCustomsDesc;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 经营单位编码
     */
    private String operateCompanyCode;

    /**
     * 经营单位社会信用代码
     */
    private String operateCompanyUSCC;

    /**
     * 经营单位名称
     */
    private String operateCompanyName;

    /**
     * 加工单位编码
     */
    private String processCompanyCode;

    /**
     * 加工单位社会信用代码
     */
    private String processCompanyUSCC;

    /**
     * 加工单位名称
     */
    private String processCompanyName;

    /**
     * 申报单位编码
     */
    private String declareCompanyCode;

    /**
     * 申报单位社会信用代码
     */
    private String declareCompanyUSCC;

    /**
     * 申报单位名称
     */
    private String declareCompanyName;

    /**
     * 录入单位编码
     */
    private String inputCompanyCode;

    /**
     * 录入单位社会信用代码
     */
    private String inputCompanyUSCC;

    /**
     * 录入单位名称
     */
    private String inputCompanyName;

    /**
     * 申报单位类型，枚举 1-企业、2-代理公司、3-报关行
     */
    private Integer declareCompanyType;
    private String declareCompanyTypeDesc;

    /**
     * 申报类型
     */
    private Integer declareType;
    private String declareTypeDesc;

    /**
     * 账册类型
     */
    private Integer bookType;
    private String bookTypeDesc;

    /**
     * 批准证编号，中英文数字特殊符号，不允许空格，限制 32 位
     */
    private String approvalCertNo;

    /**
     * 企业档案库编号，中英文数字特殊符号，不允许空格，限制 32 位
     */
    private String enterpriseArchiveNo;

    /**
     * 实际进口总金额
     */
    private BigDecimal actualImportTotalAmount;

    /**
     * 实际出口总金额
     */
    private BigDecimal actualExportTotalAmount;

    /**
     * 料件项数
     */
    private Integer mtpckCount;

    /**
     * 成品项数
     */
    private Integer endPrdCount;

    /**
     * 最大周转金额（万美金），整数部分最大 14 位，小数最大 5 位
     */
    private BigDecimal maxTurnoverAmount;

    /**
     * 备案批准日期
     */
    private Date recordApprovalDate;

    /**
     * 变更批准日期
     */
    private Date changeApprovalDate;

    /**
     * 最近核销日期
     */
    private Date lastVoucherClearanceDate;

    /**
     * 单耗申报环节，枚举，1-出口前 2-报核前
     */
    private Integer consumptionDeclarationLink;
    private String consumptionDeclarationLinkDesc;

    /**
     * 单耗版本号控制标志
     */
    private Integer consumptionVersionControlFlag;
    private String consumptionVersionControlFlagDesc;

    /**
     * 最大进口金额（美元）
     */
    private BigDecimal maxImportAmount;

    /**
     * 核销周期，整数不超过 4 位，系统默认初始化，180
     */
    private Integer voucherClearanceCycle;

    /**
     * 核销类型
     */
    private Integer voucherClearanceType;
    private String voucherClearanceTypeDesc;

    /**
     * 账册变更次数
     */
    private Integer bookChangeCount;

    /**
     * 账册结束有效期，时间组件选择年月日，格式（yyyyMMdd）
     */
    private Date bookEndValidity;

    /**
     * 账册执行标志
     */
    private Integer bookExecutionFlag;
    private String bookExecutionFlagDesc;

    /**
     * 账册用途
     */
    private Integer bookUsage;
    private String bookUsageDesc;

    /**
     * 核销方式，枚举，1-企业自核 2-海关核算
     */
    private Integer voucherClearanceMethod;
    private String voucherClearanceMethodDesc;

    /**
     * 备注
     */
    private String remark;

}
