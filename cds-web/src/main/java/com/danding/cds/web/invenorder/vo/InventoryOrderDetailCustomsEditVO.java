package com.danding.cds.web.invenorder.vo;

import com.danding.cds.common.enums.InventoryDeclarationEnum;
import com.danding.cds.common.enums.InventoryTwoStepEnum;
import lombok.Data;

/**
 * 编辑清关单详情 通关信息
 */
@Data
public class InventoryOrderDetailCustomsEditVO {

    private Long id;

    /**
     * 业务类型
     */
    private String inveBusinessType;

    /**
     * 运输方式
     */
    private String transportMode;

    /**
     * 进出境关别
     */
    private String entryExitCustoms;

    /**
     * 提单号
     */
    private String pickUpNo;

    /**
     * 货代公司
     */
    private String forwardingCompany;

    /**
     * 集装箱号
     */
    private String conNo;

    /**
     * 进境口岸
     */
    private String entryPort;

    /**
     * 起运港/始发机场
     */
    private String fromLocation;

    /**
     * 到货港口/机场
     */
    private String arrivalPort;

    /**
     * 品名
     */
    private String productName;

    /**
     * 类目
     */
    private String category;

    /**
     * 转出方
     */
    private String transferor;

    /**
     * 转入方
     */
    private String transferee;

    /**
     * 实际到港日期
     */
    private Long actualArrivalDate;

    /**
     * 对应报关单申报单位
     */
    private Long corrCusDeclareCompanyId;

    /**
     * 关联报关单境内收发货人
     */
    private Long rltCusInnerSFHRCompanyId;

    /**
     * 关联报关单消费使用单位
     */
    private Long rltCusXFDYCompanyId;

    /**
     * 关联报关单申报单位
     */
    private Long rltCusDeclareCompanyId;

    /**
     * 关联核注清单编号
     */
    private String associatedEndorsementNo;

    /**
     * 对应报关单类型
     */
    private String customsEntryType;

    /**
     * 报关单号
     */
    private String customsEntryNo;

    /**
     * 报关类型
     * {@link com.danding.cds.common.enums.InventoryCustomsTypeEnums}
     */
    private Integer customsType;

    /**
     * 是否生成报关单(1:是 2:否)
     * {@link InventoryDeclarationEnum}
     */
    private Integer declarationFlag;

    /**
     * 清关方式
     */
    private Integer declareWay;

    /**
     * 是否两步申报(1:是 0:否)（指区港联动等）
     * {@link InventoryTwoStepEnum}
     */
    private Integer twoStepFlag;

    /**
     * 启运国
     */
    private String shipmentCountry;

    /**
     * 关联转入账册
     */
    private String inAccountBook;

    /**
     * 关联转出账册
     */
    private String outAccountBook;

    /**
     * 核注单备注
     */
    private String endorsementRemark;

    /**
     * 申报表编号
     */
    private String declareFormNo;

}
