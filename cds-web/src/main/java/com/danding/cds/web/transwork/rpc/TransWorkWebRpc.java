package com.danding.cds.web.transwork.rpc;

import java.util.List;

import com.danding.cds.common.model.IdParam;
import com.danding.cds.common.model.SelectOptionVO;
import com.danding.cds.transwork.api.vo.TransAddTruckNoVO;
import com.danding.cds.transwork.api.vo.TransTruckInfoVO;
import com.danding.cds.transwork.api.vo.TransWorkDetailVO;
import com.danding.cds.transwork.api.vo.TransWorkItemEditVO;
import com.danding.cds.transwork.api.vo.TransWorkItemVO;
import com.danding.cds.transwork.api.vo.TransWorkOrderPackagingItemsVO;
import com.danding.cds.transwork.api.vo.TransWorkPageVO;
import com.danding.cds.transwork.api.vo.TransWorkSearch;
import com.danding.cds.transwork.api.vo.TransWorkTimeEditVO;
import com.danding.logistics.api.common.response.ListVO;
import com.danding.soul.client.common.result.RpcResult;

/**
 * @Author: yousx
 * @Date: 2024/03/12
 * @Description:
 */
public interface TransWorkWebRpc {


    RpcResult<ListVO<TransWorkPageVO>> paging(TransWorkSearch search);
    RpcResult<TransWorkDetailVO> detail(IdParam idParam);

    RpcResult<ListVO<TransWorkItemVO>> itemPaging(IdParam idParam);

    RpcResult<String> itemEdit(TransWorkItemEditVO param);

    RpcResult<String> editTimeInfo(TransWorkTimeEditVO param);

    RpcResult<String> addTruckInfo(TransTruckInfoVO param);

    RpcResult<String> addTruckNo(TransAddTruckNoVO param);

    RpcResult<List<SelectOptionVO<String>>> orderTypeList();

    RpcResult<String> finishTransWork(IdParam idParam);

    RpcResult<List<TransWorkOrderPackagingItemsVO>> getPackagingItems(IdParam idParam);

    RpcResult<String> packagingItemEdit(TransWorkItemEditVO param);

}
