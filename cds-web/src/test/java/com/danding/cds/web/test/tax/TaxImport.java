package com.danding.cds.web.test.tax;

import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import lombok.extern.slf4j.Slf4j;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * @Auther: Dante-GXJ
 * @Date: 2020/10/28 09:40
 * @Description: 关键词 CEB816Message
 */
@Slf4j
public class TaxImport {

//    @Test
    public void importFile() {
        String filePath = "E:\\税费日志.xlsx";
        List<TaxLogImport> apiInfos = readExcel(filePath, 0, TaxLogImport.class);
        List<TaxLogImport> taxList = new ArrayList<>();
        for (TaxLogImport apiInfo : apiInfos) {
            if (apiInfo.getMessage().startsWith("[op:ZJPortCallbackParser]")){
                taxList.add(apiInfo);
            }
        }
        System.out.println(taxList.size());
        for (TaxLogImport taxLogImport : taxList) {
            //System.out.println(taxLogImport.getMessage());
        }

    }

    public static <E> List<E> readExcel(String filePath, int sheetIndex, Class<E> clazz) {
        // 定义输入流
        FileInputStream fis = null;
        List<E> datas = null;
        try {
            // 创建输入流对象
            fis = new FileInputStream(filePath);
            // 创建一个easypoi使用的配置类
            ImportParams params = new ImportParams();
            params.setHeadRows(1);
            params.setTitleRows(0);
            // 设置表格坐标
            params.setStartSheetIndex(sheetIndex);
            // 校验Excel文件，去掉空行
            params.setNeedVerify(true);
            // 读取数据
            datas = ExcelImportUtil.importExcel(fis, clazz, params);
        } catch (Exception e) {
            log.error("处理异常：{}", e.getMessage(), e);
        } finally {
            try {
                if (fis != null) {
                    fis.close();
                }
            } catch (IOException e) {
                log.error("处理异常：{}", e.getMessage(), e);
            }
        }
        return datas;
    }
}
