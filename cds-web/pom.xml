<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cds-center</artifactId>
        <groupId>com.danding</groupId>
        <version>1.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>cds-web</artifactId>
    <dependencies>
        <!-- 一方包 -->
        <!--<dependency>-->
        <!--<groupId>com.danding</groupId>-->
        <!--<artifactId>cds-exception</artifactId>-->
        <!--<version>1.0-SNAPSHOT</version>-->
        <!--</dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-out-rpc</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cache-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-download-api</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>uc-component-dubbo</artifactId>
                    <groupId>com.danding</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>uc-component-core</artifactId>
                    <groupId>com.danding</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-order-c-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-item-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-company-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-taxes-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-mail-client</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-mq-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>cds-xxl-saas-component</artifactId>
            <version>1.0-SNAPSHOT</version>
        </dependency>
        <!--  二方包 -->
        <dependency>
            <groupId>com.alibaba.cloud</groupId>
            <artifactId>spring-cloud-starter-alibaba-nacos-config</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>danding-encryption-standalone</artifactId>
        </dependency>
        <!-- apollo -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>soul-client-apache-dubbo</artifactId>
            <version>2.1.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>soul-client-common</artifactId>
            <version>2.1.3-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-doc-rpc-api</artifactId>
            <version>1.0.1-SNAPSHOT</version>
            <scope>compile</scope>
        </dependency>
        <!--        <dependency>-->
        <!--            <groupId>com.danding</groupId>-->
        <!--            <artifactId>apolloclient-component</artifactId>-->
        <!--        </dependency>-->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>dubbo-component</artifactId>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>web-component</artifactId>
        </dependency>
        <!-- 三方包 -->
        <dependency>
            <groupId>org.codehaus.janino</groupId>
            <artifactId>janino</artifactId>
            <version>3.0.6</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>
        <dependency>
            <groupId>joda-time</groupId>
            <artifactId>joda-time</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-spring-boot-starter</artifactId>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ucenter-client-dubbo</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ucenter-client-autoconfigure</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <!--用于覆盖依赖的外部系统可能存在低版本 -->
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-dubbo</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>uc-component-core</artifactId>
            <version>1.1.1-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>park-client-autoconfigure</artifactId>
            <version>1.0.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-goods-rpc-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>uc-component-dubbo</artifactId>
                    <groupId>com.danding</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>uc-component-core</artifactId>
                    <groupId>com.danding</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.danding</groupId>
            <artifactId>ares-rpc-trade-client</artifactId>
        </dependency>
    </dependencies>
    <build>
        <finalName>ccs-web</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring-boot-maven-plugin.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>
                                repackage
                            </goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>