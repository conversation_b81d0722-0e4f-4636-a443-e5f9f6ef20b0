from registry.cn-hangzhou.aliyuncs.com/danding/corp:openjdk-8-agent-1.2
VOLUME /home/<USER>
ADD target/ccs-web.jar ccs-web.jar
EXPOSE 8083
RUN curl -Os https://arthas.aliyun.com/arthas-boot.jar && \
ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && \
echo "Asia/Shanghai" > /etc/timezone
ENV JAVA_OPTS="$JAVA_OPTS -Dfile.encoding=UTF8 -Duser.timezone=GMT+08"
ENTRYPOINT exec java $JAVA_OPTS -Xms2g -Xmx4g -Djava.security.edg=file:/dev/./urandom -jar /ccs-web.jar
