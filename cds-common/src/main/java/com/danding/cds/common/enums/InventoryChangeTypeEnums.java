package com.danding.cds.common.enums;

import lombok.Getter;

import static com.danding.cds.common.enums.InventoryCalculationTypeEnums.*;

@Getter
public enum InventoryChangeTypeEnums {

    /**
     * 账册 +
     * 可用 +
     */
    BOOKS_CHECKLIST_RECEIPT(10, "核注清单记账回执", "可用核增", ADD_AVAILABLE.getType(), true),
    /**
     * 无用
     */
    ONELINE_IN_ENDORSEMENT_CREATE(20, "一线入境核注创建", "在途核增", ADD_AVAILABLE.getType(), false),
    /**
     * 无用
     */
    ONELINE_IN_ENDORSEMENT_CANCEL(21, "一线入境取消", "可用回滚", null, false),
    /**
     * 账册 +
     * 可用 +
     */
    ONELINE_IN_ENDORSEMENT_FINISH(22, "一线入境", "可用核增", ADD_AVAILABLE.getType(), true),
    /**
     * 账册 -
     * 可用 -
     */
    ONELINE_IN_ENDORSEMENT_DELETE(23, "一线入境取消", "可用回滚", REDUCE_AVAILABLE.getType(), true),
    /**
     * 无用
     */
    SECTION_IN_CREATE(30, "结转入核注创建", "在途核增", null, false),
    /**
     * 无用
     */
    SECTION_IN_CANCEL(31, "结转入核注取消", "在途取消", null, false),
    /**
     * 已废弃
     * 拆成区间调入、区内调入
     */
    SECTION_IN_FINISH(32, "结转入核注完成", "结转核增", ADD_AVAILABLE.getType(), true),
    /**
     * 已废弃
     * 拆成区间调入、区内调入
     */
    SECTION_IN_DELETE(33, "结转入核注删除", "可用退还", REDUCE_AVAILABLE.getType(), true),
    /**
     * 账册 +
     * 可用 +
     */
    NEW_SECTION_IN_FINISH(34, "区间调入", "可用核增", ADD_AVAILABLE.getType(), true),
    /**
     * 账册 +
     * 可用 +
     */
    NEW_SECTION_INNER_IN_FINISH(35, "区内调入", "可用核增", ADD_AVAILABLE.getType(), true),
    /**
     * 账册 -
     * 可用 -
     */
    NEW_SECTION_IN_DELETE(36, "区间调入取消", "可用回滚", REDUCE_AVAILABLE.getType(), true),
    /**
     * 账册 -
     * 可用 -
     */
    NEW_SECTION_INNER_IN_DELETE(37, "区内调入取消", "可用回滚", REDUCE_AVAILABLE.getType(), true),
    /**
     * 无用
     */
    SECTION_OUT_CREATE(40, "结转出核注创建", "预占核增", ADD_OCCUPATION.getType(), false),
    /**
     * 无用
     */
    SECTION_OUT_CANCEL(41, "结转出核注取消", "占用取消", REDUCE_OCCUPATION.getType(), false),
    /**
     * 已废弃
     * 拆成区间调出、区内调出
     */
    SECTION_OUT_FINISH(42, "结转出核注完成", "预占释放", OCCUPIED_TO_USED.getType(), false),
    /**
     * 无用
     */
    SECTION_OUT_PUSH(43, "结转出核注申报", "预占核增", ADD_OCCUPATION.getType(), false),
    /**
     * 无用
     */
    ENDORSEMENT_SEND(44, "核注推送完成", "锁定", null, false),
    /**
     * 已废弃
     * 拆成区间调出、区内调出
     */
    SECTION_OUT_DELETE(45, "结转出核注删除", "已用退还", USED_TO_AVAILABLE.getType(), true),
    /**
     * 账册 -
     * 已用 +
     * 可用 -
     */
    NEW_SECTION_OUT_FINISH(46, "区间调出", "可用核减", AVAILABLE_TO_USED.getType(), true),
    /**
     * 账册 -
     * 已用 +
     * 可用 -
     */
    NEW_SECTION_INNER_OUT_FINISH(47, "区内调出", "可用核减", AVAILABLE_TO_USED.getType(), true),
    /**
     * 账册 +
     * 已用 -
     * 可用 +
     */
    NEW_SECTION_OUT_DELETE(48, "区间调出取消", "已用回滚", USED_TO_AVAILABLE.getType(), true),
    /**
     * 账册 +
     * 已用 -
     * 可用 +
     */
    NEW_SECTION_INNER_OUT_DELETE(49, "区内调出取消", "已用回滚", USED_TO_AVAILABLE.getType(), true),
    /**
     * 账册 +
     * 已用 -
     * 可用 +
     */
    REFUND_INAREA(50, "退货入区", "已用转可用", USED_TO_AVAILABLE.getType(), true),
    /**
     * 占用 +
     * 可用 -
     */
    LIST_ORDER_APPLYING(60, "清单申报", "占用核增", ADD_OCCUPATION.getType(), true),
    /**
     * 占用 +
     * 可用 -
     */
    LIST_ORDER_APPLY_CANCEL(61, "清单取消", "占用核减", REDUCE_OCCUPATION.getType(), true),
    /**
     * 无用
     */
    LIST_ORDER_APPLY_FAILED(62, "清单申报失败", "预占取消", REDUCE_OCCUPATION.getType(), true),
    /**
     * 无用
     */
    LIST_ORDER_APPLY_SUCCESS(63, "清单申报放行", "锁定转占用", null, false),
    /**
     * 占用 -
     * 可用 +
     */
    LIST_ORDER_UPDATED(64, "清单申报变更", "占用核减", REDUCE_OCCUPATION.getType(), true),
    /**
     * 占用 -
     * 可用 +
     */
    CANCEL_LIST_ORDER_APPLY_FINISH(70, "撤单完成", "占用核减", REDUCE_OCCUPATION.getType(), true),
    /**
     * 无用
     */
    REFUND_ORDER_APPLY_SUCCESS(71, "退货申报放行", "退货未核扣", null, false),
    /**
     * 账册 -
     * 占用 -
     * 已用 +
     */
    SECOND_OUT_FINISH(80, "包裹出区实扣", "占用转已用", OCCUPIED_TO_USED.getType(), true),
    /**
     * 账册 +
     * 可用 +
     */
    EXCEL_IMPORT(100, "海关账册库存同步", "账册调整", null, true),
    /**
     * 账册 +
     * 可用 +
     */
    EXCEL_ADJUST_IMPORT_INCREASE(101, "手工调整库存核增", "手动调增", ADD_AVAILABLE.getType(), true),
    /**
     * 注意 这里的+ 适用于加负数
     * 账册 +
     * 可用 +
     */
    EXCEL_ADJUST_IMPORT_REDUCE(102, "手工调整库存核减", "手动调减", ADD_AVAILABLE.getType(), true),
    /**
     * 无用
     */
    DESTORY_APPLY(110, "销毁申报", "预占核增", ADD_OCCUPATION.getType(), true),


    /**
     * 账册 -
     * 已用 +
     * 可用 -
     */
    DESTORY_FINISH(111, "销毁", "可用核减", AVAILABLE_TO_USED.getType(), true),

    /**
     * 占用 -
     * 可用 +
     */
    DELETE_APPLY(112, "删除申报", "占用核减", REDUCE_OCCUPATION.getType(), true),

    /**
     * 账册 +
     * 已用 -
     * 可用 +
     */
    DESTORY_DELETE(113, "销毁删除", "已用回滚", USED_TO_AVAILABLE.getType(), true),


    ONELINE_REFUND(120, "一线退运", "可用核减", REDUCE_AVAILABLE.getType(), true),
    ONELINE_REFUND_DELETE(121, "一线退运删除", "可用核增", ADD_AVAILABLE.getType(), true),

    BONDED_TO_TRADE(130, "保税物流转大贸", "可用核减", REDUCE_AVAILABLE.getType(), true),
    BONDED_TO_TRADE_DELETE(131, "保税物流转大贸删除", "可用核增", ADD_AVAILABLE.getType(), true),

    SUBSEQUENCE_TAX(140, "后续补税", "可用核减", REDUCE_AVAILABLE.getType(), true),
    SUBSEQUENCE_TAX_DELETE(141, "后续补税删除", "可用核增", ADD_AVAILABLE.getType(), true),

    BONDED_ONELINE_IN(150, "保税物流一线入境", "可用核增", ADD_AVAILABLE.getType(), true),
    BONDED_ONELINE_IN_DELETE(151, "保税物流一线入境删除", "可用核减", REDUCE_AVAILABLE.getType(), true),

    INVENTORY_PROFIT(160, "盘盈", "可用核增", ADD_AVAILABLE.getType(), true),
    INVENTORY_PROFIT_DELETE(161, "盘盈删除", "可用核减", REDUCE_AVAILABLE.getType(), true),

    RANDOM_INSPECTION_DECLARATION(170, "抽检申报", "可用核减", REDUCE_AVAILABLE.getType(), true),
    RANDOM_INSPECTION_DECLARATION_DELETE(171, "抽检申报删除", "可用核增", ADD_AVAILABLE.getType(), true),

    SIMPLE_PROCESSING(180, "简单加工", "可用核减", REDUCE_AVAILABLE.getType(), true),
    SIMPLE_PROCESSING_DELETE(181, "简单加工删除", "可用核增", ADD_AVAILABLE.getType(), true),

    LOCK_STOCK(190, "库存锁定", "锁定核增", ADD_LOCK.getType(), true),
    RELEASE_LOCK_STOCK(191, "库存解锁", "锁定核减", REDUCE_LOCK.getType(), true),

    SECONDE_OUT_EXCEL_IMPORT(200, "包裹出区实扣", "可用核减", REDUCE_AVAILABLE.getType(), true, true),
    REFUND_INAREA_EXCEL_IMPORT(201, "退货入区", "可用核增", ADD_AVAILABLE.getType(), true, true),

    ;


    /**
     * 库存变动类型
     */
    private Integer code;
    /**
     * 业务类型
     */
    private String businessType;
    /**
     * 变动类型
     */
    private String changeType;
    /**
     * 库存变动类型
     */
    private String inveCalType;

    private Boolean isEffective;

    private final Boolean isNotThrowError;

    InventoryChangeTypeEnums(Integer code, String businessType, String changeType, String inveCalType, Boolean isEffective) {
        this.code = code;
        this.businessType = businessType;
        this.changeType = changeType;
        this.inveCalType = inveCalType;
        this.isEffective = isEffective;
        this.isNotThrowError = false;
    }

    InventoryChangeTypeEnums(Integer code, String businessType, String changeType, String inveCalType, Boolean isEffective, Boolean isNotThrowError) {
        this.code = code;
        this.businessType = businessType;
        this.changeType = changeType;
        this.inveCalType = inveCalType;
        this.isEffective = isEffective;
        this.isNotThrowError = isNotThrowError;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getBusinessType() {
        return businessType;
    }

    public void setBusinessType(String businessType) {
        this.businessType = businessType;
    }

    public String getChangeType() {
        return changeType;
    }

    public void setChangeType(String changeType) {
        this.changeType = changeType;
    }

    public String getInveCalType() {
        return inveCalType;
    }

    public void setInveCalType(String inveCalType) {
        this.inveCalType = inveCalType;
    }

    public Boolean getEffective() {
        return isEffective;
    }

    public void setEffective(Boolean effective) {
        isEffective = effective;
    }

    public static InventoryChangeTypeEnums getEnum(Integer value) {
        for (InventoryChangeTypeEnums changeType : InventoryChangeTypeEnums.values()) {
            if (changeType.getCode().equals(value)) {
                return changeType;
            }
        }
        return null;
    }

    public static InventoryChangeTypeEnums getEnumByChangeType(String value) {
        for (InventoryChangeTypeEnums changeType : InventoryChangeTypeEnums.values()) {
            if (changeType.getChangeType().equals(value)) {
                return changeType;
            }
        }
        return null;
    }

    public static InventoryChangeTypeEnums getEnumByBusinessType(String value) {
        for (InventoryChangeTypeEnums changeType : InventoryChangeTypeEnums.values()) {
            if (changeType.getBusinessType().equals(value)) {
                return changeType;
            }
        }
        return null;
    }


}
