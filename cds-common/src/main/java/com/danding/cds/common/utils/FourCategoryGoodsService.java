package com.danding.cds.common.utils;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.Objects;

@Service
public class FourCategoryGoodsService {
    @Value("${four_category_goods_hsCode:1504100090,1504200091,1504200099}")
    private String fourCategoryGoodsHsCode;

    private String ORIGIN_COUNTRY_US = "502";
    private String ORIGIN_COUNTRY_CA = "501";


    /**
     * 四类措施商品判断
     *
     * @param originCountryCode 原产国
     * @return
     */
    public Boolean fourCategoryGoodsByOriginCountry(String originCountryCode) {
        return ORIGIN_COUNTRY_US.equals(originCountryCode) ? true : false;
    }

    /**
     * 专用账册调用
     *
     * @param originCountry
     * @param hsCode
     * @return
     */
    public boolean inventoryOuterKeyMaterialCheck(String originCountry, String hsCode) {
        if (Objects.equals(originCountry, ORIGIN_COUNTRY_US)) {
            return true;
        } else if (Objects.equals(originCountry, ORIGIN_COUNTRY_CA) && Arrays.asList(fourCategoryGoodsHsCode.split(",")).contains(hsCode)) {
            return true;
        } else {
            return false;
        }
    }

    public boolean inventoryOuterNormalMaterialCheck(String originCountry, String hsCode) {
        if (Objects.equals(originCountry, ORIGIN_COUNTRY_US)) {
            return false;
        } else if (Objects.equals(originCountry, ORIGIN_COUNTRY_CA) && Arrays.asList(fourCategoryGoodsHsCode.split(",")).contains(hsCode)) {
            return false;
        } else {
            return true;
        }
    }
}
