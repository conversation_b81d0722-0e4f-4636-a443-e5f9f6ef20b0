package com.danding.cds.common.enums;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @description:
 * @date 2022/6/1 13:58
 */
public class TrackLogConstantMixAll implements Serializable {
    public static String NULL = "无";
    public static String EMPTY = "空";
    /**
     * 结果
     */
    public static String SUCCESS = "成功";
    public static String FAIL = "失败";
    /**
     * 内部流转状态
     */
//    public static String CREATE_WAIT_PUSH = "创建待推送";
//    public static String DECLARING = "申报中";
//    public static String ORDER_PASS = "单证放行";
//    public static String DECLARE_CANCEL = "取消申报";
//    public static String DECLARE_CANCEL_FAIL = "取消申报失败";
//    public static String DECLARE_CANCEL_SUCCESS = "取消申报成功";
//    public static String INVENTORY_DECLARING = "清关中";
//    public static String INVENTORY_DECLARE_SUCCESS = "清关完成";
//    public static final String CANCELING = "撤单中";
//    public static final String CANCEL_FAIL = "撤单失败";
//    public static final String CANCEL_SUCCESS = "撤单成功";
//    public static final String REFUNDING = "退货中";
//    public static final String REFUND_FAIL = "退货失败";
//    public static final String REFUND_SUCCESS = "退货成功";
//    public static final String REFUND_INTO_WAREHOUSE = "退货入仓";

    /**
     * 系统名词
     */
    public static final String DT_CCS = "清关平台";

    public static final String DT_OMS = "OMS订单系统";

    public static final String CAINIAO = "菜鸟系统";

    public static final String ZJ_PORT = "浙江电子口岸";

    public static final String HZ_DATA_CENTER = "杭州数据分中心";

    public static final String PDD_OFFICIAL = "拼多多官方代报";

    public static final String EWTP = "EWTP节点";

    public static final String PANGU = "盘古系统";

    public static final String JIEZ_TECH = "芥舟系统";

    public static final String JD = "京东平台";

    public static final String DT_TMS = "TMS系统";

    public static final String BYTE_DANCE_CLOUD = "字节云内系统";

    public static final String JD_IN_CLOUD = "京东云内系统";

    public static final String REFUND_WAREHOUSE = "退货仓系统";

    /**
     * 节点概述
     */

    public static final String ORDER_CREATE = "申报单创建成功";
    public static final String PAYMENT_CREATE = "创建支付单";
    public static final String PAYMENT_DECLARE = "推送支付单";
    public static final String PAYMENT_DECLARE_RESULT = "支付单申报回执";
    public static final String PAYMENT_LOCK = "锁定申报单";

    public static final String PDD_OFFICIAL_ORDER_DECLARE = "拼多多官方代报订单";

    public static final String CUSTOMS_ORDER_DECLARE = "推浙电加签版订单";

    public static final String CEB_CUSTOMS_ORDER_DECLARE = "推总署加签版订单";
    public static final String CUSTOMS_ORDER_RECEIPT = "订单回执";

    public static final String PDD_OFFICIAL_LOGISTIC_DECLARE = "拼多多官方代报运单";
    public static final String CUSTOMS_LOGISTIC_DECLARE = "推送运单";
    public static final String CUSTOMS_LOGISTIC_RECEIPT = "运单回执";

    public static final String PDD_OFFICIAL_INVENTORY_DECLARE = "拼多多官方代报清单";
    public static final String ZJ_PORT_INVENTORY_DECLARE = "推浙电加签版清单";
    public static final String CEB_INVENTORY_DECLARE = "推总署加签版清单";
    public static final String CUSTOMS_INVENTORY_RECEIPT = "清单回执";
    public static final String CUSTOMS_TAX_CALLBACK = "税金回传";
    public static final String DECLARE_ORDER_INTERCEPT = "发起申报拦截";
    public static final String DECLARE_ORDER_INTERCEPT_SUCCESS = "申报拦截成功";
    public static final String DECLARE_ORDER_INTERCEPT_FAIL = "申报拦截失败";

    public static final String REVERSE_DECLARE_NOTIFY = "逆向申报通知";

    public static final String DECLARE_ORDER_REVIEW_PASS = "审单放行";
    public static final String DECLARE_ORDER_CANCEL = "订单取消";
    public static final String ENDORSEMENT_CREATE = "汇单发起成功";
    public static final String ENDORSEMENT_COMPLETED = "核注已核扣";
    public static final String CANCEL_CREATE = "撤单创建成功";

    public static final String PDD_OFFICIAL_CANCEL_DECLARE = "拼多多官方代报撤单";

    public static final String CANCEL_DECLARE = "推送撤单";
    public static final String CANCEL_RECEIPT = "撤单回执";
    public static final String CANCEL_DECLARE_FAIL = "撤单申报失败";
    public static final String CANCEL_DECLARE_REJECT = "撤单申报驳回";
    public static final String CANCEL_DECLARE_SUCCESS = "撤单申报成功";
    public static final String CANCEL_MODIFY_SUCCESS = "撤单修改成功:初始化";
    public static final String REFUND_CREATE = "退货单创建成功";

    public static final String PDD_OFFICIAL_REFUND_DECLARE = "拼多多官方代报退货";
    public static final String REFUND_DECLARE = "推送退货单";
    public static final String REFUND_RECEIPT = "退货单回执";
    public static final String REFUND_DECLARE_FAIL = "退货单申报失败";
    public static final String REFUND_DECLARE_SUCCESS = "退货单申报成功";
    public static final String REFUND_MODIFY_SUCCESS = "退货单修改成功:初始化";
    public static final String REFUND_ENDORSEMENT_SUCCESS = "退货入区核注成功";
    public static final String REFUND_MODIFY_CUSTOMS = "修改退货关区";

    public static final String REFUND_CROSS_CUSTOMS_MANUAL_ENDORSEMENT = "手动核注";

    public static final String BYTE_DANCE_CLOUD_INVENTORY_DECLARE = "字节云内申报清单";
    public static final String BYTE_DANCE_CLOUD_ORDER_DECLARE = "字节云内申报订单";
    public static final String BYTE_DANCE_CLOUD_LOGISTICS_DECLARE = "字节云内申报运单";
    public static final String BYTE_DANCE_CLOUD_CANCEL_DECLARE = "字节云内申报撤单";
    public static final String BYTE_DANCE_CLOUD_REFUND_DECLARE = "字节云内申报退货单";

    public static final String JD_IN_CLOUD_INVENTORY_DECLARE = "京东云内申报清单";
    public static final String JD_IN_CLOUD_ORDER_DECLARE = "京东云内申报订单";
    public static final String JD_IN_CLOUD_LOGISTICS_DECLARE = "京东云内申报运单";
    public static final String JD_IN_CLOUD_CANCEL_DECLARE = "京东云内申报撤单";
    public static final String JD_IN_CLOUD_REFUND_DECLARE = "京东云内申报退货单";

    public static final String DECLARE_ORDER_CHANGE = "申报单变更";


    public static final String RECEIVE_OUT_REGIN = "出区状态回传";

    public static final String TMS_LOGISTICS_DECLARE = "TMS运单申报";
    public static final String SYSTEM_CHANGE_DECLARE_BOOK = "系统变更申报账册";

    public static final String PART_REFUND_IMPORT = "导入部分退";

    public static final String COMPLETE_MESSAGE_SAVE = "明文报文清关保存单据";

    public static final String COMPLETE_MESSAGE_UPDATE = "明文报文清关更新单据";
    public static final String COMPLETE_MESSAGE_DECLARE = "明文报文清关申报";

    public static final String CAINIAO_RECEIVE_INVENTORY_EXIT_AREA = "菜鸟回传清单出区完成";

}
