package com.danding.cds.common.aspect;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.danding.cds.common.utils.ValidatorUtils;
import com.danding.component.common.api.exceptions.rpcException.ArgsInvalidException;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.validation.Validator;

/**
 * 参数校验的aop
 *
 * <AUTHOR>
 * @date 2021/3/10
 */
@Aspect
@Component
public class ParamValidateV2Aop {

    @Autowired
    private Validator validator;


    @Pointcut(value = "@annotation(com.danding.cds.common.annotation.ParamValidatorV2)")
    private void cutService() {

    }

    @Before("cutService()") // 修改包名和类名以匹配你的需求
    public void validateParam(JoinPoint point) {
        //获取拦截方法的参数
        Object[] methodParams = point.getArgs();

        //如果请求参数不为空则进行参数校验
        if (ArrayUtil.isNotEmpty(methodParams)) {
            // validate注解的校验
            for (Object param : methodParams) {
                String validator = ValidatorUtils.doValidator(this.validator, param);
                if (StrUtil.isNotBlank(validator)) throw new ArgsInvalidException(validator);
            }

            // 实现接口的校验，如果参数中，包含BaseValidatingParam的子类就开始校验参数
            String result = ValidatorUtils.validateParameters(methodParams);
            if (StrUtil.isNotBlank(result)) throw new ArgsInvalidException(result);
        }
    }
}
